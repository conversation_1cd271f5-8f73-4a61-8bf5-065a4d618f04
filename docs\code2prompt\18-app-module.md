Project Path: src

Source Tree:

```txt
src
└── main
    └── kotlin
        └── com
            └── example
                └── gymbro
                    ├── GymBroApp.kt
                    ├── MainActivity.kt
                    ├── app
                    │   ├── di
                    │   │   └── RegionModule.kt
                    │   ├── error
                    │   │   └── ErrorCodeMapper.kt
                    │   ├── extensions
                    │   │   ├── ContextExtensions.kt
                    │   │   ├── ModernResultExtensions.kt
                    │   │   └── UiTextExtensions.kt
                    │   ├── loading
                    │   │   ├── LoadingScreen.kt
                    │   │   ├── LoadingViewModel.kt
                    │   │   ├── StartupConstants.kt
                    │   │   └── components
                    │   │       └── RainbowText.kt
                    │   ├── notification
                    │   │   └── NotificationChannelManager.kt
                    │   └── version
                    │       ├── AppLockedScreen.kt
                    │       ├── ForceUpdateScreen.kt
                    │       └── RegionDetectionManager.kt
                    ├── di
                    │   └── AppConfigModule.kt
                    ├── network
                    │   └── AndroidNetworkMonitor.kt
                    └── service
                        ├── BackgroundServiceManager.kt
                        ├── BaseService.kt
                        └── ForegroundServiceManager.kt

```

`main\kotlin\com\example\gymbro\GymBroApp.kt`:

```kt
   1 | package com.example.gymbro
   2 | 
   3 | import android.app.Application
   4 | import androidx.hilt.work.HiltWorkerFactory
   5 | import androidx.work.Configuration
   6 | import androidx.work.WorkManager
   7 | import com.example.gymbro.app.notification.NotificationChannelManager
   8 | import com.example.gymbro.core.di.qualifiers.ApplicationScope
   9 | import com.example.gymbro.core.ml.service.BgeEngineManager
  10 | import com.example.gymbro.data.shared.migration.DatabaseMigrationService
  11 | import com.google.android.gms.common.GoogleApiAvailability
  12 | import com.google.firebase.FirebaseApp
  13 | import dagger.hilt.android.HiltAndroidApp
  14 | import kotlinx.coroutines.CoroutineScope
  15 | import kotlinx.coroutines.Dispatchers
  16 | import kotlinx.coroutines.launch
  17 | import timber.log.Timber
  18 | import javax.inject.Inject
  19 | 
  20 | /**
  21 |  * 应用程序类
  22 |  * 使用 @HiltAndroidApp 注解启用 Hilt 依赖注入
  23 |  * 使用手动初始化 WorkManager 避免循环依赖问题
  24 |  */
  25 | @HiltAndroidApp
  26 | class GymBroApp :
  27 |     Application(),
  28 |     Configuration.Provider {
  29 |     // 只保留启动时必须的依赖
  30 |     @Inject
  31 |     lateinit var databaseMigrationService: DatabaseMigrationService
  32 | 
  33 |     @Inject
  34 |     lateinit var notificationChannelManager: NotificationChannelManager
  35 | 
  36 |     // 🔥 BGE引擎管理器 - 用于预加载
  37 |     @Inject
  38 |     lateinit var bgeEngineManager: BgeEngineManager
  39 | 
  40 |     // 🔥 应用级协程作用域 - 用于后台预加载
  41 |     @Inject
  42 |     @ApplicationScope
  43 |     lateinit var applicationScope: CoroutineScope
  44 | 
  45 |     // 🔥 Hilt WorkerFactory - 修复 HardwareDetectionWorker ANR 问题
  46 |     @Inject
  47 |     lateinit var workerFactory: HiltWorkerFactory
  48 | 
  49 |     // 🔥 【新增】统一日志管理器
  50 |     @Inject
  51 |     lateinit var timberManager: com.example.gymbro.core.logging.TimberManager
  52 | 
  53 |     // 移除了不再需要的启动时依赖：
  54 |     // - authRepository: Firebase认证会自动初始化
  55 |     // - exerciseDataInitializer: 延迟到首次进入训练模块时
  56 |     // - syncManager: 延迟到用户登录后
  57 |     // - networkMonitor: 延迟到需要网络功能时
  58 |     // - aiProviderManager: 延迟到首次使用AI功能时
  59 | 
  60 |     // 注意：applicationScope现在通过DI注入，移除了手动创建的实例
  61 | 
  62 |     override fun onCreate() {
  63 |         super.onCreate()
  64 | 
  65 |         // 初始化Firebase
  66 |         initializeFirebase()
  67 | 
  68 |         // 配置日志记录器
  69 |         setupTimber()
  70 | 
  71 |         // 手动初始化WorkManager
  72 |         initializeWorkManager()
  73 | 
  74 |         // 初始化通知渠道
  75 |         initializeNotificationChannels()
  76 | 
  77 |         // 延迟初始化其他组件
  78 |         delayedInit()
  79 | 
  80 |         // 🔥 启动BGE引擎预加载 - 关键优化
  81 |         preheatBgeEngine()
  82 |     }
  83 | 
  84 |     /**
  85 |      * 初始化Firebase应用
  86 |      * 确保Firebase应用实例正确配置
  87 |      */
  88 |     private fun initializeFirebase() {
  89 |         try {
  90 |             // 检查Firebase是否已初始化
  91 |             if (FirebaseApp.getApps(this).isNotEmpty()) {
  92 |                 Timber.d("Firebase已初始化")
  93 |             } else {
  94 |                 Timber.d("Firebase尚未初始化，尝试重新初始化")
  95 |                 FirebaseApp.initializeApp(this)
  96 |             }
  97 | 
  98 |             // 检查Google Play服务是否可用
  99 |             val googleApiAvailability = GoogleApiAvailability.getInstance()
 100 |             val resultCode = googleApiAvailability.isGooglePlayServicesAvailable(this)
 101 |             if (resultCode != com.google.android.gms.common.ConnectionResult.SUCCESS) {
 102 |                 Timber.e("Google Play服务不可用，错误码: $resultCode")
 103 |                 if (googleApiAvailability.isUserResolvableError(resultCode)) {
 104 |                     Timber.d("Google Play服务错误可被用户解决")
 105 |                 }
 106 |             } else {
 107 |                 Timber.d("Google Play服务可用")
 108 |             }
 109 |         } catch (e: Exception) {
 110 |             Timber.e(e, "初始化Firebase时出错")
 111 |         }
 112 |     }
 113 | 
 114 |     /**
 115 |      * 🔥 【重构】配置统一的日志管理系统
 116 |      *
 117 |      * 新特性：
 118 |      * - 模块级别的日志控制
 119 |      * - ThinkingBox 日志优化（减少噪音）
 120 |      * - 环境感知的日志策略
 121 |      * - 运行时动态配置
 122 |      */
 123 |     private fun setupTimber() {
 124 |         if (BuildConfig.DEBUG) {
 125 |             // 🔥 开发环境：使用新的模块感知日志系统
 126 |             timberManager.initialize(
 127 |                 isDebug = true,
 128 |                 environment = com.example.gymbro.core.logging.LoggingConfig.Environment.DEVELOPMENT,
 129 |             )
 130 | 
 131 |             // 🔥 默认关闭 ThinkingBox 的详细日志，减少噪音
 132 |             timberManager.disableThinkingBoxVerboseLogs()
 133 | 
 134 |             Timber.tag("APP").i("🔥 统一日志系统已启动 - ${timberManager.getCurrentConfig()}")
 135 | 
 136 |             // 协程调试支持
 137 |             System.setProperty("kotlinx.coroutines.debug", "on")
 138 |         } else {
 139 |             // 🔥 生产环境：只记录错误和崩溃
 140 |             timberManager.initialize(
 141 |                 isDebug = false,
 142 |                 environment = com.example.gymbro.core.logging.LoggingConfig.Environment.PRODUCTION,
 143 |             )
 144 | 
 145 |             Timber.tag("APP").i("🔥 生产环境日志系统已启动")
 146 |         }
 147 |     }
 148 | 
 149 |     /**
 150 |      * 手动初始化WorkManager
 151 |      * 由于在AndroidManifest.xml中禁用了自动初始化，需要手动配置
 152 |      */
 153 |     private fun initializeWorkManager() {
 154 |         try {
 155 |             if (!WorkManager.isInitialized()) {
 156 |                 val config = workManagerConfiguration
 157 |                 WorkManager.initialize(this, config)
 158 |                 Timber.d("WorkManager手动初始化完成")
 159 |             } else {
 160 |                 Timber.d("WorkManager已经初始化")
 161 |             }
 162 |         } catch (e: Exception) {
 163 |             Timber.e(e, "WorkManager初始化失败")
 164 |         }
 165 |     }
 166 | 
 167 |     /**
 168 |      * 初始化通知渠道
 169 |      * 为休息计时器等功能创建必要的通知渠道
 170 |      */
 171 |     private fun initializeNotificationChannels() {
 172 |         try {
 173 |             notificationChannelManager.initializeChannels()
 174 |             Timber.d("通知渠道初始化完成")
 175 |         } catch (e: Exception) {
 176 |             Timber.e(e, "通知渠道初始化失败")
 177 |         }
 178 |     }
 179 | 
 180 |     /**
 181 |      * 提供WorkManager配置
 182 |      * 实现Configuration.Provider接口
 183 |      * 🔥 修复：注入 HiltWorkerFactory 解决 HardwareDetectionWorker ANR 问题
 184 |      */
 185 |     override val workManagerConfiguration: Configuration
 186 |         get() =
 187 |             Configuration
 188 |                 .Builder()
 189 |                 .setWorkerFactory(workerFactory)
 190 |                 .setMinimumLoggingLevel(
 191 |                     if (BuildConfig.DEBUG) android.util.Log.DEBUG else android.util.Log.INFO,
 192 |                 ).build()
 193 | 
 194 |     /**
 195 |      * 优化后的延迟初始化
 196 |      * 只执行启动时必须的任务，其他任务延迟到实际需要时执行
 197 |      */
 198 |     private fun delayedInit() {
 199 |         // 只执行必须的数据库迁移
 200 |         applicationScope.launch(Dispatchers.IO) {
 201 |             try {
 202 |                 // 数据库迁移是必须的，因为应用依赖数据库
 203 |                 migrateDatabase()
 204 |                 Timber.d("数据库迁移完成")
 205 |             } catch (e: Exception) {
 206 |                 Timber.e(e, "数据库迁移失败")
 207 |             }
 208 |         }
 209 | 
 210 |         // 🔥 调度硬件检测后台任务 - 减少启动时阻塞
 211 |         scheduleHardwareDetection()
 212 | 
 213 |         Timber.d("启动优化：移除了不必要的初始化任务")
 214 |         Timber.d("- AI提供商初始化：延迟到首次使用AI功能时")
 215 |         Timber.d("- 训练数据初始化：延迟到首次进入训练模块时")
 216 |         Timber.d("- 网络监控：延迟到需要网络功能时")
 217 |         Timber.d("- 后台同步：延迟到用户登录后")
 218 |         Timber.d("- 硬件检测：后台异步执行，不阻塞启动")
 219 |     }
 220 | 
 221 |     /**
 222 |      * 调度硬件检测后台任务
 223 |      *
 224 |      * 🎯 核心优化：将硬件检测从启动时同步执行改为后台异步执行
 225 |      * - 减少启动时的阻塞时间，特别是在低端机上
 226 |      * - 检测结果会被缓存，BGE初始化时可直接使用
 227 |      * - 检测失败时BGE会自动回退到CPU模式
 228 |      */
 229 |     private fun scheduleHardwareDetection() {
 230 |         try {
 231 |             // 使用静态方法调度硬件检测任务
 232 |             com.example.gymbro.features.coach.shared.managers.HardwareDetectionWorker
 233 |                 .enqueueDetection(
 234 |                     context = this,
 235 |                     forceDetection = false, // 如果已有缓存则跳过
 236 |                 )
 237 |             Timber.d("📱 硬件检测任务已调度，将在后台执行")
 238 |         } catch (e: Exception) {
 239 |             Timber.w(e, "调度硬件检测任务失败，BGE将在需要时进行检测")
 240 |         }
 241 |     }
 242 | 
 243 |     // 移除了不必要的启动时初始化方法：
 244 |     // - startNetworkMonitoring(): 延迟到需要网络功能时
 245 |     // - initializeAuth(): Firebase认证会自动初始化
 246 |     // - initializeAiProviders(): 延迟到首次使用AI功能时
 247 |     // - initializeExerciseData(): 延迟到首次进入训练模块时
 248 |     // - scheduleBackgroundSync(): 延迟到用户登录后
 249 |     // - scheduleWhitelistUpdate(): 延迟到需要时
 250 | 
 251 |     /**
 252 |      * 执行数据库迁移
 253 |      * 将训练动作数据从旧结构迁移到新的统一数据库结构
 254 |      */
 255 |     private fun migrateDatabase() {
 256 |         databaseMigrationService.executeMigrations()
 257 |     }
 258 | 
 259 |     /**
 260 |      * 🔥 BGE引擎预热 - 实现毫秒级AI响应的核心
 261 |      *
 262 |      * 在应用启动后台悄悄加载BGE模型，实现：
 263 |      * - 用户点击AI教练FAB时瞬时响应
 264 |      * - 不阻塞主线程和UI渲染
 265 |      * - 智能错误处理和降级
 266 |      */
 267 |     private fun preheatBgeEngine() {
 268 |         applicationScope.launch(Dispatchers.IO) {
 269 |             try {
 270 |                 Timber.i("🔥 开始后台预热BGE引擎...")
 271 | 
 272 |                 // 调用幂等的初始化方法
 273 |                 // 即使多处调用也只会执行一次真正的加载
 274 |                 bgeEngineManager.initialize().collect { status ->
 275 |                     when (status) {
 276 |                         com.example.gymbro.core.ml.embedding.EngineStatus.INITIALIZING -> {
 277 |                             Timber.d("🧠 BGE引擎预热中...")
 278 |                         }
 279 |                         com.example.gymbro.core.ml.embedding.EngineStatus.READY -> {
 280 |                             Timber.i("🎉 BGE引擎预热完成！AI功能已就绪")
 281 |                             // 可选：进行一次预热推理
 282 |                             bgeEngineManager.warmUp()
 283 |                         }
 284 |                         com.example.gymbro.core.ml.embedding.EngineStatus.ERROR -> {
 285 |                             Timber.w("⚠️ BGE引擎预热失败，将在首次使用时重试")
 286 |                         }
 287 |                         else -> {
 288 |                             Timber.d("🔄 BGE引擎状态: $status")
 289 |                         }
 290 |                     }
 291 |                 }
 292 |             } catch (e: Exception) {
 293 |                 // 预热失败不应影响应用正常启动
 294 |                 Timber.w(e, "⚠️ BGE引擎预热过程出现异常，将降级到按需加载")
 295 |             }
 296 |         }
 297 |     }
 298 | 
 299 |     // 移除了这些方法，它们现在会在需要时才执行：
 300 |     // - initializeExerciseData(): 移到WorkoutModule首次使用时
 301 |     // - scheduleBackgroundSync(): 移到用户登录后
 302 |     // - scheduleWhitelistUpdate(): 移到需要时
 303 |     // - initializeAiProviders(): 移到CoachModule首次使用时
 304 | 
 305 |     override fun onTerminate() {
 306 |         super.onTerminate()
 307 |         // 网络监控现在是按需启动的，所以这里不需要停止
 308 |         Timber.d("应用终止")
 309 |     }
 310 | }

```

`main\kotlin\com\example\gymbro\MainActivity.kt`:

```kt
   1 | package com.example.gymbro
   2 | 
   3 | import android.os.Bundle
   4 | import androidx.activity.ComponentActivity
   5 | import androidx.activity.compose.setContent
   6 | import androidx.activity.enableEdgeToEdge
   7 | import androidx.compose.foundation.layout.fillMaxSize
   8 | import androidx.compose.material3.Surface
   9 | import androidx.compose.runtime.*
  10 | import androidx.compose.runtime.CompositionLocalProvider
  11 | import androidx.compose.ui.Modifier
  12 | import androidx.lifecycle.compose.collectAsStateWithLifecycle
  13 | import androidx.navigation.NavHostController
  14 | import androidx.navigation.compose.NavHost
  15 | import androidx.navigation.compose.rememberNavController
  16 | import androidx.navigation.navOptions
  17 | import com.example.gymbro.app.loading.LoadingScreen
  18 | import com.example.gymbro.core.theme.LocalThemeManager
  19 | import com.example.gymbro.core.theme.ThemeConfig
  20 | import com.example.gymbro.core.theme.ThemeManager
  21 | import com.example.gymbro.data.local.datastore.UserPreferencesRepository
  22 | import com.example.gymbro.designSystem.components.animations.GymBroPageTransitions
  23 | import com.example.gymbro.designSystem.overlay.GlobalOverlayHost
  24 | import com.example.gymbro.designSystem.theme.GymBroTheme
  25 | import com.example.gymbro.domain.coach.config.AiProviderManager
  26 | import com.example.gymbro.features.auth.navigation.AuthRoutes
  27 | import com.example.gymbro.features.auth.navigation.authNavGraph
  28 | import com.example.gymbro.features.coach.navigation.coachGraph
  29 | import com.example.gymbro.features.exerciselibrary.api.ExerciseLibraryNavigatable
  30 | import com.example.gymbro.features.home.navigation.homeGraph
  31 | import com.example.gymbro.features.home.navigation.navigateToHomeGraph
  32 | import com.example.gymbro.features.profile.api.ProfileNavigatable
  33 | import com.example.gymbro.features.subscription.presentation.navigation.subscriptionNavigation
  34 | import com.example.gymbro.features.workout.navigation.workoutGraph
  35 | import dagger.hilt.android.AndroidEntryPoint
  36 | import kotlinx.coroutines.Dispatchers
  37 | import kotlinx.coroutines.launch
  38 | import timber.log.Timber
  39 | import javax.inject.Inject
  40 | 
  41 | /**
  42 |  * 主Activity
  43 |  * 应用程序的入口点，负责设置主题、状态栏和导航
  44 |  */
  45 | @AndroidEntryPoint
  46 | class MainActivity : ComponentActivity() {
  47 |     // 注入用户偏好仓库
  48 |     @Inject
  49 |     lateinit var userPreferencesRepository: UserPreferencesRepository
  50 | 
  51 |     // 注入主题管理器
  52 |     @Inject
  53 |     lateinit var themeManager: ThemeManager
  54 | 
  55 |     // 注入AI提供商管理器，用于主应用启动后的后台初始化
  56 |     @Inject
  57 |     lateinit var aiProviderManager: AiProviderManager
  58 | 
  59 |     // 注入动作库导航模块
  60 |     @Inject
  61 |     lateinit var exerciseLibraryNavigatable: ExerciseLibraryNavigatable
  62 | 
  63 |     // 注入个人资料导航模块
  64 |     @Inject
  65 |     lateinit var profileNavigatable: ProfileNavigatable
  66 | 
  67 |     override fun onCreate(savedInstanceState: Bundle?) {
  68 |         super.onCreate(savedInstanceState)
  69 | 
  70 |         // 启用边到边显示
  71 |         enableEdgeToEdge()
  72 | 
  73 |         setContent {
  74 |             // 显示Loading页面，然后进行后台地区检测，最后跳转到主应用
  75 |             GymBroAppWithRegionDetection(
  76 |                 userPreferencesRepository = userPreferencesRepository,
  77 |                 themeManager = themeManager,
  78 |                 aiProviderManager = aiProviderManager,
  79 |                 exerciseLibraryNavigatable = exerciseLibraryNavigatable,
  80 |                 profileNavigatable = profileNavigatable,
  81 |             )
  82 |         }
  83 |     }
  84 | }
  85 | 
  86 | /**
  87 |  * 带地区检测的主应用入口
  88 |  * 流程：Loading -> 后台地区检测 -> 主应用
  89 |  */
  90 | @Composable
  91 | fun GymBroAppWithRegionDetection(
  92 |     userPreferencesRepository: UserPreferencesRepository,
  93 |     themeManager: ThemeManager,
  94 |     aiProviderManager: AiProviderManager,
  95 |     exerciseLibraryNavigatable: ExerciseLibraryNavigatable,
  96 |     profileNavigatable: ProfileNavigatable,
  97 | ) {
  98 |     var appState by remember { mutableStateOf(AppState.LOADING) }
  99 |     val coroutineScope = rememberCoroutineScope()
 100 | 
 101 |     when (appState) {
 102 |         AppState.LOADING -> {
 103 |             // 显示Loading页面，带有彩虹色跃动的"G"
 104 |             LoadingScreen(
 105 |                 onLoadingFinished = {
 106 |                     // Loading完成后，启动后台地区检测并直接进入主应用
 107 |                     appState = AppState.MAIN_APP
 108 |                 },
 109 |             )
 110 |         }
 111 | 
 112 |         AppState.MAIN_APP -> {
 113 |             // 显示主应用，地区检测在后台进行
 114 |             GymBroApp(
 115 |                 userPreferencesRepository = userPreferencesRepository,
 116 |                 themeManager = themeManager,
 117 |                 aiProviderManager = aiProviderManager,
 118 |                 exerciseLibraryNavigatable = exerciseLibraryNavigatable,
 119 |                 profileNavigatable = profileNavigatable,
 120 |                 startDestination = AuthRoutes.AUTH_GRAPH,
 121 |             )
 122 |         }
 123 |     }
 124 | }
 125 | 
 126 | /**
 127 |  * 主应用Composable，使用ThemeManager管理主题状态
 128 |  */
 129 | @Composable
 130 | fun GymBroApp(
 131 |     userPreferencesRepository: UserPreferencesRepository,
 132 |     themeManager: ThemeManager,
 133 |     aiProviderManager: AiProviderManager,
 134 |     exerciseLibraryNavigatable: ExerciseLibraryNavigatable,
 135 |     profileNavigatable: ProfileNavigatable,
 136 |     startDestination: String = AuthRoutes.AUTH_GRAPH,
 137 | ) {
 138 |     // 使用动态主题系统
 139 |     val themeConfig by themeManager.themeConfig.collectAsStateWithLifecycle(
 140 |         initialValue = ThemeConfig(),
 141 |     )
 142 | 
 143 |     // 协程作用域用于后台任务
 144 |     val coroutineScope = rememberCoroutineScope()
 145 | 
 146 |     // 主应用启动后，后台初始化AI提供商
 147 |     LaunchedEffect(Unit) {
 148 |         coroutineScope.launch(Dispatchers.IO) {
 149 |             try {
 150 |                 Timber.d("主应用启动，开始后台初始化AI提供商")
 151 | 
 152 |                 // 触发AI提供商管理器的初始化
 153 |                 // 这会预先加载所有可用的AI提供商配置
 154 |                 val providers = aiProviderManager.availableProviders
 155 |                 Timber.d("AI提供商后台初始化完成，可用提供商数量: ${providers.size}")
 156 | 
 157 |                 // 预热当前提供商（触发StateFlow初始化）
 158 |                 aiProviderManager.currentProvider.collect { provider ->
 159 |                     Timber.d("当前AI提供商已预热: ${provider.name}")
 160 |                     // 只收集一次，然后结束
 161 |                     return@collect
 162 |                 }
 163 |             } catch (e: Exception) {
 164 |                 // 静默处理错误，不影响主应用功能
 165 |                 Timber.w(e, "AI提供商后台初始化失败，不影响主应用功能")
 166 |             }
 167 |         }
 168 |     }
 169 | 
 170 |     // 记录主题变化用于调试
 171 |     LaunchedEffect(themeConfig) {
 172 |         Timber.d("主题配置变化: $themeConfig")
 173 |     }
 174 | 
 175 |     // 提供LocalThemeManager给整个应用
 176 |     CompositionLocalProvider(LocalThemeManager provides themeManager) {
 177 |         GymBroTheme(themeConfig = themeConfig) {
 178 |             Surface(
 179 |                 modifier = Modifier.fillMaxSize(),
 180 |             ) {
 181 |                 // 全局覆盖层宿主 - 在导航树外部挂载，确保跨页面展示倒计时
 182 |                 GlobalOverlayHost()
 183 | 
 184 |                 GymBroNavHost(
 185 |                     startDestination = startDestination,
 186 |                     exerciseLibraryNavigatable = exerciseLibraryNavigatable,
 187 |                     profileNavigatable = profileNavigatable,
 188 |                 )
 189 |             }
 190 |         }
 191 |     }
 192 | }
 193 | 
 194 | /**
 195 |  * 应用程序主导航图
 196 |  */
 197 | @Composable
 198 | fun GymBroNavHost(
 199 |     navController: NavHostController = rememberNavController(),
 200 |     startDestination: String = AuthRoutes.AUTH_GRAPH,
 201 |     exerciseLibraryNavigatable: ExerciseLibraryNavigatable,
 202 |     profileNavigatable: ProfileNavigatable,
 203 | ) {
 204 |     NavHost(
 205 |         navController = navController,
 206 |         startDestination = startDestination,
 207 |         enterTransition = GymBroPageTransitions.ChatGPTStyle.enterTransition,
 208 |         exitTransition = GymBroPageTransitions.ChatGPTStyle.exitTransition,
 209 |         popEnterTransition = GymBroPageTransitions.ChatGPTStyle.popEnterTransition,
 210 |         popExitTransition = GymBroPageTransitions.ChatGPTStyle.popExitTransition,
 211 |     ) {
 212 |         // 认证导航图
 213 |         authNavGraph(
 214 |             navController = navController,
 215 |             onAuthenticated = {
 216 |                 // 导航到主页导航图
 217 |                 navController.navigateToHomeGraph(
 218 |                     navOptions =
 219 |                     navOptions {
 220 |                         popUpTo(AuthRoutes.AUTH_GRAPH) { inclusive = true }
 221 |                     },
 222 |                 )
 223 |             },
 224 |         )
 225 | 
 226 |         // 主页导航图
 227 |         homeGraph(
 228 |             navController = navController,
 229 |             exerciseLibraryNavigatable = exerciseLibraryNavigatable,
 230 |         )
 231 | 
 232 |         // 动作库导航图（独立注册）
 233 |         exerciseLibraryNavigatable.registerGraph(
 234 |             navGraphBuilder = this,
 235 |             navController = navController,
 236 |         )
 237 |         Timber.d("MainActivity: 已注册动作库导航图")
 238 | 
 239 |         /**
 240 |          * 订阅导航图
 241 |          * 使用features/subscription/presentation/navigation包中的subscriptionNavigation函数
 242 |          * 此函数将注册所有订阅相关的路由和目标屏幕，包括SubscriptionScreen.kt
 243 |          */
 244 |         subscriptionNavigation(navController = navController)
 245 |         Timber.d("MainActivity: 已注册订阅导航图")
 246 | 
 247 |         // 训练导航图
 248 |         workoutGraph(
 249 |             navController = navController,
 250 |             exerciseLibraryNavigatable = exerciseLibraryNavigatable,
 251 |         )
 252 |         Timber.d("MainActivity: 已注册训练导航图")
 253 | 
 254 |         // AI教练导航图
 255 |         coachGraph(navController = navController)
 256 |         Timber.d("MainActivity: 已注册AI教练导航图")
 257 | 
 258 |         // 个人资料导航图
 259 |         profileNavigatable.registerGraph(
 260 |             navGraphBuilder = this,
 261 |             navController = navController,
 262 |             onNavigateToAuth = {
 263 |                 // 退出登录，返回认证页面
 264 |                 navController.navigate(AuthRoutes.AUTH_GRAPH) {
 265 |                     popUpTo(0) { inclusive = true }
 266 |                 }
 267 |             },
 268 |         )
 269 |         Timber.d("MainActivity: 已注册个人资料导航图")
 270 |     }
 271 | 
 272 |     Timber.d("MainActivity: 导航图初始化完成")
 273 | }
 274 | 
 275 | /**
 276 |  * 应用状态枚举
 277 |  */
 278 | private enum class AppState {
 279 |     LOADING, // 加载中
 280 |     MAIN_APP, // 主应用（地区检测在后台进行）
 281 | }

```

`main\kotlin\com\example\gymbro\app\di\RegionModule.kt`:

```kt
   1 | package com.example.gymbro.app.di
   2 | 
   3 | import com.example.gymbro.app.version.RegionDetectionManager
   4 | import com.example.gymbro.core.region.RegionProvider
   5 | import dagger.Binds
   6 | import dagger.Module
   7 | import dagger.hilt.InstallIn
   8 | import dagger.hilt.components.SingletonComponent
   9 | import javax.inject.Singleton
  10 | 
  11 | /**
  12 |  * 应用级地区检测模块
  13 |  *
  14 |  * 在app模块中提供RegionProvider的绑定，避免模块循环依赖问题。
  15 |  * 这个模块将RegionDetectionManager绑定为RegionProvider的实现。
  16 |  */
  17 | @Module
  18 | @InstallIn(SingletonComponent::class)
  19 | abstract class RegionModule {
  20 | 
  21 |     /**
  22 |      * 绑定RegionProvider实现
  23 |      *
  24 |      * 使用RegionDetectionManager作为RegionProvider的实现，
  25 |      * 提供真实的IP地理位置检测功能
  26 |      */
  27 |     @Binds
  28 |     @Singleton
  29 |     abstract fun bindRegionProvider(impl: RegionDetectionManager): RegionProvider
  30 | }

```

`main\kotlin\com\example\gymbro\app\error\ErrorCodeMapper.kt`:

```kt
   1 | package com.example.gymbro.app.error
   2 | 
   3 | import com.example.gymbro.core.error.ErrorCode
   4 | import com.example.gymbro.core.ui.text.UiText
   5 | 
   6 | /**
   7 |  * ErrorCode映射器
   8 |  *
   9 |  * 将core层定义的ErrorCode映射到app层的string资源
  10 |  * 实现Clean Architecture的Error-code mapping pattern
  11 |  * 支持本地化和维护UI文本的统一管理
  12 |  *
  13 |  * <AUTHOR> Team
  14 |  * @since 2.0.0
  15 |  */
  16 | object ErrorCodeMapper {
  17 | 
  18 |     /**
  19 |      * 将ErrorCode映射为UiText.StringResource
  20 |      *
  21 |      * @param errorCode core层定义的错误代码
  22 |      * @param vararg params 字符串参数(用于格式化)
  23 |      * @return UiText.StringResource实例
  24 |      */
  25 |     fun mapToUiText(errorCode: ErrorCode, vararg params: Any): UiText {
  26 |         return when (errorCode.category) {
  27 |             "network" -> mapNetworkError(errorCode, params)
  28 |             "auth" -> mapAuthError(errorCode, params)
  29 |             "data" -> mapDataError(errorCode, params)
  30 |             "validation" -> mapValidationError(errorCode, params)
  31 |             "business" -> mapBusinessError(errorCode, params)
  32 |             "system" -> mapSystemError(errorCode, params)
  33 |             "payment" -> mapPaymentError(errorCode, params)
  34 |             else -> UiText.StringResource(
  35 |                 resId = com.example.gymbro.designSystem.R.string.error_generic,
  36 |                 args = emptyList(),
  37 |             )
  38 |         }
  39 |     }
  40 | 
  41 |     /**
  42 |      * 映射网络错误
  43 |      */
  44 |     private fun mapNetworkError(errorCode: ErrorCode, params: Array<out Any>): UiText {
  45 |         return when (errorCode) {
  46 |             ErrorCode.NETWORK_RETRY_EXHAUSTED,
  47 |             ErrorCode.NETWORK_FALLBACK_CACHE,
  48 |             ErrorCode.NETWORK_OFFLINE_MODE,
  49 |             ErrorCode.NETWORK_UNSTABLE,
  50 |             ->
  51 |                 UiText.StringResource(
  52 |                     resId = com.example.gymbro.designSystem.R.string.network_not_available,
  53 |                     args = emptyList(),
  54 |                 )
  55 | 
  56 |             ErrorCode.NETWORK_CONNECTION_FAILED,
  57 |             ErrorCode.NETWORK_EXCEPTION,
  58 |             ->
  59 |                 createUiText(
  60 |                     resId = com.example.gymbro.designSystem.R.string.login_error_network,
  61 |                     params = params,
  62 |                 )
  63 | 
  64 |             else -> UiText.StringResource(
  65 |                 resId = com.example.gymbro.designSystem.R.string.network_not_available,
  66 |                 args = emptyList(),
  67 |             )
  68 |         }
  69 |     }
  70 | 
  71 |     /**
  72 |      * 映射认证错误
  73 |      */
  74 |     private fun mapAuthError(errorCode: ErrorCode, params: Array<out Any>): UiText {
  75 |         return when (errorCode) {
  76 |             ErrorCode.AUTH_FAILED ->
  77 |                 UiText.StringResource(
  78 |                     resId = com.example.gymbro.designSystem.R.string.error_login_failed,
  79 |                     args = emptyList(),
  80 |                 )
  81 |             ErrorCode.AUTH_SESSION_EXPIRED ->
  82 |                 UiText.StringResource(
  83 |                     resId = com.example.gymbro.designSystem.R.string.auth_token_expired,
  84 |                     args = emptyList(),
  85 |                 )
  86 |             ErrorCode.AUTH_FORBIDDEN ->
  87 |                 UiText.StringResource(
  88 |                     resId = com.example.gymbro.designSystem.R.string.auth_unauthorized,
  89 |                     args = emptyList(),
  90 |                 )
  91 |             else ->
  92 |                 UiText.StringResource(
  93 |                     resId = com.example.gymbro.designSystem.R.string.error_login_failed,
  94 |                     args = emptyList(),
  95 |                 )
  96 |         }
  97 |     }
  98 | 
  99 |     /**
 100 |      * 映射数据错误
 101 |      */
 102 |     private fun mapDataError(errorCode: ErrorCode, params: Array<out Any>): UiText {
 103 |         return when (errorCode) {
 104 |             ErrorCode.DATA_NOT_FOUND,
 105 |             ErrorCode.DATA_NOT_FOUND_WITH_ID,
 106 |             ->
 107 |                 createUiText(
 108 |                     resId = com.example.gymbro.designSystem.R.string.error_generic,
 109 |                     params = params,
 110 |                 )
 111 |             else ->
 112 |                 UiText.StringResource(
 113 |                     resId = com.example.gymbro.designSystem.R.string.error_generic,
 114 |                     args = emptyList(),
 115 |                 )
 116 |         }
 117 |     }
 118 | 
 119 |     /**
 120 |      * 映射验证错误
 121 |      */
 122 |     private fun mapValidationError(errorCode: ErrorCode, params: Array<out Any>): UiText {
 123 |         return when (errorCode) {
 124 |             ErrorCode.VALIDATION_REQUIRED ->
 125 |                 createUiText(
 126 |                     resId = com.example.gymbro.designSystem.R.string.field_required,
 127 |                     params = params,
 128 |                 )
 129 |             ErrorCode.VALIDATION_FORMAT_INVALID ->
 130 |                 createUiText(
 131 |                     resId = com.example.gymbro.designSystem.R.string.invalid_format,
 132 |                     params = params,
 133 |                 )
 134 |             else ->
 135 |                 UiText.StringResource(
 136 |                     resId = com.example.gymbro.designSystem.R.string.general_validation_error,
 137 |                     args = emptyList(),
 138 |                 )
 139 |         }
 140 |     }
 141 | 
 142 |     /**
 143 |      * 映射业务错误
 144 |      */
 145 |     private fun mapBusinessError(errorCode: ErrorCode, params: Array<out Any>): UiText {
 146 |         return UiText.StringResource(
 147 |             resId = com.example.gymbro.designSystem.R.string.error_generic,
 148 |             args = emptyList(),
 149 |         )
 150 |     }
 151 | 
 152 |     /**
 153 |      * 映射系统错误
 154 |      */
 155 |     private fun mapSystemError(errorCode: ErrorCode, params: Array<out Any>): UiText {
 156 |         return UiText.StringResource(
 157 |             resId = com.example.gymbro.designSystem.R.string.error_generic,
 158 |             args = emptyList(),
 159 |         )
 160 |     }
 161 | 
 162 |     /**
 163 |      * 映射支付错误
 164 |      */
 165 |     private fun mapPaymentError(errorCode: ErrorCode, params: Array<out Any>): UiText {
 166 |         // 所有支付错误都使用通用错误消息
 167 |         return UiText.StringResource(
 168 |             resId = com.example.gymbro.designSystem.R.string.error_generic,
 169 |             args = emptyList(),
 170 |         )
 171 |     }
 172 | 
 173 |     /**
 174 |      * 创建带参数的UiText
 175 |      */
 176 |     private fun createUiText(resId: Int, params: Array<out Any>): UiText {
 177 |         return if (params.isNotEmpty()) {
 178 |             UiText.StringResource(
 179 |                 resId = resId,
 180 |                 args = params.map { it.toString() },
 181 |             )
 182 |         } else {
 183 |             UiText.StringResource(
 184 |                 resId = resId,
 185 |                 args = emptyList(),
 186 |             )
 187 |         }
 188 |     }
 189 | 
 190 |     /**
 191 |      * 获取ErrorCode对应的恢复建议
 192 |      *
 193 |      * @param errorCode 错误代码
 194 |      * @return 恢复建议的UiText
 195 |      */
 196 |     fun getRecoverySuggestion(errorCode: ErrorCode): UiText? {
 197 |         return when (errorCode.category) {
 198 |             "network" -> UiText.StringResource(
 199 |                 resId = com.example.gymbro.designSystem.R.string.retry,
 200 |                 args = emptyList(),
 201 |             )
 202 |             "auth" -> UiText.DynamicString("请重新登录")
 203 |             "payment" -> UiText.StringResource(
 204 |                 resId = com.example.gymbro.designSystem.R.string.retry,
 205 |                 args = emptyList(),
 206 |             )
 207 |             "validation" -> UiText.StringResource(
 208 |                 resId = com.example.gymbro.designSystem.R.string.retry,
 209 |                 args = emptyList(),
 210 |             )
 211 |             else -> null
 212 |         }
 213 |     }
 214 | 
 215 |     /**
 216 |      * 批量映射ErrorCode列表
 217 |      *
 218 |      * @param errorCodes 错误代码列表
 219 |      * @return UiText列表
 220 |      */
 221 |     fun mapErrorCodes(errorCodes: List<ErrorCode>): List<UiText> {
 222 |         return errorCodes.map { mapToUiText(it) }
 223 |     }
 224 | 
 225 |     /**
 226 |      * 根据错误分类获取通用错误消息
 227 |      *
 228 |      * @param category 错误分类
 229 |      * @return 通用错误消息
 230 |      */
 231 |     fun getGenericErrorForCategory(category: String): UiText {
 232 |         return when (category) {
 233 |             "network" -> UiText.StringResource(
 234 |                 resId = com.example.gymbro.designSystem.R.string.network_not_available,
 235 |                 args = emptyList(),
 236 |             )
 237 |             "auth" -> UiText.StringResource(
 238 |                 resId = com.example.gymbro.designSystem.R.string.error_login_failed,
 239 |                 args = emptyList(),
 240 |             )
 241 |             "data" -> UiText.StringResource(
 242 |                 resId = com.example.gymbro.designSystem.R.string.error_generic,
 243 |                 args = emptyList(),
 244 |             )
 245 |             "validation" -> UiText.StringResource(
 246 |                 resId = com.example.gymbro.designSystem.R.string.general_validation_error,
 247 |                 args = emptyList(),
 248 |             )
 249 |             "business" -> UiText.StringResource(
 250 |                 resId = com.example.gymbro.designSystem.R.string.error_generic,
 251 |                 args = emptyList(),
 252 |             )
 253 |             "system" -> UiText.StringResource(
 254 |                 resId = com.example.gymbro.designSystem.R.string.error_generic,
 255 |                 args = emptyList(),
 256 |             )
 257 |             "payment" -> UiText.StringResource(
 258 |                 resId = com.example.gymbro.designSystem.R.string.error_generic,
 259 |                 args = emptyList(),
 260 |             )
 261 |             else -> UiText.StringResource(
 262 |                 resId = com.example.gymbro.designSystem.R.string.error_generic,
 263 |                 args = emptyList(),
 264 |             )
 265 |         }
 266 |     }
 267 | }

```

`main\kotlin\com\example\gymbro\app\extensions\ContextExtensions.kt`:

```kt
   1 | package com.example.gymbro.app.extensions
   2 | 
   3 | import android.content.Context
   4 | import android.widget.Toast
   5 | import com.example.gymbro.core.resources.AndroidResourceProvider
   6 | import com.example.gymbro.core.resources.ResourceProvider
   7 | import com.example.gymbro.core.ui.text.UiText
   8 | 
   9 | /**
  10 |  * Context扩展函数 - Android特定功能
  11 |  * 从core模块迁移而来，专门处理Android Context相关操作
  12 |  */
  13 | 
  14 | /**
  15 |  * 显示Toast消息
  16 |  * @param message 要显示的消息
  17 |  * @param duration Toast持续时间，默认为LENGTH_SHORT
  18 |  */
  19 | fun Context.toast(message: String, duration: Int = Toast.LENGTH_SHORT) {
  20 |     Toast.makeText(this, message, duration).show()
  21 | }
  22 | 
  23 | /**
  24 |  * 显示UiText格式的Toast消息
  25 |  * @param uiText UiText格式的消息
  26 |  * @param duration Toast持续时间，默认为LENGTH_SHORT
  27 |  */
  28 | fun Context.toast(uiText: UiText, duration: Int = Toast.LENGTH_SHORT) {
  29 |     val message = when (uiText) {
  30 |         is UiText.DynamicString -> uiText.value
  31 |         is UiText.StringResource -> getString(uiText.resId, *uiText.args.toTypedArray())
  32 |         is UiText.ErrorCode -> "Error: ${uiText.errorCode}"
  33 |         UiText.Empty -> ""
  34 |     }
  35 |     toast(message, duration)
  36 | }
  37 | 
  38 | /**
  39 |  * Context扩展函数 - 转换为ResourceProvider
  40 |  *
  41 |  * 此扩展函数允许将Android Context对象直接转换为ResourceProvider接口实例。
  42 |  * 当组件需要ResourceProvider但只有Context可用时非常有用。
  43 |  *
  44 |  * 用法示例:
  45 |  * ```
  46 |  * val context: Context = ...
  47 |  * val resourceProvider: ResourceProvider = context.asResourceProvider()
  48 |  * val message = resourceProvider.getString(R.string.message)
  49 |  * ```
  50 |  *
  51 |  * 注意：此函数创建一个新的AndroidResourceProvider实例，不使用依赖注入。
  52 |  * 在可能的情况下，应优先使用通过Hilt注入的ResourceProvider实例。
  53 |  *
  54 |  * @return 使用提供的Context创建的ResourceProvider实例
  55 |  */
  56 | fun Context.asResourceProvider(): ResourceProvider {
  57 |     return object : ResourceProvider {
  58 |         override fun getString(resId: Int): String =
  59 |             <EMAIL>(resId)
  60 | 
  61 |         override fun getString(resId: Int, vararg args: Any): String =
  62 |             <EMAIL>(resId, *args)
  63 | 
  64 |         override fun getQuantityString(resId: Int, quantity: Int, vararg args: Any): String =
  65 |             <EMAIL>(resId, quantity, *args)
  66 | 
  67 |         override fun asUiText(resId: Int, vararg args: Any): UiText =
  68 |             UiText.StringResource(resId, args.map { it.toString() })
  69 | 
  70 |         override fun getCurrentLanguage(): String =
  71 |             java.util.Locale.getDefault().toLanguageTag()
  72 | 
  73 |         override fun getInteger(resId: Int): Int =
  74 |             <EMAIL>(resId)
  75 |     }
  76 | }
  77 | 
  78 | /**
  79 |  * ResourceProvider扩展函数 - 获取原始Context
  80 |  *
  81 |  * 当ResourceProvider是通过Context.asResourceProvider()创建时，
  82 |  * 可以通过此扩展属性获取原始Context（如果可用）。
  83 |  *
  84 |  * 注意：由于ResourceProvider是一个接口，此函数仅适用于通过上述扩展函数创建的实例。
  85 |  * 对于其他实现，将返回null。
  86 |  *
  87 |  * @return 原始Context或null（如果无法获取）
  88 |  */
  89 | val ResourceProvider.context: Context?
  90 |     get() = when (this) {
  91 |         is AndroidResourceProvider -> {
  92 |             // 通过反射获取context字段
  93 |             try {
  94 |                 val field = AndroidResourceProvider::class.java.getDeclaredField("context")
  95 |                 field.isAccessible = true
  96 |                 field.get(this) as? Context
  97 |             } catch (e: NoSuchFieldException) {
  98 |                 android.util.Log.w("ContextExtensions", "无法通过反射获取context字段: field not found", e)
  99 |                 null
 100 |             } catch (e: IllegalAccessException) {
 101 |                 android.util.Log.w("ContextExtensions", "无法通过反射获取context字段: access denied", e)
 102 |                 null
 103 |             } catch (e: Exception) {
 104 |                 android.util.Log.e("ContextExtensions", "反射访问context字段时发生未知错误", e)
 105 |                 null
 106 |             }
 107 |         }
 108 |         else -> null
 109 |     }

```

`main\kotlin\com\example\gymbro\app\extensions\ModernResultExtensions.kt`:

```kt
   1 | package com.example.gymbro.app.extensions
   2 | 
   3 | import com.example.gymbro.core.error.types.ModernDataError
   4 | import com.example.gymbro.core.error.types.ModernResult
   5 | import timber.log.Timber
   6 | 
   7 | /**
   8 |  * ModernResult扩展函数，简化错误处理和成功处理的模板代码
   9 |  */
  10 | 
  11 | /**
  12 |  * 简化的fold操作，处理成功和错误情况
  13 |  * @param onSuccess 成功时的处理函数
  14 |  * @param onError 错误时的处理函数
  15 |  */
  16 | suspend inline fun <T> ModernResult<T>.fold(
  17 |     crossinline onSuccess: suspend (T) -> Unit,
  18 |     crossinline onError: suspend (ModernDataError) -> Unit,
  19 | ) {
  20 |     when (this) {
  21 |         is ModernResult.Success -> onSuccess(data)
  22 |         is ModernResult.Error -> onError(error)
  23 |         is ModernResult.Loading -> {
  24 |             // Loading状态通常在UI层处理，这里不做特殊处理
  25 |         }
  26 |     }
  27 | }
  28 | 
  29 | /**
  30 |  * 简化的fold操作，带默认错误处理
  31 |  * @param onSuccess 成功时的处理函数
  32 |  */
  33 | suspend inline fun <T> ModernResult<T>.foldWithDefaultError(
  34 |     crossinline onSuccess: suspend (T) -> Unit,
  35 | ) {
  36 |     fold(
  37 |         onSuccess = onSuccess,
  38 |         onError = { error ->
  39 |             Timber.w("ModernResult处理错误: ${error.operationName} - ${error.message}")
  40 |         },
  41 |     )
  42 | }
  43 | 
  44 | /**
  45 |  * 获取数据或默认值
  46 |  * @param defaultValue 当结果为错误或Loading时返回的默认值
  47 |  * @return 成功时返回数据，否则返回默认值
  48 |  */
  49 | fun <T> ModernResult<T>.getOrDefault(defaultValue: T): T =
  50 |     when (this) {
  51 |         is ModernResult.Success -> data
  52 |         is ModernResult.Error -> {
  53 |             Timber.w("ModernResult获取默认值: ${error.operationName} - ${error.message}")
  54 |             defaultValue
  55 |         }
  56 |         is ModernResult.Loading -> defaultValue
  57 |     }
  58 | 
  59 | /**
  60 |  * 获取数据或null
  61 |  * @return 成功时返回数据，否则返回null
  62 |  */
  63 | fun <T> ModernResult<T>.getOrNull(): T? =
  64 |     when (this) {
  65 |         is ModernResult.Success -> data
  66 |         is ModernResult.Error -> {
  67 |             Timber.d("ModernResult返回null: ${error.operationName}")
  68 |             null
  69 |         }
  70 |         is ModernResult.Loading -> null
  71 |     }
  72 | 
  73 | /**
  74 |  * 检查是否为成功状态
  75 |  */
  76 | fun <T> ModernResult<T>.isSuccess(): Boolean = this is ModernResult.Success
  77 | 
  78 | /**
  79 |  * 检查是否为错误状态
  80 |  */
  81 | fun <T> ModernResult<T>.isError(): Boolean = this is ModernResult.Error
  82 | 
  83 | /**
  84 |  * 检查是否为加载状态
  85 |  */
  86 | fun <T> ModernResult<T>.isLoading(): Boolean = this is ModernResult.Loading
  87 | 
  88 | /**
  89 |  * 映射成功结果到新类型
  90 |  * @param transform 转换函数
  91 |  * @return 转换后的ModernResult
  92 |  */
  93 | inline fun <T, R> ModernResult<T>.map(crossinline transform: (T) -> R): ModernResult<R> =
  94 |     when (this) {
  95 |         is ModernResult.Success -> ModernResult.Success(transform(data))
  96 |         is ModernResult.Error -> ModernResult.Error(error)
  97 |         is ModernResult.Loading -> ModernResult.Loading
  98 |     }
  99 | 
 100 | /**
 101 |  * 优雅降级处理 - 当操作失败时使用默认行为
 102 |  * @param logTag 日志标签
 103 |  * @param fallbackAction 降级时执行的操作
 104 |  */
 105 | suspend inline fun <T> ModernResult<T>.gracefulFallback(
 106 |     logTag: String,
 107 |     crossinline fallbackAction: suspend () -> Unit,
 108 | ) {
 109 |     when (this) {
 110 |         is ModernResult.Success -> {
 111 |             // 成功时不需要降级
 112 |         }
 113 |         is ModernResult.Error -> {
 114 |             Timber.w("[$logTag] 执行优雅降级: ${error.operationName} - ${error.message}")
 115 |             fallbackAction()
 116 |         }
 117 |         is ModernResult.Loading -> {
 118 |             // Loading状态通常不需要降级
 119 |         }
 120 |     }
 121 | }
 122 | 
 123 | /**
 124 |  * 优雅降级处理 - 使用默认日志标签
 125 |  * @param fallbackAction 降级时执行的操作
 126 |  */
 127 | suspend inline fun <T> ModernResult<T>.gracefulFallback(
 128 |     crossinline fallbackAction: suspend () -> Unit,
 129 | ) {
 130 |     gracefulFallback("GracefulFallback", fallbackAction)
 131 | }

```

`main\kotlin\com\example\gymbro\app\extensions\UiTextExtensions.kt`:

```kt
   1 | package com.example.gymbro.app.extensions
   2 | 
   3 | import android.content.Context
   4 | import com.example.gymbro.core.ui.text.UiText
   5 | 
   6 | /**
   7 |  * UiText的Android平台扩展
   8 |  *
   9 |  * 提供与Android平台相关的UiText扩展方法
  10 |  */
  11 | 
  12 | /**
  13 |  * 将UiText转换为字符串
  14 |  *
  15 |  * 需要Android Context才能获取资源字符串
  16 |  *
  17 |  * @param context Android上下文
  18 |  * @return 格式化后的字符串
  19 |  */
  20 | fun UiText.asString(context: Context): String =
  21 |     when (this) {
  22 |         is UiText.DynamicString -> value
  23 |         is UiText.StringResource -> {
  24 |             if (args.isEmpty()) {
  25 |                 context.getString(resId)
  26 |             } else {
  27 |                 context.getString(resId, *args.toTypedArray())
  28 |             }
  29 |         }
  30 |         is UiText.ErrorCode -> "Error: $errorCode"
  31 |         UiText.Empty -> ""
  32 |     }
  33 | 
  34 | /**
  35 |  * 将可能为空的UiText转换为字符串，如果为null则返回空字符串
  36 |  *
  37 |  * @param context Android上下文
  38 |  * @return 格式化后的字符串，如果UiText为null则返回空字符串
  39 |  */
  40 | fun UiText?.asStringOrEmpty(context: Context): String = this?.asString(context) ?: ""

```

`main\kotlin\com\example\gymbro\app\loading\LoadingScreen.kt`:

```kt
   1 | package com.example.gymbro.app.loading
   2 | 
   3 | import androidx.compose.foundation.background
   4 | import androidx.compose.foundation.layout.Box
   5 | import androidx.compose.foundation.layout.fillMaxSize
   6 | import androidx.compose.material3.MaterialTheme
   7 | import androidx.compose.runtime.Composable
   8 | import androidx.compose.runtime.LaunchedEffect
   9 | import androidx.compose.runtime.collectAsState
  10 | import androidx.compose.runtime.getValue
  11 | import androidx.compose.ui.Alignment
  12 | import androidx.compose.ui.Modifier
  13 | import androidx.hilt.navigation.compose.hiltViewModel
  14 | import com.example.gymbro.app.loading.components.RainbowLogo
  15 | import com.example.gymbro.app.version.AppLockedScreen
  16 | import com.example.gymbro.app.version.ForceUpdateScreen
  17 | import kotlinx.coroutines.delay
  18 | 
  19 | @Composable
  20 | fun LoadingScreen(
  21 |     loadingViewModel: LoadingViewModel = hiltViewModel(),
  22 |     onLoadingFinished: () -> Unit,
  23 | ) {
  24 |     // 观察统一的loading状态
  25 |     val loadingState by loadingViewModel.loadingState.collectAsState()
  26 | 
  27 |     // 使用Material主题系统的背景色
  28 |     val backgroundColor = MaterialTheme.colorScheme.background
  29 | 
  30 |     // 状态变化处理：静默后台处理，只关注最终结果
  31 |     LaunchedEffect(loadingState) {
  32 |         when (loadingState) {
  33 |             is LoadingState.Ready -> {
  34 |                 // 短暂延迟确保用户看到Loading完成
  35 |                 delay(StartupConstants.Animation.LOADING_FINISH_DELAY_MS)
  36 |                 onLoadingFinished()
  37 |             }
  38 |             is LoadingState.AppLocked,
  39 |             is LoadingState.ForceUpdate,
  40 |             -> {
  41 |                 // 这些状态会在UI中直接显示，不跳转
  42 |             }
  43 |             is LoadingState.Initializing,
  44 |             is LoadingState.CheckingVersion,
  45 |             -> {
  46 |                 // 继续显示Loading，无需特殊处理
  47 |             }
  48 |         }
  49 |     }
  50 | 
  51 |     // 根据状态显示不同的UI
  52 |     when (val currentState = loadingState) {
  53 |         is LoadingState.AppLocked -> {
  54 |             AppLockedScreen(lockInfo = currentState.lockInfo)
  55 |         }
  56 |         is LoadingState.ForceUpdate -> {
  57 |             ForceUpdateScreen(updateInfo = currentState.updateInfo)
  58 |         }
  59 |         is LoadingState.Initializing,
  60 |         is LoadingState.CheckingVersion,
  61 |         is LoadingState.Ready,
  62 |         -> {
  63 |             // 统一的Loading界面：简洁优雅，无多余信息
  64 |             Box(
  65 |                 modifier = Modifier
  66 |                     .fillMaxSize()
  67 |                     .background(backgroundColor),
  68 |                 contentAlignment = Alignment.Center,
  69 |             ) {
  70 |                 RainbowLogo()
  71 |             }
  72 |         }
  73 |     }
  74 | }

```

`main\kotlin\com\example\gymbro\app\loading\LoadingViewModel.kt`:

```kt
   1 | package com.example.gymbro.app.loading
   2 | 
   3 | import androidx.lifecycle.ViewModel
   4 | import androidx.lifecycle.viewModelScope
   5 | import com.example.gymbro.app.extensions.fold
   6 | import com.example.gymbro.app.version.RegionDetectionManager
   7 | import com.example.gymbro.domain.auth.repository.AuthRepository
   8 | import com.example.gymbro.domain.auth.usecase.LoginAnonymouslyUseCase
   9 | import com.example.gymbro.domain.model.auth.AuthUser
  10 | import com.example.gymbro.domain.shared.base.version.VersionAccessResult
  11 | import com.example.gymbro.domain.shared.version.CheckVersionAccessUseCase
  12 | import dagger.hilt.android.lifecycle.HiltViewModel
  13 | import kotlinx.coroutines.Job
  14 | import kotlinx.coroutines.flow.MutableStateFlow
  15 | import kotlinx.coroutines.flow.StateFlow
  16 | import kotlinx.coroutines.flow.asStateFlow
  17 | import kotlinx.coroutines.flow.first
  18 | import kotlinx.coroutines.launch
  19 | import kotlinx.coroutines.withTimeoutOrNull
  20 | import timber.log.Timber
  21 | import javax.inject.Inject
  22 | 
  23 | /**
  24 |  * Loading页面状态机
  25 |  */
  26 | sealed class LoadingState {
  27 |     /** 初始化中 */
  28 |     object Initializing : LoadingState()
  29 | 
  30 |     /** 版本检查中 */
  31 |     object CheckingVersion : LoadingState()
  32 | 
  33 |     /** 应用被锁定 */
  34 |     data class AppLocked(
  35 |         val lockInfo: VersionAccessResult.Locked,
  36 |     ) : LoadingState()
  37 | 
  38 |     /** 需要强制更新 */
  39 |     data class ForceUpdate(
  40 |         val updateInfo: VersionAccessResult.ForceUpdate,
  41 |     ) : LoadingState()
  42 | 
  43 |     /** 准备完成，可以跳转 */
  44 |     object Ready : LoadingState()
  45 | }
  46 | 
  47 | @HiltViewModel
  48 | class LoadingViewModel
  49 | @Inject
  50 | constructor(
  51 |     private val regionDetectionManager: RegionDetectionManager,
  52 |     private val authRepository: AuthRepository,
  53 |     private val loginAnonymouslyUseCase: LoginAnonymouslyUseCase,
  54 |     private val checkVersionAccessUseCase: CheckVersionAccessUseCase,
  55 | ) : ViewModel() {
  56 |     private val _loadingState = MutableStateFlow<LoadingState>(LoadingState.Initializing)
  57 |     val loadingState: StateFlow<LoadingState> = _loadingState.asStateFlow()
  58 | 
  59 |     private var regionDetectionJob: Job? = null
  60 | 
  61 |     init {
  62 |         startLoadingProcess()
  63 |     }
  64 | 
  65 |     /**
  66 |      * 启动Loading流程
  67 |      * 采用静默后台策略，用户无需感知地区检测过程
  68 |      */
  69 |     private fun startLoadingProcess() {
  70 |         viewModelScope.launch {
  71 |             Timber.tag(StartupConstants.LogTag.STARTUP).d("Loading流程开始：启动后台静默地区检测")
  72 | 
  73 |             // 后台静默启动地区检测（不阻塞主流程）
  74 |             regionDetectionJob =
  75 |                 launch {
  76 |                     silentRegionDetection()
  77 |                 }
  78 | 
  79 |             // 主流程：直接进行版本检查（使用默认区域）
  80 |             performVersionCheck(regionCode = StartupConstants.Region.DEFAULT)
  81 |         }
  82 |     }
  83 | 
  84 |     /**
  85 |      * 后台静默地区检测
  86 |      * 失败时不影响主流程，默认使用国际区域
  87 |      */
  88 |     private suspend fun silentRegionDetection() {
  89 |         try {
  90 |             Timber.tag(StartupConstants.LogTag.REGION_DETECTION).d("后台静默地区检测开始")
  91 | 
  92 |             // 超时保护，不影响用户体验
  93 |             withTimeoutOrNull(StartupConstants.Network.REGION_DETECTION_TIMEOUT_MS) {
  94 |                 regionDetectionManager.initializeRegionDetection()
  95 |             }
  96 | 
  97 |             val detectedRegion = regionDetectionManager.getCurrentRegion()
  98 |             val regionCode =
  99 |                 when (detectedRegion) {
 100 |                     com.example.gymbro.core.region.RegionProvider.UserRegion.CN -> StartupConstants.Region.CN
 101 |                     com.example.gymbro.core.region.RegionProvider.UserRegion.INTERNATIONAL -> StartupConstants.Region.INTERNATIONAL
 102 |                     null -> StartupConstants.Region.DEFAULT
 103 |                 }
 104 | 
 105 |             Timber.tag(StartupConstants.LogTag.REGION_DETECTION).d("地区检测完成，结果: $regionCode")
 106 | 
 107 |             // 如果版本检查还在进行，用检测到的区域重新检查
 108 |             if (_loadingState.value == LoadingState.CheckingVersion) {
 109 |                 performVersionCheck(regionCode)
 110 |             }
 111 |         } catch (e: Exception) {
 112 |             Timber.tag(StartupConstants.LogTag.REGION_DETECTION).d("地区检测失败，使用默认区域: ${e.message}")
 113 |             // 静默失败，不影响用户体验
 114 |         }
 115 |     }
 116 | 
 117 |     /**
 118 |      * 执行版本检查
 119 |      * @param regionCode 区域代码，默认为国际区域
 120 |      */
 121 |     private suspend fun performVersionCheck(regionCode: String) {
 122 |         try {
 123 |             Timber.tag(StartupConstants.LogTag.VERSION_CHECK).d("开始版本检查，区域: $regionCode")
 124 |             _loadingState.value = LoadingState.CheckingVersion
 125 | 
 126 |             val versionResult = checkVersionAccessUseCase.execute(regionCode)
 127 | 
 128 |             // 使用扩展函数简化处理
 129 |             versionResult.fold(
 130 |                 onSuccess = { access ->
 131 |                     when (access) {
 132 |                         is VersionAccessResult.Allowed -> {
 133 |                             Timber.tag(StartupConstants.LogTag.VERSION_CHECK).d("版本检查通过，准备跳转")
 134 |                             _loadingState.value = LoadingState.Ready
 135 |                             // Loading完成后1秒自动执行用户ID分配
 136 |                             scheduleUserIdAssignment()
 137 |                         }
 138 |                         is VersionAccessResult.Locked -> {
 139 |                             Timber.tag(StartupConstants.LogTag.VERSION_CHECK).w("应用被锁定: ${access.reason}")
 140 |                             _loadingState.value = LoadingState.AppLocked(access)
 141 |                         }
 142 |                         is VersionAccessResult.ForceUpdate -> {
 143 |                             Timber
 144 |                                 .tag(
 145 |                                     StartupConstants.LogTag.VERSION_CHECK,
 146 |                                 ).w("需要强制更新: ${access.currentVersion} -> ${access.requiredVersion}")
 147 |                             _loadingState.value = LoadingState.ForceUpdate(access)
 148 |                         }
 149 |                     }
 150 |                 },
 151 |                 onError = { error ->
 152 |                     Timber
 153 |                         .tag(
 154 |                             StartupConstants.LogTag.VERSION_CHECK,
 155 |                         ).w("版本检查失败，允许继续使用: ${error.operationName}")
 156 |                     _loadingState.value = LoadingState.Ready
 157 |                     // Loading完成后1秒自动执行用户ID分配
 158 |                     scheduleUserIdAssignment()
 159 |                 },
 160 |             )
 161 |         } catch (e: Exception) {
 162 |             Timber.tag(StartupConstants.LogTag.VERSION_CHECK).e(e, "版本检查异常，允许继续使用")
 163 |             // 优雅降级：出错时允许继续使用
 164 |             _loadingState.value = LoadingState.Ready
 165 |             // Loading完成后1秒自动执行用户ID分配
 166 |             scheduleUserIdAssignment()
 167 |         }
 168 |     }
 169 | 
 170 |     /**
 171 |      * 调度用户ID分配任务
 172 |      * Loading完成后1秒自动执行
 173 |      */
 174 |     private fun scheduleUserIdAssignment() {
 175 |         viewModelScope.launch {
 176 |             try {
 177 |                 // 等待1秒
 178 |                 kotlinx.coroutines.delay(1000)
 179 |                 Timber.tag(StartupConstants.LogTag.USER_ID_ASSIGNMENT).d("开始自动用户ID分配...")
 180 |                 ensureUserIdAssigned()
 181 |             } catch (e: Exception) {
 182 |                 Timber.tag(StartupConstants.LogTag.USER_ID_ASSIGNMENT).e(e, "自动用户ID分配失败")
 183 |             }
 184 |         }
 185 |     }
 186 | 
 187 |     /**
 188 |      * 确保用户已分配userid（现在会在Loading完成后1秒自动执行）
 189 |      * 也可以手动调用以确保用户ID存在
 190 |      */
 191 |     suspend fun ensureUserIdAssigned(): Boolean {
 192 |         return try {
 193 |             Timber.tag(StartupConstants.LogTag.USER_ID_ASSIGNMENT).d("开始检查用户ID分配状态...")
 194 | 
 195 |             // 检查是否已经有用户ID
 196 |             val authStateResult = authRepository.getCurrentUser().first()
 197 | 
 198 |             // 使用扩展函数简化处理
 199 |             var userExists = false
 200 |             authStateResult.fold(
 201 |                 onSuccess = { currentUser: AuthUser? ->
 202 |                     if (currentUser != null && currentUser.uid.isNotEmpty()) {
 203 |                         Timber
 204 |                             .tag(
 205 |                                 StartupConstants.LogTag.USER_ID_ASSIGNMENT,
 206 |                             ).d("用户已存在，userid: ${currentUser.uid}")
 207 |                         userExists = true
 208 |                     }
 209 |                 },
 210 |                 onError = { error ->
 211 |                     Timber.tag(StartupConstants.LogTag.USER_ID_ASSIGNMENT).w("检查用户状态失败: ${error.message}")
 212 |                 },
 213 |             )
 214 | 
 215 |             if (userExists) return true
 216 | 
 217 |             // 如果没有用户ID，创建匿名用户
 218 |             Timber.tag(StartupConstants.LogTag.USER_ID_ASSIGNMENT).d("未找到现有用户，创建匿名用户...")
 219 |             val loginResult = loginAnonymouslyUseCase()
 220 | 
 221 |             var createSuccess = false
 222 |             loginResult.fold(
 223 |                 onSuccess = { anonymousUser: AuthUser ->
 224 |                     Timber
 225 |                         .tag(
 226 |                             StartupConstants.LogTag.USER_ID_ASSIGNMENT,
 227 |                         ).d("匿名用户创建成功，userid: ${anonymousUser.uid}")
 228 |                     createSuccess = true
 229 |                     // 用户ID分配成功后，启用延迟的功能
 230 |                     enableDelayedFeatures()
 231 |                 },
 232 |                 onError = { error ->
 233 |                     Timber.tag(StartupConstants.LogTag.USER_ID_ASSIGNMENT).e("创建匿名用户失败: ${error.message}")
 234 |                 },
 235 |             )
 236 | 
 237 |             createSuccess
 238 |         } catch (e: Exception) {
 239 |             Timber.tag(StartupConstants.LogTag.USER_ID_ASSIGNMENT).e(e, "用户ID分配过程中发生异常")
 240 |             false
 241 |         }
 242 |     }
 243 | 
 244 |     /**
 245 |      * 启用延迟的功能
 246 |      * 在用户ID分配成功后调用
 247 |      */
 248 |     private fun enableDelayedFeatures() {
 249 |         viewModelScope.launch {
 250 |             try {
 251 |                 Timber.tag(StartupConstants.LogTag.USER_ID_ASSIGNMENT).d("开始启用延迟功能...")
 252 | 
 253 |                 // 注意：网络监控和后台同步等功能将在需要时自动启动
 254 |                 // 这里主要是为了记录用户ID分配成功的事件
 255 | 
 256 |                 Timber.tag(StartupConstants.LogTag.USER_ID_ASSIGNMENT).d("延迟功能启用完成")
 257 |             } catch (e: Exception) {
 258 |                 Timber.tag(StartupConstants.LogTag.USER_ID_ASSIGNMENT).e(e, "启用延迟功能失败")
 259 |             }
 260 |         }
 261 |     }
 262 | 
 263 |     override fun onCleared() {
 264 |         super.onCleared()
 265 |         regionDetectionJob?.cancel()
 266 |     }
 267 | }

```

`main\kotlin\com\example\gymbro\app\loading\StartupConstants.kt`:

```kt
   1 | package com.example.gymbro.app.loading
   2 | 
   3 | /**
   4 |  * 启动流程相关常量定义
   5 |  * 统一管理所有魔数，便于维护和调整
   6 |  */
   7 | object StartupConstants {
   8 | 
   9 |     /**
  10 |      * 动画相关常量
  11 |      */
  12 |     object Animation {
  13 |         /** 彩虹渐变动画时长(毫秒) - 基于用户体验测试的最佳值 */
  14 |         const val RAINBOW_GRADIENT_DURATION_MS = 3000
  15 | 
  16 |         /** 渐变宽度 - 确保在各种屏幕尺寸下的流畅效果 */
  17 |         const val GRADIENT_WIDTH = 800f
  18 | 
  19 |         /** Loading完成后的延迟时间(毫秒) - 让用户感知到完成状态 */
  20 |         const val LOADING_FINISH_DELAY_MS = 300L
  21 |     }
  22 | 
  23 |     /**
  24 |      * 网络和超时相关常量
  25 |      */
  26 |     object Network {
  27 |         /** 地区检测超时时间(毫秒) - 平衡准确性和用户体验 */
  28 |         const val REGION_DETECTION_TIMEOUT_MS = 3000L
  29 | 
  30 |         /** 版本检查超时时间(毫秒) */
  31 |         const val VERSION_CHECK_TIMEOUT_MS = 5000L
  32 |     }
  33 | 
  34 |     /**
  35 |      * 字体相关常量
  36 |      */
  37 |     object Font {
  38 |         /** 主Logo字体大小 */
  39 |         const val LOGO_FONT_SIZE_SP = 150
  40 |     }
  41 | 
  42 |     /**
  43 |      * 区域代码常量
  44 |      */
  45 |     object Region {
  46 |         /** 国际区域代码 */
  47 |         const val INTERNATIONAL = "INTERNATIONAL"
  48 | 
  49 |         /** 中国区域代码 */
  50 |         const val CN = "CN"
  51 | 
  52 |         /** 默认区域代码 - 当检测失败时使用 */
  53 |         const val DEFAULT = INTERNATIONAL
  54 |     }
  55 | 
  56 |     /**
  57 |      * 日志标签
  58 |      */
  59 |     object LogTag {
  60 |         const val STARTUP = "Startup"
  61 |         const val REGION_DETECTION = "RegionDetection"
  62 |         const val VERSION_CHECK = "VersionCheck"
  63 |         const val USER_ID_ASSIGNMENT = "UserIdAssignment"
  64 |     }
  65 | }

```

`main\kotlin\com\example\gymbro\app\loading\components\RainbowText.kt`:

```kt
   1 | package com.example.gymbro.app.loading.components
   2 | 
   3 | import androidx.compose.animation.core.LinearEasing
   4 | import androidx.compose.animation.core.RepeatMode
   5 | import androidx.compose.animation.core.animateFloat
   6 | import androidx.compose.animation.core.infiniteRepeatable
   7 | import androidx.compose.animation.core.rememberInfiniteTransition
   8 | import androidx.compose.animation.core.tween
   9 | import androidx.compose.material3.MaterialTheme
  10 | import androidx.compose.material3.Text
  11 | import androidx.compose.runtime.Composable
  12 | import androidx.compose.runtime.getValue
  13 | import androidx.compose.runtime.remember
  14 | import androidx.compose.ui.Modifier
  15 | import androidx.compose.ui.geometry.Offset
  16 | import androidx.compose.ui.graphics.Brush
  17 | import androidx.compose.ui.graphics.Color
  18 | import androidx.compose.ui.graphics.TileMode
  19 | import androidx.compose.ui.text.TextStyle
  20 | import androidx.compose.ui.text.font.FontFamily
  21 | import androidx.compose.ui.text.font.FontWeight
  22 | import androidx.compose.ui.unit.TextUnit
  23 | import androidx.compose.ui.unit.sp
  24 | import com.example.gymbro.R
  25 | import com.example.gymbro.app.loading.StartupConstants
  26 | 
  27 | /**
  28 |  * 安全加载字体，失败时降级到默认字体
  29 |  */
  30 | @Composable
  31 | private fun rememberSafeFontFamily(fontFamily: FontFamily?): FontFamily {
  32 |     return remember {
  33 |         try {
  34 |             fontFamily ?: FontFamily(
  35 |                 androidx.compose.ui.text.font.Font(R.font.font_maple_mono_bold, FontWeight.Bold),
  36 |             )
  37 |         } catch (e: Exception) {
  38 |             FontFamily.Default
  39 |         }
  40 |     }
  41 | }
  42 | 
  43 | /**
  44 |  * 根据主题获取彩虹色彩
  45 |  */
  46 | @Composable
  47 | private fun rememberRainbowColors(): List<Color> {
  48 |     val isDarkTheme = MaterialTheme.colorScheme.surface == MaterialTheme.colorScheme.surfaceVariant
  49 |     return remember(isDarkTheme) {
  50 |         if (isDarkTheme) {
  51 |             listOf(
  52 |                 Color(0xFFFF6B6B),
  53 |                 Color(0xFFFFB347),
  54 |                 Color(0xFFFFEB3B),
  55 |                 Color(0xFF66BB6A),
  56 |                 Color(0xFF42A5F5),
  57 |                 Color(0xFF9575CD),
  58 |                 Color(0xFFEC407A),
  59 |                 Color(0xFFC0C0C0),
  60 |             )
  61 |         } else {
  62 |             listOf(
  63 |                 Color(0xFFE53935),
  64 |                 Color(0xFFFF9800),
  65 |                 Color(0xFFFFC107),
  66 |                 Color(0xFF4CAF50),
  67 |                 Color(0xFF2196F3),
  68 |                 Color(0xFF673AB7),
  69 |                 Color(0xFFE91E63),
  70 |                 Color(0xFF808080),
  71 |             )
  72 |         }
  73 |     }
  74 | }
  75 | 
  76 | /**
  77 |  * 创建彩虹渐变动画效果
  78 |  */
  79 | @Composable
  80 | private fun rememberRainbowBrush(
  81 |     rainbowColors: List<Color>,
  82 |     durationMillis: Int,
  83 | ): Brush {
  84 |     val infiniteTransition = rememberInfiniteTransition(label = "RainbowTextTransition")
  85 | 
  86 |     val gradientShift by infiniteTransition.animateFloat(
  87 |         initialValue = 0f,
  88 |         targetValue = 1f,
  89 |         animationSpec = infiniteRepeatable(
  90 |             animation = tween(durationMillis = durationMillis, easing = LinearEasing),
  91 |             repeatMode = RepeatMode.Restart,
  92 |         ),
  93 |         label = "RainbowGradientShift",
  94 |     )
  95 | 
  96 |     return remember(gradientShift, rainbowColors) {
  97 |         val gradientWidth = StartupConstants.Animation.GRADIENT_WIDTH
  98 |         Brush.linearGradient(
  99 |             colors = rainbowColors,
 100 |             start = Offset(-gradientWidth * gradientShift, 0f),
 101 |             end = Offset(gradientWidth * (1f - gradientShift), 0f),
 102 |             tileMode = TileMode.Repeated,
 103 |         )
 104 |     }
 105 | }
 106 | 
 107 | /**
 108 |  * 可复用的彩虹文字组件
 109 |  * 支持自定义字符、字体大小、动画时长等参数
 110 |  *
 111 |  * @param text 要显示的文字
 112 |  * @param modifier Modifier
 113 |  * @param fontSize 字体大小
 114 |  * @param fontFamily 字体族，默认使用Maple字体，失败时降级到系统默认字体
 115 |  * @param fontWeight 字体粗细
 116 |  * @param durationMillis 动画时长
 117 |  */
 118 | @Composable
 119 | fun RainbowText(
 120 |     text: String,
 121 |     modifier: Modifier = Modifier,
 122 |     fontSize: TextUnit = StartupConstants.Font.LOGO_FONT_SIZE_SP.sp,
 123 |     fontFamily: FontFamily? = null,
 124 |     fontWeight: FontWeight = FontWeight.Bold,
 125 |     durationMillis: Int = StartupConstants.Animation.RAINBOW_GRADIENT_DURATION_MS,
 126 | ) {
 127 |     val safeFontFamily = rememberSafeFontFamily(fontFamily)
 128 |     val rainbowColors = rememberRainbowColors()
 129 |     val rainbowBrush = rememberRainbowBrush(rainbowColors, durationMillis)
 130 | 
 131 |     Text(
 132 |         text = text,
 133 |         modifier = modifier,
 134 |         fontFamily = safeFontFamily,
 135 |         fontWeight = fontWeight,
 136 |         fontSize = fontSize,
 137 |         style = TextStyle(brush = rainbowBrush),
 138 |     )
 139 | }
 140 | 
 141 | /**
 142 |  * 专门用于Logo的彩虹文字组件
 143 |  * 使用预设的样式和大小
 144 |  */
 145 | @Composable
 146 | fun RainbowLogo(
 147 |     modifier: Modifier = Modifier,
 148 |     text: String = "G",
 149 | ) {
 150 |     RainbowText(
 151 |         text = text,
 152 |         modifier = modifier,
 153 |         fontSize = StartupConstants.Font.LOGO_FONT_SIZE_SP.sp,
 154 |         fontWeight = FontWeight.Bold,
 155 |         durationMillis = StartupConstants.Animation.RAINBOW_GRADIENT_DURATION_MS,
 156 |     )
 157 | }

```

`main\kotlin\com\example\gymbro\app\notification\NotificationChannelManager.kt`:

```kt
   1 | package com.example.gymbro.app.notification
   2 | 
   3 | import android.app.NotificationChannel
   4 | import android.app.NotificationManager
   5 | import android.content.Context
   6 | import android.os.Build
   7 | import androidx.core.app.NotificationManagerCompat
   8 | import dagger.hilt.android.qualifiers.ApplicationContext
   9 | import timber.log.Timber
  10 | import javax.inject.Inject
  11 | import javax.inject.Singleton
  12 | 
  13 | /**
  14 |  * 通知渠道管理器
  15 |  * 负责创建和管理应用的所有通知渠道
  16 |  */
  17 | @Singleton
  18 | class NotificationChannelManager @Inject constructor(
  19 |     @ApplicationContext private val context: Context,
  20 | ) {
  21 | 
  22 |     companion object {
  23 |         // 休息计时器通知渠道
  24 |         const val REST_TIMER_CHANNEL_ID = "rest_timer_channel"
  25 |         const val REST_TIMER_CHANNEL_NAME = "休息计时器"
  26 |         const val REST_TIMER_CHANNEL_DESCRIPTION = "训练休息计时器通知"
  27 | 
  28 |         // 训练提醒通知渠道
  29 |         const val WORKOUT_REMINDER_CHANNEL_ID = "workout_reminder_channel"
  30 |         const val WORKOUT_REMINDER_CHANNEL_NAME = "训练提醒"
  31 |         const val WORKOUT_REMINDER_CHANNEL_DESCRIPTION = "训练计划和提醒通知"
  32 |     }
  33 | 
  34 |     /**
  35 |      * 初始化所有通知渠道
  36 |      * 应在应用启动时调用
  37 |      */
  38 |     fun initializeChannels() {
  39 |         if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
  40 |             createRestTimerChannel()
  41 |             createWorkoutReminderChannel()
  42 |             Timber.d("通知渠道初始化完成")
  43 |         }
  44 |     }
  45 | 
  46 |     /**
  47 |      * 创建休息计时器通知渠道
  48 |      */
  49 |     private fun createRestTimerChannel() {
  50 |         if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
  51 |             val channel = NotificationChannel(
  52 |                 REST_TIMER_CHANNEL_ID,
  53 |                 REST_TIMER_CHANNEL_NAME,
  54 |                 NotificationManager.IMPORTANCE_LOW, // 低重要性，不打断用户
  55 |             ).apply {
  56 |                 description = REST_TIMER_CHANNEL_DESCRIPTION
  57 |                 setShowBadge(false) // 不显示角标
  58 |                 enableLights(false) // 不闪灯
  59 |                 enableVibration(false) // 不震动
  60 |                 setSound(null, null) // 不播放声音
  61 |             }
  62 | 
  63 |             val notificationManager = NotificationManagerCompat.from(context)
  64 |             notificationManager.createNotificationChannel(channel)
  65 |             Timber.d("休息计时器通知渠道创建成功")
  66 |         }
  67 |     }
  68 | 
  69 |     /**
  70 |      * 创建训练提醒通知渠道
  71 |      */
  72 |     private fun createWorkoutReminderChannel() {
  73 |         if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
  74 |             val channel = NotificationChannel(
  75 |                 WORKOUT_REMINDER_CHANNEL_ID,
  76 |                 WORKOUT_REMINDER_CHANNEL_NAME,
  77 |                 NotificationManager.IMPORTANCE_DEFAULT, // 默认重要性
  78 |             ).apply {
  79 |                 description = WORKOUT_REMINDER_CHANNEL_DESCRIPTION
  80 |                 setShowBadge(true)
  81 |                 enableLights(true)
  82 |                 enableVibration(true)
  83 |             }
  84 | 
  85 |             val notificationManager = NotificationManagerCompat.from(context)
  86 |             notificationManager.createNotificationChannel(channel)
  87 |             Timber.d("训练提醒通知渠道创建成功")
  88 |         }
  89 |     }
  90 | 
  91 |     /**
  92 |      * 检查通知权限
  93 |      */
  94 |     fun hasNotificationPermission(): Boolean {
  95 |         return NotificationManagerCompat.from(context).areNotificationsEnabled()
  96 |     }
  97 | 
  98 |     /**
  99 |      * 获取通知权限状态描述
 100 |      */
 101 |     fun getNotificationPermissionStatus(): String {
 102 |         return if (hasNotificationPermission()) {
 103 |             "通知权限已授予"
 104 |         } else {
 105 |             "需要授予通知权限"
 106 |         }
 107 |     }
 108 | }

```

`main\kotlin\com\example\gymbro\app\version\AppLockedScreen.kt`:

```kt
   1 | package com.example.gymbro.app.version
   2 | 
   3 | import androidx.compose.foundation.background
   4 | import androidx.compose.foundation.layout.*
   5 | import androidx.compose.foundation.shape.RoundedCornerShape
   6 | import androidx.compose.material.icons.Icons
   7 | import androidx.compose.material.icons.filled.Lock
   8 | import androidx.compose.material3.*
   9 | import androidx.compose.runtime.Composable
  10 | import androidx.compose.ui.Alignment
  11 | import androidx.compose.ui.Modifier
  12 | import androidx.compose.ui.text.font.FontWeight
  13 | import androidx.compose.ui.text.style.TextAlign
  14 | import androidx.compose.ui.unit.dp
  15 | import androidx.compose.ui.unit.sp
  16 | import com.example.gymbro.core.ui.text.UiText
  17 | import com.example.gymbro.domain.shared.base.version.VersionAccessResult
  18 | 
  19 | /**
  20 |  * 应用锁定页面
  21 |  *
  22 |  * 当应用在特定区域被锁定时显示此页面
  23 |  */
  24 | @Composable
  25 | fun AppLockedScreen(
  26 |     lockInfo: VersionAccessResult.Locked,
  27 |     modifier: Modifier = Modifier,
  28 | ) {
  29 |     Box(
  30 |         modifier = modifier
  31 |             .fillMaxSize()
  32 |             .background(MaterialTheme.colorScheme.background),
  33 |         contentAlignment = Alignment.Center,
  34 |     ) {
  35 |         Card(
  36 |             modifier = Modifier
  37 |                 .fillMaxWidth()
  38 |                 .padding(24.dp),
  39 |             shape = RoundedCornerShape(16.dp),
  40 |             colors = CardDefaults.cardColors(
  41 |                 containerColor = MaterialTheme.colorScheme.surface,
  42 |             ),
  43 |             elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
  44 |         ) {
  45 |             Column(
  46 |                 modifier = Modifier
  47 |                     .fillMaxWidth()
  48 |                     .padding(24.dp),
  49 |                 horizontalAlignment = Alignment.CenterHorizontally,
  50 |                 verticalArrangement = Arrangement.spacedBy(16.dp),
  51 |             ) {
  52 |                 // 锁定图标
  53 |                 Icon(
  54 |                     imageVector = Icons.Default.Lock,
  55 |                     contentDescription = "应用锁定",
  56 |                     modifier = Modifier.size(64.dp),
  57 |                     tint = MaterialTheme.colorScheme.error,
  58 |                 )
  59 | 
  60 |                 // 标题
  61 |                 Text(
  62 |                     text = "应用暂时不可用",
  63 |                     style = MaterialTheme.typography.headlineMedium,
  64 |                     fontWeight = FontWeight.Bold,
  65 |                     color = MaterialTheme.colorScheme.onSurface,
  66 |                     textAlign = TextAlign.Center,
  67 |                 )
  68 | 
  69 |                 // 锁定原因
  70 |                 Text(
  71 |                     text = lockInfo.reason.asString(),
  72 |                     style = MaterialTheme.typography.bodyLarge,
  73 |                     color = MaterialTheme.colorScheme.onSurfaceVariant,
  74 |                     textAlign = TextAlign.Center,
  75 |                     lineHeight = 24.sp,
  76 |                 )
  77 | 
  78 |                 // 联系信息（如果有）
  79 |                 lockInfo.contactInfo?.let { contact ->
  80 |                     HorizontalDivider(
  81 |                         modifier = Modifier.padding(vertical = 8.dp),
  82 |                         color = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f),
  83 |                     )
  84 | 
  85 |                     Text(
  86 |                         text = "如有疑问，请联系：",
  87 |                         style = MaterialTheme.typography.bodyMedium,
  88 |                         color = MaterialTheme.colorScheme.onSurfaceVariant,
  89 |                     )
  90 | 
  91 |                     Text(
  92 |                         text = contact,
  93 |                         style = MaterialTheme.typography.bodyMedium,
  94 |                         color = MaterialTheme.colorScheme.primary,
  95 |                         fontWeight = FontWeight.Medium,
  96 |                     )
  97 |                 }
  98 | 
  99 |                 Spacer(modifier = Modifier.height(8.dp))
 100 | 
 101 |                 // 说明文字
 102 |                 Text(
 103 |                     text = "感谢您的理解与支持",
 104 |                     style = MaterialTheme.typography.bodySmall,
 105 |                     color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
 106 |                     textAlign = TextAlign.Center,
 107 |                 )
 108 |             }
 109 |         }
 110 |     }
 111 | }
 112 | 
 113 | /**
 114 |  * UiText扩展函数，用于在Composable中显示文本
 115 |  */
 116 | @Composable
 117 | private fun UiText.asString(): String {
 118 |     return when (this) {
 119 |         is UiText.DynamicString -> this.value
 120 |         is UiText.StringResource -> "应用在当前区域暂时不可用" // 简化处理
 121 |         else -> "应用暂时不可用"
 122 |     }
 123 | }

```

`main\kotlin\com\example\gymbro\app\version\ForceUpdateScreen.kt`:

```kt
   1 | package com.example.gymbro.app.version
   2 | 
   3 | import android.content.Intent
   4 | import android.net.Uri
   5 | import androidx.compose.foundation.background
   6 | import androidx.compose.foundation.layout.*
   7 | import androidx.compose.foundation.shape.RoundedCornerShape
   8 | import androidx.compose.material.icons.Icons
   9 | import androidx.compose.material.icons.filled.SystemUpdate
  10 | import androidx.compose.material3.*
  11 | import androidx.compose.runtime.Composable
  12 | import androidx.compose.ui.Alignment
  13 | import androidx.compose.ui.Modifier
  14 | import androidx.compose.ui.platform.LocalContext
  15 | import androidx.compose.ui.text.font.FontWeight
  16 | import androidx.compose.ui.text.style.TextAlign
  17 | import androidx.compose.ui.unit.dp
  18 | import androidx.compose.ui.unit.sp
  19 | import com.example.gymbro.core.ui.text.UiText
  20 | import com.example.gymbro.domain.shared.base.version.VersionAccessResult
  21 | 
  22 | /**
  23 |  * 强制更新页面
  24 |  *
  25 |  * 当应用版本过低需要强制更新时显示此页面
  26 |  */
  27 | @Composable
  28 | fun ForceUpdateScreen(
  29 |     updateInfo: VersionAccessResult.ForceUpdate,
  30 |     modifier: Modifier = Modifier,
  31 | ) {
  32 |     val context = LocalContext.current
  33 | 
  34 |     Box(
  35 |         modifier = modifier
  36 |             .fillMaxSize()
  37 |             .background(MaterialTheme.colorScheme.background),
  38 |         contentAlignment = Alignment.Center,
  39 |     ) {
  40 |         Card(
  41 |             modifier = Modifier
  42 |                 .fillMaxWidth()
  43 |                 .padding(24.dp),
  44 |             shape = RoundedCornerShape(16.dp),
  45 |             colors = CardDefaults.cardColors(
  46 |                 containerColor = MaterialTheme.colorScheme.surface,
  47 |             ),
  48 |             elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
  49 |         ) {
  50 |             Column(
  51 |                 modifier = Modifier
  52 |                     .fillMaxWidth()
  53 |                     .padding(24.dp),
  54 |                 horizontalAlignment = Alignment.CenterHorizontally,
  55 |                 verticalArrangement = Arrangement.spacedBy(16.dp),
  56 |             ) {
  57 |                 // 更新图标
  58 |                 Icon(
  59 |                     imageVector = Icons.Default.SystemUpdate,
  60 |                     contentDescription = "需要更新",
  61 |                     modifier = Modifier.size(64.dp),
  62 |                     tint = MaterialTheme.colorScheme.primary,
  63 |                 )
  64 | 
  65 |                 // 标题
  66 |                 Text(
  67 |                     text = "需要更新应用",
  68 |                     style = MaterialTheme.typography.headlineMedium,
  69 |                     fontWeight = FontWeight.Bold,
  70 |                     color = MaterialTheme.colorScheme.onSurface,
  71 |                     textAlign = TextAlign.Center,
  72 |                 )
  73 | 
  74 |                 // 更新消息
  75 |                 Text(
  76 |                     text = updateInfo.updateMessage.asString(),
  77 |                     style = MaterialTheme.typography.bodyLarge,
  78 |                     color = MaterialTheme.colorScheme.onSurfaceVariant,
  79 |                     textAlign = TextAlign.Center,
  80 |                     lineHeight = 24.sp,
  81 |                 )
  82 | 
  83 |                 // 版本信息
  84 |                 VersionInfoCard(
  85 |                     currentVersion = updateInfo.currentVersion,
  86 |                     requiredVersion = updateInfo.requiredVersion,
  87 |                 )
  88 | 
  89 |                 Spacer(modifier = Modifier.height(8.dp))
  90 | 
  91 |                 // 更新按钮
  92 |                 UpdateButton(
  93 |                     downloadUrl = updateInfo.downloadUrl,
  94 |                     modifier = Modifier
  95 |                         .fillMaxWidth()
  96 |                         .height(48.dp),
  97 |                 )
  98 | 
  99 |                 // 说明文字
 100 |                 Text(
 101 |                     text = "更新后即可正常使用所有功能",
 102 |                     style = MaterialTheme.typography.bodySmall,
 103 |                     color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
 104 |                     textAlign = TextAlign.Center,
 105 |                 )
 106 |             }
 107 |         }
 108 |     }
 109 | }
 110 | 
 111 | /**
 112 |  * 版本信息卡片组件
 113 |  */
 114 | @Composable
 115 | private fun VersionInfoCard(
 116 |     currentVersion: String,
 117 |     requiredVersion: String,
 118 |     modifier: Modifier = Modifier,
 119 | ) {
 120 |     Card(
 121 |         modifier = modifier.fillMaxWidth(),
 122 |         colors = CardDefaults.cardColors(
 123 |             containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f),
 124 |         ),
 125 |         shape = RoundedCornerShape(8.dp),
 126 |     ) {
 127 |         Column(
 128 |             modifier = Modifier
 129 |                 .fillMaxWidth()
 130 |                 .padding(16.dp),
 131 |             verticalArrangement = Arrangement.spacedBy(8.dp),
 132 |         ) {
 133 |             VersionRow(label = "当前版本：", version = currentVersion, isError = true)
 134 |             VersionRow(label = "要求版本：", version = requiredVersion, isError = false)
 135 |         }
 136 |     }
 137 | }
 138 | 
 139 | /**
 140 |  * 版本信息行组件
 141 |  */
 142 | @Composable
 143 | private fun VersionRow(
 144 |     label: String,
 145 |     version: String,
 146 |     isError: Boolean,
 147 |     modifier: Modifier = Modifier,
 148 | ) {
 149 |     Row(
 150 |         modifier = modifier.fillMaxWidth(),
 151 |         horizontalArrangement = Arrangement.SpaceBetween,
 152 |     ) {
 153 |         Text(
 154 |             text = label,
 155 |             style = MaterialTheme.typography.bodyMedium,
 156 |             color = MaterialTheme.colorScheme.onSurfaceVariant,
 157 |         )
 158 |         Text(
 159 |             text = version,
 160 |             style = MaterialTheme.typography.bodyMedium,
 161 |             fontWeight = FontWeight.Medium,
 162 |             color = if (isError) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.primary,
 163 |         )
 164 |     }
 165 | }
 166 | 
 167 | /**
 168 |  * 更新按钮组件
 169 |  */
 170 | @Composable
 171 | private fun UpdateButton(
 172 |     downloadUrl: String,
 173 |     modifier: Modifier = Modifier,
 174 | ) {
 175 |     val context = LocalContext.current
 176 | 
 177 |     Button(
 178 |         onClick = { handleUpdateClick(context, downloadUrl) },
 179 |         modifier = modifier,
 180 |         enabled = downloadUrl.isNotBlank(),
 181 |         colors = ButtonDefaults.buttonColors(
 182 |             containerColor = MaterialTheme.colorScheme.primary,
 183 |         ),
 184 |         shape = RoundedCornerShape(8.dp),
 185 |     ) {
 186 |         Text(
 187 |             text = "立即更新",
 188 |             style = MaterialTheme.typography.bodyLarge,
 189 |             fontWeight = FontWeight.Medium,
 190 |         )
 191 |     }
 192 | }
 193 | 
 194 | /**
 195 |  * 处理更新按钮点击事件
 196 |  */
 197 | private fun handleUpdateClick(context: android.content.Context, downloadUrl: String) {
 198 |     if (downloadUrl.isNotBlank()) {
 199 |         try {
 200 |             val intent = Intent(Intent.ACTION_VIEW, Uri.parse(downloadUrl))
 201 |             context.startActivity(intent)
 202 |         } catch (e: Exception) {
 203 |             android.util.Log.e("ForceUpdateScreen", "无法打开下载链接: $downloadUrl", e)
 204 |             // 可以在这里添加Toast提示用户无法打开链接
 205 |         }
 206 |     }
 207 | }
 208 | 
 209 | /**
 210 |  * UiText扩展函数，用于在Composable中显示文本
 211 |  */
 212 | @Composable
 213 | private fun UiText.asString(): String {
 214 |     return when (this) {
 215 |         is UiText.DynamicString -> this.value
 216 |         is UiText.StringResource -> "请更新到最新版本以继续使用" // 简化处理
 217 |         else -> "请更新应用"
 218 |     }
 219 | }

```

`main\kotlin\com\example\gymbro\app\version\RegionDetectionManager.kt`:

```kt
   1 | package com.example.gymbro.app.version
   2 | 
   3 | import android.content.Context
   4 | import android.content.SharedPreferences
   5 | import com.example.gymbro.BuildConfig
   6 | import com.example.gymbro.core.error.types.ModernResult
   7 | import com.example.gymbro.core.region.RegionProvider
   8 | import com.example.gymbro.data.repository.region.RegionDetectionRepository
   9 | import com.example.gymbro.domain.shared.version.repository.VersionConfigRepository
  10 | import dagger.hilt.android.qualifiers.ApplicationContext
  11 | import kotlinx.coroutines.CoroutineScope
  12 | import kotlinx.coroutines.Dispatchers
  13 | import kotlinx.coroutines.SupervisorJob
  14 | import kotlinx.coroutines.flow.*
  15 | import kotlinx.datetime.Clock
  16 | import kotlinx.datetime.Instant
  17 | import timber.log.Timber
  18 | import java.util.Locale
  19 | import java.util.TimeZone
  20 | import javax.inject.Inject
  21 | import javax.inject.Singleton
  22 | import kotlin.time.Duration.Companion.hours
  23 | 
  24 | /**
  25 |  * 地区检测管理器
  26 |  *
  27 |  * 负责检测用户所在地区（CN vs 国际），并为整个应用提供统一的地区标准。
  28 |  * 所有模块的内容加载都应该参考此检测结果。
  29 |  *
  30 |  * 检测策略：
  31 |  * 1. IP地理位置检测（主要方式）
  32 |  * 2. 系统时区检测（备用方式）
  33 |  * 3. 系统语言检测（备用方式）
  34 |  * 4. 默认国际区域（兜底方案）
  35 |  */
  36 | @Singleton
  37 | class RegionDetectionManager @Inject constructor(
  38 |     @ApplicationContext private val context: Context,
  39 |     private val regionDetectionRepository: RegionDetectionRepository,
  40 |     private val versionConfigRepository: VersionConfigRepository,
  41 | ) : RegionProvider {
  42 | 
  43 |     companion object {
  44 |         private const val PREFS_NAME = "region_detection"
  45 |         private const val KEY_DETECTED_REGION = "detected_region"
  46 |         private const val KEY_DETECTION_TIME = "detection_time"
  47 |         private const val KEY_DETECTION_METHOD = "detection_method"
  48 |         private const val KEY_CONFIDENCE = "confidence"
  49 | 
  50 |         // 缓存有效期：24小时
  51 |         private const val CACHE_VALIDITY_HOURS = 24
  52 | 
  53 |         // 最小更新间隔（小时）
  54 |         private const val MIN_UPDATE_INTERVAL_HOURS = 24
  55 |     }
  56 | 
  57 |     private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
  58 | 
  59 |     private val _regionInfoState = MutableStateFlow<RegionInfo?>(null)
  60 | 
  61 |     // 为兼容性提供regionInfoState，但只在内部使用
  62 |     val regionInfoState: StateFlow<RegionInfo?> = _regionInfoState.asStateFlow()
  63 | 
  64 |     // 实现RegionProvider接口
  65 |     override val regionState: StateFlow<RegionProvider.UserRegion?> =
  66 |         _regionInfoState.map { regionInfo ->
  67 |             regionInfo?.region?.toProviderRegion()
  68 |         }.stateIn(
  69 |             scope = CoroutineScope(Dispatchers.Main + SupervisorJob()),
  70 |             started = SharingStarted.Lazily,
  71 |             initialValue = null,
  72 |         )
  73 | 
  74 |     /**
  75 |      * 内部用户地区枚举
  76 |      */
  77 |     enum class UserRegion {
  78 |         /** 中国大陆地区 */
  79 |         CN,
  80 | 
  81 |         /** 国际地区（包括港澳台） */
  82 |         INTERNATIONAL,
  83 | 
  84 |         ;
  85 | 
  86 |         /**
  87 |          * 转换为RegionProvider的UserRegion
  88 |          */
  89 |         fun toProviderRegion(): RegionProvider.UserRegion {
  90 |             return when (this) {
  91 |                 CN -> RegionProvider.UserRegion.CN
  92 |                 INTERNATIONAL -> RegionProvider.UserRegion.INTERNATIONAL
  93 |             }
  94 |         }
  95 |     }
  96 | 
  97 |     /**
  98 |      * 检测方法枚举
  99 |      */
 100 |     enum class DetectionMethod {
 101 |         /** IP地理位置检测 */
 102 |         IP_GEOLOCATION,
 103 | 
 104 |         /** 系统时区检测 */
 105 |         TIMEZONE,
 106 | 
 107 |         /** 系统语言检测 */
 108 |         LANGUAGE,
 109 | 
 110 |         /** 缓存结果 */
 111 |         CACHED,
 112 | 
 113 |         /** 默认兜底 */
 114 |         FALLBACK,
 115 |     }
 116 | 
 117 |     /**
 118 |      * 地区信息数据类
 119 |      */
 120 |     data class RegionInfo(
 121 |         val region: UserRegion,
 122 |         val detectedAt: Instant,
 123 |         val detectionMethod: DetectionMethod,
 124 |         val confidence: Float, // 0.0 - 1.0，检测置信度
 125 |     )
 126 | 
 127 |     /**
 128 |      * 初始化地区检测
 129 |      *
 130 |      * 应在应用启动时调用，会自动检测用户地区并缓存结果
 131 |      */
 132 |     suspend fun initializeRegionDetection() {
 133 |         try {
 134 |             Timber.d("开始初始化地区检测")
 135 | 
 136 |             // 首先尝试从缓存加载
 137 |             val cachedRegion = loadCachedRegion()
 138 |             if (cachedRegion != null && isCacheValid(cachedRegion)) {
 139 |                 Timber.d("使用缓存的地区信息: ${cachedRegion.region}")
 140 |                 val cachedWithMethod = cachedRegion.copy(detectionMethod = DetectionMethod.CACHED)
 141 |                 _regionInfoState.value = cachedWithMethod
 142 | 
 143 |                 return
 144 |             }
 145 | 
 146 |             // 缓存无效，重新检测
 147 |             val detectedRegion = performRegionDetection()
 148 |             _regionInfoState.value = detectedRegion
 149 | 
 150 |             // 保存到缓存
 151 |             saveRegionToCache(detectedRegion)
 152 | 
 153 |             Timber.i(
 154 |                 "地区检测完成: ${detectedRegion.region}, 方法: ${detectedRegion.detectionMethod}, 置信度: ${detectedRegion.confidence}",
 155 |             )
 156 |         } catch (e: Exception) {
 157 |             Timber.e(e, "地区检测失败，使用默认国际区域")
 158 |             val fallbackRegion = RegionInfo(
 159 |                 region = UserRegion.INTERNATIONAL,
 160 |                 detectedAt = Clock.System.now(),
 161 |                 detectionMethod = DetectionMethod.FALLBACK,
 162 |                 confidence = 0.1f,
 163 |             )
 164 |             _regionInfoState.value = fallbackRegion
 165 | 
 166 |             saveRegionToCache(fallbackRegion)
 167 |         }
 168 |     }
 169 | 
 170 |     /**
 171 |      * 执行地区检测
 172 |      */
 173 |     private suspend fun performRegionDetection(): RegionInfo {
 174 |         // 1. 优先尝试IP地理位置检测
 175 |         val ipResult = detectByIpGeolocation()
 176 |         if (ipResult.confidence > 0.8f) {
 177 |             return ipResult
 178 |         }
 179 | 
 180 |         // 2. 尝试时区检测
 181 |         val timezoneResult = detectByTimezone()
 182 |         if (timezoneResult.confidence > 0.7f) {
 183 |             return timezoneResult
 184 |         }
 185 | 
 186 |         // 3. 尝试语言检测
 187 |         val languageResult = detectByLanguage()
 188 |         if (languageResult.confidence > 0.6f) {
 189 |             return languageResult
 190 |         }
 191 | 
 192 |         // 4. 返回置信度最高的结果
 193 |         return listOf(ipResult, timezoneResult, languageResult)
 194 |             .maxByOrNull { it.confidence } ?: ipResult
 195 |     }
 196 | 
 197 |     /**
 198 |      * 基于IP地理位置检测地区
 199 |      */
 200 |     private suspend fun detectByIpGeolocation(): RegionInfo {
 201 |         return try {
 202 |             Timber.d("开始IP地理位置检测")
 203 | 
 204 |             val regionResult = regionDetectionRepository.detectCurrentRegion()
 205 |             when (regionResult) {
 206 |                 is ModernResult.Success -> {
 207 |                     val regionCode = regionResult.data
 208 |                     val region = if (regionCode == "CN") UserRegion.CN else UserRegion.INTERNATIONAL
 209 |                     val confidence = 0.9f // IP检测置信度最高
 210 | 
 211 |                     Timber.d("IP地理位置检测成功: $regionCode -> $region")
 212 |                     RegionInfo(
 213 |                         region = region,
 214 |                         detectedAt = Clock.System.now(),
 215 |                         detectionMethod = DetectionMethod.IP_GEOLOCATION,
 216 |                         confidence = confidence,
 217 |                     )
 218 |                 }
 219 |                 is ModernResult.Error -> {
 220 |                     Timber.w("IP地理位置检测失败: ${regionResult.error.message}")
 221 |                     RegionInfo(
 222 |                         region = UserRegion.INTERNATIONAL,
 223 |                         detectedAt = Clock.System.now(),
 224 |                         detectionMethod = DetectionMethod.IP_GEOLOCATION,
 225 |                         confidence = 0.1f, // 失败时置信度很低
 226 |                     )
 227 |                 }
 228 |                 is ModernResult.Loading -> {
 229 |                     // 不应该发生，但为了安全起见
 230 |                     RegionInfo(
 231 |                         region = UserRegion.INTERNATIONAL,
 232 |                         detectedAt = Clock.System.now(),
 233 |                         detectionMethod = DetectionMethod.IP_GEOLOCATION,
 234 |                         confidence = 0.1f,
 235 |                     )
 236 |                 }
 237 |             }
 238 |         } catch (e: Exception) {
 239 |             Timber.e(e, "IP地理位置检测异常")
 240 |             RegionInfo(
 241 |                 region = UserRegion.INTERNATIONAL,
 242 |                 detectedAt = Clock.System.now(),
 243 |                 detectionMethod = DetectionMethod.IP_GEOLOCATION,
 244 |                 confidence = 0.1f,
 245 |             )
 246 |         }
 247 |     }
 248 | 
 249 |     /**
 250 |      * 基于系统时区检测地区
 251 |      */
 252 |     private fun detectByTimezone(): RegionInfo {
 253 |         val timeZone = TimeZone.getDefault()
 254 |         val timeZoneId = timeZone.id
 255 | 
 256 |         Timber.d("检测到系统时区: $timeZoneId")
 257 | 
 258 |         val isChinaTimezone = timeZoneId.contains("Asia/Shanghai") ||
 259 |             timeZoneId.contains("Asia/Beijing") ||
 260 |             timeZoneId.contains("Asia/Chongqing") ||
 261 |             timeZoneId.contains("Asia/Harbin") ||
 262 |             timeZoneId.contains("Asia/Kashgar") ||
 263 |             timeZoneId.contains("Asia/Urumqi")
 264 | 
 265 |         val region = if (isChinaTimezone) UserRegion.CN else UserRegion.INTERNATIONAL
 266 |         val confidence = if (isChinaTimezone) 0.8f else 0.3f
 267 | 
 268 |         return RegionInfo(
 269 |             region = region,
 270 |             detectedAt = Clock.System.now(),
 271 |             detectionMethod = DetectionMethod.TIMEZONE,
 272 |             confidence = confidence,
 273 |         )
 274 |     }
 275 | 
 276 |     /**
 277 |      * 基于系统语言检测地区
 278 |      */
 279 |     private fun detectByLanguage(): RegionInfo {
 280 |         val locale = Locale.getDefault()
 281 |         val language = locale.language
 282 |         val country = locale.country
 283 | 
 284 |         Timber.d("检测到系统语言: $language, 国家: $country")
 285 | 
 286 |         val isChineseLocale = language == "zh" && country == "CN"
 287 |         val region = if (isChineseLocale) UserRegion.CN else UserRegion.INTERNATIONAL
 288 |         val confidence = if (isChineseLocale) 0.7f else 0.2f
 289 | 
 290 |         return RegionInfo(
 291 |             region = region,
 292 |             detectedAt = Clock.System.now(),
 293 |             detectionMethod = DetectionMethod.LANGUAGE,
 294 |             confidence = confidence,
 295 |         )
 296 |     }
 297 | 
 298 |     /**
 299 |      * 从缓存加载地区信息
 300 |      */
 301 |     private fun loadCachedRegion(): RegionInfo? {
 302 |         return try {
 303 |             val regionName = prefs.getString(KEY_DETECTED_REGION, null) ?: return null
 304 |             val detectionTime = prefs.getLong(KEY_DETECTION_TIME, 0L)
 305 |             val methodName = prefs.getString(KEY_DETECTION_METHOD, null) ?: return null
 306 |             val confidence = prefs.getFloat(KEY_CONFIDENCE, 0f)
 307 | 
 308 |             if (detectionTime == 0L) return null
 309 | 
 310 |             RegionInfo(
 311 |                 region = UserRegion.valueOf(regionName),
 312 |                 detectedAt = Instant.fromEpochMilliseconds(detectionTime),
 313 |                 detectionMethod = DetectionMethod.valueOf(methodName),
 314 |                 confidence = confidence,
 315 |             )
 316 |         } catch (e: Exception) {
 317 |             Timber.w(e, "加载缓存地区信息失败")
 318 |             null
 319 |         }
 320 |     }
 321 | 
 322 |     /**
 323 |      * 保存地区信息到缓存
 324 |      */
 325 |     private fun saveRegionToCache(regionInfo: RegionInfo) {
 326 |         try {
 327 |             prefs.edit()
 328 |                 .putString(KEY_DETECTED_REGION, regionInfo.region.name)
 329 |                 .putLong(KEY_DETECTION_TIME, regionInfo.detectedAt.toEpochMilliseconds())
 330 |                 .putString(KEY_DETECTION_METHOD, regionInfo.detectionMethod.name)
 331 |                 .putFloat(KEY_CONFIDENCE, regionInfo.confidence)
 332 |                 .apply()
 333 | 
 334 |             Timber.d("地区信息已保存到缓存")
 335 |         } catch (e: Exception) {
 336 |             Timber.w(e, "保存地区信息到缓存失败")
 337 |         }
 338 |     }
 339 | 
 340 |     /**
 341 |      * 检查缓存是否有效
 342 |      */
 343 |     private fun isCacheValid(regionInfo: RegionInfo): Boolean {
 344 |         val now = Clock.System.now()
 345 |         val cacheAge = now - regionInfo.detectedAt
 346 |         val maxAge = CACHE_VALIDITY_HOURS.hours
 347 | 
 348 |         return cacheAge < maxAge
 349 |     }
 350 | 
 351 |     // 实现RegionProvider接口方法
 352 |     override fun getCurrentRegion(): RegionProvider.UserRegion? {
 353 |         return _regionInfoState.value?.region?.toProviderRegion()
 354 |     }
 355 | 
 356 |     override fun isChinaRegion(): Boolean {
 357 |         return getCurrentRegion() == RegionProvider.UserRegion.CN
 358 |     }
 359 | 
 360 |     override fun isInternationalRegion(): Boolean {
 361 |         return getCurrentRegion() == RegionProvider.UserRegion.INTERNATIONAL
 362 |     }
 363 | 
 364 |     /**
 365 |      * 强制重新检测地区（开发用途）
 366 |      *
 367 |      * 清除缓存并重新执行检测流程
 368 |      */
 369 |     suspend fun forceRedetection() {
 370 |         Timber.d("强制重新检测地区")
 371 |         clearCache()
 372 |         initializeRegionDetection()
 373 |     }
 374 | 
 375 |     /**
 376 |      * 手动设置地区（开发用途）
 377 |      *
 378 |      * 仅在开发环境下使用，用于测试不同地区的功能
 379 |      */
 380 |     fun setRegionForDevelopment(region: UserRegion) {
 381 |         if (BuildConfig.DEBUG) {
 382 |             val devRegionInfo = RegionInfo(
 383 |                 region = region,
 384 |                 detectedAt = Clock.System.now(),
 385 |                 detectionMethod = DetectionMethod.FALLBACK,
 386 |                 confidence = 1.0f,
 387 |             )
 388 |             _regionInfoState.value = devRegionInfo
 389 |             saveRegionToCache(devRegionInfo)
 390 |             Timber.d("开发模式：手动设置地区为 $region")
 391 |         } else {
 392 |             Timber.w("生产环境不允许手动设置地区")
 393 |         }
 394 |     }
 395 | 
 396 |     /**
 397 |      * 清除缓存
 398 |      */
 399 |     private fun clearCache() {
 400 |         prefs.edit().clear().apply()
 401 |         Timber.d("地区检测缓存已清除")
 402 |     }
 403 | 
 404 |     private fun isUpdateNeeded(lastUpdate: Instant): Boolean {
 405 |         val now = Clock.System.now()
 406 |         val timeSinceUpdate = now - lastUpdate
 407 |         val minUpdateInterval = MIN_UPDATE_INTERVAL_HOURS.hours
 408 |         return timeSinceUpdate >= minUpdateInterval
 409 |     }
 410 | 
 411 |     private fun getCurrentTime(): Instant = Clock.System.now()
 412 | }

```

`main\kotlin\com\example\gymbro\di\AppConfigModule.kt`:

```kt
   1 | package com.example.gymbro.di
   2 | 
   3 | import com.example.gymbro.BuildConfig
   4 | import dagger.Module
   5 | import dagger.Provides
   6 | import dagger.hilt.InstallIn
   7 | import dagger.hilt.components.SingletonComponent
   8 | import javax.inject.Named
   9 | 
  10 | /**
  11 |  * 应用配置模块
  12 |  *
  13 |  * 提供从BuildConfig读取的配置参数，覆盖DI模块中的默认值
  14 |  */
  15 | @Module
  16 | @InstallIn(SingletonComponent::class)
  17 | object AppConfigModule {
  18 | 
  19 |     @Provides
  20 |     @Named("google_api_key")
  21 |     fun provideGoogleApiKey(): String {
  22 |         return BuildConfig.GOOGLE_API_KEY
  23 |     }
  24 | 
  25 |     @Provides
  26 |     @Named("google_base_url")
  27 |     fun provideGoogleBaseUrl(): String {
  28 |         return BuildConfig.GOOGLE_BASE_URL
  29 |     }
  30 | 
  31 |     @Provides
  32 |     @Named("openai_api_key")
  33 |     fun provideOpenaiApiKey(): String {
  34 |         return BuildConfig.OPENAI_API_KEY
  35 |     }
  36 | 
  37 |     @Provides
  38 |     @Named("deepseek_api_key")
  39 |     fun provideDeepseekApiKey(): String {
  40 |         return BuildConfig.DEEPSEEK_API_KEY
  41 |     }
  42 | 
  43 |     @Provides
  44 |     @Named("deepseek_base_url")
  45 |     fun provideDeepseekBaseUrl(): String {
  46 |         return BuildConfig.DEEPSEEK_BASE_URL
  47 |     }
  48 | 
  49 |     @Provides
  50 |     @Named("default_google_model")
  51 |     fun provideDefaultGoogleModel(): String {
  52 |         return BuildConfig.DEFAULT_GOOGLE_MODEL
  53 |     }
  54 | 
  55 |     @Provides
  56 |     @Named("default_deepseek_model")
  57 |     fun provideDefaultDeepseekModel(): String {
  58 |         return BuildConfig.DEFAULT_DEEPSEEK_MODEL
  59 |     }
  60 | 
  61 |     @Provides
  62 |     @Named("openai_default_model")
  63 |     fun provideOpenaiDefaultModel(): String {
  64 |         return BuildConfig.OPENAI_DEFAULT_MODEL
  65 |     }
  66 | 
  67 |     @Provides
  68 |     @Named("openai_base_url")
  69 |     fun provideOpenaiBaseUrl(): String {
  70 |         return BuildConfig.OPENAI_BASE_URL
  71 |     }
  72 | }

```

`main\kotlin\com\example\gymbro\network\AndroidNetworkMonitor.kt`:

```kt
   1 | package com.example.gymbro.network
   2 | 
   3 | import android.content.Context
   4 | import android.net.ConnectivityManager
   5 | import android.net.Network
   6 | import android.net.NetworkCapabilities
   7 | import android.net.NetworkRequest
   8 | import com.example.gymbro.core.network.NetworkMonitor
   9 | import com.example.gymbro.core.network.NetworkSpeed
  10 | import com.example.gymbro.core.network.NetworkStatus
  11 | import com.example.gymbro.core.network.NetworkType
  12 | import dagger.hilt.android.qualifiers.ApplicationContext
  13 | import kotlinx.coroutines.flow.Flow
  14 | import kotlinx.coroutines.flow.MutableStateFlow
  15 | import kotlinx.coroutines.flow.asStateFlow
  16 | import timber.log.Timber
  17 | import javax.inject.Inject
  18 | import javax.inject.Singleton
  19 | 
  20 | /**
  21 |  * NetworkMonitor的Android实现
  22 |  *
  23 |  * 使用Android ConnectivityManager监控网络状态
  24 |  */
  25 | @Singleton
  26 | class AndroidNetworkMonitor @Inject constructor(
  27 |     @ApplicationContext private val context: Context,
  28 | ) : NetworkMonitor {
  29 | 
  30 |     private val connectivityManager = context.getSystemService(
  31 |         Context.CONNECTIVITY_SERVICE,
  32 |     ) as ConnectivityManager
  33 | 
  34 |     private val _isNetworkAvailable = MutableStateFlow(false)
  35 |     override val isNetworkAvailable: Flow<Boolean> = _isNetworkAvailable.asStateFlow()
  36 | 
  37 |     private val _networkType = MutableStateFlow(NetworkType.NONE)
  38 |     override val networkType: Flow<NetworkType> = _networkType.asStateFlow()
  39 | 
  40 |     private val _isMetered = MutableStateFlow(false)
  41 |     override val isMetered: Flow<Boolean> = _isMetered.asStateFlow()
  42 | 
  43 |     private val _networkSpeed = MutableStateFlow(NetworkSpeed.UNKNOWN)
  44 |     override val networkSpeed: Flow<NetworkSpeed> = _networkSpeed.asStateFlow()
  45 | 
  46 |     private val _isRestricted = MutableStateFlow(false)
  47 |     override val isRestricted: Flow<Boolean> = _isRestricted.asStateFlow()
  48 | 
  49 |     private val _status = MutableStateFlow(
  50 |         NetworkStatus(
  51 |             isAvailable = false,
  52 |             type = NetworkType.NONE,
  53 |             isMetered = false,
  54 |             networkSpeed = NetworkSpeed.UNKNOWN,
  55 |             isRestricted = false,
  56 |         ),
  57 |     )
  58 |     override val status: Flow<NetworkStatus> = _status.asStateFlow()
  59 | 
  60 |     private val networkCallback = object : ConnectivityManager.NetworkCallback() {
  61 |         override fun onAvailable(network: Network) {
  62 |             Timber.d("网络连接已建立")
  63 |             _isNetworkAvailable.value = true
  64 |         }
  65 | 
  66 |         override fun onLost(network: Network) {
  67 |             Timber.d("网络连接已断开")
  68 |             _isNetworkAvailable.value = false
  69 |             _networkType.value = NetworkType.NONE
  70 |             updateStatusFlow()
  71 |         }
  72 | 
  73 |         override fun onCapabilitiesChanged(
  74 |             network: Network,
  75 |             networkCapabilities: NetworkCapabilities,
  76 |         ) {
  77 |             val hasInternet = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
  78 |                 networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
  79 |             Timber.d("网络能力已改变，Internet连接: %s", hasInternet)
  80 |             _isNetworkAvailable.value = hasInternet
  81 | 
  82 |             // 更新网络类型
  83 |             _networkType.value = when {
  84 |                 !hasInternet -> NetworkType.NONE
  85 |                 networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> NetworkType.WIFI
  86 |                 networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> NetworkType.CELLULAR
  87 |                 networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> NetworkType.ETHERNET
  88 |                 networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_VPN) -> NetworkType.VPN
  89 |                 else -> NetworkType.OTHER
  90 |             }
  91 | 
  92 |             // 更新网络速度估计
  93 |             _networkSpeed.value = estimateNetworkSpeed(networkCapabilities)
  94 | 
  95 |             // 更新计费状态
  96 |             _isMetered.value = !networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_NOT_METERED)
  97 | 
  98 |             // 更新受限状态
  99 |             _isRestricted.value = !networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_NOT_RESTRICTED)
 100 | 
 101 |             // 更新汇总状态
 102 |             updateStatusFlow()
 103 |         }
 104 |     }
 105 | 
 106 |     /**
 107 |      * 根据网络能力估计网络速度
 108 |      */
 109 |     private fun estimateNetworkSpeed(networkCapabilities: NetworkCapabilities): NetworkSpeed {
 110 |         return when {
 111 |             networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
 112 |                 networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> {
 113 |                 if (networkCapabilities.linkDownstreamBandwidthKbps > 50000) {
 114 |                     NetworkSpeed.VERY_FAST
 115 |                 } else if (networkCapabilities.linkDownstreamBandwidthKbps > 10000) {
 116 |                     NetworkSpeed.FAST
 117 |                 } else {
 118 |                     NetworkSpeed.MEDIUM
 119 |                 }
 120 |             }
 121 |             networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> {
 122 |                 if (networkCapabilities.linkDownstreamBandwidthKbps > 20000) {
 123 |                     NetworkSpeed.FAST
 124 |                 } else if (networkCapabilities.linkDownstreamBandwidthKbps > 5000) {
 125 |                     NetworkSpeed.MEDIUM
 126 |                 } else {
 127 |                     NetworkSpeed.SLOW
 128 |                 }
 129 |             }
 130 |             else -> NetworkSpeed.UNKNOWN
 131 |         }
 132 |     }
 133 | 
 134 |     override fun startMonitoring() {
 135 |         try {
 136 |             val networkRequest = NetworkRequest.Builder()
 137 |                 .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
 138 |                 .build()
 139 |             connectivityManager.registerNetworkCallback(networkRequest, networkCallback)
 140 |             Timber.d("网络状态监听已注册")
 141 | 
 142 |             // 初始化网络状态
 143 |             updateInitialNetworkState()
 144 |         } catch (e: Exception) {
 145 |             Timber.e(e, "注册网络状态监听失败")
 146 |         }
 147 |     }
 148 | 
 149 |     override fun stopMonitoring() {
 150 |         try {
 151 |             connectivityManager.unregisterNetworkCallback(networkCallback)
 152 |             Timber.d("网络状态监听已注销")
 153 |         } catch (e: Exception) {
 154 |             Timber.e(e, "注销网络状态监听失败")
 155 |         }
 156 |     }
 157 | 
 158 |     /**
 159 |      * 初始化时更新当前网络状态
 160 |      */
 161 |     private fun updateInitialNetworkState() {
 162 |         val network = connectivityManager.activeNetwork
 163 |         if (network != null) {
 164 |             val capabilities = connectivityManager.getNetworkCapabilities(network)
 165 |             if (capabilities != null) {
 166 |                 networkCallback.onCapabilitiesChanged(network, capabilities)
 167 |             } else {
 168 |                 _isNetworkAvailable.value = false
 169 |                 _networkType.value = NetworkType.NONE
 170 |                 updateStatusFlow()
 171 |             }
 172 |         } else {
 173 |             _isNetworkAvailable.value = false
 174 |             _networkType.value = NetworkType.NONE
 175 |             updateStatusFlow()
 176 |         }
 177 |     }
 178 | 
 179 |     /**
 180 |      * 更新状态Flow
 181 |      */
 182 |     private fun updateStatusFlow() {
 183 |         _status.value = NetworkStatus(
 184 |             isAvailable = _isNetworkAvailable.value,
 185 |             type = _networkType.value,
 186 |             isMetered = _isMetered.value,
 187 |             networkSpeed = _networkSpeed.value,
 188 |             isRestricted = _isRestricted.value,
 189 |         )
 190 |     }
 191 | }

```

`main\kotlin\com\example\gymbro\service\BackgroundServiceManager.kt`:

```kt
   1 | package com.example.gymbro.service
   2 | 
   3 | import android.content.Context
   4 | import android.content.Intent
   5 | import android.net.ConnectivityManager
   6 | import android.net.NetworkCapabilities
   7 | import android.os.Build
   8 | import androidx.core.content.ContextCompat
   9 | import dagger.hilt.android.qualifiers.ApplicationContext
  10 | import timber.log.Timber
  11 | import javax.inject.Inject
  12 | import javax.inject.Singleton
  13 | 
  14 | /**
  15 |  * 后台服务管理器
  16 |  *
  17 |  * 负责启动和停止应用程序的后台服务，以及管理网络连接。
  18 |  */
  19 | @Singleton
  20 | class BackgroundServiceManager @Inject constructor(
  21 |     @ApplicationContext private val context: Context,
  22 | ) {
  23 | 
  24 |     companion object {
  25 |         private const val TAG = "BackgroundServiceManager"
  26 |     }
  27 | 
  28 |     /**
  29 |      * 启动后台服务
  30 |      *
  31 |      * @return 是否成功启动服务
  32 |      */
  33 |     fun startBackgroundService(): Boolean {
  34 |         try {
  35 |             // 这里将来可以替换为实际的后台服务类
  36 |             val serviceIntent = Intent(context, GymBroBackgroundService::class.java)
  37 | 
  38 |             if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
  39 |                 ContextCompat.startForegroundService(context, serviceIntent)
  40 |             } else {
  41 |                 context.startService(serviceIntent)
  42 |             }
  43 | 
  44 |             Timber.tag(TAG).d("后台服务已启动")
  45 |             return true
  46 |         } catch (e: Exception) {
  47 |             Timber.tag(TAG).e(e, "启动后台服务失败")
  48 |             return false
  49 |         }
  50 |     }
  51 | 
  52 |     /**
  53 |      * 停止后台服务
  54 |      *
  55 |      * @return 是否成功停止服务
  56 |      */
  57 |     fun stopBackgroundService(): Boolean {
  58 |         try {
  59 |             // 这里将来可以替换为实际的后台服务类
  60 |             val serviceIntent = Intent(context, GymBroBackgroundService::class.java)
  61 |             val result = context.stopService(serviceIntent)
  62 | 
  63 |             Timber.tag(TAG).d("后台服务已停止, 结果: $result")
  64 |             return result
  65 |         } catch (e: Exception) {
  66 |             Timber.tag(TAG).e(e, "停止后台服务失败")
  67 |             return false
  68 |         }
  69 |     }
  70 | 
  71 |     /**
  72 |      * 检查网络连接是否可用
  73 |      *
  74 |      * @return 如果网络连接可用，则为true；否则为false
  75 |      */
  76 |     fun isNetworkAvailable(): Boolean {
  77 |         val connectivityManager = context.getSystemService(
  78 |             Context.CONNECTIVITY_SERVICE,
  79 |         ) as ConnectivityManager
  80 | 
  81 |         if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
  82 |             val network = connectivityManager.activeNetwork ?: return false
  83 |             val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
  84 | 
  85 |             return capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
  86 |                 capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
  87 |         } else {
  88 |             @Suppress("DEPRECATION")
  89 |             val networkInfo = connectivityManager.activeNetworkInfo
  90 |             @Suppress("DEPRECATION")
  91 |             return networkInfo != null && networkInfo.isConnected
  92 |         }
  93 |     }
  94 | 
  95 |     /**
  96 |      * 启用后台网络连接
  97 |      *
  98 |      * @return 是否成功启用网络连接
  99 |      */
 100 |     fun enableBackgroundNetwork(): Boolean {
 101 |         // 实际实现可能需要注册网络回调或设置网络请求策略
 102 |         return isNetworkAvailable()
 103 |     }
 104 | 
 105 |     /**
 106 |      * 禁用后台网络连接
 107 |      *
 108 |      * @return 是否成功禁用网络连接
 109 |      */
 110 |     fun disableBackgroundNetwork(): Boolean {
 111 |         // 实际实现可能需要取消注册网络回调或重置网络请求策略
 112 |         return true
 113 |     }
 114 | }
 115 | 
 116 | /**
 117 |  * 健身伙伴后台服务
 118 |  *
 119 |  * 此类是一个占位符，将来会替换为实际的后台服务实现。
 120 |  */
 121 | class GymBroBackgroundService {
 122 |     // 占位符类，将来会替换为实际的Service子类
 123 | }

```

`main\kotlin\com\example\gymbro\service\BaseService.kt`:

```kt
   1 | package com.example.gymbro.service
   2 | 
   3 | import android.app.Service
   4 | import android.content.Intent
   5 | import android.os.IBinder
   6 | import androidx.annotation.CallSuper
   7 | import com.example.gymbro.core.error.ErrorReporter
   8 | import dagger.hilt.android.AndroidEntryPoint
   9 | import kotlinx.coroutines.CoroutineScope
  10 | import kotlinx.coroutines.Dispatchers
  11 | import kotlinx.coroutines.SupervisorJob
  12 | import kotlinx.coroutines.cancel
  13 | import javax.inject.Inject
  14 | import kotlin.coroutines.CoroutineContext
  15 | 
  16 | /**
  17 |  * 提供基本生命周期管理和依赖注入支持的服务基类。
  18 |  */
  19 | @AndroidEntryPoint
  20 | abstract class BaseService : Service(), CoroutineScope {
  21 | 
  22 |     @Inject
  23 |     lateinit var errorReporter: ErrorReporter
  24 | 
  25 |     private val job = SupervisorJob()
  26 |     override val coroutineContext: CoroutineContext
  27 |         get() = Dispatchers.Main + job
  28 | 
  29 |     override fun onCreate() {
  30 |         super.onCreate()
  31 |         injectDependencies()
  32 |     }
  33 | 
  34 |     /**
  35 |      * 强制子类处理绑定逻辑。
  36 |      */
  37 |     abstract override fun onBind(intent: Intent?): IBinder?
  38 | 
  39 |     @CallSuper
  40 |     override fun onDestroy() {
  41 |         super.onDestroy()
  42 |         coroutineContext.cancel()
  43 |     }
  44 | 
  45 |     /**
  46 |      * Dagger Hilt 注入。
  47 |      * 虽然使用了 @AndroidEntryPoint，但保留此方法可能有助于理解或未来的手动注入需求。
  48 |      */
  49 |     protected open fun injectDependencies() {
  50 |         // Hilt 会自动处理注入，通常不需要手动调用
  51 |         // (applicationContext as? GymBroApp)?.appComponent?.inject(this as BaseService)
  52 |     }
  53 | }

```

`main\kotlin\com\example\gymbro\service\ForegroundServiceManager.kt`:

```kt
   1 | package com.example.gymbro.core.service
   2 | 
   3 | import android.content.Context
   4 | import android.content.Intent
   5 | import android.os.Build
   6 | import androidx.core.content.ContextCompat
   7 | import dagger.hilt.android.qualifiers.ApplicationContext
   8 | import timber.log.Timber
   9 | import javax.inject.Inject
  10 | import javax.inject.Singleton
  11 | 
  12 | /**
  13 |  * 前台服务管理器
  14 |  *
  15 |  * 负责启动和停止应用程序的前台服务，明确区分于WorkManager的后台任务
  16 |  */
  17 | @Singleton
  18 | class ForegroundServiceManager @Inject constructor(
  19 |     @ApplicationContext private val context: Context,
  20 | ) {
  21 | 
  22 |     companion object {
  23 |         private const val TAG = "ForegroundServiceManager"
  24 |     }
  25 | 
  26 |     /**
  27 |      * 启动前台服务
  28 |      *
  29 |      * @param serviceClass 要启动的服务类
  30 |      * @return 是否成功启动服务
  31 |      */
  32 |     fun startForegroundService(serviceClass: Class<*>): Boolean {
  33 |         try {
  34 |             val serviceIntent = Intent(context, serviceClass)
  35 | 
  36 |             if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
  37 |                 ContextCompat.startForegroundService(context, serviceIntent)
  38 |             } else {
  39 |                 context.startService(serviceIntent)
  40 |             }
  41 | 
  42 |             Timber.tag(TAG).d("前台服务已启动: ${serviceClass.simpleName}")
  43 |             return true
  44 |         } catch (e: Exception) {
  45 |             Timber.tag(TAG).e(e, "启动前台服务失败: ${serviceClass.simpleName}")
  46 |             return false
  47 |         }
  48 |     }
  49 | 
  50 |     /**
  51 |      * 停止前台服务
  52 |      *
  53 |      * @param serviceClass 要停止的服务类
  54 |      * @return 是否成功停止服务
  55 |      */
  56 |     fun stopForegroundService(serviceClass: Class<*>): Boolean {
  57 |         try {
  58 |             val serviceIntent = Intent(context, serviceClass)
  59 |             val result = context.stopService(serviceIntent)
  60 | 
  61 |             Timber.tag(TAG).d("前台服务已停止: ${serviceClass.simpleName}, 结果: $result")
  62 |             return result
  63 |         } catch (e: Exception) {
  64 |             Timber.tag(TAG).e(e, "停止前台服务失败: ${serviceClass.simpleName}")
  65 |             return false
  66 |         }
  67 |     }
  68 | 
  69 |     /**
  70 |      * 判断服务是否正在运行
  71 |      *
  72 |      * 注意: Android O及以上版本不再支持此方法，仅作为尽力而为的实现
  73 |      */
  74 |     fun isServiceRunning(serviceClass: Class<*>): Boolean {
  75 |         try {
  76 |             val activityManager = context.getSystemService(
  77 |                 Context.ACTIVITY_SERVICE,
  78 |             ) as android.app.ActivityManager
  79 |             val runningServices = activityManager.getRunningServices(Integer.MAX_VALUE)
  80 | 
  81 |             for (service in runningServices) {
  82 |                 if (serviceClass.name == service.service.className) {
  83 |                     return true
  84 |                 }
  85 |             }
  86 |         } catch (e: Exception) {
  87 |             Timber.tag(TAG).e(e, "检查服务状态失败: ${serviceClass.simpleName}")
  88 |         }
  89 |         return false
  90 |     }
  91 | }

```