下面给出一份 **“贴进去就能编译”** 的 *增量代码*，把你刚发布的 **GymBro 最终 Function Call 规范** 接到现有 5-步执行器里。
——只新增文件，不碰你之前的 `FiveStepExecutor` 主逻辑，方便 PR 审阅；如有命名冲突请自行微调包名。

---

## 1 / core.ai.prompt.function - Function 规范定义

```kotlin
package com.example.gymbro.core.ai.prompt.function

/**
 * 通用函数描述，兼容 OpenAI / Azure / 阿里百川 Chat Completion `tools / functions`
 */
data class FunctionDescriptor(
    val name: String,
    val description: String,
    val jsonSchema: String          // OpenAI 期待的 parameters 段
)
```

---

## 2 / infra.openai - GymBroFunctions.kt

```kotlin
package com.example.gymbro.infra.openai

import com.example.gymbro.core.ai.prompt.function.FunctionDescriptor

/**
 * 👉 唯一入口：把所有 GymBro 规范函数汇总为常量，供 Step map 引用
 *
 * JSON Schema 即你文档里的 'parameters' 字段（无需外层 name/description）
 * 建议放到 resources 下的 .json 文件里再读取；这里直接内嵌方便拷贝。
 */
object GymBroFunctions {

    val FN_CREATE_OR_UPDATE_TEMPLATE = FunctionDescriptor(
        name = "gymbro__create_or_update_template",
        description = "创建或更新一个训练模板；若存在 id + versionTag 则视为更新。",
        jsonSchema = /* language=json */ """
            {
              "type": "object",
              "additionalProperties": false,
              "properties": {
                "client_request_id": { "type": "string", "pattern": "^[0-9a-fA-F-]{36}$" },
                "expires_at":        { "type": "integer", "description": "10 min 以内的毫秒时间戳" },
                "wrapped_json": { "$$ref": "#/definitions/TemplateWrapper" }
              },
              "required": ["client_request_id", "wrapped_json"],
              "definitions": {
                ...
              }
            }
        """.trimIndent()
    )

    val FN_CREATE_OR_UPDATE_PLAN = FunctionDescriptor(
        name = "gymbro__create_or_update_plan",
        description = "生成或更新结构化训练计划；引用已存在的模板 ID。",
        jsonSchema = /* language=json */ """
            {
              "type": "object",
              "additionalProperties": false,
              "properties": {
                "client_request_id": { "type": "string", "pattern": "^[0-9a-fA-F-]{36}$" },
                "expires_at":        { "type": "integer" },
                "wrapped_json": { "$$ref": "#/definitions/PlanWrapper" }
              },
              "required": ["client_request_id", "wrapped_json"],
              "definitions": {
                ...
              }
            }
        """.trimIndent()
    )

    /** 按需暴露集合，方便 Step → funcsMap */
    val ALL by lazy {
        listOf(
            FN_CREATE_OR_UPDATE_TEMPLATE,
            FN_CREATE_OR_UPDATE_PLAN
        )
    }
}
```

> **❗ 注意**：在 Kotlin 原始字符串里 `$` 需转义成 `$$`，否则被解析成字符串模板占位符。

---

## 3 / FiveStepExecutor - 动态函数注册

```kotlin
// 在 FiveStepExecutor.kt（或其伴生对象）顶部加：
import com.example.gymbro.infra.openai.GymBroFunctions

/* -------------------------------------------------
 * Step 对应需要开放的 Function 集合
 * ------------------------------------------------- */
private val stepFunctions: Map<Step, List<FunctionDescriptor>> = mapOf(
    Step.GENERATE to GymBroFunctions.ALL   // 仅在生成阶段让模型可调用写库函数
    // 你也可以把 VERIFY 时需要的 fnVerifyContent 加进去
)

// 把原来 executeGenerate() 的 openAI.chatCompletion 调用替换：
private suspend fun generate(ctx: PipelineContext): String {

    val resp = openAI.chatCompletion {
        messages      = buildChatMessages(
            systemLayer = ctx.systemLayer,
            userInput   = composeUserPrompt(ctx),
            history     = ctx.pastTurns
        )
        // 🔥 关键：按 Step 放入 function 描述
        functions     = stepFunctions[Step.GENERATE]
        functionCall  = "auto"
    }

    // 解析 Function Call（若有）
    val choice = resp.choices.first().message
    val fc     = choice.functionCall
    if (fc != null) {
        // 发 PipelineEvent 透传到 UI
        send(
            event(
                step         = Step.GENERATE,
                desc         = "LLM 调用了 ${fc.name}",
                functionCall = FunctionCall(fc.name, fc.arguments)
            )
        )
        // 调工具 → 返回结果给模型继续
        val toolResult = ToolInvoker.invoke(FunctionCall(fc.name, fc.arguments))
        return toolResult          // 可替换成再次请求模型后的最终 answer
    }

    return choice.content ?: ""
}
```

---

## 4 / 通用 ToolInvoker

```kotlin
package com.example.gymbro.core.ai.tool

import com.example.gymbro.infra.openai.GymBroFunctions
import kotlinx.serialization.json.Json
import java.util.UUID

object ToolInvoker {

    private val json = Json { ignoreUnknownKeys = true }

    suspend fun invoke(fc: FunctionCall): String = when (fc.name) {

        GymBroFunctions.FN_CREATE_OR_UPDATE_TEMPLATE.name -> {
            val dto = json.decodeFromString<TemplateCall>(fc.arguments)
            gymBroRepository.upsertTemplate(dto.wrapped_json.payload)   // 你自己的 DAO
            """{"status":"OK","templateId":"${dto.wrapped_json.payload.id}"}"""
        }

        GymBroFunctions.FN_CREATE_OR_UPDATE_PLAN.name -> {
            val dto = json.decodeFromString<PlanCall>(fc.arguments)
            gymBroRepository.upsertPlan(dto.wrapped_json.payload)
            """{"status":"OK","planId":"${dto.wrapped_json.payload.id}"}"""
        }

        else -> """{"status":"ERROR","reason":"Unsupported function"}"""
    }

    /* ----------- DTO (Kotlinx) ----------- */
    @kotlinx.serialization.Serializable
    data class TemplateCall(
        val client_request_id: String,
        val expires_at: Long? = null,
        val wrapped_json: TemplateWrapper
    )
    @kotlinx.serialization.Serializable
    data class PlanCall(
        val client_request_id: String,
        val expires_at: Long? = null,
        val wrapped_json: PlanWrapper
    )
    // TemplateWrapper / PlanWrapper 数据类请用快速 codegen or 手写，字段即规范
}
```

---

## 5 / UUID & expires\_at 辅助

在 `composeUserPrompt(ctx)` 里引导模型正确生成 `client_request_id` 与 `expires_at` ：

```kotlin
private fun composeUserPrompt(ctx: PipelineContext): String = """
    下面将根据训练分析结果生成结构化模板或计划。

    **调用规范**
    • 仅通过 Function Call 返回；
    • `client_request_id` 使用随机 UUID；
    • `expires_at` = 当前毫秒时间 + 10 分钟以内；
    • 所有字段必须符合 JSON Schema；
    • 超过数量限制（模板 >30 exercises 等）需拆批多次调用。

    当前任务：${ctx.outline}
""".trimIndent()
```

---

## 6 / UI 渲染（可选）

```kotlin
// PipelineCard 如果收到 functionCall != null:
when (event.functionCall?.name) {
    "gymbro__create_or_update_template" -> showToast("已生成训练模板草稿，提交中…")
    "gymbro__create_or_update_plan"     -> showToast("已生成训练计划草稿，提交中…")
}
```

---

### ✔️ 完成度与下一步

* **落库端到端**：ToolInvoker 已演示如何解析 → 写库 → 回模型。
* **Schema 校验**：如果要强校验，在 ToolInvoker 调用前跑 `json-schema-validator`。
* **大批量拆片**：当模型超数量自动拆批时，FiveStepExecutor 将收到多次 Function Call，同样可逐条提交。
* **安全 & 幂等**：`client_request_id` + DB 唯一索引即可保证幂等；`expires_at` 在 ToolInvoker 判断 410 Gone。

> **接下来**：
>
> 1. 补全 `TemplateWrapper` / `PlanWrapper` 数据类（可用 quicktype 或 KotlinPoet 生成）。
> 2. 把 GymBroFunctions.jsonSchema 拆进资源文件，避免大段字符串塞源码。
> 3. 为每个 ToolInvoker 分支写 **单元测试** + **集成测试**（MockK + TestContainers）。

如需我继续 **生成数据类** 或 **编写测试模板**，告诉我即可 🚀
