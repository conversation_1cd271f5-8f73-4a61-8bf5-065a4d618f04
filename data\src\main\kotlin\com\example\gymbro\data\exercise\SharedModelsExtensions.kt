package com.example.gymbro.data.exercise

import com.example.gymbro.shared.models.exercise.Equipment
import com.example.gymbro.shared.models.exercise.MuscleGroup

/**
 * 字符串转换为 MuscleGroup 的扩展函数 (Data层专用)
 */
fun MuscleGroup.Companion.fromName(name: String): MuscleGroup? =
    when (name.uppercase().trim()) {
        "CHEST", "胸肌", "胸部" -> MuscleGroup.CHEST
        "BACK", "背肌", "背部" -> MuscleGroup.BACK
        "SHOULDERS", "肩膀", "肩部" -> MuscleGroup.SHOULDERS
        "BICEPS", "二头肌" -> MuscleGroup.BICEPS
        "TRICEPS", "三头肌" -> MuscleGroup.TRICEPS
        "FOREARMS", "前臂" -> MuscleGroup.FOREARMS
        "ABS", "腹肌" -> MuscleGroup.ABS
        "OBLIQUES", "斜方肌" -> MuscleGroup.OBLIQUES
        "QUADRICEPS", "股四头肌", "大腿前侧" -> MuscleGroup.QUADRICEPS
        "HAMSTRINGS", "腿筋", "大腿后侧" -> MuscleGroup.HAMSTRINGS
        "CALVES", "小腿" -> MuscleGroup.CALVES
        "GLUTES", "臀部", "臀肌" -> MuscleGroup.GLUTES
        "TRAPEZIUS", "斜方肌" -> MuscleGroup.TRAPEZIUS
        "LOWER_BACK", "下背部" -> MuscleGroup.LOWER_BACK
        "CORE", "核心" -> MuscleGroup.CORE
        "FULL_BODY", "全身" -> MuscleGroup.FULL_BODY
        else -> MuscleGroup.OTHER
    }

/**
 * 字符串转换为 Equipment 的扩展函数 (Data层专用)
 */
fun Equipment.Companion.fromName(name: String): Equipment? =
    when (name.uppercase().trim()) {
        "NONE", "无器材", "徒手" -> Equipment.NONE
        "BARBELL", "杠铃" -> Equipment.BARBELL
        "DUMBBELL", "哑铃" -> Equipment.DUMBBELL
        "KETTLEBELL", "壶铃" -> Equipment.KETTLEBELL
        "RESISTANCE_BAND", "阻力带" -> Equipment.RESISTANCE_BAND
        "CABLES", "缆绳" -> Equipment.CABLES
        "MACHINE", "健身机器", "器械" -> Equipment.MACHINE
        "MEDICINE_BALL", "药球" -> Equipment.MEDICINE_BALL
        "STABILITY_BALL", "稳定球" -> Equipment.STABILITY_BALL
        "FOAM_ROLLER", "泡沫轴" -> Equipment.FOAM_ROLLER
        "PULL_UP_BAR", "引体向上杠" -> Equipment.PULL_UP_BAR
        "BENCH", "卧推凳" -> Equipment.BENCH
        "BIKE", "健身车" -> Equipment.BIKE
        "TREADMILL", "跑步机" -> Equipment.TREADMILL
        "ELLIPTICAL", "椭圆机" -> Equipment.ELLIPTICAL
        "ROWER", "划船机" -> Equipment.ROWER
        "BATTLE_ROPE", "战绳" -> Equipment.BATTLE_ROPE
        "TRX", "TRX悬挂训练系统" -> Equipment.TRX
        else -> Equipment.OTHER
    }
