package com.example.gymbro.core.error.internal

import com.example.gymbro.core.error.ModernErrorHandler
import com.example.gymbro.core.error.icon.ErrorIconProvider
import com.example.gymbro.core.resources.ResourceProvider
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 默认错误处理器
 *
 * 这是ModernErrorHandler接口的默认实现，用于向后兼容
 * 新代码应该直接使用ModernErrorHandler接口
 *
 * @property resourceProvider 资源提供者，用于本地化消息
 * @property errorIconProvider 错误图标提供者，用于获取错误图标
 */
@Singleton
class DefaultErrorHandler
@Inject
constructor(
    private val resourceProvider: ResourceProvider,
    private val errorIconProvider: ErrorIconProvider,
) : ModernErrorHandler by com.example.gymbro.core.error.internal.ModernErrorHandlerImpl(
    resourceProvider,
    errorIconProvider,
)
