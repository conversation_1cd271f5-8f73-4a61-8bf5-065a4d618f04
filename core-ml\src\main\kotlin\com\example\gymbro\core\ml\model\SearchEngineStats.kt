package com.example.gymbro.core.ml.model

/**
 * 搜索引擎统计信息 - Core-ML Layer Model
 *
 * @param totalVectors 总向量数量
 * @param vectorDimension 向量维度
 * @param averageSearchTimeMs 平均搜索时间（毫秒）
 * @param indexType 索引类型
 * @param memoryUsageMB 内存使用量（MB）
 */
data class SearchEngineStats(
    val totalVectors: Int,
    val vectorDimension: Int,
    val averageSearchTimeMs: Long,
    val indexType: String,
    val memoryUsageMB: Float,
)
