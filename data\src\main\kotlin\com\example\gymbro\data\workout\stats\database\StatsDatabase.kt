package com.example.gymbro.data.workout.stats.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.example.gymbro.data.workout.stats.dao.DailyStatsDao
import com.example.gymbro.data.workout.stats.entity.DailyStatsEntity
import com.example.gymbro.data.shared.converter.DateTimeConverters

/**
 * 统计数据库 - Stats模块专用数据库
 * 
 * 基于Clean Architecture设计，专门用于存储和管理训练统计数据。
 * 采用独立数据库设计，确保统计功能的高性能和可扩展性。
 * 
 * 设计特点：
 * - 独立数据库：与Session、Plan等模块分离
 * - 高性能：针对统计查询优化的表结构和索引
 * - 可扩展：支持未来统计功能扩展
 * - 数据完整性：完整的约束和事务支持
 * 
 * 数据库结构：
 * - daily_stats表：日级统计数据
 * - 索引优化：支持高效的时间范围查询
 * - 类型转换器：支持LocalDate等复杂类型
 * 
 * @version 1 初始版本
 */
@Database(
    entities = [
        DailyStatsEntity::class
    ],
    version = 1,
    exportSchema = true
)
@TypeConverters(DateTimeConverters::class)
abstract class StatsDatabase : RoomDatabase() {
    
    /**
     * 日级统计数据访问对象
     */
    abstract fun dailyStatsDao(): DailyStatsDao
    
    companion object {
        private const val DATABASE_NAME = "stats_database"
        
        @Volatile
        private var INSTANCE: StatsDatabase? = null
        
        /**
         * 获取数据库实例（单例模式）
         */
        fun getDatabase(context: Context): StatsDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    StatsDatabase::class.java,
                    DATABASE_NAME
                )
                .addCallback(StatsDatabaseCallback())
                .build()
                INSTANCE = instance
                instance
            }
        }
        
        /**
         * 清理数据库实例（测试用）
         */
        fun clearInstance() {
            INSTANCE = null
        }
    }
}

/**
 * 统计数据库回调
 * 用于数据库创建和升级时的初始化操作
 */
private class StatsDatabaseCallback : RoomDatabase.Callback() {
    
    override fun onCreate(db: androidx.sqlite.db.SupportSQLiteDatabase) {
        super.onCreate(db)
        
        // 创建额外的索引以优化查询性能
        db.execSQL("""
            CREATE INDEX IF NOT EXISTS idx_daily_stats_user_date_range 
            ON daily_stats(userId, date, totalWeight)
        """)
        
        db.execSQL("""
            CREATE INDEX IF NOT EXISTS idx_daily_stats_performance 
            ON daily_stats(userId, totalWeight DESC, sessionDurationSec DESC)
        """)
        
        // 创建视图以支持复杂查询
        db.execSQL("""
            CREATE VIEW IF NOT EXISTS weekly_stats AS
            SELECT 
                userId,
                strftime('%Y-%W', date) as week,
                COUNT(*) as sessions_count,
                SUM(totalWeight) as total_weight,
                SUM(sessionDurationSec) as total_duration,
                AVG(avgRpe) as avg_rpe,
                MIN(date) as week_start,
                MAX(date) as week_end
            FROM daily_stats
            GROUP BY userId, strftime('%Y-%W', date)
        """)
        
        db.execSQL("""
            CREATE VIEW IF NOT EXISTS monthly_stats AS
            SELECT 
                userId,
                strftime('%Y-%m', date) as month,
                COUNT(*) as sessions_count,
                SUM(totalWeight) as total_weight,
                SUM(sessionDurationSec) as total_duration,
                AVG(avgRpe) as avg_rpe,
                MIN(date) as month_start,
                MAX(date) as month_end
            FROM daily_stats
            GROUP BY userId, strftime('%Y-%m', date)
        """)
    }
}
