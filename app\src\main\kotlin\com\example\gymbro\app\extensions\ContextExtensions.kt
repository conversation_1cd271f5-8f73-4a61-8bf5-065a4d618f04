package com.example.gymbro.app.extensions

import android.content.Context
import android.widget.Toast
import com.example.gymbro.core.resources.AndroidResourceProvider
import com.example.gymbro.core.resources.ResourceProvider
import com.example.gymbro.core.ui.text.UiText

/**
 * Context扩展函数 - Android特定功能
 * 从core模块迁移而来，专门处理Android Context相关操作
 */

/**
 * 显示Toast消息
 * @param message 要显示的消息
 * @param duration Toast持续时间，默认为LENGTH_SHORT
 */
fun Context.toast(message: String, duration: Int = Toast.LENGTH_SHORT) {
    Toast.makeText(this, message, duration).show()
}

/**
 * 显示UiText格式的Toast消息
 * @param uiText UiText格式的消息
 * @param duration Toast持续时间，默认为LENGTH_SHORT
 */
fun Context.toast(uiText: UiText, duration: Int = Toast.LENGTH_SHORT) {
    val message = when (uiText) {
        is UiText.DynamicString -> uiText.value
        is UiText.StringResource -> getString(uiText.resId, *uiText.args.toTypedArray())
        is UiText.ErrorCode -> "Error: ${uiText.errorCode}"
        UiText.Empty -> ""
    }
    toast(message, duration)
}

/**
 * Context扩展函数 - 转换为ResourceProvider
 *
 * 此扩展函数允许将Android Context对象直接转换为ResourceProvider接口实例。
 * 当组件需要ResourceProvider但只有Context可用时非常有用。
 *
 * 用法示例:
 * ```
 * val context: Context = ...
 * val resourceProvider: ResourceProvider = context.asResourceProvider()
 * val message = resourceProvider.getString(R.string.message)
 * ```
 *
 * 注意：此函数创建一个新的AndroidResourceProvider实例，不使用依赖注入。
 * 在可能的情况下，应优先使用通过Hilt注入的ResourceProvider实例。
 *
 * @return 使用提供的Context创建的ResourceProvider实例
 */
fun Context.asResourceProvider(): ResourceProvider {
    return object : ResourceProvider {
        override fun getString(resId: Int): String =
            <EMAIL>(resId)

        override fun getString(resId: Int, vararg args: Any): String =
            <EMAIL>(resId, *args)

        override fun getQuantityString(resId: Int, quantity: Int, vararg args: Any): String =
            <EMAIL>(resId, quantity, *args)

        override fun asUiText(resId: Int, vararg args: Any): UiText =
            UiText.StringResource(resId, args.map { it.toString() })

        override fun getCurrentLanguage(): String =
            java.util.Locale.getDefault().toLanguageTag()

        override fun getInteger(resId: Int): Int =
            <EMAIL>(resId)
    }
}

/**
 * ResourceProvider扩展函数 - 获取原始Context
 *
 * 当ResourceProvider是通过Context.asResourceProvider()创建时，
 * 可以通过此扩展属性获取原始Context（如果可用）。
 *
 * 注意：由于ResourceProvider是一个接口，此函数仅适用于通过上述扩展函数创建的实例。
 * 对于其他实现，将返回null。
 *
 * @return 原始Context或null（如果无法获取）
 */
val ResourceProvider.context: Context?
    get() = when (this) {
        is AndroidResourceProvider -> {
            // 通过反射获取context字段
            try {
                val field = AndroidResourceProvider::class.java.getDeclaredField("context")
                field.isAccessible = true
                field.get(this) as? Context
            } catch (e: NoSuchFieldException) {
                android.util.Log.w("ContextExtensions", "无法通过反射获取context字段: field not found", e)
                null
            } catch (e: IllegalAccessException) {
                android.util.Log.w("ContextExtensions", "无法通过反射获取context字段: access denied", e)
                null
            } catch (e: Exception) {
                android.util.Log.e("ContextExtensions", "反射访问context字段时发生未知错误", e)
                null
            }
        }
        else -> null
    }
