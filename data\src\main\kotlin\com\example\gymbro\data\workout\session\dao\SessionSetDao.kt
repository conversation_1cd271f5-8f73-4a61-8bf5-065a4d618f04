package com.example.gymbro.data.workout.session.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.example.gymbro.data.workout.session.entity.SessionSetEntity
import kotlinx.coroutines.flow.Flow

/**
 * 会话组数数据访问对象 - SessionDB DAO
 *
 * 基于 05_Final_Database_Architecture.md 和 04_Implementation_Plan_CleanSlate_SessionDB.md 设计
 * 提供会话中组数详细记录管理功能
 */
@Dao
interface SessionSetDao {

    // ==================== 基础 CRUD ====================

    @Query("SELECT * FROM session_sets WHERE id = :setId")
    suspend fun getSessionSetById(setId: String): SessionSetEntity?

    @Query("SELECT * FROM session_sets WHERE sessionExerciseId = :exerciseId ORDER BY setNumber ASC")
    fun getExerciseSets(exerciseId: String): Flow<List<SessionSetEntity>>

    @Query("SELECT * FROM session_sets WHERE sessionExerciseId = :exerciseId ORDER BY setNumber ASC")
    suspend fun getExerciseSetsSync(exerciseId: String): List<SessionSetEntity>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSet(set: SessionSetEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSets(sets: List<SessionSetEntity>)

    @Update
    suspend fun updateSet(set: SessionSetEntity)

    @Query("DELETE FROM session_sets WHERE id = :setId")
    suspend fun deleteSet(setId: String)

    @Query("DELETE FROM session_sets WHERE sessionExerciseId = :exerciseId")
    suspend fun deleteAllSetsForExercise(exerciseId: String)

    // ==================== 组数完成 ====================

    @Query(
        "UPDATE session_sets SET weight = :weight, reps = :reps, isCompleted = 1, timestamp = :timestamp WHERE id = :setId",
    )
    suspend fun completeSet(setId: String, weight: Double?, reps: Int?, timestamp: Long)

    @Query(
        "UPDATE session_sets SET weight = :weight, reps = :reps, timeSeconds = :timeSeconds, rpe = :rpe, isCompleted = 1, timestamp = :timestamp WHERE id = :setId",
    )
    suspend fun completeSetWithDetails(
        setId: String,
        weight: Double?,
        reps: Int?,
        timeSeconds: Int?,
        rpe: Double?,
        timestamp: Long,
    )

    @Query("UPDATE session_sets SET isCompleted = 0 WHERE id = :setId")
    suspend fun uncompleteSet(setId: String)

    // ==================== 特定查询 ====================

    @Query(
        "SELECT * FROM session_sets WHERE sessionExerciseId = :exerciseId AND isCompleted = 1 ORDER BY setNumber ASC",
    )
    fun getCompletedSets(exerciseId: String): Flow<List<SessionSetEntity>>

    @Query(
        "SELECT * FROM session_sets WHERE sessionExerciseId = :exerciseId AND isCompleted = 0 ORDER BY setNumber ASC",
    )
    fun getPendingSets(exerciseId: String): Flow<List<SessionSetEntity>>

    @Query("SELECT * FROM session_sets WHERE sessionExerciseId = :exerciseId AND setNumber = :setNumber")
    suspend fun getSetByNumber(exerciseId: String, setNumber: Int): SessionSetEntity?

    @Query(
        "SELECT * FROM session_sets WHERE sessionExerciseId = :exerciseId AND isWarmupSet = 1 ORDER BY setNumber ASC",
    )
    fun getWarmupSets(exerciseId: String): Flow<List<SessionSetEntity>>

    // ==================== 统计查询 ====================

    @Query("SELECT COUNT(*) FROM session_sets WHERE sessionExerciseId = :exerciseId")
    suspend fun getSetCount(exerciseId: String): Int

    @Query("SELECT COUNT(*) FROM session_sets WHERE sessionExerciseId = :exerciseId AND isCompleted = 1")
    suspend fun getCompletedSetCount(exerciseId: String): Int

    @Query("SELECT MAX(setNumber) FROM session_sets WHERE sessionExerciseId = :exerciseId")
    suspend fun getMaxSetNumber(exerciseId: String): Int?

    @Query(
        "SELECT SUM(weight * reps) FROM session_sets WHERE sessionExerciseId = :exerciseId AND isCompleted = 1 AND weight IS NOT NULL AND reps IS NOT NULL",
    )
    suspend fun getTotalVolumeForExercise(exerciseId: String): Double?

    @Query(
        "SELECT MAX(weight) FROM session_sets WHERE sessionExerciseId = :exerciseId AND isCompleted = 1 AND weight IS NOT NULL",
    )
    suspend fun getMaxWeightForExercise(exerciseId: String): Double?

    @Query(
        "SELECT MAX(reps) FROM session_sets WHERE sessionExerciseId = :exerciseId AND isCompleted = 1 AND reps IS NOT NULL",
    )
    suspend fun getMaxRepsForExercise(exerciseId: String): Int?
}
