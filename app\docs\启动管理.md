关键问题识别
1. HardwareDetectionWorker 完全失效

WM-WorkerFactory: Could not instantiate HardwareDetectionWorker
NoSuchMethodException: HardwareDetectionWorker.<init>
修复 HardwareDetectionWorker 构造函数

确保 Worker 类有正确的构造函数签名
添加 Hilt 或其他 DI 支持

## **启动流程设计原则**

**核心理念：**
$$\text{健壮性} = \text{故障隔离} + \text{优雅降级} + \text{快速恢复}$$

```kotlin
/**
 * 健壮的启动管理器
 * 设计原则：快速失败，优雅降级，渐进增强
 */
class RobustStartupManager private constructor(
    private val context: Context
) {
    companion object {
        @Volatile
        private var INSTANCE: RobustStartupManager? = null

        fun getInstance(context: Context): RobustStartupManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: RobustStartupManager(context.applicationContext).also {
                    INSTANCE = it
                }
            }
        }
    }

    private val _startupState = MutableStateFlow(StartupState.Initializing)
    val startupState: StateFlow<StartupState> = _startupState.asStateFlow()

    private val startupScope = CoroutineScope(
        SupervisorJob() + Dispatchers.Main.immediate
    )

    // 启动任务注册表
    private val taskRegistry = mutableMapOf<String, StartupTask>()

    // 故障恢复策略
    private val recoveryStrategies = mutableMapOf<String, RecoveryStrategy>()

    fun initialize() {
        startupScope.launch {
            try {
                executeStartupSequence()
            } catch (e: Exception) {
                handleCriticalFailure(e)
            }
        }
    }
}
```

## **分层启动架构**

**第一层：核心系统（<500ms）**
```kotlin
sealed class StartupTask(
    val name: String,
    val priority: Priority,
    val timeout: Duration,
    val isRequired: Boolean
) {
    enum class Priority { CRITICAL, HIGH, MEDIUM, LOW }

    abstract suspend fun execute(): TaskResult
    abstract fun onFailure(error: Throwable): RecoveryAction
}

class CoreSystemTask : StartupTask(
    name = "CoreSystem",
    priority = Priority.CRITICAL,
    timeout = 500.milliseconds,
    isRequired = true
) {
    override suspend fun execute(): TaskResult = withTimeout(timeout.inWholeMilliseconds) {
        try {
            // 只初始化绝对必要的组件
            initializeLogging()
            initializeErrorReporting()
            initializeBasicDI()

            TaskResult.Success("Core systems initialized")
        } catch (e: Exception) {
            TaskResult.Failure(e, canRecover = false)
        }
    }

    override fun onFailure(error: Throwable): RecoveryAction {
        // 核心系统失败 = 应用无法启动
        return RecoveryAction.Abort("Critical system failure: ${error.message}")
    }

    private suspend fun initializeLogging() {
        // 使用最简单的日志配置
        if (BuildConfig.DEBUG) {
            Timber.plant(Timber.DebugTree())
        } else {
            Timber.plant(CrashReportingTree())
        }
    }
}
```

**第二层：基础服务（<2s）**
```kotlin
class DatabaseTask : StartupTask(
    name = "Database",
    priority = Priority.HIGH,
    timeout = 2.seconds,
    isRequired = true
) {
    override suspend fun execute(): TaskResult = withContext(Dispatchers.IO) {
        withTimeout(timeout.inWholeMilliseconds) {
            try {
                val database = initializeDatabase()
                validateDatabaseIntegrity(database)
                TaskResult.Success("Database ready")
            } catch (e: SQLiteException) {
                TaskResult.Failure(e, canRecover = true)
            } catch (e: Exception) {
                TaskResult.Failure(e, canRecover = false)
            }
        }
    }

    override fun onFailure(error: Throwable): RecoveryAction {
        return when (error) {
            is SQLiteException -> RecoveryAction.Retry(
                maxAttempts = 2,
                backoffStrategy = BackoffStrategy.Linear(500.milliseconds)
            )
            else -> RecoveryAction.Degrade("Using in-memory storage")
        }
    }

    private suspend fun initializeDatabase(): AppDatabase {
        return Room.databaseBuilder(
            context,
            AppDatabase::class.java,
            "gymbro_database"
        )
        .setQueryCallback(DatabaseQueryCallback(), Dispatchers.IO)
        .fallbackToDestructiveMigration() // 紧急情况下的降级策略
        .build()
    }
}
```

**第三层：网络服务（<3s）**
```kotlin
class NetworkServicesTask : StartupTask(
    name = "NetworkServices",
    priority = Priority.HIGH,
    timeout = 3.seconds,
    isRequired = false // 可以离线工作
) {
    override suspend fun execute(): TaskResult = withContext(Dispatchers.IO) {
        withTimeout(timeout.inWholeMilliseconds) {
            try {
                initializeFirebase()
                performConnectivityCheck()
                initializeApiServices()
                TaskResult.Success("Network services ready")
            } catch (e: Exception) {
                TaskResult.Failure(e, canRecover = true)
            }
        }
    }

    override fun onFailure(error: Throwable): RecoveryAction {
        return RecoveryAction.Degrade("Working in offline mode")
    }

    private suspend fun performConnectivityCheck(): Boolean {
        return try {
            val request = Request.Builder()
                .url("https://api.yourdomain.com/health")
                .head()
                .build()

            val response = okHttpClient.newCall(request).await()
            response.isSuccessful
        } catch (e: Exception) {
            false
        }
    }
}
```

## **故障恢复机制**

```kotlin
sealed class RecoveryAction {
    object None : RecoveryAction()
    data class Retry(
        val maxAttempts: Int,
        val backoffStrategy: BackoffStrategy
    ) : RecoveryAction()
    data class Degrade(val reason: String) : RecoveryAction()
    data class Abort(val reason: String) : RecoveryAction()
}

class TaskRecoveryManager {
    suspend fun handleTaskFailure(
        task: StartupTask,
        error: Throwable,
        action: RecoveryAction
    ): TaskResult {
        return when (action) {
            is RecoveryAction.Retry -> retryTask(task, action)
            is RecoveryAction.Degrade -> degradeGracefully(task, action)
            is RecoveryAction.Abort -> abortStartup(action)
            RecoveryAction.None -> TaskResult.Failure(error, canRecover = false)
        }
    }

    private suspend fun retryTask(
        task: StartupTask,
        retryAction: RecoveryAction.Retry
    ): TaskResult {
        repeat(retryAction.maxAttempts) { attempt ->
            delay(retryAction.backoffStrategy.getDelay(attempt))

            val result = try {
                task.execute()
            } catch (e: Exception) {
                TaskResult.Failure(e, canRecover = true)
            }

            if (result is TaskResult.Success) {
                return result
            }
        }

        return TaskResult.Failure(
            RuntimeException("Task ${task.name} failed after ${retryAction.maxAttempts} attempts"),
            canRecover = false
        )
    }
}
```

## **启动状态管理**

```kotlin
sealed class StartupState {
    object Initializing : StartupState()
    data class InProgress(
        val completedTasks: Int,
        val totalTasks: Int,
        val currentTask: String
    ) : StartupState()
    data class Degraded(
        val availableFeatures: Set<String>,
        val unavailableFeatures: Set<String>
    ) : StartupState()
    object Ready : StartupState()
    data class Failed(val error: String) : StartupState()
}

class StartupStateManager {
    private val _state = MutableStateFlow<StartupState>(StartupState.Initializing)
    val state: StateFlow<StartupState> = _state.asStateFlow()

    fun updateProgress(completed: Int, total: Int, current: String) {
        _state.value = StartupState.InProgress(completed, total, current)
    }

    fun markAsDegraded(available: Set<String>, unavailable: Set<String>) {
        _state.value = StartupState.Degraded(available, unavailable)
    }

    fun markAsReady() {
        _state.value = StartupState.Ready
    }

    fun markAsFailed(error: String) {
        _state.value = StartupState.Failed(error)
    }
}
```

## **智能启动调度器**

```kotlin
class StartupScheduler(
    private val stateManager: StartupStateManager,
    private val recoveryManager: TaskRecoveryManager
) {
    private val taskQueue = PriorityQueue<StartupTask> { a, b ->
        a.priority.ordinal.compareTo(b.priority.ordinal)
    }

    suspend fun executeStartupSequence() {
        val totalTasks = taskQueue.size
        var completedTasks = 0
        val failedTasks = mutableListOf<String>()
        val degradedFeatures = mutableSetOf<String>()

        while (taskQueue.isNotEmpty()) {
            val task = taskQueue.poll()
            stateManager.updateProgress(completedTasks, totalTasks, task.name)

            val result = executeTaskWithRecovery(task)

            when (result) {
                is TaskResult.Success -> {
                    completedTasks++
                    Timber.d("✅ Task ${task.name} completed successfully")
                }
                is TaskResult.Failure -> {
                    if (task.isRequired) {
                        stateManager.markAsFailed("Critical task ${task.name} failed")
                        return
                    } else {
                        failedTasks.add(task.name)
                        degradedFeatures.add(task.name)
                        Timber.w("⚠️ Non-critical task ${task.name} failed, continuing...")
                    }
                }
                is TaskResult.Degraded -> {
                    completedTasks++
                    degradedFeatures.add(task.name)
                    Timber.w("🔄 Task ${task.name} running in degraded mode")
                }
            }
        }

        // 确定最终状态
        if (degradedFeatures.isNotEmpty()) {
            val availableFeatures = getAllFeatures() - degradedFeatures
            stateManager.markAsDegraded(availableFeatures, degradedFeatures)
        } else {
            stateManager.markAsReady()
        }
    }

    private suspend fun executeTaskWithRecovery(task: StartupTask): TaskResult {
        return try {
            task.execute()
        } catch (e: Exception) {
            val recoveryAction = task.onFailure(e)
            recoveryManager.handleTaskFailure(task, e, recoveryAction)
        }
    }
}
```

## **Application 集成**

```kotlin
class GymBroApp : Application() {
    private lateinit var startupManager: RobustStartupManager

    override fun onCreate() {
        super.onCreate()

        // 立即初始化启动管理器
        startupManager = RobustStartupManager.getInstance(this)

        // 注册启动任务
        registerStartupTasks()

        // 开始启动流程
        startupManager.initialize()
    }

    private fun registerStartupTasks() {
        startupManager.apply {
            registerTask(CoreSystemTask())
            registerTask(DatabaseTask())
            registerTask(NetworkServicesTask())
            registerTask(HardwareDetectionTask()) // 修复后的版本
            registerTask(BgeEngineTask())
        }
    }
}
```

## **UI 层集成**

```kotlin
@Composable
fun StartupScreen(
    onStartupComplete: (StartupState) -> Unit
) {
    val startupManager = remember { RobustStartupManager.getInstance(LocalContext.current) }
    val startupState by startupManager.startupState.collectAsState()

    LaunchedEffect(startupState) {
        when (startupState) {
            is StartupState.Ready,
            is StartupState.Degraded -> {
                onStartupComplete(startupState)
            }
            is StartupState.Failed -> {
                // 显示错误界面或重试选项
            }
            else -> { /* 继续显示加载界面 */ }
        }
    }

    StartupUI(startupState)
}
```

这个健壮的启动流程具备以下特点：

1. **故障隔离** - 单个组件失败不会影响整个应用
2. **优雅降级** - 非关键功能失败时应用仍可使用
3. **快速恢复** - 智能重试机制
4. **透明监控** - 详细的状态反馈
5. **前瞻性设计** - 易于扩展和维护

Ted，这才是真正的"最好的app"应该有的启动流程。准备好实施这个架构了吗？
