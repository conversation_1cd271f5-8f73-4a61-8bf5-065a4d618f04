package com.example.gymbro.core.logging

import android.util.Log
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 【新增】日志控制器
 *
 * 提供运行时日志控制功能，方便调试和性能优化
 */
@Singleton
class LoggingController
    @Inject
    constructor(
        private val timberManager: TimberManager,
        private val loggingConfig: LoggingConfig,
    ) {
        /**
         * 快速配置预设
         */
        enum class LoggingPreset {
            SILENT, // 静音模式：只记录错误
            MINIMAL, // 最小模式：只记录关键信息
            NORMAL, // 正常模式：适度日志
            DEBUG, // 调试模式：详细日志
            VERBOSE, // 详细模式：所有日志
            TOKEN_FLOW_DEBUG, // 🔥 TOKEN-FLOW 调试模式：专门用于调试流式响应
        }

        /**
         * 应用日志预设
         */
        fun applyPreset(preset: LoggingPreset) {
            when (preset) {
                LoggingPreset.SILENT -> {
                    // 静音模式：只记录错误
                    loggingConfig.updateModuleConfig(
                        LoggingConfig.MODULE_THINKING_BOX,
                        LoggingConfig.ModuleLogConfig(
                            enabled = true,
                            minLevel = Log.ERROR,
                            tags = setOf("TB-ERROR"),
                            sampleRate = 1,
                        ),
                    )
                    Timber.tag("LOG-CTRL").i("🔇 已切换到静音模式")
                }

                LoggingPreset.MINIMAL -> {
                    // 最小模式：只记录关键信息
                    timberManager.disableThinkingBoxVerboseLogs()
                    Timber.tag("LOG-CTRL").i("🔕 已切换到最小日志模式")
                }

                LoggingPreset.NORMAL -> {
                    // 正常模式：适度日志
                    loggingConfig.updateModuleConfig(
                        LoggingConfig.MODULE_THINKING_BOX,
                        LoggingConfig.ModuleLogConfig(
                            enabled = true,
                            minLevel = Log.INFO,
                            tags =
                                setOf(
                                    "TB-ERROR",
                                    "TB-STATE",
                                    "TB-UI",
                                ),
                            sampleRate = 1,
                        ),
                    )
                    Timber.tag("LOG-CTRL").i("🔔 已切换到正常日志模式")
                }

                LoggingPreset.DEBUG -> {
                    // 调试模式：详细日志（采样）
                    timberManager.enableThinkingBoxDebugLogs()
                    Timber.tag("LOG-CTRL").i("🔊 已切换到调试模式")
                }

                LoggingPreset.VERBOSE -> {
                    // 详细模式：所有日志
                    loggingConfig.updateModuleConfig(
                        LoggingConfig.MODULE_THINKING_BOX,
                        LoggingConfig.ModuleLogConfig(
                            enabled = true,
                            minLevel = Log.VERBOSE,
                            tags = setOf(), // 空集合表示允许所有标签
                            sampleRate = 1,
                        ),
                    )
                    Timber.tag("LOG-CTRL").i("📢 已切换到详细模式")
                }

                LoggingPreset.TOKEN_FLOW_DEBUG -> {
                    // 🔥 TOKEN-FLOW 调试模式：专门用于调试流式响应
                    timberManager.enableTokenFlowDebugLogs()
                    Timber.tag("LOG-CTRL").i("🚀 已切换到 TOKEN-FLOW 调试模式")
                }
            }
        }

        /**
         * 🔥 ThinkingBox 专用控制
         */
        object ThinkingBox {
            /**
             * 完全关闭 ThinkingBox 日志
             */
            fun disable() {
                val controller =
                    LoggingController::class.java.getDeclaredField("INSTANCE").get(null) as? LoggingController
                controller?.loggingConfig?.updateModuleConfig(
                    LoggingConfig.MODULE_THINKING_BOX,
                    LoggingConfig.ModuleLogConfig(
                        enabled = false,
                        minLevel = Log.ERROR,
                        tags = emptySet(),
                        sampleRate = 1,
                    ),
                )
                Timber.tag("LOG-CTRL").i("🚫 ThinkingBox 日志已完全关闭")
            }

            /**
             * 只显示错误日志
             */
            fun errorsOnly() {
                val controller =
                    LoggingController::class.java.getDeclaredField("INSTANCE").get(null) as? LoggingController
                controller?.loggingConfig?.updateModuleConfig(
                    LoggingConfig.MODULE_THINKING_BOX,
                    LoggingConfig.ModuleLogConfig(
                        enabled = true,
                        minLevel = Log.ERROR,
                        tags = setOf("TB-ERROR"),
                        sampleRate = 1,
                    ),
                )
                Timber.tag("LOG-CTRL").i("⚠️ ThinkingBox 只显示错误日志")
            }

            /**
             * 启用状态跟踪日志
             */
            fun enableStateTracking() {
                val controller =
                    LoggingController::class.java.getDeclaredField("INSTANCE").get(null) as? LoggingController
                controller?.loggingConfig?.updateModuleConfig(
                    LoggingConfig.MODULE_THINKING_BOX,
                    LoggingConfig.ModuleLogConfig(
                        enabled = true,
                        minLevel = Log.INFO,
                        tags =
                            setOf(
                                "TB-ERROR",
                                "TB-STATE",
                            ),
                        sampleRate = 1,
                    ),
                )
                Timber.tag("LOG-CTRL").i("📊 ThinkingBox 状态跟踪已启用")
            }
        }

        /**
         * 获取当前日志状态
         */
        fun getCurrentStatus(): String {
            val tbConfig = loggingConfig.getModuleConfig(LoggingConfig.MODULE_THINKING_BOX)
            val env = loggingConfig.getCurrentEnvironment()

            return buildString {
                appendLine("🔍 日志系统状态:")
                appendLine("环境: $env")
                appendLine("ThinkingBox: ${if (tbConfig?.enabled == true) "启用" else "禁用"}")
                appendLine("最小级别: ${getLevelName(tbConfig?.minLevel ?: Log.ERROR)}")
                appendLine("允许标签: ${tbConfig?.tags?.joinToString(", ") ?: "无"}")
                appendLine("采样率: ${tbConfig?.sampleRate ?: 1}")
            }
        }

        /**
         * 获取日志级别名称
         */
        private fun getLevelName(level: Int): String =
            when (level) {
                Log.VERBOSE -> "VERBOSE"
                Log.DEBUG -> "DEBUG"
                Log.INFO -> "INFO"
                Log.WARN -> "WARN"
                Log.ERROR -> "ERROR"
                Log.ASSERT -> "ASSERT"
                else -> "UNKNOWN"
            }

        /**
         * 性能监控：获取日志统计
         */
        fun getLogStats(): String {
            // 这里可以添加日志统计功能
            return "日志统计功能待实现"
        }

        companion object {
            // 单例实例，用于静态访问
            @Volatile
            private var INSTANCE: LoggingController? = null

            /**
             * 设置实例（由 Hilt 注入时调用）
             */
            internal fun setInstance(instance: LoggingController) {
                INSTANCE = instance
            }

            /**
             * 获取实例
             */
            fun getInstance(): LoggingController? = INSTANCE
        }
    }
