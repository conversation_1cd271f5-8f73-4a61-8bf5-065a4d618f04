package com.example.gymbro.data.exercise.integration

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ml.service.ExerciseEmbeddingService
import com.example.gymbro.core.ml.service.SimilarityResult
import com.example.gymbro.core.ml.service.VectorSearchService
import com.example.gymbro.data.exercise.local.dao.ExerciseDao
import com.example.gymbro.data.exercise.local.dao.ExerciseFtsDao
import com.example.gymbro.data.exercise.local.entity.ExerciseEntity
import com.example.gymbro.data.exercise.mapper.ExerciseMapper
import com.example.gymbro.domain.exercise.model.Exercise
import com.example.gymbro.domain.exercise.repository.ExerciseToolFormat
import com.example.gymbro.domain.exercise.repository.HybridSearchRepository
import com.example.gymbro.domain.exercise.repository.RelevanceScores
import com.example.gymbro.domain.exercise.repository.RelevantContent
import com.example.gymbro.domain.workout.model.json.VectorizedWorkoutState
import com.example.gymbro.shared.models.exercise.MuscleGroup
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 混合搜索引擎实现
 *
 * 整合FTS（全文搜索）和向量搜索的结果
 * 实现plan.md中定义的搜索策略：
 * 1. 先本地FTS搜索，分数≥0.8直接返回首条
 * 2. 否则跑向量搜索，余弦≥0.9返回
 * 3. 否则返回null（用于精确匹配）或合并结果（用于模糊搜索）
 *
 * 增强1.md功能集成：
 * - 支持训练状态相关的智能检索
 * - 提供多维度相关内容检索
 * - 集成上下文感知的搜索策略
 */
@Singleton
class HybridSearchEngine
@Inject
constructor(
    private val exerciseFtsDao: ExerciseFtsDao,
    private val exerciseDao: ExerciseDao,
    private val exerciseEmbeddingService: ExerciseEmbeddingService,
    private val vectorSearchService: VectorSearchService,
    private val exerciseMapper: ExerciseMapper,
    private val ioDispatcher: CoroutineDispatcher,
) : HybridSearchRepository {
    // 向量缓存：动作ID -> 向量
    private val vectorCache = mutableMapOf<String, FloatArray>()
    private val exerciseCache = mutableMapOf<String, ExerciseEntity>()

    /**
     * 初始化向量缓存
     * 从数据库加载所有包含embedding的动作
     */
    override suspend fun loadVectorCache() =
        withContext(ioDispatcher) {
            // 从数据库加载所有动作
            val exercises = exerciseDao.getAll()
            vectorCache.clear()
            exerciseCache.clear()

            exercises.forEach { exercise ->
                exercise.embedding?.let { embedding ->
                    vectorCache[exercise.id] = embedding
                    exerciseCache[exercise.id] = exercise
                }
            }

            Timber.i("向量缓存加载完成: ${vectorCache.size} 个动作向量")
        }

    /**
     * 构建动作的完整文本描述（用于向量化）
     * 包含：名称 + 肌肉群 + 器械 + 描述
     */
    private fun buildExerciseText(exercise: ExerciseEntity): String {
        val parts = mutableListOf<String>()

        // 动作名称（主要特征）
        parts.add(exercise.name)

        // 肌肉群信息
        parts.add(exercise.muscleGroup.name)
        exercise.targetMuscles.forEach { parts.add(it.name) }

        // 器械信息
        exercise.equipment.forEach { parts.add(it.name) }

        // 描述信息
        if (!exercise.description.isNullOrBlank()) {
            parts.add(exercise.description)
        }

        return parts.joinToString(" ")
    }

    /**
     * 预处理动作名称
     * 处理常见的简写、别名、中英文混合情况
     */
    private fun preprocessExerciseName(name: String): String =
        name
            .trim()
            .replace("练习", "")
            .replace("训练", "")
            .replace("动作", "")
            .replace(Regex("\\s+"), " ")

    /**
     * 精确匹配搜索 - 用于findByExactName
     *
     * 策略：
     * 1. FTS搜索，分数≥0.8直接返回首条
     * 2. 否则向量搜索，余弦≥0.9返回
     * 3. 否则返回null
     */
    override suspend fun findExactMatch(query: String): Exercise? =
        withContext(ioDispatcher) {
            if (query.isBlank()) return@withContext null

            try {
                // 第1步：FTS搜索
                val ftsResults =
                    exerciseFtsDao.searchFts(
                        query = prepareFtsQuery(query),
                        limit = 5,
                    )

                if (ftsResults.isNotEmpty()) {
                    val bestFtsMatch = ftsResults.first()
                    val ftsScore = calculateNameMatchScore(bestFtsMatch.name, query)

                    if (ftsScore >= 0.8f) {
                        Timber.d("FTS精确匹配成功: '$query' -> '${bestFtsMatch.name}' (score: $ftsScore)")
                        return@withContext exerciseMapper.toDomain(bestFtsMatch)
                    }
                }

                // 第2步：向量搜索
                val exerciseText = preprocessExerciseName(query)
                val embeddingResult = exerciseEmbeddingService.embedExerciseText(exerciseText)

                if (embeddingResult is com.example.gymbro.core.error.types.ModernResult.Success) {
                    val queryEmbedding = embeddingResult.data
                    val candidateVectors = vectorCache.values.toList()

                    val bestMatch =
                        vectorSearchService.findBestMatch(
                            queryVector = queryEmbedding,
                            candidateVectors = candidateVectors,
                            threshold = 0.9f,
                        )

                    if (bestMatch != null) {
                        val matchedExerciseId = vectorCache.keys.toList()[bestMatch.index]
                        val matchedExercise = exerciseCache[matchedExerciseId]

                        if (matchedExercise != null) {
                            Timber.d(
                                "向量精确匹配成功: '$query' -> '${matchedExercise.name}' (similarity: ${bestMatch.similarity})",
                            )
                            return@withContext exerciseMapper.toDomain(matchedExercise)
                        }
                    }
                }

                Timber.d("精确匹配失败: '$query'")
                null
            } catch (e: Exception) {
                Timber.e(e, "精确匹配搜索异常: '$query'")
                null
            }
        }

    /**
     * 模糊搜索 - 用于searchExercises
     *
     * 策略：
     * 1. 并行执行FTS和向量搜索
     * 2. 合并去重结果
     * 3. 按综合分数排序
     */
    override suspend fun hybridSearch(
        query: String,
        limit: Int,
    ): List<Exercise> =
        withContext(ioDispatcher) {
            if (query.isBlank()) return@withContext emptyList()

            try {
                // 第1步：FTS搜索
                val ftsResults =
                    exerciseFtsDao.searchFts(
                        query = prepareFtsQuery(query),
                        limit = limit,
                    )

                // 第2步：向量搜索
                val exerciseText = preprocessExerciseName(query)
                val embeddingResult = exerciseEmbeddingService.embedExerciseText(exerciseText)

                val vectorResults =
                    if (embeddingResult is com.example.gymbro.core.error.types.ModernResult.Success) {
                        val queryEmbedding = embeddingResult.data
                        val candidateVectors = vectorCache.values.toList()

                        vectorSearchService
                            .findMostSimilar(
                                queryVector = queryEmbedding,
                                candidateVectors = candidateVectors,
                                threshold = 0.5f,
                            ).take(limit)
                    } else {
                        emptyList()
                    }

                // 第3步：合并结果
                val combinedResults =
                    combineSearchResults(
                        ftsResults = ftsResults,
                        vectorResults = vectorResults,
                        query = query,
                    )

                // 第4步：转换为Domain对象并排序
                val exercises =
                    combinedResults
                        .sortedByDescending { it.combinedScore }
                        .take(limit)
                        .map { exerciseMapper.toDomain(it.exercise) }

                Timber.d(
                    "混合搜索完成: '$query' -> FTS=${ftsResults.size}, Vector=${vectorResults.size}, Combined=${exercises.size}",
                )
                exercises
            } catch (e: Exception) {
                Timber.e(e, "混合搜索异常: '$query'")
                emptyList()
            }
        }

    /**
     * 合并FTS和向量搜索结果
     */
    private fun combineSearchResults(
        ftsResults: List<ExerciseEntity>,
        vectorResults: List<SimilarityResult>,
        query: String,
    ): List<CombinedSearchResult> {
        val resultMap = mutableMapOf<String, CombinedSearchResult>()

        // 处理FTS结果
        ftsResults.forEachIndexed { index, exercise ->
            val ftsScore = calculateNameMatchScore(exercise.name, query)
            val ftsRankScore = 1.0f - (index.toFloat() / ftsResults.size) // 排名分数

            resultMap[exercise.id] =
                CombinedSearchResult(
                    exercise = exercise,
                    ftsScore = ftsScore,
                    vectorSimilarity = 0f,
                    ftsRankScore = ftsRankScore,
                    vectorRankScore = 0f,
                )
        }

        // 处理向量结果
        vectorResults.forEachIndexed { index, similarityResult ->
            val exerciseId = vectorCache.keys.toList().getOrNull(similarityResult.index)
            val exercise = exerciseId?.let { exerciseCache[it] }

            if (exercise != null) {
                val vectorRankScore = 1.0f - (index.toFloat() / vectorResults.size)

                val existing = resultMap[exercise.id]
                if (existing != null) {
                    // 更新现有结果
                    resultMap[exercise.id] =
                        existing.copy(
                            vectorSimilarity = similarityResult.similarity,
                            vectorRankScore = vectorRankScore,
                        )
                } else {
                    // 新增向量搜索结果
                    resultMap[exercise.id] =
                        CombinedSearchResult(
                            exercise = exercise,
                            ftsScore = 0f,
                            vectorSimilarity = similarityResult.similarity,
                            ftsRankScore = 0f,
                            vectorRankScore = vectorRankScore,
                        )
                }
            }
        }

        return resultMap.values.toList()
    }

    /**
     * 准备FTS查询字符串
     */
    private fun prepareFtsQuery(query: String): String {
        return query
            .trim()
            .replace(Regex("\\s+"), " ")
            .replace("\"", "\"\"") // 转义双引号
    }

    /**
     * 计算名称匹配分数
     * 简化版字符串相似度计算
     */
    private fun calculateNameMatchScore(
        exerciseName: String,
        query: String,
    ): Float {
        val name = exerciseName.lowercase().trim()
        val q = query.lowercase().trim()

        return when {
            name == q -> 1.0f
            name.contains(q) -> 0.8f
            q.contains(name) -> 0.7f
            name.split(" ").any { it.contains(q) } -> 0.6f
            else -> {
                // 简单的编辑距离相似度
                val maxLen = maxOf(name.length, q.length)
                val editDistance = calculateEditDistance(name, q)
                maxOf(0f, 1f - (editDistance.toFloat() / maxLen))
            }
        }
    }

    /**
     * 计算编辑距离（简化版）
     */
    private fun calculateEditDistance(
        s1: String,
        s2: String,
    ): Int {
        val dp = Array(s1.length + 1) { IntArray(s2.length + 1) }

        for (i in 0..s1.length) dp[i][0] = i
        for (j in 0..s2.length) dp[0][j] = j

        for (i in 1..s1.length) {
            for (j in 1..s2.length) {
                dp[i][j] =
                    if (s1[i - 1] == s2[j - 1]) {
                        dp[i - 1][j - 1]
                    } else {
                        1 + minOf(dp[i - 1][j], dp[i][j - 1], dp[i - 1][j - 1])
                    }
            }
        }

        return dp[s1.length][s2.length]
    }

    /**
     * 智能检索相关内容
     * 实现增强1.md中IntelligentVectorRetriever的核心功能
     *
     * @param userQuery 用户查询文本
     * @param vectorizedState 向量化的训练状态
     * @return 相关内容检索结果
     */
    override suspend fun retrieveRelevantContent(
        userQuery: String,
        vectorizedState: VectorizedWorkoutState?,
    ): RelevantContent =
        withContext(ioDispatcher) {
            try {
                // 1. 对用户问题进行向量化
                val queryEmbeddingResult = exerciseEmbeddingService.embedExerciseText(userQuery)

                val queryVector =
                    when (queryEmbeddingResult) {
                        is ModernResult.Success -> queryEmbeddingResult.data
                        else -> {
                            Timber.w("用户查询向量化失败，使用零向量")
                            FloatArray(384) { 0f }
                        }
                    }

                // 2. 多维度检索
                val queryRelevantExercises = findQueryRelevantExercises(userQuery, queryVector)
                val stateRelevantExercises = findStateRelevantExercises(vectorizedState)
                val contextualExercises = findContextualExercises(vectorizedState, userQuery)

                // 3. 计算相关性分数
                val relevanceScores = calculateRelevanceScores(queryVector, vectorizedState?.vector)

                RelevantContent(
                    queryRelevantExercises = queryRelevantExercises,
                    stateRelevantExercises = stateRelevantExercises,
                    contextualExercises = contextualExercises,
                    relevanceScores = relevanceScores,
                )
            } catch (e: Exception) {
                Timber.e(e, "智能检索失败")
                RelevantContent.Empty
            }
        }

    /**
     * 查找与用户查询相关的动作
     */
    private suspend fun findQueryRelevantExercises(
        userQuery: String,
        queryVector: FloatArray,
    ): List<ExerciseToolFormat> {
        // 先尝试FTS搜索
        val ftsResults = exerciseFtsDao.searchFts(userQuery, limit = 10)

        if (ftsResults.isNotEmpty()) {
            return ftsResults.take(3).mapNotNull { entity ->
                exerciseCache[entity.id]?.let { exercise ->
                    val similarity =
                        vectorCache[exercise.id]?.let { exerciseVector ->
                            cosineSimilarity(queryVector, exerciseVector)
                        } ?: 0f

                    ExerciseToolFormat(
                        id = exercise.id,
                        name = exercise.name,
                        muscleGroup = exercise.muscleGroup.name,
                        equipment = exercise.equipment.joinToString(", ") { it.name },
                        similarity = similarity,
                    )
                }
            }
        }

        // FTS无结果时，使用向量搜索
        return vectorSearchByQuery(queryVector, topK = 3)
    }

    /**
     * 查找与当前训练状态相关的动作
     */
    private suspend fun findStateRelevantExercises(
        vectorizedState: VectorizedWorkoutState?,
    ): List<ExerciseToolFormat> {
        vectorizedState ?: return emptyList()

        return vectorSearchByQuery(vectorizedState.vector, topK = 2)
    }

    /**
     * 查找上下文推荐动作
     */
    private suspend fun findContextualExercises(
        vectorizedState: VectorizedWorkoutState?,
        userQuery: String,
    ): List<ExerciseToolFormat> {
        vectorizedState ?: return emptyList()

        // 基于当前动作查找互补动作
        vectorizedState.exerciseId?.let { currentExerciseId ->
            val currentExercise = exerciseCache[currentExerciseId]
            currentExercise?.let { exercise ->
                return findComplementaryExercises(exercise).take(2)
            }
        }

        return emptyList()
    }

    /**
     * 使用向量搜索查找相似动作
     */
    private fun vectorSearchByQuery(
        queryVector: FloatArray,
        topK: Int,
    ): List<ExerciseToolFormat> {
        val similarities = mutableListOf<Pair<ExerciseEntity, Float>>()

        for ((exerciseId, exerciseVector) in vectorCache) {
            val exercise = exerciseCache[exerciseId] ?: continue
            val similarity = cosineSimilarity(queryVector, exerciseVector)

            if (similarity > 0.3f) { // 最小相似度阈值
                similarities.add(exercise to similarity)
            }
        }

        return similarities
            .sortedByDescending { it.second }
            .take(topK)
            .map { (exercise, similarity) ->
                ExerciseToolFormat(
                    id = exercise.id,
                    name = exercise.name,
                    muscleGroup = exercise.muscleGroup.name,
                    equipment = exercise.equipment.joinToString(", ") { it.name },
                    similarity = similarity,
                )
            }
    }

    /**
     * 查找互补动作
     */
    private fun findComplementaryExercises(currentExercise: ExerciseEntity): List<ExerciseToolFormat> {
        // 查找不同肌群但互补的动作
        val complementaryMuscleGroups =
            when (currentExercise.muscleGroup) {
                MuscleGroup.CHEST -> listOf(MuscleGroup.BACK)
                MuscleGroup.BACK -> listOf(MuscleGroup.CHEST)
                MuscleGroup.SHOULDERS -> listOf(MuscleGroup.BACK, MuscleGroup.CHEST)
                MuscleGroup.BICEPS -> listOf(MuscleGroup.TRICEPS)
                MuscleGroup.TRICEPS -> listOf(MuscleGroup.BICEPS)
                MuscleGroup.LEGS -> listOf(MuscleGroup.CORE)
                MuscleGroup.CORE -> listOf(MuscleGroup.LEGS)
                else -> emptyList()
            }

        return exerciseCache.values
            .filter { exercise ->
                exercise.id != currentExercise.id &&
                    exercise.muscleGroup in complementaryMuscleGroups
            }.take(3)
            .map { exercise ->
                ExerciseToolFormat(
                    id = exercise.id,
                    name = exercise.name,
                    muscleGroup = exercise.muscleGroup.name,
                    equipment = exercise.equipment.joinToString(", ") { it.name },
                    similarity = 0.8f, // 互补动作的默认相关性分数
                )
            }
    }

    /**
     * 计算相关性分数
     */
    private fun calculateRelevanceScores(
        queryVector: FloatArray?,
        stateVector: FloatArray?,
    ): RelevanceScores {
        val queryRelevance =
            if (queryVector != null && stateVector != null) {
                cosineSimilarity(queryVector, stateVector)
            } else {
                0f
            }

        return RelevanceScores(
            queryRelevance = queryRelevance,
            stateRelevance = queryVector?.let { calculateComplexity(it) } ?: 0f,
            contextualRelevance = stateVector?.let { calculateComplexity(it) } ?: 0f,
        )
    }

    /**
     * 计算向量复杂度
     */
    private fun calculateComplexity(vector: FloatArray): Float {
        val mean = vector.average().toFloat()
        val variance = vector.map { (it - mean) * (it - mean) }.average().toFloat()
        return kotlin.math.sqrt(variance)
    }

    /**
     * 计算余弦相似度
     */
    private fun cosineSimilarity(
        vector1: FloatArray,
        vector2: FloatArray,
    ): Float {
        require(vector1.size == vector2.size) { "向量维度不匹配" }

        var dotProduct = 0.0f
        var norm1 = 0.0f
        var norm2 = 0.0f

        for (i in vector1.indices) {
            dotProduct += vector1[i] * vector2[i]
            norm1 += vector1[i] * vector1[i]
            norm2 += vector2[i] * vector2[i]
        }

        norm1 = kotlin.math.sqrt(norm1)
        norm2 = kotlin.math.sqrt(norm2)

        return if (norm1 == 0f || norm2 == 0f) 0f else dotProduct / (norm1 * norm2)
    }
}

/**
 * 合并搜索结果
 */
private data class CombinedSearchResult(
    val exercise: ExerciseEntity,
    val ftsScore: Float,
    val vectorSimilarity: Float,
    val ftsRankScore: Float,
    val vectorRankScore: Float,
) {
    /**
     * 综合分数计算
     * FTS权重60%，向量搜索权重40%
     */
    val combinedScore: Float
        get() =
            (ftsScore * 0.3f + ftsRankScore * 0.3f) +
                (vectorSimilarity * 0.2f + vectorRankScore * 0.2f)
}
