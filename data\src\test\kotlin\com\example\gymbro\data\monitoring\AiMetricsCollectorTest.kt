package com.example.gymbro.data.monitoring

import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.ai.monitoring.AiMetricsCollector
import com.example.gymbro.domain.exercise.model.ExerciseSetDraft
import com.example.gymbro.domain.profile.model.user.FitnessGoal
import com.example.gymbro.domain.profile.model.user.FitnessPreference
import com.example.gymbro.domain.workout.model.DraftSource
import com.example.gymbro.domain.workout.model.TemplateDraft
import kotlinx.datetime.Clock
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import java.time.Duration

/**
 * AiMetricsCollector 单元测试
 * 验证 Grafana 监控指标收集功能
 *
 * Phase 5 Task 4: Grafana 监控设置
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class AiMetricsCollectorTest {
    private lateinit var metricsCollector: AiMetricsCollector

    @BeforeEach
    fun setUp() {
        metricsCollector = AiMetricsCollector()
        metricsCollector.resetMetrics() // 确保每个测试开始时指标为空
    }

    @Test
    fun `recordGoalMatch should track goal match rate correctly`() {
        // Given - 记录一些目标匹配结果
        metricsCollector.recordGoalMatch("muscle_gain", "gpt-4", true)
        metricsCollector.recordGoalMatch("muscle_gain", "gpt-4", true)
        metricsCollector.recordGoalMatch("weight_loss", "gpt-4", false)
        metricsCollector.recordGoalMatch("strength", "gpt-4", true)

        // When
        val snapshot = metricsCollector.getMetricsSnapshot()

        // Then
        assertEquals(4, snapshot.totalRequests, "总请求数应为4")
        assertEquals(0.75, snapshot.goalMatchRate, 0.01, "目标匹配率应为75%")
        assertEquals(2, snapshot.goalMatchesByType["muscle_gain"], "muscle_gain匹配数应为2")
        assertEquals(1, snapshot.goalMatchesByType["strength"], "strength匹配数应为1")
        assertNull(snapshot.goalMatchesByType["weight_loss"], "weight_loss无匹配记录")
    }

    @Test
    fun `recordPreferenceInjection should track injection success rate`() {
        // Given - 记录偏好注入结果
        metricsCollector.recordPreferenceInjection(true)
        metricsCollector.recordPreferenceInjection(true)
        metricsCollector.recordPreferenceInjection(false)
        metricsCollector.recordPreferenceInjection(true)

        // When
        val snapshot = metricsCollector.getMetricsSnapshot()

        // Then
        assertEquals(0.75, snapshot.preferenceInjectionRate, 0.01, "偏好注入成功率应为75%")
    }

    @Test
    fun `recordAiResponseTime should track response time statistics`() {
        // Given - 记录响应时间
        metricsCollector.recordAiResponseTime(Duration.ofMillis(1000), "gpt-4", "workout_generation")
        metricsCollector.recordAiResponseTime(Duration.ofMillis(2000), "gpt-4", "workout_generation")
        metricsCollector.recordAiResponseTime(Duration.ofMillis(3000), "gpt-4", "workout_generation")

        // When
        val snapshot = metricsCollector.getMetricsSnapshot()

        // Then
        assertEquals(2000.0, snapshot.averageResponseTimeMs, 0.1, "平均响应时间应为2000ms")
        assertEquals(3000.0, snapshot.maxResponseTimeMs, 0.1, "最大响应时间应为3000ms")
    }

    @Test
    fun `recordUserPreferenceCoverage should track coverage rate`() {
        // Given - 记录用户偏好覆盖情况
        metricsCollector.recordUserPreferenceCoverage(true)
        metricsCollector.recordUserPreferenceCoverage(false)
        metricsCollector.recordUserPreferenceCoverage(true)
        metricsCollector.recordUserPreferenceCoverage(true)

        // When
        val snapshot = metricsCollector.getMetricsSnapshot()

        // Then
        assertEquals(0.75, snapshot.userPreferenceCoverageRate, 0.01, "用户偏好覆盖率应为75%")
    }

    @Test
    fun `analyzeGoalMatch should correctly identify muscle gain goals`() {
        // Given - 增肌相关的训练模板
        val preference = FitnessPreference(primaryGoal = FitnessGoal.MUSCLE_GAIN)
        val now = Clock.System.now()
        val template =
            TemplateDraft(
                id = "test_template",
                name = "增肌力量训练",
                description = "专注于肌肉增长的重量训练计划",
                exercises =
                listOf(
                    ExerciseSetDraft(
                        id = "ex1",
                        name = UiText.DynamicString("杠铃深蹲"),
                        targetMuscles = listOf("腿部"),
                        equipment = listOf("杠铃"),
                        sets = 3,
                        reps = "8-12",
                        notes = "增强腿部肌肉力量",
                    ),
                ),
                source = DraftSource.AI_GENERATED,
                createdAt = now,
                updatedAt = now,
                userId = "test_user",
                estimatedDuration = 60,
                difficulty = 3,
                tags = emptyList(),
            )

        // When
        val isMatched = metricsCollector.analyzeGoalMatch(template, preference)

        // Then
        assertTrue(isMatched, "应该识别出增肌目标匹配")
    }

    @Test
    fun `analyzeGoalMatch should correctly identify weight loss goals`() {
        // Given - 减脂相关的训练模板
        val preference = FitnessPreference(primaryGoal = FitnessGoal.WEIGHT_LOSS)
        val now = Clock.System.now()
        val template =
            TemplateDraft(
                id = "test_template",
                name = "有氧减脂训练",
                description = "高强度有氧运动，快速燃脂减重",
                exercises =
                listOf(
                    ExerciseSetDraft(
                        id = "ex1",
                        name = UiText.DynamicString("跑步机训练"),
                        targetMuscles = listOf("心肺"),
                        equipment = listOf("跑步机"),
                        sets = 1,
                        reps = "30分钟",
                        notes = "提高心率，燃烧卡路里",
                    ),
                ),
                source = DraftSource.AI_GENERATED,
                createdAt = now,
                updatedAt = now,
                userId = "test_user",
                estimatedDuration = 45,
                difficulty = 2,
                tags = emptyList(),
            )

        // When
        val isMatched = metricsCollector.analyzeGoalMatch(template, preference)

        // Then
        assertTrue(isMatched, "应该识别出减脂目标匹配")
    }

    @Test
    fun `analyzeGoalMatch should return false for mismatched goals`() {
        // Given - 增肌偏好但瑜伽模板
        val preference = FitnessPreference(primaryGoal = FitnessGoal.MUSCLE_GAIN)
        val now = Clock.System.now()
        val template =
            TemplateDraft(
                id = "test_template",
                name = "瑜伽放松",
                description = "舒缓身心的瑜伽练习",
                exercises =
                listOf(
                    ExerciseSetDraft(
                        id = "ex1",
                        name = UiText.DynamicString("冥想"),
                        targetMuscles = emptyList(),
                        equipment = emptyList(),
                        sets = 1,
                        reps = "10分钟",
                        notes = "放松心情",
                    ),
                ),
                source = DraftSource.AI_GENERATED,
                createdAt = now,
                updatedAt = now,
                userId = "test_user",
                estimatedDuration = 30,
                difficulty = 1,
                tags = emptyList(),
            )

        // When
        val isMatched = metricsCollector.analyzeGoalMatch(template, preference)

        // Then
        assertFalse(isMatched, "不匹配的目标应该返回false")
    }

    @Test
    fun `analyzeGoalMatch should return false for no goal preference`() {
        // Given - 无目标偏好
        val preference = FitnessPreference(primaryGoal = null)
        val now = Clock.System.now()
        val template =
            TemplateDraft(
                id = "test_template",
                name = "全身训练",
                description = "综合性训练计划",
                exercises = emptyList(),
                source = DraftSource.AI_GENERATED,
                createdAt = now,
                updatedAt = now,
                userId = "test_user",
                estimatedDuration = 60,
                difficulty = 3,
                tags = emptyList(),
            )

        // When
        val isMatched = metricsCollector.analyzeGoalMatch(template, preference)

        // Then
        assertFalse(isMatched, "无目标偏好应该返回false")
    }

    @Test
    fun `getMetricsSnapshot should return correct initial values`() {
        // When - 获取初始指标快照
        val snapshot = metricsCollector.getMetricsSnapshot()

        // Then - 验证初始值
        assertEquals(0.0, snapshot.goalMatchRate, "初始目标匹配率应为0")
        assertEquals(0.0, snapshot.preferenceInjectionRate, "初始偏好注入率应为0")
        assertEquals(0.0, snapshot.averageResponseTimeMs, "初始平均响应时间应为0")
        assertEquals(0.0, snapshot.maxResponseTimeMs, "初始最大响应时间应为0")
        assertEquals(0.0, snapshot.userPreferenceCoverageRate, "初始用户偏好覆盖率应为0")
        assertEquals(0, snapshot.totalRequests, "初始总请求数应为0")
        assertTrue(snapshot.goalMatchesByType.isEmpty(), "初始目标匹配分类应为空")
    }

    @Test
    fun `resetMetrics should clear all counters`() {
        // Given - 记录一些指标
        metricsCollector.recordGoalMatch("muscle_gain", "gpt-4", true)
        metricsCollector.recordPreferenceInjection(true)
        metricsCollector.recordAiResponseTime(Duration.ofMillis(1000), "gpt-4", "test")
        metricsCollector.recordUserPreferenceCoverage(true)

        // When - 重置指标
        metricsCollector.resetMetrics()
        val snapshot = metricsCollector.getMetricsSnapshot()

        // Then - 验证所有指标已重置
        assertEquals(0.0, snapshot.goalMatchRate, "重置后目标匹配率应为0")
        assertEquals(0.0, snapshot.preferenceInjectionRate, "重置后偏好注入率应为0")
        assertEquals(0.0, snapshot.averageResponseTimeMs, "重置后平均响应时间应为0")
        assertEquals(0.0, snapshot.maxResponseTimeMs, "重置后最大响应时间应为0")
        assertEquals(0.0, snapshot.userPreferenceCoverageRate, "重置后用户偏好覆盖率应为0")
        assertEquals(0, snapshot.totalRequests, "重置后总请求数应为0")
        assertTrue(snapshot.goalMatchesByType.isEmpty(), "重置后目标匹配分类应为空")
    }

    @Test
    fun `goal match rate should handle edge cases correctly`() {
        // Given - 只有失败的匹配
        metricsCollector.recordGoalMatch("muscle_gain", "gpt-4", false)
        metricsCollector.recordGoalMatch("weight_loss", "gpt-4", false)

        // When
        val snapshot = metricsCollector.getMetricsSnapshot()

        // Then
        assertEquals(2, snapshot.totalRequests, "总请求数应为2")
        assertEquals(0.0, snapshot.goalMatchRate, "全部失败时匹配率应为0")
        assertTrue(snapshot.goalMatchesByType.isEmpty(), "失败匹配不应记录在分类中")
    }
}
