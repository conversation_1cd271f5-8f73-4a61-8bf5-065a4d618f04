# Workout 模块文件清理计划

## 需要删除的旧文件和目录

### 1. 旧的数据库文件
- `WorkoutDatabase.kt` (根目录下的旧数据库)
- `database/WorkoutDatabase.kt` (重复的数据库文件)

### 2. 旧的转换器目录
- `converter/` 目录及其内容
- `converters/` 目录及其内容

### 3. 旧的 DAO 文件
- `dao/AiFeedbackDao.kt`
- `dao/ExerciseDao.kt` (与新的 exercise/dao/ExerciseDao.kt 重复)
- `dao/WorkoutExerciseDao.kt`
- `dao/WorkoutSessionDao.kt`
- `dao/WorkoutSetDao.kt`
- `dao/WorkoutTemplateDao.kt`

### 4. 旧的实体文件
- `entity/AiFeedbackEntity.kt`
- `entity/ExerciseEntity.kt` (与新的 exercise/entity/ExerciseEntity.kt 重复)
- `entity/WorkoutExerciseEntity.kt`
- `entity/WorkoutSessionEntity.kt`
- `entity/WorkoutSessionWithExerciseSetsEntity.kt`
- `entity/WorkoutSetEntity.kt`
- `entity/WorkoutTemplateEntity.kt`
- `entity/WorkoutTemplateExerciseLinkEntity.kt`
- `entity/WorkoutTemplateWithExercisesEntity.kt`

### 5. 旧的映射器文件
- `mapper/AiFeedback.mapper.kt`
- `mapper/Exercise.mapper.kt` (与新的 exercise/mapper/ExerciseMapper.kt 重复)
- `mapper/TemplateDraftMapper.kt`
- `mapper/Workout.mapper.kt`
- `mapper/WorkoutSession.mapper.kt`
- `mapper/WorkoutTemplate.mapper.kt`

### 6. 旧的数据源文件
- `datasource/` 目录及其内容

### 7. 旧的本地数据文件
- `local/` 目录及其内容

### 8. 旧的仓库实现文件
- `repository/ActiveTrainingRepositoryImpl.kt`
- `repository/CalendarRepositoryImpl.kt`
- `repository/ExerciseCrudRepository.kt`
- `repository/ExerciseSetRepositoryImpl.kt`
- `repository/MeasurementRepositoryImpl.kt`
- `repository/TimerRepositoryImpl.kt`
- `repository/WorkoutDraftRepositoryImpl.kt`
- `repository/WorkoutPlanLocalDataSource.kt`
- `repository/WorkoutPlanManager.kt`
- `repository/WorkoutPlanRepositoryImpl.kt`
- `repository/WorkoutRepositoryImpl.kt`
- `repository/WorkoutStatsRepositoryImpl.kt`
- `repository/WorkoutTemplateRepositoryImpl.kt`
- `repository/WorkoutTemplatesRepositoryImpl.kt`
- `repository/WorkoutTimelineRepositoryImpl.kt`
- `repository/plan/` 目录及其内容

### 9. 其他文件
- `datahub/` 目录及其内容
- `service/` 目录及其内容

## 保留的新架构文件

### 四数据库架构 (新创建的)
- `exercise/` - ExerciseDB 相关文件
- `template/` - TemplateDB 相关文件  
- `plan/` - PlanDB 相关文件
- `session/` - SessionDB 相关文件

### 保留的有用文件
- `initializer/ExerciseDataInitializer.kt` - 动作数据初始化器
- `model/ExerciseData.kt` - 动作数据模型
- `model/ExerciseDataProvider.kt` - 动作数据提供者
- `remote/TemplateSyncApi.kt` - 模板同步API

## 清理后的目录结构

```
data/src/main/kotlin/com/example/gymbro/data/workout/
├── exercise/          # ExerciseDB
│   ├── entity/
│   ├── dao/
│   ├── database/
│   ├── converter/
│   ├── mapper/
│   └── repository/
├── template/          # TemplateDB
│   ├── entity/
│   ├── dao/
│   ├── database/
│   ├── converter/
│   ├── mapper/
│   └── repository/
├── plan/              # PlanDB
│   ├── entity/
│   ├── dao/
│   ├── database/
│   ├── converter/
│   ├── mapper/
│   └── repository/
├── session/           # SessionDB
│   ├── entity/
│   ├── dao/
│   ├── database/
│   ├── converter/
│   ├── mapper/
│   └── repository/
├── initializer/       # 保留
├── model/            # 保留
└── remote/           # 保留
```
