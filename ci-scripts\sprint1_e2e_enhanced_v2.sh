#!/bin/bash
# Sprint 1 增强版 E2E 测试脚本 v2.0
# 核心改进: 负面测试 + 轮询检查 + 数据驱动 + 错误恢复

set -e

echo "🚀 Sprint 1 E2E验证 v2.0 开始 - $(date '+%Y-%m-%d %H:%M:%S')"

# 环境配置
TEST_BASE_URL="${TEST_BASE_URL:-http://localhost:8080}"
TEST_USER_ID="${TEST_USER_ID:-test_user_$(date +%s)}"
LOG_FILE="/tmp/sprint1_e2e_v2_$(date +%Y%m%d_%H%M%S).log"
SCENARIO_DIR="$(dirname "$0")/../test/e2e/scenarios"

# 性能指标收集
declare -A METRICS
declare -A TEST_RESULTS
START_TIME=$(date +%s.%3N)

# 辅助函数
log_info() {
    echo "ℹ️  $1" | tee -a $LOG_FILE
}

log_error() {
    echo "❌ $1" | tee -a $LOG_FILE
}

log_success() {
    echo "✅ $1" | tee -a $LOG_FILE
}

measure_time() {
    local start_time=$1
    local end_time=$(date +%s.%3N)
    echo "scale=3; $end_time - $start_time" | bc
}

# 健壮性核心：轮询检查机制（消除sleep依赖）
wait_for_api_response() {
    local url=$1
    local expected_status=${2:-200}
    local timeout=${3:-30}
    local interval=0.5
    local elapsed=0

    log_info "等待API响应: $url (期望状态: $expected_status)"

    while [ $(echo "$elapsed < $timeout" | bc) -eq 1 ]; do
        local status_code=$(curl -s -o /dev/null -w "%{http_code}" "$url" || echo "000")

        if [[ "$status_code" == "$expected_status" ]]; then
            log_success "API响应正常: $url ($status_code)"
            return 0
        fi

        sleep $interval
        elapsed=$(echo "$elapsed + $interval" | bc)
    done

    log_error "API等待超时: $url (${timeout}s)"
    return 1
}

wait_for_app_state() {
    local session_id=$1
    local expected_status=$2
    local timeout=${3:-10}
    local interval=0.5
    local elapsed=0

    log_info "等待应用状态: $session_id -> $expected_status"

    while [ $(echo "$elapsed < $timeout" | bc) -eq 1 ]; do
        local current_state=$(adb shell content query \
            --uri content://com.example.gymbro.provider/workout_sessions \
            --where "id='$session_id'" \
            --projection "status" 2>/dev/null || echo "")

        if [[ "$current_state" == *"$expected_status"* ]]; then
            log_success "应用状态已变为: $expected_status"
            return 0
        fi

        sleep $interval
        elapsed=$(echo "$elapsed + $interval" | bc)
    done

    log_error "应用状态等待超时: $expected_status (${timeout}s)"
    return 1
}

wait_for_json_field() {
    local url=$1
    local json_path=$2
    local expected_value=$3
    local timeout=${4:-15}
    local interval=0.5
    local elapsed=0

    while [ $(echo "$elapsed < $timeout" | bc) -eq 1 ]; do
        local response=$(curl -s "$url" 2>/dev/null || echo "{}")
        local actual_value=$(echo "$response" | jq -r "$json_path" 2>/dev/null || echo "null")

        if [[ "$actual_value" == "$expected_value" ]]; then
            log_success "JSON字段检查通过: $json_path = $expected_value"
            return 0
        fi

        sleep $interval
        elapsed=$(echo "$elapsed + $interval" | bc)
    done

    log_error "JSON字段检查超时: $json_path (期望: $expected_value)"
    return 1
}

# 数据驱动：测试场景加载器
load_test_scenarios() {
    local scenario_file="$SCENARIO_DIR/test_prompts.txt"

    if [[ ! -f "$scenario_file" ]]; then
        log_info "创建默认测试场景文件: $scenario_file"
        mkdir -p "$SCENARIO_DIR"
        cat > "$scenario_file" << 'EOF'
# Happy Path场景
帮我制定一个45分钟的胸肌+三头肌训练计划，我是中级水平
给我一个30分钟的腿部训练，包含深蹲和硬拉
设计一个背部+二头肌的训练计划，时长1小时，适合高级训练者

# Edge Case场景
制定一个训练计划
我想练
给我一个包含100个动作的超长训练计划，每个动作50组
EOF
    fi

    # 读取非注释行
    grep -v '^#' "$scenario_file" | grep -v '^$' | head -10
}

# 负面测试：Function Calling错误处理验证
test_function_calling_with_malformed_response() {
    log_info "负面测试1: Function Calling错误响应处理"

    local fc_start=$(date +%s.%3N)

    # 测试场景1: 空prompt
    local empty_response=$(curl -s -X POST $TEST_BASE_URL/api/coach/generate-template \
        -H "Content-Type: application/json" \
        -H "X-User-ID: $TEST_USER_ID" \
        -d '{"prompt": "", "userId": "'$TEST_USER_ID'"}')

    # 验证错误处理
    local error_handled=$(echo "$empty_response" | jq -r '.error // "none"')
    if [[ "$error_handled" == "none" ]]; then
        log_error "空prompt应该触发错误处理，但没有"
        return 1
    fi

    # 测试场景2: 恶意prompt（SQL注入风格）
    local malicious_response=$(curl -s -X POST $TEST_BASE_URL/api/coach/generate-template \
        -H "Content-Type: application/json" \
        -H "X-User-ID: $TEST_USER_ID" \
        -d '{"prompt": "DROP TABLE users; --", "userId": "'$TEST_USER_ID'"}')

    # 应该有安全过滤
    local security_filtered=$(echo "$malicious_response" | jq -r '.filtered // false')
    if [[ "$security_filtered" != "true" ]]; then
        log_error "恶意prompt未被安全过滤"
        return 1
    fi

    # 测试场景3: 超长prompt（16KB+）
    local long_prompt=$(printf 'A%.0s' {1..17000})  # 17KB prompt
    local oversized_response=$(curl -s -X POST $TEST_BASE_URL/api/coach/generate-template \
        -H "Content-Type: application/json" \
        -H "X-User-ID: $TEST_USER_ID" \
        -d '{"prompt": "'$long_prompt'", "userId": "'$TEST_USER_ID'"}')

    # 应该被payload限制拦截
    local payload_error=$(echo "$oversized_response" | jq -r '.error.code // "none"')
    if [[ "$payload_error" != "payload_too_large" ]]; then
        log_error "超大payload未被正确拦截: $payload_error"
        return 1
    fi

    local fc_time=$(measure_time $fc_start)
    METRICS[negative_function_calling_latency]=$fc_time
    log_success "Function Calling负面测试通过 (耗时: ${fc_time}s)"

    return 0
}

# 负面测试：API失败与重试验证
test_api_failure_and_retry() {
    log_info "负面测试2: API失败重试机制验证"

    local retry_start=$(date +%s.%3N)

    # 模拟API临时不可用（通过错误的端口）
    local bad_url="http://localhost:9999/api/coach/generate-template"

    # 验证客户端重试逻辑
    local retry_response=$(timeout 10s curl -s -X POST "$bad_url" \
        -H "Content-Type: application/json" \
        -d '{"prompt": "test", "userId": "'$TEST_USER_ID'"}' || echo '{"connection_failed": true}')

    # 验证连接失败被正确处理
    local connection_failed=$(echo "$retry_response" | jq -r '.connection_failed // false')
    if [[ "$connection_failed" != "true" ]]; then
        log_error "连接失败未被正确检测"
        return 1
    fi

    # 测试降级策略：当Function Call不可用时
    log_info "测试Function Call降级策略..."

    # 发送一个预期会触发降级的请求（通过特殊header模拟）
    local fallback_response=$(curl -s -X POST $TEST_BASE_URL/api/coach/generate-template \
        -H "Content-Type: application/json" \
        -H "X-Force-Fallback: true" \
        -H "X-User-ID: $TEST_USER_ID" \
        -d '{"prompt": "简单的胸肌训练", "userId": "'$TEST_USER_ID'"}')

    # 验证降级策略被触发
    local fallback_used=$(echo "$fallback_response" | jq -r '.method // "none"')
    if [[ "$fallback_used" != "json_prompt_fallback" ]]; then
        log_error "Function Call降级策略未被正确触发: $fallback_used"
        return 1
    fi

    local retry_time=$(measure_time $retry_start)
    METRICS[api_failure_handling_latency]=$retry_time
    log_success "API失败重试验证通过 (耗时: ${retry_time}s)"

    return 0
}

# 增强的Function Calling端到端测试（数据驱动）
test_function_calling_data_driven() {
    log_info "数据驱动测试: Function Calling多场景验证"

    local scenarios=($(load_test_scenarios))
    local success_count=0
    local total_count=${#scenarios[@]}

    for prompt in "${scenarios[@]}"; do
        if [[ -z "$prompt" || "$prompt" == \#* ]]; then
            continue
        fi

        log_info "测试场景: $prompt"

        local scenario_start=$(date +%s.%3N)
        local response=$(curl -s -X POST $TEST_BASE_URL/api/coach/generate-template \
            -H "Content-Type: application/json" \
            -H "X-User-ID: $TEST_USER_ID" \
            -d '{"prompt": "'"$prompt"'", "userId": "'$TEST_USER_ID'"}')

        # 基础验证：是否有function_call或降级响应
        if echo "$response" | jq -e '.function_call' > /dev/null; then
            # Function Call成功
            local arguments=$(echo "$response" | jq -r '.function_call.arguments')
            if python scripts/validate_template_schema.py <(echo "$arguments"); then
                success_count=$((success_count + 1))
                log_success "场景成功: Function Call + Schema验证通过"
            else
                log_error "场景失败: Schema验证失败"
            fi
        elif echo "$response" | jq -e '.method == "json_prompt_fallback"' > /dev/null; then
            # 降级成功
            success_count=$((success_count + 1))
            log_success "场景成功: 降级到JSON Prompt"
        else
            log_error "场景失败: 既不是Function Call也不是降级"
        fi

        local scenario_time=$(measure_time $scenario_start)
        log_info "场景耗时: ${scenario_time}s"
    done

    local success_rate=$(echo "scale=2; $success_count * 100 / $total_count" | bc)
    METRICS[data_driven_success_rate]=$success_rate

    if (( $(echo "$success_rate >= 90" | bc -l) )); then
        log_success "数据驱动测试通过: $success_count/$total_count (${success_rate}%)"
        return 0
    else
        log_error "数据驱动测试失败: $success_count/$total_count (${success_rate}%)"
        return 1
    fi
}

# 增强的训练会话状态管理测试（轮询机制）
test_robust_workout_state() {
    log_info "健壮性测试: 训练状态管理 + 进程恢复"

    local session_id=$(cat /tmp/created_session.json | jq -r '.sessionId' 2>/dev/null || echo "")
    if [[ -z "$session_id" ]]; then
        log_error "缺少会话ID，跳过状态管理测试"
        return 1
    fi

    # 启动应用到指定会话
    adb shell am start -n com.example.gymbro/.MainActivity \
        --es "session_id" "$session_id" \
        --ez "skip_intro" true

    # 使用轮询检查应用状态（消除sleep）
    if ! wait_for_app_state "$session_id" "ACTIVE" 15; then
        log_error "应用未能成功启动到ACTIVE状态"
        return 1
    fi

    # 验证UI响应时间
    local ui_start=$(date +%s.%3N)
    adb shell input tap 500 800  # 模拟点击

    # 等待UI更新完成（通过检查特定元素）
    if ! wait_for_json_field \
         "$TEST_BASE_URL/api/workout/sessions/$session_id/ui-state" \
         ".current_screen" \
         "active_workout" \
         5; then
        log_error "UI响应超时"
        return 1
    fi

    local ui_time=$(measure_time $ui_start)
    METRICS[ui_response_time_ms]=$(echo "$ui_time * 1000" | bc)

    # 验证性能指标
    if (( $(echo "$ui_time > 0.5" | bc -l) )); then
        log_error "UI响应时间超标: ${ui_time}s > 0.5s"
        return 1
    fi

    # 强制杀死应用进程
    log_info "测试进程重启恢复..."
    adb shell am force-stop com.example.gymbro

    # 重新启动应用
    adb shell am start -n com.example.gymbro/.MainActivity

    # 轮询检查状态恢复
    if ! wait_for_app_state "$session_id" "ACTIVE" 15; then
        log_error "进程重启后状态恢复失败"
        return 1
    fi

    log_success "训练状态管理健壮性测试通过"
    return 0
}

# 性能基准验证（增强版）
test_performance_benchmarks_enhanced() {
    log_info "性能基准验证（增强版）"

    local total_time=$(measure_time $START_TIME)
    METRICS[end_to_end_latency]=$total_time

    # 验证端到端时间≤30秒
    if (( $(echo "$total_time > 30" | bc -l) )); then
        log_error "端到端时间超标: ${total_time}s > 30s"
        return 1
    fi

    # 检查系统资源使用率
    local memory_usage=$(adb shell dumpsys meminfo com.example.gymbro | grep 'TOTAL PSS:' | awk '{print $3}' || echo "0")
    METRICS[memory_usage_kb]=$memory_usage

    # 内存使用不应超过100MB
    if [[ $memory_usage -gt 102400 ]]; then
        log_error "内存使用超标: ${memory_usage}KB > 100MB"
        return 1
    fi

    # 实时获取监控指标
    if wait_for_api_response "$TEST_BASE_URL/metrics" 200 5; then
        local metrics_response=$(curl -s "$TEST_BASE_URL/metrics")

        # Function Calling成功率
        local func_call_success_rate=$(echo "$metrics_response" | grep 'func_call_success_rate' | awk '{print $2}' || echo "0")
        METRICS[func_call_success_rate]=$func_call_success_rate

        if [[ -n "$func_call_success_rate" ]] && (( $(echo "$func_call_success_rate < 0.90" | bc -l) )); then
            log_error "Function Calling成功率低于90%: ${func_call_success_rate}"
            return 1
        fi

        # Schema错误率
        local schema_error_rate=$(echo "$metrics_response" | grep 'schema_error_rate' | awk '{print $2}' || echo "0")
        METRICS[schema_error_rate]=$schema_error_rate

        if [[ -n "$schema_error_rate" ]] && (( $(echo "$schema_error_rate > 0.05" | bc -l) )); then
            log_error "Schema错误率超过5%: ${schema_error_rate}"
            return 1
        fi
    else
        log_error "无法获取监控指标"
        return 1
    fi

    log_success "性能基准验证通过 (端到端: ${total_time}s, 内存: ${memory_usage}KB)"
    return 0
}

# 生成增强版测试报告
generate_enhanced_test_report() {
    local report_file="/tmp/sprint1_e2e_report_v2_$(date +%Y%m%d_%H%M%S).json"

    cat > $report_file << EOF
{
    "version": "2.0",
    "timestamp": "$(date -Iseconds)",
    "sprint": "Sprint 1",
    "test_user_id": "$TEST_USER_ID",
    "environment": {
        "base_url": "$TEST_BASE_URL",
        "adb_connected": $(adb devices | grep -c device),
        "test_scenarios_count": $(load_test_scenarios | wc -l)
    },
    "metrics": {
        "end_to_end_latency": ${METRICS[end_to_end_latency]:-0},
        "ui_response_time_ms": ${METRICS[ui_response_time_ms]:-0},
        "memory_usage_kb": ${METRICS[memory_usage_kb]:-0},
        "func_call_success_rate": ${METRICS[func_call_success_rate]:-0},
        "schema_error_rate": ${METRICS[schema_error_rate]:-0},
        "data_driven_success_rate": ${METRICS[data_driven_success_rate]:-0},
        "negative_function_calling_latency": ${METRICS[negative_function_calling_latency]:-0},
        "api_failure_handling_latency": ${METRICS[api_failure_handling_latency]:-0}
    },
    "test_results": {
        "positive_tests": {
            "data_driven_function_calling": ${TEST_RESULTS[data_driven_function_calling]:-false},
            "robust_workout_state": ${TEST_RESULTS[robust_workout_state]:-false},
            "performance_benchmarks": ${TEST_RESULTS[performance_benchmarks]:-false}
        },
        "negative_tests": {
            "malformed_response_handling": ${TEST_RESULTS[malformed_response_handling]:-false},
            "api_failure_and_retry": ${TEST_RESULTS[api_failure_and_retry]:-false}
        }
    },
    "quality_gates": {
        "end_to_end_under_30s": $(if (( $(echo "${METRICS[end_to_end_latency]:-31} <= 30" | bc -l) )); then echo "true"; else echo "false"; fi),
        "ui_response_under_500ms": $(if (( $(echo "${METRICS[ui_response_time_ms]:-501} <= 500" | bc -l) )); then echo "true"; else echo "false"; fi),
        "func_call_success_over_90pct": $(if (( $(echo "${METRICS[func_call_success_rate]:-0} >= 0.90" | bc -l) )); then echo "true"; else echo "false"; fi),
        "schema_error_under_5pct": $(if (( $(echo "${METRICS[schema_error_rate]:-1} <= 0.05" | bc -l) )); then echo "true"; else echo "false"; fi),
        "memory_under_100mb": $(if [[ ${METRICS[memory_usage_kb]:-102401} -le 102400 ]]; then echo "true"; else echo "false"; fi)
    },
    "artifacts": {
        "test_scenarios": "$SCENARIO_DIR/test_prompts.txt",
        "full_log": "$LOG_FILE",
        "generated_templates": "/tmp/generated_template.json",
        "created_sessions": "/tmp/created_session.json"
    },
    "recommendations": [
        $(if (( $(echo "${METRICS[end_to_end_latency]:-0} > 25" | bc -l) )); then echo "\"Consider optimizing end-to-end latency\""; fi),
        $(if (( $(echo "${METRICS[func_call_success_rate]:-1} < 0.95" | bc -l) )); then echo "\"Function Calling success rate needs improvement\""; fi),
        $(if [[ ${METRICS[memory_usage_kb]:-0} -gt 81920 ]]; then echo "\"Memory usage approaching limit\""; fi)
    ]
}
EOF

    log_info "增强版测试报告生成: $report_file"
    echo "$report_file"
}

# 主测试流程（v2.0 - 包含负面测试）
main() {
    log_info "开始Sprint 1 增强版E2E验证流程 v2.0"
    log_info "测试环境: $TEST_BASE_URL"
    log_info "测试用户: $TEST_USER_ID"
    log_info "零容忍sleep策略: 全面采用轮询检查"

    # 执行正面测试套件
    test_function_calling_data_driven && TEST_RESULTS[data_driven_function_calling]=true || TEST_RESULTS[data_driven_function_calling]=false
    test_robust_workout_state && TEST_RESULTS[robust_workout_state]=true || TEST_RESULTS[robust_workout_state]=false
    test_performance_benchmarks_enhanced && TEST_RESULTS[performance_benchmarks]=true || TEST_RESULTS[performance_benchmarks]=false

    # 执行负面测试套件（新增）
    test_function_calling_with_malformed_response && TEST_RESULTS[malformed_response_handling]=true || TEST_RESULTS[malformed_response_handling]=false
    test_api_failure_and_retry && TEST_RESULTS[api_failure_and_retry]=true || TEST_RESULTS[api_failure_and_retry]=false

    # 生成报告
    local report_file=$(generate_enhanced_test_report)

    # 计算通过率
    local passed_positive=$(cat $report_file | jq '[.test_results.positive_tests[] | select(. == true)] | length')
    local total_positive=$(cat $report_file | jq '.test_results.positive_tests | length')
    local passed_negative=$(cat $report_file | jq '[.test_results.negative_tests[] | select(. == true)] | length')
    local total_negative=$(cat $report_file | jq '.test_results.negative_tests | length')

    local total_passed=$((passed_positive + passed_negative))
    local total_tests=$((total_positive + total_negative))

    # 质量门禁检查
    local quality_gates_passed=$(cat $report_file | jq '[.quality_gates[] | select(. == true)] | length')
    local total_quality_gates=$(cat $report_file | jq '.quality_gates | length')

    log_info "测试结果汇总:"
    log_info "  正面测试: $passed_positive/$total_positive 通过"
    log_info "  负面测试: $passed_negative/$total_negative 通过"
    log_info "  质量门禁: $quality_gates_passed/$total_quality_gates 通过"
    log_info "  端到端时间: ${METRICS[end_to_end_latency]}s"

    # 严格验收标准
    if [[ $total_passed -eq $total_tests && $quality_gates_passed -eq $total_quality_gates ]]; then
        log_success "🎉 Sprint 1 E2E验证 v2.0 完成: $total_passed/$total_tests 测试通过，所有质量门禁达标"
        exit 0
    else
        log_error "💥 Sprint 1 E2E验证 v2.0 失败: $total_passed/$total_tests 测试通过，$quality_gates_passed/$total_quality_gates 质量门禁达标"
        log_error "详细日志: $LOG_FILE"
        log_error "测试报告: $report_file"
        exit 1
    fi
}

# 清理函数（增强版）
cleanup() {
    log_info "清理测试环境 v2.0..."

    # 停止应用
    adb shell am force-stop com.example.gymbro || true

    # 清理测试数据（生产环境保护）
    if [[ "$TEST_BASE_URL" != *"prod"* && "$TEST_BASE_URL" != *"production"* ]]; then
        curl -s -X DELETE "$TEST_BASE_URL/api/test/cleanup/$TEST_USER_ID" \
            -H "X-Test-Environment: true" || true
    else
        log_error "拒绝在生产环境执行清理操作"
    fi

    # 保留重要日志文件
    log_info "测试日志已保存: $LOG_FILE"
}

# 设置清理钩子
trap cleanup EXIT

# 启动主流程
main "$@"
