package com.example.gymbro.core.util

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.ui.text.UiText
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import timber.log.Timber

/**
 * Flow 异常处理扩展函数
 *
 * 紧急修复：防止 Flow 异常传播导致应用崩溃
 * 捕获所有异常并转换为 ModernResult.Error
 */

/**
 * 为 Flow 添加异常捕获和日志记录
 *
 * @param tag 日志标签，用于标识异常来源
 * @param defaultValue 异常时返回的默认值
 * @return 带有异常处理的 Flow
 */
fun <T> Flow<ModernResult<T>>.catchAndLog(
    tag: String,
    defaultValue: (() -> T)? = null,
): Flow<ModernResult<T>> = catch { throwable ->
    Timber.e(throwable, "[$tag] Flow 收集失败")

    when (throwable) {
        is kotlinx.coroutines.CancellationException -> {
            // 取消异常需要重新抛出，不能被捕获
            Timber.d("[$tag] Flow 被取消")
            throw throwable
        }
        else -> {
            // 其他异常转换为 ModernResult.Error
            val errorResult = ModernResult.error<T>(
                DataErrors.DataError.access(
                    operationName = tag,
                    message = UiText.DynamicString("数据流异常: ${throwable.message}"),
                    cause = throwable,
                ),
            )
            emit(errorResult)
        }
    }
}

/**
 * 为普通 Flow 添加异常捕获和日志记录
 *
 * @param tag 日志标签
 * @param defaultValue 异常时返回的默认值
 * @return 带有异常处理的 Flow
 */
fun <T> Flow<T>.catchAndLogWithDefault(
    tag: String,
    defaultValue: T,
): Flow<T> = catch { throwable ->
    Timber.e(throwable, "[$tag] Flow 收集失败，使用默认值")

    when (throwable) {
        is kotlinx.coroutines.CancellationException -> {
            // 取消异常需要重新抛出
            throw throwable
        }
        else -> {
            emit(defaultValue)
        }
    }
}

/**
 * 为 DataStore Flow 专门设计的异常处理
 *
 * @param tag 日志标签
 * @return 带有异常处理的 Flow
 */
fun <T> Flow<T>.catchDataStoreErrors(tag: String): Flow<T> =
    catch { throwable ->
        Timber.e(throwable, "[$tag] DataStore 访问失败")

        when (throwable) {
            is kotlinx.coroutines.CancellationException -> {
                throw throwable
            }
            else -> {
                // 对于 DataStore 错误，我们记录日志但不能发出错误值
                // 因为类型不匹配，这里只记录错误
                Timber.e("[$tag] DataStore 错误将导致 Flow 终止: ${throwable.message}")
                throw throwable
            }
        }
    }
