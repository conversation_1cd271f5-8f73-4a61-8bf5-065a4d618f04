{"formatVersion": 1, "database": {"version": 1, "identityHash": "29d68ebe6b65cabdbdc216eb0cb2ece2", "entities": [{"tableName": "exercise", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `muscleGroup` TEXT NOT NULL, `equipment` TEXT NOT NULL, `description` TEXT, `imageUrl` TEXT, `videoUrl` TEXT, `defaultSets` INTEGER NOT NULL, `defaultReps` INTEGER NOT NULL, `defaultWeight` REAL, `steps` TEXT NOT NULL, `tips` TEXT NOT NULL, `userId` TEXT, `isCustom` INTEGER NOT NULL, `isFavorite` INTEGER NOT NULL, `difficultyLevel` INTEGER NOT NULL, `calories` INTEGER, `targetMuscles` TEXT NOT NULL, `instructions` TEXT NOT NULL, `embedding` TEXT, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, `createdByUserId` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "muscleGroup", "columnName": "muscleGroup", "affinity": "TEXT", "notNull": true}, {"fieldPath": "equipment", "columnName": "equipment", "affinity": "TEXT", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": false}, {"fieldPath": "imageUrl", "columnName": "imageUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "videoUrl", "columnName": "videoUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "defaultSets", "columnName": "defaultSets", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "defaultReps", "columnName": "defaultReps", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "defaultWeight", "columnName": "defaultWeight", "affinity": "REAL", "notNull": false}, {"fieldPath": "steps", "columnName": "steps", "affinity": "TEXT", "notNull": true}, {"fieldPath": "tips", "columnName": "tips", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isCustom", "columnName": "isCustom", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isFavorite", "columnName": "isFavorite", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "difficultyLevel", "columnName": "difficultyLevel", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "calories", "columnName": "calories", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "targetMuscles", "columnName": "targetMuscles", "affinity": "TEXT", "notNull": true}, {"fieldPath": "instructions", "columnName": "instructions", "affinity": "TEXT", "notNull": true}, {"fieldPath": "embedding", "columnName": "embedding", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdByUserId", "columnName": "createdByUserId", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_exercise_id", "unique": true, "columnNames": ["id"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_exercise_id` ON `${TABLE_NAME}` (`id`)"}, {"name": "index_exercise_muscleGroup", "unique": false, "columnNames": ["muscleGroup"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_exercise_muscleGroup` ON `${TABLE_NAME}` (`muscleGroup`)"}, {"name": "index_exercise_equipment", "unique": false, "columnNames": ["equipment"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_exercise_equipment` ON `${TABLE_NAME}` (`equipment`)"}, {"name": "index_exercise_isCustom", "unique": false, "columnNames": ["isCustom"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_exercise_isCustom` ON `${TABLE_NAME}` (`isCustom`)"}, {"name": "index_exercise_userId", "unique": false, "columnNames": ["userId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_exercise_userId` ON `${TABLE_NAME}` (`userId`)"}, {"name": "index_exercise_isFavorite", "unique": false, "columnNames": ["isFavorite"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_exercise_isFavorite` ON `${TABLE_NAME}` (`isFavorite`)"}, {"name": "index_exercise_name", "unique": false, "columnNames": ["name"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_exercise_name` ON `${TABLE_NAME}` (`name`)"}, {"name": "index_exercise_createdAt", "unique": false, "columnNames": ["createdAt"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_exercise_createdAt` ON `${TABLE_NAME}` (`createdAt`)"}, {"name": "index_exercise_updatedAt", "unique": false, "columnNames": ["updatedAt"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_exercise_updatedAt` ON `${TABLE_NAME}` (`updatedAt`)"}], "foreignKeys": []}, {"ftsVersion": "FTS4", "ftsOptions": {"tokenizer": "simple", "tokenizerArgs": [], "contentTable": "exercise", "languageIdColumnName": "", "matchInfo": "FTS4", "notIndexedColumns": [], "prefixSizes": [], "preferredOrder": "ASC"}, "contentSyncTriggers": ["CREATE TRIGGER IF NOT EXISTS room_fts_content_sync_exercise_fts_BEFORE_UPDATE BEFORE UPDATE ON `exercise` BEGIN DELETE FROM `exercise_fts` WHERE `docid`=OLD.`rowid`; END", "CREATE TRIGGER IF NOT EXISTS room_fts_content_sync_exercise_fts_BEFORE_DELETE BEFORE DELETE ON `exercise` BEGIN DELETE FROM `exercise_fts` WHERE `docid`=OLD.`rowid`; END", "CREATE TRIGGER IF NOT EXISTS room_fts_content_sync_exercise_fts_AFTER_UPDATE AFTER UPDATE ON `exercise` BEGIN INSERT INTO `exercise_fts`(`docid`, `name`, `description`, `muscleGroup`, `equipment`, `steps`, `tips`, `instructions`) VALUES (NEW.`rowid`, NEW.`name`, NEW.`description`, NEW.`muscleGroup`, NEW.`equipment`, NEW.`steps`, NEW.`tips`, NEW.`instructions`); END", "CREATE TRIGGER IF NOT EXISTS room_fts_content_sync_exercise_fts_AFTER_INSERT AFTER INSERT ON `exercise` BEGIN INSERT INTO `exercise_fts`(`docid`, `name`, `description`, `muscleGroup`, `equipment`, `steps`, `tips`, `instructions`) VALUES (NEW.`rowid`, NEW.`name`, NEW.`description`, NEW.`muscleGroup`, NEW.`equipment`, NEW.`steps`, NEW.`tips`, NEW.`instructions`); END"], "tableName": "exercise_fts", "createSql": "CREATE VIRTUAL TABLE IF NOT EXISTS `${TABLE_NAME}` USING FTS4(`name` TEXT NOT NULL, `description` TEXT NOT NULL, `muscleGroup` TEXT NOT NULL, `equipment` TEXT NOT NULL, `steps` TEXT NOT NULL, `tips` TEXT NOT NULL, `instructions` TEXT NOT NULL, content=`exercise`)", "fields": [{"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": true}, {"fieldPath": "muscleGroup", "columnName": "muscleGroup", "affinity": "TEXT", "notNull": true}, {"fieldPath": "equipment", "columnName": "equipment", "affinity": "TEXT", "notNull": true}, {"fieldPath": "steps", "columnName": "steps", "affinity": "TEXT", "notNull": true}, {"fieldPath": "tips", "columnName": "tips", "affinity": "TEXT", "notNull": true}, {"fieldPath": "instructions", "columnName": "instructions", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": []}, "indices": [], "foreignKeys": []}, {"tableName": "exercise_search_history", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `query` TEXT NOT NULL, `resultCount` INTEGER NOT NULL, `userId` TEXT, `timestamp` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "query", "columnName": "query", "affinity": "TEXT", "notNull": true}, {"fieldPath": "resultCount", "columnName": "resultCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_exercise_search_history_query", "unique": false, "columnNames": ["query"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_exercise_search_history_query` ON `${TABLE_NAME}` (`query`)"}, {"name": "index_exercise_search_history_timestamp", "unique": false, "columnNames": ["timestamp"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_exercise_search_history_timestamp` ON `${TABLE_NAME}` (`timestamp`)"}, {"name": "index_exercise_search_history_userId", "unique": false, "columnNames": ["userId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_exercise_search_history_userId` ON `${TABLE_NAME}` (`userId`)"}], "foreignKeys": []}, {"tableName": "exercise_usage_stats", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `exerciseId` TEXT NOT NULL, `userId` TEXT, `usageCount` INTEGER NOT NULL, `lastUsed` INTEGER NOT NULL, `totalSets` INTEGER NOT NULL, `totalReps` INTEGER NOT NULL, `maxWeight` REAL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "exerciseId", "columnName": "exerciseId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "usageCount", "columnName": "usageCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastUsed", "columnName": "lastUsed", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalSets", "columnName": "totalSets", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalReps", "columnName": "totalReps", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "maxWeight", "columnName": "maxWeight", "affinity": "REAL", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_exercise_usage_stats_exerciseId", "unique": false, "columnNames": ["exerciseId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_exercise_usage_stats_exerciseId` ON `${TABLE_NAME}` (`exerciseId`)"}, {"name": "index_exercise_usage_stats_userId", "unique": false, "columnNames": ["userId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_exercise_usage_stats_userId` ON `${TABLE_NAME}` (`userId`)"}, {"name": "index_exercise_usage_stats_lastUsed", "unique": false, "columnNames": ["lastUsed"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_exercise_usage_stats_lastUsed` ON `${TABLE_NAME}` (`lastUsed`)"}], "foreignKeys": []}], "views": [{"viewName": "exercise_stats", "createSql": "CREATE VIEW `${VIEW_NAME}` AS SELECT\n            muscleGroup,\n            COUNT(*) as exerciseCount,\n            COUNT(CASE WHEN isCustom = 1 THEN 1 END) as customCount,\n            COUNT(CASE WHEN isFavorite = 1 THEN 1 END) as favoriteCount,\n            AVG(difficultyLevel) as avgDifficulty\n        FROM exercise\n        GROUP BY muscleGroup"}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '29d68ebe6b65cabdbdc216eb0cb2ece2')"]}}