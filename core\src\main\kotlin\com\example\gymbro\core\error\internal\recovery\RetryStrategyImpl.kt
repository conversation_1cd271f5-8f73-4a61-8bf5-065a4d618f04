package com.example.gymbro.core.error.internal.recovery

import com.example.gymbro.core.error.recovery.RetryStrategy
import com.example.gymbro.core.error.types.ErrorCategory
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import kotlinx.coroutines.delay
import timber.log.Timber
import kotlin.math.min

/**
 * 重试恢复策略实现类
 * 在操作失败时提供重试机制
 *
 * @param operation 要重试的挂起操作
 * @param maxAttempts 最大重试次数
 * @param initialDelayMillis 初始延迟（毫秒）
 * @param maxDelayMillis 最大延迟（毫秒）
 * @param backoffFactor 延迟增长因子
 * @param shouldRetry 判断是否应该重试的条件函数
 */
class RetryStrategyImpl<T>(
    private val operation: suspend () -> T?,
    private val maxAttempts: Int = 3,
    private val initialDelayMillis: Long = 1000,
    private val maxDelayMillis: Long = 10000,
    private val backoffFactor: Double = 2.0,
    private val shouldRetry: ((Throwable?) -> Boolean)? = null,
) : RetryStrategy<T> {
    /**
     * 执行重试恢复
     * @return 成功时的数据，或null如果所有重试都失败
     */
    override suspend fun execute(): T? {
        var attempts = 0
        var currentDelay = initialDelayMillis
        var lastError: Throwable? = null

        while (attempts < maxAttempts) {
            // 第一次尝试不需要延迟
            if (attempts > 0) {
                delay(currentDelay)
                Timber.d("重试第 $attempts 次，延迟 $currentDelay 毫秒")
                // 计算下次重试的延迟时间（指数退避）
                currentDelay = min((currentDelay * backoffFactor).toLong(), maxDelayMillis)
            }

            attempts++
            try {
                val result = operation()
                if (result != null) {
                    Timber.d("重试成功，在第 $attempts 次尝试")
                    return result
                }
            } catch (e: Exception) {
                Timber.d("重试失败，在第 $attempts 次尝试: ${e.message}")
                lastError = e
                // 如果有条件函数且不满足重试条件，则中断重试
                if (shouldRetry != null && !shouldRetry.invoke(e)) {
                    Timber.d("不满足重试条件，中断重试")
                    break
                }
            }
        }

        Timber.d("所有重试都失败")
        return null
    }

    /**
     * 使用现代结果类型执行重试操作
     *
     * @param operation 返回ModernResult的操作
     * @return 操作结果包装在ModernResult中
     */
    suspend fun executeWithResult(operation: suspend () -> ModernResult<T>): ModernResult<T> {
        var attempts = 0
        var currentDelay = initialDelayMillis
        var lastError: ModernDataError? = null

        while (attempts < maxAttempts) {
            // 第一次尝试不需要延迟
            if (attempts > 0) {
                delay(currentDelay)
                Timber.d("重试第 $attempts 次，延迟 $currentDelay 毫秒")
                // 计算下次重试的延迟时间（指数退避）
                currentDelay = min((currentDelay * backoffFactor).toLong(), maxDelayMillis)
            }

            attempts++
            try {
                val result = operation()
                when (result) {
                    is ModernResult.Success -> {
                        Timber.d("重试成功，在第 $attempts 次尝试")
                        return result
                    }
                    is ModernResult.Error -> {
                        lastError = result.error
                        // 如果有条件函数且不满足重试条件，则中断重试
                        if (shouldRetry != null && !shouldRetry.invoke(result.error)) {
                            Timber.d("不满足重试条件，中断重试")
                            return result
                        }
                        Timber.d("操作返回错误，继续重试: ${result.error.uiMessage}")
                    }
                    is ModernResult.Loading -> {
                        // 如果返回Loading状态，暂停重试计数
                        attempts--
                        Timber.d("操作返回Loading状态，不计入重试次数")
                        delay(initialDelayMillis / 2) // 短暂等待
                    }
                }
            } catch (e: Exception) {
                Timber.d("重试失败，在第 $attempts 次尝试: ${e.message}")
                // 如果有条件函数且不满足重试条件，则中断重试
                if (shouldRetry != null && !shouldRetry.invoke(e)) {
                    Timber.d("不满足重试条件，中断重试")
                    break
                }
            }
        }

        Timber.d("所有重试都失败")
        return if (lastError != null) {
            ModernResult.Error(lastError)
        } else {
            ModernResult.Error(
                ModernDataError(
                    operationName = "RetryStrategyImpl.executeWithResult",
                    errorType = GlobalErrorType.System.Internal,
                    category = ErrorCategory.SYSTEM,
                    uiMessage = UiText.DynamicString("重试失败：达到最大尝试次数"),
                    metadataMap = mapOf("max_attempts" to maxAttempts),
                    recoverable = false,
                ),
            )
        }
    }
}
