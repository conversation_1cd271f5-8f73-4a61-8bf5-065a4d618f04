package com.example.gymbro.core.ai.prompt.model

/**
 * Core模块的纯抽象数据接口
 * 不依赖任何外部模块，只定义算法需要的数据结构
 */

/**
 * 用户资料接口
 */
interface UserProfileData {
    val gender: String
    val age: Int
    val height: Int
    val weight: Int
    val experience: String
    val bodyFatPercentage: Float?
}

/**
 * 训练模板接口
 */
interface WorkoutTemplateData {
    val id: String
    val name: String
    val description: String
    val targetMuscles: List<String>
    val difficultyLevel: String
    val exercises: List<ExerciseData>
    val similarity: Float
}

/**
 * 动作接口
 */
interface ExerciseData {
    val name: String
    val sets: Int
    val reps: String
}

/**
 * 对话消息接口
 */
interface ConversationMessageData {
    val sender: String
    val content: String
    val timestamp: Long
}

/**
 * 健身目标接口
 */
interface FitnessGoalData {
    val type: String
    val description: String
    val targetDate: String?
}

/**
 * AI上下文接口
 */
interface AiContextData {
    val userProfile: UserProfileData
    val relevantTemplates: List<WorkoutTemplateData>
    val recentHistory: List<ConversationMessageData>
    val relevantHistory: List<ConversationMessageData>
    val userGoals: List<FitnessGoalData>
    val userLimitations: List<String>
}

/**
 * Prompt配置接口
 */
interface PromptConfigData {
    val version: String
    val maxTokens: Int
    val contextTemplatesK: Int
    val recentHistoryN: Int
    val truncationStrategy: TruncationStrategyType
}

/**
 * 截断策略枚举
 */
enum class TruncationStrategyType {
    PRIORITY_BASED,
    BALANCED,
    RECENT_FIRST,
}
