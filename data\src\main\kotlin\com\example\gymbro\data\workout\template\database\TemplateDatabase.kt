package com.example.gymbro.data.workout.template.database

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.example.gymbro.data.workout.template.converter.TemplateTypeConverters
import com.example.gymbro.data.workout.template.dao.ExerciseInTemplateDao
import com.example.gymbro.data.workout.template.dao.TemplateDao
import com.example.gymbro.data.workout.template.dao.TemplateVersionDao
import com.example.gymbro.data.workout.template.entity.ExerciseInTemplateEntity
import com.example.gymbro.data.workout.template.entity.TemplateEntity
import com.example.gymbro.data.workout.template.entity.TemplateVersionEntity

/**
 * TemplateDB - 训练模板数据库
 *
 * 基于 05_Final_Database_Architecture.md 设计
 * 职责：存储用户自定义的训练模板配置，引用动作库
 * 特性：中等变化频率，用户创建和编辑模板时使用
 */
@Database(
    entities = [
        TemplateEntity::class,
        ExerciseInTemplateEntity::class,
        TemplateVersionEntity::class, // Phase1新增：版本控制实体
    ],
    version = 3, // v3: 添加版本控制字段 (isDraft, isPublished, currentVersion, lastPublishedAt)
    exportSchema = true,
)
@TypeConverters(TemplateTypeConverters::class)
abstract class TemplateDatabase : RoomDatabase() {

    abstract fun templateDao(): TemplateDao
    abstract fun exerciseInTemplateDao(): ExerciseInTemplateDao
    abstract fun templateVersionDao(): TemplateVersionDao // Phase1新增：版本控制DAO

    companion object {
        private const val DATABASE_NAME = "template_database"

        @Volatile
        private var INSTANCE: TemplateDatabase? = null

        fun getDatabase(context: Context): TemplateDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    TemplateDatabase::class.java,
                    DATABASE_NAME,
                )
                    // 🔥 JSON 数据持久化修复：保护用户数据，仅开发环境允许重建
                    .apply {
                        val debugMode = System.getProperty("DEBUG_MODE", "false").toBoolean()
                        if (debugMode) {
                            fallbackToDestructiveMigration()
                        }
                    }
                    .build()
                INSTANCE = instance
                instance
            }
        }
    }
}
