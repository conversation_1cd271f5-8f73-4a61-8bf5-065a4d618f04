package com.example.gymbro.designSystem.components.extensions

import android.content.Context
import com.example.gymbro.core.ui.text.UiText

/**
 * UiText的非Composable扩展函数
 *
 * 提供在非Composable环境中使用的UiText扩展方法
 */

/**
 * 将UiText转换为字符串
 *
 * 需要Android Context才能获取资源字符串
 *
 * @param context Android上下文
 * @return 格式化后的字符串
 */
fun UiText.asString(context: Context): String =
    when (this) {
        is UiText.DynamicString -> value
        is UiText.StringResource -> {
            if (args.isEmpty()) {
                context.getString(resId)
            } else {
                context.getString(resId, *args.toTypedArray())
            }
        }
        is UiText.ErrorCode -> {
            // ErrorCode类型需要通过ErrorCodeMapper处理，这里提供fallback
            "Error: ${errorCode.code} (${errorCode.category})"
        }
        is UiText.Empty -> ""
    }

/**
 * 将可能为空的UiText转换为字符串，如果为null则返回空字符串
 *
 * @param context Android上下文
 * @return 格式化后的字符串，如果UiText为null则返回空字符串
 */
fun UiText?.asStringOrEmpty(context: Context): String = this?.asString(context) ?: ""
