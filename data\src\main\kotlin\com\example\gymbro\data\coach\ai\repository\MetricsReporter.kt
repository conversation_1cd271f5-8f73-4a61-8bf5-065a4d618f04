package com.example.gymbro.data.coach.ai.repository

import timber.log.Timber
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * MetricsReporter - `AtomicLong` 收集，push Prometheus
 *
 * 基于 promtjson.md 文档要求，统一指标收集和推送
 * 整合现有的分散指标收集器
 *
 * 核心功能：
 * - AtomicLong 计数器确保线程安全
 * - 指数退避重试指标
 * - Prometheus push-gateway 集成
 * - 系统提示词泄漏监控
 */
@Singleton
class MetricsReporter @Inject constructor() {

    // ==================== 核心计数器 ====================

    // 🔥 AI 流式请求指标
    private val totalRequestCount = AtomicLong(0)
    private val sse5xxCount = AtomicLong(0)
    private val retryTotalCount = AtomicLong(0)
    private val retrySuccessCount = AtomicLong(0)

    // 🔥 系统安全指标
    private val systemPromptLeakCount = AtomicLong(0)
    private val roleLinterViolationCount = AtomicLong(0)

    // 🔥 备份机制指标
    private val backupFileWriteCount = AtomicLong(0)
    private val backupFileLockFailCount = AtomicLong(0)

    // 🔥 AI 响应质量指标
    private val goalMatchCount = AtomicLong(0)
    private val preferenceInjectionSuccessCount = AtomicLong(0)
    private val preferenceInjectionTotalCount = AtomicLong(0)

    // 🔥 响应时间统计
    private val responseTimeSum = AtomicLong(0)
    private val responseTimeCount = AtomicLong(0)
    private val responseTimeMax = AtomicLong(0)

    // ==================== AI 流式请求指标 ====================

    /**
     * 记录总请求数
     */
    fun incrementTotalRequests() {
        totalRequestCount.incrementAndGet()
        Timber.d("📊 total_requests: ${totalRequestCount.get()}")
    }

    /**
     * 记录 5xx 错误
     */
    fun incrementSse5xxErrors() {
        sse5xxCount.incrementAndGet()
        Timber.w("📊 sse_5xx_errors: ${sse5xxCount.get()}")
    }

    /**
     * 记录重试次数
     */
    fun incrementRetryAttempts() {
        retryTotalCount.incrementAndGet()
        Timber.d("📊 retry_attempts: ${retryTotalCount.get()}")
    }

    /**
     * 记录重试成功
     */
    fun incrementRetrySuccess() {
        retrySuccessCount.incrementAndGet()
        Timber.d("📊 retry_success: ${retrySuccessCount.get()}")
    }

    // ==================== 系统安全指标 ====================

    /**
     * 🔥 关键：记录系统提示词泄漏
     * system_prompt_leak_rate 必须为 0%
     */
    fun recordSystemPromptLeak(details: String) {
        systemPromptLeakCount.incrementAndGet()
        Timber.e("🚨 SYSTEM_PROMPT_LEAK: ${systemPromptLeakCount.get()} - $details")
    }

    /**
     * 记录 Role Linter 违规
     */
    fun recordRoleLinterViolation(violation: String) {
        roleLinterViolationCount.incrementAndGet()
        Timber.w("⚠️ role_linter_violation: ${roleLinterViolationCount.get()} - $violation")
    }

    // ==================== 备份机制指标 ====================

    /**
     * 记录备份文件写入
     */
    fun incrementBackupWrites() {
        backupFileWriteCount.incrementAndGet()
        Timber.d("📊 backup_writes: ${backupFileWriteCount.get()}")
    }

    /**
     * 🔥 关键：记录备份文件锁失败
     * chat_backup_file_lock_fail 必须 < 0.1%
     */
    fun incrementBackupLockFails() {
        backupFileLockFailCount.incrementAndGet()
        Timber.e("🚨 backup_lock_fail: ${backupFileLockFailCount.get()}")
    }

    // ==================== AI 响应质量指标 ====================

    /**
     * 记录目标匹配成功
     */
    fun recordGoalMatch() {
        goalMatchCount.incrementAndGet()
        Timber.d("📊 goal_matches: ${goalMatchCount.get()}")
    }

    /**
     * 记录偏好注入尝试
     */
    fun recordPreferenceInjectionAttempt(success: Boolean) {
        preferenceInjectionTotalCount.incrementAndGet()
        if (success) {
            preferenceInjectionSuccessCount.incrementAndGet()
        }
        Timber.d(
            "📊 preference_injection: ${preferenceInjectionSuccessCount.get()}/${preferenceInjectionTotalCount.get()}",
        )
    }

    /**
     * 记录响应时间
     */
    fun recordResponseTime(timeMs: Long) {
        responseTimeSum.addAndGet(timeMs)
        responseTimeCount.incrementAndGet()

        // 更新最大响应时间
        var currentMax = responseTimeMax.get()
        while (timeMs > currentMax && !responseTimeMax.compareAndSet(currentMax, timeMs)) {
            currentMax = responseTimeMax.get()
        }

        Timber.d("📊 response_time: ${timeMs}ms (avg: ${getAverageResponseTime()}ms)")
    }

    // ==================== 指标计算 ====================

    /**
     * 🔥 关键验证指标：系统提示词泄漏率
     * 必须为 0%
     */
    fun getSystemPromptLeakRate(): Double {
        val total = totalRequestCount.get()
        return if (total > 0) {
            (systemPromptLeakCount.get().toDouble() / total) * 100
        } else {
            0.0
        }
    }

    /**
     * 🔥 关键验证指标：备份文件锁失败率
     * 必须 < 0.1%
     */
    fun getBackupLockFailRate(): Double {
        val total = backupFileWriteCount.get()
        return if (total > 0) {
            (backupFileLockFailCount.get().toDouble() / total) * 100
        } else {
            0.0
        }
    }

    /**
     * SSE 5xx 错误率
     */
    fun getSse5xxRate(): Double {
        val total = totalRequestCount.get()
        return if (total > 0) {
            (sse5xxCount.get().toDouble() / total) * 100
        } else {
            0.0
        }
    }

    /**
     * 重试成功率
     */
    fun getRetrySuccessRate(): Double {
        val total = retryTotalCount.get()
        return if (total > 0) {
            (retrySuccessCount.get().toDouble() / total) * 100
        } else {
            0.0
        }
    }

    /**
     * 偏好注入成功率
     */
    fun getPreferenceInjectionRate(): Double {
        val total = preferenceInjectionTotalCount.get()
        return if (total > 0) {
            (preferenceInjectionSuccessCount.get().toDouble() / total) * 100
        } else {
            0.0
        }
    }

    /**
     * 平均响应时间
     */
    fun getAverageResponseTime(): Double {
        val count = responseTimeCount.get()
        return if (count > 0) {
            responseTimeSum.get().toDouble() / count
        } else {
            0.0
        }
    }

    /**
     * 获取完整指标快照
     */
    fun getMetricsSnapshot(): MetricsSnapshot {
        return MetricsSnapshot(
            systemPromptLeakRate = getSystemPromptLeakRate(),
            backupLockFailRate = getBackupLockFailRate(),
            sse5xxRate = getSse5xxRate(),
            retrySuccessRate = getRetrySuccessRate(),
            preferenceInjectionRate = getPreferenceInjectionRate(),
            averageResponseTimeMs = getAverageResponseTime(),
            maxResponseTimeMs = responseTimeMax.get().toDouble(),
            totalRequests = totalRequestCount.get(),
            goalMatches = goalMatchCount.get(),
        )
    }

    /**
     * 🔥 TODO: Prometheus Push Gateway 集成
     * 推送指标到监控系统
     */
    fun pushToPrometheus() {
        val snapshot = getMetricsSnapshot()

        // TODO: 实现 Prometheus push-gateway 推送
        Timber.i("📊 Metrics Snapshot: $snapshot")

        // 验证关键指标
        if (snapshot.systemPromptLeakRate > 0.0) {
            Timber.e("🚨 CRITICAL: System prompt leak rate: ${snapshot.systemPromptLeakRate}%")
        }

        if (snapshot.backupLockFailRate > 0.1) {
            Timber.e("🚨 CRITICAL: Backup lock fail rate: ${snapshot.backupLockFailRate}%")
        }
    }

    /**
     * 重置所有计数器（仅用于测试）
     */
    fun resetMetrics() {
        totalRequestCount.set(0)
        sse5xxCount.set(0)
        retryTotalCount.set(0)
        retrySuccessCount.set(0)
        systemPromptLeakCount.set(0)
        roleLinterViolationCount.set(0)
        backupFileWriteCount.set(0)
        backupFileLockFailCount.set(0)
        goalMatchCount.set(0)
        preferenceInjectionSuccessCount.set(0)
        preferenceInjectionTotalCount.set(0)
        responseTimeSum.set(0)
        responseTimeCount.set(0)
        responseTimeMax.set(0)

        Timber.d("🔄 所有指标计数器已重置")
    }
}

/**
 * 指标快照数据类
 */
data class MetricsSnapshot(
    val systemPromptLeakRate: Double,
    val backupLockFailRate: Double,
    val sse5xxRate: Double,
    val retrySuccessRate: Double,
    val preferenceInjectionRate: Double,
    val averageResponseTimeMs: Double,
    val maxResponseTimeMs: Double,
    val totalRequests: Long,
    val goalMatches: Long,
)
