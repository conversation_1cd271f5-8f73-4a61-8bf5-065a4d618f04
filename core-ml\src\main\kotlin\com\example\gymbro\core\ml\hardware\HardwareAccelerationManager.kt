package com.example.gymbro.core.ml.hardware

import android.content.Context
import android.os.Build
import dagger.hilt.android.qualifiers.ApplicationContext
import org.tensorflow.lite.Interpreter
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

// 注释：使用标准TensorFlow Lite API

/**
 * 硬件加速管理器 - LiteRT 1.3.0适配版
 *
 * 🎯 功能特性：
 * - 使用LiteRT 1.3.0简化的硬件加速API
 * - 自动检测最佳加速策略
 * - 提供安全的回退机制
 * - 性能监控和错误恢复
 */
@Singleton
class HardwareAccelerationManager @Inject constructor(
    @ApplicationContext private val context: Context,
) {

    // 🔥 P0性能优化：缓存硬件检测结果，避免重复计算
    private var cachedCapability: HardwareCapability? = null
    private var cachedAccelerationConfig: AccelerationConfig? = null

    /**
     * 硬件加速类型
     */
    enum class AccelerationType {
        CPU_ONLY, // 仅CPU
        GPU_RUNTIME, // GPU运行时加速
        NNAPI_RUNTIME, // NNAPI运行时加速
        AUTO_DETECT, // 自动检测最佳选项
    }

    /**
     * 硬件加速配置
     */
    data class AccelerationConfig(
        val type: AccelerationType,
        val runtime: String,
        val isSupported: Boolean,
        val deviceScore: Int, // 设备性能评分 1-10
        val enabledFeatures: Set<String> = emptySet(),
        val deviceInfo: String = "",
    )

    /**
     * 设备硬件能力评估
     */
    data class HardwareCapability(
        val hasGpuSupport: Boolean,
        val supportsNNAPI: Boolean,
        val deviceGpuInfo: String,
        val recommendedThreads: Int,
        val performanceLevel: PerformanceLevel,
    )

    enum class PerformanceLevel {
        LOW_END, // 低端设备
        MID_RANGE, // 中端设备
        HIGH_END, // 高端设备
        FLAGSHIP, // 旗舰设备
    }

    /**
     * 检测设备硬件加速能力 - 带缓存优化
     *
     * 🔥 P0性能优化：缓存检测结果，避免重复的GPU检测导致启动卡顿
     */
    fun detectHardwareCapability(): HardwareCapability {
        // 返回缓存结果，避免重复计算
        cachedCapability?.let {
            return it
        }

        val startTime = System.currentTimeMillis()

        val hasGpuSupport = isGpuSupported()
        val supportsNNAPI = isNnapiSupported()
        val deviceGpuInfo = getGpuInfo()
        val performanceLevel = assessPerformanceLevel()
        val recommendedThreads = when (performanceLevel) {
            PerformanceLevel.FLAGSHIP -> 6
            PerformanceLevel.HIGH_END -> 4
            PerformanceLevel.MID_RANGE -> 3
            PerformanceLevel.LOW_END -> 2
        }

        val capability = HardwareCapability(
            hasGpuSupport = hasGpuSupport,
            supportsNNAPI = supportsNNAPI,
            deviceGpuInfo = deviceGpuInfo,
            recommendedThreads = recommendedThreads,
            performanceLevel = performanceLevel,
        )

        // 缓存结果
        cachedCapability = capability

        val detectionTime = System.currentTimeMillis() - startTime
        Timber.i("硬件能力检测完成，耗时: ${detectionTime}ms")

        return capability
    }

    /**
     * 检测并选择最佳硬件加速方案 - 带缓存优化
     *
     * 🔥 P0性能优化：缓存加速配置，避免重复的能力检测和配置创建
     */
    fun detectBestAcceleration(): AccelerationConfig {
        // 返回缓存结果，避免重复计算
        cachedAccelerationConfig?.let {
            return it
        }

        val capability = detectHardwareCapability()

        val config = when {
            // 优先尝试GPU加速
            capability.hasGpuSupport -> {
                Timber.i("采用GPU加速")
                createGpuRuntimeConfig(capability)
            }
            // 回退到NNAPI（仅Android 8.1+老设备）
            capability.supportsNNAPI -> {
                Timber.i("采用NNAPI加速")
                createNnapiRuntimeConfig(capability)
            }
            // 最后回退到CPU
            else -> {
                Timber.w("仅CPU可用")
                createCpuOnlyConfig(capability)
            }
        }

        // 缓存配置结果
        cachedAccelerationConfig = config
        return config
    }

    /**
     * 检测GPU是否支持
     */
    private fun isGpuSupported(): Boolean {
        return try {
            // LiteRT 1.3.0内置GPU检测，检查基本OpenGL ES支持
            val hasBasicGpu = context.packageManager.hasSystemFeature("android.hardware.opengles.aep") ||
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP
            val hasAdvancedGpu = context.packageManager.hasSystemFeature("android.hardware.vulkan.level") ||
                context.packageManager.hasSystemFeature("android.hardware.vulkan.version")

            val isSupported = hasBasicGpu || hasAdvancedGpu
            Timber.d("🔍 GPU支持检测: $isSupported (基础:$hasBasicGpu, 高级:$hasAdvancedGpu)")
            isSupported
        } catch (e: Exception) {
            Timber.w(e, "GPU支持检测失败: ${e.message}")
            false
        }
    }

    /**
     * 检测NNAPI是否支持
     */
    private fun isNnapiSupported(): Boolean {
        // NNAPI在Android 15中被弃用，建议仅用于老设备回退
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1 &&
            Build.VERSION.SDK_INT < Build.VERSION_CODES.VANILLA_ICE_CREAM
    }

    /**
     * 创建GPU运行时配置
     */
    private fun createGpuRuntimeConfig(capability: HardwareCapability): AccelerationConfig {
        return AccelerationConfig(
            type = AccelerationType.GPU_RUNTIME,
            runtime = "GPU",
            isSupported = true,
            deviceScore = when (capability.performanceLevel) {
                PerformanceLevel.FLAGSHIP -> 9
                PerformanceLevel.HIGH_END -> 8
                PerformanceLevel.MID_RANGE -> 7
                PerformanceLevel.LOW_END -> 6
            },
            enabledFeatures = setOf("MLDrift加速", "并行计算", "内存优化", "低延迟推理"),
            deviceInfo = "LiteRT GPU运行时 | ${capability.deviceGpuInfo}",
        )
    }

    /**
     * 创建NNAPI运行时配置（老设备回退）
     */
    private fun createNnapiRuntimeConfig(capability: HardwareCapability): AccelerationConfig {
        return AccelerationConfig(
            type = AccelerationType.NNAPI_RUNTIME,
            runtime = "NNAPI",
            isSupported = true,
            deviceScore = when (capability.performanceLevel) {
                PerformanceLevel.HIGH_END -> 6
                PerformanceLevel.MID_RANGE -> 5
                else -> 4
            },
            enabledFeatures = setOf("神经网络加速", "低功耗", "老设备优化"),
            deviceInfo = "LiteRT NNAPI运行时 | API ${Build.VERSION.SDK_INT}",
        )
    }

    /**
     * 创建CPU-only配置
     */
    private fun createCpuOnlyConfig(capability: HardwareCapability): AccelerationConfig {
        return AccelerationConfig(
            type = AccelerationType.CPU_ONLY,
            runtime = "CPU",
            isSupported = true,
            deviceScore = when (capability.performanceLevel) {
                PerformanceLevel.FLAGSHIP -> 5
                PerformanceLevel.HIGH_END -> 4
                PerformanceLevel.MID_RANGE -> 3
                PerformanceLevel.LOW_END -> 2
            },
            enabledFeatures = setOf("通用兼容", "稳定可靠", "多线程优化"),
            deviceInfo = "LiteRT CPU运行时 | ${capability.recommendedThreads}线程",
        )
    }

    /**
     * 应用硬件加速配置到Interpreter.Options (LiteRT 1.3.0实际API)
     */
    fun applyAccelerationConfig(
        options: Interpreter.Options,
        config: AccelerationConfig,
    ): Interpreter.Options {
        Timber.i("🎯 应用硬件加速配置: ${config.type}")

        try {
            when (config.type) {
                AccelerationType.GPU_RUNTIME -> {
                    // 🔥 修复：确保GPU配置的一致性，避免检测支持但应用时回退的矛盾
                    try {
                        // TODO: 需要添加正确的GPU Delegate API
                        // options.addDelegate(GpuDelegate())
                        Timber.w("⚠️ GPU委托API待实现，暂时回退到CPU")
                        return applyCpuConfig(options, config)
                    } catch (e: Exception) {
                        Timber.w(e, "GPU配置应用失败，回退到CPU: ${e.message}")
                        return applyCpuConfig(options, config)
                    }
                }

                AccelerationType.NNAPI_RUNTIME -> {
                    if (isNnapiSupported()) {
                        options.setUseNNAPI(true)
                        Timber.i("✅ LiteRT NNAPI已启用 (性能评分: ${config.deviceScore}/10)")
                    } else {
                        Timber.w("⚠️ NNAPI不支持，回退到CPU")
                        return applyCpuConfig(options, config)
                    }
                }

                AccelerationType.CPU_ONLY -> {
                    return applyCpuConfig(options, config)
                }

                AccelerationType.AUTO_DETECT -> {
                    // 🔥 修复：防止无限递归，如果已经是AUTO_DETECT就强制使用CPU
                    val bestConfig = detectBestAcceleration()
                    if (bestConfig.type == AccelerationType.AUTO_DETECT) {
                        Timber.w("⚠️ 检测到AUTO_DETECT递归，强制使用CPU配置")
                        return applyCpuConfig(options, createCpuOnlyConfig(detectHardwareCapability()))
                    }
                    return applyAccelerationConfig(options, bestConfig)
                }
            }
        } catch (e: Exception) {
            Timber.w(e, "硬件加速配置失败，回退到CPU: ${e.message}")
            return applyCpuConfig(options, config)
        }

        return options
    }

    /**
     * 应用CPU配置
     */
    private fun applyCpuConfig(
        options: Interpreter.Options,
        config: AccelerationConfig,
    ): Interpreter.Options {
        val threads = getRecommendedThreads()
        options.setNumThreads(threads)
        options.setUseNNAPI(false)
        Timber.i("✅ LiteRT CPU运行时已配置 (${threads}线程, 性能评分: ${config.deviceScore}/10)")
        return options
    }

    /**
     * 评估设备性能等级
     */
    private fun assessPerformanceLevel(): PerformanceLevel {
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU && // Android 13+
                (
                    Build.BRAND.contains("Google", true) &&
                        Build.MODEL.contains("Pixel", true) &&
                        extractPixelGeneration() >= 7
                    ) -> PerformanceLevel.FLAGSHIP

            Build.VERSION.SDK_INT >= Build.VERSION_CODES.S && // Android 12+
                (
                    Build.BRAND.contains("Samsung", true) ||
                        Build.BRAND.contains("OnePlus", true) ||
                        Build.BRAND.contains("Xiaomi", true)
                    ) -> PerformanceLevel.HIGH_END

            Build.VERSION.SDK_INT >= Build.VERSION_CODES.R -> PerformanceLevel.MID_RANGE // Android 11+

            else -> PerformanceLevel.LOW_END
        }
    }

    /**
     * 提取Pixel设备代数
     */
    private fun extractPixelGeneration(): Int {
        return try {
            val model = Build.MODEL.lowercase()
            when {
                model.contains("pixel 8") -> 8
                model.contains("pixel 7") -> 7
                model.contains("pixel 6") -> 6
                model.contains("pixel 5") -> 5
                model.contains("pixel 4") -> 4
                else -> 3
            }
        } catch (e: Exception) {
            3
        }
    }

    /**
     * 获取推荐线程数
     */
    private fun getRecommendedThreads(): Int {
        val cores = Runtime.getRuntime().availableProcessors()
        return when {
            cores >= 8 -> 6
            cores >= 6 -> 4
            cores >= 4 -> 3
            else -> 2
        }.coerceAtMost(cores)
    }

    /**
     * 获取GPU信息 - 安全检测版本
     *
     * 🔥 SIGSEGV修复：移除危险的GLES调用，改用安全的系统特性检测
     * 避免在非UI线程中调用OpenGL API导致的段错误
     */
    private fun getGpuInfo(): String {
        return try {
            val gpuInfo = detectGpuBySafeMethod()
            val features = detectGpuFeatures()

            val featureText = if (features.isNotEmpty()) {
                " (${features.joinToString(", ")})"
            } else {
                ""
            }

            "GPU: $gpuInfo$featureText"
        } catch (e: Exception) {
            Timber.w(e, "GPU安全检测失败: ${e.message}")
            "GPU: 安全检测模式 (${e.message?.take(30) ?: "检测失败"})"
        }
    }

    /**
     * 通过安全方法检测GPU信息
     */
    private fun detectGpuBySafeMethod(): String {
        // 第一层：基于厂商和硬件平台检测
        val manufacturer = Build.MANUFACTURER?.lowercase() ?: ""
        val hardware = Build.HARDWARE?.lowercase() ?: ""
        val board = Build.BOARD?.lowercase() ?: ""

        // 第二层：SoC信息检测（Android 12+）
        val socInfo = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val socManufacturer = Build.SOC_MANUFACTURER?.lowercase() ?: ""
            val socModel = Build.SOC_MODEL?.lowercase() ?: ""
            " SoC: $socManufacturer $socModel".takeIf { socManufacturer.isNotEmpty() } ?: ""
        } else {
            ""
        }

        // GPU厂商映射逻辑
        val gpuVendor = when {
            // Qualcomm Snapdragon → Adreno
            manufacturer.contains("qualcomm") ||
                hardware.contains("qcom") ||
                hardware.contains("msm") ||
                hardware.contains("sdm") ||
                hardware.contains("sm") && hardware.length <= 6 -> "Qualcomm Adreno"

            // Samsung Exynos → ARM Mali
            manufacturer.contains("samsung") &&
                (hardware.contains("exynos") || board.contains("exynos")) -> "ARM Mali (Exynos)"

            // MediaTek → PowerVR/Mali
            hardware.contains("mt") && hardware.length <= 6 ||
                hardware.contains("mediatek") -> "PowerVR/Mali (MediaTek)"

            // HiSilicon Kirin → ARM Mali
            hardware.contains("kirin") ||
                hardware.contains("hi36") ||
                manufacturer.contains("huawei") && hardware.contains("hi") -> "ARM Mali (Kirin)"

            // Google Pixel特殊检测
            manufacturer.contains("google") -> when {
                hardware.contains("gs") -> "ARM Mali (Google Tensor)"
                else -> "Qualcomm Adreno (Pixel)"
            }

            // 其他常见厂商
            manufacturer.contains("xiaomi") -> "Qualcomm Adreno/ARM Mali"
            manufacturer.contains("oneplus") -> "Qualcomm Adreno"
            manufacturer.contains("oppo") || manufacturer.contains("realme") -> "Qualcomm Adreno/ARM Mali"
            manufacturer.contains("vivo") -> "Qualcomm Adreno/ARM Mali"

            else -> "Unknown GPU ($manufacturer/$hardware)"
        }

        return "$gpuVendor$socInfo"
    }

    /**
     * 检测GPU特性支持
     */
    private fun detectGpuFeatures(): List<String> {
        val features = mutableListOf<String>()

        try {
            // OpenGL ES特性检测
            if (context.packageManager.hasSystemFeature("android.hardware.opengles.aep")) {
                features.add("OpenGL ES AEP")
            }

            // Vulkan支持检测
            if (context.packageManager.hasSystemFeature("android.hardware.vulkan.level")) {
                features.add("Vulkan")
            }
            if (context.packageManager.hasSystemFeature("android.hardware.vulkan.version")) {
                features.add("Vulkan API")
            }

            // GPU计算支持
            if (context.packageManager.hasSystemFeature("android.hardware.vulkan.compute")) {
                features.add("Compute")
            }

            // 高性能图形
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                try {
                    // 检查性能等级
                    @Suppress("NewApi")
                    val performanceClass = Build.VERSION.MEDIA_PERFORMANCE_CLASS
                    if (performanceClass >= Build.VERSION_CODES.S) {
                        features.add("High Performance")
                    }
                } catch (e: Exception) {
                    // 静默处理，不影响主逻辑
                }
            }
        } catch (e: Exception) {
            Timber.w(e, "GPU特性检测失败")
        }

        return features
    }

    /**
     * 清理硬件加速资源 (LiteRT 1.3.0自动管理资源)
     */
    fun releaseAccelerationResources(config: AccelerationConfig) {
        // LiteRT 1.3.0自动管理所有硬件加速资源，无需手动清理
        Timber.d("🧹 LiteRT 1.3.0自动管理${config.type}资源，无需手动清理")
    }

    /**
     * 获取加速配置摘要信息
     */
    fun getAccelerationSummary(config: AccelerationConfig): String {
        return "LiteRT运行时: ${config.runtime} | " +
            "性能评分: ${config.deviceScore}/10 | " +
            "特性: ${config.enabledFeatures.joinToString(", ")} | " +
            config.deviceInfo
    }
}
