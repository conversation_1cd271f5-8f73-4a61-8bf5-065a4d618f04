package com.example.gymbro.data.local.dao

import androidx.room.*
import com.example.gymbro.data.local.dto.FtsHit
import com.example.gymbro.data.local.dto.VssHit
import com.example.gymbro.data.local.entity.SearchContentEntity

/**
 * 搜索数据访问对象
 *
 * 提供FTS5全文搜索和VSS向量搜索的数据库操作
 * 基于bge.md的数据库设计，支持混合搜索
 */
@Dao
interface SearchDao {
    /**
     * FTS5全文搜索
     *
     * @param query 搜索查询，支持FTS5语法
     * @param limit 搜索结果限制
     * @return FTS5搜索结果
     */
    @Query(
        """
        SELECT id,
               CASE
                   WHEN content LIKE '%' || :query || '%' THEN 1.0
                   ELSE 0.0
               END AS score,
               content AS highlight
        FROM search_content
        WHERE content LIKE '%' || :query || '%'
        ORDER BY score DESC, created_at DESC
        LIMIT :limit
    """,
    )
    suspend fun fullTextSearch(
        query: String,
        limit: Int = 40,
    ): List<FtsHit>

    /**
     * VSS向量搜索
     *
     * @param queryVector 查询向量
     * @param limit 搜索结果限制
     * @return VSS搜索结果
     */
    @Query(
        """
        SELECT id, 0.5 AS score
        FROM search_content
        WHERE embedding IS NOT NULL
          AND LENGTH(:queryVector) > 0
        ORDER BY created_at DESC
        LIMIT :limit
    """,
    )
    suspend fun vectorSearch(
        queryVector: FloatArray,
        limit: Int = 40,
    ): List<VssHit>

    /**
     * 根据ID列表获取搜索内容详情
     *
     * @param ids 内容ID列表
     * @return 搜索内容实体列表
     */
    @Query(
        """
        SELECT * FROM search_content
        WHERE id IN (:ids)
        ORDER BY created_at DESC
    """,
    )
    suspend fun getSearchDetails(ids: List<Long>): List<SearchContentEntity>

    /**
     * 插入可搜索内容
     *
     * @param content 文本内容
     * @param vector 内容向量
     * @param metadata 元数据
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSearchableContent(entity: SearchContentEntity)

    /**
     * 辅助方法：插入可搜索内容
     */
    suspend fun insertSearchableContent(
        content: String,
        vector: FloatArray,
        metadata: Map<String, Any>,
    ) {
        val entity =
            SearchContentEntity(
                content = content,
                embedding = vector,
                metadata = metadata,
                createdAt = System.currentTimeMillis(),
            )
        insertSearchableContent(entity)
    }

    /**
     * 批量插入可搜索内容
     *
     * @param entities 搜索内容实体列表
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSearchableContentBatch(entities: List<SearchContentEntity>)

    /**
     * 辅助方法：从索引数据批量插入可搜索内容
     */
    suspend fun insertSearchableContentFromIndexData(
        indexData: List<Triple<String, FloatArray, Map<String, Any>>>,
    ) {
        val entities =
            indexData.map { (content, vector, metadata) ->
                SearchContentEntity(
                    content = content,
                    embedding = vector,
                    metadata = metadata,
                    createdAt = System.currentTimeMillis(),
                )
            }
        insertSearchableContentBatch(entities)
    }

    /**
     * 获取搜索建议
     *
     * @param prefix 输入前缀
     * @return 搜索建议列表
     */
    @Query(
        """
        SELECT DISTINCT substr(content, 1, 50) as suggestion
        FROM search_content
        WHERE content LIKE :prefix || '%'
        ORDER BY created_at DESC
        LIMIT 10
    """,
    )
    suspend fun getSearchSuggestions(prefix: String): List<String>

    /**
     * 清理旧的搜索数据
     *
     * @param daysToKeep 保留天数
     */
    @Query(
        """
        DELETE FROM search_content
        WHERE created_at < :cutoffTime
    """,
    )
    suspend fun cleanupOldSearchData(cutoffTime: Long)

    /**
     * 辅助方法：根据天数清理旧数据
     */
    suspend fun cleanupOldSearchData(daysToKeep: Int = 90) {
        val cutoffTime = System.currentTimeMillis() - (daysToKeep * 24 * 60 * 60 * 1000L)
        cleanupOldSearchData(cutoffTime)
    }

    /**
     * 获取待向量化的内容
     * 用于后台同步向量化任务
     *
     * @param limit 批次限制
     * @return 待处理的内容列表
     */
    @Query(
        """
        SELECT * FROM search_content
        WHERE embedding IS NULL
        ORDER BY created_at ASC
        LIMIT :limit
    """,
    )
    suspend fun getPendingEmbeddings(limit: Int = 64): List<SearchContentEntity>

    /**
     * 更新内容向量
     *
     * @param id 内容ID
     * @param vector 向量数据
     */
    @Query(
        """
        UPDATE search_content
        SET embedding = :vector
        WHERE id = :id
    """,
    )
    suspend fun updateContentVector(
        id: Long,
        vector: FloatArray,
    )

    /**
     * 批量更新向量
     */
    suspend fun updateVectors(updates: List<Pair<Long, FloatArray>>) {
        updates.forEach { (id, vector) ->
            updateContentVector(id, vector)
        }
    }

    /**
     * 获取搜索统计信息
     */
    @Query(
        """
        SELECT
            COUNT(*) as total_content,
            COUNT(embedding) as vectorized_content,
            AVG(LENGTH(content)) as avg_content_length
        FROM search_content
    """,
    )
    suspend fun getSearchStats(): SearchStats

    /**
     * 搜索统计信息数据类
     */
    data class SearchStats(
        @ColumnInfo(name = "total_content") val totalContent: Int,
        @ColumnInfo(name = "vectorized_content") val vectorizedContent: Int,
        @ColumnInfo(name = "avg_content_length") val avgContentLength: Double,
    )
}
