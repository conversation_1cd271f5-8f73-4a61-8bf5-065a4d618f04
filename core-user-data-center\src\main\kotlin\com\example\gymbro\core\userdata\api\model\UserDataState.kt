package com.example.gymbro.core.userdata.api.model

import com.example.gymbro.core.error.types.ModernDataError

/**
 * 用户数据状态密封类
 * 
 * 用于表示用户数据的不同状态，支持响应式编程模式。
 * 遵循 GymBro 项目的 ModernResult 模式，提供类型安全的状态管理。
 * 
 * @param T 数据类型，通常为 UnifiedUserData
 */
sealed class UserDataState<out T> {
    
    /**
     * 加载中状态
     * 表示正在获取或同步用户数据
     */
    object Loading : UserDataState<Nothing>() {
        override fun toString(): String = "UserDataState.Loading"
    }

    /**
     * 成功状态
     * 包含成功获取的用户数据
     * 
     * @property data 用户数据
     */
    data class Success<T>(val data: T) : UserDataState<T>() {
        override fun toString(): String = "UserDataState.Success(data=$data)"
    }

    /**
     * 错误状态
     * 包含错误信息和可选的部分数据
     * 
     * @property error 错误信息
     * @property partialData 部分可用的数据，可选
     */
    data class Error(
        val error: ModernDataError,
        val partialData: UnifiedUserData? = null
    ) : UserDataState<Nothing>() {
        override fun toString(): String = "UserDataState.Error(error=$error, hasPartialData=${partialData != null})"
    }

    /**
     * 空状态
     * 表示用户未登录或数据不存在
     */
    object Empty : UserDataState<Nothing>() {
        override fun toString(): String = "UserDataState.Empty"
    }

    /**
     * 同步中状态
     * 表示数据正在同步，包含当前可用的数据
     * 
     * @property currentData 当前可用的数据
     * @property syncProgress 同步进度（0.0 到 1.0）
     */
    data class Syncing<T>(
        val currentData: T,
        val syncProgress: Float = 0.0f
    ) : UserDataState<T>() {
        init {
            require(syncProgress in 0.0f..1.0f) { "同步进度必须在 0.0 到 1.0 之间" }
        }
        
        override fun toString(): String = "UserDataState.Syncing(progress=${(syncProgress * 100).toInt()}%)"
    }

    // === 便利方法 ===

    /**
     * 检查是否为成功状态
     */
    val isSuccess: Boolean
        get() = this is Success

    /**
     * 检查是否为错误状态
     */
    val isError: Boolean
        get() = this is Error

    /**
     * 检查是否为加载中状态
     */
    val isLoading: Boolean
        get() = this is Loading

    /**
     * 检查是否为空状态
     */
    val isEmpty: Boolean
        get() = this is Empty

    /**
     * 检查是否为同步中状态
     */
    val isSyncing: Boolean
        get() = this is Syncing

    /**
     * 检查是否有可用数据
     */
    val hasData: Boolean
        get() = when (this) {
            is Success -> true
            is Syncing -> true
            is Error -> partialData != null
            else -> false
        }

    /**
     * 获取数据（如果可用）
     * @return 数据或 null
     */
    fun getDataOrNull(): T? {
        return when (this) {
            is Success -> data
            is Syncing -> currentData
            is Error -> partialData as? T
            else -> null
        }
    }

    /**
     * 获取错误信息（如果存在）
     * @return 错误信息或 null
     */
    fun getErrorOrNull(): ModernDataError? {
        return when (this) {
            is Error -> error
            else -> null
        }
    }

    /**
     * 映射数据类型
     * @param transform 转换函数
     * @return 转换后的状态
     */
    inline fun <R> map(transform: (T) -> R): UserDataState<R> {
        return when (this) {
            is Success -> Success(transform(data))
            is Syncing -> Syncing(transform(currentData), syncProgress)
            is Error -> Error(error, partialData?.let { transform(it as T) } as? UnifiedUserData)
            is Loading -> Loading
            is Empty -> Empty
        }
    }

    /**
     * 扁平映射
     * @param transform 转换函数
     * @return 转换后的状态
     */
    inline fun <R> flatMap(transform: (T) -> UserDataState<R>): UserDataState<R> {
        return when (this) {
            is Success -> transform(data)
            is Syncing -> {
                when (val result = transform(currentData)) {
                    is Success -> Syncing(result.data, syncProgress)
                    else -> result
                }
            }
            is Error -> Error(error, partialData?.let { 
                transform(it as T).getDataOrNull() 
            } as? UnifiedUserData)
            is Loading -> Loading
            is Empty -> Empty
        }
    }

    companion object {
        /**
         * 创建成功状态
         */
        fun <T> success(data: T): UserDataState<T> = Success(data)

        /**
         * 创建错误状态
         */
        fun error(error: ModernDataError, partialData: UnifiedUserData? = null): UserDataState<Nothing> = 
            Error(error, partialData)

        /**
         * 创建加载中状态
         */
        fun loading(): UserDataState<Nothing> = Loading

        /**
         * 创建空状态
         */
        fun empty(): UserDataState<Nothing> = Empty

        /**
         * 创建同步中状态
         */
        fun <T> syncing(data: T, progress: Float = 0.0f): UserDataState<T> = 
            Syncing(data, progress)
    }
}
