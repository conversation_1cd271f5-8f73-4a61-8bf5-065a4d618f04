package com.example.gymbro.core.network.rest.interceptors

import com.example.gymbro.core.network.monitor.NetworkMonitor
import com.example.gymbro.core.network.monitor.NetworkState
import io.mockk.*
import kotlinx.coroutines.flow.MutableStateFlow
import okhttp3.*
import okhttp3.mockwebserver.MockResponse
import okhttp3.mockwebserver.MockWebServer
import org.junit.After
import org.junit.Before
import org.junit.Test
import java.io.IOException
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * 拦截器单元测试
 *
 * 测试各种拦截器的功能和行为
 */
class InterceptorsTest {

    private lateinit var mockWebServer: MockWebServer
    private lateinit var okHttpClient: OkHttpClient

    @Before
    fun setup() {
        mockWebServer = MockWebServer()
        mockWebServer.start()
    }

    @After
    fun teardown() {
        mockWebServer.shutdown()
    }

    @Test
    fun `test AuthInterceptor adds authorization header`() {
        val authInterceptor = AuthInterceptor("test-api-key")

        okHttpClient = OkHttpClient.Builder()
            .addInterceptor(authInterceptor)
            .build()

        mockWebServer.enqueue(MockResponse().setResponseCode(200))

        val request = Request.Builder()
            .url(mockWebServer.url("/test"))
            .build()

        okHttpClient.newCall(request).execute().use { response ->
            assertEquals(200, response.code)
        }

        val recordedRequest = mockWebServer.takeRequest()
        assertEquals("Bearer test-api-key", recordedRequest.getHeader("Authorization"))
    }

    @Test
    fun `test AuthInterceptor skips when authorization exists`() {
        val authInterceptor = AuthInterceptor("test-api-key")

        okHttpClient = OkHttpClient.Builder()
            .addInterceptor(authInterceptor)
            .build()

        mockWebServer.enqueue(MockResponse().setResponseCode(200))

        val request = Request.Builder()
            .url(mockWebServer.url("/test"))
            .addHeader("Authorization", "Bearer existing-token")
            .build()

        okHttpClient.newCall(request).execute().use { response ->
            assertEquals(200, response.code)
        }

        val recordedRequest = mockWebServer.takeRequest()
        assertEquals("Bearer existing-token", recordedRequest.getHeader("Authorization"))
    }

    @Test
    fun `test AuthInterceptor skips when api key is empty`() {
        val authInterceptor = AuthInterceptor("")

        okHttpClient = OkHttpClient.Builder()
            .addInterceptor(authInterceptor)
            .build()

        mockWebServer.enqueue(MockResponse().setResponseCode(200))

        val request = Request.Builder()
            .url(mockWebServer.url("/test"))
            .build()

        okHttpClient.newCall(request).execute().use { response ->
            assertEquals(200, response.code)
        }

        val recordedRequest = mockWebServer.takeRequest()
        assertEquals(null, recordedRequest.getHeader("Authorization"))
    }

    @Test
    fun `test NetworkStatusInterceptor blocks when offline`() {
        val networkMonitor = mockk<NetworkMonitor> {
            every { networkState } returns MutableStateFlow(NetworkState.Unavailable)
        }

        val networkInterceptor = NetworkStatusInterceptor(networkMonitor)

        okHttpClient = OkHttpClient.Builder()
            .addInterceptor(networkInterceptor)
            .build()

        val request = Request.Builder()
            .url(mockWebServer.url("/test"))
            .build()

        try {
            okHttpClient.newCall(request).execute()
            assertTrue(false, "Should have thrown IOException")
        } catch (e: IOException) {
            assertTrue(e.message!!.contains("unavailable"))
        }
    }

    @Test
    fun `test NetworkStatusInterceptor allows when online`() {
        val networkMonitor = mockk<NetworkMonitor> {
            every { networkState } returns MutableStateFlow(NetworkState.Available())
        }

        val networkInterceptor = NetworkStatusInterceptor(networkMonitor)

        okHttpClient = OkHttpClient.Builder()
            .addInterceptor(networkInterceptor)
            .build()

        mockWebServer.enqueue(MockResponse().setResponseCode(200))

        val request = Request.Builder()
            .url(mockWebServer.url("/test"))
            .build()

        okHttpClient.newCall(request).execute().use { response ->
            assertEquals(200, response.code)
        }
    }

    @Test
    fun `test RetryInterceptor retries on server error`() {
        val retryInterceptor = RetryInterceptor(maxRetry = 2, linearDelay = 10)

        okHttpClient = OkHttpClient.Builder()
            .addInterceptor(retryInterceptor)
            .build()

        // 模拟两次502错误，然后成功
        mockWebServer.enqueue(MockResponse().setResponseCode(502))
        mockWebServer.enqueue(MockResponse().setResponseCode(502))
        mockWebServer.enqueue(MockResponse().setResponseCode(200).setBody("Success"))

        val request = Request.Builder()
            .url(mockWebServer.url("/test"))
            .build()

        okHttpClient.newCall(request).execute().use { response ->
            assertEquals(200, response.code)
            assertEquals("Success", response.body?.string())
        }

        // 验证总共发送了3次请求
        assertEquals(3, mockWebServer.requestCount)
    }

    @Test
    fun `test RetryInterceptor does not retry on client error`() {
        val retryInterceptor = RetryInterceptor(maxRetry = 2, linearDelay = 10)

        okHttpClient = OkHttpClient.Builder()
            .addInterceptor(retryInterceptor)
            .build()

        mockWebServer.enqueue(MockResponse().setResponseCode(404))

        val request = Request.Builder()
            .url(mockWebServer.url("/test"))
            .build()

        okHttpClient.newCall(request).execute().use { response ->
            assertEquals(404, response.code)
        }

        // 验证只发送了1次请求（不重试）
        assertEquals(1, mockWebServer.requestCount)
    }

    @Test
    fun `test SafeLoggingInterceptor in debug mode`() {
        val loggingInterceptor = SafeLoggingInterceptor(isDebugMode = true)

        okHttpClient = OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .build()

        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(200)
                .setBody("Response body"),
        )

        val request = Request.Builder()
            .url(mockWebServer.url("/test"))
            .addHeader("Custom-Header", "custom-value")
            .build()

        okHttpClient.newCall(request).execute().use { response ->
            assertEquals(200, response.code)
            assertNotNull(response.body?.string())
        }

        // 在调试模式下，拦截器会记录详细日志
        // 这里主要验证不会抛出异常
    }

    @Test
    fun `test SafeLoggingInterceptor in production mode`() {
        val loggingInterceptor = SafeLoggingInterceptor(isDebugMode = false)

        okHttpClient = OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .build()

        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(200)
                .setBody("Response body"),
        )

        val request = Request.Builder()
            .url(mockWebServer.url("/test"))
            .build()

        okHttpClient.newCall(request).execute().use { response ->
            assertEquals(200, response.code)
        }

        // 在生产模式下，拦截器只记录基本信息
        // 这里主要验证不会抛出异常
    }

    @Test
    fun `test interceptor chain order`() {
        val networkMonitor = mockk<NetworkMonitor> {
            every { networkState } returns MutableStateFlow(NetworkState.Available())
        }

        okHttpClient = OkHttpClient.Builder()
            .addInterceptor(AuthInterceptor("test-key"))
            .addInterceptor(NetworkStatusInterceptor(networkMonitor))
            .addInterceptor(SafeLoggingInterceptor(false))
            .addInterceptor(RetryInterceptor(1, 10))
            .build()

        mockWebServer.enqueue(MockResponse().setResponseCode(200))

        val request = Request.Builder()
            .url(mockWebServer.url("/test"))
            .build()

        okHttpClient.newCall(request).execute().use { response ->
            assertEquals(200, response.code)
        }

        val recordedRequest = mockWebServer.takeRequest()
        assertEquals("Bearer test-key", recordedRequest.getHeader("Authorization"))
    }
}
