package com.example.gymbro.data.history

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.coach.usecase.ChatSessionManagementUseCase
import com.example.gymbro.domain.coach.model.ChatSession
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.ensureActive
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

/**
 * 会话分页数据源
 *
 * 基于Clean Architecture设计，使用domain模型ChatSession：
 * - Paging3 分页加载
 * - 直接返回domain层ChatSession模型
 * - 符合依赖规则：data → domain
 * - 模型转换在features层进行
 *
 * 🔥 修复：协程生命周期管理和取消处理
 */
class ConversationPagingSource @Inject constructor(
    private val chatSessionManagementUseCase: ChatSessionManagementUseCase,
    private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
    private val logger: Logger,
    private val searchQuery: String = "",
) : PagingSource<Int, ChatSession>() {

    companion object {
        private const val STARTING_PAGE_INDEX = 0
        private const val PAGE_SIZE = 30
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, ChatSession> {
        val pageIndex = params.key ?: STARTING_PAGE_INDEX

        logger.d(
            "ConversationPagingSource",
            "📋 开始加载页面: pageIndex=$pageIndex, loadSize=${params.loadSize}, searchQuery='$searchQuery'",
        )

        return try {
            // 检查协程是否被取消
            currentCoroutineContext().ensureActive()

            // 使用统一用户ID
            val userId = "default_user"
            logger.d("ConversationPagingSource", "🔍 使用用户ID: $userId")

            // 数据库操作在IO线程执行
            val sessionsResult = withContext(Dispatchers.IO) {
                currentCoroutineContext().ensureActive()

                if (searchQuery.isNotBlank()) {
                    chatSessionManagementUseCase.searchSessions(
                        userId,
                        searchQuery,
                        pageIndex * PAGE_SIZE,
                        params.loadSize,
                    ).first()
                } else {
                    chatSessionManagementUseCase.getSessionsPaged(
                        userId,
                        params.loadSize,
                        pageIndex * PAGE_SIZE,
                    )
                }
            }

            currentCoroutineContext().ensureActive()

            when (sessionsResult) {
                is ModernResult.Success -> {
                    val sessions = sessionsResult.data

                    logger.d(
                        "ConversationPagingSource",
                        "✅ 成功加载页面: pageIndex=$pageIndex, 会话数量=${sessions.size}",
                    )

                    if (sessions.isNotEmpty()) {
                        logger.d("ConversationPagingSource", "🔍 会话详情:")
                        sessions.take(3).forEach { session ->
                            logger.d("ConversationPagingSource", "  - 会话ID: ${session.id}, 标题: ${session.title}")
                        }
                    }

                    LoadResult.Page(
                        data = sessions,
                        prevKey = if (pageIndex == STARTING_PAGE_INDEX) null else pageIndex - 1,
                        nextKey = if (sessions.isEmpty()) null else pageIndex + 1,
                    )
                }
                is ModernResult.Error -> {
                    logger.e("ConversationPagingSource", "❌ 加载会话失败: ${sessionsResult.error.message}")
                    val errorMessage = when (sessionsResult.error.errorType) {
                        is GlobalErrorType.Network -> "网络连接失败，请检查网络设置"
                        is GlobalErrorType.Database -> "数据库访问失败，请重试"
                        is GlobalErrorType.Auth -> "用户认证失败，请重新登录"
                        else -> "加载会话失败: ${sessionsResult.error.message}"
                    }
                    LoadResult.Error(Exception(errorMessage))
                }
                is ModernResult.Loading -> {
                    logger.w("ConversationPagingSource", "⏳ 数据仍在加载中（搜索模式）")
                    LoadResult.Error(Exception("Data still loading"))
                }
            }
        } catch (e: CancellationException) {
            logger.d("ConversationPagingSource", "协程被取消，停止分页加载")
            throw e // 协程取消异常必须重新抛出
        } catch (exception: Exception) {
            logger.e("ConversationPagingSource", "💥 分页加载异常: ${exception.message}")
            Timber.e(exception, "Exception in ConversationPagingSource.load")
            LoadResult.Error(exception)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, ChatSession>): Int? {
        return state.anchorPosition?.let { anchorPosition ->
            state.closestPageToPosition(anchorPosition)?.prevKey?.plus(1)
                ?: state.closestPageToPosition(anchorPosition)?.nextKey?.minus(1)
        }
    }
}

/**
 * PagingSource 工厂类
 */
class ConversationPagingSourceFactory @Inject constructor(
    private val chatSessionManagementUseCase: ChatSessionManagementUseCase,
    private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
    private val logger: Logger,
) {
    fun create(searchQuery: String = ""): ConversationPagingSource {
        return ConversationPagingSource(
            chatSessionManagementUseCase = chatSessionManagementUseCase,
            getCurrentUserIdUseCase = getCurrentUserIdUseCase,
            logger = logger,
            searchQuery = searchQuery,
        )
    }
}
