package com.example.gymbro.core.network.protocol

import android.os.SystemClock
import com.example.gymbro.core.network.config.NetworkConfigManager
import com.example.gymbro.core.network.eventbus.TokenBus
import com.example.gymbro.core.network.eventbus.TokenEvent
import com.example.gymbro.core.network.ws.LlmStreamClient
import com.example.gymbro.core.network.ws.WsState
import com.example.gymbro.shared.models.ai.ChatMessage
import com.example.gymbro.shared.models.ai.ChatRequest
import com.example.gymbro.shared.models.network.NetworkResult
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.sse.EventSource
import okhttp3.sse.EventSourceListener
import okhttp3.sse.EventSources
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 修复：删除重复的CoreChatMessage定义，使用shared-models中的ChatMessage
 */
@Serializable
data class ChatCompletionRequest(
    val model: String,
    val messages: List<ChatMessage>,
    val stream: Boolean = true,
)

/**
 * 自适应流式客户端 - 智能协议选择实现
 *
 * 🎯 协议策略：
 * 1. 默认HTTP+SSE - 主要协议，稳定可靠
 * 2. 降级HTTP基础 - SSE不支持时的备选方案
 * 3. WebSocket专用 - 仅用于热更新配置的指定模型
 * 4. 动态配置 - 从NetworkConfigManager获取API配置
 */
@Singleton
class AdaptiveStreamClient @Inject constructor(
    private val protocolDetector: ProtocolDetector,
    private val httpClient: OkHttpClient,
    private val json: Json,
    private val configManager: NetworkConfigManager,
    private val tokenBus: TokenBus, // 🔥 【事件总线架构】注入TokenBus替代TokenRouter
) : LlmStreamClient {

    /* ───────── helpers ───────── */
    private fun isDeepSeek(model: String) =
        model.contains("deepseek", ignoreCase = true)

    private fun isTopTierModel(model: String) =
        setOf("o1-preview", "o1-mini", "gpt-4-turbo", "claude-3-opus")
            .any { model.contains(it, true) }

    /* 剥掉 assistant 消息中的 reasoning_content，避免 DeepSeek 400 */
    private fun stripReasoning(msgs: List<ChatMessage>): List<ChatMessage> =
        msgs.map {
            // ChatMessage 只有 role 和 content 字段，无需特殊处理
            it
        }

    /* ───────── public API ───────── */
    /**
     * 🔥 【事件总线架构】唯一的流式聊天方法 - 直接发布到TokenBus
     */
    override suspend fun streamChatWithMessageId(request: ChatRequest, messageId: String, offset: Int) {
        val cfg = configManager.getCurrentConfig()

        // 🔥 诊断日志：验证配置来源
        Timber.d("🔍 [配置诊断] AdaptiveStreamClient获取配置:")
        Timber.d("  - Base URL: ${cfg.restBase}")
        Timber.d("  - API Key: ${if (cfg.apiKey.isNotEmpty()) "✅ 已配置 (${cfg.apiKey.take(10)}...)" else "❌ 未配置"}")
        Timber.d("  - 配置来源: NetworkConfigManager → AiProviderManager")

        Timber.d("🔥 [事件总线] 开始流式聊天并发布到TokenBus: messageId=$messageId")

        streamChatInternal(
            request = request,
            messageId = messageId,
            baseUrl = cfg.restBase,
            apiKey = cfg.apiKey,
            offset = offset
        ).collect { /* Token已发布到TokenBus，无需处理返回值 */ }
    }

    override suspend fun checkConnection(): NetworkResult<Boolean> {
        val cfg = configManager.getCurrentConfig()
        return checkConnection(cfg.restBase, cfg.apiKey)
    }

    override fun getBaseUrl(): String = configManager.getCurrentConfig().restBase
    override fun pause() { Timber.d("HTTP / SSE 无需 pause") }
    override fun resume() { Timber.d("HTTP / SSE 无需 resume") }
    override fun getCurrentState(): WsState = WsState.Open

    /* ───────── core routing ───────── */
    private suspend fun streamChatInternal(
        request: ChatRequest,
        messageId: String,
        baseUrl: String,
        apiKey: String,
        offset: Int = 0,
    ): Flow<String> {
        if (isTopTierModel(request.model)) {
            Timber.d("🔗 WebSocket path for ${request.model}")
            return tryWebSocketWithFallback(request, messageId, baseUrl, apiKey)
        }
        Timber.d("📡 HTTP+SSE path for ${request.model}")
        return tryHttpSSEWithFallback(request, messageId, baseUrl, apiKey)
    }

    /* ─────────────────────────────────────────────────────── */
    /* 3. SSE branch                                           */
    /* ─────────────────────────────────────────────────────── */
    private fun streamChatWithSSE(
        request: ChatRequest,
        messageId: String,
        baseUrl: String,
        apiKey: String,
    ): Flow<String> = callbackFlow {
        try {
            /* 3-1 build body (DeepSeek strip) */
            val msgs = if (isDeepSeek(request.model)) {
                stripReasoning(request.messages)
            } else {
                request.messages
            }
            val bodyJson = json.encodeToString(
                ChatCompletionRequest(request.model, msgs, true),
            )
            val reqBody = bodyJson.toRequestBody("application/json".toMediaTypeOrNull())

            val httpRequest = Request.Builder()
                .url("$baseUrl/v1/chat/completions")
                .post(reqBody)
                .header("Accept", "text/event-stream")
                .header("Cache-Control", "no-cache")
                .header("Connection", "keep-alive")
                .header("Authorization", "Bearer $apiKey")
                .header("User-Agent", "GymBro/1.0")
                .build()

            // 🔥 【关键诊断】详细的请求信息
            Timber.tag("TOKEN-FLOW").i("📡 [AdaptiveStreamClient] SSE请求配置:")
            Timber.tag("TOKEN-FLOW").i("  - URL: $baseUrl/v1/chat/completions")
            Timber.tag("TOKEN-FLOW").i("  - Model: ${request.model}")
            Timber.tag("TOKEN-FLOW").i("  - Stream: true")
            Timber.tag("TOKEN-FLOW").i("  - API Key: ${if (apiKey.isNotBlank()) "✅ 已配置 (${apiKey.take(10)}...)" else "❌ 未配置"}")
            Timber.tag("TOKEN-FLOW").i("  - Messages count: ${msgs.size}")
            Timber.tag("TOKEN-FLOW").d("  - Request body: ${bodyJson.take(500)}${if (bodyJson.length > 500) "..." else ""}")

            Timber.d("📡 SSE请求配置:")
            Timber.d("  - URL: $baseUrl/v1/chat/completions")
            Timber.d("  - Model: ${request.model}")
            Timber.d("  - Stream: true")

            /* 3-2 SSE listener */
            val listener = object : EventSourceListener() {
                @Volatile private var finished = false
                private var lifeKeeper: EventSource? = null
                private var lastBeat = SystemClock.elapsedRealtime()

                override fun onOpen(es: EventSource, resp: okhttp3.Response) {
                    // 🔥 【开启TOKEN-FLOW日志】SSE连接已建立
                    Timber.tag("TOKEN-FLOW").i("🔗 [AdaptiveStreamClient] SSE连接已建立: ${resp.code}")
                    Timber.tag("TOKEN-FLOW").d("🔗 [AdaptiveStreamClient] Response headers: ${resp.headers}")
                    Timber.d("📡 SSE连接已建立: ${resp.code}")
                    lifeKeeper = es
                    lastBeat = SystemClock.elapsedRealtime()

                    // 🔥 【增强诊断】启动多级超时检查
                    MainScope().launch {
                        // 5秒检查：如果连接建立后5秒内没有收到任何数据，可能是配置问题
                        delay(5000)
                        if (!finished && SystemClock.elapsedRealtime() - lastBeat > 5000) {
                            Timber.tag("TOKEN-FLOW").w("⚠️ [AdaptiveStreamClient] SSE连接5秒无数据，可能是API配置问题")
                            Timber.tag("TOKEN-FLOW").w("⚠️ [AdaptiveStreamClient] 请检查：1) API Key是否有效 2) 模型是否支持流式输出 3) 网络连接")
                        }

                        // 30秒超时检查
                        delay(25000) // 总共30秒
                        if (!finished && SystemClock.elapsedRealtime() - lastBeat > 30000) {
                            // 🔥 【开启TOKEN-FLOW日志】SSE连接超时
                            Timber.tag("TOKEN-FLOW").w("⏰ [AdaptiveStreamClient] SSE连接30秒无数据，主动关闭")
                            Timber.w("📡 SSE连接30秒无数据，主动关闭")
                            close(Exception("SSE timeout: no data received for 30 seconds"))
                            lifeKeeper?.cancel()
                        }
                    }
                }

                override fun onEvent(es: EventSource, id: String?, type: String?, data: String) {
                    lastBeat = SystemClock.elapsedRealtime()

                    // 🔥 【关闭TOKEN-FLOW日志】AdaptiveStreamClient工作正常，关闭详细日志减少噪音
                    // Timber.tag("TOKEN-FLOW").i("📨 [AdaptiveStreamClient] 收到SSE事件: id='$id', type='$type', data='${data.take(200)}${if (data.length > 200) "..." else ""}'")
                    Timber.d("📡 收到SSE数据: '${data.take(100)}${if (data.length > 100) "..." else ""}'")

                    if (data.trim() == "[DONE]") {
                        // 🔥 【事件总线架构】发布流完成事件
                        if (messageId.isNotEmpty()) {
                            val completeEvent = TokenEvent(
                                messageId = messageId,
                                token = "",
                                isComplete = true
                            )

                            MainScope().launch {
                                try {
                                    tokenBus.publish(completeEvent)
                                    Timber.tag("TOKEN-FLOW").i("🏁 [事件总线] 流完成事件已发布: messageId=$messageId")
                                } catch (e: Exception) {
                                    Timber.tag("TOKEN-FLOW").e(e, "❌ [事件总线] 流完成事件发布失败: messageId=$messageId")
                                }
                            }
                        }

                        Timber.d("📡 SSE流式传输完成")
                        finished = true
                        close()
                        // 延迟cancel，避免Socket异常
                        MainScope().launch {
                            delay(200)
                            Timber.d("📡 延迟cancel EventSource，避免Socket异常")
                            lifeKeeper?.cancel()
                        }
                        return
                    }

                    // 跳过空数据
                    if (data.trim().isEmpty() || data.trim() == "data: ") {
                        // 🔥 【关闭TOKEN-FLOW日志】跳过空数据
                        // Timber.tag("TOKEN-FLOW").v("⏭️ [AdaptiveStreamClient] 跳过空数据: '$data'")
                        Timber.v("📡 跳过空数据")
                        return
                    }

                    // 🔥 【恢复TOKEN-FLOW日志】准备调用parseSSE
                    Timber.tag("TOKEN-FLOW").i("🔄 [AdaptiveStreamClient] 准备调用parseSSE: model='${request.model}', data='${data.take(100)}...'")

                    // 🔥 增强解析：添加更详细的错误处理
                    try {
                        val parsedContent = parseSSE(data, request.model)
                        if (parsedContent != null) {
                            // 🔥 【事件总线架构】发布到TokenBus
                            if (messageId.isNotEmpty()) {
                                val tokenEvent = TokenEvent(
                                    messageId = messageId,
                                    token = parsedContent,
                                    isComplete = false
                                )

                                // 在协程作用域内发布事件
                                MainScope().launch {
                                    try {
                                        tokenBus.publish(tokenEvent)
                                        Timber.tag("TOKEN-FLOW").i("📡 [事件总线] Token已发布: messageId=$messageId, length=${parsedContent.length}")
                                    } catch (e: Exception) {
                                        Timber.tag("TOKEN-FLOW").e(e, "❌ [事件总线] Token发布失败: messageId=$messageId")
                                    }
                                }
                            }

                            // 🔥 【内部处理】继续发送到Flow用于内部处理
                            val sendResult = trySend(parsedContent)
                            if (!sendResult.isSuccess) {
                                Timber.tag("TOKEN-FLOW").w("❌ [AdaptiveStreamClient] SSE数据发送失败: ${sendResult.exceptionOrNull()}")
                                Timber.w("📡 SSE数据发送失败: ${sendResult.exceptionOrNull()}")
                            } else {
                                Timber.tag("TOKEN-FLOW").i("✅ [AdaptiveStreamClient] SSE数据发送成功")
                                Timber.v("📡 SSE数据发送成功: '${parsedContent.take(50)}...'")
                            }
                        } else {
                            Timber.tag("TOKEN-FLOW").i("🔍 [AdaptiveStreamClient] SSE解析结果为null，跳过: data='${data.take(100)}...'")
                            Timber.v("📡 SSE解析结果为null，跳过")
                        }
                    } catch (e: Exception) {
                        // 🔥 【开启TOKEN-FLOW日志】SSE数据解析异常
                        Timber.tag("TOKEN-FLOW").e(e, "❌ [AdaptiveStreamClient] SSE数据解析异常: data='${data.take(100)}...'")
                        Timber.e(e, "📡 SSE数据解析异常: $data")
                        // 尝试发送原始数据作为fallback
                        val fallbackContent = data.removePrefix("data:").trim()
                        if (fallbackContent.isNotEmpty() && fallbackContent != "[DONE]") {
                            // 🔥 【事件总线架构】Fallback也发布到TokenBus
                            if (messageId.isNotEmpty()) {
                                val tokenEvent = TokenEvent(
                                    messageId = messageId,
                                    token = fallbackContent,
                                    isComplete = false
                                )

                                MainScope().launch {
                                    try {
                                        tokenBus.publish(tokenEvent)
                                        Timber.tag("TOKEN-FLOW").d("📡 [事件总线] Fallback Token已发布: messageId=$messageId")
                                    } catch (e: Exception) {
                                        Timber.tag("TOKEN-FLOW").e(e, "❌ [事件总线] Fallback Token发布失败: messageId=$messageId")
                                    }
                                }
                            }

                            val sendResult = trySend(fallbackContent)
                            if (!sendResult.isSuccess) {
                                Timber.tag("TOKEN-FLOW").w("❌ [AdaptiveStreamClient] Fallback数据发送失败: ${sendResult.exceptionOrNull()}")
                                Timber.w("📡 Fallback数据发送失败: ${sendResult.exceptionOrNull()}")
                            } else {
                                Timber.tag("TOKEN-FLOW").d("✅ [AdaptiveStreamClient] 使用Fallback发送原始数据")
                                Timber.d("📡 使用Fallback发送原始数据: '${fallbackContent.take(50)}...'")
                            }
                        }
                    }
                }

                override fun onClosed(es: EventSource) {
                    // 🔥 【关键诊断】SSE连接关闭 - 检查是否收到过数据
                    Timber.tag("TOKEN-FLOW").w("🔒 [AdaptiveStreamClient] SSE连接已关闭 - finished=$finished, lastBeat=${SystemClock.elapsedRealtime() - lastBeat}ms ago")
                    Timber.d("📡 SSE连接已关闭")

                    // 🔥 如果连接关闭但没有收到任何数据，这可能是问题
                    if (!finished && SystemClock.elapsedRealtime() - lastBeat < 1000) {
                        Timber.tag("TOKEN-FLOW").e("❌ [AdaptiveStreamClient] SSE连接立即关闭，可能是服务器问题或API配置错误")
                        close(Exception("SSE connection closed immediately without data"))
                    } else {
                        close()
                    }
                }

                override fun onFailure(es: EventSource, t: Throwable?, r: okhttp3.Response?) {
                    // Timber.tag("TOKEN-FLOW").e(t, "❌ [AdaptiveStreamClient] SSE连接失败: response=${r?.code} ${r?.message}")
                    // Timber.tag("TOKEN-FLOW").d("❌ [AdaptiveStreamClient] Response headers: ${r?.headers}")

                    // 智能异常分级处理
                    when (t) {
                        is java.net.SocketException -> {
                            if (finished && t.message?.contains("closed") == true) {
                                Timber.i("📡 SSE socket正常关闭（业务完成后的预期行为）")
                                // 不发送Error事件，静默关闭
                                if (!isClosedForSend) {
                                    close() // 静默关闭，不传递异常
                                }
                                return
                            } else {
                                Timber.w("⚠️ SSE socket意外关闭（业务未完成）")
                                // 真正的异常，需要报错
                                if (!isClosedForSend) {
                                    close(t)
                                }
                                return
                            }
                        }
                        is java.io.EOFException -> {
                            if (finished) {
                                Timber.i("📡 SSE连接正常结束（业务完成后的EOF）")
                                close() // 静默关闭
                                return
                            } else {
                                Timber.w("⚠️ SSE连接意外结束（业务未完成的EOF）")
                                close(t)
                                return
                            }
                        }
                    }

                    // 🔥 【SSL/TLS错误增强处理】详细分析连接失败原因
                    val errorCode = r?.code
                    val errorMessage = r?.message ?: "Unknown error"

                    // 特殊处理SSL/TLS相关错误
                    when (t) {
                        is javax.net.ssl.SSLHandshakeException -> {
                            Timber.e(t, "🔒 SSL握手失败 - 可能的证书问题: $errorMessage")
                            Timber.i("🔧 建议检查：1) 网络安全配置 2) 系统时间 3) 证书有效性")
                        }

                        is javax.net.ssl.SSLPeerUnverifiedException -> {
                            Timber.e(t, "🔒 SSL证书验证失败: $errorMessage")
                            Timber.i("🔧 建议检查证书链和域名匹配")
                        }

                        is java.net.ConnectException -> {
                            Timber.e(t, "🌐 连接被拒绝 - 可能的网络问题: $errorMessage")
                            Timber.i("🔧 建议检查：1) 网络连接 2) 防火墙设置 3) 代理配置")
                        }

                        is java.net.UnknownHostException -> {
                            Timber.e(t, "🌐 DNS解析失败: $errorMessage")
                            Timber.i("🔧 建议检查DNS设置和网络连接")
                        }

                        else -> {
                            Timber.e(t, "📡 SSE连接失败: $errorCode $errorMessage")
                        }
                    }

                    // 创建适当的异常以触发重试机制
                    val exception =
                        when {
                            // SSL/TLS错误通常不应重试，除非是临时网络问题
                            t is javax.net.ssl.SSLException -> {
                                Timber.w("🔒 SSL错误，不重试")
                                Exception("SSL error: $errorMessage", t)
                            }

                            errorCode in listOf(502, 503, 504) -> {
                                Timber.w("📡 服务器错误 $errorCode，将触发重试")
                                Exception("Server error: $errorCode $errorMessage", t)
                            }

                            errorCode == 429 -> {
                                Timber.w("📡 请求过于频繁 $errorCode，将触发重试")
                                Exception("Rate limit: $errorCode $errorMessage", t)
                            }

                            errorCode in 400..499 -> {
                                Timber.w("📡 客户端错误 $errorCode，不重试")
                                Exception("Client error: $errorCode $errorMessage", t)
                            }
                        else -> {
                            Timber.w("📡 网络错误，将触发重试")
                            Exception("Network error: $errorCode $errorMessage", t)
                        }
                    }

                    close(exception)
                }
            }

            // 🔥 【开启TOKEN-FLOW日志】记录EventSource创建过程
            Timber.tag("TOKEN-FLOW").i("🚀 [AdaptiveStreamClient] 创建EventSource")
            Timber.tag("TOKEN-FLOW").d("🚀 [AdaptiveStreamClient] Request URL: ${httpRequest.url}")
            Timber.tag("TOKEN-FLOW").d("🚀 [AdaptiveStreamClient] Request headers: ${httpRequest.headers}")

            val es = EventSources.createFactory(httpClient)
                .newEventSource(httpRequest, listener)

            // 🔥 【开启TOKEN-FLOW日志】EventSource已创建
            Timber.tag("TOKEN-FLOW").i("✅ [AdaptiveStreamClient] EventSource已创建，等待连接...")

            awaitClose {
                // 🔥 【开启TOKEN-FLOW日志】关闭SSE连接
                Timber.tag("TOKEN-FLOW").i("🔒 [AdaptiveStreamClient] 关闭SSE连接")
                Timber.d("📡 关闭SSE连接")
                es.cancel()
            }
        } catch (e: Exception) {
            Timber.e(e, "SSE init failed")
            close(e)
        }
    }

    /* ───────── SSE parsing ───────── */
    private fun parseSSE(
        raw: String,
        model: String,
    ): String? {
        // 🔥 【关闭TOKEN-FLOW日志】AdaptiveStreamClient工作正常，关闭详细日志减少噪音
        val enableDebugLogs = false

        // 🔥 【关键调试】记录原始SSE数据 - 已静音
        if (enableDebugLogs) {
            Timber
                .tag("TOKEN-FLOW")
                .i("🔍 [AdaptiveStreamClient] parseSSE输入: raw='${raw.take(300)}${if (raw.length > 300) "..." else ""}', model='$model'")
        }

        val jsonStr = raw.removePrefix("data:").trim()
        if (enableDebugLogs) {
            Timber
                .tag("TOKEN-FLOW")
                .i("🔍 [AdaptiveStreamClient] 移除data:前缀后: jsonStr='${jsonStr.take(300)}${if (jsonStr.length > 300) "..." else ""}'")
        }

        if (jsonStr.isEmpty() || jsonStr == "[DONE]") {
            if (enableDebugLogs) {
                Timber.tag("TOKEN-FLOW").i("🔍 [AdaptiveStreamClient] jsonStr为空或[DONE]，返回null")
            }
            return null
        }

        return try {
            val node = json.parseToJsonElement(jsonStr).jsonObject
            if (enableDebugLogs) {
                Timber.tag("TOKEN-FLOW").i("🔍 [AdaptiveStreamClient] JSON解析成功: ${node.keys}")
            }

            val choices = node["choices"]?.jsonArray
            if (enableDebugLogs) {
                Timber.tag("TOKEN-FLOW").i("🔍 [AdaptiveStreamClient] choices数组: size=${choices?.size}")
            }

            val delta =
                choices
                    ?.firstOrNull()
                    ?.jsonObject
                    ?.get("delta")
                    ?.jsonObject
            if (delta == null) {
                if (enableDebugLogs) {
                    Timber.tag("TOKEN-FLOW").i("🔍 [AdaptiveStreamClient] delta为null，返回null")
                }
                return null
            }
            if (enableDebugLogs) {
                Timber.tag("TOKEN-FLOW").i("🔍 [AdaptiveStreamClient] delta内容: ${delta.keys}")
            }

            if (isDeepSeek(model)) {
                // — DeepSeek 专用 —
                if (enableDebugLogs) {
                    Timber.tag("TOKEN-FLOW").i("🔍 [AdaptiveStreamClient] 使用DeepSeek解析模式")
                }

                val reasoningContent = delta["reasoning_content"]?.jsonPrimitive?.contentOrNull
                if (enableDebugLogs) {
                    Timber
                        .tag("TOKEN-FLOW")
                        .i("🔍 [AdaptiveStreamClient] reasoning_content: '$reasoningContent'")
                }

                // 🔥 修复：reasoning_content 即使是空字符串也要处理，因为这表示思考开始
                if (reasoningContent != null) {
                    if (reasoningContent.isNotEmpty()) {
                        if (enableDebugLogs) {
                            Timber
                                .tag("TOKEN-FLOW")
                                .i("✅ [AdaptiveStreamClient] 返回reasoning_content: '<phase:PLAN>$reasoningContent'")
                        }
                        return "<phase:PLAN>$reasoningContent"
                    } else {
                        // 🔥 空的 reasoning_content 表示思考阶段开始，发送开始标记
                        if (enableDebugLogs) {
                            Timber
                                .tag("TOKEN-FLOW")
                                .i("✅ [AdaptiveStreamClient] 返回reasoning_content开始标记: '<think>'")
                        }
                        return "<think>"
                    }
                }

                val content = delta["content"]?.jsonPrimitive?.contentOrNull
                if (enableDebugLogs) {
                    Timber.tag("TOKEN-FLOW").i("🔍 [AdaptiveStreamClient] content: '$content'")
                }

                content?.takeIf { it.isNotEmpty() }?.let {
                    if (enableDebugLogs) {
                        Timber.tag("TOKEN-FLOW").i("✅ [AdaptiveStreamClient] 返回content: '$it'")
                    }
                    return it
                }

                if (enableDebugLogs) {
                    Timber.tag("TOKEN-FLOW").i("🔍 [AdaptiveStreamClient] DeepSeek模式无有效内容，返回null")
                }
                null
            } else {
                /* — 其他模型沿用旧逻辑 — */
                val content = delta["content"]?.jsonPrimitive?.contentOrNull

                // 过滤系统提示词复述，但保留<think>标签供解析层处理
                if (content != null && content != "null" && content.isNotEmpty()) {
                    val filteredContent = content
                        .let { text ->
                            if (text.contains("你是专业健身AI助手GymBro") ||
                                text.contains("核心能力：") ||
                                text.contains("约束条件：") ||
                                text.contains("严禁行为：") ||
                                text.contains("正确行为：")
                            ) {
                                Timber.w("🚨 检测到系统提示词复述，过滤掉")
                                ""
                            } else {
                                text
                            }
                        }.trim()

                    if (filteredContent.isNotEmpty()) {
                        filteredContent
                    } else {
                        null
                    }
                } else {
                    null
                }
            }
        } catch (e: Exception) {
            if (enableDebugLogs) {
                Timber
                    .tag("TOKEN-FLOW")
                    .w(e, "❌ [AdaptiveStreamClient] SSE解析失败，使用原始内容: raw='${raw.take(200)}...'")
            }
            Timber.w(e, "📡 SSE解析失败，使用原始内容")
            // 尝试直接返回原始数据（某些API可能直接返回文本）
            val fallbackContent = raw.removePrefix("data:").trim()
            if (fallbackContent.isNotEmpty() && fallbackContent != "[DONE]") {
                if (enableDebugLogs) {
                    Timber
                        .tag("TOKEN-FLOW")
                        .d("🔄 [AdaptiveStreamClient] 使用fallback内容: '$fallbackContent'")
                }
                fallbackContent
            } else {
                if (enableDebugLogs) {
                    Timber.tag("TOKEN-FLOW").d("🔄 [AdaptiveStreamClient] fallback内容为空，返回null")
                }
                null
            }
        }
    }

    /* ───────── WebSocket with fallback ───────── */
    private suspend fun tryWebSocketWithFallback(
        request: ChatRequest,
        messageId: String,
        baseUrl: String,
        apiKey: String,
    ): Flow<String> {
        return try {
            // 简单检查WebSocket连通性
            if (isWebSocketAvailable(baseUrl)) {
                streamChatWithWebSocket(request, messageId, baseUrl, apiKey)
            } else {
                Timber.w("⚠️ WebSocket不可用，降级到HTTP+SSE")
                tryHttpSSEWithFallback(request, messageId, baseUrl, apiKey)
            }
        } catch (e: Exception) {
            Timber.w("⚠️ WebSocket连接失败，降级到HTTP+SSE: ${e.message}")
            tryHttpSSEWithFallback(request, messageId, baseUrl, apiKey)
        }
    }

    /**
     * 📡 尝试HTTP+SSE连接，增加1次重试，失败时降级到HTTP
     */
    private suspend fun tryHttpSSEWithFallback(
        request: ChatRequest,
        messageId: String,
        baseUrl: String,
        apiKey: String,
    ): Flow<String> {
        var lastException: Exception? = null

        // 🔄 第一次尝试
        try {
            Timber.d("📡 HTTP+SSE 第1次尝试")
            return streamChatWithSSE(request, messageId, baseUrl, apiKey)
        } catch (e: Exception) {
            lastException = e
            Timber.w("⚠️ HTTP+SSE 第1次失败: ${e.message}")
        }

        // 🔄 第二次重试
        try {
            Timber.d("📡 HTTP+SSE 第2次重试")
            delay(1000) // 重试前等待1秒
            return streamChatWithSSE(request, messageId, baseUrl, apiKey)
        } catch (e: Exception) {
            Timber.w("⚠️ HTTP+SSE 第2次失败: ${e.message}")
            Timber.w("⚠️ HTTP+SSE 重试耗尽，降级到基础HTTP")
            return streamChatWithHttp(request, messageId, baseUrl, apiKey)
        }
    }

    /**
     * 🔍 简单检查WebSocket是否可用
     */
    private suspend fun isWebSocketAvailable(baseUrl: String): Boolean {
        return try {
            // 使用ProtocolDetector的简化检查
            protocolDetector.isWebSocketAvailable(baseUrl, "dummy-key")
        } catch (e: Exception) {
            Timber.w("WebSocket连通性检查失败: ${e.message}")
            false
        }
    }

    /**
     * WebSocket流式传输实现 - 专用于指定模型
     */
    private fun streamChatWithWebSocket(
        request: ChatRequest,
        messageId: String,
        baseUrl: String,
        apiKey: String,
    ): Flow<String> = flow {
        Timber.d("🔗 开始WebSocket专用流式传输")

        // TODO: 实现WebSocket流式传输和事件总线集成
        // 这里需要集成现有的WebSocket客户端
        emit("WebSocket streaming for model: ${request.model}")
    }

    /**
     * HTTP基础请求实现 - 降级方案
     */
    private fun streamChatWithHttp(
        request: ChatRequest,
        messageId: String,
        baseUrl: String,
        apiKey: String,
    ): Flow<String> = flow {
        Timber.d("📄 开始HTTP基础协议（模拟流式）")

        try {
            // 🔥 非流式请求
            val httpRequest = request.copy(stream = false)
            val requestBody = json.encodeToString(httpRequest)
                .toRequestBody("application/json".toMediaTypeOrNull())

            val httpRequestBuilder = Request.Builder()
                .url("$baseUrl/v1/chat/completions")
                .post(requestBody)
                .header("Authorization", "Bearer $apiKey")
                .build()

            val response = httpClient.newCall(httpRequestBuilder).execute()
            response.use {
                if (response.isSuccessful) {
                    val responseBody = response.body?.string() ?: ""
                    Timber.d("📄 HTTP响应成功: ${responseBody.length} chars")

                    val jsonResponse = json.parseToJsonElement(responseBody)
                    val choices = jsonResponse.jsonObject["choices"]?.jsonArray
                    val content = choices?.firstOrNull()?.jsonObject
                        ?.get("message")?.jsonObject
                        ?.get("content")?.jsonPrimitive?.content

                    if (content != null) {
                        // 🔥 模拟流式输出 - 按字符分块发送
                        val chunkSize = 10 // 每次发送10个字符
                        for (i in content.indices step chunkSize) {
                            val chunk = content.substring(i, minOf(i + chunkSize, content.length))
                            emit(chunk)
                            delay(50) // 模拟网络延迟
                        }
                    }
                } else {
                    throw Exception("HTTP请求失败: ${response.code} ${response.message}")
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "📄 HTTP请求失败")
            throw e
        }
    }

    /**
     * 检查连接状态
     */
    suspend fun checkConnection(baseUrl: String, apiKey: String): NetworkResult<Boolean> {
        return try {
            // 使用HTTP HEAD请求检查连接
            val request = Request.Builder()
                .url("$baseUrl/v1/models")
                .head()
                .header("Authorization", "Bearer $apiKey")
                .build()

            val response = httpClient.newCall(request).execute()
            response.use {
                if (response.isSuccessful) {
                    NetworkResult.Success(true)
                } else {
                    NetworkResult.Error(
                        com.example.gymbro.shared.models.network.ApiError.Http(
                            response.code,
                            response.message,
                        ),
                    )
                }
            }
        } catch (e: Exception) {
            NetworkResult.Error(
                com.example.gymbro.shared.models.network.ApiError.Unknown(
                    e.message ?: "Unknown error",
                ),
            )
        }
    }
}
