package com.example.gymbro.core.network.protocol

import okhttp3.OkHttpClient
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 简化协议检测器 - 移除复杂检测逻辑
 *
 * 新策略：
 * - 移除复杂的多层协议检测
 * - 移除协议缓存机制
 * - 只保留基本的连通性检查
 * - 让上层AdaptiveStreamClient处理协议选择
 */
@Singleton
class ProtocolDetector @Inject constructor(
    private val httpClient: OkHttpClient,
) {
    // 🗑️ 移除协议缓存 - 简化状态管理

    /**
     * 🔥 简化协议检测 - 仅基本连通性检查
     *
     * @param baseUrl 服务器基础URL
     * @param apiKey API密钥
     * @return 支持的协议类型
     */
    suspend fun detectSupportedProtocol(baseUrl: String, apiKey: String): SupportedProtocol {
        // 🗑️ 移除复杂的缓存和多层检测逻辑
        // 🎯 简单返回HTTP_SSE，让上层处理具体协议选择
        Timber.d("🔍 简化协议检测: $baseUrl → 默认HTTP_SSE")
        return SupportedProtocol.HTTP_SSE
    }

    /**
     * 🔍 简单的WebSocket连通性检查
     */
    suspend fun isWebSocketAvailable(baseUrl: String, apiKey: String): Boolean {
        return try {
            // 简单的连通性检查，不做复杂协议检测
            val wsUrl = baseUrl.replace("https://", "wss://").replace("http://", "ws://")
            Timber.d("🔍 WebSocket连通性检查: $wsUrl")
            // 这里可以添加简单的ping检查，现在先返回true让上层处理
            true
        } catch (e: Exception) {
            Timber.w("WebSocket连通性检查失败: ${e.message}")
            false
        }
    }

    // 🗑️ 移除复杂的WebSocket升级检测逻辑
    // 🗑️ 移除多端点尝试和复杂的协议握手检测

    // 🗑️ 移除复杂的HTTP SSE检测逻辑
    // 🗑️ 移除测试请求创建和复杂的响应解析
    // 🗑️ 移除协议缓存管理方法
}

/**
 * 支持的协议类型
 */
enum class SupportedProtocol {
    /**
     * WebSocket协议 - 最佳性能，支持双向通信
     */
    WEBSOCKET,

    /**
     * HTTP Server-Sent Events - 单向流式，兼容性好
     */
    HTTP_SSE,

    /**
     * 基础HTTP - 降级方案，无流式支持
     */
    HTTP_BASIC,

    ;

    /**
     * 是否支持流式传输
     */
    fun supportsStreaming(): Boolean = this != HTTP_BASIC

    /**
     * 获取协议描述
     */
    fun getDescription(): String = when (this) {
        WEBSOCKET -> "WebSocket双向流式"
        HTTP_SSE -> "HTTP单向流式"
        HTTP_BASIC -> "HTTP基础请求"
    }
}

/**
 * 协议检测结果
 */
data class ProtocolDetectionResult(
    val protocol: SupportedProtocol,
    val endpoint: String,
    val latencyMs: Long,
    val supportedFeatures: List<String>,
)
