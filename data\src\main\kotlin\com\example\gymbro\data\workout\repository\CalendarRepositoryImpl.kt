package com.example.gymbro.data.workout.repository

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.extensions.safeCatch
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.data.local.dao.CalendarEventDao
import com.example.gymbro.data.local.mapper.CalendarEventMapper
import com.example.gymbro.domain.workout.model.calendar.CalendarDayInfo
import com.example.gymbro.domain.workout.model.calendar.CalendarItem
import com.example.gymbro.domain.workout.model.calendar.CalendarItemType
import com.example.gymbro.domain.workout.model.calendar.WeeklyStats
import com.example.gymbro.domain.workout.repository.CalendarRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.atTime
import kotlinx.datetime.toInstant
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 日历仓库实现
 *
 * 🎯 功能特性：
 * - 使用AppDatabase现有的calendar_events表
 * - 集成Function Call和拖拽功能
 * - 支持CalendarItem的CRUD操作
 * - 提供WeeklyStats计算功能
 * - 完整的错误处理和日志记录
 * - 支持自动保存集成
 *
 * @param calendarEventDao 日历事件DAO
 * @param calendarEventMapper 日历事件映射器
 * @param ioDispatcher IO调度器
 * @param logger 日志记录器
 */
@Singleton
class CalendarRepositoryImpl @Inject constructor(
    private val calendarEventDao: CalendarEventDao,
    private val calendarEventMapper: CalendarEventMapper,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    private val logger: Logger,
) : CalendarRepository {

    // === 基础查询操作 ===

    override suspend fun getCalendarData(
        startDate: LocalDate,
        endDate: LocalDate,
        userId: String,
    ): ModernResult<Map<LocalDate, CalendarDayInfo>> = withContext(ioDispatcher) {
        safeCatch {
            logger.d("CalendarRepositoryImpl", "获取日历数据: $startDate - $endDate")

            val startTimestamp = startDate.atTime(
                0,
                0,
            ).toInstant(TimeZone.currentSystemDefault()).toEpochMilliseconds()
            val endTimestamp = endDate.atTime(
                0,
                0,
            ).toInstant(TimeZone.currentSystemDefault()).toEpochMilliseconds()

            // 查询日历事件
            val events = calendarEventDao.getEventsByDateRange(userId, startTimestamp, endTimestamp)

            // 转换为CalendarDayInfo
            val calendarData = events.groupBy { entity ->
                LocalDate.fromEpochDays((entity.date / (24 * 60 * 60 * 1000)).toInt())
            }.mapValues { (date, eventEntities) ->
                CalendarDayInfo(
                    date = date,
                    workoutCount = eventEntities.size,
                    completedCount = eventEntities.count { it.isCompleted },
                    hasCompletedWorkout = eventEntities.any { it.isCompleted },
                    isRestDay = eventEntities.isEmpty(),
                )
            }

            logger.d("CalendarRepositoryImpl", "日历数据获取成功: ${calendarData.size} 天")
            calendarData
        }
    }

    override suspend fun getCalendarSchedule(
        startDate: LocalDate,
        endDate: LocalDate,
        userId: String,
    ): ModernResult<Map<LocalDate, List<CalendarItem>>> = withContext(ioDispatcher) {
        safeCatch {
            logger.d("CalendarRepositoryImpl", "获取日历排程: $startDate - $endDate")

            val startTimestamp = startDate.atTime(
                0,
                0,
            ).toInstant(TimeZone.currentSystemDefault()).toEpochMilliseconds()
            val endTimestamp = endDate.atTime(
                0,
                0,
            ).toInstant(TimeZone.currentSystemDefault()).toEpochMilliseconds()

            // 查询日历事件
            val events = calendarEventDao.getEventsByDateRange(userId, startTimestamp, endTimestamp)

            // 转换为CalendarItem并按日期分组
            val calendarSchedule = events.groupBy { entity ->
                LocalDate.fromEpochDays((entity.date / (24 * 60 * 60 * 1000)).toInt())
            }.mapValues { (_, eventEntities) ->
                eventEntities.map { entity ->
                    calendarEventMapper.toDomain(entity)
                }
            }

            logger.d("CalendarRepositoryImpl", "日历排程获取成功: ${calendarSchedule.size} 天")
            calendarSchedule
        }
    }

    override suspend fun getItemsForDate(
        date: LocalDate,
        userId: String,
    ): ModernResult<List<CalendarItem>> = withContext(ioDispatcher) {
        safeCatch {
            logger.d("CalendarRepositoryImpl", "获取指定日期项目: $date")

            val timestamp = date.atTime(0, 0).toInstant(TimeZone.currentSystemDefault()).toEpochMilliseconds()

            // 查询指定日期的事件（查询当天0点到次日0点）
            val events = calendarEventDao.getEventsByDate(userId, timestamp)

            // 转换为CalendarItem
            val items = events.map { entity ->
                calendarEventMapper.toDomain(entity)
            }

            logger.d("CalendarRepositoryImpl", "指定日期项目获取成功: ${items.size} 个")
            items
        }
    }

    // === 日历项目CRUD操作 ===

    override suspend fun addCalendarItem(item: CalendarItem): ModernResult<String> = withContext(
        ioDispatcher,
    ) {
        safeCatch {
            logger.d("CalendarRepositoryImpl", "添加日历项目: ${item.name}")

            // 生成新ID如果未提供
            val itemWithId = if (item.id.isEmpty()) {
                item.copy(id = UUID.randomUUID().toString())
            } else {
                item
            }

            // 转换为实体并保存
            val entity = calendarEventMapper.toEntity(itemWithId)
            calendarEventDao.insertEvent(entity)

            logger.d("CalendarRepositoryImpl", "日历项目添加成功: ${itemWithId.id}")
            itemWithId.id
        }
    }

    override suspend fun updateCalendarItem(item: CalendarItem): ModernResult<Unit> = withContext(
        ioDispatcher,
    ) {
        safeCatch {
            logger.d("CalendarRepositoryImpl", "更新日历项目: ${item.id}")

            // 转换为实体并更新
            val entity = calendarEventMapper.toEntity(item)
            calendarEventDao.updateEvent(entity)

            logger.d("CalendarRepositoryImpl", "日历项目更新成功: ${item.id}")
        }
    }

    override suspend fun removeCalendarItem(itemId: String): ModernResult<CalendarItem> = withContext(
        ioDispatcher,
    ) {
        safeCatch {
            logger.d("CalendarRepositoryImpl", "删除日历项目: $itemId")

            // 先获取要删除的项目
            val entity = calendarEventDao.getEventById(itemId)
            if (entity == null) {
                logger.w("CalendarRepositoryImpl", "日历项目不存在: $itemId")
                throw IllegalArgumentException("Calendar item not found: $itemId")
            }

            // 删除项目
            calendarEventDao.deleteEvent(itemId)

            // 转换为Domain对象并返回
            val deletedItem = calendarEventMapper.toDomain(entity)

            logger.d("CalendarRepositoryImpl", "日历项目删除成功: $itemId")
            deletedItem
        }
    }

    // === Function Call 集成操作 ===

    override suspend fun addTemplateToCalendar(
        templateId: String,
        date: LocalDate,
        userId: String,
        metadata: Map<String, String>,
    ): ModernResult<CalendarItem> = withContext(ioDispatcher) {
        safeCatch {
            logger.d("CalendarRepositoryImpl", "添加模板到日历: $templateId -> $date")

            // 创建日历项目
            val calendarItem = CalendarItem(
                id = UUID.randomUUID().toString(),
                type = CalendarItemType.TEMPLATE,
                sourceId = templateId,
                date = date,
                name = metadata["templateName"] ?: "训练模板",
                estimatedDuration = metadata["estimatedDuration"]?.toIntOrNull() ?: 60,
                isCompleted = false,
                canDrag = true,
                order = 0,
                metadata = metadata.plus(
                    mapOf(
                        "description" to (metadata["description"] ?: ""),
                        "userId" to userId,
                    ),
                ),
            )

            // 保存到数据库
            val result = addCalendarItem(calendarItem)
            when (result) {
                is ModernResult.Success -> {
                    logger.d("CalendarRepositoryImpl", "模板添加到日历成功")
                    calendarItem
                }
                is ModernResult.Error -> {
                    throw result.error.cause ?: Exception("Failed to add template to calendar")
                }
                is ModernResult.Loading -> {
                    throw Exception("Unexpected loading state")
                }
            }
        }
    }

    override suspend fun addPlanToCalendar(
        planId: String,
        startDate: LocalDate,
        autoSchedule: Boolean,
        userId: String,
    ): ModernResult<Int> = withContext(ioDispatcher) {
        safeCatch {
            logger.d("CalendarRepositoryImpl", "添加计划到日历: $planId -> $startDate")

            // TODO: 实现计划到日历的添加逻辑
            // 这需要查询PlanDatabase获取计划详情
            val addedItemsCount = 0

            logger.d("CalendarRepositoryImpl", "计划添加到日历成功: $addedItemsCount 项")
            addedItemsCount
        }
    }

    override suspend fun addCustomWorkout(
        name: String,
        date: LocalDate,
        estimatedDuration: Int?,
        userId: String,
        notes: String?,
    ): ModernResult<CalendarItem> = withContext(ioDispatcher) {
        safeCatch {
            logger.d("CalendarRepositoryImpl", "添加自定义训练: $name -> $date")

            // 创建自定义训练项目
            val calendarItem = CalendarItem(
                id = UUID.randomUUID().toString(),
                type = CalendarItemType.CUSTOM_WORKOUT,
                sourceId = UUID.randomUUID().toString(), // 自定义训练的源ID
                date = date,
                name = name,
                estimatedDuration = estimatedDuration,
                isCompleted = false,
                canDrag = true,
                order = 0,
                metadata = mapOf(
                    "createdBy" to userId,
                    "customWorkout" to "true",
                    "notes" to (notes ?: ""),
                    "userId" to userId,
                ),
            )

            // 保存到数据库
            val result = addCalendarItem(calendarItem)
            when (result) {
                is ModernResult.Success -> {
                    logger.d("CalendarRepositoryImpl", "自定义训练添加成功")
                    calendarItem
                }
                is ModernResult.Error -> {
                    throw result.error.cause ?: Exception("Failed to add custom workout")
                }
                is ModernResult.Loading -> {
                    throw Exception("Unexpected loading state")
                }
            }
        }
    }

    // === 拖拽功能操作 ===

    override suspend fun moveCalendarItem(
        itemId: String,
        fromDate: LocalDate,
        toDate: LocalDate,
    ): ModernResult<CalendarItem> = withContext(ioDispatcher) {
        safeCatch {
            logger.d("CalendarRepositoryImpl", "移动日历项目: $itemId ($fromDate -> $toDate)")

            // 获取现有项目
            val entity = calendarEventDao.getEventById(itemId)
            if (entity == null) {
                logger.w("CalendarRepositoryImpl", "日历项目不存在: $itemId")
                throw IllegalArgumentException("Calendar item not found: $itemId")
            }

            // 更新日期
            val updatedEntity = entity.copy(
                date = toDate.atTime(0, 0).toInstant(TimeZone.currentSystemDefault()).toEpochMilliseconds(),
                modifiedAt = System.currentTimeMillis(),
            )

            // 保存更新
            calendarEventDao.updateEvent(updatedEntity)

            // 转换为Domain对象并返回
            val updatedItem = calendarEventMapper.toDomain(updatedEntity)

            logger.d("CalendarRepositoryImpl", "日历项目移动成功")
            updatedItem
        }
    }

    // === 统计功能 ===

    override suspend fun getCalendarStats(
        startDate: LocalDate,
        endDate: LocalDate,
        userId: String,
    ): ModernResult<WeeklyStats> = withContext(ioDispatcher) {
        safeCatch {
            logger.d("CalendarRepositoryImpl", "获取周统计: $startDate - $endDate")

            val startTimestamp = startDate.atTime(
                0,
                0,
            ).toInstant(TimeZone.currentSystemDefault()).toEpochMilliseconds()
            val endTimestamp = endDate.atTime(
                0,
                0,
            ).toInstant(TimeZone.currentSystemDefault()).toEpochMilliseconds()

            // 查询周内所有事件
            val events = calendarEventDao.getEventsByDateRange(userId, startTimestamp, endTimestamp)

            // 计算统计数据
            val completed = events.count { it.isCompleted }
            val total = events.size
            val totalVolume = events.sumOf { it.durationMinutes ?: 0 }.toFloat()
            val completionRate = if (total > 0) completed.toFloat() / total else 0f

            val weeklyStats = WeeklyStats(
                completed = completed,
                total = total,
                totalVolume = totalVolume,
                avgRpe = 0f, // TODO: 实现RPE平均值计算
                completionRate = completionRate,
            )

            logger.d("CalendarRepositoryImpl", "周统计获取成功: $weeklyStats")
            weeklyStats
        }
    }

    // === 自动保存支持方法 ===

    /**
     * 根据ID获取日历项目（用于自动保存）
     */
    override suspend fun getCalendarItemById(
        itemId: String,
    ): ModernResult<CalendarItem?> = withContext(ioDispatcher) {
        safeCatch {
            logger.d("CalendarRepositoryImpl", "根据ID获取日历项目: $itemId")

            val entity = calendarEventDao.getEventById(itemId)
            val item = entity?.let { calendarEventMapper.toDomain(it) }

            logger.d("CalendarRepositoryImpl", "日历项目获取${if (item != null) "成功" else "失败"}")
            item
        }
    }

    /**
     * 检查日历项目是否存在（用于自动保存）
     */
    suspend fun itemExists(itemId: String): ModernResult<Boolean> = withContext(ioDispatcher) {
        safeCatch {
            logger.d("CalendarRepositoryImpl", "检查日历项目存在性: $itemId")

            val exists = calendarEventDao.getEventById(itemId) != null

            logger.d("CalendarRepositoryImpl", "日历项目存在性检查: $exists")
            exists
        }
    }

    // === 必须实现的接口方法（暂时提供基础实现） ===

    override suspend fun moveMultipleItems(
        items: List<String>,
        targetDate: LocalDate,
    ): ModernResult<List<CalendarItem>> = withContext(ioDispatcher) {
        safeCatch {
            logger.d("CalendarRepositoryImpl", "批量移动项目: ${items.size} 个")
            // TODO: 实现批量移动逻辑
            emptyList<CalendarItem>()
        }
    }

    override suspend fun checkMoveConflict(
        item: CalendarItem,
        targetDate: LocalDate,
    ): ModernResult<Boolean> = withContext(ioDispatcher) {
        safeCatch {
            logger.d("CalendarRepositoryImpl", "检查移动冲突: ${item.id} -> $targetDate")
            // TODO: 实现冲突检查逻辑
            false
        }
    }

    override suspend fun addMultipleItems(
        items: List<CalendarItem>,
    ): ModernResult<List<String>> = withContext(ioDispatcher) {
        safeCatch {
            logger.d("CalendarRepositoryImpl", "批量添加项目: ${items.size} 个")
            // TODO: 实现批量添加逻辑
            emptyList<String>()
        }
    }

    override suspend fun removeMultipleItems(
        itemIds: List<String>,
    ): ModernResult<List<CalendarItem>> = withContext(ioDispatcher) {
        safeCatch {
            logger.d("CalendarRepositoryImpl", "批量删除项目: ${itemIds.size} 个")
            // TODO: 实现批量删除逻辑
            emptyList<CalendarItem>()
        }
    }

    override suspend fun clearDateRange(
        startDate: LocalDate,
        endDate: LocalDate,
        userId: String,
        itemTypes: List<CalendarItemType>?,
    ): ModernResult<Int> = withContext(ioDispatcher) {
        safeCatch {
            logger.d("CalendarRepositoryImpl", "清除日期范围: $startDate - $endDate")

            val startTimestamp = startDate.atTime(0, 0).toInstant(TimeZone.currentSystemDefault()).toEpochMilliseconds()
            val endTimestamp = endDate.atTime(23, 59, 59).toInstant(TimeZone.currentSystemDefault()).toEpochMilliseconds()

            // 获取要删除的事件
            val eventsToDelete = if (itemTypes.isNullOrEmpty()) {
                // 获取所有类型的项目
                calendarEventDao.getEventsByDateRange(userId, startTimestamp, endTimestamp)
            } else {
                // 获取指定类型的项目
                val allEvents = calendarEventDao.getEventsByDateRange(userId, startTimestamp, endTimestamp)
                val typeStrings = itemTypes.map { it.name }
                allEvents.filter { event -> typeStrings.contains(event.eventType) }
            }

            // 逐个删除事件
            eventsToDelete.forEach { event ->
                calendarEventDao.deleteEvent(event.id)
            }

            val deletedCount = eventsToDelete.size
            logger.d("CalendarRepositoryImpl", "清除日期范围完成: 删除了 $deletedCount 个项目")
            deletedCount
        }
    }

    override suspend fun searchCalendarItems(
        query: String,
        startDate: LocalDate?,
        endDate: LocalDate?,
        userId: String,
        itemTypes: List<CalendarItemType>?,
    ): ModernResult<List<CalendarItem>> = withContext(ioDispatcher) {
        safeCatch {
            logger.d("CalendarRepositoryImpl", "搜索日历项目: $query")

            // 获取所有事件
            val allEvents = if (startDate != null && endDate != null) {
                val startTimestamp = startDate.atTime(0, 0).toInstant(TimeZone.currentSystemDefault()).toEpochMilliseconds()
                val endTimestamp = endDate.atTime(23, 59, 59).toInstant(TimeZone.currentSystemDefault()).toEpochMilliseconds()
                calendarEventDao.getEventsByDateRange(userId, startTimestamp, endTimestamp)
            } else {
                calendarEventDao.getEventsByUser(userId).first() // 使用Flow.first()获取当前值
            }

            // 过滤事件
            val filteredEvents = allEvents.filter { event ->
                // 类型过滤
                val typeMatches = if (itemTypes.isNullOrEmpty()) {
                    true
                } else {
                    val typeStrings = itemTypes.map { it.name }
                    typeStrings.contains(event.eventType)
                }

                // 查询过滤
                val queryMatches = if (query.isBlank()) {
                    true
                } else {
                    event.title.contains(query, ignoreCase = true) ||
                    event.description?.contains(query, ignoreCase = true) == true ||
                    event.recurrenceRule?.contains(query, ignoreCase = true) == true
                }

                typeMatches && queryMatches
            }

            // 转换为CalendarItem
            val items = filteredEvents.map { entity ->
                calendarEventMapper.toDomain(entity)
            }

            logger.d("CalendarRepositoryImpl", "搜索完成: 找到 ${items.size} 个项目")
            items
        }
    }

    override fun observeCalendarData(
        startDate: LocalDate,
        endDate: LocalDate,
        userId: String,
    ): Flow<Map<LocalDate, List<CalendarItem>>> {
        // TODO: 实现观察数据流
        return kotlinx.coroutines.flow.flowOf(emptyMap())
    }

    override fun observeItemsForDate(
        date: LocalDate,
        userId: String,
    ): Flow<List<CalendarItem>> {
        // TODO: 实现观察指定日期流
        return kotlinx.coroutines.flow.flowOf(emptyList())
    }

    override fun observeCalendarStats(
        startDate: LocalDate,
        endDate: LocalDate,
        userId: String,
    ): Flow<WeeklyStats> {
        // TODO: 实现观察统计流
        return kotlinx.coroutines.flow.flowOf(WeeklyStats())
    }
}
