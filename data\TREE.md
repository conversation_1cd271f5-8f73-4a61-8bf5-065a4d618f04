# Data Module TREE

> **💾 Data Layer - File Structure**
>
> **更新日期**: 2025-06-26 | **版本**: v1.0

```
data/
├── 📄 README.md
├── 📄 TREE.md
├── 📄 INTERFACES.md
├── 📄 IMPLEMENTATION_GUIDE.md
└── 📁 src/main/kotlin/com/example/gymbro/data/
    ├── 📁 ai/
    │   ├── 📄 AiPromptContextProvider.kt
    │   └── 📁 api/
    │       ├── 📄 IpEchoService.kt
    │       ├── 📄 PricingApi.kt
    │       └── 📄 SubscriptionApi.kt
    ├── 📁 autosave/
    │   ├── 📄 AutoSaveManagerImpl.kt
    │   ├── 📄 AutoSaveRepository.kt
    │   ├── 📄 AutoSaveSession.kt
    │   ├── 📄 GlobalAutoSaveManager.kt
    │   ├── 📄 USAGE_EXAMPLES.kt
    │   ├── 📁 adapter/
    │   │   ├── 📄 CalendarAutoSaveAdapter.kt
    │   │   ├── 📄 ProfileAutoSaveAdapter.kt
    │   │   └── 📄 WorkoutAutoSaveAdapter.kt
    │   ├── 📁 di/
    │   │   ├── 📄 AutoSaveModule.kt
    │   │   └── 📄 AutoSaveServiceModule.kt
    │   ├── 📁 internal/
    │   │   ├── 📄 CacheStorage.kt
    │   │   ├── 📄 DatabaseStorage.kt
    │   │   └── 📄 JsonSerializer.kt
    │   ├── 📁 service/
    │   │   ├── 📄 ChatHistoryAutoSaveServiceImpl.kt
    │   │   ├── 📄 PlanAutoSaveServiceImpl.kt
    │   │   ├── 📄 ProfileAutoSaveServiceImpl.kt
    │   │   └── 📄 WorkoutAutoSaveServiceImpl.kt
    │   └── 📁 strategy/
    │       ├── 📄 ImmediateSaveStrategy.kt
    │       ├── 📄 IntervalSaveStrategy.kt
    │       └── 📄 ThrottledSaveStrategy.kt
    ├── 📁 coach/
    │   ├── 📁 ai/
    │   │   └── 📁 repository/
    │   │       └── 📄 MetricsReporter.kt
    │   ├── 📁 dao/
    │   │   ├── 📄 ChatRawDao.kt
    │   │   ├── 📄 ChatSearchDao.kt
    │   │   ├── 📄 ChatSessionDao.kt
    │   │   ├── 📄 MessageEmbeddingDao.kt
    │   │   └── 📄 SearchDao.kt
    │   ├── 📁 entity/
    │   │   ├── 📄 ChatFts.kt
    │   │   ├── 📄 ChatRaw.kt
    │   │   ├── 📄 ChatSessionEntity.kt
    │   │   ├── 📄 ChatVec.kt
    │   │   ├── 📄 MessageEmbeddingEntity.kt
    │   │   ├── 📄 SearchContentEntity.kt
    │   │   └── 📄 SessionSummaryEntity.kt
    │   ├── 📁 mapper/
    │   │   ├── 📄 SearchMapper.kt
    │   │   └── 📄 StreamEventMapper.kt
    │   ├── 📁 model/
    │   │   └── 📄 DataStreamEvent.kt
    │   ├── 📁 parser/
    │   │   └── 📄 ChatStreamParser.kt
    │   ├── 📁 repository/
    │   │   ├── 📄 AICoachRepositoryImpl.kt
    │   │   ├── 📄 AiStreamRepositoryImpl.kt
    │   │   ├── 📄 ChatSessionRepositoryImpl.kt
    │   │   ├── 📄 HistoryStateRepositoryImpl.kt
    │   │   ├── 📄 SuggestionRepositoryImpl.kt
    │   │   └── 📁 search/
    │   │       ├── 📄 ChatSearchRepositoryImpl.kt
    │   │       └── 📄 SearchRepositoryImpl.kt
    │   ├── 📁 service/
    │   │   ├── 📄 AiInteractionServiceImpl.kt
    │   │   ├── 📄 ChatSummaryServiceImpl.kt
    │   │   └── 📁 integration/
    │   │       └── 📄 SmartExerciseMatcherImpl.kt
    │   └── 📁 session/
    │       ├── 📁 dao/
    │       │   └── 📄 ChatSessionDaoRepo.kt
    │       ├── 📁 mapper/
    │       │   └── 📄 ChatSessionMapper.kt
    │       └── 📁 repository/
    │           └── 📄 SessionBackupRepo.kt
    ├── 📁 datasource/
    │   ├── 📄 UserDataSource.kt
    │   ├── 📄 UserSettingsDataSource.kt
    │   └── 📁 impl/
    │       ├── 📄 UserLocalDataSourceImpl.kt
    │       └── 📄 UserRemoteDataSourceImpl.kt
    ├── 📁 di/
    │   ├── 📄 AiModule.kt
    │   └── 📄 ProfileNetworkModule.kt
    ├── 📁 exercise/
    │   ├── 📄 SharedModelsExtensions.kt
    │   ├── 📁 initializer/
    │   │   ├── 📄 ExerciseLibraryInitializerService.kt
    │   │   └── 📄 OfficialExerciseInitializer.kt
    │   ├── 📁 integration/
    │   │   └── 📄 HybridSearchEngine.kt
    │   ├── 📁 local/
    │   │   ├── 📁 converter/
    │   │   │   └── 📄 ExerciseTypeConverters.kt
    │   │   ├── 📁 dao/
    │   │   │   ├── 📄 ExerciseDao.kt
    │   │   │   └── 📄 ExerciseFtsDao.kt
    │   │   ├── 📁 database/
    │   │   │   └── 📄 ExerciseDatabase.kt
    │   │   └── 📁 entity/
    │   │       └── 📄 ExerciseEntity.kt
    │   ├── 📁 mapper/
    │   │   └── 📄 ExerciseMapper.kt
    │   ├── 📁 remote/
    │   │   └── 📄 ExerciseApi.kt
    │   └── 📁 repository/
    │       └── 📄 ExerciseRepositoryImpl.kt
    ├── 📁 local/
    │   ├── 📄 QuickActionProvider.kt
    │   ├── 📁 dao/
    │   │   ├── 📄 CalendarEventDao.kt
    │   │   ├── 📁 auth/
    │   │   │   └── 📄 TokenDao.kt
    │   │   └── 📁 user/
    │   │       ├── 📄 UserCacheEntityDao.kt
    │   │       ├── 📄 UserDao.kt
    │   │       ├── 📄 UserProfileDao.kt
    │   │       └── 📄 UserSettingsDao.kt
    │   ├── 📁 database/
    │   │   ├── 📄 AppDatabase.kt
    │   │   ├── 📄 Converters.kt
    │   │   └── 📁 migrations/
    │   │       ├── 📄 Migration10To11.kt
    │   │       ├── 📄 Migration11To12.kt
    │   │       ├── 📄 Migration14To15.kt
    │   │       ├── 📄 Migration15To16.kt
    │   │       ├── 📄 Migration17To18.kt
    │   │       ├── 📄 Migration7To8.kt
    │   │       └── 📄 Migration8To9.kt
    │   ├── 📁 datastore/
    │   │   ├── 📄 AppSettingsManager.kt
    │   │   ├── 📄 LoginType.kt
    │   │   ├── 📄 PreferencesDataStore.kt
    │   │   ├── 📄 UserPreferencesDataStore.kt
    │   │   ├── 📄 UserPreferencesRepository.kt
    │   │   └── 📄 UserPreferencesRepositoryImpl.kt
    │   ├── 📁 dto/
    │   │   ├── 📄 FtsHit.kt
    │   │   └── 📄 VssHit.kt
    │   ├── 📁 entity/
    │   │   ├── 📄 CalendarEventEntity.kt
    │   │   ├── 📁 auth/
    │   │   │   └── 📄 TokenEntity.kt
    │   │   ├── 📁 subscription/
    │   │   │   └── 📄 SubscriptionEntity.kt
    │   │   ├── 📁 user/
    │   │   │   ├── 📄 UserCacheEntity.kt
    │   │   │   ├── 📄 UserProfileEntity.kt
    │   │   │   └── 📄 UserSettingsEntity.kt
    │   │   └── 📁 workout/
    │   │       └── 📄 WorkoutSessionEntity.kt
    │   ├── 📁 mapper/
    │   │   └── 📄 CalendarEventMapper.kt
    │   ├── 📁 model/
    │   │   ├── 📄 ChatSearchResult.kt
    │   │   └── 📄 PendingMessage.kt
    │   └── 📁 version/
    │       └── 📄 VersionConfigStorage.kt
    ├── 📁 mapper/
    │   ├── 📁 auth/
    │   │   ├── 📄 Auth.mapper.kt
    │   │   └── 📄 Token.mapper.kt
    │   └── 📁 user/
    │       ├── 📄 UserProfile.mapper.kt
    │       └── 📄 UserSettings.mapper.kt
    ├── 📁 memory/
    │   ├── 📁 cache/
    │   │   └── 📄 EcmMemoryCache.kt
    │   ├── 📁 dao/
    │   │   └── 📄 MemoryRecordDao.kt
    │   ├── 📁 entity/
    │   │   ├── 📄 MemoryRecordEntity.kt
    │   │   └── 📄 MemoryTierCount.kt
    │   ├── 📁 integration/
    │   │   └── 📄 MemoryIntegratorImpl.kt
    │   ├── 📁 mapper/
    │   │   └── 📄 MemoryRecordMapper.kt
    │   └── 📁 repository/
    │       └── 📄 MemoryRepositoryImpl.kt
    ├── 📁 model/
    │   ├── 📄 SyncResponse.kt
    │   ├── 📁 auth/
    │   │   ├── 📄 AuthDataResult.kt
    │   │   └── 📄 TokenDto.kt
    │   └── 📁 user/
    │       └── 📄 AvatarUploadResponse.kt
    ├── 📁 network/
    │   ├── 📁 adapter/
    │   │   └── 📄 UserProfileApiAdapter.kt
    │   └── 📁 api/
    │       └── 📄 UserProfileApiService.kt
    ├── 📁 remote/
    │   ├── 📄 AuthApi.kt
    │   ├── 📄 WorkoutApi.kt
    │   ├── 📁 firebase/
    │   │   ├── 📁 auth/
    │   │   │   ├── 📄 AccountLinkingService.kt
    │   │   │   ├── 📄 AuthDataSource.kt
    │   │   │   ├── 📄 AuthStateService.kt
    │   │   │   ├── 📄 FirebaseAuthDataSourceImpl.kt
    │   │   │   ├── 📄 FirebaseAuthService.kt
    │   │   │   ├── 📄 GoogleSignInService.kt
    │   │   │   ├── 📄 PhoneVerificationCallback.kt
    │   │   │   ├── 📄 PhoneVerificationContext.kt
    │   │   │   ├── 📄 PhoneVerificationService.kt
    │   │   │   └── 📄 UserBackupService.kt
    │   │   ├── 📁 datasource/
    │   │   │   ├── 📄 FirebaseUserDataSource.kt
    │   │   │   ├── 📄 FirebaseUserSettingsDataSource.kt
    │   │   │   ├── 📄 FirebaseUserSyncDataSource.kt
    │   │   │   ├── 📄 FirestoreDataSource.kt
    │   │   │   └── 📄 RealtimeDatabaseDataSource.kt
    │   │   └── 📁 service/
    │   │       ├── 📄 FirebaseAnalyticsService.kt
    │   │       └── 📄 FirebasePaymentService.kt
    │   └── 📁 subscription/
    │       └── 📁 dto/
    │           ├── 📄 PlanPriceDto.kt
    │           ├── 📄 PlanTypeDto.kt
    │           ├── 📄 RegionalPricingDto.kt
    │           ├── 📄 SubscriptionDto.kt
    │           ├── 📄 SubscriptionPlanDto.kt
    │           ├── 📄 SubscriptionResponseDto.kt
    │           └── 📄 SubscriptionStatusDto.kt
    ├── 📁 repository/
    │   ├── 📁 auth/
    │   │   ├── 📄 AuthCoreRepository.kt
    │   │   ├── 📄 AuthRepositoryImpl.kt
    │   │   ├── 📄 AuthStateRepository.kt
    │   │   └── 📄 UserManagementRepository.kt
    │   ├── 📁 payment/
    │   │   └── 📄 PaymentRepositoryImpl.kt
    │   ├── 📁 region/
    │   │   ├── 📄 ChinaIpDetector.kt
    │   │   └── 📄 RegionDetectionRepository.kt
    │   ├── 📁 settings/
    │   │   └── 📄 SettingsRepositoryImpl.kt
    │   ├── 📁 subscription/
    │   │   └── 📄 SubscriptionRepositoryImpl.kt
    │   ├── 📁 theme/
    │   │   └── 📄 ThemeRepositoryImpl.kt
    │   ├── 📁 user/
    │   │   ├── 📄 BlockUserRepositoryImpl.kt
    │   │   ├── 📄 FavoriteRepositoryImpl.kt
    │   │   ├── 📄 FirebaseUserRepositoryImpl.kt
    │   │   ├── 📄 TempUserRepositoryImpl.kt
    │   │   ├── 📄 UserPreferenceRepositoryImpl.kt
    │   │   ├── 📄 UserProfileRepositoryImpl.kt
    │   │   ├── 📄 UserSettingsRepositoryImpl.kt
    │   │   └── 📄 UserStatsRepositoryImpl.kt
    │   └── 📁 version/
    │       └── 📄 VersionConfigRepositoryImpl.kt
    ├── 📁 shared/
    │   ├── 📁 common/
    │   │   └── 📄 SensitiveData.kt
    │   ├── 📁 initializer/
    │   │   └── 📄 DatabaseInitializerService.kt
    │   ├── 📁 manager/
    │   │   └── 📄 SubscriptionStateManager.kt
    │   ├── 📁 migration/
    │   │   └── 📄 DatabaseMigrationService.kt
    │   ├── 📁 monitoring/
    │   │   └── 📄 MetricsEndpoint.kt
    │   ├── 📁 network/
    │   │   ├── 📄 NetworkDataSource.kt
    │   │   └── 📄 NetworkDataSourceImpl.kt
    │   ├── 📁 optimization/
    │   │   └── 📄 MemoryOptimizer.kt
    │   ├── 📁 service/
    │   │   └── 📄 AuthServiceImpl.kt
    │   ├── 📁 sync/
    │   │   ├── 📄 AuthDataSyncService.kt
    │   │   ├── 📄 SyncConflictResolver.kt
    │   │   ├── 📄 SyncCoordinatorImpl.kt
    │   │   └── 📄 SyncManager.kt
    │   └── 📁 theme/
    │       └── 📄 ThemePersistenceImpl.kt
    ├── 📁 title/
    │   └── 📄 BgeTitleGeneratorImpl.kt
    ├── 📁 util/
    │   ├── 📄 DataDateTimeConverters.kt
    │   └── 📄 IpUtils.kt
    └── 📁 workout/
        ├── 📄 cleanup_old_files.md
        ├── 📁 model/
        │   ├── 📄 ExerciseData.kt
        │   └── 📄 ExerciseDataProvider.kt
        ├── 📁 plan/
        │   ├── 📁 converter/
        │   │   └── 📄 PlanTypeConverters.kt
        │   ├── 📁 dao/
        │   │   ├── 📄 PlanDao.kt
        │   │   ├── 📄 PlanDayDao.kt
        │   │   └── 📄 PlanTemplateDao.kt
        │   ├── 📁 database/
        │   │   └── 📄 PlanDatabase.kt
        │   ├── 📁 entity/
        │   │   ├── 📄 PlanDayEntity.kt
        │   │   ├── 📄 PlanEntity.kt
        │   │   └── 📄 PlanTemplateEntity.kt
        │   ├── 📁 mapper/
        │   │   └── 📄 PlanMapper.kt
        │   └── 📁 repository/
        │       └── 📄 PlanRepositoryImpl.kt
        ├── 📁 remote/
        │   └── 📄 TemplateSyncApi.kt
        ├── 📁 repository/
        │   └── 📄 CalendarRepositoryImpl.kt
        ├── 📁 session/
        │   ├── 📁 converter/
        │   │   └── 📄 SessionTypeConverters.kt
        │   ├── 📁 dao/
        │   │   ├── 📄 ExerciseHistoryStatsDao.kt
        │   │   ├── 📄 SessionAutoSaveDao.kt
        │   │   ├── 📄 SessionDao.kt
        │   │   ├── 📄 SessionExerciseDao.kt
        │   │   └── 📄 SessionSetDao.kt
        │   ├── 📁 database/
        │   │   └── 📄 SessionDatabase.kt
        │   ├── 📁 entity/
        │   │   ├── 📄 ExerciseHistoryStatsEntity.kt
        │   │   ├── 📄 SessionAutoSaveEntity.kt
        │   │   ├── 📄 SessionEntity.kt
        │   │   ├── 📄 SessionExerciseEntity.kt
        │   │   └── 📄 SessionSetEntity.kt
        │   ├── 📁 mapper/
        │   │   └── 📄 SessionMapper.kt
        │   └── 📁 repository/
        │       └── 📄 SessionRepositoryImpl.kt
        └── 📁 template/
            ├── 📁 adapter/
            │   └── 📄 TemplateJsonAdapter.kt
            ├── 📁 converter/
            │   └── 📄 TemplateTypeConverters.kt
            ├── 📁 dao/
            │   ├── 📄 ExerciseInTemplateDao.kt
            │   ├── 📄 TemplateDao.kt
            │   └── 📄 TemplateVersionDao.kt
            ├── 📁 database/
            │   └── 📄 TemplateDatabase.kt
            ├── 📁 entity/
            │   ├── 📄 ExerciseInTemplateEntity.kt
            │   ├── 📄 TemplateEntity.kt
            │   └── 📄 TemplateVersionEntity.kt
            ├── 📁 mapper/
            │   └── 📄 TemplateMapper.kt
            └── 📁 repository/
                └── 📄 TemplateRepositoryImpl.kt
```
