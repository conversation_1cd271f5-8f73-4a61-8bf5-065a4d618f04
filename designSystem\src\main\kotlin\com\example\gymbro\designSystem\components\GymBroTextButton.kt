package com.example.gymbro.designSystem.components

import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextDecoration
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme

/**
 * 文本按钮组件
 *
 * @param text 按钮文本
 * @param onClick 点击回调
 * @param modifier Modifier修饰符
 * @param enabled 是否启用
 * @param underlined 是否添加下划线
 */
@Composable
fun gymBroTextButton(
    text: UiText,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    underlined: Boolean = false,
) {
    TextButton(
        onClick = onClick,
        modifier = modifier,
        enabled = enabled,
        colors =
        ButtonDefaults.textButtonColors(
            contentColor = MaterialTheme.colorScheme.primary,
            disabledContentColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.5f),
        ),
    ) {
        Text(
            text = text.asString(),
            style =
            MaterialTheme.typography.bodyMedium.copy(
                textDecoration = if (underlined) TextDecoration.Underline else TextDecoration.None,
            ),
        )
    }
}

@GymBroPreview
@Composable
private fun gymBroTextButtonPreview() {
    GymBroTheme {
        gymBroTextButton(
            text = UiText.DynamicString("文本按钮"),
            onClick = { },
        )
    }
}

@GymBroPreview
@Composable
private fun gymBroTextButtonDisabledPreview() {
    GymBroTheme {
        gymBroTextButton(
            text = UiText.DynamicString("禁用文本按钮"),
            onClick = { },
            enabled = false,
        )
    }
}

@GymBroPreview
@Composable
private fun gymBroTextButtonDarkPreview() {
    GymBroTheme(darkTheme = true) {
        gymBroTextButton(
            text = UiText.DynamicString("深色主题按钮"),
            onClick = { },
        )
    }
}

// GymBroTextButtonWithIcon is now removed.
