package com.example.gymbro.data.coach.session.repository

import android.content.Context
import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.coach.model.CoachMessage
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import timber.log.Timber
import java.io.File
import java.nio.channels.FileChannel
import java.nio.channels.FileLock
import java.nio.file.StandardOpenOption
import javax.inject.Inject
import javax.inject.Singleton

/**
 * SessionBackupRepo - DB 失败兜底写 File（加 FileLock）
 *
 * 基于 promtjson.md 文档要求，实现文件备份机制
 * 当数据库写入失败时的兜底机制
 *
 * 核心功能：
 * - 文件锁机制 (FileLock) 确保跨进程安全
 * - JSON 序列化备份消息
 * - 错误恢复机制
 * - 与主 DAO 解耦
 */
@Singleton
class SessionBackupRepo @Inject constructor(
    @ApplicationContext private val context: Context,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    private val json: Json,
) {

    companion object {
        private const val BACKUP_FILE_NAME = "chat_backup.json"
        private const val LOCK_TIMEOUT_MS = 5000L
    }

    /**
     * 备份消息到文件
     * 使用 FileLock 确保跨进程安全
     */
    suspend fun backupMessage(
        sessionId: String,
        message: CoachMessage,
    ): ModernResult<Unit> = withContext(ioDispatcher) {
        try {
            val backupMessage = BackupMessage(
                sessionId = sessionId,
                messageId = message.id,
                role = when (message) {
                    is CoachMessage.UserMessage -> "user"
                    is CoachMessage.AiMessage -> "assistant"
                },
                content = when (message) {
                    is CoachMessage.UserMessage -> message.content
                    is CoachMessage.AiMessage -> message.content
                },
                timestamp = message.timestamp,
            )

            val backupFile = File(context.filesDir, BACKUP_FILE_NAME)
            val backupJson = json.encodeToString(BackupMessage.serializer(), backupMessage)

            // 🔥 关键：使用 FileLock 确保跨进程安全
            writeWithFileLock(backupFile, "$backupJson\n")

            Timber.i("✅ 备份消息写入成功: sessionId=$sessionId, messageId=${message.id}")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "❌ 备份消息写入失败: sessionId=$sessionId, messageId=${message.id}")
            ModernResult.Error(
                DataErrors.DataError.access(
                    operationName = "SessionBackupRepo.backupMessage",
                    message = UiText.DynamicString("备份消息失败"),
                    entityType = "CoachMessage",
                    cause = e,
                ),
            )
        }
    }

    /**
     * 批量备份消息
     */
    suspend fun backupMessages(
        sessionId: String,
        messages: List<CoachMessage>,
    ): ModernResult<Int> = withContext(ioDispatcher) {
        try {
            val backupFile = File(context.filesDir, BACKUP_FILE_NAME)
            val backupData = StringBuilder()

            var successCount = 0
            messages.forEach { message ->
                try {
                    val backupMessage = BackupMessage(
                        sessionId = sessionId,
                        messageId = message.id,
                        role = when (message) {
                            is CoachMessage.UserMessage -> "user"
                            is CoachMessage.AiMessage -> "assistant"
                        },
                        content = when (message) {
                            is CoachMessage.UserMessage -> message.content
                            is CoachMessage.AiMessage -> message.content
                        },
                        timestamp = message.timestamp,
                    )

                    val backupJson = json.encodeToString(BackupMessage.serializer(), backupMessage)
                    backupData.append("$backupJson\n")
                    successCount++
                } catch (e: Exception) {
                    Timber.w(e, "序列化备份消息失败: messageId=${message.id}")
                }
            }

            if (backupData.isNotEmpty()) {
                writeWithFileLock(backupFile, backupData.toString())
            }

            Timber.i("✅ 批量备份完成: sessionId=$sessionId, 成功=$successCount/${messages.size}")
            ModernResult.Success(successCount)
        } catch (e: Exception) {
            Timber.e(e, "❌ 批量备份失败: sessionId=$sessionId")
            ModernResult.Error(
                DataErrors.DataError.access(
                    operationName = "SessionBackupRepo.backupMessages",
                    message = UiText.DynamicString("批量备份消息失败"),
                    entityType = "CoachMessage",
                    cause = e,
                ),
            )
        }
    }

    /**
     * 读取备份文件内容
     */
    suspend fun readBackupMessages(): ModernResult<List<BackupMessage>> = withContext(ioDispatcher) {
        try {
            val backupFile = File(context.filesDir, BACKUP_FILE_NAME)
            if (!backupFile.exists()) {
                return@withContext ModernResult.Success(emptyList())
            }

            val messages = mutableListOf<BackupMessage>()
            backupFile.readLines().forEach { line ->
                if (line.trim().isNotEmpty()) {
                    try {
                        val backupMessage = json.decodeFromString(BackupMessage.serializer(), line)
                        messages.add(backupMessage)
                    } catch (e: Exception) {
                        Timber.w(e, "解析备份消息失败: $line")
                    }
                }
            }

            Timber.d("📄 读取备份消息: ${messages.size}条")
            ModernResult.Success(messages)
        } catch (e: Exception) {
            Timber.e(e, "❌ 读取备份文件失败")
            ModernResult.Error(
                DataErrors.DataError.access(
                    operationName = "SessionBackupRepo.readBackupMessages",
                    message = UiText.DynamicString("读取备份文件失败"),
                    entityType = "BackupMessage",
                    cause = e,
                ),
            )
        }
    }

    /**
     * 清空备份文件
     */
    suspend fun clearBackup(): ModernResult<Unit> = withContext(ioDispatcher) {
        try {
            val backupFile = File(context.filesDir, BACKUP_FILE_NAME)
            if (backupFile.exists()) {
                backupFile.delete()
            }
            Timber.i("🗑️ 备份文件已清空")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "❌ 清空备份文件失败")
            ModernResult.Error(
                DataErrors.DataError.access(
                    operationName = "SessionBackupRepo.clearBackup",
                    message = UiText.DynamicString("清空备份文件失败"),
                    entityType = "BackupFile",
                    cause = e,
                ),
            )
        }
    }

    /**
     * 🔥 关键：使用 FileLock 写入文件
     * 确保跨进程安全
     */
    private fun writeWithFileLock(file: File, content: String) {
        val path = file.toPath()

        FileChannel.open(
            path,
            StandardOpenOption.CREATE,
            StandardOpenOption.WRITE,
            StandardOpenOption.APPEND,
        ).use { channel ->

            // 尝试获取文件锁
            val lock: FileLock = channel.tryLock()
                ?: throw IllegalStateException("无法获取文件锁: ${file.absolutePath}")

            try {
                // 写入内容
                val buffer = java.nio.ByteBuffer.wrap(content.toByteArray())
                channel.write(buffer)
                channel.force(true) // 强制刷新到磁盘

                Timber.v("🔒 FileLock 写入成功: ${content.length} bytes")
            } finally {
                // 释放文件锁
                lock.release()
            }
        }
    }
}

/**
 * 备份消息数据类
 */
@Serializable
data class BackupMessage(
    val sessionId: String,
    val messageId: String,
    val role: String, // "user" or "assistant"
    val content: String,
    val timestamp: Long,
)
