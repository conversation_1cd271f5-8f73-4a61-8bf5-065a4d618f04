package com.example.gymbro.data.workout.session.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.example.gymbro.data.workout.session.entity.SessionExerciseEntity
import kotlinx.coroutines.flow.Flow

/**
 * 会话动作数据访问对象 - SessionDB DAO
 *
 * 基于 05_Final_Database_Architecture.md 和 04_Implementation_Plan_CleanSlate_SessionDB.md 设计
 * 提供会话中动作执行记录管理功能
 */
@Dao
interface SessionExerciseDao {

    // ==================== 基础 CRUD ====================

    @Query("SELECT * FROM session_exercises WHERE id = :sessionExerciseId")
    suspend fun getSessionExerciseById(sessionExerciseId: String): SessionExerciseEntity?

    @Query("SELECT * FROM session_exercises WHERE sessionId = :sessionId ORDER BY `order` ASC")
    fun getSessionExercises(sessionId: String): Flow<List<SessionExerciseEntity>>

    @Query("SELECT * FROM session_exercises WHERE sessionId = :sessionId ORDER BY `order` ASC")
    suspend fun getSessionExercisesSync(sessionId: String): List<SessionExerciseEntity>

    @Query("SELECT * FROM session_exercises WHERE sessionId = :sessionId AND status = 'IN_PROGRESS'")
    suspend fun getCurrentExercise(sessionId: String): SessionExerciseEntity?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSessionExercise(exercise: SessionExerciseEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSessionExercises(exercises: List<SessionExerciseEntity>)

    @Update
    suspend fun updateSessionExercise(exercise: SessionExerciseEntity)

    @Query("DELETE FROM session_exercises WHERE id = :sessionExerciseId")
    suspend fun deleteSessionExercise(sessionExerciseId: String)

    @Query("DELETE FROM session_exercises WHERE sessionId = :sessionId")
    suspend fun deleteAllSessionExercises(sessionId: String)

    // ==================== 状态管理 ====================

    @Query("UPDATE session_exercises SET status = :status WHERE id = :exerciseId")
    suspend fun updateExerciseStatus(exerciseId: String, status: String)

    @Query(
        "UPDATE session_exercises SET completedSets = :completedSets, endTime = :endTime, isCompleted = 1 WHERE id = :exerciseId",
    )
    suspend fun completeExercise(exerciseId: String, completedSets: Int, endTime: Long)

    @Query(
        "UPDATE session_exercises SET startTime = :startTime, status = 'IN_PROGRESS' WHERE id = :exerciseId",
    )
    suspend fun startExercise(exerciseId: String, startTime: Long)

    // ==================== 特定查询 ====================

    @Query("SELECT * FROM session_exercises WHERE sessionId = :sessionId AND exerciseId = :exerciseId")
    suspend fun getSessionExerciseByExerciseId(sessionId: String, exerciseId: String): SessionExerciseEntity?

    @Query(
        "SELECT * FROM session_exercises WHERE sessionId = :sessionId AND isCompleted = 1 ORDER BY `order` ASC",
    )
    fun getCompletedExercises(sessionId: String): Flow<List<SessionExerciseEntity>>

    @Query(
        "SELECT * FROM session_exercises WHERE sessionId = :sessionId AND status = 'NOT_STARTED' ORDER BY `order` ASC",
    )
    fun getPendingExercises(sessionId: String): Flow<List<SessionExerciseEntity>>

    // ==================== 统计查询 ====================

    @Query("SELECT COUNT(*) FROM session_exercises WHERE sessionId = :sessionId")
    suspend fun getExerciseCount(sessionId: String): Int

    @Query("SELECT COUNT(*) FROM session_exercises WHERE sessionId = :sessionId AND isCompleted = 1")
    suspend fun getCompletedExerciseCount(sessionId: String): Int

    @Query("SELECT SUM(completedSets) FROM session_exercises WHERE sessionId = :sessionId")
    suspend fun getTotalCompletedSets(sessionId: String): Int?
}
