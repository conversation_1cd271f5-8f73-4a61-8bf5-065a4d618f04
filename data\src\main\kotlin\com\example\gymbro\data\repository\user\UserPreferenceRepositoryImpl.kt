package com.example.gymbro.data.repository.user

import com.example.gymbro.data.local.datastore.PreferencesDataStore
import com.example.gymbro.data.remote.firebase.auth.UserBackupService
import com.example.gymbro.domain.profile.model.user.FitnessGoal
import com.example.gymbro.domain.profile.model.user.FitnessPreference
import com.example.gymbro.domain.profile.model.user.WeekDay
import com.example.gymbro.domain.profile.usecase.GetUserProfileUseCase
import com.example.gymbro.domain.service.profile.UserPreferencePort
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.*
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.time.Duration.Companion.milliseconds

/**
 * 用户偏好设置仓库实现
 *
 * 真实实现：组合多个数据源获取用户偏好设置
 * 数据源优先级：DataStore → UserProfile → 远程备份
 *
 * @param getUserProfileUseCase 获取用户资料用例
 * @param preferencesDataStore DataStore偏好设置
 * @param userBackupService 远程备份服务
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Singleton
class UserPreferenceRepositoryImpl
@Inject
constructor(
    private val getUserProfileUseCase: GetUserProfileUseCase,
    private val preferencesDataStore: PreferencesDataStore,
    private val userBackupService: UserBackupService,
) : UserPreferencePort {
    /**
     * 用户偏好设置的响应式数据流
     * 数据源组合顺序：DataStore → UserProfile → 远程备份
     * 使用distinctUntilChanged()和debounce()防止UI抖动触发AI重算
     */
    @OptIn(FlowPreview::class)
    override val preferenceFlow: Flow<FitnessPreference> =
        combine(
            // DataStore中的健身偏好（主数据源）
            preferencesDataStore.getFitnessPreferenceFlow().catch {
                Timber.Forest.w(it, "DataStore获取健身偏好失败，使用默认值")
                emit(FitnessPreference())
            },
            // 远程备份（可选）
            getRemoteBackupFlow().catch {
                Timber.Forest.d("远程备份不可用，跳过")
                emit(null)
            },
        ) { dataStorePreference, remoteBackup ->
            // 数据源优先级：DataStore > Remote > Default
            buildFitnessPreferenceFromSources(dataStorePreference, remoteBackup)
        }.distinctUntilChanged() // 防止UI抖动触发AI重算
            .debounce(300.milliseconds) // 防止频繁变更触发AI重算

    /**
     * 获取当前用户偏好设置
     * 同步写入后立即反映新值
     *
     * @return 当前的健身偏好设置，如果获取失败则返回默认值
     */
    override suspend fun current(): FitnessPreference =
        try {
            // 直接从preferenceFlow获取最新值，确保数据一致性
            preferenceFlow.first()
        } catch (e: Exception) {
            Timber.Forest.e(e, "获取当前用户偏好失败，返回默认值")
            FitnessPreference()
        }

    /**
     * 更新完整的健身偏好设置
     *
     * @param preference 新的健身偏好设置
     */
    override suspend fun updateFitnessPreference(preference: FitnessPreference) {
        try {
            preferencesDataStore.updateFitnessPreference(preference)
            Timber.Forest.d(
                "健身偏好设置更新成功: goal=${preference.primaryGoal?.name}, workoutDays=${preference.getWorkoutDaysString()}",
            )
        } catch (e: Exception) {
            Timber.Forest.e(e, "更新健身偏好设置失败")
            throw e
        }
    }

    /**
     * 更新健身目标
     *
     * @param goal 新的健身目标，null表示清除目标
     */
    override suspend fun updateFitnessGoal(goal: FitnessGoal?) {
        try {
            val currentPreference = current()
            val updatedPreference = currentPreference.copy(primaryGoal = goal)
            updateFitnessPreference(updatedPreference)
        } catch (e: Exception) {
            Timber.Forest.e(e, "更新健身目标失败")
            throw e
        }
    }

    /**
     * 更新训练日设置
     *
     * @param days 新的训练日集合
     */
    override suspend fun updateWorkoutDays(days: Set<WeekDay>) {
        try {
            val currentPreference = current()
            val updatedPreference = currentPreference.copy(workoutDays = days)
            updateFitnessPreference(updatedPreference)
        } catch (e: Exception) {
            Timber.Forest.e(e, "更新训练日设置失败")
            throw e
        }
    }

    /**
     * 获取远程备份数据流
     */
    private fun getRemoteBackupFlow(): Flow<Map<String, Any>?> = flowOf(null)
    // TODO: 实现真实的远程备份数据流
    // return flow {
    //     val backupResult = userBackupService.getBackupSettings()
    //     when (backupResult) {
    //         is ModernResult.Success -> emit(backupResult.data)
    //         else -> emit(null)
    //     }
    // }

    /**
     * 从多个数据源构建健身偏好设置
     * 数据源优先级：DataStore > Remote > Default
     */
    private fun buildFitnessPreferenceFromSources(
        dataStorePreference: FitnessPreference,
        remoteBackup: Map<String, Any>?,
    ): FitnessPreference {
        try {
            // 如果DataStore有完整偏好，直接使用
            if (dataStorePreference.hasAnyPreference()) {
                Timber.Forest.d(
                    "使用DataStore健身偏好: goal=${dataStorePreference.primaryGoal?.name}, workoutDays=${dataStorePreference.getWorkoutDaysString()}",
                )
                return dataStorePreference
            }

            // 否则尝试从远程备份补充
            val remoteGoal = extractGoalFromRemote(remoteBackup)
            val remoteWorkoutDays = extractWorkoutDaysFromRemote(remoteBackup)

            val preference =
                FitnessPreference(
                    primaryGoal = dataStorePreference.primaryGoal ?: remoteGoal,
                    workoutDays = if (dataStorePreference.workoutDays.isNotEmpty()) dataStorePreference.workoutDays else remoteWorkoutDays,
                )

            Timber.Forest.d(
                "构建健身偏好(组合): goal=${preference.primaryGoal?.name}, workoutDays=${preference.getWorkoutDaysString()}",
            )
            return preference
        } catch (e: Exception) {
            Timber.Forest.e(e, "构建健身偏好失败，返回默认值")
            return FitnessPreference()
        }
    }

    /**
     * 从远程备份中提取健身目标
     */
    private fun extractGoalFromRemote(
        remoteBackup: Map<String, Any>?,
    ): FitnessGoal? =
        try {
            remoteBackup?.get("primaryGoal")?.let { goalName ->
                FitnessGoal
                    .valueOf(goalName.toString())
            }
        } catch (e: Exception) {
            Timber.Forest.w(e, "从远程备份提取健身目标失败")
            null
        }

    /**
     * 从远程备份中提取训练日
     */
    private fun extractWorkoutDaysFromRemote(remoteBackup: Map<String, Any>?): Set<WeekDay> =
        try {
            remoteBackup?.get("workoutDays")?.let { days ->
                @Suppress("UNCHECKED_CAST")
                (days as? List<String>)
                    ?.mapNotNull { dayName ->
                        try {
                            WeekDay.valueOf(dayName)
                        } catch (e: Exception) {
                            Timber.Forest.w(e, "转换训练日失败: $dayName")
                            null
                        }
                    }?.toSet() ?: emptySet()
            } ?: emptySet()
        } catch (e: Exception) {
            Timber.Forest.w(e, "从远程备份提取训练日失败")
            emptySet()
        }
}
