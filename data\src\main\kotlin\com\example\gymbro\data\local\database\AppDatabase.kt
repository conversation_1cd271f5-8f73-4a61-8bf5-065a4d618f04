package com.example.gymbro.data.local.database

// 导入所有DAO和Entity
import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
// 🔥 Coach v5.0: 已移除重复实体的import
// import com.example.gymbro.data.coach.dao.ConversationMetaDao     // 已删除
// import com.example.gymbro.data.coach.dao.MessageEventDao         // 已删除
// import com.example.gymbro.data.coach.entity.ConversationMetaEntity // 已删除
// import com.example.gymbro.data.coach.entity.MessageEventEntity   // 已删除
import com.example.gymbro.data.coach.dao.MessageEmbeddingDao
import com.example.gymbro.data.coach.entity.MessageEmbeddingEntity
import com.example.gymbro.data.coach.entity.SessionSummaryEntity
import com.example.gymbro.data.coach.dao.ChatRawDao
import com.example.gymbro.data.coach.dao.ChatSessionDao
import com.example.gymbro.data.coach.dao.ChatSearchDao
import com.example.gymbro.data.local.dao.CalendarEventDao
import com.example.gymbro.data.local.dao.SearchDao
import com.example.gymbro.data.local.dao.auth.TokenDao
import com.example.gymbro.data.local.dao.user.UserCacheEntityDao
import com.example.gymbro.data.local.dao.user.UserProfileDao
import com.example.gymbro.data.local.dao.user.UserSettingsDao
import com.example.gymbro.data.local.entity.*
import com.example.gymbro.data.local.entity.auth.TokenEntity
import com.example.gymbro.data.local.entity.user.UserCacheEntity
import com.example.gymbro.data.local.entity.user.UserProfileEntity
import com.example.gymbro.data.local.entity.user.UserSettingsEntity
import com.example.gymbro.data.memory.dao.MemoryRecordDao
import com.example.gymbro.data.memory.entity.MemoryRecordEntity

// 🔥 707发送按钮修复：方案B - 删库重建，无需迁移文件

/**
 * 应用本地数据库
 */
@Database(
    entities = [
        // 用户相关实体
        UserCacheEntity::class,
        UserProfileEntity::class,
        UserSettingsEntity::class,
        TokenEntity::class,

        // 搜索相关实体
        SearchContentEntity::class,
        // 日历相关实体
        CalendarEventEntity::class,
        // 聊天相关实体
        ChatRaw::class,
        ChatFts::class,
        ChatSessionEntity::class,
        ChatVec::class,
        // 🔥 Coach v5.0: 单一数据源架构 - 已移除重复实体
        // ConversationMetaEntity::class, // 已删除：与ChatSessionEntity重复
        // MessageEventEntity::class,     // 已删除：与ChatRaw重复
        // BGE-RAG增强实体
        MessageEmbeddingEntity::class,
        SessionSummaryEntity::class,
        // Memory System记忆实体
        MemoryRecordEntity::class,

    ],
    version = 31, // 🔥 v31: 添加模板版本控制字段 (isDraft, isPublished, currentVersion, lastPublishedAt)
    exportSchema = true,
)
@TypeConverters(Converters::class)
abstract class AppDatabase : RoomDatabase() {
    // 用户相关DAO
    abstract fun userCacheEntityDao(): UserCacheEntityDao

    abstract fun userProfileDao(): UserProfileDao

    abstract fun userSettingsDao(): UserSettingsDao

    abstract fun tokenDao(): TokenDao

    // 搜索相关DAO
    abstract fun searchDao(): SearchDao

    // 日历相关DAO
    abstract fun calendarEventDao(): CalendarEventDao

    // 聊天相关DAO
    abstract fun chatRawDao(): ChatRawDao

    abstract fun chatSessionDao(): ChatSessionDao

    abstract fun chatSearchDao(): ChatSearchDao

    // 🔥 Coach v5.0: 单一数据源架构 - 已移除重复DAO
    // abstract fun conversationMetaDao(): ConversationMetaDao  // 已删除：与ChatSessionDao重复
    // abstract fun messageEventDao(): MessageEventDao          // 已删除：与ChatRawDao重复

    // BGE-RAG增强DAO
    abstract fun messageEmbeddingDao(): MessageEmbeddingDao

    // Memory System记忆DAO
    abstract fun memoryRecordDao(): MemoryRecordDao

    companion object {
        private const val DATABASE_NAME = "app_database"

        @Volatile
        private var INSTANCE: AppDatabase? = null

        fun getDatabase(context: Context): AppDatabase =
            INSTANCE ?: synchronized(this) {
                val instance =
                    Room
                        .databaseBuilder(
                            context.applicationContext,
                            AppDatabase::class.java,
                            DATABASE_NAME,
                        )
                        // 🔥 JSON 数据持久化修复：移除破坏性迁移，保护用户数据
                        .addMigrations(/* 添加具体的迁移策略 */)
                        // 🔥 JSON 数据持久化修复：生产环境不使用破坏性迁移，保护用户数据
                        // 开发环境可以通过 gradle.properties 中的 DEBUG_MODE=true 启用
                        .apply {
                            val debugMode = System.getProperty("DEBUG_MODE", "false").toBoolean()
                            if (debugMode) {
                                fallbackToDestructiveMigration()
                            }
                        }
                        .build()
                INSTANCE = instance
                instance
            }

        // --- 迁移逻辑 ---
    }
}
