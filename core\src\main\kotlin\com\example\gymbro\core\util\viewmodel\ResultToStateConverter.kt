package com.example.gymbro.core.util.viewmodel

import com.example.gymbro.core.error.ModernErrorHandler
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import javax.inject.Inject

/**
 * ModernResult到UI状态的标准转换器
 *
 * 提供从ModernResult到各种UI状态类型的标准转换机制，
 * 确保整个应用中错误处理和状态转换的一致性。
 */
class ResultToStateConverter
@Inject
constructor(
    private val errorHandler: com.example.gymbro.core.error.ModernErrorHandler,
) {
    /**
     * 将ModernResult转换为基本UI状态
     *
     * @param result 需要转换的结果
     * @param currentState 当前UI状态，如果为null则创建新状态
     * @param successStateFactory 成功状态的构造函数
     * @return 根据结果转换后的UI状态
     */
    fun <T, S : BaseUiState> toBaseState(
        result: ModernResult<T>,
        currentState: S? = null,
        successStateFactory: (T) -> S,
    ): S =
        when (result) {
            is ModernResult.Success -> successStateFactory(result.data)
            is ModernResult.Error -> createErrorState(result.error, currentState)
            is ModernResult.Loading -> createLoadingState(currentState)
        }

    /**
     * 将ModernResult转换为数据加载UI状态
     *
     * @param result 需要转换的结果
     * @param currentState 当前UI状态，如果为null则创建新状态
     * @param stateFactory 创建新状态的工厂函数
     * @return 根据结果转换后的数据加载UI状态
     */
    fun <T, S : DataLoadingUiState<T>> toDataState(
        result: ModernResult<T>,
        currentState: S? = null,
        stateFactory: (
            data: T?,
            isLoading: Boolean,
            error: UiText?,
            rawError: ModernDataError?,
            isErrorRecoverable: Boolean,
        ) -> S,
    ): S =
        when (result) {
            is ModernResult.Success ->
                stateFactory(
                    result.data,
                    false,
                    null,
                    null,
                    false,
                )
            is ModernResult.Error -> {
                val errorMessage = errorHandler.getUiMessage(result.error)
                val isRecoverable = result.error.recoverable
                stateFactory(
                    currentState?.data,
                    false,
                    errorMessage,
                    result.error,
                    isRecoverable,
                )
            }
            is ModernResult.Loading ->
                stateFactory(
                    currentState?.data,
                    true,
                    null,
                    null,
                    false,
                )
        }

    /**
     * 将ModernResult转换为列表UI状态
     *
     * @param result 需要转换的结果
     * @param currentState 当前UI状态，如果为null则创建新状态
     * @param stateFactory 创建新状态的工厂函数
     * @param canLoadMore 是否可以加载更多内容的判断函数
     * @return 根据结果转换后的列表UI状态
     */
    fun <T, S : ListUiState<T>> toListState(
        result: ModernResult<List<T>>,
        currentState: S? = null,
        stateFactory: (
            items: List<T>,
            isLoading: Boolean,
            isLoadingMore: Boolean,
            canLoadMore: Boolean,
            error: UiText?,
            rawError: ModernDataError?,
        ) -> S,
        canLoadMore: (List<T>) -> Boolean = { false },
    ): S =
        when (result) {
            is ModernResult.Success ->
                stateFactory(
                    result.data,
                    false,
                    false,
                    canLoadMore(result.data),
                    null,
                    null,
                )
            is ModernResult.Error -> {
                val errorMessage = errorHandler.getUiMessage(result.error)
                stateFactory(
                    currentState?.items ?: emptyList(),
                    false,
                    false,
                    currentState?.canLoadMore ?: false,
                    errorMessage,
                    result.error,
                )
            }
            is ModernResult.Loading ->
                stateFactory(
                    currentState?.items ?: emptyList(),
                    true,
                    false,
                    currentState?.canLoadMore ?: false,
                    null,
                    null,
                )
        }

    /**
     * 处理分页加载更多结果
     *
     * @param result 包含新页数据的结果
     * @param currentState 当前UI状态
     * @param stateFactory 创建新状态的工厂函数
     * @param combineItems 合并当前数据和新数据的函数
     * @param canLoadMore 是否可以加载更多内容的判断函数
     * @return 更新后的列表UI状态
     */
    fun <T, S : ListUiState<T>> handleLoadMore(
        result: ModernResult<List<T>>,
        currentState: S,
        stateFactory: (
            items: List<T>,
            isLoading: Boolean,
            isLoadingMore: Boolean,
            canLoadMore: Boolean,
            error: UiText?,
            rawError: ModernDataError?,
        ) -> S,
        combineItems: (current: List<T>, new: List<T>) -> List<T> = { current, new -> current + new },
        canLoadMore: (List<T>) -> Boolean = { false },
    ): S =
        when (result) {
            is ModernResult.Success -> {
                val combinedItems = combineItems(currentState.items, result.data)
                stateFactory(
                    combinedItems,
                    false,
                    false,
                    canLoadMore(result.data),
                    null,
                    null,
                )
            }
            is ModernResult.Error -> {
                val errorMessage = errorHandler.getUiMessage(result.error)
                stateFactory(
                    currentState.items,
                    false,
                    false,
                    currentState.canLoadMore,
                    errorMessage,
                    result.error,
                )
            }
            is ModernResult.Loading ->
                stateFactory(
                    currentState.items,
                    false,
                    true,
                    currentState.canLoadMore,
                    null,
                    null,
                )
        }

    /**
     * 将ModernResult转换为表单UI状态
     *
     * @param result 需要转换的结果
     * @param currentState 当前UI状态
     * @param stateFactory 创建新状态的工厂函数
     * @return 根据结果转换后的表单UI状态
     */
    fun <T, S : FormUiState> toFormState(
        result: ModernResult<T>,
        currentState: S,
        stateFactory: (
            isLoading: Boolean,
            isSubmitted: Boolean,
            isSubmitSuccessful: Boolean,
            fieldErrors: Map<String, UiText>,
            error: UiText?,
            rawError: ModernDataError?,
        ) -> S,
    ): S =
        when (result) {
            is ModernResult.Success ->
                stateFactory(
                    false,
                    true,
                    true,
                    emptyMap(),
                    null,
                    null,
                )
            is ModernResult.Error -> {
                val errorMessage = errorHandler.getUiMessage(result.error)
                val fieldErrors = errorHandler.extractFieldErrors(result.error)
                stateFactory(
                    false,
                    true,
                    false,
                    fieldErrors,
                    errorMessage,
                    result.error,
                )
            }
            is ModernResult.Loading ->
                stateFactory(
                    true,
                    currentState.isSubmitted,
                    currentState.isSubmitSuccessful,
                    currentState.fieldErrors,
                    null,
                    null,
                )
        }

    /**
     * 创建一个通用的错误状态
     *
     * @param error 错误对象
     * @param currentState 当前状态，如果有的话
     * @return 包含错误信息的状态
     */
    private fun <S : BaseUiState> createErrorState(
        error: ModernDataError,
        currentState: S?,
    ): S {
        @Suppress("UNCHECKED_CAST")
        return when (currentState) {
            is CommonUiState ->
                SimpleUiState(
                    isLoading = false,
                    error = errorHandler.getUiMessage(error),
                    errorSeverity = error.severity,
                    isErrorRecoverable = error.recoverable,
                    rawError = error,
                    errorType = error.errorType,
                ) as S
            null -> throw IllegalArgumentException("当前状态为空且没有提供状态工厂函数")
            else ->
                currentState.apply {
                    // 假设所有BaseUiState实现都有这些属性的setter，但实际可能需要根据具体实现调整
                    if (this is CommonUiState) {
                        return@apply
                    }
                    throw IllegalArgumentException("不支持的UI状态类型: ${currentState::class.simpleName}")
                }
        }
    }

    /**
     * 创建一个通用的加载状态
     *
     * @param currentState 当前状态，如果有的话
     * @return 表示加载中的状态
     */
    private fun <S : BaseUiState> createLoadingState(currentState: S?): S {
        @Suppress("UNCHECKED_CAST")
        return when (currentState) {
            is CommonUiState ->
                SimpleUiState(
                    isLoading = true,
                    error = null,
                    errorSeverity = null,
                    isErrorRecoverable = false,
                    rawError = null,
                    errorType = null,
                ) as S
            null -> throw IllegalArgumentException("当前状态为空且没有提供状态工厂函数")
            else ->
                currentState.apply {
                    // 假设所有BaseUiState实现都有这些属性的setter，但实际可能需要根据具体实现调整
                    if (this is CommonUiState) {
                        return@apply
                    }
                    throw IllegalArgumentException("不支持的UI状态类型: ${currentState::class.simpleName}")
                }
        }
    }
}
