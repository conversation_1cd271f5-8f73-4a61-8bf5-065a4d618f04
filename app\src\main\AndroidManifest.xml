<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 基本网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <!-- 网络状态权限 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <!-- Google Play Services 权限 -->
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />

    <!-- 悬浮窗权限 -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <!-- 前台服务权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <!-- 前台服务类型权限 (Android 14+) -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />

    <!-- 添加Google应用包名信任 -->
    <queries>
        <package android:name="com.google.android.gms" />
    </queries>

    <application
        android:name=".GymBroApp"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.GymBro"
        android:usesCleartextTraffic="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:enableOnBackInvokedCallback="true"
        tools:targetApi="31"
        tools:replace="android:icon">

        <!-- 确保配置文件声明 -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- Firebase服务 -->
        <service
            android:name="com.google.firebase.components.ComponentDiscoveryService"
            android:exported="false">
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
        </service>

        <!-- 明确声明Google Play Services包名和版本 -->
        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />

        <!-- 声明信任的包名 -->
        <meta-data
            android:name="com.google.android.gms.security.trusted_app"
            android:value="true" />

        <!-- 休息计时器悬浮窗服务 -->
        <service
            android:name="com.example.gymbro.features.workout.shared.service.RestTimerOverlayService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="specialUse">
            <meta-data
                android:name="android.app.FOREGROUND_SERVICE_TYPE"
                android:value="specialUse" />
            <meta-data
                android:name="android.foreground_service.special_use.data_sync_reason"
                android:value="Workout rest timer overlay display" />
        </service>

        <activity
            android:name="com.example.gymbro.MainActivity"
            android:exported="true"
            android:theme="@style/Theme.GymBro"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 为WorkManager配置初始化提供程序 -->
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            android:exported="false"
            tools:node="merge">
            <!-- 禁用默认的WorkManager初始化 -->
            <meta-data
                android:name="androidx.work.WorkManagerInitializer"
                android:value="androidx.startup"
                tools:node="remove" />

            <!-- 网络监控初始化器已移除 - 改为按需初始化 -->
        </provider>

    </application>

</manifest>
