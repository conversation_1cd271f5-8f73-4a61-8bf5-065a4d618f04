package com.example.gymbro.core.error.internal

import com.example.gymbro.core.error.ErrorCode
import com.example.gymbro.core.error.ModernErrorHandler
import com.example.gymbro.core.error.extensions.toModernDataError
import com.example.gymbro.core.error.icon.ErrorIconProvider
import com.example.gymbro.core.error.recovery.RecoveryStrategy
import com.example.gymbro.core.error.recovery.getRecoveryStrategy
import com.example.gymbro.core.error.types.ErrorCategory
import com.example.gymbro.core.error.types.ErrorSeverity
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.resources.ResourceProvider
import com.example.gymbro.core.ui.text.UiText
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * ModernErrorHandler 接口的默认实现
 */
@Singleton
class ModernErrorHandlerImpl
@Inject
constructor(
    private val resourceProvider: ResourceProvider,
    private val errorIconProvider: ErrorIconProvider,
) : com.example.gymbro.core.error.ModernErrorHandler {
    /**
     * 获取用户友好的错误消息
     * P3阶段更新：使用ErrorCode替换硬编码中文字符串
     */
    override fun getUiMessage(error: ModernDataError): UiText {
        // 如果已有uiMessage且不是ErrorCode类型，直接返回
        error.uiMessage?.let { message ->
            if (message !is UiText.ErrorCode) {
                return message
            }
        }

        // 根据错误类型映射到ErrorCode
        return when (error.errorType) {
            is GlobalErrorType.Auth.Unauthorized -> UiText.ErrorCode(ErrorCode.AUTH_FAILED)
            is GlobalErrorType.Auth.TokenExpired -> UiText.ErrorCode(ErrorCode.AUTH_SESSION_EXPIRED)
            is GlobalErrorType.Auth.Forbidden -> UiText.ErrorCode(ErrorCode.AUTH_FORBIDDEN)
            is GlobalErrorType.Network.Connection -> UiText.ErrorCode(ErrorCode.NETWORK_CONNECTION_FAILED)
            is GlobalErrorType.Network.Timeout -> UiText.ErrorCode(ErrorCode.NETWORK_UNSTABLE)
            is GlobalErrorType.Data.NotFound -> {
                val entityName = error.getMetadataValue("entity_name", "资源")
                val entityId = error.getMetadataValue("entity_id", null as String?)
                if (entityId != null) {
                    UiText.ErrorCode(ErrorCode.DATA_NOT_FOUND_WITH_ID, entityName, entityId)
                } else {
                    UiText.ErrorCode(ErrorCode.DATA_NOT_FOUND, entityName)
                }
            }
            is GlobalErrorType.Validation.Required -> {
                val field = error.getMetadataValue("field", "字段")
                UiText.ErrorCode(ErrorCode.VALIDATION_REQUIRED, field)
            }
            is GlobalErrorType.Validation.Format -> {
                val field = error.getMetadataValue("field", "输入")
                UiText.ErrorCode(ErrorCode.VALIDATION_FORMAT_INVALID, field)
            }
            is GlobalErrorType.Business -> UiText.ErrorCode(ErrorCode.BUSINESS_RULE_VIOLATION)
            is GlobalErrorType.System -> UiText.ErrorCode(ErrorCode.SYSTEM_ERROR)
            is GlobalErrorType.Unknown -> UiText.ErrorCode(ErrorCode.UNKNOWN_ERROR)
            // 添加更多错误类型的处理...
            else -> UiText.ErrorCode(ErrorCode.UNKNOWN_ERROR)
        }
    }

    /**
     * 判断错误是否可恢复
     *
     * @param error 需要检查的错误
     * @return 错误是否可恢复
     */
    override fun isRetryable(error: ModernDataError): Boolean {
        // 首先检查错误自身是否标记为可恢复
        if (error.recoverable) {
            return true
        }

        // 然后检查错误类型是否通常可恢复
        return when (error.errorType) {
            is GlobalErrorType.Network.Connection,
            is GlobalErrorType.Network.Timeout,
            -> true
            is GlobalErrorType.Auth.TokenExpired -> true
            // 添加更多可恢复错误类型
            else -> false // 默认不可恢复
        }
    }

    /**
     * 获取错误的严重程度
     *
     * @param error 需要检查的错误
     * @return 错误的严重程度
     */
    override fun getSeverity(error: ModernDataError): ErrorSeverity {
        // 使用错误自身的严重程度
        return error.severity
    }

    /**
     * 获取UI层面的错误严重程度
     *
     * @param error 需要检查的错误
     * @return UI严重程度类型
     */
    override fun getSeverityType(error: ModernDataError): ModernErrorHandler.UiSeverityType =
        when (error.severity) {
            ErrorSeverity.INFO -> ModernErrorHandler.UiSeverityType.INFO
            ErrorSeverity.WARNING -> ModernErrorHandler.UiSeverityType.WARNING
            ErrorSeverity.ERROR, ErrorSeverity.CRITICAL -> ModernErrorHandler.UiSeverityType.ERROR
        }

    /**
     * 获取错误的建议操作
     *
     * @param error 需要处理的错误
     * @return 建议的用户操作
     */
    override fun getSuggestedAction(error: ModernDataError): ModernErrorHandler.SuggestedAction =
        when (error.errorType) {
            is GlobalErrorType.Network.Connection,
            -> ModernErrorHandler.SuggestedAction.CHECK_NETWORK
            is GlobalErrorType.Network.Timeout,
            -> ModernErrorHandler.SuggestedAction.RETRY
            is GlobalErrorType.Auth.TokenExpired,
            is GlobalErrorType.Auth.Unauthorized,
            -> ModernErrorHandler.SuggestedAction.RELOGIN
            is GlobalErrorType.System.Resource,
            is GlobalErrorType.System.Internal,
            -> ModernErrorHandler.SuggestedAction.RESTART_APP
            else -> ModernErrorHandler.SuggestedAction.SHOW_MESSAGE
        }

    /**
     * 获取错误的图标资源ID
     *
     * @param error 需要处理的错误
     * @return 图标资源ID
     */
    @Deprecated(
        message = "使用 getErrorIconIdentifier 代替，它不依赖Android框架",
        replaceWith = ReplaceWith("getErrorIconIdentifier(error) as Int"),
        level = DeprecationLevel.WARNING,
    )
    override fun getErrorIconResId(error: ModernDataError): Int = getErrorIconIdentifier(error) as Int

    /**
     * 获取错误图标标识符
     *
     * @param error 需要处理的错误
     * @return 错误图标标识符
     */
    override fun getErrorIconIdentifier(
        error: ModernDataError,
    ): Any = errorIconProvider.getErrorIconIdentifier(error)

    /**
     * 是否需要认证
     *
     * @param error 需要检查的错误
     * @return 是否需要认证
     */
    override fun needsReauthentication(error: ModernDataError): Boolean =
        error.errorType is GlobalErrorType.Auth.TokenExpired ||
            error.errorType is GlobalErrorType.Auth.Unauthorized

    /**
     * 是否应该全局处理
     *
     * @param error 需要检查的错误
     * @return 是否应该全局处理
     */
    override fun shouldHandleGlobally(error: ModernDataError): Boolean =
        error.severity == ErrorSeverity.CRITICAL ||
            needsReauthentication(error) ||
            error.errorType is GlobalErrorType.System

    /**
     * 获取技术错误消息（用于日志）
     *
     * @param error 需要处理的错误
     * @return 技术错误消息
     */
    override fun getTechnicalMessage(error: ModernDataError): String {
        val metadataStr =
            if (error.metadataMap.isNotEmpty()) {
                ", metadata=${error.metadataMap}"
            } else {
                ""
            }
        return "${error.errorType.fullName}: ${error.message}$metadataStr"
    }

    /**
     * 建议恢复策略
     *
     * @param error 需要处理的错误
     * @return 推荐的恢复策略
     */
    override fun <T> suggestRecoveryStrategy(error: ModernDataError): RecoveryStrategy<T>? {
        // 首先检查错误是否已有关联的恢复策略
        error.getRecoveryStrategy<T>()?.let { return it }

        // 根据错误类型提供默认恢复策略
        return null // 目前没有默认恢复策略
    }

    /**
     * 将Throwable转换为ModernResult.Error
     *
     * @param e 需要处理的异常
     * @return 包含错误的ModernResult
     */
    override fun handleError(e: Throwable): ModernResult.Error {
        val error =
            when (e) {
                is ModernDataError -> e
                else ->
                    e.toModernDataError(
                        operationName = "ModernErrorHandlerImpl.handleError",
                        uiMessage = UiText.DynamicString("发生错误"),
                    )
            }
        Timber.e(e, "错误处理: ${getTechnicalMessage(error)}")
        return ModernResult.Error(error)
    }

    /**
     * 将Throwable转换为ModernResult.Error，用于Flow上下文
     *
     * @param e 需要处理的异常
     * @return 包含错误的ModernResult
     */
    override fun handleErrorFlow(e: Throwable): ModernResult.Error {
        // 特殊处理Flow取消异常
        if (e is kotlinx.coroutines.CancellationException) {
            val error =
                ModernDataError(
                    operationName = "ModernErrorHandlerImpl.handleErrorFlow",
                    errorType = GlobalErrorType.Operation.Cancelled,
                    category = ErrorCategory.SYSTEM,
                    uiMessage = UiText.DynamicString("操作已取消"),
                    severity = ErrorSeverity.INFO,
                    cause = e,
                    metadataMap = mapOf("cancelled_by" to "flow_cancellation"),
                )
            Timber.d("Flow取消: %s", error.message)
            return ModernResult.Error(error)
        }

        return handleError(e)
    }

    /**
     * 处理ModernDataError
     *
     * @param modernDataError 要处理的错误
     * @return 处理后的GlobalErrorType
     */
    override fun processModernDataError(modernDataError: ModernDataError): GlobalErrorType {
        // 在这里添加错误处理逻辑，如日志记录、事件跟踪等
        Timber.e("处理错误: ${getTechnicalMessage(modernDataError)}")
        return modernDataError.errorType
    }

    /**
     * 处理ModernResult.Error
     *
     * @param result 要处理的错误结果
     * @return 处理后的GlobalErrorType
     */
    override fun processErrorResult(
        result: ModernResult.Error,
    ): GlobalErrorType = processModernDataError(result.error)

    /**
     * 从错误中提取字段错误信息
     *
     * @param error 错误对象
     * @return 字段名称到错误消息的映射
     */
    override fun extractFieldErrors(error: ModernDataError): Map<String, UiText> {
        val result = mutableMapOf<String, UiText>()

        // 如果是验证错误，提取字段错误
        if (error.errorType is GlobalErrorType.Validation) {
            val field = error.getMetadataValue("field", null as String?)
            if (field != null) {
                result[field] = error.uiMessage ?: UiText.DynamicString("验证错误")
            }
        }

        // 如果是聚合错误，收集所有子错误中的字段错误
        @Suppress("UNCHECKED_CAST")
        val aggregatedErrors = error.getMetadataValue(
            "aggregated_errors",
            emptyList<ModernDataError>(),
        ) as? List<ModernDataError>
        aggregatedErrors?.forEach { subError ->
            result.putAll(extractFieldErrors(subError))
        }

        return result
    }
}
