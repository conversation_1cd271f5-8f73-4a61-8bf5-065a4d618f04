package com.example.gymbro.core.ml.interfaces

import com.example.gymbro.core.error.types.ModernResult

/**
 * 动作匹配引擎接口 - Core-ML Layer
 *
 * 负责基于语义的动作搜索、匹配和推荐
 * 这是纯算法抽象接口，不依赖任何上层模块
 */
interface ExerciseMatchingEngine {

    /**
     * 基于查询文本搜索相关动作
     *
     * @param queryText 查询文本（如"胸肌训练"、"增强核心力量"）
     * @param topK 返回前K个最相关的动作
     * @param threshold 相似度阈值
     * @return 匹配结果列表
     */
    suspend fun searchExercises(
        queryText: String,
        topK: Int = 5,
        threshold: Float = 0.6f,
    ): ModernResult<List<ExerciseMatchResult>>

    /**
     * 基于当前训练状态推荐下一个动作
     *
     * @param currentState 当前训练状态描述
     * @param completedExercises 已完成的动作ID列表
     * @param targetMuscleGroups 目标肌群
     * @param topK 推荐数量
     * @return 推荐的动作列表
     */
    suspend fun recommendNextExercises(
        currentState: String,
        completedExercises: List<String>,
        targetMuscleGroups: List<String> = emptyList(),
        topK: Int = 3,
    ): ModernResult<List<ExerciseRecommendation>>

    /**
     * 查找互补动作
     *
     * @param exerciseId 基准动作ID
     * @param complementType 互补类型（对抗肌群、相同肌群、复合动作等）
     * @param topK 返回数量
     * @return 互补动作列表
     */
    suspend fun findComplementaryExercises(
        exerciseId: String,
        complementType: ComplementType = ComplementType.OPPOSING_MUSCLES,
        topK: Int = 3,
    ): ModernResult<List<ExerciseMatchResult>>

    /**
     * 批量匹配多个查询
     *
     * @param queries 查询文本列表
     * @param topK 每个查询返回的结果数量
     * @return 批量匹配结果
     */
    suspend fun batchMatch(
        queries: List<String>,
        topK: Int = 5,
    ): ModernResult<List<List<ExerciseMatchResult>>>

    /**
     * 更新动作数据（用于增量更新）
     *
     * @param exercises 要添加或更新的动作列表
     * @return 更新结果
     */
    suspend fun updateExercises(exercises: List<ExerciseData>): ModernResult<Unit>

    /**
     * 获取匹配引擎统计信息
     */
    fun getMatchingStats(): MatchingEngineStats
}

/**
 * 动作数据
 */
data class ExerciseData(
    val id: String,
    val name: String,
    val description: String,
    val primaryMuscles: List<String>,
    val secondaryMuscles: List<String>,
    val equipment: String,
    val difficulty: String,
    val instructions: List<String>,
    val tips: List<String> = emptyList(),
    val vector: FloatArray? = null, // 预计算的语义向量
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as ExerciseData

        if (id != other.id) return false
        if (name != other.name) return false
        if (vector != null) {
            if (other.vector == null) return false
            if (!vector.contentEquals(other.vector)) return false
        } else if (other.vector != null) return false

        return true
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + name.hashCode()
        result = 31 * result + (vector?.contentHashCode() ?: 0)
        return result
    }
}

/**
 * 动作匹配结果
 */
data class ExerciseMatchResult(
    val exerciseId: String,
    val exerciseName: String,
    val similarity: Float,
    val matchReason: String,
    val primaryMuscles: List<String>,
    val equipment: String,
    val difficulty: String,
    val metadata: Map<String, Any> = emptyMap(),
)

/**
 * 动作推荐结果
 */
data class ExerciseRecommendation(
    val exerciseId: String,
    val exerciseName: String,
    val recommendationScore: Float,
    val recommendationReason: String,
    val contextualFit: Float, // 与当前训练状态的匹配度
    val progressionLevel: String, // 进阶级别
    val estimatedDuration: Int, // 预估时长（分钟）
    val metadata: Map<String, Any> = emptyMap(),
)

/**
 * 互补类型枚举
 */
enum class ComplementType {
    OPPOSING_MUSCLES, // 对抗肌群
    SAME_MUSCLES, // 相同肌群
    COMPOUND_MOVEMENTS, // 复合动作
    ISOLATION_MOVEMENTS, // 孤立动作
    PROGRESSIVE_OVERLOAD, // 渐进超负荷
}

/**
 * 匹配引擎统计信息
 */
data class MatchingEngineStats(
    val totalExercises: Int,
    val vectorizedExercises: Int,
    val averageMatchTimeMs: Long,
    val cacheHitRate: Float,
    val lastUpdateTime: Long,
)
