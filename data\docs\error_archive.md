> Task :data:compileDebugKotlin FAILED
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/coach/service/AiInteractionServiceImpl.kt:6:44 Unresolved reference 'DomainErrors'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/coach/service/AiInteractionServiceImpl.kt:44:9 Argument type mismatch: actual type is 'kotlin.Any', but 'com.example.gymbro.core.error.types.ModernResult<com.example.gymbro.domain.workout.model.TemplateDraft>' was expected.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/coach/service/AiInteractionServiceImpl.kt:56:20 One type argument expected. Use class 'Success' if you don't intend to pass type arguments.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/coach/service/AiInteractionServiceImpl.kt:57:63 Unresolved reference 'data'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/coach/service/AiInteractionServiceImpl.kt:70:17 Unresolved reference 'DomainErrors'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/coach/service/AiInteractionServiceImpl.kt:101:17 Unresolved reference 'DomainErrors'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/coach/service/AiInteractionServiceImpl.kt:136:17 Unresolved reference 'DomainErrors'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/coach/service/AiInteractionServiceImpl.kt:176:17 Unresolved reference 'DomainErrors'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/coach/service/AiInteractionServiceImpl.kt:217:17 Unresolved reference 'DomainErrors'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/coach/service/AiInteractionServiceImpl.kt:239:17 Unresolved reference 'DomainErrors'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/coach/service/AiInteractionServiceImpl.kt:261:21 Unresolved reference 'healthConditions'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/coach/service/AiInteractionServiceImpl.kt:262:52 Unresolved reference 'healthConditions'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/coach/service/AiInteractionServiceImpl.kt:299:17 Unresolved reference 'DomainErrors'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/coach/service/AiInteractionServiceImpl.kt:336:17 Unresolved reference 'DomainErrors'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/coach/service/AiInteractionServiceImpl.kt:355:26 Unresolved reference 'FitnessLevel'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/coach/service/ChatSummaryServiceImpl.kt:6:44 Unresolved reference 'DomainErrors'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/coach/service/ChatSummaryServiceImpl.kt:60:17 Unresolved reference 'DomainErrors'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/coach/service/ChatSummaryServiceImpl.kt:90:17 Unresolved reference 'DomainErrors'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/coach/service/ChatSummaryServiceImpl.kt:116:43 Overload resolution ambiguity between candidates:
fun <T> Iterable<T>.sumOf(selector: (T) -> BigDecimal): BigDecimal
fun <T> Iterable<T>.sumOf(selector: (T) -> BigInteger): BigInteger
fun <T> Iterable<T>.sumOf(selector: (T) -> Double): Double
fun <T> Iterable<T>.sumOf(selector: (T) -> Int): Int
fun <T> Iterable<T>.sumOf(selector: (T) -> Long): Long
fun <T> Iterable<T>.sumOf(selector: (T) -> UInt): UInt
fun <T> Iterable<T>.sumOf(selector: (T) -> ULong): ULong
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/coach/service/ChatSummaryServiceImpl.kt:116:54 Unresolved reference 'content'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/coach/service/ChatSummaryServiceImpl.kt:117:32 'operator' modifier is required on 'FirNamedFunctionSymbol kotlin/compareTo' in 'compareTo'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/coach/service/ChatSummaryServiceImpl.kt:135:49 Unresolved reference 'role'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/coach/service/ChatSummaryServiceImpl.kt:136:54 Unresolved reference 'role'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/coach/service/ChatSummaryServiceImpl.kt:164:63 Unresolved reference 'content'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/coach/service/ChatSummaryServiceImpl.kt:179:63 Unresolved reference 'content'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/workout/service/WorkoutServiceImpl.kt:6:44 Unresolved reference 'DomainErrors'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/workout/service/WorkoutServiceImpl.kt:74:17 Unresolved reference 'DomainErrors'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/workout/service/WorkoutServiceImpl.kt:142:17 Unresolved reference 'DomainErrors'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/workout/service/WorkoutServiceImpl.kt:169:34 Argument type mismatch: actual type is 'it(kotlin.Number & kotlin.Comparable<CapturedType(*)>)', but 'kotlin.Double' was expected.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/workout/service/WorkoutServiceImpl.kt:170:32 Argument type mismatch: actual type is 'kotlin.Int?', but 'kotlin.Int' was expected.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/workout/service/WorkoutServiceImpl.kt:188:17 Unresolved reference 'DomainErrors'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/workout/service/WorkoutServiceImpl.kt:224:17 Unresolved reference 'DomainErrors'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/workout/service/WorkoutServiceImpl.kt:255:17 Unresolved reference 'DomainErrors'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/workout/service/WorkoutServiceImpl.kt:295:17 Unresolved reference 'DomainErrors'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/workout/service/WorkoutServiceImpl.kt:331:44 Operator call is prohibited on a nullable receiver of type 'kotlin.Int?'. Use '?.'-qualified call instead.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/workout/service/WorkoutServiceImpl.kt:334:51 Operator call is prohibited on a nullable receiver of type 'kotlin.Int?'. Use '?.'-qualified call instead.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/workout/service/WorkoutServiceImpl.kt:348:17 Unresolved reference 'DomainErrors'.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/workout/service/WorkoutServiceImpl.kt:378:80 Operator call is prohibited on a nullable receiver of type 'kotlin.Long?'. Use '?.'-qualified call instead.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/workout/service/WorkoutServiceImpl.kt:378:82 Argument type mismatch: actual type is 'kotlin.Long?', but 'kotlin.Long' was expected.
e: file:///D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/workout/service/WorkoutServiceImpl.kt:389:17 Unresolved reference 'DomainErrors'.

