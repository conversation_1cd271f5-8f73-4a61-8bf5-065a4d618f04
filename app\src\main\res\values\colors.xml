<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 基础颜色 -->
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>

    <!-- 启动图标背景色 -->
    <color name="ic_launcher_background">#FFFFFF</color> <!-- Updated line -->

    <!-- ITP颜色值 -->
    <color name="itp_primary">#FF0E0E0F</color>
    <color name="itp_1">#FF0E0E0F</color>
    <color name="itp_2">#FFCDCED0</color>
    <color name="itp_3">#FF5E5E5F</color>
    <color name="itp_4">#FF838484</color>
    <color name="itp_5">#FF353636</color>
    <color name="itp_6">#FF7C7C84</color>
    <color name="itp_7">#FF44444C</color>
    <color name="itp_8">#FF444C4C</color>
    <color name="itp_9">#FF242C2A</color>
    <color name="itp_10">#FF24242C</color>
    <color name="itp_11">#FFF46C2A</color>

    <!-- 暗色主题颜色 -->
    <color name="md_theme_dark_primary">@color/itp_2</color>
    <color name="md_theme_dark_onPrimary">@color/itp_1</color>
    <color name="md_theme_dark_primaryContainer">@color/itp_5</color>
    <color name="md_theme_dark_onPrimaryContainer">@color/itp_2</color>
    <color name="md_theme_dark_secondary">@color/itp_6</color>
    <color name="md_theme_dark_onSecondary">@color/itp_1</color>
    <color name="md_theme_dark_secondaryContainer">@color/itp_7</color>
    <color name="md_theme_dark_onSecondaryContainer">@color/itp_2</color>
    <color name="md_theme_dark_tertiary">@color/itp_11</color>
    <color name="md_theme_dark_onTertiary">@color/white</color>
    <color name="md_theme_dark_background">@color/itp_1</color>
    <color name="md_theme_dark_onBackground">@color/itp_2</color>
    <color name="md_theme_dark_surface">@color/itp_10</color>
    <color name="md_theme_dark_onSurface">@color/itp_2</color>
    <color name="md_theme_dark_surfaceVariant">@color/itp_7</color>
    <color name="md_theme_dark_onSurfaceVariant">@color/itp_2</color>
    <color name="md_theme_dark_outline">@color/itp_3</color>
    <color name="md_theme_dark_inverseSurface">@color/itp_2</color>
    <color name="md_theme_dark_inverseOnSurface">@color/itp_1</color>
    <color name="md_theme_dark_inversePrimary">@color/itp_1</color>
    <color name="md_theme_dark_outlineVariant">@color/itp_5</color>
    <color name="md_theme_dark_scrim">#99000000</color>
    <color name="md_theme_dark_error">#FFCF6679</color>
    <color name="md_theme_dark_onError">@color/black</color>
    <color name="md_theme_dark_errorContainer">#FFB00020</color>
    <color name="md_theme_dark_onErrorContainer">@color/white</color>
    <color name="md_theme_dark_surfaceTint">@color/itp_11</color>

    <!-- 亮色主题颜色 -->
    <color name="md_theme_light_primary">@color/itp_7</color>
    <color name="md_theme_light_onPrimary">@color/white</color>
    <color name="md_theme_light_primaryContainer">@color/itp_2</color>
    <color name="md_theme_light_onPrimaryContainer">@color/itp_7</color>
    <color name="md_theme_light_secondary">@color/itp_8</color>
    <color name="md_theme_light_onSecondary">@color/white</color>
    <color name="md_theme_light_secondaryContainer">@color/itp_2</color>
    <color name="md_theme_light_onSecondaryContainer">@color/itp_7</color>
    <color name="md_theme_light_tertiary">@color/itp_3</color>
    <color name="md_theme_light_onTertiary">@color/white</color>
    <color name="md_theme_light_background">@color/white</color>
    <color name="md_theme_light_onBackground">@color/itp_1</color>
    <color name="md_theme_light_surface">@color/white</color>
    <color name="md_theme_light_onSurface">@color/itp_1</color>
    <color name="md_theme_light_surfaceVariant">@color/itp_2</color>
    <color name="md_theme_light_onSurfaceVariant">@color/itp_3</color>
    <color name="md_theme_light_outline">@color/itp_4</color>
    <color name="md_theme_light_inverseSurface">@color/itp_7</color>
    <color name="md_theme_light_inverseOnSurface">@color/white</color>
    <color name="md_theme_light_inversePrimary">@color/itp_2</color>
    <color name="md_theme_light_outlineVariant">@color/itp_2</color>
    <color name="md_theme_light_scrim">#1F000000</color>
    <color name="md_theme_light_error">#FFB00020</color>
    <color name="md_theme_light_onError">@color/white</color>
    <color name="md_theme_light_errorContainer">#FFFFDAD6</color>
    <color name="md_theme_light_onErrorContainer">#FF410002</color>
    <color name="md_theme_light_surfaceTint">@color/itp_7</color>
</resources>
