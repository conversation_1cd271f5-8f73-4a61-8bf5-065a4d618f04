# 统一自动保存系统

## 🎯 系统概述

GymBro统一自动保存系统是一个基于Clean Architecture设计的高性能、可扩展的自动保存解决方案。系统充分利用现有的DataStore、Json、Room等基础设施，为Profile和Workout模块提供无缝的自动保存功能。

### 核心特性

- ✅ **多种保存策略**：即时保存、定时保存、防抖保存
- ✅ **多种存储后端**：DataStore缓存、Room数据库、自定义存储
- ✅ **1秒最低间隔限制**：防止过于频繁的保存操作
- ✅ **类型安全**：完整的泛型支持和类型检查
- ✅ **响应式状态**：基于StateFlow的实时状态监控
- ✅ **错误恢复**：完善的错误处理和重试机制
- ✅ **缓存恢复**：支持应用重启后的数据恢复
- ✅ **MVI集成**：与现有MVI架构无缝集成

### 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Profile UI    │    │   Workout UI    │    │   Other UI      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ProfileAdapter   │    │WorkoutAdapter   │    │CustomAdapter    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 ▼
                    ┌─────────────────┐
                    │AutoSaveRepository│
                    └─────────────────┘
                                 │
                                 ▼
                    ┌─────────────────┐
                    │AutoSaveSession  │
                    └─────────────────┘
                                 │
                                 ▼
                    ┌─────────────────┐
                    │AutoSaveManager  │
                    └─────────────────┘
                                 │
                    ┌────────────┴────────────┐
                    ▼                         ▼
            ┌─────────────────┐    ┌─────────────────┐
            │AutoSaveStrategy │    │AutoSaveStorage  │
            └─────────────────┘    └─────────────────┘
```

## 🚀 快速开始

### 1. 依赖注入配置

系统已自动集成到现有的DI架构中，无需额外配置：

```kotlin
// AutoSaveModule已包含在DataModule中
@Module(includes = [AutoSaveModule::class])
class DataModule
```

### 2. Profile模块集成

```kotlin
class ProfileViewModel @Inject constructor(
    private val profileAdapter: ProfileAutoSaveAdapter
) : ViewModel() {

    private var autoSaveSessionId: String? = null

    fun startAutoSave(userId: String) {
        viewModelScope.launch {
            val result = profileAdapter.createAutoSave(userId, viewModelScope)
            when (result) {
                is ModernResult.Success -> {
                    autoSaveSessionId = result.data
                    profileAdapter.startAutoSave(result.data, userId)
                }
                is ModernResult.Error -> {
                    // 处理错误
                }
            }
        }
    }

    fun updateProfile(profile: UserProfile) {
        autoSaveSessionId?.let { sessionId ->
            profileAdapter.updateProfile(sessionId, profile)
        }
    }
}
```

### 3. Workout模块集成

```kotlin
class WorkoutViewModel @Inject constructor(
    private val workoutAdapter: WorkoutAutoSaveAdapter
) : ViewModel() {

    private var autoSaveSessionId: String? = null

    fun startAutoSave(draftId: String) {
        viewModelScope.launch {
            val result = workoutAdapter.createAutoSave(draftId, viewModelScope)
            when (result) {
                is ModernResult.Success -> {
                    autoSaveSessionId = result.data
                    workoutAdapter.startAutoSave(result.data, draftId)
                }
                is ModernResult.Error -> {
                    // 处理错误
                }
            }
        }
    }

    fun updateDraft(draft: TemplateDraft) {
        autoSaveSessionId?.let { sessionId ->
            workoutAdapter.updateDraft(sessionId, draft)
        }
    }
}
```

## 📚 API参考

### AutoSaveRepository

会话管理的核心接口：

```kotlin
interface AutoSaveRepository {
    suspend fun <T : Any> createSession(config: AutoSaveConfig<T>, scope: CoroutineScope): ModernResult<String>
    fun <T : Any> getSession(sessionId: String): AutoSaveSession<T>?
    suspend fun deleteSession(sessionId: String): ModernResult<Unit>
    fun getActiveSessions(): Flow<List<AutoSaveSession.SessionInfo>>
    suspend fun clearAllSessions(): ModernResult<Unit>
}
```

### AutoSaveSession

单个会话的生命周期管理：

```kotlin
class AutoSaveSession<T : Any> {
    val state: StateFlow<AutoSaveState<T>>
    val sessionStatus: StateFlow<SessionStatus>

    fun start(initialData: T)
    fun update(newData: T)
    suspend fun saveNow(): Result<Unit>
    fun pause()
    fun resume()
    fun stop()
    fun restoreFromCache()
    fun discardCache()
}
```

### 适配器API

#### ProfileAutoSaveAdapter

```kotlin
class ProfileAutoSaveAdapter {
    suspend fun createAutoSave(userId: String, scope: CoroutineScope): ModernResult<String>
    suspend fun startAutoSave(sessionId: String, userId: String)
    fun updateProfile(sessionId: String, profile: UserProfile)
    suspend fun saveNow(sessionId: String): Result<Unit>
    suspend fun stopAutoSave(sessionId: String)
}
```

#### WorkoutAutoSaveAdapter

```kotlin
class WorkoutAutoSaveAdapter {
    suspend fun createAutoSave(draftId: String, scope: CoroutineScope): ModernResult<String>
    suspend fun startAutoSave(sessionId: String, draftId: String)
    fun updateDraft(sessionId: String, draft: TemplateDraft)
    suspend fun saveNow(sessionId: String): Result<Unit>
    fun pauseAutoSave(sessionId: String)
    fun resumeAutoSave(sessionId: String)
    suspend fun stopAutoSave(sessionId: String)
    fun restoreFromCache(sessionId: String)
    fun discardCache(sessionId: String)
}
```

## 🔧 配置选项

### 保存策略

- **即时保存**：数据变更后立即保存（Profile模块推荐）
- **定时保存**：按固定间隔保存（Workout模块推荐，默认3秒）
- **防抖保存**：延迟保存，新变更会重新计时（频繁编辑场景）

### 存储后端

- **缓存存储**：基于DataStore的快速缓存
- **数据库存储**：基于Room的持久化存储
- **自定义存储**：实现AutoSaveStorage接口

## 📊 性能特性

- **1秒最低间隔**：防止过于频繁的保存操作
- **协程异步**：所有操作都是非阻塞的
- **内存优化**：智能的状态管理和资源释放
- **线程安全**：使用ConcurrentHashMap和Mutex确保并发安全

## 🔍 监控和调试

### 状态监控

```kotlin
// 监控会话状态
session.state.collect { state ->
    when {
        state.isSaving -> showSavingIndicator()
        state.hasError -> showErrorMessage(state.error)
        state.hasUnsavedChanges -> showUnsavedIndicator()
        else -> hideSavingIndicator()
    }
}

// 监控会话状态
session.sessionStatus.collect { status ->
    when (status) {
        SessionStatus.ACTIVE -> enableAutoSave()
        SessionStatus.PAUSED -> showPausedIndicator()
        SessionStatus.ERROR -> handleError()
        else -> updateUI(status)
    }
}
```

### 日志记录

系统提供详细的日志记录，便于调试：

```
D/ProfileAutoSaveAdapter: 创建Profile自动保存会话: user123
D/AutoSaveRepositoryImpl: 自动保存会话创建成功: session-uuid
D/ImmediateSaveStrategy: 开始即时保存
D/ProfileAutoSaveAdapter: Profile保存成功: user123
```

## 📖 更多文档

- [详细集成指南](INTEGRATION_GUIDE.md) - 完整的集成步骤和最佳实践
- [使用示例](USAGE_EXAMPLES.kt) - 各种场景的代码示例
- [故障排除指南](INTEGRATION_GUIDE.md#故障排除) - 常见问题和解决方案

## 🤝 贡献指南

1. 遵循现有的代码风格和架构模式
2. 添加适当的单元测试
3. 更新相关文档
4. 确保向后兼容性

## 📄 许可证

本项目遵循GymBro项目的许可证协议。

## 📝 更新日志

### v1.0.1 (2024-12-19)
- 🐛 修复USAGE_EXAMPLES.kt中的编译错误
- 🔧 更正UserProfile属性名称（name → displayName, age → weight）
- 🔧 完善when表达式的ModernResult.Loading分支处理
- 📚 更新集成指南中的代码示例

### v1.0.0 (2024-12-19)
- 🎉 统一自动保存系统首次发布
- ✅ 完整的核心架构实现
- ✅ Profile和Workout模块适配器
- ✅ 依赖注入配置
- ✅ 完整的文档体系
