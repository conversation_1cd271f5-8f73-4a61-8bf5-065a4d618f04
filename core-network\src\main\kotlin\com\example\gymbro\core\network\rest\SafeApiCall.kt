package com.example.gymbro.core.network.rest

import com.example.gymbro.core.network.monitor.NetworkMonitor
import com.example.gymbro.core.network.monitor.NetworkState
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import retrofit2.Response
import timber.log.Timber
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException

/**
 * 安全的API调用工具函数
 *
 * 提供统一的错误处理和结果封装
 */

/**
 * 安全的API调用
 * 将可能抛出异常的API调用转换为ApiResult
 */
suspend fun <T> safeApiCall(
    networkMonitor: NetworkMonitor? = null,
    block: suspend () -> Response<T>,
): ApiResult<T> {
    return try {
        // 检查网络状态
        networkMonitor?.let { monitor ->
            when (monitor.networkState.value) {
                is NetworkState.Unavailable, is NetworkState.Lost -> {
                    return ApiResult.error(ApiError.Offline)
                }
                else -> { /* 继续执行 */ }
            }
        }

        val response = block()

        when {
            response.isSuccessful -> {
                val body = response.body()
                if (body != null) {
                    ApiResult.success(body)
                } else {
                    ApiResult.error(ApiError.Parse("Response body is null"))
                }
            }
            else -> {
                val errorMsg = response.message().ifEmpty { "HTTP ${response.code()}" }
                ApiResult.error(ApiError.fromHttpCode(response.code(), errorMsg))
            }
        }
    } catch (e: UnknownHostException) {
        Timber.w(e, "🚨 网络不可达")
        ApiResult.error(ApiError.Offline)
    } catch (e: SocketTimeoutException) {
        Timber.w(e, "🚨 请求超时")
        ApiResult.error(ApiError.Timeout(e.message ?: "Request timeout"))
    } catch (e: IOException) {
        Timber.w(e, "🚨 网络IO异常")
        ApiResult.error(ApiError.Network(e))
    } catch (e: Exception) {
        Timber.e(e, "🚨 API调用异常")
        ApiResult.error(ApiError.Unknown(e))
    }
}

/**
 * 安全的API调用（无响应体）
 * 用于只关心成功/失败的API调用
 */
suspend fun safeApiCallUnit(
    networkMonitor: NetworkMonitor? = null,
    block: suspend () -> Response<Unit>,
): ApiResult<Unit> {
    return safeApiCall(networkMonitor) {
        val response = block()
        if (response.isSuccessful) {
            Response.success(Unit)
        } else {
            response
        }
    }
}

/**
 * 安全的API调用（字符串响应）
 * 用于处理原始字符串响应
 */
suspend fun safeApiCallString(
    networkMonitor: NetworkMonitor? = null,
    block: suspend () -> String,
): ApiResult<String> {
    return try {
        // 检查网络状态
        networkMonitor?.let { monitor ->
            when (monitor.networkState.value) {
                is NetworkState.Unavailable, is NetworkState.Lost -> {
                    return ApiResult.error(ApiError.Offline)
                }
                else -> { /* 继续执行 */ }
            }
        }

        val result = block()
        ApiResult.success(result)
    } catch (e: UnknownHostException) {
        Timber.w(e, "🚨 网络不可达")
        ApiResult.error(ApiError.Offline)
    } catch (e: SocketTimeoutException) {
        Timber.w(e, "🚨 请求超时")
        ApiResult.error(ApiError.Timeout(e.message ?: "Request timeout"))
    } catch (e: IOException) {
        Timber.w(e, "🚨 网络IO异常")
        ApiResult.error(ApiError.Network(e))
    } catch (e: Exception) {
        Timber.e(e, "🚨 API调用异常")
        ApiResult.error(ApiError.Unknown(e))
    }
}

/**
 * 流式安全API调用
 * 将Flow<T>转换为Flow<ApiResult<T>>
 */
fun <T> Flow<T>.safeApiFlow(): Flow<ApiResult<T>> = flow {
    try {
        collect { value ->
            emit(ApiResult.success(value))
        }
    } catch (e: Exception) {
        emit(ApiResult.error(ApiError.fromException(e)))
    }
}

/**
 * 重试包装器
 * 为API调用添加重试逻辑
 */
suspend fun <T> retryApiCall(
    maxRetries: Int = 3,
    delayMs: Long = 1000,
    networkMonitor: NetworkMonitor? = null,
    block: suspend () -> ApiResult<T>,
): ApiResult<T> {
    var lastResult: ApiResult<T>? = null

    for (attempt in 0..maxRetries) {
        if (attempt > 0) {
            Timber.d("🔄 API重试第${attempt}次，延迟${delayMs}ms")
            kotlinx.coroutines.delay(delayMs)
        }

        lastResult = block()

        // 成功则直接返回
        if (lastResult!!.isSuccess) {
            return lastResult!!
        }

        // 检查是否应该重试
        val error = lastResult!!.errorOrNull()
        val shouldRetry = when (error) {
            is ApiError.Server -> true // 服务器错误可重试
            is ApiError.Timeout -> true // 超时可重试
            is ApiError.Network -> true // 网络错误可重试
            is ApiError.Offline -> {
                // 网络离线时检查当前状态
                networkMonitor?.isOnline != false
            }
            else -> false // 其他错误不重试
        }

        if (!shouldRetry) {
            Timber.d("🚫 错误类型不支持重试: ${error?.getDescription()}")
            break
        }
    }

    return lastResult!!
}
