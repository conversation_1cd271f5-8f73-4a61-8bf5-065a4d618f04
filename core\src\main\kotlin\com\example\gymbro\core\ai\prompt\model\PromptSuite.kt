package com.example.gymbro.core.ai.prompt.model

import com.example.gymbro.core.ai.prompt.registry.PromptConfig
import com.example.gymbro.core.ai.prompt.structure.SystemLayer

/**
 * Prompt 套件数据类
 *
 * 基于 promtjson.md 文档要求，封装完整的系统层配置套件
 * 用于 PromptRegistry.getSuite() 方法的返回值
 *
 * @property systemLayer 系统指令层 - 核心 AI 角色和行为定义
 * @property config 完整的 Prompt 配置信息
 * @property capabilities AI 能力列表
 * @property constraints AI 约束条件列表
 */
data class PromptSuite(
    val systemLayer: SystemLayer,
    val config: PromptConfig,
    val capabilities: List<String>,
    val constraints: List<String>,
) {
    /**
     * 获取系统提示词内容
     * 优先使用配置中的原始 systemPrompt，如果不存在则使用 systemLayer.content
     */
    val systemPrompt: String
        get() = config.systemPrompt

    /**
     * 获取配置 ID
     */
    val configId: String
        get() = config.id

    /**
     * 获取显示名称
     */
    val displayName: String
        get() = config.displayName

    /**
     * 检查是否启用思考模式
     */
    val isThinkingEnabled: Boolean
        get() = config.enableThinking ?: false
}
