package com.example.gymbro.data.remote.subscription.dto

import com.google.gson.annotations.SerializedName

/**
 * 订阅计划DTO
 * 定义远程API的订阅计划数据结构
 */
data class SubscriptionPlanDto(
    /**
     * 计划ID
     */
    @SerializedName("id")
    val id: String,
    /**
     * 计划名称
     */
    @SerializedName("name")
    val name: String,
    /**
     * 计划描述
     */
    @SerializedName("description")
    val description: String,
    /**
     * 计划价格
     */
    @SerializedName("price")
    val price: Double,
    /**
     * 货币代码
     */
    @SerializedName("currency")
    val currency: String = "CNY",
    /**
     * 计费周期 (monthly, yearly)
     */
    @SerializedName("interval")
    val interval: String,
    /**
     * 计划类型
     */
    @SerializedName("planType")
    val planType: String? = null,
    /**
     * 包含的功能列表
     */
    @SerializedName("features")
    val features: List<String> = emptyList(),
    /**
     * 使用限制（JSON字符串）
     */
    @SerializedName("usage_limits")
    val usageLimits: String? = null,
    /**
     * 是否激活
     */
    @SerializedName("active")
    val active: Boolean = true,
    /**
     * 是否热门推荐
     */
    @SerializedName("popular")
    val popular: Boolean = false,
    /**
     * 排序顺序
     */
    @SerializedName("sortOrder")
    val sortOrder: Int = 0,
    /**
     * 创建时间（时间戳毫秒）
     */
    @SerializedName("createdAt")
    val createdAt: Long? = null,
    /**
     * 更新时间（时间戳毫秒）
     */
    @SerializedName("updatedAt")
    val updatedAt: Long? = null,
    /**
     * 元数据（JSON字符串）
     */
    @SerializedName("metadata")
    val metadata: String? = null,
)
