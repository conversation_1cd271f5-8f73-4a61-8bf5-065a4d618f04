package com.example.gymbro.core.error.extensions

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.gymbro.core.error.types.*
import com.example.gymbro.core.ui.text.UiText
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * ViewModel特定的Flow扩展函数
 * 解决ViewModel与Flow<ModernResult>交互中的常见类型问题
 */

/**
 * 在ViewModel作用域中安全收集Flow<ModernResult<T>>
 * 自动处理异常，并使用明确类型参数
 *
 * @param onSuccess 处理成功状态回调
 * @param onError 处理错误状态回调
 * @param onLoading 处理加载状态回调
 */
inline fun <reified T> ViewModel.collectSafely(
    flow: Flow<ModernResult<T>>,
    crossinline onSuccess: (T) -> Unit,
    crossinline onError: (ModernDataError) -> Unit = {},
    crossinline onLoading: () -> Unit = {},
) {
    viewModelScope.launch {
        try {
            flow.collectLatest { result ->
                when (result) {
                    is ModernResult.Success -> onSuccess(result.data)
                    is ModernResult.Error -> onError(result.error)
                    is ModernResult.Loading -> onLoading()
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "Error collecting Flow<ModernResult<${T::class.java.simpleName}>>")
            onError(
                e.toModernDataError(
                    operationName = "viewModel.collectSafely",
                    uiMessage = UiText.DynamicString("收集Flow时发生错误"),
                ),
            )
        }
    }
}
