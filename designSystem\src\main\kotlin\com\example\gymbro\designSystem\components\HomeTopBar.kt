package com.example.gymbro.designSystem.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Star
import androidx.compose.material.icons.outlined.StarBorder
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.R
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme

/**
 * 订阅状态枚举
 */
enum class SubscriptionStatus {
    FREE, // 免费用户
    PREMIUM, // 付费用户
    TRIAL, // 试用期用户
}

/**
 * Home页面专用顶部栏
 *
 * 左侧：个人资料入口（头像图标）
 * 中间：GymBro Logo
 * 右侧：订阅入口（星星图标）
 *
 * @param title 中间显示的标题
 * @param onProfileClick 点击个人资料的回调
 * @param onSubscriptionClick 点击订阅的回调
 * @param subscriptionStatus 订阅状态，影响右侧图标样式
 * @param modifier Modifier修饰符
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun homeTopBar(
    title: UiText,
    onProfileClick: () -> Unit,
    onSubscriptionClick: () -> Unit,
    subscriptionStatus: SubscriptionStatus = SubscriptionStatus.FREE,
    modifier: Modifier = Modifier,
) {
    TopAppBar(
        title = {
            // 中间标题区域
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = title.asString(),
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface,
                )
            }
        },
        navigationIcon = {
            // 左侧个人资料入口
            IconButton(onClick = onProfileClick) {
                Icon(
                    imageVector = Icons.Default.Person,
                    contentDescription = UiText.StringResource(
                        R.string.user_avatar_description,
                        emptyList(),
                    ).asString(),
                    tint = MaterialTheme.colorScheme.onSurface,
                )
            }
        },
        actions = {
            // 右侧订阅入口
            IconButton(onClick = onSubscriptionClick) {
                val (icon, iconColor) = when (subscriptionStatus) {
                    SubscriptionStatus.FREE -> {
                        Icons.Outlined.StarBorder to MaterialTheme.colorScheme.onSurfaceVariant
                    }
                    SubscriptionStatus.PREMIUM -> {
                        Icons.Filled.Star to Color(0xFFFFD700) // 金色
                    }
                    SubscriptionStatus.TRIAL -> {
                        Icons.Filled.Star to MaterialTheme.colorScheme.primary
                    }
                }

                Icon(
                    imageVector = icon,
                    contentDescription = UiText.StringResource(
                        R.string.content_description_subscription,
                        emptyList(),
                    ).asString(),
                    tint = iconColor,
                )
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = MaterialTheme.colorScheme.surface,
            titleContentColor = MaterialTheme.colorScheme.onSurface,
            navigationIconContentColor = MaterialTheme.colorScheme.onSurface,
            actionIconContentColor = MaterialTheme.colorScheme.onSurface,
        ),
        modifier = modifier,
    )
}

@GymBroPreview
@Composable
private fun homeTopBarPreview() {
    GymBroTheme {
        homeTopBar(
            title = UiText.DynamicString("GymBro"),
            onProfileClick = { },
            onSubscriptionClick = { },
            subscriptionStatus = SubscriptionStatus.FREE,
        )
    }
}

@GymBroPreview
@Composable
private fun homeTopBarPremiumPreview() {
    GymBroTheme {
        homeTopBar(
            title = UiText.DynamicString("GymBro"),
            onProfileClick = { },
            onSubscriptionClick = { },
            subscriptionStatus = SubscriptionStatus.PREMIUM,
        )
    }
}

// 简单的测试预览，用于验证@GymBroPreview是否正常工作
@GymBroPreview
@Composable
private fun simpleTestPreview() {
    GymBroTheme {
        Text(
            text = "测试预览",
            style = MaterialTheme.typography.headlineSmall,
        )
    }
}
