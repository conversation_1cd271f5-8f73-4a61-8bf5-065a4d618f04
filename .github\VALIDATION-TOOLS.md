# 🛠️ GymBro MVP 验证工具指南

## 📋 概述

为了确保GymBro项目的MVP目标达成，我们提供了多种验证工具，重点关注Domain + Data模块以及核心功能的稳定性。

## 🎯 验证工具分类

### 1. 快速检查工具（日常开发）

#### `scripts/quick-mvp-check.bat`
**用途**: 日常开发中的快速验证  
**时间**: ~2-3分钟  
**检查内容**:
- Domain + Data + Core模块编译
- Coach + Workout模块编译
- 基础代码格式检查

**使用场景**:
- 修改代码后的快速验证
- 提交前的基础检查
- 确认开发环境正常

```batch
# 运行快速检查
scripts\quick-mvp-check.bat
```

### 2. 综合验证工具（完整验证）

#### `scripts/comprehensive-mvp-validation.ps1`
**用途**: 完整的MVP验证流程  
**时间**: ~5-10分钟  
**检查内容**:
- 所有模块编译和测试
- 代码质量检查
- APK构建验证
- 可选ACT验证

**使用场景**:
- PR提交前的完整验证
- 重要功能开发完成后
- 定期质量检查

```powershell
# 完整验证
powershell -ExecutionPolicy Bypass -File scripts\comprehensive-mvp-validation.ps1

# 快速模式
powershell -ExecutionPolicy Bypass -File scripts\comprehensive-mvp-validation.ps1 -Mode quick

# 仅本地验证（跳过ACT）
powershell -ExecutionPolicy Bypass -File scripts\comprehensive-mvp-validation.ps1 -Mode local-only
```

### 3. ACT本地CI/CD验证

#### `scripts/run-act-mvp.bat`
**用途**: 使用ACT工具本地验证GitHub Actions  
**时间**: ~10-15分钟  
**检查内容**:
- 完整CI/CD流程验证
- Docker容器环境测试
- 工作流程语法验证

**使用场景**:
- 修改CI/CD配置后验证
- 确保GitHub Actions正常工作
- 本地模拟云端构建环境

```batch
# 交互式ACT验证
scripts\run-act-mvp.bat
```

#### `scripts/act-mvp-validation.ps1`
**用途**: PowerShell版本的ACT验证  
**功能**: 更灵活的参数配置

```powershell
# 基础ACT验证
powershell -ExecutionPolicy Bypass -File scripts\act-mvp-validation.ps1

# 干运行模式
powershell -ExecutionPolicy Bypass -File scripts\act-mvp-validation.ps1 -DryRun

# 详细日志
powershell -ExecutionPolicy Bypass -File scripts\act-mvp-validation.ps1 -Verbose
```

### 4. 专项验证工具

#### `scripts/domain-data-focus-test.ps1`
**用途**: 专门针对Domain和Data模块的深度验证  
**时间**: ~3-5分钟  
**检查内容**:
- Domain模块业务逻辑验证
- Data模块数据访问验证
- 架构依赖关系检查
- 代码覆盖率分析

**使用场景**:
- 核心业务逻辑开发后
- 数据层重构后
- 架构变更验证

```powershell
# Domain+Data重点验证
powershell -ExecutionPolicy Bypass -File scripts\domain-data-focus-test.ps1

# 跳过测试（仅编译检查）
powershell -ExecutionPolicy Bypass -File scripts\domain-data-focus-test.ps1 -SkipTests

# 包含代码覆盖率
powershell -ExecutionPolicy Bypass -File scripts\domain-data-focus-test.ps1 -Coverage
```

## 📊 验证工具对比

| 工具 | 时间 | 覆盖范围 | 使用场景 | 推荐频率 |
|------|------|----------|----------|----------|
| quick-mvp-check.bat | 2-3分钟 | 基础编译 | 日常开发 | 每次代码修改后 |
| comprehensive-mvp-validation.ps1 | 5-10分钟 | 完整验证 | PR前验证 | 每次功能完成后 |
| run-act-mvp.bat | 10-15分钟 | CI/CD流程 | 配置变更后 | 每次CI配置修改后 |
| domain-data-focus-test.ps1 | 3-5分钟 | 核心模块 | 架构变更后 | 每次核心逻辑修改后 |

## 🔄 推荐工作流程

### 日常开发流程
```
1. 修改代码
   ↓
2. 运行 quick-mvp-check.bat
   ↓
3. 如果通过，继续开发
   如果失败，修复问题后重新检查
```

### 功能完成流程
```
1. 功能开发完成
   ↓
2. 运行 domain-data-focus-test.ps1（如果涉及核心模块）
   ↓
3. 运行 comprehensive-mvp-validation.ps1
   ↓
4. 如果全部通过，提交PR
   如果有问题，修复后重新验证
```

### CI/CD配置变更流程
```
1. 修改GitHub Actions配置
   ↓
2. 运行 run-act-mvp.bat 本地验证
   ↓
3. 如果通过，提交配置变更
   如果失败，修复配置后重新验证
```

## 🎯 MVP验证重点

### 必须通过的验证项
- ✅ Domain模块编译和测试
- ✅ Data模块编译和测试
- ✅ Core模块编译和测试
- ✅ Coach模块编译
- ✅ Workout模块编译
- ✅ Debug APK构建成功

### 建议通过的验证项
- ⚠️ 代码质量检查（Ktlint + Detekt）
- ⚠️ 单元测试覆盖率
- ⚠️ ACT CI/CD验证

### 暂时忽略的验证项（MVP阶段）
- ❌ Firebase服务集成
- ❌ Google Play发布
- ❌ 端到端测试
- ❌ 性能基准测试

## 🔧 环境要求

### 必需工具
- **Java 17+**: Gradle构建需要
- **Android SDK**: Android开发环境
- **Git**: 版本控制

### ACT验证额外要求
- **Docker Desktop**: ACT需要Docker环境
- **ACT工具**: `D:\GymBro\act_Windows_x86_64\act.exe`

### PowerShell脚本要求
- **PowerShell 5.0+**: Windows内置
- **执行策略**: 可能需要设置 `Set-ExecutionPolicy RemoteSigned`

## 📋 故障排除

### 常见问题

#### 1. Gradle构建失败
```bash
# 清理构建缓存
./gradlew clean

# 刷新依赖
./gradlew --refresh-dependencies
```

#### 2. ACT验证失败
```bash
# 检查Docker状态
docker info

# 清理Docker资源
docker system prune -f
```

#### 3. PowerShell执行策略问题
```powershell
# 设置执行策略
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### 4. 模块编译失败
- 检查代码语法错误
- 确认依赖关系正确
- 查看详细错误日志

## 📞 支持

如有问题，请：
1. 查看本文档的故障排除部分
2. 检查相关工具的官方文档
3. 在项目中创建Issue描述具体问题

---

**维护团队**: DevOps团队  
**最后更新**: 2025-01-28  
**版本**: MVP v1.0
