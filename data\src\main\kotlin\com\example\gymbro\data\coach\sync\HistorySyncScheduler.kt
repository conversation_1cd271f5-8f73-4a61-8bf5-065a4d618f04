package com.example.gymbro.data.coach.sync

import androidx.work.*
import com.example.gymbro.data.coach.worker.HistorySyncWorker
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 历史同步调度器
 * 
 * 根据706任务保存.md规格实现的WorkManager调度管理：
 * - 管理定期同步任务的生命周期
 * - 提供手动触发同步的接口
 * - 监控同步任务状态
 * - 处理同步策略和重试逻辑
 * 
 * 核心职责：
 * - 启动/停止定期同步任务
 * - 触发即时同步
 * - 监控同步状态和结果
 * - 提供同步统计信息
 */
@Singleton
class HistorySyncScheduler @Inject constructor(
    private val workManager: WorkManager
) {

    companion object {
        private const val TAG = "HistorySyncScheduler"
        private const val PERIODIC_WORK_NAME = "periodic_history_sync"
        private const val IMMEDIATE_WORK_NAME = "immediate_history_sync"
        
        // 默认配置
        private const val DEFAULT_SYNC_INTERVAL_HOURS = 6L
        private const val DEFAULT_BATCH_SIZE = 50
    }

    /**
     * 启动定期同步任务
     * 
     * @param intervalHours 同步间隔（小时）
     * @param batchSize 批量大小
     * @param replace 是否替换现有任务
     */
    fun startPeriodicSync(
        intervalHours: Long = DEFAULT_SYNC_INTERVAL_HOURS,
        batchSize: Int = DEFAULT_BATCH_SIZE,
        replace: Boolean = true
    ) {
        try {
            Timber.tag(TAG).i("🔄 启动定期同步任务: interval=${intervalHours}h, batch=$batchSize")

            val periodicWork = HistorySyncWorker.createPeriodicWork(
                intervalHours = intervalHours,
                batchSize = batchSize
            )

            val existingWorkPolicy = if (replace) {
                ExistingPeriodicWorkPolicy.REPLACE
            } else {
                ExistingPeriodicWorkPolicy.KEEP
            }

            workManager.enqueueUniquePeriodicWork(
                PERIODIC_WORK_NAME,
                existingWorkPolicy,
                periodicWork
            )

            Timber.tag(TAG).d("✅ 定期同步任务已启动")

        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "❌ 启动定期同步任务失败")
        }
    }

    /**
     * 停止定期同步任务
     */
    fun stopPeriodicSync() {
        try {
            Timber.tag(TAG).i("🛑 停止定期同步任务")
            
            workManager.cancelUniqueWork(PERIODIC_WORK_NAME)
            
            Timber.tag(TAG).d("✅ 定期同步任务已停止")

        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "❌ 停止定期同步任务失败")
        }
    }

    /**
     * 触发即时同步
     * 
     * @param batchSize 批量大小
     * @param forceSync 是否强制同步（忽略网络约束）
     * @return 工作请求ID
     */
    fun triggerImmediateSync(
        batchSize: Int = DEFAULT_BATCH_SIZE,
        forceSync: Boolean = false
    ): String? {
        return try {
            Timber.tag(TAG).i("⚡ 触发即时同步: batch=$batchSize, force=$forceSync")

            val immediateWork = HistorySyncWorker.createOneTimeWork(
                batchSize = batchSize,
                forceSync = forceSync
            )

            workManager.enqueueUniqueWork(
                IMMEDIATE_WORK_NAME,
                ExistingWorkPolicy.REPLACE,
                immediateWork
            )

            val workId = immediateWork.id.toString()
            Timber.tag(TAG).d("✅ 即时同步任务已提交: workId=$workId")
            
            workId

        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "❌ 触发即时同步失败")
            null
        }
    }

    /**
     * 获取定期同步任务状态
     */
    fun getPeriodicSyncStatus(): androidx.lifecycle.LiveData<List<WorkInfo>> {
        return workManager.getWorkInfosForUniqueWorkLiveData(PERIODIC_WORK_NAME)
    }

    /**
     * 获取即时同步任务状态
     */
    fun getImmediateSyncStatus(): androidx.lifecycle.LiveData<List<WorkInfo>> {
        return workManager.getWorkInfosForUniqueWorkLiveData(IMMEDIATE_WORK_NAME)
    }

    /**
     * 获取所有历史同步任务状态
     */
    fun getAllSyncStatus(): androidx.lifecycle.LiveData<List<WorkInfo>> {
        return workManager.getWorkInfosByTagLiveData(HistorySyncWorker::class.java.simpleName)
    }

    /**
     * 取消所有同步任务
     */
    fun cancelAllSync() {
        try {
            Timber.tag(TAG).i("🛑 取消所有同步任务")
            
            workManager.cancelUniqueWork(PERIODIC_WORK_NAME)
            workManager.cancelUniqueWork(IMMEDIATE_WORK_NAME)
            
            Timber.tag(TAG).d("✅ 所有同步任务已取消")

        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "❌ 取消同步任务失败")
        }
    }

    /**
     * 检查是否有正在运行的同步任务
     */
    suspend fun hasRunningSyncTasks(): Boolean {
        return try {
            val periodicWorkInfos = workManager.getWorkInfosForUniqueWork(PERIODIC_WORK_NAME).get()
            val immediateWorkInfos = workManager.getWorkInfosForUniqueWork(IMMEDIATE_WORK_NAME).get()
            
            val hasRunningPeriodic = periodicWorkInfos.any { 
                it.state == WorkInfo.State.RUNNING || it.state == WorkInfo.State.ENQUEUED 
            }
            val hasRunningImmediate = immediateWorkInfos.any { 
                it.state == WorkInfo.State.RUNNING || it.state == WorkInfo.State.ENQUEUED 
            }
            
            hasRunningPeriodic || hasRunningImmediate

        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "❌ 检查同步任务状态失败")
            false
        }
    }

    /**
     * 获取同步统计信息
     */
    suspend fun getSyncStatistics(): SyncStatistics {
        return try {
            val periodicWorkInfos = workManager.getWorkInfosForUniqueWork(PERIODIC_WORK_NAME).get()
            val immediateWorkInfos = workManager.getWorkInfosForUniqueWork(IMMEDIATE_WORK_NAME).get()
            
            val allWorkInfos = periodicWorkInfos + immediateWorkInfos
            
            val totalRuns = allWorkInfos.size
            val successfulRuns = allWorkInfos.count { it.state == WorkInfo.State.SUCCEEDED }
            val failedRuns = allWorkInfos.count { it.state == WorkInfo.State.FAILED }
            val runningTasks = allWorkInfos.count { 
                it.state == WorkInfo.State.RUNNING || it.state == WorkInfo.State.ENQUEUED 
            }
            
            // 获取最后一次成功同步的时间
            val lastSuccessTime = allWorkInfos
                .filter { it.state == WorkInfo.State.SUCCEEDED }
                .mapNotNull { it.outputData.getLong("sync_time", 0L).takeIf { time -> time > 0 } }
                .maxOrNull()
            
            SyncStatistics(
                totalRuns = totalRuns,
                successfulRuns = successfulRuns,
                failedRuns = failedRuns,
                runningTasks = runningTasks,
                lastSuccessTime = lastSuccessTime
            )

        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "❌ 获取同步统计失败")
            SyncStatistics()
        }
    }

    /**
     * 重置同步调度器
     * 清除所有任务并重新启动定期同步
     */
    fun reset() {
        try {
            Timber.tag(TAG).i("🔄 重置同步调度器")
            
            cancelAllSync()
            
            // 等待一段时间确保任务被取消
            Thread.sleep(1000)
            
            startPeriodicSync()
            
            Timber.tag(TAG).d("✅ 同步调度器重置完成")

        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "❌ 重置同步调度器失败")
        }
    }

    /**
     * 同步统计信息数据类
     */
    data class SyncStatistics(
        val totalRuns: Int = 0,
        val successfulRuns: Int = 0,
        val failedRuns: Int = 0,
        val runningTasks: Int = 0,
        val lastSuccessTime: Long? = null
    ) {
        val successRate: Float
            get() = if (totalRuns > 0) successfulRuns.toFloat() / totalRuns else 0f
            
        val isHealthy: Boolean
            get() = successRate >= 0.8f && runningTasks < 3
    }
}
