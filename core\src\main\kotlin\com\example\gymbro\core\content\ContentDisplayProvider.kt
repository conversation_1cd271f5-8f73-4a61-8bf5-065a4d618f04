package com.example.gymbro.core.content

/**
 * 内容展示提供者接口
 *
 * 基于用户地区提供统一的内容展示配置，避免各模块重复进行地区判断。
 * 所有模块应通过此接口获取应该展示的内容，而不是直接调用RegionProvider。
 */
interface ContentDisplayProvider {

    /**
     * 获取价格展示配置
     */
    fun getPriceConfig(): PriceDisplayConfig

    /**
     * 获取支付方式配置
     * 返回简单的字符串列表，避免对domain模块的依赖
     */
    fun getPaymentMethods(): List<String>

    /**
     * 获取语言展示配置
     */
    fun getLanguageConfig(): LanguageDisplayConfig

    /**
     * 获取AI教练配置
     */
    fun getCoachConfig(): CoachDisplayConfig

    /**
     * 获取应用通用展示配置
     */
    fun getAppDisplayConfig(): AppDisplayConfig
}

/**
 * 价格展示配置
 */
data class PriceDisplayConfig(
    val currency: String, // 货币代码: "CNY" | "USD"
    val currencySymbol: String, // 货币符号: "¥" | "$"
    val monthlyPrice: Double, // 月度价格
    val yearlyPrice: Double, // 年度价格
    val regionCode: String, // 地区代码: "CN" | "INTERNATIONAL"
)

/**
 * 语言展示配置
 */
data class LanguageDisplayConfig(
    val defaultLanguage: String, // 默认语言: "zh" | "en"
    val availableLanguages: List<String>, // 可用语言列表
    val locale: String, // 区域设置: "zh_CN" | "en_US"
)

/**
 * AI教练展示配置
 */
data class CoachDisplayConfig(
    val defaultLanguage: String, // AI对话默认语言
    val supportedFeatures: List<String>, // 支持的功能列表
    val welcomeMessageKey: String, // 欢迎消息资源key
)

/**
 * 应用通用展示配置
 */
data class AppDisplayConfig(
    val regionCode: String, // 地区代码
    val regionName: String, // 地区显示名称
    val defaultCurrency: String, // 默认货币
    val defaultLanguage: String, // 默认语言
    val isChineseRegion: Boolean, // 是否为中国区域
)
