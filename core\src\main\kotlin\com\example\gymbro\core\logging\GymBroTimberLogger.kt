package com.example.gymbro.core.logging

import android.util.Log
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * GymBro Timber日志系统
 *
 * 统一管理Timber日志配置和初始化，提供标准化的日志标签和使用指南。
 * 整合了ReleaseTree和TimberLogger的功能，简化日志管理。
 */
@Singleton
class GymBroTimberLogger
@Inject
constructor() {
    /**
     * 初始化Timber日志系统
     *
     * @param isDebug 是否为调试模式
     */
    fun initialize(isDebug: Boolean) {
        // 先清除所有已有的Tree
        Timber.uprootAll()

        // 安装适合当前环境的Tree
        if (isDebug) {
            Timber.plant(Timber.DebugTree())
        } else {
            Timber.plant(GymBroReleaseTree())
        }
    }

    companion object {
        // 日志标签常量
        object Tags {
            const val NETWORK = "Network"
            const val DATABASE = "Database"
            const val UI = "UI"
            const val AUTH = "Auth"
            const val SYNC = "Sync"
            const val WORKOUT = "Workout"
            const val NUTRITION = "Nutrition"
            const val PROFILE = "Profile"
        }

        /**
         * 日志使用规范：
         *
         * 1. 使用标签区分不同模块的日志
         *    例如：Timber.tag(Tags.NETWORK).d("API request completed")
         *
         * 2. 使用格式化字符串，避免字符串拼接
         *    正确：Timber.d("User %s logged in", username)
         *    错误：Timber.d("User " + username + " logged in")
         *
         * 3. 异常日志：异常对象作为第一个参数
         *    正确：Timber.e(exception, "Failed to load data")
         *    错误：Timber.e("Failed to load data: " + exception)
         *
         * 4. 日志级别选择：
         *    - Timber.v(): 详细日志，仅用于开发阶段（生产环境不输出）
         *    - Timber.d(): 调试信息，用于开发调试（生产环境不输出）
         *    - Timber.i(): 重要信息，如功能完成、状态变更等
         *    - Timber.w(): 警告信息，出现错误但不影响功能
         *    - Timber.e(): 错误信息，影响功能的严重错误
         *    - Timber.wtf(): 致命错误，不应该发生的严重错误
         *
         * 5. 安全注意事项：
         *    - 不要记录敏感信息（密码、令牌等）
         *    - 避免在循环中过度记录日志
         *    - 在重要状态变更处添加日志
         *    - 为复杂操作添加开始/结束日志
         */
    }
}

/**
 * 生产环境使用的Timber Tree实现
 *
 * 特点：
 * 1. 只记录警告和错误级别的日志
 * 2. 过滤敏感信息
 * 3. 支持错误上报集成
 */
class GymBroReleaseTree : Timber.Tree() {
    override fun isLoggable(
        tag: String?,
        priority: Int,
    ): Boolean {
        // 只允许警告和错误级别的日志
        return priority >= Log.WARN
    }

    override fun log(
        priority: Int,
        tag: String?,
        message: String,
        t: Throwable?,
    ) {
        if (!isLoggable(tag, priority)) {
            return
        }

        // 过滤敏感信息
        val sanitizedMessage = sanitizeMessage(message)
        val finalTag = tag ?: "GymBro"

        // 根据优先级处理日志
        when (priority) {
            Log.ERROR -> {
                Timber.tag(finalTag).e(t, sanitizedMessage)
                reportError(finalTag, sanitizedMessage, t)
            }
            Log.ASSERT -> {
                Log.wtf(finalTag, sanitizedMessage, t)
                reportFatalError(finalTag, sanitizedMessage, t)
            }
            else -> {
                Log.w(finalTag, sanitizedMessage, t)
            }
        }
    }

    /**
     * 过滤敏感信息
     */
    private fun sanitizeMessage(message: String): String = SensitiveDataFilter.filterSensitiveData(message)

    /**
     * 上报错误信息
     *
     * 可由应用层实现实际上报逻辑
     */
    @Suppress("UNUSED_PARAMETER")
    private fun reportError(
        tag: String,
        message: String,
        throwable: Throwable?,
    ) {
        // 应用层可扩展，例如集成Firebase Crashlytics
        // TODO: 实现错误上报逻辑
    }

    /**
     * 上报致命错误
     *
     * 可由应用层实现实际上报逻辑
     */
    @Suppress("UNUSED_PARAMETER")
    private fun reportFatalError(
        tag: String,
        message: String,
        throwable: Throwable?,
    ) {
        // 应用层可扩展，例如集成Firebase Crashlytics并标记为非恢复性错误
        // TODO: 实现致命错误上报逻辑
    }
}
