package com.example.gymbro.data.local.datastore

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.*
import com.example.gymbro.domain.profile.model.user.settings.UserSettings
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.serialization.json.Json
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 用户偏好数据存储
 * 封装对DataStore的访问，提供类型安全的API
 */
@Singleton
class UserPreferencesDataStore
@Inject
constructor(
    private val dataStore: DataStore<Preferences>,
) {
    companion object {
        // 通用键前缀
        private const val USAGE_COUNT_PREFIX = "usage_count_"

        private val USER_SETTINGS_KEY = stringPreferencesKey("user_settings")
        private val TRAINING_DAYS_KEY = stringSetPreferencesKey("workout_days")
    }

    /**
     * 获取特定类型的使用计数
     * @param usageType 使用类型名称
     * @return 使用计数，如果不存在则为null
     */
    fun getUsageCount(usageType: String): Flow<Int?> {
        val key = intPreferencesKey("$USAGE_COUNT_PREFIX$usageType")
        return dataStore.data.map { preferences ->
            val count = preferences[key]
            Timber.d("获取使用计数 %s: %d", usageType, count ?: 0)
            count
        }
    }

    /**
     * 获取用户设置
     * @return 用户设置的Flow，如果不存在则为null
     */
    val userPreferencesFlow: Flow<UserSettings?> =
        dataStore.data
            .map { preferences ->
                preferences[USER_SETTINGS_KEY]?.let { jsonString ->
                    try {
                        Json.decodeFromString<UserSettings>(jsonString)
                    } catch (e: Exception) {
                        Timber.e(e, "Error decoding UserSettings from DataStore")
                        null
                    }
                }
            }

    /**
     * 更新用户设置
     * @param settings 要保存的用户设置
     */
    suspend fun updateUserSettings(settings: UserSettings) {
        dataStore.edit { preferences ->
            preferences[USER_SETTINGS_KEY] = Json.encodeToString(settings)
            Timber.d("用户设置已更新")
        }
    }

    /**
     * 获取训练日设置
     * @return 训练日列表，1-7分别表示周一到周日
     */
    suspend fun getWorkoutDays(): List<Int> =
        dataStore.data
            .map { preferences ->
                preferences[TRAINING_DAYS_KEY]?.mapNotNull { it.toIntOrNull() } ?: emptyList()
            }.first()

    /**
     * 保存训练日设置
     * @param workoutDays 训练日列表，1-7分别表示周一到周日
     */
    suspend fun saveWorkoutDays(workoutDays: List<Int>) {
        dataStore.edit { preferences ->
            preferences[TRAINING_DAYS_KEY] = workoutDays.map { it.toString() }.toSet()
            Timber.d("训练日设置已保存: %s", workoutDays)
        }
    }
}
