package com.example.gymbro.core.error.types

/**
 * 错误上下文数据类
 *
 * 包含评估错误严重性所需的上下文信息
 */
data class ErrorContext(
    // 用户是否已登录
    val isUserLoggedIn: Boolean = true,
    // 用户是否处于离线状态
    val isOffline: Boolean = false,
    // 是否是关键操作
    val isCriticalOperation: Boolean = false,
    // 是否影响核心功能
    val affectsCoreFeature: Boolean = false,
    // 错误发生次数
    val errorOccurrenceCount: Int = 1,
    // 用户订阅状态
    val isUserSubscribed: Boolean = false,
    // 应用当前状态
    val appState: AppState = AppState.FOREGROUND,
    // 自定义上下文参数
    val customParams: Map<String, Any> = emptyMap(),
) {
    companion object {
        /**
         * 创建默认的错误上下文
         */
        fun default(): ErrorContext = ErrorContext()

        /**
         * 创建错误上下文构建器
         */
        fun builder(): Builder = Builder()
    }

    /**
     * 错误上下文构建器
     */
    class Builder {
        private var isUserLoggedIn: Boolean = true
        private var isOffline: Boolean = false
        private var isCriticalOperation: Boolean = false
        private var affectsCoreFeature: Boolean = false
        private var errorOccurrenceCount: Int = 1
        private var isUserSubscribed: Boolean = false
        private var appState: AppState = AppState.FOREGROUND
        private val customParams = mutableMapOf<String, Any>()

        fun build(): ErrorContext =
            ErrorContext(
                isUserLoggedIn = isUserLoggedIn,
                isOffline = isOffline,
                isCriticalOperation = isCriticalOperation,
                affectsCoreFeature = affectsCoreFeature,
                errorOccurrenceCount = errorOccurrenceCount,
                isUserSubscribed = isUserSubscribed,
                appState = appState,
                customParams = customParams.toMap(),
            )
    }
}

/**
 * 应用状态枚举
 */
enum class AppState {
    FOREGROUND, // 应用在前台运行
}

/**
 * 错误严重性枚举
 */
enum class ErrorSeverity {
    INFO, // 信息级别错误
    WARNING, // 警告级别错误
    ERROR, // 普通错误级别
    CRITICAL, // 严重错误级别
}
