{"dashboard": {"id": null, "title": "GymBro AI State Machine Monitoring", "description": "监控AI流式响应状态机的健康状态和性能指标", "tags": ["gymbro", "ai", "state-machine", "monitoring"], "timezone": "browser", "panels": [{"id": 1, "title": "AI消息数量监控 🎯", "type": "stat", "gridPos": {"h": 6, "w": 6, "x": 0, "y": 0}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "textMode": "auto", "colorMode": "background", "graphMode": "area", "justifyMode": "auto"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1.1}]}, "unit": "short", "min": 0, "max": 3}}, "targets": [{"expr": "ai_message_count", "legendFormat": "AI消息数量", "refId": "A"}], "alert": {"name": "AI消息数量异常", "message": "检测到多条AI消息，状态机异常", "frequency": "10s", "conditions": [{"query": {"params": ["A", "5m", "now"]}, "reducer": {"params": [], "type": "last"}, "evaluator": {"params": [1], "type": "gt"}}]}}, {"id": 2, "title": "状态转换时序图 📊", "type": "timeseries", "gridPos": {"h": 6, "w": 12, "x": 6, "y": 0}, "options": {"tooltip": {"mode": "multi"}, "legend": {"displayMode": "table", "placement": "bottom"}, "displayMode": "stacked"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "targets": [{"expr": "rate(ai_state_transition_total[5m])", "legendFormat": "{{state}} 转换速率", "refId": "A"}]}, {"id": 3, "title": "重试次数统计 🔄", "type": "barchart", "gridPos": {"h": 6, "w": 6, "x": 18, "y": 0}, "options": {"orientation": "horizontal", "barWidth": 0.97, "groupWidth": 0.7, "showValue": "auto"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 3}, {"color": "red", "value": 5}]}}}, "targets": [{"expr": "increase(retry_attempt_count[1h])", "legendFormat": "重试次数", "refId": "A"}]}, {"id": 4, "title": "Thinking状态持续时间 ⏱️", "type": "histogram", "gridPos": {"h": 6, "w": 12, "x": 0, "y": 6}, "options": {"bucketSize": 5}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "s"}}, "targets": [{"expr": "histogram_quantile(0.95, thinking_state_duration_seconds_bucket)", "legendFormat": "P95 Thinking时长", "refId": "A"}, {"expr": "histogram_quantile(0.50, thinking_state_duration_seconds_bucket)", "legendFormat": "P50 Thinking时长", "refId": "B"}], "alert": {"name": "Thinking状态过长", "message": "thinking状态持续时间超过30秒", "frequency": "30s", "conditions": [{"query": {"params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "last"}, "evaluator": {"params": [30], "type": "gt"}}]}}, {"id": 5, "title": "状态机错误率 ❌", "type": "timeseries", "gridPos": {"h": 6, "w": 12, "x": 12, "y": 6}, "options": {"tooltip": {"mode": "multi"}, "legend": {"displayMode": "table", "placement": "bottom"}}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "percentunit", "min": 0, "max": 1}}, "targets": [{"expr": "rate(state_transition_error_total[5m]) / rate(ai_state_transition_total[5m])", "legendFormat": "状态转换错误率", "refId": "A"}, {"expr": "rate(ui_state_inconsistency_total[5m]) / rate(ai_message_update_total[5m])", "legendFormat": "UI状态不一致率", "refId": "B"}]}, {"id": 6, "title": "网络请求成功率 🌐", "type": "gauge", "gridPos": {"h": 6, "w": 6, "x": 0, "y": 12}, "options": {"orientation": "auto", "showThresholdLabels": false, "showThresholdMarkers": true}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 0.8}, {"color": "green", "value": 0.95}]}, "unit": "percentunit", "min": 0, "max": 1}}, "targets": [{"expr": "rate(ai_request_success_total[5m]) / rate(ai_request_total[5m])", "legendFormat": "请求成功率", "refId": "A"}]}, {"id": 7, "title": "流式响应延迟分布 📈", "type": "timeseries", "gridPos": {"h": 6, "w": 9, "x": 6, "y": 12}, "options": {"tooltip": {"mode": "multi"}, "legend": {"displayMode": "table", "placement": "bottom"}}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "ms"}}, "targets": [{"expr": "histogram_quantile(0.99, streaming_response_duration_ms_bucket)", "legendFormat": "P99", "refId": "A"}, {"expr": "histogram_quantile(0.95, streaming_response_duration_ms_bucket)", "legendFormat": "P95", "refId": "B"}, {"expr": "histogram_quantile(0.50, streaming_response_duration_ms_bucket)", "legendFormat": "P50", "refId": "C"}]}, {"id": 8, "title": "混沌测试指标 🎭", "type": "table", "gridPos": {"h": 6, "w": 9, "x": 15, "y": 12}, "options": {"showHeader": true}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.1}, {"color": "red", "value": 0.2}]}, "unit": "percentunit"}}, "targets": [{"expr": "chaos_test_failure_rate", "legendFormat": "混沌测试失败率", "refId": "A", "format": "table"}, {"expr": "chaos_network_timeout_rate", "legendFormat": "网络超时率", "refId": "B", "format": "table"}, {"expr": "chaos_data_corruption_rate", "legendFormat": "数据损坏率", "refId": "C", "format": "table"}]}, {"id": 9, "title": "用户体验指标 👤", "type": "stat", "gridPos": {"h": 4, "w": 12, "x": 0, "y": 18}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"]}, "orientation": "horizontal", "textMode": "auto", "colorMode": "background"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 0.8}, {"color": "green", "value": 0.95}]}, "unit": "percentunit"}}, "targets": [{"expr": "user_satisfaction_score", "legendFormat": "用户满意度", "refId": "A"}, {"expr": "conversation_completion_rate", "legendFormat": "对话完成率", "refId": "B"}, {"expr": "ai_response_relevance_score", "legendFormat": "AI回复相关性", "refId": "C"}]}, {"id": 10, "title": "系统资源使用 💻", "type": "timeseries", "gridPos": {"h": 4, "w": 12, "x": 12, "y": 18}, "options": {"tooltip": {"mode": "multi"}, "legend": {"displayMode": "table", "placement": "bottom"}}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "percent"}}, "targets": [{"expr": "cpu_usage_percent", "legendFormat": "CPU使用率", "refId": "A"}, {"expr": "memory_usage_percent", "legendFormat": "内存使用率", "refId": "B"}, {"expr": "network_usage_mbps", "legendFormat": "网络使用(Mbps)", "refId": "C"}]}], "time": {"from": "now-1h", "to": "now"}, "refresh": "5s", "schemaVersion": 27, "version": 1, "links": [{"title": "AI Coach代码仓库", "url": "https://github.com/your-org/gymbro", "type": "link"}, {"title": "状态机文档", "url": "/docs/state_machine.md", "type": "link"}, {"title": "混沌测试结果", "url": "/grafana/d/chaos-tests/chaos-engineering-results", "type": "link"}], "annotations": {"list": [{"name": "部署事件", "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": false, "iconColor": "green", "query": "deployment"}, {"name": "状态机异常", "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": false, "iconColor": "red", "query": "state_machine_error"}]}}}