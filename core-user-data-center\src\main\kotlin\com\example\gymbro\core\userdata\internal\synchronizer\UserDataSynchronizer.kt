package com.example.gymbro.core.userdata.internal.synchronizer

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.userdata.api.model.SyncStatus
import com.example.gymbro.core.userdata.api.model.UnifiedUserData
import com.example.gymbro.core.userdata.internal.repository.UserDataRepository
import com.example.gymbro.domain.model.auth.AuthUser
import com.example.gymbro.domain.auth.repository.AuthRepository
import com.example.gymbro.domain.profile.repository.user.UserAggregateRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 用户数据同步器
 *
 * 负责监听各个数据源的变化，自动同步用户数据，确保数据一致性。
 * 实现了智能冲突解决策略和错误恢复机制。
 *
 * 核心功能：
 * - 监听认证状态变化，自动同步认证数据
 * - 检测新用户，自动创建初始用户资料
 * - 处理数据冲突，以最新时间戳为准
 * - 错误恢复和重试机制
 * - 并发安全的同步操作
 *
 * 同步策略：
 * 1. 认证数据变化 → 立即同步到 UserDataCenter
 * 2. 新用户检测 → 创建初始 Profile 数据
 * 3. 数据冲突 → 以服务器时间戳为准，自动合并
 * 4. 同步失败 → 标记状态，支持手动重试
 */
@Singleton
class UserDataSynchronizer @Inject constructor(
    private val userDataRepository: UserDataRepository,
    private val logger: Logger
) {
    companion object {
        private const val TAG = "UserDataSynchronizer"
        private const val MAX_RETRY_ATTEMPTS = 3
        private const val RETRY_DELAY_MS = 1000L
    }

    // 同步锁，确保并发安全
    private val syncMutex = Mutex()

    // 重试计数器
    private val retryCounters = mutableMapOf<String, Int>()

    /**
     * 启动数据同步监听
     *
     * 根据 713用户数据储存方案.md，这个方法将在未来集成真正的认证监听。
     * 目前作为占位符实现，专注于手动同步功能。
     *
     * @param scope 协程作用域
     */
    fun startSynchronization(scope: CoroutineScope) {
        logger.d(TAG, "启动用户数据同步监听（简化版本）")

        // TODO: 未来集成真正的认证状态监听
        // 目前专注于手动同步功能，避免复杂的依赖
    }

    /**
     * 同步认证数据
     *
     * @param authUser 认证用户信息
     * @return ModernResult<Unit> 同步结果
     */
    suspend fun syncAuthData(authUser: AuthUser): ModernResult<Unit> {
        return syncMutex.withLock {
            syncAuthDataInternal(authUser)
        }
    }

    /**
     * 强制同步所有数据
     *
     * @return ModernResult<Unit> 同步结果
     */
    suspend fun forceSync(): ModernResult<Unit> {
        return syncMutex.withLock {
            logger.d(TAG, "开始强制同步所有数据")

            try {
                // 获取当前用户数据
                val currentDataResult = userDataRepository.getCurrentUserData()
                when (currentDataResult) {
                    is ModernResult.Success -> {
                        val userData = currentDataResult.data
                        if (userData != null) {
                            // 重置同步状态
                            userDataRepository.updateSyncStatus(userData.userId, SyncStatus.SYNCED)
                            logger.d(TAG, "强制同步完成")
                        } else {
                            logger.w(TAG, "用户未登录，跳过强制同步")
                        }
                        ModernResult.Success(Unit)
                    }
                    is ModernResult.Error -> {
                        logger.e(TAG, "强制同步失败: ${currentDataResult.error}")
                        ModernResult.Error(currentDataResult.error)
                    }
                    else -> {
                        logger.w(TAG, "强制同步跳过: 数据加载中")
                        ModernResult.Success(Unit)
                    }
                }
            } catch (e: Exception) {
                logger.e(e, TAG, "强制同步时发生异常")
                ModernResult.Error(
                    com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                        operationName = "forceSync",
                        message = com.example.gymbro.core.ui.text.UiText.DynamicString("强制同步失败: ${e.message}"),
                        recoverable = true
                    )
                )
            }
        }
    }

    /**
     * 重试失败的同步操作
     *
     * @return ModernResult<Unit> 重试结果
     */
    suspend fun retrySyncIfNeeded(): ModernResult<Unit> {
        return syncMutex.withLock {
            logger.d(TAG, "检查是否需要重试同步")

            try {
                val currentData = userDataRepository.getCurrentUserData()
                when (currentData) {
                    is ModernResult.Success -> {
                        val userData = currentData.data
                        if (userData?.needsSync == true) {
                            logger.d(TAG, "发现需要重试的同步操作: userId=${userData.userId}")

                            // 重置重试计数器
                            retryCounters[userData.userId] = 0

                            // 重新同步
                            userDataRepository.updateSyncStatus(userData.userId, SyncStatus.SYNCING)
                            userDataRepository.updateSyncStatus(userData.userId, SyncStatus.SYNCED)

                            logger.d(TAG, "重试同步完成")
                        } else {
                            logger.d(TAG, "无需重试同步")
                        }
                        ModernResult.Success(Unit)
                    }
                    is ModernResult.Error -> {
                        logger.e(TAG, "重试同步失败: 无法获取当前数据")
                        ModernResult.Error(currentData.error)
                    }
                    else -> {
                        logger.d(TAG, "重试同步跳过: 数据加载中")
                        ModernResult.Success(Unit)
                    }
                }
            } catch (e: Exception) {
                logger.e(e, TAG, "重试同步时发生异常")
                ModernResult.Error(
                    com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                        operationName = "retrySyncIfNeeded",
                        message = com.example.gymbro.core.ui.text.UiText.DynamicString("重试同步失败: ${e.message}"),
                        recoverable = true
                    )
                )
            }
        }
    }

    // === 私有方法 ===

    /**
     * 内部同步认证数据方法
     */
    private suspend fun syncAuthDataInternal(authUser: AuthUser): ModernResult<Unit> {
        return try {
            logger.d(TAG, "开始同步认证数据: userId=${authUser.uid}")

            // 检查用户是否已存在
            val userExists = userDataRepository.userExists(authUser.uid)
            when (userExists) {
                is ModernResult.Success -> {
                    if (userExists.data) {
                        // 用户已存在，更新认证数据
                        logger.d(TAG, "更新现有用户的认证数据")
                        userDataRepository.updateAuthData(authUser)
                    } else {
                        // 新用户，创建初始数据
                        logger.d(TAG, "创建新用户数据")
                        createNewUserData(authUser)
                    }
                }
                is ModernResult.Error -> {
                    logger.e(TAG, "检查用户存在性失败: ${userExists.error}")
                    ModernResult.Error(userExists.error)
                }
                else -> {
                    logger.w(TAG, "检查用户存在性超时")
                    ModernResult.Success(Unit)
                }
            }
        } catch (e: Exception) {
            logger.e(e, TAG, "同步认证数据时发生异常")
            handleSyncError(authUser.uid, e)
        }
    }

    /**
     * 创建新用户数据
     */
    private suspend fun createNewUserData(authUser: AuthUser): ModernResult<Unit> {
        return try {
            logger.d(TAG, "为新用户创建初始数据: userId=${authUser.uid}")

            val newUserData = userDataRepository.createUserData(authUser)
            when (newUserData) {
                is ModernResult.Success -> {
                    logger.d(TAG, "新用户数据创建成功")

                    // 如果需要，可以在这里触发创建初始 Profile
                    // createInitialProfile(authUser)

                    ModernResult.Success(Unit)
                }
                is ModernResult.Error -> {
                    logger.e(TAG, "创建新用户数据失败: ${newUserData.error}")
                    ModernResult.Error(newUserData.error)
                }
                else -> {
                    logger.w(TAG, "创建新用户数据超时")
                    ModernResult.Success(Unit)
                }
            }
        } catch (e: Exception) {
            logger.e(e, TAG, "创建新用户数据时发生异常")
            handleSyncError(authUser.uid, e)
        }
    }

    /**
     * 清除用户数据
     */
    private suspend fun clearUserDataInternal(): ModernResult<Unit> {
        return try {
            logger.d(TAG, "清除用户数据")
            userDataRepository.clearUserData()
        } catch (e: Exception) {
            logger.e(e, TAG, "清除用户数据时发生异常")
            ModernResult.Error(
                com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                    operationName = "clearUserData",
                    message = com.example.gymbro.core.ui.text.UiText.DynamicString("清除用户数据失败: ${e.message}"),
                    recoverable = true
                )
            )
        }
    }

    /**
     * 处理同步错误
     */
    private suspend fun handleSyncError(userId: String, exception: Exception): ModernResult<Unit> {
        val retryCount = retryCounters.getOrDefault(userId, 0)

        if (retryCount < MAX_RETRY_ATTEMPTS) {
            retryCounters[userId] = retryCount + 1
            logger.w(TAG, "同步失败，将重试 (${retryCount + 1}/$MAX_RETRY_ATTEMPTS): ${exception.message}")

            // 标记为等待重试
            userDataRepository.updateSyncStatus(userId, SyncStatus.PENDING_SYNC)
        } else {
            logger.e(TAG, "同步失败，已达到最大重试次数: ${exception.message}")

            // 标记为同步失败
            userDataRepository.updateSyncStatus(userId, SyncStatus.SYNC_FAILED)
            retryCounters.remove(userId)
        }

        return ModernResult.Error(
            com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                operationName = "syncAuthData",
                message = com.example.gymbro.core.ui.text.UiText.DynamicString("同步失败: ${exception.message}"),
                recoverable = retryCount < MAX_RETRY_ATTEMPTS
            )
        )
    }
}
