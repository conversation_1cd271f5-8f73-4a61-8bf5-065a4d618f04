package com.example.gymbro.core.network.router

import com.example.gymbro.core.di.qualifiers.ApplicationScope
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * TokenRouter - ConversationScope 路由管理中心
 *
 * 🔥 多轮对话架构升级核心组件：
 * - 管理所有 ConversationScope 的生命周期
 * - 提供线程安全的 scope 创建、获取和释放
 * - 实现 Idle-Timeout 自动清理机制
 * - 支持高并发场景下的对话隔离
 *
 * 设计原则：
 * - 使用 ApplicationScope 确保 ConversationScope 的生命周期不与 ViewModel 绑定
 * - 提供明确的资源释放入口，防止内存泄漏
 * - 支持调试和监控功能
 */
@Singleton
class TokenRouter @Inject constructor(
    // 🔥 注入应用级别的CoroutineScope，用于管理所有ConversationScope
    @ApplicationScope
    private val applicationScope: CoroutineScope
) {
    companion object {
        private const val TAG = "TokenRouter"

        // 🔥 Idle-Timeout 配置
        private const val IDLE_TIMEOUT_MS = 30 * 60 * 1000L // 30分钟
        private const val CLEANUP_INTERVAL_MS = 5 * 60 * 1000L // 5分钟检查一次

        // 🔥 最大 scope 数量限制，防止内存泄漏
        private const val MAX_SCOPES = 100
    }

    /**
     * 存储所有活跃的 ConversationScope
     * 使用 ConcurrentHashMap 确保线程安全
     */
    private val conversationScopes = ConcurrentHashMap<String, ConversationScope>()

    // 🔥 监控指标
    private var totalTokensRouted = 0L
    private var totalEventsRouted = 0L

    init {
        // 🔥 启动自动清理任务
        startCleanupTask()
    }

    /**
     * 获取或创建指定messageId的ConversationScope
     *
     * @param messageId 对话标识
     * @return ConversationScope实例
     */
    fun getOrCreateScope(messageId: String): ConversationScope {
        return conversationScopes.computeIfAbsent(messageId) { id ->
            Timber.tag(TAG).d("🔥 创建新的ConversationScope: messageId=$id")

            // 🔥 检查scope数量限制
            if (conversationScopes.size >= MAX_SCOPES) {
                Timber.tag(TAG).w("⚠️ ConversationScope数量达到上限($MAX_SCOPES)，强制清理最旧的scope")
                cleanupOldestScopes(10) // 清理10个最旧的scope
            }

            ConversationScope(
                messageId = id,
                parentScope = applicationScope
            ).also {
                Timber.tag(TAG).d("📊 Active scopes: ${conversationScopes.size + 1}")
            }
        }
    }

    /**
     * 将 token 路由到指定的对话作用域。
     *
     * @param messageId 对话标识
     * @param token 原始 token
     */
    suspend fun routeToken(messageId: String, token: String) {
        try {
            val scope = getOrCreateScope(messageId)
            scope.emitToken(token)
            totalTokensRouted++
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "❌ Failed to route token for messageId: $messageId")
            throw e
        }
    }

    /**
     * 将语义事件路由到指定的对话作用域。
     *
     * @param messageId 对话标识
     * @param event 语义事件
     */
    suspend fun routeEvent(messageId: String, event: Any) {
        try {
            val scope = getOrCreateScope(messageId)
            scope.emitEvent(event)
            totalEventsRouted++
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "❌ Failed to route event for messageId: $messageId")
            throw e
        }
    }

    /**
     * 释放指定的ConversationScope
     *
     * @param messageId 对话标识
     */
    fun releaseScope(messageId: String) {
        conversationScopes.remove(messageId)?.let { scope ->
            Timber.tag(TAG).d("🗑️ 释放ConversationScope: messageId=$messageId")
            scope.close()
            Timber.tag(TAG).d("📊 Active scopes: ${conversationScopes.size}")
        }
    }

    /**
     * 获取指定的ConversationScope（不创建新的）
     *
     * @param messageId 对话标识
     * @return ConversationScope实例或null
     */
    fun getScope(messageId: String): ConversationScope? {
        return conversationScopes[messageId]
    }

    /**
     * 检查指定的scope是否活跃
     *
     * @param messageId 对话标识
     * @return 是否活跃
     */
    fun isScopeActive(messageId: String): Boolean {
        return conversationScopes.containsKey(messageId)
    }

    /**
     * 获取活跃的scope数量
     *
     * @return 活跃scope数量
     */
    fun getActiveScopeCount(): Int {
        return conversationScopes.size
    }

    /**
     * 获取所有活跃的scope ID列表
     *
     * @return scope ID列表
     */
    fun getActiveScopeIds(): List<String> {
        return conversationScopes.keys.toList()
    }

    /**
     * 释放所有ConversationScope
     */
    fun releaseAll() {
        Timber.tag(TAG).d("🗑️ 释放所有ConversationScope: ${conversationScopes.size}个")
        conversationScopes.values.forEach { it.close() }
        conversationScopes.clear()
    }

    /**
     * 获取调试信息
     *
     * @return 调试信息字符串
     */
    fun getDebugInfo(): String {
        return buildString {
            appendLine("=== TokenRouter Debug Info ===")
            appendLine("Active Scopes: ${conversationScopes.size}")
            appendLine("Total Tokens Routed: $totalTokensRouted")
            appendLine("Total Events Routed: $totalEventsRouted")
            appendLine("Active Scope IDs:")
            conversationScopes.keys.forEach { messageId ->
                appendLine("  - $messageId")
            }
        }
    }

    /**
     * 启动自动清理任务
     */
    private fun startCleanupTask() {
        applicationScope.launch {
            while (isActive) {
                try {
                    delay(CLEANUP_INTERVAL_MS)
                    cleanupIdleScopes()
                } catch (e: Exception) {
                    Timber.tag(TAG).e(e, "❌ 自动清理任务异常")
                }
            }
        }
    }

    /**
     * 清理空闲的ConversationScope
     */
    private fun cleanupIdleScopes() {
        val currentTime = System.currentTimeMillis()
        val scopesToRemove = mutableListOf<String>()

        conversationScopes.forEach { (messageId, scope) ->
            if (currentTime - scope.getLastActivityTime() > IDLE_TIMEOUT_MS) {
                scopesToRemove.add(messageId)
            }
        }

        if (scopesToRemove.isNotEmpty()) {
            Timber.tag(TAG).d("🧹 清理${scopesToRemove.size}个空闲scope")
            scopesToRemove.forEach { messageId ->
                releaseScope(messageId)
            }
        }
    }

    /**
     * 强制清理最旧的scope
     */
    private fun cleanupOldestScopes(count: Int) {
        val sortedScopes = conversationScopes.entries
            .sortedBy { it.value.getCreatedTime() }
            .take(count)

        sortedScopes.forEach { (messageId, _) ->
            releaseScope(messageId)
        }
    }
}
