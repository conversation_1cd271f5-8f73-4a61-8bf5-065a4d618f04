package com.example.gymbro.di.utils

import android.content.Context
import com.example.gymbro.core.logging.Logger
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject

/**
 * DI模块扫描工具 - 用于在DEBUG模式下检测和分析DI组件性能问题
 *
 * 该工具在开发阶段帮助开发者识别依赖注入中的潜在性能问题，包括：
 * - 检测过度使用@Singleton
 * - 识别重型对象的懒加载需求
 * - 检查作用域一致性
 * - 分析依赖链深度
 *
 * 注意：此工具只应在DEBUG构建中使用，移除@Singleton以减少Release版本的内存占用
 */
class DiScannerTool @Inject constructor(
    @ApplicationContext private val context: Context,
    private val logger: Logger,
) {
    private val heavyObjectClasses = setOf(
        "ImageProcessor",
        "MapRenderer",
        "WorkoutAnalyzer",
        "VideoProcessor",
        "CacheManager",
    )

    private val singletonDependencies = mutableMapOf<String, List<String>>()
    private val inappropriateSingletons = mutableListOf<String>()
    private val potentialLazyObjects = mutableListOf<String>()
    private val deepDependencyChains = mutableListOf<String>()

    /**
     * 扫描并分析应用中的DI组件
     *
     * 调用此方法会生成一份DI性能分析报告，打印到Logcat
     */
    fun scanAndAnalyze() {
        if (!isDebugBuild()) {
            logger.w("DiScannerTool仅应在DEBUG构建中使用")
            return
        }

        logger.d("开始扫描DI组件性能问题...")

        // 扫描逻辑在实际项目中实现
        // 这里只是演示结构

        scanForSingletonOveruse()
        scanForHeavyObjects()
        scanForScopeInconsistency()
        scanForDeepDependencyChains()

        printAnalysisReport()
    }

    /**
     * 检测过度使用@Singleton的情况
     *
     * 注意：当前为演示实现，实际项目中应该通过反射或编译时注解处理器
     * 扫描所有@Module类中的@Provides和@Binds方法，检查@Singleton使用情况
     */
    private fun scanForSingletonOveruse() {
        logger.d("扫描@Singleton过度使用...")

        // 基于当前DI重构经验的常见问题检测
        // 这些是在实际重构中发现的过度使用@Singleton的模式
        checkForUnnecessarySingletons()
    }

    /**
     * 识别应该使用懒加载的重型对象
     *
     * 注意：当前为演示实现，实际项目中应该分析对象创建成本
     * 和使用频率来确定是否需要懒加载
     */
    private fun scanForHeavyObjects() {
        logger.d("扫描重型对象...")

        // 基于GymBro项目特点的重型对象检测
        checkForHeavyObjectsInGymBro()
    }

    /**
     * 检查作用域一致性问题
     *
     * 注意：当前为演示实现，实际项目中应该分析依赖图
     * 检查长生命周期组件是否依赖短生命周期组件
     */
    private fun scanForScopeInconsistency() {
        logger.d("扫描作用域一致性问题...")

        // 基于Clean Architecture原则的作用域检查
        checkForScopeViolations()
    }

    /**
     * 检测过深的依赖链
     *
     * 注意：当前为演示实现，实际项目中应该分析构造函数依赖
     * 检查是否存在过长的依赖链（通常超过5层就需要重构）
     */
    private fun scanForDeepDependencyChains() {
        logger.d("扫描过深依赖链...")

        // 基于MVVM和Clean Architecture的依赖链检查
        checkForDeepDependencyChains()
    }

    /**
     * 检查不必要的单例使用
     */
    private fun checkForUnnecessarySingletons() {
        // 基于重构经验，这些类型通常不需要@Singleton
        val unnecessarySingletonPatterns = listOf(
            "简单映射器类（如ProfileToDomainMapper）",
            "无状态工具类（如UuidGenerator）",
            "简单查询用例（如GetUserWorkoutPlanUseCase）",
        )
        inappropriateSingletons.addAll(unnecessarySingletonPatterns)
    }

    /**
     * 检查GymBro项目中的重型对象
     */
    private fun checkForHeavyObjectsInGymBro() {
        // 基于GymBro项目特点的重型对象
        val heavyObjects = listOf(
            "UserAnalyzer（用户数据分析器）",
            "CoachAnalyzer（AI教练分析器）",
            "ProfileAnalyzer（用户资料分析器）",
            "SubscriptionAnalyzer（订阅分析器）",
            "AuthAnalyzer（认证分析器）",
        )
        potentialLazyObjects.addAll(heavyObjects)
    }

    /**
     * 检查作用域违规
     */
    private fun checkForScopeViolations() {
        // 基于Clean Architecture的作用域检查
        singletonDependencies["Repository实现"] = listOf("应该依赖@Singleton的DataSource")
        singletonDependencies["UseCase"] = listOf("不应该使用@Singleton，除非需要维护状态")
    }

    /**
     * 检查深依赖链
     */
    private fun checkForDeepDependencyChains() {
        // 基于MVVM模式的依赖链检查
        deepDependencyChains.add("ViewModel -> UseCase -> Repository -> DataSource -> API（正常4层）")
        deepDependencyChains.add("避免：ViewModel -> UseCase -> Service -> Manager -> Helper -> Util（过深6层）")
    }

    /**
     * 打印分析报告
     */
    private fun printAnalysisReport() {
        logger.d("======= DI性能分析报告 =======")

        // 报告@Singleton过度使用
        if (inappropriateSingletons.isNotEmpty()) {
            logger.w("可能过度使用@Singleton的组件:")
            inappropriateSingletons.forEach { logger.w("- %s", it) }
        }

        // 报告应该懒加载的重型对象
        if (potentialLazyObjects.isNotEmpty()) {
            logger.w("应考虑懒加载的重型对象:")
            potentialLazyObjects.forEach { logger.w("- %s", it) }
        }

        // 报告作用域一致性问题
        singletonDependencies.forEach { (singleton, dependencies) ->
            logger.w("Singleton '%s' 依赖于可能不一致作用域的组件:", singleton)
            dependencies.forEach { logger.w("- %s", it) }
        }

        // 报告深依赖链
        if (deepDependencyChains.isNotEmpty()) {
            logger.w("过深的依赖链:")
            deepDependencyChains.forEach { logger.w("- %s", it) }
        }

        logger.d("===============================")
    }

    /**
     * 检查是否为DEBUG构建
     */
    private fun isDebugBuild(): Boolean {
        return try {
            val applicationInfo = context.applicationInfo
            (applicationInfo.flags and android.content.pm.ApplicationInfo.FLAG_DEBUGGABLE) != 0
        } catch (e: Exception) {
            logger.e(e, "无法确定构建类型")
            false
        }
    }

    companion object {
        /**
         * 在应用启动时初始化扫描工具
         *
         * 仅在DEBUG模式下有效
         */
        fun init(context: Context) {
            // 实际项目中，这将使用Hilt注入的实例
            // 这里只是示例逻辑
            // 注意：这里无法直接使用logger，因为companion object中没有注入
            // 实际使用时应该通过Hilt注入的实例调用
        }
    }
}
