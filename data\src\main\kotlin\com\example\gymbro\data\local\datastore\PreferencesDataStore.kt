package com.example.gymbro.data.local.datastore

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.*
import com.example.gymbro.domain.profile.model.user.FitnessGoal
import com.example.gymbro.domain.profile.model.user.FitnessPreference
import com.example.gymbro.domain.profile.model.user.WeekDay
import com.example.gymbro.domain.profile.model.user.settings.UserSettings
import com.google.gson.Gson
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import timber.log.Timber
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 用户偏好设置DataStore
 * 封装对DataStore的访问，提供类型安全的API
 */
@Singleton
class PreferencesDataStore
@Inject
constructor(
    private val dataStore: DataStore<Preferences>,
    private val gson: Gson,
) {
    // 偏好设置键
    private object PreferencesKeys {
        val USER_SETTINGS = stringPreferencesKey("user_settings")

        // 健身偏好设置键
        val FITNESS_GOAL = stringPreferencesKey("fitness_goal")
        val TRAINING_DAYS = stringSetPreferencesKey("workout_days")
        val FITNESS_LAST_UPDATED = longPreferencesKey("fitness_last_updated")
    }

    /**
     * 获取用户设置
     * @param userId 用户ID（用于兼容性，目前未使用）
     * @return 用户设置对象的Flow
     */
    fun getUserSettings(userId: String = "default"): Flow<UserSettings> =
        dataStore.data
            .catch { exception ->
                // 如果读取时出现错误（如数据损坏），发出一个空的设置对象
                if (exception is IOException) {
                    Timber.e(exception, "读取用户设置时出错")
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }.map { preferences ->
                val settingsJson = preferences[PreferencesKeys.USER_SETTINGS]
                if (settingsJson.isNullOrBlank()) {
                    Timber.d("未找到用户设置，返回默认值")
                    UserSettings(userId = userId) // 使用构造函数替代getDefaultSettings
                } else {
                    try {
                        gson.fromJson(settingsJson, UserSettings::class.java)
                    } catch (e: Exception) {
                        Timber.e(e, "解析用户设置JSON失败")
                        UserSettings(userId = userId) // 使用构造函数替代getDefaultSettings
                    }
                }
            }

    /**
     * 更新用户设置
     * @param userSettings 用户设置对象
     * @param userId 用户ID（用于兼容性，目前未使用）
     */
    suspend fun updateUserSettings(
        userSettings: UserSettings,
        userId: String = "default",
    ) {
        try {
            val settingsJson = gson.toJson(userSettings)
            dataStore.edit { preferences ->
                preferences[PreferencesKeys.USER_SETTINGS] = settingsJson
            }
            Timber.d("用户设置已更新")
        } catch (e: Exception) {
            Timber.e(e, "更新用户设置失败")
            throw e
        }
    }

    /**
     * 获取健身偏好设置的响应式数据流
     * @return 健身偏好设置的Flow
     */
    fun getFitnessPreferenceFlow(): Flow<FitnessPreference> =
        dataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    Timber.e(exception, "读取健身偏好设置时出错")
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }.map { preferences ->
                try {
                    // 从DataStore读取健身目标
                    val goalString = preferences[PreferencesKeys.FITNESS_GOAL]
                    val primaryGoal =
                        goalString?.let {
                            try {
                                FitnessGoal.valueOf(it)
                            } catch (e: IllegalArgumentException) {
                                Timber.w("无效的健身目标: $it")
                                null
                            }
                        }

                    // 从DataStore读取训练日
                    val workoutDayStrings = preferences[PreferencesKeys.TRAINING_DAYS] ?: emptySet()
                    val workoutDays =
                        workoutDayStrings
                            .mapNotNull { dayString ->
                                try {
                                    WeekDay.valueOf(dayString)
                                } catch (e: IllegalArgumentException) {
                                    Timber.w("无效的训练日: $dayString")
                                    null
                                }
                            }.toSet()

                    val preference =
                        FitnessPreference(
                            primaryGoal = primaryGoal,
                            workoutDays = workoutDays,
                        )

                    Timber.d(
                        "从DataStore读取健身偏好: goal=${primaryGoal?.name}, workoutDays=${preference.getWorkoutDaysString()}",
                    )
                    preference
                } catch (e: Exception) {
                    Timber.e(e, "解析健身偏好设置失败，返回默认值")
                    FitnessPreference()
                }
            }

    /**
     * 更新健身偏好设置
     * @param fitnessPreference 健身偏好设置对象
     */
    suspend fun updateFitnessPreference(fitnessPreference: FitnessPreference) {
        try {
            dataStore.edit { preferences ->
                // 保存健身目标
                val goal = fitnessPreference.primaryGoal
                if (goal != null) {
                    preferences[PreferencesKeys.FITNESS_GOAL] = goal.name
                } else {
                    preferences.remove(PreferencesKeys.FITNESS_GOAL)
                }

                // 保存训练日
                if (fitnessPreference.workoutDays.isNotEmpty()) {
                    preferences[PreferencesKeys.TRAINING_DAYS] = fitnessPreference.workoutDays.map { it.name }.toSet()
                } else {
                    preferences.remove(PreferencesKeys.TRAINING_DAYS)
                }

                // 保存更新时间戳
                preferences[PreferencesKeys.FITNESS_LAST_UPDATED] = System.currentTimeMillis()
            }

            Timber.d(
                "健身偏好设置已更新: goal=${fitnessPreference.primaryGoal?.name}, workoutDays=${fitnessPreference.getWorkoutDaysString()}",
            )
        } catch (e: Exception) {
            Timber.e(e, "更新健身偏好设置失败")
            throw e
        }
    }
}
