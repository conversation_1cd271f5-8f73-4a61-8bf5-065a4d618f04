package com.example.gymbro.core.ai.prompt.template

/**
 * 模板引擎接口
 *
 * 负责渲染prompt模板，支持变量替换和格式化
 */
interface TemplateEngine {

    /**
     * 渲染指定的模板
     *
     * @param templateName 模板名称
     * @param variables 模板变量
     * @return 渲染后的文本
     */
    fun render(templateName: String, variables: Map<String, String>): String

    /**
     * 渲染文本模板
     *
     * @param template 模板文本
     * @param variables 模板变量
     * @return 渲染后的文本
     */
    fun renderText(template: String, variables: Map<String, String>): String
}

/**
 * 简单模板引擎实现
 */
class TemplateEngineImpl : TemplateEngine {

    private val templates = mapOf(
        "fitness_qa" to """
            <system>
            你是专业健身AI助手GymBro。

            核心能力：
            • 基于用户数据提供个性化训练建议
            • 分析训练进度并调整计划
            • 回答健身相关问题
            • 引用具体训练模板和数据

            约束条件：
            • 仅基于提供的上下文信息回答
            • 不确定时明确说明需要更多信息
            • 优先引用相关训练模板
            • 保持专业且易懂的语言

            输出格式：简洁明了的建议，包含具体的训练参数和模板引用
            </system>

            {{profile}}

            {{templates}}

            {{summary}}

            {{history}}

            {{recent}}
        """.trimIndent(),

        "summary" to """
            请总结以下对话的关键要点：

            {{content}}

            请提供简洁的摘要，突出重要信息和决策。
        """.trimIndent(),

        // Pipeline系统模板
        "pipeline_common_header" to """
            You are GymBro‑LLM v1.0. YOU MUST follow the 5‑Step Method in order.
            NEVER skip, merge or reorder steps. After each step output the required
            JSON under a single root key exactly as specified. No additional keys.
        """.trimIndent(),

        "pipeline_step1" to """
            <!--step:1-->
            Task: Clean the user input & context.
            Rules:
            1. Remove jokes, greetings, expletives.
            2. Fill obvious missing units (e.g. kg, cm) when deducible.
            3. DO NOT transform semantics.
            Respond ONLY with:
            {
              "sanitized": "<string>",
              "removedNoise": ["<string>"]
            }

            User Input: {{userInput}}
        """.trimIndent(),

        "pipeline_step2" to """
            <!--step:2-->
            Use previous Sanitized‑Context to build Task‑Spec.
            Return:
            {
              "purpose": "<string>",
              "hardConstraints": ["<string>"],
              "preferences": ["<string>"]
            }

            Sanitized Context: {{sanitizedContext}}
        """.trimIndent(),

        "pipeline_step3" to """
            <!--step:3-->
            Fetch knowledge via tools. Return list sorted by relevance DESC.
            {
              "knowledgePack": [
                 {"source": "url|doc", "snippet": "<string>"}
              ]
            }

            Task Purpose: {{taskPurpose}}
            Hard Constraints: {{hardConstraints}}
            Preferences: {{preferences}}
        """.trimIndent(),

        "pipeline_step4" to """
            <!--step:4-->
            1. Produce Outline (max depth 3).
            2. Wait for client CONFIRMATION token "APPROVED" before expanding.
            Return initially:
            {"outline": "…"}
            After approval return:
            {"draft": "…markdown…"}

            Knowledge Pack: {{knowledgePack}}
            Task Spec: {{taskSpec}}
        """.trimIndent(),

        "pipeline_step5" to """
            <!--step:5-->
            Validate Draft for consistency, citations, safety.
            If OK:
            {"finalAnswer": "…markdown…"}
            Else:
            {"errorReport": "…"}

            Draft Content: {{draftContent}}
            Original Task: {{originalTask}}
        """.trimIndent(),
    )

    override fun render(templateName: String, variables: Map<String, String>): String {
        val template = templates[templateName]
            ?: throw IllegalArgumentException("Template not found: $templateName")

        return renderText(template, variables)
    }

    override fun renderText(template: String, variables: Map<String, String>): String {
        var result = template

        variables.forEach { (key, value) ->
            result = result.replace("{{$key}}", value)
        }

        // 清理未替换的占位符
        result = result.replace(Regex("\\{\\{[^}]+\\}\\}"), "")

        // 清理多余的空行
        result = result.replace(Regex("\n\\s*\n\\s*\n"), "\n\n")

        return result.trim()
    }
}
