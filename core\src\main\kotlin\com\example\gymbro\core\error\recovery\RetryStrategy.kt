package com.example.gymbro.core.error.recovery

import com.example.gymbro.core.error.internal.recovery.RetryStrategyImpl

/**
 * 重试恢复策略接口
 * 在操作失败时提供重试机制
 */
interface RetryStrategy<T> : RecoveryStrategy<T> {
    companion object {
        /**
         * 创建简单的重试策略
         * @param operation 要重试的操作
         * @param maxAttempts 最大重试次数
         * @return 重试策略
         */
        fun <T> simple(
            operation: suspend () -> T?,
            maxAttempts: Int = 3,
        ): RetryStrategy<T> =
            RetryStrategyImpl(
                operation = operation,
                maxAttempts = maxAttempts,
            )

        /**
         * 创建带指数退避的重试策略
         * @param operation 要重试的操作
         * @param maxAttempts 最大重试次数
         * @param initialDelayMillis 初始延迟（毫秒）
         * @param maxDelayMillis 最大延迟（毫秒）
         * @param backoffFactor 延迟增长因子
         * @return 重试策略
         */
        fun <T> withExponentialBackoff(
            operation: suspend () -> T?,
            maxAttempts: Int = 3,
            initialDelayMillis: Long = 1000,
            maxDelayMillis: Long = 10000,
            backoffFactor: Double = 2.0,
        ): RetryStrategy<T> =
            RetryStrategyImpl(
                operation = operation,
                maxAttempts = maxAttempts,
                initialDelayMillis = initialDelayMillis,
                maxDelayMillis = maxDelayMillis,
                backoffFactor = backoffFactor,
            )
    }
}
