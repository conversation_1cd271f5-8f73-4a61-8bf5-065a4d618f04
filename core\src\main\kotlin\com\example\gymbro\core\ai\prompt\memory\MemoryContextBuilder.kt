package com.example.gymbro.core.ai.prompt.memory

import com.example.gymbro.core.ai.prompt.builder.ConversationTurn
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Memory上下文构建器
 *
 * 从用户输入和对话历史中智能提取Memory查询所需的上下文信息
 *
 * @since 618重构
 */
@Singleton
class MemoryContextBuilder @Inject constructor() {

    /**
     * 从用户输入构建Memory上下文
     *
     * @param userId 用户ID
     * @param userInput 用户当前输入
     * @param history 对话历史
     * @return Memory上下文，如果无需召回记忆则返回null
     *
     * @since 618重构
     */
    fun buildContext(
        userId: String,
        userInput: String,
        history: List<ConversationTurn> = emptyList(),
    ): MemoryContext? {
        Timber.d("MemoryContextBuilder: 开始构建上下文，用户输入长度=${userInput.length}")

        // 1. 分析用户输入，判断是否需要Memory支持
        if (!needsMemorySupport(userInput)) {
            Timber.d("MemoryContextBuilder: 用户输入无需Memory支持")
            return null
        }

        // 2. 确定上下文类型
        val contextType = detectContextType(userInput, history)

        // 3. 提取查询关键词
        val query = extractQueryKeywords(userInput, history)

        // 4. 收集元数据
        val metadata = collectMetadata(userInput, history)

        val context = MemoryContext(
            userId = userId,
            query = query,
            contextType = contextType,
            metadata = metadata,
        )

        Timber.d("MemoryContextBuilder: 上下文构建完成，类型=$contextType，查询=$query")
        return context
    }

    /**
     * 判断是否需要Memory支持
     */
    private fun needsMemorySupport(userInput: String): Boolean {
        // 简单问候不需要Memory
        val greetingPatterns = listOf(
            "你好", "hello", "hi", "嗨", "早上好", "晚上好",
            "再见", "拜拜", "bye", "谢谢",
        )

        val lowerInput = userInput.lowercase().trim()
        if (greetingPatterns.any { lowerInput.contains(it) } && userInput.length < 20) {
            return false
        }

        // 过短的输入不需要Memory
        if (userInput.length < 10) {
            return false
        }

        // 其他情况都尝试使用Memory增强回答
        return true
    }

    /**
     * 检测上下文类型
     */
    private fun detectContextType(
        userInput: String,
        history: List<ConversationTurn>,
    ): MemoryContextType {
        val lowerInput = userInput.lowercase()

        // 训练相关关键词
        val trainingKeywords = listOf(
            "训练", "健身", "锻炼", "运动", "肌肉", "力量",
            "减脂", "增肌", "计划", "模板", "workout",
            "exercise", "training", "muscle", "strength",
        )

        // 用户档案相关关键词
        val profileKeywords = listOf(
            "我的", "个人", "身高", "体重", "年龄", "目标",
            "档案", "资料", "profile", "my", "personal",
        )

        // 检查当前输入
        return when {
            trainingKeywords.any { lowerInput.contains(it) } -> {
                MemoryContextType.TRAINING
            }
            profileKeywords.any { lowerInput.contains(it) } -> {
                MemoryContextType.PROFILE
            }
            history.isNotEmpty() -> {
                // 如果有对话历史，认为是对话上下文
                MemoryContextType.CONVERSATION
            }
            else -> {
                MemoryContextType.GENERAL
            }
        }
    }

    /**
     * 提取查询关键词
     */
    private fun extractQueryKeywords(
        userInput: String,
        history: List<ConversationTurn>,
    ): String {
        // 如果用户输入较短，结合历史对话生成查询
        return if (userInput.length < 30 && history.isNotEmpty()) {
            // 结合最近的对话内容
            val recentContext = history.takeLast(2).joinToString(" ") { it.user }
            "$userInput $recentContext".take(100)
        } else {
            // 直接使用用户输入作为查询
            userInput.take(100)
        }
    }

    /**
     * 收集元数据
     */
    private fun collectMetadata(
        userInput: String,
        history: List<ConversationTurn>,
    ): Map<String, Any> {
        return buildMap {
            // 对话轮次
            put("conversation_turns", history.size)

            // 输入长度
            put("input_length", userInput.length)

            // 是否包含问号（疑问句）
            put("is_question", userInput.contains("?") || userInput.contains("？"))

            // 最近活跃时间
            if (history.isNotEmpty()) {
                put("last_active", System.currentTimeMillis())
            }
        }
    }

    /**
     * 从AiContext构建Memory上下文（兼容旧接口）
     *
     * @since 618重构
     */
    fun buildFromAiContext(
        userId: String,
        query: String,
        hasActiveWorkout: Boolean = false,
        hasRecentTemplate: Boolean = false,
    ): MemoryContext {
        val contextType = when {
            hasActiveWorkout -> MemoryContextType.TRAINING
            hasRecentTemplate -> MemoryContextType.TRAINING
            else -> MemoryContextType.GENERAL
        }

        return MemoryContext(
            userId = userId,
            query = query,
            contextType = contextType,
            metadata = mapOf(
                "has_active_workout" to hasActiveWorkout,
                "has_recent_template" to hasRecentTemplate,
            ),
        )
    }
}
