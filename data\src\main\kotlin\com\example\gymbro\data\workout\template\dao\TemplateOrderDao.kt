package com.example.gymbro.data.workout.template.dao

import androidx.room.*
import com.example.gymbro.data.workout.template.entity.TemplateOrderEntity
import com.example.gymbro.data.workout.template.entity.DraftOrderEntity
import kotlinx.coroutines.flow.Flow

/**
 * 模板排序数据访问对象
 * 
 * 提供模板和草稿排序的数据库操作接口，支持：
 * - 排序信息的增删改查
 * - 批量排序更新
 * - 置顶操作
 * - 用户隔离查询
 * 
 * 🏗️ 架构原则：
 * - 使用 Flow 提供响应式数据流
 * - 支持事务操作确保数据一致性
 * - 提供批量操作提高性能
 * - 自动时间戳管理
 */
@Dao
interface TemplateOrderDao {
    
    // === 模板排序操作 ===
    
    /**
     * 获取用户的模板排序列表
     * 按 sortOrder 升序排列
     */
    @Query("""
        SELECT * FROM template_order 
        WHERE user_id = :userId 
        ORDER BY sort_order ASC
    """)
    fun getTemplateOrdersByUser(userId: String): Flow<List<TemplateOrderEntity>>
    
    /**
     * 获取特定模板的排序信息
     */
    @Query("""
        SELECT * FROM template_order 
        WHERE user_id = :userId AND template_id = :templateId
        LIMIT 1
    """)
    suspend fun getTemplateOrder(userId: String, templateId: String): TemplateOrderEntity?
    
    /**
     * 插入或更新模板排序
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTemplateOrder(order: TemplateOrderEntity)
    
    /**
     * 批量插入或更新模板排序
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTemplateOrders(orders: List<TemplateOrderEntity>)
    
    /**
     * 更新模板排序
     */
    @Update
    suspend fun updateTemplateOrder(order: TemplateOrderEntity)
    
    /**
     * 删除模板排序
     */
    @Query("DELETE FROM template_order WHERE user_id = :userId AND template_id = :templateId")
    suspend fun deleteTemplateOrder(userId: String, templateId: String)
    
    /**
     * 获取用户模板的最大排序值
     */
    @Query("SELECT MAX(sort_order) FROM template_order WHERE user_id = :userId")
    suspend fun getMaxTemplateOrder(userId: String): Int?
    
    /**
     * 模板置顶操作
     * 将指定模板设置为最小排序值（置顶），其他模板排序值+1
     */
    @Transaction
    suspend fun moveTemplateToTop(userId: String, templateId: String) {
        // 获取当前模板的排序信息
        val currentOrder = getTemplateOrder(userId, templateId)
        
        if (currentOrder != null) {
            // 将所有排序值小于当前模板的记录+1
            incrementTemplateOrdersBelow(userId, currentOrder.sortOrder)
            
            // 将当前模板设置为排序值0（置顶）
            updateTemplateOrder(currentOrder.copy(
                sortOrder = 0,
                updatedAt = System.currentTimeMillis()
            ))
        } else {
            // 如果模板没有排序记录，创建一个置顶记录
            insertTemplateOrder(TemplateOrderEntity(
                templateId = templateId,
                userId = userId,
                sortOrder = 0
            ))
            
            // 将所有现有记录的排序值+1
            incrementAllTemplateOrders(userId)
        }
    }
    
    /**
     * 将排序值小于指定值的记录+1
     */
    @Query("""
        UPDATE template_order 
        SET sort_order = sort_order + 1, updated_at = :timestamp
        WHERE user_id = :userId AND sort_order < :sortOrder
    """)
    suspend fun incrementTemplateOrdersBelow(
        userId: String, 
        sortOrder: Int, 
        timestamp: Long = System.currentTimeMillis()
    )
    
    /**
     * 将所有模板排序值+1
     */
    @Query("""
        UPDATE template_order 
        SET sort_order = sort_order + 1, updated_at = :timestamp
        WHERE user_id = :userId
    """)
    suspend fun incrementAllTemplateOrders(
        userId: String, 
        timestamp: Long = System.currentTimeMillis()
    )
    
    // === 草稿排序操作 ===
    
    /**
     * 获取用户的草稿排序列表
     * 按 sortOrder 升序排列
     */
    @Query("""
        SELECT * FROM draft_order 
        WHERE user_id = :userId 
        ORDER BY sort_order ASC
    """)
    fun getDraftOrdersByUser(userId: String): Flow<List<DraftOrderEntity>>
    
    /**
     * 获取特定草稿的排序信息
     */
    @Query("""
        SELECT * FROM draft_order 
        WHERE user_id = :userId AND draft_id = :draftId
        LIMIT 1
    """)
    suspend fun getDraftOrder(userId: String, draftId: String): DraftOrderEntity?
    
    /**
     * 插入或更新草稿排序
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertDraftOrder(order: DraftOrderEntity)
    
    /**
     * 批量插入或更新草稿排序
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertDraftOrders(orders: List<DraftOrderEntity>)
    
    /**
     * 更新草稿排序
     */
    @Update
    suspend fun updateDraftOrder(order: DraftOrderEntity)
    
    /**
     * 删除草稿排序
     */
    @Query("DELETE FROM draft_order WHERE user_id = :userId AND draft_id = :draftId")
    suspend fun deleteDraftOrder(userId: String, draftId: String)
    
    /**
     * 获取用户草稿的最大排序值
     */
    @Query("SELECT MAX(sort_order) FROM draft_order WHERE user_id = :userId")
    suspend fun getMaxDraftOrder(userId: String): Int?
    
    /**
     * 草稿置顶操作
     * 将指定草稿设置为最小排序值（置顶），其他草稿排序值+1
     */
    @Transaction
    suspend fun moveDraftToTop(userId: String, draftId: String) {
        // 获取当前草稿的排序信息
        val currentOrder = getDraftOrder(userId, draftId)
        
        if (currentOrder != null) {
            // 将所有排序值小于当前草稿的记录+1
            incrementDraftOrdersBelow(userId, currentOrder.sortOrder)
            
            // 将当前草稿设置为排序值0（置顶）
            updateDraftOrder(currentOrder.copy(
                sortOrder = 0,
                updatedAt = System.currentTimeMillis()
            ))
        } else {
            // 如果草稿没有排序记录，创建一个置顶记录
            insertDraftOrder(DraftOrderEntity(
                draftId = draftId,
                userId = userId,
                sortOrder = 0
            ))
            
            // 将所有现有记录的排序值+1
            incrementAllDraftOrders(userId)
        }
    }
    
    /**
     * 将排序值小于指定值的记录+1
     */
    @Query("""
        UPDATE draft_order 
        SET sort_order = sort_order + 1, updated_at = :timestamp
        WHERE user_id = :userId AND sort_order < :sortOrder
    """)
    suspend fun incrementDraftOrdersBelow(
        userId: String, 
        sortOrder: Int, 
        timestamp: Long = System.currentTimeMillis()
    )
    
    /**
     * 将所有草稿排序值+1
     */
    @Query("""
        UPDATE draft_order 
        SET sort_order = sort_order + 1, updated_at = :timestamp
        WHERE user_id = :userId
    """)
    suspend fun incrementAllDraftOrders(
        userId: String, 
        timestamp: Long = System.currentTimeMillis()
    )
}
