package com.example.gymbro.designSystem.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme

/**
 * GymBro设置组件 - 用于包装一组相关的设置项
 *
 * 基于ProfileScreen设计标准，提供统一的设置分组容器。
 * 采用圆角卡片设计，使用标准的间距和颜色规范。
 *
 * @param modifier 修饰符
 * @param content 分组内容，使用ColumnScope
 */
@Composable
fun GymBroSettingsGroup(
    modifier: Modifier = Modifier,
    content: @Composable ColumnScope.() -> Unit,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
            .padding(bottom = 16.dp),
    ) {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface,
            ),
            shape = RoundedCornerShape(12.dp),
        ) {
            content()
        }
    }
}

/**
 * GymBro设置项组件 - 单个设置项
 *
 * 基于ProfileScreen设计标准，提供统一的设置项样式。
 * 支持UiText国际化、无障碍访问和标准的交互反馈。
 *
 * @param icon 前导图标
 * @param title 设置项标题，支持UiText
 * @param onClick 点击回调
 * @param modifier 修饰符
 * @param showArrow 是否显示右箭头，默认为true
 * @param subtitle 副标题，可选
 * @param enabled 是否启用，默认为true
 */
@Composable
fun GymBroSettingsItem(
    icon: ImageVector,
    title: UiText,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    showArrow: Boolean = true,
    subtitle: UiText? = null,
    enabled: Boolean = true,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .height(56.dp)
            .clickable(
                enabled = enabled,
                role = Role.Button,
                onClick = onClick,
            )
            .padding(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Icon(
            imageVector = icon,
            contentDescription = title.asString(),
            tint = if (enabled) {
                MaterialTheme.colorScheme.primary
            } else {
                MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
            },
            modifier = Modifier.size(24.dp),
        )

        Spacer(modifier = Modifier.width(16.dp))

        Column(
            modifier = Modifier.weight(1f),
        ) {
            Text(
                text = title.asString(),
                color = if (enabled) {
                    MaterialTheme.colorScheme.onSurface
                } else {
                    MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                },
                fontSize = 16.sp,
            )

            subtitle?.let { subtitleText ->
                Text(
                    text = subtitleText.asString(),
                    color = if (enabled) {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.38f)
                    },
                    fontSize = 12.sp,
                )
            }
        }

        if (showArrow) {
            Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = "前往",
                tint = if (enabled) {
                    MaterialTheme.colorScheme.onSurfaceVariant
                } else {
                    MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.38f)
                },
                modifier = Modifier.size(24.dp),
            )
        }
    }
}

/**
 * GymBro设置项组件 - 字符串版本（向后兼容）
 *
 * 为了向后兼容，提供接受String参数的版本。
 * 建议新代码使用UiText版本以支持国际化。
 *
 * @param icon 前导图标
 * @param title 设置项标题
 * @param onClick 点击回调
 * @param modifier 修饰符
 * @param showArrow 是否显示右箭头，默认为true
 * @param subtitle 副标题，可选
 * @param enabled 是否启用，默认为true
 */
@Composable
fun GymBroSettingsItem(
    icon: ImageVector,
    title: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    showArrow: Boolean = true,
    subtitle: String? = null,
    enabled: Boolean = true,
) {
    GymBroSettingsItem(
        icon = icon,
        title = UiText.DynamicString(title),
        onClick = onClick,
        modifier = modifier,
        showArrow = showArrow,
        subtitle = subtitle?.let { UiText.DynamicString(it) },
        enabled = enabled,
    )
}

// ===== Preview Functions =====

@GymBroPreview
@Composable
private fun GymBroSettingsGroupPreview() {
    GymBroTheme {
        GymBroSettingsGroup {
            GymBroSettingsItem(
                icon = Icons.Default.ChevronRight,
                title = UiText.DynamicString("个人信息"),
                onClick = {},
            )
            GymBroSettingsItem(
                icon = Icons.Default.ChevronRight,
                title = UiText.DynamicString("通知设置"),
                subtitle = UiText.DynamicString("已开启"),
                onClick = {},
            )
            GymBroSettingsItem(
                icon = Icons.Default.ChevronRight,
                title = UiText.DynamicString("退出登录"),
                onClick = {},
                showArrow = false,
                enabled = false,
            )
        }
    }
}

@GymBroPreview
@Composable
private fun GymBroSettingsItemPreview() {
    GymBroTheme {
        Column {
            GymBroSettingsItem(
                icon = Icons.Default.ChevronRight,
                title = UiText.DynamicString("个人信息"),
                onClick = {},
            )
            GymBroSettingsItem(
                icon = Icons.Default.ChevronRight,
                title = UiText.DynamicString("通知设置"),
                subtitle = UiText.DynamicString("已开启"),
                onClick = {},
            )
        }
    }
}
