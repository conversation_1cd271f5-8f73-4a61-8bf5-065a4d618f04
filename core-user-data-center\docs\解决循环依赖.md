### 1 ⃣ 明确依赖拓扑（现状 ➜ 目标）

```mermaid
flowchart TD
    subgraph Core
        core-arch["core-arch (MVI)"]
        core-network["core-network"]
        core-ml["core-ml"]
        core-user-data-center["core-user-data-center ❌"]
    end
    subgraph Platform
        shared-models["shared-models"]
        di["di (Hilt)"]
        navigation["navigation"]
    end
    subgraph Domain
        domain["domain (37 UseCases)"]
    end
    subgraph Features
        auth["features/auth"]
        profile["features/profile"]
        workout["features/workout"]
        coach["features/coach"]
    end
    app["app"]

    core-arch --> shared-models
    core-network --> core-arch
    core-ml --> core-network
    shared-models -->|DTOs| domain
    core-user-data-center --> shared-models
    domain -->|Repo interfaces & UseCases| core-user-data-center  %% illegal right now -> creates cycle
    auth --> domain
    profile --> domain
    workout --> domain
    coach --> domain
    coach -->|UserData| core-user-data-center
    Features --> di
    di --> app
    navigation --> app

    %% target fixes
    classDef fix fill:#ffd8d8;
    core-user-data-center:::fix
```

* **红框**表示当前导致 *`:core-user-data-center ↔ :domain`* 互相引用的死环。
* 目标：让 **Domain 只依赖抽象接口**，而 **数据实现下沉到 Core**，从而保证 *Core ← Domain ← Features* 的单向流。

---

### 2 ⃣ 拆环方案

| 步骤                | 动作                                                                                                                   | 结果                             |
| ----------------- | -------------------------------------------------------------------------------------------------------------------- | ------------------------------ |
| **a. 提取 API 层**   | 新建 `core-user-data-center-api`，仅放 `UnifiedUserData`, `UserDataRepository` 接口                                         | Domain ↔ API（纯接口，无实现）          |
| **b. 迁移实现**       | 现有 `core-user-data-center` 只实现接口，实现层依赖 `core-user-data-center-api`                                                   | Core → API，不再依赖 Domain         |
| **c. 搬迁 UseCase** | 所有 `UserData…UseCase` 移到 `domain`，依赖接口而非实现                                                                           | Domain 不再指向实现模块                |
| **d. Gradle 调整**  | `domain` `api` 关闭 *ksp* 互引；`core-user-data-center` 只使用 *kapt/ksp* 自身                                                 | `:bundleLibCompileToJar` 死循环消失 |
| **e. Hilt 绑定**    | 在 `di/core/UserDataCenterModule.kt`<br>`kotlin<br>@Binds fun bind(repo: UserDataRepositoryImpl): UserDataRepository` | 单例从 Core 注入到全局                 |

---

### 3 ⃣ 数据库统一策略

1. **Room 多-module DB**

   * 将 *User* 与 *Workout* entity 放在 `shared-db-schemas`（纯 `entity/dao`）
   * 两个 feature 各自用 `@Database(entities = […])` **module-local DB**，用 `Entity/*Id` 做跨表关联。
2. **Facade** — `UnifiedUserDataCenter` 暴露 `Flow<UnifiedUserData>`，Feature 通过 repository 订阅，不直接持有 Room 引用。
3. **迁移脚本** 在 `core-user-data-center/internal/migration` 中实现，启动时一次性把旧表同步到新 schema，然后删除旧 DAO、Entity。

---

### 4 ⃣ Coach × UserDataCenter 集成

> 设计文档早已指出需要把 **UserDataCenterApi 注入 Coach** 并传递到 `LayeredPromptBuilder`  。
> 同时 `core-user-data-center` 把 **Auth+Profile** 聚合为 `UnifiedUserData`  。

#### 修改点

```kotlin
// domain.coach.usecase.SendChatMessageAndGetResponseUseCase
class SendChatMessageAndGetResponseUseCase @Inject constructor(
    private val aiGateway: AiGateway,
    private val layeredPromptBuilder: LayeredPromptBuilder,
    private val userDataCenter: UserDataCenterApi,   // ← 新增
) {
    suspend operator fun invoke(params: Params): Flow<StreamEvent> = flow {
        val userData = userDataCenter.observeUserData().first()          // ① 拿最新上下文
        val chatMessages = layeredPromptBuilder.buildChatMessages(       // ② 注入到 Prompt
            userInput = params.message,
            userContext = userData
        )
        aiGateway.stream(chatMessages).collect { emit(it) }              // ③ 发送并转发流
    }
}
```

```kotlin
// core.ai.prompt.LayeredPromptBuilder
fun buildChatMessages(
    userInput: String,
    userContext: UnifiedUserData? = null      // ← 可选参数
): List<ChatMessage> {
    val layers = mutableListOf<PromptLayer>().apply {
        userContext?.let { add(buildUserContextLayer(it)) }              // 新增层
        addAll(defaultLayers())
    }
    return assemble(layers, userInput)
}
```

* **UserContextLayer** 把健身等级、目标、体测数据、训练日等格式化为系统消息：

  ```
  系统: { "fitnessLevel":"Intermediate", "goal":"Hypertrophy", … }
  ```
* 体系内已有的 `MemoryIntegrator` 通过 `UserDataMemoryIntegrator` 补全记忆流。

---

### 5 ⃣ 文件清理 & 重组 Checklist

| ✔                                                                    | Action |
| -------------------------------------------------------------------- | ------ |
| ⬜ 删除旧 `UserDao`, `UserProfileDao`, `UserSettingsDao`（迁移后不再直接访问）      |        |
| ⬜ 移除 Profile 模块中对 Auth Model 的直接引用（改走 `UserDataCenterApi`)           |        |
| ⬜ 删除 `TokenDao` 本地缓存，改由 `AuthStateRepository` 提供 `AccessToken`       |        |
| ⬜ 移除 `TODO`/硬编码常量，改用 `@StringRes` + `BuildConfig` 开关                 |        |
| ⬜ 更新 Gradle settings：`include(":core-user-data-center-api")`；各模块依赖调整 |        |
| ⬜ 编写 `DatabaseMigration_5_6.kt` 处理表合并                                |        |
| ⬜ 新增单元 + 集成测试（UserData sync, Coach prompt injection）                 |        |

---

### 6 ⃣ 重构后数据流（时序图）

```mermaid
sequenceDiagram
    participant UI
    participant CoachVM
    participant SendUC as SendChatMessageAndGetResponseUseCase
    participant UDC as UserDataCenterApi
    participant LPB as LayeredPromptBuilder
    participant AIGW as AiGateway

    UI->>CoachVM: 用户发送消息
    CoachVM->>SendUC: invoke(message)
    SendUC->>UDC: observeUserData().first()
    UDC-->>SendUC: UnifiedUserData
    SendUC->>LPB: buildChatMessages(message, UnifiedUserData)
    LPB-->>SendUC: List<ChatMessage>
    SendUC->>AIGW: stream(chatMessages)
    AIGW-->>CoachVM: StreamEvent(flow)
```

---

### 7 ⃣ 上线步骤 & 回滚

1. **Dual-write**：上线 `UserDataCenterApi` 后先在 Auth/Profile 同步写入新表 1 周。
2. **Read-shadowing**：Coach 先并行读取旧 `ProfileFeatureApi` & 新流，对比日志。
3. **Cut-over**：一键 Feature Flag 关闭旧路径。
4. **Rollback**：保留旧 DAO + Feature Flag 可瞬时切回，无需二次迁移。

> 以上方案移除了编译循环、统一了用户数据单源，Coach 能在 *一次函数调用* 内拿到完整用户画像并拼装 Prompt，完全满足“无硬编码 / 无 TODO / 全流程示意图”要求。
