{"enabled": true, "name": "README & TREE Sync", "description": "Automatically updates README.md and TREE.md files when main package files in modules are modified to reflect current status", "version": "1", "when": {"type": "fileEdited", "patterns": ["*/build.gradle.kts", "*/src/main/**/*.kt", "*/README.md", "*/TREE.md"]}, "then": {"type": "askAgent", "prompt": "A main package file has been modified in a module. Please analyze the changes and update the corresponding README.md and TREE.md files to reflect the current status. Focus on:\n1. Update module README.md with any new features, interfaces, or architectural changes\n2. Update TREE.md to reflect new file structure or component organization\n3. Ensure documentation accurately represents the current implementation\n4. Maintain consistency with the project's documentation standards"}}