package com.example.gymbro.data.autosave

import com.example.gymbro.core.autosave.config.AutoSaveConfig
import com.example.gymbro.core.autosave.state.AutoSaveState
import com.example.gymbro.core.logging.Logger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.util.*
import javax.inject.Inject

/**
 * 自动保存会话
 *
 * 🎯 功能特性：
 * - 管理单个自动保存会话的完整生命周期
 * - 支持会话的启动、暂停、恢复、停止
 * - 提供响应式状态监控
 * - 支持错误恢复和重试机制
 * - 会话隔离和资源管理
 *
 * @param T 要保存的数据类型
 * @param sessionId 会话唯一标识符
 * @param config 自动保存配置
 * @param scope 协程作用域
 * @param logger 日志记录器
 */
class AutoSaveSession<T : Any>
@Inject
constructor(
    val sessionId: String = UUID.randomUUID().toString(),
    private val config: AutoSaveConfig<T>,
    private val scope: CoroutineScope,
    private val logger: Logger,
) {
    private val _state = MutableStateFlow(AutoSaveState<T>())
    val state: StateFlow<AutoSaveState<T>> = _state.asStateFlow()

    private var currentData: T? = null
    private var isStarted = false
    private var isPaused = false
    private var autoSaveManager: AutoSaveManagerImpl<T>? = null

    /**
     * 会话状态枚举
     */
    enum class SessionStatus {
        IDLE, // 空闲状态
        STARTING, // 启动中
        ACTIVE, // 活跃状态
        PAUSED, // 暂停状态
        STOPPING, // 停止中
        STOPPED, // 已停止
        ERROR, // 错误状态
    }

    private val _sessionStatus = MutableStateFlow(SessionStatus.IDLE)
    val sessionStatus: StateFlow<SessionStatus> = _sessionStatus.asStateFlow()

    /**
     * 启动会话
     *
     * @param initialData 初始数据
     */
    fun start(initialData: T) {
        if (isStarted) {
            logger.w("AutoSaveSession", "会话已启动，忽略重复启动: $sessionId")
            return
        }

        logger.d("AutoSaveSession", "启动自动保存会话: $sessionId")

        _sessionStatus.value = SessionStatus.STARTING

        try {
            // 创建AutoSaveManager实例
            autoSaveManager = AutoSaveManagerImpl(config, scope, logger)

            // 启动自动保存
            autoSaveManager?.start(initialData)

            currentData = initialData
            isStarted = true
            isPaused = false

            _sessionStatus.value = SessionStatus.ACTIVE

            // 监听AutoSaveManager的状态变化
            scope.launch {
                autoSaveManager?.state?.collect { managerState ->
                    _state.value = managerState
                }
            }

            logger.d("AutoSaveSession", "自动保存会话启动成功: $sessionId")
        } catch (e: Exception) {
            logger.e(e, "启动自动保存会话失败: $sessionId")
            _sessionStatus.value = SessionStatus.ERROR
            updateState { it.copy(error = e) }
        }
    }

    /**
     * 更新数据
     *
     * @param newData 新数据
     */
    fun update(newData: T) {
        if (!isStarted) {
            logger.w("AutoSaveSession", "会话未启动，忽略更新: $sessionId")
            return
        }

        if (isPaused) {
            logger.d("AutoSaveSession", "会话已暂停，缓存数据更新: $sessionId")
            currentData = newData
            return
        }

        try {
            autoSaveManager?.update(newData)
            currentData = newData

            logger.d("AutoSaveSession", "会话数据已更新: $sessionId")
        } catch (e: Exception) {
            logger.e(e, "更新会话数据失败: $sessionId")
            updateState { it.copy(error = e) }
        }
    }

    /**
     * 立即保存
     */
    suspend fun saveNow(): Result<Unit> {
        if (!isStarted) {
            return Result.failure(IllegalStateException("会话未启动"))
        }

        return try {
            logger.d("AutoSaveSession", "执行立即保存: $sessionId")
            autoSaveManager?.saveNow() ?: Result.failure(IllegalStateException("AutoSaveManager未初始化"))
        } catch (e: Exception) {
            logger.e(e, "立即保存失败: $sessionId")
            Result.failure(e)
        }
    }

    /**
     * 暂停会话
     */
    fun pause() {
        if (!isStarted || isPaused) {
            return
        }

        logger.d("AutoSaveSession", "暂停自动保存会话: $sessionId")

        isPaused = true
        autoSaveManager?.pause()
        _sessionStatus.value = SessionStatus.PAUSED
    }

    /**
     * 恢复会话
     */
    fun resume() {
        if (!isStarted || !isPaused) {
            return
        }

        logger.d("AutoSaveSession", "恢复自动保存会话: $sessionId")

        isPaused = false
        autoSaveManager?.resume()
        _sessionStatus.value = SessionStatus.ACTIVE

        // 如果在暂停期间有数据更新，现在应用这些更新
        currentData?.let { data ->
            autoSaveManager?.update(data)
        }
    }

    /**
     * 停止会话
     */
    fun stop() {
        if (!isStarted) {
            return
        }

        logger.d("AutoSaveSession", "停止自动保存会话: $sessionId")

        _sessionStatus.value = SessionStatus.STOPPING

        try {
            autoSaveManager?.stop()

            isStarted = false
            isPaused = false
            currentData = null
            autoSaveManager = null

            _sessionStatus.value = SessionStatus.STOPPED
            _state.value = AutoSaveState()

            logger.d("AutoSaveSession", "自动保存会话已停止: $sessionId")
        } catch (e: Exception) {
            logger.e(e, "停止自动保存会话失败: $sessionId")
            _sessionStatus.value = SessionStatus.ERROR
        }
    }

    /**
     * 恢复缓存数据
     */
    fun restoreFromCache() {
        if (!isStarted) {
            logger.w("AutoSaveSession", "会话未启动，无法恢复缓存: $sessionId")
            return
        }

        logger.d("AutoSaveSession", "恢复缓存数据: $sessionId")
        autoSaveManager?.restoreFromCache()
    }

    /**
     * 丢弃缓存数据
     */
    fun discardCache() {
        if (!isStarted) {
            logger.w("AutoSaveSession", "会话未启动，无法丢弃缓存: $sessionId")
            return
        }

        logger.d("AutoSaveSession", "丢弃缓存数据: $sessionId")
        autoSaveManager?.discardCache()
    }

    /**
     * 清除所有数据
     */
    suspend fun clear() {
        if (!isStarted) {
            logger.w("AutoSaveSession", "会话未启动，无法清除数据: $sessionId")
            return
        }

        try {
            logger.d("AutoSaveSession", "清除会话数据: $sessionId")
            autoSaveManager?.clear()
        } catch (e: Exception) {
            logger.e(e, "清除会话数据失败: $sessionId")
            throw e
        }
    }

    /**
     * 获取会话信息
     */
    fun getSessionInfo(): SessionInfo =
        SessionInfo(
            sessionId = sessionId,
            configId = config.id,
            isStarted = isStarted,
            isPaused = isPaused,
            status = _sessionStatus.value,
            dataType = currentData?.javaClass?.simpleName ?: "Unknown",
            lastUpdateTime = _state.value.lastUpdateTime,
            lastSaveTime = _state.value.lastSaveTime,
            saveCount = _state.value.saveCount,
        )

    /**
     * 更新状态
     */
    private fun updateState(update: (AutoSaveState<T>) -> AutoSaveState<T>) {
        _state.value = update(_state.value)
    }

    /**
     * 会话信息数据类
     */
    data class SessionInfo(
        val sessionId: String,
        val configId: String,
        val isStarted: Boolean,
        val isPaused: Boolean,
        val status: SessionStatus,
        val dataType: String,
        val lastUpdateTime: Long,
        val lastSaveTime: Long,
        val saveCount: Int,
    )

    companion object {
        /**
         * 创建AutoSaveSession实例
         */
        fun <T : Any> create(
            config: AutoSaveConfig<T>,
            scope: CoroutineScope,
            logger: Logger,
            sessionId: String = UUID.randomUUID().toString(),
        ): AutoSaveSession<T> = AutoSaveSession(sessionId, config, scope, logger)
    }
}
