package com.example.gymbro.data.autosave.internal

import com.example.gymbro.core.logging.Logger
import kotlinx.serialization.json.Json
import javax.inject.Inject
import javax.inject.Singleton

/**
 * JSON序列化器
 *
 * 🎯 功能特性：
 * - 封装Json序列化操作
 * - 提供类型安全的泛型API
 * - 统一的错误处理
 * - 支持自定义序列化配置
 *
 * @param json Json实例
 * @param logger 日志记录器
 */
@Singleton
class JsonSerializer
@Inject
constructor(
    private val json: Json,
    private val logger: Logger,
) {
    /**
     * 序列化对象为JSON字符串
     *
     * @param data 要序列化的数据
     * @return JSON字符串
     * @throws Exception 序列化失败时抛出异常
     */
    fun <T : Any> serialize(
        data: T,
        serializer: (T) -> String,
    ): String =
        try {
            val jsonString = serializer(data)
            logger.d("JsonSerializer", "序列化成功: ${data::class.simpleName}, 长度: ${jsonString.length}")
            jsonString
        } catch (e: Exception) {
            logger.e(e, "序列化失败: ${data::class.simpleName}")
            throw e
        }

    /**
     * 反序列化JSON字符串为对象
     *
     * @param jsonString JSON字符串
     * @return 反序列化的对象，失败时返回null
     */
    fun <T : Any> deserialize(
        jsonString: String,
        deserializer: (String) -> T?,
        typeName: String = "Unknown",
    ): T? {
        return try {
            if (jsonString.isBlank()) {
                logger.d("JsonSerializer", "JSON字符串为空: $typeName")
                return null
            }

            val data = deserializer(jsonString)
            logger.d("JsonSerializer", "反序列化成功: $typeName")
            data
        } catch (e: Exception) {
            logger.e(e, "反序列化失败: $typeName, JSON: ${jsonString.take(100)}")
            null
        }
    }

    /**
     * 安全序列化（不抛出异常）
     *
     * @param data 要序列化的数据
     * @return JSON字符串，失败时返回null
     */
    fun <T : Any> serializeSafe(
        data: T,
        serializer: (T) -> String,
    ): String? =
        try {
            serialize(data, serializer)
        } catch (e: Exception) {
            logger.e(e, "安全序列化失败: ${data::class.simpleName}")
            null
        }

    /**
     * 验证JSON字符串格式
     *
     * @param jsonString JSON字符串
     * @return 是否为有效的JSON格式
     */
    fun isValidJson(jsonString: String): Boolean {
        return try {
            if (jsonString.isBlank()) return false

            // 尝试解析为JsonElement来验证格式
            json.parseToJsonElement(jsonString)
            logger.d("JsonSerializer", "JSON格式验证成功")
            true
        } catch (e: Exception) {
            logger.d("JsonSerializer", "JSON格式验证失败: ${e.message}")
            false
        }
    }

    /**
     * 获取JSON字符串的大小（字节）
     *
     * @param jsonString JSON字符串
     * @return 字节大小
     */
    fun getJsonSize(jsonString: String): Long =
        try {
            val size = jsonString.toByteArray(Charsets.UTF_8).size.toLong()
            logger.d("JsonSerializer", "JSON大小: $size 字节")
            size
        } catch (e: Exception) {
            logger.e(e, "计算JSON大小失败")
            0L
        }

    // 🔥 【性能优化】缓存Json实例，避免重复创建
    private val compactJsonInstance by lazy {
        Json { prettyPrint = false }
    }

    private val prettyJsonInstance by lazy {
        Json { prettyPrint = true }
    }

    /**
     * 压缩JSON字符串（移除空白字符）
     *
     * @param jsonString JSON字符串
     * @return 压缩后的JSON字符串
     */
    fun compactJson(jsonString: String): String =
        try {
            // 解析后重新编码，自动移除空白字符
            val element = json.parseToJsonElement(jsonString)
            val compactJson = compactJsonInstance.encodeToString(element)

            logger.d("JsonSerializer", "JSON压缩完成: ${jsonString.length} -> ${compactJson.length}")
            compactJson
        } catch (e: Exception) {
            logger.e(e, "JSON压缩失败")
            jsonString
        }

    /**
     * 格式化JSON字符串（添加缩进）
     *
     * @param jsonString JSON字符串
     * @return 格式化后的JSON字符串
     */
    fun prettyJson(jsonString: String): String =
        try {
            // 解析后重新编码，添加格式化
            val element = json.parseToJsonElement(jsonString)
            val prettyJson = prettyJsonInstance.encodeToString(element)

            logger.d("JsonSerializer", "JSON格式化完成")
            prettyJson
        } catch (e: Exception) {
            logger.e(e, "JSON格式化失败")
            jsonString
        }

    companion object {
        /**
         * 创建JsonSerializer实例
         */
        fun create(
            json: Json,
            logger: Logger,
        ): JsonSerializer = JsonSerializer(json, logger)

        /**
         * 创建带有默认配置的JsonSerializer实例
         */
        fun createDefault(logger: Logger): JsonSerializer {
            val json =
                Json {
                    prettyPrint = false
                    ignoreUnknownKeys = true
                    coerceInputValues = true
                    encodeDefaults = true
                    isLenient = true
                }
            return JsonSerializer(json, logger)
        }
    }
}
