package com.example.gymbro.core.ml.impl

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.features.FeatureErrors
import com.example.gymbro.core.ml.interfaces.VectorizedWorkoutState
import com.example.gymbro.core.ml.interfaces.VectorizerInfo
import com.example.gymbro.core.ml.interfaces.WorkoutStateVectorizer
import com.example.gymbro.core.ml.service.BgeEmbeddingService
import com.example.gymbro.core.ui.text.UiText
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 基于BGE的训练状态向量化器实现 - Core-ML Layer
 *
 * 使用BGE嵌入服务将训练状态描述转换为语义向量
 * 纯core-ml层实现，不依赖任何上层模块
 */
@Singleton
class BgeWorkoutStateVectorizer @Inject constructor(
    private val bgeEmbeddingService: BgeEmbeddingService,
) : WorkoutStateVectorizer {

    companion object {
        private const val VECTORIZER_NAME = "BGE-WorkoutState"
        private const val VECTORIZER_VERSION = "1.0.0"
        private const val MAX_STATE_TEXT_LENGTH = 512
    }

    override suspend fun vectorizeWorkoutState(stateDescription: String): ModernResult<VectorizedWorkoutState> {
        return try {
            if (stateDescription.isBlank()) {
                return ModernResult.Error(
                    FeatureErrors.CoachError.invalidInput(
                        operationName = "BgeWorkoutStateVectorizer.vectorizeWorkoutState",
                        message = UiText.DynamicString("训练状态描述不能为空"),
                        inputType = "empty_state_description",
                        value = stateDescription,
                    ),
                )
            }

            // 预处理状态描述
            val processedText = preprocessStateDescription(stateDescription)

            // 调用BGE嵌入服务
            val embeddingResult = bgeEmbeddingService.embedText(processedText)

            when (embeddingResult) {
                is ModernResult.Success -> {
                    val vector = embeddingResult.data
                    val vectorizedState = VectorizedWorkoutState(
                        originalText = stateDescription,
                        vector = vector,
                        vectorDim = vector.size,
                        timestamp = System.currentTimeMillis(),
                        metadata = mapOf(
                            "preprocessed_text" to processedText,
                            "text_length" to stateDescription.length,
                            "vector_norm" to calculateVectorNorm(vector),
                        ),
                    )

                    Timber.d(
                        "BgeWorkoutStateVectorizer: 训练状态向量化完成 dim=${vector.size}, text='${processedText.take(
                            50,
                        )}...'",
                    )
                    ModernResult.Success(vectorizedState)
                }
                is ModernResult.Error -> {
                    Timber.w("BgeWorkoutStateVectorizer: BGE嵌入服务失败")
                    embeddingResult.copy()
                }
                is ModernResult.Loading -> {
                    Timber.d("BgeWorkoutStateVectorizer: BGE嵌入服务加载中")
                    ModernResult.Loading
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "BgeWorkoutStateVectorizer: 训练状态向量化异常")
            ModernResult.Error(
                FeatureErrors.CoachError.processingFailed(
                    operationName = "BgeWorkoutStateVectorizer.vectorizeWorkoutState",
                    message = UiText.DynamicString("训练状态向量化失败"),
                    processType = "state_vectorization",
                    reason = "vectorization_exception",
                    cause = e,
                    metadataMap = mapOf(
                        "state_text_length" to stateDescription.length,
                    ),
                ),
            )
        }
    }

    override suspend fun vectorizeBatchStates(stateDescriptions: List<String>): ModernResult<List<VectorizedWorkoutState>> {
        return try {
            if (stateDescriptions.isEmpty()) {
                return ModernResult.Error(
                    FeatureErrors.CoachError.invalidInput(
                        operationName = "BgeWorkoutStateVectorizer.vectorizeBatchStates",
                        message = UiText.DynamicString("状态描述列表不能为空"),
                        inputType = "empty_state_list",
                        value = "size=0",
                    ),
                )
            }

            // 预处理所有状态描述
            val processedTexts = stateDescriptions.map { preprocessStateDescription(it) }

            // 批量嵌入
            val embeddingResult = bgeEmbeddingService.embedTexts(processedTexts)

            when (embeddingResult) {
                is ModernResult.Success -> {
                    val vectors = embeddingResult.data
                    val vectorizedStates = stateDescriptions.mapIndexed { index, originalText ->
                        val vector = vectors[index]
                        VectorizedWorkoutState(
                            originalText = originalText,
                            vector = vector,
                            vectorDim = vector.size,
                            timestamp = System.currentTimeMillis(),
                            metadata = mapOf(
                                "preprocessed_text" to processedTexts[index],
                                "batch_index" to index,
                                "vector_norm" to calculateVectorNorm(vector),
                            ),
                        )
                    }

                    Timber.d("BgeWorkoutStateVectorizer: 批量训练状态向量化完成 count=${vectorizedStates.size}")
                    ModernResult.Success(vectorizedStates)
                }
                is ModernResult.Error -> {
                    Timber.w("BgeWorkoutStateVectorizer: 批量BGE嵌入失败")
                    embeddingResult.copy()
                }
                is ModernResult.Loading -> {
                    Timber.d("BgeWorkoutStateVectorizer: 批量BGE嵌入服务加载中")
                    ModernResult.Loading
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "BgeWorkoutStateVectorizer: 批量训练状态向量化异常")
            ModernResult.Error(
                FeatureErrors.CoachError.processingFailed(
                    operationName = "BgeWorkoutStateVectorizer.vectorizeBatchStates",
                    message = UiText.DynamicString("批量训练状态向量化失败"),
                    processType = "batch_state_vectorization",
                    reason = "batch_vectorization_exception",
                    cause = e,
                    metadataMap = mapOf(
                        "batch_size" to stateDescriptions.size,
                    ),
                ),
            )
        }
    }

    override fun calculateStateSimilarity(state1: VectorizedWorkoutState, state2: VectorizedWorkoutState): Float {
        return try {
            if (state1.vectorDim != state2.vectorDim) {
                Timber.w("BgeWorkoutStateVectorizer: 向量维度不匹配 ${state1.vectorDim} vs ${state2.vectorDim}")
                return 0.0f
            }

            val similarity = cosineSimilarity(state1.vector, state2.vector)
            Timber.d("BgeWorkoutStateVectorizer: 计算相似度 = $similarity")
            similarity
        } catch (e: Exception) {
            Timber.e(e, "BgeWorkoutStateVectorizer: 计算相似度异常")
            0.0f
        }
    }

    override fun getVectorizerInfo(): VectorizerInfo {
        val stats = bgeEmbeddingService.getPerformanceStats()
        return VectorizerInfo(
            name = VECTORIZER_NAME,
            version = VECTORIZER_VERSION,
            vectorDimension = stats.embeddingDim,
            maxTextLength = MAX_STATE_TEXT_LENGTH,
            isInitialized = stats.isInitialized && stats.isWarmedUp,
        )
    }

    /**
     * 预处理训练状态描述
     * 规范化文本格式，提取关键信息
     */
    private fun preprocessStateDescription(stateDescription: String): String {
        return stateDescription
            .trim()
            .take(MAX_STATE_TEXT_LENGTH) // 限制长度
            .replace(Regex("\\s+"), " ") // 标准化空格
            .let { text ->
                // 添加训练上下文标识
                "训练状态: $text"
            }
    }

    /**
     * 计算向量的L2范数
     */
    private fun calculateVectorNorm(vector: FloatArray): Float {
        return kotlin.math.sqrt(vector.sumOf { (it * it).toDouble() }).toFloat()
    }

    /**
     * 计算余弦相似度
     */
    private fun cosineSimilarity(vector1: FloatArray, vector2: FloatArray): Float {
        require(vector1.size == vector2.size) { "向量维度必须相同" }

        var dotProduct = 0.0f
        var norm1 = 0.0f
        var norm2 = 0.0f

        for (i in vector1.indices) {
            dotProduct += vector1[i] * vector2[i]
            norm1 += vector1[i] * vector1[i]
            norm2 += vector2[i] * vector2[i]
        }

        norm1 = kotlin.math.sqrt(norm1)
        norm2 = kotlin.math.sqrt(norm2)

        return if (norm1 == 0.0f || norm2 == 0.0f) {
            0.0f
        } else {
            dotProduct / (norm1 * norm2)
        }
    }
}
