package com.example.gymbro.designSystem.components.animations

import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.components.extensions.gymBroBreathing
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.motion.MotionDurations

/**
 * GymBro Logo组件 - 使用统一动画系统
 *
 * 特性：
 * - 统一的呼吸动画效果
 * - 支持可选副标题
 * - 动画配置可控制
 * - 响应系统动画缩放设置
 *
 * @param title 主标题文本
 * @param subtitle 副标题文本（可选）
 * @param modifier Modifier修饰符
 * @param titleColor 主标题颜色
 * @param subtitleColor 副标题颜色
 * @param style 文本样式
 * @param enableAnimation 是否启用呼吸动画
 */
@Composable
fun GymBroLogo(
    title: UiText,
    subtitle: UiText? = null,
    modifier: Modifier = Modifier,
    titleColor: Color = MaterialTheme.colorScheme.onBackground,
    subtitleColor: Color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.8f),
    style: TextStyle =
        MaterialTheme.typography.displaySmall.copy(
            fontWeight = FontWeight.Bold,
        ),
    enableAnimation: Boolean = true,
) {
    val displayText =
        if (subtitle != null) {
            "${title.asString()}\n${subtitle.asString()}"
        } else {
            title.asString()
        }

    val animationModifier =
        if (enableAnimation) {
            Modifier.gymBroBreathing(
                amplitude = 16.dp, // BREATHING_AMPLITUDE_MEDIUM
                duration = MotionDurations.L,
            )
        } else {
            Modifier
        }

    Text(
        text = displayText,
        textAlign = TextAlign.Center,
        color = titleColor,
        style = style,
        lineHeight = style.fontSize * 1.1f,
        modifier = modifier.then(animationModifier),
    )
}

/**
 * 简化版GymBro Logo，只显示主标题
 *
 * @param title 标题文本
 * @param modifier Modifier修饰符
 * @param titleColor 标题颜色
 * @param style 文本样式
 * @param enableAnimation 是否启用动画
 */
@Composable
fun GymBroLogoSimple(
    title: UiText,
    modifier: Modifier = Modifier,
    titleColor: Color = MaterialTheme.colorScheme.onBackground,
    style: TextStyle =
        MaterialTheme.typography.displaySmall.copy(
            fontWeight = FontWeight.Bold,
        ),
    enableAnimation: Boolean = true,
) {
    GymBroLogo(
        title = title,
        subtitle = null,
        modifier = modifier,
        titleColor = titleColor,
        style = style,
        enableAnimation = enableAnimation,
    )
}

// === Preview组件 ===

@GymBroPreview
@Composable
private fun GymBroLogoPreview() {
    GymBroTheme {
        GymBroLogo(
            title = UiText.DynamicString("GymBro"),
            subtitle = UiText.DynamicString("Android Beta"),
            titleColor = Color.White,
            subtitleColor = Color.White.copy(alpha = 0.8f),
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroLogoSimplePreview() {
    GymBroTheme {
        GymBroLogoSimple(
            title = UiText.DynamicString("GymBro"),
            titleColor = Color.Black,
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroLogoNoAnimationPreview() {
    GymBroTheme {
        GymBroLogo(
            title = UiText.DynamicString("GymBro"),
            subtitle = UiText.DynamicString("Static Mode"),
            enableAnimation = false,
        )
    }
}
