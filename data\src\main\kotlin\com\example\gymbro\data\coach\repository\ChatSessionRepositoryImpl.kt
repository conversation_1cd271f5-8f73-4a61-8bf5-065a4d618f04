package com.example.gymbro.data.coach.repository

import android.content.Context
import androidx.room.Transaction
import com.example.gymbro.core.ai.prompt.builder.CoreChatMessage
import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.coach.session.mapper.ChatSessionMapper.toDomainMessage
import com.example.gymbro.data.coach.dao.ChatRawDao
import com.example.gymbro.data.coach.dao.ChatSessionDao
import com.example.gymbro.data.local.entity.ChatRaw
import com.example.gymbro.data.local.entity.ChatSessionEntity
import com.example.gymbro.domain.autosave.ChatHistoryAutoSaveService
import com.example.gymbro.domain.coach.model.ChatSession
import com.example.gymbro.domain.coach.model.CoachMessage
import com.example.gymbro.domain.coach.repository.ChatRepository
import com.example.gymbro.domain.title.GenerateSessionTitleUseCase
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import timber.log.Timber
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton
import com.example.gymbro.data.local.entity.ChatSessionEntity as LocalChatSessionEntity

/**
 * 聊天会话仓库实现
 *
 * 实现聊天会话的数据访问操作，包括会话管理和消息管理
 * 🔥 修复：移除AutoSave双重写入，ROOM作为单一数据源
 * 🔥 修复：使用GenerateSessionTitleUseCase避免循环依赖
 */
@Singleton
class ChatSessionRepositoryImpl
    @Inject
    constructor(
        private val chatSessionDao: ChatSessionDao,
        private val chatRawDao: ChatRawDao,
        private val generateSessionTitleUseCase: GenerateSessionTitleUseCase,
        private val chatHistoryAutoSaveService: ChatHistoryAutoSaveService,
        @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
        @ApplicationContext private val context: Context,
        private val json: Json,
    ) : ChatRepository {
        // 🔥 修复：恢复AutoSave支持，按照HISTORY_README.md文档要求
        private val scope = CoroutineScope(ioDispatcher + SupervisorJob())

        override suspend fun createSession(
            userId: String,
            title: String?,
        ): ModernResult<ChatSession> =
            withContext(ioDispatcher) {
                try {
                    val session =
                        ChatSession(
                            title = title ?: "新对话",
                            userId = userId,
                        )
                    val entity = ChatSessionEntity.fromDomain(session)
                    chatSessionDao.insertSession(entity)

                    // 🔥 修复：移除AutoSave初始化，简化会话创建流程
                    Timber.d("会话创建成功: sessionId=${session.id}")
                    ModernResult.Success(session)
                } catch (e: Exception) {
                    Timber.e(e, "创建会话失败")
                    ModernResult.Error(
                        DataErrors.DataError.access(
                            operationName = "ChatSessionRepositoryImpl.createSession",
                            message = UiText.DynamicString("创建会话失败"),
                            entityType = "ChatSession",
                            cause = e,
                        ),
                    )
                }
            }

        // 🔥 修复：移除AutoSave相关方法，简化代码结构

        override suspend fun getSession(sessionId: String): ModernResult<ChatSession?> =
            withContext(ioDispatcher) {
                try {
                    val sessionEntity =
                        chatSessionDao.getSessionById(sessionId)
                            ?: return@withContext ModernResult.Success(null)

                    val messages = chatRawDao.getChatMessagesBySession(sessionId).map { it.toDomainMessage() }
                    val session = sessionEntity.toDomain(messages)
                    Timber.d("会话加载成功: sessionId=$sessionId, messageCount=${messages.size}")
                    ModernResult.Success(session)
                } catch (e: Exception) {
                    Timber.e(e, "获取会话失败")
                    ModernResult.Error(
                        DataErrors.DataError.access(
                            operationName = "ChatSessionRepositoryImpl.getSessionById",
                            message = UiText.DynamicString("获取会话失败"),
                            entityType = "ChatSession",
                            cause = e,
                        ),
                    )
                }
            }

        override fun getUserSessions(
            userId: String,
            limit: Int,
            offset: Int,
        ): Flow<ModernResult<List<ChatSession>>> =
            flow {
                emit(ModernResult.Loading)

                chatSessionDao
                    .getUserSessions(userId, false, limit, offset)
                    .map { entities ->
                        entities.map { entity ->
                            // 不包含消息列表以提高性能
                            entity.toDomain(emptyList())
                        }
                    }.catch { e ->
                        Timber.e(e, "获取用户会话失败")
                        emit(
                            ModernResult.Error(
                                DataErrors.DataError.access(
                                    operationName = "ChatSessionRepositoryImpl.getUserSessions",
                                    message = UiText.DynamicString("获取用户会话失败"),
                                    entityType = "ChatSession",
                                    cause = e,
                                ),
                            ),
                        )
                    }.collect { sessions ->
                        emit(ModernResult.Success(sessions))
                    }
            }

        override fun observeLatestSession(userId: String): Flow<ChatSession?> =
            chatSessionDao
                .observeLatestSession(userId)
                .map { sessionEntity ->
                    sessionEntity?.let { entity ->
                        // 获取该会话的消息
                        val messages =
                            chatRawDao
                            .getChatMessagesBySession(entity.id)
                            .map { it.toDomainMessage() }
                    entity.toDomain(messages)
                }
            }.catch { e ->
                Timber.e(e, "观察最新会话失败")
                // 发生错误时返回null，而不是抛出异常，保证Flow的连续性
                emit(null)
            }.flowOn(ioDispatcher)

    override suspend fun updateSession(
        sessionId: String,
        title: String,
    ): ModernResult<Unit> =
        withContext(ioDispatcher) {
            try {
                chatSessionDao.updateSessionTitle(sessionId, title)
                Timber.d("会话更新成功: sessionId=$sessionId")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "更新会话失败")
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "ChatSessionRepositoryImpl.updateSession",
                        message = UiText.DynamicString("更新会话失败"),
                        entityType = "ChatSession",
                        cause = e,
                    ),
                )
            }
        }

    override suspend fun updateSessionSummary(
        sessionId: String,
        summary: String,
    ): ModernResult<Unit> =
        withContext(ioDispatcher) {
            try {
                chatSessionDao.updateSessionSummary(sessionId, summary)
                Timber.d("会话概要更新成功: sessionId=$sessionId, summary=$summary")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "更新会话概要失败")
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "ChatSessionRepositoryImpl.updateSessionSummary",
                        message = UiText.DynamicString("更新会话概要失败"),
                        entityType = "ChatSession",
                        cause = e,
                    ),
                )
            }
        }

    @Transaction
    override suspend fun addMessage(
        sessionId: String,
        message: CoachMessage,
    ): ModernResult<Unit> =
        withContext(ioDispatcher) {
            try {
                Timber.d("🚀 [DEBUG] addMessage 开始: sessionId=$sessionId, messageId=${message.id}, messageType=${message::class.simpleName}")
                val messageContent =
                    when (message) {
                        is CoachMessage.UserMessage -> message.content
                        is CoachMessage.AiMessage -> message.content
                    }
                Timber.d("🚀 [DEBUG] 消息内容: ${messageContent.take(100)}${if (messageContent.length > 100) "..." else ""}")
                // 🔥 验证会话存在性，如果不存在则自动创建
                val sessionExists = chatSessionDao.getSessionById(sessionId) != null
                Timber.d("🔍 [DEBUG] 会话存在性检查: sessionId=$sessionId, exists=$sessionExists")

                if (!sessionExists) {
                    Timber.w("🆕 [DEBUG] 会话不存在，自动创建会话: sessionId=$sessionId")

                    // 🔥 自动创建会话（第一条消息场景）
                    val newSession =
                        LocalChatSessionEntity(
                            id = sessionId,
                            title =
                                when (message) {
                                    is CoachMessage.UserMessage -> message.content.take(50) + if (message.content.length > 50) "..." else ""
                                    else -> "新对话"
                                },
                            userId = "default_user", // TODO: 从上下文获取真实用户ID
                            createdAt = System.currentTimeMillis(),
                            lastActiveAt = message.timestamp,
                            messageCount = 0,
                            // 🔥 修复：移除 dbUpdatedAt 参数，使用默认值
                            // dbUpdatedAt = System.currentTimeMillis()
                        )

                    try {
                        val insertResult = chatSessionDao.insertSession(newSession)
                        Timber.d("✅ [DEBUG] 会话自动创建成功: sessionId=$sessionId, insertResult=$insertResult")
                        Timber.d("✅ [DEBUG] 新会话详情: title=${newSession.title}, userId=${newSession.userId}")

                        // 🔍 验证会话是否真的创建了
                        val verifySession = chatSessionDao.getSessionById(sessionId)
                        if (verifySession != null) {
                            Timber.d("✅ [DEBUG] 会话创建验证成功: sessionId=$sessionId")
                        } else {
                            Timber.e("❌ [DEBUG] 会话创建验证失败: 无法找到刚创建的会话")
                        }
                    } catch (e: Exception) {
                        Timber.e(e, "❌ [DEBUG] 会话自动创建失败: sessionId=$sessionId")
                        Timber.e("❌ [DEBUG] 异常详情: ${e.message}")
                        if (e.message?.contains("FOREIGN KEY") == true) {
                            Timber.e("❌ [DEBUG] 外键约束错误，可能是数据库结构问题")
                        }
                        return@withContext ModernResult.Error(
                            DataErrors.DataError.access(
                                operationName = "ChatSessionRepositoryImpl.addMessage",
                                message = UiText.DynamicString("创建会话失败: ${e.message}"),
                                entityType = "ChatSession",
                                cause = e,
                            ),
                        )
                    }
                }

                // 🚀 主路径：立即写入ROOM数据库（同步）
                val chatRaw = message.toChatRaw(sessionId)
                Timber.d("💾 [DEBUG] 准备写入ChatRaw: sessionId=$sessionId, role=${chatRaw.role}, messageId=${chatRaw.messageId}")
                Timber.d("💾 [DEBUG] ChatRaw内容: ${chatRaw.content.take(100)}${if (chatRaw.content.length > 100) "..." else ""}")

                val insertedId =
                    try {
                        chatRawDao.insertChatMessage(chatRaw)
                    } catch (e: Exception) {
                        Timber.e(
                            e,
                            "❌ [DEBUG] ChatRaw插入异常: sessionId=$sessionId, messageId=${chatRaw.messageId}",
                        )
                        Timber.e("❌ [DEBUG] 异常详情: ${e.message}")
                        if (e.message?.contains("FOREIGN KEY") == true) {
                            Timber.e("❌ [DEBUG] 外键约束失败: session_id=$sessionId 在 chat_sessions 表中不存在")
                            // 再次检查会话是否存在
                            val sessionCheck = chatSessionDao.getSessionById(sessionId)
                            Timber.e("❌ [DEBUG] 会话存在性二次检查: sessionId=$sessionId, exists=${sessionCheck != null}")
                        }
                        throw e
                    }
                Timber.d("💾 [DEBUG] ChatRaw写入结果: insertedId=$insertedId")

                // 验证主路径成功
                if (insertedId == -1L) {
                    Timber.e("❌ [DEBUG] 主路径ROOM写入失败 – messageId=${message.id}")
                    Timber.e("❌ [DEBUG] 失败的ChatRaw详情: id=${chatRaw.id}, sessionId=${chatRaw.sessionId}, role=${chatRaw.role}")
                    error("主路径ROOM写入失败 – messageId=${message.id}")
                } else {
                    Timber.d("✅ [DEBUG] 主路径ROOM写入成功: insertedId=$insertedId")
                    Timber.d("✅ [DEBUG] 成功的ChatRaw详情: id=${chatRaw.id}, sessionId=${chatRaw.sessionId}, messageId=${chatRaw.messageId}")
                }

                // 🔥 修复：恢复AutoSave调用，按照HISTORY_README.md文档要求
                // 主路径：立即写入ROOM数据库，辅助路径：异步触发AutoSave备份
                scope.launch {
                    chatHistoryAutoSaveService.saveMessage(sessionId, message)
                }
                Timber.d("💾 [DEBUG] 消息已保存到ROOM，并触发AutoSave备份: sessionId=$sessionId, messageId=${message.id}")

                // 🔥 使用单一SQL更新会话信息
                chatSessionDao.updateSessionOnNewMessage(sessionId, message.timestamp)
                Timber.d("🔄 [DEBUG] 会话信息更新完成: sessionId=$sessionId, timestamp=${message.timestamp}")

                // 🔍 验证消息是否真的保存了
                Timber.d("🔍 [DEBUG] 开始验证消息保存: insertedId=$insertedId, chatRaw.messageId=${chatRaw.messageId}")
                val savedMessage = chatRawDao.getChatMessageById(insertedId)
                if (savedMessage != null) {
                    Timber.d(
                        "✅ [DEBUG] 消息保存验证成功: messageId=${savedMessage.messageId}, savedContent=${
                            savedMessage.content.take(
                                50
                            )
                        }..."
                    )
                } else {
                    Timber.e("❌ [DEBUG] 消息保存验证失败: 无法找到已保存的消息")
                    Timber.e("❌ [DEBUG] 查询参数: insertedId=$insertedId, chatRaw.messageId=${chatRaw.messageId}")

                    // 🔍 检查该会话的所有消息
                    val allMessages = chatRawDao.getChatMessagesBySession(sessionId)
                    Timber.e("❌ [DEBUG] 该会话所有消息数量: ${allMessages.size}")
                    allMessages.forEachIndexed { index, msg ->
                        Timber.e(
                            "❌ [DEBUG] 消息[$index]: id=${msg.id}, messageId=${msg.messageId}, content=${
                                msg.content.take(
                                    30
                                )
                            }..."
                        )
                    }
                }

                // 🔍 检查会话中的消息总数
                val totalMessages = chatRawDao.getChatMessageCountBySession(sessionId)
                Timber.d("📊 [DEBUG] 会话消息总数: sessionId=$sessionId, count=$totalMessages")

                // 🔥 修复：自动标题生成功能 - 避免循环依赖
                // 如果这是第一条用户消息，则触发标题生成
                if (message is CoachMessage.UserMessage && totalMessages == 1) {
                    scope.launch {
                        try {
                            Timber.d("🏷️ [标题生成] 检测到第一条用户消息，开始生成标题: sessionId=$sessionId")

                            // 使用GenerateSessionTitleUseCase生成标题（避免循环依赖）
                            val generatedTitle = generateSessionTitleUseCase(message.content)
                            Timber.d("🏷️ [标题生成] 标题生成成功: $generatedTitle")

                            // 直接更新会话标题到数据库
                            val updateResult = updateSession(sessionId, generatedTitle)
                            when (updateResult) {
                                is ModernResult.Success -> {
                                    Timber.d("🏷️ [标题生成] 标题更新到数据库成功")
                                }

                                is ModernResult.Error -> {
                                    Timber.w("🏷️ [标题生成] 标题更新到数据库失败: ${updateResult.error}")
                                }

                                is ModernResult.Loading -> {
                                    Timber.d("🏷️ [标题生成] 标题更新中...")
                                }
                            }
                        } catch (e: Exception) {
                            Timber.w(e, "🏷️ [标题生成] 标题生成异常，但不影响消息保存")
                        }
                    }
                }

                Timber.d("🎉 [DEBUG] addMessage 完成: sessionId=$sessionId, messageId=${message.id}, dbId=$insertedId")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "添加消息失败，启用备份机制")

                // P0-2修复：历史消息落库兜底机制（恢复备份机制）
                try {
                    writeMessageToBackupFile(sessionId, message)
                    Timber.w("消息已写入备份文件: sessionId=$sessionId, messageId=${message.id}")

                    // 返回成功，但记录这是兜底操作
                    ModernResult.Success(Unit)
                } catch (backupException: Exception) {
                    Timber.e(backupException, "备份文件写入也失败")
                    ModernResult.Error(
                        DataErrors.DataError.access(
                            operationName = "ChatSessionRepositoryImpl.addMessageToSession",
                            message = UiText.DynamicString("添加消息失败"),
                            entityType = "CoachMessage",
                            cause = e,
                        ),
                    )
                }
            }
        }

    override fun getMessages(
        sessionId: String,
        limit: Int,
        offset: Int,
    ): Flow<ModernResult<List<CoachMessage>>> =
        flow {
            emit(ModernResult.Loading)

            try {
                val chatRaws = chatRawDao.getMessagesBySessionPaged(sessionId, limit, offset)
                val messages = chatRaws.map { it.toDomainMessage() }
                emit(ModernResult.Success(messages))
            } catch (e: Exception) {
                Timber.e(e, "获取会话消息失败")
                emit(
                    ModernResult.Error(
                        DataErrors.DataError.access(
                            operationName = "ChatSessionRepositoryImpl.getSessionMessages",
                            message = UiText.DynamicString("获取会话消息失败"),
                            entityType = "CoachMessage",
                            cause = e,
                        ),
                    ),
                )
            }
        }

    override suspend fun clearMessages(sessionId: String): ModernResult<Unit> =
        withContext(ioDispatcher) {
            try {
                chatRawDao.deleteChatMessagesBySession(sessionId)
                chatSessionDao.updateMessageCount(sessionId, 0)
                Timber.d("会话消息清空成功: sessionId=$sessionId")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "清空会话消息失败")
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "ChatSessionRepositoryImpl.clearMessages",
                        message = UiText.DynamicString("清空会话消息失败"),
                        entityType = "CoachMessage",
                        cause = e,
                    ),
                )
            }
        }

    // ===== Task2-CoachContext数据中心集成：RAG相关方法实现 =====

    override suspend fun findSimilarMessages(
        queryVector: FloatArray,
        k: Int,
        sessionId: String?,
    ): ModernResult<List<CoachMessage>> =
        withContext(ioDispatcher) {
            try {
                Timber.d("🔍 开始向量搜索消息: queryVector.size=${queryVector.size}, k=$k, sessionId=$sessionId")

                // 1. 将查询向量转换为ByteArray
                val queryVectorBytes =
                    com.example.gymbro.core.ml.utils.VectorUtils
                        .floatArrayToByteArray(queryVector)

                // 2. 从数据库获取所有有向量的消息
                val allVectorMessages =
                    if (sessionId != null) {
                        chatRawDao.getChatMessagesWithVectorsBySession(sessionId)
                    } else {
                        chatRawDao.getAllChatMessagesWithVectors()
                    }

                if (allVectorMessages.isEmpty()) {
                    Timber.d("📭 没有找到有向量的消息")
                    return@withContext ModernResult.Success(emptyList())
                }

                // 3. 计算相似度并排序
                val similarities =
                    allVectorMessages
                        .mapNotNull { chatRawWithVector ->
                            try {
                                val messageVector =
                                    com.example.gymbro.core.ml.utils.VectorUtils
                                        .byteArrayToFloatArray(chatRawWithVector.vector!!)
                                val similarity =
                                    com.example.gymbro.core.ml.utils.VectorUtils
                                        .cosineSimilarity(queryVector, messageVector)
                                chatRawWithVector to similarity
                            } catch (e: Exception) {
                                Timber.w(
                                    e,
                                    "计算消息向量相似度失败: messageId=${chatRawWithVector.messageId}"
                                )
                                null
                            }
                        }.sortedByDescending { it.second }
                        .take(k)

                // 4. 转换为Domain模型
                val similarMessages =
                    similarities.map { (chatRawWithVector, similarity) ->
                        chatRawWithVector.toChatRaw().toDomainMessage().also {
                            Timber.d("📊 相似消息: id=${it.id}, similarity=${"%.3f".format(similarity)}")
                        }
                    }

                Timber.d("✅ 向量搜索完成: 找到${similarMessages.size}条相似消息")
                ModernResult.Success(similarMessages)
            } catch (e: Exception) {
                Timber.e(e, "向量搜索消息失败")
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "ChatSessionRepositoryImpl.findSimilarMessages",
                        message = UiText.DynamicString("向量搜索消息失败"),
                        entityType = "CoachMessage",
                        cause = e,
                    ),
                )
            }
        }

    override suspend fun getRecentHistory(
        sessionId: String,
        n: Int,
    ): ModernResult<List<CoachMessage>> =
        withContext(ioDispatcher) {
            try {
                Timber.d("📚 [DEBUG] getRecentHistory 开始: sessionId=$sessionId, n=$n")

                // 🔍 先检查会话是否存在
                val sessionExists = chatSessionDao.getSessionById(sessionId) != null
                Timber.d("🔍 [DEBUG] 会话存在性检查: sessionId=$sessionId, exists=$sessionExists")

                // 🔥 实时从ROOM数据库获取最新的N条消息，按时间戳倒序
                val recentChatRaws = chatRawDao.getRecentChatMessagesBySession(sessionId, n)
                Timber.d("📊 [DEBUG] 从数据库获取到 ${recentChatRaws.size} 条原始消息")

                if (recentChatRaws.isEmpty()) {
                    Timber.w("📭 [DEBUG] 会话中没有历史消息: sessionId=$sessionId")

                    // 🔍 检查数据库中是否有任何消息
                    val totalMessages = chatRawDao.getChatMessageCountBySession(sessionId)
                    Timber.d("📊 [DEBUG] 数据库中该会话总消息数: $totalMessages")

                    return@withContext ModernResult.Success(emptyList())
                }

                // 转换为Domain模型，保持时间顺序（最新的在后面）
                val recentMessages =
                    recentChatRaws.reversed().mapIndexed { index, chatRaw ->
                        val domainMessage = chatRaw.toDomainMessage()
                        val messageContent =
                            when (domainMessage) {
                                is CoachMessage.UserMessage -> domainMessage.content
                                is CoachMessage.AiMessage -> domainMessage.content
                            }
                        Timber.d(
                            "📝 [DEBUG] 历史消息[$index]: id=${domainMessage.id}, role=${chatRaw.role}, " +
                                "timestamp=${chatRaw.timestamp}, content=${messageContent.take(50)}...",
                        )
                        domainMessage
                    }

                Timber.d("✅ [DEBUG] getRecentHistory 完成: 返回${recentMessages.size}条消息")

                // 🔍 详细记录每条消息的类型
                recentMessages.forEachIndexed { index, message ->
                    Timber.d("📋 [DEBUG] 返回消息[$index]: type=${message::class.simpleName}, id=${message.id}")
                }

                ModernResult.Success(recentMessages)
            } catch (e: Exception) {
                Timber.e(e, "获取最近历史失败")
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "ChatSessionRepositoryImpl.getRecentHistory",
                        message = UiText.DynamicString("获取最近历史失败"),
                        entityType = "CoachMessage",
                        cause = e,
                    ),
                )
            }
        }

    /**
     * 🔥 新增：实时构建Prompt上下文
     *
     * 从ROOM数据库实时获取最新对话历史，构建AI Prompt所需的上下文
     *
     * @param sessionId 会话ID
     * @param maxMessages 最大消息数量，默认20条
     * @return Prompt上下文消息列表
     */
    suspend fun buildRealtimePromptContext(
        sessionId: String,
        maxMessages: Int = 20,
    ): ModernResult<List<CoreChatMessage>> =
        withContext(ioDispatcher) {
            try {
                Timber.d("[ROOM版] 🚀 实时构建Prompt上下文: sessionId=$sessionId, maxMessages=$maxMessages")

                // 🔥 实时获取最新对话历史
                val recentChatRaws = chatRawDao.getRecentChatMessagesBySession(sessionId, maxMessages)

                if (recentChatRaws.isEmpty()) {
                    Timber.d("[ROOM版] 📭 无历史消息，返回空上下文")
                    return@withContext ModernResult.Success(emptyList())
                }

                // 🔥 转换为CoreChatMessage格式（AI Prompt标准格式）
                val promptMessages =
                    recentChatRaws.reversed().map { chatRaw ->
                        val role =
                            when (chatRaw.role) {
                                ChatRaw.ROLE_USER -> "user"
                                ChatRaw.ROLE_ASSISTANT -> "assistant"
                                "system" -> "system"
                                else -> "user" // 默认为用户消息
                            }

                        CoreChatMessage(
                            role = role,
                            content = chatRaw.content,
                        )
                    }

                Timber.d("[ROOM版] ✅ Prompt上下文构建成功: ${promptMessages.size}条消息")
                ModernResult.Success(promptMessages)
            } catch (e: Exception) {
                Timber.e(e, "[ROOM版] 构建Prompt上下文失败: sessionId=$sessionId")
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "ChatSessionRepositoryImpl.buildRealtimePromptContext",
                        message = UiText.DynamicString("构建Prompt上下文失败"),
                        entityType = "ChatMessage",
                        cause = e,
                    ),
                )
            }
        }

    override suspend fun indexAllMessages(): ModernResult<Int> =
        withContext(ioDispatcher) {
            try {
                Timber.d("🔄 开始索引所有消息")

                // 1. 获取所有没有向量的消息
                val messagesWithoutVectors = chatRawDao.getChatMessagesWithoutVectors()

                if (messagesWithoutVectors.isEmpty()) {
                    Timber.d("✅ 所有消息都已有向量索引")
                    return@withContext ModernResult.Success(0)
                }

                Timber.d("📝 找到${messagesWithoutVectors.size}条需要索引的消息")

                // 2. 批量向量化和存储
                var indexedCount = 0
                messagesWithoutVectors.forEach { chatRaw ->
                    try {
                        // 向量化消息内容
                        // TODO: 注入EmbeddingEngine
                        // val vector = embeddingEngine.embed(chatRaw.content)
                        // val vectorBytes = VectorUtils.floatArrayToByteArray(vector)

                        // 暂时跳过实际向量化，只记录日志
                        Timber.d("🧠 向量化消息: id=${chatRaw.id}, content=${chatRaw.content.take(50)}...")

                        // TODO: 保存向量到ChatVec表
                        // val chatVec = ChatVec(
                        //     id = chatRaw.id,
                        //     embedding = vectorBytes
                        // )
                        // chatVecDao.insertChatVec(chatVec)

                        indexedCount++
                    } catch (e: Exception) {
                        Timber.w(e, "向量化消息失败: id=${chatRaw.id}")
                    }
                }

                Timber.d("✅ 索引完成: 处理了${indexedCount}条消息")
                ModernResult.Success(indexedCount)
            } catch (e: Exception) {
                Timber.e(e, "索引所有消息失败")
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "ChatSessionRepositoryImpl.indexAllMessages",
                        message = UiText.DynamicString("索引所有消息失败"),
                        entityType = "CoachMessage",
                        cause = e,
                    ),
                )
            }
        }

    @Transaction
    override suspend fun saveAiMessage(
        sessionId: String,
        aiMessage: CoachMessage.AiMessage,
        inReplyToMessageId: String,
    ): ModernResult<Unit> =
        withContext(ioDispatcher) {
            try {
                Timber.d(
                    "[ROOM版] 💾 保存AI消息: sessionId=$sessionId, aiMessageId=${aiMessage.id}, inReplyTo=$inReplyToMessageId",
                )

                // 🔥 验证会话存在性
                val sessionExists = chatSessionDao.getSessionById(sessionId) != null
                if (!sessionExists) {
                    Timber.e("[ROOM版] 会话不存在，无法保存AI消息: sessionId=$sessionId")
                    return@withContext ModernResult.Error(
                        DataErrors.DataError.access(
                            operationName = "ChatSessionRepositoryImpl.saveAiMessage",
                            message = UiText.DynamicString("会话不存在"),
                            entityType = "ChatSession",
                        ),
                    )
                }

                // 🚀 主路径：立即写入ROOM数据库（同步）
                val chatRaw =
                    ChatRaw(
                        sessionId = sessionId,
                        role = ChatRaw.ROLE_ASSISTANT,
                        content = aiMessage.content,
                        timestamp = aiMessage.timestamp,
                        metadata =
                            buildMap<String, Any> {
                                put("messageType", "ai_response")
                                put("aiMessageId", aiMessage.id)
                                // 🔥 【单一数据源修复】存储finalMarkdown到metadata
                                aiMessage.finalMarkdown?.let { put("finalMarkdown", it) }
                            },
                        messageId = aiMessage.id,
                        inReplyToMessageId = inReplyToMessageId, // 关键：建立消息关联
                        // 🔥 【单一数据源修复】保存ThinkingBox思考节点数据到ChatRaw表
                        thinkingNodes = aiMessage.rawTokens, // 使用rawTokens字段存储思考过程
                        finalMarkdown = aiMessage.finalMarkdown, // 🔥 恢复finalMarkdown字段
                    )

                val insertedId = chatRawDao.insertChatMessage(chatRaw)

                // 验证主路径成功
                if (insertedId == -1L) {
                    error("主路径AI消息插入失败: sessionId=$sessionId, aiMessageId=${aiMessage.id}")
                }

                // 🔥 修复：恢复AutoSave调用，按照HISTORY_README.md文档要求
                // 主路径：立即写入ROOM数据库，辅助路径：异步触发AutoSave备份
                scope.launch {
                    chatHistoryAutoSaveService.saveMessage(sessionId, aiMessage)
                }
                Timber.d("💾 [DEBUG] AI消息已保存到ROOM，并触发AutoSave备份: sessionId=$sessionId, messageId=${aiMessage.id}")

                // 🔥 使用单一SQL更新会话信息
                chatSessionDao.updateSessionOnNewMessage(sessionId, aiMessage.timestamp)

                Timber.d("[ROOM版] ✅ AI消息保存成功: dbId=$insertedId, 关联到用户消息: $inReplyToMessageId")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "保存AI消息失败，启用备份机制")

                // 备份机制：保存AI消息到备份文件
                try {
                    writeMessageToBackupFile(sessionId, aiMessage)
                    Timber.w("AI消息已写入备份文件: sessionId=$sessionId, messageId=${aiMessage.id}")
                    ModernResult.Success(Unit)
                } catch (backupException: Exception) {
                    Timber.e(backupException, "AI消息备份文件写入也失败")
                    ModernResult.Error(
                        DataErrors.DataError.access(
                            operationName = "ChatSessionRepositoryImpl.saveAiMessage",
                            message = UiText.DynamicString("保存AI消息失败"),
                            entityType = "CoachMessage",
                            cause = e,
                        ),
                    )
                }
            }
        }

    /**
     * 扩展函数：将CoachMessage转换为ChatRaw
     */
    private fun CoachMessage.toChatRaw(sessionId: String): ChatRaw =
        ChatRaw(
            sessionId = sessionId,
            role =
                when (this) {
                    is CoachMessage.UserMessage -> ChatRaw.ROLE_USER
                    is CoachMessage.AiMessage -> ChatRaw.ROLE_ASSISTANT
                },
            content =
                when (this) {
                    is CoachMessage.UserMessage -> this.content
                    is CoachMessage.AiMessage -> this.content
                },
            timestamp = this.timestamp,
            messageId = this.id, // P0-1修复：确保传递消息ID
            // 🔥 新增：处理ThinkingBox思考节点数据
            thinkingNodes =
                when (this) {
                    is CoachMessage.AiMessage -> this.rawTokens // AI消息可能包含思考过程
                    is CoachMessage.UserMessage -> null // 用户消息不包含思考过程
                },
            // 🔥 暂时注释：等数据库字段恢复
            // finalMarkdown = when (this) {
            //     is CoachMessage.AiMessage -> this.finalMarkdown
            //     is CoachMessage.UserMessage -> null
            // },
            metadata =
                when (this) {
                    is CoachMessage.AiMessage ->
                        buildMap<String, Any> {
                            put("messageType", "ai_response")
                            put("aiMessageId", <EMAIL>)
                            // 🔥 暂时存储到metadata，等数据库字段恢复后再使用专用字段
                            <EMAIL>?.let { put("finalMarkdown", it) }
                        }

                    is CoachMessage.UserMessage ->
                        mapOf(
                            "messageType" to "user_message",
                            "userMessageId" to this.id,
                        )
                },
        )

    /**
     * P0-2修复：备份消息数据类
     */
    @Serializable
    private data class BackupMessage(
        val sessionId: String,
        val messageId: String,
        val role: String,
        val content: String,
        val timestamp: Long,
        val backupTimestamp: Long = System.currentTimeMillis(),
    )

    /**
     * P0-2修复：将消息写入备份文件
     * 当数据库写入失败时的兜底机制
     */
    private suspend fun writeMessageToBackupFile(
        sessionId: String,
        message: CoachMessage,
    ) {
        val backupMessage =
            BackupMessage(
                sessionId = sessionId,
                messageId = message.id,
                role =
                    when (message) {
                        is CoachMessage.UserMessage -> "user"
                        is CoachMessage.AiMessage -> "assistant"
                    },
                content =
                    when (message) {
                        is CoachMessage.UserMessage -> message.content
                        is CoachMessage.AiMessage -> message.content
                    },
                timestamp = message.timestamp,
            )

        val backupFile = File(context.filesDir, "chat_backup.json")
        val backupJson = json.encodeToString(backupMessage)

        // 追加写入备份文件
        backupFile.appendText("$backupJson\n")

        Timber.i("备份消息写入成功: ${backupFile.absolutePath}")
    }

    /**
     * 根据消息ID获取单个消息
     *
     * 基于 history-todo-plan.md 的单个消息获取逻辑要求实现
     */
    override suspend fun getMessageById(messageId: String): ModernResult<CoachMessage?> =
        withContext(ioDispatcher) {
            try {
                Timber.d("🔍 开始获取消息: messageId=$messageId")

                // 验证参数
                if (messageId.isBlank()) {
                    Timber.w("消息ID为空")
                    return@withContext ModernResult.Success(null)
                }

                // 查询所有会话中的消息（因为messageId是全局唯一的）
                val allMessages = chatRawDao.getAllChatMessages()
                val targetMessage = allMessages.find { it.messageId == messageId }

                if (targetMessage != null) {
                    val domainMessage = targetMessage.toDomainMessage()
                    Timber.d("✅ 成功获取消息: messageId=$messageId, type=${domainMessage::class.simpleName}")
                    ModernResult.Success(domainMessage)
                } else {
                    Timber.d("❌ 消息不存在: messageId=$messageId")
                    ModernResult.Success(null)
                }
            } catch (e: Exception) {
                Timber.e(e, "获取消息失败: messageId=$messageId")
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "ChatSessionRepositoryImpl.getMessageById",
                        message = UiText.DynamicString("获取消息失败"),
                        entityType = "CoachMessage",
                        cause = e,
                    ),
                )
            }
        }

    override suspend fun getUserSessionsPaged(
        userId: String,
        limit: Int,
        offset: Int,
    ): ModernResult<List<ChatSession>> =
        withContext(ioDispatcher) {
            try {
                // 使用现有的getUserSessions方法，但是同步版本
                val sessionEntities =
                    chatSessionDao
                        .getUserSessions(
                            userId = userId,
                            includeArchived = false,
                            limit = limit,
                            offset = offset,
                        ).first() // 获取第一个值，转为同步操作

                val sessions =
                    sessionEntities.map { entity ->
                        // 不包含消息列表以提高性能
                        entity.toDomain(emptyList())
                    }
                ModernResult.Success(sessions)
            } catch (e: Exception) {
                Timber.e(e, "获取分页会话失败")
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "ChatSessionRepositoryImpl.getUserSessionsPaged",
                        message = UiText.DynamicString("获取分页会话失败"),
                        cause = e,
                    ),
                )
            }
        }

    // ==================== 历史记录专用方法实现 ====================

    override suspend fun getSessionsForUser(
        userId: String,
        limit: Int,
        offset: Int,
    ): ModernResult<List<ChatSession>> =
        withContext(ioDispatcher) {
            try {
                // 简化实现：暂时返回空列表，后续可以根据实际需要实现
                ModernResult.Success(emptyList())
            } catch (e: Exception) {
                Timber.e(e, "获取用户会话列表失败")
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "ChatSessionRepositoryImpl.getSessionsForUser",
                        message = UiText.DynamicString("获取会话列表失败"),
                        entityType = "ChatSession",
                        cause = e,
                    ),
                )
            }
        }

    override suspend fun searchSessions(
        userId: String,
        query: String,
        limit: Int,
        offset: Int,
    ): ModernResult<List<ChatSession>> =
        withContext(ioDispatcher) {
            try {
                // 简化实现：暂时返回空列表
                ModernResult.Success(emptyList())
            } catch (e: Exception) {
                Timber.e(e, "搜索会话失败")
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "ChatSessionRepositoryImpl.searchSessions",
                        message = UiText.DynamicString("搜索会话失败"),
                        entityType = "ChatSession",
                        cause = e,
                    ),
                )
            }
        }

    override suspend fun getSessionHistory(
        sessionId: String,
        includeThinkingNodes: Boolean,
    ): ModernResult<List<CoachMessage>> =
        withContext(ioDispatcher) {
            try {
                val chatRaws =
                    if (includeThinkingNodes) {
                        chatRawDao.getChatMessagesBySession(sessionId)
                    } else {
                        // 如果不需要思考节点，可以优化查询
                        chatRawDao.getChatMessagesBySession(sessionId)
                    }

                val messages = chatRaws.map { it.toDomainMessage() }
                ModernResult.Success(messages)
            } catch (e: Exception) {
                Timber.e(e, "获取会话历史失败")
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "ChatSessionRepositoryImpl.getSessionHistory",
                        message = UiText.DynamicString("获取会话历史失败"),
                        entityType = "CoachMessage",
                        cause = e,
                    ),
                )
            }
        }

    override suspend fun getAiMessagesWithFinalContent(
        userId: String,
        limit: Int,
    ): ModernResult<List<CoachMessage.AiMessage>> =
        withContext(ioDispatcher) {
            try {
                // 简化实现：暂时返回空列表
                ModernResult.Success(emptyList())
            } catch (e: Exception) {
                Timber.e(e, "获取AI最终内容消息失败")
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "ChatSessionRepositoryImpl.getAiMessagesWithFinalContent",
                        message = UiText.DynamicString("获取AI消息失败"),
                        entityType = "CoachMessage",
                        cause = e,
                    ),
                )
            }
        }

    override suspend fun getSessionsByTimeRange(
        userId: String,
        startTime: Long,
        endTime: Long,
    ): ModernResult<List<ChatSession>> =
        withContext(ioDispatcher) {
            try {
                // 简化实现：暂时返回空列表
                ModernResult.Success(emptyList())
            } catch (e: Exception) {
                Timber.e(e, "按时间范围获取会话失败")
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "ChatSessionRepositoryImpl.getSessionsByTimeRange",
                        message = UiText.DynamicString("获取会话失败"),
                        entityType = "ChatSession",
                        cause = e,
                    ),
                )
            }
        }

    override suspend fun deleteSession(sessionId: String): ModernResult<Unit> =
        withContext(ioDispatcher) {
            try {
                // 使用现有的删除方法
                chatSessionDao.deleteSession(sessionId)
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "删除会话失败")
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "ChatSessionRepositoryImpl.deleteSession",
                        message = UiText.DynamicString("删除会话失败"),
                        entityType = "ChatSession",
                        cause = e,
                    ),
                )
            }
        }

    override suspend fun updateSessionTitle(
        sessionId: String,
        newTitle: String,
    ): ModernResult<Unit> =
        withContext(ioDispatcher) {
            try {
                chatSessionDao.updateSessionTitle(sessionId, newTitle)
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "更新会话标题失败")
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "ChatSessionRepositoryImpl.updateSessionTitle",
                        message = UiText.DynamicString("更新会话标题失败"),
                        entityType = "ChatSession",
                        cause = e,
                    ),
                )
            }
        }
}
