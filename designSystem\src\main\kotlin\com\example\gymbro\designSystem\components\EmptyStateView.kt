package com.example.gymbro.designSystem.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.FitnessCenter
import androidx.compose.material.icons.filled.Inbox
import androidx.compose.material3.Button
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.style.TextAlign
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens

/**
 * 空状态视图组件
 * 用于显示列表或数据为空时的状态
 *
 * @param icon 显示的图标
 * @param title 标题文本
 * @param description 描述文本
 * @param actionButtonText 可选的操作按钮文本
 * @param onActionButtonClick 操作按钮点击回调
 * @param modifier 修饰符
 */
@Composable
fun emptyStateView(
    icon: ImageVector,
    title: UiText,
    description: UiText,
    actionButtonText: UiText? = null,
    onActionButtonClick: () -> Unit = {},
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier.fillMaxWidth(),
        contentAlignment = Alignment.Center,
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier.padding(Tokens.Spacing.XLarge),
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                modifier = Modifier.size(Tokens.Spacing.Huge),
                tint = MaterialTheme.colorScheme.primary.copy(alpha = 0.5f),
            )

            Spacer(modifier = Modifier.height(Tokens.Spacing.Medium))

            Text(
                text = title.asString(),
                style = MaterialTheme.typography.titleLarge,
                color = MaterialTheme.colorScheme.onSurface,
            )

            Spacer(modifier = Modifier.height(Tokens.Spacing.Small))

            Text(
                text = description.asString(),
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center,
            )

            if (actionButtonText != null) {
                Spacer(modifier = Modifier.height(Tokens.Spacing.Large))

                Button(onClick = onActionButtonClick) {
                    Text(text = actionButtonText.asString())
                }
            }
        }
    }
}

/**
 * EmptyStateView 组件预览
 */
@GymBroPreview
@Composable
private fun emptyStateViewPreview() {
    GymBroTheme {
        Surface(
            modifier = Modifier.fillMaxSize(),
        ) {
            emptyStateView(
                icon = Icons.Default.Inbox,
                title = UiText.DynamicString("暂无数据"),
                description = UiText.DynamicString("当前没有可显示的内容"),
            )
        }
    }
}

@GymBroPreview
@Composable
private fun emptyStateViewWithActionPreview() {
    GymBroTheme {
        emptyStateView(
            icon = Icons.Default.FitnessCenter,
            title = UiText.DynamicString("暂无训练记录"),
            description = UiText.DynamicString("开始你的第一次训练吧"),
            actionButtonText = UiText.DynamicString("开始训练"),
            onActionButtonClick = { },
        )
    }
}
