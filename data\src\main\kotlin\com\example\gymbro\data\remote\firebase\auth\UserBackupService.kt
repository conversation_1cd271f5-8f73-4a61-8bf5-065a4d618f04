package com.example.gymbro.data.remote.firebase.auth

import com.example.gymbro.core.error.extensions.toModernDataError
import com.example.gymbro.core.error.types.*
import com.example.gymbro.core.ui.text.UiText
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.SetOptions
import kotlinx.coroutines.tasks.await
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Firebase用户备份服务，负责用户设置备份恢复和邮箱验证相关操作
 */
@Singleton
class UserBackupService
@Inject
constructor(
    private val auth: FirebaseAuth,
    private val firestore: FirebaseFirestore,
) {
    /**
     * 获取用户的备份设置
     * @return 返回包含设置的Map或null/错误
     */
    suspend fun getBackupSettings(): ModernResult<Map<String, Any>?> {
        return try {
            val user =
                auth.currentUser
                    ?: return ModernResult.error(
                        ModernDataError(
                            operationName = "getBackupSettings",
                            errorType = GlobalErrorType.Auth.General,
                            category = ErrorCategory.AUTH,
                            uiMessage = UiText.DynamicString("用户未登录"),
                            severity = ErrorSeverity.WARNING,
                            recoverable = true,
                            metadataMap =
                            mapOf(
                                StandardKeys.ERROR_SUBTYPE.key to "not_logged_in",
                            ),
                        ),
                    )

            // 从Firestore获取用户设置
            val snapshot =
                firestore
                    .collection("users")
                    .document(user.uid)
                    .collection("settings")
                    .document("backup")
                    .get()
                    .await()

            if (snapshot.exists()) {
                ModernResult.success(snapshot.data)
            } else {
                ModernResult.success(null)
            }
        } catch (e: Exception) {
            Timber.e(e, "获取备份设置失败")
            ModernResult.error(
                e.toModernDataError(
                    operationName = "UserBackupService.getBackupSettings",
                    uiMessage = UiText.DynamicString("获取备份设置失败"),
                ),
            )
        }
    }

    /**
     * 更新用户的备份设置
     * @param settings 包含要更新的设置的Map
     * @return 操作成功或失败的结果
     */
    suspend fun updateBackupSettings(settings: Map<String, Any>): ModernResult<Unit> {
        return try {
            val user =
                auth.currentUser
                    ?: return ModernResult.error(
                        ModernDataError(
                            operationName = "updateBackupSettings",
                            errorType = GlobalErrorType.Auth.General,
                            category = ErrorCategory.AUTH,
                            uiMessage = UiText.DynamicString("用户未登录"),
                            severity = ErrorSeverity.WARNING,
                            recoverable = true,
                            metadataMap =
                            mapOf(
                                StandardKeys.ERROR_SUBTYPE.key to "not_logged_in",
                            ),
                        ),
                    )

            // 更新Firestore中的用户设置
            firestore
                .collection("users")
                .document(user.uid)
                .collection("settings")
                .document("backup")
                .set(settings, SetOptions.merge())
                .await()

            ModernResult.success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "更新备份设置失败")
            ModernResult.error(
                e.toModernDataError(
                    operationName = "UserBackupService.updateBackupSettings",
                    uiMessage = UiText.DynamicString("更新备份设置失败"),
                ),
            )
        }
    }

    /**
     * 发送邮箱验证码
     * @param email 邮箱地址
     * @return 操作结果
     */
    fun sendEmailVerificationCode(email: String?): ModernResult<Unit> =
        ModernResult.error(
            ModernDataError(
                operationName = "sendEmailVerificationCode",
                errorType = GlobalErrorType.System.General,
                uiMessage = UiText.DynamicString("发送邮箱验证码功能未实现"),
                severity = ErrorSeverity.WARNING,
            ),
        )

    /**
     * 验证邮箱验证码
     * @param code 验证码
     * @return 验证结果
     */
    fun verifyEmailCode(code: String): ModernResult<Boolean> =
        ModernResult.error(
            ModernDataError(
                operationName = "verifyEmailCode",
                errorType = GlobalErrorType.System.General,
                uiMessage = UiText.DynamicString("验证邮箱验证码功能未实现"),
                severity = ErrorSeverity.WARNING,
            ),
        )

    /**
     * 发送邮箱验证链接
     * @return 操作结果
     */
    suspend fun sendEmailVerification(): ModernResult<Unit> {
        return try {
            val user = auth.currentUser
            if (user == null) {
                return ModernResult.error(
                    ModernDataError(
                        operationName = "sendEmailVerification",
                        errorType = GlobalErrorType.Auth.General,
                        uiMessage = UiText.DynamicString("用户未登录"),
                        severity = ErrorSeverity.ERROR,
                    ),
                )
            }
            user.sendEmailVerification().await()
            ModernResult.success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "发送邮箱验证失败")
            ModernResult.error(
                e.toModernDataError(
                    operationName = "UserBackupService.sendEmailVerification",
                    uiMessage = UiText.DynamicString("发送邮箱验证失败"),
                ),
            )
        }
    }

    /**
     * 在更新邮箱前发送验证链接
     * @param email 新邮箱
     * @return 操作结果
     */
    suspend fun verifyBeforeUpdateEmail(email: String): ModernResult<Unit> {
        return try {
            val user = auth.currentUser
            if (user == null) {
                return ModernResult.error(
                    ModernDataError(
                        operationName = "verifyBeforeUpdateEmail",
                        errorType = GlobalErrorType.Auth.General,
                        uiMessage = UiText.DynamicString("用户未登录"),
                        severity = ErrorSeverity.ERROR,
                    ),
                )
            }
            user.verifyBeforeUpdateEmail(email).await()
            ModernResult.success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "更新邮箱验证失败 - email: %s", email)
            ModernResult.error(
                e.toModernDataError(
                    operationName = "UserBackupService.verifyBeforeUpdateEmail",
                    uiMessage = UiText.DynamicString("更新邮箱验证失败"),
                ),
            )
        }
    }

    /**
     * 重置密码
     * @param email 邮箱
     * @return 操作结果
     */
    suspend fun resetPassword(email: String): ModernResult<Unit> =
        try {
            auth.sendPasswordResetEmail(email).await()
            ModernResult.success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "重置密码邮件发送失败 - email: %s", email)
            ModernResult.error(
                e.toModernDataError(
                    operationName = "UserBackupService.resetPassword",
                    uiMessage = UiText.DynamicString("重置密码邮件发送失败"),
                ),
            )
        }
}
