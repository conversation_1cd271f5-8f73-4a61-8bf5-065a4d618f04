package com.example.gymbro.data.network.adapter

import com.example.gymbro.core.network.mapper.toNetworkResult
import com.example.gymbro.shared.models.network.NetworkResult
import com.example.gymbro.shared.models.user.UserProfileDto
import retrofit2.Response
import retrofit2.http.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * UserProfile API的内部Retrofit接口
 *
 * 使用标准的Response<T>返回类型，由适配器转换为NetworkResult<T>
 */
internal interface UserProfileRetrofitApi {
    @GET("v1/users/{userId}/profile")
    suspend fun getUserProfile(@Path("userId") userId: String): Response<UserProfileDto>

    @PUT("v1/users/{userId}/profile")
    suspend fun updateUserProfile(
        @Path("userId") userId: String,
        @Body profile: UserProfileDto,
    ): Response<UserProfileDto>

    @POST("v1/users/profile")
    suspend fun createUserProfile(@Body profile: UserProfileDto): Response<UserProfileDto>

    @POST("v1/users/profiles/batch")
    suspend fun syncUserProfiles(@Body profiles: List<UserProfileDto>): Response<List<UserProfileDto>>
}

/**
 * UserProfileApiService的适配器实现
 *
 * 将Retrofit的Response<T>转换为NetworkResult<T>
 * 提供统一的错误处理和状态管理
 */
@Singleton
internal class UserProfileApiAdapter @Inject constructor(
    private val retrofitApi: UserProfileRetrofitApi,
) : com.example.gymbro.data.network.api.UserProfileApiService {

    override suspend fun getUserProfile(userId: String): NetworkResult<UserProfileDto> {
        return retrofitApi.getUserProfile(userId).toNetworkResult()
    }

    override suspend fun updateUserProfile(
        userId: String,
        profile: UserProfileDto,
    ): NetworkResult<UserProfileDto> {
        return retrofitApi.updateUserProfile(userId, profile).toNetworkResult()
    }

    override suspend fun createUserProfile(profile: UserProfileDto): NetworkResult<UserProfileDto> {
        return retrofitApi.createUserProfile(profile).toNetworkResult()
    }

    override suspend fun syncUserProfiles(profiles: List<UserProfileDto>): NetworkResult<List<UserProfileDto>> {
        return retrofitApi.syncUserProfiles(profiles).toNetworkResult()
    }
}
