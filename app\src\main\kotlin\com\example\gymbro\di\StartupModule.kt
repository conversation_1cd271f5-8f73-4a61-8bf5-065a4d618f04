package com.example.gymbro.di

import com.example.gymbro.core.startup.StartupStatusProvider
import com.example.gymbro.startup.AppStartupManager
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 启动状态DI模块 - App模块自洽
 * 
 * 为features模块提供启动状态访问能力，遵循Clean Architecture依赖规则
 * 在app模块中完成绑定，避免跨模块依赖问题
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class StartupModule {

    @Binds
    @Singleton
    abstract fun bindStartupStatusProvider(
        appStartupManager: AppStartupManager
    ): StartupStatusProvider
}
