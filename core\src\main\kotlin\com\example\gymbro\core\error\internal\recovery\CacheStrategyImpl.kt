package com.example.gymbro.core.error.internal.recovery

import com.example.gymbro.core.error.recovery.CacheStrategy
import timber.log.Timber

/**
 * 缓存恢复策略实现类
 * 当主操作失败时从缓存中获取数据
 *
 * @param getCachedData 获取缓存数据的函数
 * @param validateCache 验证缓存数据有效性的函数
 */
class CacheStrategyImpl<T>(
    private val getCachedData: suspend () -> T?,
    private val validateCache: suspend (T) -> Boolean = { true },
) : CacheStrategy<T> {
    /**
     * 执行缓存恢复
     * @return 有效的缓存数据或null（如果缓存不可用或无效）
     */
    override suspend fun execute(): T? {
        try {
            val cachedData =
                getCachedData() ?: run {
                    Timber.d("缓存数据不存在")
                    return null
                }

            if (!validateCache(cachedData)) {
                Timber.d("缓存数据无效")
                return null
            }

            Timber.d("成功使用缓存数据恢复")
            return cachedData
        } catch (e: Exception) {
            Timber.e(e, "获取缓存数据时发生错误")
            return null
        }
    }
}
