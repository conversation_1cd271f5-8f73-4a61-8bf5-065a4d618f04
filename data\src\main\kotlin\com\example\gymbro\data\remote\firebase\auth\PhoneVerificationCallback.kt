package com.example.gymbro.data.remote.firebase.auth

import com.google.firebase.auth.PhoneAuthCredential

/**
 * 手机验证回调接口，用于处理验证过程中的各种事件
 * 使上层组件无需直接依赖Firebase特定的回调API
 */
interface PhoneVerificationCallback {
    /**
     * 当验证码发送成功时回调
     * @param verificationId 可用于后续创建PhoneAuthCredential的验证ID
     * @param resendToken 可用于重新发送验证码的令牌
     */
    fun onCodeSent(verificationId: String, resendToken: Any?)

    /**
     * 当验证失败时回调
     * @param exception 包含失败信息的异常
     */
    fun onVerificationFailed(exception: Exception)

    /**
     * 当验证自动完成时回调（例如在支持自动验证的设备上）
     * @param credential 可用于登录的凭证
     */
    fun onVerificationCompleted(credential: PhoneAuthCredential)

    /**
     * 当验证超时时回调
     */
    fun onVerificationTimeout()
}
