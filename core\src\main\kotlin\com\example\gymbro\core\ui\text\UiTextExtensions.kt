package com.example.gymbro.core.ui.text

import android.content.Context
import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import com.example.gymbro.core.resources.ResourceProvider
import timber.log.Timber

/**
 * UiText扩展函数
 *
 * 提供UiText相关的辅助方法，确保一致的转换和使用模式
 *
 * 注意：此文件是优化后的统一UiText扩展函数文件，合并了以下文件的功能：
 * - core/util/UiTextExt.kt
 * - core/util/UiTextUtils.kt
 * - core/util/UiTextExtensions.kt
 */

//region 核心转换函数

/**
 * 将UiText转换为String
 *
 * @param resourceProvider 资源提供者，用于解析StringResource类型的UiText
 * @return 转换后的字符串
 */
fun UiText.asStringExt(resourceProvider: ResourceProvider): String =
    try {
        when (this) {
            is UiText.DynamicString -> this.value
            is UiText.StringResource -> resourceProvider.getString(this.resId, *this.args.toTypedArray())
            is UiText.ErrorCode -> {
                // ErrorCode类型需要通过ErrorCodeMapper处理，这里提供fallback
                "Error: ${this.errorCode.code} (${this.errorCode.category})"
            }
            is UiText.Empty -> ""
        }
    } catch (e: Exception) {
        Timber.e(e, "将UiText转换为字符串时出错")
        "" // 出错时返回空字符串
    }

/**
 * 将UiText安全转换为String，允许可空UiText参数
 *
 * @param resourceProvider 资源提供者
 * @param defaultValue 当UiText为null时返回的默认值
 * @return 转换后的字符串或默认值
 */
fun UiText?.asStringSafely(
    resourceProvider: ResourceProvider,
    defaultValue: String = "",
): String {
    if (this == null) return defaultValue

    return try {
        this.asStringExt(resourceProvider)
    } catch (e: Exception) {
        Timber.e(e, "将UiText安全转换为字符串时出错")
        defaultValue
    }
}

/**
 * 安全地将UiText转换为字符串，允许可空UiText和ResourceProvider
 *
 * @param resourceProvider 资源提供者，可为空
 * @param defaultValue 当UiText或ResourceProvider为null时返回的默认值
 * @return 转换后的字符串或默认值
 */
fun UiText?.asStringSafe(
    resourceProvider: ResourceProvider?,
    defaultValue: String = "",
): String {
    if (this == null || resourceProvider == null) return defaultValue

    return try {
        when (this) {
            is UiText.DynamicString -> this.value
            is UiText.StringResource -> resourceProvider.getString(this.resId, *this.args.toTypedArray())
            is UiText.ErrorCode -> {
                // ErrorCode类型需要通过ErrorCodeMapper处理，这里提供fallback
                "Error: ${this.errorCode.code} (${this.errorCode.category})"
            }
            is UiText.Empty -> ""
        }
    } catch (e: Exception) {
        Timber.e(e, "在asStringSafe中将UiText转换为字符串时出错")
        defaultValue
    }
}

//endregion

//region 创建UiText的工具函数

/**
 * 创建动态字符串UiText，确保resourceProvider参数一致性
 *
 * @param text 字符串内容
 * @param resourceProvider 资源提供者，确保DynamicString有正确的上下文
 * @return 新的UiText.DynamicString实例
 */
@Suppress("UNUSED_PARAMETER")
fun dynamicText(
    text: String,
    resourceProvider: ResourceProvider,
): UiText = UiText.DynamicString(text)

/**
 * 简化版动态字符串UiText创建函数
 * 这个函数仅应在使用DynamicString时且确保外部有ResourceProvider注入时使用
 *
 * @param text 字符串内容
 * @return 新的UiText.DynamicString实例
 */
fun dynamicText(text: String): UiText.DynamicString = UiText.DynamicString(text)

/**
 * 创建一个动态字符串UiText（来自UiTextUtils）
 *
 * @param text 动态字符串内容
 * @return UiText实例
 */
fun text(text: String): UiText = UiText.DynamicString(text)

/**
 * 创建一个资源引用UiText（来自UiTextUtils）
 *
 * @param resourceId 字符串资源ID
 * @param args 格式化参数
 * @return UiText实例
 */
fun resource(
    resourceId: Int,
    vararg args: Any,
): UiText = UiText.StringResource(resourceId, args.map { it.toString() })

//endregion

//region UiText操作函数

/**
 * 确保 DynamicString 获得 ResourceProvider 的实用函数
 * 解决缺少 ResourceProvider 的常见问题
 *
 * @param resourceProvider 资源提供者
 * @return 原始UiText，但确保内部处理使用了resourceProvider
 */
fun UiText.ensureResourceProvider(resourceProvider: ResourceProvider): UiText =
    try {
        when (this) {
            is UiText.DynamicString -> this
            is UiText.StringResource -> this
            is UiText.ErrorCode -> this
            is UiText.Empty -> this
        }
    } catch (e: Exception) {
        Timber.e(e, "在ensureResourceProvider中处理UiText时出错")
        UiText.DynamicString("") // 出错时返回空DynamicString
    }

//endregion

//region 类型转换函数

/**
 * 将UiText转换为特定类型
 *
 * @param resourceProvider 资源提供者
 * @param converter 转换函数，将字符串转换为目标类型
 * @param defaultValue 转换失败时的默认值
 * @return 转换后的值，如果转换失败则返回默认值
 */
inline fun <T> UiText?.convertToType(
    resourceProvider: ResourceProvider,
    converter: (String) -> T,
    defaultValue: T,
): T {
    val stringValue = this.asStringSafely(resourceProvider)
    if (stringValue.isBlank()) return defaultValue

    return try {
        converter(stringValue)
    } catch (e: Exception) {
        Timber.w("无法将UiText字符串转换为目标类型: ${e.message}")
        defaultValue
    }
}

//endregion

//region 常用错误消息常量

/**
 * 服务器错误信息
 */
val SERVER_ERROR = UiText.DynamicString("Server error")

//endregion

//region 便捷扩展函数

/**
 * 将String类型转换为UiText.DynamicString
 * @return 包含该字符串的UiText.DynamicString
 */
fun String.toUiText(): UiText.DynamicString = UiText.DynamicString(this)

/**
 * 将Int资源ID转换为UiText.StringResource
 * @param args 格式化参数，如果有的话
 * @return 包含该资源ID的UiText.StringResource
 */
fun Int.toUiText(
    vararg args: Any,
): UiText.StringResource = UiText.StringResource(this, args.map { it.toString() })

//endregion

/**
 * 转换UiText为字符串
 *
 * @return 字符串形式的文本内容
 */
@Composable
fun UiText.asString(): String {
    return when (this) {
        is UiText.DynamicString -> this.value
        is UiText.StringResource -> stringResource(this.resId, *this.args.toTypedArray())
        is UiText.ErrorCode -> {
            // 这里应该根据ErrorCode获取对应的字符串资源
            "Error: ${this.errorCode.code} (${this.errorCode.category})"
        }
        is UiText.Empty -> ""
    }
}

/**
 * 转换UiText为字符串（非Composable版本）
 *
 * @param context Android上下文
 * @return 字符串形式的文本内容
 */
fun UiText.asString(context: Context): String {
    return when (this) {
        is UiText.DynamicString -> this.value
        is UiText.StringResource -> context.getString(this.resId, *this.args.toTypedArray())
        is UiText.ErrorCode -> {
            // 获取ErrorCode对应的字符串资源
            "Error: ${this.errorCode.code} (${this.errorCode.category})"
        }
        is UiText.Empty -> ""
    }
}

/**
 * 检查UiText是否为空
 *
 * @return 如果文本为空则返回true
 */
fun UiText.isEmpty(): Boolean {
    return when (this) {
        is UiText.DynamicString -> this.value.isEmpty()
        is UiText.StringResource -> false // 字符串资源通常不为空
        is UiText.ErrorCode -> false // 错误代码通常不为空
        is UiText.Empty -> true
    }
}

/**
 * 检查UiText是否不为空
 *
 * @return 如果文本不为空则返回true
 */
fun UiText.isNotEmpty(): Boolean = !isEmpty()

/**
 * 在UiText为空时提供默认值
 *
 * @param default 默认的UiText
 * @return 如果当前UiText为空则返回默认值，否则返回当前值
 */
fun UiText.orDefault(default: UiText): UiText {
    return if (isEmpty()) default else this
}

/**
 * 获取UiText的长度
 *
 * @param context Android上下文
 * @return 文本的字符长度
 */
fun UiText.length(context: Context): Int {
    return asString(context).length
}

/**
 * 将UiText转换为Compose中的AnnotatedString
 * 支持富文本格式化
 *
 * @return AnnotatedString格式的文本
 */
@Composable
fun UiText.toAnnotatedString(): AnnotatedString {
    return when (this) {
        is UiText.DynamicString -> AnnotatedString(this.value)
        is UiText.StringResource -> AnnotatedString(stringResource(this.resId, *this.args.toTypedArray()))
        is UiText.ErrorCode -> {
            AnnotatedString("Error: ${this.errorCode.code} (${this.errorCode.category})")
        }
        is UiText.Empty -> AnnotatedString("")
    }
}

/**
 * 创建带样式的UiText（仅适用于DynamicString）
 *
 * @param style 文本样式
 * @return 带样式的AnnotatedString
 */
@Composable
fun UiText.withStyle(style: SpanStyle): AnnotatedString {
    val text = asString()
    return buildAnnotatedString {
        withStyle(style) {
            append(text)
        }
    }
}
