package com.example.gymbro.core.service

import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.content.ContextCompat
import dagger.hilt.android.qualifiers.ApplicationContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 前台服务管理器
 *
 * 负责启动和停止应用程序的前台服务，明确区分于WorkManager的后台任务
 */
@Singleton
class ForegroundServiceManager @Inject constructor(
    @ApplicationContext private val context: Context,
) {

    companion object {
        private const val TAG = "ForegroundServiceManager"
    }

    /**
     * 启动前台服务
     *
     * @param serviceClass 要启动的服务类
     * @return 是否成功启动服务
     */
    fun startForegroundService(serviceClass: Class<*>): Boolean {
        try {
            val serviceIntent = Intent(context, serviceClass)

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                ContextCompat.startForegroundService(context, serviceIntent)
            } else {
                context.startService(serviceIntent)
            }

            Timber.tag(TAG).d("前台服务已启动: ${serviceClass.simpleName}")
            return true
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "启动前台服务失败: ${serviceClass.simpleName}")
            return false
        }
    }

    /**
     * 停止前台服务
     *
     * @param serviceClass 要停止的服务类
     * @return 是否成功停止服务
     */
    fun stopForegroundService(serviceClass: Class<*>): Boolean {
        try {
            val serviceIntent = Intent(context, serviceClass)
            val result = context.stopService(serviceIntent)

            Timber.tag(TAG).d("前台服务已停止: ${serviceClass.simpleName}, 结果: $result")
            return result
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "停止前台服务失败: ${serviceClass.simpleName}")
            return false
        }
    }

    /**
     * 判断服务是否正在运行
     *
     * 注意: Android O及以上版本不再支持此方法，仅作为尽力而为的实现
     */
    fun isServiceRunning(serviceClass: Class<*>): Boolean {
        try {
            val activityManager = context.getSystemService(
                Context.ACTIVITY_SERVICE,
            ) as android.app.ActivityManager
            val runningServices = activityManager.getRunningServices(Integer.MAX_VALUE)

            for (service in runningServices) {
                if (serviceClass.name == service.service.className) {
                    return true
                }
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "检查服务状态失败: ${serviceClass.simpleName}")
        }
        return false
    }
}