package com.example.gymbro.designSystem.components.smart

import androidx.compose.animation.*
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Send
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme

/**
 * SmartActionButton - 智能动作按钮组件
 *
 * 提供发送按钮等动作按钮的高性能动画和状态管理
 * 支持Loading、Success、Error等状态转换动画
 */
@Composable
fun SmartActionButton(
    config: SmartInputConfig.ActionButton,
    currentText: String,
    modifier: Modifier = Modifier,
) {
    when (config) {
        is SmartInputConfig.ActionButton.Send -> {
            SendButton(
                enabled = config.enabled && currentText.isNotBlank(),
                onClick = config.onClick,
                modifier = modifier,
            )
        }
        is SmartInputConfig.ActionButton.Custom -> {
            CustomButton(
                iconRes = config.iconRes,
                enabled = config.enabled && currentText.isNotBlank(),
                onClick = config.onClick,
                modifier = modifier,
            )
        }
    }
}

/**
 * 发送按钮组件
 */
@Composable
private fun SendButton(
    enabled: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val buttonColor by animateColorAsState(
        targetValue = if (enabled) {
            MaterialTheme.colorScheme.primary
        } else {
            MaterialTheme.colorScheme.onSurface.copy(alpha = 0.12f)
        },
        label = "SendButtonColor",
    )

    val iconColor by animateColorAsState(
        targetValue = if (enabled) {
            MaterialTheme.colorScheme.onPrimary
        } else {
            MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
        },
        label = "SendIconColor",
    )

    IconButton(
        onClick = onClick,
        enabled = enabled,
        modifier = modifier.size(40.dp),
    ) {
        Icon(
            imageVector = Icons.AutoMirrored.Filled.Send,
            contentDescription = "发送",
            tint = iconColor,
            modifier = Modifier.size(20.dp),
        )
    }
}

/**
 * 自定义按钮组件
 */
@Composable
private fun CustomButton(
    iconRes: String,
    enabled: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val buttonColor by animateColorAsState(
        targetValue = if (enabled) {
            MaterialTheme.colorScheme.primary
        } else {
            MaterialTheme.colorScheme.onSurface.copy(alpha = 0.12f)
        },
        label = "CustomButtonColor",
    )

    IconButton(
        onClick = onClick,
        enabled = enabled,
        modifier = modifier.size(40.dp),
    ) {
        // 这里应该根据iconRes加载图标，暂时使用发送图标
        Icon(
            imageVector = Icons.AutoMirrored.Filled.Send,
            contentDescription = "动作",
            tint = if (enabled) {
                MaterialTheme.colorScheme.onPrimary
            } else {
                MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
            },
        )
    }
}

// === Preview组件 ===

@GymBroPreview
@Composable
private fun SmartActionButtonPreview() {
    GymBroTheme {
        SmartActionButton(
            config = SmartInputConfig.ActionButton.Send(onClick = {}),
            currentText = "Hello world",
            modifier = Modifier,
        )
    }
}

@GymBroPreview
@Composable
private fun SmartActionButtonDisabledPreview() {
    GymBroTheme {
        SmartActionButton(
            config = SmartInputConfig.ActionButton.Send(onClick = {}),
            currentText = "",
            modifier = Modifier,
        )
    }
}
