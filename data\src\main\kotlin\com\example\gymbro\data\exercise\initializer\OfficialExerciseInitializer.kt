package com.example.gymbro.data.exercise.initializer

import android.content.Context
import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.data.exercise.local.dao.ExerciseDao
import com.example.gymbro.data.exercise.mapper.ExerciseMapper
import com.example.gymbro.shared.models.exercise.*
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 官方动作初始化器
 *
 * 负责在应用首次启动时初始化官方动作库数据
 * 确保基础动作（如杠铃卧推、深蹲等）可用
 */
@Singleton
class OfficialExerciseInitializer @Inject constructor(
    @ApplicationContext private val context: Context,
    private val exerciseDao: ExerciseDao,
    private val exerciseMapper: ExerciseMapper,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
) {

    companion object {
        private const val SEED_DATA_PATH = "exercise/exercise_official_seed.json"
    }

    /**
     * 初始化官方动作库
     * 只在首次启动或数据库为空时执行
     */
    suspend fun initializeOfficialExercises() = withContext(ioDispatcher) {
        try {
            // 检查是否已有官方动作
            val officialCount = exerciseDao.getOfficialCount()
            if (officialCount > 0) {
                Timber.d("官方动作库已存在，跳过初始化 (${officialCount}条)")
                return@withContext
            }

            Timber.i("开始初始化官方动作库...")

            // 从 assets 加载种子数据
            val seedData = loadSeedDataFromAssets()
            val officialExercises = seedData.payload

            // 转换为Entity并批量插入
            val exerciseEntities = officialExercises.map { dto ->
                exerciseMapper.toEntity(exerciseMapper.toDomain(dto))
            }

            exerciseDao.upsertBatch(exerciseEntities)

            Timber.i("官方动作库初始化完成，共${officialExercises.size}条动作")
        } catch (e: Exception) {
            Timber.e(e, "官方动作库初始化失败")
            throw e
        }
    }

    /**
     * 从 assets 加载种子数据
     */
    private fun loadSeedDataFromAssets(): ExerciseSeedData {
        return try {
            val json = context.assets.open(SEED_DATA_PATH).bufferedReader().use { it.readText() }
            Json.decodeFromString<ExerciseSeedData>(json)
        } catch (e: Exception) {
            Timber.w(e, "从 assets 加载种子数据失败，使用硬编码数据")
            // 降级到硬编码数据
            createFallbackSeedData()
        }
    }

    /**
     * 创建降级种子数据（硬编码）
     * 当 assets 文件加载失败时使用
     */
    private fun createFallbackSeedData(): ExerciseSeedData {
        return ExerciseSeedData(
            schemaVersion = "1.0.0",
            entity = "EXERCISE",
            entityVersion = 1,
            generatedAt = System.currentTimeMillis(),
            totalCount = 11,
            description = "GymBro 官方动作库降级数据",
            payload = createOfficialExerciseSeedData(),
        )
    }

    /**
     * 创建官方动作种子数据（硬编码）
     * 包含基础的健身动作
     */
    private fun createOfficialExerciseSeedData(): List<ExerciseDto> {
        return listOf(
            // 胸部动作
            createOfficialExercise(
                name = "杠铃卧推",
                description = "经典的胸部训练动作，主要锻炼胸大肌、前三角肌和肱三头肌",
                muscleGroup = MuscleGroup.CHEST,
                category = ExerciseCategory.UPPER_BODY,
                equipment = listOf(Equipment.BARBELL),
                targetMuscles = listOf(MuscleGroup.CHEST, MuscleGroup.SHOULDERS, MuscleGroup.ARMS),
                steps = listOf(
                    "平躺在卧推凳上，双脚平放在地面",
                    "双手握住杠铃，握距略宽于肩膀",
                    "将杠铃从架子上取下，控制下降至胸部",
                    "用力推起杠铃至起始位置",
                ),
                tips = listOf(
                    "保持肩胛骨收紧",
                    "下降时吸气，推起时呼气",
                    "控制动作节奏，避免反弹",
                ),
            ),

            createOfficialExercise(
                name = "哑铃卧推",
                description = "使用哑铃进行的胸部训练，提供更大的运动范围",
                muscleGroup = MuscleGroup.CHEST,
                category = ExerciseCategory.UPPER_BODY,
                equipment = listOf(Equipment.DUMBBELL),
                targetMuscles = listOf(MuscleGroup.CHEST, MuscleGroup.SHOULDERS, MuscleGroup.ARMS),
            ),

            // 背部动作
            createOfficialExercise(
                name = "引体向上",
                description = "经典的背部训练动作，主要锻炼背阔肌和肱二头肌",
                muscleGroup = MuscleGroup.BACK,
                category = ExerciseCategory.UPPER_BODY,
                equipment = listOf(Equipment.NONE),
                targetMuscles = listOf(MuscleGroup.BACK, MuscleGroup.ARMS),
                steps = listOf(
                    "双手握住单杠，握距略宽于肩膀",
                    "身体悬挂，双腿可微微弯曲",
                    "用背部力量拉起身体至下巴过杠",
                    "控制下降至起始位置",
                ),
            ),

            createOfficialExercise(
                name = "杠铃划船",
                description = "俯身杠铃划船，主要锻炼背阔肌、菱形肌和后三角肌",
                muscleGroup = MuscleGroup.BACK,
                category = ExerciseCategory.UPPER_BODY,
                equipment = listOf(Equipment.BARBELL),
                targetMuscles = listOf(MuscleGroup.BACK, MuscleGroup.SHOULDERS),
            ),

            // 腿部动作
            createOfficialExercise(
                name = "杠铃深蹲",
                description = "力量训练之王，主要锻炼股四头肌、臀大肌和核心",
                muscleGroup = MuscleGroup.LEGS,
                category = ExerciseCategory.LOWER_BODY,
                equipment = listOf(Equipment.BARBELL),
                targetMuscles = listOf(MuscleGroup.LEGS, MuscleGroup.CORE),
                steps = listOf(
                    "将杠铃放在斜方肌上，双脚与肩同宽",
                    "保持背部挺直，核心收紧",
                    "下蹲至大腿与地面平行",
                    "用力站起至起始位置",
                ),
                tips = listOf(
                    "膝盖与脚尖方向一致",
                    "下蹲时吸气，站起时呼气",
                    "保持重心在脚跟",
                ),
            ),

            createOfficialExercise(
                name = "硬拉",
                description = "复合动作，锻炼整个后链肌群",
                muscleGroup = MuscleGroup.LEGS,
                category = ExerciseCategory.FULL_BODY,
                equipment = listOf(Equipment.BARBELL),
                targetMuscles = listOf(MuscleGroup.LEGS, MuscleGroup.BACK, MuscleGroup.CORE),
            ),

            // 肩部动作
            createOfficialExercise(
                name = "杠铃推举",
                description = "站姿杠铃推举，主要锻炼三角肌前束和中束",
                muscleGroup = MuscleGroup.SHOULDERS,
                category = ExerciseCategory.UPPER_BODY,
                equipment = listOf(Equipment.BARBELL),
                targetMuscles = listOf(MuscleGroup.SHOULDERS, MuscleGroup.ARMS, MuscleGroup.CORE),
            ),

            createOfficialExercise(
                name = "哑铃侧平举",
                description = "孤立训练三角肌中束的经典动作",
                muscleGroup = MuscleGroup.SHOULDERS,
                category = ExerciseCategory.UPPER_BODY,
                equipment = listOf(Equipment.DUMBBELL),
                targetMuscles = listOf(MuscleGroup.SHOULDERS),
            ),

            // 手臂动作
            createOfficialExercise(
                name = "杠铃弯举",
                description = "经典的肱二头肌训练动作",
                muscleGroup = MuscleGroup.ARMS,
                category = ExerciseCategory.UPPER_BODY,
                equipment = listOf(Equipment.BARBELL),
                targetMuscles = listOf(MuscleGroup.ARMS),
            ),

            createOfficialExercise(
                name = "双杠臂屈伸",
                description = "主要锻炼肱三头肌和胸肌下部",
                muscleGroup = MuscleGroup.ARMS,
                category = ExerciseCategory.UPPER_BODY,
                equipment = listOf(Equipment.NONE),
                targetMuscles = listOf(MuscleGroup.ARMS, MuscleGroup.CHEST),
            ),

            // 核心动作
            createOfficialExercise(
                name = "平板支撑",
                description = "静态核心训练动作，锻炼整个核心肌群",
                muscleGroup = MuscleGroup.CORE,
                category = ExerciseCategory.CORE,
                equipment = listOf(Equipment.NONE),
                targetMuscles = listOf(MuscleGroup.CORE),
            ),
        )
    }

    /**
     * 创建单个官方动作
     */
    private fun createOfficialExercise(
        name: String,
        description: String,
        muscleGroup: MuscleGroup,
        category: ExerciseCategory,
        equipment: List<Equipment>,
        targetMuscles: List<MuscleGroup> = listOf(muscleGroup),
        steps: List<String> = emptyList(),
        tips: List<String> = emptyList(),
        defaultSets: Int = 3,
        defaultReps: Int = 10,
    ): ExerciseDto {
        return ExerciseDto(
            id = ExerciseDto.generateOfficialId(name),
            name = name,
            description = description,
            muscleGroup = muscleGroup,
            category = category,
            equipment = equipment,
            targetMuscles = targetMuscles,
            steps = steps,
            tips = tips,
            defaultSets = defaultSets,
            defaultReps = defaultReps,
            isCustom = false,
            isOfficial = true,
            source = ExerciseSource.OFFICIAL,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis(),
        )
    }
}

/**
 * 动作库种子数据模型
 */
@Serializable
data class ExerciseSeedData(
    val schemaVersion: String,
    val entity: String,
    val entityVersion: Int,
    val generatedAt: Long,
    val totalCount: Int,
    val description: String? = null,
    val payload: List<ExerciseDto>,
)
