---
description:
globs:
alwaysApply: false
---
# \[项目/模块名称] 技术施工方案（精简版）

---

## 1. 问题概要 & 修复大纲

| 项        | 说明                              |
| -------- | ------------------------------- |
| **现存问题** | • 概述缺陷/痛点<br>• 受影响范围            |
| **目标状态** | • 功能/性能/稳定性指标<br>• 预期架构形态       |
| **修复要点** | • 需重构或新增的核心模块<br>• 必要的兼容 / 回滚策略 |

---

## 2. 架构说明 & 关键接口

### 2.1 总体架构图

> *可插入简洁的 UML / Mermaid 图*

### 2.2 模块职责

| 模块 | 职责 | 读写数据 | 外部依赖 |
| -- | -- | ---- | ---- |

### 2.3 关键接口定义

| 接口 | 所属模块 | 入参 | 出参 | 重要逻辑 |
| -- | ---- | -- | -- | ---- |

### 2.4 代码质量规范

* **目录与命名**：统一 *snake\_case*（文件） / *camelCase*（变量 & 方法）
* **注释**：公共接口必须有完整 JSDoc / docstring；内部函数最少一句功能说明
* **异常处理**：统一错误码 + 封装；对外接口参数显式校验
* **测试**：新增逻辑必须伴随单测，核心路径覆盖率 ≥ 80 %
* **性能**：数据结构 / 算法调整需附 10 行内基准摘要

---

## 3. 施工方案（分阶段）

| 阶段          | 主要任务 | 输出物 | 风险控制 |
| ----------- | ---- | --- | ---- |
| **Phase 1** | …    | …   | …    |
| **Phase 2** | …    | …   | …    |
| **Phase 3** | …    | …   | …    |

---

## 4. 文件清单

```text
/path/to/new_file.ext   | 新增 | 功能：… | 接口：funcA()
/path/to/changed.ext    | 修改 | 变更：… | 影响：ClassB.methodX()
/path/to/obsolete.ext   | 删除 | 原因：…
```

---

## 5. 交接 & 下一步蓝图

* **交付物**：代码、文档、测试报告、回滚脚本
* **后续迭代方向**：功能拓展 / 性能优化 / 技术债清理
* **开放问题**：列出待确认或需决策事项（负责人 → 协作方）

---
