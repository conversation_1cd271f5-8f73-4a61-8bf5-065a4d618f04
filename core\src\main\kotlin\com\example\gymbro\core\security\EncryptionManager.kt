package com.example.gymbro.core.security

import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import android.util.Base64
import timber.log.Timber
import java.security.KeyStore
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.GCMParameterSpec
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 加密管理器
 *
 * 负责应用程序中敏感数据的加密和解密操作
 * 使用Android Keystore系统确保密钥安全存储
 */
@Singleton
class EncryptionManager
@Inject
constructor() {
    companion object {
        private const val KEYSTORE_ALIAS = "GymBroEncryptionKey"
        private const val ANDROID_KEYSTORE = "AndroidKeyStore"
        private const val TRANSFORMATION = "AES/GCM/NoPadding"
        private const val GCM_IV_LENGTH = 12
        private const val GCM_TAG_LENGTH = 16
    }

    private val keyStore: KeyStore by lazy {
        KeyStore.getInstance(ANDROID_KEYSTORE).apply {
            load(null)
        }
    }

    init {
        initializeKey()
    }

    /**
     * 初始化加密密钥
     * 如果密钥不存在则创建新密钥
     */
    private fun initializeKey() {
        try {
            if (!keyStore.containsAlias(KEYSTORE_ALIAS)) {
                generateKey()
            }
        } catch (e: Exception) {
            Timber.e(e, "初始化加密密钥失败")
            throw SecurityException("无法初始化加密系统", e)
        }
    }

    /**
     * 生成新的加密密钥
     */
    private fun generateKey() {
        val keyGenerator = KeyGenerator.getInstance(KeyProperties.KEY_ALGORITHM_AES, ANDROID_KEYSTORE)

        val keyGenParameterSpec =
            KeyGenParameterSpec
                .Builder(
                    KEYSTORE_ALIAS,
                    KeyProperties.PURPOSE_ENCRYPT or KeyProperties.PURPOSE_DECRYPT,
                ).setBlockModes(KeyProperties.BLOCK_MODE_GCM)
                .setEncryptionPaddings(KeyProperties.ENCRYPTION_PADDING_NONE)
                .setRandomizedEncryptionRequired(true)
                .build()

        keyGenerator.init(keyGenParameterSpec)
        keyGenerator.generateKey()

        Timber.d("加密密钥生成成功")
    }

    /**
     * 获取密钥
     */
    private fun getKey(): SecretKey = keyStore.getKey(KEYSTORE_ALIAS, null) as SecretKey

    /**
     * 加密字符串
     *
     * @param plainText 要加密的明文
     * @return 加密后的Base64编码字符串
     */
    fun encrypt(plainText: String): String {
        try {
            val cipher = Cipher.getInstance(TRANSFORMATION)
            cipher.init(Cipher.ENCRYPT_MODE, getKey())

            val iv = cipher.iv
            val encryptedData = cipher.doFinal(plainText.toByteArray(Charsets.UTF_8))

            // 将IV和加密数据组合
            val combined = iv + encryptedData

            return Base64.encodeToString(combined, Base64.DEFAULT)
        } catch (e: Exception) {
            Timber.e(e, "加密失败")
            throw SecurityException("加密操作失败", e)
        }
    }

    /**
     * 解密字符串
     *
     * @param encryptedText 加密的Base64编码字符串
     * @return 解密后的明文
     */
    fun decrypt(encryptedText: String): String {
        try {
            val combined = Base64.decode(encryptedText, Base64.DEFAULT)

            // 分离IV和加密数据
            val iv = combined.sliceArray(0 until GCM_IV_LENGTH)
            val encryptedData = combined.sliceArray(GCM_IV_LENGTH until combined.size)

            val cipher = Cipher.getInstance(TRANSFORMATION)
            val gcmParameterSpec = GCMParameterSpec(GCM_TAG_LENGTH * 8, iv)
            cipher.init(Cipher.DECRYPT_MODE, getKey(), gcmParameterSpec)

            val decryptedData = cipher.doFinal(encryptedData)

            return String(decryptedData, Charsets.UTF_8)
        } catch (e: Exception) {
            Timber.e(e, "解密失败")
            throw SecurityException("解密操作失败", e)
        }
    }

    /**
     * 检查加密系统是否可用
     *
     * @return 如果加密系统可用返回true，否则返回false
     */
    fun isAvailable(): Boolean =
        try {
            keyStore.containsAlias(KEYSTORE_ALIAS)
        } catch (e: Exception) {
            Timber.w(e, "检查加密系统可用性失败")
            false
        }
}
