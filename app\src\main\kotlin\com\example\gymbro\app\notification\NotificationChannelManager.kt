package com.example.gymbro.app.notification

import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.os.Build
import androidx.core.app.NotificationManagerCompat
import dagger.hilt.android.qualifiers.ApplicationContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 通知渠道管理器
 * 负责创建和管理应用的所有通知渠道
 */
@Singleton
class NotificationChannelManager @Inject constructor(
    @ApplicationContext private val context: Context,
) {

    companion object {
        // 休息计时器通知渠道
        const val REST_TIMER_CHANNEL_ID = "rest_timer_channel"
        const val REST_TIMER_CHANNEL_NAME = "休息计时器"
        const val REST_TIMER_CHANNEL_DESCRIPTION = "训练休息计时器通知"

        // 训练提醒通知渠道
        const val WORKOUT_REMINDER_CHANNEL_ID = "workout_reminder_channel"
        const val WORKOUT_REMINDER_CHANNEL_NAME = "训练提醒"
        const val WORKOUT_REMINDER_CHANNEL_DESCRIPTION = "训练计划和提醒通知"
    }

    /**
     * 初始化所有通知渠道
     * 应在应用启动时调用
     */
    fun initializeChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            createRestTimerChannel()
            createWorkoutReminderChannel()
            Timber.d("通知渠道初始化完成")
        }
    }

    /**
     * 创建休息计时器通知渠道
     */
    private fun createRestTimerChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                REST_TIMER_CHANNEL_ID,
                REST_TIMER_CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW, // 低重要性，不打断用户
            ).apply {
                description = REST_TIMER_CHANNEL_DESCRIPTION
                setShowBadge(false) // 不显示角标
                enableLights(false) // 不闪灯
                enableVibration(false) // 不震动
                setSound(null, null) // 不播放声音
            }

            val notificationManager = NotificationManagerCompat.from(context)
            notificationManager.createNotificationChannel(channel)
            Timber.d("休息计时器通知渠道创建成功")
        }
    }

    /**
     * 创建训练提醒通知渠道
     */
    private fun createWorkoutReminderChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                WORKOUT_REMINDER_CHANNEL_ID,
                WORKOUT_REMINDER_CHANNEL_NAME,
                NotificationManager.IMPORTANCE_DEFAULT, // 默认重要性
            ).apply {
                description = WORKOUT_REMINDER_CHANNEL_DESCRIPTION
                setShowBadge(true)
                enableLights(true)
                enableVibration(true)
            }

            val notificationManager = NotificationManagerCompat.from(context)
            notificationManager.createNotificationChannel(channel)
            Timber.d("训练提醒通知渠道创建成功")
        }
    }

    /**
     * 检查通知权限
     */
    fun hasNotificationPermission(): Boolean {
        return NotificationManagerCompat.from(context).areNotificationsEnabled()
    }

    /**
     * 获取通知权限状态描述
     */
    fun getNotificationPermissionStatus(): String {
        return if (hasNotificationPermission()) {
            "通知权限已授予"
        } else {
            "需要授予通知权限"
        }
    }
}
