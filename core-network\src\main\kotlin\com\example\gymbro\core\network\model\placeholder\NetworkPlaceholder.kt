package com.example.gymbro.core.network.model.placeholder

import com.example.gymbro.shared.models.ai.ChatMessage
import com.example.gymbro.shared.models.ai.ChatRequest
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import timber.log.Timber

/**
 * Stage B 网络工具类
 *
 * Stage B 完成：移除临时模型，使用shared-models中的真正DTO
 * 保留工具方法，但使用强类型模型
 *
 * 设计原则：
 * 1. 使用shared-models中的强类型DTO
 * 2. 通过依赖注入获取配置，避免硬编码
 * 3. 提供类型安全的网络操作工具
 */

/**
 * 流式响应解析结果 - Stage B版本
 *
 * 保留ParsedStreamResponse，但移除临时模型依赖
 * 这个类仍然有用，用于解析SSE响应
 */
@Serializable
data class StreamResponseChunk(
    @SerialName("choices")
    val choices: List<StreamChoice>? = null,
    @SerialName("finish_reason")
    val finishReason: String? = null,
    @SerialName("error")
    val error: StreamError? = null,
) {
    @Serializable
    data class StreamChoice(
        @SerialName("delta")
        val delta: StreamDelta? = null,
        @SerialName("finish_reason")
        val finishReason: String? = null,
    )

    @Serializable
    data class StreamDelta(
        @SerialName("content")
        val content: String? = null,
        @SerialName("role")
        val role: String? = null,
    )

    @Serializable
    data class StreamError(
        @SerialName("message")
        val message: String,
        @SerialName("type")
        val type: String? = null,
    )
}

/**
 * 网络工具类 - Stage B版本
 *
 * 提供强类型的网络功能
 * 使用shared-models中的真正DTO
 */
class NetworkPlaceholder(
    private val json: Json,
) {
    companion object {
        const val STAGE = "B"
        const val DESCRIPTION = "强类型网络接口 - 使用shared-models"

        // 默认配置常量（可被依赖注入覆盖）
        private const val DEFAULT_TEMPERATURE = 0.7
        private const val DEFAULT_MAX_TOKENS = 4000
        private const val DEFAULT_STREAM = true
    }

    /**
     * 构建聊天请求 - Stage B强类型版本
     *
     * 使用shared-models中的ChatRequest和ChatMessage
     * 返回强类型对象而不是JSON字符串
     */
    fun buildChatRequest(
        prompt: String,
        model: String, // 从 AiProviderManager 获取，不硬编码
        temperature: Double = DEFAULT_TEMPERATURE,
        maxTokens: Int = DEFAULT_MAX_TOKENS,
    ): ChatRequest {
        return ChatRequest(
            model = model,
            messages = listOf(
                ChatMessage(
                    role = "user",
                    content = prompt,
                ),
            ),
            stream = DEFAULT_STREAM,
            maxTokens = maxTokens,
            temperature = temperature,
        )
    }

    /**
     * 构建聊天请求JSON - 向后兼容版本
     *
     * @deprecated 使用buildChatRequest返回强类型对象
     */
    @Deprecated("使用buildChatRequest返回强类型对象")
    fun buildChatRequestJson(
        prompt: String,
        sessionId: String,
        userMessageId: String,
        aiResponseId: String,
        model: String,
        temperature: Double = DEFAULT_TEMPERATURE,
        maxTokens: Int = DEFAULT_MAX_TOKENS,
    ): String {
        return try {
            val request = buildChatRequest(prompt, model, temperature, maxTokens)
            json.encodeToString(request)
        } catch (e: Exception) {
            Timber.e(e, "🚨 构建请求JSON失败")
            throw IllegalStateException("Failed to build chat request JSON", e)
        }
    }

    /**
     * 解析流式响应 - Stage B强类型版本
     *
     * 使用StreamResponseChunk进行安全解析
     */
    fun parseStreamResponse(rawResponse: String): ParsedStreamResponse {
        return try {
            val streamChunk = json.decodeFromString<StreamResponseChunk>(rawResponse)

            ParsedStreamResponse(
                content = extractContentFromChunk(streamChunk),
                type = extractTypeFromChunk(streamChunk),
                finishReason = streamChunk.finishReason,
                error = streamChunk.error?.message,
                raw = rawResponse,
                timestamp = System.currentTimeMillis(),
                isValid = true,
            )
        } catch (e: Exception) {
            Timber.w(e, "🚨 解析流式响应失败: ${rawResponse.take(100)}")
            ParsedStreamResponse(
                content = null,
                type = "error",
                finishReason = null,
                error = e.message,
                raw = rawResponse,
                timestamp = System.currentTimeMillis(),
                isValid = false,
            )
        }
    }

    /**
     * 从流式响应块中提取内容
     */
    private fun extractContentFromChunk(response: StreamResponseChunk): String? {
        return response.choices?.firstOrNull()?.delta?.content
    }

    /**
     * 从流式响应块中提取类型
     */
    private fun extractTypeFromChunk(response: StreamResponseChunk): String {
        return when {
            response.error != null -> "error"
            response.finishReason != null -> "done"
            response.choices?.any { it.delta?.content != null } == true -> "chunk"
            else -> "unknown"
        }
    }

    /**
     * 验证JSON格式
     */
    fun isValidJson(jsonString: String): Boolean {
        return try {
            json.parseToJsonElement(jsonString)
            true
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 提取错误信息 - Stage B强类型版本
     */
    fun extractError(rawResponse: String): String? {
        return try {
            val streamChunk = json.decodeFromString<StreamResponseChunk>(rawResponse)
            streamChunk.error?.message
        } catch (e: Exception) {
            // 如果无法解析为结构化错误，尝试简单的JSON解析
            try {
                val jsonElement = json.parseToJsonElement(rawResponse)
                val jsonObject = jsonElement.jsonObject

                jsonObject["error"]?.jsonObject?.get("message")?.jsonPrimitive?.content
                    ?: jsonObject["error"]?.jsonPrimitive?.content
                    ?: jsonObject["message"]?.jsonPrimitive?.content
            } catch (e2: Exception) {
                null
            }
        }
    }
}

/**
 * 解析后的流式响应结果
 *
 * 提供类型安全的响应数据访问
 */
data class ParsedStreamResponse(
    val content: String?,
    val type: String,
    val finishReason: String?,
    val error: String?,
    val raw: String,
    val timestamp: Long,
    val isValid: Boolean,
) {
    /**
     * 是否为内容块
     */
    val isContentChunk: Boolean get() = type == "chunk" && content != null

    /**
     * 是否为完成信号
     */
    val isDone: Boolean get() = type == "done" || finishReason != null

    /**
     * 是否为错误
     */
    val isError: Boolean get() = type == "error" || error != null
}
