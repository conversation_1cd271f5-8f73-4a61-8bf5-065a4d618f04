package com.example.gymbro.core.util

import android.widget.Toast

/**
 * 应用全局常量类
 * 集中管理所有常用常量，避免硬编码字符串
 */
object Constants {
    /**
     * 存储相关常量
     */
    object Storage {
        /**
         * SharedPreferences文件名
         */
        const val EXERCISE_LIBRARY_PREFS = "exercise_library_prefs"

        /**
         * SharedPreferences键名
         */
        const val LIBRARY_VERSION_KEY = "library_version"
    }

    /**
     * 时间相关常量（毫秒）
     */
    object Time {
        const val SECOND_MS = 1000L
        const val MINUTE_MS = 60 * SECOND_MS
        const val HOUR_MS = 60 * MINUTE_MS
        const val DAY_MS = 24 * HOUR_MS
    }

    /**
     * 消息文本资源ID常量
     *
     * 注意：这些是资源ID，不是直接的字符串，需要通过ResourceProvider访问
     */
    object MessageResId {
        // 训练动作库相关消息
        const val LIBRARY_UPDATED = 3101 // R.string.message_library_updated
        const val LIBRARY_UPDATE_FAILED = 3102 // R.string.message_library_update_failed
        const val LIBRARY_VERSION_RESET = 3103 // R.string.message_library_version_reset
        const val LIBRARY_VERSION_RESET_FAILED = 3104 // R.string.message_library_version_reset_failed

        // 通用错误消息
        const val UNKNOWN_ERROR = 1001 // 直接使用数值代替资源ID
    }

    /**
     * 消息文本常量
     *
     * @deprecated 使用UiText.StringResource和MessageResId中的资源ID替代
     * 例如: UiText.stringResource(MessageResId.LIBRARY_UPDATED)
     */
    @Deprecated(
        message = "使用UiText.StringResource和MessageResId中的资源ID替代硬编码字符串",
        replaceWith = ReplaceWith("UiText.stringResource(MessageResId.XXX)"),
        level = DeprecationLevel.ERROR,
    )
    object Messages {
        // 已弃用的消息常量，保留为空对象以方便迁移
    }

    /**
     * 默认值常量
     */
    object Defaults {
        const val DEFAULT_REST_TIME_SECONDS = 60
        const val DEFAULT_LIBRARY_VERSION = 0
    }

    /**
     * UI相关常量
     *
     * @deprecated 此常量组应移至UI相关模块，请使用com.example.gymbro.designSystem.constants.ToastDuration或android.widget.Toast常量
     */
    @Deprecated(
        message = "UI相关常量应该移至相关的UI模块，请使用com.example.gymbro.designSystem.constants.ToastDuration或android.widget.Toast常量",
        level = DeprecationLevel.ERROR,
    )
    object UI {
        const val TOAST_DURATION_SHORT = Toast.LENGTH_SHORT
        const val TOAST_DURATION_LONG = Toast.LENGTH_LONG
    }

    /**
     * 键值常量，用于Intent、Bundle、WorkManager等
     */
    const val KEY_USER_ID = "user_id"

    /**
     * 消息ID相关工具
     * P0-1修复：统一消息ID生成，确保Stream ID一致性
     */
    object MessageId {
        /**
         * 生成统一的消息ID
         * 替代分散在各处的UUID.randomUUID().toString()
         * 确保UI生成ID与SSE回包ID一致
         */
        fun generate(): String = java.util.UUID.randomUUID().toString()

        /**
         * 验证消息ID格式
         * @param id 待验证的ID
         * @return 是否为有效的UUID格式
         */
        fun isValid(id: String): Boolean {
            return try {
                java.util.UUID.fromString(id)
                true
            } catch (e: IllegalArgumentException) {
                false
            }
        }

        /**
         * 创建测试用的固定ID
         * 用于单元测试，避免随机UUID导致断言失败
         */
        fun testFixed(suffix: String): String = "test-message-$suffix"

        /**
         * 测试工具类
         * 提供测试环境下的ID生成和验证
         */
        object Test {
            /**
             * 生成固定的测试ID
             * @param suffix 后缀，用于区分不同的测试场景
             */
            fun fixed(suffix: String): String = "test-message-$suffix"

            /**
             * 生成随机的测试ID（仍使用真实UUID格式）
             * 用于需要真实UUID格式但内容可控的测试场景
             */
            fun random(): String = java.util.UUID.randomUUID().toString()

            /**
             * 验证是否为测试ID
             */
            fun isTestId(id: String): Boolean = id.startsWith("test-message-")
        }
    }
}
