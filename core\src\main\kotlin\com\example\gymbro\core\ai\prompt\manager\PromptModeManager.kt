package com.example.gymbro.core.ai.prompt.manager

import com.example.gymbro.core.ai.prompt.builder.ConversationTurn
import com.example.gymbro.core.ai.prompt.builder.CoreChatMessage
import com.example.gymbro.core.ai.prompt.builder.LayeredPromptBuilder
import com.example.gymbro.core.ai.prompt.builder.PromptBuilder
import com.example.gymbro.core.ai.prompt.config.PromptConfigManager
import com.example.gymbro.core.ai.prompt.model.AiContextData
import com.example.gymbro.core.ai.prompt.registry.PromptRegistry
import com.example.gymbro.core.ai.prompt.structure.SystemLayer
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Prompt模式枚举
 * 用于在不同的Prompt构建器之间切换
 */
enum class PromptMode(val displayName: String, val description: String) {
    LAYERED("LayeredPromptBuilder", "原有的分层Prompt构建器 - 传统模式"),
    PIPELINE("PipelinePromptBuilder", "新的5步Pipeline构建器 - 流式模式"),
    STANDARD("标准思考模式", "支持ThinkingBox的标准模式 - 带阶段指令"),
    BLANK("空白模式", "最简单的Prompt - 用于对比测试"),
}

/**
 * Prompt模式管理器 - 统一版本
 * 负责管理统一 PromptBuilder 的不同模式，方便实机调试和对比
 *
 * 🔥 升级架构：PromptConfigManager ← PromptModeManager → PromptRegistry
 * 遵循GymBro项目的Clean Architecture + MVI 2.0架构标准
 */
@Singleton
class PromptModeManager @Inject constructor(
    private val promptBuilder: LayeredPromptBuilder, // 只需要一个统一实现
    private val promptRegistry: PromptRegistry,
    private val promptConfigManager: PromptConfigManager, // 🔥 新增：配置管理器协作
) {

    private val _currentMode = MutableStateFlow(PromptMode.STANDARD) // 🔥 修改：默认使用标准思考模式
    val currentMode: StateFlow<PromptMode> = _currentMode.asStateFlow()

    private val _debugInfo = MutableStateFlow("")
    val debugInfo: StateFlow<String> = _debugInfo.asStateFlow()

    init {
        // 🔥 新功能：初始化时同步配置管理器和注册表
        syncConfigManagerAndRegistry()
        Timber.i("🔧 PromptModeManager初始化完成，统一配置管理架构就绪")
    }

    /**
     * 🔥 核心方法：统一切换入口
     * 同时更新PromptConfigManager和PromptRegistry
     */
    fun switchMode(mode: PromptMode) {
        val oldMode = _currentMode.value
        _currentMode.value = mode

        try {
            // 1. 更新PromptConfigManager
            val configModeString = mapModeToConfigString(mode)
            promptConfigManager.switchMode(configModeString)

            // 2. 同步更新PromptRegistry
            promptRegistry.switchMode(mode)

            val debugMessage = "🔄 统一Prompt模式切换: ${oldMode.displayName} → ${mode.displayName}"
            _debugInfo.value = debugMessage
            Timber.i(debugMessage)
        } catch (e: Exception) {
            Timber.e(e, "❌ 统一Prompt模式切换失败: $mode")
            _debugInfo.value = "❌ 切换失败: ${e.message}"
        }
    }

    /**
     * 🔥 新方法：字符串模式切换（兼容Coach模块）
     */
    fun switchMode(modeString: String) {
        val mode = when (modeString) {
            "standard" -> PromptMode.STANDARD
            "layered" -> PromptMode.LAYERED
            "pipeline" -> PromptMode.PIPELINE
            "blank" -> PromptMode.BLANK
            else -> PromptMode.STANDARD
        }
        switchMode(mode)
    }

    /**
     * 🔥 新方法：配置同步
     */
    private fun syncConfigManagerAndRegistry() {
        // 从PromptConfigManager获取当前模式
        val currentConfigMode = promptConfigManager.currentMode.value
        val correspondingMode = mapConfigStringToMode(currentConfigMode)

        // 同步状态
        _currentMode.value = correspondingMode
        promptRegistry.syncWithConfigManager()

        Timber.d("🔄 配置同步完成: ConfigManager=$currentConfigMode → Mode=$correspondingMode")
    }

    /**
     * 🔥 辅助方法：PromptMode转配置字符串
     */
    private fun mapModeToConfigString(mode: PromptMode): String {
        return when (mode) {
            PromptMode.STANDARD -> "standard"
            PromptMode.LAYERED -> "layered"
            PromptMode.PIPELINE -> "pipeline"
            PromptMode.BLANK -> "blank"
        }
    }

    /**
     * 🔥 辅助方法：配置字符串转PromptMode
     */
    private fun mapConfigStringToMode(configString: String): PromptMode {
        return when (configString) {
            "standard" -> PromptMode.STANDARD
            "layered" -> PromptMode.LAYERED
            "pipeline" -> PromptMode.PIPELINE
            "blank" -> PromptMode.BLANK
            else -> PromptMode.STANDARD
        }
    }

    /**
     * 获取当前模式的Prompt构建器
     * 🔥 统一实现：始终返回同一个 LayeredPromptBuilder，但内部模式不同
     */
    fun getCurrentPromptBuilder(): PromptBuilder {
        return when (_currentMode.value) {
            PromptMode.LAYERED,
            PromptMode.PIPELINE,
            PromptMode.STANDARD,
            -> promptBuilder // 统一使用 LayeredPromptBuilder
            PromptMode.BLANK -> BlankPromptBuilder()
        }
    }

    /**
     * 构建Prompt并记录调试信息
     */
    suspend fun buildPromptWithDebug(
        context: AiContextData,
        userMessage: String,
        tokenBudget: Int = 3000,
    ): PromptResult {
        val startTime = System.currentTimeMillis()
        val mode = _currentMode.value

        try {
            val promptBuilder = getCurrentPromptBuilder()
            // 使用新的 buildChatMessages 方法
            val messages = promptBuilder.buildChatMessages(
                systemLayer = null,
                userInput = userMessage,
                history = emptyList(),
            )
            val prompt = messages.joinToString("\n") { "${it.role}: ${it.content}" }
            val tokenCount = promptBuilder.estimateTokens(prompt)
            val duration = System.currentTimeMillis() - startTime

            val debugInfo = buildString {
                appendLine("🔧 Prompt构建调试信息")
                appendLine("模式: ${mode.displayName}")
                appendLine("耗时: ${duration}ms")
                appendLine("Token数: $tokenCount")
                appendLine("长度: ${prompt.length}字符")
                appendLine("用户输入: $userMessage")
                appendLine("=".repeat(40))
            }

            _debugInfo.value = debugInfo
            Timber.d(debugInfo)

            return PromptResult(
                prompt = prompt,
                mode = mode,
                tokenCount = tokenCount,
                duration = duration,
                success = true,
                debugInfo = debugInfo,
            )
        } catch (e: Exception) {
            val duration = System.currentTimeMillis() - startTime
            val errorInfo = "❌ Prompt构建失败: ${e.message} (耗时: ${duration}ms)"

            _debugInfo.value = errorInfo
            Timber.e(e, errorInfo)

            return PromptResult(
                prompt = "",
                mode = mode,
                tokenCount = 0,
                duration = duration,
                success = false,
                error = e.message,
                debugInfo = errorInfo,
            )
        }
    }

    /**
     * 获取所有可用模式
     */
    fun getAllModes(): List<PromptMode> = PromptMode.values().toList()

    /**
     * 获取PromptRegistry实例 - 用于UI组件访问
     */
    fun getPromptRegistry(): PromptRegistry = promptRegistry

    /**
     * 重置调试信息
     */
    fun clearDebugInfo() {
        _debugInfo.value = ""
    }

    /**
     * 获取模式统计信息
     */
    fun getModeStats(): Map<PromptMode, ModeStats> {
        // 这里可以添加统计逻辑，记录每种模式的使用情况
        return mapOf(
            PromptMode.LAYERED to ModeStats(
                usageCount = 0,
                avgDuration = 0,
                avgTokenCount = 0,
            ),
            PromptMode.PIPELINE to ModeStats(
                usageCount = 0,
                avgDuration = 0,
                avgTokenCount = 0,
            ),
            PromptMode.STANDARD to ModeStats(
                usageCount = 0,
                avgDuration = 0,
                avgTokenCount = 0,
            ),
            PromptMode.BLANK to ModeStats(
                usageCount = 0,
                avgDuration = 0,
                avgTokenCount = 0,
            ),
        )
    }
}

/**
 * Prompt构建结果
 */
data class PromptResult(
    val prompt: String,
    val mode: PromptMode,
    val tokenCount: Int,
    val duration: Long,
    val success: Boolean,
    val error: String? = null,
    val debugInfo: String,
)

/**
 * 模式统计信息
 */
data class ModeStats(
    val usageCount: Int,
    val avgDuration: Long,
    val avgTokenCount: Int,
)

/**
 * 空白Prompt构建器
 * 用于对比测试的最简单实现
 */
private class BlankPromptBuilder : PromptBuilder {

    // 废弃方法的实现（为了编译通过）
    override suspend fun buildPrompt(
        context: AiContextData,
        userMessage: String,
        tokenBudget: Int,
    ): String {
        return "你是专业健身AI助手GymBro。\n\n用户问题：$userMessage\n\n请提供专业的健身建议。"
    }

    override suspend fun buildIncrementalPrompt(
        context: AiContextData,
        previousContext: AiContextData?,
        userMessage: String,
    ): String {
        return buildPrompt(context, userMessage)
    }

    override fun estimateTokens(prompt: String): Int {
        return prompt.length / 4 // 简单估算
    }

    // Pipeline 废弃方法的实现（为了编译通过）
    override fun executeSteps(
        userPrompt: String,
        systemLayer: SystemLayer,
        enableFunctions: Boolean,
    ): kotlinx.coroutines.flow.Flow<com.example.gymbro.core.ai.prompt.builder.PipelineEvent> {
        return kotlinx.coroutines.flow.flowOf(
            com.example.gymbro.core.ai.prompt.builder.PipelineEvent(
                step = "GENERATE",
                description = "空白模式直接回答",
                payload = userPrompt,
                done = true,
            ),
        )
    }

    override fun executeStepsIntelligently(
        userPrompt: String,
        systemLayer: SystemLayer,
        enableFunctions: Boolean,
        forceSimpleMode: Boolean,
    ): kotlinx.coroutines.flow.Flow<com.example.gymbro.core.ai.prompt.builder.PipelineEvent> {
        return executeSteps(userPrompt, systemLayer, enableFunctions)
    }

    // 🆕 实现新的接口方法，使用统一提示词
    override suspend fun buildChatMessages(
        systemLayer: SystemLayer?,
        userInput: String,
        history: List<ConversationTurn>,
        model: String?, // 🆕 新增模型参数（统一提示词系统）
    ): List<CoreChatMessage> {
        // 🔥 统一提示词，不再区分模型
        val systemPrompt = "你是专业健身AI助手GymBro。请简洁回答用户的健身相关问题。"

        return listOf(
            CoreChatMessage("system", systemPrompt),
            CoreChatMessage("user", userInput),
        )
    }
}
