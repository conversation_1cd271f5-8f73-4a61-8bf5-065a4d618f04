# App模块 接口文档

> **版本**: v2.0 - MVI 架构重构
> **状态**: ✅ 生产就绪
> **最后更新**: 2025-06-26

## 🎯 模块职责

`app` 模块作为应用的顶层组装模块，其主要职责是初始化全局服务、管理应用生命周期、配置导航图，并将所有功能（feature）模块集成起来。它本身不提供任何具体的业务功能接口，而是作为整个应用的容器和入口点。

## 🔌 公共接口

`app` 模块没有向其他模块提供可调用的公共接口（如UseCase或Repository）。其所有组件都是内部的，用于完成应用的组装和启动。

| 类/文件                  | 职责                                       | 状态       |
| ------------------------ | ------------------------------------------ | ---------- |
| `GymBroApp.kt`           | `Application`类，负责应用的全局初始化。      | ✅ 存在    |
| `MainActivity.kt`        | 应用的唯一Activity，负责UI和导航的组装。     | ✅ 存在    |

## 📋 内部核心组件

虽然没有公共接口，但以下是`app`模块内部的一些核心组件，它们共同完成了应用的启动和组装。

### `GymBroApp`

- **职责**: 应用的全局初始化入口。
- **核心任务**:
    - 初始化Hilt依赖注入。
    - 初始化Firebase。
    - 初始化Timber日志库。
    - 手动初始化WorkManager，并注入`HiltWorkerFactory`。
    - 创建通知渠道。
    - 在后台线程中执行数据库迁移和BGE（AI）引擎的预热。

### `MainActivity`

- **职责**: 应用的UI入口和导航中心。
- **核心任务**:
    - 设置Compose内容，应用动态主题。
    - 配置`NavHost`，将所有`feature`模块的导航图（`authNavGraph`, `homeGraph`, `workoutGraph`等）集成在一起。
    - 管理应用的启动流程，从`LoadingScreen`过渡到主应用。

### `LoadingViewModel`

- **职责**: 管理启动加载屏幕的状态机。
- **核心任务**:
    - 并行执行地区检测和版本检查。
    - 在检查通过后，确保用户拥有一个匿名的`userId`。
    - 根据不同的结果（如成功、应用被锁定、需要强制更新）更新UI状态。

## 📊 UI数据模型

`app`模块本身不定义复杂的UI数据模型，它主要使用`feature`模块提供的UI State和数据模型。`LoadingViewModel`中定义的`LoadingState`是其内部最核心的UI状态模型。

| 数据类         | 职责                                       |
| -------------- | ------------------------------------------ |
| `LoadingState` | 定义了加载过程中的各种状态，如`Initializing`, `CheckingVersion`, `Ready`, `AppLocked`, `ForceUpdate`。 |

## 🔄 清理说明

`app`模块作为顶层模块，其接口相对稳定。未来的清理工作将主要集中在优化启动流程和移除废弃的`feature`模块依赖上。