package com.example.gymbro.core.util

import kotlinx.datetime.Clock
import kotlinx.datetime.DateTimeUnit
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDate
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.atStartOfDayIn
import kotlinx.datetime.toInstant
import kotlinx.datetime.toLocalDateTime
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

/**
 * 日期时间处理扩展函数
 * 提供安全的日期时间类型转换与处理函数，确保异常安全并提供合理的默认值
 */

// 常用日期格式字符串
private const val ISO_DATE_PATTERN = "yyyy-MM-dd"
private const val ISO_DATE_TIME_PATTERN = "yyyy-MM-dd'T'HH:mm:ss"
private val DATE_PATTERNS =
    listOf(
        "yyyy-MM-dd",
        "dd/MM/yyyy",
        "MM/dd/yyyy",
        "yyyyMMdd",
    )

/**
 * 将字符串安全转换为LocalDate
 *
 * @param defaultValue 转换失败时返回的默认值，默认为当前日期
 * @return 转换后的LocalDate或默认值
 */
fun String?.toLocalDateSafely(
    defaultValue: LocalDate =
        Clock.System
            .now()
            .toLocalDateTime(TimeZone.currentSystemDefault())
            .date,
): LocalDate {
    if (this == null || this.isBlank()) return defaultValue

    // 尝试使用kotlinx.datetime原生解析
    try {
        return LocalDate.parse(this.trim())
    } catch (e: Exception) {
        // 原生解析失败，尝试使用java.text.SimpleDateFormat进行解析
        for (pattern in DATE_PATTERNS) {
            try {
                val format = SimpleDateFormat(pattern, Locale.getDefault())
                val date = format.parse(this.trim())
                if (date != null) {
                    val calendar = java.util.Calendar.getInstance()
                    calendar.time = date
                    return LocalDate(
                        calendar.get(java.util.Calendar.YEAR),
                        calendar.get(java.util.Calendar.MONTH) + 1,
                        calendar.get(java.util.Calendar.DAY_OF_MONTH),
                    )
                }
            } catch (e: Exception) {
                // 继续尝试下一个格式
                continue
            }
        }

        // 所有格式都失败，记录警告并返回默认值
        Timber.w("无法将字符串 '$this' 解析为LocalDate: ${e.message}")
        return defaultValue
    }
}

/**
 * 将LocalDate转换为毫秒时间戳
 *
 * @param timeZone 时区，默认为系统默认时区
 * @return 表示日期的毫秒时间戳
 */
fun LocalDate.toMillis(
    timeZone: TimeZone = TimeZone.currentSystemDefault(),
): Long = this.atStartOfDayIn(timeZone).toEpochMilliseconds()

/**
 * 将毫秒时间戳安全转换为LocalDate
 *
 * @param timeZone 时区，默认为系统默认时区
 * @param defaultValue 转换失败时返回的默认值，默认为当前日期
 * @return 转换后的LocalDate或默认值
 */
fun Long?.toLocalDateSafely(
    timeZone: TimeZone = TimeZone.currentSystemDefault(),
    defaultValue: LocalDate =
        Clock.System
            .now()
            .toLocalDateTime(TimeZone.currentSystemDefault())
            .date,
): LocalDate {
    if (this == null) return defaultValue

    return try {
        Instant.fromEpochMilliseconds(this).toLocalDateTime(timeZone).date
    } catch (e: Exception) {
        Timber.w("无法将时间戳 $this 转换为LocalDate: ${e.message}")
        defaultValue
    }
}

/**
 * 将LocalDate格式化为字符串
 *
 * @param pattern 日期格式模式，默认为ISO格式(yyyy-MM-dd)
 * @return 格式化后的日期字符串
 */
fun LocalDate.format(pattern: String = ISO_DATE_PATTERN): String {
    // kotlinx.datetime不直接支持格式化，使用Java的SimpleDateFormat
    val calendar = java.util.Calendar.getInstance()
    calendar.set(year, month.ordinal, dayOfMonth)
    return SimpleDateFormat(pattern, Locale.getDefault()).format(calendar.time)
}

/**
 * 将字符串安全转换为LocalDateTime
 *
 * @param defaultValue 转换失败时返回的默认值，默认为当前日期时间
 * @return 转换后的LocalDateTime或默认值
 */
fun String?.toLocalDateTimeSafely(
    defaultValue: LocalDateTime = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()),
): LocalDateTime {
    if (this == null || this.isBlank()) return defaultValue

    try {
        return LocalDateTime.parse(this.trim())
    } catch (e: Exception) {
        // 尝试使用SimpleDateFormat解析
        try {
            val format = SimpleDateFormat(ISO_DATE_TIME_PATTERN, Locale.getDefault())
            val date = format.parse(this.trim())
            if (date != null) {
                val calendar = java.util.Calendar.getInstance()
                calendar.time = date
                return LocalDateTime(
                    calendar.get(java.util.Calendar.YEAR),
                    calendar.get(java.util.Calendar.MONTH) + 1,
                    calendar.get(java.util.Calendar.DAY_OF_MONTH),
                    calendar.get(java.util.Calendar.HOUR_OF_DAY),
                    calendar.get(java.util.Calendar.MINUTE),
                    calendar.get(java.util.Calendar.SECOND),
                    calendar.get(java.util.Calendar.MILLISECOND) * 1_000_000,
                )
            }
        } catch (e: Exception) {
            // 忽略此异常，下面会返回默认值
        }

        Timber.w("无法将字符串 '$this' 解析为LocalDateTime: ${e.message}")
        return defaultValue
    }
}

/**
 * 将毫秒时间戳转换为LocalDateTime
 *
 * @param timeZone 时区，默认为系统默认时区
 * @return 转换后的LocalDateTime，如果转换失败则返回null
 */
fun Long?.toLocalDateTime(timeZone: TimeZone = TimeZone.currentSystemDefault()): LocalDateTime? =
    this?.let {
        try {
            Instant.fromEpochMilliseconds(it).toLocalDateTime(timeZone)
        } catch (e: Exception) {
            Timber.w("无法将时间戳 $this 转换为LocalDateTime: ${e.message}")
            null
        }
    }

/**
 * 将毫秒时间戳安全转换为LocalDateTime
 *
 * @param timeZone 时区，默认为系统默认时区
 * @param defaultValue 转换失败时返回的默认值，默认为当前日期时间
 * @return 转换后的LocalDateTime或默认值
 */
fun Long?.toLocalDateTimeSafely(
    timeZone: TimeZone = TimeZone.currentSystemDefault(),
    defaultValue: LocalDateTime = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()),
): LocalDateTime {
    if (this == null) return defaultValue

    return try {
        Instant.fromEpochMilliseconds(this).toLocalDateTime(timeZone)
    } catch (e: Exception) {
        Timber.w("无法将时间戳 $this 转换为LocalDateTime: ${e.message}")
        defaultValue
    }
}

/**
 * 将LocalDateTime转换为毫秒时间戳
 *
 * @param timeZone 时区，默认为系统默认时区
 * @return 表示日期时间的毫秒时间戳
 */
fun LocalDateTime.toMillis(
    timeZone: TimeZone = TimeZone.currentSystemDefault(),
): Long = this.toInstant(timeZone).toEpochMilliseconds()

/**
 * 将LocalDateTime转换为毫秒时间戳 (toMillis的别名)
 *
 * @param timeZone 时区，默认为系统默认时区
 * @return 表示日期时间的毫秒时间戳
 */
fun LocalDateTime.toEpochMillis(
    timeZone: TimeZone = TimeZone.currentSystemDefault(),
): Long = this.toInstant(timeZone).toEpochMilliseconds()

/**
 * 获取指定时区的今天日期
 *
 * @param timeZone 时区
 * @return 指定时区的今天日期
 */
fun Clock.todayIn(timeZone: TimeZone): LocalDate = now().toLocalDateTime(timeZone).date

/**
 * 获取指定时区的日期
 *
 * @param timeZone 时区
 * @return 指定时区的日期
 */
fun Instant.todayIn(timeZone: TimeZone): LocalDate = toLocalDateTime(timeZone).date

/**
 * 安全地解析字符串为Instant
 *
 * @param defaultValue 解析失败时的默认值
 * @return 解析后的Instant值，如果解析失败则返回默认值
 */
fun String?.toInstantSafely(defaultValue: Instant = Clock.System.now()): Instant {
    if (this == null || this.isBlank()) return defaultValue

    return try {
        // 尝试按照ISO格式解析
        val trimmed = this.trim()
        when {
            // ISO 8601格式
            trimmed.contains('T') -> Instant.parse(trimmed)
            // 只有日期部分
            trimmed.matches(Regex("\\d{4}-\\d{2}-\\d{2}")) -> {
                val localDate = LocalDate.parse(trimmed)
                localDate.atStartOfDayIn(TimeZone.currentSystemDefault())
            }
            // 时间戳格式（毫秒）
            trimmed.all { it.isDigit() } -> {
                val timestamp = trimmed.toLong()
                Instant.fromEpochMilliseconds(timestamp)
            }
            else -> {
                Timber.w("无法解析时间字符串: '$this'")
                defaultValue
            }
        }
    } catch (e: Exception) {
        Timber.w(e, "解析时间字符串失败: '$this'")
        defaultValue
    }
}

/**
 * 将LocalDate格式化为字符串（中文格式）
 *
 * @param pattern 格式化模式，默认为"yyyy年MM月dd日"
 * @return 格式化后的字符串
 */
fun LocalDate.toChineseString(pattern: String = "yyyy年MM月dd日"): String {
    return "${year}年${monthNumber.toString().padStart(2, '0')}月${dayOfMonth.toString().padStart(2, '0')}日"
}

/**
 * 将时间戳安全转换为Instant
 *
 * @param unit 时间单位，默认为毫秒
 * @return 转换后的Instant，如果转换失败则返回当前时间
 */
fun Long?.toInstantSafely(unit: DateTimeUnit.TimeBased = DateTimeUnit.MILLISECOND): Instant {
    if (this == null) return Clock.System.now()

    return try {
        when (unit) {
            DateTimeUnit.NANOSECOND -> Instant.fromEpochMilliseconds(this / 1_000_000)
            DateTimeUnit.MICROSECOND -> Instant.fromEpochMilliseconds(this / 1_000)
            DateTimeUnit.MILLISECOND -> Instant.fromEpochMilliseconds(this)
            DateTimeUnit.SECOND -> Instant.fromEpochSeconds(this)
            DateTimeUnit.MINUTE -> Instant.fromEpochSeconds(this * 60)
            DateTimeUnit.HOUR -> Instant.fromEpochSeconds(this * 3600)
            else -> {
                Timber.w("不支持的时间单位: $unit")
                Clock.System.now()
            }
        }
    } catch (e: Exception) {
        Timber.w(e, "时间戳转换失败: $this")
        Clock.System.now()
    }
}

/**
 * 将LocalDate转换为Date（Java时间）
 *
 * @return 转换后的Date对象
 */
fun LocalDate.toJavaDate(): Date {
    return Date(
        Calendar.getInstance().apply {
            set(Calendar.YEAR, year)
            set(Calendar.MONTH, monthNumber - 1) // Calendar月份从0开始
            set(Calendar.DAY_OF_MONTH, dayOfMonth)
        }.timeInMillis,
    )
}

/**
 * 获取时间戳的Instant表示
 *
 * @return Instant对象
 */
fun Long?.toInstantOrNow(): Instant {
    return this?.let { Instant.fromEpochMilliseconds(it) } ?: Clock.System.now()
}

/**
 * 将时间戳转换为LocalDateTime
 *
 * @param timeZone 时区，默认为系统时区
 * @return 转换后的LocalDateTime
 */
fun Long?.toLocalDateTimeSafely(timeZone: TimeZone = TimeZone.currentSystemDefault()): LocalDateTime {
    if (this == null) return Clock.System.now().toLocalDateTime(timeZone)

    return try {
        Instant.fromEpochMilliseconds(this).toLocalDateTime(timeZone)
    } catch (e: Exception) {
        Timber.w(e, "时间戳转换为LocalDateTime失败: $this")
        Clock.System.now().toLocalDateTime(timeZone)
    }
}

/**
 * 将LocalDateTime格式化为字符串（中文格式）
 *
 * @param includeSeconds 是否包含秒数
 * @return 格式化后的字符串
 */
fun LocalDateTime.toChineseString(includeSeconds: Boolean = false): String {
    val datePart = date.toChineseString()
    val timePart = if (includeSeconds) {
        "${hour.toString().padStart(
            2,
            '0',
        )}:${minute.toString().padStart(2, '0')}:${second.toString().padStart(2, '0')}"
    } else {
        "${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}"
    }
    return "$datePart $timePart"
}

/**
 * 将LocalDateTime格式化为简短字符串
 *
 * @param timeZone 时区
 * @return 格式化后的字符串
 */
fun LocalDateTime.toShortString(timeZone: TimeZone = TimeZone.currentSystemDefault()): String {
    return "${monthNumber.toString().padStart(
        2,
        '0',
    )}-${dayOfMonth.toString().padStart(
        2,
        '0',
    )} ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}"
}

/**
 * 获取当前时间的Instant
 *
 * @return 当前时间的Instant对象
 */
fun Clock.nowInstant(): Instant = now()

/**
 * 将Instant格式化为显示字符串
 *
 * @param timeZone 时区
 * @return 格式化后的字符串
 */
fun Instant.toDisplayString(timeZone: TimeZone = TimeZone.currentSystemDefault()): String {
    return toLocalDateTime(timeZone).toChineseString(includeSeconds = false)
}
