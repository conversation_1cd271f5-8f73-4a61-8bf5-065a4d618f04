# 🔧 Step 3: PromptBuilder唯一化 - Detekt规则
# 
# 自定义Detekt规则，确保只有LayeredPromptBuilder被使用

style:
  # 禁止创建其他PromptBuilder实现
  ForbiddenClassName:
    active: true
    forbiddenNames:
      - 'SimplePromptBuilder'
      - 'BasicPromptBuilder'
      - 'DefaultPromptBuilder'
      - 'CustomPromptBuilder'
      - 'LegacyPromptBuilder'
    excludes: ['**/test/**', '**/androidTest/**']

  # 禁止SSE相关类
  ForbiddenImport:
    active: true
    forbiddenImports:
      - 'okhttp3.sse.*'
      - '*.SseClient'
      - '*.SseParser'
      - '*.EventSource'
    excludes: ['**/test/**', '**/androidTest/**']

naming:
  # 确保网络配置类命名规范
  ClassNaming:
    active: true
    classPattern: '[A-Z][a-zA-Z0-9]*'
    excludes: ['**/test/**', '**/androidTest/**']

complexity:
  # 限制DI模块复杂度
  ComplexMethod:
    active: true
    threshold: 15
    ignoreSingleWhenExpression: true
    excludes: ['**/test/**', '**/androidTest/**']

comments:
  # 要求关键类有文档注释
  UndocumentedPublicClass:
    active: true
    searchInNestedClass: true
    searchInInnerClass: true
    searchInInnerObject: true
    searchInProtectedClass: false
    excludes: ['**/test/**', '**/androidTest/**']

# 自定义规则：禁止硬编码URL和API密钥
potential-bugs:
  # 检测硬编码的URL
  HardcodedUrl:
    active: true
    pattern: '(https?|wss?)://[^\s"''`]+'
    excludes: ['**/test/**', '**/androidTest/**', '**/BuildConfig.kt']

  # 检测硬编码的API密钥
  HardcodedApiKey:
    active: true
    pattern: '(sk-|pk_|api_key)[a-zA-Z0-9_-]+'
    excludes: ['**/test/**', '**/androidTest/**', '**/BuildConfig.kt']

# 确保正确的依赖注入使用
coroutines:
  # 检查协程作用域使用
  GlobalCoroutineUsage:
    active: true
    excludes: ['**/test/**', '**/androidTest/**']

# 性能相关规则
performance:
  # 避免在循环中创建对象
  ObjectCreationInLoop:
    active: true
    excludes: ['**/test/**', '**/androidTest/**']

# 安全相关规则
security:
  # 检测敏感信息泄露
  SensitiveDataExposure:
    active: true
    sensitivePatterns:
      - 'password'
      - 'secret'
      - 'token'
      - 'key'
    excludes: ['**/test/**', '**/androidTest/**']
