package com.example.gymbro.data.remote.firebase.datasource

import com.google.firebase.database.FirebaseDatabase
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Firebase实时数据库数据源，封装所有与Realtime Database交互的操作
 */
@Singleton
class RealtimeDatabaseDataSource
@Inject
constructor(
    private val database: FirebaseDatabase,
) {
    companion object {
        // 路径常量
        private const val USER_LOCATIONS_PATH = "user_locations"
    }

    // 用户位置相关操作
}
