# Core-Arch Module

## 概述

Core-Arch模块提供MVI（Model-View-Intent）架构的基础设施，用于构建可预测、可测试的UI组件。

## 核心组件

### 1. AppIntent
```kotlin
interface AppIntent
```
所有用户意图的基础接口，表示用户交互或系统事件。

### 2. UiState
```kotlin
interface UiState
interface BaseUiState : UiState
interface ListUiState<T> : BaseUiState
interface DetailUiState<T> : BaseUiState
```
UI状态的契约定义，包含加载、错误、数据等状态。

### 3. UiEffect
```kotlin
interface UiEffect
sealed class SystemEffect : UiEffect
```
一次性副作用，如导航、Toast消息、对话框等。

### 4. Reducer
```kotlin
interface Reducer<Intent, State, Effect>
abstract class BaseReducer<Intent, State, Effect>
```
纯函数状态归约器，处理Intent并产生新的State和Effect。

### 5. MviViewModel
```kotlin
abstract class MviViewModel<Intent, State, Effect>
abstract class SimpleMviViewModel<Intent, State, Effect>
```
MVI架构的ViewModel基类，提供Intent分发、State管理、Effect处理。

## 使用示例

### 定义Contract
```kotlin
object FeatureContract {
    sealed class Intent : AppIntent {
        object LoadData : Intent()
        data class UpdateItem(val id: String) : Intent()
    }

    data class State(
        override val isLoading: Boolean = false,
        override val error: UiText? = null,
        val items: List<Item> = emptyList()
    ) : ListUiState<Item> {
        override val isRefreshing: Boolean = false
        override val hasMore: Boolean = false
    }

    sealed class Effect : UiEffect {
        data class ShowToast(val message: UiText) : Effect()
        object NavigateBack : Effect()
    }
}
```

### 实现Reducer
```kotlin
@Singleton
class FeatureReducer @Inject constructor() :
    BaseReducer<FeatureContract.Intent, FeatureContract.State, FeatureContract.Effect>() {

    override fun reduceInternal(
        intent: FeatureContract.Intent,
        currentState: FeatureContract.State
    ): ReduceResult<FeatureContract.State, FeatureContract.Effect> {
        return when (intent) {
            is FeatureContract.Intent.LoadData -> {
                ReduceResult.stateOnly(
                    currentState.copy(isLoading = true, error = null)
                )
            }
            // ... 其他Intent处理
        }
    }
}
```

### 实现ViewModel
```kotlin
@HiltViewModel
class FeatureViewModel @Inject constructor(
    private val featureReducer: FeatureReducer
) : MviViewModel<FeatureContract.Intent, FeatureContract.State, FeatureContract.Effect>(
    initialState = FeatureContract.State()
) {
    override val reducer = featureReducer

    fun loadData() = dispatch(FeatureContract.Intent.LoadData)
}
```

## 架构原则

1. **单向数据流**: Intent → Reducer → State → UI → Intent
2. **纯函数归约**: Reducer必须是纯函数，便于测试
3. **状态不可变**: State对象不可变，每次都创建新实例
4. **副作用隔离**: 副作用通过Effect处理，不直接修改State
5. **类型安全**: 强类型约束，编译时检查

## 测试支持

```kotlin
@Test
fun `should handle load data intent`() {
    val reducer = FeatureReducer()
    val initialState = FeatureContract.State()
    val intent = FeatureContract.Intent.LoadData

    val result = reducer.reduce(intent, initialState)

    assertTrue(result.newState.isLoading)
    assertNull(result.newState.error)
}
```

## 迁移指南

从现有ViewModel迁移到MVI架构：

1. 定义Feature Contract（Intent、State、Effect）
2. 实现Reducer处理业务逻辑
3. 继承MviViewModel替换现有ViewModel
4. 更新UI层使用dispatch()发送Intent
5. 处理Effect进行副作用操作

## 最佳实践

- Intent命名要明确表达用户意图
- State保持简单，避免嵌套过深
- Effect用于一次性操作，不要用于状态同步
- Reducer保持纯函数，不要有副作用
- 单个Feature的文件控制在250行以内
