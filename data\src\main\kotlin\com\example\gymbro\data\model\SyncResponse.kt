package com.example.gymbro.data.model

import com.google.gson.annotations.SerializedName

/**
 * 同步响应数据模型
 * 用于服务器同步操作的响应数据
 */
data class SyncResponse(
    /**
     * 同步是否成功
     */
    @SerializedName("success")
    val success: Boolean = false,

    /**
     * 服务器端时间戳
     */
    @SerializedName("serverTimestamp")
    val serverTimestamp: Long = System.currentTimeMillis(),

    /**
     * 同步的条目数
     */
    @SerializedName("itemCount")
    val itemCount: Int = 0,

    /**
     * 处理的条目IDs
     */
    @SerializedName("processedIds")
    val processedIds: List<String> = emptyList(),

    /**
     * 失败的条目IDs及原因
     */
    @SerializedName("failedItems")
    val failedItems: Map<String, String> = emptyMap(),

    /**
     * 需要客户端手动解决的冲突
     */
    @SerializedName("conflicts")
    val conflicts: List<SyncConflict> = emptyList(),

    /**
     * 错误信息(如果有)
     */
    @SerializedName("errorMessage")
    val errorMessage: String? = null,
) {
    /**
     * 同步冲突数据类
     */
    data class SyncConflict(
        /**
         * 冲突的项目ID
         */
        @SerializedName("itemId")
        val itemId: String,

        /**
         * 冲突类型
         */
        @SerializedName("type")
        val type: String, // "DELETE_MODIFIED", "CONCURRENT_MODIFICATION", etc.

        /**
         * 服务器版本的数据
         */
        @SerializedName("serverData")
        val serverData: Map<String, Any>? = null,

        /**
         * 本地版本的时间戳
         */
        @SerializedName("localTimestamp")
        val localTimestamp: Long = 0L,

        /**
         * 服务器版本的时间戳
         */
        @SerializedName("serverTimestamp")
        val serverTimestamp: Long = 0L,
    )
}
