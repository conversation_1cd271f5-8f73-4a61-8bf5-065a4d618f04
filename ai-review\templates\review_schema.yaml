module: ":features/auth"                 # Gradle path
verdict: "changes_requested"             # approve / changes_requested / comment_only / insufficient_review
highlights:
  - "[Dependency] features↛core-network 违反分层"
  - "[Compose] LazyColumn 缺少 key"
issues:
  - id: "DEP_LAYER_001"                  # 规则或自定义编号
    severity: "critical"                 # critical / major / minor
    file: "build.gradle.kts"             # 若非文件级问题可留空
    line: 42                             # 行号, 0 表示未知
    message: "features/auth 直接依赖 core-network"
    suggestion: "抽象接口于 domain，再由 data 实现"
statistics:
  changed_lines: 214
  issue_count: 9
  scan_duration_ms: 4123
