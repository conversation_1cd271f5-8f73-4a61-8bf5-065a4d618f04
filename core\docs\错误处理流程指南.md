# 错误处理流程指南

本文档提供了GymBro项目中统一的错误处理流程指南，包括在不同层次中如何处理错误，以及如何使用统一的错误处理工具类和扩展函数。

## 1. 错误处理工具类

GymBro项目使用`CoreErrorHandlingUtils`作为统一的错误处理工具类，提供了以下标准化的错误处理方法：

- `safeCall`: 通用的安全调用方法，适用于所有类型的操作
- `safeApiCall`: 专门用于API调用的安全方法
- `safeDbCall`: 专门用于数据库操作的安全方法
- `safeCallWithRetry`: 带重试的安全调用方法
- `safeCallWithRecovery`: 带恢复策略的安全调用方法
- `handleThrowable`: 将Throwable转换为ModernResult.Error
- `asModernResultFlow`: 将Flow转换为ModernResult Flow

## 2. 错误处理流程

### 2.1 Repository层

在Repository层，我们应该捕获所有异常，并将它们转换为`ModernResult.Error`。推荐使用`CoreErrorHandlingUtils`中的`safeCall`、`safeApiCall`或`safeDbCall`方法。

```kotlin
class UserRepositoryImpl @Inject constructor(
    private val errorHandlingUtils: CoreErrorHandlingUtils,
    private val userApi: UserApi,
    private val userDao: UserDao
) : UserRepository {

    override suspend fun getUser(userId: String): ModernResult<User> {
        return errorHandlingUtils.safeApiCall(
            block = { userApi.getUser(userId) },
            errorMessage = UiText.DynamicString("获取用户信息失败")
        )
    }

    override suspend fun saveUser(user: User): ModernResult<Unit> {
        return errorHandlingUtils.safeDbCall(
            block = { userDao.insertUser(user) },
            errorMessage = UiText.DynamicString("保存用户信息失败")
        )
    }

    override fun observeUser(userId: String): Flow<ModernResult<User>> {
        return userDao.observeUser(userId)
            .asModernResultFlow()
    }
}
```

### 2.2 UseCase层

在UseCase层，我们应该处理错误并应用恢复策略。推荐使用`ModernResultExtensions`中的扩展函数。

```kotlin
class GetUserUseCase @Inject constructor(
    private val userRepository: UserRepository,
    private val errorHandlingUtils: CoreErrorHandlingUtils
) {

    suspend operator fun invoke(userId: String): ModernResult<User> {
        return userRepository.getUser(userId)
            .recover { error ->
                // 尝试从缓存中恢复
                when (error.type) {
                    is GlobalErrorType.Network -> {
                        userRepository.getCachedUser(userId)
                    }
                    else -> ModernResult.Error(error)
                }
            }
    }
}
```

### 2.3 ViewModel层

在ViewModel层，我们应该将错误转换为用户友好的消息，并更新UI状态。推荐使用`BaseViewModel`中的`handleResult`方法。

```kotlin
@HiltViewModel
class UserViewModel @Inject constructor(
    private val getUserUseCase: GetUserUseCase,
    private val saveUserUseCase: SaveUserUseCase,
    private val errorHandler: ModernErrorHandler
) : BaseViewModel<UserUiState>(UserUiState()) {

    fun getUser(userId: String) {
        executeOperation(
            operationName = "getUser",
            params = mapOf("userId" to userId),
            execution = { getUserUseCase(userId) },
            handleSuccess = { user ->
                updateState(
                    { state -> state.copy(user = user, isLoading = false, error = null) },
                    "getUser.success"
                )
            }
        )
    }
}
```

### 2.4 UI层

在UI层，我们应该显示错误消息并提供恢复选项。推荐使用`ModernResultExtensions`中的`fold`方法。

```kotlin
@Composable
fun UserScreen(
    viewModel: UserViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.getUser("123")
    }

    uiState.user?.let { user ->
        UserContent(user = user)
    }

    if (uiState.isLoading) {
        LoadingIndicator()
    }

    uiState.error?.let { error ->
        ErrorView(
            error = error,
            onRetry = { viewModel.getUser("123") }
        )
    }
}
```

## 3. 错误恢复策略

在需要恢复策略的地方，我们应该使用`RecoveryStrategy`接口及其实现。推荐使用`CoreErrorHandlingUtils`中的`safeCallWithRecovery`方法。

```kotlin
class SyncUserUseCase @Inject constructor(
    private val userRepository: UserRepository,
    private val errorHandlingUtils: CoreErrorHandlingUtils
) {

    suspend operator fun invoke(userId: String): ModernResult<Unit> {
        val retryStrategy = RetryStrategy.forNetwork(
            operation = { userRepository.syncUser(userId) },
            maxRetries = 3
        )

        return errorHandlingUtils.safeCallWithRecovery(
            block = { userRepository.syncUser(userId) },
            recoveryStrategy = retryStrategy,
            errorMessage = UiText.DynamicString("同步用户信息失败")
        )
    }
}
```

## 4. 错误处理最佳实践

1. 使用`ModernResult<T>`作为所有异步操作的返回类型
2. 使用`CoreErrorHandlingUtils`中的方法进行安全调用
3. 使用`ModernResultExtensions`中的扩展函数处理结果
4. 使用`RecoveryStrategy`接口及其实现进行错误恢复
5. 在Repository层捕获异常并转换为`ModernResult.Error`
6. 在UseCase层处理错误并应用恢复策略
7. 在ViewModel层将错误转换为用户友好的消息
8. 在UI层显示错误消息并提供恢复选项

## 5. 错误处理流程图

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│     UI      │     │  ViewModel  │     │   UseCase   │     │ Repository  │
└──────┬──────┘     └──────┬──────┘     └──────┬──────┘     └──────┬──────┘
       │                   │                   │                   │
       │  用户操作请求      │                   │                   │
       │ ─────────────────>│                   │                   │
       │                   │                   │                   │
       │                   │  调用UseCase      │                   │
       │                   │ ─────────────────>│                   │
       │                   │                   │                   │
       │                   │                   │  调用Repository   │
       │                   │                   │ ─────────────────>│
       │                   │                   │                   │
       │                   │                   │                   │  safeApiCall
       │                   │                   │                   │ ───────────┐
       │                   │                   │                   │            │
       │                   │                   │                   │ <──────────┘
       │                   │                   │                   │
       │                   │                   │  ModernResult     │
       │                   │                   │ <─────────────────│
       │                   │                   │                   │
       │                   │                   │  应用恢复策略      │
       │                   │                   │ ───────────┐      │
       │                   │                   │            │      │
       │                   │                   │ <──────────┘      │
       │                   │                   │                   │
       │                   │  ModernResult     │                   │
       │                   │ <─────────────────│                   │
       │                   │                   │                   │
       │                   │  更新UI状态        │                   │
       │                   │ ───────────┐      │                   │
       │                   │            │      │                   │
       │                   │ <──────────┘      │                   │
       │                   │                   │                   │
       │  UI状态更新        │                   │                   │
       │ <─────────────────│                   │                   │
       │                   │                   │                   │
       │  显示结果/错误      │                   │                   │
       │ ───────────┐      │                   │                   │
       │            │      │                   │                   │
       │ <──────────┘      │                   │                   │
       │                   │                   │                   │
```

通过遵循这些最佳实践，我们可以确保项目中的所有代码都遵循相同的错误处理模式，提高代码的可维护性和可读性。
