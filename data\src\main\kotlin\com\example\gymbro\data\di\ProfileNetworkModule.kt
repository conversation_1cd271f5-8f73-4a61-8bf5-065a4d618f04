package com.example.gymbro.data.di

import com.example.gymbro.core.network.config.NetworkConfig
import com.example.gymbro.data.network.api.UserProfileApiService
import com.jakewharton.retrofit2.converter.kotlinx.serialization.asConverterFactory
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.serialization.json.Json
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import javax.inject.Named
import javax.inject.Singleton

/**
 * Profile网络模块DI配置
 *
 * 🎯 职责：
 * - 提供UserProfileApiService的Retrofit实例
 * - 绑定UserProfileRepository的实现
 * - 使用core-network提供的OkHttpClient和NetworkConfig
 * - 遵循"DTO in shared-models, API in data"的架构原则
 */
@Module
@InstallIn(SingletonComponent::class)
object ProfileNetworkModule {
    /**
     * 提供UserProfileApiService
     *
     * 🔧 使用core-network提供的组件：
     * - OkHttpClient: 来自core-network，包含完整拦截器链
     * - NetworkConfig: 统一配置，包含restBase等
     * - Json: 序列化配置
     */
    @Provides
    @Singleton
    fun provideUserProfileApiService(
        @Named("rest_client") okHttpClient: OkHttpClient, // 来自core-network
        @Named("core_network_json") json: Json, // 来自core-network
        config: NetworkConfig, // 来自core-network
    ): UserProfileApiService =
        Retrofit
            .Builder()
            .baseUrl(config.getSecureRestUrl()) // 使用NetworkConfig，无硬编码
            .client(okHttpClient) // 使用配置好的OkHttpClient
            .addConverterFactory(
                json.asConverterFactory("application/json".toMediaType()),
            ).build()
            .create(UserProfileApiService::class.java)
}

/**
 * 🧹 REMOVED: ProfileRepositoryBindModule
 *
 * Repository绑定已经在features/profile/internal/di/ProfileModule中处理
 * 避免重复绑定错误，这里只提供API服务
 *
 * 架构原则：
 * - data模块：提供API服务和Repository实现
 * - feature模块：负责自己的DI绑定
 * - 避免跨模块重复绑定同一接口
 */
