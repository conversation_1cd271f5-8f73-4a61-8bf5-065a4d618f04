<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 暗色基础主题，继承自 Material3 的 Dark 主题 -->
    <style name="Theme.GymBro.Base.Dark" parent="Theme.Material3.Dark.NoActionBar">
        <!-- 定义夜间模式特定的基础 XML 属性 -->
        <item name="android:windowBackground">@android:color/black</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        
        <!-- 与 DarkColorScheme 匹配的颜色集 -->
        <item name="colorPrimary">@color/md_theme_dark_primary</item>
        <item name="colorOnPrimary">@color/md_theme_dark_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_dark_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_dark_onPrimaryContainer</item>
        <item name="colorSecondary">@color/md_theme_dark_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_dark_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_dark_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_dark_onSecondaryContainer</item>
        <item name="colorTertiary">@color/md_theme_dark_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_dark_onTertiary</item>
        <item name="android:colorBackground">@color/md_theme_dark_background</item>
        <item name="colorOnBackground">@color/md_theme_dark_onBackground</item>
        <item name="colorSurface">@color/md_theme_dark_surface</item>
        <item name="colorOnSurface">@color/md_theme_dark_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_dark_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_dark_onSurfaceVariant</item>
        <item name="colorOutline">@color/md_theme_dark_outline</item>
        <item name="colorError">@color/md_theme_dark_error</item>
        <item name="colorOnError">@color/md_theme_dark_onError</item>
        <item name="colorErrorContainer">@color/md_theme_dark_errorContainer</item>
        <item name="colorOnErrorContainer">@color/md_theme_dark_onErrorContainer</item>
        <item name="colorSurfaceInverse">@color/md_theme_dark_inverseSurface</item>
        <item name="colorOnSurfaceInverse">@color/md_theme_dark_inverseOnSurface</item>
        <item name="colorPrimaryInverse">@color/md_theme_dark_inversePrimary</item>
        <item name="colorOutlineVariant">@color/md_theme_dark_outlineVariant</item>
        <item name="scrimBackground">@color/md_theme_dark_scrim</item>
        <item name="colorSurfaceTint">@color/md_theme_dark_surfaceTint</item>
    </style>

    <!-- 应用的暗色主题 -->
    <style name="Theme.GymBro.Dark" parent="Theme.GymBro.Base.Dark" />
</resources>