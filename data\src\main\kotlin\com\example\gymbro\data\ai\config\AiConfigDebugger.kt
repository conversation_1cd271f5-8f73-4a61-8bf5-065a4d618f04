package com.example.gymbro.data.config

import com.example.gymbro.domain.coach.config.AiConfig
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * AI配置调试器
 * 用于诊断OPENAI配置问题
 */
@Singleton
class AiConfigDebugger
@Inject
constructor(
    private val aiConfig: AiConfig,
) {
    /**
     * 打印所有AI配置信息（隐藏敏感信息）
     */
    fun logConfigStatus() {
        Timber.d("=== AI Configuration Status ===")

        // Google配置
        Timber.d(
            "Google API Key: ${if (aiConfig.googleApiKey.isNotEmpty()) {
                "✅ Configured (${aiConfig.googleApiKey.take(
                    10,
                )}...)"
            } else {
                "❌ Empty"
            }}",
        )
        Timber.d(
            "Google Base URL: ${if (aiConfig.googleBaseUrl.isNotEmpty()) "✅ ${aiConfig.googleBaseUrl}" else "❌ Empty"}",
        )
        Timber.d(
            "Google Model: ${if (aiConfig.defaultGoogleModel.isNotEmpty()) "✅ ${aiConfig.defaultGoogleModel}" else "❌ Empty"}",
        )

        // OpenAI配置
        Timber.d(
            "OpenAI API Key: ${if (aiConfig.openaiApiKey.isNotEmpty()) {
                "✅ Configured (${aiConfig.openaiApiKey.take(
                    10,
                )}...)"
            } else {
                "❌ Empty"
            }}",
        )
        Timber.d(
            "OpenAI Base URL: ${if (aiConfig.openaiBaseUrl.isNotEmpty()) "✅ ${aiConfig.openaiBaseUrl}" else "❌ Empty"}",
        )
        Timber.d(
            "OpenAI Model: ${if (aiConfig.openaiDefaultModel.isNotEmpty()) "✅ ${aiConfig.openaiDefaultModel}" else "❌ Empty"}",
        )

        // DeepSeek配置
        Timber.d(
            "DeepSeek API Key: ${if (aiConfig.deepseekApiKey.isNotEmpty()) {
                "✅ Configured (${aiConfig.deepseekApiKey.take(
                    10,
                )}...)"
            } else {
                "❌ Empty"
            }}",
        )
        Timber.d(
            "DeepSeek Base URL: ${if (aiConfig.deepseekBaseUrl.isNotEmpty()) "✅ ${aiConfig.deepseekBaseUrl}" else "❌ Empty"}",
        )
        Timber.d(
            "DeepSeek Model: ${if (aiConfig.defaultDeepseekModel.isNotEmpty()) "✅ ${aiConfig.defaultDeepseekModel}" else "❌ Empty"}",
        )

        Timber.d("=== End Configuration Status ===")
    }

    /**
     * 验证OPENAI配置是否完整
     */
    fun validateOpenAiConfig(): OpenAiValidationResult {
        val issues = mutableListOf<String>()

        if (aiConfig.openaiApiKey.isEmpty()) {
            issues.add("OPENAI_API_KEY 未配置或为空")
        } else if (!aiConfig.openaiApiKey.startsWith("sk-")) {
            issues.add("OPENAI_API_KEY 格式不正确，应以 'sk-' 开头")
        }

        if (aiConfig.openaiBaseUrl.isEmpty()) {
            issues.add("OPENAI_BASE_URL 未配置或为空")
        } else if (!aiConfig.openaiBaseUrl.startsWith("http")) {
            issues.add("OPENAI_BASE_URL 格式不正确，应以 'http' 开头")
        }

        if (aiConfig.openaiDefaultModel.isEmpty()) {
            issues.add("OPENAI_DEFAULT_MODEL 未配置或为空")
        }

        return OpenAiValidationResult(
            isValid = issues.isEmpty(),
            issues = issues,
            config =
            OpenAiConfigSnapshot(
                apiKey = aiConfig.openaiApiKey,
                baseUrl = aiConfig.openaiBaseUrl,
                defaultModel = aiConfig.openaiDefaultModel,
            ),
        )
    }
}

/**
 * OpenAI配置验证结果
 */
data class OpenAiValidationResult(
    val isValid: Boolean,
    val issues: List<String>,
    val config: OpenAiConfigSnapshot,
)

/**
 * OpenAI配置快照
 */
data class OpenAiConfigSnapshot(
    val apiKey: String,
    val baseUrl: String,
    val defaultModel: String,
)
