package com.example.gymbro.data.workout.plan.database

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.example.gymbro.data.workout.plan.converter.PlanTypeConverters
import com.example.gymbro.data.workout.plan.dao.PlanDao
import com.example.gymbro.data.workout.plan.dao.PlanDayDao
import com.example.gymbro.data.workout.plan.dao.PlanTemplateDao
import com.example.gymbro.data.workout.plan.entity.PlanDayEntity
import com.example.gymbro.data.workout.plan.entity.PlanEntity
import com.example.gymbro.data.workout.plan.entity.PlanTemplateEntity
import com.example.gymbro.data.database.migrations.Migration_1_2

/**
 * PlanDB - 训练计划数据库
 *
 * 基于 05_Final_Database_Architecture.md 设计
 * 职责：存储训练计划调度信息，不存储实际内容
 * 特性：低变化频率，计划创建修改较少
 */
@Database(
    entities = [
        PlanEntity::class,
        PlanDayEntity::class,
        PlanTemplateEntity::class,
    ],
    version = 2,
    exportSchema = true,
)
@TypeConverters(PlanTypeConverters::class)
abstract class PlanDatabase : RoomDatabase() {

    abstract fun planDao(): PlanDao
    abstract fun planDayDao(): PlanDayDao
    abstract fun planTemplateDao(): PlanTemplateDao

    companion object {
        private const val DATABASE_NAME = "plan_database"

        @Volatile
        private var INSTANCE: PlanDatabase? = null

        fun getDatabase(context: Context): PlanDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    PlanDatabase::class.java,
                    DATABASE_NAME,
                )
                .addMigrations(Migration_1_2)
                .build()
                INSTANCE = instance
                instance
            }
        }
    }
}
