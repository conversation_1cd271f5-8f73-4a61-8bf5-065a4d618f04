package com.example.gymbro.core.network.metrics

/**
 * 网络监控指标接口
 * 🔧 P1: 可观测性支持，用于监控WebSocket连接质量
 */
interface NetworkMetrics {

    /**
     * 记录WebSocket连接延迟
     * @param latencyMs 延迟毫秒数
     */
    fun recordTokenLatency(latencyMs: Long)

    /**
     * 记录WebSocket重连次数
     * @param retryCount 重试次数
     */
    fun recordReconnect(retryCount: Int)

    /**
     * 记录WebSocket连接状态变化
     * @param fromState 原状态
     * @param toState 新状态
     */
    fun recordStateChange(fromState: String, toState: String)

    /**
     * 记录错误事件
     * @param errorType 错误类型
     * @param errorMessage 错误消息
     */
    fun recordError(errorType: String, errorMessage: String)
}

/**
 * MetricsReporter - 按照websock+Okhttp.md核心实现方案
 *
 * 🎯 实现指标收集：
 * - ws_token_latency_ms: Token延迟指标
 * - ws_reconnect_count: WebSocket重连次数
 * - rest_http_code: REST API状态码统计
 * - crash_anr_ws: WebSocket相关崩溃
 */
class MetricsReporter {

    /**
     * 记录Token延迟指标
     * 每条token收到即埋点
     */
    fun tickTokenLatency(latencyMs: Long) {
        timber.log.Timber.d("📊 ws_token_latency_ms: ${latencyMs}ms")
    }

    /**
     * 增加重连计数
     * onFailure事件触发
     */
    fun incReconnect() {
        timber.log.Timber.i("📊 ws_reconnect_count: +1")
    }

    /**
     * 记录HTTP状态码
     * Retrofit EventListener触发
     */
    fun tagHttp(code: Int, url: String = "") {
        timber.log.Timber.d("📊 rest_http_code: $code (url: $url)")
    }

    /**
     * 记录WebSocket崩溃
     * Crashlytics key=ws_state
     */
    fun recordWebSocketCrash(wsState: String, error: Throwable) {
        timber.log.Timber.e("📊 crash_anr_ws: state=$wsState, error=${error.message}")
    }

    /**
     * 记录状态变化
     */
    fun recordStateChange(fromState: String, toState: String) {
        timber.log.Timber.d("📊 ws_state_change: $fromState -> $toState")
    }

    /**
     * 记录错误
     */
    fun recordError(errorType: String, errorMessage: String) {
        timber.log.Timber.w("📊 ws_error: $errorType - $errorMessage")
    }
}

/**
 * 默认的Metrics实现 - 使用Timber记录
 * 🔧 生产环境可替换为Firebase Analytics或其他监控系统
 */
class TimberNetworkMetrics : NetworkMetrics {

    override fun recordTokenLatency(latencyMs: Long) {
        timber.log.Timber.d("📊 ws_token_latency_ms: $latencyMs")
    }

    override fun recordReconnect(retryCount: Int) {
        timber.log.Timber.i("📊 ws_reconnect_count: $retryCount")
    }

    override fun recordStateChange(fromState: String, toState: String) {
        timber.log.Timber.d("📊 ws_state_change: $fromState -> $toState")
    }

    override fun recordError(errorType: String, errorMessage: String) {
        timber.log.Timber.w("📊 ws_error: $errorType - $errorMessage")
    }
}
