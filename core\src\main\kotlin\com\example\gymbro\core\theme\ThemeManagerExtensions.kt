package com.example.gymbro.core.theme

/**
 * 主题管理器扩展功能
 *
 * 提供便捷的主题切换和状态转换方法，简化主题管理的使用。
 */

/**
 * 切换到下一个主题模式
 * 循环顺序：浅色 -> 深色 -> 跟随系统 -> 浅色
 */
fun ThemeManager.toggleTheme() {
    val currentTheme = getCurrentTheme()
    val nextTheme = when (currentTheme) {
        ThemeMode.LIGHT -> ThemeMode.DARK
        ThemeMode.DARK -> ThemeMode.SYSTEM
        ThemeMode.SYSTEM -> ThemeMode.LIGHT
    }
    setTheme(nextTheme)
}

/**
 * 获取当前主题的字符串表示
 *
 * @return 主题模式的字符串形式，用于UI组件显示
 */
fun ThemeManager.getCurrentThemeString(): String {
    return when (getCurrentTheme()) {
        ThemeMode.LIGHT -> ThemeManager.THEME_LIGHT
        ThemeMode.DARK -> ThemeManager.THEME_DARK
        ThemeMode.SYSTEM -> "system"
    }
}

/**
 * 检查当前是否为指定主题模式
 *
 * @param themeMode 要检查的主题模式
 * @return true如果当前主题匹配指定模式
 */
fun ThemeManager.isCurrentTheme(themeMode: ThemeMode): Boolean {
    return getCurrentTheme() == themeMode
}

/**
 * 检查当前是否为浅色主题
 */
fun ThemeManager.isLightMode(): Boolean = isCurrentTheme(ThemeMode.LIGHT)

/**
 * 检查当前是否为深色主题
 */
fun ThemeManager.isDarkMode(): Boolean {
    return when (getCurrentTheme()) {
        ThemeMode.DARK -> true
        ThemeMode.LIGHT -> false
        ThemeMode.SYSTEM -> {
            // 系统模式下需要检查系统设置
            // 这里可以添加系统设置检查逻辑
            false
        }
    }
}

/**
 * 检查当前是否为跟随系统主题
 */
fun ThemeManager.isSystemMode(): Boolean = isCurrentTheme(ThemeMode.SYSTEM)

/**
 * 获取主题模式的显示名称
 */
fun ThemeManager.getThemeDisplayName(): String {
    return when (getCurrentTheme()) {
        ThemeMode.LIGHT -> "浅色模式"
        ThemeMode.DARK -> "深色模式"
        ThemeMode.SYSTEM -> "跟随系统"
    }
}

/**
 * 切换到浅色主题
 */
fun ThemeManager.switchToLight() {
    setTheme(ThemeMode.LIGHT)
}

/**
 * 切换到深色主题
 */
fun ThemeManager.switchToDark() {
    setTheme(ThemeMode.DARK)
}

/**
 * 切换到系统主题
 */
fun ThemeManager.switchToSystem() {
    setTheme(ThemeMode.SYSTEM)
}
