package com.example.gymbro.di.network

/**
 * 🧹 DEPRECATED: 旧的Core Network模块DI配置
 *
 * 已完全迁移到core-network模块中的CoreNetworkModule
 * 避免重复绑定错误，此文件已清空
 *
 * 新的网络配置位置：
 * - core-network/src/main/kotlin/com/example/gymbro/core/network/di/CoreNetworkModule.kt
 *
 * 🔧 Step 5: DI整合 - 唯一网络DI入口
 * 所有网络相关的依赖注入现在统一在core-network模块中管理
 *
 * 重复绑定已解决：
 * - LlmStreamClient
 * - NetworkConfig
 * - @Named("ws_client") OkHttpClient
 * - @Named("rest_client") OkHttpClient
 * - @Named("core_network_json") Json
 * - NetworkMonitor
 * - NetworkWatchdog
 */
