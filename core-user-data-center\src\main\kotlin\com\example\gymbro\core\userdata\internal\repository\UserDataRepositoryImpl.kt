package com.example.gymbro.core.userdata.internal.repository

import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.core.userdata.api.model.SyncStatus
import com.example.gymbro.core.userdata.api.model.UnifiedUserData
import com.example.gymbro.core.userdata.internal.datasource.UserLocalDataSource
import com.example.gymbro.core.userdata.internal.di.DefaultUserDataSource
import com.example.gymbro.core.userdata.internal.mapper.UserDataMapper
import com.example.gymbro.domain.model.auth.AuthUser
import com.example.gymbro.domain.profile.model.user.UserProfile
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 用户数据仓库实现
 *
 * 基于 Room 数据库的用户数据管理实现，作为用户数据的单一真实来源（SSOT）。
 * 遵循 userdata-center保存方案.md 的设计原则，使用 UserLocalDataSource 进行数据持久化。
 *
 * 设计特点：
 * - Room 持久化: 使用 UserLocalDataSource 进行数据库操作
 * - 响应式: 通过 Flow 提供实时数据更新
 * - 统一管理: 作为 Auth、Profile、Coach 模块的统一数据源
 * - 并发安全: 使用 Mutex 保护写操作
 *
 * 根据 userdata-center保存方案.md 的设计，这个实现将：
 * 1. 统一收敛所有用户数据写入操作
 * 2. 通过 UserLocalDataSource 进行 Room 数据库持久化
 * 3. 提供响应式的 observeUserData() 接口
 * 4. 支持 syncAuthData() 和 updateUserProfile() 操作
 * 5. 维护数据同步状态和冲突解决
 */
@Singleton
class UserDataRepositoryImpl @Inject constructor(
    @DefaultUserDataSource private val userLocalDataSource: UserLocalDataSource,
    private val userDataMapper: UserDataMapper,
    private val logger: Logger
) : UserDataRepository {

    companion object {
        private const val TAG = "UserDataRepositoryImpl"
    }

    // 并发控制
    private val operationMutex = Mutex()

    /**
     * 观察用户数据变化
     *
     * 根据 userdata-center保存方案.md，这是 UserDataCenter 的核心方法。
     * 提供响应式的用户数据流，作为所有消费模块的统一数据源。
     * 现在直接委托给 UserLocalDataSource。
     */
    override fun observeUserData(): Flow<UnifiedUserData?> {
        logger.d(TAG, "开始观察用户数据变化")

        return userLocalDataSource.observeUserData()
    }

    /**
     * 获取当前用户数据
     */
    override suspend fun getCurrentUserData(): ModernResult<UnifiedUserData?> {
        logger.d(TAG, "获取当前用户数据")
        return userLocalDataSource.getCurrentUserData()
    }

    /**
     * 保存用户数据
     */
    override suspend fun saveUserData(userData: UnifiedUserData): ModernResult<Unit> {
        return operationMutex.withLock {
            try {
                logger.d(TAG, "保存用户数据: userId=${userData.userId}")

                // 验证数据
                val validationErrors = validateUserData(userData)
                if (validationErrors.isNotEmpty()) {
                    logger.w(TAG, "用户数据验证失败: $validationErrors")
                    return ModernResult.Error(
                        BusinessErrors.BusinessError.rule(
                            operationName = "saveUserData",
                            message = UiText.DynamicString("数据验证失败: ${validationErrors.joinToString(", ")}"),
                            recoverable = true
                        )
                    )
                }

                // 将统一数据分解为认证数据和资料数据，分别保存
                val authUser = userDataMapper.toAuthUser(userData)
                val userProfile = userDataMapper.toUserProfile(userData)

                // 保存认证数据
                val authResult = userLocalDataSource.saveAuthData(authUser)
                if (authResult is ModernResult.Error) {
                    return authResult
                }

                // 保存资料数据
                val profileResult = userLocalDataSource.saveProfileData(userProfile)
                if (profileResult is ModernResult.Error) {
                    return profileResult
                }

                logger.d(TAG, "用户数据保存成功")
                ModernResult.Success(Unit)

            } catch (e: Exception) {
                logger.e(e, TAG, "保存用户数据时发生异常")
                ModernResult.Error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "saveUserData",
                        message = UiText.DynamicString("保存用户数据失败: ${e.message}"),
                        recoverable = true
                    )
                )
            }
        }
    }

    /**
     * 更新认证数据
     */
    override suspend fun updateAuthData(authUser: AuthUser): ModernResult<Unit> {
        return operationMutex.withLock {
            try {
                logger.d(TAG, "更新认证数据: userId=${authUser.uid}")

                // 直接委托给 UserLocalDataSource
                val result = userLocalDataSource.updateAuthData(authUser)

                when (result) {
                    is ModernResult.Success -> {
                        logger.d(TAG, "认证数据更新成功")
                        ModernResult.Success(Unit)
                    }
                    is ModernResult.Error -> {
                        logger.e(TAG, "认证数据更新失败: ${result.error}")
                        result
                    }
                    else -> {
                        logger.w(TAG, "认证数据更新超时")
                        ModernResult.Success(Unit)
                    }
                }

            } catch (e: Exception) {
                logger.e(e, TAG, "更新认证数据时发生异常")
                ModernResult.Error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "updateAuthData",
                        message = UiText.DynamicString("更新认证数据失败: ${e.message}"),
                        recoverable = true
                    )
                )
            }
        }
    }

    /**
     * 更新用户资料数据
     */
    override suspend fun updateProfileData(userId: String, profileData: UserProfile): ModernResult<Unit> {
        return operationMutex.withLock {
            try {
                logger.d(TAG, "更新用户资料数据: userId=$userId")

                // 委托给 UserLocalDataSource
                val result = userLocalDataSource.updateProfileData(profileData)

                when (result) {
                    is ModernResult.Success -> {
                        logger.d(TAG, "用户资料数据更新成功")
                        ModernResult.Success(Unit)
                    }
                    is ModernResult.Error -> {
                        logger.e(TAG, "用户资料数据更新失败: ${result.error}")
                        result
                    }
                    else -> {
                        logger.w(TAG, "用户资料数据更新超时")
                        ModernResult.Success(Unit)
                    }
                }

            } catch (e: Exception) {
                logger.e(e, TAG, "更新用户资料数据时发生异常")
                ModernResult.Error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "updateProfileData",
                        message = UiText.DynamicString("更新用户资料失败: ${e.message}"),
                        recoverable = true
                    )
                )
            }
        }
    }

    /**
     * 清除用户数据
     */
    override suspend fun clearUserData(): ModernResult<Unit> {
        return operationMutex.withLock {
            try {
                logger.d(TAG, "清除用户数据")

                // 委托给 UserLocalDataSource
                val result = userLocalDataSource.clearAllUserData()

                when (result) {
                    is ModernResult.Success -> {
                        logger.d(TAG, "用户数据清除成功")
                        ModernResult.Success(Unit)
                    }
                    is ModernResult.Error -> {
                        logger.e(TAG, "用户数据清除失败: ${result.error}")
                        result
                    }
                    else -> {
                        logger.w(TAG, "用户数据清除超时")
                        ModernResult.Success(Unit)
                    }
                }

            } catch (e: Exception) {
                logger.e(e, TAG, "清除用户数据时发生异常")
                ModernResult.Error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "clearUserData",
                        message = UiText.DynamicString("清除用户数据失败: ${e.message}"),
                        recoverable = true
                    )
                )
            }
        }
    }

    /**
     * 获取同步状态
     */
    override suspend fun getSyncStatus(userId: String): ModernResult<SyncStatus> {
        return try {
            logger.d(TAG, "获取同步状态: userId=$userId")

            // 从当前用户数据中获取同步状态
            val currentDataResult = userLocalDataSource.getCurrentUserData()
            when (currentDataResult) {
                is ModernResult.Success -> {
                    val userData = currentDataResult.data
                    val status = if (userData?.userId == userId) {
                        userData.syncStatus
                    } else {
                        SyncStatus.SYNCED
                    }
                    logger.d(TAG, "获取同步状态成功: userId=$userId, status=$status")
                    ModernResult.Success(status)
                }
                is ModernResult.Error -> {
                    logger.e(TAG, "获取同步状态失败: ${currentDataResult.error}")
                    currentDataResult
                }
                else -> {
                    logger.w(TAG, "获取同步状态超时")
                    ModernResult.Success(SyncStatus.SYNCED)
                }
            }
        } catch (e: Exception) {
            logger.e(e, TAG, "获取同步状态时发生异常")
            ModernResult.Error(
                BusinessErrors.BusinessError.rule(
                    operationName = "getSyncStatus",
                    message = UiText.DynamicString("获取同步状态失败: ${e.message}"),
                    recoverable = true
                )
            )
        }
    }

    /**
     * 更新同步状态
     */
    override suspend fun updateSyncStatus(userId: String, status: SyncStatus): ModernResult<Unit> {
        return try {
            logger.d(TAG, "更新同步状态: userId=$userId, status=$status")

            // 简化实现：同步状态更新通过重新保存用户数据来实现
            val currentDataResult = userLocalDataSource.getCurrentUserData()
            when (currentDataResult) {
                is ModernResult.Success -> {
                    val userData = currentDataResult.data
                    if (userData?.userId == userId) {
                        val updatedData = userData.copy(syncStatus = status)
                        saveUserData(updatedData)
                    } else {
                        logger.w(TAG, "用户不存在，无法更新同步状态: userId=$userId")
                        ModernResult.Success(Unit)
                    }
                }
                is ModernResult.Error -> {
                    logger.e(TAG, "更新同步状态失败: ${currentDataResult.error}")
                    currentDataResult
                }
                else -> {
                    logger.w(TAG, "更新同步状态超时")
                    ModernResult.Success(Unit)
                }
            }
        } catch (e: Exception) {
            logger.e(e, TAG, "更新同步状态时发生异常")
            ModernResult.Error(
                BusinessErrors.BusinessError.rule(
                    operationName = "updateSyncStatus",
                    message = UiText.DynamicString("更新同步状态失败: ${e.message}"),
                    recoverable = true
                )
            )
        }
    }

    /**
     * 检查用户是否存在
     */
    override suspend fun userExists(userId: String): ModernResult<Boolean> {
        return try {
            logger.d(TAG, "检查用户是否存在: userId=$userId")

            // 委托给 UserLocalDataSource
            userLocalDataSource.userExists(userId)

        } catch (e: Exception) {
            logger.e(e, TAG, "检查用户存在性时发生异常")
            ModernResult.Error(
                BusinessErrors.BusinessError.rule(
                    operationName = "userExists",
                    message = UiText.DynamicString("检查用户存在性失败: ${e.message}"),
                    recoverable = true
                )
            )
        }
    }

    /**
     * 创建新用户数据
     */
    override suspend fun createUserData(authUser: AuthUser): ModernResult<UnifiedUserData> {
        return try {
            logger.d(TAG, "创建新用户数据: userId=${authUser.uid}")

            val newUserData = userDataMapper.combineAuthAndProfile(
                authUser = authUser,
                userProfile = null,
                syncStatus = SyncStatus.SYNCED
            )

            val saveResult = saveUserData(newUserData)
            when (saveResult) {
                is ModernResult.Success -> {
                    logger.d(TAG, "新用户数据创建成功")
                    ModernResult.Success(newUserData)
                }
                is ModernResult.Error -> {
                    logger.e(TAG, "保存新用户数据失败: ${saveResult.error}")
                    ModernResult.Error(saveResult.error)
                }
                else -> {
                    logger.w(TAG, "保存新用户数据超时")
                    ModernResult.Success(newUserData)
                }
            }
        } catch (e: Exception) {
            logger.e(e, TAG, "创建新用户数据时发生异常")
            ModernResult.Error(
                BusinessErrors.BusinessError.rule(
                    operationName = "createUserData",
                    message = UiText.DynamicString("创建用户数据失败: ${e.message}"),
                    recoverable = true
                )
            )
        }
    }

    /**
     * 合并用户数据
     *
     * 将新数据与现有数据合并，解决数据冲突
     */
    override fun mergeUserData(existing: UnifiedUserData, new: UnifiedUserData): UnifiedUserData {
        logger.d(TAG, "合并用户数据: existingVersion=${existing.dataVersion}, newVersion=${new.dataVersion}")

        // 以最新时间戳为准
        return if (new.lastUpdated >= existing.lastUpdated) {
            logger.d(TAG, "使用新数据（时间戳更新）")
            new.copy(
                // 保留一些不应该被覆盖的字段
                userId = existing.userId,
                // 合并统计数据（取较大值）
                totalActivityCount = maxOf(existing.totalActivityCount, new.totalActivityCount),
                weeklyActiveMinutes = maxOf(existing.weeklyActiveMinutes, new.weeklyActiveMinutes)
            )
        } else {
            logger.d(TAG, "保留现有数据（时间戳较新）")
            existing.copy(
                // 更新同步状态
                syncStatus = new.syncStatus,
                lastUpdated = maxOf(existing.lastUpdated, new.lastUpdated)
            )
        }
    }

    /**
     * 验证用户数据完整性
     */
    override fun validateUserData(userData: UnifiedUserData): List<String> {
        val errors = mutableListOf<String>()

        // 基础验证
        if (userData.userId.isBlank()) {
            errors.add("用户ID不能为空")
        }

        // 邮箱格式验证
        userData.email?.let { email ->
            if (email.isNotBlank() && !email.contains("@")) {
                errors.add("邮箱格式无效")
            }
        }

        // 身体数据验证
        userData.height?.let { height ->
            if (height <= 0 || height > 300) {
                errors.add("身高数据无效")
            }
        }

        userData.weight?.let { weight ->
            if (weight <= 0 || weight > 500) {
                errors.add("体重数据无效")
            }
        }

        // 数据版本验证
        if (userData.dataVersion.isBlank()) {
            errors.add("数据版本不能为空")
        }

        logger.d(TAG, "数据验证完成: ${errors.size} 个错误")
        return errors
    }

    // === 私有方法 ===

    /**
     * 从数据源刷新用户数据
     */
    private suspend fun refreshUserDataFromSources(userId: String): ModernResult<UnifiedUserData?> {
        return try {
            logger.d(TAG, "从数据源刷新用户数据: userId=$userId")

            // 委托给 UserLocalDataSource
            userLocalDataSource.getUserData(userId)

        } catch (e: Exception) {
            logger.e(e, TAG, "刷新用户数据时发生异常")
            ModernResult.Error(
                BusinessErrors.BusinessError.rule(
                    operationName = "refreshUserDataFromSources",
                    message = UiText.DynamicString("刷新用户数据失败: ${e.message}"),
                    recoverable = true
                )
            )
        }
    }
}
