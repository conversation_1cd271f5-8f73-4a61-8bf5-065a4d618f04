package com.example.gymbro.core.util

import com.example.gymbro.core.resources.ResourceProvider
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 常量提供器
 *
 * 使用ResourceProvider访问常量资源，替代直接使用硬编码字符串
 */
@Singleton
class ConstantsProvider
@Inject
constructor(
    private val resourceProvider: ResourceProvider,
) {
    /**
     * 获取字符串消息
     */
    fun getMessage(messageResId: Int): String = resourceProvider.getString(messageResId)

    /**
     * 获取带格式的字符串消息
     */
    fun getMessage(
        messageResId: Int,
        vararg args: Any,
    ): String = resourceProvider.getString(messageResId, *args)

    /**
     * 获取时间常量，单位毫秒
     */
    object Time {
        const val SECOND_MS = Constants.Time.SECOND_MS
        const val MINUTE_MS = Constants.Time.MINUTE_MS
        const val HOUR_MS = Constants.Time.HOUR_MS
        const val DAY_MS = Constants.Time.DAY_MS
    }

    /**
     * 获取默认值常量
     */
    object Defaults {
        const val DEFAULT_REST_TIME_SECONDS = Constants.Defaults.DEFAULT_REST_TIME_SECONDS
        const val DEFAULT_LIBRARY_VERSION = Constants.Defaults.DEFAULT_LIBRARY_VERSION
    }

    /**
     * 消息相关常量，使用资源ID
     */
    object MessageRes {
        // 训练动作库相关消息
        val LIBRARY_UPDATED = Constants.MessageResId.LIBRARY_UPDATED
        val LIBRARY_UPDATE_FAILED = Constants.MessageResId.LIBRARY_UPDATE_FAILED
        val LIBRARY_VERSION_RESET = Constants.MessageResId.LIBRARY_VERSION_RESET
        val LIBRARY_VERSION_RESET_FAILED = Constants.MessageResId.LIBRARY_VERSION_RESET_FAILED

        // 通用错误消息
        val UNKNOWN_ERROR = Constants.MessageResId.UNKNOWN_ERROR
    }

    /**
     * 动画时长常量 - 已弃用
     *
     * @deprecated 请使用 designSystem 模块的 Motion 系统
     *
     * 迁移指南：
     * - getAnimationShortDuration() → MotionDurations.XS (120ms)
     * - getAnimationMediumDuration() → MotionDurations.S (240ms)
     * - getAnimationLongDuration() → MotionDurations.M (400ms)
     *
     * 导入方式：
     * import com.example.gymbro.designSystem.theme.motion.MotionDurations
     */
    @Deprecated(
        message = "使用 designSystem.MotionDurations 替代硬编码动画时长",
        replaceWith = ReplaceWith(
            "MotionDurations.XS",
            "com.example.gymbro.designSystem.theme.motion.MotionDurations",
        ),
    )
    fun getAnimationShortDuration(): Int {
        return 120 // 迁移到 MotionDurations.XS
    }

    @Deprecated(
        message = "使用 designSystem.MotionDurations 替代硬编码动画时长",
        replaceWith = ReplaceWith(
            "MotionDurations.S",
            "com.example.gymbro.designSystem.theme.motion.MotionDurations",
        ),
    )
    fun getAnimationMediumDuration(): Int {
        return 240 // 迁移到 MotionDurations.S
    }

    @Deprecated(
        message = "使用 designSystem.MotionDurations 替代硬编码动画时长",
        replaceWith = ReplaceWith(
            "MotionDurations.M",
            "com.example.gymbro.designSystem.theme.motion.MotionDurations",
        ),
    )
    fun getAnimationLongDuration(): Int {
        return 400 // 迁移到 MotionDurations.M
    }

    /**
     * 获取Toast持续时间常量
     */
    fun getToastDurationShort(): Int {
        return 2000 // 短Toast持续时间2000ms
    }

    fun getToastDurationLong(): Int {
        return 3500 // 长Toast持续时间3500ms
    }
}
