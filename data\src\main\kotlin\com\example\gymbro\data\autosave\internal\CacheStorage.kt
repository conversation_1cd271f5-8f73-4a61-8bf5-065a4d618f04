package com.example.gymbro.data.autosave.internal

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import com.example.gymbro.core.autosave.storage.AutoSaveStorage
import com.example.gymbro.core.logging.Logger
import kotlinx.coroutines.flow.first
import kotlinx.serialization.json.Json
import javax.inject.Inject

/**
 * 缓存存储实现
 *
 * 🎯 功能特性：
 * - 使用DataStore进行缓存存储
 * - 支持泛型数据类型
 * - 集成Json序列化
 * - 提供完整的CRUD操作
 *
 * @param T 要存储的数据类型
 * @param dataStore DataStore实例
 * @param json Json序列化器
 * @param serializer 序列化函数
 * @param deserializer 反序列化函数
 * @param logger 日志记录器
 */
class CacheStorage<T : Any> @Inject constructor(
    private val dataStore: DataStore<Preferences>,
    private val json: Json,
    private val serializer: (T) -> String,
    private val deserializer: (String) -> T?,
    private val logger: Logger,
) : AutoSaveStorage<T> {

    companion object {
        private const val CACHE_KEY_PREFIX = "cache_"
        private const val CACHE_SIZE_PREFIX = "cache_size_"
        private const val CACHE_TIME_PREFIX = "cache_time_"

        /**
         * 创建CacheStorage实例
         */
        fun <T : Any> create(
            dataStore: DataStore<Preferences>,
            json: Json,
            serializer: (T) -> String,
            deserializer: (String) -> T?,
            logger: Logger,
        ): CacheStorage<T> {
            return CacheStorage(dataStore, json, serializer, deserializer, logger)
        }

        /**
         * 创建带有默认Json序列化的CacheStorage实例
         */
        fun <T : Any> createWithJson(
            dataStore: DataStore<Preferences>,
            json: Json,
            logger: Logger,
            serializer: (T) -> String,
            deserializer: (String) -> T?,
        ): CacheStorage<T> {
            return CacheStorage(
                dataStore = dataStore,
                json = json,
                serializer = serializer,
                deserializer = deserializer,
                logger = logger,
            )
        }
    }

    override suspend fun save(id: String, data: T) {
        try {
            val jsonString = serializer(data)
            val currentTime = System.currentTimeMillis()

            dataStore.edit { preferences ->
                val dataKey = stringPreferencesKey("$CACHE_KEY_PREFIX$id")
                val sizeKey = stringPreferencesKey("$CACHE_SIZE_PREFIX$id")
                val timeKey = stringPreferencesKey("$CACHE_TIME_PREFIX$id")

                preferences[dataKey] = jsonString
                preferences[sizeKey] = jsonString.length.toString()
                preferences[timeKey] = currentTime.toString()
            }

            logger.d("CacheStorage", "缓存数据已保存: $id, 大小: ${jsonString.length}")
        } catch (e: Exception) {
            logger.e(e, "保存缓存数据失败: $id")
            throw e
        }
    }

    override suspend fun restore(id: String): T? {
        return try {
            val preferences = dataStore.data.first()
            val dataKey = stringPreferencesKey("$CACHE_KEY_PREFIX$id")
            val jsonString = preferences[dataKey]

            if (jsonString.isNullOrBlank()) {
                logger.d("CacheStorage", "未找到缓存数据: $id")
                return null
            }

            val data = deserializer(jsonString)
            logger.d("CacheStorage", "缓存数据已恢复: $id")
            data
        } catch (e: Exception) {
            logger.e(e, "恢复缓存数据失败: $id")
            null
        }
    }

    override suspend fun clear(id: String) {
        try {
            dataStore.edit { preferences ->
                val dataKey = stringPreferencesKey("$CACHE_KEY_PREFIX$id")
                val sizeKey = stringPreferencesKey("$CACHE_SIZE_PREFIX$id")
                val timeKey = stringPreferencesKey("$CACHE_TIME_PREFIX$id")

                preferences.remove(dataKey)
                preferences.remove(sizeKey)
                preferences.remove(timeKey)
            }

            logger.d("CacheStorage", "缓存数据已清除: $id")
        } catch (e: Exception) {
            logger.e(e, "清除缓存数据失败: $id")
            throw e
        }
    }

    override suspend fun exists(id: String): Boolean {
        return try {
            val preferences = dataStore.data.first()
            val dataKey = stringPreferencesKey("$CACHE_KEY_PREFIX$id")
            val exists = preferences.contains(dataKey)

            logger.d("CacheStorage", "检查缓存存在性: $id = $exists")
            exists
        } catch (e: Exception) {
            logger.e(e, "检查缓存存在性失败: $id")
            false
        }
    }

    override suspend fun getSize(id: String): Long {
        return try {
            val preferences = dataStore.data.first()
            val sizeKey = stringPreferencesKey("$CACHE_SIZE_PREFIX$id")
            val sizeString = preferences[sizeKey]
            val size = sizeString?.toLongOrNull() ?: 0L

            logger.d("CacheStorage", "获取缓存大小: $id = $size")
            size
        } catch (e: Exception) {
            logger.e(e, "获取缓存大小失败: $id")
            0L
        }
    }

    /**
     * 获取缓存时间
     */
    suspend fun getCacheTime(id: String): Long {
        return try {
            val preferences = dataStore.data.first()
            val timeKey = stringPreferencesKey("$CACHE_TIME_PREFIX$id")
            val timeString = preferences[timeKey]
            val time = timeString?.toLongOrNull() ?: 0L

            logger.d("CacheStorage", "获取缓存时间: $id = $time")
            time
        } catch (e: Exception) {
            logger.e(e, "获取缓存时间失败: $id")
            0L
        }
    }

    /**
     * 检查缓存是否过期
     */
    suspend fun isExpired(id: String, maxAgeMs: Long): Boolean {
        return try {
            val cacheTime = getCacheTime(id)
            if (cacheTime == 0L) return true

            val currentTime = System.currentTimeMillis()
            val isExpired = (currentTime - cacheTime) > maxAgeMs

            logger.d("CacheStorage", "检查缓存过期: $id = $isExpired")
            isExpired
        } catch (e: Exception) {
            logger.e(e, "检查缓存过期失败: $id")
            true
        }
    }
}
