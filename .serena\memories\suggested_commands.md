# GymBro Development Commands

## Build and Test
- `./gradlew build` - Full project build
- `./gradlew test` - Run unit tests
- `./gradlew connectedAndroidTest` - Run instrumented tests
- `./gradlew lint` - Run lint checks
- `./gradlew detekt` - Run static analysis

## Code Quality
- `./gradlew ktlintFormat` - Format Kotlin code
- `./gradlew ktlintCheck` - Check Kotlin code style

## Template Module Specific
- Focus on `features/workout/` module for template editing
- Key files: TemplateEditViewModel, TemplateDataMapper, TemplateTransactionManager
- Test template editing functionality after changes

## Windows Commands
- Use `gradlew.bat` instead of `./gradlew` on Windows
- Git commands: `git status`, `git add`, `git commit`, `git push`