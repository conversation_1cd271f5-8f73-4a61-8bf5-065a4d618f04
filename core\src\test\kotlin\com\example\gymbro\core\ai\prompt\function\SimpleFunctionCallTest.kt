package com.example.gymbro.core.ai.prompt.function

import org.junit.Assert.*
import org.junit.Test

/**
 * 简单的Function Call测试
 * 验证基本功能是否正常
 */
class SimpleFunctionCallTest {

    @Test
    fun `test basic function call creation`() {
        // 测试基本的Function Call创建
        val functionCall = FunctionCall(
            name = "gymbro.exercise.search",
            arguments = "{\"query\": \"test\"}",
        )

        assertEquals("Function name should match", "gymbro.exercise.search", functionCall.name)
        assertEquals("Arguments should match", "{\"query\": \"test\"}", functionCall.arguments)
    }

    @Test
    fun `test validation result creation`() {
        // 测试ValidationResult创建
        val validResult = ValidationResult(true, "Success")
        assertTrue("Should be valid", validResult.isValid)
        assertEquals("Message should match", "Success", validResult.message)

        val invalidResult = ValidationResult(false, "Error")
        assertFalse("Should be invalid", invalidResult.isValid)
        assertEquals("Message should match", "Error", invalidResult.message)
    }

    @Test
    fun `test function call validator exists`() {
        // 测试FunctionCallValidator是否可以实例化
        val validator = FunctionCallValidator()
        assertNotNull("Validator should not be null", validator)
    }
}