package com.example.gymbro.designSystem.theme.motion

import androidx.compose.animation.core.*
import androidx.compose.runtime.*

/**
 * GymBro动画Token系统 - 简化兼容性包装器
 *
 * @deprecated 此文件已被拆分为更细粒度的模块，请使用新的Motion系统：
 * - MotionDurations: 动画时长
 * - MotionEasings: 缓动函数
 * - MotionSprings: 弹簧配置
 * - MotionSpecs: 动画规格
 *
 * 此文件仅为向后兼容而保留，新代码请使用新的Motion模块。
 */
@Deprecated(
    message = "AnimTokens has been split into Motion modules. Use MotionDurations, MotionEasings, MotionSprings, and MotionSpecs instead.",
    level = DeprecationLevel.WARNING,
)
object AnimTokensCompat {

    // === 最常用的兼容性映射 ===

    @Deprecated("Use MotionDurations.XS", ReplaceWith("MotionDurations.XS"))
    const val DURATION_XS = MotionDurations.XS

    @Deprecated("Use MotionDurations.S", ReplaceWith("MotionDurations.S"))
    const val DURATION_S = MotionDurations.S

    @Deprecated("Use MotionDurations.M", ReplaceWith("MotionDurations.M"))
    const val DURATION_M = MotionDurations.M

    @Deprecated("Use MotionDurations.L", ReplaceWith("MotionDurations.L"))
    const val DURATION_L = MotionDurations.L

    @Deprecated("Use MotionEasings.STANDARD", ReplaceWith("MotionEasings.STANDARD"))
    val STANDARD_EASING = MotionEasings.STANDARD

    @Deprecated("Use MotionEasings.LINEAR", ReplaceWith("MotionEasings.LINEAR"))
    val LINEAR_EASING = MotionEasings.LINEAR

    @Deprecated("Use MotionSprings.LIGHT", ReplaceWith("MotionSprings.LIGHT"))
    val SPRING_LIGHT = MotionSprings.LIGHT

    @Deprecated("Use MotionSprings.MEDIUM", ReplaceWith("MotionSprings.MEDIUM"))
    val SPRING_MEDIUM = MotionSprings.MEDIUM

    // === 最常用的函数映射 ===

    @Deprecated("Use MotionSpecs.tweenXS", ReplaceWith("MotionSpecs.tweenXS(easing)"))
    @Composable
    fun tweenXS(easing: Easing = STANDARD_EASING): TweenSpec<Float> = MotionSpecs.tweenXS(easing)

    @Deprecated("Use MotionSpecs.tweenS", ReplaceWith("MotionSpecs.tweenS(easing)"))
    @Composable
    fun tweenS(easing: Easing = STANDARD_EASING): TweenSpec<Float> = MotionSpecs.tweenS(easing)

    @Deprecated("Use MotionSpecs.tweenM", ReplaceWith("MotionSpecs.tweenM(easing)"))
    @Composable
    fun tweenM(easing: Easing = STANDARD_EASING): TweenSpec<Float> = MotionSpecs.tweenM(easing)

    // === Profile模块兼容性 ===

    @Deprecated("Use MotionSpecs.Profile", level = DeprecationLevel.WARNING)
    object Profile {
        @Deprecated("Use MotionSpecs.Profile.headerEnter", ReplaceWith("MotionSpecs.Profile.headerEnter()"))
        @Composable
        fun headerEnter(): TweenSpec<Float> = MotionSpecs.Profile.headerEnter()

        @Deprecated("Use MotionSpecs.Profile.themeSwitch", ReplaceWith("MotionSpecs.Profile.themeSwitch()"))
        @Composable
        fun themeSwitch(): TweenSpec<Float> = MotionSpecs.Profile.themeSwitch()
    }

    // === Coach模块兼容性 ===

    @Deprecated("Use MotionSpecs.Coach", level = DeprecationLevel.WARNING)
    object Coach {
        @Deprecated("Use MotionSpecs.Coach.messageEnter", ReplaceWith("MotionSpecs.Coach.messageEnter()"))
        @Composable
        fun messageEnter(): SpringSpec<Float> = MotionSpecs.Coach.messageEnter()

        @Deprecated(
            "Use MotionSpecs.Coach.sendButtonPress",
            ReplaceWith("MotionSpecs.Coach.sendButtonPress()"),
        )
        @Composable
        fun sendButtonPress(): SpringSpec<Float> = MotionSpecs.Coach.sendButtonPress()
    }

    // === Workout模块兼容性 ===

    @Deprecated("Use MotionSpecs.Workout", level = DeprecationLevel.WARNING)
    object Workout {
        @Deprecated("Use MotionSpecs.Workout.cardEnter", ReplaceWith("MotionSpecs.Workout.cardEnter()"))
        @Composable
        fun cardEnter(): SpringSpec<Float> = MotionSpecs.Workout.cardEnter()

        @Deprecated(
            "Use MotionSpecs.Workout.progressUpdate",
            ReplaceWith("MotionSpecs.Workout.progressUpdate()"),
        )
        @Composable
        fun progressUpdate(): SpringSpec<Float> = MotionSpecs.Workout.progressUpdate()
    }
}

/**
 * 全局类型别名，用于最常见的兼容性需求
 */
@Deprecated("Use MotionDurations, MotionEasings, MotionSprings, and MotionSpecs instead")
typealias AnimTokens = AnimTokensCompat
