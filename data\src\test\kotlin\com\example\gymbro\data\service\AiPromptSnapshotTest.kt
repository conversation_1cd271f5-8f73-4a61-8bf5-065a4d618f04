package com.example.gymbro.data.service

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.coach.model.ai.WorkoutContext
import com.example.gymbro.domain.profile.model.user.FitnessGoal
import com.example.gymbro.domain.profile.model.user.FitnessPreference
import com.example.gymbro.domain.profile.model.user.WeekDay
import com.example.gymbro.domain.service.profile.UserPreferencePort
import com.example.gymbro.domain.workout.model.TemplateDraft
import com.example.gymbro.domain.workout.repository.WorkoutDraftRepository
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.slot
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonArray
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

/**
 * AI Prompt Snapshot 测试
 *
 * 验证AI Prompt格式的一致性和完整性，防止格式回归
 * 确保USER_PREFERENCE块正确注入到AI Prompt中
 *
 * Phase 5 Task 3: Prompt Snapshot单测
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class AiPromptSnapshotTest {
    private lateinit var aiInteractionService: AiInteractionServiceImpl
    private lateinit var workoutDraftRepository: WorkoutDraftRepository
    private lateinit var userPreferencePort: UserPreferencePort
    private lateinit var json: Json

    private val testDispatcher = StandardTestDispatcher()

    @BeforeEach
    fun setUp() {
        workoutDraftRepository = mockk()
        userPreferencePort = mockk()
        json = Json { ignoreUnknownKeys = true }

        aiInteractionService =
            AiInteractionServiceImpl(
                workoutDraftRepository = workoutDraftRepository,
                userPreferencePort = userPreferencePort,
                json = json,
                ioDispatcher = testDispatcher,
            )
    }

    @Test
    fun `AI Prompt should contain USER_PREFERENCE block with correct format`() =
        runTest(testDispatcher) {
            // Given - 用户有完整的健身偏好
            val userPreference =
                FitnessPreference(
                    primaryGoal = FitnessGoal.MUSCLE_GAIN,
                    workoutDays = setOf(WeekDay.MONDAY, WeekDay.WEDNESDAY, WeekDay.FRIDAY),
                )

            val expectedTemplate = mockk<TemplateDraft>()
            val promptSlot = slot<String>()

            coEvery { userPreferencePort.current() } returns userPreference
            coEvery {
                workoutDraftRepository.generateTemplateDraftWithAI(
                    prompt = capture(promptSlot),
                    userId = any(),
                    history = any(),
                )
            } returns flowOf(ModernResult.Success(expectedTemplate))

            // When
            aiInteractionService.generateWorkoutTemplateFromPrompt(
                userId = "test_user_123",
                prompt = "帮我制定胸肌训练计划",
                context = null,
            )

            // Then - 验证Prompt Snapshot格式
            val capturedPrompt = promptSlot.captured

            // 1. 验证包含USER_PREFERENCE块
            assertTrue(
                capturedPrompt.contains("USER_PREFERENCE:"),
                "Prompt应包含USER_PREFERENCE块标识",
            )

            // 2. 验证JSON格式正确
            val userPrefStart = capturedPrompt.indexOf("USER_PREFERENCE:")
            val userPrefEnd = capturedPrompt.indexOf("\n\n", userPrefStart)
            val userPrefBlock = capturedPrompt.substring(userPrefStart, userPrefEnd)

            assertTrue(userPrefBlock.contains("\"goal\""), "应包含goal字段")
            assertTrue(userPrefBlock.contains("\"workoutDays\""), "应包含workoutDays字段")
            assertTrue(userPrefBlock.contains("muscle_gain"), "应包含具体的健身目标")

            // 验证训练日（排序后比对，避免Set迭代顺序抖动）
            val expectedDays = listOf("friday", "monday", "wednesday").sorted()
            expectedDays.forEach { day ->
                assertTrue(userPrefBlock.contains(day), "应包含训练日: $day")
            }

            // 3. 验证goal_match_rate相关内容（用于监控）
            assertTrue(
                capturedPrompt.contains("muscle_gain"),
                "Prompt应包含可用于goal_match_rate计算的目标信息",
            )

            // 4. 验证原始用户prompt保持完整
            assertTrue(
                capturedPrompt.contains("帮我制定胸肌训练计划"),
                "原始用户prompt应保持完整",
            )

            // 5. 验证Prompt结构顺序：USER_PREFERENCE -> 用户prompt -> 上下文
            val userPromptIndex = capturedPrompt.indexOf("帮我制定胸肌训练计划")
            assertTrue(
                userPrefStart < userPromptIndex,
                "USER_PREFERENCE块应在用户prompt之前",
            )
        }

    @Test
    fun `AI Prompt should skip USER_PREFERENCE block when user has no preferences`() =
        runTest(
            testDispatcher,
        ) {
            // Given - 用户没有健身偏好
            val emptyPreference = FitnessPreference()
            val expectedTemplate = mockk<TemplateDraft>()
            val promptSlot = slot<String>()

            coEvery { userPreferencePort.current() } returns emptyPreference
            coEvery {
                workoutDraftRepository.generateTemplateDraftWithAI(
                    prompt = capture(promptSlot),
                    userId = any(),
                    history = any(),
                )
            } returns flowOf(ModernResult.Success(expectedTemplate))

            // When
            aiInteractionService.generateWorkoutTemplateFromPrompt(
                userId = "test_user_123",
                prompt = "帮我制定训练计划",
                context = null,
            )

            // Then - 验证Prompt Snapshot不包含USER_PREFERENCE
            val capturedPrompt = promptSlot.captured

            assertFalse(
                capturedPrompt.contains("USER_PREFERENCE:"),
                "无偏好时不应包含USER_PREFERENCE块",
            )
            assertTrue(
                capturedPrompt.contains("帮我制定训练计划"),
                "原始用户prompt应保持完整",
            )
        }

    @Test
    fun `AI Prompt should handle USER_PREFERENCE errors gracefully`() =
        runTest(testDispatcher) {
            // Given - 获取用户偏好时发生异常
            val expectedTemplate = mockk<TemplateDraft>()
            val promptSlot = slot<String>()

            coEvery { userPreferencePort.current() } throws RuntimeException("DataStore error")
            coEvery {
                workoutDraftRepository.generateTemplateDraftWithAI(
                    prompt = capture(promptSlot),
                    userId = any(),
                    history = any(),
                )
            } returns flowOf(ModernResult.Success(expectedTemplate))

            // When
            aiInteractionService.generateWorkoutTemplateFromPrompt(
                userId = "test_user_123",
                prompt = "帮我制定训练计划",
                context = null,
            )

            // Then - 验证错误处理后的Prompt Snapshot
            val capturedPrompt = promptSlot.captured

            assertFalse(
                capturedPrompt.contains("USER_PREFERENCE:"),
                "偏好获取失败时不应包含USER_PREFERENCE块",
            )
            assertTrue(
                capturedPrompt.contains("帮我制定训练计划"),
                "原始用户prompt应保持完整",
            )
        }

    @Test
    fun `AI Prompt should include context information when provided`() =
        runTest(testDispatcher) {
            // Given - 用户有偏好和训练上下文
            val userPreference =
                FitnessPreference(
                    primaryGoal = FitnessGoal.STRENGTH,
                    workoutDays = setOf(WeekDay.TUESDAY, WeekDay.THURSDAY),
                )

            val context =
                WorkoutContext(
                    fitnessLevel = "中级",
                    availableEquipment = listOf("杠铃", "哑铃"),
                    timeConstraints = 60,
                    injuryHistory = listOf("膝盖伤病"),
                )

            val expectedTemplate = mockk<TemplateDraft>()
            val promptSlot = slot<String>()

            coEvery { userPreferencePort.current() } returns userPreference
            coEvery {
                workoutDraftRepository.generateTemplateDraftWithAI(
                    prompt = capture(promptSlot),
                    userId = any(),
                    history = any(),
                )
            } returns flowOf(ModernResult.Success(expectedTemplate))

            // When
            aiInteractionService.generateWorkoutTemplateFromPrompt(
                userId = "test_user_123",
                prompt = "制定力量训练计划",
                context = context,
            )

            // Then - 验证完整的Prompt Snapshot格式
            val capturedPrompt = promptSlot.captured

            // 验证包含所有组件
            assertTrue(capturedPrompt.contains("USER_PREFERENCE:"), "应包含用户偏好")
            assertTrue(capturedPrompt.contains("strength"), "应包含力量训练目标")
            assertTrue(capturedPrompt.contains("tuesday"), "应包含训练日")
            assertTrue(capturedPrompt.contains("制定力量训练计划"), "应包含原始prompt")
            assertTrue(capturedPrompt.contains("用户背景信息"), "应包含上下文信息")
            assertTrue(capturedPrompt.contains("健身水平：中级"), "应包含健身水平")
            assertTrue(capturedPrompt.contains("可用器械：杠铃, 哑铃"), "应包含器械信息")
            assertTrue(capturedPrompt.contains("时间限制：每次训练约60分钟"), "应包含时间限制")
            assertTrue(capturedPrompt.contains("伤病史：膝盖伤病"), "应包含伤病史")

            // 验证结构顺序
            val userPrefIndex = capturedPrompt.indexOf("USER_PREFERENCE:")
            val userPromptIndex = capturedPrompt.indexOf("制定力量训练计划")
            val contextIndex = capturedPrompt.indexOf("用户背景信息")

            assertTrue(userPrefIndex < userPromptIndex, "USER_PREFERENCE应在用户prompt之前")
            assertTrue(userPromptIndex < contextIndex, "用户prompt应在上下文信息之前")
        }

    @Test
    fun `USER_PREFERENCE JSON format should be valid and consistent`() =
        runTest(testDispatcher) {
            // Given - 用户有完整偏好
            val userPreference =
                FitnessPreference(
                    primaryGoal = FitnessGoal.MUSCLE_GAIN,
                    workoutDays = setOf(WeekDay.MONDAY, WeekDay.WEDNESDAY, WeekDay.FRIDAY),
                )

            val expectedTemplate = mockk<TemplateDraft>()
            val promptSlot = slot<String>()

            coEvery { userPreferencePort.current() } returns userPreference
            coEvery {
                workoutDraftRepository.generateTemplateDraftWithAI(
                    prompt = capture(promptSlot),
                    userId = any(),
                    history = any(),
                )
            } returns flowOf(ModernResult.Success(expectedTemplate))

            // When
            aiInteractionService.generateWorkoutTemplateFromPrompt(
                userId = "test_user_123",
                prompt = "制定训练计划",
                context = null,
            )

            // Then - 严格验证JSON格式
            val capturedPrompt = promptSlot.captured
            val userPrefStart = capturedPrompt.indexOf("USER_PREFERENCE:")
            val jsonStart = capturedPrompt.indexOf("{", userPrefStart)
            val jsonEnd = capturedPrompt.indexOf("\n\n", jsonStart)
            val jsonString = capturedPrompt.substring(jsonStart, jsonEnd).trim()

            // 解析JSON并验证结构
            val jsonElement = json.parseToJsonElement(jsonString)
            val jsonObject = jsonElement.jsonObject

            // 验证goal字段
            assertTrue(jsonObject.containsKey("goal"), "JSON应包含goal字段")
            assertEquals(
                "muscle_gain",
                jsonObject["goal"]?.jsonPrimitive?.content,
                "goal字段应为snake_case格式",
            )

            // 验证workoutDays字段（排序后比对）
            assertTrue(jsonObject.containsKey("workoutDays"), "JSON应包含workoutDays字段")
            val workoutDaysArray = jsonObject["workoutDays"]?.jsonArray
            assertNotNull(workoutDaysArray, "workoutDays应为数组")

            val actualDays = workoutDaysArray!!.map { it.jsonPrimitive.content }.sorted()
            val expectedDays = listOf("friday", "monday", "wednesday")
            assertEquals(expectedDays, actualDays, "训练日应按字母顺序排列")
        }

    @Test
    fun `AI Prompt format should be consistent for goal_match_rate monitoring`() =
        runTest(testDispatcher) {
            // Given - 不同的健身目标，用于验证goal_match_rate监控
            val testCases =
                listOf(
                    FitnessGoal.MUSCLE_GAIN to "muscle_gain",
                    FitnessGoal.WEIGHT_LOSS to "weight_loss",
                    FitnessGoal.STRENGTH to "strength",
                    FitnessGoal.ENDURANCE to "endurance",
                )

            testCases.forEach { (goal, expectedString) ->
                val userPreference = FitnessPreference(primaryGoal = goal)
                val expectedTemplate = mockk<TemplateDraft>()
                val promptSlot = slot<String>()

                coEvery { userPreferencePort.current() } returns userPreference
                coEvery {
                    workoutDraftRepository.generateTemplateDraftWithAI(
                        prompt = capture(promptSlot),
                        userId = any(),
                        history = any(),
                    )
                } returns flowOf(ModernResult.Success(expectedTemplate))

                // When
                aiInteractionService.generateWorkoutTemplateFromPrompt(
                    userId = "test_user_123",
                    prompt = "制定训练计划",
                    context = null,
                )

                // Then - 验证goal_match_rate监控所需的格式一致性
                val capturedPrompt = promptSlot.captured
                assertTrue(
                    capturedPrompt.contains(expectedString),
                    "Prompt应包含标准化的目标字符串: $expectedString",
                )
                assertTrue(
                    capturedPrompt.contains("\"goal\""),
                    "应包含标准的goal字段用于监控",
                )
            }
        }
}
