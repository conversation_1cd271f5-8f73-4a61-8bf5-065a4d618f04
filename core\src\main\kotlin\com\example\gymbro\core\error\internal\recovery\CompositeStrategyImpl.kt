package com.example.gymbro.core.error.internal.recovery

import com.example.gymbro.core.error.recovery.CompositeStrategy
import com.example.gymbro.core.error.recovery.RecoveryStrategy
import timber.log.Timber

/**
 * 组合恢复策略实现类，按顺序尝试多个恢复策略
 *
 * @param strategies 要尝试的恢复策略列表
 * @param stopOnSuccess 是否在第一个成功的策略后停止（默认为true）
 * @param executor 自定义策略执行逻辑（可选）
 */
class CompositeStrategyImpl<T>(
    private val strategies: List<RecoveryStrategy<T>>,
    private val stopOnSuccess: Boolean = true,
    private val executor: (suspend (List<RecoveryStrategy<T>>) -> T?)? = null,
) : CompositeStrategy<T> {

    /**
     * 执行所有恢复策略
     * 按顺序尝试每个策略，直到有一个成功或全部失败
     * 如果提供了自定义执行器，则使用自定义执行逻辑
     *
     * @return 成功策略的结果，或null如果全部失败
     */
    override suspend fun execute(): T? {
        // 如果提供了自定义执行器，使用它
        if (executor != null) {
            return try {
                executor.invoke(strategies)
            } catch (e: Exception) {
                Timber.e(e, "组合策略: 自定义执行器异常")
                null
            }
        }

        // 默认执行逻辑：依次尝试每个策略
        for (strategy in strategies) {
            try {
                // 尝试执行当前策略
                val result = strategy.execute()

                // 如果策略成功，并且配置为成功后停止，则返回结果
                if (result != null) {
                    Timber.d("组合策略: 策略成功")
                    if (stopOnSuccess) {
                        return result
                    }
                    // 如果不停止，继续尝试其他策略
                    continue
                }
            } catch (e: Exception) {
                // 当前策略执行失败，记录日志但继续尝试下一个
                Timber.w(e, "组合策略: 策略执行异常")
            }
        }

        // 如果未找到成功结果或已处理所有策略，返回null表示失败
        Timber.d("组合策略: 所有策略都失败或已处理完毕")
        return null
    }
}
