package com.example.gymbro.designSystem.components

import android.graphics.RenderEffect
import android.graphics.RuntimeShader
import android.graphics.Shader
import android.os.Build
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.gestures.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.*
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.asComposeRenderEffect
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.*
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.*
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.R
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import kotlinx.coroutines.delay
import kotlin.math.*

/**
 * Toast严重性级别 - 升级为优雅视觉系统
 */
enum class ToastSeverity(
    val icon: ImageVector,
    val primaryColor: Color,
    val accentColor: Color,
    val glowColor: Color,
    val autoDismissDelay: Long,
) {
    SUCCESS(
        icon = Icons.Default.CheckCircle,
        primaryColor = Color(0xFF38A169),
        accentColor = Color(0xFF68D391),
        glowColor = Color(0xFF9AE6B4),
        autoDismissDelay = 3000L,
    ),
    INFO(
        icon = Icons.Default.Info,
        primaryColor = Color(0xFF4299E1),
        accentColor = Color(0xFF63B3ED),
        glowColor = Color(0xFF90CDF4),
        autoDismissDelay = 4000L,
    ),
    WARNING(
        icon = Icons.Default.Warning,
        primaryColor = Color(0xFFFFB020),
        accentColor = Color(0xFFFFD93D),
        glowColor = Color(0xFFFFE066),
        autoDismissDelay = 6000L,
    ),
    ERROR(
        icon = Icons.Default.Error,
        primaryColor = Color(0xFFE53E3E),
        accentColor = Color(0xFFFF6B6B),
        glowColor = Color(0xFFFF6B9D),
        autoDismissDelay = 8000L,
    ),
}

/**
 * Toast显示位置
 */
enum class ToastPosition {
    TOP_START, // 左上角
    TOP_CENTER, // 顶部居中
    TOP_END, // 右上角
    BOTTOM_START, // 左下角
    BOTTOM_CENTER, // 底部居中
    BOTTOM_END, // 右下角
}

/**
 * Toast状态数据类
 *
 * @param message 消息内容
 * @param severity 严重性级别
 * @param isRetryable 是否支持重试
 * @param actionLabel 动作按钮文本
 * @param retryAction 重试回调函数
 */
data class ToastState(
    val message: UiText,
    val severity: ToastSeverity = ToastSeverity.ERROR,
    val isRetryable: Boolean = false,
    val actionLabel: UiText? = null,
    val retryAction: (() -> Unit)? = null,
)

/**
 * LiquidGlass Toast 组件配置
 */
private object LiquidToastTokens {
    val MaxWidth = 360.dp
    val MinHeight = 80.dp
    val Padding = 20.dp
    val ContentPadding = 24.dp
    val CornerRadius = 28.dp
    val IconSize = 32.dp
    val IconSpacing = 16.dp
    val ActionSpacing = 16.dp
    val CloseButtonSize = 24.dp
    val BlurRadius = 20f
    val ChromaticAberration = 0.018f
    val RadiusFactor = 0.55f
}

/**
 * GymBro优雅Toast组件 - 基于LiquidGlass的震撼视觉效果
 *
 * 特性：
 * - LiquidGlass材质效果
 * - 动态粒子动画
 * - 智能手势操作
 * - 触觉反馈
 * - 自适应背景模糊
 * - 完全向后兼容原有API
 *
 * @param message 消息内容 (UiText)
 * @param onDismiss 关闭回调函数
 * @param modifier Modifier修饰符
 * @param severity 严重性级别，影响颜色和图标
 * @param position 显示位置，影响动画方向
 * @param isRetryable 是否显示重试按钮
 * @param actionLabel 动作按钮文本
 * @param onAction 动作按钮回调函数
 * @param autoDismissDelay 自动消失延迟时间（毫秒），使用severity默认值或自定义
 * @param showCloseButton 是否显示关闭按钮
 */
@Composable
fun GymBroToast(
    message: UiText,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
    severity: ToastSeverity = ToastSeverity.ERROR,
    position: ToastPosition = ToastPosition.TOP_START,
    isRetryable: Boolean = false,
    actionLabel: UiText? = null,
    onAction: (() -> Unit)? = null,
    autoDismissDelay: Long = severity.autoDismissDelay,
    showCloseButton: Boolean = true,
) {
    val haptic = LocalHapticFeedback.current
    val density = LocalDensity.current

    // 触发触觉反馈
    LaunchedEffect(message) {
        when (severity) {
            ToastSeverity.ERROR -> haptic.performHapticFeedback(HapticFeedbackType.LongPress)
            ToastSeverity.WARNING -> haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
            else -> haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
        }
    }

    // 自动消失效果
    LaunchedEffect(message) {
        if (autoDismissDelay > 0) {
            delay(autoDismissDelay)
            onDismiss()
        }
    }

    // 动画状态
    var isVisible by remember { mutableStateOf(false) }
    var offsetY by remember { mutableStateOf(0f) }

    LaunchedEffect(message) {
        isVisible = true
    }

    // 手势状态
    val dismissThreshold = with(density) { 80.dp.toPx() }

    // 根据位置确定动画
    val (enterTransition, exitTransition) = getAnimationsForPosition(position)

    AnimatedVisibility(
        visible = isVisible,
        enter = enterTransition,
        exit = exitTransition,
        modifier = modifier,
    ) {
        Box(
            modifier = Modifier.padding(LiquidToastTokens.Padding),
        ) {
            // 粒子效果层
            ToastParticleEffect(
                severity = severity,
                visible = isVisible,
                modifier = Modifier.matchParentSize(),
            )

            // 主要Toast内容 - LiquidGlass效果
            LiquidGlassToastContent(
                message = message,
                severity = severity,
                isRetryable = isRetryable,
                actionLabel = actionLabel,
                onAction = onAction,
                showCloseButton = showCloseButton,
                onDismiss = {
                    isVisible = false
                    onDismiss()
                },
                modifier =
                Modifier
                    .widthIn(max = LiquidToastTokens.MaxWidth)
                    .offset(y = with(density) { offsetY.toDp() })
                    .pointerInput(Unit) {
                        detectDragGestures(
                            onDragEnd = {
                                if (abs(offsetY) > dismissThreshold) {
                                    isVisible = false
                                    onDismiss()
                                } else {
                                    offsetY = 0f
                                }
                            },
                        ) { change, dragAmount ->
                            offsetY += dragAmount.y
                            // 添加阻尼效果
                            if (offsetY < 0) {
                                offsetY *= 0.5f
                            }
                        }
                    },
            )
        }
    }
}

/**
 * 基于ToastState的便捷Toast组件
 */
@Composable
fun GymBroToast(
    toastState: ToastState,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
    position: ToastPosition = ToastPosition.TOP_START,
    autoDismissDelay: Long = toastState.severity.autoDismissDelay,
    showCloseButton: Boolean = true,
) {
    GymBroToast(
        message = toastState.message,
        onDismiss = onDismiss,
        modifier = modifier,
        severity = toastState.severity,
        position = position,
        isRetryable = toastState.isRetryable,
        actionLabel = toastState.actionLabel,
        onAction = toastState.retryAction,
        autoDismissDelay = autoDismissDelay,
        showCloseButton = showCloseButton,
    )
}

/**
 * LiquidGlass Toast 内容组件
 */
@Composable
private fun LiquidGlassToastContent(
    message: UiText,
    severity: ToastSeverity,
    isRetryable: Boolean,
    actionLabel: UiText?,
    onAction: (() -> Unit)?,
    showCloseButton: Boolean,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
) {
    // LiquidGlass 效果容器
    LiquidGlassContainer(
        severity = severity,
        modifier = modifier,
    ) {
        Row(
            modifier =
            Modifier
                .fillMaxWidth()
                .padding(LiquidToastTokens.ContentPadding),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // 状态图标 - 带动态光效
            ToastIconSection(
                severity = severity,
                modifier = Modifier.size(LiquidToastTokens.IconSize),
            )

            Spacer(modifier = Modifier.width(LiquidToastTokens.IconSpacing))

            // 消息内容
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = message.asString(),
                    style =
                    MaterialTheme.typography.bodyMedium.copy(
                        fontWeight = FontWeight.Medium,
                        color = Color.White.copy(alpha = 0.95f),
                    ),
                    maxLines = 3,
                    overflow = TextOverflow.Ellipsis,
                )

                // 操作按钮（如果需要）
                if (isRetryable && onAction != null) {
                    Spacer(modifier = Modifier.height(LiquidToastTokens.ActionSpacing))

                    LiquidGlassActionButton(
                        label = actionLabel?.asString() ?: "重试",
                        severity = severity,
                        onClick = {
                            onAction()
                            onDismiss()
                        },
                    )
                }
            }

            // 关闭按钮
            if (showCloseButton) {
                ToastCloseButton(
                    severity = severity,
                    onClick = onDismiss,
                    modifier = Modifier.size(LiquidToastTokens.CloseButtonSize),
                )
            }
        }
    }
}

/**
 * LiquidGlass 容器组件
 */
@Composable
private fun LiquidGlassContainer(
    severity: ToastSeverity,
    modifier: Modifier = Modifier,
    content: @Composable BoxScope.() -> Unit,
) {
    // 基于现有 LiquidGlass 实现的核心逻辑
    val pointerState = remember { ToastPointerState() }

    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
        // 降级方案
        Box(
            modifier =
            modifier
                .clip(RoundedCornerShape(LiquidToastTokens.CornerRadius))
                .background(
                    brush =
                    Brush.linearGradient(
                        colors =
                        listOf(
                            severity.primaryColor.copy(alpha = 0.15f),
                            severity.accentColor.copy(alpha = 0.08f),
                        ),
                    ),
                ).border(
                    1.dp,
                    severity.glowColor.copy(alpha = 0.3f),
                    RoundedCornerShape(LiquidToastTokens.CornerRadius),
                ).graphicsLayer {
                    renderEffect =
                        RenderEffect
                            .createBlurEffect(
                                LiquidToastTokens.BlurRadius,
                                LiquidToastTokens.BlurRadius,
                                Shader.TileMode.CLAMP,
                            ).asComposeRenderEffect()
                },
            content = content,
        )
        return
    }

    // 高级 LiquidGlass 效果
    val runtimeShader = remember { RuntimeShader(LIQUID_GLASS_SKSL) }

    val chromaAnim by animateFloatAsState(
        targetValue =
        if (pointerState.isPressed) {
            LiquidToastTokens.ChromaticAberration * 1.8f
        } else {
            LiquidToastTokens.ChromaticAberration
        },
        label = "chromaAnim",
    )

    Box(
        modifier =
        modifier
            .clip(RoundedCornerShape(LiquidToastTokens.CornerRadius))
            .background(
                brush =
                Brush.radialGradient(
                    colors =
                    listOf(
                        severity.glowColor.copy(alpha = 0.2f),
                        severity.primaryColor.copy(alpha = 0.1f),
                        Color.Transparent,
                    ),
                    radius = 300f,
                ),
            ).pointerInput(Unit) {
                detectDragGestures(
                    onDragStart = { offset -> pointerState.start(offset, size) },
                    onDragEnd = { pointerState.stop() },
                    onDragCancel = { pointerState.stop() },
                ) { change, _ ->
                    pointerState.update(change.position)
                }
            }.graphicsLayer {
                runtimeShader.setFloatUniform("uRes", size.width, size.height)
                runtimeShader.setFloatUniform("uCenter", pointerState.centerX, pointerState.centerY)
                runtimeShader.setFloatUniform("uVelDir", pointerState.dirX, pointerState.dirY)
                runtimeShader.setFloatUniform("uVelMag", pointerState.speed)
                runtimeShader.setFloatUniform(
                    "uRadius",
                    min(size.width, size.height) * LiquidToastTokens.RadiusFactor,
                )
                runtimeShader.setFloatUniform("uChrom", chromaAnim)

                renderEffect =
                    RenderEffect
                        .createRuntimeShaderEffect(
                            runtimeShader,
                            "uScene",
                        ).asComposeRenderEffect()
            },
        content = content,
    )
}

/**
 * Toast 图标区域 - 带动态光效
 */
@Composable
private fun ToastIconSection(
    severity: ToastSeverity,
    modifier: Modifier = Modifier,
) {
    val infiniteTransition = rememberInfiniteTransition(label = "icon_animation")

    val iconScale by infiniteTransition.animateFloat(
        initialValue = 1f,
        targetValue = 1.15f,
        animationSpec =
        infiniteRepeatable(
            animation = tween(2000, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse,
        ),
        label = "icon_scale",
    )

    val glowIntensity by infiniteTransition.animateFloat(
        initialValue = 0.4f,
        targetValue = 0.8f,
        animationSpec =
        infiniteRepeatable(
            animation = tween(1500, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse,
        ),
        label = "glow_intensity",
    )

    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center,
    ) {
        // 背景光环
        Box(
            modifier =
            Modifier
                .size(48.dp)
                .scale(iconScale)
                .background(
                    brush =
                    Brush.radialGradient(
                        colors =
                        listOf(
                            severity.glowColor.copy(alpha = glowIntensity * 0.4f),
                            severity.glowColor.copy(alpha = glowIntensity * 0.2f),
                            Color.Transparent,
                        ),
                        radius = 80f,
                    ),
                    shape = CircleShape,
                ),
        )

        // 主图标
        Icon(
            imageVector = severity.icon,
            contentDescription = null,
            modifier =
            Modifier
                .size(24.dp)
                .scale(iconScale),
            tint = severity.primaryColor,
        )
    }
}

/**
 * LiquidGlass 动作按钮
 */
@Composable
private fun LiquidGlassActionButton(
    label: String,
    severity: ToastSeverity,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Surface(
        onClick = onClick,
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        color = severity.primaryColor.copy(alpha = 0.2f),
        border = BorderStroke(1.dp, severity.accentColor.copy(alpha = 0.5f)),
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Icon(
                imageVector = Icons.Default.Refresh,
                contentDescription = null,
                modifier = Modifier.size(16.dp),
                tint = severity.primaryColor,
            )
            Spacer(modifier = Modifier.width(6.dp))
            Text(
                text = label,
                style =
                MaterialTheme.typography.labelMedium.copy(
                    fontWeight = FontWeight.Medium,
                    color = severity.primaryColor,
                ),
            )
        }
    }
}

/**
 * Toast 关闭按钮
 */
@Composable
private fun ToastCloseButton(
    severity: ToastSeverity,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    IconButton(
        onClick = onClick,
        modifier = modifier,
    ) {
        Icon(
            imageVector = Icons.Default.Close,
            contentDescription = "关闭",
            modifier = Modifier.size(18.dp),
            tint = Color.White.copy(alpha = 0.7f),
        )
    }
}

/**
 * Toast 粒子效果
 */
@Composable
private fun ToastParticleEffect(
    severity: ToastSeverity,
    visible: Boolean,
    modifier: Modifier = Modifier,
) {
    val infiniteTransition = rememberInfiniteTransition(label = "particle_animation")

    val particleOffset by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec =
        infiniteRepeatable(
            animation = tween(6000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart,
        ),
        label = "particle_offset",
    )

    AnimatedVisibility(
        visible = visible,
        enter = fadeIn(animationSpec = tween(1000)),
        exit = fadeOut(animationSpec = tween(500)),
    ) {
        Canvas(modifier = modifier) {
            val center = Offset(size.width / 2f, size.height / 2f)
            val particleCount =
                when (severity) {
                    ToastSeverity.ERROR -> 20
                    ToastSeverity.WARNING -> 15
                    ToastSeverity.SUCCESS -> 12
                    ToastSeverity.INFO -> 10
                }

            repeat(particleCount) { index ->
                val angle = (index * 360f / particleCount + particleOffset) * PI / 180
                val radius = 60f + (index % 3) * 40f
                val x = center.x + cos(angle).toFloat() * radius
                val y = center.y + sin(angle).toFloat() * radius

                val alpha = (0.2f + sin(particleOffset * PI / 180 + index * 0.2f).toFloat() * 0.3f)
                val particleSize = 1.5f + (index % 2) * 0.5f

                drawCircle(
                    color = severity.glowColor.copy(alpha = alpha),
                    radius = particleSize,
                    center = Offset(x, y),
                )
            }
        }
    }
}

/**
 * 根据位置获取对应的动画
 */
private fun getAnimationsForPosition(position: ToastPosition): Pair<EnterTransition, ExitTransition> =
    when (position) {
        ToastPosition.TOP_START, ToastPosition.BOTTOM_START -> {
            slideInHorizontally(
                initialOffsetX = { -it },
                animationSpec =
                spring(
                    dampingRatio = Spring.DampingRatioMediumBouncy,
                    stiffness = Spring.StiffnessMedium,
                ),
            ) + fadeIn(animationSpec = tween(400)) to
                slideOutHorizontally(
                    targetOffsetX = { -it },
                    animationSpec = tween(300),
                ) + fadeOut(animationSpec = tween(300))
        }
        ToastPosition.TOP_END, ToastPosition.BOTTOM_END -> {
            slideInHorizontally(
                initialOffsetX = { it },
                animationSpec =
                spring(
                    dampingRatio = Spring.DampingRatioMediumBouncy,
                    stiffness = Spring.StiffnessMedium,
                ),
            ) + fadeIn(animationSpec = tween(400)) to
                slideOutHorizontally(
                    targetOffsetX = { it },
                    animationSpec = tween(300),
                ) + fadeOut(animationSpec = tween(300))
        }
        ToastPosition.TOP_CENTER -> {
            slideInVertically(
                initialOffsetY = { -it },
                animationSpec =
                spring(
                    dampingRatio = Spring.DampingRatioMediumBouncy,
                    stiffness = Spring.StiffnessMedium,
                ),
            ) + fadeIn(animationSpec = tween(400)) to
                slideOutVertically(
                    targetOffsetY = { -it },
                    animationSpec = tween(300),
                ) + fadeOut(animationSpec = tween(300))
        }
        ToastPosition.BOTTOM_CENTER -> {
            slideInVertically(
                initialOffsetY = { it },
                animationSpec =
                spring(
                    dampingRatio = Spring.DampingRatioMediumBouncy,
                    stiffness = Spring.StiffnessMedium,
                ),
            ) + fadeIn(animationSpec = tween(400)) to
                slideOutVertically(
                    targetOffsetY = { it },
                    animationSpec = tween(300),
                ) + fadeOut(animationSpec = tween(300))
        }
    }

/**
 * Toast指针状态管理 - 避免与LiquidGlass的PointerState冲突
 */
private class ToastPointerState {
    private var viewWidth = 1f
    private var viewHeight = 1f
    private var velX = 0f
    private var velY = 0f
    private var lastTime = 0L
    private val decay = 6f

    var isPressed by mutableStateOf(false)
        private set

    // uniforms (0‑1 space)
    var centerX by mutableStateOf(0.5f)
        private set
    var centerY by mutableStateOf(0.5f)
        private set
    var dirX by mutableStateOf(0f)
        private set
    var dirY by mutableStateOf(0f)
        private set
    var speed by mutableStateOf(0f)
        private set

    fun start(
        offset: Offset,
        size: IntSize,
    ) {
        viewWidth = size.width.toFloat()
        viewHeight = size.height.toFloat()
        centerX = offset.x / viewWidth
        centerY = offset.y / viewHeight
        isPressed = true
        lastTime = System.nanoTime()
    }

    fun stop() {
        isPressed = false
    }

    fun update(cur: Offset) {
        val now = System.nanoTime()
        val dt = (now - lastTime).coerceAtLeast(1_000_000L) / 1e9f
        val dx = cur.x / viewWidth - centerX
        val dy = cur.y / viewHeight - centerY
        centerX = cur.x / viewWidth
        centerY = cur.y / viewHeight
        velX = velX * (1f - decay * dt) + dx / dt
        velY = velY * (1f - decay * dt) + dy / dt
        val mag = sqrt(velX * velX + velY * velY)
        speed = (mag / 30f).coerceIn(0f, 1f)
        if (mag > 1e-3f) {
            dirX = velX / mag
            dirY = velY / mag
        }
        lastTime = now
    }
}

/**
 * LiquidGlass SkSL Shader - 复用现有实现
 */
private const val LIQUID_GLASS_SKSL = """
uniform float2 uRes;
uniform float2 uCenter;
uniform float2 uVelDir;
uniform float uVelMag;
uniform float uRadius;
uniform float uChrom;
uniform shader uScene;

float4 main(float2 coord) {
    float2 uv = coord / uRes;
    float2 center = uCenter;

    // Distance from touch point
    float dist = distance(uv, center);
    float radius = uRadius / min(uRes.x, uRes.y);

    // Velocity-based distortion
    float2 velocity = uVelDir * uVelMag;
    float2 distortion = velocity * exp(-dist * 8.0) * 0.1;

    // Glass refraction effect
    float2 refractUV = uv + distortion;

    // Chromatic aberration
    float chromaOffset = uChrom * (1.0 + uVelMag * 2.0);
    float4 colorR = uScene.eval(refractUV + float2(chromaOffset, 0.0));
    float4 colorG = uScene.eval(refractUV);
    float4 colorB = uScene.eval(refractUV - float2(chromaOffset, 0.0));

    float4 color = float4(colorR.r, colorG.g, colorB.b, 1.0);

    // Rim light effect
    float rimFactor = 1.0 - smoothstep(radius * 0.8, radius, dist);
    float rim = rimFactor * (0.3 + uVelMag * 0.5);

    // Glass tint and highlights
    color.rgb += rim * float3(0.8, 0.9, 1.0);
    color.rgb *= 0.95 + 0.1 * sin(dist * 20.0 + uVelMag * 10.0);

    // Soft shadow at edges
    float shadow = smoothstep(0.0, 0.1, dist);
    color.rgb *= 0.7 + 0.3 * shadow;

    return color;
}
"""

/**
 * ModernDataError转换为ToastState的扩展函数 - 保持向后兼容
 */
fun ModernDataError.toToastState(): ToastState =
    ToastState(
        message = this.uiMessage ?: UiText.DynamicString("发生未知错误"),
        severity =
        when {
            this.operationName.contains("network", ignoreCase = true) -> ToastSeverity.ERROR
            this.operationName.contains("timeout", ignoreCase = true) -> ToastSeverity.ERROR
            this.operationName.contains("validation", ignoreCase = true) -> ToastSeverity.WARNING
            else -> ToastSeverity.ERROR
        },
        isRetryable =
        when {
            this.operationName.contains("network", ignoreCase = true) -> true
            this.operationName.contains("timeout", ignoreCase = true) -> true
            this.operationName.contains("service", ignoreCase = true) -> true
            else -> false
        },
        actionLabel =
        when {
            this.operationName.contains("network", ignoreCase = true) ->
                UiText.StringResource(
                    R.string.retry,
                    emptyList<String>(),
                )
            this.operationName.contains("timeout", ignoreCase = true) ->
                UiText.StringResource(
                    R.string.retry,
                    emptyList<String>(),
                )
            else -> null
        },
        retryAction = null, // 需要外部提供重试逻辑
    )

// ===== 便捷构造函数 - 保持API兼容性 =====

/**
 * 网络错误Toast
 */
fun networkErrorToast(
    onRetry: () -> Unit,
    onDismiss: () -> Unit,
): ToastState =
    ToastState(
        message = UiText.DynamicString("网络连接失败，请检查网络设置"),
        severity = ToastSeverity.ERROR,
        isRetryable = true,
        actionLabel = UiText.DynamicString("重新连接"),
        retryAction = onRetry,
    )

/**
 * 成功Toast
 */
fun successToast(message: String): ToastState =
    ToastState(
        message = UiText.DynamicString(message),
        severity = ToastSeverity.SUCCESS,
        isRetryable = false,
    )

/**
 * 警告Toast
 */
fun warningToast(message: String): ToastState =
    ToastState(
        message = UiText.DynamicString(message),
        severity = ToastSeverity.WARNING,
        isRetryable = false,
    )

// ===== Preview组件 =====

@GymBroPreview
@Composable
private fun LiquidGlassToastSuccessPreview() {
    GymBroTheme {
        Box(modifier = Modifier.fillMaxSize().background(Color.Black)) {
            GymBroToast(
                message = UiText.DynamicString("训练计划创建成功！🎉"),
                severity = ToastSeverity.SUCCESS,
                onDismiss = {},
                modifier = Modifier.align(Alignment.TopStart),
            )
        }
    }
}

@GymBroPreview
@Composable
private fun LiquidGlassToastErrorPreview() {
    GymBroTheme {
        Box(modifier = Modifier.fillMaxSize().background(Color.Black)) {
            GymBroToast(
                message = UiText.DynamicString("网络连接失败，请检查网络设置后重试"),
                severity = ToastSeverity.ERROR,
                isRetryable = true,
                actionLabel = UiText.DynamicString("重新连接"),
                onAction = {},
                onDismiss = {},
                modifier = Modifier.align(Alignment.TopStart),
            )
        }
    }
}

@GymBroPreview
@Composable
private fun LiquidGlassToastWarningPreview() {
    GymBroTheme {
        Box(modifier = Modifier.fillMaxSize().background(Color.Black)) {
            GymBroToast(
                message = UiText.DynamicString("AI教练功能需要注册账户才能使用"),
                severity = ToastSeverity.WARNING,
                isRetryable = true,
                actionLabel = UiText.DynamicString("立即登录"),
                onAction = {},
                onDismiss = {},
                modifier = Modifier.align(Alignment.TopStart),
            )
        }
    }
}

@GymBroPreview
@Composable
private fun LiquidGlassToastInfoPreview() {
    GymBroTheme {
        Box(modifier = Modifier.fillMaxSize().background(Color.Black)) {
            GymBroToast(
                message = UiText.DynamicString("新功能上线！快来体验AI训练建议功能"),
                severity = ToastSeverity.INFO,
                position = ToastPosition.TOP_CENTER,
                onDismiss = {},
                modifier = Modifier.align(Alignment.TopCenter),
            )
        }
    }
}

@GymBroPreview
@Composable
private fun LiquidGlassToastStatePreview() {
    GymBroTheme {
        Box(modifier = Modifier.fillMaxSize().background(Color.Black)) {
            val toastState = successToast("操作完成！数据已成功保存")

            GymBroToast(
                toastState = toastState,
                onDismiss = {},
                modifier = Modifier.align(Alignment.TopStart),
            )
        }
    }
}

@GymBroPreview
@Composable
private fun LiquidGlassToastComparisonPreview() {
    GymBroTheme {
        Box(modifier = Modifier.fillMaxSize().background(Color.Black)) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp),
            ) {
                GymBroToast(
                    message = UiText.DynamicString("成功 ✅"),
                    severity = ToastSeverity.SUCCESS,
                    onDismiss = {},
                )

                GymBroToast(
                    message = UiText.DynamicString("信息 ℹ️"),
                    severity = ToastSeverity.INFO,
                    onDismiss = {},
                )

                GymBroToast(
                    message = UiText.DynamicString("警告 ⚠️"),
                    severity = ToastSeverity.WARNING,
                    onDismiss = {},
                )

                GymBroToast(
                    message = UiText.DynamicString("错误 ❌"),
                    severity = ToastSeverity.ERROR,
                    isRetryable = true,
                    onAction = {},
                    onDismiss = {},
                )
            }
        }
    }
}
