package com.example.gymbro.data.local.model

import androidx.room.ColumnInfo

/**
 * 聊天搜索结果数据模型
 * 用于FTS4和VSS搜索结果的统一表示
 */
data class ChatSearchResult(
    /**
     * 消息ID
     */
    val id: Long,

    /**
     * 会话ID
     */
    @ColumnInfo(name = "session_id")
    val sessionId: String,

    /**
     * 消息角色
     */
    val role: String,

    /**
     * 消息内容
     */
    val content: String,

    /**
     * 消息时间戳
     */
    val timestamp: Long,

    /**
     * 搜索评分
     * FTS4搜索：bm25评分
     * VSS搜索：相似度评分
     * 混合搜索：综合评分
     */
    val score: Float,

    /**
     * 元数据
     */
    val metadata: Map<String, Any> = emptyMap(),
)

/**
 * FTS5搜索命中结果
 */
data class FtsHit(
    val id: Long,
    val bm25Score: Float,
)

/**
 * VSS向量搜索命中结果
 */
data class VssHit(
    val id: Long,
    val similarity: Float,
)
