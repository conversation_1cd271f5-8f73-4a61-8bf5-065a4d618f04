plugins {
    id("gymbro.android.library")
    id("gymbro.hilt.library")
    id("org.jetbrains.kotlin.plugin.serialization")
}

android {
    namespace = "com.example.gymbro.di" // Define namespace directly
    compileSdk =
        libs.versions.compileSdk
            .get()
            .toInt() // Use version from catalog

    defaultConfig {
        minSdk =
            libs.versions.minSdk
                .get()
                .toInt() // Use version from catalog
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro",
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = libs.versions.jvmTarget.get() // Use version from catalog
    }
}

dependencies {
    // Hilt dependencies (core and compiler)
    implementation(libs.hilt.android)
    ksp(libs.hilt.compiler) // Add KSP compiler for Hilt processing

    // Room dependencies - DI模块需要访问AppDatabase类
    implementation(libs.androidx.room.runtime)
    implementation(libs.androidx.room.ktx)
    ksp(libs.androidx.room.compiler)

    // JSON序列化依赖 - 通过gymbro.kotlin.serialization插件自动提供
    // 但是需要显式添加，因为DI模块直接使用Json类
    implementation(libs.kotlinx.serialization.json)

    // DateTime依赖 - TestDependenciesModule需要使用kotlinx.datetime
    implementation(libs.kotlinx.datetime)

    // Gson for JSON processing - 使用 libs.versions.toml 中的版本
    implementation(libs.gson)

    // 网络依赖 - DI模块需要这些依赖因为NetworkModule直接使用了这些类
    implementation(libs.retrofit)
    implementation(libs.retrofit.converter.gson)
    implementation(libs.retrofit.converter.scalars)
    implementation(libs.retrofit.kotlinx.serialization)
    implementation(libs.okhttp)
    implementation(libs.okhttp.logging.interceptor)

    // Timber日志依赖 - 需要显式添加，因为DI模块直接使用Timber
    implementation(libs.timber)

    // Firebase dependencies - DI模块需要这些依赖因为FirebaseModule直接使用了这些类
    implementation(platform(libs.firebase.bom))
    implementation(libs.firebase.auth.ktx)
    implementation(libs.firebase.firestore.ktx)
    implementation(libs.firebase.database.ktx)
    implementation(libs.firebase.remoteconfig.ktx)
    implementation(libs.firebase.analytics.ktx)
    implementation(libs.firebase.storage.ktx)

    // Google Play Services - 用于GoogleSignInModule
    implementation(libs.play.services.auth)

    // DataStore dependencies - DI模块需要这些依赖因为DataModule直接使用了DataStore
    implementation(libs.androidx.datastore.preferences)
    implementation(libs.androidx.datastore.core)

    // WorkManager - 用于同步相关DI配置
    implementation(libs.androidx.work.runtime.ktx)
    implementation(libs.androidx.hilt.work)
    ksp(libs.androidx.hilt.compiler)

    // Google Play Billing - 用于BillingModule
    implementation(libs.androidx.billing.ktx)

    // 只保留必要的依赖，避免循环依赖
    implementation(project(":core"))
    implementation(project(":core-ml"))
    implementation(project(":core-network")) // 🔧 修复循环依赖：di依赖core-network
    implementation(project(":core-user-data-center")) // 🆕 添加UserDataCenter依赖
    implementation(project(":domain"))
    // 这里需要data依赖，因为PaymentProvidersModule直接引用了data模块中的实现类
    implementation(project(":data"))
    // 添加navigation模块依赖，因为NavigationModule需要绑定AppNavigator接口
    implementation(project(":navigation"))
    // 添加designSystem模块依赖，因为GymBroAppModule需要FloatingCountdownModule
    implementation(project(":designSystem"))

    // Feature modules - DI模块需要绑定各feature模块的API接口
    implementation(project(":features:exercise-library"))

    // Testing Dependencies - 简化配置，专注基本测试
    testImplementation(libs.junit)
    testImplementation(libs.kotlin.test)
    testImplementation(libs.kotlinx.coroutines.test)

    // Hilt Testing - 仅保留基本测试
    testImplementation(libs.hilt.android.testing)
    kspTest(libs.hilt.compiler)

    // 基本Android测试框架
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)

    // Hilt Android Testing
    androidTestImplementation(libs.hilt.android.testing)
    kspAndroidTest(libs.hilt.compiler)

    // Core module
    implementation(project(":core"))
    testImplementation(project(":core"))

    // Room testing
    testImplementation(libs.androidx.room.testing)

    // Coroutines testing
    testImplementation(libs.kotlinx.coroutines.test)
    androidTestImplementation(libs.kotlinx.coroutines.test)
}
