package com.example.gymbro.core.arch.mvi

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

/**
 * ⚠️ DEPRECATED - 请使用 BaseMviViewModel 替代
 *
 * MVI架构 - ViewModel基类（已废弃）
 *
 * 🔄 迁移指南：
 * - 使用 BaseMviViewModel 替代此类
 * - 新版本提供更好的性能和ModernResult集成
 * - 支持Box+LazyColumn+Surface架构优化
 *
 * 提供MVI模式的标准实现，包括：
 * - Intent处理
 * - State管理
 * - Effect分发
 *
 * @param Intent 用户意图类型
 * @param State UI状态类型
 * @param Effect 副作用类型
 * @param initialState 初始状态
 *
 * @since v1.1 - MVI基座建设
 * @deprecated v6.0 - 请使用 BaseMviViewModel
 */
@Deprecated(
    message = "使用 BaseMviViewModel 替代，提供更好的性能和ModernResult集成",
    replaceWith = ReplaceWith("BaseMviViewModel"),
    level = DeprecationLevel.WARNING,
)
abstract class MviViewModel<Intent : AppIntent, State : UiState, Effect : UiEffect>(
    initialState: State,
) : ViewModel() {

    // State管理
    private val _state = MutableStateFlow(initialState)
    val state: StateFlow<State> = _state.asStateFlow()

    // Effect管理
    private val _effects = Channel<Effect>(Channel.UNLIMITED)
    val effects: Flow<Effect> = _effects.receiveAsFlow()

    // Intent处理通道
    private val _intents = Channel<Intent>(Channel.UNLIMITED)

    // 抽象属性，由子类提供具体的Reducer
    protected abstract val reducer: Reducer<Intent, State, Effect>

    init {
        // 启动Intent处理循环
        viewModelScope.launch {
            _intents.receiveAsFlow().collect { intent ->
                handleIntent(intent)
            }
        }
    }

    /**
     * 分发Intent
     * 这是外部与ViewModel交互的唯一入口
     */
    fun dispatch(intent: Intent) {
        viewModelScope.launch {
            _intents.send(intent)
        }
    }

    /**
     * 处理Intent的内部逻辑
     */
    private suspend fun handleIntent(intent: Intent) {
        val currentState = _state.value
        val result = reducer.reduce(intent, currentState)

        // 更新状态
        _state.value = result.newState

        // 分发副作用
        result.effects.forEach { effect ->
            _effects.send(effect)
        }

        // 处理特殊效果（子类可重写）
        handleSpecialEffects(result.effects)
    }

    /**
     * 处理特殊副作用
     * 子类可以重写此方法来处理需要特殊逻辑的Effect
     */
    protected open suspend fun handleSpecialEffects(effects: List<Effect>) {
        // 默认空实现
    }

    /**
     * 获取当前状态的快照
     */
    protected fun currentState(): State = _state.value

    /**
     * 发送单个副作用
     * 供子类在特殊情况下使用
     */
    protected suspend fun sendEffect(effect: Effect) {
        _effects.send(effect)
    }

    /**
     * 直接更新状态
     * 供子类在异步操作完成后使用（谨慎使用，优先考虑通过dispatch(Intent)）
     */
    protected fun updateState(newState: State) {
        _state.value = newState
    }

    override fun onCleared() {
        super.onCleared()
        _effects.close()
        _intents.close()
    }
}

/**
 * ⚠️ DEPRECATED - 请使用 SimpleBaseMviViewModel 替代
 *
 * 简化的MVI ViewModel实现（已废弃）
 * 适用于简单页面，不需要复杂的Reducer逻辑
 *
 * @deprecated v6.0 - 请使用 SimpleBaseMviViewModel
 */
@Deprecated(
    message = "使用 SimpleBaseMviViewModel 替代，提供更好的性能和错误处理",
    replaceWith = ReplaceWith("SimpleBaseMviViewModel"),
    level = DeprecationLevel.WARNING,
)
abstract class SimpleMviViewModel<Intent : AppIntent, State : UiState, Effect : UiEffect>(
    initialState: State,
) : MviViewModel<Intent, State, Effect>(initialState) {

    /**
     * 简化的Intent处理方法
     * 子类只需要实现这个方法，返回新的状态和可选的副作用
     */
    protected abstract suspend fun handleIntent(
        intent: Intent,
        currentState: State,
    ): ReduceResult<State, Effect>

    // 提供一个简单的Reducer实现
    override val reducer: Reducer<Intent, State, Effect> = object : Reducer<Intent, State, Effect> {
        override fun reduce(intent: Intent, currentState: State): ReduceResult<State, Effect> {
            // 注意：这里使用runBlocking是因为接口限制，在实际使用中应该避免
            // 建议使用完整的MviViewModel和异步Reducer
            return try {
                kotlinx.coroutines.runBlocking {
                    handleIntent(intent, currentState)
                }
            } catch (e: Exception) {
                ReduceResult.stateOnly(currentState)
            }
        }
    }
}
