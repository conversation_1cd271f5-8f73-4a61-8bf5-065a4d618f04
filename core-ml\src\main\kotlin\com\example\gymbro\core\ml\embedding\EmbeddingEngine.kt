package com.example.gymbro.core.ml.embedding

/**
 * 文本嵌入引擎接口
 *
 * 负责将文本转换为高维向量表示，用于语义搜索和相似度计算
 */
interface EmbeddingEngine {

    /**
     * 将单个文本转换为向量嵌入
     *
     * @param text 待嵌入的文本
     * @return 归一化的浮点向量数组
     */
    suspend fun embed(text: String): FloatArray

    /**
     * 批量将文本列表转换为向量嵌入
     *
     * @param texts 待嵌入的文本列表
     * @return 对应的向量列表
     */
    suspend fun embedBatch(texts: List<String>): List<FloatArray>

    /**
     * 获取嵌入向量的维度
     *
     * @return 向量维度大小
     */
    val embeddingDim: Int

    /**
     * 获取支持的最大序列长度
     *
     * @return 最大token数量
     */
    val maxSequenceLength: Int

    /**
     * 预热模型
     *
     * 使用简短文本进行一次推理，减少首次使用的延迟
     */
    suspend fun warmUp()

    /**
     * 获取模型信息
     */
    fun getModelInfo(): ModelInfo

    /**
     * 🔥 文本摘要生成 - 富文本功能新增
     *
     * @param text 输入文本
     * @param maxLength 最大摘要长度，默认15字符
     * @return 生成的摘要文本
     */
    suspend fun summarize(text: String, maxLength: Int = 15): String

    /**
     * 🔥 计算两个向量的余弦相似度 - 富文本功能新增
     *
     * @param vector1 第一个向量
     * @param vector2 第二个向量
     * @return 相似度值 [0.0, 1.0]
     */
    fun cosineSimilarity(vector1: FloatArray, vector2: FloatArray): Float

    /**
     * 释放引擎资源
     */
    fun close()
}

/**
 * 模型信息数据类
 */
data class ModelInfo(
    val modelName: String,
    val embeddingDim: Int,
    val maxSequenceLength: Int,
    val isInitialized: Boolean,
)
