package com.example.gymbro.data.ai.cache

import com.example.gymbro.shared.models.ai.AiExerciseReference
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * AI动作库缓存
 *
 * 为AI Function Call提供高性能的内存缓存服务
 * 缓存热点搜索结果，减少重复计算和数据库查询
 *
 * 核心功能：
 * 1. 内存缓存：基于LRU策略的内存缓存
 * 2. 线程安全：支持并发读写操作
 * 3. 自动过期：支持TTL和手动清除
 * 4. 统计监控：缓存命中率和性能统计
 *
 * 缓存策略：
 * - 热点数据：常用搜索结果优先缓存
 * - 容量控制：限制缓存大小，避免内存溢出
 * - 过期清理：定期清理过期数据
 *
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0 (P6阶段动作库Function Call链路)
 */
@Singleton
class AiLibraryCache @Inject constructor() {

    private val cache = ConcurrentHashMap<String, CacheEntry>()
    private val mutex = Mutex()
    private val stats = CacheStats()

    companion object {
        private const val DEFAULT_TTL_MS = 30 * 60 * 1000L // 30分钟
        private const val MAX_CACHE_SIZE = 1000 // 最大缓存条目数
        private const val CLEANUP_THRESHOLD = 1200 // 清理阈值
    }

    /**
     * 获取缓存数据
     *
     * @param key 缓存键
     * @return 缓存的动作引用列表，如果不存在或已过期返回null
     */
    suspend fun get(key: String): List<AiExerciseReference>? {
        val entry = cache[key]

        return if (entry != null && !entry.isExpired()) {
            stats.recordHit()
            Timber.d("缓存命中: $key")
            entry.data
        } else {
            stats.recordMiss()
            if (entry != null) {
                cache.remove(key)
                Timber.d("缓存过期移除: $key")
            }
            null
        }
    }

    /**
     * 存储缓存数据
     *
     * @param key 缓存键
     * @param data 要缓存的数据
     * @param ttlMs 生存时间（毫秒），默认30分钟
     */
    suspend fun put(
        key: String,
        data: List<AiExerciseReference>,
        ttlMs: Long = DEFAULT_TTL_MS,
    ) {
        mutex.withLock {
            // 检查缓存大小，必要时清理
            if (cache.size >= CLEANUP_THRESHOLD) {
                performCleanup()
            }

            val entry = CacheEntry(
                data = data,
                createdAt = System.currentTimeMillis(),
                ttlMs = ttlMs,
            )

            cache[key] = entry
            stats.recordPut()

            Timber.d("缓存存储: $key (${data.size}条数据, TTL=${ttlMs}ms)")
        }
    }

    /**
     * 批量获取缓存数据
     */
    suspend fun getBatch(keys: List<String>): Map<String, List<AiExerciseReference>> {
        val results = mutableMapOf<String, List<AiExerciseReference>>()

        keys.forEach { key ->
            get(key)?.let { data ->
                results[key] = data
            }
        }

        Timber.d("批量缓存获取: ${keys.size}个键, 命中${results.size}个")
        return results
    }

    /**
     * 批量存储缓存数据
     */
    suspend fun putBatch(
        data: Map<String, List<AiExerciseReference>>,
        ttlMs: Long = DEFAULT_TTL_MS,
    ) {
        mutex.withLock {
            data.forEach { (key, value) ->
                val entry = CacheEntry(
                    data = value,
                    createdAt = System.currentTimeMillis(),
                    ttlMs = ttlMs,
                )
                cache[key] = entry
                stats.recordPut()
            }

            Timber.d("批量缓存存储: ${data.size}个条目")
        }
    }

    /**
     * 检查缓存是否存在且未过期
     */
    suspend fun contains(key: String): Boolean {
        val entry = cache[key]
        return entry != null && !entry.isExpired()
    }

    /**
     * 移除指定缓存
     */
    suspend fun remove(key: String): Boolean {
        val removed = cache.remove(key) != null
        if (removed) {
            Timber.d("缓存移除: $key")
        }
        return removed
    }

    /**
     * 清空所有缓存
     */
    suspend fun clear() {
        mutex.withLock {
            val size = cache.size
            cache.clear()
            stats.reset()
            Timber.i("缓存清空: 移除${size}个条目")
        }
    }

    /**
     * 执行缓存清理
     * 移除过期条目，控制缓存大小
     */
    private fun performCleanup() {
        val currentTime = System.currentTimeMillis()
        val expiredKeys = mutableListOf<String>()

        // 找出过期的条目
        cache.forEach { (key, entry) ->
            if (entry.isExpired(currentTime)) {
                expiredKeys.add(key)
            }
        }

        // 移除过期条目
        expiredKeys.forEach { key ->
            cache.remove(key)
        }

        // 如果还是太大，移除最老的条目
        if (cache.size > MAX_CACHE_SIZE) {
            val sortedEntries = cache.entries.sortedBy { it.value.createdAt }
            val toRemove = sortedEntries.take(cache.size - MAX_CACHE_SIZE)

            toRemove.forEach { (key, _) ->
                cache.remove(key)
            }
        }

        Timber.d("缓存清理完成: 移除${expiredKeys.size}个过期条目, 当前大小${cache.size}")
    }

    /**
     * 获取缓存统计信息
     */
    fun getStats(): CacheStats {
        return stats.copy()
    }

    /**
     * 获取缓存大小
     */
    fun size(): Int {
        return cache.size
    }

    /**
     * 预热缓存
     * 加载常用的搜索结果
     */
    suspend fun warmup(commonQueries: List<Pair<String, List<AiExerciseReference>>>) {
        mutex.withLock {
            commonQueries.forEach { (key, data) ->
                val entry = CacheEntry(
                    data = data,
                    createdAt = System.currentTimeMillis(),
                    ttlMs = DEFAULT_TTL_MS * 2, // 预热数据保存更久
                )
                cache[key] = entry
            }

            Timber.i("缓存预热完成: 加载${commonQueries.size}个常用查询")
        }
    }
}

/**
 * 缓存条目
 */
private data class CacheEntry(
    val data: List<AiExerciseReference>,
    val createdAt: Long,
    val ttlMs: Long,
) {
    fun isExpired(currentTime: Long = System.currentTimeMillis()): Boolean {
        return currentTime - createdAt > ttlMs
    }
}

/**
 * 缓存统计信息
 */
data class CacheStats(
    var hits: Long = 0,
    var misses: Long = 0,
    var puts: Long = 0,
    var createdAt: Long = System.currentTimeMillis(),
) {
    fun recordHit() { hits++ }
    fun recordMiss() { misses++ }
    fun recordPut() { puts++ }

    fun hitRate(): Double {
        val total = hits + misses
        return if (total > 0) hits.toDouble() / total else 0.0
    }

    fun reset() {
        hits = 0
        misses = 0
        puts = 0
        createdAt = System.currentTimeMillis()
    }

    fun copy(): CacheStats {
        return CacheStats(hits, misses, puts, createdAt)
    }
}
