package com.example.gymbro.core.error.extensions

import com.example.gymbro.core.error.recovery.RecoveryStrategy
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import timber.log.Timber

/**
 * ModernResult 扩展函数
 *
 * 本文件包含 ModernResult 类的扩展函数，按功能分类组织：
 * 1. 基本转换函数 - map, flatMap 等
 * 2. 结果处理函数 - fold, handle 等
 * 3. 回调函数 - onSuccess, onError, onLoading 等
 * 4. 错误处理函数 - recover, safeCatch 等
 *
 * 注意：
 * - Flow相关函数已移至 FlowErrorHandlingExtensions.kt
 * - 过滤与验证相关函数已移至 ModernResultValidations.kt
 */

//region 1. 基本转换函数

//endregion

//region 2. 结果处理函数

/**
 * 通用ModernResult处理函数，确保处理所有状态
 *
 * @param onSuccess 处理成功状态的函数
 * @param onError 处理错误状态的函数
 * @param onLoading 处理加载状态的函数，默认为null
 * @return 处理后的结果
 */
inline fun <T, R> ModernResult<T>.handle(
    onSuccess: (T) -> R,
    onError: (ModernDataError) -> R,
    noinline onLoading: (() -> R)? = null,
): R =
    when (this) {
        is ModernResult.Success -> onSuccess(data)
        is ModernResult.Error -> onError(error)
        is ModernResult.Loading -> {
            onLoading?.invoke() ?: throw IllegalStateException("没有提供处理Loading状态的函数")
        }
    }

/**
 * 将多个ModernResult组合成一个，所有成功才成功
 *
 * @param results 要组合的结果列表
 * @return 如果所有结果都是成功的，返回包含所有成功数据的列表；否则返回第一个错误
 */
fun <T> combineResults(results: List<ModernResult<T>>): ModernResult<List<T>> {
    val successData = mutableListOf<T>()

    for (result in results) {
        when (result) {
            is ModernResult.Loading -> return ModernResult.Loading
            is ModernResult.Error -> return ModernResult.error(result.error)
            is ModernResult.Success -> successData.add(result.data)
        }
    }

    return ModernResult.success(successData)
}

/**
 * 在结果为[ModernResult.Error]时，尝试恢复为新的结果
 *
 * @param recovery 恢复函数，从错误生成新的结果
 * @return 如果原始结果是成功的，则返回原始结果；如果是错误的，则返回恢复函数的结果
 */
inline fun <T> ModernResult<T>.recover(recovery: (ModernDataError) -> ModernResult<T>): ModernResult<T> =
    when (this) {
        is ModernResult.Success -> this
        is ModernResult.Error -> recovery(error)
        is ModernResult.Loading -> this
    }

/**
 * 使用恢复策略恢复错误
 */
suspend fun <T> ModernResult.Error.recover(strategy: RecoveryStrategy<T>): ModernResult<T> =
    try {
        val recoveryResult = strategy.execute()
        if (recoveryResult != null) {
            ModernResult.success(recoveryResult)
        } else {
            this
        }
    } catch (e: Exception) {
        Timber.e(e, "恢复操作失败")
        this
    }

/**
 * 安全执行并返回ModernResult
 */
suspend inline fun <T> safeCatch(crossinline block: suspend () -> T): ModernResult<T> =
    try {
        ModernResult.success(block())
    } catch (e: Exception) {
        ModernResult.error(
            e.toModernDataError(
                operationName = "safeCatch",
                uiMessage = UiText.DynamicString("操作失败"),
            ),
        )
    }

//endregion

//region 3. 回调函数

/**
 * 记录结果
 *
 * @param tag 日志标签
 * @return 原始ModernResult
 */
fun <T> ModernResult<T>.log(tag: String = "ModernResult"): ModernResult<T> {
    when (this) {
        is ModernResult.Success -> Timber.tag(tag).d("成功: $data")
        is ModernResult.Error -> Timber.tag(tag).e("错误: ${error.message}")
        is ModernResult.Loading -> Timber.tag(tag).d("加载中...")
    }
    return this
}

//endregion

//region 4. 错误处理函数

//endregion
