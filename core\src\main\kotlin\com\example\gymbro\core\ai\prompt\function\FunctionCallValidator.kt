package com.example.gymbro.core.ai.prompt.function

import javax.inject.Inject
import javax.inject.Singleton

/**
 * Function Call 验证器
 *
 * 负责验证 Function Call 的有效性，包括：
 * 1. 函数名称验证
 * 2. 参数格式验证
 * 3. 业务规则验证
 *
 * @since 618重构
 */
@Singleton
class FunctionCallValidator @Inject constructor() {

    companion object {
        // 有效的函数名前缀
        private val VALID_PREFIXES = setOf(
            "gymbro.exercise.",
            "gymbro.template.",
            "gymbro.plan.",
            "gymbro.session.",
            "start_workout_session",
            "add_exercise_to_session",
            "log_exercise_set",
            "search_exercises",
            "get_workout_history",
            "complete_workout_session",
            "set_rest_timer",
            "get_exercise_recommendations",
        )
    }

    /**
     * 验证 Function Call 的有效性
     *
     * @param functionCall Function Call 对象
     * @return 验证结果
     */
    fun validateFunctionCall(functionCall: FunctionCall): ValidationResult {
        // 1. 验证函数名称
        if (functionCall.name.isBlank()) {
            return ValidationResult(false, "函数名称不能为空")
        }

        // 2. 验证函数名称格式
        if (!isValidFunctionName(functionCall.name)) {
            return ValidationResult(false, "函数名称格式不正确: ${functionCall.name}")
        }

        // 3. 验证参数格式
        if (!isValidArgumentsFormat(functionCall.arguments)) {
            return ValidationResult(false, "参数格式不正确")
        }

        return ValidationResult(true, "验证通过")
    }

    /**
     * 验证函数名称是否有效
     */
    private fun isValidFunctionName(name: String): Boolean {
        // 检查是否以有效前缀开头或者是已知的旧版函数名
        return VALID_PREFIXES.any { prefix ->
            name.startsWith(prefix) || name == prefix
        }
    }

    /**
     * 验证参数格式是否有效
     */
    private fun isValidArgumentsFormat(arguments: String): Boolean {
        if (arguments.isBlank()) return true

        return try {
            // 简单的 JSON 格式验证
            arguments.trim().startsWith("{") && arguments.trim().endsWith("}")
        } catch (e: Exception) {
            false
        }
    }
}
