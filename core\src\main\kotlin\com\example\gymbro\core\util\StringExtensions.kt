package com.example.gymbro.core.util

/**
 * Filters the string to keep only digit characters.
 * @param maxLength Optional maximum length for the resulting string.
 * @return A string containing only digits from the original string, optionally truncated to maxLength.
 */
fun String.toNumericOnly(maxLength: Int? = null): String {
    val numeric = this.filter { it.isDigit() }
    return if (maxLength != null) numeric.take(maxLength) else numeric
}

/**
 * 转换为非空字符串，空值时返回空字符串
 */
fun String?.orEmpty(): String = this ?: ""

/**
 * 判断字符串是否为空或空白
 */
fun String?.isNullOrEmpty(): Boolean = this == null || isEmpty()

/**
 * 安全地获取字符串，空值时返回指定默认值
 */
fun String?.orDefault(default: String): String = this?.takeIf { it.isNotEmpty() } ?: default
