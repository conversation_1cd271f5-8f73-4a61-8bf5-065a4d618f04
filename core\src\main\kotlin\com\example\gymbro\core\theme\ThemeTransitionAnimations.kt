package com.example.gymbro.core.theme

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color

/**
 * 主题过渡动画配置
 * 与Workout模块的动画风格保持一致，提供流畅的主题切换体验
 */
object ThemeTransitionAnimations {

    /**
     * 动画持续时间（毫秒）
     * 与WorkoutNavAnimations保持一致
     */
    const val ANIMATION_DURATION = 300

    /**
     * 主题切换动画规格
     */
    val themeTransitionSpec = tween<Color>(
        durationMillis = ANIMATION_DURATION,
        easing = EaseInOut,
    )

    /**
     * 缓入动画规格
     */
    val fadeInSpec = tween<Float>(
        durationMillis = ANIMATION_DURATION,
        easing = EaseIn,
    )

    /**
     * 缓出动画规格
     */
    val fadeOutSpec = tween<Float>(
        durationMillis = ANIMATION_DURATION,
        easing = EaseOut,
    )

    /**
     * 弹性动画规格（用于主题切换按钮）
     */
    val springSpec = spring<Float>(
        dampingRatio = Spring.DampingRatioMediumBouncy,
        stiffness = Spring.StiffnessLow,
    )
}

/**
 * 主题过渡容器组件
 * 为主题切换提供动画效果，与workout模块的CrossModuleAnimations风格一致
 */
@Composable
fun ThemeTransitionContainer(
    targetState: Boolean, // true for dark theme, false for light theme
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit,
) {
    AnimatedContent(
        targetState = targetState,
        modifier = modifier,
        transitionSpec = {
            // 使用与workout模块一致的过渡动画
            fadeIn(
                animationSpec = ThemeTransitionAnimations.fadeInSpec,
            ) + slideInVertically(
                animationSpec = tween(
                    durationMillis = ThemeTransitionAnimations.ANIMATION_DURATION,
                    easing = EaseOut,
                ),
                initialOffsetY = { it / 10 },
            ) togetherWith fadeOut(
                animationSpec = ThemeTransitionAnimations.fadeOutSpec,
            ) + slideOutVertically(
                animationSpec = tween(
                    durationMillis = ThemeTransitionAnimations.ANIMATION_DURATION,
                    easing = EaseIn,
                ),
                targetOffsetY = { -it / 10 },
            )
        },
        label = "ThemeTransition",
    ) { isDarkTheme ->
        content()
    }
}

/**
 * 主题切换加载动画
 * 与workout模块的LoadingAnimation保持一致的风格
 */
@Composable
fun ThemeLoadingAnimation(
    isVisible: Boolean,
    modifier: Modifier = Modifier,
    overlayColor: Color = MaterialTheme.colorScheme.surface.copy(alpha = 0.8f),
) {
    AnimatedVisibility(
        visible = isVisible,
        enter = fadeIn(
            animationSpec = ThemeTransitionAnimations.fadeInSpec,
        ) + scaleIn(
            animationSpec = ThemeTransitionAnimations.springSpec,
        ),
        exit = fadeOut(
            animationSpec = ThemeTransitionAnimations.fadeOutSpec,
        ) + scaleOut(
            animationSpec = tween(
                durationMillis = ThemeTransitionAnimations.ANIMATION_DURATION / 2,
            ),
        ),
        modifier = modifier,
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(overlayColor),
        )
    }
}

/**
 * 主题切换成功动画
 * 与workout模块的SuccessAnimation保持一致的风格
 */
@Composable
fun ThemeChangeSuccessAnimation(
    visible: Boolean,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit,
) {
    AnimatedVisibility(
        visible = visible,
        enter = slideInVertically(
            initialOffsetY = { it },
            animationSpec = spring(
                dampingRatio = Spring.DampingRatioMediumBouncy,
                stiffness = Spring.StiffnessLow,
            ),
        ) + fadeIn(
            animationSpec = ThemeTransitionAnimations.fadeInSpec,
        ),
        exit = slideOutVertically(
            targetOffsetY = { -it / 2 },
            animationSpec = tween(ThemeTransitionAnimations.ANIMATION_DURATION),
        ) + fadeOut(
            animationSpec = ThemeTransitionAnimations.fadeOutSpec,
        ),
        modifier = modifier,
    ) {
        content()
    }
}

/**
 * 主题切换按钮动画状态
 */
@Composable
fun rememberThemeButtonAnimationState(): MutableState<Boolean> {
    return remember { mutableStateOf(false) }
}

/**
 * 颜色过渡动画 Hook
 * 用于平滑的颜色切换效果
 */
@Composable
fun animateColorAsState(
    targetValue: Color,
    label: String = "ColorAnimation",
): State<Color> {
    return androidx.compose.animation.animateColorAsState(
        targetValue = targetValue,
        animationSpec = ThemeTransitionAnimations.themeTransitionSpec,
        label = label,
    )
}

/**
 * 主题切换状态管理
 */
@Stable
class ThemeTransitionState {
    var isTransitioning by mutableStateOf(false)
        private set

    var transitionProgress by mutableStateOf(0f)
        private set

    fun startTransition() {
        isTransitioning = true
        transitionProgress = 0f
    }

    fun updateProgress(progress: Float) {
        transitionProgress = progress.coerceIn(0f, 1f)
    }

    fun completeTransition() {
        isTransitioning = false
        transitionProgress = 1f
    }
}

/**
 * 记住主题切换状态
 */
@Composable
fun rememberThemeTransitionState(): ThemeTransitionState {
    return remember { ThemeTransitionState() }
}
