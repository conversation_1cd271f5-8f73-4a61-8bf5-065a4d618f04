package com.example.gymbro.data.repository

import com.example.gymbro.data.local.QuickActionProvider
import com.example.gymbro.domain.shared.common.model.QuickAction
import com.example.gymbro.domain.shared.common.model.QuickActionCategoryGroup
import com.example.gymbro.domain.shared.common.repository.QuickActionRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 快捷动作Repository实现
 *
 * 使用QuickActionProvider提供数据，未来可扩展为从Firebase Remote Config获取
 *
 * @since 凤凰行动 - 数据层重构
 */
@Singleton
class QuickActionRepositoryImpl
@Inject
constructor(
    private val quickActionProvider: QuickActionProvider,
) : QuickActionRepository {
    override fun getQuickActions(): Flow<List<QuickAction>> = flowOf(
        quickActionProvider.getAllQuickActions(),
    )

    override fun getQuickActionById(id: String): Flow<QuickAction?> = flowOf(
        quickActionProvider.getQuickActionById(id),
    )

    override suspend fun refreshQuickActions() {
        // TODO: 未来可以从Firebase Remote Config刷新数据
        // 目前使用本地预定义数据，无需刷新
    }

    override fun getQuickActionCategoryGroups(): Flow<List<QuickActionCategoryGroup>> = flowOf(
        quickActionProvider.getQuickActionCategoryGroups(),
    )

    override fun getPrimaryQuickActions(): Flow<List<QuickAction>> = flowOf(
        quickActionProvider.getPrimaryQuickActions(),
    )
}
