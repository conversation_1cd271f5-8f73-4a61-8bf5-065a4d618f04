package com.example.gymbro.designSystem.theme.motion

import androidx.compose.animation.core.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalDensity

/**
 * GymBro动画规格Token系统
 *
 * 提供统一的动画规格定义，结合时长、缓动和弹簧配置
 * 支持系统动画缩放和无障碍友好设计
 */
object MotionSpecs {

    // === 基础Tween动画规格 ===

    /**
     * 创建标准的Tween动画规格（考虑系统缩放）
     */
    @Composable
    fun tweenXS(easing: Easing = MotionEasings.STANDARD): TweenSpec<Float> =
        tween(effectiveDuration(MotionDurations.XS), easing = easing)

    @Composable
    fun tweenS(easing: Easing = MotionEasings.STANDARD): TweenSpec<Float> =
        tween(effectiveDuration(MotionDurations.S), easing = easing)

    @Composable
    fun tweenM(easing: Easing = MotionEasings.STANDARD): TweenSpec<Float> =
        tween(effectiveDuration(MotionDurations.M), easing = easing)

    @Composable
    fun tweenL(easing: Easing = MotionEasings.LINEAR): TweenSpec<Float> =
        tween(effectiveDuration(MotionDurations.L), easing = easing)

    @Composable
    fun tweenXL(easing: Easing = MotionEasings.LINEAR): TweenSpec<Float> =
        tween(effectiveDuration(MotionDurations.XL), easing = easing)

    // === 语义化动画规格 ===
    object Semantic {
        // 微交互动画（120ms）
        @Composable
        fun microInteraction(): TweenSpec<Float> = tweenXS()

        // 页面过渡动画（240ms）
        @Composable
        fun pageTransition(): TweenSpec<Float> = tweenS()

        // 内容切换动画（400ms）
        @Composable
        fun contentTransition(): TweenSpec<Float> = tweenM()

        // 装饰性动画（1000ms）
        @Composable
        fun decorative(): TweenSpec<Float> = tweenL()

        // 长时间循环动画（3000ms）
        @Composable
        fun longCycle(): TweenSpec<Float> = tweenXL()

        // 按钮反馈弹簧
        @Composable
        fun buttonFeedback(): SpringSpec<Float> = MotionSprings.LIGHT

        // 页面切换弹簧
        @Composable
        fun pageSpring(): SpringSpec<Float> = MotionSprings.MEDIUM

        // 拖拽释放弹簧
        @Composable
        fun dragRelease(): SpringSpec<Float> = MotionSprings.HEAVY

        // 特殊效果弹簧
        @Composable
        fun specialEffect(): SpringSpec<Float> = MotionSprings.BOUNCY
    }

    // === 组件专用动画规格 ===
    object Button {
        @Composable
        fun pressAnimation(): TweenSpec<Float> = tweenXS()

        @Composable
        fun hoverAnimation(): TweenSpec<Float> = tweenXS(MotionEasings.DECELERATE)

        @Composable
        fun loadingRotation(): TweenSpec<Float> = tween(
            effectiveDuration(MotionDurations.M),
            easing = MotionEasings.LINEAR,
        )

        @Composable
        fun toggleSpring(): SpringSpec<Float> = MotionSprings.Component.BUTTON_TOGGLE

        // FluidInteraction子对象 - 流畅交互动画
        object FluidInteraction {
            @Composable
            fun INSTANT_STATE_CHANGE(): TweenSpec<Float> = tweenXS()

            @Composable
            fun QUICK_FEEDBACK(): TweenSpec<Float> = tweenXS()

            @Composable
            fun SMOOTH_SLIDE(): TweenSpec<Float> = tweenS()
        }

        // Toggle子对象 - 切换动画
        object Toggle {
            const val PRESSED_SCALE = 0.97f

            @Composable
            fun PRESS_SCALE_ANIMATION_SPEC(): SpringSpec<Float> = MotionSprings.Component.BUTTON_TOGGLE
        }
    }

    object Card {
        @Composable
        fun enterAnimation(): TweenSpec<Float> = tweenS()

        @Composable
        fun hoverAnimation(): TweenSpec<Float> = tweenXS()

        @Composable
        fun pressAnimation(): SpringSpec<Float> = MotionSprings.LIGHT
    }

    object Input {
        @Composable
        fun focusAnimation(): TweenSpec<Float> = tweenXS()

        @Composable
        fun errorShake(): TweenSpec<Float> = tween(
            effectiveDuration(MotionDurations.XS * 2),
            easing = MotionEasings.EMPHASIZE,
        )

        @Composable
        fun labelFloat(): TweenSpec<Float> = tweenS()
    }

    object Page {
        @Composable
        fun enterTransition(): TweenSpec<Float> = tweenS()

        @Composable
        fun exitTransition(): TweenSpec<Float> = tweenS(MotionEasings.ACCELERATE)

        @Composable
        fun modalEnter(): SpringSpec<Float> = MotionSprings.MEDIUM

        @Composable
        fun modalExit(): TweenSpec<Float> = tweenS(MotionEasings.ACCELERATE)
    }

    // === 模块专用动画规格 ===
    object Profile {
        @Composable
        fun themeSwitch(): TweenSpec<Float> = tweenS()

        @Composable
        fun themePreview(): TweenSpec<Float> = tweenXS()

        @Composable
        fun settingItemPress(): SpringSpec<Float> = MotionSprings.LIGHT

        @Composable
        fun settingItemHover(): TweenSpec<Float> = tweenXS()

        @Composable
        fun avatarChange(): TweenSpec<Float> = tweenS()

        @Composable
        fun avatarHover(): SpringSpec<Float> = MotionSprings.LIGHT

        @Composable
        fun switchToggle(): SpringSpec<Float> = MotionSprings.Profile.SWITCH_TOGGLE

        @Composable
        fun headerEnter(): TweenSpec<Float> = tweenS(MotionEasings.DECELERATE)

        @Composable
        fun loadingIndicator(): TweenSpec<Float> = tween(
            effectiveDuration(MotionDurations.M),
            easing = MotionEasings.LINEAR,
        )
    }

    object Coach {
        @Composable
        fun messageEnter(): SpringSpec<Float> = MotionSprings.Coach.MESSAGE_ENTER

        @Composable
        fun messageExit(): TweenSpec<Float> = tweenXS()

        @Composable
        fun sendButtonPress(): SpringSpec<Float> = MotionSprings.Coach.SEND_BUTTON_PRESS

        @Composable
        fun sendButtonRotation(): TweenSpec<Float> = tween(
            effectiveDuration(MotionDurations.Coach.SEND_BUTTON_ROTATION),
            easing = MotionEasings.LINEAR,
        )

        @Composable
        fun aiStreamingPulse(): TweenSpec<Float> = tween(
            effectiveDuration(MotionDurations.Coach.AI_STREAMING_PULSE),
            easing = MotionEasings.LINEAR,
        )

        // === 扩展的Coach专用动画规格（配合CoachMotionValues使用）===

        @Composable
        fun tagPress(): SpringSpec<Float> = MotionSprings.Coach.TAG_PRESS

        @Composable
        fun tagHover(): TweenSpec<Float> = tween(
            effectiveDuration(MotionDurations.Coach.HOVER_FADE),
            easing = MotionEasings.STANDARD,
        )

        @Composable
        fun voiceButtonPress(): SpringSpec<Float> = MotionSprings.Coach.VOICE_BUTTON

        @Composable
        fun voiceButtonActive(): TweenSpec<Float> = tween(
            effectiveDuration(MotionDurations.Coach.VOICE_BUTTON_PRESS),
            easing = MotionEasings.DECELERATE,
        )

        @Composable
        fun modelChipAnimation(): SpringSpec<Float> = MotionSprings.Coach.MODEL_CHIP

        @Composable
        fun modelChipRotation(): TweenSpec<Float> = tween(
            effectiveDuration(MotionDurations.Coach.MODEL_CHIP_ANIMATION),
            easing = MotionEasings.STANDARD,
        )

        @Composable
        fun panelSlide(): SpringSpec<Float> = MotionSprings.Coach.PANEL_SLIDE

        @Composable
        fun panelFade(): TweenSpec<Float> = tween(
            effectiveDuration(MotionDurations.Coach.SUGGESTION_FADE),
            easing = MotionEasings.STANDARD,
        )

        @Composable
        fun suggestionPanelEnter(): SpringSpec<Float> = MotionSprings.Coach.SUGGESTION_PANEL

        @Composable
        fun alphaTransition(): TweenSpec<Float> = tween(
            effectiveDuration(MotionDurations.Coach.ALPHA_TRANSITION),
            easing = MotionEasings.STANDARD,
        )

        @Composable
        fun quickFeedback(): TweenSpec<Float> = tween(
            effectiveDuration(MotionDurations.Coach.QUICK_FEEDBACK),
            easing = MotionEasings.DECELERATE,
        )

        @Composable
        fun smoothTransition(): TweenSpec<Float> = tween(
            effectiveDuration(MotionDurations.Coach.SMOOTH_TRANSITION),
            easing = MotionEasings.STANDARD,
        )

        @Composable
        fun thinkingBoxPulse(): TweenSpec<Float> = tween(
            effectiveDuration(MotionDurations.Coach.THINKING_BOX_PULSE),
            easing = MotionEasings.LINEAR,
        )
    }

    object Workout {
        @Composable
        fun cardEnter(): SpringSpec<Float> = MotionSprings.Workout.CARD_ENTER

        @Composable
        fun cardPress(): TweenSpec<Float> = tween(
            effectiveDuration(MotionDurations.Workout.CARD_PRESS),
            easing = MotionEasings.STANDARD,
        )

        @Composable
        fun progressUpdate(): SpringSpec<Float> = MotionSprings.MEDIUM

        @Composable
        fun timerAnimation(): TweenSpec<Float> = tweenM()
    }

    // === 无限循环动画规格 ===
    @Composable
    fun forBreathing(): InfiniteRepeatableSpec<Float> = infiniteRepeatable(
        animation = tweenL(),
        repeatMode = RepeatMode.Reverse,
    )

    @Composable
    fun forRotation(): InfiniteRepeatableSpec<Float> = infiniteRepeatable(
        animation = tween(effectiveDuration(MotionDurations.M), easing = MotionEasings.LINEAR),
        repeatMode = RepeatMode.Restart,
    )

    @Composable
    fun forPulse(): InfiniteRepeatableSpec<Float> = infiniteRepeatable(
        animation = tweenM(),
        repeatMode = RepeatMode.Reverse,
    )

    // === 工具函数 ===

    /**
     * 考虑系统动画缩放的有效时长
     */
    @Composable
    fun effectiveDuration(baseDuration: Int): Int {
        val durationScale = androidx.compose.ui.platform.LocalDensity.current.density // 临时使用density作为替代
        return (baseDuration * 1f).toInt().coerceAtLeast(1) // 暂时不应用缩放
    }

    /**
     * 检查是否应该禁用动画
     */
    @Composable
    fun isMotionDisabled(): Boolean {
        val motionConfig = LocalGymBroMotionConfig.current
        return !motionConfig.enableAnimations
    }

    /**
     * 根据动画类型创建标准动画规格
     */
    @Composable
    fun forInteraction(): AnimationSpec<Float> = tweenXS()

    @Composable
    fun forTransition(): AnimationSpec<Float> = tweenS()

    @Composable
    fun forStandardAnimation(): AnimationSpec<Float> = tweenM()

    @Composable
    fun forSpringInteraction(): SpringSpec<Float> = MotionSprings.LIGHT

    @Composable
    fun forSpringTransition(): SpringSpec<Float> = MotionSprings.MEDIUM
}
