package com.example.gymbro.data.remote.subscription.dto

import com.google.gson.annotations.SerializedName

/**
 * 订阅计划类型DTO
 * 定义远程API的订阅计划类型
 */
enum class PlanTypeDto {
    /**
     * 免费计划
     */
    @SerializedName("free")
    FREE,

    /**
     * 基础计划
     */
    @SerializedName("basic")
    BASIC,

    /**
     * 高级计划
     */
    @SerializedName("premium")
    PREMIUM,

    /**
     * 终极计划
     */
    @SerializedName("ultimate")
    ULTIMATE,

    /**
     * 未知计划类型
     */
    @SerializedName("unknown")
    UNKNOWN,
}
