package com.example.gymbro.core.userdata.internal.di

import javax.inject.Qualifier

/**
 * Hilt qualifiers for UserLocalDataSource implementations
 *
 * These qualifiers allow us to have multiple implementations of UserLocalDataSource
 * without causing binding conflicts, while ensuring the correct implementation
 * is used in different contexts.
 */

/**
 * Qualifier for the simple in-memory implementation of UserLocalDataSource
 *
 * This implementation is used for:
 * - Testing scenarios where Room database is not needed
 * - Fallback scenarios during development
 * - Unit tests that need fast, isolated data source
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class SimpleUserDataSource

/**
 * Qualifier for the Room-based implementation of UserLocalDataSource
 *
 * This implementation is used for:
 * - Production application with real database persistence
 * - Integration tests that need real database behavior
 * - Default implementation in the main application
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class RoomUserDataSource

/**
 * Qualifier for the default UserLocalDataSource implementation
 *
 * This points to the implementation that should be used by default
 * in the application. Currently points to the Room implementation.
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class DefaultUserDataSource
