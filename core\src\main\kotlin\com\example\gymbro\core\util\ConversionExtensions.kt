package com.example.gymbro.core.util

import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.ui.text.UiText
import timber.log.Timber

/**
 * 统一类型转换扩展函数
 *
 * 提供安全的类型转换方法，支持字符串和任意对象的转换
 * 合并了原 NumberExtensions.kt 的功能以消除重复
 */

/**
 * 安全地将字符串转换为Int
 *
 * @param defaultValue 转换失败时的默认值
 * @return 转换后的Int值，如果转换失败则返回默认值
 */
fun String?.toIntSafely(defaultValue: Int = 0): Int {
    if (this == null || this.isBlank()) return defaultValue
    return try {
        this.toInt()
    } catch (e: NumberFormatException) {
        Timber.w("无法将'$this'转换为Int: ${e.message}")
        defaultValue
    }
}

/**
 * 安全地将字符串转换为Float
 *
 * @param defaultValue 转换失败时的默认值
 * @return 转换后的Float值，如果转换失败则返回默认值
 */
fun String?.toFloatSafely(defaultValue: Float = 0f): Float {
    if (this == null || this.isBlank()) return defaultValue
    return try {
        this.toFloat()
    } catch (e: NumberFormatException) {
        Timber.w("无法将'$this'转换为Float: ${e.message}")
        defaultValue
    }
}

/**
 * 安全地将字符串转换为Long
 *
 * @param defaultValue 转换失败时的默认值
 * @return 转换后的Long值，如果转换失败则返回默认值
 */
fun String?.toLongSafely(defaultValue: Long = 0L): Long {
    if (this == null || this.isBlank()) return defaultValue
    return try {
        this.toLong()
    } catch (e: NumberFormatException) {
        Timber.w("无法将'$this'转换为Long: ${e.message}")
        defaultValue
    }
}

/**
 * 安全地将字符串转换为Double
 *
 * @param defaultValue 转换失败时的默认值
 * @return 转换后的Double值，如果转换失败则返回默认值
 */
fun String?.toDoubleSafely(defaultValue: Double = 0.0): Double {
    if (this == null || this.isBlank()) return defaultValue
    return try {
        this.toDouble()
    } catch (e: NumberFormatException) {
        Timber.w("无法将'$this'转换为Double: ${e.message}")
        defaultValue
    }
}

/**
 * 将任意对象安全转换为Int
 *
 * @param defaultValue 转换失败时返回的默认值
 * @return 转换后的Int值或默认值
 */
fun Any?.toIntSafely(defaultValue: Int = 0): Int {
    if (this == null) return defaultValue

    return when (this) {
        is Int -> this
        is Long -> {
            if (this > Int.MAX_VALUE || this < Int.MIN_VALUE) {
                Timber.w("Long值 $this 超出Int范围，使用默认值")
                defaultValue
            } else {
                this.toInt()
            }
        }
        is Float -> {
            if (this > Int.MAX_VALUE || this < Int.MIN_VALUE) {
                Timber.w("Float值 $this 超出Int范围，使用默认值")
                defaultValue
            } else {
                this.toInt()
            }
        }
        is Double -> {
            if (this > Int.MAX_VALUE || this < Int.MIN_VALUE) {
                Timber.w("Double值 $this 超出Int范围，使用默认值")
                defaultValue
            } else {
                this.toInt()
            }
        }
        is String -> this.toIntSafely(defaultValue)
        else -> {
            try {
                val str = this.toString()
                str.toIntSafely(defaultValue)
            } catch (e: Exception) {
                Timber.w("无法将类型 ${this::class.java.simpleName} 转换为Int: ${e.message}")
                defaultValue
            }
        }
    }
}

/**
 * 将任意对象安全转换为Long
 *
 * @param defaultValue 转换失败时返回的默认值
 * @return 转换后的Long值或默认值
 */
fun Any?.toLongSafely(defaultValue: Long = 0L): Long {
    if (this == null) return defaultValue

    return when (this) {
        is Long -> this
        is Int -> this.toLong()
        is Float -> {
            if (this > Long.MAX_VALUE || this < Long.MIN_VALUE) {
                Timber.w("Float值 $this 超出Long范围，使用默认值")
                defaultValue
            } else {
                this.toLong()
            }
        }
        is Double -> {
            if (this > Long.MAX_VALUE || this < Long.MIN_VALUE) {
                Timber.w("Double值 $this 超出Long范围，使用默认值")
                defaultValue
            } else {
                this.toLong()
            }
        }
        is String -> this.toLongSafely(defaultValue)
        else -> {
            try {
                val str = this.toString()
                str.toLongSafely(defaultValue)
            } catch (e: Exception) {
                Timber.w("无法将类型 ${this::class.java.simpleName} 转换为Long: ${e.message}")
                defaultValue
            }
        }
    }
}

/**
 * 将任意对象安全转换为Float
 *
 * @param defaultValue 转换失败时返回的默认值
 * @return 转换后的Float值或默认值
 */
fun Any?.toFloatSafely(defaultValue: Float = 0f): Float {
    if (this == null) return defaultValue

    return when (this) {
        is Float -> this
        is Int -> this.toFloat()
        is Long -> {
            if (this > Float.MAX_VALUE || this < Float.MIN_VALUE) {
                Timber.w("Long值 $this 超出Float范围，使用默认值")
                defaultValue
            } else {
                this.toFloat()
            }
        }
        is Double -> {
            if (this > Float.MAX_VALUE || this < Float.MIN_VALUE) {
                Timber.w("Double值 $this 超出Float范围，使用默认值")
                defaultValue
            } else {
                this.toFloat()
            }
        }
        is String -> this.toFloatSafely(defaultValue)
        else -> {
            try {
                val str = this.toString()
                str.toFloatSafely(defaultValue)
            } catch (e: Exception) {
                Timber.w("无法将类型 ${this::class.java.simpleName} 转换为Float: ${e.message}")
                defaultValue
            }
        }
    }
}

/**
 * 将任意对象安全转换为Double
 *
 * @param defaultValue 转换失败时返回的默认值
 * @return 转换后的Double值或默认值
 */
fun Any?.toDoubleSafely(defaultValue: Double = 0.0): Double {
    if (this == null) return defaultValue

    return when (this) {
        is Double -> this
        is Float -> this.toDouble()
        is Int -> this.toDouble()
        is Long -> this.toDouble()
        is String -> this.toDoubleSafely(defaultValue)
        else -> {
            try {
                val str = this.toString()
                str.toDoubleSafely(defaultValue)
            } catch (e: Exception) {
                Timber.w("无法将类型 ${this::class.java.simpleName} 转换为Double: ${e.message}")
                defaultValue
            }
        }
    }
}

/**
 * 将字符串转换为Int并包装为ModernResult
 *
 * @return 包含Int值的ModernResult，如果转换失败则返回ModernResult.Error
 */
fun String?.toIntResult(): ModernResult<Int> {
    if (this == null || this.isBlank()) {
        return ModernResult.Error(
            DataErrors.Validation.customValidationError(
                operationName = "toIntResult",
                message = UiText.DynamicString("输入不能为空"),
                errorType = GlobalErrorType.Validation.Required,
                metadataMap = mapOf("field" to "input"),
            ),
        )
    }

    return try {
        ModernResult.Success(this.toInt())
    } catch (e: NumberFormatException) {
        Timber.w("无法将'$this'转换为Int: ${e.message}")
        ModernResult.Error(
            DataErrors.Validation.customValidationError(
                operationName = "toIntResult",
                message = UiText.DynamicString("无效的整数格式: $this"),
                errorType = GlobalErrorType.Validation.Format,
                metadataMap = mapOf("field" to "input", "value" to this),
            ),
        )
    }
}

/**
 * 将字符串转换为Float并包装为ModernResult
 *
 * @return 包含Float值的ModernResult，如果转换失败则返回ModernResult.Error
 */
fun String?.toFloatResult(): ModernResult<Float> {
    if (this == null || this.isBlank()) {
        return ModernResult.Error(
            DataErrors.Validation.customValidationError(
                operationName = "toFloatResult",
                message = UiText.DynamicString("输入不能为空"),
                errorType = GlobalErrorType.Validation.Required,
                metadataMap = mapOf("field" to "input"),
            ),
        )
    }

    return try {
        ModernResult.Success(this.toFloat())
    } catch (e: NumberFormatException) {
        Timber.w("无法将'$this'转换为Float: ${e.message}")
        ModernResult.Error(
            DataErrors.Validation.customValidationError(
                operationName = "toFloatResult",
                message = UiText.DynamicString("无效的浮点数格式: $this"),
                errorType = GlobalErrorType.Validation.Format,
                metadataMap = mapOf("field" to "input", "value" to this),
            ),
        )
    }
}

/**
 * 将字符串转换为Long并包装为ModernResult
 *
 * @return 包含Long值的ModernResult，如果转换失败则返回ModernResult.Error
 */
fun String?.toLongResult(): ModernResult<Long> {
    if (this == null || this.isBlank()) {
        return ModernResult.Error(
            DataErrors.Validation.customValidationError(
                operationName = "toLongResult",
                message = UiText.DynamicString("输入不能为空"),
                errorType = GlobalErrorType.Validation.Required,
                metadataMap = mapOf("field" to "input"),
            ),
        )
    }

    return try {
        ModernResult.Success(this.toLong())
    } catch (e: NumberFormatException) {
        Timber.w("无法将'$this'转换为Long: ${e.message}")
        ModernResult.Error(
            DataErrors.Validation.customValidationError(
                operationName = "toLongResult",
                message = UiText.DynamicString("无效的长整型格式: $this"),
                errorType = GlobalErrorType.Validation.Format,
                metadataMap = mapOf("field" to "input", "value" to this),
            ),
        )
    }
}

/**
 * 安全地将任何对象转换为字符串
 *
 * @param defaultValue 转换失败或空值时的默认值
 * @return 对象的字符串表示，如果对象为null则返回默认值
 */
fun Any?.toStringSafely(defaultValue: String = ""): String =
    when {
        this == null -> defaultValue
        this is String -> this
        else ->
            try {
                this.toString()
            } catch (e: Exception) {
                Timber.w("无法将'$this'转换为String: ${e.message}")
                defaultValue
            }
    }

// UiText相关的扩展函数已移至 UiTextExtensions.kt
// 包括 asStringSafely 等方法，请直接使用那里的实现

/**
 * 安全地将一个类型转换为另一个类型
 *
 * @param transform 转换函数
 * @param defaultValue 转换失败时的默认值
 * @return 转换后的值，如果转换失败则返回默认值
 */
inline fun <T, R> T?.transformSafely(
    transform: (T) -> R,
    defaultValue: R,
): R {
    if (this == null) return defaultValue
    return try {
        transform(this)
    } catch (e: Exception) {
        Timber.w("转换失败: ${e.message}")
        defaultValue
    }
}

/**
 * 安全地将一个类型转换为另一个类型并包装为ModernResult
 *
 * @param transform 转换函数
 * @return 包含转换结果的ModernResult，如果转换失败则返回ModernResult.Error
 */
inline fun <T, R> T?.transformToResult(
    transform: (T) -> R,
    errorMessage: (T?) -> UiText = { UiText.DynamicString("无法转换值: $it") },
): ModernResult<R> {
    if (this == null) {
        return ModernResult.Error(
            DataErrors.Validation.customValidationError(
                operationName = "transformToResult",
                message = errorMessage(null),
                validationType = "required",
                errorType = GlobalErrorType.Validation.Required,
            ),
        )
    }

    return try {
        ModernResult.Success(transform(this))
    } catch (e: Exception) {
        Timber.w("转换失败: ${e.message}")
        ModernResult.Error(
            DataErrors.Validation.customValidationError(
                operationName = "transformToResult",
                message = errorMessage(this),
                validationType = "invalidValue",
                errorType = GlobalErrorType.Validation.InvalidValue,
                metadataMap = mapOf("cause" to e),
            ),
        )
    }
}

/**
 * Map content类型转换为ModernResult
 *
 * @param content 要转换的内容
 * @param mapper 映射函数
 * @param uiMessage 可选的错误消息
 * @return 转换后的ModernResult
 */
fun <T : Any, R : Any> mapContentToModernResult(
    content: T?,
    mapper: (T) -> R,
    uiMessage: UiText = UiText.DynamicString("转换数据失败"),
    operationName: String = "mapContentToModernResult",
): ModernResult<R> =
    try {
        content?.let {
            try {
                ModernResult.Success(mapper(it))
            } catch (e: Exception) {
                Timber.e(e, "映射内容时发生异常: %s", uiMessage.toString())
                ModernResult.Error(
                    DataErrors.DataError.serialization(
                        operationName = operationName,
                        message = uiMessage,
                        cause = e,
                    ),
                )
            }
        } ?: ModernResult.Error(
            DataErrors.DataError.notFound(
                operationName = operationName,
                message = uiMessage,
            ),
        )
    } catch (e: Exception) {
        Timber.e(e, "转换到ModernResult时发生异常: %s", uiMessage.toString())
        ModernResult.Error(
            DataErrors.DataError.serialization(
                operationName = operationName,
                message = uiMessage,
                cause = e,
            ),
        )
    }
