package com.example.gymbro.core.util

import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult

/**
 * 如果接收者为null，则返回提供的默认值，否则返回接收者本身。
 *
 * @param defaultValue 如果接收者为null时返回的值。
 * @return 接收者或默认值。
 */
fun <T> T?.orDefault(defaultValue: T): T = this ?: defaultValue

/**
 * 安全地将可空的 String 转换为非空的 String。
 * 如果原始字符串为 null，则返回空字符串 ""。
 *
 * @return 非空字符串。
 */
fun String?.safeToStringOrEmpty(): String = this ?: ""

/**
 * 将可空类型 T? 转换为 ModernResult<T>。
 * 如果接收者不为 null，则返回 ModernResult.Success(value)。
 * 如果接收者为 null，则返回 ModernResult.Error(errorIfNull)。
 *
 * @param errorIfNull 当接收者为 null 时要返回的 ModernDataError。
 * @return ModernResult<T> 实例。
 */
fun <T> T?.toResultOrError(errorIfNull: () -> ModernDataError): ModernResult<T> =
    if (this != null) {
        ModernResult.Success(this)
    } else {
        ModernResult.Error(errorIfNull())
    }

/**
 * 如果列表为null，则返回一个空列表，否则返回原始列表。
 *
 * @return 一个非空列表。
 */
fun <T> List<T>?.orEmptyList(): List<T> = this ?: emptyList()

/**
 * 如果Map为null，则返回一个空Map，否则返回原始Map。
 *
 * @return 一个非空Map。
 */
fun <K, V> Map<K, V>?.orEmptyMap(): Map<K, V> = this ?: emptyMap()

/**
 * 对可空对象执行给定的操作块，并返回结果，如果对象本身为null，则返回null。
 * 类似于 Kotlin 标准库的 `let`，但明确用于可空链式调用并返回可空结果。
 *
 * @param R 操作块返回的类型。
 * @param block 如果接收者不为null，则对其执行的操作块。
 * @return 操作块的结果，如果原始对象为null，则为null。
 */
inline fun <T, R> T?.ifNotNull(block: (T) -> R): R? = this?.let(block)

/**
 * 空安全的字符串比较
 *
 * @param other 要比较的字符串
 * @param ignoreCase 是否忽略大小写
 * @return 字符串是否相等
 */
fun String?.safeEquals(other: String?, ignoreCase: Boolean = false): Boolean {
    return this?.equals(other, ignoreCase) ?: (other == null)
}
