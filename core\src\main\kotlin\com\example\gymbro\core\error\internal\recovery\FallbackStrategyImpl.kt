package com.example.gymbro.core.error.internal.recovery

import com.example.gymbro.core.error.recovery.FallbackStrategy
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import timber.log.Timber

/**
 * 回退恢复策略实现类
 *
 * 当主操作失败时提供备用数据源或默认值
 *
 * @param T 返回数据类型
 * @property fallbackOperation 提供备用数据的操作
 * @property predicate 可选的判断函数，用于决定哪些错误能使用此恢复策略
 */
class FallbackStrategyImpl<T>(
    private val fallbackOperation: suspend () -> T?,
    private val predicate: ((ModernDataError) -> Boolean)? = null,
) : FallbackStrategy<T> {

    /**
     * 执行回退策略
     *
     * @return 回退操作的结果或null（如果回退操作失败）
     */
    override suspend fun execute(): T? {
        return try {
            val result = fallbackOperation()
            if (result != null) {
                Timber.d("回退策略成功")
            } else {
                Timber.d("回退策略返回空值")
            }
            result
        } catch (e: Exception) {
            Timber.e(e, "回退操作执行失败: %s", e.message)
            null
        }
    }

    /**
     * 判断是否可以处理指定的错误
     *
     * @param error 需要判断的错误
     * @return 如果可以处理返回true，否则返回false
     */
    override fun canHandle(error: ModernDataError): Boolean {
        return predicate?.invoke(error) ?: true
    }

    /**
     * 执行回退策略并返回ModernResult
     *
     * @return 包装在ModernResult中的回退结果
     */
    override suspend fun executeWithResult(): ModernResult<T>? {
        val result = execute()
        return if (result != null) {
            ModernResult.Success(result)
        } else {
            null
        }
    }
}
