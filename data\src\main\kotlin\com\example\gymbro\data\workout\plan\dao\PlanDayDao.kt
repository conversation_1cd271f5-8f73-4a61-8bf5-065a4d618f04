package com.example.gymbro.data.workout.plan.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import androidx.room.Transaction
import com.example.gymbro.data.workout.plan.entity.PlanDayEntity
import com.example.gymbro.shared.models.workout.PlanProgressStatus
import kotlinx.coroutines.flow.Flow

/**
 * 计划日数据访问对象 - PlanDB DAO
 *
 * 基于 05_Final_Database_Architecture.md 设计
 * 提供计划日程管理功能
 */
@Dao
interface PlanDayDao {

    // ==================== 基础 CRUD ====================

    @Query("SELECT * FROM plan_days WHERE id = :planDayId")
    suspend fun getPlanDayById(planDayId: String): PlanDayEntity?

    @Query("SELECT * FROM plan_days WHERE planId = :planId ORDER BY dayNumber ASC")
    fun getPlanDaysByPlan(planId: String): Flow<List<PlanDayEntity>>

    @Query("SELECT * FROM plan_days WHERE planId = :planId ORDER BY dayNumber ASC")
    suspend fun getPlanDaysByPlanSync(planId: String): List<PlanDayEntity>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPlanDay(planDay: PlanDayEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPlanDays(planDays: List<PlanDayEntity>)

    @Update
    suspend fun updatePlanDay(planDay: PlanDayEntity)

    @Query("DELETE FROM plan_days WHERE id = :planDayId")
    suspend fun deletePlanDay(planDayId: String)

    @Query("DELETE FROM plan_days WHERE planId = :planId")
    suspend fun deleteAllPlanDays(planId: String)

    // ==================== 特定查询 ====================

    @Query("SELECT * FROM plan_days WHERE planId = :planId AND dayNumber = :dayNumber")
    suspend fun getPlanDayByNumber(planId: String, dayNumber: Int): PlanDayEntity?

    @Query("SELECT * FROM plan_days WHERE planId = :planId AND isRestDay = 0 ORDER BY dayNumber ASC")
    fun getWorkoutDaysInPlan(planId: String): Flow<List<PlanDayEntity>>

    @Query("SELECT * FROM plan_days WHERE planId = :planId AND isRestDay = 1 ORDER BY dayNumber ASC")
    fun getRestDaysInPlan(planId: String): Flow<List<PlanDayEntity>>

    // ==================== 统计查询 ====================

    @Query("SELECT COUNT(*) FROM plan_days WHERE planId = :planId")
    suspend fun getPlanDayCount(planId: String): Int

    @Query("SELECT COUNT(*) FROM plan_days WHERE planId = :planId AND isRestDay = 0")
    suspend fun getWorkoutDayCount(planId: String): Int

    @Query("SELECT COUNT(*) FROM plan_days WHERE planId = :planId AND isRestDay = 1")
    suspend fun getRestDayCount(planId: String): Int

    @Query("SELECT COUNT(*) FROM plan_days WHERE planId = :planId AND isCompleted = 1")
    suspend fun getCompletedDayCount(planId: String): Int // 新增：已完成天数统计

    @Query("SELECT MAX(dayNumber) FROM plan_days WHERE planId = :planId")
    suspend fun getMaxDayNumber(planId: String): Int?

    // ==================== 批量操作 ====================

    @Query("UPDATE plan_days SET isRestDay = :isRestDay WHERE id = :planDayId")
    suspend fun updatePlanDayRestStatus(planDayId: String, isRestDay: Boolean)

    @Query("UPDATE plan_days SET notes = :notes WHERE id = :planDayId")
    suspend fun updatePlanDayNotes(planDayId: String, notes: String?)

    @Transaction
    @Query("UPDATE plan_days SET isCompleted = :isCompleted WHERE planId = :planId AND dayNumber = :dayNumber")
    suspend fun updateCompletionStatus(planId: String, dayNumber: Int, isCompleted: Boolean) // 新增：更新完成状态

    // ==================== 进度相关操作 ====================

    @Transaction
    @Query("UPDATE plan_days SET progress = :progress WHERE id = :planDayId")
    suspend fun updatePlanDayProgress(planDayId: String, progress: PlanProgressStatus)

    @Transaction
    @Query("UPDATE plan_days SET progress = :progress WHERE planId = :planId AND dayNumber = :dayNumber")
    suspend fun updatePlanDayProgressByNumber(planId: String, dayNumber: Int, progress: PlanProgressStatus)

    @Query("SELECT * FROM plan_days WHERE planId = :planId AND progress = :progress ORDER BY dayNumber ASC")
    fun getPlanDaysByProgress(planId: String, progress: PlanProgressStatus): Flow<List<PlanDayEntity>>

    @Query("SELECT COUNT(*) FROM plan_days WHERE planId = :planId AND progress = :progress")
    suspend fun getCountByProgress(planId: String, progress: PlanProgressStatus): Int

    @Query("SELECT * FROM plan_days WHERE planId = :planId AND progress != 'NOT_STARTED' ORDER BY dayNumber ASC")
    fun getStartedPlanDays(planId: String): Flow<List<PlanDayEntity>>
}
