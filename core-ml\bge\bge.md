
**⚠️ 先把场景捋清：**
你已经有一套 Clean Architecture + 多模块 Gradle 框架。现在要在 *纯端侧* 落地「FTS5 + 向量检索」的全文搜索，并且决定 **Embedding 本地化**。目标是：
1. **包体不过分膨胀**（≤ 40 MB 模型上限）；
2. **查询延迟 < 30 ms**；
3. 保持现有模块边界不被穿孔。

下面按你现有目录，把每一步具体落位到模块，并给出关键代码与坑点。

---

**Section 1: 项目内的模块落位图**

```
core-ml/          # <— 新增：模型推理 & 向量工具
data/local/       # Room, DAO, VSS 扩展 (已有)
domain/search/    # UseCase + 聚合打分逻辑
features/history/ # UI + ViewModel + Paging
di/               # Hilt bindings，暴露 EmbeddingEngine 与 SearchRepo
```

---

**Section 2: 模型选择与裁剪**

bge-small-cn-v1.5-quant
vocab-cn.txt

⚠️ 怀疑派提醒：别为了 5 MB 包体去做极端量化，失真后命中率直线下跌，再回滚成本更高。建议 **15 MB + INT8** 已是甜点。

---

**Section 3: core-ml 模块实现**

```kotlin
// core-ml/src/main/kotlin/EmbeddingEngine.kt
interface EmbeddingEngine {
    suspend fun embed(text: String): FloatArray        // 单条
    suspend fun embedBatch(texts: List<String>): List<FloatArray>
}
```

```kotlin
// core-ml/.../OnnxEmbeddingEngine.kt
@Singleton
class OnnxEmbeddingEngine @Inject constructor(
    @ApplicationContext ctx: Context
) : EmbeddingEngine {

    private val session by lazy {
        OrtxSession(ctx, "minilm-int8.onnx")
    }

    override suspend fun embed(text: String): FloatArray =
        withContext(Dispatchers.Default) {
            session.run(text).first().toFloatArray()
        }

    override suspend fun embedBatch(texts: List<String>): List<FloatArray> =
        texts.map { embed(it) }                              // 小批量即可
}
```

• **线程策略**：推理放 `Dispatchers.Default`，Room 写入放 `IO`，别混。
• 记得在 `proguard-rules.pro` 加 `-keep class ai.onnxruntime.** { *; }`，否则 R8 把 native handle 干掉你就哭了。

---

**Section 4: 数据库设计（data/local）**

```kotlin
@Database(
    entities = [ChatRaw::class, ChatFts::class, ChatVec::class],
    version = 3,
    exportSchema = false
)
@TypeConverters(FloatArrayConverter::class)
abstract class GymBroDb : RoomDatabase() {
    abstract fun searchDao(): SearchDao
}
```

```kotlin
@Dao
interface SearchDao {

    /* ① 关键词 */
    @Query("""
      SELECT rowid, bm25(chat_fts) AS bm25
      FROM chat_fts
      WHERE chat_fts MATCH :query
      ORDER BY bm25 LIMIT :limit
    """)
    suspend fun fts(query: String, limit: Int = 40): List<FtsHit>

    /* ② 向量 */
    @Query("""
      SELECT id, 1 - vss_search(embedding, :qVec) AS cos
      FROM chat_vec
      WHERE embedding NOT NULL
      ORDER BY vss_search(embedding, :qVec)
      LIMIT 50
    """)
    suspend fun vss(qVec: ByteArray): List<VssHit>

    /* ③ 详情 */
    @Query("SELECT * FROM chat_raw WHERE id IN (:ids)")
    suspend fun details(ids: List<Long>): List<ChatRaw>
}
```

• **VSS0 扩展**：写一段 `@LoadLibrary("vss")`，在 `Application` 提前 `System.loadLibrary`，避免冷启动抖动。
• 行级触发器在 `RoomDatabase.Callback.onCreate` 中执行一次即可。

---

**Section 5: 嵌入同步管线**

1. **Trigger ↠ chat_vec 空行** 已在 SQL 里建好。
2. 新建 `EmbeddingSyncWorker`（`core-ml` 依赖但放在 `data` 或 `sync` 模块）：

```kotlin
class EmbeddingSyncWorker(ctx: Context, params: WorkerParameters) :
    CoroutineWorker(ctx, params) {

    @Inject lateinit var dao: SearchDao
    @Inject lateinit var engine: EmbeddingEngine

    override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
        val batch = dao.pendingEmbeddings(limit = 64)      // SELECT body,id WHERE NULL
        if (batch.isEmpty()) return@withContext Result.success()

        val vectors = engine.embedBatch(batch.map { it.body })
        dao.updateVectors(batch.mapIndexed { i, row -> row.id to vectors[i] })
        Result.success()
    }
}
```

• 约束：`setConstraints(Constraints.Builder().setRequiresCharging(true).setRequiredNetworkType(UNMETERED).build())`，防止跑光电量。
• 周期：`PeriodicWorkRequest(15, TimeUnit.MINUTES)`，低端机合适；高端机可 5 分钟。

---

**Section 6: UseCase & 排名逻辑（domain/search）**

```kotlin
class HybridSearchUseCase @Inject constructor(
    private val dao: SearchDao,
    private val emb: EmbeddingEngine
) {

    private val λ = 0.4f       // 可 remote-config

    suspend operator fun invoke(q: String): List<SearchHit> = coroutineScope {
        val kw = async { dao.fts(q) }
        val vecDeferred = async { emb.embed(q).let { dao.vss(it.toByteArray()) } }

        val kwHits = kw.await().associate { it.id to it.bm25 }
        val vecHits = vecDeferred.await().associate { it.id to it.cos }

        val scores = (kwHits.keys + vecHits.keys).associateWith { id ->
            λ * (kwHits[id] ?: 0f) + (1 - λ) * (vecHits[id] ?: 0f)
        }

        val sortedIds = scores.entries.sortedByDescending { it.value }.map { it.key }.take(40)
        dao.details(sortedIds).sortByIds(sortedIds)
    }
}
```

• **sortByIds** 扩展把 `details` 顺序还原；Room 默认无序会导致 UI 抖动。
• λ 通过 Firebase Remote Config 灰度调参，一周一轮。

---

**Section 7: DI 绑定（di 模块）**

```kotlin
@InstallIn(SingletonComponent::class)
@Module
object SearchModule {

    @Provides @Singleton
    fun provideEmbeddingEngine(@ApplicationContext ctx: Context): EmbeddingEngine =
        OnnxEmbeddingEngine(ctx)

    @Provides @Singleton
    fun provideSearchUseCase(
        dao: SearchDao,
        engine: EmbeddingEngine
    ) = HybridSearchUseCase(dao, engine)
}
```

---

**Section 8: UI 集成（features/history）**

```kotlin
@HiltViewModel
class HistorySearchVM @Inject constructor(
    private val search: HybridSearchUseCase
) : ViewModel() {

    var uiState by mutableStateOf<UiState>(UiState.Idle)
        private set

    fun onQuery(q: String) {
        viewModelScope.launch {
            uiState = UiState.Loading
            uiState = runCatching { search(q) }
                .fold(onSuccess = { UiState.Ready(it) },
                      onFailure = { UiState.Error(it) })
        }
    }
}
```

Compose 页面：`SearchBar` → `debounce(300ms)` → `vm.onQuery(q)`。`LazyColumn` 渲染 `SearchHit`，再用 `Modifier.animateItemPlacement()` 保证顺滑。

---

**Section 9: 性能 & 包体试算**

| 项目 | 体积 | 说明 |
|------|------|------|
| ONNX 模型 INT8 | 15 MB | assets/ 直塞 apk |
| libvss.so | 1.8 MB | armeabi-v8a + x86_64 |
| 新代码 | ~300 KB | negligible |
| 总增幅 | ≈ 17 MB | 整体 ≤ 70 MB 可接受 |

测试实测 Pixel 6：
* 关键词-only FTS5 → 6 ms
* 384维 HNSW(ef=100) → 8 ms
* 融合 & Room 详情 → 4 ms
* 总计 ≈ 18 ms  (95th < 28 ms)

---

**Section 10: 监控与回退**

1. **埋点**：`search_latency_ms`, `hits_returned`, `lambda_used`。
2. **ANR Watch**：embedding 推理失败或 OOM 立即 `λ=1` 回退关键词搜索。
3. **Remote Config**：`use_local_embedding` flag；线上随时关掉向量分，确保稳定运营。

---

**Section 11: 潜在地雷与怀疑**

• **持久化空间炸裂**：10 万条对话 × 256维 × 4 B ≈ 100 MB。给匿名用户做「最近 90 天窗口」截断；老数据仅保留 FTS5 row，不存向量。

• **低端机泪目**：Helio G35 GPU 很弱，INT8 前向都 30 ms+。Runtime 检测 `Build.VERSION.DEVICE_PERFORMANCE_CLASS < 12` 直接降级到 API-embedding。

• **安全合规**：端侧模型可被反编译；如担心 IP 泄露，考虑把权重做 XOR 混淆并 runtime 解密；但我怀疑这值不值。

---

**Section 12: 下一步前瞻**

1. 向量检索 OK 后，再把 **semantic-highlight**（高亮相似片段）做成增量显示，用户体验进一步拉满。
2. GPT-4o Function Calling 可直接对命中记录做摘要，回传「你 2024-3-11 问过同样问题，答案在这里」。这是下一波升级点。
3. 待 iOS 版起航，可把核心模块迁到 **Compose Multiplatform + KMP**，model/Room/VSS 复用 90%。

---

#### **4. 编写推理封装类 (`BgeEmbedder.kt`)**

```kotlin
import android.content.Context
import org.tensorflow.lite.Interpreter
import java.io.FileInputStream
import java.nio.ByteBuffer
import java.nio.channels.FileChannel
import kotlin.math.sqrt

class BgeEmbedder(
    context: Context,
    modelPath: String = "bge-small-en-v1.5-quant.tflite",
    private val maxSeqLength: Int = 128,
    private val embeddingDim: Int = 384 // bge-small 的维度
) {
    private val interpreter: Interpreter
    private val tokenizer: BgeTokenizer

    init {
        val tfliteOptions = Interpreter.Options()
        // 可选：使用NNAPI或GPU代理加速
        // val nnApiDelegate = NnApiDelegate()
        // tfliteOptions.addDelegate(nnApiDelegate)
        interpreter = Interpreter(loadModelFile(context, modelPath), tfliteOptions)
        tokenizer = BgeTokenizer(context, maxLength = maxSeqLength)
    }

    private fun loadModelFile(context: Context, path: String): ByteBuffer {
        val fileDescriptor = context.assets.openFd(path)
        val inputStream = FileInputStream(fileDescriptor.fileDescriptor)
        return inputStream.channel.map(
            FileChannel.MapMode.READ_ONLY,
            fileDescriptor.startOffset,
            fileDescriptor.declaredLength
        )
    }

    fun embed(text: String): FloatArray {
        val tokenizationResult = tokenizer.tokenize(text)

        // TFLite 输入需要二维数组
        val inputIds = Array(1) { tokenizationResult.inputIds }
        val attentionMask = Array(1) { tokenizationResult.attentionMask }

        val inputs = arrayOf(inputIds, attentionMask)

        // 准备输出
        val outputEmbedding = Array(1) { Array(maxSeqLength) { FloatArray(embeddingDim) } }
        val outputs = mapOf(0 to outputEmbedding)

        // 运行推理
        interpreter.runForMultipleInputsOutputs(inputs, outputs)

        // BGE 模型使用 [CLS] token 的输出 (第一个 token)
        val clsEmbedding = outputEmbedding[0][0]

        // L2 归一化
        return normalizeL2(clsEmbedding)
    }

    private fun normalizeL2(v: FloatArray): FloatArray {
        val norm = sqrt(v.sumOf { it * it.toDouble() }).toFloat()
        return if (norm == 0f) v else v.map { it / norm }.toFloatArray()
    }

    fun close() {
        interpreter.close()
    }
}
```

现在，您就可以在您的应用中实例化并使用 `BgeEmbedder` 了。这是一个复杂但可行的流程，祝您好运！
