package com.example.gymbro.data.local.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * 日历事件实体
 *
 * 🎯 功能特性：
 * - 映射AppDatabase中的calendar_events表
 * - 支持训练相关的日历事件存储
 * - 提供完整的元数据支持
 *
 * 表结构基于AppDatabase Schema定义：
 * - 基础字段: id, user_id, date, title
 * - 训练字段: workout_id, duration_minutes
 * - 状态字段: is_completed, cancelled
 * - 同步字段: created_at, modified_at, is_synced
 *
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0 (Calendar Persistence Implementation)
 */
@Entity(
    tableName = "calendar_events",
    indices = [
        Index("user_id"),
        Index("workout_id"),
        Index("date"),
    ],
)
data class CalendarEventEntity(
    @PrimaryKey
    val id: String,

    // === 关联信息 ===
    @ColumnInfo(name = "user_id")
    val userId: String,

    @ColumnInfo(name = "workout_id")
    val workoutId: String? = null,

    // === 基础信息 ===
    @ColumnInfo(name = "event_type")
    val eventType: String, // TEMPLATE, PLAN, CUSTOM_WORKOUT, REST_DAY

    @ColumnInfo(name = "date")
    val date: Long, // 日期时间戳

    @ColumnInfo(name = "title")
    val title: String,

    @ColumnInfo(name = "description")
    val description: String? = null,

    // === 训练相关信息 ===
    @ColumnInfo(name = "duration_minutes")
    val durationMinutes: Int? = null,

    @ColumnInfo(name = "color")
    val color: String? = null,

    @ColumnInfo(name = "is_all_day")
    val isAllDay: Boolean = false,

    @ColumnInfo(name = "reminder_minutes_before")
    val reminderMinutesBefore: Int? = null,

    // === 状态信息 ===
    @ColumnInfo(name = "is_completed")
    val isCompleted: Boolean = false,

    @ColumnInfo(name = "completion_date")
    val completionDate: Long? = null,

    @ColumnInfo(name = "cancelled")
    val cancelled: Boolean = false,

    // === 重复规则 ===
    @ColumnInfo(name = "recurrence_rule")
    val recurrenceRule: String? = null,

    // === 时间戳 ===
    @ColumnInfo(name = "created_at")
    val createdAt: Long = System.currentTimeMillis(),

    @ColumnInfo(name = "modified_at")
    val modifiedAt: Long = System.currentTimeMillis(),

    // === 同步信息 ===
    @ColumnInfo(name = "is_synced")
    val isSynced: Boolean = false,
)
