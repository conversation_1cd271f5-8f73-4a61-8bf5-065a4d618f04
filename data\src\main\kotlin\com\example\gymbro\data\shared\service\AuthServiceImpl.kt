package com.example.gymbro.data.shared.service

import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.model.auth.AuthState
import com.example.gymbro.domain.profile.model.user.User
import com.example.gymbro.domain.profile.model.user.enums.UserType
import com.example.gymbro.domain.profile.model.user.state.UserTypeWithNetworkStatus
import com.example.gymbro.domain.service.auth.AuthService
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 认证服务实现
 *
 * 重构说明：移除V1/V2版本方法，统一使用User模型，简化实现。
 * 当前为临时实现，提供基础功能框架。
 */
@Singleton
class AuthServiceImpl
@Inject
constructor() : AuthService {
    override suspend fun loginWithEmail(
        email: String,
        password: String,
    ): ModernResult<User> =
        ModernResult.Error(
            ModernDataError(
                operationName = "AuthService.loginWithEmail",
                errorType = GlobalErrorType.System.NotImplemented,
                uiMessage = UiText.DynamicString("认证服务暂未实现"),
            ),
        )

    override suspend fun loginAnonymously(): ModernResult<User> =
        ModernResult.Error(
            ModernDataError(
                operationName = "AuthService.loginAnonymously",
                errorType = GlobalErrorType.System.NotImplemented,
                uiMessage = UiText.DynamicString("认证服务暂未实现"),
            ),
        )

    override suspend fun loginWithGoogle(idToken: String): ModernResult<User> =
        ModernResult.Error(
            ModernDataError(
                operationName = "AuthService.loginWithGoogle",
                errorType = GlobalErrorType.System.NotImplemented,
                uiMessage = UiText.DynamicString("认证服务暂未实现"),
            ),
        )

    override suspend fun registerWithEmail(
        email: String,
        password: String,
        username: String,
    ): ModernResult<User> =
        ModernResult.Error(
            ModernDataError(
                operationName = "AuthService.registerWithEmail",
                errorType = GlobalErrorType.System.NotImplemented,
                uiMessage = UiText.DynamicString("认证服务暂未实现"),
            ),
        )

    override suspend fun requestPhoneVerificationCode(phoneNumber: String): ModernResult<Unit> = ModernResult.Success(
        Unit,
    )

    override suspend fun verifyPhoneCodeAndLogin(
        phoneNumber: String,
        code: String,
    ): ModernResult<User> =
        ModernResult.Error(
            ModernDataError(
                operationName = "AuthService.verifyPhoneCodeAndLogin",
                errorType = GlobalErrorType.System.NotImplemented,
                uiMessage = UiText.DynamicString("认证服务暂未实现"),
            ),
        )

    override suspend fun resendPhoneVerificationCode(phoneNumber: String): ModernResult<Unit> = ModernResult.Success(
        Unit,
    )

    override suspend fun linkEmailCredential(
        email: String,
        password: String,
    ): ModernResult<Unit> = ModernResult.Success(Unit)

    override suspend fun linkPhoneCredential(
        phoneNumber: String,
        code: String,
    ): ModernResult<Unit> = ModernResult.Success(Unit)

    override suspend fun logout(): ModernResult<Unit> = ModernResult.Success(Unit)

    override fun checkAuthState(): Flow<ModernResult<AuthState>> =
        flowOf(
            ModernResult.Error(
                ModernDataError(
                    operationName = "AuthService.checkAuthState",
                    errorType = GlobalErrorType.System.NotImplemented,
                    uiMessage = UiText.DynamicString("认证服务暂未实现"),
                ),
            ),
        )

    override fun observeAuthState(): Flow<ModernResult<User?>> = flowOf(ModernResult.Success(null))

    override suspend fun getCurrentUser(): ModernResult<User> =
        ModernResult.Error(
            ModernDataError(
                operationName = "AuthService.getCurrentUser",
                errorType = GlobalErrorType.System.NotImplemented,
                uiMessage = UiText.DynamicString("认证服务暂未实现"),
            ),
        )

    override suspend fun getCurrentUserId(): ModernResult<String?> = ModernResult.Success(null)

    override suspend fun isLoggedIn(): ModernResult<Boolean> = ModernResult.Success(false)

    override suspend fun isAnonymousUser(): ModernResult<Boolean> = ModernResult.Success(true)

    override suspend fun saveAnonymousUser(userId: String): ModernResult<Unit> = ModernResult.Success(
        Unit,
    )

    override suspend fun updateLastLoginTime(userId: String): ModernResult<Unit> = ModernResult.Success(
        Unit,
    )

    override fun getUserTypeWithNetworkStatus(): Flow<ModernResult<UserTypeWithNetworkStatus>> =
        flowOf(
            ModernResult.Error(
                ModernDataError(
                    operationName = "AuthService.getUserTypeWithNetworkStatus",
                    errorType = GlobalErrorType.System.NotImplemented,
                    uiMessage = UiText.DynamicString("认证服务暂未实现"),
                ),
            ),
        )

    override suspend fun getUserType(): ModernResult<UserType> =
        ModernResult.Error(
            ModernDataError(
                operationName = "AuthService.getUserType",
                errorType = GlobalErrorType.System.NotImplemented,
                uiMessage = UiText.DynamicString("认证服务暂未实现"),
            ),
        )

    override suspend fun uploadAvatar(imageData: ByteArray): ModernResult<String> =
        ModernResult.Error(
            ModernDataError(
                operationName = "AuthService.uploadAvatar",
                errorType = GlobalErrorType.System.NotImplemented,
                uiMessage = UiText.DynamicString("认证服务暂未实现"),
            ),
        )
}
