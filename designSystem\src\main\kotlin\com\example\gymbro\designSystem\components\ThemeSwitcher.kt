package com.example.gymbro.designSystem.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.example.gymbro.core.theme.ColorMode
import com.example.gymbro.core.theme.ThemeStyle
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.GymBroTokenValidator
import com.example.gymbro.designSystem.theme.tokens.Tokens

/**
 * 主题切换器组件
 *
 * 提供快速的主题切换功能，支持：
 * - 主题风格切换（Grok ⇄ ChatGPT）
 * - 颜色模式切换（深色 ⇄ 浅色 ⇄ 跟随系统）
 *
 * 可以作为独立组件放置在任何需要主题切换的地方
 */
@Composable
fun ThemeSwitcher(
    currentStyle: ThemeStyle,
    currentMode: ColorMode,
    onToggleStyle: () -> Unit,
    onToggleColor: () -> Unit,
    modifier: Modifier = Modifier,
    showLabels: Boolean = true,
    orientation: ThemeSwitcherOrientation = ThemeSwitcherOrientation.Horizontal,
) {
    // Token 使用验证 (仅在 DEBUG 模式下)
    GymBroTokenValidator.validateTokenUsage("ThemeSwitcher")
    when (orientation) {
        ThemeSwitcherOrientation.Horizontal -> {
            Row(
                modifier = modifier,
                horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small), // 8.dp - 使用 Token
                verticalAlignment = Alignment.CenterVertically,
            ) {
                ThemeSwitcherContent(
                    currentStyle = currentStyle,
                    currentMode = currentMode,
                    onToggleStyle = onToggleStyle,
                    onToggleColor = onToggleColor,
                    showLabels = showLabels,
                )
            }
        }
        ThemeSwitcherOrientation.Vertical -> {
            Column(
                modifier = modifier,
                verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small), // 8.dp - 使用 Token
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                ThemeSwitcherContent(
                    currentStyle = currentStyle,
                    currentMode = currentMode,
                    onToggleStyle = onToggleStyle,
                    onToggleColor = onToggleColor,
                    showLabels = showLabels,
                )
            }
        }
    }
}

/**
 * 主题切换器内容组件
 */
@Composable
private fun ThemeSwitcherContent(
    currentStyle: ThemeStyle,
    currentMode: ColorMode,
    onToggleStyle: () -> Unit,
    onToggleColor: () -> Unit,
    showLabels: Boolean,
) {
    // 主题风格切换按钮
    ThemeStyleToggleButton(
        currentStyle = currentStyle,
        onToggle = onToggleStyle,
        showLabel = showLabels,
    )

    // 颜色模式切换按钮
    ColorModeToggleButton(
        currentMode = currentMode,
        onToggle = onToggleColor,
        showLabel = showLabels,
    )
}

/**
 * 主题风格切换按钮
 */
@Composable
private fun ThemeStyleToggleButton(
    currentStyle: ThemeStyle,
    onToggle: () -> Unit,
    showLabel: Boolean,
    modifier: Modifier = Modifier,
) {
    val (icon, label) =
        when (currentStyle) {
            ThemeStyle.GROK -> Icons.Default.AutoAwesome to "科技"
            ThemeStyle.CHATGPT -> Icons.Default.Psychology to "简约"
            ThemeStyle.DEEPSEEK -> Icons.Default.Psychology to "DeepSeek"
        }

    if (showLabel) {
        OutlinedButton(
            onClick = onToggle,
            modifier = modifier,
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                modifier = Modifier.size(Tokens.Icon.Small + Tokens.Spacing.Tiny), // 18.dp - 使用 Token 组合
            )
            Spacer(modifier = Modifier.width(Tokens.Spacing.Small)) // 8.dp - 使用 Token
            Text(label)
        }
    } else {
        IconButton(
            onClick = onToggle,
            modifier = modifier,
        ) {
            Icon(
                imageVector = icon,
                contentDescription = "切换主题风格：当前为$label",
                tint = MaterialTheme.colorScheme.onSurface,
            )
        }
    }
}

/**
 * 颜色模式切换按钮
 */
@Composable
private fun ColorModeToggleButton(
    currentMode: ColorMode,
    onToggle: () -> Unit,
    showLabel: Boolean,
    modifier: Modifier = Modifier,
) {
    val (icon, label) =
        when (currentMode) {
            ColorMode.LIGHT -> Icons.Default.LightMode to "浅色"
            ColorMode.DARK -> Icons.Default.DarkMode to "深色"
            ColorMode.SYSTEM -> Icons.Default.Smartphone to "跟随系统"
        }

    if (showLabel) {
        OutlinedButton(
            onClick = onToggle,
            modifier = modifier,
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                modifier = Modifier.size(Tokens.Icon.Small + Tokens.Spacing.Tiny), // 18.dp - 使用 Token 组合
            )
            Spacer(modifier = Modifier.width(Tokens.Spacing.Small)) // 8.dp - 使用 Token
            Text(label)
        }
    } else {
        IconButton(
            onClick = onToggle,
            modifier = modifier,
        ) {
            Icon(
                imageVector = icon,
                contentDescription = "切换颜色模式：当前为$label",
                tint = MaterialTheme.colorScheme.onSurface,
            )
        }
    }
}

/**
 * 紧凑型主题切换器示例
 *
 * 只显示图标，适合放在工具栏或导航栏中
 * 注意：实际使用时需要传入真实的主题状态和回调
 */
@Composable
fun CompactThemeSwitcherExample(
    currentStyle: ThemeStyle = ThemeStyle.GROK,
    currentMode: ColorMode = ColorMode.SYSTEM,
    onToggleStyle: () -> Unit = {},
    onToggleColor: () -> Unit = {},
    modifier: Modifier = Modifier,
) {
    ThemeSwitcher(
        currentStyle = currentStyle,
        currentMode = currentMode,
        onToggleStyle = onToggleStyle,
        onToggleColor = onToggleColor,
        modifier = modifier,
        showLabels = false,
        orientation = ThemeSwitcherOrientation.Horizontal,
    )
}

/**
 * 扩展型主题切换器示例
 *
 * 显示完整的标签，适合放在设置页面中
 * 注意：实际使用时需要传入真实的主题状态和回调
 */
@Composable
fun ExpandedThemeSwitcherExample(
    currentStyle: ThemeStyle = ThemeStyle.GROK,
    currentMode: ColorMode = ColorMode.SYSTEM,
    onToggleStyle: () -> Unit = {},
    onToggleColor: () -> Unit = {},
    modifier: Modifier = Modifier,
    orientation: ThemeSwitcherOrientation = ThemeSwitcherOrientation.Horizontal,
) {
    ThemeSwitcher(
        currentStyle = currentStyle,
        currentMode = currentMode,
        onToggleStyle = onToggleStyle,
        onToggleColor = onToggleColor,
        modifier = modifier,
        showLabels = true,
        orientation = orientation,
    )
}

/**
 * 主题切换器方向枚举
 */
enum class ThemeSwitcherOrientation {
    Horizontal,
    Vertical,
}

// === 预览组件 ===

@GymBroPreview
@Composable
private fun ThemeSwitcherPreview() {
    GymBroTheme {
        Surface {
            Column(
                modifier = Modifier.padding(Tokens.Spacing.Medium), // 16.dp - 使用 Token
                verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium), // 16.dp - 使用 Token
            ) {
                Text("水平布局（带标签）")
                ExpandedThemeSwitcherExample()

                Text("水平布局（仅图标）")
                CompactThemeSwitcherExample()

                Text("垂直布局（带标签）")
                ExpandedThemeSwitcherExample(orientation = ThemeSwitcherOrientation.Vertical)
            }
        }
    }
}

@GymBroPreview
@Composable
private fun CompactThemeSwitcherPreview() {
    GymBroTheme {
        Surface {
            Row(
                modifier = Modifier.padding(Tokens.Spacing.Medium), // 16.dp - 使用 Token
                horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small), // 8.dp - 使用 Token
            ) {
                Text("工具栏示例：")
                CompactThemeSwitcherExample()
            }
        }
    }
}
