# 🚀 GymBro性能优化ProGuard规则
# 基于Clean Architecture + MVI 2.0架构的优化配置

# ==================== 基础配置 ====================
# 保持行号信息，便于调试
-keepattributes LineNumberTable,SourceFile
-renamesourcefileattribute SourceFile

# 保持注解
-keepattributes *Annotation*

# ==================== GymBro项目特定规则 ====================
# 保持所有GymBro包的公共API
-keep class com.example.gymbro.** { *; }

# 保持所有数据类（用于序列化）
-keep class * extends com.example.gymbro.domain.** { *; }
-keep class * extends com.example.gymbro.shared.models.** { *; }

# ==================== Kotlin序列化 ====================
# 保持序列化字段名
-keepclassmembers class * {
    @kotlinx.serialization.SerialName <fields>;
}

# 保持序列化类
-keep,includedescriptorclasses class com.example.gymbro.**$$serializer { *; }
-keepclassmembers class com.example.gymbro.** {
    *** Companion;
}
-keepclasseswithmembers class com.example.gymbro.** {
    kotlinx.serialization.KSerializer serializer(...);
}

# ==================== Hilt依赖注入 ====================
# 保持Hilt生成的类
-keep class dagger.hilt.** { *; }
-keep class * extends dagger.hilt.** { *; }
-keep @dagger.hilt.** class * { *; }

# ==================== Retrofit网络请求 ====================
# 保持Retrofit接口
-keep interface com.example.gymbro.**.api.** { *; }
-keep class com.example.gymbro.**.dto.** { *; }

# ==================== Room数据库 ====================
# 保持Room实体和DAO
-keep class * extends androidx.room.RoomDatabase { *; }
-keep @androidx.room.Entity class * { *; }
-keep @androidx.room.Dao class * { *; }

# ==================== Compose UI ====================
# 保持Compose相关类
-keep class androidx.compose.** { *; }
-keep class * extends androidx.compose.** { *; }

# ==================== Firebase ====================
# 保持Firebase相关类
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }

# ==================== 第三方库优化 ====================
# OkHttp
-dontwarn okhttp3.**
-dontwarn okio.**

# Timber日志
-dontwarn org.slf4j.**

# Conscrypt
-dontwarn org.conscrypt.**

# ==================== 通用优化规则 ====================
# 移除日志调用（Release版本）
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# 移除Timber日志（Release版本）
-assumenosideeffects class timber.log.Timber {
    public static *** v(...);
    public static *** d(...);
    public static *** i(...);
    public static *** w(...);
    public static *** e(...);
}

# ==================== 性能优化 ====================
# 启用优化
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification
-dontpreverify

# 保持枚举
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# 保持Parcelable
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# 保持Serializable
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}
