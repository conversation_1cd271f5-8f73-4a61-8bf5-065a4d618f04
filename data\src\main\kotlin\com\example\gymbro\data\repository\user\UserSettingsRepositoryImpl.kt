package com.example.gymbro.data.repository.user

import com.example.gymbro.core.error.extensions.toModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.local.datastore.PreferencesDataStore
import com.example.gymbro.domain.profile.model.user.settings.UserSettings
import com.example.gymbro.domain.profile.repository.user.UserSettingsRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * UserSettingsRepository接口的实现
 * 管理用户设置的存储和检索
 *
 * Phase 5 错误处理统一：移除errorHandlingUtils依赖，直接处理错误
 */
@Singleton
class UserSettingsRepositoryImpl
@Inject
constructor(
    private val preferencesDataStore: PreferencesDataStore,
    private val logger: Logger,
) : UserSettingsRepository {
    /**
     * 获取用户设置
     * @param userId 用户ID
     * @return 用户设置流
     */
    override fun getUserSettings(userId: String): Flow<ModernResult<UserSettings?>> =
        preferencesDataStore
            .getUserSettings(userId)
            .map { settings ->
                // 确保存储的设置与当前用户匹配
                if (settings.userId.isEmpty() || settings.userId == userId) {
                    ModernResult.success(settings)
                } else {
                    ModernResult.success(null)
                }
            }.catch { e ->
                logger.e(e, "获取用户设置流失败")
                emit(
                    ModernResult.error(
                        e.toModernDataError(
                            uiMessage = UiText.DynamicString("获取用户设置流失败"),
                            operationName = "getUserSettings_flow_catch",
                        ),
                    ),
                )
            }

    /**
     * 保存用户设置
     * @param userSettings 用户设置
     * @return 操作结果
     *
     * Phase 5 错误处理统一：内联safeApiCall，使用简化错误类型
     */
    override suspend fun saveUserSettings(userSettings: UserSettings): ModernResult<Unit> =
        try {
            preferencesDataStore.updateUserSettings(userSettings, userSettings.userId)
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            logger.e(e, "保存用户设置失败")
            ModernResult.Error(
                e.toModernDataError(
                    operationName = "saveUserSettings",
                    uiMessage = UiText.DynamicString("保存用户设置失败"),
                ),
            )
        }
}
