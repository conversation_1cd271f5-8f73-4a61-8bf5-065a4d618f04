package com.example.gymbro.data.network.api

import com.example.gymbro.shared.models.network.NetworkResult
import com.example.gymbro.shared.models.user.UserProfileDto
import retrofit2.http.*

/**
 * 用户资料网络API服务
 *
 * 使用SharedModels的UserProfileDto进行数据传输
 * 返回NetworkResult<T>统一错误处理模式
 * 使用相对路径避免硬编码
 */
interface UserProfileApiService {
    /**
     * 获取用户资料
     * @param userId 用户ID
     * @return NetworkResult包装的用户资料DTO
     */
    @GET("v1/users/{userId}/profile")
    suspend fun getUserProfile(
        @Path("userId") userId: String,
    ): NetworkResult<UserProfileDto>

    /**
     * 更新用户资料
     * @param userId 用户ID
     * @param profile 用户资料DTO
     * @return NetworkResult包装的更新后用户资料DTO
     */
    @PUT("v1/users/{userId}/profile")
    suspend fun updateUserProfile(
        @Path("userId") userId: String,
        @Body profile: UserProfileDto,
    ): NetworkResult<UserProfileDto>

    /**
     * 创建用户资料
     * @param profile 用户资料DTO
     * @return NetworkResult包装的创建的用户资料DTO
     */
    @POST("v1/users/profile")
    suspend fun createUserProfile(
        @Body profile: UserProfileDto,
    ): NetworkResult<UserProfileDto>

    /**
     * 批量同步用户资料
     * @param profiles 用户资料列表
     * @return NetworkResult包装的同步结果
     */
    @POST("v1/users/profiles/batch")
    suspend fun syncUserProfiles(
        @Body profiles: List<UserProfileDto>,
    ): NetworkResult<List<UserProfileDto>>
}
