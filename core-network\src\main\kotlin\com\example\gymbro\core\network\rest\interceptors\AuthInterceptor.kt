package com.example.gymbro.core.network.rest.interceptors

import okhttp3.Interceptor
import okhttp3.Response
import timber.log.Timber

/**
 * 认证拦截器
 *
 * 自动为请求添加认证头部
 */
class AuthInterceptor(
    private val apiKey: String,
) : Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()

        // 如果已经有Authorization头部，则不添加
        if (originalRequest.header("Authorization") != null) {
            return chain.proceed(originalRequest)
        }

        // 如果API密钥为空，则不添加认证头
        if (apiKey.isBlank()) {
            Timber.w("🔑 API密钥为空，跳过认证头部添加")
            return chain.proceed(originalRequest)
        }

        val authenticatedRequest = originalRequest.newBuilder()
            .addHeader("Authorization", "Bearer $apiKey")
            .build()

        Timber.v("🔑 添加认证头部: Bearer ${apiKey.take(10)}...")

        return chain.proceed(authenticatedRequest)
    }
}
