package com.example.gymbro.core.util.viewmodel

import com.example.gymbro.core.error.recovery.getRecoveryStrategy
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import timber.log.Timber

/**
 * ViewModel日志记录工具
 *
 * 提供统一的ViewModel日志记录功能，确保错误日志格式一致，
 * 方便问题排查和分析。
 */
object ViewModelLogging {
    /**
     * 记录操作开始
     *
     * @param viewModelName ViewModel名称
     * @param operation 操作名称
     * @param params 可选的操作参数
     */
    fun logOperationStart(
        viewModelName: String,
        operation: String,
        params: Map<String, Any?> = emptyMap(),
    ) {
        val paramsString = formatParams(params)
        Timber.Forest.d("[$viewModelName] 开始执行: $operation $paramsString")
    }

    /**
     * 记录操作结果
     *
     * @param viewModelName ViewModel名称
     * @param operation 操作名称
     * @param result 操作结果
     */
    fun <T> logOperationResult(
        viewModelName: String,
        operation: String,
        result: ModernResult<T>,
    ) {
        when (result) {
            is ModernResult.Success -> {
                Timber.Forest.d("[$viewModelName] 操作成功: $operation")
            }
            is ModernResult.Error -> {
                logError(viewModelName, operation, result.error)
            }
            is ModernResult.Loading -> {
                // 通常不需要记录Loading状态
            }
        }
    }

    /**
     * 记录错误
     *
     * @param viewModelName ViewModel名称
     * @param operation 操作名称
     * @param error 错误对象
     */
    fun logError(
        viewModelName: String,
        operation: String,
        error: ModernDataError,
    ) {
        val errorMessage = error.message
        val errorType = error.errorType
        val errorCategory = error.category
        val errorMeta = error.metadataMap.let { formatParams(it) }

        val causeTrace =
            error.cause
                ?.stackTraceToString()
                ?.lineSequence()
                ?.take(3)
                ?.joinToString("\n") ?: "无异常栈"

        Timber.Forest.e(
            """
            [$viewModelName] 操作失败: $operation
            | 错误类型: $errorType
            | 错误分类: $errorCategory
            | 错误消息: $errorMessage
            | 错误元数据: $errorMeta
            | 异常栈:
            $causeTrace
            """.trimIndent(),
        )
    }

    /**
     * 记录恢复尝试
     *
     * @param viewModelName ViewModel名称
     * @param operation 操作名称
     * @param error 错误对象
     */
    fun logRecoveryAttempt(
        viewModelName: String,
        operation: String,
        error: ModernDataError,
    ) {
        val recoveryStrategy = error.getRecoveryStrategy<Any>()
        Timber.Forest.d(
            """
            [$viewModelName] 尝试恢复操作: $operation
            | 错误类型: ${error.errorType}
            | 错误分类: ${error.category}
            | 恢复策略: ${recoveryStrategy?.javaClass?.simpleName ?: "无可用策略"}
            """.trimIndent(),
        )
    }

    /**
     * 记录恢复结果
     *
     * @param viewModelName ViewModel名称
     * @param operation 操作名称
     * @param success 恢复是否成功
     * @param error 错误对象，可选
     */
    fun logRecoveryResult(
        viewModelName: String,
        operation: String,
        success: Boolean,
        error: ModernDataError? = null,
    ) {
        if (success) {
            Timber.Forest.d("[$viewModelName] 恢复成功: $operation")
        } else {
            Timber.Forest.w(
                """
                [$viewModelName] 恢复失败: $operation
                | 错误类型: ${error?.errorType ?: "未知"}
                | 错误分类: ${error?.category ?: "未知"}
                """.trimIndent(),
            )
        }
    }

    /**
     * 记录状态更新
     *
     * @param viewModelName ViewModel名称
     * @param oldState 旧状态
     * @param newState 新状态
     * @param source 状态更新来源
     */
    fun logStateUpdate(
        viewModelName: String,
        oldState: Any?,
        newState: Any,
        source: String,
    ) {
        if (oldState != newState) {
            Timber.Forest.v("[$viewModelName] 状态更新 (来源: $source)")

            // 仅在调试构建中记录详细状态差异
            if (isDebugBuild()) {
                val oldStateString = oldState?.toString() ?: "null"
                val newStateString = newState.toString()

                if (oldStateString != newStateString) {
                    Timber.Forest.v("状态从:\n$oldStateString\n变为:\n$newStateString")
                }
            }
        }
    }

    /**
     * 记录用户操作
     *
     * @param viewModelName ViewModel名称
     * @param action 用户操作
     * @param params 操作参数
     */
    fun logUserAction(
        viewModelName: String,
        action: String,
        params: Map<String, Any?> = emptyMap(),
    ) {
        val paramsString = formatParams(params)
        Timber.Forest.i("[$viewModelName] 用户操作: $action $paramsString")
    }

    /**
     * 记录生命周期事件
     *
     * @param viewModelName ViewModel名称
     * @param event 生命周期事件
     */
    fun logLifecycleEvent(
        viewModelName: String,
        event: String,
    ) {
        Timber.Forest.v("[$viewModelName] 生命周期: $event")
    }

    /**
     * 格式化参数为字符串
     *
     * @param params 参数映射
     * @return 格式化后的参数字符串
     */
    private fun formatParams(params: Map<String, Any?>): String {
        if (params.isEmpty()) return ""

        return params.entries.joinToString(
            prefix = "{ ",
            postfix = " }",
            separator = ", ",
        ) { (key, value) ->
            val valueStr =
                when (value) {
                    null -> "null"
                    is String -> "\"$value\""
                    is Collection<*> -> "[size=${value.size}]"
                    is Map<*, *> -> "{size=${value.size}}"
                    is ByteArray -> "byte[${value.size}]"
                    else -> value.toString()
                }
            "$key=$valueStr"
        }
    }

    /**
     * 判断当前是否为调试构建
     */
    private fun isDebugBuild(): Boolean {
        // 实际项目中，这应该使用BuildConfig.DEBUG或其他机制
        // 由于我们无法直接访问BuildConfig，先使用返回true替代
        return true
    }

    /**
     * 为ViewModel提供的简化日志记录扩展函数
     */

    /**
     * 记录操作开始
     */
    fun <S : BaseUiState> BaseViewModel<S>.logOperationStart(
        operation: String,
        params: Map<String, Any?> = emptyMap(),
    ) {
        logOperationStart(this::class.java.simpleName, operation, params)
    }

    /**
     * 记录操作结果
     */
    fun <S : BaseUiState, T> BaseViewModel<S>.logOperationResult(
        operation: String,
        result: ModernResult<T>,
    ) {
        logOperationResult(this::class.java.simpleName, operation, result)
    }

    /**
     * 记录错误
     */
    fun <S : BaseUiState> BaseViewModel<S>.logError(
        operation: String,
        error: ModernDataError,
    ) {
        logError(this::class.java.simpleName, operation, error)
    }

    /**
     * 记录恢复尝试
     */
    fun <S : BaseUiState> BaseViewModel<S>.logRecoveryAttempt(
        operation: String,
        error: ModernDataError,
    ) {
        logRecoveryAttempt(this::class.java.simpleName, operation, error)
    }

    /**
     * 记录恢复结果
     */
    fun <S : BaseUiState> BaseViewModel<S>.logRecoveryResult(
        operation: String,
        success: Boolean,
        error: ModernDataError? = null,
    ) {
        logRecoveryResult(this::class.java.simpleName, operation, success, error)
    }

    /**
     * 记录状态更新
     */
    fun <S : BaseUiState> BaseViewModel<S>.logStateUpdate(
        oldState: Any?,
        newState: Any,
        source: String,
    ) {
        logStateUpdate(this::class.java.simpleName, oldState, newState, source)
    }

    /**
     * 记录用户操作
     */
    fun <S : BaseUiState> BaseViewModel<S>.logUserAction(
        action: String,
        params: Map<String, Any?> = emptyMap(),
    ) {
        logUserAction(this::class.java.simpleName, action, params)
    }
}
