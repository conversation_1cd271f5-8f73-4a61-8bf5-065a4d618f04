package com.example.gymbro.core.logging

/**
 * 敏感信息过滤器
 *
 * 用于过滤日志中的敏感信息，防止隐私泄露
 * 支持邮箱、手机号、密码、令牌等敏感信息的过滤
 */
object SensitiveDataFilter {

    // 邮箱地址正则表达式
    private val EMAIL_PATTERN = Regex(
        "[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}",
        RegexOption.IGNORE_CASE,
    )

    // 手机号正则表达式（支持国际格式）
    private val PHONE_PATTERN = Regex(
        "\\+?[1-9]\\d{1,14}",
        RegexOption.IGNORE_CASE,
    )

    // 中国手机号正则表达式
    private val CHINA_PHONE_PATTERN = Regex(
        "1[3-9]\\d{9}",
        RegexOption.IGNORE_CASE,
    )

    // 密码相关正则表达式
    private val PASSWORD_PATTERNS = arrayOf(
        Regex("password[=:]\\s*[^\\s,;]+", RegexOption.IGNORE_CASE),
        Regex("pwd[=:]\\s*[^\\s,;]+", RegexOption.IGNORE_CASE),
        Regex("passwd[=:]\\s*[^\\s,;]+", RegexOption.IGNORE_CASE),
    )

    // 令牌相关正则表达式
    private val TOKEN_PATTERNS = arrayOf(
        Regex("token[=:]\\s*[^\\s,;]+", RegexOption.IGNORE_CASE),
        Regex("accessToken[=:]\\s*[^\\s,;]+", RegexOption.IGNORE_CASE),
        Regex("refreshToken[=:]\\s*[^\\s,;]+", RegexOption.IGNORE_CASE),
        Regex("idToken[=:]\\s*[^\\s,;]+", RegexOption.IGNORE_CASE),
        Regex("bearer\\s+[a-zA-Z0-9._-]+", RegexOption.IGNORE_CASE),
    )

    // 密钥相关正则表达式
    private val KEY_PATTERNS = arrayOf(
        Regex("key[=:]\\s*[^\\s,;]+", RegexOption.IGNORE_CASE),
        Regex("apiKey[=:]\\s*[^\\s,;]+", RegexOption.IGNORE_CASE),
        Regex("secretKey[=:]\\s*[^\\s,;]+", RegexOption.IGNORE_CASE),
        Regex("privateKey[=:]\\s*[^\\s,;]+", RegexOption.IGNORE_CASE),
    )

    // 秘密相关正则表达式
    private val SECRET_PATTERNS = arrayOf(
        Regex("secret[=:]\\s*[^\\s,;]+", RegexOption.IGNORE_CASE),
        Regex("clientSecret[=:]\\s*[^\\s,;]+", RegexOption.IGNORE_CASE),
    )

    // 信用卡号正则表达式
    private val CREDIT_CARD_PATTERN = Regex(
        "\\b(?:\\d{4}[\\s-]?){3}\\d{4}\\b",
        RegexOption.IGNORE_CASE,
    )

    // 身份证号正则表达式（中国）
    private val ID_CARD_PATTERN = Regex(
        "\\b[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx]\\b",
        RegexOption.IGNORE_CASE,
    )

    /**
     * 过滤消息中的敏感信息
     *
     * @param message 原始消息
     * @return 过滤后的消息
     */
    fun filterSensitiveData(message: String): String {
        var filteredMessage = message

        // 过滤邮箱地址
        filteredMessage = filteredMessage.replace(EMAIL_PATTERN, "***@***.***")

        // 过滤手机号
        filteredMessage = filteredMessage.replace(PHONE_PATTERN, "***-***-****")
        filteredMessage = filteredMessage.replace(CHINA_PHONE_PATTERN, "***-****-****")

        // 过滤密码
        PASSWORD_PATTERNS.forEach { pattern ->
            filteredMessage = filteredMessage.replace(pattern) { matchResult ->
                val prefix = matchResult.value.substringBefore('=', matchResult.value.substringBefore(':'))
                when {
                    '=' in matchResult.value -> "$prefix=***"
                    ':' in matchResult.value -> "$prefix:***"
                    else -> "password=***"
                }
            }
        }

        // 过滤令牌
        TOKEN_PATTERNS.forEach { pattern ->
            filteredMessage = filteredMessage.replace(pattern) { matchResult ->
                val prefix = matchResult.value.substringBefore('=', matchResult.value.substringBefore(':'))
                when {
                    '=' in matchResult.value -> "$prefix=***"
                    ':' in matchResult.value -> "$prefix:***"
                    matchResult.value.startsWith("bearer", ignoreCase = true) -> "Bearer ***"
                    else -> "token=***"
                }
            }
        }

        // 过滤密钥
        KEY_PATTERNS.forEach { pattern ->
            filteredMessage = filteredMessage.replace(pattern) { matchResult ->
                val prefix = matchResult.value.substringBefore('=', matchResult.value.substringBefore(':'))
                when {
                    '=' in matchResult.value -> "$prefix=***"
                    ':' in matchResult.value -> "$prefix:***"
                    else -> "key=***"
                }
            }
        }

        // 过滤秘密
        SECRET_PATTERNS.forEach { pattern ->
            filteredMessage = filteredMessage.replace(pattern) { matchResult ->
                val prefix = matchResult.value.substringBefore('=', matchResult.value.substringBefore(':'))
                when {
                    '=' in matchResult.value -> "$prefix=***"
                    ':' in matchResult.value -> "$prefix:***"
                    else -> "secret=***"
                }
            }
        }

        // 过滤信用卡号
        filteredMessage = filteredMessage.replace(CREDIT_CARD_PATTERN, "****-****-****-****")

        // 过滤身份证号
        filteredMessage = filteredMessage.replace(ID_CARD_PATTERN, "******************")

        return filteredMessage
    }

    /**
     * 检查消息是否包含敏感信息
     *
     * @param message 要检查的消息
     * @return 如果包含敏感信息返回true，否则返回false
     */
    fun containsSensitiveData(message: String): Boolean {
        return EMAIL_PATTERN.containsMatchIn(message) ||
            PHONE_PATTERN.containsMatchIn(message) ||
            CHINA_PHONE_PATTERN.containsMatchIn(message) ||
            PASSWORD_PATTERNS.any { it.containsMatchIn(message) } ||
            TOKEN_PATTERNS.any { it.containsMatchIn(message) } ||
            KEY_PATTERNS.any { it.containsMatchIn(message) } ||
            SECRET_PATTERNS.any { it.containsMatchIn(message) } ||
            CREDIT_CARD_PATTERN.containsMatchIn(message) ||
            ID_CARD_PATTERN.containsMatchIn(message)
    }

    /**
     * 获取敏感信息类型列表
     *
     * @param message 要检查的消息
     * @return 包含的敏感信息类型列表
     */
    fun getSensitiveDataTypes(message: String): List<String> {
        val types = mutableListOf<String>()

        if (EMAIL_PATTERN.containsMatchIn(message)) types.add("邮箱")
        if (PHONE_PATTERN.containsMatchIn(message) || CHINA_PHONE_PATTERN.containsMatchIn(message)) {
            types.add(
                "手机号",
            )
        }
        if (PASSWORD_PATTERNS.any { it.containsMatchIn(message) }) types.add("密码")
        if (TOKEN_PATTERNS.any { it.containsMatchIn(message) }) types.add("令牌")
        if (KEY_PATTERNS.any { it.containsMatchIn(message) }) types.add("密钥")
        if (SECRET_PATTERNS.any { it.containsMatchIn(message) }) types.add("秘密")
        if (CREDIT_CARD_PATTERN.containsMatchIn(message)) types.add("信用卡")
        if (ID_CARD_PATTERN.containsMatchIn(message)) types.add("身份证")

        return types
    }
}
