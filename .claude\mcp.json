{"mcpServers": {"memory-bank": {"command": "npx", "args": ["-y", "@alioshr/memory-bank-mcp"], "timeout": 600}, "mcp-feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced@latest"], "timeout": 600, "env": {"MCP_DEBUG": "false", "MCP_WEB_PORT": "9999"}, "autoApprove": ["interactive_feedback"]}, "serena": {"command": "uvx", "args": ["--from", "git+https://github.com/oraios/serena", "serena-mcp-server", "--project", "D:\\GymBro\\GymBro", "--context", "ide-assistant", "--mode", "interactive", "--mode", "editing", "--enable-web-dashboard", "true"]}, "code-reasoning": {"command": "cmd", "args": ["/c", "npx", "-y", "@mettamatt/code-reasoning"]}, "context7": {"command": "cmd", "args": ["/c", "npx", "-y", "@upstash/context7-mcp@latest"]}}}