# 状态机熔断器配置文件
# Circuit Breaker Configuration for State Machine

# 核心指标阈值配置
metrics_thresholds:
  # P95 thinking状态持续时间
  thinking_duration:
    target: "2s"          # 目标值：≤2秒
    warning: "3s"         # 警告阈值：>3秒
    critical: "4s"        # 熔断阈值：>4秒持续5分钟
    duration: "5m"        # 持续时间
    action: "auto_fallback"  # 自动降级：关闭重试，转同步响应

  # AI消息数量监控
  ai_message_count:
    target: 1             # 目标值：=1
    warning: 1            # 警告阈值：=1 (任何偏离都是异常)
    critical: 1           # 熔断阈值：>1持续10秒
    duration: "10s"       # 持续时间
    action: "immediate_rollback"  # 立即回滚

  # 重试成功率监控
  retry_success_rate:
    target: "95%"         # 目标值：≥95%
    warning: "90%"        # 警告阈值：<90%
    critical: "80%"       # 熔断阈值：<80%持续1小时
    duration: "1h"        # 持续时间
    action: "switch_backup_node"  # 自动切换备用节点

# 自动化动作配置
automation_actions:
  auto_fallback:
    description: "自动降级处理"
    steps:
      - action: "disable_retry_mechanism"
        timeout: "30s"
      - action: "switch_to_sync_response"
        timeout: "30s"
      - action: "notify_oncall_team"
        template: "thinking_duration_fallback"

  immediate_rollback:
    description: "立即回滚到上一个稳定版本"
    steps:
      - action: "trigger_rollback"
        target: "previous_stable_version"
        timeout: "60s"
      - action: "preserve_user_sessions"
        timeout: "30s"
      - action: "emergency_alert"
        channels: ["slack", "pagerduty", "email"]
        severity: "critical"

  switch_backup_node:
    description: "切换到备用节点"
    steps:
      - action: "health_check_backup_nodes"
        timeout: "10s"
      - action: "gradual_traffic_shift"
        percentage: "100%"
        duration: "5m"
      - action: "monitor_backup_performance"
        duration: "15m"

# 恢复策略配置
recovery_strategies:
  thinking_duration_recovery:
    conditions:
      - "thinking_duration < 2s for 10m"
      - "ai_message_count == 1 for 10m"
      - "no_user_complaints for 15m"
    actions:
      - "gradual_re_enable_retry"
      - "monitor_closely for 30m"

  message_count_recovery:
    conditions:
      - "ai_message_count == 1 for 5m"
      - "state_machine_consistency_check passed"
      - "shadow_traffic_diff < 1% for 10m"
    actions:
      - "cancel_rollback_if_in_progress"
      - "resume_normal_operation"

  retry_rate_recovery:
    conditions:
      - "retry_success_rate > 90% for 30m"
      - "primary_node_health restored"
    actions:
      - "gradual_traffic_shift_back"
      - "performance_comparison_report"

# 监控集成配置
monitoring_integration:
  grafana:
    dashboard_url: "http://grafana.gymbro.com/d/state-machine/state-machine-monitoring"
    api_key: "${GRAFANA_API_KEY}"
    alert_rules:
      - name: "thinking_duration_exceeded"
        query: "avg_over_time(thinking_state_duration[5m]) > 4"
        threshold: 4
        duration: "5m"

      - name: "ai_message_count_anomaly"
        query: "max(ai_message_count) > 1"
        threshold: 1
        duration: "10s"

      - name: "retry_success_rate_low"
        query: "(successful_retries / total_retries) < 0.8"
        threshold: 0.8
        duration: "1h"

  prometheus:
    endpoint: "http://prometheus.gymbro.com:9090"
    metrics:
      - "gymbro_ai_message_count"
      - "gymbro_thinking_duration_seconds"
      - "gymbro_retry_success_rate"
      - "gymbro_state_transition_errors_total"

  alertmanager:
    webhook_url: "http://alertmanager.gymbro.com:9093/api/v1/alerts"
    routing:
      - match:
          severity: "critical"
        receiver: "pagerduty-critical"
      - match:
          severity: "warning"
        receiver: "slack-alerts"

# 测试和验证配置
testing_config:
  chaos_engineering:
    enabled: true
    scenarios:
      - name: "network_partition"
        probability: 0.1  # 10%概率
        duration: "30s"

      - name: "high_latency"
        probability: 0.2  # 20%概率
        latency: "3s"
        duration: "60s"

      - name: "memory_pressure"
        probability: 0.05  # 5%概率
        memory_limit: "80%"
        duration: "120s"

  load_testing:
    concurrent_users: 100
    duration: "10m"
    ramp_up: "2m"
    scenarios:
      - "normal_conversation_flow"
      - "rapid_message_sending"
      - "connection_interruption"

  mutation_testing:
    enabled: true
    coverage_threshold: 80  # 80%覆盖率要求
    mutation_operators:
      - "remove_synchronization"
      - "change_state_transitions"
      - "modify_retry_logic"

# 环境特定配置
environments:
  production:
    thinking_duration:
      target: "1.5s"
      critical: "3s"
    ai_message_count:
      duration: "5s"  # 生产环境更严格
    retry_success_rate:
      target: "98%"
      critical: "85%"

  staging:
    thinking_duration:
      target: "2s"
      critical: "5s"
    ai_message_count:
      duration: "15s"  # 测试环境宽松一些
    retry_success_rate:
      target: "90%"
      critical: "70%"

  development:
    thinking_duration:
      target: "5s"
      critical: "10s"
    ai_message_count:
      duration: "30s"
    retry_success_rate:
      target: "80%"
      critical: "60%"

# 报告和审计配置
reporting:
  daily_health_report:
    enabled: true
    recipients: ["<EMAIL>", "<EMAIL>"]
    metrics_included:
      - "thinking_duration_p95"
      - "ai_message_count_violations"
      - "retry_success_rate_average"
      - "circuit_breaker_activations"

  incident_tracking:
    enabled: true
    integration: "jira"
    auto_create_tickets: true
    severity_mapping:
      critical: "P1"
      warning: "P3"

  performance_trends:
    enabled: true
    retention_period: "90d"
    trend_analysis:
      - "weekly_performance_degradation"
      - "monthly_reliability_score"
      - "quarterly_capacity_planning"
