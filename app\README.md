# App Module (应用主模块)

> **版本**: v2.0 - MVI 架构重构
> **状态**: ✅ 生产就绪
> **最后更新**: 2025-06-26
> **UI设计标准**: GymBro Design System

## 📖 概述 (Overview)

`app` 模块是整个GymBro应用的入口和组装中心。它不包含任何具体的业务功能，其核心职责是初始化应用所需的全局服务、配置主题、管理页面导航，并将所有独立的`feature`模块集成在一起，形成一个完整的、可运行的应用。

- **核心职责**: 应用的初始化、组装和导航管理。
- **核心功能**:
    - **全局初始化**: 在`GymBroApp`中初始化Firebase, Timber, WorkManager等全局服务。
    - **UI入口**: `MainActivity`作为应用的唯一Activity，承载所有UI和Compose内容。
    - **导航组装**: 在`MainActivity`中配置`NavHost`，将所有`feature`模块的导航图集成在一起。
    - **启动流程**: 管理应用的启动过程，包括显示`LoadingScreen`和进行地区检测。

## 🏗️ 架构设计 (Architecture)

`app`模块作为顶层模块，负责将所有其他模块（`core`, `data`, `domain`, `features`）组装在一起。它通过依赖注入（Hilt）获取各个模块提供的服务和功能，并在`MainActivity`中将它们连接起来。

- **数据流**: `app`模块不直接处理业务数据，而是通过导航将用户引导到相应的`feature`模块，由`feature`模块负责处理数据。
- **模块依赖**: `app`模块依赖于项目中几乎所有的其他模块，以实现功能的组装。

## 🎨 UI设计标准

- **核心设计原则**: 
    - **单一Activity**: 整个应用采用单Activity架构，所有页面都由Compose实现。
    - **动态主题**: 通过`ThemeManager`实现动态主题切换，支持日间/夜间模式。
    - **统一导航**: 使用`Jetpack Navigation Compose`实现统一的页面导航和转场动画。

## 🔧 核心接口 (Core Interfaces)

| 接口/类                  | 职责                                       |
| ------------------------ | ------------------------------------------ |
| `GymBroApp`              | `Application`类，负责应用的全局初始化。      |
| `MainActivity`           | 应用的唯一Activity，负责UI和导航的组装。     |
| `LoadingScreen`          | 应用启动时的加载屏幕。                     |
| `RegionDetectionManager` | 负责检测用户所在的地区。                   |

## 📦 技术栈 (Tech Stack)

- **核心依赖**: 
    - **Hilt**: 用于依赖注入。
    - **Jetpack Compose**: 用于构建UI。
    - **Jetpack Navigation Compose**: 用于页面导航。
    - **Kotlin Coroutines**: 用于异步处理。
- **GymBro模块依赖**: `core`, `core-ml`, `domain`, `data`, `di`, `designSystem`, `navigation`, 以及所有的`features`模块。

## 📁 模块结构

```
app
└── src
    ├── main: 模块的主要源代码
    │   ├── app: 应用相关的辅助类，如di, error, extensions等。
    │   ├── di: Hilt依赖注入模块。
    │   ├── network: 网络状态监控。
    │   ├── service: 后台服务管理。
    │   ├── GymBroApp.kt: Application类。
    │   └── MainActivity.kt: 主Activity。
    └── test: 单元测试和集成测试。
```

## 🧪 测试策略

- **测试覆盖率目标**: `app`模块的核心逻辑（如`LoadingViewModel`）的覆盖率目标为70%。
- **已实现测试**: 
    - `GymBroAppTest`: 对`Application`类的初始化逻辑进行测试。
- **测试工具**: JUnit, MockK, Turbine

## 📚 使用示例

`app`模块是应用的入口，不能被其他模块作为库使用。要运行整个应用，只需在Android Studio中选择`app`模块并点击运行即可。

## 🎯 质量标准

- **启动性能**: 应用的启动时间（冷启动和热启动）应尽可能短，避免白屏。
- **稳定性**: 应用应具有高稳定性，避免ANR和崩溃。
- **兼容性**: 应用应在目标API级别的设备上正常运行。

## 🔄 版本历史

- **v2.0**: MVI 架构重构，优化了启动流程和主题管理。
- **v1.5**: 引入了地区检测功能，支持分区域的内容展示。
- **v1.0**: 初始版本，实现了基本的应用框架和导航。

## 📞 支持与维护

- **文档资源**: `TREE.md`, `INTERFACES.md`
- **开发团队**: GymBro Android Team