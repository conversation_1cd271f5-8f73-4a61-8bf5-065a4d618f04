package com.example.gymbro.data.remote.firebase.auth

// import com.example.gymbro.designSystem.R // 暂时注释掉，使用硬编码的client ID
import android.content.Context
import androidx.credentials.CredentialManager
import androidx.credentials.GetCredentialRequest
import androidx.credentials.GetCredentialResponse
import androidx.credentials.exceptions.GetCredentialException
import com.example.gymbro.core.error.types.ErrorCategory
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.google.android.libraries.identity.googleid.GetGoogleIdOption
import com.google.android.libraries.identity.googleid.GoogleIdTokenCredential
import com.google.android.libraries.identity.googleid.GoogleIdTokenParsingException
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.GoogleAuthProvider
import kotlinx.coroutines.tasks.await
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 现代化Google登录服务
 * 使用Credential Manager API - Android原生推荐方式
 */
@Singleton
class GoogleSignInService
@Inject
constructor(
    private val firebaseAuth: FirebaseAuth,
    private val credentialManager: CredentialManager,
) {
    /**
     * 开始Google登录流程
     */
    suspend fun signInWithGoogle(context: Context): ModernResult<String> =
        try {
            Timber.d("开始Google登录流程")

            // 1. 配置Google ID选项
            // TODO: 从配置文件或环境变量获取client ID
            val serverClientId = "**********-lqdp6rv5q5q54e15ticej61uf86up2vi.apps.googleusercontent.com"
            val googleIdOption =
                GetGoogleIdOption
                    .Builder()
                    .setServerClientId(serverClientId)
                    .setFilterByAuthorizedAccounts(false) // 显示所有Google账户
                    .setAutoSelectEnabled(true)
                    .build()

            // 2. 创建凭证请求
            val request =
                GetCredentialRequest
                    .Builder()
                    .addCredentialOption(googleIdOption)
                    .build()

            // 3. 获取凭证
            val result =
                credentialManager.getCredential(
                    request = request,
                    context = context,
                )

            // 4. 处理Google ID Token
            handleGoogleCredential(result)
        } catch (e: GetCredentialException) {
            Timber.e(e, "Google登录凭证获取失败")
            ModernResult.Error(
                ModernDataError(
                    operationName = "Google登录",
                    errorType = GlobalErrorType.Auth.General,
                    category = ErrorCategory.AUTH,
                    uiMessage = UiText.DynamicString("Google登录失败: ${e.message}"),
                    cause = e,
                ),
            )
        } catch (e: Exception) {
            Timber.e(e, "Google登录过程异常")
            ModernResult.Error(
                ModernDataError(
                    operationName = "Google登录",
                    errorType = GlobalErrorType.System.Internal,
                    category = ErrorCategory.SYSTEM,
                    uiMessage = UiText.DynamicString("登录过程发生异常"),
                    cause = e,
                ),
            )
        }

    /**
     * 处理Google凭证响应
     */
    private suspend fun handleGoogleCredential(result: GetCredentialResponse): ModernResult<String> =
        try {
            // 提取Google ID Token
            val credential =
                GoogleIdTokenCredential
                    .createFrom(result.credential.data)

            val idToken = credential.idToken
            Timber.d("获取到Google ID Token，准备Firebase认证")

            // 使用Firebase进行最终认证
            val firebaseCredential = GoogleAuthProvider.getCredential(idToken, null)
            val authResult = firebaseAuth.signInWithCredential(firebaseCredential).await()

            val user = authResult.user
            if (user != null) {
                Timber.d("Firebase Google登录成功: ${user.email}")
                ModernResult.Success(user.uid)
            } else {
                Timber.w("Firebase认证成功但用户为空")
                ModernResult.Error(
                    ModernDataError(
                        operationName = "Firebase认证",
                        errorType = GlobalErrorType.Auth.General,
                        category = ErrorCategory.AUTH,
                        uiMessage = UiText.DynamicString("认证成功但获取用户信息失败"),
                    ),
                )
            }
        } catch (e: GoogleIdTokenParsingException) {
            Timber.e(e, "Google ID Token解析失败")
            ModernResult.Error(
                ModernDataError(
                    operationName = "Google Token解析",
                    errorType = GlobalErrorType.Auth.General,
                    category = ErrorCategory.AUTH,
                    uiMessage = UiText.DynamicString("Google认证令牌解析失败"),
                    cause = e,
                ),
            )
        } catch (e: Exception) {
            Timber.e(e, "Firebase Google认证失败")
            ModernResult.Error(
                ModernDataError(
                    operationName = "Firebase Google认证",
                    errorType = GlobalErrorType.Auth.General,
                    category = ErrorCategory.AUTH,
                    uiMessage = UiText.DynamicString("Firebase认证失败: ${e.message}"),
                    cause = e,
                ),
            )
        }

    /**
     * Google登出
     */
    fun signOut(): ModernResult<Unit> =
        try {
            firebaseAuth.signOut()
            Timber.d("Google登出成功")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "Google登出失败")
            ModernResult.Error(
                ModernDataError(
                    operationName = "Google登出",
                    errorType = GlobalErrorType.Auth.General,
                    category = ErrorCategory.AUTH,
                    uiMessage = UiText.DynamicString("登出失败"),
                    cause = e,
                ),
            )
        }
}
