package com.example.gymbro.data.local.dao.user

import androidx.room.*
import com.example.gymbro.data.local.entity.user.UserCacheEntity
import kotlinx.coroutines.flow.Flow

/**
 * 用户缓存实体数据访问对象
 * 提供对UserCacheEntity的CRUD操作
 */
@Dao
interface UserCacheEntityDao {
    /**
     * 根据用户ID获取用户缓存
     * @param userId 用户ID
     * @return 用户缓存实体，不存在则返回null
     */
    @Query("SELECT * FROM users WHERE user_id = :userId")
    suspend fun getUserCacheById(userId: String): UserCacheEntity?

    /**
     * 根据用户ID获取用户缓存Flow
     * @param userId 用户ID
     * @return 发射用户缓存实体的Flow，不存在则发射null
     */
    @Query("SELECT * FROM users WHERE user_id = :userId")
    fun observeUserCacheById(userId: String): Flow<UserCacheEntity?>

    /**
     * 插入或更新用户缓存
     * @param userCache 用户缓存实体
     * @return 插入的行ID
     */
    @Upsert
    suspend fun upsertUserCache(userCache: UserCacheEntity): Long

    /**
     * 插入用户缓存
     * @param userCache 用户缓存实体
     * @return 插入的行ID
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUserCache(userCache: UserCacheEntity): Long

    /**
     * 更新用户缓存
     * @param userCache 用户缓存实体
     * @return 更新的行数
     */
    @Update
    suspend fun updateUserCache(userCache: UserCacheEntity): Int

    /**
     * 删除用户缓存
     * @param userCache 用户缓存实体
     * @return 删除的行数
     */
    @Delete
    suspend fun deleteUserCache(userCache: UserCacheEntity): Int

    /**
     * 根据用户ID删除用户缓存
     * @param userId 用户ID
     * @return 删除的行数
     */
    @Query("DELETE FROM users WHERE user_id = :userId")
    suspend fun deleteUserCacheById(userId: String): Int

    /**
     * 获取所有用户缓存(仅管理员功能)
     * @return 所有用户缓存实体列表
     * @hide 仅供系统/管理员使用，需要在Repository层添加权限检查
     */
    @Query("SELECT * FROM users")
    suspend fun getAllUserCaches(): List<UserCacheEntity>

    /**
     * 观察所有用户缓存(仅管理员功能)
     * @return 发射所有用户缓存实体列表的Flow
     * @hide 仅供系统/管理员使用，需要在Repository层添加权限检查
     */
    @Query("SELECT * FROM users")
    fun observeAllUserCaches(): Flow<List<UserCacheEntity>>

    /**
     * 更新用户设置
     * @param userId 用户ID
     * @param themeMode 主题模式
     * @param languageCode 语言代码
     * @param measurementSystem 计量单位系统
     * @param notificationsEnabled 通知开关
     * @param soundsEnabled 声音开关
     * @param locationSharingEnabled 位置共享开关
     * @param settingsJson 序列化的设置JSON
     * @return 更新的行数
     */
    @Query(
        "UPDATE users SET themeMode = :themeMode, languageCode = :languageCode, " +
            "measurementSystem = :measurementSystem, notificationsEnabled = :notificationsEnabled, " +
            "soundsEnabled = :soundsEnabled, locationSharingEnabled = :locationSharingEnabled, " +
            "settingsJson = :settingsJson, lastModified = :lastModified, isSynced = 0 " +
            "WHERE user_id = :userId",
    )
    suspend fun updateUserSettings(
        userId: String,
        themeMode: String,
        languageCode: String,
        measurementSystem: String,
        notificationsEnabled: Boolean,
        soundsEnabled: Boolean,
        locationSharingEnabled: Boolean,
        settingsJson: String,
        lastModified: Long = System.currentTimeMillis(),
    ): Int

    // TODO: 在重制workout模块后重新添加incrementWorkoutCount方法
    // /**
    //  * 增加用户的训练计数
    //  * @param userId 用户ID
    //  * @param count 增加的数量
    //  * @return 更新的行数
    //  */
    // @Query("UPDATE users SET totalWorkoutCount = totalWorkoutCount + :count, lastModified = :lastModified, isSynced = 0 WHERE user_id = :userId")
    // suspend fun incrementWorkoutCount(userId: String, count: Int = 1, lastModified: Long = System.currentTimeMillis()): Int
}
