package com.example.gymbro.data.local.entity.user

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * 用户缓存实体
 * 存储用户的基本数据，与UserSettingsEntity形成一对一关系
 * 个人资料设置已移至UserSettingsEntity
 */
@Entity(
    tableName = "users", // 保持与原来的UserEntity表名一致
    indices = [
        Index(value = ["email"], name = "index_users_email"),
        Index(value = ["isActive"], name = "index_users_isActive"),
        Index(value = ["userType"], name = "index_users_userType"),
        Index(value = ["lastLoginAt"], name = "index_users_lastLoginAt"),
        Index(value = ["isSynced"], name = "index_users_synced"),
    ],
)
data class UserCacheEntity(
    @PrimaryKey
    @ColumnInfo(name = "user_id") // 保持与原来的UserEntity主键列名一致
    val userId: String,

    // --- 基本用户信息 (原UserEntity字段) ---
    val email: String?,
    val username: String,
    val displayName: String?,
    val photoUrl: String?,
    val phoneNumber: String?,
    val isActive: Boolean = true,
    val isEmailVerified: Boolean = false,

    // --- 用户资料字段 (整合UserProfileEntity字段) ---
    val wechatId: String? = null,
    val anonymousId: String? = null,
    val gender: String? = null, // "male", "female", "neutral"
    val weight: Float? = null,
    val weightUnit: String? = null, // "kg", "lb"
    val fitnessLevel: Int? = null, // 0-4
    val preferredGym: String? = null,
    val avatar: String? = null, // 头像URL (可能与photoUrl冗余，保留两者以兼容)
    val bio: String? = null,

    // --- 用户设置字段 ---
    val themeMode: String = "system", // "light", "dark", "system"
    val languageCode: String = "zh-CN",
    val measurementSystem: String = "metric", // "metric", "imperial"
    val notificationsEnabled: Boolean = true,
    val soundsEnabled: Boolean = true,
    val locationSharingEnabled: Boolean = false,

    // --- JSON存储字段 ---
    val settingsJson: String? = null, // 序列化的设置JSON
    val fitnessGoalsJson: String? = null, // 序列化的健身目标JSON
    val privacySettingsJson: String? = null, // 序列化的隐私设置JSON
    val workoutDaysJson: String? = null, // 序列化的训练日JSON
    val preferredWorkoutTimesJson: String? = null, // 序列化的偏好锻炼时间JSON
    val preferredFoodsJson: String? = null, // 序列化的偏好食物JSON
    val notificationSettingsJson: String? = null, // 序列化的通知设置JSON
    val soundSettingsJson: String? = null, // 序列化的声音设置JSON
    val backupSettingsJson: String? = null, // 序列化的备份设置JSON
    val partnerMatchPreferencesJson: String? = null, // 序列化的配对偏好JSON
    val blockedUsersJson: String? = null, // 序列化的屏蔽用户JSON

    // --- 用户设置字段 (补充) ---
    val allowPartnerMatching: Boolean = false,

    // --- 统计信息 ---
    // TODO: 在重制workout模块后重新添加totalWorkoutCount
    val weeklyActiveMinutes: Int = 0,
    val likesReceived: Int = 0,

    // --- 时间戳字段 (使用Long存储毫秒时间戳) ---
    val createdAt: Long? = null, // 创建时间的时间戳(毫秒)
    val lastLoginAt: Long? = null, // 最后登录时间的时间戳(毫秒)

    // --- 同步信息 ---
    val isSynced: Boolean = false,
    val lastSynced: Long = 0,
    val lastModified: Long = System.currentTimeMillis(), // 本地最后修改时间
    val serverUpdatedAt: Long = 0, // 服务器更新时间
    val isAnonymous: Boolean = false,

    // --- 用户类型和订阅信息 ---
    val userType: String = if (isAnonymous) "anonymous" else "registered", // "anonymous", "registered", "subscribed"
    val subscriptionPlan: String = "none", // "none", "free_trial", "china_plan", "global_plan"
    val subscriptionExpiryDate: Long? = null, // 订阅过期时间
) {
    companion object {

        // 用户类型常量
        const val USER_TYPE_ANONYMOUS = "anonymous"
        const val USER_TYPE_REGISTERED = "registered"

        // 订阅计划常量
        const val SUBSCRIPTION_PLAN_NONE = "none"
        const val SUBSCRIPTION_PLAN_FREE_TRIAL = "free_trial"
        const val SUBSCRIPTION_PLAN_GLOBAL = "global_plan"
    }
}
