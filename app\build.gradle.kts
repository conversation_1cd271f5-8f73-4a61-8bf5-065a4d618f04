import java.io.FileInputStream
import java.util.*

plugins {
    id("gymbro.android.application")
    id("gymbro.compose.library")
    id("gymbro.testing.library")
    id("gymbro.jacoco")
    id("org.jetbrains.kotlin.plugin.compose")
    id("dagger.hilt.android.plugin")
    alias(libs.plugins.google.services) // 使用版本目录中的Google Services插件
}

// 读取local.properties中的API密钥
val localProperties = Properties()
val localPropertiesFile = rootProject.file("local.properties")
if (localPropertiesFile.exists()) {
    localProperties.load(FileInputStream(localPropertiesFile))
}

android {
    namespace = "com.example.gymbro"
    compileSdk =
        libs.versions.compileSdk
            .get()
            .toInt()

    defaultConfig {
        applicationId = "com.example.gymbro"
        minSdk =
            libs.versions.minSdk
                .get()
                .toInt()
        targetSdk =
            libs.versions.targetSdk
                .get()
                .toInt()
        versionCode = 1 // Keep app specific
        versionName = "1.0" // Keep app specific

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

        // 🚀 性能优化配置已移至build-logic/PerformanceOptimizationPlugin
    }

    // 签名配置
    signingConfigs {
        create("release") {
            // 从环境变量或local.properties读取签名信息
            val keystoreFile =
                System.getenv("KEYSTORE_FILE")
                    ?: localProperties.getProperty("KEYSTORE_FILE")
            val keystorePassword =
                System.getenv("KEYSTORE_PASSWORD")
                    ?: localProperties.getProperty("KEYSTORE_PASSWORD")
            val keyAlias =
                System.getenv("KEY_ALIAS")
                    ?: localProperties.getProperty("KEY_ALIAS")
            val keyPassword =
                System.getenv("KEY_PASSWORD")
                    ?: localProperties.getProperty("KEY_PASSWORD")

            if (keystoreFile != null && File(keystoreFile).exists()) {
                storeFile = File(keystoreFile)
                storePassword = keystorePassword
                this.keyAlias = keyAlias
                this.keyPassword = keyPassword
            }
        }
    }

    buildTypes {
        debug {
            // 🚀 性能优化配置已移至build-logic/PerformanceOptimizationPlugin

            // 示例：定义调试环境的API URL
            buildConfigField("String", "API_BASE_URL", "\"https://debug.api.gymbro.com\"")
            // 示例：定义日志级别
            buildConfigField("Boolean", "ENABLE_LOGGING", "true")

            // AI Coach API配置 - 全部从local.properties读取，无硬编码默认值
            buildConfigField("String", "GOOGLE_API_KEY", "\"${localProperties.getProperty("GOOGLE_API_KEY", "")}\"")
            buildConfigField("String", "GOOGLE_BASE_URL", "\"${localProperties.getProperty("GOOGLE_BASE_URL", "")}\"")
            buildConfigField("String", "OPENAI_API_KEY", "\"${localProperties.getProperty("OPENAI_API_KEY", "")}\"")
            buildConfigField("String", "OPENAI_BASE_URL", "\"${localProperties.getProperty("OPENAI_BASE_URL", "")}\"")
            buildConfigField("String", "DEEPSEEK_API_KEY", "\"${localProperties.getProperty("DEEPSEEK_API_KEY", "")}\"")
            buildConfigField("String", "DEEPSEEK_BASE_URL", "\"${localProperties.getProperty("DEEPSEEK_BASE_URL", "")}\"")

            // 模型配置 - 全部从local.properties读取，无硬编码默认值
            buildConfigField("String", "DEFAULT_GOOGLE_MODEL", "\"${localProperties.getProperty("DEFAULT_GOOGLE_MODEL", "")}\"")
            buildConfigField("String", "DEFAULT_DEEPSEEK_MODEL", "\"${localProperties.getProperty("DEFAULT_DEEPSEEK_MODEL", "")}\"")
            buildConfigField("String", "OPENAI_DEFAULT_MODEL", "\"${localProperties.getProperty("OPENAI_DEFAULT_MODEL", "")}\"")
        }
        release {
            // 🚀 性能优化配置已移至build-logic/PerformanceOptimizationPlugin

            // 示例：定义生产环境的API URL (应从安全来源读取)
            buildConfigField("String", "API_BASE_URL", "\"https://api.gymbro.com\"")
            // 示例：禁用日志
            buildConfigField("Boolean", "ENABLE_LOGGING", "false")

            // AI Coach API配置 - Release版本使用环境变量，无硬编码默认值
            buildConfigField("String", "GOOGLE_API_KEY", "\"${System.getenv("GOOGLE_API_KEY") ?: ""}\"")
            buildConfigField("String", "GOOGLE_BASE_URL", "\"${System.getenv("GOOGLE_BASE_URL") ?: ""}\"")
            buildConfigField("String", "OPENAI_API_KEY", "\"${System.getenv("OPENAI_API_KEY") ?: ""}\"")
            buildConfigField("String", "OPENAI_BASE_URL", "\"${System.getenv("OPENAI_BASE_URL") ?: ""}\"")
            buildConfigField("String", "DEEPSEEK_API_KEY", "\"${System.getenv("DEEPSEEK_API_KEY") ?: ""}\"")
            buildConfigField("String", "DEEPSEEK_BASE_URL", "\"${System.getenv("DEEPSEEK_BASE_URL") ?: ""}\"")

            // 模型配置 - 使用环境变量，无硬编码默认值
            buildConfigField("String", "DEFAULT_GOOGLE_MODEL", "\"${System.getenv("DEFAULT_GOOGLE_MODEL") ?: ""}\"")
            buildConfigField("String", "DEFAULT_DEEPSEEK_MODEL", "\"${System.getenv("DEFAULT_DEEPSEEK_MODEL") ?: ""}\"")
            buildConfigField("String", "OPENAI_DEFAULT_MODEL", "\"${System.getenv("OPENAI_DEFAULT_MODEL") ?: ""}\"")

            // 使用签名配置
            signingConfig = signingConfigs.getByName("release")
        }
    }

    compileOptions {
        encoding = "UTF-8"
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
        isCoreLibraryDesugaringEnabled = true
    }

    kotlinOptions {
        jvmTarget = libs.versions.jvmTarget.get() // Use version from catalog
        // 🚀 Kotlin编译器优化已移至build-logic/PerformanceOptimizationPlugin
    }

    buildFeatures {
        compose = true
        buildConfig = true
    }

    // 🚀 Bundle配置已移至build-logic/PerformanceOptimizationPlugin

    // KSP 配置
    ksp {
        // 确保KSP正确处理Room和Hilt注解
        arg("room.schemaLocation", "$projectDir/schemas")
        arg("room.incremental", "true")
        arg("room.expandProjection", "true")
        arg("ksp.incremental", "true") // 🚀 启用KSP增量编译，提升构建速度
    }

    // 🚀 Packaging配置已移至build-logic/PerformanceOptimizationPlugin

    // 🚀 Android资源配置已移至build-logic/PerformanceOptimizationPlugin
}

dependencies {
    // 引入基础模块
    implementation(project(":core"))
    implementation(project(":core-ml"))
    implementation(project(":core-network"))
    implementation(project(":domain"))
    implementation(project(":data"))
    implementation(project(":di"))
    implementation(project(":designSystem"))
    implementation(project(":navigation"))

    // 集成所有特性模块
    implementation(project(":features:auth"))
    implementation(project(":features:coach"))
    implementation(project(":features:exercise-library"))
    implementation(project(":features:home"))
    implementation(project(":features:profile"))
    implementation(project(":features:subscription"))
    implementation(project(":features:thinkingbox"))
    implementation(project(":features:workout"))

    // 添加WorkManager依赖
    implementation(libs.androidx.work.runtime.ktx)

    // 添加Hilt-WorkManager集成
    implementation(libs.androidx.hilt.work)
    // 注意：Hilt编译器在下面统一配置，避免重复

    // Firebase依赖
    implementation(platform(libs.firebase.bom))
    implementation(libs.firebase.analytics.ktx)
    implementation(libs.firebase.auth.ktx)
    implementation(libs.firebase.firestore.ktx)
    implementation(libs.firebase.storage.ktx)
    implementation(libs.firebase.database.ktx)
    implementation(libs.play.services.auth)

    // 添加Credential Manager依赖 - 新的Google Sign-In推荐方式
    implementation("androidx.credentials:credentials:1.3.0")
    implementation("androidx.credentials:credentials-play-services-auth:1.3.0")
    implementation("com.google.android.libraries.identity.googleid:googleid:1.1.1")

    // Timber日志
    implementation(libs.timber)

    // Navigation组件
    implementation(libs.androidx.navigation.compose)
    implementation(libs.hilt.navigation.compose)

    // Hilt依赖注入
    implementation(libs.hilt.android)
    ksp(libs.hilt.compiler)

    // Kotlin协程
    implementation(libs.kotlinx.coroutines.android)

    // Kotlin DateTime - 用于RegionDetectionManager
    implementation(libs.kotlinx.datetime)

    // Kotlin序列化（用于AI API）
    implementation(libs.kotlinx.serialization.json)

    // Retrofit Kotlin序列化转换器（用于AI API）
    implementation(libs.retrofit.kotlinx.serialization)

    // Core Library Desugaring依赖
    coreLibraryDesugaring(libs.desugarJdkLibs)

    // 🚀 性能监控依赖已移至build-logic/PerformanceOptimizationPlugin
}
