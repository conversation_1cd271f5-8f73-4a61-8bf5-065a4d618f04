package com.example.gymbro.data.ai.compression

import com.example.gymbro.shared.models.ai.FunctionDefinition
import kotlinx.serialization.json.Json
import timber.log.Timber
import java.util.*
import java.util.zip.GZIPInputStream
import java.util.zip.GZIPOutputStream
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Function定义压缩器
 * Sprint 1A: 实现18% Token节省目标
 *
 * 压缩策略：
 * 1. LZ-String风格压缩 (基于GZIP实现)
 * 2. Base64编码
 * 3. 服务端x-func-deflated=true协议支持
 */
@Singleton
class FunctionCompressor
@Inject
constructor(
    private val json: Json,
) {
    /**
     * 压缩Function定义
     * @param function 要压缩的Function定义
     * @return 压缩后的字符串
     */
    fun compressFunction(function: FunctionDefinition): CompressedFunction {
        val originalJson = json.encodeToString(function)

        return try {
            val compressed = compress(originalJson)
            val compressionRatio = (compressed.length.toDouble() / originalJson.length * 100).toInt()

            Timber.d(
                "Function压缩完成: ${originalJson.length} -> ${compressed.length} bytes ($compressionRatio%)",
            )

            CompressedFunction(
                compressed = compressed,
                original = function,
                originalSize = originalJson.length,
                compressedSize = compressed.length,
                compressionRatio = compressionRatio,
                isCompressed = true,
            )
        } catch (e: Exception) {
            Timber.w(e, "Function压缩失败，使用原始数据")

            CompressedFunction(
                compressed = originalJson,
                original = function,
                originalSize = originalJson.length,
                compressedSize = originalJson.length,
                compressionRatio = 100,
                isCompressed = false,
            )
        }
    }

    /**
     * 批量压缩多个Function
     * @param functions Function定义列表
     * @return 压缩结果映射
     */
    fun compressFunctions(functions: List<FunctionDefinition>): Map<String, CompressedFunction> =
        functions.associate { function ->
            function.name to compressFunction(function)
        }

    /**
     * 解压Function定义
     * @param compressedData 压缩的数据
     * @return 解压后的Function定义
     */
    fun decompressFunction(compressedData: String): FunctionDefinition =
        try {
            val decompressed = decompress(compressedData)
            json.decodeFromString<FunctionDefinition>(decompressed)
        } catch (e: Exception) {
            Timber.e(e, "Function解压失败")
            throw CompressionException("Failed to decompress function definition", e)
        }

    /**
     * GZIP压缩实现
     * 使用GZIP模拟LZ-String压缩效果
     */
    private fun compress(data: String): String {
        val bytes = data.toByteArray(Charsets.UTF_8)
        val outputStream = java.io.ByteArrayOutputStream()

        val compressedBytes =
            GZIPOutputStream(outputStream).use { gzip ->
                gzip.write(bytes)
                gzip.finish()
                outputStream.toByteArray()
            }

        return Base64.getEncoder().encodeToString(compressedBytes)
    }

    /**
     * GZIP解压实现
     */
    private fun decompress(compressedData: String): String {
        val compressedBytes = Base64.getDecoder().decode(compressedData)

        return GZIPInputStream(
            java.io.ByteArrayInputStream(compressedBytes),
        ).use { gzip ->
            gzip.readBytes().toString(Charsets.UTF_8)
        }
    }

    /**
     * 估算压缩效果
     * @param sampleText 样本文本
     * @return 压缩效果估算
     */
    fun estimateCompressionRatio(sampleText: String): CompressionEstimate {
        val originalSize = sampleText.length
        val compressed = compress(sampleText)
        val compressedSize = compressed.length
        val ratio = (compressedSize.toDouble() / originalSize * 100).toInt()

        return CompressionEstimate(
            originalSize = originalSize,
            compressedSize = compressedSize,
            compressionRatio = ratio,
            tokenSavingEstimate = ((100 - ratio) * 0.01 * (originalSize / 4)).toInt(), // 粗略Token节省估算
        )
    }
}

/**
 * 压缩后的Function数据
 */
data class CompressedFunction(
    val compressed: String,
    val original: FunctionDefinition,
    val originalSize: Int,
    val compressedSize: Int,
    val compressionRatio: Int, // 压缩后大小占原始大小的百分比
    val isCompressed: Boolean,
) {
    /**
     * 获取Token节省估算
     */
    fun getTokenSavings(): Int =
        if (isCompressed) {
            ((originalSize - compressedSize) / 4) // 粗略估算：4字符≈1Token
        } else {
            0
        }

    /**
     * 是否达到目标压缩比
     * @param targetRatio 目标压缩比（例如82表示压缩到原来的82%，即节省18%）
     */
    fun meetsCompressionTarget(targetRatio: Int = 82): Boolean = compressionRatio <= targetRatio
}

/**
 * 压缩效果估算
 */
data class CompressionEstimate(
    val originalSize: Int,
    val compressedSize: Int,
    val compressionRatio: Int,
    val tokenSavingEstimate: Int,
)

/**
 * 压缩异常
 */
class CompressionException(
    message: String,
    cause: Throwable? = null,
) : Exception(message, cause)

/**
 * Function压缩配置
 */
data class CompressionConfig(
    val enableCompression: Boolean = true,
    val targetCompressionRatio: Int = 82, // 目标：18%节省
    val fallbackToOriginal: Boolean = true, // 压缩失败时使用原始数据
    val compressionThreshold: Int = 500, // 只对大于500字符的Function进行压缩
)
