/*
package com.example.gymbro.core.ml.embedding

import android.content.Context
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.gymbro.core.ml.model.CandidateVector
import com.example.gymbro.core.ml.utils.VectorUtils
import com.google.common.truth.Truth.assertThat
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.kotlin.mock
import kotlin.math.abs
import kotlin.math.sqrt

/**
 * BGE→RAG完整链路集成测试
 * 验证"PersonInfo→BGE→RAG→PROMPT"的完整数据流
 */
@RunWith(AndroidJUnit4::class)
class BgeRagIntegrationTest {

    private lateinit var context: Context
    private lateinit var bgeEngine: BgeEmbeddingEngine

    @Before
    fun setup() {
        context = ApplicationProvider.getApplicationContext()
        bgeEngine = BgeEmbeddingEngine(
            context = context,
            ioDispatcher = Dispatchers.IO,
            defaultDispatcher = Dispatchers.Default,
            modelConfig = BgeModelConfig,
            memoryMonitor = mock(),
            hardwareAccelerationManager = mock(),
        )
    }

    @Test
    fun `完整RAG链路测试 - PersonInfo到向量搜索`() = runTest {
        // === Phase 1: PersonInfo → BGE ===

        // 模拟用户个人信息
        val userProfile = """
            用户信息：
            姓名：张三
            年龄：28岁
            身高：175cm
            体重：70kg
            健身经验：1年
            健身目标：增肌
            可用时间：每周3次，每次1小时
            偏好：力量训练，不喜欢有氧
        """.trimIndent()

        // 模拟训练模板库
        val workoutTemplates = listOf(
            "力量训练计划：胸部和三头肌训练，适合初中级健身者，60分钟完成",
            "有氧训练计划：跑步机间歇训练，适合减脂，45分钟",
            "腿部力量训练：深蹲、硬拉、腿屈伸，适合增肌，90分钟",
            "全身循环训练：适合初学者，30分钟快速训练",
            "背部和二头肌训练：引体向上、划船动作，中高级，75分钟",
        )

        // === Phase 2: BGE向量化 ===

        // 1. 用户查询向量化
        val userQueryVector = bgeEngine.embed("我想要增肌的力量训练计划")
        assertThat(userQueryVector.size).isEqualTo(384)
        assertThat(userQueryVector.any { abs(it) > 0.001f }).isTrue()

        // 2. 模板库向量化
        val templateVectors = workoutTemplates.map { template ->
            val vector = bgeEngine.embed(template)
            CandidateVector(template, vector)
        }

        // === Phase 3: RAG向量搜索 ===

        val topK = VectorUtils.findTopK(
            targetVector = userQueryVector,
            candidateVectors = templateVectors,
            k = 3,
        )

        // 验证搜索结果
        assertThat(topK).hasSize(3)
        assertThat(topK[0].similarity).isGreaterThan(topK[1].similarity)
        assertThat(topK[1].similarity).isGreaterThan(topK[2].similarity)

        // === Phase 4: RAG结果验证 ===

        // 验证最相关的结果应该是力量训练相关
        val topResult = topK[0]
        val topTemplate = templateVectors.find { it.id == topResult.id }?.id

        // 最匹配的应该包含"力量训练"或"增肌"关键词
        assertThat(topTemplate).isNotNull()
        val containsRelevantKeywords = topTemplate!!.contains("力量训练") ||
            topTemplate.contains("增肌") ||
            topTemplate.contains("胸部") ||
            topTemplate.contains("背部")
        assertThat(containsRelevantKeywords).isTrue()

        // 打印搜索结果用于验证
        println("🔍 用户查询: 我想要增肌的力量训练计划")
        println("📊 Top-3 搜索结果:")
        topK.forEachIndexed { index, result ->
            val template = templateVectors.find { it.id == result.id }?.id
            println("${index + 1}. [相似度: ${"%.3f".format(result.similarity)}] $template")
        }
    }

    @Test
    fun `BGE引擎确定性测试 - 相同输入产生相同向量`() = runTest {
        val text = "力量训练计划：胸部和三头肌"

        // 多次生成向量
        val vector1 = bgeEngine.embed(text)
        val vector2 = bgeEngine.embed(text)
        val vector3 = bgeEngine.embed(text)

        // 验证确定性
        assertThat(vector1.size).isEqualTo(vector2.size)
        assertThat(vector2.size).isEqualTo(vector3.size)

        // 验证向量内容完全一致
        for (i in vector1.indices) {
            assertThat(abs(vector1[i] - vector2[i])).isLessThan(0.0001f)
            assertThat(abs(vector2[i] - vector3[i])).isLessThan(0.0001f)
        }
    }

    @Test
    fun `BGE语义理解测试 - 语义相似的文本产生相似向量`() = runTest {
        // 语义相似的文本对
        val text1 = "胸部力量训练计划"
        val text2 = "胸肌增强锻炼方案"

        // 语义不同的文本
        val text3 = "有氧跑步减脂训练"

        val vector1 = bgeEngine.embed(text1)
        val vector2 = bgeEngine.embed(text2)
        val vector3 = bgeEngine.embed(text3)

        // 计算余弦相似度
        val similarity12 = VectorUtils.cosineSimilarity(vector1, vector2)
        val similarity13 = VectorUtils.cosineSimilarity(vector1, vector3)
        val similarity23 = VectorUtils.cosineSimilarity(vector2, vector3)

        // 语义相似的文本应该有更高的相似度
        assertThat(similarity12).isGreaterThan(similarity13)
        assertThat(similarity12).isGreaterThan(similarity23)

        println("🔤 语义相似度测试结果:")
        println("胸部力量训练 vs 胸肌增强锻炼: ${"%.3f".format(similarity12)}")
        println("胸部力量训练 vs 有氧跑步减脂: ${"%.3f".format(similarity13)}")
        println("胸肌增强锻炼 vs 有氧跑步减脂: ${"%.3f".format(similarity23)}")
    }

    @Test
    fun `向量L2归一化验证`() = runTest {
        val texts = listOf(
            "短文本",
            "这是一个稍微长一点的健身相关文本内容",
            "这是一个更长的文本，包含了详细的健身训练计划描述，涵盖了力量训练、有氧运动、营养建议等多个方面的综合内容",
        )

        texts.forEach { text ->
            val vector = bgeEngine.embed(text)
            val l2Norm = sqrt(vector.sumOf { (it * it).toDouble() }).toFloat()

            // L2归一化后的向量范数应该接近1.0
            assertThat(abs(l2Norm - 1.0f)).isLessThan(0.01f)
            println("📏 文本长度: ${text.length}, L2范数: ${"%.6f".format(l2Norm)}")
        }
    }
}
*/