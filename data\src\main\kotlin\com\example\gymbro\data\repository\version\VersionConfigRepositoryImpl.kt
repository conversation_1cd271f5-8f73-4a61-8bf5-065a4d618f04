package com.example.gymbro.data.repository.version

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.local.version.VersionConfigStorage
import com.example.gymbro.domain.shared.base.version.RegionVersionConfig
import com.example.gymbro.domain.shared.version.repository.VersionConfigRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 版本配置仓库实现
 *
 * 使用本地存储管理版本配置，支持默认配置
 * 实现Domain层的VersionConfigRepository接口
 */
@Singleton
class VersionConfigRepositoryImpl @Inject constructor(
    private val versionConfigStorage: VersionConfigStorage,
) : VersionConfigRepository {

    override suspend fun getRegionVersionConfig(region: String): ModernResult<RegionVersionConfig> {
        return try {
            val result = versionConfigStorage.getRegionConfig(region)

            when (result) {
                is ModernResult.Success -> {
                    val config = result.data ?: getDefaultConfig(region)
                    ModernResult.Success(config)
                }
                is ModernResult.Error -> result
                is ModernResult.Loading -> result
            }
        } catch (e: Exception) {
            Timber.e(e, "Failed to get region version config for: $region")
            // 返回默认配置作为fallback
            ModernResult.Success(getDefaultConfig(region))
        }
    }

    override suspend fun updateRegionVersionConfig(config: RegionVersionConfig): ModernResult<Unit> {
        return versionConfigStorage.saveRegionConfig(config)
    }

    override fun observeRegionVersionConfig(region: String): Flow<ModernResult<RegionVersionConfig>> = flow {
        try {
            val config = getRegionVersionConfig(region)
            emit(config)
        } catch (e: Exception) {
            Timber.e(e, "Failed to observe region version config for: $region")
            emit(ModernResult.Success(getDefaultConfig(region)))
        }
    }

    override suspend fun clearCache(): ModernResult<Unit> {
        return versionConfigStorage.clearAll()
    }

    override suspend fun getAllRegionConfigs(): ModernResult<Map<String, RegionVersionConfig>> {
        return try {
            val configs = mutableMapOf<String, RegionVersionConfig>()

            // 获取CN区域配置
            val cnResult = getRegionVersionConfig("CN")
            if (cnResult is ModernResult.Success) {
                configs["CN"] = cnResult.data
            }

            // 获取INTERNATIONAL区域配置
            val intlResult = getRegionVersionConfig("INTERNATIONAL")
            if (intlResult is ModernResult.Success) {
                configs["INTERNATIONAL"] = intlResult.data
            }

            ModernResult.Success(configs)
        } catch (e: Exception) {
            Timber.e(e, "Failed to get all region configs")
            ModernResult.Error(
                com.example.gymbro.core.error.types.ModernDataError(
                    operationName = "getAllRegionConfigs",
                    errorType = com.example.gymbro.core.error.types.GlobalErrorType.System.General,
                    uiMessage = UiText.DynamicString("获取所有区域配置失败"),
                    cause = e,
                ),
            )
        }
    }

    /**
     * 获取默认配置
     */
    private fun getDefaultConfig(region: String): RegionVersionConfig {
        return when (region) {
            "CN" -> RegionVersionConfig(
                region = "CN",
                isLocked = false,
                lockReason = null,
                minimumVersion = "1.0.0",
                blockedVersions = emptyList(),
                forceUpdateUrl = "https://example.com/download",
                contactInfo = "<EMAIL>",
            )
            "INTERNATIONAL" -> RegionVersionConfig(
                region = "INTERNATIONAL",
                isLocked = false,
                lockReason = null,
                minimumVersion = "1.0.0",
                blockedVersions = emptyList(),
                forceUpdateUrl = "https://play.google.com/store/apps/details?id=com.example.gymbro",
                contactInfo = "<EMAIL>",
            )
            else -> RegionVersionConfig(
                region = region,
                isLocked = false,
                lockReason = null,
                minimumVersion = "1.0.0",
                blockedVersions = emptyList(),
                forceUpdateUrl = null,
                contactInfo = "<EMAIL>",
            )
        }
    }
}
