package com.example.gymbro.data.local.entity.user

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 用户资料实体类
 *
 * 映射到数据库中的用户资料表
 */
@Entity(tableName = "user_profiles")
data class UserProfileEntity(
    @PrimaryKey
    val userId: String,

    val username: String?,
    val displayName: String?,
    val email: String?,
    val phoneNumber: String?,
    val profileImageUrl: String?,
    val bio: String?,
    val gender: String?,
    val height: Float?,
    val heightUnit: String?,
    val weight: Float?,
    val weightUnit: String?,
    val fitnessLevel: Int?,
    val fitnessGoals: List<String>,
    val workoutDays: List<String>,
    val allowPartnerMatching: Bo<PERSON>an,
    val totalWorkoutCount: Int,
    val weeklyActiveMinutes: Int,
    val likesReceived: Int,
    val isAnonymous: <PERSON><PERSON><PERSON>,
    val hasValidSubscription: Bo<PERSON>an,
    val lastUpdated: Long,
    val createdAt: Long,

    // === RAG集成字段 ===
    val profileSummary: String? = null, // 用于RAG的Profile摘要文本
    val vector: ByteArray? = null, // BGE向量嵌入（384维）
    val vectorCreatedAt: Long? = null, // 向量创建时间
)
