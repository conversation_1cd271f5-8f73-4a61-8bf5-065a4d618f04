package com.example.gymbro.data.exercise.local.dao

import androidx.room.Dao
import androidx.room.Query
import com.example.gymbro.data.exercise.local.entity.ExerciseEntity

/**
 * Exercise全文搜索DAO
 *
 * 基于plan.md设计：
 * - FTS4全文搜索引擎
 * - 支持中文、拼音、英文搜索
 * - 高性能模糊匹配
 * - 相关性排序
 */
@Dao
interface ExerciseFtsDao {
    /**
     * FTS全文搜索
     *
     * 搜索策略：
     * 1. 精确匹配优先
     * 2. 前缀匹配
     * 3. 模糊匹配
     * 4. 按相关性排序
     *
     * @param query 搜索关键词
     * @param limit 结果数量限制
     * @return 匹配的动作列表，按相关性排序
     */
    @Query(
        """
        SELECT exercise.* FROM exercise
        JOIN exercise_fts ON exercise.rowid = exercise_fts.rowid
        WHERE exercise_fts MATCH :query
        ORDER BY exercise.rowid ASC
        LIMIT :limit
    """,
    )
    suspend fun searchFts(
        query: String,
        limit: Int = 20,
    ): List<ExerciseEntity>

    /**
     * 精确名称搜索
     * 用于高精度匹配
     */
    @Query(
        """
        SELECT exercise.* FROM exercise
        JOIN exercise_fts ON exercise.rowid = exercise_fts.rowid
        WHERE exercise_fts.name MATCH :exactName
        ORDER BY exercise.rowid ASC
        LIMIT :limit
    """,
    )
    suspend fun searchExactName(
        exactName: String,
        limit: Int = 5,
    ): List<ExerciseEntity>

    /**
     * 按肌群搜索
     * 结合FTS和肌群筛选
     */
    @Query(
        """
        SELECT exercise.* FROM exercise
        JOIN exercise_fts ON exercise.rowid = exercise_fts.rowid
        WHERE exercise_fts MATCH :query
        AND exercise.muscleGroup = :muscleGroup
        ORDER BY exercise.rowid ASC
        LIMIT :limit
    """,
    )
    suspend fun searchByMuscleGroup(
        query: String,
        muscleGroup: String,
        limit: Int = 20,
    ): List<ExerciseEntity>

    /**
     * 按器械搜索
     * 结合FTS和器械筛选
     */
    @Query(
        """
        SELECT exercise.* FROM exercise
        JOIN exercise_fts ON exercise.rowid = exercise_fts.rowid
        WHERE exercise_fts MATCH :query
        AND exercise.equipment LIKE '%' || :equipment || '%'
        ORDER BY exercise.rowid ASC
        LIMIT :limit
    """,
    )
    suspend fun searchByEquipment(
        query: String,
        equipment: String,
        limit: Int = 20,
    ): List<ExerciseEntity>

    /**
     * 多字段搜索
     * 搜索名称、描述、步骤等多个字段
     */
    @Query(
        """
        SELECT exercise.* FROM exercise
        JOIN exercise_fts ON exercise.rowid = exercise_fts.rowid
        WHERE exercise_fts MATCH :query
        ORDER BY
            CASE
                WHEN exercise_fts.name MATCH :query THEN 1
                WHEN exercise_fts.description MATCH :query THEN 2
                WHEN exercise_fts.steps MATCH :query THEN 3
                ELSE 4
            END,
            exercise.rowid ASC
        LIMIT :limit
    """,
    )
    suspend fun searchMultiField(
        query: String,
        limit: Int = 20,
    ): List<ExerciseEntity>

    /**
     * 前缀搜索
     * 用于搜索建议和自动完成
     */
    @Query(
        """
        SELECT exercise.* FROM exercise
        JOIN exercise_fts ON exercise.rowid = exercise_fts.rowid
        WHERE exercise_fts.name MATCH :prefix || '*'
        ORDER BY LENGTH(exercise.name) ASC, exercise.name ASC
        LIMIT :limit
    """,
    )
    suspend fun searchPrefix(
        prefix: String,
        limit: Int = 10,
    ): List<ExerciseEntity>

    /**
     * 搜索建议
     * 返回匹配的动作名称，用于搜索提示
     */
    @Query(
        """
        SELECT DISTINCT exercise.name FROM exercise
        JOIN exercise_fts ON exercise.rowid = exercise_fts.rowid
        WHERE exercise_fts.name MATCH :query || '*'
        ORDER BY LENGTH(exercise.name) ASC
        LIMIT :limit
    """,
    )
    suspend fun getSearchSuggestions(
        query: String,
        limit: Int = 5,
    ): List<String>

    /**
     * 热门搜索
     * 基于收藏数量和使用频率
     */
    @Query(
        """
        SELECT exercise.* FROM exercise
        WHERE exercise.isFavorite = 1
        ORDER BY exercise.name ASC
        LIMIT :limit
    """,
    )
    suspend fun getPopularExercises(limit: Int = 10): List<ExerciseEntity>

    /**
     * 相似动作搜索
     * 基于肌群和器械的相似性
     */
    @Query(
        """
        SELECT exercise.* FROM exercise
        WHERE exercise.muscleGroup = :muscleGroup
        AND exercise.id != :excludeId
        ORDER BY
            CASE WHEN exercise.equipment = :equipment THEN 1 ELSE 2 END,
            exercise.name ASC
        LIMIT :limit
    """,
    )
    suspend fun findSimilarExercises(
        muscleGroup: String,
        equipment: String,
        excludeId: String,
        limit: Int = 5,
    ): List<ExerciseEntity>

    // ========== FTS维护操作 ==========

    /**
     * 重建FTS索引
     * 用于数据同步后的索引优化
     */
    @Query("INSERT INTO exercise_fts(exercise_fts) VALUES('rebuild')")
    suspend fun rebuildFtsIndex()

    /**
     * 优化FTS索引
     * 定期维护操作
     */
    @Query("INSERT INTO exercise_fts(exercise_fts) VALUES('optimize')")
    suspend fun optimizeFtsIndex()

    /**
     * 获取FTS统计信息
     */
    @Query("SELECT COUNT(*) FROM exercise_fts")
    suspend fun getFtsIndexCount(): Int

    // ========== 高级搜索功能 ==========

    /**
     * 布尔搜索
     * 支持AND、OR、NOT操作符
     *
     * 示例：
     * - "胸肌 AND 杠铃" - 同时包含胸肌和杠铃
     * - "深蹲 OR 硬拉" - 包含深蹲或硬拉
     * - "卧推 NOT 哑铃" - 包含卧推但不包含哑铃
     */
    @Query(
        """
        SELECT exercise.* FROM exercise
        JOIN exercise_fts ON exercise.rowid = exercise_fts.rowid
        WHERE exercise_fts MATCH :booleanQuery
        ORDER BY exercise.rowid ASC
        LIMIT :limit
    """,
    )
    suspend fun searchBoolean(
        booleanQuery: String,
        limit: Int = 20,
    ): List<ExerciseEntity>

    /**
     * 短语搜索
     * 精确匹配短语
     */
    @Query(
        """
        SELECT exercise.* FROM exercise
        JOIN exercise_fts ON exercise.rowid = exercise_fts.rowid
        WHERE exercise_fts MATCH '"' || :phrase || '"'
        ORDER BY exercise.rowid ASC
        LIMIT :limit
    """,
    )
    suspend fun searchPhrase(
        phrase: String,
        limit: Int = 20,
    ): List<ExerciseEntity>

    /**
     * 近似搜索
     * 查找相近的词语
     */
    @Query(
        """
        SELECT exercise.* FROM exercise
        JOIN exercise_fts ON exercise.rowid = exercise_fts.rowid
        WHERE exercise_fts MATCH 'NEAR(' || :term1 || ' ' || :term2 || ', 5)'
        ORDER BY exercise.rowid ASC
        LIMIT :limit
    """,
    )
    suspend fun searchNear(
        term1: String,
        term2: String,
        limit: Int = 20,
    ): List<ExerciseEntity>

    // ========== 搜索分析 ==========

    /**
     * 获取搜索结果数量
     * 不返回具体结果，只返回匹配数量
     */
    @Query(
        """
        SELECT COUNT(*) FROM exercise
        JOIN exercise_fts ON exercise.rowid = exercise_fts.rowid
        WHERE exercise_fts MATCH :query
    """,
    )
    suspend fun getSearchResultCount(query: String): Int

    /**
     * 搜索结果分页
     * 支持大结果集的分页查询
     */
    @Query(
        """
        SELECT exercise.* FROM exercise
        JOIN exercise_fts ON exercise.rowid = exercise_fts.rowid
        WHERE exercise_fts MATCH :query
        ORDER BY exercise.rowid ASC
        LIMIT :limit OFFSET :offset
    """,
    )
    suspend fun searchWithPagination(
        query: String,
        limit: Int,
        offset: Int,
    ): List<ExerciseEntity>
}
