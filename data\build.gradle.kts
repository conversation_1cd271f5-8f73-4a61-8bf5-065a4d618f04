plugins {
    id("gymbro.data.module")
    id("gymbro.testing.library")
    id("org.jetbrains.kotlin.plugin.serialization")
}

android {
    namespace = "com.example.gymbro.data" // Define namespace directly

    // 添加 Room schema 导出配置
    ksp {
        arg("room.schemaLocation", "$projectDir/schemas")
    }
}

dependencies {
    // Project modules
    implementation(project(":domain"))
    implementation(project(":core"))
    implementation(project(":core-ml"))
    // 🔧 添加core-network依赖，用于LlmStreamClient
    implementation(project(":core-network"))
    implementation(project(":designSystem"))

    // ★ 添加shared-models依赖 - Stage B核心变更
    api(project(":shared-models"))

    // UserDataCenter 集成
    implementation(project(":core-user-data-center"))

    // Kotlin
    implementation(libs.kotlin.stdlib)
    implementation(libs.kotlinx.coroutines.core)
    implementation(libs.kotlinx.coroutines.android)
    implementation(libs.kotlinx.datetime)

    // Room
    implementation(libs.androidx.room.runtime)
    implementation(libs.androidx.room.ktx)
    ksp(libs.androidx.room.compiler)

    // DataStore
    implementation("androidx.datastore:datastore-preferences:1.0.0")

    // Network
    implementation(libs.retrofit)
    implementation(libs.retrofit.converter.gson)
    implementation(libs.okhttp)
    implementation(libs.okhttp.logging.interceptor)
    implementation(libs.gson)

    // Firebase
    implementation(platform(libs.firebase.bom))
    implementation(libs.firebase.auth.ktx)
    implementation(libs.firebase.firestore.ktx)
    implementation(libs.firebase.database.ktx)
    implementation(libs.firebase.remoteconfig.ktx)
    implementation(libs.firebase.analytics.ktx)
    implementation(libs.firebase.storage.ktx)

    // Google Play Services
    implementation(libs.play.services.auth)

    // Credentials & Identity (新增)
    implementation(libs.androidx.credentials)
    implementation(libs.androidx.credentials.playservices)
    implementation(libs.googleid)

    // Google Play Billing
    implementation(libs.androidx.billing.ktx)

    // Work Manager & Hilt Integration
    implementation(libs.androidx.work.runtime.ktx)
    implementation(libs.androidx.hilt.work)
    // 注意：Hilt编译器在下面统一配置，避免重复

    // Networking (用于AI API) - 🧹 移除SSE依赖，已迁移到core-network
    implementation(libs.retrofit)
    implementation(libs.retrofit.kotlinx.serialization)
    implementation(libs.okhttp)
    implementation(libs.okhttp.logging.interceptor)
    // 🧹 REMOVED: implementation("com.squareup.okhttp3:okhttp-sse:4.12.0") // 已迁移到WebSocket
    implementation(libs.kotlinx.serialization.json)

    // Logging
    implementation(libs.timber)

    // Paging 3 (移动ConversationPagingSource后需要)
    implementation("androidx.paging:paging-runtime:3.2.1")

    // DI (Hilt is often needed in data layer for injection)
    implementation(libs.dagger)
    implementation(libs.hilt.android)
    ksp(libs.hilt.compiler)

    // 注意：测试依赖由testing-library插件提供，无需重复添加
    // 但是为了确保某些特定依赖正确配置，我们显式添加
    testImplementation(libs.androidx.arch.core.testing)
    testImplementation(libs.turbine)
    testImplementation(libs.androidx.test.core)
    testImplementation(libs.mockk)
    testImplementation(libs.junit)
    testImplementation(libs.kotlinx.coroutines.test)
}
