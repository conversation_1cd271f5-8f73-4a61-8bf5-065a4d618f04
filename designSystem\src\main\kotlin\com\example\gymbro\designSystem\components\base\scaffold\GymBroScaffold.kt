package com.example.gymbro.designSystem.components.base.scaffold

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.gymbro.core.theme.LocalThemeManager
import com.example.gymbro.core.theme.ThemeConfig
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.base.header.GymBroHeader
import com.example.gymbro.designSystem.theme.GymBroTheme

/**
 * GymBro 统一 Scaffold 组件 (V2 - 已完善)
 *
 * 提供了与官方 Material 3 Scaffold 类似的灵活性，同时集成了统一的主题和 Header。
 *
 * 新特性：
 * - 支持 `bottomBar` 和 `floatingActionButton` 插槽
 * - `content` 区域完全灵活，支持任何布局（LazyColumn, Box 等）
 * - `GymBroHeader` 作为可配置的 `topBar`
 * - 与官方 Scaffold API 完全对等
 *
 * @param modifier Modifier 修饰符
 * @param title 页面标题，用于默认的 GymBroHeader
 * @param onNavigateBack 返回按钮回调，用于默认的 GymBroHeader
 * @param topBar 顶栏 Composable，默认为 GymBroHeader
 * @param bottomBar 底栏 Composable
 * @param snackbarHostState Snackbar 状态管理
 * @param floatingActionButton 悬浮操作按钮
 * @param floatingActionButtonPosition FAB 位置
 * @param containerColor 容器背景色
 * @param contentColor 内容颜色
 * @param contentWindowInsets 内容窗口插入
 * @param themeVariant 主题变体
 * @param isLoading 是否显示加载状态，用于默认的 GymBroHeader
 * @param showBackButton 是否显示返回按钮，用于默认的 GymBroHeader
 * @param leadingContent Header 左侧内容，用于默认的 GymBroHeader
 * @param actions Header 右侧操作按钮，用于默认的 GymBroHeader
 * @param content 页面内容，接收 PaddingValues 以处理内部边距
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GymBroScaffold(
    modifier: Modifier = Modifier,
    title: UiText? = null,
    onNavigateBack: (() -> Unit)? = null,
    // 以下参数用于默认的 GymBroHeader，需要在topBar之前声明
    isLoading: Boolean = false,
    showBackButton: Boolean = true,
    leadingContent: @Composable RowScope.() -> Unit = {},
    actions: @Composable RowScope.() -> Unit = {},
    topBar: @Composable () -> Unit = {
        // 默认的 TopBar 就是我们的 GymBroHeader
        // 只有在提供了 title 和 onNavigateBack 时才显示
        if (title != null && onNavigateBack != null) {
            GymBroHeader(
                title = title,
                onNavigateBack = onNavigateBack,
                isLoading = isLoading,
                showBackButton = showBackButton,
                leadingContent = leadingContent,
                trailingContent = actions,
            )
        }
    },
    bottomBar: @Composable () -> Unit = {},
    snackbarHostState: SnackbarHostState = remember { SnackbarHostState() },
    floatingActionButton: @Composable () -> Unit = {},
    floatingActionButtonPosition: FabPosition = FabPosition.End,
    containerColor: Color = Color.Unspecified,
    contentColor: Color = Color.Unspecified,
    contentWindowInsets: WindowInsets = ScaffoldDefaults.contentWindowInsets, // 🎯 使用系统默认insets
    themeVariant: GymBroThemeVariant = GymBroThemeVariant.DEFAULT,
    content: @Composable (PaddingValues) -> Unit,
) {
    // 🎯 集成动态主题系统
    val themeManager = LocalThemeManager.current
    val themeConfig by themeManager.themeConfig.collectAsStateWithLifecycle(
        initialValue = ThemeConfig.getDefault(),
    )

    // 🎯 根据主题变体应用不同的主题配置
    val effectiveThemeConfig = when (themeVariant) {
        GymBroThemeVariant.DEFAULT -> themeConfig
        GymBroThemeVariant.PROFILE -> themeConfig.copy(
            // Profile模块特定的主题调整
        )
        GymBroThemeVariant.COACH -> themeConfig.copy(
            // Coach模块特定的主题调整
        )
        GymBroThemeVariant.WORKOUT -> themeConfig.copy(
            // Workout模块特定的主题调整
        )
    }

    GymBroTheme(themeConfig = effectiveThemeConfig) {
        Scaffold(
            modifier = modifier.fillMaxSize(),
            topBar = topBar,
            bottomBar = bottomBar,
            snackbarHost = { SnackbarHost(snackbarHostState) },
            floatingActionButton = floatingActionButton,
            floatingActionButtonPosition = floatingActionButtonPosition,
            containerColor = if (containerColor != Color.Unspecified) {
                containerColor
            } else {
                when (themeVariant) {
                    GymBroThemeVariant.DEFAULT -> MaterialTheme.colorScheme.background
                    GymBroThemeVariant.PROFILE -> MaterialTheme.colorScheme.background
                    GymBroThemeVariant.COACH -> MaterialTheme.colorScheme.background
                    GymBroThemeVariant.WORKOUT -> MaterialTheme.colorScheme.background
                }
            },
            contentColor = if (contentColor != Color.Unspecified) {
                contentColor
            } else {
                MaterialTheme.colorScheme.onBackground
            },
            contentWindowInsets = contentWindowInsets,
        ) { paddingValues ->
            // 直接将内容和 paddingValues 交给调用者，不再强制使用 Column
            content(paddingValues)
        }
    }
}

/**
 * 主题变体枚举
 * 用于区分不同模块的主题风格
 */
enum class GymBroThemeVariant {
    DEFAULT, // 默认主题
    PROFILE, // Profile模块主题
    COACH, // Coach模块主题
    WORKOUT, // Workout模块主题
}

// === 便捷别名 - 提供更简洁的API ===

/**
 * Workout模块专用Scaffold
 *
 * 提供Workout模块常用的配置，同时继承所有GymBroScaffold的新功能
 *
 * @param title 页面标题
 * @param onNavigateBack 返回按钮回调
 * @param modifier Modifier修饰符
 * @param bottomBar 底栏 Composable
 * @param snackbarHostState Snackbar 状态管理
 * @param floatingActionButton 悬浮操作按钮
 * @param floatingActionButtonPosition FAB 位置
 * @param isLoading 是否显示加载状态
 * @param showBackButton 是否显示返回按钮
 * @param actions Header 右侧操作按钮
 * @param content 页面内容，接收 PaddingValues
 */
@Composable
fun WorkoutScaffold(
    title: UiText,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
    bottomBar: @Composable () -> Unit = {},
    snackbarHostState: SnackbarHostState = remember { SnackbarHostState() },
    floatingActionButton: @Composable () -> Unit = {},
    floatingActionButtonPosition: FabPosition = FabPosition.End,
    isLoading: Boolean = false,
    showBackButton: Boolean = true,
    actions: @Composable RowScope.() -> Unit = {},
    content: @Composable (PaddingValues) -> Unit,
) {
    GymBroScaffold(
        modifier = modifier,
        title = title,
        onNavigateBack = onNavigateBack,
        // 将 actions 传递给默认的 GymBroHeader
        topBar = {
            GymBroHeader(
                title = title,
                onNavigateBack = onNavigateBack,
                isLoading = isLoading,
                showBackButton = showBackButton,
                trailingContent = actions,
            )
        },
        bottomBar = bottomBar,
        snackbarHostState = snackbarHostState,
        floatingActionButton = floatingActionButton,
        floatingActionButtonPosition = floatingActionButtonPosition,
        themeVariant = GymBroThemeVariant.WORKOUT,
        content = content,
    )
}

/**
 * Profile模块专用Scaffold
 *
 * 提供Profile模块常用的配置，同时继承所有GymBroScaffold的新功能
 */
@Composable
fun ProfileScaffold(
    title: UiText,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
    bottomBar: @Composable () -> Unit = {},
    snackbarHostState: SnackbarHostState = remember { SnackbarHostState() },
    floatingActionButton: @Composable () -> Unit = {},
    floatingActionButtonPosition: FabPosition = FabPosition.End,
    isLoading: Boolean = false,
    showBackButton: Boolean = true,
    leadingContent: @Composable RowScope.() -> Unit = {},
    actions: @Composable RowScope.() -> Unit = {},
    content: @Composable (PaddingValues) -> Unit,
) {
    GymBroScaffold(
        modifier = modifier,
        title = title,
        onNavigateBack = onNavigateBack,
        topBar = {
            GymBroHeader(
                title = title,
                onNavigateBack = onNavigateBack,
                isLoading = isLoading,
                showBackButton = showBackButton,
                leadingContent = leadingContent,
                trailingContent = actions,
            )
        },
        bottomBar = bottomBar,
        snackbarHostState = snackbarHostState,
        floatingActionButton = floatingActionButton,
        floatingActionButtonPosition = floatingActionButtonPosition,
        themeVariant = GymBroThemeVariant.PROFILE,
        content = content,
    )
}

/**
 * Coach模块专用Scaffold
 *
 * 提供Coach模块常用的配置，同时继承所有GymBroScaffold的新功能
 */
@Composable
fun CoachScaffold(
    modifier: Modifier = Modifier,
    topBar: @Composable () -> Unit = {},
    bottomBar: @Composable () -> Unit = {},
    snackbarHostState: SnackbarHostState = remember { SnackbarHostState() },
    floatingActionButton: @Composable () -> Unit = {},
    floatingActionButtonPosition: FabPosition = FabPosition.End,
    content: @Composable (PaddingValues) -> Unit,
) {
    GymBroScaffold(
        modifier = modifier,
        topBar = topBar,
        bottomBar = bottomBar,
        snackbarHostState = snackbarHostState,
        floatingActionButton = floatingActionButton,
        floatingActionButtonPosition = floatingActionButtonPosition,
        themeVariant = GymBroThemeVariant.COACH,
        content = content,
    )
}

// === 向后兼容的废弃别名 ===

/**
 * Profile模块Scaffold别名（字符串标题版本）
 * @deprecated 使用 ProfileScaffold 的 UiText 版本替代
 */
@Deprecated(
    message = "Use ProfileScaffold with UiText title parameter",
    replaceWith = ReplaceWith(
        "ProfileScaffold(title = UiText.DynamicString(title), onNavigateBack = onNavigateBack, " +
            "modifier = modifier, isLoading = isLoading, snackbarHostState = snackbarHostState, " +
            "content = { paddingValues -> Column(Modifier.fillMaxSize().padding(paddingValues)" +
            ".let { if (enableScroll) it.verticalScroll(rememberScrollState()) else it }) { content() } })",
        "com.example.gymbro.designSystem.components.base.scaffold.ProfileScaffold",
        "com.example.gymbro.core.ui.text.UiText",
        "androidx.compose.foundation.layout.Column",
        "androidx.compose.foundation.layout.fillMaxSize",
        "androidx.compose.foundation.layout.padding",
        "androidx.compose.foundation.rememberScrollState",
        "androidx.compose.foundation.verticalScroll",
        "androidx.compose.runtime.Composable",
    ),
)
@Composable
fun ProfileScaffold(
    title: String,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
    isLoading: Boolean = false,
    snackbarHostState: SnackbarHostState = remember { SnackbarHostState() },
    enableScroll: Boolean = true,
    content: @Composable (ColumnScope.() -> Unit),
) {
    ProfileScaffold(
        title = UiText.DynamicString(title),
        onNavigateBack = onNavigateBack,
        modifier = modifier,
        isLoading = isLoading,
        snackbarHostState = snackbarHostState,
        content = { paddingValues ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .let { if (enableScroll) it.verticalScroll(rememberScrollState()) else it },
            ) {
                content()
            }
        },
    )
}
