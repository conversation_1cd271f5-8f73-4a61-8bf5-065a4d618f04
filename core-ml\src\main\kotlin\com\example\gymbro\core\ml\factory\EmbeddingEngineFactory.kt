package com.example.gymbro.core.ml.factory

import com.example.gymbro.core.ml.config.BgeModelConfig
import com.example.gymbro.core.ml.embedding.EmbeddingEngine
import com.example.gymbro.core.ml.embedding.OnnxEmbeddingEngine
import java.io.InputStream

/**
 * 嵌入引擎工厂类
 *
 * 根据配置和设备能力选择最适合的推理引擎
 */
object EmbeddingEngineFactory {
    /**
     * 推理引擎类型
     */
    enum class EngineType {
        TENSORFLOW_LITE,
        ONNX_RUNTIME,
        AUTO,
    }

    /**
     * 引擎配置参数
     */
    data class EngineConfig(
        val engineType: EngineType = EngineType.AUTO,
        val maxSequenceLength: Int = BgeModelConfig.maxSequenceLength, // 🔥 使用统一配置
        val embeddingDim: Int = BgeModelConfig.embeddingDim, // 🔥 使用统一配置
        val enableOptimization: Boolean = true,
        val threadCount: Int = 4,
    )

    /**
     * 创建嵌入引擎实例
     *
     * @param modelStream 模型文件流
     * @param vocabularyStream 词汇表文件流
     * @param config 引擎配置
     * @return 嵌入引擎实例
     */
    fun createEngine(
        modelStream: InputStream,
        vocabularyStream: InputStream,
        config: EngineConfig = EngineConfig(),
    ): EmbeddingEngine {
        val selectedType =
            when (config.engineType) {
                EngineType.AUTO -> selectOptimalEngine()
                else -> config.engineType
            }

        return when (selectedType) {
            EngineType.TENSORFLOW_LITE ->
                createTensorFlowLiteEngine(
                    modelStream,
                    vocabularyStream,
                    config,
                )
            EngineType.ONNX_RUNTIME ->
                createOnnxEngine(
                    modelStream,
                    vocabularyStream,
                    config,
                )
            EngineType.AUTO -> throw IllegalStateException("AUTO类型应该已被解析")
        }
    }

    /**
     * 创建TensorFlow Lite引擎
     *
     * 🔥 注意：此工厂方法已废弃，因为新的 BgeEmbeddingEngine 需要 Context 和 Dispatcher
     * 应该直接通过 Hilt 注入 BgeEmbeddingEngine，而不是通过工厂创建
     */
    @Deprecated("使用 Hilt 直接注入 BgeEmbeddingEngine")
    private fun createTensorFlowLiteEngine(
        modelStream: InputStream,
        vocabularyStream: InputStream,
        config: EngineConfig,
    ): EmbeddingEngine {
        throw UnsupportedOperationException(
            "BgeEmbeddingEngine 现在需要 Context 和 CoroutineDispatcher，" +
                "请通过 Hilt 依赖注入获取实例，而不是通过工厂创建",
        )
    }

    /**
     * 创建ONNX Runtime引擎
     */
    private fun createOnnxEngine(
        modelStream: InputStream,
        vocabularyStream: InputStream,
        config: EngineConfig,
    ): EmbeddingEngine =
        OnnxEmbeddingEngine(
            modelStream = modelStream,
            vocabularyStream = vocabularyStream,
            maxSequenceLength = config.maxSequenceLength,
            embeddingDim = config.embeddingDim,
        )

    /**
     * 根据设备特性选择最优引擎
     */
    private fun selectOptimalEngine(): EngineType =
        try {
            // 检查可用的推理框架
            when {
                isOnnxRuntimeAvailable() && hasHighPerformanceDevice() -> {
                    EngineType.ONNX_RUNTIME
                }
                isTensorFlowLiteAvailable() -> {
                    EngineType.TENSORFLOW_LITE
                }
                else -> {
                    // 默认尝试TensorFlow Lite
                    EngineType.TENSORFLOW_LITE
                }
            }
        } catch (e: Exception) {
            // 发生错误时回退到TensorFlow Lite
            EngineType.TENSORFLOW_LITE
        }

    /**
     * 检查ONNX Runtime是否可用
     */
    private fun isOnnxRuntimeAvailable(): Boolean =
        try {
            // TODO: 检查ONNX Runtime依赖是否存在
            // Class.forName("ai.onnxruntime.OrtEnvironment")
            // true
            false // 暂时返回false，实际部署时需要检查
        } catch (e: Exception) {
            false
        }

    /**
     * 检查TensorFlow Lite是否可用
     */
    private fun isTensorFlowLiteAvailable(): Boolean =
        try {
            // TODO: 检查TensorFlow Lite依赖是否存在
            // Class.forName("org.tensorflow.lite.Interpreter")
            // true
            true // 默认假设TFLite可用
        } catch (e: Exception) {
            false
        }

    /**
     * 判断是否为高性能设备
     */
    private fun hasHighPerformanceDevice(): Boolean =
        @Suppress("ktlint:standard:no-consecutive-comments")
        try {
            // TODO: 基于设备信息判断性能等级
            /*
            val performanceClass = Build.VERSION.SDK_INT >= Build.VERSION_CODES.S &&
                    Build.VERSION.PERFORMANCE_CLASS >= Build.VERSION_CODES.S
            val totalMemory = Runtime.getRuntime().maxMemory()
            val processorCount = Runtime.getRuntime().availableProcessors()

            performanceClass || (totalMemory > 2L * 1024 * 1024 * 1024 && processorCount >= 8)
             */
            true // 暂时默认为高性能设备
        } catch (e: Exception) {
            false
        }

    /**
     * 获取推荐的引擎配置
     *
     * @param isLowMemoryDevice 是否为低内存设备
     * @return 推荐配置
     */
    fun getRecommendedConfig(isLowMemoryDevice: Boolean = false): EngineConfig =
        if (isLowMemoryDevice) {
            EngineConfig(
                engineType = EngineType.TENSORFLOW_LITE,
                maxSequenceLength = 256, // 降低序列长度
                embeddingDim = 384,
                enableOptimization = true,
                threadCount = 2, // 减少线程数
            )
        } else {
            EngineConfig(
                engineType = EngineType.AUTO,
                maxSequenceLength = BgeModelConfig.maxSequenceLength, // 🔥 使用统一配置
                embeddingDim = BgeModelConfig.embeddingDim, // 🔥 使用统一配置
                enableOptimization = true,
                threadCount = 4,
            )
        }

    /**
     * 验证模型文件
     *
     * @param modelStream 模型文件流
     * @return 验证结果
     */
    fun validateModel(modelStream: InputStream): ValidationResult =
        @Suppress("ktlint:standard:no-consecutive-comments")
        try {
            // TODO: 实现模型文件验证逻辑
            /*
            val header = ByteArray(8)
            modelStream.read(header)

            val isValidTfLite = header.contentEquals("TfLite".toByteArray())
            val isValidOnnx = header.contentEquals("ONNX".toByteArray())

            when {
                isValidTfLite -> ValidationResult.VALID_TFLITE
                isValidOnnx -> ValidationResult.VALID_ONNX
                else -> ValidationResult.INVALID
            }
             */
            ValidationResult.VALID_TFLITE // 暂时默认有效
        } catch (e: Exception) {
            ValidationResult.INVALID
        }

    /**
     * 模型验证结果
     */
    enum class ValidationResult {
        VALID_TFLITE,
        VALID_ONNX,
        INVALID,
    }
}
