package com.example.gymbro.designSystem.components.smart

import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalContext
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

/**
 * SmartInput状态管理系统
 * 提供自动保存、验证状态管理、恢复等智能功能
 */
@Composable
fun rememberSmartInputState(
    config: SmartInputConfig = SmartInputConfig(),
): SmartInputState {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()

    return remember(config) {
        SmartInputState(
            config = config,
            coroutineScope = coroutineScope,
            dataStore = if (config.autoSave != null) context.dataStore else null,
        )
    }
}

/**
 * SmartInput状态管理器
 */
class SmartInputState internal constructor(
    private val config: SmartInputConfig,
    private val coroutineScope: CoroutineScope,
    private val dataStore: DataStore<Preferences>?,
) {

    // 验证状态
    private val _validationErrors = mutableStateOf<List<ValidationError>>(emptyList())
    val validationErrors: State<List<ValidationError>> = _validationErrors

    private val _isValid = mutableStateOf(true)
    val isValid: State<Boolean> = _isValid

    // 建议状态
    private val _suggestions = mutableStateOf<List<String>>(emptyList())
    val suggestions: State<List<String>> = _suggestions

    // 焦点状态
    private val _isFocused = mutableStateOf(false)
    val isFocused: State<Boolean> = _isFocused

    /**
     * 设置焦点状态
     */
    fun setFocused(focused: Boolean) {
        _isFocused.value = focused
    }

    /**
     * 设置自动保存
     */
    @OptIn(FlowPreview::class)
    fun setupAutoSave(textFlow: Flow<String>) {
        val autoSaveConfig = config.autoSave
        if (autoSaveConfig == null || dataStore == null) return

        textFlow
            .debounce(autoSaveConfig.debounceMs)
            .distinctUntilChanged()
            .onEach { text ->
                if (autoSaveConfig.enabled) {
                    saveToDataStore(autoSaveConfig.key, text)
                }
            }
            .launchIn(coroutineScope)
    }

    /**
     * 恢复保存的内容
     */
    suspend fun restoreFromAutoSave(): String {
        val autoSaveConfig = config.autoSave
        if (autoSaveConfig == null || dataStore == null) return ""

        return try {
            val preferences = dataStore.data.first()
            preferences[stringPreferencesKey(autoSaveConfig.key)] ?: ""
        } catch (e: Exception) {
            ""
        }
    }

    /**
     * 验证输入内容
     */
    fun validateInput(text: String) {
        val validationConfig = config.validation ?: return

        val errors = validationConfig.rules.mapNotNull { rule ->
            rule.validate(text)
        }

        _validationErrors.value = errors
        _isValid.value = errors.isEmpty()
    }

    /**
     * 更新建议列表
     */
    fun updateSuggestions(newSuggestions: List<String>) {
        val suggestionsConfig = config.suggestions ?: return

        if (suggestionsConfig.enabled) {
            _suggestions.value = newSuggestions.take(suggestionsConfig.maxVisible)
        }
    }

    /**
     * 清除验证错误
     */
    fun clearValidationErrors() {
        _validationErrors.value = emptyList()
        _isValid.value = true
    }

    /**
     * 清除保存的内容
     */
    fun clearAutoSave() {
        val autoSaveConfig = config.autoSave ?: return
        if (dataStore == null) return

        coroutineScope.launch {
            try {
                dataStore?.edit { preferences ->
                    preferences -= stringPreferencesKey(autoSaveConfig.key)
                }
            } catch (e: Exception) {
                // 静默处理，不影响用户体验
            }
        }
    }

    /**
     * 保存到DataStore
     */
    private suspend fun saveToDataStore(key: String, text: String) {
        try {
            dataStore?.edit { preferences ->
                preferences[stringPreferencesKey(key)] = text
            }
        } catch (e: Exception) {
            // 静默处理，不影响用户体验
        }
    }
}

/**
 * DataStore扩展，为Context提供preferences数据存储
 */
private val android.content.Context.dataStore: DataStore<Preferences> by preferencesDataStore(
    name = "smart_input_preferences",
)
