package com.example.gymbro.core.util

/**
 * 通用验证工具类
 * 提供各种常见数据验证功能
 */
object ValidationUtils {
    /**
     * 邮箱格式的正则表达式
     */
    private val EMAIL_REGEX =
        Regex(
            "[a-zA-Z0-9+._%\\-]{1,256}" +
                "@" +
                "[a-zA-Z0-9][a-zA-Z0-9\\-]{0,64}" +
                "(" +
                "\\." +
                "[a-zA-Z0-9][a-zA-Z0-9\\-]{0,25}" +
                ")+",
        )

    /**
     * 简化的国际手机号格式正则表达式（包括可选的国家代码）
     */
    private val PHONE_REGEX = Regex("^\\+?[0-9]{6,15}$")

    /**
     * 中国手机号正则表达式（更严格的匹配）
     */
    private val CHINA_PHONE_REGEX = Regex("^(\\+?86)?1[3-9]\\d{9}$")

    /**
     * 用户名正则表达式（英文字母、数字和下划线，长度6-20）
     */
    private val USERNAME_REGEX = Regex("^[a-zA-Z0-9_]{6,20}$")

    /**
     * 验证邮箱格式是否有效
     *
     * @param email 要验证的邮箱地址
     * @return 如果邮箱格式有效返回true，否则返回false
     */
    fun isValidEmail(email: String?): Boolean {
        if (email.isNullOrBlank()) return false

        // 基本格式检查
        if (!email.contains("@") || email.count { it == '@' } != 1) return false

        val parts = email.split("@")
        if (parts.size != 2) return false

        val localPart = parts[0]
        val domainPart = parts[1]

        // 验证本地部分
        if (!isValidLocalPart(localPart)) return false

        // 验证域名部分
        if (!isValidDomainPart(domainPart)) return false

        return true
    }

    /**
     * 验证邮箱本地部分（@之前的部分）
     */
    private fun isValidLocalPart(localPart: String): Boolean {
        // 长度检查（RFC 5321限制为64字符）
        if (localPart.isEmpty() || localPart.length > 64) return false

        // 不能以点开头或结尾
        if (localPart.startsWith(".") || localPart.endsWith(".")) return false

        // 不能有连续的点
        if (localPart.contains("..")) return false

        // 不能包含空格
        if (localPart.contains(" ")) return false

        // 允许的字符：字母、数字、点、下划线、加号、百分号、连字符
        val allowedChars = Regex("[a-zA-Z0-9._%+-]+")
        return allowedChars.matches(localPart)
    }

    /**
     * 验证邮箱域名部分（@之后的部分）
     */
    private fun isValidDomainPart(domainPart: String): Boolean {
        // 长度检查
        if (domainPart.isEmpty() || domainPart.length > 253) return false

        // 不能以点开头或结尾
        if (domainPart.startsWith(".") || domainPart.endsWith(".")) return false

        // 不能有连续的点
        if (domainPart.contains("..")) return false

        // 不能包含空格
        if (domainPart.contains(" ")) return false

        // 检查是否为IP格式（包括无效的IP形式，如123.456.789.123）
        if (looksLikeIPAddress(domainPart)) return false

        // 必须包含至少一个点（顶级域名）
        if (!domainPart.contains(".")) return false

        // 分割域名各部分
        val domainParts = domainPart.split(".")
        if (domainParts.size < 2) return false

        // 检查每个域名部分
        for (part in domainParts) {
            if (part.isEmpty() || part.length > 63) return false

            // 不能以连字符开头或结尾
            if (part.startsWith("-") || part.endsWith("-")) return false

            // 只允许字母、数字和连字符
            val allowedChars = Regex("[a-zA-Z0-9-]+")
            if (!allowedChars.matches(part)) return false
        }

        // 顶级域名必须至少2个字符
        val topLevelDomain = domainParts.last()
        if (topLevelDomain.length < 2) return false

        return true
    }

    /**
     * 检查字符串是否看起来像IP地址格式
     * 注意：此方法检查字符串是否格式上类似IP地址，不验证其有效性
     */
    private fun looksLikeIPAddress(domain: String): Boolean {
        // 简单的IPv4外观检查 - 只检查格式，不验证有效性
        val ipv4Pattern = Regex("^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}$")
        if (ipv4Pattern.matches(domain)) {
            return true
        }

        // IPv6格式检查
        if (domain.startsWith("[") && domain.endsWith("]")) return true

        return false
    }

    /**
     * 检查是否为有效的IP地址
     * 此方法不仅检查格式，还验证IP地址的有效性
     */
    private fun isIPAddress(domain: String): Boolean {
        // 简单的IPv4检查
        val ipv4Pattern = Regex("^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}$")
        if (ipv4Pattern.matches(domain)) {
            // 检查每个段是否在0-255范围内
            val parts = domain.split(".")
            return parts.all { part ->
                val num = part.toIntOrNull()
                num != null && num in 0..255
            }
        }

        // 简单的IPv6检查（我们不支持复杂的IPv6格式）
        if (domain.startsWith("[") && domain.endsWith("]")) return true

        return false
    }
}
