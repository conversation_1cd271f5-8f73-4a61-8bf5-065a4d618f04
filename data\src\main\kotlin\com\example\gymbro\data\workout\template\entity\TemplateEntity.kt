package com.example.gymbro.data.workout.template.entity

import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.example.gymbro.data.workout.template.converter.TemplateTypeConverters

/**
 * 训练模板实体 - TemplateDB 核心实体 v30
 *
 * 基于 05_Final_Database_Architecture.md 设计
 * 存储用户自定义的训练模板配置
 *
 * 🔥 v30重构：添加完整的版本控制字段，支持双表架构
 */
@Entity(
    tableName = "workout_templates",
    indices = [
        Index("userId"),
        Index("isPublic"),
        Index("isFavorite"),
        Index("createdAt"),
        Index("updatedAt"),
        // 🔥 v30新增：版本控制字段索引
        Index("isDraft"),
        Index("isPublished"),
        Index("currentVersion"),
    ],
)
@TypeConverters(TemplateTypeConverters::class)
data class TemplateEntity(
    @PrimaryKey
    val id: String,

    // 基本信息
    val name: String,
    val description: String?,
    val targetMuscleGroups: List<String>, // 目标肌群列表
    val difficulty: Int, // 1-5 难度等级
    val estimatedDuration: Int?, // 预计时长（分钟）

    // 用户信息
    val userId: String,

    // 状态标识
    val isPublic: Boolean = false, // 是否公开
    val isFavorite: Boolean = false, // 是否收藏
    val tags: List<String> = emptyList(), // 标签列表
    // 🔥 v30新增：完整的版本控制字段 (Phase1双表架构)
    /**
     * 当前版本号
     */
    val currentVersion: Int = 1,
    /**
     * 是否为草稿状态
     * true = 草稿，显示在Drafts tab
     * false = 正式模板，显示在Templates tab
     */
    val isDraft: Boolean = true,
    /**
     * 是否已发布
     */
    val isPublished: Boolean = false,
    /**
     * 最后发布时间
     */
    val lastPublishedAt: Long? = null,
    // 版本控制（乐观锁）
    val versionTag: Int = 1, // 乐观锁版本号
    // JSON存储（扩展字段）
    val jsonData: String? = null, // 扩展数据存储
    // 时间戳
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
)
