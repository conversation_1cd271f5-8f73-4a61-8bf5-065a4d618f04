package com.example.gymbro.designSystem.theme.tokens

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.graphics.Color
import com.example.gymbro.designSystem.theme.LocalDarkTheme
import com.example.gymbro.designSystem.theme.rememberThemeConfig

/**
 * Profile模块专属颜色系统 v4.2
 * 基于UI统一4.0规范，提供Profile模块的专属颜色定义
 * 支持动态主题和多风格切换
 */

// === CompositionLocal定义 ===
val LocalProfileColors =
    staticCompositionLocalOf {
        ProfileColors.defaultColors()
    }

// === MaterialTheme扩展 ===
val MaterialTheme.profileColors: ProfileColors
    @Composable
    get() {
        val isDark = LocalDarkTheme.current
        val themeConfig = rememberThemeConfig()
        return when (themeConfig.themeStyle) {
            com.example.gymbro.core.theme.ThemeStyle.GROK -> ProfileColors.createGrokTheme(isDark)
            com.example.gymbro.core.theme.ThemeStyle.CHATGPT -> ProfileColors.createChatGPTTheme(isDark)
            com.example.gymbro.core.theme.ThemeStyle.DEEPSEEK -> ProfileColors.createDeepSeekTheme(isDark)
        }
    }

val MaterialTheme.workoutColors: ExtendedWorkoutColors
    @Composable
    get() {
        val isDark = LocalDarkTheme.current
        val themeConfig = rememberThemeConfig()
        return when (themeConfig.themeStyle) {
            com.example.gymbro.core.theme.ThemeStyle.GROK -> ExtendedWorkoutColors.createGrokTheme(isDark)
            com.example.gymbro.core.theme.ThemeStyle.CHATGPT -> ExtendedWorkoutColors.createChatGPTTheme(
                isDark,
            )
            com.example.gymbro.core.theme.ThemeStyle.DEEPSEEK -> ExtendedWorkoutColors.createDeepSeekTheme(
                isDark,
            )
        }
    }

// === Profile模块颜色类定义 ===
@Stable
@Immutable
data class ProfileColors(
    val avatarBackground: Color,
    val avatarBorder: Color,
    val settingItemBackground: Color,
    val settingItemBorder: Color,
    val badgeBackground: Color,
    val badgeText: Color,
) {
    companion object {
        /**
         * 创建 Grok 主题风格的 Profile 颜色方案
         */
        fun createGrokTheme(isDark: Boolean): ProfileColors = ProfileColors(
            avatarBackground = if (isDark) Color(0xFF2A2A2A) else Color(0xFFF5F5F5),
            avatarBorder = if (isDark) Color(0xFF3A3A3A) else Color(0xFFE0E0E0),
            settingItemBackground = if (isDark) Color(0xFF1A1A1A) else Color(0xFFFFFFFF),
            settingItemBorder = if (isDark) Color(0xFF3A3A3A) else Color(0xFFE0E0E0),
            badgeBackground = Color(0xFF3F6CF3), // Grok 蓝色
            badgeText = Color(0xFFFFFFFF),
        )

        /**
         * 创建 ChatGPT 主题风格的 Profile 颜色方案
         */
        fun createChatGPTTheme(isDark: Boolean): ProfileColors = ProfileColors(
            avatarBackground = if (isDark) Color(0xFF343434) else Color(0xFFF8F9FA),
            avatarBorder = if (isDark) Color(0xFF4A5568) else Color(0xFFE5E7EB),
            settingItemBackground = if (isDark) Color(0xFF212121) else Color(0xFFFFFFFF),
            settingItemBorder = if (isDark) Color(0xFF4A5568) else Color(0xFFE5E7EB),
            badgeBackground = Color(0xFF00BC80), // ChatGPT 绿色
            badgeText = Color(0xFFFFFFFF),
        )

        /**
         * 创建 DeepSeek 主题风格的 Profile 颜色方案
         */
        fun createDeepSeekTheme(isDark: Boolean): ProfileColors = ProfileColors(
            avatarBackground = if (isDark) Color(0xFF343434) else Color(0xFFF0F4F9),
            avatarBorder = if (isDark) Color(0xFF3C4043) else Color(0xFFD8E2ED),
            settingItemBackground = if (isDark) Color(0xFF1E1E1E) else Color(0xFFFFFFFF),
            settingItemBorder = if (isDark) Color(0xFF3C4043) else Color(0xFFD8E2ED),
            badgeBackground = Color(0xFF4285F4), // DeepSeek 蓝色
            badgeText = Color(0xFFFFFFFF),
        )

        /**
         * 默认颜色方案（向后兼容）
         */
        fun defaultColors(): ProfileColors = createGrokTheme(isDark = false)
    }
}
