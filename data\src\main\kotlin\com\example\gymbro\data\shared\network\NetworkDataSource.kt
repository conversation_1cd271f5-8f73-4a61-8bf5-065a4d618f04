package com.example.gymbro.data.shared.network

import com.example.gymbro.core.error.types.ModernResult

/**
 * 网络数据源接口
 *
 * 用于从网络获取各种类型的数据。
 */
interface NetworkDataSource {
    /**
     * 获取文本数据
     * @param url 要获取的URL
     * @return 包含文本内容的ModernResult
     */
    suspend fun fetchText(url: String): ModernResult<String>

    /**
     * 获取二进制数据
     * @param url 要获取的URL
     * @return 包含二进制数据的ModernResult
     */
    suspend fun fetchBytes(url: String): ModernResult<ByteArray>
}
