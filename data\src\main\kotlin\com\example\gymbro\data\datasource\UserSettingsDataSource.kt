package com.example.gymbro.data.datasource

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.profile.model.user.settings.UserSettings

/**
 * 用户设置数据源接口
 * 专注于用户设置和偏好数据的管理
 */
interface UserSettingsDataSource {
    // === 用户设置功能 ===

    /**
     * 获取用户设置
     *
     * @param userId 用户ID
     * @return 包含用户设置对象或错误的ModernResult
     */
    suspend fun getUserSettings(userId: String): ModernResult<UserSettings?>

    /**
     * 保存用户设置
     *
     * @param userId 用户ID
     * @param settings 用户设置对象
     * @return 包含操作结果或错误的ModernResult
     */
    suspend fun saveUserSettings(
        userId: String,
        settings: UserSettings,
    ): ModernResult<Unit>

    /**
     * 更新用户设置的特定字段。
     *
     * @param userId 用户的唯一标识符。
     * @param fieldPath 要更新的字段路径。
     * @param value 字段的新值。
     * @return 表示操作结果的[ModernResult]。
     */
    suspend fun updateUserSettingField(
        userId: String,
        fieldPath: String,
        value: Any,
    ): ModernResult<Unit>

    // === 用户偏好数据功能 ===

    /**
     * 保存用户偏好数据。
     *
     * @param userId 用户的唯一标识符。
     * @param userPreferences 要保存的用户偏好数据。
     * @return 表示操作结果的[ModernResult]。
     */
    suspend fun saveUserData(
        userId: String,
        userPreferences: UserSettings,
    ): ModernResult<Unit>

    /**
     * 获取用户偏好数据。
     *
     * @param userId 用户的唯一标识符。
     * @return 包含用户偏好数据或null的[ModernResult]。
     */
    suspend fun getUserData(userId: String): ModernResult<UserSettings?>
}
