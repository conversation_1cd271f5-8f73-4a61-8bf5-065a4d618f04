package com.example.gymbro.core.ml.embedding

import android.content.Context
import com.example.gymbro.core.di.qualifiers.DefaultDispatcher
import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.ml.config.BgeModelConfig
import com.example.gymbro.core.ml.monitor.BgeMemoryMonitor
import com.example.gymbro.core.ml.tokenizer.BgeTokenizer
import com.example.gymbro.core.ml.utils.VectorUtils
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import org.tensorflow.lite.Interpreter
import timber.log.Timber
import java.io.InputStream
import java.nio.ByteBuffer
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.min

/**
 * BGE引擎初始化状态
 */
enum class EngineStatus {
    UNINITIALIZED,
    INITIALIZING,
    READY,
    ERROR,
}

/**
 * BGE模型嵌入引擎实现 - 异步初始化版本
 *
 * 使用TensorFlow Lite进行本地推理，支持中英文文本向量化
 * 修复主线程阻塞问题，支持异步初始化和状态监听
 */
@Singleton // 🔥 确保全局只有一个实例
class BgeEmbeddingEngine @Inject constructor(
    @ApplicationContext private val context: android.content.Context,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
    private val memoryMonitor: BgeMemoryMonitor,
    private val hardwareAccelerationManager: com.example.gymbro.core.ml.hardware.HardwareAccelerationManager,
) : EmbeddingEngine {

    // 🔥 使用ThreadLocal实现真正的并行推理，而不是单个共享实例
    private var sharedModelBuffer: ByteBuffer? = null
    private var sharedInterpreterOptions: Interpreter.Options? = null
    private var tokenizer: BgeTokenizer? = null

    // 🔥 ThreadLocal Interpreter - 每个线程都有自己的Interpreter实例
    private val interpreterThreadLocal = object : ThreadLocal<Interpreter?>() {
        override fun initialValue(): Interpreter? {
            return try {
                // 只有在模型已加载后才创建线程本地实例
                val modelBuffer = sharedModelBuffer ?: return null
                val options = sharedInterpreterOptions ?: return null

                Timber.d("🔥 为线程 ${Thread.currentThread().name} 创建新的TFLite Interpreter")
                Interpreter(modelBuffer, options)
            } catch (e: Exception) {
                Timber.e(e, "为线程 ${Thread.currentThread().name} 创建TFLite Interpreter失败")
                null
            }
        }
    }

    // 🔥 引擎状态管理
    private val _status = MutableStateFlow(EngineStatus.UNINITIALIZED)
    val status: StateFlow<EngineStatus> = _status.asStateFlow()

    // 🔥 懒加载状态管理 - 移除isModelLoaded，使用_status统一管理
    private val initializationMutex = Mutex()

    // 🔥 硬件加速配置 - TASK-005新增
    private var hardwareAccelerationConfig:
        com.example.gymbro.core.ml.hardware.HardwareAccelerationManager.AccelerationConfig? = null

    // 🔥 内存池设计 - 减少GC压力
    private val inputBufferPool = ArrayDeque<Array<IntArray>>()
    private val outputBufferPool = ArrayDeque<Array<FloatArray>>()
    private val maxPoolSize = 3 // 限制池大小，避免过度缓存

    // 🔥 BGE功能开关 - 内存优化版本重新启用
    private val enableBge = true

    // 🔥 使用统一配置，而不是通过构造函数注入
    override val maxSequenceLength: Int = BgeModelConfig.maxSequenceLength
    override val embeddingDim: Int = BgeModelConfig.embeddingDim

    // 🔥 缓存内存检查结果，避免重复计算
    private var lastMemoryCheckTime = 0L
    private var lastMemoryCheckResult = true
    private val memoryCheckCacheMs = 5000L // 5秒缓存

    /**
     * 🔥 内存池管理方法 - 获取或创建输入缓冲区
     */
    @Synchronized
    private fun getInputBuffer(batchSize: Int, seqLength: Int): Array<IntArray> {
        return inputBufferPool.removeFirstOrNull() ?: Array(batchSize) { IntArray(seqLength) }
    }

    /**
     * 🔥 内存池管理方法 - 获取或创建输出缓冲区
     */
    @Synchronized
    private fun getOutputBuffer(batchSize: Int, embeddingDim: Int): Array<FloatArray> {
        return outputBufferPool.removeFirstOrNull() ?: Array(batchSize) { FloatArray(embeddingDim) }
    }

    /**
     * 🔥 内存池管理方法 - 回收缓冲区到池中
     */
    private fun recycleBuffers(
        inputBuffer: Array<IntArray>,
        outputBuffer: Array<FloatArray>,
    ) {
        if (inputBufferPool.size < maxPoolSize) {
            // 清零数组内容，避免数据污染
            inputBuffer.forEach { it.fill(0) }
            inputBufferPool.addLast(inputBuffer)
        }

        if (outputBufferPool.size < maxPoolSize) {
            outputBuffer.forEach { it.fill(0f) }
            outputBufferPool.addLast(outputBuffer)
        }
    }

    /**
     * 🔥【核心改造】异步初始化方法。这个方法是幂等的，可以安全地重复调用。
     * 它会检查引擎状态，仅在未初始化时才执行加载。
     */
    suspend fun initialize() {
        // 如果已经就绪或正在初始化，则直接返回
        if (_status.value == EngineStatus.READY || _status.value == EngineStatus.INITIALIZING) {
            return
        }

        initializationMutex.withLock {
            // 双重检查，防止并发初始化
            if (_status.value == EngineStatus.READY || _status.value == EngineStatus.INITIALIZING) {
                return@withLock
            }

            _status.value = EngineStatus.INITIALIZING
            withContext(ioDispatcher) {
                try {
                    Timber.i("🔄 BGE模型懒加载开始...")
                    val startTime = System.currentTimeMillis()

                    Timber.i("📋 ${BgeModelConfig.getDebugInfo()}")

                    val modelStream = context.assets.open(BgeModelConfig.modelFileName)
                    val vocabStream = context.assets.open(BgeModelConfig.vocabFileName)

                    // 先初始化分词器（内存占用小）
                    tokenizer = BgeTokenizer(vocabStream, BgeModelConfig.maxSequenceLength)
                    Timber.i("✅ BGE分词器初始化成功")

                    // 再初始化模型（使用优化选项）
                    initializeModel(modelStream)
                    Timber.i("✅ BGE模型初始化成功")

                    _status.value = EngineStatus.READY

                    val loadTime = System.currentTimeMillis() - startTime
                    Timber.i("✅ BGE模型懒加载完成，耗时: ${loadTime}ms")
                } catch (e: Exception) {
                    _status.value = EngineStatus.ERROR
                    Timber.e(e, "❌ BGE模型懒加载失败")
                    throw e
                }
            }
        }
    }

    /**
     * 🔥 真实的TensorFlow Lite模型初始化 - 智能硬件加速版 (TASK-005)
     */
    private fun initializeModel(modelStream: InputStream) {
        try {
            // 1. 从 assets 加载模型文件到 ByteBuffer
            val modelByteBuffer = loadModelBuffer(modelStream)

            // 2. 🚀 创建最优硬件加速配置 - TASK-005核心功能
            hardwareAccelerationConfig = hardwareAccelerationManager.detectBestAcceleration()
            val accelerationConfig = hardwareAccelerationConfig!!

            Timber.i("🚀 应用硬件加速配置: ${hardwareAccelerationManager.getAccelerationSummary(accelerationConfig)}")

            // 3. 配置 TFLite Interpreter 选项 - LiteRT 1.3.0简化版
            val options = Interpreter.Options()

            // 🔥 使用HardwareAccelerationManager统一应用配置
            hardwareAccelerationManager.applyAccelerationConfig(options, accelerationConfig)

            // 4. 保存共享模型缓冲区和选项供ThreadLocal使用
            sharedModelBuffer = modelByteBuffer
            sharedInterpreterOptions = options

            // 5. 创建一个临时Interpreter来验证和记录模型信息
            val tempInterpreter = try {
                Interpreter(modelByteBuffer, options)
            } catch (e: Exception) {
                if (accelerationConfig.type != com.example.gymbro.core.ml.hardware.HardwareAccelerationManager.AccelerationType.CPU_ONLY) {
                    Timber.w(e, "⚠️ 硬件加速初始化失败，回退到CPU")

                    // 创建CPU-only回退配置
                    val fallbackOptions = Interpreter.Options()
                    fallbackOptions.setNumThreads(4)
                    fallbackOptions.setUseNNAPI(false)

                    // 更新共享选项为回退选项
                    sharedInterpreterOptions = fallbackOptions

                    Interpreter(modelByteBuffer, fallbackOptions)
                } else {
                    throw e
                }
            }

            // 6. 记录模型信息和加速状态
            val inputTensorCount = tempInterpreter.getInputTensorCount()
            val outputTensorCount = tempInterpreter.getOutputTensorCount()
            Timber.i("✅ TFLite Interpreter for BGE initialized successfully with acceleration!")
            Timber.i("📊 模型信息: 输入张量数=$inputTensorCount, 输出张量数=$outputTensorCount")
            Timber.i("🚀 加速状态: ${hardwareAccelerationManager.getAccelerationSummary(accelerationConfig)}")

            // 7. 关闭临时Interpreter，真正的推理将使用ThreadLocal实例
            tempInterpreter.close()
        } catch (e: Exception) {
            Timber.e(e, "❌ Failed to initialize TFLite model with hardware acceleration.")
            throw RuntimeException("Failed to initialize BGE model", e)
        }
    }

    /**
     * 🔥 加载优化模型文件到ByteBuffer - 内存优化版
     */
    private fun loadModelBuffer(modelStream: InputStream): ByteBuffer {
        return modelStream.use { stream ->
            val byteArray = stream.readBytes()

            // 🔥 使用直接内存分配，减少Java堆压力
            val byteBuffer = ByteBuffer.allocateDirect(byteArray.size)
            byteBuffer.order(java.nio.ByteOrder.LITTLE_ENDIAN)
            byteBuffer.put(byteArray)
            byteBuffer.rewind()

            Timber.i("📦 优化模型加载完成，大小: ${byteArray.size / (1024 * 1024)}MB (直接内存)")
            byteBuffer
        }
    }

    /**
     * 🔥 异步推理方法 - 自动初始化
     */
    override suspend fun embed(text: String): FloatArray = withContext(defaultDispatcher) {
        // 🔥 检查BGE功能是否启用
        if (!enableBge) {
            Timber.w("🚫 BGE功能已禁用，返回零向量")
            return@withContext FloatArray(embeddingDim) { 0f }
        }

        // 优化：只在内存检查缓存过期时才进行内存监控，避免重复计算
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastMemoryCheckTime >= memoryCheckCacheMs) {
            memoryMonitor.logMemoryStatus()
        }

        // 检查内存可用性（使用缓存）
        if (!hasEnoughMemoryForBGE()) {
            Timber.w("BGE内存不足，返回零向量")
            return@withContext FloatArray(embeddingDim) { 0f }
        }

        // 🔥 自动触发懒加载初始化（首次调用时才加载模型）
        initialize() // 会自动处理是否需要初始化

        // 再次检查，如果初始化失败则返回零向量而不是抛异常
        if (_status.value != EngineStatus.READY) {
            Timber.w("⚠️ BGE引擎未就绪，当前状态: ${_status.value}，返回零向量")
            return@withContext FloatArray(embeddingDim) { 0f }
        }

        require(text.isNotBlank()) { "输入文本不能为空" }

        // 🔥 使用统一配置的文本长度限制
        val recommendedLength = BgeModelConfig.recommendedTextLength
        val processedText = if (text.length > recommendedLength) {
            val truncatedText = text.take(recommendedLength)
            Timber.w("⚠️ 文本过长，已截断: ${text.length} -> ${truncatedText.length} (推荐长度: $recommendedLength)")
            truncatedText
        } else {
            text
        }

        try {
            // 1. 分词处理
            val currentTokenizer = tokenizer ?: throw IllegalStateException("分词器未初始化")

            // 🔍 添加输入文本调试信息
            Timber.d("🔍 BgeDebug - 输入文本长度: ${processedText.length}")
            Timber.d("🔍 BgeDebug - 输入文本内容: ${processedText.take(50)}...")

            val tokenizationResult = currentTokenizer.tokenize(processedText)

            // 🔍 添加分词结果调试信息
            Timber.d(
                "🔍 BgeDebug - Token化后长度: input_ids=${tokenizationResult.inputIds.size}, attention_mask=${tokenizationResult.attentionMask.size}",
            )
            Timber.d("🔍 BgeDebug - 前10个token IDs: ${tokenizationResult.inputIds.take(10).joinToString(",")}")

            // 🔥 强制截断到模型期望的长度 - 最后一道防线
            val modelMaxLength = BgeModelConfig.maxSequenceLength // 使用统一配置
            val truncatedInputIds = if (tokenizationResult.inputIds.size > modelMaxLength) {
                Timber.w("⚠️ Token序列过长，强制截断: ${tokenizationResult.inputIds.size} -> $modelMaxLength")
                tokenizationResult.inputIds.take(modelMaxLength).toIntArray()
            } else if (tokenizationResult.inputIds.size < modelMaxLength) {
                // 填充到正确长度
                tokenizationResult.inputIds + IntArray(modelMaxLength - tokenizationResult.inputIds.size) { 0 }
            } else {
                tokenizationResult.inputIds
            }

            val truncatedAttentionMask = if (tokenizationResult.attentionMask.size > modelMaxLength) {
                tokenizationResult.attentionMask.take(modelMaxLength).toIntArray()
            } else if (tokenizationResult.attentionMask.size < modelMaxLength) {
                // 填充到正确长度
                tokenizationResult.attentionMask + IntArray(modelMaxLength - tokenizationResult.attentionMask.size) { 0 }
            } else {
                tokenizationResult.attentionMask
            }

            Timber.d(
                "🔍 BgeDebug - 最终Token长度: input_ids=${truncatedInputIds.size}, attention_mask=${truncatedAttentionMask.size}",
            )

            // 2. 准备模型输入
            val inputIds = Array(1) { truncatedInputIds }
            val attentionMask = Array(1) { truncatedAttentionMask }

            // 3. 运行推理
            val embedding = runInference(inputIds, attentionMask)

            // 4. L2归一化
            VectorUtils.normalizeL2(embedding)
        } catch (e: Exception) {
            Timber.e(e, "BGE嵌入推理失败: ${e.message}")
            throw RuntimeException("BGE嵌入推理失败: ${e.message}", e)
        }
    }

    override suspend fun embedBatch(texts: List<String>): List<FloatArray> = withContext(
        defaultDispatcher,
    ) {
        // 🔍 内存状态监控
        memoryMonitor.logMemoryStatus()

        // 🔥 确保模型已懒加载
        initialize()

        require(_status.value == EngineStatus.READY) { "BGE引擎未就绪" }
        require(texts.isNotEmpty()) { "文本列表不能为空" }

        // 🔥 使用增强的动态批处理大小调整
        val optimalBatchSize = getOptimalBatchSize()
        Timber.d("🔍 根据内存状态调整批处理大小: $optimalBatchSize")
        val results = mutableListOf<FloatArray>()

        // 🔥 分批处理，避免内存峰值
        for (i in texts.indices step optimalBatchSize) {
            val batchEnd = min(i + optimalBatchSize, texts.size)
            val batch = texts.subList(i, batchEnd)

            // 检查内存压力，必要时触发GC
            if (shouldTriggerGC()) {
                Timber.d("🔍 内存压力较高，触发GC")
                System.gc()
                kotlinx.coroutines.delay(10) // 给GC一些时间
            }

            // 并行处理当前批次
            val batchResults = batch.map { text ->
                embed(text)
            }

            results.addAll(batchResults)
        }

        results
    }

    /**
     * 🔥 真实的模型推理实现
     *
     * @param inputIds Token ID数组
     * @param attentionMask 注意力掩码数组
     * @return 嵌入向量
     */
    private fun runInference(
        inputIds: Array<IntArray>,
        attentionMask: Array<IntArray>,
    ): FloatArray {
        // 🔥 获取当前线程的Interpreter实例
        val currentInterpreter = interpreterThreadLocal.get() ?: run {
            Timber.w("ThreadLocal Interpreter not initialized for thread ${Thread.currentThread().name}, returning zero vector.")
            return FloatArray(embeddingDim) { 0f }
        }

        try {
            // 1. 🔥 准备输入数据 - 使用内存池优化
            val batchSize = 1
            val seqLength = inputIds[0].size

            // ---- 🔥 关键调试代码 - 第二步验证实现 ----
            Timber.d("🔍 BgeDebug - TFLite Input Shape Check:")
            Timber.d("🔍 BgeDebug - input_ids shape: [${inputIds.size}, ${inputIds[0].size}]")
            Timber.d("🔍 BgeDebug - attention_mask shape: [${attentionMask.size}, ${attentionMask[0].size}]")
            Timber.d("🔍 BgeDebug - MAX_SEQUENCE_LENGTH: $maxSequenceLength")
            Timber.d("🔍 BgeDebug - EMBEDDING_DIM: $embeddingDim")

            // 检查模型输入输出张量信息
            val inputTensorCount = currentInterpreter.getInputTensorCount()
            val outputTensorCount = currentInterpreter.getOutputTensorCount()
            Timber.d("🔍 BgeDebug - Model tensor info: inputs=$inputTensorCount, outputs=$outputTensorCount")

            // 检查每个输入张量的形状
            for (i in 0 until inputTensorCount) {
                val inputTensor = currentInterpreter.getInputTensor(i)
                Timber.d("🔍 BgeDebug - Input tensor $i shape: ${inputTensor.shape().contentToString()}")
                Timber.d("🔍 BgeDebug - Input tensor $i dataType: ${inputTensor.dataType()}")
            }

            // 检查每个输出张量的形状
            for (i in 0 until outputTensorCount) {
                val outputTensor = currentInterpreter.getOutputTensor(i)
                Timber.d("🔍 BgeDebug - Output tensor $i shape: ${outputTensor.shape().contentToString()}")
                Timber.d("🔍 BgeDebug - Output tensor $i dataType: ${outputTensor.dataType()}")
            }
            // ---- 调试代码结束 ----

            // 🔥 从内存池获取缓冲区，避免重复分配
            val inputBuffer = getInputBuffer(batchSize, seqLength)
            val outputBuffer = getOutputBuffer(batchSize, embeddingDim)

            try {
                // 复制数据到池化缓冲区
                for (i in inputIds[0].indices) {
                    inputBuffer[0][i] = inputIds[0][i]
                    // 注意：这里需要第二个输入张量用于attention_mask
                }

                // 创建注意力掩码缓冲区（暂时不池化，因为维度可能不同）
                val attentionBuffer = Array(batchSize) { IntArray(seqLength) }
                for (i in attentionMask[0].indices) {
                    attentionBuffer[0][i] = attentionMask[0][i]
                }

                // 3. 🔥 执行推理 - 使用池化的缓冲区
                val inputs = arrayOf(inputBuffer, attentionBuffer)
                val outputs = mapOf(0 to outputBuffer)

                Timber.d("🔍 BgeDebug - About to run inference with pooled buffers...")
                currentInterpreter.runForMultipleInputsOutputs(inputs, outputs)
                Timber.d("🔍 BgeDebug - Inference completed successfully!")

                // 4. 复制结果，避免返回池中的引用
                val result = outputBuffer[0].copyOf()

                Timber.v("🎯 推理完成，向量维度: ${result.size}, 前3维: [${result.take(3).joinToString(", ")}]")

                return result
            } finally {
                // 🔥 关键：无论成功失败都要回收缓冲区
                recycleBuffers(inputBuffer, outputBuffer)
            }
        } catch (e: Exception) {
            Timber.e(e, "❌ TFLite inference failed.")
            // 推理失败，返回零向量而不是随机向量
            return FloatArray(embeddingDim) { 0f }
        }
    }

    /**
     * 预热模型
     *
     * 使用简短文本进行一次推理，减少首次使用的延迟
     */
    override suspend fun warmUp() {
        try {
            Timber.d("🔥 BGE 预热：执行一次简单的推理...")
            // 确保模型已加载
            initialize()
            // 执行一次真实的推理来触发JIT编译和硬件资源分配
            embed("warmup")
            Timber.d("✅ BGE 预热完成：首次推理冷启动成本已消除")
        } catch (e: Exception) {
            // 预热失败不影响主要功能
            Timber.w(e, "BGE模型预热失败: ${e.message}")
        }
    }

    /**
     * 获取模型信息
     */
    override fun getModelInfo(): ModelInfo {
        return ModelInfo(
            modelName = BgeModelConfig.modelName, // 🔥 使用统一配置
            embeddingDim = embeddingDim,
            maxSequenceLength = maxSequenceLength,
            isInitialized = (_status.value == EngineStatus.READY),
        )
    }

    /**
     * 🔥 文本摘要生成 - 富文本功能新增
     */
    override suspend fun summarize(text: String, maxLength: Int): String {
        // 简单的截取式摘要实现
        return if (text.length <= maxLength) {
            text
        } else {
            // 寻找合适的截取点（句号、问号、感叹号）
            val punctuations = listOf("。", "？", "！", ".", "?", "!")

            for (punct in punctuations) {
                val index = text.indexOf(punct)
                if (index in 5..maxLength) {
                    return text.substring(0, index + 1)
                }
            }

            // 没有合适标点，直接截取
            text.take(maxLength) + "..."
        }
    }

    /**
     * 🔥 计算两个向量的余弦相似度 - 富文本功能新增
     */
    override fun cosineSimilarity(vector1: FloatArray, vector2: FloatArray): Float {
        require(vector1.size == vector2.size) { "向量维度不匹配: ${vector1.size} vs ${vector2.size}" }

        var dotProduct = 0.0f
        var normA = 0.0f
        var normB = 0.0f

        for (i in vector1.indices) {
            dotProduct += vector1[i] * vector2[i]
            normA += vector1[i] * vector1[i]
            normB += vector2[i] * vector2[i]
        }

        normA = kotlin.math.sqrt(normA)
        normB = kotlin.math.sqrt(normB)

        return if (normA == 0.0f || normB == 0.0f) {
            0.0f
        } else {
            dotProduct / (normA * normB)
        }
    }

    override fun close() {
        try {
            // 🔥 释放ThreadLocal Interpreter资源
            interpreterThreadLocal.get()?.close()
            interpreterThreadLocal.remove()

            // 🔥 清理共享资源
            sharedModelBuffer = null
            sharedInterpreterOptions = null
            tokenizer = null

            // 🔥 释放硬件加速资源 - TASK-005新增 (LiteRT 1.3.0自动管理)
            hardwareAccelerationConfig?.let { config ->
                hardwareAccelerationManager.releaseAccelerationResources(config)
                hardwareAccelerationConfig = null
            }

            // 🔥 清理内存池
            inputBufferPool.clear()
            outputBufferPool.clear()

            // 不重置状态，以防万一有地方还在使用
            Timber.i("✅ BGE引擎资源已释放，内存池已清理，硬件加速已停止，ThreadLocal已清理")
        } catch (e: Exception) {
            Timber.w(e, "释放BGE模型资源时出错: ${e.message}")
        }
    }

    /**
     * 🔥 根据可用内存和系统指标动态调整批处理大小
     */
    private fun getOptimalBatchSize(): Int {
        val runtime = Runtime.getRuntime()
        val freeMemory = runtime.freeMemory()
        val totalMemory = runtime.totalMemory()
        val usedMemory = totalMemory - freeMemory
        val memoryUsageRatio = usedMemory.toFloat() / runtime.maxMemory()

        return when {
            memoryUsageRatio > 0.8 -> 1      // 高内存压力：单个处理
            memoryUsageRatio > 0.6 -> 2      // 中等压力：小批处理
            memoryUsageRatio > 0.4 -> 4      // 正常压力：中批处理
            else -> 8                        // 低压力：大批处理
        }.also { batchSize ->
            Timber.d("🔍 内存使用率: ${(memoryUsageRatio * 100).toInt()}%, 批处理大小: $batchSize")
        }
    }

    /**
     * 🔥 判断是否需要主动触发GC
     */
    private fun shouldTriggerGC(): Boolean {
        val runtime = Runtime.getRuntime()
        val memoryUsageRatio = (runtime.totalMemory() - runtime.freeMemory()).toFloat() / runtime.maxMemory()
        return memoryUsageRatio > 0.75
    }

    /**
     * 🔥 缓存的内存检查方法 - 避免重复计算
     */
    private fun hasEnoughMemoryForBGE(): Boolean {
        val currentTime = System.currentTimeMillis()

        // 如果缓存未过期，直接返回缓存结果
        if (currentTime - lastMemoryCheckTime < memoryCheckCacheMs) {
            return lastMemoryCheckResult
        }

        // 缓存过期，重新检查
        lastMemoryCheckResult = memoryMonitor.hasEnoughMemoryForBGE()
        lastMemoryCheckTime = currentTime

        return lastMemoryCheckResult
    }
}
