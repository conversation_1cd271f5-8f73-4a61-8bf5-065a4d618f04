package com.example.gymbro.data.mapper.auth

import com.example.gymbro.data.local.entity.auth.TokenEntity
import com.example.gymbro.data.model.auth.TokenDto

/**
 * Token数据映射扩展函数
 *
 * 简化版Token映射，直接在TokenDto和TokenEntity之间转换
 * 避免使用已删除的domain层Token类
 */

/**
 * 将DTO转换为数据库实体
 */
fun TokenDto.toEntity(): TokenEntity {
    return TokenEntity(
        accessToken = this.accessToken,
        refreshToken = this.refreshToken,
        tokenType = this.tokenType,
        expiresIn = this.expiresIn,
        issuedAt = this.issuedAt ?: System.currentTimeMillis(),
        userId = this.userId,
        scope = this.scope,
    )
}

/**
 * 将数据库实体转换为DTO
 */
fun TokenEntity.toDto(): TokenDto {
    return TokenDto(
        accessToken = this.accessToken,
        refreshToken = this.refreshToken,
        tokenType = this.tokenType,
        expiresIn = this.expiresIn,
        issuedAt = this.issuedAt,
        userId = this.userId,
        scope = this.scope,
    )
}

/**
 * 获取带有令牌类型的认证头值
 * 常用于添加到HTTP请求头中
 */
fun TokenDto.getAuthorizationHeader(): String = "$tokenType $accessToken"

/**
 * 获取带有令牌类型的认证头值
 * 常用于添加到HTTP请求头中
 */
fun TokenEntity.getAuthorizationHeader(): String = "$tokenType $accessToken"

/**
 * 判断令牌是否已过期
 * 预留30秒时间差，以避免临界值问题
 */
fun TokenDto.isExpired(): Boolean {
    val issuedTime = this.issuedAt ?: System.currentTimeMillis()
    val expiresAt = issuedTime + (this.expiresIn * 1000)
    return (System.currentTimeMillis() + 30_000) >= expiresAt
}

/**
 * 判断令牌是否已过期
 * 预留30秒时间差，以避免临界值问题
 */
fun TokenEntity.isExpired(): Boolean {
    val expiresAt = this.issuedAt + (this.expiresIn * 1000)
    return (System.currentTimeMillis() + 30_000) >= expiresAt
}
