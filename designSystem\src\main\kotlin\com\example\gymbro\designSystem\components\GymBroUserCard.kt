package com.example.gymbro.designSystem.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens

/**
 * 通用用户信息卡片组件
 *
 * 采用平面化设计，符合黑白双色基调的克制风格。
 * 支持UiText国际化，可在多个模块复用。
 *
 * @param displayName 显示名称
 * @param username 用户名（可选）
 * @param email 邮箱（可选）
 * @param bio 个人简介（可选）
 * @param avatarIcon 头像图标
 * @param onAvatarClick 头像点击回调
 * @param modifier 修饰符
 * @param showEmail 是否显示邮箱
 * @param showBio 是否显示个人简介
 * @param additionalInfo 额外信息内容
 */
@Composable
fun GymBroUserCard(
    displayName: UiText,
    modifier: Modifier = Modifier,
    username: UiText? = null,
    email: UiText? = null,
    bio: UiText? = null,
    avatarIcon: ImageVector = Icons.Default.Person,
    onAvatarClick: (() -> Unit)? = null,
    showEmail: Boolean = true,
    showBio: Boolean = true,
    additionalInfo: (@Composable ColumnScope.() -> Unit)? = null,
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = Tokens.Elevation.Card),
        shape = RoundedCornerShape(16.dp), // 美化方案：与workout模块对齐，使用16dp精细圆角
        colors =
        CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface,
        ),
    ) {
        Column(
            modifier =
            Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Large),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            // 头像区域
            Card(
                modifier =
                Modifier
                    .size(Tokens.Spacing.Huge)
                    .then(
                        if (onAvatarClick != null) {
                            Modifier.clickable { onAvatarClick() }
                        } else {
                            Modifier
                        },
                    ),
                shape = CircleShape,
                elevation = CardDefaults.cardElevation(defaultElevation = Tokens.Elevation.Card),
                colors =
                CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer,
                ),
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center,
                ) {
                    Icon(
                        imageVector = avatarIcon,
                        contentDescription = displayName.asString(),
                        modifier = Modifier.size(Tokens.Spacing.XLarge),
                        tint = MaterialTheme.colorScheme.onPrimaryContainer,
                    )
                }
            }

            // 用户信息
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
            ) {
                // 显示名称
                Text(
                    text = displayName.asString(),
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface,
                )

                // 用户名
                if (username != null) {
                    Surface(
                        color = MaterialTheme.colorScheme.primaryContainer,
                        shape = RoundedCornerShape(16.dp), // 美化方案：与workout模块对齐，使用16dp精细圆角
                    ) {
                        Text(
                            text = "@${username.asString()}",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onPrimaryContainer,
                            modifier =
                            Modifier.padding(
                                horizontal = Tokens.Spacing.Medium,
                                vertical = Tokens.Spacing.XSmall,
                            ),
                        )
                    }
                }

                // 邮箱
                if (showEmail && email != null) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
                    ) {
                        Text(
                            text = "✉️",
                            style = MaterialTheme.typography.bodyMedium,
                        )
                        Text(
                            text = email.asString(),
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                        )
                    }
                }

                // 个人简介
                if (showBio && bio != null) {
                    Surface(
                        color = MaterialTheme.colorScheme.surfaceVariant,
                        shape = RoundedCornerShape(16.dp), // 美化方案：与workout模块对齐，使用16dp精细圆角
                    ) {
                        Text(
                            text = bio.asString(),
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurface,
                            modifier = Modifier.padding(Tokens.Spacing.Medium),
                        )
                    }
                }

                // 额外信息
                additionalInfo?.invoke(this)
            }
        }
    }
}

/**
 * 紧凑型用户卡片
 *
 * 适用于列表项或较小空间的用户信息展示。
 */
@Composable
fun GymBroUserCardCompact(
    displayName: UiText,
    modifier: Modifier = Modifier,
    username: UiText? = null,
    avatarIcon: ImageVector = Icons.Default.Person,
    onAvatarClick: (() -> Unit)? = null,
    onClick: (() -> Unit)? = null,
    onDisplayNameClick: (() -> Unit)? = null,
    trailingContent: (@Composable RowScope.() -> Unit)? = null,
) {
    // API兼容层：当显示"未设置姓名"时强制要求回调
    val isNameNotSet = displayName.asString().contains("未设置")
    if (isNameNotSet) {
        requireNotNull(onDisplayNameClick) {
            "GymBroUserCardCompact: onDisplayNameClick must be provided when nameNotSet"
        }
    }
    Surface(
        modifier =
        modifier
            .fillMaxWidth()
            .then(
                if (onClick != null) {
                    Modifier.clickable { onClick() }
                } else {
                    Modifier
                },
            ),
        color = MaterialTheme.colorScheme.surface,
    ) {
        Row(
            modifier =
            Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Medium),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            // 头像 - 美化方案：添加无障碍描述
            Card(
                modifier =
                Modifier
                    .size(Tokens.Spacing.XLarge)
                    .then(
                        if (onAvatarClick != null) {
                            Modifier
                                .clickable { onAvatarClick() }
                                .semantics {
                                    contentDescription = "编辑头像" // 美化方案：无障碍描述
                                    role = androidx.compose.ui.semantics.Role.Button
                                }
                        } else {
                            Modifier
                        },
                    ),
                shape = CircleShape,
                colors =
                CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer,
                ),
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center,
                ) {
                    Icon(
                        imageVector = avatarIcon,
                        contentDescription = displayName.asString(),
                        modifier = Modifier.size(Tokens.Spacing.Large),
                        tint = MaterialTheme.colorScheme.onPrimaryContainer,
                    )
                }
            }

            // 用户信息
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.XSmall),
            ) {
                // 姓名显示，支持点击编辑 - 美化方案：添加动画效果
                val isNameNotSet = displayName.asString().contains("未设置")

                // 美化方案：未设置姓名点击动画
                val interactionSource = remember { MutableInteractionSource() }
                val isPressed by interactionSource.collectIsPressedAsState()
                val animatedTextColor by animateColorAsState(
                    targetValue =
                    if (isNameNotSet && isPressed) {
                        MaterialTheme.colorScheme.secondaryContainer
                    } else if (isNameNotSet) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.onSurface
                    },
                    animationSpec = tween(durationMillis = 150), // 150ms easeOut
                    label = "nameTextColor",
                )

                Text(
                    text = displayName.asString(),
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Medium,
                    color = animatedTextColor, // 美化方案：使用动画颜色
                    textDecoration = if (isNameNotSet) TextDecoration.Underline else null,
                    modifier =
                    Modifier.then(
                        if (onDisplayNameClick != null && isNameNotSet) {
                            Modifier
                                .clickable(
                                    interactionSource = interactionSource,
                                    indication = null, // 禁用默认波纹，使用自定义颜色动画
                                    role = androidx.compose.ui.semantics.Role.Button,
                                    onClickLabel = "编辑姓名",
                                ) { onDisplayNameClick() }
                                .semantics {
                                    contentDescription = "点击编辑您的姓名"
                                    role = androidx.compose.ui.semantics.Role.Button
                                }
                        } else {
                            Modifier
                        },
                    ),
                )

                if (username != null) {
                    Text(
                        text = "@${username.asString()}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                    )
                }
            }

            // 尾随内容
            trailingContent?.invoke(this)
        }
    }
}

// ===== Preview Functions =====

@GymBroPreview
@Composable
private fun GymBroUserCardPreview() {
    GymBroTheme {
        GymBroUserCard(
            displayName = UiText.DynamicString("张三"),
            username = UiText.DynamicString("健身达人"),
            email = UiText.DynamicString("<EMAIL>"),
            bio = UiText.DynamicString("热爱健身，追求健康生活方式"),
            onAvatarClick = {},
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroUserCardCompactPreview() {
    GymBroTheme {
        GymBroUserCardCompact(
            displayName = UiText.DynamicString("李四"),
            username = UiText.DynamicString("跑步爱好者"),
            onAvatarClick = {},
            onClick = {},
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroUserCardCompactNameNotSetPreview() {
    GymBroTheme {
        GymBroUserCardCompact(
            displayName = UiText.DynamicString("未设置姓名"),
            username = UiText.DynamicString("用户123"),
            onAvatarClick = {},
            onDisplayNameClick = { /* 点击编辑姓名 */ },
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroUserCardDarkPreview() {
    GymBroTheme(darkTheme = true) {
        Column(
            modifier = Modifier.padding(Tokens.Spacing.Medium),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            GymBroUserCard(
                displayName = UiText.DynamicString("王五"),
                username = UiText.DynamicString("夜间健身者"),
                email = UiText.DynamicString("<EMAIL>"),
                bio = UiText.DynamicString("夜间训练爱好者，专注力量训练"),
                onAvatarClick = {},
            )
        }
    }
}
