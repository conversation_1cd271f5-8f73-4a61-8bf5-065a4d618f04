package com.example.gymbro.core.network

import kotlinx.coroutines.test.runTest
import okhttp3.OkHttpClient
import okhttp3.Protocol
import okhttp3.Request
import org.junit.Assert.*
import org.junit.Test

/**
 * HTTP协议版本修复验证测试
 *
 * 验证OkHttpClient是否正确限制为HTTP/1.1协议，
 * 避免HTTP/3协议协商问题导致的连接失败。
 */
class HttpProtocolTest {
    @Test
    fun `验证OkHttpClient协议配置`() {
        // 创建与CoreNetworkModule相同的配置
        val client =
            OkHttpClient
                .Builder()
                .protocols(listOf(Protocol.HTTP_1_1))
                .build()

        // 验证协议配置
        val protocols = client.protocols
        assertEquals("应该只支持HTTP/1.1", 1, protocols.size)
        assertEquals("应该是HTTP/1.1协议", Protocol.HTTP_1_1, protocols[0])
    }

    @Test
    fun `验证不支持HTTP2和HTTP3`() {
        val client =
            OkHttpClient
                .Builder()
                .protocols(listOf(Protocol.HTTP_1_1))
                .build()

        val protocols = client.protocols

        // 确保不包含HTTP/2和HTTP/3
        assertFalse("不应该支持HTTP/2", protocols.contains(Protocol.HTTP_2))
        // 注意：Protocol.HTTP_3可能在某些OkHttp版本中不存在
        // assertFalse("不应该支持HTTP/3", protocols.contains(Protocol.HTTP_3))
    }

    @Test
    fun `验证网络请求构建`() =
        runTest {
            val client =
                OkHttpClient
                    .Builder()
                    .protocols(listOf(Protocol.HTTP_1_1))
                    .build()

            val request =
                Request
                    .Builder()
                    .url("https://httpbin.org/get")
                    .build()

            // 验证请求可以正常构建
            assertNotNull("请求应该能正常构建", request)
            assertEquals("URL应该正确", "https://httpbin.org/get", request.url.toString())
        }

    @Test
    fun `验证协议降级策略`() {
        // 测试默认协议配置（应该包含多种协议）
        val defaultClient = OkHttpClient.Builder().build()
        val defaultProtocols = defaultClient.protocols

        // 默认应该支持多种协议
        assertTrue("默认配置应该支持多种协议", defaultProtocols.size > 1)

        // 我们的修复版本应该只支持HTTP/1.1
        val fixedClient =
            OkHttpClient
                .Builder()
                .protocols(listOf(Protocol.HTTP_1_1))
                .build()
        val fixedProtocols = fixedClient.protocols

        assertEquals("修复版本应该只支持HTTP/1.1", 1, fixedProtocols.size)
        assertEquals("应该是HTTP/1.1", Protocol.HTTP_1_1, fixedProtocols[0])
    }
}
