package com.example.gymbro.data.memory.repository

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.extensions.safeCatch
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ml.service.BgeEngineManager
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.memory.cache.EcmMemoryCache
import com.example.gymbro.data.memory.dao.MemoryRecordDao
import com.example.gymbro.data.memory.entity.MemoryRecordEntity
import com.example.gymbro.data.memory.entity.toMemoryByteArray
import com.example.gymbro.data.memory.mapper.MemoryRecordMapper
import com.example.gymbro.domain.memory.repository.MemoryRepository
import com.example.gymbro.domain.memory.repository.MemoryStats
import com.example.gymbro.shared.models.memory.MemoryQuery
import com.example.gymbro.shared.models.memory.MemoryRecord
import com.example.gymbro.shared.models.memory.MemorySearchResult
import com.example.gymbro.shared.models.memory.MemoryTier
import com.example.gymbro.shared.models.memory.calculateCosineSimilarity
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 记忆仓库实现
 *
 * 基于Memory System四层金字塔设计，整合：
 * - ECM: 内存缓存 (EcmMemoryCache)
 * - DWM/UPM/GIM: 数据库存储 (Room + BGE向量)
 *
 * 遵循GymBro data层标准模式，使用ModernResult错误处理
 */
@Singleton
class MemoryRepositoryImpl @Inject constructor(
    private val memoryRecordDao: MemoryRecordDao,
    private val ecmMemoryCache: EcmMemoryCache,
    private val bgeEngineManager: BgeEngineManager,
    private val logger: Logger,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
) : MemoryRepository {

    companion object {
        private const val DEFAULT_SEARCH_LIMIT = 1000
        private const val MIN_SIMILARITY_THRESHOLD = 0.3f
    }

    /**
     * 保存记忆记录
     * 根据记忆层级选择不同的存储策略
     */
    override suspend fun saveMemory(record: MemoryRecord): ModernResult<Unit> =
        withContext(ioDispatcher) {
            safeCatch {
                logger.d("MemoryRepositoryImpl", "保存记忆: tier=${record.tier}, id=${record.id}")

                when (record.tier) {
                    MemoryTier.ECM -> {
                        // ECM记忆存储到内存缓存
                        ecmMemoryCache.addMemory(record.userId, record)
                        logger.d("MemoryRepositoryImpl", "ECM记忆已保存到缓存")
                    }

                    MemoryTier.DWM, MemoryTier.UPM, MemoryTier.GIM -> {
                        // 其他层级记忆存储到数据库
                        val entity = MemoryRecordMapper.toEntity(record)

                        // 如果有文本内容且需要向量化，生成嵌入向量
                        val finalEntity = if (shouldGenerateEmbedding(record)) {
                            generateEmbeddingForRecord(entity)
                        } else {
                            entity
                        }

                        memoryRecordDao.insertMemory(finalEntity)
                        logger.d("MemoryRepositoryImpl", "记忆已保存到数据库: ${record.tier}")
                    }
                }
            }
        }

    /**
     * 批量保存记忆记录
     */
    override suspend fun saveMemories(records: List<MemoryRecord>): ModernResult<Unit> =
        withContext(ioDispatcher) {
            safeCatch {
                logger.d("MemoryRepositoryImpl", "批量保存记忆: ${records.size}条")

                // 分组处理不同层级的记忆
                val ecmRecords = records.filter { it.tier == MemoryTier.ECM }
                val dbRecords = records.filter { it.tier != MemoryTier.ECM }

                // 处理ECM记忆
                ecmRecords.forEach { record ->
                    ecmMemoryCache.addMemory(record.userId, record)
                }

                // 处理数据库记忆
                if (dbRecords.isNotEmpty()) {
                    val entities = mutableListOf<MemoryRecordEntity>()
                    for (record in dbRecords) {
                        val entity = MemoryRecordMapper.toEntity(record)
                        val finalEntity = if (shouldGenerateEmbedding(record)) {
                            generateEmbeddingForRecord(entity)
                        } else {
                            entity
                        }
                        entities.add(finalEntity)
                    }
                    memoryRecordDao.insertMemories(entities)
                }

                logger.d("MemoryRepositoryImpl", "批量保存完成: ECM=${ecmRecords.size}, DB=${dbRecords.size}")
            }
        }

    /**
     * 根据ID获取记忆记录
     */
    override suspend fun getMemoryById(memoryId: String): ModernResult<MemoryRecord?> =
        withContext(ioDispatcher) {
            safeCatch {
                val entity = memoryRecordDao.getMemoryById(memoryId)
                entity?.let { MemoryRecordMapper.toDomain(it) }
            }
        }

    /**
     * 根据查询参数召回记忆
     * 实现分层召回逻辑：ECM + DWM + UPM + GIM
     */
    override suspend fun recallMemories(query: MemoryQuery): ModernResult<List<MemorySearchResult>> =
        withContext(ioDispatcher) {
            safeCatch {
                logger.d("MemoryRepositoryImpl", "开始记忆召回: query=${query.query}, tiers=${query.tiers}")

                val allResults = mutableListOf<MemorySearchResult>()
                val userId = extractUserIdFromQuery(query.query) // 从查询中提取用户ID

                // 处理各个记忆层级
                for (tier in query.tiers) {
                    val tierResults = when (tier) {
                        MemoryTier.ECM -> recallEcmMemories(userId, query)
                        MemoryTier.DWM -> recallDwmMemories(userId, query)
                        MemoryTier.UPM -> recallUpmMemories(userId, query)
                        MemoryTier.GIM -> recallGimMemories(userId, query)
                    }
                    allResults.addAll(tierResults)
                }

                // 按相似度和重要性排序
                val sortedResults = allResults
                    .filter { it.similarity >= query.minSimilarity }
                    .sortedWith(
                        compareByDescending<MemorySearchResult> { it.memory.importance }
                            .thenByDescending { it.similarity },
                    )
                    .take(query.maxResults)

                logger.d("MemoryRepositoryImpl", "记忆召回完成: 返回${sortedResults.size}条结果")
                sortedResults
            }
        }

    /**
     * 根据用户ID和记忆层级获取记忆
     */
    override suspend fun getMemoriesByTier(
        userId: String,
        tier: MemoryTier,
        limit: Int,
    ): ModernResult<List<MemoryRecord>> =
        withContext(ioDispatcher) {
            safeCatch {
                when (tier) {
                    MemoryTier.ECM -> {
                        ecmMemoryCache.getMemories(userId, limit)
                    }
                    else -> {
                        val entities = memoryRecordDao.getMemoriesByTier(userId, tier.name, limit)
                        entities.map { MemoryRecordMapper.toDomain(it) }
                    }
                }
            }
        }

    /**
     * 根据用户ID和时间范围获取记忆
     */
    override suspend fun getMemoriesByTimeRange(
        userId: String,
        startTime: Long,
        endTime: Long,
        tiers: List<MemoryTier>?,
    ): ModernResult<List<MemoryRecord>> =
        withContext(ioDispatcher) {
            safeCatch {
                val entities = memoryRecordDao.getMemoriesByTimeRange(userId, startTime, endTime)
                val filteredEntities = if (tiers != null) {
                    entities.filter { entity -> tiers.any { it.name == entity.tier } }
                } else {
                    entities
                }
                filteredEntities.map { MemoryRecordMapper.toDomain(it) }
            }
        }

    /**
     * 删除记忆记录
     */
    override suspend fun deleteMemory(memoryId: String): ModernResult<Unit> =
        withContext(ioDispatcher) {
            safeCatch {
                // 先尝试从数据库删除
                memoryRecordDao.deleteMemory(memoryId)

                // TODO: 如果是ECM记忆，需要从缓存中删除
                // 这需要扩展EcmMemoryCache支持按ID删除

                logger.d("MemoryRepositoryImpl", "记忆已删除: $memoryId")
            }
        }

    /**
     * 删除用户的所有特定层级记忆
     */
    override suspend fun deleteMemoriesByTier(userId: String, tier: MemoryTier): ModernResult<Unit> =
        withContext(ioDispatcher) {
            safeCatch {
                when (tier) {
                    MemoryTier.ECM -> {
                        ecmMemoryCache.clearUserMemories(userId)
                    }
                    else -> {
                        memoryRecordDao.deleteMemoriesByTier(userId, tier.name)
                    }
                }
                logger.d("MemoryRepositoryImpl", "已删除用户记忆: userId=$userId, tier=$tier")
            }
        }

    /**
     * 清理过期记忆
     */
    override suspend fun cleanupExpiredMemories(): ModernResult<Int> =
        withContext(ioDispatcher) {
            safeCatch {
                val currentTime = System.currentTimeMillis()

                // 清理数据库中的过期记忆
                val dbDeletedCount = memoryRecordDao.deleteExpiredMemories(currentTime)

                // 清理ECM缓存中的过期记忆
                val cacheDeletedCount = ecmMemoryCache.cleanupExpiredMemories()

                val totalDeleted = dbDeletedCount + cacheDeletedCount
                logger.d("MemoryRepositoryImpl", "过期记忆清理完成: 数据库${dbDeletedCount}条, 缓存${cacheDeletedCount}条")

                totalDeleted
            }
        }

    /**
     * 获取用户记忆统计信息
     */
    override suspend fun getMemoryStats(userId: String): ModernResult<MemoryStats> =
        withContext(ioDispatcher) {
            safeCatch {
                val dbStats = memoryRecordDao.getMemoryStatsByUser(userId)
                val cacheStats = ecmMemoryCache.getCacheStats()

                // 合并统计信息
                val totalMemories = dbStats.sumOf {
                    it.count
                } + if (cacheStats.totalUsers > 0) cacheStats.totalMemories else 0
                val memoriesByTier = dbStats.associate { MemoryTier.valueOf(it.tier) to it.count }.toMutableMap()

                // 添加ECM统计
                if (cacheStats.totalMemories > 0) {
                    memoriesByTier[MemoryTier.ECM] = cacheStats.totalMemories
                }

                val avgImportance = if (dbStats.isNotEmpty()) {
                    dbStats.map { it.avgImportance }.average().toFloat()
                } else {
                    3f
                }

                val oldestTime = dbStats.mapNotNull { it.oldestTime }.minOrNull()
                val newestTime = dbStats.mapNotNull { it.newestTime }.maxOrNull()
                val totalSize = dbStats.sumOf { it.totalSize }

                MemoryStats(
                    totalMemories = totalMemories,
                    memoriesByTier = memoriesByTier,
                    averageImportance = avgImportance,
                    oldestMemoryTime = oldestTime,
                    newestMemoryTime = newestTime,
                    totalStorageSize = totalSize,
                )
            }
        }

    /**
     * 观察用户记忆变化
     */
    override fun observeMemories(userId: String, tier: MemoryTier?): Flow<ModernResult<List<MemoryRecord>>> {
        return memoryRecordDao.observeMemories(userId, tier?.name).map { entities ->
            try {
                val records = entities.map { MemoryRecordMapper.toDomain(it) }
                ModernResult.Success(records)
            } catch (e: Exception) {
                logger.e("MemoryRepositoryImpl", "观察记忆变化失败", e)
                ModernResult.Error(
                    ModernDataError(
                        operationName = "observeMemories",
                        errorType = GlobalErrorType.Data.Access,
                        uiMessage = UiText.DynamicString("观察记忆变化失败"),
                        cause = e,
                    ),
                )
            }
        }
    }

    /**
     * 向量相似度搜索
     */
    override suspend fun searchByEmbedding(
        queryEmbedding: FloatArray,
        userId: String,
        tier: MemoryTier?,
        topK: Int,
        minSimilarity: Float,
    ): ModernResult<List<MemorySearchResult>> =
        withContext(ioDispatcher) {
            safeCatch {
                val entities = memoryRecordDao.getEmbeddingsForSearch(
                    userId,
                    tier?.name,
                    DEFAULT_SEARCH_LIMIT,
                )

                val results = entities.mapNotNull { entity ->
                    try {
                        val entityVector = entity.getEmbeddingAsFloatArray() ?: return@mapNotNull null
                        val similarity = calculateCosineSimilarity(queryEmbedding, entityVector)

                        if (similarity >= minSimilarity) {
                            MemorySearchResult(
                                memory = MemoryRecordMapper.toDomain(entity),
                                similarity = similarity,
                                reason = "向量相似度匹配",
                            )
                        } else {
                            null
                        }
                    } catch (e: Exception) {
                        logger.w("MemoryRepositoryImpl", "计算向量相似度失败: ${entity.id}", e)
                        null
                    }
                }

                results.sortedByDescending { it.similarity }.take(topK)
            }
        }

    // ==================== 私有辅助方法 ====================

    /**
     * 判断记忆是否需要生成嵌入向量
     */
    private fun shouldGenerateEmbedding(record: MemoryRecord): Boolean {
        return record.tier in listOf(MemoryTier.DWM, MemoryTier.GIM) &&
            record.payload.toString().isNotBlank()
    }

    /**
     * 为记忆记录生成嵌入向量
     */
    private suspend fun generateEmbeddingForRecord(entity: MemoryRecordEntity): MemoryRecordEntity {
        return try {
            val content = entity.payloadJson
            val embedding = bgeEngineManager.getEmbeddingEngine().embed(content)
            entity.copy(
                embedding = embedding.toMemoryByteArray(),
                embeddingStatus = MemoryRecordEntity.STATUS_COMPLETED,
                generationTimeMs = System.currentTimeMillis() - entity.createdAt,
            )
        } catch (e: Exception) {
            logger.e("MemoryRepositoryImpl", "生成嵌入向量异常", e)
            entity.copy(embeddingStatus = MemoryRecordEntity.STATUS_FAILED)
        }
    }

    /**
     * 召回ECM记忆
     */
    private suspend fun recallEcmMemories(userId: String, query: MemoryQuery): List<MemorySearchResult> {
        val memories = ecmMemoryCache.searchMemories(userId, query.query, 3)
        return memories.map { memory ->
            MemorySearchResult(
                memory = memory,
                similarity = 1.0f, // ECM记忆使用固定高相似度
                reason = "临时上下文记忆",
            )
        }
    }

    /**
     * 召回DWM记忆
     */
    private suspend fun recallDwmMemories(userId: String, query: MemoryQuery): List<MemorySearchResult> {
        // 使用向量搜索或文本匹配
        return if (query.query.isNotBlank()) {
            // 生成查询向量并搜索
            try {
                val embedding = bgeEngineManager.getEmbeddingEngine().embed(query.query)
                searchByEmbedding(
                    embedding,
                    userId,
                    MemoryTier.DWM,
                    5,
                    query.minSimilarity,
                ).getOrNull() ?: emptyList()
            } catch (e: Exception) {
                logger.w("MemoryRepositoryImpl", "生成查询向量失败", e)
                emptyList()
            }
        } else {
            // 返回最新的DWM记忆
            val entities = memoryRecordDao.getMemoriesByTier(userId, MemoryTier.DWM.name, 5)
            entities.map { entity ->
                MemorySearchResult(
                    memory = MemoryRecordMapper.toDomain(entity),
                    similarity = 0.8f,
                    reason = "最近对话记忆",
                )
            }
        }
    }

    /**
     * 召回UPM记忆
     */
    private suspend fun recallUpmMemories(userId: String, query: MemoryQuery): List<MemorySearchResult> {
        val entities = memoryRecordDao.getMemoriesByTier(userId, MemoryTier.UPM.name, 10)
        return entities.map { entity ->
            MemorySearchResult(
                memory = MemoryRecordMapper.toDomain(entity),
                similarity = 1.0f, // UPM记忆始终相关
                reason = "用户档案记忆",
            )
        }
    }

    /**
     * 召回GIM记忆
     */
    private suspend fun recallGimMemories(userId: String, query: MemoryQuery): List<MemorySearchResult> {
        val entities = memoryRecordDao.getMemoriesByImportance(userId, 4, 1) // 获取最重要的1条
        return entities.map { entity ->
            MemorySearchResult(
                memory = MemoryRecordMapper.toDomain(entity),
                similarity = 0.9f,
                reason = "全局洞察记忆",
            )
        }
    }

    /**
     * 从查询中提取用户ID (临时实现)
     * TODO: 改进为从上下文或参数中获取
     */
    private fun extractUserIdFromQuery(query: String): String {
        // 临时实现：返回默认用户ID
        // 实际应该从查询上下文或参数中获取
        return "default_user"
    }
}
