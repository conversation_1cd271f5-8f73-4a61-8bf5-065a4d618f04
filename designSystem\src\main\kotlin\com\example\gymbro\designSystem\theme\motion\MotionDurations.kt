package com.example.gymbro.designSystem.theme.motion

/**
 * GymBro动画时长Token系统
 *
 * 提供统一的动画时长定义，基于8的倍数系统
 * 支持系统动画缩放和无障碍友好设计
 */
object MotionDurations {
    // === 基础时长定义（基于8的倍数系统）===
    const val XS = 120 // 按钮微动、图标按压
    const val S = 240 // 页面切换、组件淡入
    const val Short = 400 // 短时动画 (Tokens.Motion.Duration.Short)
    const val M = 400 // 标准过渡动画
    const val L = 1000 // 呼吸动画、背景渐变
    const val XL = 3000 // Logo浮动、长时间循环

    // === 语义化时长定义 ===
    const val MICRO_INTERACTION = XS // 微交互动画
    const val PAGE_TRANSITION = S // 页面过渡动画
    const val CONTENT_TRANSITION = M // 内容切换动画
    const val DECORATIVE = L // 装饰性动画
    const val LONG_CYCLE = XL // 长时间循环动画

    // === 组件特定时长 ===
    object Component {
        // 按钮动画
        const val BUTTON_PRESS = XS
        const val BUTTON_HOVER = XS
        const val BUTTON_LOADING = M

        // 卡片动画
        const val CARD_ENTER = S
        const val CARD_HOVER = XS
        const val CARD_PRESS = XS

        // 输入框动画
        const val INPUT_FOCUS = XS
        const val INPUT_ERROR_SHAKE = XS * 2
        const val INPUT_LABEL_FLOAT = S

        // 页面动画
        const val PAGE_ENTER = S
        const val PAGE_EXIT = S
        const val MODAL_ENTER = S
        const val MODAL_EXIT = S

        // 进度指示器
        const val PROGRESS_ROTATION = M
        const val LOADING_ROTATION = M

        // 对话框动画
        const val DIALOG_ENTER = S
        const val DIALOG_EXIT = S

        // 底部表单动画
        const val BOTTOM_SHEET_ENTER = S
        const val BOTTOM_SHEET_EXIT = S
    }

    // === 呼吸动画配置 ===
    object Breathing {
        const val AMPLITUDE_SMALL = 2f // dp
        const val AMPLITUDE_MEDIUM = 4f // dp
        const val AMPLITUDE_LARGE = 6f // dp

        const val ALPHA_MIN = 0.92f
        const val ALPHA_MAX = 1.0f

        const val DURATION = L // 呼吸动画周期
    }

    // === 按钮交互动画配置 ===
    object Button {
        const val SCALE_PRESSED = 0.97f
        const val SCALE_DISABLED = 1.0f
        const val RIPPLE_ALPHA = 0.16f
    }

    // === 页面过渡动画配置 ===
    object Page {
        const val SLIDE_DISTANCE_DP = 32f
        const val MODAL_SLIDE_DISTANCE_DP = 48f
    }

    // === 模块专用时长 ===
    object Profile {
        const val THEME_SWITCH = S
        const val THEME_PREVIEW = XS
        const val SETTING_ITEM_PRESS = XS
        const val SETTING_ITEM_HOVER = XS
        const val AVATAR_CHANGE = S
        const val AVATAR_HOVER = XS
        const val HEADER_ENTER = S
        const val LOADING_INDICATOR = M
        const val LIST_ITEM_ENTER = S
        const val LIST_ITEM_EXIT = XS
        const val MODAL_SLIDE_IN = S
        const val MODAL_SLIDE_OUT = S

        // 触觉反馈延迟
        const val HAPTIC_THEME_SWITCH_DELAY = 50L
        const val HAPTIC_SETTING_ITEM_DELAY = 30L
        const val HAPTIC_SWITCH_TOGGLE_DELAY = 40L
    }

    object Coach {
        const val MESSAGE_ENTER = S
        const val MESSAGE_EXIT = XS
        const val SEND_BUTTON_ROTATION = 1200
        const val INPUT_BORDER_ANIMATION = 200
        const val INPUT_STREAMING_ANIMATION = 2000
        const val AI_STREAMING_PULSE = 3000
        const val AI_TYPING_INDICATOR = 600
        const val PAGE_TRANSITION = 300
        const val QUICK_TRANSITION = 220
        const val BUBBLE_ANIMATION = 600
        const val RIPPLE_EFFECT = 150

        // 动画常量
        const val ANIMATION_DURATION = 300
        const val INPUT_FEEDBACK = 200
        const val AI_RESPONSE = 600

        // === 扩展的Coach专用动画时长（基于input/README.md硬编码值标准化）===
        const val TAG_PRESS_DURATION = 150 // ChatGptStyleTag按压动画时长
        const val VOICE_BUTTON_PRESS = 120 // 语音按钮按压时长
        const val MODEL_CHIP_ANIMATION = 200 // ModelChip动画时长
        const val PANEL_SLIDE_DURATION = 300 // 面板滑动时长
        const val SUGGESTION_FADE = 200 // 建议面板淡入淡出
        const val THINKING_BOX_PULSE = 2000 // 思考框脉动效果

        // 交互动画时长
        const val QUICK_FEEDBACK = 150 // 快速反馈动画
        const val NORMAL_INTERACTION = 200 // 标准交互动画
        const val SMOOTH_TRANSITION = 300 // 平滑过渡动画

        // 透明度动画
        const val ALPHA_TRANSITION = 200 // 透明度变化时长
        const val HOVER_FADE = 150 // 悬停淡入淡出
        const val DISABLED_FADE = 240 // 禁用状态淡化

        // === 🚀 统一文本渲染器专用动画时长 ===
        object TextRenderer {
            // 金属跃动效果动画时长
            const val METALLIC_PULSE_FAST = 2000      // 快速金属跃动 - 标题专用
            const val METALLIC_PULSE_NORMAL = 3000    // 标准金属跃动 - 默认效果
            const val METALLIC_PULSE_SLOW = 4000      // 慢速金属跃动 - 装饰用

            // 打字机效果速度配置（ms/字符）
            const val TYPEWRITER_INSTANT = 0L         // 瞬显 - PreThink使用
            const val TYPEWRITER_FAST = 8L            // 快速打字 - 8ms/字符
            const val TYPEWRITER_SMOOTH = 16L         // 流畅打字 - 16ms/字符 (默认)
            const val TYPEWRITER_COMFORTABLE = 33L    // 舒适打字 - 33ms/字符
            const val TYPEWRITER_SLOW = 50L           // 慢速打字 - 50ms/字符

            // 文本过渡动画时长
            const val TEXT_FADE_IN = 200              // 文本淡入动画
            const val TEXT_FADE_OUT = 150             // 文本淡出动画
            const val TEXT_TRANSITION = 300          // 文本切换动画

            // 高级效果动画时长
            const val GLOW_PULSE = 1500              // 发光脉冲效果
            const val SHIMMER_SWEEP = 2500           // 闪烁扫过效果
            const val BREATHING_EFFECT = 4000        // 呼吸灯效果
        }
    }

    object Workout {
        const val CARD_ENTER = S
        const val CARD_PRESS = 300
        const val NAVIGATION_ENTER = S
        const val NAVIGATION_EXIT = S
        const val CROSS_MODULE_TRANSITION = S
        const val TIMER_ANIMATION = M

        // 动画常量
        const val NAV_DURATION = 300
        const val CARD_INTERACTION = 250
        const val STATE_CHANGE = 240
    }
}
