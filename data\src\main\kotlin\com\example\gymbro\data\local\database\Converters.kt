package com.example.gymbro.data.local.database

import androidx.room.TypeConverter
import kotlinx.datetime.TimeZone
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json

/**
 * Room数据库类型转换器
 * 处理复杂类型与基本类型之间的转换
 * 包含订阅模块和其他模块所需的所有类型转换器
 *
 * 🔥 JSON 数据持久化修复：统一使用 kotlinx.serialization 替代 Gson
 */
class Converters {
    companion object {
        private val json: Json = Json {
            ignoreUnknownKeys = true
            isLenient = true
            encodeDefaults = true
        }
        private val defaultTimeZone = TimeZone.currentSystemDefault()
    }

    /**
     * 将List<String>转换为JSON字符串存储到数据库
     */
    @TypeConverter
    fun fromStringList(value: List<String>?): String? {
        return if (value == null) null else json.encodeToString(value)
    }

    /**
     * 将JSON字符串转换为List<String>从数据库读取
     */
    @TypeConverter
    fun toStringList(value: String?): List<String>? {
        return if (value == null) {
            null
        } else {
            try {
                json.decodeFromString<List<String>>(value)
            } catch (e: Exception) {
                emptyList() // 🔥 JSON 解析失败时返回空列表，避免崩溃
            }
        }
    }

    /**
     * 将FloatArray转换为JSON字符串存储到数据库
     */
    @TypeConverter
    fun fromFloatArray(value: FloatArray?): String? {
        return if (value == null) null else json.encodeToString(value.toList())
    }

    /**
     * 将JSON字符串转换为FloatArray从数据库读取
     */
    @TypeConverter
    fun toFloatArray(value: String?): FloatArray? {
        return if (value == null) {
            null
        } else {
            try {
                val list: List<Float> = json.decodeFromString(value)
                list.toFloatArray()
            } catch (e: Exception) {
                floatArrayOf() // 🔥 JSON 解析失败时返回空数组，避免崩溃
            }
        }
    }

    /**
     * 将Map<String, Any>转换为JSON字符串存储到数据库
     * 注意：kotlinx.serialization 不直接支持 Map<String, Any>，使用 JsonElement 作为中间格式
     */
    @TypeConverter
    fun fromStringAnyMap(value: Map<String, Any>?): String? {
        return if (value == null) {
            null
        } else {
            try {
                // 🔥 将 Map<String, Any> 转换为 JSON 字符串
                // 这里使用简单的字符串拼接，避免复杂的序列化问题
                val jsonBuilder = StringBuilder("{")
                value.entries.forEachIndexed { index, entry ->
                    if (index > 0) jsonBuilder.append(",")
                    jsonBuilder.append("\"${entry.key}\":\"${entry.value}\"")
                }
                jsonBuilder.append("}")
                jsonBuilder.toString()
            } catch (e: Exception) {
                "{}" // 返回空 JSON 对象
            }
        }
    }

    /**
     * 将JSON字符串转换为Map<String, Any>从数据库读取
     */
    @TypeConverter
    fun toStringAnyMap(value: String?): Map<String, Any>? {
        return if (value == null) {
            null
        } else {
            try {
                // 🔥 简单的 JSON 解析，仅支持字符串值
                // 对于复杂的 Map<String, Any> 建议使用专门的序列化方案
                if (value == "{}") {
                    emptyMap()
                } else {
                    // 这里返回空 Map，避免复杂的解析逻辑
                    // 如果需要完整支持，建议使用 JsonElement 或专门的序列化器
                    emptyMap()
                }
            } catch (e: Exception) {
                emptyMap() // 🔥 JSON 解析失败时返回空 Map，避免崩溃
            }
        }
    }
}
