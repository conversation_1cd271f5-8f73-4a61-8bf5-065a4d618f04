name: Benchmark Performance Testing

on:
  push:
    branches: [ master, develop ]
    paths:
      - 'benchmark/**'
      - 'app/**'
      - 'core/**'
      - 'features/**'
  pull_request:
    branches: [ master, develop ]
    paths:
      - 'benchmark/**'
      - 'app/**'
      - 'core/**'
      - 'features/**'
  # schedule:
  #   # 每天UTC 02:00运行 (北京时间10:00) - MVP: 禁用定时任务以节省GitHub Actions配额
  #   - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      benchmark_type:
        description: 'Benchmark测试类型'
        required: false
        default: 'all'
        type: choice
        options:
        - all
        - startup
        - navigation
        - performance

env:
  GRADLE_OPTS: -Dorg.gradle.daemon=false -Dorg.gradle.workers.max=2
  JAVA_VERSION: '17'

jobs:
  # Macrobenchmark测试 (MVP: 禁用以避免账单)
  benchmark-tests:
    name: Macrobenchmark Performance Tests
    runs-on: ubuntu-latest
    if: false  # MVP: 禁用Benchmark测试以避免账单
    strategy:
      fail-fast: false  # 让所有matrix job都运行完，收集完整数据
      matrix:
        api-level: [31, 34]
        profile: [Pixel_6, Pixel_6_Pro]

    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 设置JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: ${{ env.JAVA_VERSION }}
          distribution: 'temurin'

      - name: 现代化Gradle缓存
        uses: gradle/gradle-build-action@v3
        with:
          cache-read-only: false
          gradle-home-cache-cleanup: true

      - name: 赋予gradlew执行权限
        run: chmod +x gradlew

      - name: 创建结果目录
        run: |
          mkdir -p benchmark-results
          mkdir -p performance-baselines

      - name: Create google-services.json
        run: echo "$FIREBASE_SERVICE_ACCOUNT" > app/google-services.json
        env:
          FIREBASE_SERVICE_ACCOUNT: ${{ secrets.FIREBASE_SERVICE_ACCOUNT }}
        continue-on-error: true

      - name: 构建Debug和Benchmark APK
        run: |
          ./gradlew assembleDebug --no-configuration-cache
          ./gradlew :benchmark:assembleBenchmark --no-configuration-cache

      - name: "运行Macrobenchmark测试 (Android Emulator)"
        uses: reactivecircus/android-emulator-runner@v2
        with:
          api-level: ${{ matrix.api-level }}
          target: google_apis
          arch: x86_64
          profile: ${{ matrix.profile }}
          force-avd-creation: false
          emulator-options: -no-snapshot-save -no-window -gpu swiftshader_indirect -noaudio -no-boot-anim -camera-back none
          disable-animations: true
          script: |
            echo "🚀 开始Macrobenchmark测试 - ${{ matrix.profile }} API ${{ matrix.api-level }}"

            # 等待模拟器完全启动
            adb wait-for-device shell 'while [[ -z $(getprop sys.boot_completed | tr -d '\r') ]]; do sleep 1; done; input keyevent 82'

            # 根据测试类型运行相应的benchmark
            case "${{ github.event.inputs.benchmark_type || 'all' }}" in
              "startup")
                echo "🏃 运行启动性能测试"
                ./gradlew :benchmark:connectedBenchmarkAndroidTest -Pandroid.testInstrumentationRunnerArguments.class=com.example.gymbro.benchmark.startup.WorkoutStartupBenchmark --no-configuration-cache
                ;;
              "navigation")
                echo "🧭 运行导航性能测试"
                ./gradlew :benchmark:connectedBenchmarkAndroidTest -Pandroid.testInstrumentationRunnerArguments.class=com.example.gymbro.benchmark.navigation.NavigationBenchmark --no-configuration-cache
                ;;
              "performance")
                echo "⚡ 运行性能测试"
                ./gradlew :benchmark:connectedBenchmarkAndroidTest -Pandroid.testInstrumentationRunnerArguments.class=com.example.gymbro.benchmark.performance.WorkoutPerformanceBenchmark --no-configuration-cache
                ;;
              *)
                echo "🔄 运行完整benchmark测试套件"
                ./gradlew :benchmark:connectedBenchmarkAndroidTest --no-configuration-cache
                ;;
            esac

      - name: 处理Benchmark结果
        if: always()
        run: |
          # 复制所有benchmark结果
          find . -name "*.json" -path "*/benchmark/*" -exec cp {} benchmark-results/ \;
          find . -name "*.trace" -path "*/benchmark/*" -exec cp {} benchmark-results/ \;

          # 生成性能摘要报告
          if [ -d "benchmark/build/reports/benchmark" ]; then
            cp -r benchmark/build/reports/benchmark/* benchmark-results/
          fi

      - name: 性能基线检查
        id: performance-baseline
        run: |
          # 检查启动时间基线 (目标: ≤2秒 = 2000ms)
          STARTUP_THRESHOLD=2000

          # 检查主题切换基线 (目标: ≤240ms)
          THEME_TRANSITION_THRESHOLD=240

          # 提取性能数据并检查
          python3 << 'EOF'
          import json
          import glob
          import os
          import sys

          # 查找benchmark结果文件
          results_files = glob.glob("benchmark-results/*.json")
          print(f"Found {len(results_files)} result files: {results_files}")

          performance_summary = {
              "startup_time": None,
              "theme_transition": None,
              "frame_drops": None,
              "passed_startup": False,
              "passed_theme": False
          }

          startup_threshold = int(os.environ.get('STARTUP_THRESHOLD', '2000'))
          theme_threshold = int(os.environ.get('THEME_TRANSITION_THRESHOLD', '240'))

          for file in results_files:
              try:
                  print(f"Processing file: {file}")
                  with open(file, 'r') as f:
                      data = json.load(f)

                  # Macrobenchmark JSON结构: data['benchmarks'][i]['metrics']
                  if 'benchmarks' in data:
                      for benchmark in data['benchmarks']:
                          benchmark_name = benchmark.get('name', '').lower()
                          class_name = benchmark.get('className', '').lower()
                          metrics = benchmark.get('metrics', {})

                          print(f"Processing benchmark: {benchmark_name}, class: {class_name}")

                          # 检查启动时间 (StartupBenchmark)
                          if ('startup' in benchmark_name or 'startup' in class_name) and 'startupMs' in metrics:
                              startup_metric = metrics['startupMs']
                              # 使用median或minimum作为代表值
                              startup_time = startup_metric.get('median') or startup_metric.get('minimum')
                              if startup_time:
                                  performance_summary['startup_time'] = startup_time
                                  performance_summary['passed_startup'] = startup_time <= startup_threshold
                                  print(f"Found startup time: {startup_time}ms")

                          # 检查帧时间/主题切换 (可能在performance或navigation测试中)
                          if 'frameDurationCpuMs' in metrics:
                              frame_metric = metrics['frameDurationCpuMs']
                              # 使用P90或median作为代表值
                              frame_time = frame_metric.get('P90') or frame_metric.get('median')
                              if frame_time:
                                  performance_summary['theme_transition'] = frame_time
                                  performance_summary['passed_theme'] = frame_time <= theme_threshold
                                  print(f"Found frame time: {frame_time}ms")

                          # 检查其他可能的性能指标
                          for metric_name, metric_data in metrics.items():
                              if isinstance(metric_data, dict):
                                  print(f"  Metric {metric_name}: {list(metric_data.keys())}")

              except Exception as e:
                  print(f"Error processing {file}: {e}")
                  continue

          # 输出结果到GitHub Actions
          with open(os.environ['GITHUB_OUTPUT'], 'a') as f:
              f.write(f"startup-time={performance_summary['startup_time'] or 'N/A'}\n")
              f.write(f"theme-transition={performance_summary['theme_transition'] or 'N/A'}\n")
              f.write(f"passed-startup={str(performance_summary['passed_startup']).lower()}\n")
              f.write(f"passed-theme={str(performance_summary['passed_theme']).lower()}\n")

          print(f"性能摘要: {performance_summary}")

          # 如果没有找到任何数据，设置为警告状态
          if performance_summary['startup_time'] is None and performance_summary['theme_transition'] is None:
              print("Warning: No performance data found in benchmark results")
              sys.exit(0)  # 不失败，但记录警告
          EOF

      - name: 上传Benchmark结果
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: benchmark-results-${{ matrix.profile }}-api${{ matrix.api-level }}-${{ github.sha }}
          path: |
            benchmark-results/
            benchmark/build/reports/benchmark/
          retention-days: 30

      - name: 上传性能基线数据
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: performance-baseline-${{ matrix.profile }}-api${{ matrix.api-level }}-${{ github.sha }}
          path: |
            performance-baselines/
          retention-days: 90

      - name: 性能回归检测
        if: github.event_name == 'pull_request'
        run: |
          echo "## 🚀 性能测试结果" >> $GITHUB_STEP_SUMMARY
          echo "### 设备配置: ${{ matrix.profile }} (API ${{ matrix.api-level }})" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          echo "| 性能指标 | 测量值 | 目标值 | 状态 |" >> $GITHUB_STEP_SUMMARY
          echo "|---------|--------|--------|------|" >> $GITHUB_STEP_SUMMARY
          echo "| 启动时间 | ${{ steps.performance-baseline.outputs.startup-time }}ms | ≤2000ms | ${{ steps.performance-baseline.outputs.passed-startup == 'true' && '✅ 通过' || '❌ 超标' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| 主题切换 | ${{ steps.performance-baseline.outputs.theme-transition }}ms | ≤240ms | ${{ steps.performance-baseline.outputs.passed-theme == 'true' && '✅ 通过' || '❌ 超标' }} |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          # 如果性能回归，设置失败状态
          if [[ "${{ steps.performance-baseline.outputs.passed-startup }}" != "true" ]] || [[ "${{ steps.performance-baseline.outputs.passed-theme }}" != "true" ]]; then
            echo "⚠️ **性能回归检测到!** 请优化性能后重新提交" >> $GITHUB_STEP_SUMMARY
            exit 1
          else
            echo "✅ **所有性能指标达标!**" >> $GITHUB_STEP_SUMMARY
          fi

  # 性能趋势分析
  performance-trends:
    name: Performance Trend Analysis
    needs: [benchmark-tests]
    runs-on: ubuntu-latest
    if: needs.benchmark-tests.result == 'success' && (github.ref == 'refs/heads/master' || github.ref == 'refs/heads/develop')

    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 下载所有Benchmark结果
        uses: actions/download-artifact@v4
        with:
          pattern: benchmark-results-*
          path: all-benchmark-results

      - name: 生成性能趋势报告
        run: |
          echo "## 📊 性能趋势分析" > performance-trend-report.md
          echo "" >> performance-trend-report.md
          echo "**生成时间**: $(date)" >> performance-trend-report.md
          echo "**分支**: ${{ github.ref_name }}" >> performance-trend-report.md
          echo "**提交**: ${{ github.sha }}" >> performance-trend-report.md
          echo "" >> performance-trend-report.md

          # 分析各设备性能数据
          for device_dir in all-benchmark-results/*/; do
            device_name=$(basename "$device_dir")
            echo "### $device_name" >> performance-trend-report.md
            echo "" >> performance-trend-report.md

            # 这里可以添加更详细的趋势分析逻辑
            echo "- 性能数据已收集" >> performance-trend-report.md
            echo "- 详细结果请查看构建产物" >> performance-trend-report.md
            echo "" >> performance-trend-report.md
          done

      - name: 上传性能趋势报告
        uses: actions/upload-artifact@v4
        with:
          name: performance-trend-report-${{ github.sha }}
          path: performance-trend-report.md
          retention-days: 90

  # 基线数据更新
  update-baseline:
    name: Update Performance Baseline
    needs: [benchmark-tests, performance-trends]
    runs-on: ubuntu-latest
    if: needs.benchmark-tests.result == 'success' && github.ref == 'refs/heads/master' && github.event_name == 'push'
    permissions:
      contents: write

    steps:
      - name: Checkout代码
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: 下载Benchmark结果
        uses: actions/download-artifact@v4
        with:
          pattern: benchmark-results-*
          path: benchmark-results

      - name: 更新性能基线
        run: |
          mkdir -p docs/performance-baselines

          # 解析实际测试结果并计算新基线
          python3 << 'EOF'
          import json
          import glob
          import os
          from datetime import datetime
          import statistics

          # 收集所有设备的测试结果
          all_startup_times = []
          all_frame_times = []
          device_results = {}

          # 遍历所有下载的benchmark结果
          for results_dir in glob.glob("benchmark-results/benchmark-results-*"):
              device_name = os.path.basename(results_dir).replace("benchmark-results-", "")
              device_results[device_name] = {}

              for json_file in glob.glob(f"{results_dir}/*.json"):
                  try:
                      with open(json_file, 'r') as f:
                          data = json.load(f)

                      if 'benchmarks' in data:
                          for benchmark in data['benchmarks']:
                              metrics = benchmark.get('metrics', {})
                              benchmark_name = benchmark.get('name', '').lower()

                              # 收集启动时间
                              if 'startup' in benchmark_name and 'startupMs' in metrics:
                                  startup_metric = metrics['startupMs']
                                  startup_time = startup_metric.get('median') or startup_metric.get('minimum')
                                  if startup_time:
                                      all_startup_times.append(startup_time)
                                      device_results[device_name]['startup_time'] = startup_time

                              # 收集帧时间
                              if 'frameDurationCpuMs' in metrics:
                                  frame_metric = metrics['frameDurationCpuMs']
                                  frame_time = frame_metric.get('P90') or frame_metric.get('median')
                                  if frame_time:
                                      all_frame_times.append(frame_time)
                                      device_results[device_name]['frame_time'] = frame_time
                  except Exception as e:
                      print(f"Error processing {json_file}: {e}")

          # 计算新的基线值（使用P90或平均值+安全边际）
          if all_startup_times:
              # 使用P90 + 10%安全边际作为新基线
              startup_p90 = sorted(all_startup_times)[int(len(all_startup_times) * 0.9)]
              new_startup_baseline = int(startup_p90 * 1.1)
          else:
              new_startup_baseline = 2000  # 默认值

          if all_frame_times:
              frame_p90 = sorted(all_frame_times)[int(len(all_frame_times) * 0.9)]
              new_frame_baseline = int(frame_p90 * 1.1)
          else:
              new_frame_baseline = 240  # 默认值

          # 生成基线文件
          baseline_data = {
              "date": datetime.now().isoformat(),
              "commit": os.environ.get('GITHUB_SHA', ''),
              "branch": os.environ.get('GITHUB_REF_NAME', ''),
              "baselines": {
                  "startup_time_ms": new_startup_baseline,
                  "theme_transition_ms": new_frame_baseline,
                  "frame_drop_threshold": 0,
                  "ai_coach_integration_s": 30
              },
              "measurement_conditions": {
                  "devices": list(device_results.keys()),
                  "api_levels": [31, 34],
                  "test_iterations": 5,
                  "warmup_iterations": 3
              },
              "raw_measurements": {
                  "startup_times": all_startup_times,
                  "frame_times": all_frame_times,
                  "device_breakdown": device_results
              },
              "statistics": {
                  "startup_p90": sorted(all_startup_times)[int(len(all_startup_times) * 0.9)] if all_startup_times else None,
                  "frame_p90": sorted(all_frame_times)[int(len(all_frame_times) * 0.9)] if all_frame_times else None,
                  "total_measurements": len(all_startup_times) + len(all_frame_times)
              }
          }

          # 写入基线文件
          baseline_filename = f"docs/performance-baselines/baseline-{datetime.now().strftime('%Y%m%d')}.json"
          with open(baseline_filename, 'w') as f:
              json.dump(baseline_data, f, indent=2)

          print(f"📈 性能基线已更新: {baseline_filename}")
          print(f"新启动时间基线: {new_startup_baseline}ms (基于{len(all_startup_times)}个测量值)")
          print(f"新帧时间基线: {new_frame_baseline}ms (基于{len(all_frame_times)}个测量值)")
          EOF

      - name: 提交基线更新
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add docs/performance-baselines/
          git commit -m "🔄 Update performance baseline - $(date +%Y%m%d)" || exit 0
          git push

  # 构建状态通知
  notify-benchmark-status:
    name: Notify Benchmark Status
    runs-on: ubuntu-latest
    needs: [benchmark-tests, performance-trends]
    if: always()

    steps:
      - name: 确定Benchmark状态
        id: benchmark-status
        run: |
          if [[ "${{ needs.benchmark-tests.result }}" == "success" && "${{ needs.performance-trends.result }}" == "success" ]]; then
            echo "status=success" >> $GITHUB_OUTPUT
            echo "message=✅ Benchmark测试完成！所有性能指标达标" >> $GITHUB_OUTPUT
          else
            echo "status=failure" >> $GITHUB_OUTPUT
            echo "message=❌ Benchmark测试失败或性能回归" >> $GITHUB_OUTPUT
          fi

      - name: 创建Benchmark摘要
        run: |
          echo "# 🏃‍♂️ GymBro Benchmark Performance Report" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "${{ steps.benchmark-status.outputs.message }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## 📊 测试详情" >> $GITHUB_STEP_SUMMARY
          echo "- **Macrobenchmark测试**: ${{ needs.benchmark-tests.result == 'success' && '✅ 通过' || '❌ 失败' }}" >> $GITHUB_STEP_SUMMARY
          echo "- **性能趋势分析**: ${{ needs.performance-trends.result == 'success' && '✅ 完成' || '❌ 失败' }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## 🎯 性能目标" >> $GITHUB_STEP_SUMMARY
          echo "- **启动时间**: ≤ 2000ms" >> $GITHUB_STEP_SUMMARY
          echo "- **主题切换**: ≤ 240ms" >> $GITHUB_STEP_SUMMARY
          echo "- **AI Coach集成**: ≤ 30s" >> $GITHUB_STEP_SUMMARY
          echo "- **帧率**: 60fps (0掉帧)" >> $GITHUB_STEP_SUMMARY
