package com.example.gymbro.designSystem.components.extras

import android.graphics.RenderEffect
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsHoveredAsState
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.theme.tokens.ColorTokens

/*
 * ==========================================================================
 * 🌓 动态主题适配液态玻璃系统
 * ==========================================================================
 */

/**
 * 🎯 主题感知配置
 */
enum class ThemeMode {
    LIGHT, // 浅色主题（白色背景）
    DARK, // 深色主题（黑色背景）
    AUTO, // 自动检测
}

/**
 * 🔥 黑色背景专用增强配置
 *
 * 核心策略：
 * 1. 亮度增强 - 使用明亮元素而非暗色
 * 2. 发光效果 - 在黑色背景上创造光源感
 * 3. 边缘高光 - 用亮边定义形状
 * 4. 能量感 - 营造科技感和未来感
 */
@Immutable
data class DarkBackgroundEnhancement(
    // 🌟 亮度增强
    val glowIntensity: Float = 0.6f, // 整体发光强度
    val brightnessBoost: Float = 2.5f, // 亮度提升倍数
    val edgeBrightness: Float = 1.2f, // 边缘亮度

    // 🎨 发光色彩策略
    val primaryGlow: Color = Color(0xFF00D4FF), // 主发光色（青蓝）
    val primaryIntensity: Float = 0.15f, // 主发光强度
    val accentGlow: Color = Color(0xFF7C3AED), // 强调发光色（紫色）
    val accentIntensity: Float = 0.12f, // 强调发光强度
    val energyColor: Color = Color(0xFF00FF88), // 能量色（绿色）
    val energyIntensity: Float = 0.08f, // 能量强度

    // ✨ 发光效果
    val outerGlow: Boolean = true, // 外发光
    val outerGlowRadius: Float = 12f, // 外发光半径
    val innerGlow: Boolean = true, // 内发光
    val innerGlowIntensity: Float = 0.8f, // 内发光强度
    val pulseEffect: Boolean = true, // 脉冲效果
    val pulseSpeed: Float = 1.5f, // 脉冲速度

    // 🔆 边缘和高光
    val rimLighting: Boolean = true, // 边缘光照
    val rimIntensity: Float = 1.0f, // 边缘光强度
    val specularHighlights: Boolean = true, // 镜面高光
    val specularIntensity: Float = 0.9f, // 镜面强度

    // 🌈 能量流动
    val energyFlow: Boolean = true, // 能量流动效果
    val flowSpeed: Float = 2.0f, // 流动速度
    val flowIntensity: Float = 0.4f, // 流动强度

    // 📐 几何增强
    val bevelEffect: Boolean = true, // 斜面效果（反向）
    val bevelIntensity: Float = 0.8f, // 斜面强度
    val holographicEffect: Boolean = false, // 全息效果
    val holographicIntensity: Float = 0.3f, // 全息强度
)

/**
 * 🌓 主题自适应外观配置
 */
@Immutable
data class AdaptiveLiquidGlassAppearance(
    // 基础配置
    val base: LiquidGlassAppearance,

    // 主题特定增强
    val lightEnhancement: WhiteBackgroundEnhancement = WhiteBackgroundEnhancement(),
    val darkEnhancement: DarkBackgroundEnhancement = DarkBackgroundEnhancement(),

    // 自适应设置
    val themeMode: ThemeMode = ThemeMode.AUTO,
    val transitionDuration: Int = 300, // 主题切换动画时长(ms)
    val adaptiveContrast: Boolean = true, // 自适应对比度
    val smartIntensity: Boolean = true, // 智能强度调节
) {
    companion object {
        /**
         * 🎯 预设配置 - 自适应版本
         */

        // 自适应卡片
        val AdaptiveCard = AdaptiveLiquidGlassAppearance(
            base = LiquidGlassAppearance(
                blurRadius = 10f,
                distortionStrength = 0.01f,
                rimLightIntensity = 0.2f,
                chromaticAberration = 0.012f,
                performanceLevel = LiquidGlassAppearance.PerformanceLevel.BALANCED,
            ),
            lightEnhancement = WhiteBackgroundEnhancement(
                shadowIntensity = 0.15f,
                contrastBoost = 2.0f,
                tintIntensity = 0.08f,
                accentIntensity = 0.12f,
            ),
            darkEnhancement = DarkBackgroundEnhancement(
                glowIntensity = 0.5f,
                brightnessBoost = 2.0f,
                primaryIntensity = 0.12f,
                accentIntensity = 0.1f,
                outerGlowRadius = 8f,
            ),
        )

        // 自适应输入框
        val AdaptiveInput = AdaptiveLiquidGlassAppearance(
            base = LiquidGlassAppearance(
                blurRadius = 6f,
                distortionStrength = 0.006f,
                rimLightIntensity = 0.15f,
                chromaticAberration = 0.008f,
                performanceLevel = LiquidGlassAppearance.PerformanceLevel.BALANCED,
            ),
            lightEnhancement = WhiteBackgroundEnhancement(
                shadowIntensity = 0.05f, // 降低阴影强度
                contrastBoost = 1.2f, // 降低对比度提升
                tintIntensity = 0.03f, // 降低色调强度
                dropShadow = false, // 禁用投影
                innerGlow = false, // 禁用内发光
                reflectionSpots = false, // 禁用反射光点
            ),
            darkEnhancement = DarkBackgroundEnhancement(
                glowIntensity = 0.4f,
                brightnessBoost = 1.8f,
                primaryIntensity = 0.1f,
                pulseEffect = false, // 输入框不需要脉冲
                energyFlow = false, // 输入框不需要能量流
            ),
        )

        // 自适应按钮
        val AdaptiveButton = AdaptiveLiquidGlassAppearance(
            base = LiquidGlassAppearance(
                blurRadius = 12f,
                distortionStrength = 0.015f,
                rimLightIntensity = 0.3f,
                chromaticAberration = 0.018f,
                performanceLevel = LiquidGlassAppearance.PerformanceLevel.PREMIUM,
            ),
            lightEnhancement = WhiteBackgroundEnhancement(
                shadowIntensity = 0.25f,
                contrastBoost = 2.5f,
                tintIntensity = 0.12f,
                accentIntensity = 0.16f,
                innerGlowIntensity = 0.5f,
                reflectionIntensity = 0.7f,
            ),
            darkEnhancement = DarkBackgroundEnhancement(
                glowIntensity = 0.8f,
                brightnessBoost = 3.0f,
                primaryIntensity = 0.18f,
                accentIntensity = 0.15f,
                pulseEffect = true,
                energyFlow = true,
                outerGlowRadius = 15f,
                specularIntensity = 1.2f,
            ),
        )

        // 🚀 高能版本 - 适合游戏UI或科技感界面
        val AdaptiveEnergy = AdaptiveLiquidGlassAppearance(
            base = LiquidGlassAppearance(
                blurRadius = 16f,
                distortionStrength = 0.025f,
                rimLightIntensity = 0.4f,
                chromaticAberration = 0.025f,
                performanceLevel = LiquidGlassAppearance.PerformanceLevel.PREMIUM,
            ),
            lightEnhancement = WhiteBackgroundEnhancement(
                shadowIntensity = 0.3f,
                contrastBoost = 3.0f,
                tintIntensity = 0.15f,
                accentIntensity = 0.2f,
                innerGlowIntensity = 0.7f,
                reflectionIntensity = 1.0f,
                bevelDepth = 4f,
            ),
            darkEnhancement = DarkBackgroundEnhancement(
                glowIntensity = 1.0f,
                brightnessBoost = 4.0f,
                primaryIntensity = 0.25f,
                accentIntensity = 0.2f,
                energyIntensity = 0.15f,
                pulseEffect = true,
                energyFlow = true,
                holographicEffect = true,
                outerGlowRadius = 20f,
                specularIntensity = 1.5f,
            ),
        )
    }
}

/*
 * ==========================================================================
 * 🌓 主题检测和状态管理
 * ==========================================================================
 */

/**
 * 🎯 主题状态管理器
 */
@Composable
fun rememberThemeState(
    initialMode: ThemeMode = ThemeMode.AUTO,
): ThemeState {
    val isSystemInDarkTheme = isSystemInDarkTheme()

    return remember(isSystemInDarkTheme) {
        ThemeState(
            initialMode = when (initialMode) {
                ThemeMode.AUTO -> if (isSystemInDarkTheme) ThemeMode.DARK else ThemeMode.LIGHT
                else -> initialMode
            },
            isSystemDark = isSystemInDarkTheme,
        )
    }
}

@Stable
class ThemeState(
    private val initialMode: ThemeMode,
    val isSystemDark: Boolean,
) {
    var currentMode by mutableStateOf(initialMode)
        private set

    val isDarkMode: Boolean
        get() = when (currentMode) {
            ThemeMode.DARK -> true
            ThemeMode.LIGHT -> false
            ThemeMode.AUTO -> isSystemDark
        }

    fun toggleTheme() {
        currentMode = when (currentMode) {
            ThemeMode.LIGHT -> ThemeMode.DARK
            ThemeMode.DARK -> ThemeMode.LIGHT
            ThemeMode.AUTO -> if (isSystemDark) ThemeMode.LIGHT else ThemeMode.DARK
        }
    }

    fun setMode(mode: ThemeMode) {
        currentMode = mode
    }
}

/*
 * ==========================================================================
 * 🌓 自适应修饰符实现
 * ==========================================================================
 */

/**
 * 🔥 主题自适应液态玻璃修饰符
 */
fun Modifier.adaptiveLiquidGlass(
    appearance: AdaptiveLiquidGlassAppearance,
    themeState: ThemeState? = null,
    debugMode: Boolean = false,
): Modifier = composed {
    // 获取或创建主题状态
    val currentThemeState = themeState ?: rememberThemeState(appearance.themeMode)

    // 🎯 主题切换动画
    val isDark by remember { derivedStateOf { currentThemeState.isDarkMode } }

    val themeTransition by animateFloatAsState(
        targetValue = if (isDark) 1f else 0f,
        animationSpec = tween(
            durationMillis = appearance.transitionDuration,
            easing = FastOutSlowInEasing,
        ),
        label = "themeTransition",
    )

    // 根据当前主题选择实现
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        adaptiveLiquidGlassModern(appearance, themeTransition, debugMode)
    } else {
        adaptiveLiquidGlassLegacy(appearance, themeTransition)
    }
}

/**
 * 🚀 现代实现 - 着色器版本
 */
@RequiresApi(Build.VERSION_CODES.TIRAMISU)
@Composable
private fun Modifier.adaptiveLiquidGlassModern(
    appearance: AdaptiveLiquidGlassAppearance,
    themeTransition: Float,
    debugMode: Boolean,
): Modifier = composed {
    val shaderCache = LocalShaderCache.current
    val pointerState = remember { OptimizedPointerState() }
    val lightEnhancement = appearance.lightEnhancement
    val darkEnhancement = appearance.darkEnhancement

    // 时间动画
    val animatedTime by rememberInfiniteTransition(label = "adaptiveTime").animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(8000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart,
        ),
        label = "time",
    )

    // 脉冲动画（仅黑色主题）
    val pulseAnimation by rememberInfiniteTransition(label = "pulse").animateFloat(
        initialValue = 0.8f,
        targetValue = 1.2f,
        animationSpec = infiniteRepeatable(
            animation = tween(
                durationMillis = (2000f / darkEnhancement.pulseSpeed).toInt(),
                easing = EaseInOut,
            ),
            repeatMode = RepeatMode.Reverse,
        ),
        label = "pulse",
    )

    this
        // 🌟 外发光效果（仅黑色主题）
        .then(
            if (themeTransition > 0.1f && darkEnhancement.outerGlow) {
                Modifier.drawBehind {
                    val cornerRadius = appearance.base.cornerRadius.toPx()
                    val glowRadius = darkEnhancement.outerGlowRadius.dp.toPx()
                    val glowAlpha = themeTransition * darkEnhancement.glowIntensity

                    // 🔥 多层外发光
                    // 第一层：主发光
                    drawRoundRect(
                        brush = Brush.radialGradient(
                            colors = listOf(
                                darkEnhancement.primaryGlow.copy(alpha = glowAlpha * 0.8f),
                                darkEnhancement.primaryGlow.copy(alpha = glowAlpha * 0.4f),
                                Color.Transparent,
                            ),
                            center = Offset(size.width * 0.5f, size.height * 0.5f),
                            radius = glowRadius * 1.5f,
                        ),
                        topLeft = Offset(-glowRadius, -glowRadius),
                        size = Size(size.width + glowRadius * 2, size.height + glowRadius * 2),
                        cornerRadius = CornerRadius(cornerRadius + glowRadius),
                    )

                    // 第二层：强调发光
                    drawRoundRect(
                        brush = Brush.radialGradient(
                            colors = listOf(
                                darkEnhancement.accentGlow.copy(alpha = glowAlpha * 0.6f),
                                Color.Transparent,
                            ),
                            center = Offset(size.width * 0.3f, size.height * 0.3f),
                            radius = glowRadius,
                        ),
                        topLeft = Offset(-glowRadius * 0.5f, -glowRadius * 0.5f),
                        size = Size(size.width + glowRadius, size.height + glowRadius),
                        cornerRadius = CornerRadius(cornerRadius + glowRadius * 0.5f),
                    )

                    // 第三层：脉冲发光
                    if (darkEnhancement.pulseEffect) {
                        val pulseAlpha = glowAlpha * pulseAnimation * 0.3f
                        drawRoundRect(
                            brush = Brush.radialGradient(
                                colors = listOf(
                                    darkEnhancement.energyColor.copy(alpha = pulseAlpha),
                                    Color.Transparent,
                                ),
                                center = Offset(size.width * 0.5f, size.height * 0.5f),
                                radius = glowRadius * pulseAnimation,
                            ),
                            topLeft = Offset(-glowRadius, -glowRadius),
                            size = Size(size.width + glowRadius * 2, size.height + glowRadius * 2),
                            cornerRadius = CornerRadius(cornerRadius + glowRadius),
                        )
                    }
                }
            } else {
                Modifier
            },
        )
        // 投影效果（仅浅色主题）
        .then(
            if (themeTransition < 0.9f && lightEnhancement.dropShadow) {
                val shadowAlpha = (1f - themeTransition) * lightEnhancement.shadowIntensity
                Modifier.drawBehind {
                    val cornerRadius = appearance.base.cornerRadius.toPx()
                    drawRoundRect(
                        color = Color.Black.copy(alpha = shadowAlpha),
                        topLeft = lightEnhancement.dropShadowOffset,
                        size = size,
                        cornerRadius = CornerRadius(cornerRadius),
                    )
                }
                    .blur(lightEnhancement.dropShadowBlur.dp)
            } else {
                Modifier
            },
        )
        .clip(RoundedCornerShape(appearance.base.cornerRadius))
        .then(
            if (appearance.base.enableInteraction) {
                Modifier.pointerInput(Unit) {
                    detectDragGestures(
                        onDragStart = { offset -> pointerState.start(offset, size) },
                        onDragEnd = { pointerState.stop() },
                        onDragCancel = { pointerState.stop() },
                    ) { change, _ ->
                        pointerState.update(change.position)
                    }
                }
            } else {
                Modifier
            },
        )
        .graphicsLayer {
            // 🌓 自适应着色器
            val adaptiveShader = shaderCache.getOrCreate(
                "liquid_adaptive",
                ADAPTIVE_LIQUID_SHADER,
            ).apply {
                // 基础参数
                setFloatUniform("uResolution", size.width, size.height)
                setFloatUniform("uTime", animatedTime * 8f)
                setFloatUniform("uPointerPos", pointerState.normalizedX, pointerState.normalizedY)
                setFloatUniform("uPointerVel", pointerState.velocityX * 0.1f, pointerState.velocityY * 0.1f)

                // 液态效果
                setFloatUniform("uDistortion", appearance.base.optimizedDistortion)
                setFloatUniform("uChromatic", appearance.base.chromaticAberration)
                setFloatUniform("uBlur", appearance.base.optimizedBlurRadius)

                // 🌓 主题混合参数
                setFloatUniform("uThemeTransition", themeTransition)
                setFloatUniform("uPulse", if (darkEnhancement.pulseEffect) pulseAnimation else 1f)

                // 浅色主题参数
                setFloatUniform("uLightContrastBoost", lightEnhancement.contrastBoost)
                setFloatUniform(
                    "uLightTintColor",
                    lightEnhancement.tintColor.red,
                    lightEnhancement.tintColor.green,
                    lightEnhancement.tintColor.blue,
                )
                setFloatUniform("uLightTintIntensity", lightEnhancement.tintIntensity)

                // 深色主题参数
                setFloatUniform("uDarkBrightnessBoost", darkEnhancement.brightnessBoost)
                setFloatUniform(
                    "uDarkPrimaryGlow",
                    darkEnhancement.primaryGlow.red,
                    darkEnhancement.primaryGlow.green,
                    darkEnhancement.primaryGlow.blue,
                )
                setFloatUniform("uDarkPrimaryIntensity", darkEnhancement.primaryIntensity)
                setFloatUniform(
                    "uDarkAccentGlow",
                    darkEnhancement.accentGlow.red,
                    darkEnhancement.accentGlow.green,
                    darkEnhancement.accentGlow.blue,
                )
                setFloatUniform("uDarkAccentIntensity", darkEnhancement.accentIntensity)

                // 效果开关
                setIntUniform("uEnergyFlow", if (darkEnhancement.energyFlow) 1 else 0)
                setIntUniform("uHolographic", if (darkEnhancement.holographicEffect) 1 else 0)
                setIntUniform("uDebug", if (debugMode) 1 else 0)
            }

            val liquidEffect = RenderEffect.createRuntimeShaderEffect(adaptiveShader, "uTexture")
            renderEffect = liquidEffect.asComposeRenderEffect()
        }
        .drawBehind {
            val cornerRadius = appearance.base.cornerRadius.toPx()

            // 🌓 自适应边缘效果
            if (themeTransition < 0.5f) {
                // 浅色主题边缘
                val lightAlpha = (1f - themeTransition * 2f)

                // 斜面效果
                if (lightEnhancement.bevelEffect) {
                    val bevelWidth = lightEnhancement.bevelDepth.dp.toPx()
                    drawRoundRect(
                        color = Color.White.copy(alpha = 0.4f * lightAlpha),
                        size = size,
                        cornerRadius = CornerRadius(cornerRadius),
                        style = Stroke(width = bevelWidth),
                    )
                }

                // 边缘定义
                drawRoundRect(
                    color = lightEnhancement.tintColor.copy(
                        alpha = lightEnhancement.edgeDefinition * 0.3f * lightAlpha,
                    ),
                    size = size,
                    cornerRadius = CornerRadius(cornerRadius),
                    style = Stroke(width = 1.dp.toPx()),
                )
            }

            if (themeTransition > 0.5f) {
                // 深色主题边缘
                val darkAlpha = (themeTransition - 0.5f) * 2f

                // 边缘发光
                if (darkEnhancement.rimLighting) {
                    drawRoundRect(
                        brush = Brush.linearGradient(
                            colors = listOf(
                                darkEnhancement.primaryGlow.copy(
                                    alpha = darkEnhancement.rimIntensity * 0.8f * darkAlpha,
                                ),
                                darkEnhancement.accentGlow.copy(
                                    alpha = darkEnhancement.rimIntensity * 0.6f * darkAlpha,
                                ),
                            ),
                        ),
                        size = size,
                        cornerRadius = CornerRadius(cornerRadius),
                        style = Stroke(width = 2.dp.toPx()),
                    )
                }

                // 镜面高光
                if (darkEnhancement.specularHighlights) {
                    drawCircle(
                        brush = Brush.radialGradient(
                            colors = listOf(
                                Color.White.copy(
                                    alpha = darkEnhancement.specularIntensity * 0.9f * darkAlpha,
                                ),
                                Color.Transparent,
                            ),
                            radius = 20.dp.toPx(),
                        ),
                        radius = 15.dp.toPx(),
                        center = Offset(size.width * 0.25f, size.height * 0.2f),
                    )
                }

                // 能量流动
                if (darkEnhancement.energyFlow) {
                    val flowOffset = (animatedTime * darkEnhancement.flowSpeed) % 1f
                    drawRoundRect(
                        brush = Brush.linearGradient(
                            colors = listOf(
                                Color.Transparent,
                                darkEnhancement.energyColor.copy(
                                    alpha = darkEnhancement.flowIntensity * darkAlpha,
                                ),
                                Color.Transparent,
                            ),
                            start = Offset(size.width * flowOffset, 0f),
                            end = Offset(size.width * (flowOffset + 0.2f), size.height),
                        ),
                        size = size,
                        cornerRadius = CornerRadius(cornerRadius),
                        style = Stroke(width = 1.5.dp.toPx()),
                    )
                }
            }
        }
}

/**
 * 📱 兼容版本实现
 */
@Composable
private fun Modifier.adaptiveLiquidGlassLegacy(
    appearance: AdaptiveLiquidGlassAppearance,
    themeTransition: Float,
): Modifier = composed {
    val lightEnhancement = appearance.lightEnhancement
    val darkEnhancement = appearance.darkEnhancement

    // 流动动画
    val flowAnimation by rememberInfiniteTransition(label = "flow").animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(4000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart,
        ),
        label = "flowOffset",
    )

    // 脉冲动画
    val pulseAnimation by rememberInfiniteTransition(label = "pulse").animateFloat(
        initialValue = 0.8f,
        targetValue = 1.2f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = EaseInOut),
            repeatMode = RepeatMode.Reverse,
        ),
        label = "pulse",
    )

    this
        // 外发光层（深色主题）
        .then(
            if (themeTransition > 0.1f) {
                Modifier.drawBehind {
                    val cornerRadius = appearance.base.cornerRadius.toPx()
                    val glowAlpha = themeTransition * darkEnhancement.glowIntensity

                    // 外发光
                    drawRoundRect(
                        brush = Brush.radialGradient(
                            colors = listOf(
                                darkEnhancement.primaryGlow.copy(alpha = glowAlpha * 0.6f),
                                darkEnhancement.accentGlow.copy(alpha = glowAlpha * 0.4f),
                                Color.Transparent,
                            ),
                            center = Offset(size.width * 0.5f, size.height * 0.5f),
                            radius = size.width * 0.8f,
                        ),
                        topLeft = Offset(-10.dp.toPx(), -10.dp.toPx()),
                        size = Size(size.width + 20.dp.toPx(), size.height + 20.dp.toPx()),
                        cornerRadius = CornerRadius(cornerRadius + 10.dp.toPx()),
                    )
                }
            } else {
                Modifier
            },
        )
        // 投影层（浅色主题）
        .then(
            if (themeTransition < 0.9f) {
                val shadowAlpha = (1f - themeTransition) * lightEnhancement.shadowIntensity
                Modifier.drawBehind {
                    val cornerRadius = appearance.base.cornerRadius.toPx()
                    drawRoundRect(
                        color = Color.Black.copy(alpha = shadowAlpha),
                        topLeft = lightEnhancement.dropShadowOffset,
                        size = size,
                        cornerRadius = CornerRadius(cornerRadius),
                    )
                }
                    .blur((lightEnhancement.dropShadowBlur * 0.5f).dp)
            } else {
                Modifier
            },
        )
        .clip(RoundedCornerShape(appearance.base.cornerRadius))
        // 🌓 自适应背景
        .background(
            brush = Brush.radialGradient(
                colors = buildList {
                    // 浅色主题颜色
                    if (themeTransition < 1f) {
                        val lightAlpha = (1f - themeTransition)
                        add(
                            lightEnhancement.tintColor.copy(
                                alpha = lightEnhancement.tintIntensity * lightAlpha,
                            ),
                        )
                        add(
                            lightEnhancement.accentColor.copy(
                                alpha = lightEnhancement.accentIntensity * lightAlpha * 0.7f,
                            ),
                        )
                    }

                    // 深色主题颜色
                    if (themeTransition > 0f) {
                        val darkAlpha = themeTransition
                        add(
                            darkEnhancement.primaryGlow.copy(
                                alpha = darkEnhancement.primaryIntensity * darkAlpha,
                            ),
                        )
                        add(
                            darkEnhancement.accentGlow.copy(
                                alpha = darkEnhancement.accentIntensity * darkAlpha,
                            ),
                        )
                        add(
                            darkEnhancement.energyColor.copy(
                                alpha = darkEnhancement.energyIntensity * darkAlpha * 0.6f,
                            ),
                        )
                    }

                    // 透明终点
                    add(Color.Transparent)
                },
                center = Offset.Infinite,
                radius = Float.POSITIVE_INFINITY,
            ),
        )
        // 动态边框和效果
        .drawBehind {
            val cornerRadius = appearance.base.cornerRadius.toPx()

            // 浅色主题效果
            if (themeTransition < 0.5f) {
                val lightAlpha = (1f - themeTransition * 2f)

                // 流动彩色边框
                val flowColors = listOf(
                    lightEnhancement.tintColor.copy(alpha = 0.6f * lightAlpha),
                    lightEnhancement.accentColor.copy(alpha = 0.8f * lightAlpha),
                    Color.Transparent,
                )

                drawRoundRect(
                    brush = Brush.linearGradient(
                        colors = flowColors,
                        start = Offset(size.width * flowAnimation, 0f),
                        end = Offset(size.width * (flowAnimation + 0.3f), size.height),
                    ),
                    size = size,
                    cornerRadius = CornerRadius(cornerRadius),
                    style = Stroke(width = 2.dp.toPx()),
                )
            }

            // 深色主题效果
            if (themeTransition > 0.5f) {
                val darkAlpha = (themeTransition - 0.5f) * 2f

                // 发光边框
                drawRoundRect(
                    brush = Brush.linearGradient(
                        colors = listOf(
                            darkEnhancement.primaryGlow.copy(alpha = 0.8f * darkAlpha),
                            darkEnhancement.accentGlow.copy(alpha = 0.6f * darkAlpha),
                            darkEnhancement.energyColor.copy(alpha = 0.4f * darkAlpha),
                        ),
                    ),
                    size = size,
                    cornerRadius = CornerRadius(cornerRadius),
                    style = Stroke(width = 2.dp.toPx()),
                )

                // 镜面高光
                if (darkEnhancement.specularHighlights) {
                    drawCircle(
                        brush = Brush.radialGradient(
                            colors = listOf(
                                Color.White.copy(alpha = darkEnhancement.specularIntensity * darkAlpha),
                                Color.Transparent,
                            ),
                            radius = 15.dp.toPx(),
                        ),
                        radius = 12.dp.toPx(),
                        center = Offset(size.width * 0.25f, size.height * 0.2f),
                    )
                }

                // 能量流动
                if (darkEnhancement.energyFlow) {
                    val flowOffset = (flowAnimation * darkEnhancement.flowSpeed) % 1f
                    drawRoundRect(
                        brush = Brush.linearGradient(
                            colors = listOf(
                                Color.Transparent,
                                darkEnhancement.energyColor.copy(
                                    alpha = darkEnhancement.flowIntensity * darkAlpha,
                                ),
                                Color.Transparent,
                            ),
                            start = Offset(size.width * flowOffset, 0f),
                            end = Offset(size.width * (flowOffset + 0.2f), size.height),
                        ),
                        size = size,
                        cornerRadius = CornerRadius(cornerRadius),
                        style = Stroke(width = 1.5.dp.toPx()),
                    )
                }

                // 脉冲效果
                if (darkEnhancement.pulseEffect) {
                    val pulseAlpha = darkAlpha * pulseAnimation * 0.3f
                    drawRoundRect(
                        color = darkEnhancement.energyColor.copy(alpha = pulseAlpha),
                        size = size,
                        cornerRadius = CornerRadius(cornerRadius),
                        style = Stroke(width = (pulseAnimation * 2).dp.toPx()),
                    )
                }
            }
        }
}

/*
 * ==========================================================================
 * 🎨 自适应着色器
 * ==========================================================================
 */

private const val ADAPTIVE_LIQUID_SHADER = """
uniform float2 uResolution;
uniform float uTime;
uniform float2 uPointerPos;
uniform float2 uPointerVel;
uniform float uDistortion;
uniform float uChromatic;
uniform float uBlur;
uniform float uThemeTransition;
uniform float uPulse;

// 浅色主题参数
uniform float uLightContrastBoost;
uniform float3 uLightTintColor;
uniform float uLightTintIntensity;

// 深色主题参数
uniform float uDarkBrightnessBoost;
uniform float3 uDarkPrimaryGlow;
uniform float uDarkPrimaryIntensity;
uniform float3 uDarkAccentGlow;
uniform float uDarkAccentIntensity;

uniform int uEnergyFlow;
uniform int uHolographic;
uniform int uDebug;
uniform shader uTexture;

// 🎯 高级噪声函数
float advancedNoise(float2 p) {
    float value = 0.0;
    value += 0.5 * sin(p.x * 6.0 + uTime * 0.8) * cos(p.y * 6.0 + uTime * 0.6);
    value += 0.25 * sin(p.x * 12.0 + uTime * 1.2) * cos(p.y * 12.0 + uTime * 0.9);
    value += 0.125 * sin(p.x * 24.0 + uTime * 0.5) * cos(p.y * 24.0 + uTime * 1.1);
    return value;
}

// 🌓 主题混合函数
float3 mixThemeColors(float3 lightColor, float3 darkColor, float transition) {
    return mix(lightColor, darkColor, transition);
}

// ✨ 能量流动效果
float energyFlow(float2 uv, float time) {
    float flow = sin(uv.x * 10.0 + time * 3.0) * cos(uv.y * 8.0 + time * 2.0);
    return flow * 0.5 + 0.5;
}

// 🌈 全息效果
float3 holographicShift(float3 color, float2 uv, float time) {
    float shift = sin(uv.y * 20.0 + time * 4.0) * 0.1;
    return float3(
        color.r + shift,
        color.g + shift * 0.5,
        color.b - shift
    );
}

half4 main(float2 coord) {
    float2 uv = coord / uResolution;

    // 调试模式
    if (uDebug > 0) {
        float debug = uThemeTransition;
        return half4(debug, debug, debug, 1.0);
    }

    // 🎯 基础液态扭曲
    float2 distortion = float2(
        advancedNoise(uv * 5.0 + uTime * 0.1),
        advancedNoise(uv * 5.0 + float2(3.0, 1.0) + uTime * 0.1)
    ) * uDistortion;

    // 指针交互
    float2 pointerInfluence = (uPointerPos - uv) * length(uPointerVel) * 0.1;
    distortion += pointerInfluence;

    // 脉冲影响（仅深色主题）
    float pulseInfluence = mix(1.0, uPulse, uThemeTransition);
    distortion *= pulseInfluence;

    // 🌈 增强的色散效果
    float2 offset = distortion * uResolution;
    float chromatic = uChromatic * 25.0;

    half4 colorR = uTexture.eval(coord + offset + float2(chromatic, 0.0));
    half4 colorG = uTexture.eval(coord + offset);
    half4 colorB = uTexture.eval(coord + offset - float2(chromatic, 0.0));

    half4 baseColor = half4(colorR.r, colorG.g, colorB.b, colorG.a);

    // 🌓 主题自适应处理

    float centerDistance = distance(uv, float2(0.5));

    // 浅色主题处理
    if (uThemeTransition < 1.0) {
        float lightWeight = 1.0 - uThemeTransition;

        // 对比度增强
        baseColor.rgb = mix(baseColor.rgb,
            (baseColor.rgb - 0.5) * uLightContrastBoost + 0.5,
            lightWeight);

        // 色彩注入
        float tintMask = 1.0 - smoothstep(0.0, 0.7, centerDistance);
        baseColor.rgb = mix(baseColor.rgb, uLightTintColor,
            uLightTintIntensity * tintMask * lightWeight);
    }

    // 深色主题处理
    if (uThemeTransition > 0.0) {
        float darkWeight = uThemeTransition;

        // 亮度增强
        baseColor.rgb = mix(baseColor.rgb,
            baseColor.rgb * uDarkBrightnessBoost,
            darkWeight);

        // 主发光注入
        float glowMask = 1.0 - smoothstep(0.0, 0.6, centerDistance);
        baseColor.rgb = mix(baseColor.rgb, uDarkPrimaryGlow,
            uDarkPrimaryIntensity * glowMask * darkWeight);

        // 强调发光（边缘区域）
        float edgeMask = smoothstep(0.4, 0.9, centerDistance);
        baseColor.rgb = mix(baseColor.rgb, uDarkAccentGlow,
            uDarkAccentIntensity * edgeMask * darkWeight);

        // 能量流动
        if (uEnergyFlow > 0) {
            float flow = energyFlow(uv, uTime);
            baseColor.rgb += flow * 0.1 * darkWeight;
        }

        // 全息效果
        if (uHolographic > 0) {
            baseColor.rgb = mix(baseColor.rgb,
                holographicShift(baseColor.rgb, uv, uTime),
                0.3 * darkWeight);
        }

        // 脉冲发光增强
        float pulseMask = 1.0 - smoothstep(0.0, 0.4, centerDistance);
        baseColor.rgb += uDarkPrimaryGlow * 0.2 * pulseMask * (uPulse - 1.0) * darkWeight;
    }

    return baseColor;
}
"""

/*
 * ==========================================================================
 * 🎯 便捷组件 - 自适应版本
 * ==========================================================================
 */

/**
 * 🌓 自适应液态输入框背景 - 超级性能优化版
 *
 * 专为解决高频重组问题设计：
 * 1. 禁用所有动画和交互
 * 2. 使用最简化的视觉效果
 * 3. 缓存所有计算结果
 * 4. 应用@Stable优化
 */
@Stable
@Composable
fun AdaptiveLiquidInputBackground(
    modifier: Modifier = Modifier,
    themeState: ThemeState? = null,
    content: @Composable BoxScope.() -> Unit = {},
) {
    // 🚀 超级性能优化：完全静态的外观配置
    val ultraOptimizedAppearance = remember {
        AdaptiveLiquidGlassAppearance(
            base = LiquidGlassAppearance(
                blurRadius = 2f, // 最小模糊
                distortionStrength = 0.001f, // 最小扭曲
                rimLightIntensity = 0.05f, // 最小边缘光
                chromaticAberration = 0.002f, // 最小色散
                performanceLevel = LiquidGlassAppearance.PerformanceLevel.MINIMAL,
                enableInteraction = false, // 完全禁用交互
                enableAnimation = false, // 完全禁用动画
                colorIntensity = 0.8f, // 降低颜色强度
            ),
            lightEnhancement = WhiteBackgroundEnhancement(
                shadowIntensity = 0.02f, // 最小阴影
                contrastBoost = 1.1f, // 最小对比度提升
                tintIntensity = 0.01f, // 最小色调
                dropShadow = false, // 禁用所有额外效果
                innerGlow = false,
                reflectionSpots = false,
                bevelEffect = false,
                surfaceTexture = false,
            ),
            darkEnhancement = DarkBackgroundEnhancement(
                glowIntensity = 0.1f, // 最小发光
                brightnessBoost = 1.1f, // 最小亮度提升
                primaryIntensity = 0.02f, // 最小主发光
                accentIntensity = 0.01f, // 最小强调发光
                pulseEffect = false, // 禁用所有动态效果
                energyFlow = false,
                outerGlow = false,
                innerGlow = false,
                specularHighlights = false,
                holographicEffect = false,
                rimLighting = false,
                bevelEffect = false,
            ),
            themeMode = ThemeMode.AUTO,
            transitionDuration = 0, // 禁用过渡动画
            adaptiveContrast = false, // 禁用自适应对比度
            smartIntensity = false, // 禁用智能强度调节
        )
    }

    // 🎯 使用最简化的实现 - 🚨 布局安全保证
    Box(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight() // 🚨 确保液态玻璃背景也使用安全约束
            .clip(RoundedCornerShape(ultraOptimizedAppearance.base.cornerRadius))
            .background(
                // 使用简单的静态背景，避免复杂的着色器
                brush = remember(themeState?.isDarkMode) {
                    val isDark = themeState?.isDarkMode ?: false
                    if (isDark) {
                        Brush.radialGradient(
                            colors = listOf(
                                Color(0xFF1A1A1A).copy(alpha = 0.8f),
                                Color.Transparent,
                            ),
                        )
                    } else {
                        Brush.radialGradient(
                            colors = listOf(
                                Color.White.copy(alpha = 0.9f),
                                Color(0xFFF5F5F5).copy(alpha = 0.7f),
                            ),
                        )
                    }
                },
            )
            .drawBehind {
                // 最简化的边框效果
                val cornerRadius = ultraOptimizedAppearance.base.cornerRadius.toPx()
                val isDark = themeState?.isDarkMode ?: false

                drawRoundRect(
                    color = if (isDark) {
                        Color.White.copy(alpha = 0.1f)
                    } else {
                        Color.Black.copy(alpha = 0.1f)
                    },
                    size = size,
                    cornerRadius = CornerRadius(cornerRadius),
                    style = Stroke(width = 0.5.dp.toPx()),
                )
            },
        content = content,
    )
}

/**
 * 🌓 原版自适应液态输入框背景 - 完整效果版（备用）
 */
@Composable
fun AdaptiveLiquidInputBackgroundFull(
    modifier: Modifier = Modifier,
    themeState: ThemeState? = null,
    content: @Composable BoxScope.() -> Unit = {},
) {
    Box(
        modifier = modifier.adaptiveLiquidGlass(
            AdaptiveLiquidGlassAppearance.AdaptiveInput,
            themeState,
        ),
        content = content,
    )
}

/**
 * 🌓 自适应液态按钮
 */
@Composable
fun AdaptiveLiquidButton(
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
    themeState: ThemeState? = null,
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() },
    content: @Composable BoxScope.() -> Unit = {},
) {
    val isPressed by interactionSource.collectIsPressedAsState()
    val isHovered by interactionSource.collectIsHoveredAsState()

    // 🎯 动态增强 - 交互时提升效果
    val dynamicAppearance = remember(isPressed, isHovered, themeState?.isDarkMode) {
        val isDark = themeState?.isDarkMode ?: false

        if (isDark) {
            AdaptiveLiquidGlassAppearance.AdaptiveButton.copy(
                darkEnhancement = AdaptiveLiquidGlassAppearance.AdaptiveButton.darkEnhancement.copy(
                    glowIntensity = if (isPressed) 1.2f else if (isHovered) 1.0f else 0.8f,
                    primaryIntensity = if (isPressed) 0.25f else if (isHovered) 0.22f else 0.18f,
                    pulseSpeed = if (isPressed) 3f else if (isHovered) 2f else 1.5f,
                ),
            )
        } else {
            AdaptiveLiquidGlassAppearance.AdaptiveButton.copy(
                lightEnhancement = AdaptiveLiquidGlassAppearance.AdaptiveButton.lightEnhancement.copy(
                    shadowIntensity = if (isPressed) 0.35f else if (isHovered) 0.3f else 0.25f,
                    contrastBoost = if (isPressed) 3f else if (isHovered) 2.8f else 2.5f,
                    tintIntensity = if (isPressed) 0.18f else if (isHovered) 0.15f else 0.12f,
                ),
            )
        }
    }

    Box(
        modifier = modifier
            .adaptiveLiquidGlass(dynamicAppearance, themeState)
            .pointerInput(onClick) {
                detectTapGestures(onTap = { onClick() })
            },
        content = content,
    )
}

/**
 * 🌓 自适应液态卡片
 */
@Composable
fun AdaptiveLiquidCard(
    modifier: Modifier = Modifier,
    themeState: ThemeState? = null,
    content: @Composable BoxScope.() -> Unit = {},
) {
    Box(
        modifier = modifier.adaptiveLiquidGlass(
            AdaptiveLiquidGlassAppearance.AdaptiveCard,
            themeState,
        ),
        content = content,
    )
}

/**
 * 🚀 高能自适应液态背景
 */
@Composable
fun AdaptiveLiquidEnergyBackground(
    modifier: Modifier = Modifier,
    themeState: ThemeState? = null,
    content: @Composable BoxScope.() -> Unit = {},
) {
    Box(
        modifier = modifier.adaptiveLiquidGlass(
            AdaptiveLiquidGlassAppearance.AdaptiveEnergy,
            themeState,
        ),
        content = content,
    )
}

/*
 * ==========================================================================
 * 预览
 * ==========================================================================
 */

@GymBroPreview
@Composable
private fun AdaptiveLiquidGlassPreview() {
    val themeState = rememberThemeState()

    GymBroTheme {
        // 使用 Token 系统的背景颜色
        val backgroundColor = if (themeState.isDarkMode) ColorTokens.Dark.Background else ColorTokens.Light.Background
        val textColor = if (themeState.isDarkMode) ColorTokens.Dark.OnSurface else ColorTokens.Light.OnSurface
        val primaryColor = if (themeState.isDarkMode) ColorTokens.Dark.Primary else ColorTokens.Light.Primary

        Column(
            modifier = Modifier
                .background(backgroundColor)
                .padding(Tokens.Spacing.Medium), // 1个字符宽度的内部间距
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            // 主题切换按钮 - 使用 Token 高度
            AdaptiveLiquidButton(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(Tokens.Button.HeightSecondary), // 48dp - 符合按钮标准
                onClick = { themeState.toggleTheme() },
                themeState = themeState,
            ) {
                Box(
                    modifier = Modifier.padding(Tokens.Spacing.Medium), // 内部间距
                    contentAlignment = androidx.compose.ui.Alignment.Center
                ) {
                    Text(
                        text = if (themeState.isDarkMode) "切换到浅色主题" else "切换到深色主题",
                        style = MaterialTheme.typography.labelLarge,
                        color = textColor, // 使用 Token 颜色
                    )
                }
            }

            // 自适应卡片 - 使用 Token 高度和圆角
            AdaptiveLiquidCard(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(Tokens.Card.HeightMin) // 80dp - 标准卡片最小高度
                    .clip(RoundedCornerShape(Tokens.Radius.Card)), // 自然美观的圆角
                themeState = themeState,
            ) {
                Box(
                    modifier = Modifier.padding(Tokens.Spacing.Medium), // 内部间距
                    contentAlignment = androidx.compose.ui.Alignment.Center
                ) {
                    Text(
                        text = "自适应液态卡片",
                        style = MaterialTheme.typography.headlineSmall,
                        color = textColor, // 使用 Token 颜色
                    )
                }
            }

            // 自适应输入框 - 使用 Token 高度
            AdaptiveLiquidInputBackground(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(Tokens.Input.HeightStandard) // 56dp - 标准输入框高度
                    .clip(RoundedCornerShape(Tokens.Radius.Input)), // 输入框圆角
                themeState = themeState,
            ) {
                Box(
                    modifier = Modifier.padding(Tokens.Spacing.Medium), // 内部间距
                    contentAlignment = androidx.compose.ui.Alignment.Center
                ) {
                    Text(
                        text = "自适应输入框背景", 
                        style = MaterialTheme.typography.bodyLarge,
                        color = textColor, // 使用 Token 颜色
                    )
                }
            }

            // 高能背景 - 使用 Token 高度
            AdaptiveLiquidEnergyBackground(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(Tokens.Card.HeightSmall) // 120dp - 小卡片高度
                    .clip(RoundedCornerShape(Tokens.Radius.Card)), // 卡片圆角
                themeState = themeState,
            ) {
                Box(
                    modifier = Modifier.padding(Tokens.Spacing.Medium), // 内部间距
                    contentAlignment = androidx.compose.ui.Alignment.Center
                ) {
                    Text(
                        text = "高能液态背景",
                        style = MaterialTheme.typography.headlineMedium,
                        color = primaryColor, // 使用 Token 主色
                    )
                }
            }
        }
    }
}
