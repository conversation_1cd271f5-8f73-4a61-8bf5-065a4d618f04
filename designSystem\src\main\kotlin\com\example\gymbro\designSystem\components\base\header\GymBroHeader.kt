package com.example.gymbro.designSystem.components.base.header

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.motion.MotionSpecs

/**
 * GymBro统一Header组件
 *
 * 合并了所有模块的Header功能，提供统一的页面头部布局：
 * - 标准化的后退按钮位置和样式
 * - 可选的加载指示器显示
 * - 灵活的页面标题支持
 * - 统一的padding和对齐规范
 * - 支持RTL布局
 * - 响应式设计适配
 * - 可配置的前导和尾随内容插槽
 *
 * 🎯 性能优化: 只在导航和加载状态变化时重组
 * 🎯 设计规范: 遵循UI统一4.0标准
 */
@Composable
fun GymBroHeader(
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
    title: UiText? = null,
    isLoading: Boolean = false,
    showBackButton: Boolean = true,
    leadingContent: @Composable RowScope.() -> Unit = {},
    trailingContent: @Composable RowScope.() -> Unit = {},
    contentDescription: String = "返回",
) {
    // 🎯 使用统一的动画规格 - Header进入动画
    val headerAlpha by animateFloatAsState(
        targetValue = 1f,
        animationSpec = MotionSpecs.Profile.headerEnter(),
        label = "header_enter",
    )

    // 🎯 加载指示器旋转动画
    val loadingRotation by animateFloatAsState(
        targetValue = if (isLoading) 360f else 0f,
        animationSpec = MotionSpecs.Profile.loadingIndicator(),
        label = "loading_rotation",
    )

    Column(
        modifier = modifier
            .fillMaxWidth()
            .statusBarsPadding(), // 🎯 统一状态栏适配
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp) // 标准Header高度
                .padding(horizontal = 16.dp)
                .graphicsLayer { alpha = headerAlpha },
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // 🎯 前导内容区域
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp),
            ) {
                // 返回按钮
                if (showBackButton) {
                    IconButton(
                        onClick = onNavigateBack,
                        modifier = Modifier.size(40.dp),
                    ) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = contentDescription,
                            tint = MaterialTheme.colorScheme.onSurface,
                        )
                    }
                }

                // 自定义前导内容
                leadingContent()

                // 页面标题
                title?.let { titleText ->
                    val context = LocalContext.current
                    Text(
                        text = titleText.asString(context),
                        style = MaterialTheme.typography.titleLarge,
                        color = MaterialTheme.colorScheme.onSurface,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.weight(1f, fill = false),
                    )
                }
            }

            // 🎯 尾随内容区域
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp),
            ) {
                // 加载指示器
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier
                            .size(24.dp)
                            .graphicsLayer { rotationZ = loadingRotation },
                        strokeWidth = 2.dp,
                        color = MaterialTheme.colorScheme.primary,
                    )
                }

                // 自定义尾随内容
                trailingContent()
            }
        }
    }
}

/**
 * 简化版Header - 只有标题和返回按钮
 */
@Composable
fun GymBroSimpleHeader(
    title: String,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
    isLoading: Boolean = false,
) {
    GymBroHeader(
        title = UiText.DynamicString(title),
        onNavigateBack = onNavigateBack,
        modifier = modifier,
        isLoading = isLoading,
    )
}

/**
 * 带操作按钮的Header
 */
@Composable
fun GymBroActionHeader(
    title: String,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
    isLoading: Boolean = false,
    actions: @Composable RowScope.() -> Unit = {},
) {
    GymBroHeader(
        title = UiText.DynamicString(title),
        onNavigateBack = onNavigateBack,
        modifier = modifier,
        isLoading = isLoading,
        trailingContent = actions,
    )
}

// === 兼容性别名 - 保持向后兼容 ===

/**
 * GymBroPageHeader别名
 * @deprecated 使用 GymBroHeader 替代
 */
@Deprecated(
    message = "Use GymBroHeader instead",
    replaceWith = ReplaceWith(
        "GymBroHeader(onNavigateBack = onNavigateBack, title = title, " +
            "isLoading = isLoading, trailingContent = actions, " +
            "contentDescription = contentDescription)",
        "com.example.gymbro.designSystem.components.base.header.GymBroHeader",
    ),
)
@Composable
fun GymBroPageHeader(
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
    title: UiText? = null,
    isLoading: Boolean = false,
    actions: @Composable RowScope.() -> Unit = {},
    contentDescription: String = "返回",
) {
    GymBroHeader(
        onNavigateBack = onNavigateBack,
        modifier = modifier,
        title = title,
        isLoading = isLoading,
        trailingContent = actions,
        contentDescription = contentDescription,
    )
}

/**
 * ProfilePageHeader别名
 * @deprecated 使用 GymBroHeader 替代
 */
@Deprecated(
    message = "Use GymBroHeader instead",
    replaceWith = ReplaceWith(
        "GymBroHeader(onNavigateBack = onNavigateBack, title = UiText.DynamicString(title), " +
            "isLoading = isLoading)",
        "com.example.gymbro.designSystem.components.base.header.GymBroHeader",
        "com.example.gymbro.shared.models.ui.UiText",
    ),
)
@Composable
fun ProfilePageHeader(
    onNavigateBack: () -> Unit,
    isLoading: Boolean,
    title: String,
    modifier: Modifier = Modifier,
) {
    GymBroHeader(
        onNavigateBack = onNavigateBack,
        modifier = modifier,
        title = UiText.DynamicString(title),
        isLoading = isLoading,
    )
}

// === Preview组件 ===

@GymBroPreview
@Composable
private fun GymBroHeaderPreview() {
    GymBroTheme {
        Surface {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp),
            ) {
                // 基础Header
                GymBroHeader(
                    title = UiText.DynamicString("基础标题"),
                    onNavigateBack = {},
                )

                // 带加载状态的Header
                GymBroHeader(
                    title = UiText.DynamicString("加载中..."),
                    onNavigateBack = {},
                    isLoading = true,
                )

                // 带操作按钮的Header
                GymBroHeader(
                    title = UiText.DynamicString("带操作按钮"),
                    onNavigateBack = {},
                    trailingContent = {
                        IconButton(onClick = {}) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "更多",
                            )
                        }
                    },
                )

                // 自定义前导内容的Header
                GymBroHeader(
                    title = UiText.DynamicString("自定义前导"),
                    onNavigateBack = {},
                    showBackButton = false,
                    leadingContent = {
                        IconButton(onClick = {}) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "菜单",
                            )
                        }
                    },
                )
            }
        }
    }
}
