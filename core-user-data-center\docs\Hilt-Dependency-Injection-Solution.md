# Hilt Dependency Injection Solution for UserDataCenter

## Problem Statement

We encountered a Hilt dependency injection conflict where `UserLocalDataSource` was bound multiple times:

1. **Simple Implementation** in `core-user-data-center` module: `UserLocalDataSourceSimpleImpl`
2. **Room Implementation** in `di` module: `UserLocalDataSourceRoomImpl`

This caused a duplicate binding error during Hilt compilation.

## Solution Overview

We resolved the conflict using **Hilt Qualifiers** to differentiate between implementations while ensuring the Room implementation takes precedence in production.

### Key Components

#### 1. Qualifiers (`UserDataSourceQualifiers.kt`)

```kotlin
@SimpleUserDataSource    // For in-memory implementation
@RoomUserDataSource      // For Room database implementation  
@DefaultUserDataSource   // Points to the default (Room) implementation
```

#### 2. Binding Configuration

**core-user-data-center module:**
```kotlin
@Binds @Singleton @SimpleUserDataSource
internal abstract fun bindSimpleUserLocalDataSource(
    impl: UserLocalDataSourceSimpleImpl
): UserLocalDataSource
```

**di module:**
```kotlin
@Binds @Singleton @RoomUserDataSource
internal abstract fun bindRoomUserLocalDataSource(
    roomImpl: UserLocalDataSourceRoomImpl
): UserLocalDataSource

@Binds @Singleton @DefaultUserDataSource  
internal abstract fun bindDefaultUserLocalDataSource(
    roomImpl: UserLocalDataSourceRoomImpl
): UserLocalDataSource
```

#### 3. Consumer Configuration

**UserDataRepositoryImpl:**
```kotlin
class UserDataRepositoryImpl @Inject constructor(
    @DefaultUserDataSource private val userLocalDataSource: UserLocalDataSource,
    // ...
)
```

## Architecture Benefits

### ✅ **Resolved Issues**
- **No Hilt Conflicts**: Each implementation has a unique qualifier
- **Room Implementation Active**: `@DefaultUserDataSource` ensures Room persistence is used
- **Testing Support**: `@SimpleUserDataSource` available for unit tests
- **Clean Architecture**: Maintains dependency inversion principles

### ✅ **Maintained Benefits**
- **No Circular Dependencies**: core-user-data-center doesn't depend on data module
- **Real Database Persistence**: Room implementation provides true persistence
- **Modular Design**: Clear separation of concerns between modules
- **Extensible**: Easy to add more implementations (e.g., remote, mock)

## Usage Patterns

### Production Usage
```kotlin
// Automatically gets Room implementation
@Inject constructor(
    @DefaultUserDataSource private val dataSource: UserLocalDataSource
)
```

### Testing Usage
```kotlin
// Gets simple in-memory implementation for fast tests
@Inject constructor(
    @SimpleUserDataSource private val dataSource: UserLocalDataSource
)
```

### Advanced Usage with Provider
```kotlin
@Inject constructor(
    private val provider: UserDataSourceProvider
) {
    fun useDefault() = provider.getDefault()    // Room implementation
    fun useSimple() = provider.getSimple()      // In-memory implementation
    fun useRoom() = provider.getRoom()          // Explicit Room implementation
}
```

## Implementation Details

### Module Structure
```
core-user-data-center/
├── qualifiers/          # @SimpleUserDataSource, @DefaultUserDataSource
├── simple-impl/         # UserLocalDataSourceSimpleImpl
└── bindings/           # Simple implementation binding

di/
├── adapters/           # UserDataDaoAdapter, UserProfileDaoAdapter  
├── room-impl/          # UserLocalDataSourceRoomImpl
├── extensions/         # Data conversion extensions
└── bindings/          # Room implementation bindings
```

### Dependency Flow
```
Application
    ↓ @DefaultUserDataSource
UserDataRepositoryImpl
    ↓ 
UserLocalDataSourceRoomImpl (di module)
    ↓
UserDataDaoAdapter + UserProfileDaoAdapter (di module)
    ↓
UserDao + UserProfileDao (data module)
    ↓
Room Database
```

## Testing Strategy

### Unit Tests
- Use `@SimpleUserDataSource` for fast, isolated tests
- No database setup required
- Predictable in-memory behavior

### Integration Tests  
- Use `@RoomUserDataSource` for database integration tests
- Real Room database behavior
- Test actual persistence logic

### Production
- Uses `@DefaultUserDataSource` (points to Room implementation)
- Real database persistence
- Full feature functionality

## Migration Notes

### Before (Conflicting)
```kotlin
// Both modules bound UserLocalDataSource without qualifiers
// Caused: [Dagger/DuplicateBindings] UserLocalDataSource is bound multiple times
```

### After (Resolved)
```kotlin
// Each implementation has unique qualifier
// Default points to Room implementation
// Simple available for testing
```

## Future Extensibility

The qualifier-based approach makes it easy to add new implementations:

```kotlin
@Qualifier
annotation class RemoteUserDataSource

@Qualifier  
annotation class MockUserDataSource

// Easy to add without conflicts
@Binds @RemoteUserDataSource
fun bindRemoteDataSource(impl: RemoteImpl): UserLocalDataSource
```

## Conclusion

This solution successfully resolves the Hilt dependency injection conflict while:
- ✅ Maintaining Clean Architecture principles
- ✅ Ensuring Room implementation is used in production  
- ✅ Keeping simple implementation available for testing
- ✅ Avoiding circular dependencies
- ✅ Providing clear, extensible architecture

The Room-based `UserLocalDataSource` is now the default implementation used throughout the application, providing real database persistence as intended.
