# Data Module Interfaces

> **💾 Data Layer Interfaces - Clean Architecture + MVI**
>
> **更新日期**: 2025-06-26 | **版本**: v1.0

This document outlines the public interfaces of the `data` module, which are implemented by the repositories and data sources.

## Repositories

The `data` module provides the implementations for the repository interfaces defined in the `domain` module. These repositories are responsible for coordinating data from the different data sources and providing a single source of truth for the application.

- `AICoachRepositoryImpl`: Implements the `AICoachRepository` interface.
- `AiStreamRepositoryImpl`: Implements the `AiStreamRepository` interface.
- `AuthRepositoryImpl`: Implements the `AuthRepository` interface.
- `BlockUserRepositoryImpl`: Implements the `BlockUserRepository` interface.
- `CalendarRepositoryImpl`: Implements the `CalendarRepository` interface.
- `ChatRepositoryImpl`: Implements the `ChatRepository` interface.
- `ChatSearchRepositoryImpl`: Implements the `ChatSearchRepository` interface.
- `ExerciseRepositoryImpl`: Implements the `ExerciseRepository` interface.
- `FavoriteRepositoryImpl`: Implements the `FavoriteRepository` interface.
- `FirebaseUserRepositoryImpl`: Implements the `FirebaseUserRepository` interface.
- `HistoryStateRepositoryImpl`: Implements the `HistoryStateRepository` interface.
- `MemoryRepositoryImpl`: Implements the `MemoryRepository` interface.
- `PaymentRepositoryImpl`: Implements the `PaymentRepository` interface.
- `PlanRepositoryImpl`: Implements the `PlanRepository` interface.
- `ProfileRepositoryImpl`: Implements the `ProfileRepository` interface.
- `QuickActionRepositoryImpl`: Implements the `QuickActionRepository` interface.
- `RegionDetectionRepository`: Implements the `RegionDetectionRepository` interface.
- `SearchRepositoryImpl`: Implements the `SearchRepository` interface.
- `SessionRepositoryImpl`: Implements the `SessionRepository` interface.
- `SettingsRepositoryImpl`: Implements the `SettingsRepository` interface.
- `SubscriptionRepositoryImpl`: Implements the `SubscriptionRepository` interface.
- `TempUserRepositoryImpl`: Implements the `UserRepository` interface.
- `ThemeRepositoryImpl`: Implements the `ThemeRepository` interface.
- `UserPreferenceRepositoryImpl`: Implements the `UserPreferenceRepository` interface.
- `UserProfileRepositoryImpl`: Implements the `UserProfileRepository` interface.
- `UserSettingsRepositoryImpl`: Implements the `UserSettingsRepository` interface.
- `UserStatsRepositoryImpl`: Implements the `UserStatsRepository` interface.
- `VersionConfigRepositoryImpl`: Implements the `VersionConfigRepository` interface.

## Data Sources

The `data` module also defines a set of data source interfaces that abstract the data sources from the repositories. These interfaces are implemented by the local and remote data sources.

- `AuthDataSource`: Defines the interface for the authentication data source.
- `UserDataSource`: Defines the interface for the user data source.
- `UserSettingsDataSource`: Defines the interface for the user settings data source.
