package com.example.gymbro.designSystem.components.extensions

import androidx.compose.animation.core.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.theme.motion.LocalGymBroMotionConfig
import com.example.gymbro.designSystem.theme.motion.MotionDurations
import com.example.gymbro.designSystem.theme.motion.MotionEasings
import com.example.gymbro.designSystem.theme.motion.MotionSpecs

/**
 * GymBro动画扩展函数
 * 提供统一的动画行为和参数
 */

/**
 * 呼吸动画扩展 - 统一的上下浮动 + Alpha变化
 *
 * @param amplitude 浮动幅度，默认中等
 * @param duration 动画周期，默认长周期
 * @param enableAlpha 是否启用透明度变化
 * @param alphaMin 最小透明度
 * @param alphaMax 最大透明度
 */
fun Modifier.gymBroBreathing(
    amplitude: Dp = 16.dp, // MotionDurations.BREATHING_AMPLITUDE_MEDIUM
    duration: Int = MotionDurations.L,
    enableAlpha: Boolean = true,
    alphaMin: Float = 0.6f, // MotionDurations.BREATHING_ALPHA_MIN
    alphaMax: Float = 1.0f, // MotionDurations.BREATHING_ALPHA_MAX
) = composed {
    val motionConfig = LocalGymBroMotionConfig.current
    val density = LocalDensity.current

    // 检查是否应该禁用动画
    if (!motionConfig.enableAnimations ||
        !motionConfig.enableBreathing
    ) {
        return@composed this
    }

    val amplitudePx = with(density) { amplitude.toPx() }

    val infiniteTransition = rememberInfiniteTransition(label = "gymBroBreathing")

    val offsetY by infiniteTransition.animateFloat(
        initialValue = -amplitudePx,
        targetValue = amplitudePx,
        animationSpec =
        infiniteRepeatable(
            animation = tween(duration, easing = MotionEasings.STANDARD),
            repeatMode = RepeatMode.Reverse,
        ),
        label = "breathingOffset",
    )

    val alpha by infiniteTransition.animateFloat(
        initialValue = alphaMin,
        targetValue = alphaMax,
        animationSpec =
        infiniteRepeatable(
            animation = tween(duration, easing = MotionEasings.STANDARD),
            repeatMode = RepeatMode.Reverse,
        ),
        label = "breathingAlpha",
    )

    graphicsLayer {
        translationY = offsetY
        if (enableAlpha) {
            this.alpha = alpha
        }
    }
}

/**
 * 按钮按压动画扩展 - 快速缩放反馈
 */
fun Modifier.gymBroClickable(
    pressed: Boolean,
    scalePressed: Float = 0.97f,
    duration: Int = MotionDurations.XS,
) = composed {
    val motionConfig = LocalGymBroMotionConfig.current

    if (!motionConfig.enableAnimations) {
        return@composed this
    }

    val scale by animateFloatAsState(
        targetValue = if (pressed) scalePressed else 1f,
        animationSpec = MotionSpecs.tweenXS(),
        label = "clickScale",
    )

    graphicsLayer {
        scaleX = scale
        scaleY = scale
    }
}

/**
 * 进入动画扩展 - 标准的淡入+向上滑动
 */
fun Modifier.gymBroEnterAnimation(
    visible: Boolean,
    slideDistance: Dp = 20.dp,
    duration: Int = MotionDurations.S,
) = composed {
    val motionConfig = LocalGymBroMotionConfig.current
    val density = LocalDensity.current

    if (!motionConfig.enableAnimations) {
        return@composed this
    }

    val slideDistancePx = with(density) { slideDistance.toPx() }

    val alpha by animateFloatAsState(
        targetValue = if (visible) 1f else 0f,
        animationSpec = MotionSpecs.tweenS(),
        label = "enterAlpha",
    )

    val offsetY by animateFloatAsState(
        targetValue = if (visible) 0f else slideDistancePx,
        animationSpec = MotionSpecs.tweenS(),
        label = "enterOffset",
    )

    graphicsLayer {
        this.alpha = alpha
        translationY = offsetY
    }
}

/**
 * 加载动画扩展 - 旋转动画
 */
fun Modifier.gymBroLoadingRotation(
    isLoading: Boolean,
    duration: Int = MotionDurations.M,
) = composed {
    val motionConfig = LocalGymBroMotionConfig.current

    if (!motionConfig.enableAnimations) {
        return@composed this
    }

    val infiniteTransition = rememberInfiniteTransition(label = "loadingRotation")

    val rotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = if (isLoading) 360f else 0f,
        animationSpec =
        infiniteRepeatable(
            animation = tween(duration, easing = MotionEasings.LINEAR),
            repeatMode = RepeatMode.Restart,
        ),
        label = "loadingRotation",
    )

    graphicsLayer {
        rotationZ = if (isLoading) rotation else 0f
    }
}

/**
 * 脉冲动画扩展 - 周期性缩放
 */
fun Modifier.gymBroPulse(
    enabled: Boolean = true,
    scaleMin: Float = 0.95f,
    scaleMax: Float = 1.05f,
    duration: Int = MotionDurations.M,
) = composed {
    val motionConfig = LocalGymBroMotionConfig.current

    if (!enabled || !motionConfig.enableAnimations) {
        return@composed this
    }

    val infiniteTransition = rememberInfiniteTransition(label = "pulse")

    val scale by infiniteTransition.animateFloat(
        initialValue = scaleMin,
        targetValue = scaleMax,
        animationSpec =
        infiniteRepeatable(
            animation = MotionSpecs.tweenM(),
            repeatMode = RepeatMode.Reverse,
        ),
        label = "pulseScale",
    )

    graphicsLayer {
        scaleX = scale
        scaleY = scale
    }
}
