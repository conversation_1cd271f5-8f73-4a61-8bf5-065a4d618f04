package com.example.gymbro.data.database.migrations

import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

/**
 * Migration from version 1 to 2 for PlanDatabase
 * 
 * Adds progress field to plan_days table with default value 'NOT_STARTED'
 * 
 * Changes:
 * - Adds 'progress' column to plan_days table
 * - Sets default value to 'NOT_STARTED' for existing rows
 * - Creates index on progress column for performance
 */
object Migration_1_2 : Migration(1, 2) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // Add progress column with default value
        database.execSQL(
            """
            ALTER TABLE plan_days 
            ADD COLUMN progress TEXT NOT NULL DEFAULT 'NOT_STARTED'
            """.trimIndent()
        )
        
        // Create index on progress column for better query performance
        database.execSQL(
            """
            CREATE INDEX IF NOT EXISTS index_plan_days_progress 
            ON plan_days(progress)
            """.trimIndent()
        )
    }
}
