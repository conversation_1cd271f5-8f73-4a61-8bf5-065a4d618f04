
---

### **修正版：迁移与重构计划 (V3.1)**

**目标**: 彻底移除所有基于类的 Mapper（如 `UserEntityToDomainMapper`），统一为扩展函数。

#### **执行步骤 (可分批进行)**:

1.  **选择目标 (Select a Target)**:
    -   从项目中选择一个**现有的、仍在使用旧 Mapper 类**的 `RepositoryImpl` 文件，例如 `UserRepositoryImpl.kt`。

2.  **创建/确认映射函数 (Create/Confirm Mapping Function)**:
    -   打开 `data/mapper/` 目录下的相应文件（如 `UserMappers.kt`）。
    -   确保该 `RepositoryImpl` 所需的 `.toDomain()` 或 `.toEntity()` 等扩展函数已经存在。如果不存在，请根据旧 `Mapper` 类中的逻辑创建它们。

3.  **直接修改 Repository 实现 (Directly Modify the Repository)**:
    -   打开**现有的** `UserRepositoryImpl.kt` 文件。
    -   从其构造函数中**移除**对 `UserMapper` 的注入。
    -   在实现方法中，将 `userMapper.map(entity)` 的调用方式，**替换**为 `entity.toDomain()`。
    -   确保已 `import` 新的扩展函数。

    **修改前 (示例):**
    ```kotlin
    // UserRepositoryImpl.kt
    class UserRepositoryImpl @Inject constructor(
        private val localDataSource: UserLocalDataSource,
        private val userMapper: UserMapper // 将被移除
    ) : UserRepository {
        override suspend fun getUser(id: String): User {
            val userEntity = localDataSource.getUserById(id)
            return userMapper.map(userEntity) // 旧的调用方式
        }
    }
    ```

    **修改后 (示例):**
    ```kotlin
    // UserRepositoryImpl.kt (直接修改此文件)
    class UserRepositoryImpl @Inject constructor(
        private val localDataSource: UserLocalDataSource
        // Mapper 已被移除
    ) : UserRepository {
        override suspend fun getUser(id: String): User {
            val userEntity = localDataSource.getUserById(id)
            return userEntity.toDomain() // 新的、直接的调用方式
        }
    }
    ```

4.  **删除旧代码 (Delete Old Code)**:
    -   在确认旧的 `UserMapper` 类不再有任何被引用的地方之后，**安全地删除** `UserMapper.kt` 文件。
    -   如果在 `di` 模块中有相关的绑定，一并删除。

5.  **重复 (Repeat)**:
    -   选择下一个仍在使用旧模式的 `RepositoryImpl`，重复以上 1-4 步，直到项目中再无此类文件。

再次感谢您的宝贵反馈，这使得这份指南变得更加清晰和准确。
