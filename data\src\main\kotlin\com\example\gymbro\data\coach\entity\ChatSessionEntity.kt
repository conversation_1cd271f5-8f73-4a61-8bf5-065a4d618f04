package com.example.gymbro.data.local.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import kotlinx.datetime.Instant

/**
 * 聊天会话数据库实体
 *
 * 存储聊天会话的基本信息和元数据
 * 与ChatRaw表建立一对多关系
 */
@Entity(
    tableName = "chat_sessions",
    indices = [
        Index(value = ["user_id"]),
        Index(value = ["user_id", "is_active"]),
        Index(value = ["user_id", "last_active_at"]),
        Index(value = ["created_at"]),
        Index(value = ["status"]),
    ],
)
data class ChatSessionEntity(
    /**
     * 会话唯一标识符
     */
    @PrimaryKey
    val id: String,

    /**
     * 会话标题
     */
    @ColumnInfo(name = "title")
    val title: String,

    /**
     * 用户ID
     */
    @ColumnInfo(name = "user_id")
    val userId: String,

    /**
     * 会话创建时间（时间戳）
     */
    @ColumnInfo(name = "created_at")
    val createdAt: Long,

    /**
     * 最后活跃时间（时间戳）
     */
    @ColumnInfo(name = "last_active_at")
    val lastActiveAt: Long,

    /**
     * 会话是否处于活跃状态
     */
    @ColumnInfo(name = "is_active")
    val isActive: Boolean = true,

    /**
     * 会话状态
     * 0: ACTIVE, 1: ARCHIVED, 2: DELETED
     */
    @ColumnInfo(name = "status")
    val status: Int = 0,

    /**
     * 消息总数
     */
    @ColumnInfo(name = "message_count")
    val messageCount: Int = 0,

    /**
     * 🔧 新增：会话概要（由AI生成）
     */
    @ColumnInfo(name = "summary")
    val summary: String? = null,

    /**
     * 会话元数据（JSON格式）
     */
    @ColumnInfo(name = "metadata")
    val metadata: String? = null,

    /**
     * 记录创建时间（用于数据库管理）
     */
    @ColumnInfo(name = "db_created_at")
    val dbCreatedAt: Long = System.currentTimeMillis(),

    /**
     * 记录更新时间（用于数据库管理）
     */
    @ColumnInfo(name = "db_updated_at")
    val dbUpdatedAt: Long = System.currentTimeMillis(),
) {
    companion object {
        /**
         * 会话状态常量
         */
        const val STATUS_ACTIVE = 0
        const val STATUS_ARCHIVED = 1
        const val STATUS_DELETED = 2

        /**
         * 从领域模型创建实体
         */
        fun fromDomain(session: com.example.gymbro.domain.coach.model.ChatSession): ChatSessionEntity {
            return ChatSessionEntity(
                id = session.id,
                title = session.title,
                userId = session.userId,
                createdAt = session.createdAt.toEpochMilliseconds(),
                lastActiveAt = session.lastActiveAt.toEpochMilliseconds(),
                isActive = session.isActive,
                status = when (session.status) {
                    com.example.gymbro.domain.coach.model.ChatSession.SessionStatus.ACTIVE -> STATUS_ACTIVE
                    com.example.gymbro.domain.coach.model.ChatSession.SessionStatus.ARCHIVED -> STATUS_ARCHIVED
                    com.example.gymbro.domain.coach.model.ChatSession.SessionStatus.DELETED -> STATUS_DELETED
                },
                summary = session.summary, // 🔧 新增：映射概要字段
                messageCount = session.messageCount,
                metadata = if (session.metadata.isNotEmpty()) {
                    kotlinx.serialization.json.Json.encodeToString(
                        kotlinx.serialization.serializer<Map<String, String>>(),
                        session.metadata,
                    )
                } else {
                    null
                },
            )
        }
    }

    /**
     * 转换为领域模型
     */
    fun toDomain(
        messages: List<com.example.gymbro.domain.coach.model.CoachMessage> = emptyList(),
    ): com.example.gymbro.domain.coach.model.ChatSession {
        return com.example.gymbro.domain.coach.model.ChatSession(
            id = id,
            title = title,
            userId = userId,
            createdAt = Instant.fromEpochMilliseconds(createdAt),
            lastActiveAt = Instant.fromEpochMilliseconds(lastActiveAt),
            isActive = isActive,
            status = when (status) {
                STATUS_ACTIVE -> com.example.gymbro.domain.coach.model.ChatSession.SessionStatus.ACTIVE
                STATUS_ARCHIVED -> com.example.gymbro.domain.coach.model.ChatSession.SessionStatus.ARCHIVED
                STATUS_DELETED -> com.example.gymbro.domain.coach.model.ChatSession.SessionStatus.DELETED
                else -> com.example.gymbro.domain.coach.model.ChatSession.SessionStatus.ACTIVE
            },
            messageCount = messageCount,
            messages = messages,
            summary = summary, // 🔧 新增：映射概要字段
            metadata = metadata?.let {
                try {
                    kotlinx.serialization.json.Json.decodeFromString(
                        kotlinx.serialization.serializer<Map<String, String>>(),
                        it,
                    )
                } catch (e: Exception) {
                    emptyMap()
                }
            } ?: emptyMap(),
        )
    }
}
