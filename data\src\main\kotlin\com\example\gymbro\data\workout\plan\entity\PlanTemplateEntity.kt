package com.example.gymbro.data.workout.plan.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * 计划模板关联实体 - PlanDB 关联实体 (扩展版)
 *
 * 🎯 架构升级：支持dailySchedule新架构
 * - 表示计划日与训练模板的关联关系
 * - 支持一天多个Template调度
 * - 支持Template执行顺序
 *
 * 基于 05_Final_Database_Architecture.md 设计
 * 存储计划日与训练模板的多对多关联
 */
@Entity(
    tableName = "plan_templates",
    foreignKeys = [
        ForeignKey(
            entity = PlanDayEntity::class,
            parentColumns = ["id"],
            childColumns = ["planDayId"],
            onDelete = ForeignKey.CASCADE,
        ),
    ],
    indices = [
        Index("planDayId"),
        Index("templateId"),
        Index("order"),
    ],
)
data class PlanTemplateEntity(
    @PrimaryKey
    val id: String,

    // 关联信息
    val planDayId: String, // 关联的计划日ID
    val templateId: String, // 引用 TemplateEntity.id

    // 执行配置
    val order: Int = 0, // 在当日的执行顺序

    // 时间戳
    val createdAt: Long = System.currentTimeMillis(),
)
