package com.example.gymbro.data.remote.subscription.dto

import com.google.gson.annotations.SerializedName

/**
 * 订阅DTO
 * 定义远程API的订阅数据结构
 */
data class SubscriptionDto(
    /**
     * 订阅ID
     */
    @SerializedName("id")
    val id: String,
    /**
     * 用户ID
     */
    @SerializedName("userId")
    val userId: String,
    /**
     * 计划ID
     */
    @SerializedName("planId")
    val planId: String? = null,
    /**
     * 计划类型
     */
    @SerializedName("planType")
    val planType: String? = null,
    /**
     * 订阅状态
     */
    @SerializedName("status")
    val status: String? = null,
    /**
     * 订阅开始日期（时间戳毫秒）
     */
    @SerializedName("startDate")
    val startDate: Long? = null,
    /**
     * 订阅结束日期（时间戳毫秒）
     */
    @SerializedName("endDate")
    val endDate: Long? = null,
    /**
     * 试用期结束日期（时间戳毫秒）
     */
    @SerializedName("trialEndDate")
    val trialEndDate: Long? = null,
    /**
     * 支付方式
     */
    @SerializedName("paymentMethod")
    val paymentMethod: String? = null,
    /**
     * 支付ID
     */
    @SerializedName("paymentId")
    val paymentId: String? = null,
    /**
     * 是否自动续订
     */
    @SerializedName("autoRenew")
    val autoRenew: Boolean? = null,
    /**
     * 取消日期（时间戳毫秒）
     */
    @SerializedName("cancelDate")
    val cancelDate: Long? = null,
    /**
     * 价格
     */
    @SerializedName("price")
    val price: Double? = null,
    /**
     * 货币代码
     */
    @SerializedName("currency")
    val currency: String? = null,
    /**
     * 名称
     */
    @SerializedName("name")
    val name: String? = null,
    /**
     * 描述
     */
    @SerializedName("description")
    val description: String? = null,
    /**
     * 功能列表
     */
    @SerializedName("features")
    val features: List<String>? = null,
    /**
     * 创建时间（时间戳毫秒）
     */
    @SerializedName("createdAt")
    val createdAt: Long? = null,
    /**
     * 更新时间（时间戳毫秒）
     */
    @SerializedName("updatedAt")
    val updatedAt: Long? = null,
)
