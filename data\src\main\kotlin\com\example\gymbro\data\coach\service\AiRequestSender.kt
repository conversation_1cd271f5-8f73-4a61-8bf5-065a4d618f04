package com.example.gymbro.data.coach.service

import com.example.gymbro.core.ai.prompt.builder.ConversationTurn
import com.example.gymbro.core.ai.prompt.builder.CoreChatMessage
import com.example.gymbro.core.ai.prompt.builder.LayeredPromptBuilder
import com.example.gymbro.core.ai.prompt.registry.PromptRegistry
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.core.util.Constants
import com.example.gymbro.data.coach.dao.ChatRawDao
import com.example.gymbro.data.local.entity.ChatRaw
import com.example.gymbro.domain.coach.config.AiProviderManager
import com.example.gymbro.domain.coach.model.AiTaskType
import com.example.gymbro.domain.coach.repository.TaskCapabilities
import com.example.gymbro.shared.models.ai.ChatRequest
import kotlinx.datetime.Clock
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * AI请求发送服务
 *
 * 职责：
 * - 构建AI请求
 * - 选择合适的Provider配置
 * - 管理任务类型路由
 * - 插入thinking占位符
 */
@Singleton
class AiRequestSender @Inject constructor(
    private val chatRawDao: ChatRawDao,
    private val chatSessionDao: com.example.gymbro.data.coach.dao.ChatSessionDao,
    private val aiProviderManager: AiProviderManager,
    private val promptRegistry: PromptRegistry,
    private val layeredPromptBuilder: LayeredPromptBuilder,
) {

    /**
     * 串行插入thinking占位
     */
    suspend fun insertThinking(
        sessionId: String,
        prompt: String,
    ): ModernResult<String> = try {
        val thinkingId = Constants.MessageId.generate()

        // 创建thinking占位消息
        val thinkingMessage = ChatRaw(
            sessionId = sessionId,
            role = "assistant",
            content = "AI正在思考中...",
            timestamp = Clock.System.now().toEpochMilliseconds(),
            metadata = mapOf(
                "type" to "thinking",
                "thinkingId" to thinkingId,
                "prompt" to prompt.take(100), // 截断prompt避免过长
            ),
        )

        // 串行插入到数据库（使用IGNORE策略防止冲突）
        val insertedId = chatRawDao.insertThinking(thinkingMessage)

        Timber.d("串行插入thinking成功: thinkingId=$thinkingId, dbId=$insertedId")
        ModernResult.Success(thinkingId)
    } catch (e: Exception) {
        Timber.e(e, "串行插入thinking失败")
        ModernResult.Error(
            ModernDataError(
                operationName = "insertThinking",
                errorType = GlobalErrorType.Database.QueryFailed,
                uiMessage = UiText.DynamicString("插入思考状态失败"),
                cause = e,
            ),
        )
    }

    /**
     * 构建AI请求消息列表
     */
    suspend fun buildRequestMessages(
        sessionId: String,
        prompt: String,
        taskType: AiTaskType,
    ): List<CoreChatMessage> {
        Timber.d("🔥 开始构建AI请求消息: sessionId=$sessionId, taskType=$taskType")

        // 构建历史对话
        val history = buildHistory(sessionId, n = 8)

        // 获取用户ID
        val userId = try {
            chatSessionDao.getSessionById(sessionId)?.userId ?: "unknown"
        } catch (e: Exception) {
            Timber.w(e, "获取用户ID失败，使用默认值")
            "unknown"
        }

        // 使用LayeredPromptBuilder构建完整的消息列表
        return layeredPromptBuilder.buildChatMessages(
            systemLayer = null, // 使用默认系统层
            userInput = prompt,
            history = history,
            userId = userId,
            forceOmitSystemPrompt = false,
            model = null // 使用默认模型
        )
    }

    /**
     * 根据任务类型优化请求参数
     */
    fun optimizeRequestForTask(
        request: ChatRequest,
        taskType: AiTaskType,
    ): ChatRequest {
        // 根据任务类型选择合适的提供商配置
        val providerConfig = when (taskType) {
            AiTaskType.TITLE_GENERATION, AiTaskType.SUMMARY -> {
                aiProviderManager.getSummaryProviderConfig()
            }
            else -> {
                aiProviderManager.currentProvider.value
            }
        }

        // 根据任务类型和提供商配置调整请求参数
        return when (taskType) {
            AiTaskType.TITLE_GENERATION -> request.copy(
                model = providerConfig.summaryModel,
                maxTokens = 100,
                temperature = 0.8,
            )
            AiTaskType.SUMMARY -> request.copy(
                model = providerConfig.summaryModel,
                maxTokens = 500,
                temperature = 0.3,
            )
            else -> request.copy(
                model = providerConfig.defaultModel,
                maxTokens = providerConfig.maxTokens,
                temperature = 0.7,
            )
        }
    }

    /**
     * 获取任务类型支持的功能
     */
    suspend fun getTaskCapabilities(taskType: AiTaskType): TaskCapabilities {
        // 根据任务类型选择合适的提供商配置
        val providerConfig = when (taskType) {
            AiTaskType.TITLE_GENERATION, AiTaskType.SUMMARY -> {
                aiProviderManager.getSummaryProviderConfig()
            }
            else -> {
                aiProviderManager.currentProvider.value
            }
        }

        // 从可用提供商列表中获取支持的提供商名称
        val supportedProviders = aiProviderManager.availableProviders.map { it.name }

        return when (taskType) {
            AiTaskType.TITLE_GENERATION -> TaskCapabilities(
                taskType = taskType,
                supportedProviders = supportedProviders,
                recommendedModel = providerConfig.summaryModel,
                maxTokens = 100,
                temperatureRange = 0.5f..0.9f,
            )
            AiTaskType.SUMMARY -> TaskCapabilities(
                taskType = taskType,
                supportedProviders = supportedProviders,
                recommendedModel = providerConfig.summaryModel,
                maxTokens = 500,
                temperatureRange = 0.1f..0.5f,
            )
            AiTaskType.TRAINING_PLAN -> TaskCapabilities(
                taskType = taskType,
                supportedProviders = supportedProviders,
                recommendedModel = providerConfig.defaultModel,
                maxTokens = 3000,
                temperatureRange = 0.3f..0.7f,
            )
            AiTaskType.NUTRITION_ADVICE -> TaskCapabilities(
                taskType = taskType,
                supportedProviders = supportedProviders,
                recommendedModel = providerConfig.defaultModel,
                maxTokens = 2000,
                temperatureRange = 0.3f..0.7f,
            )
            else -> TaskCapabilities(
                taskType = taskType,
                supportedProviders = supportedProviders,
                recommendedModel = providerConfig.defaultModel,
                maxTokens = providerConfig.maxTokens,
                temperatureRange = 0.1f..1.0f,
            )
        }
    }

    /**
     * 把最近 N 条对话转成 ConversationTurn 列表
     */
    private suspend fun buildHistory(
        sessionId: String,
        n: Int = 8,
    ): List<ConversationTurn> = chatRawDao
        .getRecentChatMessagesBySession(sessionId, n)
        .reversed() // 保证时间正序
        .chunked(2) // 每两条消息为一组
        .mapNotNull { chunk ->
            if (chunk.size == 2) {
                val userMsg = chunk.find { it.role == "user" }
                val assistantMsg = chunk.find { it.role == "assistant" }
                if (userMsg != null && assistantMsg != null) {
                    ConversationTurn(user = userMsg.content, assistant = assistantMsg.content)
                } else {
                    null
                }
            } else {
                null
            }
        }
}
