package com.example.gymbro.core.ml.embedding

import com.example.gymbro.core.ml.tokenizer.BgeTokenizer
import com.example.gymbro.core.ml.tokenizer.TokenizationResult
import com.example.gymbro.core.ml.utils.VectorUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.InputStream

/**
 * 基于ONNX Runtime的嵌入引擎实现
 *
 * 相比TensorFlow Lite，ONNX Runtime在某些情况下可能有更好的性能表现
 */
class OnnxEmbeddingEngine(
    modelStream: InputStream,
    vocabularyStream: InputStream,
    override val maxSequenceLength: Int = 128, // 🔥 修复：BGE模型期望128，不是512
    override val embeddingDim: Int = 384,
) : EmbeddingEngine {
    private var ortSession: Any? = null // ONNX Runtime Session placeholder
    private val tokenizer: BgeTokenizer
    private var isInitialized = false

    init {
        tokenizer = BgeTokenizer(vocabularyStream, maxSequenceLength)
        initializeModel(modelStream)
    }

    /**
     * 初始化ONNX Runtime会话
     * 注意：需要在项目中添加ONNX Runtime依赖
     */
    private fun initializeModel(modelStream: InputStream) {
        try {
            // TODO: 实际实现需要添加 ONNX Runtime 依赖
            /*
            val ortEnvironment = OrtEnvironment.getEnvironment()
            val sessionOptions = OrtSession.SessionOptions()

            // 可选：启用优化
            sessionOptions.setOptimizationLevel(OrtSession.SessionOptions.OptLevel.ALL_OPT)

            // 可选：设置线程数
            sessionOptions.setIntraOpNumThreads(4)

            val modelBytes = modelStream.readBytes()
            ortSession = ortEnvironment.createSession(modelBytes, sessionOptions)
             */

            // 暂时标记为已初始化
            isInitialized = true
        } catch (e: Exception) {
            throw RuntimeException("Failed to initialize ONNX model", e)
        }
    }

    override suspend fun embed(text: String): FloatArray =
        withContext(Dispatchers.Default) {
            require(isInitialized) { "ONNX模型未正确初始化" }
            require(text.isNotBlank()) { "输入文本不能为空" }

            try {
                // 1. 分词处理
                val tokenizationResult = tokenizer.tokenize(text)

                // 2. 运行ONNX推理
                val embedding =
                    runOnnxInference(
                        tokenizationResult.inputIds,
                        tokenizationResult.attentionMask,
                    )

                // 3. L2归一化
                VectorUtils.normalizeL2(embedding)
            } catch (e: Exception) {
                throw RuntimeException("ONNX嵌入推理失败: ${e.message}", e)
            }
        }

    override suspend fun embedBatch(texts: List<String>): List<FloatArray> =
        withContext(Dispatchers.Default) {
            require(isInitialized) { "ONNX模型未正确初始化" }
            require(texts.isNotEmpty()) { "文本列表不能为空" }

            // ONNX Runtime通常支持更大的批处理
            val batchSize = 16
            val results = mutableListOf<FloatArray>()

            for (i in texts.indices step batchSize) {
                val batchEnd = minOf(i + batchSize, texts.size)
                val batch = texts.subList(i, batchEnd)

                // 批量分词
                val tokenizedBatch =
                    batch.map { text ->
                        tokenizer.tokenize(text)
                    }

                // 批量推理
                val batchResults = runBatchOnnxInference(tokenizedBatch)
                results.addAll(batchResults)
            }

            results
        }

    /**
     * 执行单个样本的ONNX推理
     */
    private fun runOnnxInference(
        inputIds: IntArray,
        attentionMask: IntArray,
    ): FloatArray {
        // TODO: 实际实现需要调用ONNX Runtime
        /*
        实际实现示例：

        val env = OrtEnvironment.getEnvironment()

        // 创建输入tensor
        val inputIdsTensor = OnnxTensor.createTensor(
            env,
            arrayOf(inputIds.map { it.toLong() }.toLongArray())
        )
        val attentionMaskTensor = OnnxTensor.createTensor(
            env,
            arrayOf(attentionMask.map { it.toLong() }.toLongArray())
        )

        val inputs = mapOf(
            "input_ids" to inputIdsTensor,
            "attention_mask" to attentionMaskTensor
        )

        // 运行推理
        val results = ortSession?.run(inputs)
        val outputTensor = results?.get(0)?.value as? Array<Array<FloatArray>>

        // 提取[CLS] token的嵌入（第一个token）
        val embedding = outputTensor?.get(0)?.get(0) ?: FloatArray(embeddingDim)

        // 清理资源
        inputIdsTensor.close()
        attentionMaskTensor.close()
        results?.close()

        return embedding
         */

        // 模拟返回随机向量（仅用于演示）
        return FloatArray(embeddingDim) { kotlin.random.Random.nextFloat() * 2f - 1f }
    }

    /**
     * 执行批量ONNX推理
     */
    private fun runBatchOnnxInference(
        tokenizedBatch: List<TokenizationResult>,
    ): List<FloatArray> {
        // TODO: 实际批量推理实现
        /*
        实际实现需要：
        1. 将批量的tokenization结果合并成批量tensor
        2. 一次性推理整个批次
        3. 从输出中提取每个样本的嵌入
         */

        // 模拟实现：逐个处理
        return tokenizedBatch.map { tokenized ->
            runOnnxInference(tokenized.inputIds, tokenized.attentionMask)
        }
    }

    /**
     * 预热模型
     */
    override suspend fun warmUp() {
        try {
            embed("warmup")
        } catch (e: Exception) {
            println("ONNX模型预热失败: ${e.message}")
        }
    }

    /**
     * 获取模型信息
     */
    override fun getModelInfo(): ModelInfo {
        return ModelInfo(
            modelName = "BGE-ONNX",
            embeddingDim = embeddingDim,
            maxSequenceLength = maxSequenceLength,
            isInitialized = isInitialized,
        )
    }

    /**
     * 🔥 文本摘要生成 - 富文本功能新增
     */
    override suspend fun summarize(text: String, maxLength: Int): String {
        // 简单的截取式摘要实现
        return if (text.length <= maxLength) {
            text
        } else {
            // 寻找合适的截取点（句号、问号、感叹号）
            val punctuations = listOf("。", "？", "！", ".", "?", "!")

            for (punct in punctuations) {
                val index = text.indexOf(punct)
                if (index in 5..maxLength) {
                    return text.substring(0, index + 1)
                }
            }

            // 没有合适标点，直接截取
            text.take(maxLength) + "..."
        }
    }

    /**
     * 🔥 计算两个向量的余弦相似度 - 富文本功能新增
     */
    override fun cosineSimilarity(vector1: FloatArray, vector2: FloatArray): Float {
        require(vector1.size == vector2.size) { "向量维度不匹配: ${vector1.size} vs ${vector2.size}" }

        var dotProduct = 0.0f
        var normA = 0.0f
        var normB = 0.0f

        for (i in vector1.indices) {
            dotProduct += vector1[i] * vector2[i]
            normA += vector1[i] * vector1[i]
            normB += vector2[i] * vector2[i]
        }

        normA = kotlin.math.sqrt(normA)
        normB = kotlin.math.sqrt(normB)

        return if (normA == 0.0f || normB == 0.0f) {
            0.0f
        } else {
            dotProduct / (normA * normB)
        }
    }

    /**
     * 获取模型性能信息
     */
    fun getPerformanceInfo(): PerformanceInfo =
        PerformanceInfo(
            modelFormat = "ONNX",
            supportsBatching = true,
            optimalBatchSize = 16,
            averageLatencyMs = estimateLatency(),
        )

    /**
     * 估算推理延迟
     */
    private fun estimateLatency(): Long {
        // TODO: 实际测量推理时间
        return 15L // 模拟15ms延迟
    }

    override fun close() {
        try {
            // TODO: 释放ONNX Runtime资源
            /*
            ortSession?.close()
            OrtEnvironment.getEnvironment().close()
             */

            ortSession = null
            isInitialized = false
        } catch (e: Exception) {
            println("释放ONNX模型资源时出错: ${e.message}")
        }
    }

    /**
     * 性能信息数据类
     */
    data class PerformanceInfo(
        val modelFormat: String,
        val supportsBatching: Boolean,
        val optimalBatchSize: Int,
        val averageLatencyMs: Long,
    )
}
