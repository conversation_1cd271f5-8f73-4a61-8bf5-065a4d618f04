package com.example.gymbro.data.workout.plan.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import com.example.gymbro.shared.models.workout.PlanProgressStatus

/**
 * 计划日实体 - PlanDB 日程实体 (扩展版)
 *
 * 🎯 架构升级：支持dailySchedule新架构
 * - 支持多Template调度
 * - 支持休息日标记
 * - 支持预估时长
 * - 支持排序索引
 *
 * 基于 05_Final_Database_Architecture.md 设计
 * 存储计划中每一天的配置信息
 */
@Entity(
    tableName = "plan_days",
    foreignKeys = [
        ForeignKey(
            entity = PlanEntity::class,
            parentColumns = ["id"],
            childColumns = ["planId"],
            onDelete = ForeignKey.CASCADE,
        ),
    ],
    indices = [
        Index("planId"),
        Index("dayNumber"),
        Index("orderIndex"),
        Index("isCompleted"), // 新增索引
        Index("progress"), // 进度状态索引
    ],
)
data class PlanDayEntity(
    @PrimaryKey
    val id: String,

    // 关联信息
    val planId: String, // 关联的计划ID
    val dayNumber: Int, // 第几天（从1开始）

    // 日程配置
    val isRestDay: Boolean = false, // 是否为休息日
    val notes: String? = null, // 当日备注
    val orderIndex: Int = 0, // 排序索引，用于UI显示顺序
    val estimatedDuration: Int? = null, // 预估训练时长（分钟）
    val isCompleted: Boolean = false, // 是否已完成（新增）
    val progress: PlanProgressStatus = PlanProgressStatus.NOT_STARTED, // 进度状态

    // 时间戳
    val createdAt: Long = System.currentTimeMillis(),
)
