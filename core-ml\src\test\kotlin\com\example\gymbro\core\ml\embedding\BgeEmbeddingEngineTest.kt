/*
package com.example.gymbro.core.ml.embedding

import android.content.Context
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.gymbro.core.ml.config.BgeModelConfig
import com.google.common.truth.Truth.assertThat
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.kotlin.mock
import kotlin.math.abs
import kotlin.math.sqrt

/**
 * BGE引擎真实实现测试
 * 验证向量化引擎的基本功能和输出质量
 */
@RunWith(AndroidJUnit4::class)
class BgeEmbeddingEngineTest {

    private lateinit var context: Context
    private lateinit var bgeEngine: BgeEmbeddingEngine

    @Before
    fun setup() {
        context = ApplicationProvider.getApplicationContext()
        bgeEngine = BgeEmbeddingEngine(
            context = context,
            ioDispatcher = Dispatchers.IO,
            defaultDispatcher = Dispatchers.Default,
            modelConfig = BgeModelConfig,
            memoryMonitor = mock(),
            hardwareAccelerationManager = mock(),
        )
    }

    @Test
    fun `engine should initialize successfully`() = runTest {
        // When
        bgeEngine.initialize()

        // Then
        assertThat(bgeEngine.status.value).isEqualTo(EngineStatus.READY)
    }

    @Test
    fun `embed should return non-zero vector for simple text`() = runTest {
        // Given
        val text = "健身训练"

        // When
        val embedding = bgeEngine.embed(text)

        // Then
        assertThat(embedding).isNotNull()
        assertThat(embedding.size).isEqualTo(384)

        // 验证不是全零向量
        val hasNonZeroValue = embedding.any { abs(it) > 0.001f }
        assertThat(hasNonZeroValue).isTrue()
    }

    @Test
    fun `embed should return deterministic results for same input`() = runTest {
        // Given
        val text = "跑步锻炼"

        // When
        val embedding1 = bgeEngine.embed(text)
        val embedding2 = bgeEngine.embed(text)

        // Then
        assertThat(embedding1.size).isEqualTo(embedding2.size)

        // 验证确定性：相同输入应产生相同输出（误差范围内）
        for (i in embedding1.indices) {
            assertThat(abs(embedding1[i] - embedding2[i])).isLessThan(0.0001f)
        }
    }

    @Test
    fun `embedding should be L2 normalized`() = runTest {
        // Given
        val text = "力量训练计划"

        // When
        val embedding = bgeEngine.embed(text)

        // Then
        // 计算L2范数
        val l2Norm = sqrt(embedding.sumOf { (it * it).toDouble() }).toFloat()

        // L2归一化后的向量范数应该接近1.0
        assertThat(abs(l2Norm - 1.0f)).isLessThan(0.01f)
    }

    @Test
    fun `different texts should produce different embeddings`() = runTest {
        // Given
        val text1 = "有氧运动"
        val text2 = "力量训练"

        // When
        val embedding1 = bgeEngine.embed(text1)
        val embedding2 = bgeEngine.embed(text2)

        // Then
        // 计算余弦相似度
        val dotProduct = embedding1.zip(embedding2) { a, b -> a * b }.sum()

        // 不同文本的嵌入应该有一定差异（相似度不应该是1.0）
        assertThat(abs(dotProduct - 1.0f)).isGreaterThan(0.01f)
    }

    @Test
    fun `embedBatch should handle multiple texts`() = runTest {
        // Given
        val texts = listOf("跑步", "游泳", "健身", "瑜伽")

        // When
        val embeddings = bgeEngine.embedBatch(texts)

        // Then
        assertThat(embeddings).hasSize(4)
        embeddings.forEach { embedding ->
            assertThat(embedding.size).isEqualTo(384)
            // 验证每个向量都经过L2归一化
            val l2Norm = sqrt(embedding.sumOf { (it * it).toDouble() }).toFloat()
            assertThat(abs(l2Norm - 1.0f)).isLessThan(0.01f)
        }
    }

    @Test
    fun `warmUp should complete without errors`() = runTest {
        // When & Then - 预热不应该抛出异常
        bgeEngine.warmUp()

        // 预热后引擎应该处于就绪状态
        assertThat(bgeEngine.status.value).isEqualTo(EngineStatus.READY)
    }

    @Test
    fun `getModelInfo should return correct information`() = runTest {
        // When
        val modelInfo = bgeEngine.getModelInfo()

        // Then
        assertThat(modelInfo.modelName).isEqualTo(BgeModelConfig.modelName) // 🔥 使用统一配置
        assertThat(modelInfo.embeddingDim).isEqualTo(BgeModelConfig.embeddingDim) // 🔥 使用统一配置
        assertThat(modelInfo.maxSequenceLength).isEqualTo(BgeModelConfig.maxSequenceLength) // 🔥 使用统一配置
    }
}
*/