package com.example.gymbro.core.error.types.system

import com.example.gymbro.core.error.ErrorCode
import com.example.gymbro.core.error.types.ErrorCategory
import com.example.gymbro.core.error.types.ErrorSeverity
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.StandardKeys
import com.example.gymbro.core.ui.text.UiText

/**
 * 系统错误类型集合
 *
 * 包含网络错误和系统错误的工厂方法
 * 用于创建与系统基础设施相关的错误实例
 *
 * P3阶段更新：使用ErrorCode替换硬编码中文字符串
 * 实现Error-code mapping pattern
 *
 * <AUTHOR> Team
 * @since 1.0.0
 * @updated 2.0.0 (P3阶段 - Error-code mapping)
 */
object SystemErrors {
    /**
     * 网络错误类
     *
     * 处理所有网络相关的错误情况，包括连接、超时、服务器错误等
     * P3阶段更新：默认使用ErrorCode，支持自定义message覆盖
     */
    object Network {
        /**
         * 创建网络连接错误
         */
        fun connection(
            operationName: String = "Network.connection",
            message: UiText = UiText.ErrorCode(ErrorCode.NETWORK_CONNECTION_FAILED),
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError =
            ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.Network.Connection,
                category = ErrorCategory.NETWORK,
                uiMessage = message,
                severity = ErrorSeverity.ERROR,
                recoverable = true,
                cause = cause,
                metadataMap = metadataMap + mapOf("errorCode" to ErrorCode.NETWORK_CONNECTION_FAILED.code),
            )

        /**
         * 创建网络超时错误
         */
        fun timeout(
            operationName: String = "Network.timeout",
            message: UiText = UiText.ErrorCode(ErrorCode.NETWORK_UNSTABLE),
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError =
            ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.Network.Timeout,
                category = ErrorCategory.NETWORK,
                uiMessage = message,
                severity = ErrorSeverity.WARNING,
                recoverable = true,
                cause = cause,
                metadataMap = metadataMap + mapOf("errorCode" to ErrorCode.NETWORK_UNSTABLE.code),
            )

        /**
         * 创建客户端错误
         */
        fun client(
            operationName: String = "Network.client",
            message: UiText = UiText.ErrorCode(ErrorCode.NETWORK_CONNECTION_FAILED),
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError =
            ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.Network.Connection,
                category = ErrorCategory.NETWORK,
                uiMessage = message,
                severity = ErrorSeverity.ERROR,
                recoverable = true,
                cause = cause,
                metadataMap = metadataMap + mapOf("errorCode" to ErrorCode.NETWORK_CONNECTION_FAILED.code),
            )

        /**
         * 创建服务端错误
         */
        fun server(
            operationName: String = "Network.server",
            message: UiText = UiText.ErrorCode(ErrorCode.SYSTEM_ERROR),
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError =
            ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.Network.ServerError,
                category = ErrorCategory.NETWORK,
                uiMessage = message,
                severity = ErrorSeverity.ERROR,
                recoverable = true,
                cause = cause,
                metadataMap = metadataMap + mapOf("errorCode" to ErrorCode.SYSTEM_ERROR.code),
            )

        /**
         * 创建解析错误
         */
        fun parsing(
            operationName: String = "Network.parsing",
            message: UiText = UiText.ErrorCode(ErrorCode.SYSTEM_ERROR),
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError =
            ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.Network.Connection,
                category = ErrorCategory.NETWORK,
                uiMessage = message,
                severity = ErrorSeverity.ERROR,
                recoverable = false,
                cause = cause,
                metadataMap = metadataMap + mapOf("errorCode" to ErrorCode.SYSTEM_ERROR.code),
            )

        /**
         * 创建网络请求错误
         */
        fun request(
            operationName: String = "Network.request",
            message: UiText = UiText.ErrorCode(ErrorCode.NETWORK_CONNECTION_FAILED),
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError =
            ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.Network.Connection,
                category = ErrorCategory.NETWORK,
                uiMessage = message,
                severity = ErrorSeverity.ERROR,
                recoverable = true,
                cause = cause,
                metadataMap = metadataMap + mapOf("errorCode" to ErrorCode.NETWORK_CONNECTION_FAILED.code),
            )

        /**
         * 创建资源不可用错误
         */
        fun resourceUnavailable(
            operationName: String = "Network.resourceUnavailable",
            message: UiText = UiText.ErrorCode(ErrorCode.SYSTEM_ERROR),
            resourceUrl: String? = null,
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError {
            val metadataWithResource =
                metadataMap.toMutableMap().apply {
                    resourceUrl?.let { put(StandardKeys.URL.key, it) }
                    put("errorCode", ErrorCode.SYSTEM_ERROR.code)
                }
            return ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.Network.ResourceUnavailable,
                category = ErrorCategory.NETWORK,
                uiMessage = message,
                severity = ErrorSeverity.ERROR,
                recoverable = true,
                cause = cause,
                metadataMap = metadataWithResource,
            )
        }

        /**
         * 创建未知网络错误
         */
        fun unknown(
            operationName: String = "Network.unknown",
            message: UiText = UiText.ErrorCode(ErrorCode.UNKNOWN_ERROR),
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError =
            ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.Network.General,
                category = ErrorCategory.NETWORK,
                uiMessage = message,
                severity = ErrorSeverity.ERROR,
                recoverable = false,
                cause = cause,
                metadataMap = metadataMap + mapOf("errorCode" to ErrorCode.UNKNOWN_ERROR.code),
            )
    }

    /**
     * 系统错误类
     *
     * 处理系统级别的错误，包括内部错误、资源错误、配置错误等
     * P3阶段更新：默认使用ErrorCode
     */
    object System {
        /**
         * 创建系统内部错误
         */
        fun internal(
            operationName: String = "System.internal",
            message: UiText = UiText.ErrorCode(ErrorCode.SYSTEM_ERROR),
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError =
            ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.System.Internal,
                category = ErrorCategory.SYSTEM,
                uiMessage = message,
                severity = ErrorSeverity.CRITICAL,
                recoverable = false,
                cause = cause,
                metadataMap = metadataMap + mapOf("errorCode" to ErrorCode.SYSTEM_ERROR.code),
            )

        /**
         * 创建系统资源错误
         */
        fun resource(
            operationName: String = "System.resource",
            message: UiText = UiText.ErrorCode(ErrorCode.SYSTEM_ERROR),
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError =
            ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.System.Resource,
                category = ErrorCategory.SYSTEM,
                uiMessage = message,
                severity = ErrorSeverity.ERROR,
                recoverable = true,
                cause = cause,
                metadataMap = metadataMap + mapOf("errorCode" to ErrorCode.SYSTEM_ERROR.code),
            )

        /**
         * 创建系统配置错误
         */
        fun configuration(
            operationName: String = "SystemError.configuration",
            message: UiText = UiText.ErrorCode(ErrorCode.SYSTEM_CONFIGURATION_ERROR),
            configKey: String? = null,
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError {
            val metadataWithConfig =
                metadataMap.toMutableMap().apply {
                    configKey?.let { put("config_key", it) }
                    put("errorCode", ErrorCode.SYSTEM_CONFIGURATION_ERROR.code)
                }
            return ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.System.Configuration,
                category = ErrorCategory.SYSTEM,
                uiMessage = message,
                severity = ErrorSeverity.ERROR,
                recoverable = false,
                cause = cause,
                metadataMap = metadataWithConfig,
            )
        }

        /**
         * 创建系统权限错误
         */
        fun permission(
            operationName: String = "SystemError.permission",
            message: UiText = UiText.ErrorCode(ErrorCode.SYSTEM_PERMISSION_ERROR),
            requiredPermission: String? = null,
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError {
            val metadataWithPermission =
                metadataMap.toMutableMap().apply {
                    requiredPermission?.let { put("required_permission", it) }
                    put("errorCode", ErrorCode.SYSTEM_PERMISSION_ERROR.code)
                }
            return ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.System.Permission,
                category = ErrorCategory.SYSTEM,
                uiMessage = message,
                severity = ErrorSeverity.ERROR,
                recoverable = false,
                cause = cause,
                metadataMap = metadataWithPermission,
            )
        }

        /**
         * 创建功能未实现错误
         */
        fun notImplemented(
            operationName: String = "SystemError.notImplemented",
            message: UiText = UiText.ErrorCode(ErrorCode.SYSTEM_NOT_IMPLEMENTED),
            featureName: String? = null,
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError {
            val metadataWithFeature =
                metadataMap.toMutableMap().apply {
                    featureName?.let { put("feature_name", it) }
                    put("errorCode", ErrorCode.SYSTEM_NOT_IMPLEMENTED.code)
                }
            return ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.System.NotImplemented,
                category = ErrorCategory.SYSTEM,
                uiMessage = message,
                severity = ErrorSeverity.ERROR,
                recoverable = false,
                cause = cause,
                metadataMap = metadataWithFeature,
            )
        }

        /**
         * 创建通用系统错误
         */
        fun general(
            operationName: String = "SystemError.general",
            message: UiText = UiText.ErrorCode(ErrorCode.SYSTEM_ERROR),
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError =
            ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.System.General,
                category = ErrorCategory.SYSTEM,
                uiMessage = message,
                severity = ErrorSeverity.ERROR,
                recoverable = false,
                cause = cause,
                metadataMap = metadataMap + mapOf("errorCode" to ErrorCode.SYSTEM_ERROR.code),
            )
    }
}
