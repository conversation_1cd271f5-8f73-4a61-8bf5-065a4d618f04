package com.example.gymbro.data.datasource.impl

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.data.datasource.UserDataSource
import com.example.gymbro.data.datasource.UserSettingsDataSource
import com.example.gymbro.domain.profile.model.user.BlockedUser
import com.example.gymbro.domain.profile.model.user.User
import com.example.gymbro.domain.profile.model.user.settings.UserSettings
import kotlinx.coroutines.flow.Flow
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

/**
 * UserRemoteDataSource的实现类
 * 通过组合模式将不同职责委托给专门的组件
 */
@Singleton
class UserRemoteDataSourceImpl
@Inject
constructor(
    private val userDataSource: UserDataSource,
    private val userSettingsDataSource: UserSettingsDataSource,
) {
    // ===============================================
    // UserDataSource 方法委托
    // ===============================================

    suspend fun getUser(userId: String): ModernResult<User?> = userDataSource.getUser(userId)

    suspend fun getUserById(userId: String): ModernResult<User> = userDataSource.getUserById(userId)

    suspend fun createUser(user: User): ModernResult<String> = userDataSource.createUser(user)

    suspend fun updateUser(
        userId: String,
        updates: Map<String, Any?>,
    ): ModernResult<Unit> = userDataSource.updateUser(userId, updates)

    suspend fun updateUser(user: User): ModernResult<Unit> = userDataSource.updateUser(user)

    suspend fun createOrUpdateUserProfile(user: User): ModernResult<String> = userDataSource.createOrUpdateUserProfile(
        user,
    )

    suspend fun deleteUser(userId: String): ModernResult<Unit> = userDataSource.deleteUser(userId)

    fun getUserFlow(userId: String): Flow<User?> = userDataSource.getUserFlow(userId)

    suspend fun queryUsers(
        field: String,
        value: Any,
    ): ModernResult<List<User>> = userDataSource.queryUsers(field, value)

    suspend fun getUsers(userIds: List<String>): ModernResult<List<User>> = userDataSource.getUsers(
        userIds,
    )

    suspend fun getNearbyUsers(
        latitude: Double,
        longitude: Double,
        radiusInKm: Double,
    ): ModernResult<List<User>> = userDataSource.getNearbyUsers(latitude, longitude, radiusInKm)

    suspend fun getUsersByGym(gymId: String): ModernResult<List<User>> = userDataSource.getUsersByGym(
        gymId,
    )

    suspend fun updateUserOnlineStatus(
        userId: String,
        isOnline: Boolean,
    ): ModernResult<Unit> = userDataSource.updateUserOnlineStatus(userId, isOnline)

    fun getOnlineUsersFlow(): Flow<List<String>> = userDataSource.getOnlineUsersFlow()

    suspend fun blockUser(
        userId: String,
        targetUserId: String,
    ): ModernResult<Unit> = userDataSource.blockUser(userId, targetUserId)

    suspend fun unblockUser(
        userId: String,
        targetUserId: String,
    ): ModernResult<Unit> = userDataSource.unblockUser(userId, targetUserId)

    suspend fun getBlockedUserDetails(
        userId: String,
        blockedUserIds: List<String>,
    ): ModernResult<List<BlockedUser>> = userDataSource.getBlockedUserDetails(userId, blockedUserIds)

    suspend fun uploadUserAvatar(
        userId: String,
        avatarFile: File,
    ): ModernResult<String> = userDataSource.uploadUserAvatar(userId, avatarFile)

    // ===============================================
    // UserSettingsDataSource 方法委托
    // ===============================================

    suspend fun getUserSettings(userId: String): ModernResult<UserSettings?> = userSettingsDataSource.getUserSettings(
        userId,
    )

    suspend fun saveUserSettings(
        userId: String,
        settings: UserSettings,
    ): ModernResult<Unit> = userSettingsDataSource.saveUserSettings(userId, settings)

    suspend fun updateUserSettingField(
        userId: String,
        fieldPath: String,
        value: Any,
    ): ModernResult<Unit> = userSettingsDataSource.updateUserSettingField(userId, fieldPath, value)

    suspend fun saveUserData(
        userId: String,
        userPreferences: UserSettings,
    ): ModernResult<Unit> = userSettingsDataSource.saveUserData(userId, userPreferences)

    suspend fun getUserData(userId: String): ModernResult<UserSettings?> = userSettingsDataSource.getUserData(
        userId,
    )

    // ===============================================
    // UserSyncDataSource 方法委托 - 临时实现
    // ===============================================

    fun syncUserData(
        user: User,
        forceUpload: Boolean,
    ): ModernResult<Unit> {
        // TODO: 实现用户数据同步逻辑
        return ModernResult.Success(Unit)
    }

    fun updateUserBatch(users: List<User>): ModernResult<Unit> {
        // TODO: 实现批量用户更新逻辑
        return ModernResult.Success(Unit)
    }
}
