# GymBro Project Structure

## Tech Stack
- **Language**: <PERSON><PERSON><PERSON>
- **Architecture**: Clean Architecture + MVI 2.0
- **UI**: Jetpack Compose
- **Build**: Gradle with <PERSON><PERSON><PERSON> DSL
- **Platform**: Android

## Module Structure
- `app/` - Main application module
- `core/` - Core utilities and base classes
- `data/` - Data layer implementation
- `domain/` - Business logic and use cases
- `features/` - Feature modules (workout, coach, profile, etc.)
- `shared-models/` - Shared data models
- `designSystem/` - UI design system and tokens

## Key Patterns
- MVI 2.0 with Intent-State-Effect pattern
- Repository pattern for data access
- Use cases for business logic
- Dependency injection with Hilt