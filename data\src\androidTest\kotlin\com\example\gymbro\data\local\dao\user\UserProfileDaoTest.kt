package com.example.gymbro.data.local.dao.user

import androidx.room.Room
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.gymbro.data.local.database.AppDatabase
import com.example.gymbro.data.local.entity.user.UserProfileEntity
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * UserProfileDao集成测试
 *
 * 使用in-memory Room数据库验证真实的数据库操作：
 * 1. 插入和查询用户资料
 * 2. 更新用户资料
 * 3. 删除用户资料
 * 4. Flow响应式查询
 */
@RunWith(AndroidJUnit4::class)
class UserProfileDaoTest {

    private lateinit var database: AppDatabase
    private lateinit var userProfileDao: UserProfileDao

    @Before
    fun setup() {
        database = Room.inMemoryDatabaseBuilder(
            ApplicationProvider.getApplicationContext(),
            AppDatabase::class.java,
        ).allowMainThreadQueries().build()

        userProfileDao = database.userProfileDao()
    }

    @After
    fun tearDown() {
        database.close()
    }

    @Test
    fun insertAndGetUserProfile_shouldWorkCorrectly() = runTest {
        // Given
        val userProfile = createTestUserProfileEntity("user_123")

        // When
        val insertId = userProfileDao.insertOrUpdateUserProfile(userProfile)
        val retrievedProfile = userProfileDao.getUserProfileSync("user_123")

        // Then
        assertTrue(insertId > 0)
        assertNotNull(retrievedProfile)
        assertEquals("user_123", retrievedProfile.userId)
        assertEquals("Test User", retrievedProfile.displayName)
        assertEquals("<EMAIL>", retrievedProfile.email)
    }

    @Test
    fun getUserProfile_withNonExistentUser_shouldReturnNull() = runTest {
        // When
        val retrievedProfile = userProfileDao.getUserProfileSync("nonexistent_user")

        // Then
        assertNull(retrievedProfile)
    }

    @Test
    fun updateUserProfile_shouldModifyExistingRecord() = runTest {
        // Given
        val originalProfile = createTestUserProfileEntity("user_456")
        userProfileDao.insertOrUpdateUserProfile(originalProfile)

        val updatedProfile = originalProfile.copy(
            displayName = "Updated User",
            bio = "Updated bio",
        )

        // When
        val updateCount = userProfileDao.updateUserProfile(updatedProfile)
        val retrievedProfile = userProfileDao.getUserProfileSync("user_456")

        // Then
        assertEquals(1, updateCount)
        assertNotNull(retrievedProfile)
        assertEquals("Updated User", retrievedProfile.displayName)
        assertEquals("Updated bio", retrievedProfile.bio)
    }

    @Test
    fun deleteUserProfile_shouldRemoveRecord() = runTest {
        // Given
        val userProfile = createTestUserProfileEntity("user_789")
        userProfileDao.insertOrUpdateUserProfile(userProfile)

        // When
        val deleteCount = userProfileDao.deleteUserProfile("user_789")
        val retrievedProfile = userProfileDao.getUserProfileSync("user_789")

        // Then
        assertEquals(1, deleteCount)
        assertNull(retrievedProfile)
    }

    @Test
    fun getUserProfile_flow_shouldEmitUpdates() = runTest {
        // Given
        val userId = "user_flow_test"
        val userProfile = createTestUserProfileEntity(userId)

        // When - 初始状态应该为null
        val initialResult = userProfileDao.getUserProfile(userId).first()
        assertNull(initialResult)

        // When - 插入用户资料
        userProfileDao.insertOrUpdateUserProfile(userProfile)
        val afterInsertResult = userProfileDao.getUserProfile(userId).first()

        // Then
        assertNotNull(afterInsertResult)
        assertEquals(userId, afterInsertResult.userId)
        assertEquals("Test User", afterInsertResult.displayName)
    }

    @Test
    fun userProfileExists_shouldReturnCorrectStatus() = runTest {
        // Given
        val userId = "user_exists_test"
        val userProfile = createTestUserProfileEntity(userId)

        // When - 用户不存在时
        val existsBeforeInsert = userProfileDao.userProfileExists(userId)

        // When - 插入用户后
        userProfileDao.insertOrUpdateUserProfile(userProfile)
        val existsAfterInsert = userProfileDao.userProfileExists(userId)

        // Then
        assertEquals(false, existsBeforeInsert)
        assertEquals(true, existsAfterInsert)
    }

    @Test
    fun updateLastActiveTime_shouldModifyTimestamp() = runTest {
        // Given
        val userId = "user_timestamp_test"
        val userProfile = createTestUserProfileEntity(userId)
        userProfileDao.insertOrUpdateUserProfile(userProfile)

        val newTimestamp = System.currentTimeMillis() + 10000

        // When
        val updateCount = userProfileDao.updateLastActiveTime(userId, newTimestamp)
        val retrievedProfile = userProfileDao.getUserProfileSync(userId)

        // Then
        assertEquals(1, updateCount)
        assertNotNull(retrievedProfile)
        assertEquals(newTimestamp, retrievedProfile.lastUpdated)
    }

    @Test
    fun clearAllUserProfiles_shouldRemoveAllRecords() = runTest {
        // Given
        val profile1 = createTestUserProfileEntity("user_1")
        val profile2 = createTestUserProfileEntity("user_2")
        userProfileDao.insertOrUpdateUserProfile(profile1)
        userProfileDao.insertOrUpdateUserProfile(profile2)

        // When
        val deleteCount = userProfileDao.clearAllUserProfiles()
        val allProfiles = userProfileDao.getAllUserProfiles().first()

        // Then
        assertEquals(2, deleteCount)
        assertTrue(allProfiles.isEmpty())
    }

    private fun createTestUserProfileEntity(userId: String): UserProfileEntity {
        return UserProfileEntity(
            userId = userId,
            username = "testuser",
            displayName = "Test User",
            email = "<EMAIL>",
            phoneNumber = "+1234567890",
            profileImageUrl = "https://example.com/avatar.jpg",
            bio = "Test bio",
            gender = "MALE",
            height = 175.0f,
            heightUnit = "CM",
            weight = 70.0f,
            weightUnit = "KG",
            fitnessLevel = 1, // INTERMEDIATE
            fitnessGoals = emptyList(),
            workoutDays = emptyList(),
            allowPartnerMatching = true,
            totalWorkoutCount = 10,
            weeklyActiveMinutes = 300,
            likesReceived = 5,
            isAnonymous = false,
            hasValidSubscription = false,
            lastUpdated = System.currentTimeMillis(),
            createdAt = System.currentTimeMillis(),
        )
    }
}
