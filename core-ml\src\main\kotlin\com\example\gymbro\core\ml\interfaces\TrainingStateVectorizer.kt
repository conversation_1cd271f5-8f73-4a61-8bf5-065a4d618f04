package com.example.gymbro.core.ml.interfaces

import com.example.gymbro.core.error.types.ModernResult

/**
 * 训练状态向量化器接口 - Core-ML Layer
 *
 * 负责将训练状态描述文本转换为向量表示，用于语义检索和匹配
 * 这是纯算法抽象接口，不依赖任何上层模块
 */
interface WorkoutStateVectorizer {

    /**
     * 将训练状态描述转换为向量
     *
     * @param stateDescription 训练状态的文本描述
     * @return 向量化结果，包含状态向量和元数据
     */
    suspend fun vectorizeWorkoutState(stateDescription: String): ModernResult<VectorizedWorkoutState>

    /**
     * 批量向量化训练状态
     *
     * @param stateDescriptions 多个训练状态描述
     * @return 向量化结果列表
     */
    suspend fun vectorizeBatchStates(
        stateDescriptions: List<String>,
    ): ModernResult<List<VectorizedWorkoutState>>

    /**
     * 计算两个训练状态的相似度
     *
     * @param state1 第一个训练状态向量
     * @param state2 第二个训练状态向量
     * @return 相似度分数 (0.0 到 1.0)
     */
    fun calculateStateSimilarity(state1: VectorizedWorkoutState, state2: VectorizedWorkoutState): Float

    /**
     * 获取向量化器信息
     */
    fun getVectorizerInfo(): VectorizerInfo
}

/**
 * 向量化的训练状态 - Core-ML Layer
 *
 * 简单的向量化数据结构，只包含算法层需要的基础信息
 */
data class VectorizedWorkoutState(
    val originalText: String,
    val vector: FloatArray,
    val vectorDim: Int,
    val timestamp: Long = System.currentTimeMillis(),
    val metadata: Map<String, Any> = emptyMap(),
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as VectorizedWorkoutState

        if (originalText != other.originalText) return false
        if (!vector.contentEquals(other.vector)) return false
        if (vectorDim != other.vectorDim) return false

        return true
    }

    override fun hashCode(): Int {
        var result = originalText.hashCode()
        result = 31 * result + vector.contentHashCode()
        result = 31 * result + vectorDim
        return result
    }
}

/**
 * 向量化器信息
 */
data class VectorizerInfo(
    val name: String,
    val version: String,
    val vectorDimension: Int,
    val maxTextLength: Int,
    val isInitialized: Boolean,
)
