package com.example.gymbro.data.shared.network

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.StandardKeys
import com.example.gymbro.core.error.types.system.SystemErrors
import com.example.gymbro.core.ui.text.UiText
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import timber.log.Timber
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 网络数据源实现类
 *
 * 使用OkHttpClient实现从网络获取数据。
 */
@Singleton
class NetworkDataSourceImpl
@Inject
constructor(
    private val okHttpClient: OkHttpClient,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
) : NetworkDataSource {
    /**
     * 获取文本数据
     * @param url 要获取的URL
     * @return 包含文本内容的ModernResult
     */
    override suspend fun fetchText(url: String): ModernResult<String> =
        withContext(ioDispatcher) {
            try {
                val request =
                    Request
                        .Builder()
                        .url(url)
                        .build()

                val response = okHttpClient.newCall(request).execute()
                if (response.isSuccessful) {
                    val body = response.body?.string()
                    if (body != null) {
                        ModernResult.success(body)
                    } else {
                        ModernResult.error(
                            SystemErrors.Network.parsing(
                                operationName = "NetworkDataSourceImpl.fetchText.emptyBody",
                                message = UiText.DynamicString("响应体为空"),
                                metadataMap =
                                mapOf(
                                    StandardKeys.URL.key to url,
                                ),
                            ),
                        )
                    }
                } else {
                    ModernResult.error(
                        SystemErrors.Network.server(
                            operationName = "NetworkDataSourceImpl.fetchText.httpError",
                            message = UiText.DynamicString("HTTP错误: ${response.code}"),
                            metadataMap =
                            mapOf(
                                StandardKeys.URL.key to url,
                                StandardKeys.HTTP_STATUS.key to response.code,
                            ),
                        ),
                    )
                }
            } catch (e: IOException) {
                Timber.e(e, "获取文本时发生网络错误")
                ModernResult.error(
                    SystemErrors.Network.connection(
                        operationName = "NetworkDataSourceImpl.fetchText.ioException",
                        message = UiText.DynamicString("网络连接错误"),
                        cause = e,
                        metadataMap = mapOf(StandardKeys.URL.key to url),
                    ),
                )
            } catch (e: Exception) {
                Timber.e(e, "获取文本时发生异常")
                ModernResult.error(
                    SystemErrors.Network.unknown(
                        operationName = "NetworkDataSourceImpl.fetchText.exception",
                        message = UiText.DynamicString("获取文本失败: ${e.message}"),
                        cause = e,
                        metadataMap = mapOf(StandardKeys.URL.key to url),
                    ),
                )
            }
        }

    /**
     * 获取二进制数据
     * @param url 要获取的URL
     * @return 包含二进制数据的ModernResult
     */
    override suspend fun fetchBytes(url: String): ModernResult<ByteArray> =
        withContext(ioDispatcher) {
            try {
                val request =
                    Request
                        .Builder()
                        .url(url)
                        .build()

                val response = okHttpClient.newCall(request).execute()
                if (response.isSuccessful) {
                    val body = response.body?.bytes()
                    if (body != null) {
                        ModernResult.success(body)
                    } else {
                        ModernResult.error(
                            SystemErrors.Network.parsing(
                                operationName = "NetworkDataSourceImpl.fetchBytes.emptyBody",
                                message = UiText.DynamicString("响应体为空"),
                                metadataMap =
                                mapOf(
                                    StandardKeys.URL.key to url,
                                ),
                            ),
                        )
                    }
                } else {
                    ModernResult.error(
                        SystemErrors.Network.server(
                            operationName = "NetworkDataSourceImpl.fetchBytes.httpError",
                            message = UiText.DynamicString("HTTP错误: ${response.code}"),
                            metadataMap =
                            mapOf(
                                StandardKeys.URL.key to url,
                                StandardKeys.HTTP_STATUS.key to response.code,
                            ),
                        ),
                    )
                }
            } catch (e: IOException) {
                Timber.e(e, "获取二进制数据时发生网络错误")
                ModernResult.error(
                    SystemErrors.Network.connection(
                        operationName = "NetworkDataSourceImpl.fetchBytes.ioException",
                        message = UiText.DynamicString("网络连接错误"),
                        cause = e,
                        metadataMap = mapOf(StandardKeys.URL.key to url),
                    ),
                )
            } catch (e: Exception) {
                Timber.e(e, "获取二进制数据时发生异常")
                ModernResult.error(
                    SystemErrors.Network.unknown(
                        operationName = "NetworkDataSourceImpl.fetchBytes.exception",
                        message = UiText.DynamicString("获取二进制数据失败: ${e.message}"),
                        cause = e,
                        metadataMap = mapOf(StandardKeys.URL.key to url),
                    ),
                )
            }
        }

    companion object {
        private const val TAG = "NetworkDataSource"
    }
}
