package com.example.gymbro.core.network.ws

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonElement

/**
 * WebSocket统一帧格式
 *
 * 支持多种消息类型：token、tool_call、done、error、ping、pong
 * 实现断点续传和心跳机制
 */
@Serializable
data class WsFrame(
    /**
     * 帧类型
     * 支持: "token" | "tool_call" | "done" | "error" | "ping" | "pong"
     */
    val type: String,

    /**
     * 消息ID
     * UUID格式，用于关联userMsg/aiMsg
     */
    val id: String,

    /**
     * Token序号
     * 用于断点续传，服务端根据offset回补缺帧
     */
    val idx: Int? = null,

    /**
     * 消息数据
     * 根据type不同包含不同内容：
     * - token: { "text": "...", "delta": "..." }
     * - tool_call: { "name": "...", "arguments": "..." }
     * - error: { "code": 4401, "msg": "unauthorized" }
     * - ping/pong: null 或心跳数据
     */
    val data: JsonElement? = null,

    /**
     * 时间戳
     * 消息发送/接收时间，用于性能监控
     */
    val timestamp: Long? = null,
) {
    companion object {
        // 帧类型常量
        const val TYPE_TOKEN = "token"
        const val TYPE_TOOL_CALL = "tool_call"
        const val TYPE_DONE = "done"
        const val TYPE_ERROR = "error"
        const val TYPE_PING = "ping"
        const val TYPE_PONG = "pong"
        const val TYPE_THINKING = "thinking"
        const val TYPE_PROGRESS = "progress"

        /**
         * 创建ping帧
         */
        fun createPing(id: String): WsFrame = WsFrame(
            type = TYPE_PING,
            id = id,
            timestamp = System.currentTimeMillis(),
        )

        /**
         * 创建pong帧
         */
        fun createPong(id: String): WsFrame = WsFrame(
            type = TYPE_PONG,
            id = id,
            timestamp = System.currentTimeMillis(),
        )

        /**
         * 检查是否为心跳帧
         */
        fun isHeartbeat(frame: WsFrame): Boolean =
            frame.type in listOf(TYPE_PING, TYPE_PONG)

        /**
         * 检查是否为数据帧
         */
        fun isDataFrame(frame: WsFrame): Boolean =
            frame.type in listOf(TYPE_TOKEN, TYPE_TOOL_CALL, TYPE_THINKING, TYPE_PROGRESS)

        /**
         * 检查是否为控制帧
         */
        fun isControlFrame(frame: WsFrame): Boolean =
            frame.type in listOf(TYPE_DONE, TYPE_ERROR)
    }

    /**
     * 检查是否为错误帧
     */
    fun isError(): Boolean = type == TYPE_ERROR

    /**
     * 检查是否为完成帧
     */
    fun isDone(): Boolean = type == TYPE_DONE

    /**
     * 检查是否为ping帧
     */
    fun isPing(): Boolean = type == TYPE_PING

    /**
     * 检查是否为pong帧
     */
    fun isPong(): Boolean = type == TYPE_PONG

    /**
     * 获取错误信息
     * 仅当type为error时有效
     */
    fun getErrorMessage(): String? {
        if (!isError() || data == null) return null
        return try {
            // 简单解析错误信息，实际项目中可能需要更复杂的解析
            data.toString()
        } catch (e: Exception) {
            "Failed to parse error message"
        }
    }
}
