package com.example.gymbro.core.network.ws

import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber
import kotlin.math.min
import kotlin.math.pow

/**
 * WebSocket连接状态 - 按照websock+Okhttp.md规范实现
 *
 * 状态机流转：INIT → OPEN → STREAMING → RECONNECTING → DEAD
 */
sealed interface WsState {
    /**
     * 初始状态 - WebSocket尚未连接
     */
    data object Init : WsState

    /**
     * 连接中 - 正在建立WebSocket连接
     */
    data object Connecting : WsState

    /**
     * 已连接 - WebSocket连接已建立，可以发送消息
     */
    data object Open : WsState

    /**
     * 流式传输中 - 正在接收流式数据
     */
    data object Streaming : WsState

    /**
     * 重连中 - 连接断开，正在尝试重新连接
     * @param attempt 当前重连尝试次数
     * @param backoffMs 当前退避延迟时间（毫秒）
     */
    data class Reconnecting(
        val attempt: Int,
        val backoffMs: Long,
    ) : WsState

    /**
     * 连接死亡 - 连接彻底失败，无法恢复
     * @param reason 失败原因
     */
    data class Dead(val reason: String) : WsState

    /**
     * 检查是否为已连接状态
     */
    fun isConnected(): Boolean = this is Open || this is Streaming

    /**
     * 检查是否可以重连
     */
    fun canReconnect(): Boolean = this !is Dead
}

/**
 * WebSocket状态机 - 按照websock+Okhttp.md核心实现方案
 *
 * 🎯 功能：
 * - 管理WebSocket连接状态转换
 * - 实现指数退避重连策略
 * - 提供状态流供UI订阅
 * - 支持续流机制的状态管理
 */
class WsStateMachine(
    private val maxBackoffSec: Int = 30,
    private val maxReconnectAttempts: Int = 5,
) {
    private val _state = MutableStateFlow<WsState>(WsState.Init)
    val state: StateFlow<WsState> = _state.asStateFlow()

    /**
     * WebSocket连接成功
     */
    suspend fun onOpen() {
        Timber.d("🔗 WebSocket状态: ${_state.value} → Open")
        _state.value = WsState.Open
    }

    /**
     * 开始接收Token流
     */
    suspend fun onToken() {
        val currentState = _state.value
        if (currentState is WsState.Open) {
            Timber.d("🌊 WebSocket状态: Open → Streaming")
            _state.value = WsState.Streaming
        }
    }

    /**
     * WebSocket连接关闭
     */
    suspend fun onClosed(reason: String = "Connection closed") {
        Timber.w("💀 WebSocket状态: ${_state.value} → Dead ($reason)")
        _state.value = WsState.Dead(reason)
    }

    /**
     * WebSocket连接失败，启动重连逻辑
     */
    suspend fun onFailure(e: Throwable) {
        val currentState = _state.value
        val nextAttempt = when (currentState) {
            is WsState.Reconnecting -> currentState.attempt + 1
            else -> 1
        }

        if (nextAttempt > maxReconnectAttempts) {
            Timber.e("💀 WebSocket重连失败，达到最大重试次数: $maxReconnectAttempts")
            _state.value = WsState.Dead("Max reconnect attempts exceeded")
            return
        }

        // 指数退避策略：2^attempt秒，最大30秒
        val backoffMs = min(
            (2f.pow(nextAttempt).toInt() * 1000).toLong(),
            maxBackoffSec * 1000L,
        )

        Timber.w("🔄 WebSocket重连 #$nextAttempt，退避${backoffMs}ms: ${e.message}")
        _state.value = WsState.Reconnecting(nextAttempt, backoffMs)

        // 执行退避延迟
        delay(backoffMs)
    }

    /**
     * 手动重置状态机到初始状态
     */
    fun reset() {
        Timber.d("🔄 WebSocket状态机重置")
        _state.value = WsState.Init
    }

    /**
     * 获取当前状态
     */
    fun getCurrentState(): WsState = _state.value

    /**
     * 检查是否需要重连
     */
    fun shouldReconnect(): Boolean {
        val currentState = _state.value
        return currentState is WsState.Reconnecting && currentState.canReconnect()
    }
}
