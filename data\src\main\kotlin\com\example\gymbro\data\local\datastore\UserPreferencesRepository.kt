package com.example.gymbro.data.local.datastore

import com.example.gymbro.core.error.types.ModernResult
import kotlinx.coroutines.flow.Flow

/**
 * 用户偏好设置仓库接口
 * 用于读写用户本地设置
 */
interface UserPreferencesRepository {
    /**
     * 获取用户区域代码
     * @return 包含区域代码字符串的ModernResult流（如"CN", "US"等）
     */
    fun getUserRegionCode(): Flow<ModernResult<String?>>

    /**
     * 设置用户区域代码
     * @param regionCode 区域代码字符串（如"CN", "US"等）
     * @return 设置操作的结果
     */
    suspend fun setUserRegionCode(regionCode: String): ModernResult<Unit>

    /**
     * 获取主题偏好(深色/浅色)
     * @return 是否深色主题的ModernResult流
     */
    fun getDarkThemePreference(): Flow<ModernResult<Boolean>>

    /**
     * 设置主题偏好
     * @param isDarkTheme 是否深色主题
     * @return 设置操作的结果
     */
    suspend fun setDarkThemePreference(isDarkTheme: Boolean): ModernResult<Unit>

    /**
     * 获取动态主题偏好
     * @return 是否启用动态主题的ModernResult流
     */
    fun getDynamicColorPreference(): Flow<ModernResult<Boolean>>

    /**
     * 设置动态主题偏好
     * @param useDynamicColor 是否启用动态主题
     * @return 设置操作的结果
     */
    suspend fun setDynamicColorPreference(useDynamicColor: Boolean): ModernResult<Unit>

    /**
     * 获取IP属地
     * @return IP属地字符串(例如 "CN")的ModernResult流
     */
    fun getIpLocation(): Flow<ModernResult<String?>>

    /**
     * 设置IP属地
     * @param location IP属地字符串(例如 "CN")
     * @return 设置操作的结果
     */
    suspend fun setIpLocation(location: String): ModernResult<Unit>

    /**
     * 获取匿名模式偏好
     * @return 是否启用匿名模式的ModernResult流
     */
    fun getAnonymousMode(): Flow<ModernResult<Boolean>>

    /**
     * 设置匿名模式偏好
     * @param enabled 是否启用匿名模式
     * @return 设置操作的结果
     */
    suspend fun setAnonymousMode(enabled: Boolean): ModernResult<Unit>

    /**
     * 获取隐私设置 - 位置共享
     * @return 是否共享位置的ModernResult流
     */
    fun getLocationSharing(): Flow<ModernResult<Boolean>>

    /**
     * 设置隐私设置 - 位置共享
     * @param enabled 是否共享位置
     * @return 设置操作的结果
     */
    suspend fun setLocationSharing(enabled: Boolean): ModernResult<Unit>

    /**
     * 获取隐私设置 - 个人资料公开
     * @return 是否公开个人资料的ModernResult流
     */
    fun getProfileVisibility(): Flow<ModernResult<Boolean>>

    /**
     * 设置隐私设置 - 个人资料公开
     * @param visible 是否公开个人资料
     * @return 设置操作的结果
     */
    suspend fun setProfileVisibility(visible: Boolean): ModernResult<Unit>

    /**
     * 获取通知设置 - 附近动态
     * @return 是否接收附近动态通知的ModernResult流
     */
    fun getNearbyNotifications(): Flow<ModernResult<Boolean>>

    /**
     * 设置通知设置 - 附近动态
     * @param enabled 是否接收附近动态通知
     * @return 设置操作的结果
     */
    suspend fun setNearbyNotifications(enabled: Boolean): ModernResult<Unit>

    /**
     * 获取通知设置 - 消息通知
     * @return 是否接收消息通知的ModernResult流
     */
    fun getMessageNotifications(): Flow<ModernResult<Boolean>>

    /**
     * 设置通知设置 - 消息通知
     * @param enabled 是否接收消息通知
     * @return 设置操作的结果
     */
    suspend fun setMessageNotifications(enabled: Boolean): ModernResult<Unit>

    /**
     * 获取默认倒计时时长
     * @return 默认倒计时时长（秒）的ModernResult流
     */
    fun getDefaultCountdownDuration(): Flow<ModernResult<Int>>

    /**
     * 设置默认倒计时时长
     * @param seconds 默认倒计时时长（秒）
     * @return 设置操作的结果
     */
    suspend fun setDefaultCountdownDuration(seconds: Int): ModernResult<Unit>

    /**
     * 获取是否启用倒计时声音
     * @return 是否启用倒计时声音的ModernResult流
     */
    fun getCountdownSoundEnabled(): Flow<ModernResult<Boolean>>

    /**
     * 设置是否启用倒计时声音
     * @param enabled 是否启用倒计时声音
     * @return 设置操作的结果
     */
    suspend fun setCountdownSoundEnabled(enabled: Boolean): ModernResult<Unit>

    /**
     * 获取休息计时器时长
     * @return 休息计时器时长（秒）的ModernResult
     */
    suspend fun getRestTimerDuration(): ModernResult<Int>

    /**
     * 设置休息计时器时长
     * @param duration 休息计时器时长（秒）
     * @return 设置操作的结果
     */
    suspend fun setRestTimerDuration(duration: Int): ModernResult<Unit>

    /**
     * 获取上次使用的计时器时长
     * @return 上次使用的计时器时长（秒）的ModernResult
     */
    suspend fun getLastTimerDuration(): ModernResult<Int>

    /**
     * 保存上次使用的计时器时长
     * @param duration 计时器时长（秒）
     * @return 保存操作的结果
     */
    suspend fun saveLastTimerDuration(duration: Int): ModernResult<Unit>

    /**
     * 获取上次同步时间的时间戳
     * @return 上次同步的时间戳（毫秒）的ModernResult，如果从未同步则返回0
     */
    suspend fun getLastSyncTimeMillis(): ModernResult<Long>

    /**
     * 保存上次同步时间的时间戳
     * @param timeMillis 要保存的时间戳（毫秒）
     * @return 保存操作的结果
     */
    suspend fun saveLastSyncTimeMillis(timeMillis: Long): ModernResult<Unit>

    // Language Preferences
    /**
     * Sets the user's preferred language code (e.g., "en", "zh").
     *
     * @param languageCode The ISO 639-1 language code.
     * @return Result of the operation.
     */
    suspend fun setLanguage(languageCode: String): ModernResult<Unit>

    /**
     * Gets the user's preferred language code.
     *
     * @return A Flow emitting the language code as a String, or null if not set.
     *         It might be beneficial to return a default (e.g., "en") if null.
     */
    fun getLanguage(): Flow<ModernResult<String?>>
}
