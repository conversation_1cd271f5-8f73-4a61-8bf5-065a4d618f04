package com.example.gymbro.data.local

import com.example.gymbro.domain.shared.common.model.QuickAction
import com.example.gymbro.domain.shared.common.model.QuickActionCategory
import com.example.gymbro.domain.shared.common.model.QuickActionCategoryGroup
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 快捷动作数据提供者
 *
 * 负责提供预定义的快捷动作数据，未来可扩展为从Firebase Remote Config获取
 *
 * @since 凤凰行动 - 数据层重构
 */
@Singleton
class QuickActionProvider
@Inject
constructor() {
    /**
     * 获取所有快捷动作分类组
     */
    fun getQuickActionCategoryGroups(): List<QuickActionCategoryGroup> =
        listOf(
            // 训练相关分类
            QuickActionCategoryGroup(
                category = QuickActionCategory.TRAINING,
                actions =
                listOf(
                    QuickAction(
                        id = "create_plan",
                        displayText = "制定计划",
                        prefillText = "帮我制定一个适合初学者的健身计划",
                        iconName = "fitness_center",
                        suggestions =
                        listOf(
                            "制定一个适合初学者的健身计划",
                            "为我设计一个减脂训练方案",
                            "制定一个增肌训练计划",
                            "设计一个居家健身计划",
                            "为我安排一周的训练计划",
                        ),
                        category = QuickActionCategory.TRAINING,
                    ),
                    QuickAction(
                        id = "learn_technique",
                        displayText = "学习技巧",
                        prefillText = "教我正确的深蹲动作",
                        iconName = "school",
                        suggestions =
                        listOf(
                            "教我正确的深蹲动作",
                            "如何做标准的俯卧撑",
                            "硬拉的正确姿势是什么",
                            "卧推的技巧和注意事项",
                            "如何避免运动损伤",
                        ),
                        category = QuickActionCategory.TRAINING,
                    ),
                    QuickAction(
                        id = "check_progress",
                        displayText = "查看进度",
                        prefillText = "帮我分析一下最近的训练进度",
                        iconName = "trending_up",
                        suggestions =
                        listOf(
                            "分析我的训练进度",
                            "评估我的健身效果",
                            "查看我的力量提升",
                            "对比我的体重变化",
                            "总结我的训练成果",
                        ),
                        category = QuickActionCategory.TRAINING,
                    ),
                ),
            ),
            // 营养相关分类
            QuickActionCategoryGroup(
                category = QuickActionCategory.NUTRITION,
                actions =
                listOf(
                    QuickAction(
                        id = "diet_advice",
                        displayText = "饮食建议",
                        prefillText = "制定增肌期的饮食计划",
                        iconName = "restaurant",
                        suggestions =
                        listOf(
                            "制定一个减脂饮食计划",
                            "增肌期间应该怎么吃",
                            "运动前后的营养补充",
                            "健康的一日三餐搭配",
                            "如何计算每日所需热量",
                        ),
                        category = QuickActionCategory.NUTRITION,
                    ),
                    QuickAction(
                        id = "meal_plan",
                        displayText = "餐食计划",
                        prefillText = "请帮我制定一个健康的饮食计划",
                        iconName = "restaurant_menu",
                        suggestions =
                        listOf(
                            "制定一周的健康餐食计划",
                            "推荐高蛋白低脂肪的食物",
                            "设计适合减脂的餐食搭配",
                            "安排增肌期的营养摄入",
                            "制定素食健身饮食方案",
                        ),
                        category = QuickActionCategory.NUTRITION,
                    ),
                ),
            ),
            // 恢复相关分类
            QuickActionCategoryGroup(
                category = QuickActionCategory.RECOVERY,
                actions =
                listOf(
                    QuickAction(
                        id = "recovery_tips",
                        displayText = "恢复建议",
                        prefillText = "如何安排训练的休息日",
                        iconName = "bedtime",
                        suggestions =
                        listOf(
                            "如何安排训练的休息日",
                            "改善睡眠质量的方法",
                            "推荐训练后的拉伸动作",
                            "如何缓解肌肉酸痛",
                            "运动后的恢复策略",
                        ),
                        category = QuickActionCategory.RECOVERY,
                    ),
                    QuickAction(
                        id = "get_motivation",
                        displayText = "获取激励",
                        prefillText = "给我一些训练的激励和建议",
                        iconName = "psychology",
                        suggestions =
                        listOf(
                            "给我一些坚持训练的动力",
                            "如何克服训练中的困难",
                            "分享一些健身成功的故事",
                            "帮我制定训练目标",
                            "如何保持长期的健身习惯",
                        ),
                        category = QuickActionCategory.RECOVERY,
                    ),
                ),
            ),
        )

    /**
     * 获取所有快捷动作（扁平化列表）
     */
    fun getAllQuickActions(): List<QuickAction> = getQuickActionCategoryGroups().flatMap { it.actions }

    /**
     * 根据ID获取快捷动作
     */
    fun getQuickActionById(id: String): QuickAction? = getAllQuickActions().find { it.id == id }

    /**
     * 获取主要显示的快捷动作（用于主界面显示）
     */
    fun getPrimaryQuickActions(): List<QuickAction> =
        listOf(
            getQuickActionById("create_plan"),
            getQuickActionById("check_progress"),
            getQuickActionById("get_motivation"),
        ).filterNotNull()
}
