package com.example.gymbro.core.network.rest

import com.example.gymbro.core.network.config.NetworkConfig
import com.example.gymbro.core.network.config.NetworkConfigManager
import com.example.gymbro.core.network.config.NetworkConnectionLogger
import com.example.gymbro.core.network.monitor.NetworkMonitor
import com.example.gymbro.core.network.rest.interceptors.AuthInterceptor
import com.example.gymbro.core.network.rest.interceptors.NetworkStatusInterceptor
import com.example.gymbro.core.network.rest.interceptors.RetryInterceptor
import com.example.gymbro.core.network.rest.interceptors.SafeLoggingInterceptor
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import timber.log.Timber
import java.io.IOException
import java.util.concurrent.TimeUnit
import kotlin.time.Duration.Companion.milliseconds

/**
 * 🔥 工业级REST客户端 - 动态配置热切换版本
 *
 * 核心改进：
 * 1. 🔥 监听NetworkConfigManager配置变更，自动重建客户端
 * 2. 🔗 记录实际连接日志，确保配置生效溯源
 * 3. ⚡ 热切换：Provider变更时立即更新拦截器链
 * 4. 🛡️ 禁止配置缓存：始终使用最新配置
 */
class RestClientImpl(
    private val baseOkHttpClient: OkHttpClient,
    private val configManager: NetworkConfigManager, // 🔥 注入配置管理器
    private val networkMonitor: NetworkMonitor? = null,
) : RestClient {

    // 🔥 协程作用域用于监听配置变更
    private val configScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    // 🔥 当前生效配置（从configManager获取）
    private var currentConfig: NetworkConfig = configManager.getCurrentConfig()

    // 🔥 动态OkHttpClient - 配置变更时重建
    private var okHttpClient: OkHttpClient = buildOkHttpClient(currentConfig)

    init {
        Timber.d("🔥 RestClient初始化 (工业级动态配置版本):")
        Timber.d("  - 配置管理器: ${configManager::class.simpleName}")
        Timber.d("  - 初始配置: ${currentConfig.restBase}")

        // 🔥 启动配置监听
        startConfigMonitoring()
    }

    /**
     * 🔥 监听配置变更并热切换
     */
    private fun startConfigMonitoring() {
        configScope.launch {
            configManager.config.collect { newConfig ->
                Timber.d("🔄 检测到NetworkConfig变更 (REST)")
                handleConfigChange(newConfig)
            }
        }
    }

    /**
     * 🔥 处理配置变更 - 热切换核心逻辑
     */
    private suspend fun handleConfigChange(newConfig: NetworkConfig) {
        val oldConfig = currentConfig

        // 检查是否真的变更了
        if (oldConfig.restBase == newConfig.restBase &&
            oldConfig.apiKey == newConfig.apiKey &&
            oldConfig.connectTimeoutSec == newConfig.connectTimeoutSec &&
            oldConfig.readTimeoutSec == newConfig.readTimeoutSec
        ) {
            Timber.v("📋 REST配置无实质变更，跳过热切换")
            return
        }

        Timber.d("🔄 开始REST配置热切换:")
        Timber.d("  - 旧配置: ${oldConfig.restBase}")
        Timber.d("  - 新配置: ${newConfig.restBase}")

        // 记录配置变更
        NetworkConnectionLogger.logConnectionStateChange(
            type = "REST",
            fromState = "Config(${oldConfig.restBase})",
            toState = "Config(${newConfig.restBase})",
            reason = "NetworkConfigManager配置变更",
        )

        // 🔥 更新当前配置并重建客户端
        currentConfig = newConfig
        okHttpClient = buildOkHttpClient(newConfig)

        Timber.d("✅ REST配置热切换完成，下次请求将使用新配置")
    }

    /**
     * 🔥 构建OkHttpClient - 使用动态配置
     */
    private fun buildOkHttpClient(config: NetworkConfig): OkHttpClient {
        return baseOkHttpClient.newBuilder()
            .connectTimeout(config.connectTimeoutSec.toLong(), TimeUnit.SECONDS)
            .readTimeout(config.readTimeoutSec.toLong(), TimeUnit.SECONDS)
            .writeTimeout(config.writeTimeoutSec.toLong(), TimeUnit.SECONDS)
            .apply {
                // 添加拦截器链：Auth → NetworkStatus → Logging → Retry
                if (config.apiKey.isNotEmpty()) {
                    addInterceptor(AuthInterceptor(config.apiKey))
                }

                networkMonitor?.let { monitor ->
                    addInterceptor(NetworkStatusInterceptor(monitor))
                }

                if (config.enableDebugLogging) {
                    addInterceptor(SafeLoggingInterceptor(config.enableDebugLogging))
                }

                if (config.enableRetry) {
                    addInterceptor(
                        RetryInterceptor(
                            max = config.maxRetries,
                            base = config.retryDelayMs.milliseconds,
                        ),
                    )
                }
            }
            .build()
    }

    override suspend fun get(url: String, headers: Map<String, String>): ApiResult<String> {
        Timber.d("🌐 GET请求: $url")

        // 🔥 记录实际连接日志 - 工业级溯源
        NetworkConnectionLogger.logActualConnection(
            type = "REST",
            actualUrl = url,
            actualApiKey = currentConfig.apiKey,
            provider = "NetworkConfig",
            user = "current_user",
        )

        return safeApiCallString(networkMonitor) {
            val request = Request.Builder()
                .url(url)
                .apply {
                    headers.forEach { (key, value) ->
                        addHeader(key, value)
                        Timber.v("📋 请求头: $key = $value")
                    }
                }
                .build()

            okHttpClient.newCall(request).execute().use { response ->
                if (!response.isSuccessful) {
                    val errorMsg = "HTTP ${response.code}: ${response.message}"
                    Timber.e("🚨 GET请求失败: $errorMsg")
                    throw IOException(errorMsg)
                }

                val responseBody = response.body?.string() ?: ""
                Timber.d("✅ GET请求成功: ${responseBody.length} 字符")
                responseBody
            }
        }
    }

    override suspend fun post(url: String, body: String, headers: Map<String, String>): ApiResult<String> {
        Timber.d("🌐 POST请求: $url")
        Timber.v("📝 请求体: ${body.take(200)}${if (body.length > 200) "..." else ""}")

        return safeApiCallString(networkMonitor) {
            val request = Request.Builder()
                .url(url)
                .post(body.toRequestBody("application/json".toMediaType()))
                .apply {
                    headers.forEach { (key, value) ->
                        addHeader(key, value)
                        Timber.v("📋 请求头: $key = $value")
                    }
                }
                .build()

            okHttpClient.newCall(request).execute().use { response ->
                if (!response.isSuccessful) {
                    val errorMsg = "HTTP ${response.code}: ${response.message}"
                    Timber.e("🚨 POST请求失败: $errorMsg")
                    throw IOException(errorMsg)
                }

                val responseBody = response.body?.string() ?: ""
                Timber.d("✅ POST请求成功: ${responseBody.length} 字符")
                responseBody
            }
        }
    }

    override suspend fun put(url: String, body: String, headers: Map<String, String>): ApiResult<String> {
        Timber.d("🌐 PUT请求: $url")
        Timber.v("📝 请求体: ${body.take(200)}${if (body.length > 200) "..." else ""}")

        return safeApiCallString(networkMonitor) {
            val request = Request.Builder()
                .url(url)
                .put(body.toRequestBody("application/json".toMediaType()))
                .apply {
                    headers.forEach { (key, value) ->
                        addHeader(key, value)
                        Timber.v("📋 请求头: $key = $value")
                    }
                }
                .build()

            okHttpClient.newCall(request).execute().use { response ->
                if (!response.isSuccessful) {
                    val errorMsg = "HTTP ${response.code}: ${response.message}"
                    Timber.e("🚨 PUT请求失败: $errorMsg")
                    throw IOException(errorMsg)
                }

                val responseBody = response.body?.string() ?: ""
                Timber.d("✅ PUT请求成功: ${responseBody.length} 字符")
                responseBody
            }
        }
    }

    override suspend fun delete(url: String, headers: Map<String, String>): ApiResult<String> {
        Timber.d("🌐 DELETE请求: $url")

        return safeApiCallString(networkMonitor) {
            val request = Request.Builder()
                .url(url)
                .delete()
                .apply {
                    headers.forEach { (key, value) ->
                        addHeader(key, value)
                        Timber.v("📋 请求头: $key = $value")
                    }
                }
                .build()

            okHttpClient.newCall(request).execute().use { response ->
                if (!response.isSuccessful) {
                    val errorMsg = "HTTP ${response.code}: ${response.message}"
                    Timber.e("🚨 DELETE请求失败: $errorMsg")
                    throw IOException(errorMsg)
                }

                val responseBody = response.body?.string() ?: ""
                Timber.d("✅ DELETE请求成功: ${responseBody.length} 字符")
                responseBody
            }
        }
    }

    // 🗑️ 已清理：所有Legacy方法实现已删除
}
