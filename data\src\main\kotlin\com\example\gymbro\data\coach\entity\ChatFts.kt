package com.example.gymbro.data.local.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Fts4

/**
 * FTS5虚拟表
 * 专门用于全文搜索，支持bm25评分和高级搜索功能
 *
 * 升级到FTS5以获得更好的性能和功能支持
 * 支持更精确的BM25评分和高级搜索语法
 */
@Entity(tableName = "chat_fts")
@Fts4 // Room注解保持Fts4，但SQL使用FTS5语法
data class ChatFts(
    /**
     * 消息内容
     * 用于FTS4全文搜索
     */
    @ColumnInfo(name = "content")
    val content: String,
)
