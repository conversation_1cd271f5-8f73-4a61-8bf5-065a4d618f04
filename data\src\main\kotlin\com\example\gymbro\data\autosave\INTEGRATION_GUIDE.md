# 统一自动保存系统集成指南

## 📋 目录

1. [系统架构概述](#系统架构概述)
2. [Profile模块集成](#profile模块集成)
3. [Workout模块集成](#workout模块集成)
4. [自定义数据类型集成](#自定义数据类型集成)
5. [最佳实践](#最佳实践)
6. [故障排除](#故障排除)
7. [性能优化](#性能优化)

## 系统架构概述

### 核心组件

统一自动保存系统采用分层架构设计：

```
UI Layer (ViewModel)
    ↓
Adapter Layer (ProfileAutoSaveAdapter, WorkoutAutoSaveAdapter)
    ↓
Repository Layer (AutoSaveRepository)
    ↓
Session Layer (AutoSaveSession)
    ↓
Manager Layer (AutoSaveManager)
    ↓
Strategy & Storage Layer (AutoSaveStrategy, AutoSaveStorage)
```

### 依赖注入配置

系统已自动集成到现有DI架构，相关组件会自动注入：

```kotlin
// 在ViewModel中注入适配器
class YourViewModel @Inject constructor(
    private val profileAdapter: ProfileAutoSaveAdapter,
    private val workoutAdapter: WorkoutAutoSaveAdapter
) : ViewModel()
```

## Profile模块集成

### 步骤1：注入ProfileAutoSaveAdapter

```kotlin
@HiltViewModel
class ProfileViewModel @Inject constructor(
    private val profileAdapter: ProfileAutoSaveAdapter,
    private val updateUserProfileUseCase: UpdateUserProfileUseCase,
    private val getUserProfileUseCase: GetUserProfileUseCase
) : ViewModel() {

    private var autoSaveSessionId: String? = null
    private val _profileState = MutableStateFlow(ProfileState())
    val profileState = _profileState.asStateFlow()
}
```

### 步骤2：启动自动保存会话

```kotlin
fun startAutoSave(userId: String) {
    viewModelScope.launch {
        try {
            // 创建自动保存会话
            val createResult = profileAdapter.createAutoSave(userId, viewModelScope)

            when (createResult) {
                is ModernResult.Success -> {
                    autoSaveSessionId = createResult.data

                    // 启动自动保存
                    profileAdapter.startAutoSave(createResult.data, userId)

                    _profileState.update {
                        it.copy(autoSaveEnabled = true, autoSaveSessionId = createResult.data)
                    }

                    logger.d("ProfileViewModel", "自动保存已启动: ${createResult.data}")
                }
                is ModernResult.Error -> {
                    logger.e("ProfileViewModel", "启动自动保存失败", createResult.error.cause)
                    _profileState.update {
                        it.copy(autoSaveError = createResult.error.toString())
                    }
                }
                is ModernResult.Loading -> {
                    _profileState.update { it.copy(isLoading = true) }
                }
            }
        } catch (e: Exception) {
            logger.e(e, "启动自动保存异常")
            _profileState.update {
                it.copy(autoSaveError = e.message)
            }
        }
    }
}
```

### 步骤3：更新Profile数据

```kotlin
fun updateProfile(profile: UserProfile) {
    // 更新本地状态
    _profileState.update { it.copy(currentProfile = profile) }

    // 触发自动保存
    autoSaveSessionId?.let { sessionId ->
        profileAdapter.updateProfile(sessionId, profile)
        logger.d("ProfileViewModel", "Profile数据已更新并触发自动保存")
    }
}

// 处理UI事件
fun onDisplayNameChanged(displayName: String) {
    val currentProfile = _profileState.value.currentProfile
    val updatedProfile = currentProfile.copy(displayName = displayName)
    updateProfile(updatedProfile)
}

fun onWeightChanged(weight: Float) {
    val currentProfile = _profileState.value.currentProfile
    val updatedProfile = currentProfile.copy(weight = weight)
    updateProfile(updatedProfile)
}
```

### 步骤4：立即保存和停止

```kotlin
fun saveNow() {
    autoSaveSessionId?.let { sessionId ->
        viewModelScope.launch {
            val result = profileAdapter.saveNow(sessionId)
            result.fold(
                onSuccess = {
                    logger.d("ProfileViewModel", "立即保存成功")
                    _profileState.update { it.copy(lastSaveTime = System.currentTimeMillis()) }
                },
                onFailure = { error ->
                    logger.e(error, "立即保存失败")
                    _profileState.update { it.copy(autoSaveError = error.message) }
                }
            )
        }
    }
}

fun stopAutoSave() {
    autoSaveSessionId?.let { sessionId ->
        viewModelScope.launch {
            profileAdapter.stopAutoSave(sessionId)
            autoSaveSessionId = null
            _profileState.update {
                it.copy(autoSaveEnabled = false, autoSaveSessionId = null)
            }
            logger.d("ProfileViewModel", "自动保存已停止")
        }
    }
}

override fun onCleared() {
    super.onCleared()
    stopAutoSave()
}
```

## Workout模块集成

### 步骤1：注入WorkoutAutoSaveAdapter

```kotlin
@HiltViewModel
class WorkoutViewModel @Inject constructor(
    private val workoutAdapter: WorkoutAutoSaveAdapter,
    private val workoutDraftRepository: WorkoutDraftRepository
) : ViewModel() {

    private var autoSaveSessionId: String? = null
    private val _workoutState = MutableStateFlow(WorkoutState())
    val workoutState = _workoutState.asStateFlow()
}
```

### 步骤2：启动自动保存会话

```kotlin
fun startAutoSave(draftId: String) {
    viewModelScope.launch {
        try {
            // 创建自动保存会话（使用定时保存策略）
            val createResult = workoutAdapter.createAutoSave(draftId, viewModelScope)

            when (createResult) {
                is ModernResult.Success -> {
                    autoSaveSessionId = createResult.data

                    // 启动自动保存
                    workoutAdapter.startAutoSave(createResult.data, draftId)

                    _workoutState.update {
                        it.copy(
                            autoSaveEnabled = true,
                            autoSaveSessionId = createResult.data,
                            draftId = draftId
                        )
                    }

                    logger.d("WorkoutViewModel", "自动保存已启动: ${createResult.data}")
                }
                is ModernResult.Error -> {
                    logger.e("WorkoutViewModel", "启动自动保存失败", createResult.error.cause)
                    _workoutState.update {
                        it.copy(autoSaveError = createResult.error.toString())
                    }
                }
            }
        } catch (e: Exception) {
            logger.e(e, "启动自动保存异常")
        }
    }
}
```

### 步骤3：更新TemplateDraft数据

```kotlin
fun updateDraft(draft: TemplateDraft) {
    // 更新本地状态
    _workoutState.update { it.copy(currentDraft = draft) }

    // 触发自动保存（定时保存，3秒间隔）
    autoSaveSessionId?.let { sessionId ->
        workoutAdapter.updateDraft(sessionId, draft)
        logger.d("WorkoutViewModel", "TemplateDraft数据已更新")
    }
}

// 处理UI事件
fun onExerciseAdded(exercise: Exercise) {
    val currentDraft = _workoutState.value.currentDraft
    val updatedDraft = currentDraft.copy(
        exercises = currentDraft.exercises + exercise
    )
    updateDraft(updatedDraft)
}

fun onExerciseRemoved(exerciseId: String) {
    val currentDraft = _workoutState.value.currentDraft
    val updatedDraft = currentDraft.copy(
        exercises = currentDraft.exercises.filter { it.id != exerciseId }
    )
    updateDraft(updatedDraft)
}
```

### 步骤4：暂停/恢复和缓存管理

```kotlin
fun pauseAutoSave() {
    autoSaveSessionId?.let { sessionId ->
        workoutAdapter.pauseAutoSave(sessionId)
        _workoutState.update { it.copy(autoSavePaused = true) }
        logger.d("WorkoutViewModel", "自动保存已暂停")
    }
}

fun resumeAutoSave() {
    autoSaveSessionId?.let { sessionId ->
        workoutAdapter.resumeAutoSave(sessionId)
        _workoutState.update { it.copy(autoSavePaused = false) }
        logger.d("WorkoutViewModel", "自动保存已恢复")
    }
}

fun restoreFromCache() {
    autoSaveSessionId?.let { sessionId ->
        workoutAdapter.restoreFromCache(sessionId)
        logger.d("WorkoutViewModel", "正在恢复缓存数据")
    }
}

fun discardCache() {
    autoSaveSessionId?.let { sessionId ->
        workoutAdapter.discardCache(sessionId)
        logger.d("WorkoutViewModel", "缓存数据已丢弃")
    }
}
```

## 自定义数据类型集成

### 步骤1：创建自定义适配器

```kotlin
@Singleton
class CustomAutoSaveAdapter @Inject constructor(
    private val autoSaveRepository: AutoSaveRepository,
    private val customRepository: CustomRepository,
    private val json: Json,
    private val logger: Logger
) {

    suspend fun createAutoSave(
        dataId: String,
        scope: CoroutineScope,
        strategy: AutoSaveStrategy<CustomData> = ThrottledSaveStrategy.create(logger, 2000L)
    ): ModernResult<String> {
        return try {
            // 创建存储后端
            val storage = createCustomStorage(dataId)

            // 创建自动保存配置
            val config = AutoSaveConfig(
                id = "custom_autosave_$dataId",
                strategy = strategy,
                storage = storage,
                enableRecovery = true
            )

            // 创建会话
            autoSaveRepository.createSession(config, scope)
        } catch (e: Exception) {
            ModernResult.Error(
                ModernDataError(
                    operationName = "CustomAutoSaveAdapter.createAutoSave",
                    errorType = GlobalErrorType.Data.General,
                    category = ErrorCategory.DATA,
                    cause = e
                )
            )
        }
    }

    private fun createCustomStorage(dataId: String): AutoSaveStorage<CustomData> {
        return object : AutoSaveStorage<CustomData> {
            override suspend fun save(id: String, data: CustomData) {
                val result = customRepository.saveData(data)
                when (result) {
                    is ModernResult.Success -> {
                        logger.d("CustomAutoSaveAdapter", "自定义数据保存成功: ${data.id}")
                    }
                    is ModernResult.Error -> {
                        logger.e("CustomAutoSaveAdapter", "自定义数据保存失败", result.error.cause)
                        throw Exception("自定义数据保存失败: ${result.error}")
                    }
                }
            }

            override suspend fun restore(id: String): CustomData? {
                val result = customRepository.getData(dataId)
                return when (result) {
                    is ModernResult.Success -> result.data
                    else -> null
                }
            }

            override suspend fun clear(id: String) {
                customRepository.deleteData(dataId)
            }

            override suspend fun exists(id: String): Boolean {
                val result = customRepository.getData(dataId)
                return result is ModernResult.Success
            }

            override suspend fun getSize(id: String): Long {
                return try {
                    val data = restore(id)
                    if (data != null) {
                        json.encodeToString(data).length.toLong()
                    } else {
                        0L
                    }
                } catch (e: Exception) {
                    0L
                }
            }
        }
    }
}
```

### 步骤2：在ViewModel中使用

```kotlin
@HiltViewModel
class CustomViewModel @Inject constructor(
    private val customAdapter: CustomAutoSaveAdapter
) : ViewModel() {

    fun startAutoSave(dataId: String) {
        viewModelScope.launch {
            // 使用防抖策略，2秒延迟
            val strategy = ThrottledSaveStrategy.create<CustomData>(logger, 2000L)
            val result = customAdapter.createAutoSave(dataId, viewModelScope, strategy)

            when (result) {
                is ModernResult.Success -> {
                    // 处理成功
                }
                is ModernResult.Error -> {
                    // 处理错误
                }
            }
        }
    }
}
```

## 最佳实践

### 1. 选择合适的保存策略

- **即时保存**：适用于重要数据，如用户资料
- **定时保存**：适用于频繁编辑的数据，如训练模板
- **防抖保存**：适用于高频变更的数据，如搜索输入

### 2. 生命周期管理

```kotlin
// 在ViewModel中正确管理生命周期
override fun onCleared() {
    super.onCleared()
    stopAutoSave() // 确保资源正确释放
}

// 在Activity/Fragment中处理暂停/恢复
override fun onPause() {
    super.onPause()
    viewModel.pauseAutoSave()
}

override fun onResume() {
    super.onResume()
    viewModel.resumeAutoSave()
}
```

### 3. 错误处理

```kotlin
// 监控自动保存状态
session.state.collect { state ->
    when {
        state.hasError -> {
            // 显示错误提示
            showErrorSnackbar("自动保存失败: ${state.error?.message}")

            // 可选：提供重试机制
            showRetryButton { viewModel.saveNow() }
        }
        state.isSaving -> {
            // 显示保存指示器
            showSavingIndicator()
        }
        state.hasUnsavedChanges -> {
            // 显示未保存提示
            showUnsavedIndicator()
        }
    }
}
```

### 4. 性能优化

```kotlin
// 避免在高频回调中直接调用更新
private val updateDebouncer = Debouncer(300L) // 300ms防抖

fun onTextChanged(text: String) {
    updateDebouncer.debounce {
        updateData(text)
    }
}
```

## 故障排除

### 常见问题1：自动保存不工作

**症状**：数据变更后没有触发自动保存

**可能原因**：
- 会话未正确启动
- 数据更新方法未调用适配器
- 1秒间隔限制阻止了保存

**解决方案**：
```kotlin
// 检查会话状态
session.sessionStatus.collect { status ->
    logger.d("AutoSave", "会话状态: $status")
    if (status != SessionStatus.ACTIVE) {
        // 重新启动会话
        startAutoSave()
    }
}

// 检查最后保存时间
val lastSaveTime = session.getLastSaveTime()
val timeSinceLastSave = System.currentTimeMillis() - lastSaveTime
logger.d("AutoSave", "距离上次保存: ${timeSinceLastSave}ms")
```

### 常见问题2：保存频率过高

**症状**：日志显示大量"保存操作被限制"消息

**可能原因**：
- UI更新过于频繁
- 使用了即时保存策略但数据变更频繁

**解决方案**：
```kotlin
// 改用防抖策略
val strategy = ThrottledSaveStrategy.create<T>(logger, 1500L)

// 或者在UI层添加防抖
private val updateDebouncer = Debouncer(500L)
fun onDataChanged(data: T) {
    updateDebouncer.debounce {
        adapter.updateData(sessionId, data)
    }
}
```

### 常见问题3：内存泄漏

**症状**：应用内存使用持续增长

**可能原因**：
- 会话未正确停止
- 协程作用域未正确管理

**解决方案**：
```kotlin
// 确保在ViewModel销毁时停止会话
override fun onCleared() {
    super.onCleared()
    stopAutoSave()
}

// 使用正确的协程作用域
fun startAutoSave() {
    // 使用viewModelScope，会自动取消
    val result = adapter.createAutoSave(id, viewModelScope)
}
```

### 常见问题4：数据丢失

**症状**：应用重启后数据丢失

**可能原因**：
- 缓存恢复未启用
- 存储后端配置错误

**解决方案**：
```kotlin
// 启用缓存恢复
val config = AutoSaveConfig(
    id = "data_id",
    strategy = strategy,
    storage = storage,
    enableRecovery = true // 确保启用
)

// 在应用启动时检查缓存
fun checkCache() {
    session.state.collect { state ->
        if (state.hasRecoverableCache) {
            // 显示恢复对话框
            showRecoveryDialog {
                session.restoreFromCache()
            }
        }
    }
}
```

## 性能优化

### 1. 减少序列化开销

```kotlin
// 使用轻量级的数据类
@Serializable
data class LightweightData(
    val id: String,
    val essentialField: String
    // 避免包含大量嵌套对象
)

// 自定义序列化器
object CustomSerializer : KSerializer<CustomData> {
    override fun serialize(encoder: Encoder, value: CustomData) {
        // 只序列化必要字段
    }
}
```

### 2. 优化保存频率

```kotlin
// 使用合适的间隔
val strategy = IntervalSaveStrategy.create<T>(logger, 5000L) // 5秒间隔

// 根据数据重要性调整策略
val criticalDataStrategy = ImmediateSaveStrategy.create<T>(logger)
val normalDataStrategy = IntervalSaveStrategy.create<T>(logger, 10000L)
```

### 3. 监控性能指标

```kotlin
// 监控保存次数和频率
session.state.collect { state ->
    logger.d("Performance", "保存次数: ${state.saveCount}")
    logger.d("Performance", "最后保存时间: ${state.lastSaveTime}")
}

// 监控会话统计
val stats = repository.getSessionStats()
logger.d("Performance", "活跃会话: ${stats.activeSessions}")
logger.d("Performance", "总保存次数: ${stats.totalSaveCount}")
```

---

更多详细信息请参考：
- [README.md](README.md) - 系统概述
- [USAGE_EXAMPLES.kt](USAGE_EXAMPLES.kt) - 代码示例
