package com.example.gymbro.core.resources

import android.content.Context
import com.example.gymbro.core.ui.text.UiText
import dagger.hilt.android.qualifiers.ApplicationContext
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 资源提供器实现的统一文件
 *
 * 此文件包含ResourceProvider接口的所有实现，包括：
 * - AndroidResourceProvider：使用Android Context访问资源
 * - CachedResourceProvider：通过LRU缓存优化资源访问性能
 * - ConstantResourceProvider：基于常量的平台无关实现
 *
 * 优化目标：
 * - 减少文件碎片化
 * - 优化模块结构
 * - 提高代码可维护性
 */

/**
 * 资源提供器接口
 *
 * 定义用于访问应用程序资源的标准API。
 * 此接口抽象了资源访问逻辑，使业务逻辑与平台特定的资源访问机制隔离。
 * 这允许核心业务逻辑不依赖于特定平台的资源系统，便于测试和跨平台使用。
 */
interface ResourceProvider {
    /**
     * 获取字符串资源
     *
     * @param resId 资源ID
     * @return 字符串资源值
     */
    fun getString(resId: Int): String

    /**
     * 获取带格式化参数的字符串资源
     *
     * @param resId 资源ID
     * @param args 格式化参数
     * @return 格式化后的字符串
     */
    fun getString(
        resId: Int,
        vararg args: Any,
    ): String

    /**
     * 获取基于数量的复数形式字符串
     *
     * @param resId 资源ID
     * @param quantity 数量
     * @param args 格式化参数
     * @return 基于数量的字符串
     */
    fun getQuantityString(
        resId: Int,
        quantity: Int,
        vararg args: Any,
    ): String

    /**
     * 将资源ID转换为UiText
     *
     * @param resId 资源ID
     * @param args 格式化参数
     * @return UiText实例
     */
    fun asUiText(
        resId: Int,
        vararg args: Any,
    ): UiText

    /**
     * 获取当前语言
     *
     * @return 语言代码
     */
    fun getCurrentLanguage(): String

    /**
     * 获取整数资源
     *
     * @param resId 资源ID
     * @return 整数资源值
     */
    fun getInteger(resId: Int): Int
}

/**
 * ResourceProvider的Android实现
 *
 * 使用Android Context访问资源，实现ResourceProvider接口
 * 注意：此实现依赖Android平台，通常应在平台特定模块中提供
 */
@Singleton
class AndroidResourceProvider
    @Inject
    constructor(
        @ApplicationContext private val context: Context,
    ) : ResourceProvider {
        override fun getString(resId: Int): String = context.getString(resId)

        override fun getString(
            resId: Int,
            vararg args: Any,
        ): String = context.getString(resId, *args)

        override fun getQuantityString(
            resId: Int,
            quantity: Int,
            vararg args: Any,
        ): String = context.resources.getQuantityString(resId, quantity, *args)

        override fun asUiText(
            resId: Int,
            vararg args: Any,
        ): UiText = UiText.stringResource(resId, *args.map { it.toString() }.toTypedArray())

        override fun getCurrentLanguage(): String = Locale.getDefault().toLanguageTag()

        override fun getInteger(resId: Int): Int = context.resources.getInteger(resId)
    }

/**
 * 基于常量的资源提供者
 *
 * 简化的测试用ResourceProvider实现，不依赖Android资源系统。
 * 主要用于单元测试和非Android环境，提供基本的资源访问功能。
 *
 * 注意：此实现已简化，移除了复杂的硬编码映射，
 * 改为提供简单的默认值和格式化支持。
 */
@Singleton
class ConstantResourceProvider
    @Inject
    constructor() : ResourceProvider {
        /**
         * 从常量获取字符串
         *
         * @param resId 资源ID（标识符）
         * @return 字符串值
         */
        override fun getString(resId: Int): String = "test_string_$resId"

        /**
         * 获取带格式参数的字符串
         *
         * @param resId 资源ID（标识符）
         * @param args 格式参数
         * @return 格式化后的字符串
         */
        override fun getString(
            resId: Int,
            vararg args: Any,
        ): String {
            val baseString = "test_string_$resId"
            return if (args.isNotEmpty()) {
                try {
                    String.format("$baseString with args: %s", args.joinToString(", "))
                } catch (e: Exception) {
                    baseString
                }
            } else {
                baseString
            }
        }

        /**
         * 获取基于数量的复数形式字符串
         *
         * @param resId 资源ID（标识符）
         * @param quantity 数量
         * @param args 格式参数
         * @return 基于数量选择的字符串
         */
        override fun getQuantityString(
            resId: Int,
            quantity: Int,
            vararg args: Any,
        ): String {
            val baseString = "test_quantity_string_$resId"
            val quantityText = if (quantity == 1) "item" else "items"
            return "$baseString: $quantity $quantityText"
        }

        /**
         * 将资源ID转换为UiText
         *
         * @param resId 资源ID（标识符）
         * @param args 格式参数
         * @return UiText实例
         */
        override fun asUiText(
            resId: Int,
            vararg args: Any,
        ): UiText {
            // 使用动态字符串创建UiText，而不是资源ID
            return UiText.DynamicString(getString(resId, *args))
        }

        /**
         * 获取当前语言代码
         *
         * @return 语言代码
         */
        override fun getCurrentLanguage(): String = Locale.getDefault().toLanguageTag()

        /**
         * 获取整数值
         *
         * @param resId 资源ID（标识符）
         * @return 整数值
         */
        override fun getInteger(resId: Int): Int = resId // 简化实现：直接返回ID作为测试值

        companion object {
            // 移除了复杂的硬编码映射方法，简化为基本的测试实现
        }
    }

/**
 * ResourceProvider的缓存实现
 * 通过LRU缓存机制优化资源访问性能
 */
@Singleton
class CachedResourceProvider
    @Inject
    constructor(
        private val delegate: ResourceProvider,
    ) : ResourceProvider {
        // 普通字符串资源缓存
        private val stringCache = LruCache<Int, String>(STRING_CACHE_SIZE)

        // 参数化字符串资源缓存 (resId + args.hashCode 作为key)
        private val paramStringCache = LruCache<String, String>(PARAM_CACHE_SIZE)

        // 复数字符串资源缓存 (resId + quantity + args.hashCode 作为key)
        private val quantityStringCache = LruCache<String, String>(QUANTITY_CACHE_SIZE)

        // 整数资源缓存
        private val integerCache = LruCache<Int, Int>(INTEGER_CACHE_SIZE)

        // 当前语言缓存
        private var cachedLanguage: String? = null

        /**
         * 获取普通字符串资源
         */
        override fun getString(resId: Int): String {
            // 检查缓存
            stringCache.get(resId)?.let { return it }

            // 缓存未命中，委托给实际ResourceProvider
            val value = delegate.getString(resId)

            // 添加到缓存
            stringCache.put(resId, value)

            return value
        }

        /**
         * 获取带参数的字符串资源
         */
        override fun getString(
            resId: Int,
            vararg args: Any,
        ): String {
            // 为参数化字符串创建缓存键
            val cacheKey = "${resId}_${args.contentDeepHashCode()}"

            // 检查缓存
            paramStringCache.get(cacheKey)?.let { return it }

            // 缓存未命中，委托给实际ResourceProvider
            val value = delegate.getString(resId, *args)

            // 添加到缓存
            paramStringCache.put(cacheKey, value)

            return value
        }

        /**
         * 获取复数字符串资源
         */
        override fun getQuantityString(
            resId: Int,
            quantity: Int,
            vararg args: Any,
        ): String {
            // 为复数字符串创建缓存键
            val cacheKey = "${resId}_${quantity}_${args.contentDeepHashCode()}"

            // 检查缓存
            quantityStringCache.get(cacheKey)?.let { return it }

            // 缓存未命中，委托给实际ResourceProvider
            val value = delegate.getQuantityString(resId, quantity, *args)

            // 添加到缓存
            quantityStringCache.put(cacheKey, value)

            return value
        }

        /**
         * 创建UiText包装
         */
        override fun asUiText(
            resId: Int,
            vararg args: Any,
        ): UiText = UiText.stringResource(resId, *args.map { it.toString() }.toTypedArray())

        /**
         * 获取当前语言
         */
        override fun getCurrentLanguage(): String {
            // 检查缓存
            cachedLanguage?.let { return it }

            // 缓存未命中，委托给实际ResourceProvider
            val language = delegate.getCurrentLanguage()

            // 添加到缓存
            cachedLanguage = language

            return language
        }

        /**
         * 获取整数资源
         */
        override fun getInteger(resId: Int): Int {
            // 检查缓存
            integerCache.get(resId)?.let { return it }

            // 缓存未命中，委托给实际ResourceProvider
            val value = delegate.getInteger(resId)

            // 添加到缓存
            integerCache.put(resId, value)

            return value
        }

        companion object {
            private const val STRING_CACHE_SIZE = 100 // 普通字符串缓存大小
            private const val PARAM_CACHE_SIZE = 50 // 参数化字符串缓存大小
            private const val QUANTITY_CACHE_SIZE = 30 // 复数字符串缓存大小
            private const val INTEGER_CACHE_SIZE = 20 // 整数资源缓存大小
        }

        /**
         * 简单的LRU缓存实现
         */
        private class LruCache<K, V>(
            private var maxSize: Int,
        ) {
            private val map = LinkedHashMap<K, V>(0, 0.75f, true)

            fun get(key: K): V? = map[key]

            fun put(
                key: K,
                value: V,
            ) {
                map[key] = value
                trimToSize()
            }

            fun evictAll() {
                map.clear()
            }

            fun resize(maxSize: Int) {
                this.maxSize = maxSize
                trimToSize()
            }

            private fun trimToSize() {
                while (map.size > maxSize) {
                    val toEvict = map.entries.iterator().next()
                    map.remove(toEvict.key)
                }
            }
        }
    }
