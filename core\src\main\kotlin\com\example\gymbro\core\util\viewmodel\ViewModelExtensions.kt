/**
 * ViewModel扩展函数
 *
 * 提供标准化的ViewModel操作扩展函数，简化常见操作。
 * 特别是对ModernResult的处理，使错误处理更加统一和简洁。
 */
package com.example.gymbro.core.util.viewmodel

import com.example.gymbro.core.error.ModernErrorHandler
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

/**
 * 处理ModernResult结果，并更新ViewModel的状态
 *
 * 这个扩展函数封装了ModernResult的标准处理流程：
 * 1. 关闭加载状态
 * 2. 处理成功或错误结果
 * 3. 更新相应的状态
 *
 * @param errorHandler 错误处理器，用于将技术错误转换为UI友好的消息
 * @param setLoading 设置加载状态的函数
 * @param setSuccess 设置成功数据的函数
 * @param setError 设置错误消息的函数
 */
fun <T> ModernResult<T>.handleInViewModel(
    errorHandler: ModernErrorHandler,
    setLoading: (Boolean) -> Unit,
    setSuccess: (T) -> Unit,
    setError: (UiText?) -> Unit,
) {
    setLoading(false)
    when (this) {
        is ModernResult.Success -> {
            setSuccess(data)
            setError(null)
        }
        is ModernResult.Error -> {
            setError(errorHandler.getUiMessage(error))
        }
        is ModernResult.Loading -> {
            setLoading(true)
            setError(null)
        }
    }
}

/**
 * 处理Flow<ModernResult>流，并映射为UI状态
 *
 * 这个扩展函数简化了从Flow<ModernResult>到UI状态的转换：
 * 1. 将每个ModernResult转换为对应的UI状态
 * 2. 使用提供的状态构造函数创建不同状态（成功、错误）
 * 3. 加载中状态应由调用方在收集前手动设置
 *
 * @param errorHandler 错误处理器
 * @param successState 创建成功状态的函数
 * @param errorState 创建错误状态的函数
 * @return 映射后的UI状态流
 */
fun <T, S> Flow<ModernResult<T>>.mapToUiState(
    errorHandler: ModernErrorHandler,
    successState: (T) -> S,
    errorState: (UiText) -> S,
    loadingState: () -> S,
): Flow<S> =
    this.map { result ->
        when (result) {
            is ModernResult.Success -> successState(result.data)
            is ModernResult.Error -> errorState(errorHandler.getUiMessage(result.error))
            is ModernResult.Loading -> loadingState()
        }
    }

/**
 * 简化错误处理的函数，仅关注错误消息部分
 *
 * 对于只需要获取错误消息的场景，这个函数提供了更简洁的API。
 *
 * @param errorHandler 错误处理器
 * @return 错误消息，如果是成功结果则返回null
 */
fun ModernResult<*>.getErrorMessage(errorHandler: ModernErrorHandler): UiText? =
    if (this is ModernResult.Error) {
        errorHandler.getUiMessage(error)
    } else {
        null
    }

/**
 * 根据建议操作类型处理错误
 *
 * 根据ModernErrorHandler判断的建议操作，执行不同的处理函数。
 * 这简化了对不同类型错误的分发处理。
 *
 * @param errorHandler 错误处理器
 * @param handlers 不同建议操作的处理函数映射
 */
fun ModernDataError.handleBySuggestedAction(
    errorHandler: ModernErrorHandler,
    handlers: Map<ModernErrorHandler.SuggestedAction, (ModernDataError) -> Unit>,
) {
    val action = errorHandler.getSuggestedAction(this)
    handlers[action]?.invoke(this)
}

/**
 * 创建标准UI状态加载器
 *
 * 返回一个函数，能够根据ModernResult的不同状态来更新UI状态。
 * 这简化了ViewModel中状态加载的标准模式实现。
 *
 * @param errorHandler 错误处理器
 * @param onSuccess 处理成功数据的函数
 * @param onError 处理错误的函数，默认为空实现
 * @param onLoading 处理加载状态的函数，默认为空实现
 * @return 用于处理ModernResult的函数
 */
fun <T> createStateLoader(
    errorHandler: ModernErrorHandler,
    onSuccess: (T) -> Unit,
    onError: (UiText) -> Unit = {},
    onLoading: () -> Unit = {},
): (ModernResult<T>) -> Unit =
    { result ->
        when (result) {
            is ModernResult.Success -> onSuccess(result.data)
            is ModernResult.Error -> onError(errorHandler.getUiMessage(result.error))
            is ModernResult.Loading -> onLoading()
        }
    }

/**
 * 映射ModernResult的成功值，保持错误和加载状态不变
 */
fun <T, R> ModernResult<T>.mapSuccess(transform: (T) -> R): ModernResult<R> {
    return when (this) {
        is ModernResult.Loading -> ModernResult.Loading
        is ModernResult.Success -> ModernResult.Success(transform(data))
        is ModernResult.Error -> ModernResult.Error(error)
    }
}

/**
 * 将ModernDataError转换为用户友好的错误消息
 */
fun ModernDataError.toUserFriendlyMessage(): UiText {
    return uiMessage ?: when (errorType) {
        is GlobalErrorType.Network -> UiText.DynamicString("网络连接异常，请检查网络设置")
        is GlobalErrorType.Auth -> UiText.DynamicString("身份验证失败，请重新登录")
        is GlobalErrorType.Business -> UiText.DynamicString("操作失败，请稍后重试")
        is GlobalErrorType.Data -> UiText.DynamicString("数据处理异常")
        is GlobalErrorType.System -> UiText.DynamicString("系统异常，请联系客服")
        else -> UiText.DynamicString("未知错误")
    }
}
