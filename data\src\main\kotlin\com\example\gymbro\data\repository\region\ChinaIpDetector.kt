package com.example.gymbro.data.repository.region

import android.content.Context
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.data.util.IpUtils
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 中国IP段检测器
 *
 * 使用assets中的CN IP段文件进行检测
 * 支持优化的CIDR格式和详细IP段格式的双文件容错机制
 */
@Singleton
class ChinaIpDetector @Inject constructor(
    @ApplicationContext private val context: Context,
) {

    companion object {
        private const val CIDR_FILE = "all_cn_cidr.txt"
        private const val FALLBACK_FILE = "all_cn.txt"
    }

    // 缓存IP段列表，避免重复加载
    private var cachedCidrList: List<String>? = null
    private var cacheInitialized = false

    /**
     * 检测IP地址是否属于中国
     * @param ip IP地址字符串
     * @return 是否为中国IP
     */
    suspend fun isChinaIp(ip: String): ModernResult<Boolean> {
        return withContext(Dispatchers.IO) {
            try {
                // 验证IP格式
                if (!IpUtils.isValidIpv4(ip)) {
                    Timber.w("无效的IP地址格式: $ip")
                    return@withContext ModernResult.Success(false)
                }

                // 获取中国IP段列表
                val cidrList = getChinaIpList()
                if (cidrList.isEmpty()) {
                    Timber.w("无法加载中国IP段文件，默认返回false")
                    return@withContext ModernResult.Success(false)
                }

                // 检测IP是否在中国IP段中
                val isChina = IpUtils.isIpInCidrList(ip, cidrList)
                Timber.d("IP $ip 检测结果: ${if (isChina) "中国" else "国际"}")

                ModernResult.Success(isChina)
            } catch (e: Exception) {
                Timber.e(e, "中国IP检测异常: $ip")
                ModernResult.Success(false) // 异常时默认返回false
            }
        }
    }

    /**
     * 批量检测多个IP地址
     * @param ips IP地址列表
     * @return 检测结果映射
     */
    suspend fun batchDetectChinaIps(ips: List<String>): ModernResult<Map<String, Boolean>> {
        return withContext(Dispatchers.IO) {
            try {
                val cidrList = getChinaIpList()
                if (cidrList.isEmpty()) {
                    Timber.w("无法加载中国IP段文件，批量检测返回空结果")
                    return@withContext ModernResult.Success(emptyMap())
                }

                val results = mutableMapOf<String, Boolean>()
                for (ip in ips) {
                    if (IpUtils.isValidIpv4(ip)) {
                        results[ip] = IpUtils.isIpInCidrList(ip, cidrList)
                    } else {
                        results[ip] = false
                    }
                }

                Timber.d("批量IP检测完成: ${results.size}个IP")
                ModernResult.Success(results)
            } catch (e: Exception) {
                Timber.e(e, "批量IP检测异常")
                ModernResult.Success(emptyMap())
            }
        }
    }

    /**
     * 清除缓存，强制重新加载
     */
    fun clearCache() {
        cachedCidrList = null
        cacheInitialized = false
        Timber.d("中国IP段缓存已清除")
    }

    /**
     * 获取中国IP段列表
     * 使用缓存机制避免重复加载
     */
    private suspend fun getChinaIpList(): List<String> {
        return withContext(Dispatchers.IO) {
            if (cacheInitialized && cachedCidrList != null) {
                return@withContext cachedCidrList!!
            }

            try {
                // 优先尝试加载优化的CIDR文件
                val cidrList = try {
                    loadAssetFile(CIDR_FILE)
                } catch (e: Exception) {
                    Timber.w(e, "加载主CIDR文件失败，尝试备用文件")
                    // 备用：加载详细IP段文件
                    loadAssetFile(FALLBACK_FILE)
                }

                cachedCidrList = cidrList
                cacheInitialized = true

                Timber.i("中国IP段加载成功: ${cidrList.size}条记录")
                cidrList
            } catch (e: Exception) {
                Timber.e(e, "加载中国IP段文件失败")
                cacheInitialized = true
                cachedCidrList = emptyList()
                emptyList()
            }
        }
    }

    /**
     * 从assets加载文件内容
     */
    private fun loadAssetFile(fileName: String): List<String> {
        return context.assets.open(fileName).use { inputStream ->
            inputStream.bufferedReader().useLines { lines ->
                lines.filter { line ->
                    line.isNotBlank() && !line.startsWith("#")
                }.map { line ->
                    line.trim()
                }.toList()
            }
        }
    }
}