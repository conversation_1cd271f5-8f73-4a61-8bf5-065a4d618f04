package com.example.gymbro.core.userdata.internal.service

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.userdata.api.UserDataCenterApi
import com.example.gymbro.domain.profile.model.user.UserProfile
import com.example.gymbro.domain.user.service.UserDataService
import javax.inject.Inject
import javax.inject.Singleton

/**
 * UserDataService 的实现类
 *
 * 作为 domain 层 UserDataService 接口的具体实现，桥接 domain 层和 core-user-data-center 模块。
 * 遵循 Clean Architecture 的依赖倒置原则，domain 层定义接口，基础设施层提供实现。
 *
 * 核心职责：
 * - 实现 domain 层定义的 UserDataService 接口
 * - 将调用委托给 UserDataCenterApi
 * - 处理数据转换和错误映射
 * - 提供统一的用户数据访问服务
 *
 * 设计特点：
 * - 适配器模式：适配 UserDataCenterApi 到 UserDataService 接口
 * - 单一职责：专注于用户数据的 CRUD 操作
 * - 错误透传：保持原始错误信息的完整性
 */
@Singleton
class UserDataServiceImpl @Inject constructor(
    private val userDataCenterApi: UserDataCenterApi,
    private val logger: Logger
) : UserDataService {

    companion object {
        private const val TAG = "UserDataServiceImpl"
    }

    /**
     * 获取当前用户的资料信息
     *
     * 通过 UserDataCenterApi 获取统一用户数据，然后提取 UserProfile 信息。
     *
     * @return 用户资料，如果用户未登录或资料不存在则返回 null
     */
    override suspend fun getCurrentUserProfile(): ModernResult<UserProfile?> {
        logger.d(TAG, "获取当前用户资料")

        return try {
            when (val result = userDataCenterApi.getCurrentUserData()) {
                is ModernResult.Success -> {
                    val userData = result.data
                    if (userData != null) {
                        // 从 UnifiedUserData 提取 UserProfile 信息
                        val profile = UserProfile(
                            userId = userData.userId,
                            username = userData.username,
                            displayName = userData.displayName,
                            email = userData.email,
                            phoneNumber = userData.phoneNumber,
                            avatarUrl = null, // UnifiedUserData 中没有此字段，使用默认值
                            bio = userData.bio,
                            gender = userData.gender,
                            height = userData.height,
                            weight = userData.weight,
                            fitnessLevel = userData.fitnessLevel,
                            fitnessGoals = userData.fitnessGoals,
                            workoutDays = userData.workoutDays,
                            allowPartnerMatching = userData.allowPartnerMatching,
                            totalActivityCount = userData.totalActivityCount,
                            weeklyActiveMinutes = userData.weeklyActiveMinutes,
                            likesReceived = 0, // UnifiedUserData 中没有此字段，使用默认值
                            isAnonymous = userData.isAnonymous
                        )

                        logger.d(TAG, "成功获取用户资料: userId=${profile.userId}, displayName=${profile.displayName}")
                        ModernResult.Success(profile)
                    } else {
                        logger.d(TAG, "用户未登录或数据不存在")
                        ModernResult.Success(null)
                    }
                }
                is ModernResult.Error -> {
                    logger.e(TAG, "获取用户数据失败: ${result.error}")
                    ModernResult.Error(result.error)
                }
                is ModernResult.Loading -> {
                    logger.d(TAG, "用户数据加载中")
                    ModernResult.Success(null) // 加载中时返回 null
                }
            }
        } catch (e: Exception) {
            logger.e(e, TAG, "获取当前用户资料时发生异常")
            ModernResult.Error(
                com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                    operationName = "getCurrentUserProfile",
                    message = com.example.gymbro.core.ui.text.UiText.DynamicString("获取用户资料失败: ${e.message}"),
                    recoverable = true
                )
            )
        }
    }

    /**
     * 更新用户资料信息
     *
     * 直接委托给 UserDataCenterApi 进行用户资料更新。
     *
     * @param profile 要更新的用户资料
     * @return 更新结果
     */
    override suspend fun updateUserProfile(profile: UserProfile): ModernResult<Unit> {
        logger.d(TAG, "更新用户资料: userId=${profile.userId}, displayName=${profile.displayName}")

        return try {
            // 直接委托给 UserDataCenterApi
            userDataCenterApi.updateProfile(profile)
        } catch (e: Exception) {
            logger.e(e, TAG, "更新用户资料时发生异常")
            ModernResult.Error(
                com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                    operationName = "updateUserProfile",
                    message = com.example.gymbro.core.ui.text.UiText.DynamicString("更新用户资料失败: ${e.message}"),
                    recoverable = true
                )
            )
        }
    }
}
