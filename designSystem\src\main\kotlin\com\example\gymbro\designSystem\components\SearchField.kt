package com.example.gymbro.designSystem.components

import android.content.res.Configuration
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.theme.gymBroTheme

/**
 * 通用搜索字段组件
 *
 * @param value 当前搜索查询
 * @param onValueChange 查询变化回调
 * @param onSearch 搜索执行回调（点击搜索按钮或按下回车时触发）
 * @param modifier 修饰符
 * @param placeholder 占位符文本
 * @param enabled 是否启用
 * @param showClearButton 是否显示清除按钮
 * @param leadingIcon 前导图标（默认为搜索图标）
 * @param isError 是否显示错误状态
 * @param errorMessage 错误消息
 */
@Composable
fun searchField(
    value: String,
    onValueChange: (String) -> Unit,
    onSearch: (String) -> Unit,
    modifier: Modifier = Modifier,
    placeholder: UiText = UiText.DynamicString("搜索..."),
    enabled: Boolean = true,
    showClearButton: Boolean = true,
    leadingIcon: (@Composable () -> Unit)? = {
        Icon(
            Icons.Default.Search,
            contentDescription = "搜索",
            tint = androidx.compose.material3.MaterialTheme.colorScheme.onSurfaceVariant,
        )
    },
    isError: Boolean = false,
    errorMessage: UiText? = null,
) {
    GymBroInputField(
        value = value,
        onValueChange = onValueChange,
        placeholder = placeholder.asString(),
        modifier = modifier,
        enabled = enabled,
        singleLine = true,
        keyboardOptions = KeyboardOptions(
            keyboardType = KeyboardType.Text,
            imeAction = ImeAction.Search,
        ),
        keyboardActions = KeyboardActions(
            onSearch = { onSearch(value) },
        ),
        leadingIcon = leadingIcon,
        trailingIcon = if (showClearButton && value.isNotEmpty()) {
            {
                IconButton(onClick = { onValueChange("") }) {
                    Icon(
                        Icons.Default.Clear,
                        contentDescription = "清除",
                        tint = androidx.compose.material3.MaterialTheme.colorScheme.onSurfaceVariant,
                    )
                }
            }
        } else {
            null
        },
        isError = isError,
        errorMessage = errorMessage,
    )
}

@Preview(name = "Empty Search", showBackground = true)
@Composable
private fun searchFieldEmptyPreview() {
    gymBroTheme {
        var query by remember { mutableStateOf("") }
        searchField(
            value = query,
            onValueChange = { query = it },
            onSearch = { },
        )
    }
}

@Preview(name = "With Text", showBackground = true)
@Composable
private fun searchFieldWithTextPreview() {
    gymBroTheme {
        var query by remember { mutableStateOf("健身") }
        searchField(
            value = query,
            onValueChange = { query = it },
            onSearch = { },
        )
    }
}

@Preview(name = "Dark Theme", uiMode = Configuration.UI_MODE_NIGHT_YES)
@Composable
private fun searchFieldDarkPreview() {
    gymBroTheme {
        var query by remember { mutableStateOf("训练计划") }
        searchField(
            value = query,
            onValueChange = { query = it },
            onSearch = { },
        )
    }
}

@Preview(name = "Disabled", showBackground = true)
@Composable
private fun searchFieldDisabledPreview() {
    gymBroTheme {
        var query by remember { mutableStateOf("") }
        searchField(
            value = query,
            onValueChange = { query = it },
            onSearch = { },
            enabled = false,
        )
    }
}

@Preview(name = "No Clear Button", showBackground = true)
@Composable
private fun searchFieldNoClearPreview() {
    gymBroTheme {
        var query by remember { mutableStateOf("搜索内容") }
        searchField(
            value = query,
            onValueChange = { query = it },
            onSearch = { },
            showClearButton = false,
        )
    }
}

@Preview(name = "Custom Placeholder", showBackground = true)
@Composable
private fun searchFieldCustomPlaceholderPreview() {
    gymBroTheme {
        var query by remember { mutableStateOf("") }
        searchField(
            value = query,
            onValueChange = { query = it },
            onSearch = { },
            placeholder = UiText.DynamicString("搜索运动..."),
        )
    }
}
