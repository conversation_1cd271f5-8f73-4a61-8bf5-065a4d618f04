package com.example.gymbro.core.network.eventbus

import com.example.gymbro.core.di.qualifiers.ApplicationScope
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.shareIn
import kotlinx.coroutines.flow.SharingStarted
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 【架构重构阶段三】全局 Token 事件总线
 *
 * 实现完全解耦的事件驱动架构：
 * - 网络层发布 TokenEvent 到全局总线
 * - ThinkingBox 实例订阅并过滤自己的 messageId
 * - Coach 模块完全解耦，只负责业务逻辑
 *
 * 设计原则：
 * - 发布-订阅模式，完全解耦
 * - 基于 messageId 的事件过滤
 * - 高性能的 SharedFlow 实现
 * - 支持多个订阅者并发处理
 */
@Singleton
class TokenBus @Inject constructor(
    @ApplicationScope
    private val applicationScope: CoroutineScope
) {
    companion object {
        private const val TAG = "TokenBus"

        // 🔥 缓冲区配置：支持高并发的 token 事件
        private const val TOKEN_BUFFER_CAPACITY = 256
        private const val EVENT_BUFFER_CAPACITY = 64
    }

    /**
     * 全局 Token 事件流
     * 🔥 关键设计：
     * - replay = 0: 非粘性，新订阅者不会收到历史事件
     * - extraBufferCapacity = 256: 支持高并发的 token 流
     * - onBufferOverflow = SUSPEND: 背压策略，防止内存溢出
     */
    private val _tokenEvents = MutableSharedFlow<TokenEvent>(
        replay = 0,
        extraBufferCapacity = TOKEN_BUFFER_CAPACITY,
        onBufferOverflow = BufferOverflow.SUSPEND
    )

    /**
     * 只读的 Token 事件流，供外部订阅
     */
    val tokenEvents: SharedFlow<TokenEvent> = _tokenEvents.asSharedFlow()

    /**
     * 统计信息（用于监控和调试）
     */
    @Volatile
    private var totalEventsPublished = 0L

    @Volatile
    private var activeSubscribers = 0

    /**
     * 发布 Token 事件到全局总线
     *
     * @param event Token 事件
     */
    suspend fun publish(event: TokenEvent) {
        try {
            Timber.tag(TAG).d("📡 发布TokenEvent: messageId=${event.messageId}, tokenLength=${event.token.length}")

            _tokenEvents.emit(event)
            totalEventsPublished++

            Timber.tag(TAG).v("✅ TokenEvent发布成功: messageId=${event.messageId}, total=$totalEventsPublished")
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "❌ TokenEvent发布失败: messageId=${event.messageId}")
            throw e
        }
    }

    /**
     * 订阅特定 messageId 的 Token 事件
     *
     * 🔧 【ClassCastException 修复】：
     * - 使用 shareIn() 将 Flow 正确转换为 SharedFlow
     * - 避免强制类型转换导致的运行时异常
     * - 保持 SharedFlow 的热流特性和背压处理
     * - 支持多个订阅者并发访问同一个过滤流
     *
     * @param messageId 要过滤的消息ID
     * @return 过滤后的 Token 事件流
     */
    fun subscribe(messageId: String): SharedFlow<TokenEvent> {
        Timber.tag(TAG).d("🔔 新订阅者: messageId=$messageId")
        activeSubscribers++

        return tokenEvents.filter { event ->
            val matches = event.messageId == messageId
            if (matches) {
                Timber.tag(TAG).v("🎯 事件匹配: messageId=$messageId, tokenLength=${event.token.length}")
            }
            matches
        }.shareIn(
            scope = applicationScope,
            started = SharingStarted.WhileSubscribed(
                stopTimeoutMillis = 5000, // 5秒后停止上游，避免内存泄漏
                replayExpirationMillis = 0 // 不重放历史事件，保持实时性
            ),
            replay = 0 // 不缓存事件，新订阅者不会收到历史事件
        )
    }

    /**
     * 取消订阅（用于资源清理）
     *
     * @param messageId 消息ID
     */
    fun unsubscribe(messageId: String) {
        Timber.tag(TAG).d("🔕 取消订阅: messageId=$messageId")
        activeSubscribers = maxOf(0, activeSubscribers - 1)
    }

    /**
     * 获取总线状态信息（用于调试和监控）
     */
    fun getStatus(): TokenBusStatus {
        return TokenBusStatus(
            totalEventsPublished = totalEventsPublished,
            activeSubscribers = activeSubscribers,
            bufferCapacity = TOKEN_BUFFER_CAPACITY,
            currentSubscriberCount = 0 // 🔥 简化：移除 subscriptionCount 依赖
        )
    }

    /**
     * 清理总线（通常在应用关闭时调用）
     */
    fun cleanup() {
        Timber.tag(TAG).i("🧹 清理TokenBus: totalEvents=$totalEventsPublished, subscribers=$activeSubscribers")
        activeSubscribers = 0
        // SharedFlow 会自动处理资源清理
    }
}

/**
 * Token 事件数据结构
 */
data class TokenEvent(
    val messageId: String,
    val token: String,
    val isComplete: Boolean = false,
    val timestamp: Long = System.currentTimeMillis(),
    val metadata: Map<String, Any> = emptyMap()
)

/**
 * TokenBus 状态信息
 */
data class TokenBusStatus(
    val totalEventsPublished: Long,
    val activeSubscribers: Int,
    val bufferCapacity: Int,
    val currentSubscriberCount: Int
)
