package com.example.gymbro.core.ai.prompt.model

/**
 * 质量等级枚举
 *
 * 用于表示 AI 生成内容的质量等级
 *
 * @since 618重构
 */
enum class QualityGrade(
    val displayName: String,
    val description: String,
    val scoreRange: IntRange,
) {
    /**
     * 优秀 - 90-100分
     */
    EXCELLENT(
        displayName = "优秀",
        description = "质量极高，完全满足要求",
        scoreRange = 90..100,
    ),

    /**
     * 良好 - 70-89分
     */
    GOOD(
        displayName = "良好",
        description = "质量较高，基本满足要求",
        scoreRange = 70..89,
    ),

    /**
     * 一般 - 50-69分
     */
    FAIR(
        displayName = "一般",
        description = "质量中等，部分满足要求",
        scoreRange = 50..69,
    ),

    /**
     * 较差 - 30-49分
     */
    POOR(
        displayName = "较差",
        description = "质量偏低，勉强可用",
        scoreRange = 30..49,
    ),

    /**
     * 很差 - 0-29分
     */
    VERY_POOR(
        displayName = "很差",
        description = "质量很低，不建议使用",
        scoreRange = 0..29,
    ),
    ;

    /**
     * 检查分数是否在当前等级范围内
     */
    fun containsScore(score: Int): Boolean {
        return score in scoreRange
    }

    /**
     * 检查是否为可接受的质量等级
     */
    val isAcceptable: Boolean
        get() = this in listOf(EXCELLENT, GOOD, FAIR)

    /**
     * 检查是否为高质量等级
     */
    val isHighQuality: Boolean
        get() = this in listOf(EXCELLENT, GOOD)

    companion object {
        /**
         * 根据分数获取对应的质量等级
         */
        fun fromScore(score: Int): QualityGrade {
            return values().find { it.containsScore(score) } ?: VERY_POOR
        }

        /**
         * 获取所有可接受的质量等级
         */
        fun getAcceptableLevels(): List<QualityGrade> {
            return values().filter { it.isAcceptable }
        }

        /**
         * 获取所有高质量等级
         */
        fun getHighQualityLevels(): List<QualityGrade> {
            return values().filter { it.isHighQuality }
        }
    }
}
