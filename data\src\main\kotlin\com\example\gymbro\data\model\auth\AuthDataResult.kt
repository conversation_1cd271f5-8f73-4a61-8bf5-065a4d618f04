package com.example.gymbro.data.model.auth

/**
 * 认证结果数据模型 (数据层)
 *
 * 这是数据层使用的认证结果数据类，与domain层的AuthResult区分开来。
 * domain层的AuthResult是ModernResult<T>的类型别名。
 *
 * @property userId 用户唯一标识
 * @property email 用户邮箱
 * @property displayName 用户显示名称
 * @property phoneNumber 用户电话号码
 * @property isEmailVerified 邮箱是否已验证
 * @property isAnonymous 是否匿名用户
 * @property photoUrl 用户头像URL
 */
data class AuthDataResult(
    val userId: String,
    val email: String? = null,
    val displayName: String? = null,
    val phoneNumber: String? = null,
    val isEmailVerified: Boolean = false,
    val isAnonymous: Boolean = false,
    val photoUrl: String? = null,
)
