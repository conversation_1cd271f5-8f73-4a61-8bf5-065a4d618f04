plugins {
    id("gymbro.android.library")
    id("gymbro.compose.library")
    id("gymbro.hilt.library")
    id("gymbro.testing.library")
    id("org.jetbrains.kotlin.plugin.compose")
    kotlin("plugin.serialization")
}

android {
    namespace = "com.example.gymbro.core.ml"
    compileSdk =
        libs.versions.compileSdk
            .get()
            .toInt()

    defaultConfig {
        minSdk =
            libs.versions.minSdk
                .get()
                .toInt()

        // 显式禁用资源编译，使用空资源 (This comment is from the original, explaining the setup)
        sourceSets {
            getByName("main") {
                assets.srcDirs("src/main/assets")
            }
        }
    }

    // 完全禁用资源目录
    sourceSets {
        getByName("main") {
            res.srcDirs(emptyList<String>())
            // Keep this if .kt files are in src/main/kotlin and also considered 'resources' for some reason
            // resources.srcDirs("src/main/kotlin")
        }

        getByName("test") {
            res.srcDirs(emptyList<String>())
        }

        getByName("androidTest") {
            res.srcDirs(emptyList<String>())
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = libs.versions.jvmTarget.get()
        // freeCompilerArgs += listOf("-Explicit-api=strict") // Example, if strict API mode is desired
    }

    buildFeatures {
        compose = true // Enabled Compose
        buildConfig = true
        androidResources = false // Explicitly disabled; core module provides no Android resources, even with Compose enabled.
    }

    // 避免生成空的资源
    lint {
        abortOnError = false
    }
}

dependencies {
    // Kotlin 标准库和反射
    implementation(libs.kotlin.reflect) // 添加反射支持，修复GlobalErrorType.kt:53的反射API警告

    // Kotlin 协程 - 使用 libs.versions.toml 中的版本
    implementation(libs.kotlinx.coroutines.core)
    implementation(libs.kotlinx.coroutines.android)

    // Kotlin DateTime - 使用 libs.versions.toml 中的版本
    implementation(libs.kotlinx.datetime)

    // ViewModel 和 Lifecycle - 使用 libs.versions.toml 中的版本
    implementation(libs.androidx.lifecycle.viewmodel.ktx)

    // Android App Startup - 修复NetworkInitializer.kt编译错误
    implementation(libs.androidx.startup.runtime)

    // Compose Dependencies
    implementation(platform(libs.compose.bom)) // Import Compose BOM
    implementation(libs.compose.runtime) // Essential for @Composable and CompositionLocal
    implementation(libs.compose.ui) // Needed if UiText or related utilities use any UI primitives

    // 网络 (Retrofit core) - 使用 libs.versions.toml 中的版本
    implementation(libs.retrofit)

    // 日志 - 使用 libs.versions.toml 中的版本
    implementation(libs.timber)

        // Core模块依赖
    implementation(project(":core"))

                                        // 🚀 TASK-005: LiteRT硬件加速依赖 - 官方推荐迁移方案
    val litertVersion = "1.3.0"          // LiteRT官方稳定版

    // 【LiteRT方案】完全替代TensorFlow Lite，解决namespace冲突
    // LiteRT核心运行时 - 包含CPU/GPU/NNAPI/Hexagon所有delegate
    implementation("com.google.ai.edge.litert:litert:$litertVersion")

    // LiteRT支持库 - TensorBuffer、ImageProcessor等工具类，排除传递依赖避免namespace冲突
    implementation("com.google.ai.edge.litert:litert-support:$litertVersion") {
        exclude(group = "com.google.ai.edge.litert", module = "litert-support-api")
    }

    // ✅ 已移除所有org.tensorflow.*依赖，彻底解决manifest冲突

    // ONNX Runtime保持不变
    implementation(libs.onnxruntime.android) // 1.17.1

    // Kotlin序列化
    implementation(libs.kotlinx.serialization.json)

    // JTokkit - OpenAI tokenizer for accurate token counting
    implementation("com.knuddels:jtokkit:1.1.0")

    // 测试依赖
    testImplementation(libs.junit4)
    testImplementation(libs.kotlinx.coroutines.test)
    testImplementation(libs.mockk)
    testImplementation(libs.truth)
    testImplementation(libs.mockito.kotlin)

    androidTestImplementation(libs.androidx.test.ext)
    androidTestImplementation(libs.androidx.test.espresso.core)
}
