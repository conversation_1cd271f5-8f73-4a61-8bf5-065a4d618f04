package com.example.gymbro.core.network.rest

/**
 * REST客户端接口 - V2版本
 *
 * 提供基础的HTTP请求功能，支持ApiResult统一结果封装
 * 配置通过构造函数注入，消除对DI框架的依赖
 */
interface RestClient {

    /**
     * GET请求
     *
     * @param url 请求URL
     * @param headers 请求头部
     * @return ApiResult封装的响应内容
     */
    suspend fun get(url: String, headers: Map<String, String> = emptyMap()): ApiResult<String>

    /**
     * POST请求
     *
     * @param url 请求URL
     * @param body 请求体
     * @param headers 请求头部
     * @return ApiResult封装的响应内容
     */
    suspend fun post(url: String, body: String, headers: Map<String, String> = emptyMap()): ApiResult<String>

    /**
     * PUT请求
     *
     * @param url 请求URL
     * @param body 请求体
     * @param headers 请求头部
     * @return ApiResult封装的响应内容
     */
    suspend fun put(url: String, body: String, headers: Map<String, String> = emptyMap()): ApiResult<String>

    /**
     * DELETE请求
     *
     * @param url 请求URL
     * @param headers 请求头部
     * @return ApiResult封装的响应内容
     */
    suspend fun delete(url: String, headers: Map<String, String> = emptyMap()): ApiResult<String>

    // 🗑️ 已清理：所有Legacy方法已删除，统一使用ApiResult返回类型
}
