package com.example.gymbro.data.local.dao.auth

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.example.gymbro.data.local.entity.auth.TokenEntity
import kotlinx.coroutines.flow.Flow

/**
 * Token数据访问对象接口
 * 用于对Token实体的数据库操作
 */
@Dao
interface TokenDao {

    /**
     * 插入或更新Token
     * @param token Token实体
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertToken(token: TokenEntity)

    /**
     * 获取Token
     * @return Token实体Flow
     */
    @Query("SELECT * FROM tokens LIMIT 1")
    fun getToken(): Flow<TokenEntity?>

    /**
     * 删除所有Token
     */
    @Query("DELETE FROM tokens")
    suspend fun deleteToken()
}
