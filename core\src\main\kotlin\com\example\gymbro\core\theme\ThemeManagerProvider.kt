package com.example.gymbro.core.theme

import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 主题管理器提供者接口
 *
 * 定义主题管理器的核心功能，该接口完全平台无关。
 * 实际实现可能会使用平台特定的机制，但核心逻辑保持一致。
 */
interface ThemeManagerProvider {
    /**
     * 当前主题模式
     */
    val currentThemeMode: Flow<ThemeMode>

    /**
     * 当前主题是否为暗色
     */
    val isDarkTheme: Flow<Boolean>

    /**
     * 设置主题模式
     *
     * @param themeMode 要设置的主题模式
     */
    suspend fun setThemeMode(themeMode: ThemeMode)

    /**
     * 获取当前应用的主题模式
     *
     * @return 当前主题模式
     */
    fun getCurrentThemeMode(): ThemeMode

    /**
     * 获取当前应用的实际暗色模式状态
     * 考虑了系统设置和用户偏好
     *
     * @param systemInDarkMode 系统是否处于暗色模式
     * @return 应用是否应该使用暗色主题
     */
    fun resolveActualThemeMode(systemInDarkMode: Boolean): Boolean
}

/**
 * 默认主题管理器提供者实现
 *
 * 提供一个基本的平台无关实现，可以被特定平台的实现扩展
 */
@Singleton
class DefaultThemeManagerProvider @Inject constructor(
    private val themeManager: ThemeManager,
) : ThemeManagerProvider {

    override val currentThemeMode: Flow<ThemeMode> = themeManager.currentTheme

    override val isDarkTheme: Flow<Boolean> = themeManager.isDarkTheme

    override suspend fun setThemeMode(themeMode: ThemeMode) {
        themeManager.setTheme(themeMode)
    }

    override fun getCurrentThemeMode(): ThemeMode {
        return themeManager.getCurrentTheme()
    }

    override fun resolveActualThemeMode(systemInDarkMode: Boolean): Boolean {
        return when (getCurrentThemeMode()) {
            ThemeMode.LIGHT -> false
            ThemeMode.DARK -> true
            ThemeMode.SYSTEM -> systemInDarkMode
        }
    }
}
