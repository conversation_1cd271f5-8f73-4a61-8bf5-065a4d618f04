package com.example.gymbro.data.remote.subscription.dto

import com.google.gson.annotations.SerializedName

/**
 * 订阅响应DTO
 * 定义API操作的标准响应数据结构
 */
data class SubscriptionResponseDto(
    /**
     * 操作是否成功
     */
    @SerializedName("success")
    val success: Boolean,
    /**
     * 操作消息
     */
    @SerializedName("message")
    val message: String? = null,
    /**
     * 订阅ID (如适用)
     */
    @SerializedName("subscription_id")
    val subscriptionId: String? = null,
    /**
     * 订阅状态 (如适用)
     */
    @SerializedName("status")
    val status: SubscriptionStatusDto? = null,
    /**
     * 操作时间戳
     */
    @SerializedName("timestamp")
    val timestamp: Long = System.currentTimeMillis(),
)
