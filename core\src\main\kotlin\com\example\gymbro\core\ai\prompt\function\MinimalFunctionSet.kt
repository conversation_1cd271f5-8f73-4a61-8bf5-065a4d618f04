package com.example.gymbro.core.ai.prompt.function

import javax.inject.Inject
import javax.inject.Singleton

/**
 * Function 级别枚举
 */
enum class FunctionLevel {
    /**
     * 核心功能 - 覆盖80%需求
     */
    CORE,

    /**
     * 扩展功能 - 覆盖额外15%需求
     */
    EXTENDED,

    /**
     * 高级功能 - 覆盖剩余5%需求
     */
    ADVANCED,
}

/**
 * 最小化 Function 集合
 *
 * 提供精简的 Function 集合，用于：
 * 1. Token 预算紧张时的降级策略
 * 2. 核心功能优先级管理
 * 3. 性能优化场景
 *
 * @since 618重构
 */
@Singleton
class MinimalFunctionSet @Inject constructor() {

    /**
     * 核心 Function 列表 - 覆盖80%需求
     * 直接使用 GymBroFunctions 中定义的核心函数
     */
    private val coreFunctions = GymBroFunctions.CORE_FUNCTIONS

    /**
     * 扩展 Function 列表 - 使用 GymBroFunctions 中的计划域函数
     */
    private val extendedFunctions = GymBroFunctions.PLAN_FUNCTIONS

    /**
     * 高级 Function 列表 - 暂时为空，未来可扩展
     */
    private val advancedFunctions = emptyList<FunctionDescriptor>()

    /**
     * 根据级别获取 Function 列表
     *
     * @param level Function 级别
     * @return Function 列表
     */
    fun getFunctionsByLevel(level: FunctionLevel): List<FunctionDescriptor> {
        return when (level) {
            FunctionLevel.CORE -> coreFunctions
            FunctionLevel.EXTENDED -> coreFunctions + extendedFunctions
            FunctionLevel.ADVANCED -> coreFunctions + extendedFunctions + advancedFunctions
        }
    }

    /**
     * 获取所有 Function
     */
    fun getAllFunctions(): List<FunctionDescriptor> {
        return getFunctionsByLevel(FunctionLevel.ADVANCED)
    }

    /**
     * 根据 Token 预算获取适合的 Function 集合
     *
     * @param tokenBudget Token 预算
     * @return 适合的 Function 列表
     */
    fun getFunctionsByTokenBudget(tokenBudget: Int): List<FunctionDescriptor> {
        return when {
            tokenBudget < 1000 -> getFunctionsByLevel(FunctionLevel.CORE).take(3) // 最小集合
            tokenBudget < 2000 -> getFunctionsByLevel(FunctionLevel.CORE)
            tokenBudget < 3000 -> getFunctionsByLevel(FunctionLevel.EXTENDED)
            else -> getAllFunctions()
        }
    }
}
