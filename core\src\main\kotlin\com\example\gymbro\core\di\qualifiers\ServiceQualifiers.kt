package com.example.gymbro.core.di.qualifiers

import javax.inject.Qualifier

/**
 * 服务相关的Qualifier注解
 * 用于在依赖注入中区分不同的服务管理器实现
 *
 * 统一管理所有服务相关的限定符，确保全局唯一性
 */

/**
 * 标识平台无关实现的限定符
 * 适用于NoOp实现和测试场景，不依赖特定平台API
 * 主要用于core模块提供的默认实现
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class PlatformIndependent

/**
 * 标识平台特定实现的限定符
 * 适用于依赖特定平台API的实现
 * 主要用于app模块或平台特定模块提供的实现
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class PlatformSpecific
