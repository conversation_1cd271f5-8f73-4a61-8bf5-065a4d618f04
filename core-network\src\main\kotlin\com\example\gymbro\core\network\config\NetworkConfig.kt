package com.example.gymbro.core.network.config

/**
 * 统一网络配置 - 纯数据类
 * 遵循Clean Architecture，零依赖，只定义网络层需要的配置
 *
 * 🎯 核心原则：
 * 1. 纯数据类，不依赖任何其他模块
 * 2. 只关心网络层配置，不关心业务逻辑
 * 3. 配置来源由上层App通过NetworkConfigProvider注入
 */
data class NetworkConfig(
    // 🔧 核心网络配置
    val wsBase: String,
    val restBase: String,
    val apiKey: String,

    // 🔧 WebSocket配置
    val connectTimeoutSec: Int = 30,
    val pingSec: Long = 15,
    val pongTimeoutSec: Int = 5,
    val maxReconnect: Int = 5,
    val backoffStartMs: Long = 1000,
    val backoffCapMs: Long = 30000,
    val enableOffsetResume: Boolean = true,

    // 🔧 REST配置
    val readTimeoutSec: Int = 30,
    val writeTimeoutSec: Int = 30,
    val enableRetry: Boolean = true,
    val maxRetries: Int = 2,
    val retryDelayMs: Long = 2000,

    // 🔧 调试配置
    val enableDebugLogging: Boolean = false,
) {

    /**
     * 验证配置有效性
     */
    fun validate(): Boolean {
        // 🔥 增强验证 - 检查默认无效值
        val isValidApiKey = apiKey.isNotBlank() &&
            !apiKey.contains("default-missing-key") &&
            !apiKey.contains("sk-default")

        return wsBase.isNotBlank() &&
            restBase.isNotBlank() &&
            isValidApiKey &&
            connectTimeoutSec > 0 &&
            pingSec > 0 &&
            pongTimeoutSec > 0 &&
            maxReconnect > 0 &&
            backoffStartMs > 0 &&
            backoffCapMs >= backoffStartMs
    }

    /**
     * 获取WebSocket URL（确保使用wss://协议）
     */
    fun getSecureWsUrl(): String {
        return when {
            wsBase.startsWith("wss://") -> wsBase
            wsBase.startsWith("ws://") -> wsBase.replace("ws://", "wss://")
            wsBase.startsWith("https://") -> wsBase.replace("https://", "wss://") + "/v1/chat/completions"
            wsBase.startsWith("http://") -> wsBase.replace("http://", "wss://") + "/v1/chat/completions"
            else -> "wss://$wsBase/v1/chat/completions"
        }
    }

    /**
     * 获取REST基础URL（确保使用https://协议并以/结尾）
     */
    fun getSecureRestUrl(): String {
        val secureUrl = when {
            restBase.startsWith("https://") -> restBase
            restBase.startsWith("http://") -> restBase.replace("http://", "https://")
            else -> "https://$restBase"
        }
        return if (secureUrl.endsWith("/")) secureUrl else "$secureUrl/"
    }

    /**
     * 🔥 获取配置诊断信息
     */
    fun getDiagnosticInfo(): String {
        return buildString {
            appendLine("网络配置诊断:")
            appendLine("  - WebSocket URL: ${getSecureWsUrl()}")
            appendLine("  - REST URL: ${getSecureRestUrl()}")
            appendLine("  - API Key: ${if (apiKey.isNotBlank()) "已配置 (${apiKey.take(10)}...)" else "未配置"}")
            appendLine("  - 连接超时: ${connectTimeoutSec}s")
            appendLine("  - 配置有效性: ${if (validate()) "✅ 有效" else "❌ 无效"}")
        }
    }

    companion object {
        /**
         * 创建默认配置（仅用于测试或fallback）
         */
        fun createDefault(): NetworkConfig = NetworkConfig(
            wsBase = "wss://api.openai.com",
            restBase = "https://api.openai.com",
            apiKey = "sk-default-missing-key",
            enableDebugLogging = false,
        )
    }
}

/**
 * 网络配置提供者接口
 * 由App层实现，注入到core-network层
 */
interface NetworkConfigProvider {
    fun getConfig(): NetworkConfig

    /**
     * 支持动态配置变更（可选实现）
     */
    fun observeConfig(): kotlinx.coroutines.flow.Flow<NetworkConfig> {
        return kotlinx.coroutines.flow.flowOf(getConfig())
    }
}

/**
 * 扩展函数：确保URL以斜杠结尾
 */
fun String.ensureSlash(): String = if (endsWith("/")) this else "$this/"

// 🗑️ WsConfig已清理 - 统一使用NetworkConfig
