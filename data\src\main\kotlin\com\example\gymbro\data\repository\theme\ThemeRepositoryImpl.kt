package com.example.gymbro.data.repository.theme

import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.theme.ThemePersistence
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.local.datastore.UserPreferencesRepository
import com.example.gymbro.domain.profile.model.settings.ThemeMode
import com.example.gymbro.domain.profile.model.settings.ThemeSettings
import com.example.gymbro.domain.shared.theme.ThemeRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 主题仓库实现
 *
 * 负责管理应用主题设置，包括：
 * - 获取/保存主题设置
 * - 主题模式切换 (浅色/深色/跟随系统)
 * - 与UserPreferencesRepository集成
 */
@Singleton
class ThemeRepositoryImpl
@Inject
constructor(
    private val userPreferencesRepository: UserPreferencesRepository,
    private val themePersistence: ThemePersistence,
) : ThemeRepository {
    /**
     * 获取当前主题设置
     * 使用新的ThemePersistence接口获取主题配置
     * @return 包含ThemeSettings的Flow
     */
    override fun getThemeSettings(): Flow<ModernResult<ThemeSettings>> =
        combine(
            themePersistence.darkThemeFlow(),
            themePersistence.dynamicColorFlow(),
        ) { isDarkTheme, useDynamicColor ->
            try {
                val themeMode = if (isDarkTheme) ThemeMode.DARK else ThemeMode.LIGHT
                val themeSettings =
                    ThemeSettings(
                        themeMode = themeMode,
                        useDynamicColor = useDynamicColor,
                        useAmoledBlack = false, // 默认不使用AMOLED黑色
                        primaryColorHex = null,
                        secondaryColorHex = null,
                        accentColorHex = null,
                        fontScale = 1.0f,
                        useRoundedCorners = true,
                        animationsEnabled = true,
                        customFontFamily = null,
                    )
                Timber.d("获取主题设置成功: $themeSettings")
                ModernResult.Success(themeSettings)
            } catch (e: Exception) {
                Timber.e(e, "获取主题设置时发生异常")
                ModernResult.Error(
                    ModernDataError(
                        operationName = "getThemeSettings",
                        errorType = GlobalErrorType.Unknown,
                        uiMessage = UiText.DynamicString("获取主题设置失败"),
                        cause = e,
                    ),
                )
            }
        }

    /**
     * 保存主题设置
     * 使用新的ThemePersistence接口保存主题配置
     * @param settings 要保存的ThemeSettings
     * @return 操作结果
     */
    override suspend fun saveThemeSettings(settings: ThemeSettings): ModernResult<Unit> {
        return try {
            Timber.d("保存主题设置: $settings")

            // 将ThemeMode转换为boolean值
            val isDarkTheme =
                when (settings.themeMode) {
                    ThemeMode.DARK -> true
                    ThemeMode.LIGHT -> false
                    ThemeMode.SYSTEM -> {
                        // 对于系统模式，暂时使用false作为默认值
                        false
                    }
                }

            // 使用ThemePersistence保存设置
            val darkThemeSuccess = themePersistence.saveDarkTheme(isDarkTheme)
            if (!darkThemeSuccess) {
                Timber.e("保存深色主题设置失败")
                return ModernResult.Error(
                    ModernDataError(
                        operationName = "saveThemeSettings",
                        errorType = GlobalErrorType.Unknown,
                        uiMessage = UiText.DynamicString("保存深色主题设置失败"),
                        cause = null,
                    ),
                )
            }

            val dynamicColorSuccess = themePersistence.saveDynamicColor(settings.useDynamicColor)
            if (!dynamicColorSuccess) {
                Timber.e("保存动态颜色设置失败")
                return ModernResult.Error(
                    ModernDataError(
                        operationName = "saveThemeSettings",
                        errorType = GlobalErrorType.Unknown,
                        uiMessage = UiText.DynamicString("保存动态颜色设置失败"),
                        cause = null,
                    ),
                )
            }

            Timber.i("主题设置保存成功")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "保存主题设置时发生异常")
            ModernResult.Error(
                ModernDataError(
                    operationName = "saveThemeSettings",
                    errorType = GlobalErrorType.Unknown,
                    uiMessage = UiText.DynamicString("保存主题设置失败"),
                    cause = e,
                ),
            )
        }
    }
}
