{"formatVersion": 1, "database": {"version": 1, "identityHash": "823c210fd08e625aae8973146c71c2b6", "entities": [{"tableName": "workout_sessions", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `userId` TEXT NOT NULL, `templateId` TEXT NOT NULL, `templateVersion` INTEGER, `planId` TEXT, `name` TEXT NOT NULL, `status` TEXT NOT NULL, `startTime` INTEGER NOT NULL, `endTime` INTEGER, `totalDuration` INTEGER, `totalVolume` REAL, `caloriesBurned` INTEGER, `notes` TEXT, `rating` INTEGER, `lastAutosaveTime` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "templateId", "columnName": "templateId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "templateVersion", "columnName": "templateVersion", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "planId", "columnName": "planId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": true}, {"fieldPath": "startTime", "columnName": "startTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "endTime", "columnName": "endTime", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "totalDuration", "columnName": "totalDuration", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "totalVolume", "columnName": "totalVolume", "affinity": "REAL", "notNull": false}, {"fieldPath": "caloriesBurned", "columnName": "caloriesBurned", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "notes", "columnName": "notes", "affinity": "TEXT", "notNull": false}, {"fieldPath": "rating", "columnName": "rating", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "lastAutosaveTime", "columnName": "lastAutosaveTime", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_workout_sessions_userId", "unique": false, "columnNames": ["userId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_sessions_userId` ON `${TABLE_NAME}` (`userId`)"}, {"name": "index_workout_sessions_templateId", "unique": false, "columnNames": ["templateId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_sessions_templateId` ON `${TABLE_NAME}` (`templateId`)"}, {"name": "index_workout_sessions_planId", "unique": false, "columnNames": ["planId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_sessions_planId` ON `${TABLE_NAME}` (`planId`)"}, {"name": "index_workout_sessions_status", "unique": false, "columnNames": ["status"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_sessions_status` ON `${TABLE_NAME}` (`status`)"}, {"name": "index_workout_sessions_startTime", "unique": false, "columnNames": ["startTime"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_sessions_startTime` ON `${TABLE_NAME}` (`startTime`)"}, {"name": "index_workout_sessions_endTime", "unique": false, "columnNames": ["endTime"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_sessions_endTime` ON `${TABLE_NAME}` (`endTime`)"}, {"name": "index_workout_sessions_lastAutosaveTime", "unique": false, "columnNames": ["lastAutosaveTime"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_sessions_lastAutosaveTime` ON `${TABLE_NAME}` (`lastAutosaveTime`)"}], "foreignKeys": []}, {"tableName": "session_exercises", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `sessionId` TEXT NOT NULL, `exerciseId` TEXT NOT NULL, `order` INTEGER NOT NULL, `name` TEXT NOT NULL, `targetSets` INTEGER NOT NULL, `completedSets` INTEGER NOT NULL, `restSeconds` INTEGER, `restSecondsOverride` INTEGER, `imageUrl` TEXT, `videoUrl` TEXT, `status` TEXT NOT NULL, `startTime` INTEGER, `endTime` INTEGER, `notes` TEXT, `isCompleted` INTEGER NOT NULL, PRIMARY KEY(`id`), FOREIGN KEY(`sessionId`) REFERENCES `workout_sessions`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sessionId", "columnName": "sessionId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "exerciseId", "columnName": "exerciseId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "order", "columnName": "order", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "targetSets", "columnName": "targetSets", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "completedSets", "columnName": "completedSets", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "restSeconds", "columnName": "restSeconds", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "restSecondsOverride", "columnName": "restSecondsOverride", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "imageUrl", "columnName": "imageUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "videoUrl", "columnName": "videoUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": true}, {"fieldPath": "startTime", "columnName": "startTime", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "endTime", "columnName": "endTime", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "notes", "columnName": "notes", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isCompleted", "columnName": "isCompleted", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_session_exercises_sessionId", "unique": false, "columnNames": ["sessionId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_session_exercises_sessionId` ON `${TABLE_NAME}` (`sessionId`)"}, {"name": "index_session_exercises_exerciseId", "unique": false, "columnNames": ["exerciseId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_session_exercises_exerciseId` ON `${TABLE_NAME}` (`exerciseId`)"}, {"name": "index_session_exercises_order", "unique": false, "columnNames": ["order"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_session_exercises_order` ON `${TABLE_NAME}` (`order`)"}, {"name": "index_session_exercises_status", "unique": false, "columnNames": ["status"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_session_exercises_status` ON `${TABLE_NAME}` (`status`)"}], "foreignKeys": [{"table": "workout_sessions", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["sessionId"], "referencedColumns": ["id"]}]}, {"tableName": "session_sets", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `sessionExerciseId` TEXT NOT NULL, `setNumber` INTEGER NOT NULL, `weight` REAL, `weightUnit` TEXT, `reps` INTEGER, `timeSeconds` INTEGER, `rpe` REAL, `isCompleted` INTEGER NOT NULL, `isWarmupSet` INTEGER NOT NULL, `notes` TEXT, `timestamp` INTEGER NOT NULL, PRIMARY KEY(`id`), FOREIGN KEY(`sessionExerciseId`) REFERENCES `session_exercises`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sessionExerciseId", "columnName": "sessionExerciseId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "setNumber", "columnName": "setNumber", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "weight", "columnName": "weight", "affinity": "REAL", "notNull": false}, {"fieldPath": "weightUnit", "columnName": "weightUnit", "affinity": "TEXT", "notNull": false}, {"fieldPath": "reps", "columnName": "reps", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "timeSeconds", "columnName": "timeSeconds", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "rpe", "columnName": "rpe", "affinity": "REAL", "notNull": false}, {"fieldPath": "isCompleted", "columnName": "isCompleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isWarmupSet", "columnName": "isWarmupSet", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "notes", "columnName": "notes", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_session_sets_sessionExerciseId", "unique": false, "columnNames": ["sessionExerciseId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_session_sets_sessionExerciseId` ON `${TABLE_NAME}` (`sessionExerciseId`)"}, {"name": "index_session_sets_setNumber", "unique": false, "columnNames": ["setNumber"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_session_sets_setNumber` ON `${TABLE_NAME}` (`setNumber`)"}, {"name": "index_session_sets_isCompleted", "unique": false, "columnNames": ["isCompleted"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_session_sets_isCompleted` ON `${TABLE_NAME}` (`isCompleted`)"}, {"name": "index_session_sets_timestamp", "unique": false, "columnNames": ["timestamp"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_session_sets_timestamp` ON `${TABLE_NAME}` (`timestamp`)"}], "foreignKeys": [{"table": "session_exercises", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["sessionExerciseId"], "referencedColumns": ["id"]}]}, {"tableName": "exercise_history_stats", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `userId` TEXT NOT NULL, `exerciseId` TEXT NOT NULL, `personalBestWeight` REAL, `personalBestReps` INTEGER, `totalSetsCompleted` INTEGER NOT NULL, `totalVolumeLifted` REAL NOT NULL, `lastPerformanceDate` INTEGER NOT NULL, `lastUpdated` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "exerciseId", "columnName": "exerciseId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "personalBestWeight", "columnName": "personalBestWeight", "affinity": "REAL", "notNull": false}, {"fieldPath": "personalBestReps", "columnName": "personalBestReps", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "totalSetsCompleted", "columnName": "totalSetsCompleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalVolumeLifted", "columnName": "totalVolumeLifted", "affinity": "REAL", "notNull": true}, {"fieldPath": "lastPerformanceDate", "columnName": "lastPerformanceDate", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastUpdated", "columnName": "lastUpdated", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_exercise_history_stats_userId", "unique": false, "columnNames": ["userId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_exercise_history_stats_userId` ON `${TABLE_NAME}` (`userId`)"}, {"name": "index_exercise_history_stats_exerciseId", "unique": false, "columnNames": ["exerciseId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_exercise_history_stats_exerciseId` ON `${TABLE_NAME}` (`exerciseId`)"}, {"name": "index_exercise_history_stats_lastPerformanceDate", "unique": false, "columnNames": ["lastPerformanceDate"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_exercise_history_stats_lastPerformanceDate` ON `${TABLE_NAME}` (`lastPerformanceDate`)"}, {"name": "index_exercise_history_stats_lastUpdated", "unique": false, "columnNames": ["lastUpdated"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_exercise_history_stats_lastUpdated` ON `${TABLE_NAME}` (`lastUpdated`)"}], "foreignKeys": []}, {"tableName": "session_autosave", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `sessionId` TEXT NOT NULL, `saveType` TEXT NOT NULL, `saveTime` INTEGER NOT NULL, `sessionSnapshot` TEXT NOT NULL, `progressSnapshot` TEXT NOT NULL, `currentState` TEXT NOT NULL, `nextAction` TEXT NOT NULL, `isValid` INTEGER NOT NULL, `expiresAt` INTEGER, `createdAt` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sessionId", "columnName": "sessionId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "saveType", "columnName": "saveType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "saveTime", "columnName": "saveTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sessionSnapshot", "columnName": "sessionSnapshot", "affinity": "TEXT", "notNull": true}, {"fieldPath": "progressSnapshot", "columnName": "progressSnapshot", "affinity": "TEXT", "notNull": true}, {"fieldPath": "currentState", "columnName": "currentState", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nextAction", "columnName": "nextAction", "affinity": "TEXT", "notNull": true}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON>", "columnName": "<PERSON><PERSON><PERSON><PERSON>", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "expiresAt", "columnName": "expiresAt", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_session_autosave_sessionId", "unique": false, "columnNames": ["sessionId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_session_autosave_sessionId` ON `${TABLE_NAME}` (`sessionId`)"}, {"name": "index_session_autosave_saveTime", "unique": false, "columnNames": ["saveTime"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_session_autosave_saveTime` ON `${TABLE_NAME}` (`saveTime`)"}, {"name": "index_session_autosave_saveType", "unique": false, "columnNames": ["saveType"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_session_autosave_saveType` ON `${TABLE_NAME}` (`saveType`)"}, {"name": "index_session_autosave_isValid", "unique": false, "columnNames": ["<PERSON><PERSON><PERSON><PERSON>"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_session_autosave_isValid` ON `${TABLE_NAME}` (`isValid`)"}], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '823c210fd08e625aae8973146c71c2b6')"]}}