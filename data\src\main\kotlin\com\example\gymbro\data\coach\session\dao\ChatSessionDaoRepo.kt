package com.example.gymbro.data.coach.session.dao

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.coach.session.mapper.ChatSessionMapper.toDomain
import com.example.gymbro.data.coach.session.mapper.ChatSessionMapper.toDomainMessage
import com.example.gymbro.data.coach.dao.ChatRawDao
import com.example.gymbro.data.coach.dao.ChatSessionDao
import com.example.gymbro.data.local.entity.ChatSessionEntity
import com.example.gymbro.domain.coach.model.ChatSession
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * ChatSessionDaoRepo - 纯 Room CRUD 操作
 *
 * 基于 promtjson.md 文档要求，专注于数据库操作
 * 不包含备份逻辑、autosave 集成等复杂业务逻辑
 *
 * 职责：
 * - 纯粹的 Room 数据库 CRUD 操作
 * - 事务只包含 DB 操作
 * - Entity ↔ Domain 转换
 */
@Singleton
class ChatSessionDaoRepo @Inject constructor(
    private val chatSessionDao: ChatSessionDao,
    private val chatRawDao: ChatRawDao,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
) {

    /**
     * 创建会话 - 纯数据库操作
     */
    suspend fun createSession(
        userId: String,
        title: String?,
    ): ModernResult<ChatSession> = withContext(ioDispatcher) {
        try {
            val session = ChatSession(
                title = title ?: "新对话",
                userId = userId,
            )
            val entity = ChatSessionEntity.fromDomain(session)
            chatSessionDao.insertSession(entity)
            Timber.d("会话创建成功: sessionId=${session.id}")
            ModernResult.Success(session)
        } catch (e: Exception) {
            Timber.e(e, "创建会话失败")
            ModernResult.Error(
                DataErrors.DataError.access(
                    operationName = "ChatSessionDaoRepo.createSession",
                    message = UiText.DynamicString("创建会话失败"),
                    entityType = "ChatSession",
                    cause = e,
                ),
            )
        }
    }

    /**
     * 获取会话 - 纯数据库查询
     */
    suspend fun getSession(sessionId: String): ModernResult<ChatSession?> = withContext(ioDispatcher) {
        try {
            val sessionEntity = chatSessionDao.getSessionById(sessionId)
                ?: return@withContext ModernResult.Success(null)

            val messages = chatRawDao.getChatMessagesBySession(sessionId).map { it.toDomainMessage() }
            val session = sessionEntity.toDomain(messages)
            Timber.d("会话加载成功: sessionId=$sessionId, messageCount=${messages.size}")
            ModernResult.Success(session)
        } catch (e: Exception) {
            Timber.e(e, "获取会话失败")
            ModernResult.Error(
                DataErrors.DataError.access(
                    operationName = "ChatSessionDaoRepo.getSession",
                    message = UiText.DynamicString("获取会话失败"),
                    entityType = "ChatSession",
                    cause = e,
                ),
            )
        }
    }

    /**
     * 获取用户会话列表 - 纯数据库查询
     */
    fun getUserSessions(
        userId: String,
        limit: Int,
        offset: Int,
    ): Flow<ModernResult<List<ChatSession>>> = flow {
        emit(ModernResult.Loading)

        chatSessionDao
            .getUserSessions(userId, false, limit, offset)
            .map { entities ->
                entities.map { entity ->
                    // 不包含消息列表以提高性能
                    entity.toDomain(emptyList())
                }
            }.catch { e ->
                Timber.e(e, "获取用户会话失败")
                emit(
                    ModernResult.Error(
                        DataErrors.DataError.access(
                            operationName = "ChatSessionDaoRepo.getUserSessions",
                            message = UiText.DynamicString("获取用户会话失败"),
                            entityType = "ChatSession",
                            cause = e,
                        ),
                    ),
                )
            }.collect { sessions ->
                emit(ModernResult.Success(sessions))
            }
    }

    /**
     * 观察最新会话 - 纯数据库观察
     */
    fun observeLatestSession(userId: String): Flow<ChatSession?> =
        chatSessionDao
            .observeLatestSession(userId)
            .map { sessionEntity ->
                sessionEntity?.let { entity ->
                    // 获取该会话的消息
                    val messages = chatRawDao
                        .getChatMessagesBySession(entity.id)
                        .map { it.toDomainMessage() }
                    entity.toDomain(messages)
                }
            }.catch { e ->
                Timber.e(e, "观察最新会话失败")
                // 发生错误时返回null，而不是抛出异常，保证Flow的连续性
                emit(null)
            }.flowOn(ioDispatcher)

    /**
     * 更新会话标题 - 纯数据库操作
     */
    suspend fun updateSession(
        sessionId: String,
        title: String,
    ): ModernResult<Unit> = withContext(ioDispatcher) {
        try {
            chatSessionDao.updateSessionTitle(sessionId, title)
            Timber.d("会话更新成功: sessionId=$sessionId")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "更新会话失败")
            ModernResult.Error(
                DataErrors.DataError.access(
                    operationName = "ChatSessionDaoRepo.updateSession",
                    message = UiText.DynamicString("更新会话失败"),
                    entityType = "ChatSession",
                    cause = e,
                ),
            )
        }
    }

    /**
     * 更新会话摘要 - 纯数据库操作
     */
    suspend fun updateSessionSummary(
        sessionId: String,
        summary: String,
    ): ModernResult<Unit> = withContext(ioDispatcher) {
        try {
            chatSessionDao.updateSessionSummary(sessionId, summary)
            Timber.d("会话概要更新成功: sessionId=$sessionId")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "更新会话概要失败")
            ModernResult.Error(
                DataErrors.DataError.access(
                    operationName = "ChatSessionDaoRepo.updateSessionSummary",
                    message = UiText.DynamicString("更新会话概要失败"),
                    entityType = "ChatSession",
                    cause = e,
                ),
            )
        }
    }

    /**
     * 删除会话 - 纯数据库操作
     */
    suspend fun deleteSession(sessionId: String): ModernResult<Unit> = withContext(ioDispatcher) {
        try {
            chatSessionDao.deleteSession(sessionId)
            Timber.d("会话删除成功: sessionId=$sessionId")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "删除会话失败")
            ModernResult.Error(
                DataErrors.DataError.access(
                    operationName = "ChatSessionDaoRepo.deleteSession",
                    message = UiText.DynamicString("删除会话失败"),
                    entityType = "ChatSession",
                    cause = e,
                ),
            )
        }
    }
}
