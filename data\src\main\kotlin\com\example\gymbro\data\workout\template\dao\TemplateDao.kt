package com.example.gymbro.data.workout.template.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.example.gymbro.data.workout.template.entity.TemplateEntity
import kotlinx.coroutines.flow.Flow

/**
 * 模板数据访问对象 - TemplateDB DAO
 *
 * 基于 05_Final_Database_Architecture.md 设计
 * 提供训练模板管理功能
 */
@Dao
interface TemplateDao {

    // ==================== 基础 CRUD ====================

    @Query("SELECT * FROM workout_templates WHERE id = :templateId")
    suspend fun getTemplateById(templateId: String): TemplateEntity?

    @Query("SELECT * FROM workout_templates WHERE userId = :userId ORDER BY updatedAt DESC")
    fun getTemplatesByUser(userId: String): Flow<List<TemplateEntity>>

    @Query("SELECT * FROM workout_templates WHERE isPublic = 1 ORDER BY updatedAt DESC")
    fun getPublicTemplates(): Flow<List<TemplateEntity>>

    @Query("SELECT * FROM workout_templates WHERE isDraft = :isDraft ORDER BY updatedAt DESC")
    fun getTemplatesByDraftStatus(isDraft: Boolean): Flow<List<TemplateEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTemplate(template: TemplateEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTemplates(templates: List<TemplateEntity>)

    @Update
    suspend fun updateTemplate(template: TemplateEntity)

    @Query("DELETE FROM workout_templates WHERE id = :templateId")
    suspend fun deleteTemplate(templateId: String)

    // ==================== 特定查询 ====================

    @Query(
        "SELECT * FROM workout_templates WHERE userId = :userId AND isFavorite = 1 ORDER BY updatedAt DESC",
    )
    fun getFavoriteTemplates(userId: String): Flow<List<TemplateEntity>>

    @Query("SELECT * FROM workout_templates WHERE name LIKE '%' || :query || '%' ORDER BY name ASC")
    fun searchTemplatesByName(query: String): Flow<List<TemplateEntity>>

    @Query(
        "SELECT * FROM workout_templates WHERE userId = :userId AND name LIKE '%' || :query || '%' ORDER BY name ASC",
    )
    fun searchUserTemplatesByName(userId: String, query: String): Flow<List<TemplateEntity>>

    // ==================== 统计查询 ====================

    @Query("SELECT COUNT(*) FROM workout_templates WHERE userId = :userId")
    suspend fun getUserTemplateCount(userId: String): Int

    @Query("SELECT COUNT(*) FROM workout_templates WHERE isPublic = 1")
    suspend fun getPublicTemplateCount(): Int

    @Query("SELECT COUNT(*) FROM workout_templates WHERE userId = :userId AND isFavorite = 1")
    suspend fun getFavoriteTemplateCount(userId: String): Int

    // ==================== 批量操作 ====================

    @Query("UPDATE workout_templates SET isFavorite = :isFavorite WHERE id = :templateId")
    suspend fun updateTemplateFavoriteStatus(templateId: String, isFavorite: Boolean)

    @Query("UPDATE workout_templates SET isPublic = :isPublic WHERE id = :templateId")
    suspend fun updateTemplatePublicStatus(templateId: String, isPublic: Boolean)

    @Query("SELECT * FROM workout_templates WHERE updatedAt > :timestamp")
    suspend fun getTemplatesUpdatedAfter(timestamp: Long): List<TemplateEntity>
}
