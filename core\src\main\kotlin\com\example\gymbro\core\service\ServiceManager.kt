package com.example.gymbro.core.service

/**
 * 服务管理器接口
 *
 * 提供平台无关的服务管理抽象，用于启动和停止后台/前台服务。
 * 具体实现由各平台提供。
 */
interface ServiceManager {
    /**
     * 启动服务
     *
     * @param serviceId 服务标识符
     * @param parameters 可选的启动参数
     * @return 操作结果，表示是否成功启动服务
     */
    fun startService(
        serviceId: String,
        parameters: Map<String, Any> = emptyMap(),
    ): ServiceOperationResult

    /**
     * 停止服务
     *
     * @param serviceId 服务标识符
     * @return 操作结果，表示是否成功停止服务
     */
    fun stopService(serviceId: String): ServiceOperationResult

    /**
     * 检查服务是否正在运行
     *
     * @param serviceId 服务标识符
     * @return 如果服务正在运行，则为true；否则为false
     */
    fun isServiceRunning(serviceId: String): Boolean

    /**
     * 获取服务生命周期状态
     *
     * @param serviceId 服务标识符
     * @return 服务当前的生命周期状态
     */
    fun getServiceLifecycleState(serviceId: String): ServiceLifecycleState
}

/**
 * 前台服务管理器接口
 *
 * 专门处理前台服务的管理，继承自基本服务管理器接口。
 */
interface IForegroundServiceManager : ServiceManager {
    /**
     * 将服务提升为前台服务
     *
     * @param serviceId 服务标识符
     * @param notificationConfig 通知配置（根据平台可能有不同的实现要求）
     * @return 操作结果
     */
    fun promoteToForeground(
        serviceId: String,
        notificationConfig: Any?,
    ): ServiceOperationResult

    /**
     * 将前台服务降级为普通后台服务
     *
     * @param serviceId 服务标识符
     * @return 操作结果
     */
    fun demoteToBackground(serviceId: String): ServiceOperationResult
}

/**
 * 后台服务管理器接口
 *
 * 专门处理后台服务的管理，继承自基本服务管理器接口。
 */
interface IBackgroundServiceManager : ServiceManager {
    /**
     * 检查网络连接是否可用
     *
     * @return 如果网络连接可用，则为true；否则为false
     */
    fun isNetworkAvailable(): Boolean

    /**
     * 启用后台网络连接
     *
     * @return 操作结果
     */
    fun enableBackgroundNetwork(): ServiceOperationResult

    /**
     * 禁用后台网络连接
     *
     * @return 操作结果
     */
    fun disableBackgroundNetwork(): ServiceOperationResult

    /**
     * 设置后台服务优先级
     *
     * @param serviceId 服务标识符
     * @param priority 优先级值
     * @return 操作结果
     */
    fun setServicePriority(
        serviceId: String,
        priority: ServicePriority,
    ): ServiceOperationResult
}

/**
 * 服务操作结果
 *
 * 表示服务操作的结果状态和相关信息。
 */
sealed class ServiceOperationResult {
    /**
     * 操作成功
     *
     * @param data 可选的返回数据
     */
    data class Success(
        val data: Any? = null,
    ) : ServiceOperationResult()

    /**
     * 操作失败
     *
     * @param error 错误信息
     * @param exception 可选的异常对象
     */
    data class Failure(
        val error: String,
        val exception: Throwable? = null,
    ) : ServiceOperationResult()
}

/**
 * 服务生命周期状态
 *
 * 定义服务可能处于的各种生命周期状态。
 */
enum class ServiceLifecycleState {
    /** 服务未创建 */
    NOT_CREATED,

    /** 服务处于错误状态 */
    ERROR,

    /** 服务状态未知 */
    UNKNOWN,
}

/**
 * 服务优先级
 *
 * 定义服务可能的优先级级别。
 */
enum class ServicePriority {
    /** 高优先级，系统较少终止 */
    HIGH,
}
