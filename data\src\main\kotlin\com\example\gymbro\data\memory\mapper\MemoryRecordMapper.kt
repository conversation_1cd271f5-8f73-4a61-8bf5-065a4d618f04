package com.example.gymbro.data.memory.mapper

import com.example.gymbro.data.memory.entity.MemoryRecordEntity
import com.example.gymbro.shared.models.memory.MemoryRecord
import com.example.gymbro.shared.models.memory.MemoryTier
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.jsonObject

/**
 * MemoryRecord数据转换器
 *
 * 负责Domain层MemoryRecord和Data层MemoryRecordEntity之间的转换
 * 遵循GymBro mapper模式，提供双向转换功能
 */
object MemoryRecordMapper {

    /**
     * 将Domain层MemoryRecord转换为Data层MemoryRecordEntity
     */
    fun toEntity(record: MemoryRecord): MemoryRecordEntity {
        return MemoryRecordEntity(
            id = record.id,
            userId = record.userId,
            tier = record.tier.name,
            createdAt = record.createdAt,
            expiresAt = record.expiresAt,
            importance = record.importance,
            embedding = record.embedding,
            embeddingDim = record.embedding?.let { it.size / 4 } ?: MemoryRecord.DEFAULT_EMBEDDING_DIM,
            embeddingStatus = if (record.embedding != null) {
                MemoryRecordEntity.STATUS_COMPLETED
            } else {
                MemoryRecordEntity.STATUS_PENDING
            },
            payloadJson = Json.encodeToString(JsonObject.serializer(), record.payload),
            contentLength = record.payload.toString().length,
            modelVersion = MemoryRecordEntity.BGE_MODEL_VERSION,
            generationTimeMs = null,
            updatedAt = System.currentTimeMillis(),
        )
    }

    /**
     * 将Data层MemoryRecordEntity转换为Domain层MemoryRecord
     */
    fun toDomain(entity: MemoryRecordEntity): MemoryRecord {
        return MemoryRecord(
            id = entity.id,
            userId = entity.userId,
            tier = MemoryTier.valueOf(entity.tier),
            createdAt = entity.createdAt,
            expiresAt = entity.expiresAt,
            importance = entity.importance,
            embedding = entity.embedding,
            embeddingDim = entity.embeddingDim,
            payload = Json.parseToJsonElement(entity.payloadJson).jsonObject,
        )
    }

    /**
     * 批量转换为Entity
     */
    fun toEntities(records: List<MemoryRecord>): List<MemoryRecordEntity> {
        return records.map { toEntity(it) }
    }

    /**
     * 批量转换为Domain
     */
    fun toDomains(entities: List<MemoryRecordEntity>): List<MemoryRecord> {
        return entities.map { toDomain(it) }
    }
}
