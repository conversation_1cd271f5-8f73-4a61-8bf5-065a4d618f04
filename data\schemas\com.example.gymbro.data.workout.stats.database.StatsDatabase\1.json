{"formatVersion": 1, "database": {"version": 1, "identityHash": "390c33e60eb1349b2c39c93584c40b44", "entities": [{"tableName": "daily_stats", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` TEXT NOT NULL, `date` TEXT NOT NULL, `completedSessions` INTEGER NOT NULL, `completedExercises` INTEGER NOT NULL, `completedSets` INTEGER NOT NULL, `totalReps` INTEGER NOT NULL, `totalWeight` REAL NOT NULL, `avgRpe` REAL, `sessionDurationSec` INTEGER NOT NULL, `planId` TEXT, `dayOfWeek` INTEGER NOT NULL, `caloriesBurned` INTEGER, `averageHeartRate` INTEGER, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, PRIMARY KEY(`userId`, `date`))", "fields": [{"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "date", "columnName": "date", "affinity": "TEXT", "notNull": true}, {"fieldPath": "completedSessions", "columnName": "completedSessions", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "completedExercises", "columnName": "completedExercises", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "completedSets", "columnName": "completedSets", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalReps", "columnName": "totalReps", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalWeight", "columnName": "totalWeight", "affinity": "REAL", "notNull": true}, {"fieldPath": "avgRpe", "columnName": "avgRpe", "affinity": "REAL", "notNull": false}, {"fieldPath": "sessionDurationSec", "columnName": "sessionDurationSec", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "planId", "columnName": "planId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "dayOfWeek", "columnName": "dayOfWeek", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "caloriesBurned", "columnName": "caloriesBurned", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "averageHeartRate", "columnName": "averageHeartRate", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["userId", "date"]}, "indices": [{"name": "index_daily_stats_userId", "unique": false, "columnNames": ["userId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_daily_stats_userId` ON `${TABLE_NAME}` (`userId`)"}, {"name": "index_daily_stats_date", "unique": false, "columnNames": ["date"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_daily_stats_date` ON `${TABLE_NAME}` (`date`)"}, {"name": "index_daily_stats_userId_date", "unique": false, "columnNames": ["userId", "date"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_daily_stats_userId_date` ON `${TABLE_NAME}` (`userId`, `date`)"}, {"name": "index_daily_stats_planId", "unique": false, "columnNames": ["planId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_daily_stats_planId` ON `${TABLE_NAME}` (`planId`)"}, {"name": "index_daily_stats_createdAt", "unique": false, "columnNames": ["createdAt"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_daily_stats_createdAt` ON `${TABLE_NAME}` (`createdAt`)"}, {"name": "index_daily_stats_updatedAt", "unique": false, "columnNames": ["updatedAt"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_daily_stats_updatedAt` ON `${TABLE_NAME}` (`updatedAt`)"}], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '390c33e60eb1349b2c39c93584c40b44')"]}}