package com.example.gymbro.data.coach.repository

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.domain.coach.repository.HistorySessionMetadata
import com.example.gymbro.domain.coach.repository.HistoryStateRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.map
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * HistoryStateRepository实现
 *
 * 提供对History模块状态的访问，作为domain层和feature层的桥梁
 * 通过内部状态流管理preparedContextForPrompt的传递
 */
@Singleton
class HistoryStateRepositoryImpl @Inject constructor() : HistoryStateRepository {

    companion object {
        private const val TAG = "HistoryStateRepo"
    }

    // 内部状态管理 - 模拟History模块的状态
    // 实际项目中应该通过依赖注入获取HistoryViewModel的状态流
    private val _preparedContext = MutableStateFlow<String?>(null)
    private val _contextConsumed = MutableStateFlow(true)
    private val _sessionMetadata = MutableStateFlow<HistorySessionMetadata?>(null)

    /**
     * 获取当前准备的上下文内容
     *
     * 监听History模块的preparedContextForPrompt状态变化
     */
    override fun getCurrentPreparedContext(): Flow<String?> {
        return _preparedContext
            .map { context ->
                Timber.d("$TAG: 上下文状态变化，长度=${context?.length ?: 0}")
                context
            }
    }

    /**
     * 获取上下文是否已被消费的状态
     */
    override fun getContextConsumedState(): Flow<Boolean> {
        return _contextConsumed
            .map { consumed ->
                Timber.d("$TAG: 上下文消费状态变化，已消费=$consumed")
                consumed
            }
    }

    /**
     * 标记上下文为已消费
     */
    override suspend fun markContextAsConsumed(): ModernResult<Unit> {
        return try {
            _contextConsumed.value = true
            Timber.d("$TAG: 上下文已标记为已消费")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "$TAG: 标记上下文消费状态失败")
            ModernResult.Error(
                DataErrors.DataError.update(
                    operationName = "markContextAsConsumed",
                    message = com.example.gymbro.core.ui.text.UiText.DynamicString("标记消费状态失败: ${e.message}"),
                    entityType = "HistoryContext",
                    entityId = "current",
                ),
            )
        }
    }

    /**
     * 清除当前准备的上下文
     */
    override suspend fun clearPreparedContext(): ModernResult<Unit> {
        return try {
            _preparedContext.value = null
            _contextConsumed.value = true
            Timber.d("$TAG: 准备上下文已清除")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "$TAG: 清除准备上下文失败")
            ModernResult.Error(
                DataErrors.DataError.delete(
                    operationName = "clearPreparedContext",
                    message = com.example.gymbro.core.ui.text.UiText.DynamicString("清除上下文失败: ${e.message}"),
                    entityType = "HistoryContext",
                    entityId = "current",
                ),
            )
        }
    }

    /**
     * 获取当前History会话的元数据
     */
    override suspend fun getSessionMetadata(sessionId: String?): ModernResult<HistorySessionMetadata> {
        return try {
            val metadata = _sessionMetadata.value ?: HistorySessionMetadata(
                sessionId = sessionId ?: "default_session",
                title = "当前会话",
                messageCount = 0,
                lastActiveTime = System.currentTimeMillis(),
                createdAt = System.currentTimeMillis(),
            )

            Timber.d("$TAG: 获取会话元数据成功，sessionId=${metadata.sessionId}")
            ModernResult.Success(metadata)
        } catch (e: Exception) {
            Timber.e(e, "$TAG: 获取会话元数据失败")
            ModernResult.Error(
                DataErrors.DataError.notFound(
                    operationName = "getSessionMetadata",
                    message = com.example.gymbro.core.ui.text.UiText.DynamicString("获取会话元数据失败: ${e.message}"),
                    entityType = "HistorySessionMetadata",
                    entityId = sessionId ?: "unknown",
                ),
            )
        }
    }

    // ==================== 内部方法（用于测试和集成） ====================

    /**
     * 更新准备的上下文（内部方法）
     * 实际项目中应该由History模块调用
     */
    internal fun updatePreparedContext(context: String?) {
        Timber.d("$TAG: 更新准备上下文，长度=${context?.length ?: 0}")
        _preparedContext.value = context
        _contextConsumed.value = false // 新上下文默认未消费
    }

    /**
     * 更新会话元数据（内部方法）
     */
    internal fun updateSessionMetadata(metadata: HistorySessionMetadata) {
        Timber.d("$TAG: 更新会话元数据，sessionId=${metadata.sessionId}")
        _sessionMetadata.value = metadata
    }

    /**
     * 重置所有状态（内部方法，用于测试）
     */
    internal fun resetState() {
        _preparedContext.value = null
        _contextConsumed.value = true
        _sessionMetadata.value = null
        Timber.d("$TAG: 状态已重置")
    }
}
