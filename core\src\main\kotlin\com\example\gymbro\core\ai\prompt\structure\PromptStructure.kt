package com.example.gymbro.core.ai.prompt.structure

/**
 * Prompt分层结构（已废弃）
 *
 * @deprecated 在618重构中已废弃，改用LayeredPromptBuilder直接构建ChatMessage
 * 原有的多层结构已简化为统一的ChatMessage构建流程
 *
 * @since 1.0.0
 */
@Deprecated("在618重构中已废弃，改用LayeredPromptBuilder直接构建ChatMessage")
data class PromptStructure(
    val system: SystemLayer,
) {
    /**
     * 构建完整的Prompt内容（已废弃）
     * @deprecated 改用LayeredPromptBuilder.buildChatMessages()
     */
    @Deprecated("改用LayeredPromptBuilder.buildChatMessages()")
    fun buildFullPrompt(): String {
        return system.content
    }

    /**
     * 构建增量Prompt内容（已废弃）
     * @deprecated 改用LayeredPromptBuilder.buildChatMessages()
     */
    @Deprecated("改用LayeredPromptBuilder.buildChatMessages()")
    fun buildIncrementalPrompt(lastStructure: PromptStructure?): String {
        return system.content
    }

    /**
     * 获取总体hash值（已废弃）
     * @deprecated 改用LayeredPromptBuilder
     */
    @Deprecated("改用LayeredPromptBuilder")
    val overallHash: String
        get() = system.content.hashCode().toString()

    /**
     * 检查是否有有效内容（已废弃）
     * @deprecated 改用LayeredPromptBuilder
     */
    @Deprecated("改用LayeredPromptBuilder")
    val hasValidContent: Boolean
        get() = system.content.isNotBlank()
}
