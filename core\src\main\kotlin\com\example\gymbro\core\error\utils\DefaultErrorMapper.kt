package com.example.gymbro.core.error.utils

import android.database.sqlite.SQLiteException
import com.example.gymbro.core.error.extensions.toModernDataError
import com.example.gymbro.core.error.types.ErrorCategory
import com.example.gymbro.core.error.types.ErrorSeverity
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.ui.text.UiText
import retrofit2.HttpException
import timber.log.Timber
import java.io.IOException
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * ErrorMapper 接口的默认实现
 * 提供通用异常到 ModernDataError 的映射
 */
@Singleton
class DefaultErrorMapper @Inject constructor() : ErrorMapper {

    /**
     * 将异常映射为 ModernDataError
     *
     * @param exception 要映射的异常
     * @param errorMessage 可选的错误消息，用于覆盖默认消息
     * @return 映射后的 ModernDataError
     */
    override fun mapExceptionToError(exception: Throwable, errorMessage: UiText?): ModernDataError {
        Timber.d("映射异常: %s", exception.javaClass.simpleName)

        return when (exception) {
            is ModernDataError -> {
                // 如果已经是 ModernDataError，则直接返回或更新消息
                if (errorMessage != null) {
                    exception.copy(uiMessage = errorMessage)
                } else {
                    exception
                }
            }
            is HttpException -> mapHttpException(exception, errorMessage)
            is UnknownHostException, is ConnectException -> {
                ModernDataError(
                    operationName = "NetworkError",
                    errorType = GlobalErrorType.Network.Connection,
                    category = ErrorCategory.NETWORK,
                    uiMessage = errorMessage ?: UiText.DynamicString("网络连接错误，请检查您的网络连接"),
                    severity = ErrorSeverity.WARNING,
                    recoverable = true,
                    cause = exception,
                )
            }
            is SocketTimeoutException -> {
                ModernDataError(
                    operationName = "TimeoutError",
                    errorType = GlobalErrorType.Network.Timeout,
                    category = ErrorCategory.NETWORK,
                    uiMessage = errorMessage ?: UiText.DynamicString("请求超时，请稍后重试"),
                    severity = ErrorSeverity.WARNING,
                    recoverable = true,
                    cause = exception,
                )
            }
            is IOException -> {
                ModernDataError(
                    operationName = "IOError",
                    errorType = GlobalErrorType.System.Resource,
                    category = ErrorCategory.SYSTEM,
                    uiMessage = errorMessage ?: UiText.DynamicString("系统资源错误，请重试"),
                    severity = ErrorSeverity.ERROR,
                    recoverable = true,
                    cause = exception,
                )
            }
            is SQLiteException -> {
                ModernDataError(
                    operationName = "DatabaseError",
                    errorType = GlobalErrorType.Database.QueryFailed,
                    category = ErrorCategory.DATA,
                    uiMessage = errorMessage ?: UiText.DynamicString("数据库操作失败"),
                    severity = ErrorSeverity.ERROR,
                    recoverable = true,
                    cause = exception,
                )
            }
            else -> {
                // 使用扩展函数将通用 Throwable 转换为 ModernDataError
                exception.toModernDataError(
                    operationName = "DefaultErrorMapper.mapExceptionToError",
                    uiMessage = errorMessage,
                )
            }
        }
    }

    /**
     * 映射 HTTP 异常到对应的 ModernDataError
     *
     * @param exception HTTP 异常
     * @param errorMessage 可选的错误消息
     * @return 映射后的 ModernDataError
     */
    private fun mapHttpException(exception: HttpException, errorMessage: UiText?): ModernDataError {
        val statusCode = exception.code()
        val errorBody = exception.response()?.errorBody()?.string()

        return when (statusCode) {
            in 400..499 -> {
                when (statusCode) {
                    401 -> ModernDataError(
                        operationName = "Unauthorized",
                        errorType = GlobalErrorType.Auth.Unauthorized,
                        category = ErrorCategory.AUTH,
                        uiMessage = errorMessage ?: UiText.DynamicString("认证失败，请重新登录"),
                        severity = ErrorSeverity.WARNING,
                        recoverable = true,
                        cause = exception,
                        statusCode = statusCode,
                        metadataMap = mapOf("errorBody" to (errorBody ?: "无错误内容")),
                    )
                    403 -> ModernDataError(
                        operationName = "Forbidden",
                        errorType = GlobalErrorType.Auth.Forbidden,
                        category = ErrorCategory.AUTH,
                        uiMessage = errorMessage ?: UiText.DynamicString("没有权限执行此操作"),
                        severity = ErrorSeverity.WARNING,
                        recoverable = false,
                        cause = exception,
                        statusCode = statusCode,
                        metadataMap = mapOf("errorBody" to (errorBody ?: "无错误内容")),
                    )
                    404 -> ModernDataError(
                        operationName = "NotFound",
                        errorType = GlobalErrorType.Data.NotFound,
                        category = ErrorCategory.DATA,
                        uiMessage = errorMessage ?: UiText.DynamicString("请求的资源不存在"),
                        severity = ErrorSeverity.WARNING,
                        recoverable = false,
                        cause = exception,
                        statusCode = statusCode,
                        metadataMap = mapOf("errorBody" to (errorBody ?: "无错误内容")),
                    )
                    else -> ModernDataError(
                        operationName = "ClientError",
                        errorType = GlobalErrorType.System.General,
                        category = ErrorCategory.SYSTEM,
                        uiMessage = errorMessage ?: UiText.DynamicString("请求错误，状态码：$statusCode"),
                        severity = ErrorSeverity.WARNING,
                        recoverable = false,
                        cause = exception,
                        statusCode = statusCode,
                        metadataMap = mapOf("errorBody" to (errorBody ?: "无错误内容")),
                    )
                }
            }
            in 500..599 -> ModernDataError(
                operationName = "ServerError",
                errorType = GlobalErrorType.System.Internal,
                category = ErrorCategory.SYSTEM,
                uiMessage = errorMessage ?: UiText.DynamicString("服务器错误，请稍后重试"),
                severity = ErrorSeverity.ERROR,
                recoverable = true,
                cause = exception,
                statusCode = statusCode,
                metadataMap = mapOf("errorBody" to (errorBody ?: "无错误内容")),
            )
            else -> ModernDataError(
                operationName = "HttpError",
                errorType = GlobalErrorType.System.General,
                category = ErrorCategory.SYSTEM,
                uiMessage = errorMessage ?: UiText.DynamicString("HTTP错误，状态码：$statusCode"),
                severity = ErrorSeverity.ERROR,
                recoverable = false,
                cause = exception,
                statusCode = statusCode,
                metadataMap = mapOf("errorBody" to (errorBody ?: "无错误内容")),
            )
        }
    }

    /**
     * 创建一个表示未知错误的 ModernDataError
     *
     * @param message 可选的错误消息
     * @param operationName 操作名称，帮助定位错误来源
     * @return 表示未知错误的 ModernDataError
     */
    override fun createUnknownError(message: UiText?, operationName: String): ModernDataError {
        return ModernDataError(
            operationName = operationName,
            errorType = GlobalErrorType.Unknown,
            category = ErrorCategory.UNKNOWN,
            uiMessage = message ?: UiText.DynamicString("发生未知错误"),
            severity = ErrorSeverity.ERROR,
            recoverable = false,
            cause = null,
        )
    }
}
