package com.example.gymbro.data.local.datastore

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.*
import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.ui.text.UiText
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 用户偏好设置仓库实现
 * 使用DataStore存储和检索用户偏好设置
 *
 * 紧急修复：直接注入 DataStore 实例，避免多实例问题
 */
@Singleton
class UserPreferencesRepositoryImpl
@Inject
constructor(
    private val dataStore: DataStore<Preferences>,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
) : UserPreferencesRepository {
    // 偏好设置键
    private object PreferencesKeys {
        // 区域设置
        val USER_REGION = stringPreferencesKey("user_region")
        val REGION_LOCKED = booleanPreferencesKey("region_locked")
        val REGION_LAST_UPDATED = longPreferencesKey("region_last_updated")

        // 主题设置
        val DARK_THEME = booleanPreferencesKey("dark_theme")
        val DYNAMIC_COLOR = booleanPreferencesKey("dynamic_color")

        // IP属地
        val IP_LOCATION = stringPreferencesKey("ip_location")

        // 隐私设置
        val ANONYMOUS_MODE = booleanPreferencesKey("anonymous_mode")
        val LOCATION_SHARING = booleanPreferencesKey("location_sharing")
        val PROFILE_VISIBILITY = booleanPreferencesKey("profile_visibility")

        // 通知设置
        val NEARBY_NOTIFICATIONS = booleanPreferencesKey("nearby_notifications")
        val MESSAGE_NOTIFICATIONS = booleanPreferencesKey("message_notifications")

        // 计时器设置
        val DEFAULT_COUNTDOWN_DURATION = intPreferencesKey("default_countdown_duration")
        val COUNTDOWN_SOUND_ENABLED = booleanPreferencesKey("countdown_sound_enabled")
        val REST_TIMER_DURATION = intPreferencesKey("rest_timer_duration")
        val LAST_TIMER_DURATION = intPreferencesKey("last_timer_duration")

        // 同步设置
        val LAST_SYNC_TIME = longPreferencesKey("last_sync_time")

        // 语言设置 (新增)
        val APP_LANGUAGE = stringPreferencesKey("app_language")
    }

    /**
     * 获取用户区域代码
     * @return 包含区域代码字符串的ModernResult流
     */
    override fun getUserRegionCode(): Flow<ModernResult<String?>> =
        flow {
            dataStore.data
                .map { preferences ->
                    val regionCode = preferences[PreferencesKeys.USER_REGION]
                    Timber.d("获取用户区域代码：%s", regionCode ?: "null")
                    ModernResult.success(regionCode)
                }.catch { e ->
                    Timber.e(e, "读取用户区域代码时发生错误")
                    emit(
                        ModernResult.error(
                            DataErrors.DataError.access(
                                operationName = "getUserRegionCode",
                                message = UiText.DynamicString("Failed to read user region code"),
                                cause = e,
                            ),
                        ),
                    )
                }.collect { emit(it) }
        }

    /**
     * 设置用户区域代码
     * @param regionCode 区域代码字符串
     * @return 设置操作的结果
     */
    override suspend fun setUserRegionCode(regionCode: String): ModernResult<Unit> =
        withContext(ioDispatcher) {
            try {
                Timber.d("设置用户区域代码：%s", regionCode)
                dataStore.edit { preferences ->
                    preferences[PreferencesKeys.USER_REGION] = regionCode
                    preferences[PreferencesKeys.REGION_LAST_UPDATED] = System.currentTimeMillis()
                }
                Timber.i("用户区域代码设置成功")
                ModernResult.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "设置用户区域代码时发生错误")
                ModernResult.error(
                    DataErrors.DataError.access(
                        operationName = "setUserRegionCode",
                        message = UiText.DynamicString("Failed to write user region code"),
                        cause = e,
                    ),
                )
            }
        }

    /**
     * 获取主题偏好(深色/浅色)
     * @return 是否深色主题的ModernResult流
     */
    override fun getDarkThemePreference(): Flow<ModernResult<Boolean>> =
        flow {
            dataStore.data
                .map { preferences ->
                    val isDarkTheme = preferences[PreferencesKeys.DARK_THEME] ?: false
                    Timber.d("获取深色主题偏好：%s", isDarkTheme)
                    ModernResult.success(isDarkTheme)
                }.catch { e ->
                    Timber.e(e, "读取深色主题偏好时发生错误")
                    emit(
                        ModernResult.error(
                            DataErrors.DataError.access(
                                operationName = "getDarkThemePreference",
                                message = UiText.DynamicString("Failed to read theme preference"),
                                cause = e,
                            ),
                        ),
                    )
                }.collect { emit(it) }
        }

    /**
     * 设置主题偏好
     * @param isDarkTheme 是否深色主题
     * @return 设置操作的结果
     */
    override suspend fun setDarkThemePreference(isDarkTheme: Boolean): ModernResult<Unit> =
        withContext(ioDispatcher) {
            try {
                Timber.d("设置深色主题偏好：%s", isDarkTheme)
                dataStore.edit { preferences ->
                    preferences[PreferencesKeys.DARK_THEME] = isDarkTheme
                }
                Timber.i("深色主题偏好设置成功")
                ModernResult.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "设置深色主题偏好时发生错误")
                ModernResult.error(
                    DataErrors.DataError.access(
                        operationName = "setDarkThemePreference",
                        message = UiText.DynamicString("Failed to write theme preference"),
                        cause = e,
                    ),
                )
            }
        }

    /**
     * 获取动态主题偏好
     * @return 是否启用动态主题的ModernResult流
     */
    override fun getDynamicColorPreference(): Flow<ModernResult<Boolean>> =
        flow {
            dataStore.data
                .map { preferences ->
                    val useDynamicColor = preferences[PreferencesKeys.DYNAMIC_COLOR] ?: true // 默认启用动态主题
                    Timber.d("获取动态主题偏好：%s", useDynamicColor)
                    ModernResult.success(useDynamicColor)
                }.catch { e ->
                    Timber.e(e, "读取动态主题偏好时发生错误")
                    emit(
                        ModernResult.error(
                            DataErrors.DataError.access(
                                operationName = "getDynamicColorPreference",
                                message = UiText.DynamicString("Failed to read dynamic color preference"),
                                cause = e,
                            ),
                        ),
                    )
                }.collect { emit(it) }
        }

    /**
     * 设置动态主题偏好
     * @param useDynamicColor 是否启用动态主题
     * @return 设置操作的结果
     */
    override suspend fun setDynamicColorPreference(useDynamicColor: Boolean): ModernResult<Unit> =
        withContext(ioDispatcher) {
            try {
                Timber.d("设置动态主题偏好：%s", useDynamicColor)
                dataStore.edit { preferences ->
                    preferences[PreferencesKeys.DYNAMIC_COLOR] = useDynamicColor
                }
                Timber.i("动态主题偏好设置成功")
                ModernResult.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "设置动态主题偏好时发生错误")
                ModernResult.error(
                    DataErrors.DataError.access(
                        operationName = "setDynamicColorPreference",
                        message = UiText.DynamicString("Failed to write dynamic color preference"),
                        cause = e,
                    ),
                )
            }
        }

    /**
     * 获取IP属地
     * @return IP属地字符串(例如 "CN")的ModernResult流
     */
    override fun getIpLocation(): Flow<ModernResult<String?>> =
        flow {
            dataStore.data
                .map { preferences ->
                    val location = preferences[PreferencesKeys.IP_LOCATION]
                    Timber.d("获取IP属地：%s", location ?: "null")
                    ModernResult.success(location)
                }.catch { e ->
                    Timber.e(e, "读取IP属地时发生错误")
                    emit(
                        ModernResult.error(
                            DataErrors.DataError.access(
                                operationName = "getIpLocation",
                                message = UiText.DynamicString("Failed to read IP location preference"),
                                cause = e,
                            ),
                        ),
                    )
                }.collect { emit(it) }
        }

    /**
     * 设置IP属地
     * @param location IP属地字符串
     * @return 设置操作的结果
     */
    override suspend fun setIpLocation(location: String): ModernResult<Unit> =
        withContext(ioDispatcher) {
            try {
                Timber.d("设置IP属地：%s", location)
                dataStore.edit { preferences ->
                    preferences[PreferencesKeys.IP_LOCATION] = location
                }
                Timber.i("IP属地设置成功")
                ModernResult.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "设置IP属地时发生错误")
                ModernResult.error(
                    DataErrors.DataError.access(
                        operationName = "setIpLocation",
                        message = UiText.DynamicString("Failed to write IP location preference"),
                        cause = e,
                    ),
                )
            }
        }

    // 简化实现：为了紧急修复 DataStore 多实例问题，暂时提供基础实现
    // TODO: 完整实现所有方法

    override fun getAnonymousMode(): Flow<ModernResult<Boolean>> = flow {
        emit(
            ModernResult.success(false),
        )
    }

    override suspend fun setAnonymousMode(enabled: Boolean): ModernResult<Unit> = ModernResult.success(
        Unit,
    )

    override fun getLocationSharing(): Flow<ModernResult<Boolean>> = flow {
        emit(
            ModernResult.success(false),
        )
    }

    override suspend fun setLocationSharing(enabled: Boolean): ModernResult<Unit> = ModernResult.success(
        Unit,
    )

    override fun getProfileVisibility(): Flow<ModernResult<Boolean>> = flow {
        emit(
            ModernResult.success(true),
        )
    }

    override suspend fun setProfileVisibility(visible: Boolean): ModernResult<Unit> = ModernResult.success(
        Unit,
    )

    override fun getNearbyNotifications(): Flow<ModernResult<Boolean>> = flow {
        emit(
            ModernResult.success(true),
        )
    }

    override suspend fun setNearbyNotifications(enabled: Boolean): ModernResult<Unit> = ModernResult.success(
        Unit,
    )

    override fun getMessageNotifications(): Flow<ModernResult<Boolean>> = flow {
        emit(ModernResult.success(true))
    }

    override suspend fun setMessageNotifications(enabled: Boolean): ModernResult<Unit> = ModernResult.success(
        Unit,
    )

    override fun getDefaultCountdownDuration(): Flow<ModernResult<Int>> = flow {
        emit(
            ModernResult.success(60),
        )
    }

    override suspend fun setDefaultCountdownDuration(seconds: Int): ModernResult<Unit> = ModernResult.success(
        Unit,
    )

    override fun getCountdownSoundEnabled(): Flow<ModernResult<Boolean>> = flow {
        emit(ModernResult.success(true))
    }

    override suspend fun setCountdownSoundEnabled(enabled: Boolean): ModernResult<Unit> = ModernResult.success(
        Unit,
    )

    override suspend fun getRestTimerDuration(): ModernResult<Int> = ModernResult.success(90)

    override suspend fun setRestTimerDuration(duration: Int): ModernResult<Unit> = ModernResult.success(
        Unit,
    )

    override suspend fun getLastTimerDuration(): ModernResult<Int> = ModernResult.success(60)

    override suspend fun saveLastTimerDuration(duration: Int): ModernResult<Unit> = ModernResult.success(
        Unit,
    )

    override suspend fun getLastSyncTimeMillis(): ModernResult<Long> = ModernResult.success(0L)

    override suspend fun saveLastSyncTimeMillis(timeMillis: Long): ModernResult<Unit> = ModernResult.success(
        Unit,
    )

    override suspend fun setLanguage(languageCode: String): ModernResult<Unit> = ModernResult.success(
        Unit,
    )

    override fun getLanguage(): Flow<ModernResult<String?>> = flow { emit(ModernResult.success(null)) }

    companion object {
        // 默认倒计时时长（秒）
        const val DEFAULT_COUNTDOWN_DURATION = 60
    }
}
