# 影子流量测试配置文件
# Shadow Traffic Testing Configuration

# 基础测试配置
shadow_percentage: 0.05  # 5%用户参与影子测试
max_difference_threshold: 0.1  # 最大允许差异阈值 (10%)
test_duration_hours: 168  # 7天 = 168小时
comparison_interval_seconds: 30  # 每30秒对比一次

# API端点配置
prod_api_endpoint: "https://api.gymbro.com/v1"
shadow_api_endpoint: "https://shadow-api.gymbro.com/v1"

# 监控和告警配置
alert_webhook_url: "*****************************************************************************"
metrics_endpoint: "https://metrics.gymbro.com/api/v1/metrics"

# 高级配置
advanced:
  # 并发控制
  max_concurrent_comparisons: 10

  # 重试配置
  max_retries: 3
  retry_backoff_seconds: 5

  # 数据采样
  sample_rate: 1.0  # 100%采样

  # 告警配置
  critical_difference_threshold: 0.5  # 50%以上差异视为关键
  consecutive_failures_threshold: 5  # 连续5次失败触发告警

  # 存储配置
  enable_detailed_logging: true
  log_retention_days: 30

  # 性能配置
  request_timeout_seconds: 30
  max_memory_usage_mb: 1024

# 状态机专项检查配置
state_machine_checks:
  # AI消息数量检查
  ai_message_count_check:
    enabled: true
    max_allowed_count: 1
    alert_on_violation: true

  # 加载状态检查
  loading_state_check:
    enabled: true
    max_loading_duration_seconds: 60
    alert_on_timeout: true

  # 状态转换检查
  state_transition_check:
    enabled: true
    valid_states: ["THINKING", "STREAMING", "DONE", "ERROR"]
    transition_timeout_seconds: 30

  # 重试状态检查
  retry_state_check:
    enabled: true
    max_retry_count: 3
    reset_on_success: true

# 详细比较配置
comparison_config:
  # 内容比较
  content_comparison:
    algorithm: "levenshtein"  # 使用编辑距离算法
    ignore_whitespace: false
    ignore_case: false

  # 时序比较
  timing_comparison:
    enabled: true
    max_delay_seconds: 5

  # 结构比较
  structure_comparison:
    enabled: true
    check_message_order: true
    check_message_types: true

# 环境特定配置
environments:
  production:
    shadow_percentage: 0.05
    max_difference_threshold: 0.05
    alert_webhook_url: "https://hooks.slack.com/services/PROD/WEBHOOK"

  staging:
    shadow_percentage: 0.20
    max_difference_threshold: 0.20
    alert_webhook_url: "https://hooks.slack.com/services/STAGING/WEBHOOK"

  development:
    shadow_percentage: 1.0
    max_difference_threshold: 0.50
    alert_webhook_url: "https://hooks.slack.com/services/DEV/WEBHOOK"
