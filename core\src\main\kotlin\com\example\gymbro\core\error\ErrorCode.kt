package com.example.gymbro.core.error

import kotlinx.serialization.Serializable

/**
 * 错误代码枚举
 *
 * 用于将core层的错误抽象为代码，由app层映射到具体的本地化字符串
 * 遵循Clean Architecture原则：core层定义错误代码，app层处理UI文本
 *
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Serializable
enum class ErrorCode(val code: String, val category: String) {

    // ==================== 网络错误 ====================
    /** 网络连接失败，已尝试多次重试 */
    NETWORK_RETRY_EXHAUSTED("NET_001", "network"),

    /** 网络连接失败，正在使用离线数据 */
    NETWORK_FALLBACK_CACHE("NET_002", "network"),

    /** 已切换到离线模式，数据将自动同步 */
    NETWORK_OFFLINE_MODE("NET_003", "network"),

    /** 网络连接不稳定，请检查网络设置 */
    NETWORK_UNSTABLE("NET_004", "network"),

    /** 网络连接失败，请检查网络设置 */
    NETWORK_CONNECTION_FAILED("NET_005", "network"),

    /** 网络连接异常 */
    NETWORK_EXCEPTION("NET_006", "network"),

    // ==================== 认证错误 ====================
    /** 认证失败，请重新登录 */
    AUTH_FAILED("AUTH_001", "auth"),

    /** 会话已过期，请重新登录 */
    AUTH_SESSION_EXPIRED("AUTH_002", "auth"),

    /** 没有权限执行此操作 */
    AUTH_FORBIDDEN("AUTH_003", "auth"),

    // ==================== 数据错误 ====================
    /** 未找到资源 */
    DATA_NOT_FOUND("DATA_001", "data"),

    /** 未找到资源(带ID) */
    DATA_NOT_FOUND_WITH_ID("DATA_002", "data"),

    // ==================== 验证错误 ====================
    /** 字段是必填的 */
    VALIDATION_REQUIRED("VAL_001", "validation"),

    /** 格式无效 */
    VALIDATION_FORMAT_INVALID("VAL_002", "validation"),

    // ==================== 业务错误 ====================
    /** 业务规则验证失败 */
    BUSINESS_RULE_VIOLATION("BIZ_001", "business"),

    // ==================== 系统错误 ====================
    /** 系统错误，请稍后重试 */
    SYSTEM_ERROR("SYS_001", "system"),

    /** 系统配置错误 */
    SYSTEM_CONFIGURATION_ERROR("SYS_002", "system"),

    /** 系统权限不足 */
    SYSTEM_PERMISSION_ERROR("SYS_003", "system"),

    /** 功能尚未实现 */
    SYSTEM_NOT_IMPLEMENTED("SYS_004", "system"),

    /** 发生未知错误 */
    UNKNOWN_ERROR("UNK_001", "unknown"),

    // ==================== 支付错误 ====================
    /** 网络连接失败，请检查网络设置 */
    PAYMENT_NETWORK_ERROR("PAY_001", "payment"),

    /** 连接超时，请稍后重试 */
    PAYMENT_TIMEOUT("PAY_002", "payment"),

    /** 支付被拒绝，请检查支付信息 */
    PAYMENT_DECLINED("PAY_003", "payment"),

    /** 账户余额不足 */
    PAYMENT_INSUFFICIENT_FUNDS("PAY_004", "payment"),

    /** 无效的支付方式 */
    PAYMENT_INVALID_METHOD("PAY_005", "payment"),

    /** 支付已过期 */
    PAYMENT_EXPIRED("PAY_006", "payment"),

    /** 身份验证失败 */
    PAYMENT_AUTH_FAILED("PAY_007", "payment"),

    /** 支付授权失败 */
    PAYMENT_AUTHORIZATION_FAILED("PAY_008", "payment"),

    /** 重复支付 */
    PAYMENT_DUPLICATE("PAY_009", "payment"),

    /** 找不到支付记录 */
    PAYMENT_NOT_FOUND("PAY_010", "payment"),

    /** 无效的支付金额 */
    PAYMENT_INVALID_AMOUNT("PAY_011", "payment"),

    /** 不支持的货币类型 */
    PAYMENT_CURRENCY_NOT_SUPPORTED("PAY_012", "payment"),

    /** 支付服务不可用 */
    PAYMENT_SERVICE_UNAVAILABLE("PAY_013", "payment"),

    /** 数据存储失败 */
    PAYMENT_DATABASE_ERROR("PAY_014", "payment"),

    /** 支付处理失败 */
    PAYMENT_UNKNOWN_ERROR("PAY_015", "payment"),
    ;

    /**
     * 获取错误代码的完整标识符
     */
    fun getFullCode(): String = "${category.uppercase()}_$code"

    companion object {
        /**
         * 根据代码查找ErrorCode
         */
        fun fromCode(code: String): ErrorCode? = values().find { it.code == code }

        /**
         * 根据分类获取所有ErrorCode
         */
        fun fromCategory(category: String): List<ErrorCode> =
            values().filter { it.category == category }

        /**
         * 获取所有分类
         */
        fun getAllCategories(): List<String> =
            values().map { it.category }.distinct()
    }
}
