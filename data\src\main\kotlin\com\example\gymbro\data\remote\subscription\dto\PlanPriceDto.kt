package com.example.gymbro.data.remote.subscription.dto

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * 计划价格数据传输对象
 *
 * @property planId 计划唯一标识符
 * @property planName 计划名称
 * @property description 计划描述
 * @property currency 货币代码 (例如, "USD", "CNY")
 * @property amount 价格金额
 * @property interval 订阅间隔（月度、年度等）
 * @property intervalCount 间隔计数（例如，1个月，12个月）
 * @property features 包含的功能列表
 */
@Serializable
data class PlanPriceDto(
    @SerialName("plan_id") val planId: String,
    @SerialName("plan_name") val planName: String,
    @SerialName("description") val description: String,
    @SerialName("currency") val currency: String,
    @SerialName("amount") val amount: Double,
    @SerialName("interval") val interval: String,
    @SerialName("interval_count") val intervalCount: Int,
    @SerialName("features") val features: List<String> = emptyList(),
)
