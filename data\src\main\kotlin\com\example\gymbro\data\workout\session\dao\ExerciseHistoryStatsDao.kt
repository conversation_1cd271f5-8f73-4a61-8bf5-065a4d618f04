package com.example.gymbro.data.workout.session.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.example.gymbro.data.workout.session.entity.ExerciseHistoryStatsEntity
import kotlinx.coroutines.flow.Flow

/**
 * 动作历史统计数据访问对象 - SessionDB DAO
 *
 * 基于 05_Final_Database_Architecture.md 设计
 * 提供动作历史统计和个人最佳记录管理功能
 */
@Dao
interface ExerciseHistoryStatsDao {

    // ==================== 基础 CRUD ====================

    @Query("SELECT * FROM exercise_history_stats WHERE id = :statsId")
    suspend fun getStatsById(statsId: String): ExerciseHistoryStatsEntity?

    @Query("SELECT * FROM exercise_history_stats WHERE userId = :userId AND exerciseId = :exerciseId")
    suspend fun getStatsByUserAndExercise(userId: String, exerciseId: String): ExerciseHistoryStatsEntity?

    @Query("SELECT * FROM exercise_history_stats WHERE userId = :userId ORDER BY lastPerformanceDate DESC")
    fun getUserStats(userId: String): Flow<List<ExerciseHistoryStatsEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertStats(stats: ExerciseHistoryStatsEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMultipleStats(statsList: List<ExerciseHistoryStatsEntity>)

    @Update
    suspend fun updateStats(stats: ExerciseHistoryStatsEntity)

    @Query("DELETE FROM exercise_history_stats WHERE id = :statsId")
    suspend fun deleteStats(statsId: String)

    // ==================== 个人最佳记录更新 ====================

    @Query(
        "UPDATE exercise_history_stats SET personalBestWeight = :weight, lastUpdated = :updateTime WHERE userId = :userId AND exerciseId = :exerciseId AND (personalBestWeight IS NULL OR personalBestWeight < :weight)",
    )
    suspend fun updatePersonalBestWeight(userId: String, exerciseId: String, weight: Double, updateTime: Long)

    @Query(
        "UPDATE exercise_history_stats SET personalBestReps = :reps, lastUpdated = :updateTime WHERE userId = :userId AND exerciseId = :exerciseId AND (personalBestReps IS NULL OR personalBestReps < :reps)",
    )
    suspend fun updatePersonalBestReps(userId: String, exerciseId: String, reps: Int, updateTime: Long)

    @Query(
        "UPDATE exercise_history_stats SET totalSetsCompleted = totalSetsCompleted + :additionalSets, totalVolumeLifted = totalVolumeLifted + :additionalVolume, lastPerformanceDate = :performanceDate, lastUpdated = :updateTime WHERE userId = :userId AND exerciseId = :exerciseId",
    )
    suspend fun updateCumulativeStats(
        userId: String,
        exerciseId: String,
        additionalSets: Int,
        additionalVolume: Double,
        performanceDate: Long,
        updateTime: Long,
    )

    // ==================== 统计查询 ====================

    @Query(
        "SELECT * FROM exercise_history_stats WHERE userId = :userId ORDER BY personalBestWeight DESC LIMIT :limit",
    )
    fun getTopWeightRecords(userId: String, limit: Int): Flow<List<ExerciseHistoryStatsEntity>>

    @Query(
        "SELECT * FROM exercise_history_stats WHERE userId = :userId ORDER BY totalVolumeLifted DESC LIMIT :limit",
    )
    fun getTopVolumeRecords(userId: String, limit: Int): Flow<List<ExerciseHistoryStatsEntity>>

    @Query(
        "SELECT * FROM exercise_history_stats WHERE userId = :userId ORDER BY lastPerformanceDate DESC LIMIT :limit",
    )
    fun getRecentlyPerformedExercises(userId: String, limit: Int): Flow<List<ExerciseHistoryStatsEntity>>

    @Query("SELECT COUNT(*) FROM exercise_history_stats WHERE userId = :userId")
    suspend fun getUserExerciseCount(userId: String): Int

    @Query("SELECT SUM(totalVolumeLifted) FROM exercise_history_stats WHERE userId = :userId")
    suspend fun getTotalLifetimeVolume(userId: String): Double?

    @Query("SELECT SUM(totalSetsCompleted) FROM exercise_history_stats WHERE userId = :userId")
    suspend fun getTotalLifetimeSets(userId: String): Int?

    // ==================== 时间范围查询 ====================

    @Query(
        "SELECT * FROM exercise_history_stats WHERE userId = :userId AND lastPerformanceDate >= :startTime AND lastPerformanceDate < :endTime ORDER BY lastPerformanceDate DESC",
    )
    fun getStatsInPeriod(
        userId: String,
        startTime: Long,
        endTime: Long,
    ): Flow<List<ExerciseHistoryStatsEntity>>

    @Query(
        "SELECT COUNT(*) FROM exercise_history_stats WHERE userId = :userId AND lastPerformanceDate >= :startTime",
    )
    suspend fun getExerciseCountSince(userId: String, startTime: Long): Int
}
