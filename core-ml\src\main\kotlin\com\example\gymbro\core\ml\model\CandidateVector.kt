package com.example.gymbro.core.ml.model

/**
 * 候选向量 - Core-ML Layer Model
 *
 * @param id 向量唯一标识
 * @param vector 向量数据
 * @param metadata 附加元数据
 */
data class CandidateVector(
    val id: String,
    val vector: FloatArray,
    val metadata: Map<String, Any> = emptyMap(),
) {
    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as CandidateVector

        if (id != other.id) return false
        if (!vector.contentEquals(other.vector)) return false

        return true
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + vector.contentHashCode()
        return result
    }
}
