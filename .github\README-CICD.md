# 🚀 GymBro CI/CD 系统 (MVP版本)

## 📋 概述

GymBro项目采用MVP优化的CI/CD流程，专注于核心功能：APP构建运行 + coach/workout模块 + 代码质量。
暂时禁用服务器相关功能（Firebase、Google Play），确保开发团队能专注于核心业务逻辑。

## 🎯 MVP原则
- ✅ APP本地构建和运行
- ✅ Coach + Workout模块功能完整
- ✅ 代码质量保证（Ktlint + Detekt）
- ❌ Firebase服务（使用mock配置）
- ❌ Google Play发布（暂时禁用）
- ❌ 复杂的服务器集成

## 🏗️ 架构图1

```mermaid
graph TD
    A[开发者推送代码] --> B{触发条件}
    B -->|Push to main/develop| C[basic-ci-cd.yml]
    B -->|Pull Request| D[pr-checks.yml]
    B -->|Release| E[release.yml]

    C --> F[代码质量检查]
    C --> G[单元测试]
    C --> H[构建APK]
    H --> I[部署到内测]

    D --> J[PR验证]
    D --> K[快速检查]
    D --> L[增量测试]
    L --> M[PR评论]

    E --> N[发布验证]
    E --> O[构建签名版本]
    E --> P[部署到指定轨道]
    P --> Q[创建GitHub Release]
```

## 🔄 工作流程

### 1. 日常开发流程
```bash
# 1. 创建功能分支
git checkout -b feature/新功能名

# 2. 开发并提交（遵循Conventional Commits）
git commit -m "feat(auth): 添加用户登录功能"

# 3. 推送并创建PR
git push origin feature/新功能名
# 在GitHub创建Pull Request

# 4. 自动触发pr-checks.yml
# - PR标题和分支命名检查
# - 快速编译检查
# - 增量测试
# - 自动PR评论

# 5. 代码审查通过后合并
# 合并到develop分支触发basic-ci-cd.yml
```

### 2. 发布流程
```bash
# 方式1: GitHub Release触发
# 在GitHub创建Release，自动触发生产部署

# 方式2: 手动触发
# 在GitHub Actions页面手动运行release.yml
# 选择发布类型: internal/alpha/beta/production
```

## 📁 文件结构

```
.github/
├── workflows/
│   ├── pr-validation.yml        # PR验证流水线 (现代化v2.0)
│   ├── develop-integration.yml  # 分支集成流水线 (现代化v2.0)
│   ├── release-pipeline.yml     # 发布流水线 (现代化v2.0)
│   ├── nightly.yml             # 夜间回归测试 (现代化v2.0)
│   ├── e2e-testing.yml         # E2E端到端测试流水线 ✨新增
│   ├── benchmark-ci.yml        # 性能基准测试
│   └── docs_validation.yml     # 文档验证
├── ci-config.md                # CI/CD配置指南
└── README-CICD.md             # 本文件

ci-scripts/                     # ✨新增E2E测试脚本目录
├── e2e_workout_flow.sh        # Sprint 0 E2E测试脚本
├── sprint1_e2e_enhanced.sh     # Sprint 1 基础E2E测试脚本
└── sprint1_e2e_enhanced_v2.sh  # Sprint 1 增强E2E测试脚本 v2.0

docs/
├── github-environments-setup.md    # GitHub环境配置指南
├── github-secrets-checklist.md     # Secrets配置清单
└── paparazzi-setup-guide.md       # UI快照测试配置
```

## ⚙️ MVP快速设置

### 1. MVP验证（推荐）
```bash
# 运行MVP验证脚本
chmod +x scripts/mvp-ci-validation.sh
./scripts/mvp-ci-validation.sh
```

### 2. MVP快速测试
```bash
# 运行MVP快速测试
chmod +x scripts/mvp-quick-test.sh
./scripts/mvp-quick-test.sh
```

### 3. 传统设置（可选）
```bash
# 运行完整设置脚本（包含服务器功能）
chmod +x scripts/setup-ci.sh
./scripts/setup-ci.sh
```

### 4. 手动设置
参考 [CI/CD配置指南](.github/ci-config.md) 进行手动配置。

## 🔐 MVP GitHub Secrets配置

| Secret名称                        | 描述                        | MVP状态               |
| --------------------------------- | --------------------------- | --------------------- |
| `ANDROID_SIGNING_KEY`             | Android签名密钥Base64编码   | 可选（Release时需要） |
| `ANDROID_KEY_ALIAS`               | 密钥别名                    | 可选（Release时需要） |
| `ANDROID_KEYSTORE_PASSWORD`       | Keystore密码                | 可选（Release时需要） |
| `ANDROID_KEY_PASSWORD`            | 密钥密码                    | 可选（Release时需要） |
| ~~`GYMBRO_API_KEY`~~              | ~~GymBro API密钥~~          | ❌ MVP暂不需要         |
| ~~`FIREBASE_API_KEY`~~            | ~~Firebase API密钥~~        | ❌ MVP暂不需要         |
| ~~`GOOGLE_PLAY_SERVICE_ACCOUNT`~~ | ~~Google Play服务账号JSON~~ | ❌ MVP禁用             |

**MVP说明**: 大部分secrets在MVP阶段都不需要配置，CI/CD使用mock配置确保流程正常运行。

## 🎯 现代化工作流程详解

### pr-validation.yml - PR验证流水线 (现代化v2.0)
**触发条件**: PR到main/master/develop分支

**执行步骤**:
1. **模块变更检测** (30秒)
   - 使用dorny/paths-filter智能检测
   - 支持所有水平和垂直模块
   - 动态计算受影响模块

2. **并行质量检查** (3分钟)
   - Ktlint + Detekt + Android Lint
   - UI质量验证脚本
   - 现代化gradle/gradle-build-action缓存

3. **增量测试** (2分钟)
   - 仅测试受影响模块
   - 并行执行提升速度
   - 智能跳过无关测试

4. **构建验证** (1分钟)
   - 增量编译验证
   - APK构建（如app模块变更）
   - Build Scan发布

### develop-integration.yml - 分支集成流水线 (现代化v2.0)
**触发条件**: Push到develop/master分支

**执行步骤**:
1. **完整CI检查** (5分钟)
   - ciCheck统一任务
   - testAll全量测试
   - coverageCheck覆盖率验证

2. **UI快照测试** (2分钟)
   - Paparazzi自动检测和执行
   - 无模拟器依赖
   - 快照对比验证

3. **Firebase分发** (2分钟)
   - 自动分发到QA团队
   - 智能发布说明生成
   - 包含Build Scan链接

### release-pipeline.yml - 发布流水线 (现代化v2.0)
**触发条件**: GitHub Release发布、手动触发

**执行步骤**:
1. **发布验证** (1分钟)
   - 版本号格式验证
   - 发布类型确定
   - 环境权限检查

2. **Firebase Test Lab** (8分钟)
   - 多设备矩阵冒烟测试
   - Robo自动化测试
   - 智能结果分析

3. **Google Play部署** (3分钟)
   - 支持4个发布轨道
   - 环境保护规则
   - 自动发布说明生成

4. **GitHub Release** (2分钟)
   - 仅生产版本创建
   - APK/AAB自动上传
   - 详细发布说明

### nightly.yml - 夜间回归测试 (现代化v2.0)
**触发条件**: 定时触发、手动触发

**执行步骤**:
1. **安全扫描** (10分钟)
   - CodeQL静态安全分析
   - OWASP依赖漏洞扫描
   - 智能缓存优化

2. **覆盖率分析** (8分钟)
   - 全量测试执行
   - JaCoCo报告生成
   - Codecov集成

3. **Firebase回归** (15分钟)
   - 8设备矩阵测试
   - 英文+中文语言支持
   - 智能通过率计算

4. **E2E测试触发** (1分钟)
   - 自动触发完整E2E测试套件
   - 验证核心业务流程

5. **通知集成** (1分钟)
   - Slack自动通知
   - 详细报告生成
   - 趋势分析

### e2e-testing.yml - E2E端到端测试流水线 ✨新增
**触发条件**: Push/PR核心变更、定时触发、手动触发

**执行步骤**:
1. **Sprint 0 E2E测试** (15分钟)
   - AI聊天基础功能验证
   - Function Call Schema检查
   - Activeworkout集成验证
   - 状态恢复功能测试

2. **Sprint 1 E2E测试** (15分钟)
   - Function Calling端到端验证
   - 模板→训练会话转换
   - 训练数据持久化验证
   - 性能基准检查

3. **Sprint 1 Enhanced E2E v2.0** (15分钟)
   - 负面测试（错误响应处理）
   - API失败重试机制验证
   - 数据驱动多场景测试
   - 轮询检查机制（消除sleep依赖）
   - 质量门禁验证

4. **测试汇总报告** (2分钟)
   - 生成详细测试报告
   - 设置PR状态检查
   - 上传测试产物

## 📊 监控和报告

### 构建状态
- ✅ 所有检查通过：绿色状态
- ⚠️ 部分检查失败：黄色警告
- ❌ 构建失败：红色错误

### 报告内容
- 代码质量检查结果
- 测试覆盖率报告
- APK大小和构建信息
- 部署状态和链接

### 构建产物
- Debug APK（保留7天）
- Release APK/AAB（保留30天）
- 测试报告（保留7天）
- 代码质量报告（保留7天）

## 🔧 自定义配置

### 修改构建变体
在 `basic-ci-cd.yml` 中修改构建矩阵：
```yaml
strategy:
  matrix:
    variant: [Debug, Release, Staging]  # 添加新变体
```

### 调整测试策略
在 `pr-checks.yml` 中修改增量测试逻辑：
```bash
# 添加新模块检测
if echo "$CHANGED_FILES" | grep -q "^新模块/"; then
  MODULES_TO_TEST="$MODULES_TO_TEST :新模块"
fi
```

### 配置通知
添加Slack通知（可选）：
```yaml
- name: Slack通知
  uses: 8398a7/action-slack@v3
  with:
    status: ${{ job.status }}
    webhook_url: ${{ secrets.SLACK_WEBHOOK }}
```

## 🚨 故障排除

### 常见问题

#### 1. 构建失败
```bash
# 本地复现
./gradlew assembleDebug --stacktrace

# 检查依赖
./gradlew dependencies
```

#### 2. 测试失败
```bash
# 运行特定测试
./gradlew :domain:testDebugUnitTest

# 查看测试报告
open domain/build/reports/tests/testDebugUnitTest/index.html
```

#### 3. 部署失败
- 检查Google Play Console权限
- 验证包名和签名
- 确认版本号递增

### 调试技巧
1. 查看GitHub Actions日志
2. 本地运行相同命令
3. 检查环境变量配置
4. 验证Secrets设置

## 📈 性能优化

### 构建优化
- 使用Gradle缓存
- 并行构建策略
- 增量编译配置

### 测试优化
- 增量测试策略
- 模块化测试执行
- 测试结果缓存

## 🔗 相关链接

- [CI/CD配置指南](.github/ci-config.md)
- [项目架构文档](../docs/technical/架构规范.md)
- [编码规范](../docs/technical/编码与风格指南.md)
- [GitHub Actions文档](https://docs.github.com/en/actions)

## 🤝 贡献指南

### 修改CI/CD流程
1. 在feature分支中修改workflow文件
2. 创建PR测试变更
3. 确保不影响现有流程
4. 更新相关文档

### 添加新检查
1. 在对应workflow中添加步骤
2. 确保错误处理正确
3. 添加适当的报告
4. 更新文档说明

---

**维护团队**: DevOps团队
**最后更新**: 2025-01-28
**版本**: v1.0
