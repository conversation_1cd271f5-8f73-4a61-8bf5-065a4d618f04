package com.example.gymbro.core.error.types.business

import com.example.gymbro.core.error.types.ErrorCategory
import com.example.gymbro.core.error.types.ErrorSeverity
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.StandardKeys
import com.example.gymbro.core.ui.text.UiText

/**
 * 业务相关错误类型集合
 *
 * 包含业务逻辑、订阅管理和支付处理相关的错误处理
 * 从 DomainErrors.kt 迁移而来，专门处理业务层错误
 */
object BusinessErrors {

    /**
     * 业务错误类，用于表示业务逻辑相关的错误
     */
    class BusinessError private constructor(
        val operationName: String,
        val customUiMessage: UiText,
        val errorType: GlobalErrorType = GlobalErrorType.Business.Rule,
        val customCategory: ErrorCategory = ErrorCategory.BUSINESS,
        val customSeverity: ErrorSeverity = ErrorSeverity.ERROR,
        val customRecoverable: Boolean = false,
        val customCause: Throwable? = null,
        val customMetadataMap: Map<String, Any> = emptyMap(),
    ) {
        /**
         * 转换为ModernDataError
         */
        fun toModernDataError(): ModernDataError {
            return ModernDataError(
                operationName = operationName,
                errorType = errorType,
                category = customCategory,
                uiMessage = customUiMessage,
                severity = customSeverity,
                recoverable = customRecoverable,
                cause = customCause,
                metadataMap = customMetadataMap,
            )
        }

        companion object {
            /**
             * 创建一个业务规则错误
             */
            fun rule(
                operationName: String = "BusinessRule",
                message: UiText,
                cause: Throwable? = null,
                recoverable: Boolean = false,
                metadataMap: Map<String, Any> = emptyMap(),
            ): ModernDataError =
                BusinessError(
                    operationName = operationName,
                    customUiMessage = message,
                    errorType = GlobalErrorType.Business.Rule,
                    customCategory = ErrorCategory.BUSINESS,
                    customSeverity = ErrorSeverity.ERROR,
                    customRecoverable = recoverable,
                    customCause = cause,
                    customMetadataMap = metadataMap,
                ).toModernDataError()

            /**
             * 创建一个业务实体未找到错误
             */
            fun notFound(
                operationName: String = "BusinessNotFound",
                message: UiText,
                entityType: String? = null,
                entityId: String? = null,
                cause: Throwable? = null,
                recoverable: Boolean = false,
                metadataMap: Map<String, Any> = emptyMap(),
            ): ModernDataError {
                val metadataWithEntityInfo = metadataMap.toMutableMap().apply {
                    entityType?.let { put("entity_type", it) }
                    entityId?.let { put("entity_id", it) }
                }
                return BusinessError(
                    operationName = operationName,
                    customUiMessage = message,
                    errorType = GlobalErrorType.Business.NotFound,
                    customCategory = ErrorCategory.BUSINESS,
                    customSeverity = ErrorSeverity.ERROR,
                    customRecoverable = recoverable,
                    customCause = cause,
                    customMetadataMap = metadataWithEntityInfo,
                ).toModernDataError()
            }

            /**
             * 创建一个业务冲突错误
             */
            fun conflict(
                operationName: String = "BusinessConflict",
                message: UiText,
                cause: Throwable? = null,
                recoverable: Boolean = true,
                metadataMap: Map<String, Any> = emptyMap(),
            ): ModernDataError =
                BusinessError(
                    operationName = operationName,
                    customUiMessage = message,
                    errorType = GlobalErrorType.Business.Conflict,
                    customCategory = ErrorCategory.BUSINESS,
                    customSeverity = ErrorSeverity.WARNING,
                    customRecoverable = recoverable,
                    customCause = cause,
                    customMetadataMap = metadataMap,
                ).toModernDataError()
        }
    }

    /**
     * 订阅错误类
     */
    class SubscriptionError private constructor(
        val operationName: String,
        val uiMessage: UiText,
        val errorType: GlobalErrorType.Subscription,
        val severity: ErrorSeverity = ErrorSeverity.ERROR,
        val recoverable: Boolean = false,
        val cause: Throwable? = null,
        val errorSubtype: String? = null,
        val subscriptionPlan: String? = null,
        val metadataMap: Map<String, Any> = emptyMap(),
    ) {
        /**
         * 转换为ModernDataError
         */
        fun toModernDataError(): ModernDataError {
            return ModernDataError(
                operationName = operationName,
                errorType = errorType,
                category = ErrorCategory.BUSINESS,
                uiMessage = uiMessage,
                severity = severity,
                recoverable = recoverable,
                cause = cause,
                metadataMap = createMetadataMap(errorSubtype, subscriptionPlan, metadataMap),
            )
        }

        companion object {
            /**
             * 创建元数据映射
             */
            private fun createMetadataMap(
                errorSubtype: String?,
                subscriptionPlan: String?,
                baseMetadata: Map<String, Any>,
            ): Map<String, Any> {
                val resultMap = baseMetadata.toMutableMap()
                errorSubtype?.let { resultMap[StandardKeys.ERROR_SUBTYPE.key] = it }
                subscriptionPlan?.let { resultMap[StandardKeys.SUBSCRIPTION_PLAN.key] = it }
                return resultMap
            }

            /**
             * 创建未订阅错误
             */
            fun notSubscribed(
                operationName: String = "SubscriptionNotSubscribed",
                message: UiText,
                errorSubtype: String? = null, // 例如 "trial_ended", "never_subscribed", "cancelled"
                subscriptionPlan: String? = null,
                featureId: String? = null,
                metadataMap: Map<String, Any> = emptyMap(),
            ): ModernDataError {
                val metadata = mutableMapOf<String, Any>()
                metadata.putAll(metadataMap)
                featureId?.let { metadata[StandardKeys.FEATURE_ID.key] = it }

                return SubscriptionError(
                    operationName = operationName,
                    uiMessage = message,
                    errorType = GlobalErrorType.Subscription.NotSubscribed,
                    errorSubtype = errorSubtype,
                    subscriptionPlan = subscriptionPlan,
                    metadataMap = metadata,
                ).toModernDataError()
            }

            /**
             * 创建订阅过期错误
             */
            fun expired(
                operationName: String = "SubscriptionExpired",
                message: UiText,
                expiryTime: Long? = null,
                subscriptionPlan: String? = null,
                metadataMap: Map<String, Any> = emptyMap(),
            ): ModernDataError {
                val metadata = mutableMapOf<String, Any>()
                metadata.putAll(metadataMap)
                expiryTime?.let { metadata[StandardKeys.EXPIRY_TIME.key] = it }

                return SubscriptionError(
                    operationName = operationName,
                    uiMessage = message,
                    errorType = GlobalErrorType.Subscription.Expired,
                    errorSubtype = "expired",
                    subscriptionPlan = subscriptionPlan,
                    metadataMap = metadata,
                ).toModernDataError()
            }
        }
    }

    /**
     * 支付错误类
     */
    class PaymentError private constructor(
        val operationName: String,
        val uiMessage: UiText,
        val errorType: GlobalErrorType.Payment,
        val severity: ErrorSeverity = ErrorSeverity.ERROR,
        val recoverable: Boolean = false,
        val cause: Throwable? = null,
        val errorSubtype: String? = null,
        val paymentMethod: String? = null,
        val transactionId: String? = null,
        val metadataMap: Map<String, Any> = emptyMap(),
    ) {
        /**
         * 转换为ModernDataError
         */
        fun toModernDataError(): ModernDataError {
            return ModernDataError(
                operationName = operationName,
                errorType = errorType,
                category = ErrorCategory.BUSINESS,
                uiMessage = uiMessage,
                severity = severity,
                recoverable = recoverable,
                cause = cause,
                metadataMap = createMetadataMap(errorSubtype, paymentMethod, transactionId, metadataMap),
            )
        }

        companion object {
            /**
             * 创建包含支付详情的元数据映射
             */
            private fun createMetadataMap(
                errorSubtype: String?,
                paymentMethod: String?,
                transactionId: String?,
                baseMetadata: Map<String, Any>,
            ): Map<String, Any> {
                val resultMap = baseMetadata.toMutableMap()
                errorSubtype?.let { resultMap[StandardKeys.ERROR_SUBTYPE.key] = it }
                paymentMethod?.let { resultMap[StandardKeys.PAYMENT_METHOD.key] = it }
                transactionId?.let { resultMap[StandardKeys.TRANSACTION_ID.key] = it }
                return resultMap
            }

            /**
             * 创建支付处理失败错误
             */
            fun processingFailed(
                operationName: String = "PaymentError.processingFailed",
                message: UiText,
                errorSubtype: String? = null,
                paymentMethod: String? = null,
                transactionId: String? = null,
                cause: Throwable? = null,
                metadataMap: Map<String, Any> = emptyMap(),
            ): ModernDataError = PaymentError(
                operationName = operationName,
                uiMessage = message,
                errorType = GlobalErrorType.Payment.ProcessingFailed,
                errorSubtype = errorSubtype,
                paymentMethod = paymentMethod,
                transactionId = transactionId,
                cause = cause,
                metadataMap = metadataMap,
            ).toModernDataError()

            /**
             * 创建无效支付方式错误
             */
            fun invalidPaymentMethod(
                operationName: String = "PaymentError.invalidPaymentMethod",
                message: UiText,
                paymentMethod: String? = null,
                errorSubtype: String? = null,
                cause: Throwable? = null,
                metadataMap: Map<String, Any> = emptyMap(),
            ): ModernDataError = PaymentError(
                operationName = operationName,
                uiMessage = message,
                errorType = GlobalErrorType.Payment.InvalidPaymentMethod,
                errorSubtype = errorSubtype,
                paymentMethod = paymentMethod,
                cause = cause,
                metadataMap = metadataMap,
            ).toModernDataError()
        }
    }
}
