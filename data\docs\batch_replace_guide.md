# GymBro Data层编译错误批量替换指南

## 📋 概述
由于IDEA文件移动操作未响应，导致大量import引用错误。本文档提供IDEA批量替换的完整方案。

## 🔧 批量替换操作步骤

### 1. AI相关类的包路径修复

#### 1.1 FunctionDefinition类引用修复
**错误引用**: `com.example.gymbro.data.remote.ai.dto.FunctionDefinition`
**正确引用**: `com.example.gymbro.data.ai.dto.FunctionDefinition`

**IDEA批量替换**:
- 查找: `com.example.gymbro.data.remote.ai.dto.FunctionDefinition`
- 替换为: `com.example.gymbro.data.ai.dto.FunctionDefinition`

#### 1.2 ChatRequest类引用修复
**错误引用**: `com.example.gymbro.data.remote.ai.dto.ChatRequest`
**正确引用**: `com.example.gymbro.data.ai.dto.ChatRequest`

**IDEA批量替换**:
- 查找: `com.example.gymbro.data.remote.ai.dto.ChatRequest`
- 替换为: `com.example.gymbro.data.ai.dto.ChatRequest`

#### 1.3 StreamingChatResponse类引用修复
**错误引用**: `com.example.gymbro.data.remote.ai.dto.StreamingChatResponse`
**正确引用**: `com.example.gymbro.data.ai.dto.StreamingChatResponse`

**IDEA批量替换**:
- 查找: `com.example.gymbro.data.remote.ai.dto.StreamingChatResponse`
- 替换为: `com.example.gymbro.data.ai.dto.StreamingChatResponse`

#### 1.4 StreamEvent类引用修复
**错误引用**: `com.example.gymbro.data.remote.ai.dto.StreamEvent`
**正确引用**: `com.example.gymbro.data.ai.dto.StreamEvent`

**IDEA批量替换**:
- 查找: `com.example.gymbro.data.remote.ai.dto.StreamEvent`
- 替换为: `com.example.gymbro.data.ai.dto.StreamEvent`

#### 1.5 StreamingEvent类引用修复
**错误引用**: `com.example.gymbro.data.remote.ai.dto.StreamingEvent`
**正确引用**: `com.example.gymbro.data.ai.dto.StreamingEvent`

**IDEA批量替换**:
- 查找: `com.example.gymbro.data.remote.ai.dto.StreamingEvent`
- 替换为: `com.example.gymbro.data.ai.dto.StreamingEvent`

#### 1.6 StreamingEventType类引用修复
**错误引用**: `com.example.gymbro.data.remote.ai.dto.StreamingEventType`
**正确引用**: `com.example.gymbro.data.ai.dto.StreamingEventType`

**IDEA批量替换**:
- 查找: `com.example.gymbro.data.remote.ai.dto.StreamingEventType`
- 替换为: `com.example.gymbro.data.ai.dto.StreamingEventType`

### 2. Coach相关类的包路径修复

#### 2.1 CoachMessage类引用修复
**错误引用**: `com.example.gymbro.domain.coach.model.CoachMessage`
**正确引用**: `com.example.gymbro.domain.model.coach.CoachMessage`

**IDEA批量替换**:
- 查找: `com.example.gymbro.domain.coach.model.CoachMessage`
- 替换为: `com.example.gymbro.domain.model.coach.CoachMessage`

#### 2.2 StreamEvent类引用修复（Domain层）
**错误引用**: `com.example.gymbro.domain.coach.model.StreamEvent`
**正确引用**: `com.example.gymbro.domain.model.coach.StreamEvent`

**IDEA批量替换**:
- 查找: `com.example.gymbro.domain.coach.model.StreamEvent`
- 替换为: `com.example.gymbro.domain.model.coach.StreamEvent`

### 3. 搜索相关类的修复

#### 3.1 SearchResult构造函数修复
**问题**: SearchResult缺少必需的contentType参数

**修复方案**: 在SearchMapper.kt中添加contentType参数

**文件**: `data/src/main/kotlin/com/example/gymbro/data/coach/mapper/SearchMapper.kt`

**第35行修复**:
```kotlin
// 修复前
SearchResult(
    id = hit.id.toString(),
    content = hit.highlight ?: detail.content,
    score = hit.score,
    metadata = detail.metadata,
    timestamp = detail.createdAt
)

// 修复后
SearchResult(
    id = hit.id.toString(),
    content = hit.highlight ?: detail.content,
    score = hit.score,
    contentType = "chat_message", // 添加此行
    metadata = detail.metadata,
    timestamp = detail.createdAt
)
```

**第58行修复**:
```kotlin
// 修复前
SearchResult(
    id = hit.id.toString(),
    content = detail.content,
    score = hit.score,
    metadata = detail.metadata,
    timestamp = detail.createdAt
)

// 修复后
SearchResult(
    id = hit.id.toString(),
    content = detail.content,
    score = hit.score,
    contentType = "chat_message", // 添加此行
    metadata = detail.metadata,
    timestamp = detail.createdAt
)
```

### 4. 数据库相关方法修复

#### 4.1 ExerciseDao方法名修复
**问题**: `getExerciseCount`和`insertExercises`方法不存在

**文件**: `data/src/main/kotlin/com/example/gymbro/data/workout/initializer/ExerciseDataInitializer.kt`

**第30行修复**:
```kotlin
// 修复前
val existingCount = exerciseDao.getExerciseCount()

// 修复后
val existingCount = exerciseDao.getExerciseCountByUserId("system") // 或使用适当的用户ID
```

**第41行修复**:
```kotlin
// 修复前
exerciseDao.insertExercises(exerciseEntities)

// 修复后
exerciseEntities.forEach { exerciseDao.insertExercise(it) }
```

#### 4.2 Intent.setAction修复
**问题**: Intent.setAction语法错误

**文件**: `data/src/main/kotlin/com/example/gymbro/data/workout/repository/TimerRepositoryImpl.kt`

**第418行修复**:
```kotlin
// 修复前
Intent.setAction = "ACTION_SHOW_OVERLAY"

// 修复后
action = "ACTION_SHOW_OVERLAY"
```

**第441行修复**:
```kotlin
// 修复前
Intent.setAction = "ACTION_HIDE_OVERLAY"

// 修复后
action = "ACTION_HIDE_OVERLAY"
```

**第470行修复**:
```kotlin
// 修复前
Intent.setAction = actionString

// 修复后
action = actionString
```

### 5. 其他包路径修复

#### 5.1 VssHit和FtsHit类型修复
**文件**: `data/src/main/kotlin/com/example/gymbro/data/coach/repository/search/SearchRepositoryImpl.kt`

**第144行修复**:
```kotlin
// 修复前
searchMapper.mapVssToSearchResults(vssHits, details)

// 修复后
searchMapper.mapVssToSearchResults(vssHits.map { VssHit(it.id, it.score) }, details)
```

## 🚀 执行顺序

### 步骤1: 全局包路径替换
1. 打开IDEA的"Replace in Path"功能 (Ctrl+Shift+R)
2. 设置范围为整个data模块
3. 按照上述顺序逐一执行包路径替换

### 步骤2: 手动修复特定文件
1. SearchMapper.kt - 添加contentType参数
2. ExerciseDataInitializer.kt - 修复DAO方法调用
3. TimerRepositoryImpl.kt - 修复Intent.setAction语法
4. SearchRepositoryImpl.kt - 修复类型转换

### 步骤3: 验证修复
1. 执行gradle clean
2. 执行gradle build
3. 检查编译错误是否解决

## ⚠️ 注意事项

1. **备份代码**: 执行批量替换前请确保代码已提交到版本控制
2. **分批执行**: 建议分批执行替换，每次替换后检查结果
3. **测试验证**: 修复后运行相关测试确保功能正常
4. **依赖检查**: 确保所有依赖的模块都已正确更新

## 📝 修复完成检查清单

- [ ] AI相关类包路径修复完成
- [ ] Coach相关类包路径修复完成  
- [ ] SearchResult构造函数修复完成
- [ ] ExerciseDao方法调用修复完成
- [ ] Intent.setAction语法修复完成
- [ ] 编译错误全部解决
- [ ] 相关测试通过
- [ ] 功能验证正常

## 🔍 常见问题

**Q: 替换后仍有编译错误怎么办？**
A: 检查是否有遗漏的引用，使用IDEA的"Find Usages"功能查找所有引用

**Q: 如何确认包路径是否正确？**
A: 在IDEA中按Ctrl+点击类名，查看实际的类定义位置

**Q: 批量替换影响了其他模块怎么办？**
A: 限制替换范围仅在data模块内，避免影响其他模块