package com.example.gymbro.designSystem.components

import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.statusBars
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.stringResource
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.R
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme

/**
 * GymBro应用顶部应用栏
 *
 * 支持响应式设计，在大屏幕设备上提供更好的布局体验
 * 优化了性能，减少不必要的文本转换操作
 *
 * 特性：
 * - 响应式设计，支持大屏幕适配
 * - 性能优化的文本处理
 * - 统一的Material 3 设计语言
 * - 可选的导航返回按钮
 * - 灵活的操作按钮插槽
 *
 * @param title 标题文本 (UiText)
 * @param modifier Modifier修饰符
 * @param onNavigateBack 导航回调，如果为null则不显示返回按钮
 * @param actions 顶部应用栏右侧操作按钮
 * @param enableLargeScreenAdaptation 是否启用大屏幕适配
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GymBroTopAppBar(
    title: UiText,
    modifier: Modifier = Modifier,
    onNavigateBack: (() -> Unit)? = null,
    actions: @Composable RowScope.() -> Unit = {},
    enableLargeScreenAdaptation: Boolean = true,
) {
    // 性能优化：缓存标题文本转换
    val titleText = title.asString()

    // 大屏幕适配：根据屏幕尺寸调整布局
    val configuration = LocalConfiguration.current
    val isLargeScreen = enableLargeScreenAdaptation && configuration.screenWidthDp >= 840

    // 大屏幕适配：调整内边距和高度
    val topAppBarColors = TopAppBarDefaults.centerAlignedTopAppBarColors(
        containerColor = MaterialTheme.colorScheme.surface,
        titleContentColor = MaterialTheme.colorScheme.onSurface,
        navigationIconContentColor = MaterialTheme.colorScheme.onSurface,
        actionIconContentColor = MaterialTheme.colorScheme.onSurface,
    )

    CenterAlignedTopAppBar(
        title = {
            Text(
                text = titleText,
                style = if (isLargeScreen) {
                    MaterialTheme.typography.titleLarge
                } else {
                    MaterialTheme.typography.titleMedium
                },
            )
        },
        navigationIcon = {
            if (onNavigateBack != null) {
                IconButton(onClick = onNavigateBack) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                        contentDescription = stringResource(R.string.content_description_back),
                    )
                }
            }
        },
        actions = actions,
        colors = topAppBarColors,
        windowInsets = if (isLargeScreen) {
            // 大屏幕：增加顶部间距
            WindowInsets.statusBars
        } else {
            TopAppBarDefaults.windowInsets
        },
        modifier = modifier,
    )
}

/**
 * 简化版GymBroTopAppBar，用于不需要大屏幕适配的场景
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GymBroTopAppBarSimple(
    title: UiText,
    modifier: Modifier = Modifier,
    onNavigateBack: (() -> Unit)? = null,
    actions: @Composable RowScope.() -> Unit = {},
) {
    GymBroTopAppBar(
        title = title,
        modifier = modifier,
        onNavigateBack = onNavigateBack,
        actions = actions,
        enableLargeScreenAdaptation = false,
    )
}

// === Preview组件 - 标准化预览 ===

@GymBroPreview
@Composable
private fun GymBroTopAppBarSimplePreview() {
    GymBroTheme {
        GymBroTopAppBar(
            title = UiText.DynamicString("首页"),
            onNavigateBack = { },
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroTopAppBarWithActionsPreview() {
    GymBroTheme {
        GymBroTopAppBar(
            title = UiText.DynamicString("训练记录"),
            onNavigateBack = { },
            actions = {
                IconButton(onClick = { }) {
                    Icon(
                        imageVector = Icons.Default.MoreVert,
                        contentDescription = stringResource(id = R.string.content_description_more_options),
                    )
                }
            },
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroTopAppBarNoBackPreview() {
    GymBroTheme {
        GymBroTopAppBar(
            title = UiText.StringResource(R.string.app_name, emptyList()),
            onNavigateBack = null,
            actions = {
                IconButton(onClick = { }) {
                    Icon(
                        imageVector = Icons.Default.MoreVert,
                        contentDescription = stringResource(id = R.string.content_description_more_options),
                    )
                }
            },
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroTopAppBarLargeScreenPreview() {
    GymBroTheme {
        // 模拟大屏幕场景
        GymBroTopAppBar(
            title = UiText.DynamicString("大屏幕模式"),
            onNavigateBack = { },
            enableLargeScreenAdaptation = true,
        )
    }
}
