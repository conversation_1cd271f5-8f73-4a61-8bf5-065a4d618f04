#!/bin/bash
# Sprint 1 增强版 E2E 测试脚本
# 验证: AI模板生成 → 计划创建 → 训练会话 → 数据持久化 完整闭环

set -e

echo "🚀 Sprint 1 E2E验证开始 - $(date '+%Y-%m-%d %H:%M:%S')"

# 环境配置
TEST_BASE_URL="${TEST_BASE_URL:-http://localhost:8080}"
TEST_USER_ID="${TEST_USER_ID:-test_user_$(date +%s)}"
LOG_FILE="/tmp/sprint1_e2e_$(date +%Y%m%d_%H%M%S).log"

# 性能指标收集
declare -A METRICS
START_TIME=$(date +%s.%3N)

# 辅助函数
log_info() {
    echo "ℹ️  $1" | tee -a $LOG_FILE
}

log_error() {
    echo "❌ $1" | tee -a $LOG_FILE
}

log_success() {
    echo "✅ $1" | tee -a $LOG_FILE
}

measure_time() {
    local start_time=$1
    local end_time=$(date +%s.%3N)
    echo "scale=3; $end_time - $start_time" | bc
}

# 验证Function Calling端到端
test_function_calling() {
    log_info "测试1: Function Calling AI模板生成"

    local fc_start=$(date +%s.%3N)

    # 发送AI模板生成请求
    local response=$(curl -s -X POST $TEST_BASE_URL/api/coach/generate-template \
        -H "Content-Type: application/json" \
        -H "X-User-ID: $TEST_USER_ID" \
        -d '{
            "prompt": "帮我制定一个45分钟的胸肌+三头肌训练计划，我是中级水平",
            "userId": "'$TEST_USER_ID'",
            "preferences": {
                "duration": 45,
                "level": "intermediate",
                "equipment": ["barbell", "dumbbell", "cable"]
            }
        }')

    # 验证Function Call响应
    if ! echo "$response" | jq -e '.function_call' > /dev/null; then
        log_error "Function Calling未触发，响应: $response"
        return 1
    fi

    # 提取并验证arguments
    local arguments=$(echo "$response" | jq -r '.function_call.arguments')
    echo "$arguments" > /tmp/generated_template.json

    # Schema验证
    if ! python scripts/validate_template_schema.py /tmp/generated_template.json; then
        log_error "Template Schema验证失败"
        return 1
    fi

    local fc_time=$(measure_time $fc_start)
    METRICS[function_calling_latency]=$fc_time
    log_success "Function Calling完成，耗时: ${fc_time}s"

    # 验证关键字段
    local template_name=$(echo "$arguments" | jq -r '.template.name')
    local exercise_count=$(echo "$arguments" | jq -r '.template.exercises | length')

    if [[ $exercise_count -lt 3 ]]; then
        log_error "生成的训练动作数量不足: $exercise_count < 3"
        return 1
    fi

    log_success "AI模板生成验证通过: '$template_name' ($exercise_count个动作)"
    return 0
}

# 验证模板→训练会话转换
test_template_to_session() {
    log_info "测试2: 模板→训练会话转换"

    local conversion_start=$(date +%s.%3N)

    # 读取生成的模板
    local template_data=$(cat /tmp/generated_template.json)
    local template_id=$(echo "$template_data" | jq -r '.template.id // "generated_template"')

    # 创建训练会话
    local session_response=$(curl -s -X POST $TEST_BASE_URL/api/workout/sessions \
        -H "Content-Type: application/json" \
        -H "X-User-ID: $TEST_USER_ID" \
        -d '{
            "templateData": '$template_data',
            "userId": "'$TEST_USER_ID'",
            "scheduledDate": "'$(date -I)'",
            "source": "COACH_AI"
        }')

    # 验证会话创建
    local session_id=$(echo "$session_response" | jq -r '.sessionId')
    if [[ "$session_id" == "null" || -z "$session_id" ]]; then
        log_error "训练会话创建失败: $session_response"
        return 1
    fi

    echo "$session_response" > /tmp/created_session.json

    local conversion_time=$(measure_time $conversion_start)
    METRICS[template_conversion_latency]=$conversion_time
    log_success "训练会话创建成功: $session_id (耗时: ${conversion_time}s)"

    return 0
}

# 验证ActiveWorkout状态管理
test_active_workout_state() {
    log_info "测试3: ActiveWorkout状态管理"

    local session_id=$(cat /tmp/created_session.json | jq -r '.sessionId')

    # 启动Espresso测试验证UI状态
    log_info "运行Android仪表化测试..."
    ./gradlew :app:connectedAndroidTest \
        -Pandroid.testInstrumentationRunnerArguments.class=com.example.gymbro.e2e.ActiveWorkoutE2ETest \
        -Pandroid.testInstrumentationRunnerArguments.session_id=$session_id \
        --no-daemon

    if [[ $? -ne 0 ]]; then
        log_error "ActiveWorkout UI测试失败"
        return 1
    fi

    # 验证SavedStateHandle恢复
    log_info "验证进程重启状态恢复..."
    adb shell am force-stop com.example.gymbro
    sleep 2
    adb shell am start -n com.example.gymbro/.MainActivity \
        --es "session_id" "$session_id"

    # 等待应用启动并验证状态
    sleep 3
    local recovered_state=$(adb shell content query \
        --uri content://com.example.gymbro.provider/workout_sessions \
        --where "id='$session_id'" \
        --projection "status,current_exercise_index")

    if [[ "$recovered_state" == *"ACTIVE"* ]]; then
        log_success "进程重启状态恢复验证通过"
    else
        log_error "进程重启状态恢复失败: $recovered_state"
        return 1
    fi

    return 0
}

# 验证训练数据持久化
test_workout_data_persistence() {
    log_info "测试4: 训练数据持久化验证"

    local session_id=$(cat /tmp/created_session.json | jq -r '.sessionId')

    # 模拟训练数据录入
    local workout_data='{
        "sessionId": "'$session_id'",
        "exercises": [
            {
                "exerciseCode": "bench_press",
                "sets": [
                    {"reps": 8, "weight": 80.0, "restDuration": 90},
                    {"reps": 8, "weight": 82.5, "restDuration": 90},
                    {"reps": 6, "weight": 85.0, "restDuration": 120}
                ]
            },
            {
                "exerciseCode": "incline_dumbbell_press",
                "sets": [
                    {"reps": 10, "weight": 30.0, "restDuration": 75},
                    {"reps": 9, "weight": 32.5, "restDuration": 75}
                ]
            }
        ]
    }'

    # 提交训练数据
    local persist_response=$(curl -s -X POST $TEST_BASE_URL/api/workout/sessions/$session_id/data \
        -H "Content-Type: application/json" \
        -H "X-User-ID: $TEST_USER_ID" \
        -d "$workout_data")

    # 验证数据写入
    local saved_data=$(curl -s -X GET $TEST_BASE_URL/api/workout/sessions/$session_id \
        -H "X-User-ID: $TEST_USER_ID")

    local total_sets=$(echo "$saved_data" | jq '[.exercises[].sets] | add | length')
    if [[ $total_sets -ne 5 ]]; then
        log_error "训练数据持久化失败，预期5组，实际: $total_sets"
        return 1
    fi

    # 验证元数据标记
    local source=$(echo "$saved_data" | jq -r '.source')
    if [[ "$source" != "COACH_AI" ]]; then
        log_error "Coach元数据标记错误，预期: COACH_AI，实际: $source"
        return 1
    fi

    log_success "训练数据持久化验证通过 (5组数据，source=COACH_AI)"
    return 0
}

# 性能基准验证
test_performance_benchmarks() {
    log_info "测试5: 性能基准验证"

    local total_time=$(measure_time $START_TIME)
    METRICS[end_to_end_latency]=$total_time

    # 验证端到端时间≤30秒
    if (( $(echo "$total_time > 30" | bc -l) )); then
        log_error "端到端时间超标: ${total_time}s > 30s"
        return 1
    fi

    # 检查Function Calling成功率
    local func_call_success_rate=$(curl -s "$TEST_BASE_URL/metrics" | \
        grep 'func_call_success_rate' | awk '{print $2}')

    if [[ -n "$func_call_success_rate" ]] && \
       (( $(echo "$func_call_success_rate < 0.90" | bc -l) )); then
        log_error "Function Calling成功率低于90%: ${func_call_success_rate}"
        return 1
    fi

    # 检查Schema错误率
    local schema_error_rate=$(curl -s "$TEST_BASE_URL/metrics" | \
        grep 'schema_error_rate' | awk '{print $2}')

    if [[ -n "$schema_error_rate" ]] && \
       (( $(echo "$schema_error_rate > 0.05" | bc -l) )); then
        log_error "Schema错误率超过5%: ${schema_error_rate}"
        return 1
    fi

    log_success "性能基准验证通过 (端到端: ${total_time}s)"
    return 0
}

# 生成测试报告
generate_test_report() {
    local report_file="/tmp/sprint1_e2e_report_$(date +%Y%m%d_%H%M%S).json"

    cat > $report_file << EOF
{
    "timestamp": "$(date -Iseconds)",
    "sprint": "Sprint 1",
    "test_user_id": "$TEST_USER_ID",
    "metrics": {
        "function_calling_latency": ${METRICS[function_calling_latency]:-0},
        "template_conversion_latency": ${METRICS[template_conversion_latency]:-0},
        "end_to_end_latency": ${METRICS[end_to_end_latency]:-0}
    },
    "test_results": {
        "function_calling": $test_function_calling_result,
        "template_conversion": $test_template_conversion_result,
        "active_workout_state": $test_active_workout_result,
        "data_persistence": $test_data_persistence_result,
        "performance_benchmarks": $test_performance_result
    },
    "artifacts": {
        "generated_template": "/tmp/generated_template.json",
        "created_session": "/tmp/created_session.json",
        "full_log": "$LOG_FILE"
    }
}
EOF

    log_info "测试报告生成: $report_file"
    echo "$report_file"
}

# 主测试流程
main() {
    log_info "开始Sprint 1完整E2E验证流程"
    log_info "测试环境: $TEST_BASE_URL"
    log_info "测试用户: $TEST_USER_ID"

    # 执行测试套件
    test_function_calling && test_function_calling_result=true || test_function_calling_result=false
    test_template_to_session && test_template_conversion_result=true || test_template_conversion_result=false
    test_active_workout_state && test_active_workout_result=true || test_active_workout_result=false
    test_workout_data_persistence && test_data_persistence_result=true || test_data_persistence_result=false
    test_performance_benchmarks && test_performance_result=true || test_performance_result=false

    # 生成报告
    local report_file=$(generate_test_report)

    # 总结
    local passed_tests=$(cat $report_file | jq '[.test_results[] | select(. == true)] | length')
    local total_tests=5

    if [[ $passed_tests -eq $total_tests ]]; then
        log_success "🎉 Sprint 1 E2E验证完成: $passed_tests/$total_tests 通过"
        log_success "端到端时间: ${METRICS[end_to_end_latency]}s (目标: ≤30s)"
        exit 0
    else
        log_error "💥 Sprint 1 E2E验证失败: $passed_tests/$total_tests 通过"
        log_error "请检查失败的测试用例和日志: $LOG_FILE"
        exit 1
    fi
}

# 清理函数
cleanup() {
    log_info "清理测试环境..."
    # 清理测试数据
    if [[ -n "$TEST_USER_ID" ]]; then
        curl -s -X DELETE "$TEST_BASE_URL/api/test/cleanup/$TEST_USER_ID" || true
    fi
}

# 设置清理钩子
trap cleanup EXIT

# 启动主流程
main "$@"
