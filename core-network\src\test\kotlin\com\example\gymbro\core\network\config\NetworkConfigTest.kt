package com.example.gymbro.core.network.config

import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

/**
 * NetworkConfig测试类
 *
 * 验证URL处理逻辑，特别是自动添加末尾斜杠的功能
 */
class NetworkConfigTest {

    @Nested
    @DisplayName("getSecureRestUrl() 测试")
    inner class GetSecureRestUrlTest {

        @Test
        @DisplayName("应该自动添加末尾斜杠")
        fun `should automatically add trailing slash`() {
            // Given
            val config = NetworkConfig(restBase = "https://api.example.com")

            // When
            val result = config.getSecureRestUrl()

            // Then
            assertEquals("https://api.example.com/", result)
        }

        @Test
        @DisplayName("已有末尾斜杠时不应重复添加")
        fun `should not add duplicate trailing slash`() {
            // Given
            val config = NetworkConfig(restBase = "https://api.example.com/")

            // When
            val result = config.getSecureRestUrl()

            // Then
            assertEquals("https://api.example.com/", result)
        }

        @Test
        @DisplayName("应该将http协议转换为https并添加斜杠")
        fun `should convert http to https and add slash`() {
            // Given
            val config = NetworkConfig(restBase = "http://api.example.com")

            // When
            val result = config.getSecureRestUrl()

            // Then
            assertEquals("https://api.example.com/", result)
        }

        @Test
        @DisplayName("无协议时应该添加https和斜杠")
        fun `should add https protocol and slash when no protocol`() {
            // Given
            val config = NetworkConfig(restBase = "api.example.com")

            // When
            val result = config.getSecureRestUrl()

            // Then
            assertEquals("https://api.example.com/", result)
        }

        @Test
        @DisplayName("复杂路径应该正确处理")
        fun `should handle complex paths correctly`() {
            // Given
            val config = NetworkConfig(restBase = "https://api.example.com/v1/api")

            // When
            val result = config.getSecureRestUrl()

            // Then
            assertEquals("https://api.example.com/v1/api/", result)
        }
    }

    @Nested
    @DisplayName("ensureSlash() 扩展函数测试")
    inner class EnsureSlashTest {

        @Test
        @DisplayName("应该为没有斜杠的URL添加斜杠")
        fun `should add slash to URL without slash`() {
            // Given
            val url = "https://api.example.com"

            // When
            val result = url.ensureSlash()

            // Then
            assertEquals("https://api.example.com/", result)
        }

        @Test
        @DisplayName("已有斜杠的URL不应重复添加")
        fun `should not add duplicate slash to URL with slash`() {
            // Given
            val url = "https://api.example.com/"

            // When
            val result = url.ensureSlash()

            // Then
            assertEquals("https://api.example.com/", result)
        }

        @Test
        @DisplayName("空字符串应该返回单个斜杠")
        fun `should return single slash for empty string`() {
            // Given
            val url = ""

            // When
            val result = url.ensureSlash()

            // Then
            assertEquals("/", result)
        }

        @Test
        @DisplayName("只有斜杠的字符串应该保持不变")
        fun `should keep single slash unchanged`() {
            // Given
            val url = "/"

            // When
            val result = url.ensureSlash()

            // Then
            assertEquals("/", result)
        }
    }
}
