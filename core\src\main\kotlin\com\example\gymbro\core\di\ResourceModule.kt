package com.example.gymbro.core.di

import android.content.Context
import com.example.gymbro.core.di.qualifiers.ConstantResource
import com.example.gymbro.core.resources.AndroidResourceProvider
import com.example.gymbro.core.resources.CachedResourceProvider
import com.example.gymbro.core.resources.ConstantResourceProvider
import com.example.gymbro.core.resources.ResourceProvider
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 统一的资源提供模块
 *
 * 这个模块负责提供各种ResourceProvider实现的依赖注入。
 * 合并了之前分散在多个文件中的依赖注入逻辑，解决了冲突问题。
 *
 * 提供以下资源提供器：
 * - AndroidResourceProvider：基于Android Context的资源提供器
 * - CachedResourceProvider：带缓存的资源提供器（默认被注入的ResourceProvider）
 * - ConstantResourceProvider：基于常量的资源提供器，不依赖Android资源系统
 */
@Module
@InstallIn(SingletonComponent::class)
object ResourceModule {
    /**
     * 提供Android资源提供器
     */
    @Provides
    @Singleton
    internal fun provideAndroidResourceProvider(
        @ApplicationContext context: Context,
    ): AndroidResourceProvider = AndroidResourceProvider(context)

    /**
     * 提供默认的资源提供器实现
     *
     * 直接使用AndroidResourceProvider作为主要实现，
     * 因为Android系统本身已经对资源访问进行了优化
     */
    @Provides
    @Singleton
    fun provideResourceProvider(androidResourceProvider: AndroidResourceProvider): ResourceProvider =
        androidResourceProvider

    /**
     * 提供带缓存的资源提供器实现（可选）
     *
     * 此实现保留用于特殊场景，但不再作为默认实现
     * 因为Android系统本身已经对资源访问进行了充分优化
     *
     * 注意：仅在确实需要额外缓存层时使用
     */
    @Provides
    @Singleton
    fun provideCachedResourceProvider(
        androidResourceProvider: AndroidResourceProvider,
    ): CachedResourceProvider =
        CachedResourceProvider(androidResourceProvider)

    /**
     * 提供基于常量的资源提供器实现
     *
     * 此实现不依赖Android资源系统，适用于：
     * - 单元测试
     * - 通用模块
     * - 非Android环境
     *
     * 使用@ConstantResource限定符注入
     * 注意：ConstantResource限定符已迁移到core.di.qualifiers.ResourceQualifiers统一管理
     */
    @Provides
    @Singleton
    @ConstantResource
    fun provideConstantResourceProvider(): ResourceProvider = ConstantResourceProvider()
}
