package com.example.gymbro.data.coach.service

import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.coach.model.ChatSession
import com.example.gymbro.domain.coach.model.CoachMessage
import com.example.gymbro.domain.service.coach.ChatSummaryService
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 聊天摘要服务实现
 *
 * 重构说明：移除复杂依赖，统一使用ModernDataError，简化实现。
 * 当前为临时实现，提供基础功能框架。
 */
@Singleton
class ChatSummaryServiceImpl @Inject constructor() : ChatSummaryService {

    override suspend fun generateSummary(session: ChatSession): ModernResult<String> = ModernResult.Error(
        ModernDataError(
            operationName = "ChatSummaryService.generateSummary",
            errorType = GlobalErrorType.System.NotImplemented,
            uiMessage = UiText.DynamicString("聊天摘要服务暂未实现"),
        ),
    )

    override suspend fun generateSummary(messages: List<CoachMessage>): ModernResult<String> = ModernResult.Error(
        ModernDataError(
            operationName = "ChatSummaryService.generateSummary",
            errorType = GlobalErrorType.System.NotImplemented,
            uiMessage = UiText.DynamicString("聊天摘要服务暂未实现"),
        ),
    )

    override fun needsSummary(session: ChatSession): Boolean = false
}
