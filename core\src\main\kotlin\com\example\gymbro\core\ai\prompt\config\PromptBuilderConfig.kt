package com.example.gymbro.core.ai.prompt.config

/**
 * Prompt构建器配置
 *
 * 定义Prompt构建的核心配置参数
 * 遵循Clean Architecture原则，在core层定义配置类
 *
 * @since 618重构 - 架构修复
 */
data class PromptBuilderConfig(
    /**
     * 配置版本
     */
    val version: String,

    /**
     * 最大token数量
     */
    val maxTokens: Int,

    /**
     * 上下文模板数量K
     */
    val contextTemplatesK: Int,

    /**
     * 近期历史数量N
     */
    val recentHistoryN: Int,

    /**
     * 截断策略
     */
    val truncationStrategy: TruncationStrategy,
)

/**
 * 截断策略枚举
 *
 * 定义当内容超过token限制时的处理策略
 */
enum class TruncationStrategy {
    /**
     * 基于优先级的截断
     * 保留重要性高的内容
     */
    PRIORITY_BASED,

    /**
     * 最近优先截断
     * 保留最新的内容
     */
    RECENT_FIRST,

    /**
     * 平衡截断
     * 在重要性和时间之间平衡
     */
    BALANCED,
}

/**
 * Prompt场景枚举
 *
 * 定义不同的Prompt使用场景
 */
enum class PromptScenario {
    /**
     * 快速响应场景
     * 优化速度和成本
     */
    FAST_RESPONSE,

    /**
     * 高质量场景
     * 优化输出质量
     */
    HIGH_QUALITY,

    /**
     * 成本优化场景
     * 最小化token使用
     */
    COST_OPTIMIZED,

    /**
     * 平衡场景
     * 在质量、速度、成本之间平衡
     */
    BALANCED,

    /**
     * 默认场景
     * 使用默认配置
     */
    DEFAULT,
}
