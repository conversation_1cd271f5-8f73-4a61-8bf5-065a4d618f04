package com.example.gymbro.data.workout.session.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.example.gymbro.data.workout.session.entity.SessionAutoSaveEntity
import kotlinx.coroutines.flow.Flow

/**
 * 会话自动保存数据访问对象 - SessionDB DAO
 *
 * 基于 04_Implementation_Plan_CleanSlate_SessionDB.md 设计
 * 提供自动保存快照管理功能，用于崩溃恢复
 */
@Dao
interface SessionAutoSaveDao {

    // ==================== 基础 CRUD ====================

    @Query("SELECT * FROM session_autosave WHERE id = :autoSaveId")
    suspend fun getAutoSaveById(autoSaveId: String): SessionAutoSaveEntity?

    @Query("SELECT * FROM session_autosave WHERE sessionId = :sessionId ORDER BY saveTime DESC")
    fun getAutoSavesBySession(sessionId: String): Flow<List<SessionAutoSaveEntity>>

    @Query("SELECT * FROM session_autosave WHERE sessionId = :sessionId ORDER BY saveTime DESC LIMIT 1")
    suspend fun getLatestAutoSave(sessionId: String): SessionAutoSaveEntity?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAutoSave(autoSave: SessionAutoSaveEntity)

    @Update
    suspend fun updateAutoSave(autoSave: SessionAutoSaveEntity)

    @Query("DELETE FROM session_autosave WHERE id = :autoSaveId")
    suspend fun deleteAutoSave(autoSaveId: String)

    @Query("DELETE FROM session_autosave WHERE sessionId = :sessionId")
    suspend fun deleteAllAutoSaves(sessionId: String)

    // ==================== 恢复相关查询 ====================

    @Query(
        "SELECT sa.* FROM session_autosave sa INNER JOIN workout_sessions ws ON sa.sessionId = ws.id WHERE ws.userId = :userId AND ws.status IN ('IN_PROGRESS', 'PAUSED') AND sa.isValid = 1 ORDER BY sa.saveTime DESC",
    )
    suspend fun getRecoverableSessions(userId: String): List<SessionAutoSaveEntity>

    @Query(
        "SELECT * FROM session_autosave WHERE sessionId = :sessionId AND saveType = :saveType ORDER BY saveTime DESC LIMIT 1",
    )
    suspend fun getLatestAutoSaveByType(sessionId: String, saveType: String): SessionAutoSaveEntity?

    @Query(
        "SELECT * FROM session_autosave WHERE sessionId = :sessionId AND isValid = 1 ORDER BY saveTime DESC LIMIT :limit",
    )
    suspend fun getValidAutoSaves(sessionId: String, limit: Int): List<SessionAutoSaveEntity>

    // ==================== 清理操作 ====================

    @Query(
        "DELETE FROM session_autosave WHERE sessionId = :sessionId AND id NOT IN (SELECT id FROM session_autosave WHERE sessionId = :sessionId ORDER BY saveTime DESC LIMIT :keepCount)",
    )
    suspend fun deleteOldAutoSaves(sessionId: String, keepCount: Int)

    @Query("DELETE FROM session_autosave WHERE expiresAt IS NOT NULL AND expiresAt < :currentTime")
    suspend fun deleteExpiredAutoSaves(currentTime: Long)

    @Query("UPDATE session_autosave SET isValid = 0 WHERE sessionId = :sessionId AND id != :excludeId")
    suspend fun invalidateOtherAutoSaves(sessionId: String, excludeId: String)

    // ==================== 统计查询 ====================

    @Query("SELECT COUNT(*) FROM session_autosave WHERE sessionId = :sessionId")
    suspend fun getAutoSaveCount(sessionId: String): Int

    @Query("SELECT COUNT(*) FROM session_autosave WHERE sessionId = :sessionId AND saveType = :saveType")
    suspend fun getAutoSaveCountByType(sessionId: String, saveType: String): Int

    @Query(
        "SELECT saveTime FROM session_autosave WHERE sessionId = :sessionId ORDER BY saveTime DESC LIMIT 1",
    )
    suspend fun getLastAutoSaveTime(sessionId: String): Long?

    // ==================== 批量操作 ====================

    @Query("UPDATE session_autosave SET isValid = :isValid WHERE sessionId = :sessionId")
    suspend fun updateValidityForSession(sessionId: String, isValid: Boolean)

    @Query("SELECT * FROM session_autosave WHERE saveTime < :cutoffTime ORDER BY saveTime ASC")
    suspend fun getAutoSavesOlderThan(cutoffTime: Long): List<SessionAutoSaveEntity>
}
