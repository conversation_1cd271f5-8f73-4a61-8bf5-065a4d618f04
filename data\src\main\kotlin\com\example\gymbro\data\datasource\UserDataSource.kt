package com.example.gymbro.data.datasource

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.profile.model.user.BlockedUser
import com.example.gymbro.domain.profile.model.user.User
import kotlinx.coroutines.flow.Flow
import java.io.File

/**
 * 基础用户数据源接口
 * 专注于用户的基本CRUD操作和查询功能
 */
interface UserDataSource {
    // === 基础用户CRUD操作 ===

    /**
     * 根据用户ID从远程数据源获取用户数据。
     *
     * @param userId 用户的唯一标识符。
     * @return 包含用户数据对象或null的[ModernResult]。
     */
    suspend fun getUser(userId: String): ModernResult<User?>

    /**
     * 根据用户ID获取用户完整数据。
     * 与getUser不同的是，此方法在用户不存在时返回错误，而不是null。
     *
     * @param userId 用户的唯一标识符。
     * @return 包含用户数据的[ModernResult]。
     */
    suspend fun getUserById(userId: String): ModernResult<User>

    /**
     * 创建新用户。
     *
     * @param user 要创建的用户对象。
     * @return 包含新用户ID的[ModernResult]。
     */
    suspend fun createUser(user: User): ModernResult<String>

    /**
     * 更新远程数据源中指定用户的部分字段。
     *
     * @param userId 用户的唯一标识符。
     * @param updates 包含要更新字段及其新值的Map。
     * @return 表示操作结果的[ModernResult]。
     */
    suspend fun updateUser(
        userId: String,
        updates: Map<String, Any?>,
    ): ModernResult<Unit>

    /**
     * 更新远程数据源中的整个用户数据对象。
     *
     * @param user 要更新的用户数据对象。
     * @return 表示操作结果的[ModernResult]。
     */
    suspend fun updateUser(user: User): ModernResult<Unit>

    /**
     * 创建或更新用户配置文件。
     * 如果用户ID为空，将创建新用户；否则更新现有用户。
     *
     * @param user 包含用户信息的对象。
     * @return 包含用户ID的[ModernResult]。
     */
    suspend fun createOrUpdateUserProfile(user: User): ModernResult<String>

    /**
     * 删除指定用户。
     *
     * @param userId 要删除用户的ID。
     * @return 表示操作结果的[ModernResult]。
     */
    suspend fun deleteUser(userId: String): ModernResult<Unit>

    /**
     * 获取用户数据流。
     *
     * @param userId 用户的唯一标识符。
     * @return 包含用户数据的[Flow]。
     */
    fun getUserFlow(userId: String): Flow<User?>

    // === 用户查询功能 ===

    /**
     * 根据指定字段和值查询远程数据源中的用户。
     *
     * @param field 要查询的字段名。
     * @param value 要查询的字段值。
     * @return 包含符合条件用户数据对象列表的[ModernResult]。
     */
    suspend fun queryUsers(
        field: String,
        value: Any,
    ): ModernResult<List<User>>

    /**
     * 根据用户ID列表批量获取用户。
     *
     * @param userIds 要获取用户的ID列表。
     * @return 包含用户列表的[ModernResult]。
     */
    suspend fun getUsers(userIds: List<String>): ModernResult<List<User>>

    /**
     * 获取附近的用户列表。
     *
     * @param latitude 当前纬度。
     * @param longitude 当前经度。
     * @param radiusInKm 搜索半径（公里）。
     * @return 包含附近用户列表的[ModernResult]。
     */
    suspend fun getNearbyUsers(
        latitude: Double,
        longitude: Double,
        radiusInKm: Double,
    ): ModernResult<List<User>>

    /**
     * 根据健身房ID查找用户列表。
     *
     * @param gymId 健身房的唯一标识符。
     * @return 包含用户列表的[ModernResult]。
     */
    suspend fun getUsersByGym(gymId: String): ModernResult<List<User>>

    // === 在线状态功能 ===

    /**
     * 更新用户的在线状态。
     *
     * @param userId 用户的唯一标识符。
     * @param isOnline 用户是否在线。
     * @return 表示操作结果的[ModernResult]。
     */
    suspend fun updateUserOnlineStatus(
        userId: String,
        isOnline: Boolean,
    ): ModernResult<Unit>

    /**
     * 获取在线用户ID的实时流。
     *
     * @return 包含在线用户ID列表的Flow。
     */
    fun getOnlineUsersFlow(): Flow<List<String>>

    // === 用户屏蔽功能 ===

    /**
     * 屏蔽指定用户。
     *
     * @param userId 执行屏蔽操作的用户ID。
     * @param targetUserId 要屏蔽的目标用户ID。
     * @return 表示操作结果的[ModernResult]。
     */
    suspend fun blockUser(
        userId: String,
        targetUserId: String,
    ): ModernResult<Unit>

    /**
     * 解除屏蔽指定用户。
     *
     * @param userId 执行解除屏蔽操作的用户ID。
     * @param targetUserId 要解除屏蔽的目标用户ID。
     * @return 表示操作结果的[ModernResult]。
     */
    suspend fun unblockUser(
        userId: String,
        targetUserId: String,
    ): ModernResult<Unit>

    /**
     * 获取用户屏蔽的用户ID列表
     * @param userId 当前用户ID
     * @return 屏蔽用户ID列表流
     */
    fun getBlockedUserIds(userId: String): Flow<List<String>>

    /**
     * 获取被屏蔽用户详情
     * @param userId 当前用户ID
     * @param blockedUserIds 被屏蔽用户ID列表
     * @return 包含被屏蔽用户详情的结果
     */
    suspend fun getBlockedUserDetails(
        userId: String,
        blockedUserIds: List<String>,
    ): ModernResult<List<BlockedUser>>

    /**
     * 检查用户是否被屏蔽
     * @param userId 当前用户ID
     * @param targetUserId 目标用户ID
     * @return 屏蔽状态流
     */
    fun isUserBlocked(
        userId: String,
        targetUserId: String,
    ): Flow<Boolean>

    // === 文件上传功能 ===

    /**
     * 上传用户头像
     *
     * @param userId 用户ID
     * @param avatarFile 头像文件
     * @return 包含头像URL的ModernResult
     */
    suspend fun uploadUserAvatar(
        userId: String,
        avatarFile: File,
    ): ModernResult<String>
}
