package com.example.gymbro.data.exercise.local.entity

import androidx.room.*
import com.example.gymbro.shared.models.exercise.Equipment
import com.example.gymbro.shared.models.exercise.MuscleGroup

/**
 * Exercise Room实体
 *
 * 基于plan.md设计：
 * - 主表存储完整动作数据
 * - FTS4表支持全文搜索
 * - 索引优化查询性能
 * - 支持向量搜索
 */
@Entity(
    tableName = "exercise",
    indices = [
        Index(value = ["id"], unique = true),
        Index(value = ["muscleGroup"]),
        Index(value = ["equipment"]),
        Index(value = ["isCustom"]),
        Index(value = ["userId"]),
        Index(value = ["isFavorite"]),
        Index(value = ["name"]),
        Index(value = ["createdAt"]),
        Index(value = ["updatedAt"]),
    ],
)
data class ExerciseEntity(
    @PrimaryKey
    val id: String,
    @ColumnInfo(name = "name")
    val name: String,
    @ColumnInfo(name = "muscleGroup")
    val muscleGroup: MuscleGroup,
    @ColumnInfo(name = "equipment")
    val equipment: List<Equipment>,
    @ColumnInfo(name = "description")
    val description: String? = null,
    @ColumnInfo(name = "imageUrl")
    val imageUrl: String? = null,
    @ColumnInfo(name = "videoUrl")
    val videoUrl: String? = null,
    @ColumnInfo(name = "defaultSets")
    val defaultSets: Int = 3,
    @ColumnInfo(name = "defaultReps")
    val defaultReps: Int = 12,
    @ColumnInfo(name = "defaultWeight")
    val defaultWeight: Float? = null,
    @ColumnInfo(name = "steps")
    val steps: List<String> = emptyList(),
    @ColumnInfo(name = "tips")
    val tips: List<String> = emptyList(),
    @ColumnInfo(name = "userId")
    val userId: String? = null,
    @ColumnInfo(name = "isCustom")
    val isCustom: Boolean = false,
    @ColumnInfo(name = "isFavorite")
    val isFavorite: Boolean = false,
    @ColumnInfo(name = "difficultyLevel")
    val difficultyLevel: Int = 3,
    @ColumnInfo(name = "calories")
    val calories: Int? = null,
    @ColumnInfo(name = "targetMuscles")
    val targetMuscles: List<MuscleGroup> = emptyList(),
    @ColumnInfo(name = "instructions")
    val instructions: List<String> = emptyList(),
    @ColumnInfo(name = "embedding")
    val embedding: FloatArray? = null,
    @ColumnInfo(name = "createdAt")
    val createdAt: Long = System.currentTimeMillis(),
    @ColumnInfo(name = "updatedAt")
    val updatedAt: Long = createdAt,
    @ColumnInfo(name = "createdByUserId")
    val createdByUserId: String? = null,
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as ExerciseEntity

        if (id != other.id) return false
        if (name != other.name) return false
        if (muscleGroup != other.muscleGroup) return false
        if (equipment != other.equipment) return false
        if (description != other.description) return false
        if (imageUrl != other.imageUrl) return false
        if (videoUrl != other.videoUrl) return false
        if (defaultSets != other.defaultSets) return false
        if (defaultReps != other.defaultReps) return false
        if (defaultWeight != other.defaultWeight) return false
        if (steps != other.steps) return false
        if (tips != other.tips) return false
        if (userId != other.userId) return false
        if (isCustom != other.isCustom) return false
        if (isFavorite != other.isFavorite) return false
        if (difficultyLevel != other.difficultyLevel) return false
        if (calories != other.calories) return false
        if (targetMuscles != other.targetMuscles) return false
        if (instructions != other.instructions) return false
        if (embedding != null) {
            if (other.embedding == null) return false
            if (!embedding.contentEquals(other.embedding)) return false
        } else if (other.embedding != null) {
            return false
        }
        if (createdAt != other.createdAt) return false
        if (updatedAt != other.updatedAt) return false
        if (createdByUserId != other.createdByUserId) return false

        return true
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + name.hashCode()
        result = 31 * result + muscleGroup.hashCode()
        result = 31 * result + equipment.hashCode()
        result = 31 * result + (description?.hashCode() ?: 0)
        result = 31 * result + (imageUrl?.hashCode() ?: 0)
        result = 31 * result + (videoUrl?.hashCode() ?: 0)
        result = 31 * result + defaultSets
        result = 31 * result + defaultReps
        result = 31 * result + (defaultWeight?.hashCode() ?: 0)
        result = 31 * result + steps.hashCode()
        result = 31 * result + tips.hashCode()
        result = 31 * result + (userId?.hashCode() ?: 0)
        result = 31 * result + isCustom.hashCode()
        result = 31 * result + isFavorite.hashCode()
        result = 31 * result + difficultyLevel
        result = 31 * result + (calories ?: 0)
        result = 31 * result + targetMuscles.hashCode()
        result = 31 * result + instructions.hashCode()
        result = 31 * result + (embedding?.contentHashCode() ?: 0)
        result = 31 * result + createdAt.hashCode()
        result = 31 * result + updatedAt.hashCode()
        result = 31 * result + (createdByUserId?.hashCode() ?: 0)
        return result
    }
}

/**
 * Exercise FTS4虚拟表
 *
 * 用于全文搜索，支持：
 * - 中文分词
 * - 拼音搜索
 * - 英文搜索
 * - 相关性排序
 */
@Fts4(contentEntity = ExerciseEntity::class)
@Entity(tableName = "exercise_fts")
data class ExerciseFtsEntity(
    @ColumnInfo(name = "name")
    val name: String,
    @ColumnInfo(name = "description")
    val description: String,
    @ColumnInfo(name = "muscleGroup")
    val muscleGroup: String,
    @ColumnInfo(name = "equipment")
    val equipment: String,
    @ColumnInfo(name = "steps")
    val steps: String,
    @ColumnInfo(name = "tips")
    val tips: String,
    @ColumnInfo(name = "instructions")
    val instructions: String,
)

/**
 * Exercise统计视图
 * 用于性能优化的统计查询
 */
@DatabaseView(
    viewName = "exercise_stats",
    value = """
        SELECT
            muscleGroup,
            COUNT(*) as exerciseCount,
            COUNT(CASE WHEN isCustom = 1 THEN 1 END) as customCount,
            COUNT(CASE WHEN isFavorite = 1 THEN 1 END) as favoriteCount,
            AVG(difficultyLevel) as avgDifficulty
        FROM exercise
        GROUP BY muscleGroup
    """,
)
data class ExerciseStatsView(
    val muscleGroup: MuscleGroup,
    val exerciseCount: Int,
    val customCount: Int,
    val favoriteCount: Int,
    val avgDifficulty: Float,
)

/**
 * Exercise搜索历史实体
 * 用于搜索建议和热门搜索
 */
@Entity(
    tableName = "exercise_search_history",
    indices = [
        Index(value = ["query"]),
        Index(value = ["timestamp"]),
        Index(value = ["userId"]),
    ],
)
data class ExerciseSearchHistoryEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    @ColumnInfo(name = "query")
    val query: String,
    @ColumnInfo(name = "resultCount")
    val resultCount: Int,
    @ColumnInfo(name = "userId")
    val userId: String? = null,
    @ColumnInfo(name = "timestamp")
    val timestamp: Long = System.currentTimeMillis(),
)

/**
 * Exercise使用统计实体
 * 用于推荐和热门排序
 */
@Entity(
    tableName = "exercise_usage_stats",
    indices = [
        Index(value = ["exerciseId"]),
        Index(value = ["userId"]),
        Index(value = ["lastUsed"]),
    ],
)
data class ExerciseUsageStatsEntity(
    @PrimaryKey
    val id: String,
    @ColumnInfo(name = "exerciseId")
    val exerciseId: String,
    @ColumnInfo(name = "userId")
    val userId: String? = null,
    @ColumnInfo(name = "usageCount")
    val usageCount: Int = 0,
    @ColumnInfo(name = "lastUsed")
    val lastUsed: Long = System.currentTimeMillis(),
    @ColumnInfo(name = "totalSets")
    val totalSets: Int = 0,
    @ColumnInfo(name = "totalReps")
    val totalReps: Int = 0,
    @ColumnInfo(name = "maxWeight")
    val maxWeight: Float? = null,
)
