package com.example.gymbro.data.repository.user

import com.example.gymbro.core.error.extensions.toModernDataError
import com.example.gymbro.core.error.types.*
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.repository.AuthRepository
import com.example.gymbro.domain.profile.model.user.FavoriteItem
import com.example.gymbro.domain.profile.repository.user.FavoriteRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FavoriteRepositoryImpl
@Inject
constructor(
    private val authRepository: AuthRepository,
) : FavoriteRepository {
    /**
     * 获取用户的所有收藏项目
     * @param userId 用户ID
     * @return 包含收藏列表的结果流
     */
    override fun getFavoriteItems(userId: String): Flow<ModernResult<List<FavoriteItem>>> =
        flow {
            try {
                Timber.d("获取用户收藏: $userId")
                // 简化实现：返回空的收藏列表
                val items = emptyList<FavoriteItem>()
                emit(ModernResult.Success(items))
            } catch (e: Exception) {
                Timber.e(e, "获取用户收藏失败: $userId")
                emit(
                    ModernResult.Error(
                        e.toModernDataError(
                            operationName = "FavoriteRepo.getFavoriteItems.catch",
                            uiMessage = UiText.DynamicString("获取收藏失败"),
                        ),
                    ),
                )
            }
        }

    /**
     * 添加收藏项目
     * @param favoriteItem 收藏项目
     * @return 操作结果
     */
    override suspend fun addFavoriteItem(favoriteItem: FavoriteItem): ModernResult<Unit> =
        withContext(Dispatchers.IO) {
            try {
                // 获取当前用户ID
                val currentUserFlow = authRepository.getCurrentUser()
                val currentUserResult = currentUserFlow.first()
                val currentUser = (currentUserResult as? ModernResult.Success)?.data
                val userId =
                    currentUser?.uid
                        ?: return@withContext ModernResult.Error(
                            ModernDataError(
                                operationName = "FavoriteRepo.addFavorite.userNotLoggedIn",
                                errorType = GlobalErrorType.Auth.Unauthorized,
                                category = ErrorCategory.AUTH,
                                uiMessage = UiText.DynamicString("用户未登录"),
                                severity = ErrorSeverity.WARNING,
                            ),
                        )

                Timber.d("添加收藏: ${favoriteItem.id} 为用户 $userId")
                // 简化实现：仅记录日志，不实际保存到数据库
                return@withContext ModernResult.Success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "添加收藏失败: ${favoriteItem.id}")
                return@withContext ModernResult.Error(
                    e.toModernDataError(
                        operationName = "FavoriteRepo.addFavoriteItem.catch",
                        uiMessage = UiText.DynamicString("添加收藏失败"),
                    ),
                )
            }
        }

    /**
     * 移除收藏项目
     * @param favoriteItem 收藏项目
     * @return 操作结果
     */
    override suspend fun removeFavoriteItem(favoriteItem: FavoriteItem): ModernResult<Unit> =
        withContext(Dispatchers.IO) {
            try {
                // 获取当前用户ID
                val currentUserFlow = authRepository.getCurrentUser()
                val currentUserResult = currentUserFlow.first()
                val currentUser = (currentUserResult as? ModernResult.Success)?.data
                val userId =
                    currentUser?.uid
                        ?: return@withContext ModernResult.Error(
                            ModernDataError(
                                operationName = "FavoriteRepo.removeFavorite.userNotLoggedIn",
                                errorType = GlobalErrorType.Auth.Unauthorized,
                                category = ErrorCategory.AUTH,
                                uiMessage = UiText.DynamicString("用户未登录"),
                                severity = ErrorSeverity.WARNING,
                            ),
                        )

                Timber.d("移除收藏: ${favoriteItem.id} 为用户 $userId")
                // 简化实现：仅记录日志，不实际从数据库删除
                return@withContext ModernResult.Success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "移除收藏失败: ${favoriteItem.id}")
                return@withContext ModernResult.Error(
                    e.toModernDataError(
                        operationName = "FavoriteRepo.removeFavoriteItem.catch",
                        uiMessage = UiText.DynamicString("移除收藏失败"),
                    ),
                )
            }
        }

    /**
     * 检查项目是否被收藏
     * @param userId 用户ID
     * @param itemId 项目ID
     * @return 包含是否收藏的Result流
     */
    override fun isItemFavorited(
        userId: String,
        itemId: String,
    ): Flow<ModernResult<Boolean>> =
        flow {
            try {
                Timber.d("检查收藏状态: $itemId 为用户 $userId")
                // 简化实现：返回false表示没有收藏
                emit(ModernResult.Success(false))
            } catch (e: Exception) {
                Timber.e(e, "检查收藏状态失败: $itemId")
                emit(
                    ModernResult.Error(
                        e.toModernDataError(
                            operationName = "FavoriteRepo.isItemFavorited.catch",
                            uiMessage = UiText.DynamicString("检查收藏状态失败"),
                        ),
                    ),
                )
            }
        }
}
