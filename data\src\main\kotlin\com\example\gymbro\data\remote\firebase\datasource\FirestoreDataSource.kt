package com.example.gymbro.data.remote.firebase.datasource

import com.example.gymbro.core.error.extensions.toModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.ui.text.UiText
import com.google.firebase.firestore.*
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.suspendCancellableCoroutine
import timber.log.Timber
import javax.inject.Inject
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * Firestore数据源实现
 * 提供对Firestore数据库的基本操作
 */
class FirestoreDataSource
@Inject
constructor(
    private val firestore: FirebaseFirestore,
) {
    /**
     * 获取文档
     *
     * @param path 文档路径
     * @return 包含文档数据的ModernResult
     */
    suspend fun getDocument(path: String): ModernResult<DocumentSnapshot> =
        try {
            val snapshot =
                suspendCancellableCoroutine { continuation ->
                    firestore
                        .document(path)
                        .get()
                        .addOnSuccessListener { snapshot ->
                            continuation.resume(snapshot)
                        }.addOnFailureListener { exception ->
                            continuation.resumeWithException(exception)
                        }
                }
            ModernResult.Success(snapshot)
        } catch (e: Exception) {
            ModernResult.Error(
                DataErrors.DataError.access(
                    operationName = "FirestoreDataSource.getDocument",
                    message = UiText.DynamicString("获取数据失败"),
                    entityType = "Document",
                    cause = e,
                    metadataMap = mapOf("path" to path),
                ),
            )
        }

    /**
     * 设置文档数据
     *
     * @param path 文档路径
     * @param data 要设置的数据
     * @return 操作结果
     */
    suspend fun setDocument(
        path: String,
        data: Map<String, Any?>,
    ): ModernResult<Unit> =
        try {
            suspendCancellableCoroutine { continuation ->
                firestore
                    .document(path)
                    .set(data)
                    .addOnSuccessListener {
                        continuation.resume(Unit)
                    }.addOnFailureListener { exception ->
                        continuation.resumeWithException(exception)
                    }
            }
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            ModernResult.Error(
                DataErrors.DataError.access(
                    operationName = "FirestoreDataSource.setDocument",
                    message = UiText.DynamicString("保存数据失败"),
                    entityType = "Document",
                    cause = e,
                    metadataMap = mapOf("path" to path),
                ),
            )
        }

    /**
     * 更新文档
     *
     * @param path 文档路径
     * @param data 要更新的数据
     * @return 操作结果
     */
    suspend fun updateDocument(
        path: String,
        data: Map<String, Any?>,
    ): ModernResult<Unit> =
        try {
            suspendCancellableCoroutine { continuation ->
                firestore
                    .document(path)
                    .update(data)
                    .addOnSuccessListener {
                        continuation.resume(Unit)
                    }.addOnFailureListener { exception ->
                        continuation.resumeWithException(exception)
                    }
            }
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            ModernResult.Error(
                DataErrors.DataError.access(
                    operationName = "FirestoreDataSource.updateDocument",
                    message = UiText.DynamicString("更新数据失败"),
                    entityType = "Document",
                    cause = e,
                    metadataMap = mapOf("path" to path),
                ),
            )
        }

    /**
     * 删除文档
     *
     * @param path 文档路径
     * @return 操作结果
     */
    suspend fun deleteDocument(path: String): ModernResult<Unit> =
        try {
            suspendCancellableCoroutine { continuation ->
                firestore
                    .document(path)
                    .delete()
                    .addOnSuccessListener {
                        continuation.resume(Unit)
                    }.addOnFailureListener { exception ->
                        continuation.resumeWithException(exception)
                    }
            }
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            ModernResult.Error(
                DataErrors.DataError.access(
                    operationName = "FirestoreDataSource.deleteDocument",
                    message = UiText.DynamicString("删除数据失败"),
                    entityType = "Document",
                    cause = e,
                    metadataMap = mapOf("path" to path),
                ),
            )
        }

    /**
     * 添加文档到集合
     *
     * @param collectionPath 集合路径
     * @param data 要添加的数据
     * @return 新创建的文档引用
     */
    suspend fun addDocument(
        collectionPath: String,
        data: Map<String, Any?>,
    ): ModernResult<DocumentReference> =
        try {
            val documentReference =
                suspendCancellableCoroutine { continuation ->
                    firestore
                        .collection(collectionPath)
                        .add(data)
                        .addOnSuccessListener { documentReference ->
                            continuation.resume(documentReference)
                        }.addOnFailureListener { exception ->
                            continuation.resumeWithException(exception)
                        }
                }
            ModernResult.Success(documentReference)
        } catch (e: Exception) {
            ModernResult.Error(
                DataErrors.DataError.access(
                    operationName = "FirestoreDataSource.addDocument",
                    message = UiText.DynamicString("添加数据失败"),
                    entityType = "Document",
                    cause = e,
                    metadataMap = mapOf("collectionPath" to collectionPath),
                ),
            )
        }

    /**
     * 获取文档的子集合
     *
     * @param documentPath 文档路径
     * @param subCollectionName 子集合名称
     * @return 查询结果
     */
    suspend fun getSubCollection(
        documentPath: String,
        subCollectionName: String,
    ): ModernResult<QuerySnapshot> =
        try {
            val snapshot =
                suspendCancellableCoroutine { continuation ->
                    firestore
                        .document(documentPath)
                        .collection(subCollectionName)
                        .get()
                        .addOnSuccessListener { snapshot ->
                            continuation.resume(snapshot)
                        }.addOnFailureListener { exception ->
                            continuation.resumeWithException(exception)
                        }
                }
            ModernResult.Success(snapshot)
        } catch (e: Exception) {
            ModernResult.Error(
                DataErrors.DataError.access(
                    operationName = "FirestoreDataSource.getSubCollection",
                    message = UiText.DynamicString("获取子集合失败"),
                    entityType = "SubCollection",
                    cause = e,
                    metadataMap =
                    mapOf(
                        "documentPath" to documentPath,
                        "subCollectionName" to subCollectionName,
                    ),
                ),
            )
        }

    /**
     * 获取集合
     *
     * @param collectionPath 集合路径
     * @return 查询结果
     */
    suspend fun getCollection(collectionPath: String): ModernResult<QuerySnapshot> =
        try {
            val snapshot =
                suspendCancellableCoroutine { continuation ->
                    firestore
                        .collection(collectionPath)
                        .get()
                        .addOnSuccessListener { snapshot ->
                            continuation.resume(snapshot)
                        }.addOnFailureListener { exception ->
                            continuation.resumeWithException(exception)
                        }
                }
            ModernResult.Success(snapshot)
        } catch (e: Exception) {
            ModernResult.Error(
                DataErrors.DataError.access(
                    operationName = "FirestoreDataSource.getCollection",
                    message = UiText.DynamicString("获取集合失败"),
                    entityType = "Collection",
                    cause = e,
                    metadataMap = mapOf("collectionPath" to collectionPath),
                ),
            )
        }

    /**
     * 批量写入数据
     *
     * @param operations 批量操作列表
     * @return 操作结果
     */
    suspend fun executeBatch(operations: List<FirestoreOperation>): ModernResult<Unit> =
        try {
            val batch = firestore.batch()

            operations.forEach { operation ->
                when (operation) {
                    is FirestoreOperation.Set -> {
                        batch.set(firestore.document(operation.path), operation.data)
                    }
                    is FirestoreOperation.Update -> {
                        batch.update(firestore.document(operation.path), operation.data)
                    }
                    is FirestoreOperation.Delete -> {
                        batch.delete(firestore.document(operation.path))
                    }
                }
            }

            suspendCancellableCoroutine { continuation ->
                batch
                    .commit()
                    .addOnSuccessListener {
                        continuation.resume(Unit)
                    }.addOnFailureListener { exception ->
                        continuation.resumeWithException(exception)
                    }
            }
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            ModernResult.Error(
                DataErrors.DataError.access(
                    operationName = "FirestoreDataSource.executeBatch",
                    message = UiText.DynamicString("批量操作失败"),
                    entityType = "Batch",
                    cause = e,
                    metadataMap = mapOf("operationCount" to operations.size.toString()),
                ),
            )
        }

    /**
     * 监听Firestore文档的实时变化
     *
     * @param path 文档路径
     * @return 返回一个Flow，该Flow在文档发生变化时发出最新的DocumentSnapshot
     */
    fun listenDocument(path: String): Flow<ModernResult<DocumentSnapshot>> =
        callbackFlow {
            val docRef = firestore.document(path)
            val listener =
                docRef.addSnapshotListener { snapshot, error ->
                    if (error != null) {
                        Timber.w(error, "Error listening to document: $path")
                        trySend(
                            ModernResult.Error(
                                error.toModernDataError(
                                    operationName = "FirestoreDataSource.listenDocument",
                                    uiMessage = UiText.DynamicString("监听文档变化失败"),
                                ),
                            ),
                        )
                        close(error) // Close the flow on error
                        return@addSnapshotListener
                    }
                    if (snapshot != null) {
                        trySend(ModernResult.Success(snapshot))
                    } else {
                        // Can represent non-existence as a specific type of success or error if needed
                        // For now, let's assume null snapshot means it might not exist or error handled by `error` param
                        Timber.d(
                            "Snapshot is null for path: $path, potentially non-existent after deletion or error.",
                        )
                    }
                }
            awaitClose { listener.remove() }
        }

    /**
     * 执行一个 Firestore 查询
     *
     * @param queryBuilder 一个 lambda，接收 FirebaseFirestore 实例并返回一个 Query 对象
     * @param operationName 操作名称，用于日志和错误报告
     * @return 查询结果 QuerySnapshot
     */
    suspend fun executeQuery(
        queryBuilder: (FirebaseFirestore) -> Query,
        operationName: String,
    ): ModernResult<QuerySnapshot> =
        try {
            val query = queryBuilder(firestore)
            val snapshot =
                suspendCancellableCoroutine { continuation ->
                    query
                        .get()
                        .addOnSuccessListener { snapshot ->
                            continuation.resume(snapshot)
                        }.addOnFailureListener { exception ->
                            continuation.resumeWithException(exception)
                        }
                }
            ModernResult.Success(snapshot)
        } catch (e: Exception) {
            ModernResult.Error(
                DataErrors.DataError.access(
                    operationName = "FirestoreDataSource.$operationName",
                    message = UiText.DynamicString("查询数据失败"),
                    entityType = "Query",
                    cause = e,
                ),
            )
        }
}

/**
 * Firestore操作密封类
 */
sealed class FirestoreOperation {
    /**
     * 设置文档数据
     */
    data class Set(
        val path: String,
        val data: Map<String, Any?>,
    ) : FirestoreOperation()

    /**
     * 更新文档数据
     */
    data class Update(
        val path: String,
        val data: Map<String, Any?>,
    ) : FirestoreOperation()

    /**
     * 删除文档
     */
    data class Delete(
        val path: String,
    ) : FirestoreOperation()
}
