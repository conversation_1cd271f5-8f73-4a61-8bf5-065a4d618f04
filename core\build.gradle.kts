plugins {
    id("gymbro.android.library")
    id("gymbro.compose.library")
    id("gymbro.hilt.library")
    id("gymbro.testing.library")
    id("gymbro.quality")
    id("org.jetbrains.kotlin.plugin.compose")
    kotlin("plugin.serialization")
}

android {
    namespace = "com.example.gymbro.core"
    // 🚀 基础配置已移至build-logic Convention插件

    // 完全禁用资源目录
    sourceSets {
        getByName("main") {
            res.srcDirs(emptyList<String>())
            // Keep this if .kt files are in src/main/kotlin and also considered 'resources' for some reason
            // resources.srcDirs("src/main/kotlin")
        }

        getByName("test") {
            res.srcDirs(emptyList<String>())
        }

        getByName("androidTest") {
            res.srcDirs(emptyList<String>())
        }
    }

    // 🚀 编译配置已移至build-logic Convention插件

    buildFeatures {
        compose = true // Enabled Compose
        buildConfig = false
        androidResources = false // Explicitly disabled; core module provides no Android resources, even with Compose enabled.
    }

    // 避免生成空的资源
    lint {
        abortOnError = false
    }
}

dependencies {
    // Kotlin 标准库和反射
    implementation(libs.kotlin.reflect) // 添加反射支持，修复GlobalErrorType.kt:53的反射API警告

    // Kotlin 协程 - 使用 libs.versions.toml 中的版本
    implementation(libs.kotlinx.coroutines.core)
    implementation(libs.kotlinx.coroutines.android)

    // Kotlin DateTime - 使用 libs.versions.toml 中的版本
    implementation(libs.kotlinx.datetime)

    // Kotlin Serialization - 用于UiText和ErrorCode序列化
    implementation(libs.kotlinx.serialization.json)

    // ViewModel 和 Lifecycle - 使用 libs.versions.toml 中的版本
    implementation(libs.androidx.lifecycle.viewmodel.ktx)
    // implementation(libs.androidx.lifecycle.runtime.ktx)

    // Android App Startup - 修复NetworkInitializer.kt编译错误
    implementation(libs.androidx.startup.runtime)

    // Compose Dependencies
    implementation(platform(libs.compose.bom)) // Import Compose BOM
    implementation(libs.compose.runtime) // Essential for @Composable and CompositionLocal
    implementation(libs.compose.ui) // Needed if UiText or related utilities use any UI primitives
    // implementation(libs.androidx.runtime.android) // This was 'androidx.compose.runtime:runtime-android:1.8.1'
    // Covered by compose.runtime from BOM if versions align,
    // but explicit 'runtime-android' can be kept if it's a specific variant needed.
    // For now, relying on compose.runtime from BOM.

    // 网络 (Retrofit core) - 使用 libs.versions.toml 中的版本
    implementation(libs.retrofit)

    // DataStore - 紧急修复：添加 DataStore 依赖
    implementation(libs.androidx.datastore.preferences)

    // 日志 - 使用 libs.versions.toml 中的版本
    implementation(libs.timber)

    // Hilt依赖 - 使用 libs.versions.toml 中的版本
    implementation(libs.hilt.android)
    ksp(libs.hilt.compiler)
}
