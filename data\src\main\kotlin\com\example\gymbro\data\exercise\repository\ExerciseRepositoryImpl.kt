package com.example.gymbro.data.exercise.repository

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.extensions.safeCatch
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.data.exercise.initializer.ExerciseLibraryInitializerService
import com.example.gymbro.data.exercise.integration.HybridSearchEngine
import com.example.gymbro.data.exercise.local.dao.ExerciseDao
import com.example.gymbro.data.exercise.local.dao.ExerciseFtsDao
import com.example.gymbro.data.exercise.mapper.ExerciseMapper
import com.example.gymbro.data.exercise.remote.ExerciseApi
import com.example.gymbro.domain.exercise.model.Exercise
import com.example.gymbro.domain.exercise.repository.ExerciseRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.concurrent.atomic.AtomicBoolean
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Exercise Repository实现
 *
 * 实现策略（基于plan.md设计）：
 * 1. 先本地FTS搜索，分数≥0.8直接返回首条
 * 2. 否则跑向量搜索，余弦≥0.9返回
 * 3. 否则返回null
 *
 * 特性：
 * - FTS + 向量融合搜索
 * - 官方库同步
 * - 用户自定义动作管理
 * - 高性能本地缓存
 */
@Singleton
class ExerciseRepositoryImpl
@Inject
constructor(
    private val exerciseDao: ExerciseDao,
    private val exerciseFtsDao: ExerciseFtsDao,
    private val exerciseApi: ExerciseApi,
    private val exerciseMapper: ExerciseMapper,
    private val hybridSearchEngine: HybridSearchEngine,
    private val exerciseLibraryInitializer: ExerciseLibraryInitializerService,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
) : ExerciseRepository {

    // 确保初始化只执行一次
    private val isInitialized = AtomicBoolean(false)

    /**
     * 获取所有动作
     * 优先返回本地缓存，支持离线使用
     * 首次访问时自动初始化官方动作库
     */
    override fun getExercises(): Flow<ModernResult<List<Exercise>>> =
        flow {
            emit(ModernResult.Loading)

            // 确保动作库已初始化
            ensureInitialized()

            val result =
                safeCatch {
                    val entities = exerciseDao.getAll()
                    val exercises = exerciseMapper.toDomainListFromEntity(entities)
                    Timber.d("获取所有动作: ${exercises.size} 条")
                    exercises
                }
            emit(result)
        }.flowOn(ioDispatcher)

    /**
     * 搜索动作 - FTS + 向量融合 (suspend版本)
     */
    override suspend fun searchExercises(
        query: String,
        limit: Int,
    ): ModernResult<List<Exercise>> =
        safeCatch {
            withContext(ioDispatcher) {
                if (query.isBlank()) {
                    return@withContext emptyList<Exercise>()
                }

                // 使用混合搜索引擎
                val exercises = hybridSearchEngine.hybridSearch(query, limit)

                Timber.d("混合搜索 '$query': ${exercises.size} 条结果")
                exercises
            }
        }

    /**
     * 搜索动作 - FTS + 向量融合 (Flow版本)
     */
    override fun searchExercises(query: String): Flow<ModernResult<List<Exercise>>> =
        flow {
            emit(ModernResult.Loading)
            val result = searchExercises(query, 20)
            emit(result)
        }.flowOn(ioDispatcher)

    /**
     * 精确匹配动作名称 - Coach模块专用
     *
     * 用于AI生成模板时的动作匹配
     * 优先级：完全匹配 > FTS匹配 > 向量匹配
     */
    override suspend fun findByExactName(name: String): ModernResult<Exercise?> =
        safeCatch {
            withContext(ioDispatcher) {
                if (name.isBlank()) {
                    return@withContext null
                }

                // 1. 完全匹配
                val exactMatch = exerciseDao.getByExactName(name)
                if (exactMatch != null) {
                    Timber.d("精确匹配成功: $name -> ${exactMatch.id}")
                    return@withContext exerciseMapper.toDomain(exactMatch)
                }

                // 2. 使用混合搜索引擎进行精确匹配
                val result = hybridSearchEngine.findExactMatch(name)

                if (result != null) {
                    Timber.d("混合搜索匹配成功: $name -> ${result.name}")
                    return@withContext result
                }

                Timber.d("动作匹配失败: $name")
                null
            }
        }

    /**
     * 根据ID列表获取动作
     */
    override fun getExercisesByIds(exerciseIds: List<String>): Flow<ModernResult<List<Exercise>>> =
        flow {
            emit(ModernResult.Loading)
            val result =
                safeCatch {
                    val entities = exerciseIds.mapNotNull { exerciseDao.getById(it) }
                    exerciseMapper.toDomainListFromEntity(entities)
                }
            emit(result)
        }.flowOn(ioDispatcher)

    /**
     * 根据ID获取动作
     */
    override suspend fun getExerciseById(exerciseId: String): ModernResult<Exercise> =
        safeCatch {
            withContext(ioDispatcher) {
                val entity =
                    exerciseDao.getById(exerciseId)
                        ?: throw IllegalArgumentException("Exercise not found: $exerciseId")
                exerciseMapper.toDomain(entity)
            }
        }

    /**
     * 搜索动作（使用搜索参数）
     */
    override fun searchExercises(
        searchParams: com.example.gymbro.shared.models.exercise.ExerciseSearchParamsDto,
    ): Flow<ModernResult<List<Exercise>>> =
        flow {
            emit(ModernResult.Loading)
            val result =
                safeCatch {
                    // TODO: 实现基于搜索参数的复杂搜索
                    val query = searchParams.query ?: ""
                    if (query.isBlank()) {
                        emptyList<Exercise>()
                    } else {
                        val ftsResults = exerciseFtsDao.searchFts(query, searchParams.pageSize)
                        exerciseMapper.toDomainListFromEntity(ftsResults)
                    }
                }
            emit(result)
        }.flowOn(ioDispatcher)

    /**
     * 根据肌肉群获取动作
     */
    override suspend fun getExercisesByMuscleGroups(
        muscleGroups: List<com.example.gymbro.shared.models.exercise.MuscleGroup>,
        limit: Int,
    ): ModernResult<List<Exercise>> =
        safeCatch {
            withContext(ioDispatcher) {
                val allExercises =
                    mutableListOf<com.example.gymbro.data.exercise.local.entity.ExerciseEntity>()
                muscleGroups.forEach { muscleGroup ->
                    val exercises = exerciseDao.getByMuscleGroup(muscleGroup)
                    allExercises.addAll(exercises)
                }
                val uniqueExercises = allExercises.distinctBy { it.id }.take(limit)
                exerciseMapper.toDomainListFromEntity(uniqueExercises)
            }
        }

    /**
     * 获取推荐动作
     */
    override suspend fun getRecommendedExercises(
        userId: String,
        limit: Int,
    ): ModernResult<List<Exercise>> =
        safeCatch {
            withContext(ioDispatcher) {
                // TODO: 实现基于用户历史的推荐算法
                val popularExercises = exerciseDao.getOfficialExercises().take(limit)
                exerciseMapper.toDomainListFromEntity(popularExercises)
            }
        }

    /**
     * 获取用户收藏的动作
     */
    override fun getUserFavoriteExercises(userId: String): Flow<ModernResult<List<Exercise>>> =
        flow {
            emit(ModernResult.Loading)
            val result =
                safeCatch {
                    val favoriteExercises = exerciseDao.getFavoriteExercises()
                    exerciseMapper.toDomainListFromEntity(favoriteExercises)
                }
            emit(result)
        }.flowOn(ioDispatcher)

    /**
     * 添加动作到收藏
     */
    override suspend fun addToFavorites(
        userId: String,
        exerciseId: String,
    ): ModernResult<Unit> =
        safeCatch {
            withContext(ioDispatcher) {
                exerciseDao.updateFavoriteStatus(exerciseId, true)
                Timber.d("添加收藏: $exerciseId")
            }
        }

    /**
     * 从收藏中移除动作
     */
    override suspend fun removeFromFavorites(
        userId: String,
        exerciseId: String,
    ): ModernResult<Unit> =
        safeCatch {
            withContext(ioDispatcher) {
                exerciseDao.updateFavoriteStatus(exerciseId, false)
                Timber.d("移除收藏: $exerciseId")
            }
        }

    /**
     * 创建自定义动作
     */
    override suspend fun createCustomExercise(exercise: Exercise): ModernResult<Exercise> =
        safeCatch {
            withContext(ioDispatcher) {
                val entity = exerciseMapper.toEntity(exercise)
                exerciseDao.insert(entity)
                Timber.d("创建自定义动作成功: ${exercise.name}")
                exercise
            }
        }

    /**
     * 更新自定义动作
     */
    override suspend fun updateCustomExercise(exercise: Exercise): ModernResult<Exercise> =
        safeCatch {
            withContext(ioDispatcher) {
                val entity = exerciseMapper.toEntity(exercise)
                exerciseDao.update(entity)
                Timber.d("更新自定义动作成功: ${exercise.name}")
                exercise
            }
        }

    /**
     * 删除自定义动作
     */
    override suspend fun deleteCustomExercise(
        exerciseId: String,
        userId: String,
    ): ModernResult<Unit> =
        safeCatch {
            withContext(ioDispatcher) {
                val existing = exerciseDao.getById(exerciseId)
                if (existing?.createdByUserId == userId) {
                    exerciseDao.deleteById(exerciseId)
                    Timber.d("删除自定义动作成功: $exerciseId")
                } else {
                    throw IllegalArgumentException("无权限删除此动作")
                }
            }
        }

    /**
     * 获取用户自定义动作
     */
    override fun getUserCustomExercises(userId: String): Flow<ModernResult<List<Exercise>>> =
        flow {
            emit(ModernResult.Loading)
            val result =
                safeCatch {
                    val customExercises = exerciseDao.getCustomExercises(userId)
                    exerciseMapper.toDomainListFromEntity(customExercises)
                }
            emit(result)
        }.flowOn(ioDispatcher)

    /**
     * 检查动作名称是否存在
     */
    override suspend fun isExerciseNameExists(
        name: String,
        userId: String,
    ): ModernResult<Boolean> =
        safeCatch {
            withContext(ioDispatcher) {
                val existing = exerciseDao.getByExactName(name)
                existing != null
            }
        }

    // ========== 以下是额外的方法，不在接口中但保留用于内部使用 ==========

    /**
     * 确保动作库已初始化
     * 首次访问时自动初始化官方动作种子数据
     */
    private suspend fun ensureInitialized() {
        if (isInitialized.compareAndSet(false, true)) {
            try {
                Timber.d("首次访问动作库，开始初始化...")
                exerciseLibraryInitializer.initializeExerciseLibrary()
                Timber.d("动作库初始化完成")
            } catch (e: Exception) {
                Timber.e(e, "动作库初始化失败")
                // 重置标志，允许下次重试
                isInitialized.set(false)
                // 不抛出异常，允许应用继续运行
            }
        }
    }

    /**
     * 保存用户自定义动作
     */
    suspend fun upsertCustom(exercise: Exercise): ModernResult<Exercise> =
        safeCatch {
            withContext(ioDispatcher) {
                val entity =
                    exerciseMapper.toEntity(exercise).copy(
                        isCustom = true,
                        updatedAt = System.currentTimeMillis(),
                    )

                exerciseDao.upsert(entity)
                Timber.d("保存自定义动作: ${exercise.name}")

                exerciseMapper.toDomain(entity)
            }
        }

    /**
     * 删除动作
     */
    suspend fun delete(id: String): ModernResult<Unit> =
        safeCatch {
            withContext(ioDispatcher) {
                exerciseDao.delete(id)
                Timber.d("删除动作: $id")
            }
        }

    /**
     * 同步官方动作库
     *
     * 策略：
     * 1. 检查远端版本
     * 2. 下载增量更新
     * 3. 本地数据库更新
     */
    suspend fun syncOfficialLibrary(): ModernResult<Unit> =
        safeCatch {
            withContext(ioDispatcher) {
                try {
                    // 1. 获取远端版本信息
                    val remoteMeta = exerciseApi.getLibraryMeta()
                    val localVersion = exerciseDao.getLibraryVersion()

                    if (remoteMeta.version <= localVersion) {
                        Timber.d("官方库已是最新版本: $localVersion")
                        return@withContext
                    }

                    // 2. 下载增量数据
                    val updates =
                        exerciseApi.getLibraryUpdates(
                            fromVersion = localVersion,
                            toVersion = remoteMeta.version,
                        )

                    Timber.d(
                        "下载官方库更新: ${updates.added.size} 新增, ${updates.updated.size} 更新, ${updates.deleted.size} 删除",
                    )

                    // 3. 应用更新
                    exerciseDao.runInTransaction {
                        // 删除
                        updates.deleted.forEach { id ->
                            exerciseDao.delete(id)
                        }

                        // 新增和更新
                        val allUpdates = updates.added + updates.updated
                        val entities = exerciseMapper.toEntityList(
                            allUpdates.map { exerciseMapper.toDomain(it) },
                        )
                        exerciseDao.upsertBatch(entities)

                        // 更新版本号
                        exerciseDao.updateLibraryVersion(remoteMeta.version)
                    }

                    Timber.d("官方库同步完成: v$localVersion -> v${remoteMeta.version}")
                } catch (e: Exception) {
                    Timber.e(e, "官方库同步失败")
                    throw e
                }
            }
        }

    /**
     * 准备FTS查询字符串
     * 支持中文、拼音、英文搜索
     */
    private fun prepareFtsQuery(query: String): String {
        // 简化版FTS查询准备
        // TODO: 添加拼音转换和分词逻辑
        return query.trim().replace(Regex("\\s+"), " AND ")
    }

    /**
     * 计算相关性分数
     * 基于名称匹配、肌群匹配等因素
     */
    private fun calculateRelevanceScore(
        exercise: Exercise,
        query: String,
    ): Float {
        var score = 0f
        val queryLower = query.lowercase()
        val nameLower = exercise.name.toString().lowercase()

        // 名称匹配权重最高
        when {
            nameLower == queryLower -> score += 1.0f
            nameLower.contains(queryLower) -> score += 0.8f
            queryLower.contains(nameLower) -> score += 0.6f
        }

        // TODO: 添加肌群、器械等匹配因素

        return score
    }

    /**
     * 计算名称匹配分数
     * 用于精确匹配的阈值判断
     */
    private fun calculateNameMatchScore(
        exerciseName: String,
        queryName: String,
    ): Float {
        val name1 = exerciseName.lowercase().trim()
        val name2 = queryName.lowercase().trim()

        return when {
            name1 == name2 -> 1.0f
            name1.contains(name2) || name2.contains(name1) -> 0.9f
            // TODO: 添加编辑距离算法
            else -> 0.0f
        }
    }
}
