package com.example.gymbro.core.autosave

/**
 * 自动保存异常类
 *
 * 用于表示自动保存过程中发生的各种错误
 */
sealed class AutoSaveException(
    message: String,
    cause: Throwable? = null,
) : Exception(message, cause) {

    /**
     * 配置错误
     *
     * @param message 错误消息
     * @param cause 原因
     */
    class ConfigurationError(
        message: String,
        cause: Throwable? = null,
    ) : AutoSaveException("配置错误: $message", cause)

    /**
     * 存储错误
     *
     * @param message 错误消息
     * @param cause 原因
     */
    class StorageError(
        message: String,
        cause: Throwable? = null,
    ) : AutoSaveException("存储错误: $message", cause)

    /**
     * 序列化错误
     *
     * @param message 错误消息
     * @param cause 原因
     */
    class SerializationError(
        message: String,
        cause: Throwable? = null,
    ) : AutoSaveException("序列化错误: $message", cause)

    /**
     * 策略错误
     *
     * @param message 错误消息
     * @param cause 原因
     */
    class StrategyError(
        message: String,
        cause: Throwable? = null,
    ) : AutoSaveException("策略错误: $message", cause)

    /**
     * 生命周期错误
     *
     * @param message 错误消息
     * @param cause 原因
     */
    class LifecycleError(
        message: String,
        cause: Throwable? = null,
    ) : AutoSaveException("生命周期错误: $message", cause)

    /**
     * 超时错误
     *
     * @param message 错误消息
     * @param cause 原因
     */
    class TimeoutError(
        message: String,
        cause: Throwable? = null,
    ) : AutoSaveException("超时错误: $message", cause)

    /**
     * 网络错误
     *
     * @param message 错误消息
     * @param cause 原因
     */
    class NetworkError(
        message: String,
        cause: Throwable? = null,
    ) : AutoSaveException("网络错误: $message", cause)

    /**
     * 权限错误
     *
     * @param message 错误消息
     * @param cause 原因
     */
    class PermissionError(
        message: String,
        cause: Throwable? = null,
    ) : AutoSaveException("权限错误: $message", cause)

    /**
     * 数据冲突错误
     *
     * @param message 错误消息
     * @param cause 原因
     */
    class DataConflictError(
        message: String,
        cause: Throwable? = null,
    ) : AutoSaveException("数据冲突错误: $message", cause)

    /**
     * 未知错误
     *
     * @param message 错误消息
     * @param cause 原因
     */
    class UnknownError(
        message: String,
        cause: Throwable? = null,
    ) : AutoSaveException("未知错误: $message", cause)
}

/**
 * 自动保存异常工厂
 */
object AutoSaveExceptionFactory {

    /**
     * 从通用异常创建自动保存异常
     *
     * @param throwable 原始异常
     * @param context 上下文信息
     * @return 自动保存异常
     */
    fun fromThrowable(throwable: Throwable, context: String = ""): AutoSaveException {
        val message = if (context.isNotBlank()) "$context: ${throwable.message}" else throwable.message ?: "未知错误"

        return when (throwable) {
            is AutoSaveException -> throwable
            is IllegalArgumentException -> AutoSaveException.ConfigurationError(message, throwable)
            is IllegalStateException -> AutoSaveException.LifecycleError(message, throwable)
            is SecurityException -> AutoSaveException.PermissionError(message, throwable)
            is java.util.concurrent.TimeoutException -> AutoSaveException.TimeoutError(message, throwable)
            is java.io.IOException -> AutoSaveException.StorageError(message, throwable)
            // 检查序列化异常（通过类名匹配，避免直接依赖kotlinx.serialization）
            else -> {
                val className = throwable::class.java.simpleName
                if (className.contains("Serialization") || className.contains("Json")) {
                    AutoSaveException.SerializationError(message, throwable)
                } else {
                    AutoSaveException.UnknownError(message, throwable)
                }
            }
        }
    }

    /**
     * 创建配置错误
     *
     * @param message 错误消息
     * @param cause 原因
     * @return 配置错误异常
     */
    fun configurationError(message: String, cause: Throwable? = null): AutoSaveException.ConfigurationError {
        return AutoSaveException.ConfigurationError(message, cause)
    }

    /**
     * 创建存储错误
     *
     * @param message 错误消息
     * @param cause 原因
     * @return 存储错误异常
     */
    fun storageError(message: String, cause: Throwable? = null): AutoSaveException.StorageError {
        return AutoSaveException.StorageError(message, cause)
    }
}
