package com.example.gymbro.core.error.types

/**
 * 类型安全的元数据键
 *
 * 用于ErrorMetadata中存储和获取特定类型的元数据
 * 提供类型安全的方式访问错误元数据
 *
 * @param T 键对应值的类型
 * @property key 键名
 * @property description 元数据键的描述
 */
data class MetadataKey<T : Any>(
    val key: String,
    val description: String = "",
) {
    override fun toString(): String = key

    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (this === other) return true
        if (other !is MetadataKey<*>) return false
        return key == other.key
    }

    override fun hashCode(): Int = key.hashCode()

    companion object {
        /**
         * 创建字符串类型的元数据键
         */
        fun string(key: String): MetadataKey<String> = MetadataKey(key)

        /**
         * 创建整数类型的元数据键
         */
        fun int(key: String): MetadataKey<Int> = MetadataKey(key)

        /**
         * 创建长整型的元数据键
         */
        fun long(key: String): MetadataKey<Long> = MetadataKey(key)

        /**
         * 创建布尔类型的元数据键
         */
        fun boolean(key: String): MetadataKey<Boolean> = MetadataKey(key)

        /**
         * 创建任意类型的元数据键
         */
        fun <T : Any> of(key: String): MetadataKey<T> = MetadataKey(key)
    }
}

/**
 * 带有默认值的元数据键
 *
 * @param T 键对应值的类型
 * @property key 原始元数据键
 * @property defaultValue 默认值
 */
class MetadataKeyWithDefault<T : Any>(
    val key: MetadataKey<T>,
    val defaultValue: T,
) {
    override fun toString(): String = "${key.key}(default=$defaultValue)"
}
