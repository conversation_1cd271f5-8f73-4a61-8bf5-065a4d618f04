package com.example.gymbro.designSystem.components.base.scaffold

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.AutoAwesome
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme

/**
 * 新版 GymBroScaffold 功能展示预览
 *
 * 展示了完善后的 GymBroScaffold 的所有新功能：
 * - FloatingActionButton 支持
 * - BottomBar 支持
 * - 灵活的 content 区域（支持 LazyColumn）
 * - 完整的 PaddingValues 处理
 */

@GymBroPreview
@Composable
private fun GymBroScaffoldWithFABPreview() {
    GymBroTheme {
        GymBroScaffold(
            title = UiText.DynamicString("新版 Scaffold"),
            onNavigateBack = { },
            actions = {
                IconButton(onClick = { }) {
                    Icon(Icons.Default.Refresh, contentDescription = "刷新")
                }
                IconButton(onClick = { }) {
                    Icon(Icons.Default.Add, contentDescription = "添加")
                }
            },
            floatingActionButton = {
                FloatingActionButton(
                    onClick = { },
                    containerColor = MaterialTheme.colorScheme.primary,
                ) {
                    Icon(Icons.Default.AutoAwesome, contentDescription = "AI")
                }
            },
        ) { paddingValues ->
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp),
            ) {
                items(20) { index ->
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                    ) {
                        Text(
                            text = "列表项 ${index + 1}",
                            modifier = Modifier.padding(16.dp),
                        )
                    }
                }
            }
        }
    }
}

@GymBroPreview
@Composable
private fun WorkoutScaffoldPreview() {
    GymBroTheme {
        WorkoutScaffold(
            title = UiText.DynamicString("训练计划"),
            onNavigateBack = { },
            actions = {
                IconButton(onClick = { }) {
                    Icon(Icons.Default.Refresh, contentDescription = "刷新")
                }
                IconButton(onClick = { }) {
                    Icon(Icons.Default.Add, contentDescription = "添加")
                }
            },
            floatingActionButton = {
                FloatingActionButton(
                    onClick = { },
                    containerColor = MaterialTheme.colorScheme.primary,
                ) {
                    Icon(Icons.Default.AutoAwesome, contentDescription = "AI 生成")
                }
            },
        ) { paddingValues ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp),
            ) {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp),
                    ) {
                        Text(
                            text = "训练计划 1",
                            style = MaterialTheme.typography.titleMedium,
                        )
                        Text(
                            text = "胸部 + 三头肌训练",
                            style = MaterialTheme.typography.bodyMedium,
                        )
                    }
                }

                Card(
                    modifier = Modifier.fillMaxWidth(),
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp),
                    ) {
                        Text(
                            text = "训练计划 2",
                            style = MaterialTheme.typography.titleMedium,
                        )
                        Text(
                            text = "背部 + 二头肌训练",
                            style = MaterialTheme.typography.bodyMedium,
                        )
                    }
                }
            }
        }
    }
}

@GymBroPreview
@Composable
private fun GymBroScaffoldWithBottomBarPreview() {
    GymBroTheme {
        GymBroScaffold(
            title = UiText.DynamicString("带底栏的页面"),
            onNavigateBack = { },
            bottomBar = {
                NavigationBar {
                    NavigationBarItem(
                        icon = { Icon(Icons.Default.Add, contentDescription = null) },
                        label = { Text("首页") },
                        selected = true,
                        onClick = { },
                    )
                    NavigationBarItem(
                        icon = { Icon(Icons.Default.Refresh, contentDescription = null) },
                        label = { Text("计划") },
                        selected = false,
                        onClick = { },
                    )
                    NavigationBarItem(
                        icon = { Icon(Icons.Default.AutoAwesome, contentDescription = null) },
                        label = { Text("AI") },
                        selected = false,
                        onClick = { },
                    )
                }
            },
            floatingActionButton = {
                FloatingActionButton(onClick = { }) {
                    Icon(Icons.Default.Add, contentDescription = "添加")
                }
            },
        ) { paddingValues ->
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .padding(16.dp),
            ) {
                Text(
                    text = "这是一个带有底部导航栏和悬浮按钮的页面示例。\n\n" +
                        "注意 PaddingValues 正确处理了顶栏、底栏和 FAB 的空间，" +
                        "确保内容不会被遮挡。",
                    style = MaterialTheme.typography.bodyLarge,
                )
            }
        }
    }
}
