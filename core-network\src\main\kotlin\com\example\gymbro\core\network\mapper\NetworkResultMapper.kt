package com.example.gymbro.core.network.mapper

import com.example.gymbro.shared.models.network.ApiError
import com.example.gymbro.shared.models.network.NetworkResult
import retrofit2.Response
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException

/**
 * Response<T>到NetworkResult<T>的转换器
 *
 * 统一处理Retrofit响应转换为项目标准的NetworkResult格式
 * 提供一致的错误处理和状态码映射
 */
object NetworkResultMapper {

    /**
     * 将Retrofit Response<T>转换为NetworkResult<T>
     *
     * @param T 响应数据类型
     * @return NetworkResult<T> 统一的结果封装
     */
    suspend fun <T> Response<T>.toNetworkResult(): NetworkResult<T> {
        return try {
            if (isSuccessful) {
                val body = body()
                if (body != null) {
                    NetworkResult.Success(body)
                } else {
                    NetworkResult.Error(
                        ApiError.Parse("Response body is null"),
                    )
                }
            } else {
                NetworkResult.Error(
                    mapHttpError(code(), message()),
                )
            }
        } catch (e: Exception) {
            NetworkResult.Error(
                mapException(e),
            )
        }
    }

    /**
     * 安全执行网络请求并转换为NetworkResult
     *
     * @param apiCall 网络请求的挂起函数
     * @return NetworkResult<T> 包含成功数据或错误信息
     */
    suspend fun <T> safeApiCall(
        apiCall: suspend () -> Response<T>,
    ): NetworkResult<T> {
        return try {
            val response = apiCall()
            response.toNetworkResult()
        } catch (e: Exception) {
            NetworkResult.Error(mapException(e))
        }
    }

    /**
     * 映射HTTP错误状态码到ApiError
     */
    private fun mapHttpError(code: Int, message: String): ApiError {
        return when (code) {
            400 -> ApiError.Http(code, "请求参数错误: $message")
            401 -> ApiError.Http(code, "未授权访问: $message")
            403 -> ApiError.Http(code, "访问被禁止: $message")
            404 -> ApiError.Http(code, "资源未找到: $message")
            408 -> ApiError.Http(code, "请求超时: $message")
            429 -> ApiError.Http(code, "请求过于频繁: $message")
            in 500..599 -> ApiError.Http(code, "服务器错误: $message")
            else -> ApiError.Http(code, "网络错误: $message")
        }
    }

    /**
     * 映射异常到ApiError
     */
    private fun mapException(exception: Exception): ApiError {
        return when (exception) {
            is UnknownHostException -> ApiError.Offline
            is SocketTimeoutException -> ApiError.Http(408, "连接超时")
            is IOException -> ApiError.Http(0, "网络连接失败: ${exception.message}")
            else -> ApiError.Unknown(exception.message ?: "未知错误")
        }
    }
}

/**
 * 扩展函数：简化Response<T>转换
 */
suspend fun <T> Response<T>.toNetworkResult(): NetworkResult<T> =
    NetworkResultMapper.run { toNetworkResult() }

/**
 * 扩展函数：安全执行API调用
 */
suspend fun <T> safeApiCall(
    apiCall: suspend () -> Response<T>,
): NetworkResult<T> = NetworkResultMapper.safeApiCall(apiCall)
