package com.example.gymbro.core.ml.service

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.features.FeatureErrors
import com.example.gymbro.core.ml.embedding.EmbeddingEngine
import com.example.gymbro.core.ui.text.UiText
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.sqrt

/**
 * Exercise专用BGE向量化服务 - Core-ML Layer
 *
 * 提供动作库相关的向量化功能，不依赖shared-models
 * 使用简单数据结构和接口，通过data层mapper进行转换
 */
@Singleton
class ExerciseEmbeddingService @Inject constructor(
    private val embeddingEngine: EmbeddingEngine,
) {

    /**
     * 为动作文本生成向量
     * @param exerciseText 预处理后的动作文本（由data层提供）
     * @return 128维向量
     */
    suspend fun embedExerciseText(exerciseText: String): ModernResult<FloatArray> {
        return withContext(Dispatchers.Default) {
            try {
                if (exerciseText.isBlank()) {
                    return@withContext ModernResult.Error(
                        FeatureErrors.CoachError.invalidInput(
                            operationName = "ExerciseEmbeddingService.embedExerciseText",
                            message = UiText.DynamicString("动作文本不能为空"),
                            inputType = "empty_exercise_text",
                            value = exerciseText,
                        ),
                    )
                }

                val startTime = System.currentTimeMillis()
                val embedding = embeddingEngine.embed(exerciseText)
                val duration = System.currentTimeMillis() - startTime

                Timber.d("ExerciseEmbeddingService: 动作向量化完成 duration=${duration}ms, dim=${embedding.size}")

                ModernResult.Success(embedding)
            } catch (e: Exception) {
                Timber.e(e, "ExerciseEmbeddingService: 动作向量化失败")
                ModernResult.Error(
                    FeatureErrors.CoachError.processingFailed(
                        operationName = "ExerciseEmbeddingService.embedExerciseText",
                        message = UiText.DynamicString("动作向量化失败"),
                        processType = "exercise_embedding",
                        reason = "embedding_failed",
                        cause = e,
                    ),
                )
            }
        }
    }

    /**
     * 批量为动作文本生成向量
     * @param exerciseTexts 预处理后的动作文本列表
     * @return 向量列表
     */
    suspend fun embedExerciseTexts(exerciseTexts: List<String>): ModernResult<List<FloatArray>> {
        return withContext(Dispatchers.Default) {
            try {
                if (exerciseTexts.isEmpty()) {
                    return@withContext ModernResult.Error(
                        FeatureErrors.CoachError.invalidInput(
                            operationName = "ExerciseEmbeddingService.embedExerciseTexts",
                            message = UiText.DynamicString("动作文本列表不能为空"),
                            inputType = "empty_exercise_list",
                            value = "size=0",
                        ),
                    )
                }

                val validTexts = exerciseTexts.filter { it.isNotBlank() }
                if (validTexts.isEmpty()) {
                    return@withContext ModernResult.Error(
                        FeatureErrors.CoachError.invalidInput(
                            operationName = "ExerciseEmbeddingService.embedExerciseTexts",
                            message = UiText.DynamicString("没有有效的动作文本"),
                            inputType = "no_valid_exercise_text",
                            value = "filtered_size=0",
                        ),
                    )
                }

                val startTime = System.currentTimeMillis()
                val embeddings = embeddingEngine.embedBatch(validTexts)
                val duration = System.currentTimeMillis() - startTime

                val avgTimePerText = duration.toFloat() / validTexts.size
                Timber.d(
                    "ExerciseEmbeddingService: 批量动作向量化完成 count=${validTexts.size}, total=${duration}ms, avg=${avgTimePerText}ms",
                )

                ModernResult.Success(embeddings)
            } catch (e: Exception) {
                Timber.e(e, "ExerciseEmbeddingService: 批量动作向量化失败")
                ModernResult.Error(
                    FeatureErrors.CoachError.processingFailed(
                        operationName = "ExerciseEmbeddingService.embedExerciseTexts",
                        message = UiText.DynamicString("批量动作向量化失败"),
                        processType = "batch_exercise_embedding",
                        reason = "batch_embedding_failed",
                        cause = e,
                    ),
                )
            }
        }
    }
}

/**
 * 向量搜索服务 - Core-ML Layer
 *
 * 提供向量相似度计算和搜索功能
 */
@Singleton
class VectorSearchService @Inject constructor() {

    /**
     * 计算余弦相似度
     * @param vector1 向量1
     * @param vector2 向量2
     * @return 相似度值 [0, 1]
     */
    fun calculateCosineSimilarity(vector1: FloatArray, vector2: FloatArray): Float {
        require(vector1.size == vector2.size) {
            "向量维度不匹配: ${vector1.size} vs ${vector2.size}"
        }

        var dotProduct = 0f
        var norm1 = 0f
        var norm2 = 0f

        for (i in vector1.indices) {
            dotProduct += vector1[i] * vector2[i]
            norm1 += vector1[i] * vector1[i]
            norm2 += vector2[i] * vector2[i]
        }

        val magnitude = sqrt(norm1) * sqrt(norm2)
        return if (magnitude > 0f) dotProduct / magnitude else 0f
    }

    /**
     * 查找最相似的向量
     * @param queryVector 查询向量
     * @param candidateVectors 候选向量列表
     * @param threshold 相似度阈值
     * @return 相似度结果列表（按相似度降序）
     */
    fun findMostSimilar(
        queryVector: FloatArray,
        candidateVectors: List<FloatArray>,
        threshold: Float = 0.5f,
    ): List<SimilarityResult> {
        return candidateVectors
            .mapIndexed { index, vector ->
                val similarity = calculateCosineSimilarity(queryVector, vector)
                SimilarityResult(index, similarity)
            }
            .filter { it.similarity >= threshold }
            .sortedByDescending { it.similarity }
    }

    /**
     * 查找最佳匹配
     * @param queryVector 查询向量
     * @param candidateVectors 候选向量列表
     * @param threshold 高阈值
     * @return 最佳匹配结果，如果没有符合阈值的则返回null
     */
    fun findBestMatch(
        queryVector: FloatArray,
        candidateVectors: List<FloatArray>,
        threshold: Float = 0.9f,
    ): SimilarityResult? {
        return findMostSimilar(queryVector, candidateVectors, threshold).firstOrNull()
    }
}

/**
 * 相似度搜索结果
 * @param index 候选向量在列表中的索引
 * @param similarity 相似度分数
 */
data class SimilarityResult(
    val index: Int,
    val similarity: Float,
)
