package com.example.gymbro.data.workout.session.database

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.example.gymbro.data.workout.session.converter.SessionTypeConverters
import com.example.gymbro.data.workout.session.dao.ExerciseHistoryStatsDao
import com.example.gymbro.data.workout.session.dao.SessionAutoSaveDao
import com.example.gymbro.data.workout.session.dao.SessionDao
import com.example.gymbro.data.workout.session.dao.SessionExerciseDao
import com.example.gymbro.data.workout.session.dao.SessionSetDao
import com.example.gymbro.data.workout.session.entity.ExerciseHistoryStatsEntity
import com.example.gymbro.data.workout.session.entity.SessionAutoSaveEntity
import com.example.gymbro.data.workout.session.entity.SessionEntity
import com.example.gymbro.data.workout.session.entity.SessionExerciseEntity
import com.example.gymbro.data.workout.session.entity.SessionSetEntity

/**
 * SessionDB - 训练记录和统计数据库
 *
 * 基于 05_Final_Database_Architecture.md 和 04_Implementation_Plan_CleanSlate_SessionDB.md 设计
 * 职责：存储训练记录和统计数据，实时保存进度+持久化统计
 * 特性：高使用率，非常高写入频率（30秒自动保存+用户操作）
 */
@Database(
    entities = [
        SessionEntity::class,
        SessionExerciseEntity::class,
        SessionSetEntity::class,
        ExerciseHistoryStatsEntity::class,
        SessionAutoSaveEntity::class,
    ],
    version = 1,
    exportSchema = true,
)
@TypeConverters(SessionTypeConverters::class)
abstract class SessionDatabase : RoomDatabase() {

    abstract fun sessionDao(): SessionDao
    abstract fun sessionExerciseDao(): SessionExerciseDao
    abstract fun sessionSetDao(): SessionSetDao
    abstract fun exerciseHistoryStatsDao(): ExerciseHistoryStatsDao
    abstract fun sessionAutoSaveDao(): SessionAutoSaveDao

    companion object {
        private const val DATABASE_NAME = "session_database"

        @Volatile
        private var INSTANCE: SessionDatabase? = null

        fun getDatabase(context: Context): SessionDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    SessionDatabase::class.java,
                    DATABASE_NAME,
                ).build()
                INSTANCE = instance
                instance
            }
        }
    }
}
