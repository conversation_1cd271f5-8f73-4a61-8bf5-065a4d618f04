# 硬性关键词强制 Function Call 使用指南

## 🎯 **功能概述**

基于用户提供的设计，我们实现了"硬性关键词 → 强制 Function Call"功能，允许高级用户通过特定关键词直接触发函数调用，绕过常规的文本生成流程。

## 🔧 **关键词协议**

### **统一前缀：`!fc_`**

| 关键词            | 强制调用的函数                             | 用途                    |
| -------------- | ----------------------------------- | --------------------- |
| `!fc_search`   | `gymbro__search_knowledge`          | 强制搜索知识库             |
| `!fc_template` | `gymbro__create_or_update_template` | 强制创建/更新训练模板         |
| `!fc_plan`     | `gymbro__create_or_update_plan`     | 强制创建/更新训练计划         |
| `!fc_verify`   | `gymbro__verify_content`            | 强制验证内容质量            |

## 🚀 **使用示例**

### **1. 强制创建训练模板**
```
!fc_template
请生成一个 4×8 的基础卧推模板，ownerId 为 user_123，版本号 1。
```

**预期结果**：
- 直接调用 `gymbro__create_or_update_template` 函数
- 返回结构化的模板创建结果
- 无任何多余的文本回复

### **2. 强制创建训练计划**
```
!fc_plan
创建一个为期 8 周的增肌计划，包含胸、背、腿三个主要部位。
```

**预期结果**：
- 直接调用 `gymbro__create_or_update_plan` 函数
- 返回结构化的计划创建结果

### **3. 强制搜索知识库**
```
!fc_search
查找关于深蹲技巧的相关信息
```

**预期结果**：
- 直接调用 `gymbro__search_knowledge` 函数
- 返回搜索结果，无文本解释

### **4. 强制验证内容**
```
!fc_verify
验证以下训练计划的合理性：[训练计划内容]
```

**预期结果**：
- 直接调用 `gymbro__verify_content` 函数
- 返回验证结果和评分

## 🔒 **安全机制**

### **1. 系统层约束**
在 SystemLayer 中添加了硬性约束：
```kotlin
"🔥 若用户输入包含以 !fc_ 开头的关键词，只能通过对应 Function Call 返回结果，不要回复文本"
```

### **2. 代码层强制**
通过 `detectForcedFunction()` 方法检测关键词，直接设置 `function_call` 参数：
```kotlin
val forcedFunction = detectForcedFunction(userInput)
functionCall = forcedFunction?.let { mapOf("name" to it) } ?: "auto"
```

### **3. 权限控制建议**
- 仅对内部白名单用户开放硬关键词
- 普通用户输入关键词时忽略，走正常流程
- 通过 `client_request_id` 和 `expires_at` 确保幂等性

## 📊 **技术实现**

### **关键词检测**
```kotlin
private val HARD_KEYWORD = Regex("""!fc_[a-z]+""")

fun detectForcedFunction(userInput: String): String? {
    return HARD_KEYWORD.find(userInput)?.value?.let { keyword ->
        KEYWORD_TO_FUNCTION[keyword]
    }
}
```

### **强制调用逻辑**
```kotlin
// 在任意执行步骤中
val forcedFunction = detectForcedFunction(context.userPrompt)
val functionName = forcedFunction ?: defaultFunction

// 根据强制函数生成对应参数
val arguments = when (functionName) {
    "gymbro__create_or_update_template" -> generateTemplateArgs()
    "gymbro__create_or_update_plan" -> generatePlanArgs()
    // ...
}
```

## ⚠️ **注意事项**

1. **仅限高级用户**：避免普通对话被误触
2. **幂等保证**：通过 UUID 和过期时间确保安全
3. **错误处理**：强制调用失败时的降级策略
4. **日志记录**：记录所有强制调用的审计日志

## 🎉 **集成状态**

- ✅ **关键词检测** - 已实现正则表达式匹配
- ✅ **系统约束** - 已添加到 SystemLayer
- ✅ **强制调用** - 已集成到所有执行步骤
- ✅ **占位实现** - 提供完整的模拟逻辑
- ✅ **编译验证** - BUILD SUCCESSFUL

这个功能为高级用户提供了直接、精确的函数调用能力，完美补充了现有的智能对话系统！🚀
