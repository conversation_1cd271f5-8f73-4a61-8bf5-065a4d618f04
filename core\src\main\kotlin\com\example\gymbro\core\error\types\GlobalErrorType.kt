package com.example.gymbro.core.error.types

/**
 * 全局错误类型类，采用密封类层次结构来替代分散的错误类型枚举。
 * 该设计允许更强类型安全和结构化的错误分类。
 * 已简化为更少的核心错误类型，更多细节通过元数据提供。
 */
sealed class GlobalErrorType {
    /**
     * 获取错误类型的全名称
     */
    val fullName: String
        get() {
            val builder = StringBuilder()
            appendPath(this, builder)
            return builder.toString()
        }

    /**
     * 检查此错误类型是否属于特定类别
     */
    fun isCategoryOf(category: ErrorCategory): Boolean {
        return when (this) {
            is Network -> category == ErrorCategory.NETWORK
            is Data -> category == ErrorCategory.DATA
            is Database -> category == ErrorCategory.DATA
            is Cache -> category == ErrorCategory.DATA
            is Validation -> category == ErrorCategory.VALIDATION
            is Auth -> category == ErrorCategory.AUTH
            is Business -> category == ErrorCategory.BUSINESS
            is System -> category == ErrorCategory.SYSTEM
            is Payment -> category == ErrorCategory.BUSINESS
            is Subscription -> category == ErrorCategory.BUSINESS
            is Operation -> category == ErrorCategory.SYSTEM
            is Sync -> category == ErrorCategory.DATA
            is AI -> category == ErrorCategory.SYSTEM
            is User -> category == ErrorCategory.BUSINESS
            is Feature -> category == ErrorCategory.BUSINESS
            is General -> category == ErrorCategory.UNKNOWN
            Unknown -> category == ErrorCategory.UNKNOWN
        }
    }

    /**
     * 递归构建完整路径
     */
    private fun appendPath(errorType: GlobalErrorType, builder: StringBuilder) {
        val enclosingClass = errorType::class.java.enclosingClass?.kotlin
        if (enclosingClass != null && GlobalErrorType::class.java.isAssignableFrom(enclosingClass.java)) {
            // Find the object instance for the enclosing sealed class if possible
            val parentObjectInstance = enclosingClass.objectInstance as? GlobalErrorType
            if (parentObjectInstance != null) {
                appendPath(parentObjectInstance, builder)
                builder.append(".")
            } else {
                // Fallback for nested sealed classes that are not objects themselves
                // This might happen if a sealed class itself is not an object but contains objects.
                // We try to append the simple name of the enclosing class.
                val simpleName = enclosingClass.simpleName
                if (simpleName != null && simpleName != GlobalErrorType::class.simpleName) {
                    builder.append(simpleName).append(".")
                }
            }
        }
        builder.append(errorType::class.simpleName ?: "UnknownType")
    }

    /**
     * 网络相关错误
     */
    sealed class Network : GlobalErrorType() {
        /** 网络连接问题，例如无法访问主机或网络中断。 */
        data object Connection : Network()

        /** 操作在允许的时间内未完成。 */
        data object Timeout : Network()

        /** 服务器错误。 */
        data object ServerError : Network()

        /** DNS解析失败。 */
        data object DnsFailure : Network()

        /** 网络资源不可用。 */
        data object ResourceUnavailable : Network()

        /** 速率限制错误。 */
        data object RateLimited : Network()

        /** 通用网络错误。 */
        data object General : Network()
    }

    /**
     * 数据处理或访问相关错误
     */
    sealed class Data : GlobalErrorType() {
        /** 数据已过期。 */
        data object Stale : Data()

        /** 数据已损坏或格式不正确。 */
        data object Corruption : Data()

        /** 数据转换失败。 */
        data object Conversion : Data()

        /** 数据不存在。 */
        data object NotFound : Data()

        /** 数据已损坏。 */
        data object Corrupted : Data()

        /** 数据访问错误。 */
        data object Access : Data()

        /** 数据约束错误。 */
        data object Constraint : Data()

        /** 通用数据错误。 */
        data object General : Data()
    }

    /**
     * 数据库特定错误
     */
    sealed class Database : GlobalErrorType() {
        /** 无法连接到数据库。 */
        data object ConnectionFailed : Database()

        /** 数据库查询执行失败。 */
        data object QueryFailed : Database()
    }

    /**
     * 缓存相关错误
     */
    sealed class Cache : GlobalErrorType() {
        /** 缓存数据已过期。 */
        data object Expired : Cache()

        /** 缓存数据不可用。 */
        data object NotAvailable : Cache()
    }

    /**
     * 输入验证失败
     */
    sealed class Validation : GlobalErrorType() {
        /** 输入无效。 */
        data object InvalidInput : Validation()

        /** 数据格式无效。 */
        data object InvalidFormat : Validation()

        /** 值无效。 */
        data object InvalidValue : Validation()

        /** 必填字段缺失。 */
        data object Required : Validation()

        /** 值超出范围。 */
        data object Range : Validation()

        /** 格式错误。 */
        data object Format : Validation()

        /** 通用验证错误。 */
        data object General : Validation()
    }

    /**
     * 认证和授权错误
     */
    sealed class Auth : GlobalErrorType() {
        /** 用户未经身份验证或身份验证失败。 */
        data object Unauthorized : Auth()

        /** 身份验证令牌已过期。 */
        data object TokenExpired : Auth()

        /** 提供的凭据无效。 */
        data object InvalidCredentials : Auth()

        /** 用户已通过身份验证，但无权访问资源。 */
        data object Forbidden : Auth()

        /** 通用认证错误。 */
        data object General : Auth()
    }

    /**
     * 业务逻辑相关错误
     */
    sealed class Business : GlobalErrorType() {
        /** 违反了业务规则。 */
        data object Rule : Business()

        /** 未找到特定业务实体。 */
        data object NotFound : Business() // Example: UserNotFound, ProductNotFound

        /** 操作导致业务状态冲突。 */
        data object Conflict : Business()

        /** 当前状态下不允许操作。 */
        data object InvalidState : Business()

        /** 资源未找到。 */
        data object ResourceNotFound : Business()

        /** 操作失败。 */
        data object InvalidOperation : Business()

        /** 操作失败。 */
        data object RateLimitExceeded : Business()

        /** 通用业务错误。 */
        data object General : Business()
    }

    /**
     * 系统级错误
     */
    sealed class System : GlobalErrorType() {
        /** 关键系统资源不可用。 */
        data object Resource : System() // Example: FileSystem, ExternalService

        /** 发生意外的内部错误。 */
        data object Internal : System()

        /** 系统配置错误。 */
        data object Configuration : System()

        /** 通用系统错误。 */
        data object General : System() // 添加通用系统错误类型，替代之前的Generic

        /** 权限错误。 */
        data object Permission : System()

        /** 未实现的功能。 */
        data object NotImplemented : System()
    }

    /**
     * 支付处理错误
     *
     * 注意：已从多个错误类型简化为仅保留两个核心类型。
     * 使用元数据提供更具体的错误信息，参见 MetadataKeyGuide.PaymentGuide。
     */
    sealed class Payment : GlobalErrorType() {
        /** 支付处理失败。
         * 使用元数据提供更详细信息，例如：
         * - error_subtype: "insufficient_funds", "fraud_detected", "payment_declined" 等
         * - gateway_error: 支付网关返回的具体错误信息
         */
        data object ProcessingFailed : Payment()

        /** 提供的支付方式无效。
         * 使用元数据提供更详细信息，例如：
         * - error_subtype: "card_expired", "unsupported_method" 等
         * - payment_method: 支付方式（如"credit_card", "paypal"）
         */
        data object InvalidPaymentMethod : Payment()
    }

    /**
     * 订阅相关错误
     *
     * 注意：已从多个错误类型简化为仅保留四个核心类型。
     * 使用元数据提供更具体的错误信息，参见 MetadataKeyGuide.SubscriptionGuide。
     */
    sealed class Subscription : GlobalErrorType() {
        /** 用户没有有效的订阅。
         * 使用元数据提供更详细信息，例如：
         * - error_subtype: "never_subscribed", "trial_ended", "cancelled" 等
         * - subscription_plan: 所需的订阅计划
         */
        data object NotSubscribed : Subscription()

        /** 当前订阅已过期。
         * 使用元数据提供更详细信息，例如：
         * - expiry_time: 过期时间
         * - grace_period_end: 宽限期结束时间
         */
        data object Expired : Subscription()

        /** 订阅支付失败。
         * 使用元数据提供更详细信息，例如：
         * - error_subtype: "billing_issue", "payment_declined" 等
         * - retry_count: 重试次数
         */
        data object PaymentFailed : Subscription()

        /** 订阅验证失败 (例如，收据无效)。
         * 使用元数据提供更详细信息，例如：
         * - error_subtype: "invalid_receipt", "tampered_receipt" 等
         * - receipt_id: 收据ID
         */
        data object ValidationFailed : Subscription()

        /** 通用订阅错误。 */
        data object General : Subscription()
    }

    /**
     * 通用操作错误 (CRUD等)
     *
     * 注意：已从多个错误类型简化为仅保留四个核心类型。
     * 使用元数据提供更具体的错误信息，参见 MetadataKeyGuide.OperationGuide。
     */
    sealed class Operation : GlobalErrorType() {
        /** 操作被取消。 */
        data object Cancelled : Operation()

        /** 操作超时。 */
        data object Timeout : Operation()

        /** 操作冲突。 */
        data object Conflict : Operation()

        /** 无效操作。 */
        data object Invalid : Operation()

        /** 通用操作错误。 */
        data object General : Operation()
    }

    /**
     * 数据同步错误
     */
    sealed class Sync : GlobalErrorType() {
        /** 同步操作失败。 */
        data object Failed : Sync()

        /** 同步期间发生数据冲突。 */
        data object Conflict : Sync()
    }

    /**
     * AI服务相关错误
     */
    sealed class AI : GlobalErrorType() {
        /** AI服务当前不可用。 */
        data object ServiceUnavailable : AI()

        /** 已超出AI服务配额。 */
        data object QuotaExceeded : AI()

        /** 无效的AI请求。 */
        data object InvalidRequest : AI()

        /** 通用AI错误。 */
        data object General : AI()

        /** 模型未找到。 */
        data object ModelNotFound : AI()

        /** 配置无效。 */
        data object ConfigurationInvalid : AI()

        /** 处理错误。 */
        data object ProcessingError : AI()

        /** 令牌已过期。 */
        data object TokenExpired : AI()

        /** 请求限制已超出。 */
        data object RequestLimitExceeded : AI()
    }

    /**
     * 用户相关业务错误
     */
    sealed class User : GlobalErrorType() { // Note: isCategoryOf maps this to ErrorCategory.BUSINESS
        /** 未找到用户。 */
        data object NotFound : User()

        /** 对用户执行的操作无效。 */
        data object InvalidOperation : User()
    }

    /**
     * 功能可用性错误
     */
    sealed class Feature : GlobalErrorType() {
        /** 该功能当前不可用。 */
        data object Unavailable : Feature()

        /** 需要升级才能使用该功能。 */
        data object RequiresUpgrade : Feature()
    }

    /**
     * 未知或未分类的错误
     */
    data object Unknown : GlobalErrorType()

    /**
     * 通用错误，当错误无法归类时使用
     */
    sealed class General : GlobalErrorType() {
        /**
         * 通用未知错误
         */
        data object Unknown : General()

        /**
         * 意外错误
         */
        data object Unexpected : General()
    }
}
