# core Module - 文件结构

> **版本**: v2.0 - 核心功能库
> **状态**: ✅ 生产就绪
> **最后更新**: 2025-06-25

## 📁 目录树结构

```
core/
└── src/main/kotlin/com/example/gymbro/core/
    ├── ai/
    │   ├── config/PromptConfig.kt
    │   ├── prompt/
    │   │   ├── builder/LayeredPromptBuilder.kt
    │   │   ├── config/PromptConfigManager.kt
    │   │   ├── function/GymBroFunctionsV2.kt
    │   │   ├── memory/MemoryIntegrator.kt
    │   │   └── ...
    │   └── tokenizer/TokenizerService.kt
    ├── autosave/
    │   ├── AutoSaveManager.kt
    │   ├── config/AutoSaveConfig.kt
    │   └── ...
    ├── error/
    │   ├── types/ModernResult.kt
    │   ├── types/ModernDataError.kt
    │   ├── types/GlobalErrorType.kt
    │   ├── recovery/RecoveryStrategy.kt
    │   ├── ModernErrorHandler.kt
    │   └── ...
    ├── logging/
    │   ├── Logger.kt
    │   └── TimberManager.kt
    ├── network/
    │   └── NetworkMonitor.kt
    ├── resources/
    │   └── ResourceProvider.kt
    ├── theme/
    │   └── ThemeManager.kt
    └── ui/
        └── text/UiText.kt
```

## 🎯 模块职责

- **核心功能**:
    - **错误处理**: 定义了`ModernResult`和`ModernDataError`，提供统一的、健壮的错误处理框架。
    - **日志记录**: 提供`Logger`接口和`TimberManager`，实现可配置、可扩展的日志系统。
    - **资源管理**: 通过`ResourceProvider`和`UiText`，实现与Android框架解耦的文本和资源管理。
    - **AI Prompt构建**: 包含`LayeredPromptBuilder`和`PromptRegistry`，负责动态构建和管理AI模型的提示词。
    - **自动保存**: 提供`AutoSaveManager`，实现可配置的数据自动保存和恢复机制。
    - **网络监控**: `NetworkMonitor`提供网络状态的实时监控。
    - **主题管理**: `ThemeManager`负责动态主题切换和持久化。
- **关键组件**:
    - `ModernResult`: 封装异步操作结果（Success, Error, Loading）的密封类。
    - `ModernErrorHandler`: 统一的错误处理器，将技术错误转换为用户友好的消息。
    - `Logger`: 日志记录接口，解耦具体实现。
    - `UiText`: UI文本的抽象表示，支持国际化和动态文本。
    - `LayeredPromptBuilder`: 核心的AI提示词构建器。
    - `AutoSaveManager`: 自动保存管理器。

## 🔗 依赖关系

- **依赖模块**:
    - 无，`core`是项目的最底层模块之一，不依赖其他业务模块。
- **被依赖**:
    - 几乎所有其他模块（`data`, `domain`, `features:*`, `core-network`等）都依赖此模块获取核心工具和服务。

## 📋 开发规范

- **错误处理**:
    ```kotlin
    // 所有可能失败的操作都应返回ModernResult
    suspend fun fetchData(): ModernResult<MyData> {
        return try {
            val data = api.fetch()
            ModernResult.Success(data)
        } catch (e: Exception) {
            ModernResult.Error(errorHandler.handle(e))
        }
    }
    ```
- **日志记录**:
    ```kotlin
    // 使用注入的Logger接口记录日志
    class MyUseCase @Inject constructor(private val logger: Logger) {
        fun execute() {
            logger.tag("MyUseCase").d("Executing...")
        }
    }
    ```
- **UI文本**:
    ```kotlin
    // 在ViewModel中创建UiText，在View中解析
    val title = UiText.StringResource(R.string.my_title)
    // 在Composable中
    Text(text = title.asString())
    ```

## 🚀 开发状态

- **完成功能**:
    - ✅ 统一错误处理框架 (`ModernResult`, `ModernErrorHandler`)。
    - ✅ 可扩展的日志系统 (`Logger`, `TimberManager`)。
    - ✅ 平台无关的资源管理 (`ResourceProvider`, `UiText`)。
    - ✅ 动态AI Prompt构建系统。
    - ✅ 可配置的自动保存机制。
    - ✅ 网络状态监控。
    - ✅ 动态主题管理。
- **进行中功能**:
    - 🔄 完善错误恢复策略注册表 (`RecoveryStrategyRegistry`)。
- **计划功能**:
    - 📋 增加对更多服务的抽象（如文件、地理位置）。