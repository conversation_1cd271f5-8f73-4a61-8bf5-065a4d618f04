package com.example.gymbro.data.local.entity

import androidx.room.*

/**
 * 聊天原始数据表
 * 存储完整的聊天消息内容和元数据
 *
 * 作为FTS5和VSS搜索的数据源，遵循三表分离设计原则
 * 与ChatSessionEntity建立外键关联
 */
@Entity(
    tableName = "chat_raw",
    foreignKeys = [
        ForeignKey(
            entity = ChatSessionEntity::class,
            parentColumns = ["id"],
            childColumns = ["session_id"],
            onDelete = ForeignKey.CASCADE,
        ),
    ],
    indices = [
        Index(value = ["session_id"]),
        Index(value = ["role"]),
        Index(value = ["timestamp"]),
        Index(value = ["session_id", "timestamp"]),
    ],
)
data class ChatRaw(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    /**
     * 会话ID
     * 用于关联同一对话中的多条消息
     */
    @ColumnInfo(name = "session_id")
    val sessionId: String,
    /**
     * 消息角色
     * "user" - 用户消息
     * "assistant" - AI助手消息
     */
    @ColumnInfo(name = "role")
    val role: String,
    /**
     * 消息内容
     * 用于FTS5全文搜索和向量化
     */
    @ColumnInfo(name = "content")
    val content: String,
    /**
     * 消息时间戳
     * 用于排序和数据清理
     */
    @ColumnInfo(name = "timestamp")
    val timestamp: Long = System.currentTimeMillis(),
    /**
     * 元数据
     * 存储额外的结构化信息，如来源、类型、标签等
     */
    @ColumnInfo(name = "metadata")
    val metadata: Map<String, Any> = emptyMap(),
    /**
     * 消息唯一标识符
     * P0-1修复：确保Stream ID一致性
     * 用于关联UI生成的ID与SSE回包ID
     */
    @ColumnInfo(name = "message_id")
    val messageId: String =
        com.example.gymbro.core.util.Constants.MessageId
            .generate(),
    /**
     * 回复的消息ID
     * Task2-CoachContext数据中心集成：消息关联字段
     * 用于建立AI消息与用户消息的关联关系，支持消息追溯
     */
    @ColumnInfo(name = "in_reply_to_message_id")
    val inReplyToMessageId: String? = null,

    /**
     * 思考节点数据
     * 存储 ThinkingBox 的思考过程节点，用于历史回放
     * JSON 格式存储 RenderableNode 列表，仅对 AI 消息有效
     */
    @ColumnInfo(name = "thinking_nodes")
    val thinkingNodes: String? = null,

    /**
     * 最终富文本内容
     * 存储 AI 消息的最终 Markdown 内容，用于快速显示和搜索
     * 仅对 AI 消息有效，从 <final> 标签中提取
     */
    @ColumnInfo(name = "final_markdown")
    val finalMarkdown: String? = null,
) {
    companion object {
        /**
         * 用户消息角色常量
         */
        const val ROLE_USER = "user"

        /**
         * AI助手消息角色常量
         */
        const val ROLE_ASSISTANT = "assistant"
    }

    /**
     * 检查是否为用户消息
     */
    fun isUserMessage(): Boolean = role == ROLE_USER

    /**
     * 检查是否为AI消息
     */
    fun isAssistantMessage(): Boolean = role == ROLE_ASSISTANT
}

/**
 * 带向量的聊天消息数据类
 * 用于向量搜索查询结果
 */
data class ChatRawWithVector(
    @ColumnInfo(name = "id")
    val id: Long,
    @ColumnInfo(name = "session_id")
    val sessionId: String,
    @ColumnInfo(name = "role")
    val role: String,
    @ColumnInfo(name = "content")
    val content: String,
    @ColumnInfo(name = "timestamp")
    val timestamp: Long,
    @ColumnInfo(name = "metadata")
    val metadata: Map<String, Any>,
    @ColumnInfo(name = "message_id")
    val messageId: String,
    @ColumnInfo(name = "in_reply_to_message_id")
    val inReplyToMessageId: String?,
    @ColumnInfo(name = "thinking_nodes")
    val thinkingNodes: String?,
    @ColumnInfo(name = "final_markdown")
    val finalMarkdown: String?,
    @ColumnInfo(name = "vector", typeAffinity = ColumnInfo.BLOB)
    val vector: ByteArray?,
) {
    /**
     * 转换为ChatRaw
     */
    fun toChatRaw(): ChatRaw =
        ChatRaw(
            id = id,
            sessionId = sessionId,
            role = role,
            content = content,
            timestamp = timestamp,
            metadata = metadata,
            messageId = messageId,
            inReplyToMessageId = inReplyToMessageId,
            thinkingNodes = thinkingNodes,
            finalMarkdown = finalMarkdown,
        )

    /**
     * 重写equals方法
     * ByteArray需要特殊处理
     */
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as ChatRawWithVector

        if (id != other.id) return false
        if (sessionId != other.sessionId) return false
        if (role != other.role) return false
        if (content != other.content) return false
        if (timestamp != other.timestamp) return false
        if (metadata != other.metadata) return false
        if (messageId != other.messageId) return false
        if (inReplyToMessageId != other.inReplyToMessageId) return false
        if (vector != null) {
            if (other.vector == null) return false
            if (!vector.contentEquals(other.vector)) return false
        } else if (other.vector != null) {
            return false
        }

        return true
    }

    /**
     * 重写hashCode方法
     * ByteArray需要特殊处理
     */
    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + sessionId.hashCode()
        result = 31 * result + role.hashCode()
        result = 31 * result + content.hashCode()
        result = 31 * result + timestamp.hashCode()
        result = 31 * result + metadata.hashCode()
        result = 31 * result + messageId.hashCode()
        result = 31 * result + (inReplyToMessageId?.hashCode() ?: 0)
        result = 31 * result + (vector?.contentHashCode() ?: 0)
        return result
    }
}
