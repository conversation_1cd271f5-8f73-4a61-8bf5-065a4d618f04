package com.example.gymbro.data.ai.api

import com.example.gymbro.data.remote.subscription.dto.SubscriptionDto
import com.example.gymbro.data.remote.subscription.dto.SubscriptionResponseDto
import com.example.gymbro.data.remote.subscription.dto.SubscriptionStatusDto
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query

/**
 * 订阅API服务接口
 * 定义与订阅相关的网络请求
 */
interface SubscriptionApi {
    /**
     * 获取用户的订阅信息
     *
     * @param userId 用户ID
     * @return 订阅信息DTO
     */
    @GET("subscriptions/user/{userId}")
    suspend fun getUserSubscription(
        @Path("userId") userId: String,
    ): Response<SubscriptionDto>

    /**
     * 创建新的订阅
     *
     * @param userId 用户ID
     * @param planId 订阅计划ID
     * @param paymentMethod 支付方式
     * @return 订阅创建结果DTO
     */
    @POST("subscriptions/create")
    suspend fun createSubscription(
        @Query("userId") userId: String,
        @Query("planId") planId: String,
        @Query("paymentMethod") paymentMethod: String,
    ): Response<SubscriptionResponseDto>

    /**
     * 取消订阅
     *
     * @param subscriptionId 订阅ID
     * @return 取消结果状态DTO
     */
    @POST("subscriptions/{subscriptionId}/cancel")
    suspend fun cancelSubscription(
        @Path("subscriptionId") subscriptionId: String,
    ): Response<SubscriptionStatusDto>

    /**
     * 验证订阅状态
     *
     * @param subscriptionId 订阅ID
     * @return 验证结果状态DTO
     */
    @GET("subscriptions/{subscriptionId}/verify")
    suspend fun verifySubscription(
        @Path("subscriptionId") subscriptionId: String,
    ): Response<SubscriptionStatusDto>
}
