package com.example.gymbro.data.workout.plan.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.example.gymbro.data.workout.plan.entity.PlanTemplateEntity
import kotlinx.coroutines.flow.Flow

/**
 * 计划模板关联数据访问对象 - PlanDB DAO
 *
 * 基于 05_Final_Database_Architecture.md 设计
 * 提供计划与模板关联管理功能
 */
@Dao
interface PlanTemplateDao {

    // ==================== 基础 CRUD ====================

    @Query("SELECT * FROM plan_templates WHERE id = :planTemplateId")
    suspend fun getPlanTemplateById(planTemplateId: String): PlanTemplateEntity?

    @Query("SELECT * FROM plan_templates WHERE planDayId = :planDayId ORDER BY `order` ASC")
    fun getPlanTemplatesByDay(planDayId: String): Flow<List<PlanTemplateEntity>>

    @Query("SELECT * FROM plan_templates WHERE planDayId = :planDayId ORDER BY `order` ASC")
    suspend fun getPlanTemplatesByDaySync(planDayId: String): List<PlanTemplateEntity>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPlanTemplate(planTemplate: PlanTemplateEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPlanTemplates(planTemplates: List<PlanTemplateEntity>)

    @Update
    suspend fun updatePlanTemplate(planTemplate: PlanTemplateEntity)

    @Query("DELETE FROM plan_templates WHERE id = :planTemplateId")
    suspend fun deletePlanTemplate(planTemplateId: String)

    @Query("DELETE FROM plan_templates WHERE planDayId = :planDayId")
    suspend fun deleteAllPlanTemplates(planDayId: String)

    // ==================== 特定查询 ====================

    @Query("SELECT * FROM plan_templates WHERE planDayId = :planDayId AND templateId = :templateId")
    suspend fun getPlanTemplateByTemplateId(planDayId: String, templateId: String): PlanTemplateEntity?

    @Query("SELECT * FROM plan_templates WHERE templateId = :templateId")
    fun getPlanTemplatesByTemplate(templateId: String): Flow<List<PlanTemplateEntity>>

    // ==================== 统计查询 ====================

    @Query("SELECT COUNT(*) FROM plan_templates WHERE planDayId = :planDayId")
    suspend fun getPlanTemplateCount(planDayId: String): Int

    @Query("SELECT MAX(`order`) FROM plan_templates WHERE planDayId = :planDayId")
    suspend fun getMaxOrderInDay(planDayId: String): Int?

    @Query("SELECT COUNT(DISTINCT templateId) FROM plan_templates WHERE planDayId = :planDayId")
    suspend fun getUniqueTemplateCount(planDayId: String): Int

    // ==================== 批量操作 ====================

    @Query(
        "UPDATE plan_templates SET `order` = `order` + 1 WHERE planDayId = :planDayId AND `order` >= :fromOrder",
    )
    suspend fun shiftTemplateOrdersUp(planDayId: String, fromOrder: Int)

    @Query(
        "UPDATE plan_templates SET `order` = `order` - 1 WHERE planDayId = :planDayId AND `order` > :fromOrder",
    )
    suspend fun shiftTemplateOrdersDown(planDayId: String, fromOrder: Int)
}
