package com.example.gymbro.data.ai.retry

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.features.FeatureErrors
import com.example.gymbro.core.ui.text.UiText
import kotlinx.coroutines.delay
import timber.log.Timber
import kotlin.math.pow
import kotlin.random.Random

/**
 * AI请求重试策略
 *
 * Sprint 0要求：实现指数退避和降级策略
 * - Function Call失败时回退到普通聊天
 * - 网络超时时指数退避重试
 * - 错误率监控和熔断
 */
class AiRetryStrategy {
    companion object {
        private const val MAX_RETRIES = 3
        private const val BASE_DELAY_MS = 1000L
        private const val MAX_DELAY_MS = 10000L
        private const val JITTER_FACTOR = 0.1

        // 错误率监控
        private const val ERROR_RATE_WINDOW_SIZE = 10
        private const val MAX_ERROR_RATE = 0.5 // 50%
    }

    // 错误率统计窗口
    private val recentResults = mutableListOf<Boolean>()
    private var funcCallErrorCount = 0
    private var totalFuncCallAttempts = 0

    /**
     * 执行带重试的AI请求
     * ✅ 关键修复：确保重试不会破坏UI状态管理
     */
    suspend fun <T> executeWithRetry(
        operationName: String,
        maxRetries: Int = MAX_RETRIES,
        operation: suspend () -> ModernResult<T>,
    ): ModernResult<T> {
        var lastError: Throwable? = null

        repeat(maxRetries + 1) { attempt ->
            try {
                Timber.d("执行AI请求: $operationName, 尝试次数: ${attempt + 1}")

                // ✅ 注意：重试策略不再干预UI状态管理
                // UI状态重置由ViewModel层的ensureThinkingMessageExists()处理

                val result = operation()

                // 记录成功
                recordResult(true)

                when (result) {
                    is ModernResult.Success -> {
                        if (attempt > 0) {
                            Timber.i("AI请求重试成功: $operationName, 重试次数: $attempt")
                        }
                        return result
                    }
                    is ModernResult.Error -> {
                        recordResult(false)

                        if (shouldRetry(result.error, attempt)) {
                            val delayTime = calculateBackoffDelay(attempt)
                            Timber.w("AI请求失败，${delayTime}ms后重试: $operationName, attempt=${attempt + 1}")
                            delay(delayTime)
                            return@repeat
                        } else {
                            Timber.e("AI请求失败，不再重试: $operationName")
                            return result
                        }
                    }
                    is ModernResult.Loading -> {
                        // Loading状态不应该在这里出现
                        Timber.w("AI请求返回Loading状态，继续重试: $operationName")
                        delay(calculateBackoffDelay(attempt))
                        return@repeat
                    }
                }
            } catch (e: Exception) {
                lastError = e
                recordResult(false)

                if (attempt < maxRetries && shouldRetryException(e)) {
                    val delayTime = calculateBackoffDelay(attempt)
                    Timber.w(e, "AI请求异常，${delayTime}ms后重试: $operationName")
                    delay(delayTime)
                    return@repeat
                } else {
                    Timber.e(e, "AI请求异常，不再重试: $operationName")
                    return ModernResult.Error(
                        FeatureErrors.CoachError.processingFailed(
                            operationName = "AiRetryStrategy.$operationName",
                            message = UiText.DynamicString("AI服务请求失败，请稍后重试"),
                            processType = "retry_exhausted",
                            reason = "exception_occurred",
                            cause = e,
                            metadataMap =
                            mapOf(
                                "max_retries" to MAX_RETRIES,
                                "error_rate" to getCurrentErrorRate(),
                            ),
                        ),
                    )
                }
            }
        }

        // 所有重试都失败
        return ModernResult.Error(
            FeatureErrors.CoachError.processingFailed(
                operationName = "AiRetryStrategy.$operationName",
                message = UiText.DynamicString("AI服务请求失败，请稍后重试"),
                processType = "retry_exhausted",
                reason = "max_retries_exceeded",
                cause = lastError,
                metadataMap =
                mapOf(
                    "max_retries" to MAX_RETRIES,
                    "error_rate" to getCurrentErrorRate(),
                ),
            ),
        )
    }

    /**
     * 处理Function Call降级策略
     */
    suspend fun handleFunctionCallFallback(
        functionName: String,
        arguments: String,
        originalError: Throwable,
    ): ModernResult<String> {
        totalFuncCallAttempts++
        funcCallErrorCount++

        val funcErrorRate =
            if (totalFuncCallAttempts > 0) {
                funcCallErrorCount.toDouble() / totalFuncCallAttempts
            } else {
                0.0
            }

        Timber.w("Function Call失败，降级为普通文本: $functionName, 错误率: ${"%.2f".format(funcErrorRate * 100)}%")

        // 生成降级文本消息
        val fallbackMessage =
            when (functionName) {
                "start_workout_session" -> "我来帮你开始训练。请准备好开始你的健身计划！"
                "add_exercise_to_session" -> "我建议你添加一个练习到你的训练中。"
                "log_exercise_set" -> "很好！请记录你完成的这一组练习。"
                "search_exercises" -> "我来帮你找一些适合的练习动作。"
                "get_workout_history" -> "我可以帮你查看之前的训练记录。"
                "complete_workout_session" -> "恭喜！你完成了今天的训练。"
                "set_rest_timer" -> "现在休息一下，准备下一组练习。"
                "get_exercise_recommendations" -> "我来为你推荐一些适合的练习。"
                else -> "抱歉，我暂时无法执行这个操作，但我可以为你提供其他帮助。"
            }

        return ModernResult.Success(fallbackMessage)
    }

    /**
     * 计算指数退避延迟时间
     */
    private fun calculateBackoffDelay(attempt: Int): Long {
        val exponentialDelay = BASE_DELAY_MS * (2.0.pow(attempt.toDouble())).toLong()
        val cappedDelay = minOf(exponentialDelay, MAX_DELAY_MS)

        // 添加随机抖动，避免雷群效应
        val jitter = (cappedDelay * JITTER_FACTOR * Random.nextDouble()).toLong()
        val finalDelay = cappedDelay + jitter

        return finalDelay
    }

    /**
     * 判断是否应该重试
     */
    private fun shouldRetry(
        error: com.example.gymbro.core.error.types.ModernDataError,
        attempt: Int,
    ): Boolean {
        if (attempt >= MAX_RETRIES) return false

        // 检查错误率
        if (getCurrentErrorRate() > MAX_ERROR_RATE) {
            Timber.w("错误率过高，暂停重试: ${getCurrentErrorRate()}")
            return false
        }

        // 根据错误类型决定是否重试
        return when {
            // 网络错误可以重试
            error.toString().contains("timeout", ignoreCase = true) -> true
            error.toString().contains("network", ignoreCase = true) -> true
            error.toString().contains("connection", ignoreCase = true) -> true

            // 认证错误不重试
            error.toString().contains("auth", ignoreCase = true) -> false
            error.toString().contains("401", ignoreCase = true) -> false
            error.toString().contains("403", ignoreCase = true) -> false

            // 其他错误可以重试
            else -> true
        }
    }

    /**
     * 判断异常是否应该重试
     */
    private fun shouldRetryException(exception: Throwable): Boolean =
        when (exception) {
            is java.net.SocketTimeoutException -> true
            is java.net.ConnectException -> true
            is java.io.IOException -> true
            is kotlinx.coroutines.TimeoutCancellationException -> true
            else -> false
        }

    /**
     * 记录请求结果
     */
    private fun recordResult(success: Boolean) {
        recentResults.add(success)

        // 保持窗口大小
        if (recentResults.size > ERROR_RATE_WINDOW_SIZE) {
            recentResults.removeFirst()
        }
    }

    /**
     * 获取当前错误率
     */
    private fun getCurrentErrorRate(): Double {
        if (recentResults.isEmpty()) return 0.0

        val errorCount = recentResults.count { !it }
        return errorCount.toDouble() / recentResults.size
    }

    /**
     * 获取Function Call错误率
     */
    fun getFunctionCallErrorRate(): Double =
        if (totalFuncCallAttempts > 0) {
            funcCallErrorCount.toDouble() / totalFuncCallAttempts
        } else {
            0.0
        }

    /**
     * 重置统计
     */
    fun resetStats() {
        recentResults.clear()
        funcCallErrorCount = 0
        totalFuncCallAttempts = 0
        Timber.d("AI重试策略统计已重置")
    }
}
