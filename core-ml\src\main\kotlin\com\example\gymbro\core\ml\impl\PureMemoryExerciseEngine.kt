package com.example.gymbro.core.ml.impl

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.features.FeatureErrors
import com.example.gymbro.core.ml.interfaces.ComplementType
import com.example.gymbro.core.ml.interfaces.ExerciseData
import com.example.gymbro.core.ml.interfaces.ExerciseMatchResult
import com.example.gymbro.core.ml.interfaces.ExerciseMatchingEngine
import com.example.gymbro.core.ml.interfaces.ExerciseRecommendation
import com.example.gymbro.core.ml.interfaces.MatchingEngineStats
import com.example.gymbro.core.ui.text.UiText
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 纯内存动作匹配引擎实现 - Core-ML Layer
 *
 * 使用简化的内部数据结构，不依赖复杂的domain模型
 * 支持：
 * - 基于文本的动作搜索
 * - 智能动作推荐
 * - 互补动作查找
 * - 批量匹配处理
 */
@Singleton
class PureMemoryExerciseEngine @Inject constructor() : ExerciseMatchingEngine {

    private val exercises = ConcurrentHashMap<String, ExerciseData>()
    private val nameIndex = ConcurrentHashMap<String, String>() // 标准化名称 -> ID
    private val muscleIndex = ConcurrentHashMap<String, MutableSet<String>>() // 肌群 -> ID集合
    private val equipmentIndex = ConcurrentHashMap<String, MutableSet<String>>() // 器械 -> ID集合
    private val mutex = Mutex()
    private var totalMatches = 0L
    private var totalMatchTime = 0L
    private var cacheHits = 0L
    private var lastUpdateTime = System.currentTimeMillis()

    override suspend fun searchExercises(
        queryText: String,
        topK: Int,
        threshold: Float,
    ): ModernResult<List<ExerciseMatchResult>> {
        return try {
            val startTime = System.currentTimeMillis()

            if (queryText.isBlank()) {
                return ModernResult.Error(
                    FeatureErrors.CoachError.invalidInput(
                        operationName = "PureMemoryExerciseEngine.searchExercises",
                        message = UiText.DynamicString("查询文本不能为空"),
                        inputType = "empty_query",
                        value = queryText,
                    ),
                )
            }

            val normalizedQuery = normalizeText(queryText)
            val results = mutableListOf<ExerciseMatchResult>()

            // 1. 精确名称匹配
            val exactMatch = findExactNameMatch(normalizedQuery)
            if (exactMatch != null && exactMatch.similarity >= threshold) {
                results.add(exactMatch)
            }

            // 2. 肌群匹配
            val muscleMatches = findMuscleMatches(normalizedQuery, threshold)
            results.addAll(muscleMatches)

            // 3. 器械匹配
            val equipmentMatches = findEquipmentMatches(normalizedQuery, threshold)
            results.addAll(equipmentMatches)

            // 4. 模糊匹配
            val fuzzyMatches = findFuzzyMatches(normalizedQuery, threshold)
            results.addAll(fuzzyMatches)

            // 去重、排序、限制数量
            val finalResults = results
                .distinctBy { it.exerciseId }
                .sortedByDescending { it.similarity }
                .take(topK)

            updateMatchStats(System.currentTimeMillis() - startTime)

            Timber.d("PureMemoryExerciseEngine: 搜索完成 query='$queryText', results=${finalResults.size}")
            ModernResult.Success(finalResults)
        } catch (e: Exception) {
            Timber.e(e, "PureMemoryExerciseEngine: 搜索异常")
            ModernResult.Error(
                FeatureErrors.CoachError.processingFailed(
                    operationName = "PureMemoryExerciseEngine.searchExercises",
                    message = UiText.DynamicString("动作搜索失败"),
                    processType = "exercise_search",
                    reason = "search_exception",
                    cause = e,
                    metadataMap = mapOf(
                        "query" to queryText,
                        "threshold" to threshold,
                        "top_k" to topK,
                    ),
                ),
            )
        }
    }

    override suspend fun recommendNextExercises(
        currentState: String,
        completedExercises: List<String>,
        targetMuscleGroups: List<String>,
        topK: Int,
    ): ModernResult<List<ExerciseRecommendation>> {
        return try {
            val startTime = System.currentTimeMillis()

            if (exercises.isEmpty()) {
                return ModernResult.Error(
                    FeatureErrors.CoachError.invalidInput(
                        operationName = "PureMemoryExerciseEngine.recommendNextExercises",
                        message = UiText.DynamicString("动作数据库为空"),
                        inputType = "empty_exercise_database",
                        value = "exercises_count=0",
                    ),
                )
            }

            val recommendations = mutableListOf<ExerciseRecommendation>()

            // 获取未完成的动作
            val availableExercises = exercises.values.filter { it.id !in completedExercises }

            for (exercise in availableExercises) {
                val score = calculateRecommendationScore(
                    exercise = exercise,
                    currentState = currentState,
                    targetMuscleGroups = targetMuscleGroups,
                    completedExercises = completedExercises,
                )

                if (score > 0.3f) { // 推荐阈值
                    recommendations.add(
                        ExerciseRecommendation(
                            exerciseId = exercise.id,
                            exerciseName = exercise.name,
                            recommendationScore = score,
                            recommendationReason = generateRecommendationReason(
                                exercise,
                                currentState,
                                targetMuscleGroups,
                            ),
                            contextualFit = calculateContextualFit(exercise, currentState),
                            progressionLevel = determineProgressionLevel(exercise, completedExercises),
                            estimatedDuration = estimateDuration(exercise),
                            metadata = mapOf(
                                "primary_muscles" to exercise.primaryMuscles,
                                "equipment" to exercise.equipment,
                                "difficulty" to exercise.difficulty,
                            ),
                        ),
                    )
                }
            }

            val finalRecommendations = recommendations
                .sortedByDescending { it.recommendationScore }
                .take(topK)

            updateMatchStats(System.currentTimeMillis() - startTime)

            Timber.d("PureMemoryExerciseEngine: 推荐完成 recommendations=${finalRecommendations.size}")
            ModernResult.Success(finalRecommendations)
        } catch (e: Exception) {
            Timber.e(e, "PureMemoryExerciseEngine: 推荐异常")
            ModernResult.Error(
                FeatureErrors.CoachError.processingFailed(
                    operationName = "PureMemoryExerciseEngine.recommendNextExercises",
                    message = UiText.DynamicString("动作推荐失败"),
                    processType = "exercise_recommendation",
                    reason = "recommendation_exception",
                    cause = e,
                    metadataMap = mapOf(
                        "current_state" to currentState,
                        "completed_count" to completedExercises.size,
                        "target_muscles" to targetMuscleGroups,
                    ),
                ),
            )
        }
    }

    override suspend fun findComplementaryExercises(
        exerciseId: String,
        complementType: ComplementType,
        topK: Int,
    ): ModernResult<List<ExerciseMatchResult>> {
        return try {
            val startTime = System.currentTimeMillis()

            val baseExercise = exercises[exerciseId]
                ?: return ModernResult.Error(
                    FeatureErrors.CoachError.invalidInput(
                        operationName = "PureMemoryExerciseEngine.findComplementaryExercises",
                        message = UiText.DynamicString("找不到指定的动作"),
                        inputType = "exercise_not_found",
                        value = exerciseId,
                    ),
                )

            val complementaryExercises = when (complementType) {
                ComplementType.OPPOSING_MUSCLES -> findOpposingMuscleExercises(baseExercise)
                ComplementType.SAME_MUSCLES -> findSameMuscleExercises(baseExercise)
                ComplementType.COMPOUND_MOVEMENTS -> findCompoundMovements(baseExercise)
                ComplementType.ISOLATION_MOVEMENTS -> findIsolationMovements(baseExercise)
                ComplementType.PROGRESSIVE_OVERLOAD -> findProgressiveOverloadExercises(baseExercise)
            }

            val results = complementaryExercises
                .take(topK)
                .map { exercise ->
                    ExerciseMatchResult(
                        exerciseId = exercise.id,
                        exerciseName = exercise.name,
                        similarity = calculateComplementarySimilarity(baseExercise, exercise, complementType),
                        matchReason = "互补动作: ${complementType.name}",
                        primaryMuscles = exercise.primaryMuscles,
                        equipment = exercise.equipment,
                        difficulty = exercise.difficulty,
                        metadata = mapOf(
                            "complement_type" to complementType.name,
                            "base_exercise" to baseExercise.name,
                        ),
                    )
                }

            updateMatchStats(System.currentTimeMillis() - startTime)

            Timber.d("PureMemoryExerciseEngine: 互补动作查找完成 results=${results.size}")
            ModernResult.Success(results)
        } catch (e: Exception) {
            Timber.e(e, "PureMemoryExerciseEngine: 互补动作查找异常")
            ModernResult.Error(
                FeatureErrors.CoachError.processingFailed(
                    operationName = "PureMemoryExerciseEngine.findComplementaryExercises",
                    message = UiText.DynamicString("互补动作查找失败"),
                    processType = "complementary_exercise_search",
                    reason = "search_exception",
                    cause = e,
                    metadataMap = mapOf(
                        "base_exercise_id" to exerciseId,
                        "complement_type" to complementType.name,
                    ),
                ),
            )
        }
    }

    override suspend fun batchMatch(
        queries: List<String>,
        topK: Int,
    ): ModernResult<List<List<ExerciseMatchResult>>> {
        return try {
            val startTime = System.currentTimeMillis()

            if (queries.isEmpty()) {
                return ModernResult.Error(
                    FeatureErrors.CoachError.invalidInput(
                        operationName = "PureMemoryExerciseEngine.batchMatch",
                        message = UiText.DynamicString("查询列表不能为空"),
                        inputType = "empty_queries_list",
                        value = "size=0",
                    ),
                )
            }

            val batchResults = mutableListOf<List<ExerciseMatchResult>>()

            for (query in queries) {
                val searchResult = searchExercises(query, topK, 0.6f)
                when (searchResult) {
                    is ModernResult.Success -> batchResults.add(searchResult.data)
                    is ModernResult.Error -> return ModernResult.Error(searchResult.error)
                    is ModernResult.Loading -> batchResults.add(emptyList())
                }
            }

            updateMatchStats(System.currentTimeMillis() - startTime)

            Timber.d("PureMemoryExerciseEngine: 批量匹配完成 queries=${queries.size}")
            ModernResult.Success(batchResults)
        } catch (e: Exception) {
            Timber.e(e, "PureMemoryExerciseEngine: 批量匹配异常")
            ModernResult.Error(
                FeatureErrors.CoachError.processingFailed(
                    operationName = "PureMemoryExerciseEngine.batchMatch",
                    message = UiText.DynamicString("批量动作匹配失败"),
                    processType = "batch_exercise_match",
                    reason = "batch_match_exception",
                    cause = e,
                    metadataMap = mapOf("queries_count" to queries.size),
                ),
            )
        }
    }

    override suspend fun updateExercises(exercises: List<ExerciseData>): ModernResult<Unit> {
        return try {
            mutex.withLock {
                for (exercise in exercises) {
                    this.exercises[exercise.id] = exercise
                    updateIndices(exercise)
                }
                lastUpdateTime = System.currentTimeMillis()
            }

            Timber.d("PureMemoryExerciseEngine: 动作数据更新完成 count=${exercises.size}")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "PureMemoryExerciseEngine: 动作数据更新异常")
            ModernResult.Error(
                FeatureErrors.CoachError.processingFailed(
                    operationName = "PureMemoryExerciseEngine.updateExercises",
                    message = UiText.DynamicString("动作数据更新失败"),
                    processType = "exercise_data_update",
                    reason = "update_exception",
                    cause = e,
                    metadataMap = mapOf("exercises_count" to exercises.size),
                ),
            )
        }
    }

    override fun getMatchingStats(): MatchingEngineStats {
        return MatchingEngineStats(
            totalExercises = exercises.size,
            vectorizedExercises = exercises.values.count { it.vector != null },
            averageMatchTimeMs = if (totalMatches > 0) totalMatchTime / totalMatches else 0L,
            cacheHitRate = if (totalMatches > 0) cacheHits.toFloat() / totalMatches else 0f,
            lastUpdateTime = lastUpdateTime,
        )
    }

    // ========== 私有辅助方法 ==========

    private fun normalizeText(text: String): String {
        return text.trim().lowercase()
            .replace(Regex("[^\\w\\s]"), "")
            .replace(Regex("\\s+"), " ")
    }

    private fun updateIndices(exercise: ExerciseData) {
        // 名称索引
        nameIndex[normalizeText(exercise.name)] = exercise.id

        // 肌群索引
        (exercise.primaryMuscles + exercise.secondaryMuscles).forEach { muscle ->
            muscleIndex.getOrPut(normalizeText(muscle)) { mutableSetOf() }.add(exercise.id)
        }

        // 器械索引
        equipmentIndex.getOrPut(normalizeText(exercise.equipment)) { mutableSetOf() }.add(exercise.id)
    }

    private fun findExactNameMatch(normalizedQuery: String): ExerciseMatchResult? {
        val exerciseId = nameIndex[normalizedQuery] ?: return null
        val exercise = exercises[exerciseId] ?: return null

        return ExerciseMatchResult(
            exerciseId = exercise.id,
            exerciseName = exercise.name,
            similarity = 1.0f,
            matchReason = "精确名称匹配",
            primaryMuscles = exercise.primaryMuscles,
            equipment = exercise.equipment,
            difficulty = exercise.difficulty,
        )
    }

    private fun findMuscleMatches(normalizedQuery: String, threshold: Float): List<ExerciseMatchResult> {
        val results = mutableListOf<ExerciseMatchResult>()

        for ((muscle, exerciseIds) in muscleIndex) {
            val similarity = calculateTextSimilarity(normalizedQuery, muscle)
            if (similarity >= threshold) {
                for (exerciseId in exerciseIds) {
                    exercises[exerciseId]?.let { exercise ->
                        results.add(
                            ExerciseMatchResult(
                                exerciseId = exercise.id,
                                exerciseName = exercise.name,
                                similarity = similarity * 0.8f, // 肌群匹配权重稍低
                                matchReason = "肌群匹配: $muscle",
                                primaryMuscles = exercise.primaryMuscles,
                                equipment = exercise.equipment,
                                difficulty = exercise.difficulty,
                            ),
                        )
                    }
                }
            }
        }

        return results
    }

    private fun findEquipmentMatches(normalizedQuery: String, threshold: Float): List<ExerciseMatchResult> {
        val results = mutableListOf<ExerciseMatchResult>()

        for ((equipment, exerciseIds) in equipmentIndex) {
            val similarity = calculateTextSimilarity(normalizedQuery, equipment)
            if (similarity >= threshold) {
                for (exerciseId in exerciseIds) {
                    exercises[exerciseId]?.let { exercise ->
                        results.add(
                            ExerciseMatchResult(
                                exerciseId = exercise.id,
                                exerciseName = exercise.name,
                                similarity = similarity * 0.7f, // 器械匹配权重更低
                                matchReason = "器械匹配: $equipment",
                                primaryMuscles = exercise.primaryMuscles,
                                equipment = exercise.equipment,
                                difficulty = exercise.difficulty,
                            ),
                        )
                    }
                }
            }
        }

        return results
    }

    private fun findFuzzyMatches(normalizedQuery: String, threshold: Float): List<ExerciseMatchResult> {
        val results = mutableListOf<ExerciseMatchResult>()

        for (exercise in exercises.values) {
            val nameSimilarity = calculateTextSimilarity(normalizedQuery, normalizeText(exercise.name))
            val descSimilarity = calculateTextSimilarity(normalizedQuery, normalizeText(exercise.description))
            val maxSimilarity = maxOf(nameSimilarity, descSimilarity)

            if (maxSimilarity >= threshold) {
                results.add(
                    ExerciseMatchResult(
                        exerciseId = exercise.id,
                        exerciseName = exercise.name,
                        similarity = maxSimilarity * 0.9f,
                        matchReason = "模糊匹配",
                        primaryMuscles = exercise.primaryMuscles,
                        equipment = exercise.equipment,
                        difficulty = exercise.difficulty,
                    ),
                )
            }
        }

        return results
    }

    private fun calculateTextSimilarity(text1: String, text2: String): Float {
        if (text1 == text2) return 1.0f
        if (text1.isEmpty() || text2.isEmpty()) return 0.0f

        // 子串匹配
        if (text1.contains(text2) || text2.contains(text1)) {
            return 0.8f
        }

        // 简化的编辑距离
        val maxLen = maxOf(text1.length, text2.length)
        val distance = levenshteinDistance(text1, text2)
        return maxOf(0f, 1.0f - distance.toFloat() / maxLen)
    }

    private fun levenshteinDistance(s1: String, s2: String): Int {
        val dp = Array(s1.length + 1) { IntArray(s2.length + 1) }

        for (i in 0..s1.length) dp[i][0] = i
        for (j in 0..s2.length) dp[0][j] = j

        for (i in 1..s1.length) {
            for (j in 1..s2.length) {
                val cost = if (s1[i - 1] == s2[j - 1]) 0 else 1
                dp[i][j] = minOf(
                    dp[i - 1][j] + 1,
                    dp[i][j - 1] + 1,
                    dp[i - 1][j - 1] + cost,
                )
            }
        }

        return dp[s1.length][s2.length]
    }

    private fun calculateRecommendationScore(
        exercise: ExerciseData,
        currentState: String,
        targetMuscleGroups: List<String>,
        completedExercises: List<String>,
    ): Float {
        var score = 0.5f // 基础分数

        // 目标肌群匹配
        val muscleMatch = exercise.primaryMuscles.intersect(targetMuscleGroups.toSet())
        score += muscleMatch.size * 0.2f

        // 状态相关性（简化）
        if (currentState.contains("力量") && exercise.description.contains("力量")) {
            score += 0.2f
        }
        if (currentState.contains("有氧") && exercise.description.contains("有氧")) {
            score += 0.2f
        }

        // 难度适配（简化）
        when (exercise.difficulty) {
            "初级" -> score += 0.1f
            "中级" -> score += 0.15f
            "高级" -> score += 0.05f
        }

        return minOf(1.0f, score)
    }

    private fun generateRecommendationReason(
        exercise: ExerciseData,
        currentState: String,
        targetMuscleGroups: List<String>,
    ): String {
        val reasons = mutableListOf<String>()

        if (exercise.primaryMuscles.intersect(targetMuscleGroups.toSet()).isNotEmpty()) {
            reasons.add("目标肌群匹配")
        }

        if (currentState.contains("力量") && exercise.description.contains("力量")) {
            reasons.add("力量训练相关")
        }

        return reasons.ifEmpty { listOf("综合推荐") }.joinToString(", ")
    }

    private fun calculateContextualFit(exercise: ExerciseData, currentState: String): Float {
        // 简化的上下文适配计算
        return when {
            currentState.contains("疲劳") && exercise.difficulty == "初级" -> 0.8f
            currentState.contains("精力充沛") && exercise.difficulty == "高级" -> 0.9f
            else -> 0.7f
        }
    }

    private fun determineProgressionLevel(exercise: ExerciseData, completedExercises: List<String>): String {
        // 基于完成动作数量和难度确定进阶级别
        return when {
            completedExercises.size < 3 -> "入门"
            completedExercises.size < 10 -> "初级"
            completedExercises.size < 20 -> "中级"
            else -> "高级"
        }
    }

    private fun estimateDuration(exercise: ExerciseData): Int {
        // 基于难度和动作类型估算时长
        return when (exercise.difficulty) {
            "初级" -> 10
            "中级" -> 15
            "高级" -> 20
            else -> 12
        }
    }

    private fun findOpposingMuscleExercises(baseExercise: ExerciseData): List<ExerciseData> {
        // 简化的对抗肌群映射
        val opposingMuscles = mapOf(
            "胸部" to listOf("背部"),
            "背部" to listOf("胸部"),
            "二头肌" to listOf("三头肌"),
            "三头肌" to listOf("二头肌"),
            "股四头肌" to listOf("股二头肌"),
            "股二头肌" to listOf("股四头肌"),
        )

        val targetMuscles = baseExercise.primaryMuscles.flatMap { muscle ->
            opposingMuscles[muscle] ?: emptyList()
        }

        return exercises.values.filter { exercise ->
            exercise.id != baseExercise.id &&
                exercise.primaryMuscles.intersect(targetMuscles.toSet()).isNotEmpty()
        }
    }

    private fun findSameMuscleExercises(baseExercise: ExerciseData): List<ExerciseData> {
        return exercises.values.filter { exercise ->
            exercise.id != baseExercise.id &&
                exercise.primaryMuscles.intersect(baseExercise.primaryMuscles.toSet()).isNotEmpty()
        }
    }

    private fun findCompoundMovements(baseExercise: ExerciseData): List<ExerciseData> {
        return exercises.values.filter { exercise ->
            exercise.id != baseExercise.id &&
                exercise.primaryMuscles.size >= 2 // 复合动作通常涉及多个主要肌群
        }
    }

    private fun findIsolationMovements(baseExercise: ExerciseData): List<ExerciseData> {
        return exercises.values.filter { exercise ->
            exercise.id != baseExercise.id &&
                exercise.primaryMuscles.size == 1 // 孤立动作通常只涉及一个主要肌群
        }
    }

    private fun findProgressiveOverloadExercises(baseExercise: ExerciseData): List<ExerciseData> {
        return exercises.values.filter { exercise ->
            exercise.id != baseExercise.id &&
                exercise.primaryMuscles.intersect(baseExercise.primaryMuscles.toSet()).isNotEmpty() &&
                isProgressiveOverload(baseExercise, exercise)
        }
    }

    private fun isProgressiveOverload(baseExercise: ExerciseData, targetExercise: ExerciseData): Boolean {
        // 简化的渐进超负荷判断
        val difficultyOrder = mapOf("初级" to 1, "中级" to 2, "高级" to 3)
        val baseDifficulty = difficultyOrder[baseExercise.difficulty] ?: 2
        val targetDifficulty = difficultyOrder[targetExercise.difficulty] ?: 2

        return targetDifficulty > baseDifficulty
    }

    private fun calculateComplementarySimilarity(
        baseExercise: ExerciseData,
        targetExercise: ExerciseData,
        complementType: ComplementType,
    ): Float {
        // 根据互补类型计算相似度
        return when (complementType) {
            ComplementType.OPPOSING_MUSCLES -> 0.8f
            ComplementType.SAME_MUSCLES -> 0.9f
            ComplementType.COMPOUND_MOVEMENTS -> 0.7f
            ComplementType.ISOLATION_MOVEMENTS -> 0.6f
            ComplementType.PROGRESSIVE_OVERLOAD -> 0.85f
        }
    }

    private fun updateMatchStats(elapsedTime: Long) {
        totalMatches++
        totalMatchTime += elapsedTime
    }
}
