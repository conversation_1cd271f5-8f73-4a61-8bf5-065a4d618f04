package com.example.gymbro.data.workout.plan.entity

import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.example.gymbro.data.workout.plan.converter.PlanTypeConverters

/**
 * 训练计划实体 - PlanDB 核心实体
 *
 * 基于 05_Final_Database_Architecture.md 设计
 * 存储训练计划的基本信息和调度配置
 */
@Entity(
    tableName = "workout_plans",
    indices = [
        Index("userId"),
        Index("isPublic"),
        Index("isTemplate"),
        Index("isFavorite"),
        Index("createdAt"),
        Index("updatedAt"),
    ],
)
@TypeConverters(PlanTypeConverters::class)
data class PlanEntity(
    @PrimaryKey
    val id: String,

    // 基本信息
    val name: String,
    val description: String?,
    val userId: String,
    val targetGoal: String?, // 训练目标：增肌、减脂、力量等
    val difficultyLevel: Int, // 1-5 难度等级
    val estimatedDuration: Int?, // 预计时长（分钟）

    // 状态标识
    val isPublic: Boolean = false, // 是否公开
    val isTemplate: Boolean = false, // 是否为模板计划
    val isFavorite: Boolean = false, // 是否收藏
    val isAIGenerated: Boolean = false, // 是否AI生成
    val tags: List<String> = emptyList(), // 标签列表

    // 计划配置
    val totalDays: Int, // 总天数

    // 时间戳
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
)
