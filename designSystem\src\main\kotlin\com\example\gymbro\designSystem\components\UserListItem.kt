package com.example.gymbro.designSystem.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextOverflow
import coil.compose.AsyncImage
import com.example.gymbro.designSystem.theme.tokens.spacing

/**
 * 用户列表项组件
 * 用于在各种列表中显示用户信息
 *
 * @param name 用户名称
 * @param imageUrl 用户头像URL
 * @param subtitle 用户副标题/描述
 * @param actionIcon 操作图标
 * @param actionIconContentDescription 操作图标的内容描述
 * @param onActionClick 操作图标点击回调
 * @param modifier 修饰符
 */
@Composable
fun userListItem(
    name: String,
    imageUrl: String?,
    subtitle: String,
    actionIcon: ImageVector,
    actionIconContentDescription: String?,
    onActionClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier =
        modifier
            .fillMaxWidth()
            .padding(MaterialTheme.spacing.small),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // 用户头像
        AsyncImage(
            model = imageUrl ?: "https://via.placeholder.com/40",
            contentDescription = "用户头像",
            contentScale = ContentScale.Crop,
            modifier =
            Modifier
                .size(MaterialTheme.spacing.large)
                .clip(CircleShape),
        )

        // 用户信息
        Column(
            modifier =
            Modifier
                .weight(1f)
                .padding(horizontal = MaterialTheme.spacing.medium),
        ) {
            Text(
                text = name,
                style = MaterialTheme.typography.bodyLarge,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )

            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )
        }

        // 操作按钮
        IconButton(onClick = onActionClick) {
            Icon(
                imageVector = actionIcon,
                contentDescription = actionIconContentDescription,
                tint = MaterialTheme.colorScheme.primary,
            )
        }
    }
}
