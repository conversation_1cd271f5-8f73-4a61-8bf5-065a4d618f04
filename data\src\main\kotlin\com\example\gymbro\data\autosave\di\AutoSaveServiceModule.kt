package com.example.gymbro.data.autosave.di

import com.example.gymbro.data.autosave.service.ChatHistoryAutoSaveServiceImpl
import com.example.gymbro.data.autosave.service.PlanAutoSaveServiceImpl
import com.example.gymbro.data.autosave.service.ProfileAutoSaveServiceImpl
import com.example.gymbro.data.autosave.service.WorkoutAutoSaveServiceImpl
import com.example.gymbro.domain.autosave.ChatHistoryAutoSaveService
import com.example.gymbro.domain.autosave.PlanAutoSaveService
import com.example.gymbro.domain.autosave.ProfileAutoSaveService
import com.example.gymbro.domain.autosave.WorkoutAutoSaveService
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 自动保存服务依赖注入模块
 *
 * 绑定domain层接口到data层实现
 * 遵循Clean Architecture的依赖倒置原则
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class AutoSaveServiceModule {

    /**
     * 绑定Profile自动保存服务
     *
     * features/profile模块将依赖ProfileAutoSaveService接口
     * data层提供ProfileAutoSaveServiceImpl实现
     */
    @Binds
    @Singleton
    abstract fun bindProfileAutoSaveService(
        impl: ProfileAutoSaveServiceImpl,
    ): ProfileAutoSaveService

    /**
     * 绑定Workout自动保存服务
     *
     * features/workout模块将依赖WorkoutAutoSaveService接口
     * data层提供WorkoutAutoSaveServiceImpl实现
     */
    @Binds
    @Singleton
    abstract fun bindWorkoutAutoSaveService(
        impl: WorkoutAutoSaveServiceImpl,
    ): WorkoutAutoSaveService

    /**
     * 绑定Plan自动保存服务
     *
     * features/plan模块将依赖PlanAutoSaveService接口
     * data层提供PlanAutoSaveServiceImpl实现
     */
    @Binds
    @Singleton
    abstract fun bindPlanAutoSaveService(
        impl: PlanAutoSaveServiceImpl,
    ): PlanAutoSaveService

    /**
     * 绑定Chat History自动保存服务
     *
     * features/coach模块将依赖ChatHistoryAutoSaveService接口
     * data层提供ChatHistoryAutoSaveServiceImpl实现
     * 支持聊天消息的自动保存、批量保存和缓存恢复功能
     */
    @Binds
    @Singleton
    abstract fun bindChatHistoryAutoSaveService(
        impl: ChatHistoryAutoSaveServiceImpl,
    ): ChatHistoryAutoSaveService
}
