package com.example.gymbro.core.ml.config

/**
 * BGE模型配置统一管理
 *
 * 🎯 集中管理所有BGE相关配置，避免在多个地方硬编码
 * 支持不同的模型配置预设，方便切换和调试
 */
object BgeModelConfig {

    /**
     * 🔥 当前使用的配置 - 统一控制点
     *
     * 修改这里可以全局切换BGE模型配置：
     * - SMALL_128: 小模型，128序列长度（推荐，兼容性最好）
     * - SMALL_256: 小模型，256序列长度（平衡性能和精度）
     * - SMALL_512: 小模型，512序列长度（最高精度，可能有兼容性问题）
     * - BASE_512: 基础模型，512序列长度（高精度，资源消耗大）
     */
    val CURRENT: ModelVariant = ModelVariant.SMALL_128

    /**
     * BGE模型变体配置
     */
    enum class ModelVariant(
        val modelName: String,
        val fileName: String,
        val maxSequenceLength: Int,
        val embeddingDim: Int,
        val vocabFile: String,
        val description: String,
        val recommendedFor: String,
    ) {
        /**
         * 🔥 BGE-small 128序列长度版本（推荐）
         * - 兼容性最好，适合大多数设备
         * - 推理速度快，内存占用低
         * - 适合移动端部署
         */
        SMALL_128(
            modelName = "BGE-small-zh-v1.5-128",
            fileName = "bge-small-cn-v1.5.tflite",
            maxSequenceLength = 128,
            embeddingDim = 512, // 🔥 修复：根据模型实际输出维度修正为512
            vocabFile = "vocab-cn.txt",
            description = "BGE小模型，128序列长度，兼容性最佳",
            recommendedFor = "移动端、低内存设备、快速响应场景",
        ),

        /**
         * BGE-small 256序列长度版本
         * - 平衡性能和精度
         * - 适合中等长度文本
         */
        SMALL_256(
            modelName = "BGE-small-zh-v1.5-256",
            fileName = "bge-small-cn-v1.5-256.tflite",
            maxSequenceLength = 256,
            embeddingDim = 512, // 🔥 修复：BGE-small系列都是512维输出
            vocabFile = "vocab-cn.txt",
            description = "BGE小模型，256序列长度，平衡性能",
            recommendedFor = "中等长度文本、平衡场景",
        ),

        /**
         * BGE-small 512序列长度版本
         * - 最高精度，但可能有兼容性问题
         * - 适合长文本处理
         */
        SMALL_512(
            modelName = "BGE-small-zh-v1.5-512",
            fileName = "bge-small-cn-v1.5-512.tflite",
            maxSequenceLength = 512,
            embeddingDim = 512, // 🔥 修复：BGE-small系列都是512维输出
            vocabFile = "vocab-cn.txt",
            description = "BGE小模型，512序列长度，最高精度",
            recommendedFor = "长文本、高精度要求场景",
        ),

        /**
         * BGE-base 512序列长度版本
         * - 基础模型，更高精度
         * - 资源消耗较大
         */
        BASE_512(
            modelName = "BGE-base-zh-v1.5",
            fileName = "bge-base-cn-v1.5.tflite",
            maxSequenceLength = 512,
            embeddingDim = 768,
            vocabFile = "vocab-cn.txt",
            description = "BGE基础模型，768维嵌入，最高质量",
            recommendedFor = "高端设备、最高精度要求",
        ),
        ;

        /**
         * 获取文本截断长度建议
         * 基于序列长度估算合理的文本长度
         */
        fun getRecommendedTextLength(): Int {
            return when (maxSequenceLength) {
                128 -> 100 // 约100字符对应128个token
                256 -> 200 // 约200字符对应256个token
                512 -> 400 // 约400字符对应512个token
                else -> maxSequenceLength * 0.8.toInt()
            }
        }

        /**
         * 检查是否为低内存配置
         */
        fun isLowMemoryConfig(): Boolean {
            return maxSequenceLength <= 128 && embeddingDim <= 512 // 🔥 修复：512维现在是BGE-small的标准配置
        }

        /**
         * 获取推荐的批处理大小
         */
        fun getRecommendedBatchSize(): Int {
            return when {
                maxSequenceLength <= 128 -> 16
                maxSequenceLength <= 256 -> 8
                maxSequenceLength <= 512 -> 4
                else -> 2
            }
        }
    }

    /**
     * 🔥 快速访问当前配置的属性
     */
    val maxSequenceLength: Int get() = CURRENT.maxSequenceLength
    val embeddingDim: Int get() = CURRENT.embeddingDim
    val modelFileName: String get() = CURRENT.fileName
    val vocabFileName: String get() = CURRENT.vocabFile
    val modelName: String get() = CURRENT.modelName
    val recommendedTextLength: Int get() = CURRENT.getRecommendedTextLength()
    val recommendedBatchSize: Int get() = CURRENT.getRecommendedBatchSize()

    /**
     * 🔥 运行时配置验证
     */
    fun validateConfig(): ConfigValidationResult {
        val issues = mutableListOf<String>()

        // 检查序列长度合理性
        if (maxSequenceLength !in 64..1024) {
            issues.add("序列长度 $maxSequenceLength 超出合理范围 [64, 1024]")
        }

        // 检查嵌入维度
        if (embeddingDim !in 256..1024) { // 🔥 修复：BGE模型最小输出维度是512
            issues.add("嵌入维度 $embeddingDim 超出合理范围 [256, 1024]")
        }

        // 检查文件名
        if (!modelFileName.endsWith(".tflite")) {
            issues.add("模型文件名 $modelFileName 不是有效的 .tflite 文件")
        }

        return ConfigValidationResult(
            isValid = issues.isEmpty(),
            issues = issues,
            config = CURRENT,
        )
    }

    /**
     * 配置验证结果
     */
    data class ConfigValidationResult(
        val isValid: Boolean,
        val issues: List<String>,
        val config: ModelVariant,
    )

    /**
     * 🔥 调试信息输出
     */
    fun getDebugInfo(): String {
        return buildString {
            appendLine("=== BGE模型配置信息 ===")
            appendLine("当前配置: ${CURRENT.name}")
            appendLine("模型名称: $modelName")
            appendLine("模型文件: $modelFileName")
            appendLine("词汇表文件: $vocabFileName")
            appendLine("最大序列长度: $maxSequenceLength")
            appendLine("嵌入维度: $embeddingDim")
            appendLine("推荐文本长度: $recommendedTextLength")
            appendLine("推荐批处理大小: $recommendedBatchSize")
            appendLine("描述: ${CURRENT.description}")
            appendLine("推荐用途: ${CURRENT.recommendedFor}")
            appendLine("低内存配置: ${CURRENT.isLowMemoryConfig()}")
            appendLine("========================")
        }
    }
}
