package com.example.gymbro.core.error

import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.withTimeout
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 网络超时处理器
 *
 * 专门处理AI请求的超时机制，提供统一的超时处理逻辑
 * 确保AI请求在10秒内完成，超时则返回用户友好的错误信息
 *
 * 设计原则：
 * - 快速失败：10秒超时，避免用户长时间等待
 * - 用户友好：提供清晰的超时错误提示
 * - 可恢复：超时错误标记为可恢复，支持重试
 */
@Singleton
class NetworkTimeoutHandler @Inject constructor() {

    companion object {
        /**
         * AI请求超时时间（毫秒）
         * 设置为30秒，支持SSE流式响应首包延迟
         * 修复：从10s调整到30s，覆盖AI侧排队导致的延迟
         */
        private const val AI_REQUEST_TIMEOUT_MS = 30_000L

        /**
         * 一般网络操作超时时间（毫秒）
         * 用于非AI请求的网络操作
         */
        private const val GENERAL_NETWORK_TIMEOUT_MS = 15_000L
    }

    /**
     * 执行带AI超时的操作
     *
     * 为AI相关的网络请求提供10秒超时保护
     * 超时后返回用户友好的错误信息，支持重试
     *
     * @param T 操作返回的数据类型
     * @param operationName 操作名称，用于日志和错误追踪
     * @param operation 要执行的挂起操作
     * @return 包装后的操作结果，超时时返回Error
     */
    suspend fun <T> executeWithAiTimeout(
        operationName: String,
        operation: suspend () -> ModernResult<T>,
    ): ModernResult<T> {
        return executeWithTimeout(
            timeoutMs = AI_REQUEST_TIMEOUT_MS,
            operationName = operationName,
            operation = operation,
            timeoutErrorMessage = "AI回复超时，请检查网络连接后重试",
        )
    }

    /**
     * 执行带一般网络超时的操作
     *
     * 为一般网络请求提供15秒超时保护
     *
     * @param T 操作返回的数据类型
     * @param operationName 操作名称，用于日志和错误追踪
     * @param operation 要执行的挂起操作
     * @return 包装后的操作结果，超时时返回Error
     */
    suspend fun <T> executeWithNetworkTimeout(
        operationName: String,
        operation: suspend () -> ModernResult<T>,
    ): ModernResult<T> {
        return executeWithTimeout(
            timeoutMs = GENERAL_NETWORK_TIMEOUT_MS,
            operationName = operationName,
            operation = operation,
            timeoutErrorMessage = "网络请求超时，请检查网络连接后重试",
        )
    }

    /**
     * 执行带自定义超时的操作
     *
     * 通用的超时处理方法，支持自定义超时时间和错误信息
     *
     * @param T 操作返回的数据类型
     * @param timeoutMs 超时时间（毫秒）
     * @param operationName 操作名称
     * @param operation 要执行的挂起操作
     * @param timeoutErrorMessage 超时时的错误信息
     * @return 包装后的操作结果
     */
    suspend fun <T> executeWithTimeout(
        timeoutMs: Long,
        operationName: String,
        operation: suspend () -> ModernResult<T>,
        timeoutErrorMessage: String = "操作超时，请重试",
    ): ModernResult<T> {
        return try {
            Timber.d("开始执行超时操作: $operationName, 超时时间: ${timeoutMs}ms")

            withTimeout(timeoutMs) {
                operation()
            }
        } catch (e: TimeoutCancellationException) {
            Timber.w("操作超时: $operationName, 超时时间: ${timeoutMs}ms")

            ModernResult.Error(
                ModernDataError(
                    operationName = operationName,
                    errorType = GlobalErrorType.Network.Timeout,
                    uiMessage = UiText.DynamicString(timeoutErrorMessage),
                    cause = e,
                    recoverable = true,
                    metadataMap = mapOf(
                        "timeout_ms" to timeoutMs,
                        "operation_name" to operationName,
                    ),
                ),
            )
        } catch (e: kotlinx.coroutines.CancellationException) {
            // 重新抛出协程取消异常，保持取消语义
            Timber.d("操作被取消: $operationName")
            throw e
        } catch (e: Exception) {
            Timber.e(e, "执行超时操作时发生异常: $operationName")

            ModernResult.Error(
                ModernDataError(
                    operationName = operationName,
                    errorType = GlobalErrorType.Network.General,
                    uiMessage = UiText.DynamicString("网络操作失败，请重试"),
                    cause = e,
                    recoverable = true,
                    metadataMap = mapOf(
                        "operation_name" to operationName,
                        "exception_type" to e.javaClass.simpleName,
                    ),
                ),
            )
        }
    }

    /**
     * 检查异常是否为超时异常
     *
     * 用于错误处理中判断异常类型
     *
     * @param throwable 要检查的异常
     * @return 是否为超时异常
     */
    fun isTimeoutException(throwable: Throwable): Boolean {
        return when (throwable) {
            is TimeoutCancellationException,
            is java.net.SocketTimeoutException,
            -> {
                Timber.d("检测到超时异常: ${throwable.javaClass.simpleName}")
                true
            }
            else -> false
        }
    }

    /**
     * 获取超时异常的用户友好提示
     *
     * @param throwable 超时异常
     * @param operationName 操作名称
     * @return 用户友好的错误提示
     */
    fun getTimeoutErrorMessage(throwable: Throwable, operationName: String): UiText {
        return when {
            operationName.contains("ai", ignoreCase = true) ||
                operationName.contains("coach", ignoreCase = true) -> {
                UiText.DynamicString("AI回复超时，请检查网络连接后重试")
            }
            else -> {
                UiText.DynamicString("网络请求超时，请检查网络连接后重试")
            }
        }
    }
}
