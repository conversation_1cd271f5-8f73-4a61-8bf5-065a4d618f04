package com.example.gymbro.data.memory.entity

import androidx.room.*
import com.example.gymbro.shared.models.memory.MemoryTier

/**
 * 记忆记录数据库实体
 *
 * 基于Memory System设计，支持四层记忆金字塔存储
 * 参考MessageEmbeddingEntity模式，兼容BGE 768维向量格式
 * 使用Room数据库实现DWM、UPM、GIM记忆的持久化存储
 */
@Entity(
    tableName = "memory_records",
    indices = [
        Index(value = ["user_id"]),
        Index(value = ["tier"]),
        Index(value = ["user_id", "tier"]),
        Index(value = ["user_id", "tier", "created_at"]),
        Index(value = ["expires_at"]),
        Index(value = ["importance"]),
        Index(value = ["embedding_status"]),
        Index(value = ["created_at"]),
    ],
)
data class MemoryRecordEntity(
    /**
     * 记忆唯一标识符
     */
    @PrimaryKey
    val id: String,

    /**
     * 用户ID
     * 确保记忆的用户隔离性
     */
    @ColumnInfo(name = "user_id")
    val userId: String,

    /**
     * 记忆层级
     * ECM, DWM, UPM, GIM
     */
    @ColumnInfo(name = "tier")
    val tier: String,

    /**
     * 创建时间戳
     */
    @ColumnInfo(name = "created_at")
    val createdAt: Long,

    /**
     * 过期时间戳
     * null表示永久保存 (UPM记忆)
     */
    @ColumnInfo(name = "expires_at")
    val expiresAt: Long? = null,

    /**
     * 重要性评分
     * 1=低 → 5=高，用于召回时的优先级排序
     */
    @ColumnInfo(name = "importance")
    val importance: Int,

    /**
     * BGE嵌入向量 (768维)
     * 存储为ByteArray格式，与MessageEmbeddingEntity兼容
     * null表示无需向量检索 (如结构化UPM数据)
     */
    @ColumnInfo(name = "embedding", typeAffinity = ColumnInfo.BLOB)
    val embedding: ByteArray? = null,

    /**
     * 向量维度
     * 默认768维BGE向量，用于兼容性验证
     */
    @ColumnInfo(name = "embedding_dim")
    val embeddingDim: Int = 768,

    /**
     * 向量嵌入状态
     * PENDING, PROCESSING, COMPLETED, FAILED
     * 与MessageEmbeddingEntity保持一致
     */
    @ColumnInfo(name = "embedding_status")
    val embeddingStatus: String = "COMPLETED",

    /**
     * 记忆内容载荷 (JSON格式)
     * 支持结构化JSON和自由文本混合存储
     */
    @ColumnInfo(name = "payload_json")
    val payloadJson: String,

    /**
     * 记忆内容长度
     * 用于性能分析和token预算估算
     */
    @ColumnInfo(name = "content_length")
    val contentLength: Int,

    /**
     * BGE模型版本
     * 用于向量兼容性检查
     */
    @ColumnInfo(name = "model_version")
    val modelVersion: String = "bge-small-zh-v1.5",

    /**
     * 向量生成耗时 (毫秒)
     * 用于性能监控
     */
    @ColumnInfo(name = "generation_time_ms")
    val generationTimeMs: Long? = null,

    /**
     * 记录更新时间戳
     * 用于跟踪记忆的修改历史
     */
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long = System.currentTimeMillis(),
) {
    companion object {
        // 嵌入状态常量 (与MessageEmbeddingEntity保持一致)
        const val STATUS_PENDING = "PENDING"
        const val STATUS_PROCESSING = "PROCESSING"
        const val STATUS_COMPLETED = "COMPLETED"
        const val STATUS_FAILED = "FAILED"

        // BGE模型配置
        const val BGE_VECTOR_DIM = 768
        const val BGE_MODEL_VERSION = "bge-small-zh-v1.5"
    }

    /**
     * 将ByteArray向量转换为FloatArray
     * 与MessageEmbeddingEntity保持兼容
     */
    fun getEmbeddingAsFloatArray(): FloatArray? {
        if (embedding == null) return null

        val floatArray = FloatArray(embeddingDim)
        val buffer = java.nio.ByteBuffer.wrap(embedding)
        buffer.order(java.nio.ByteOrder.LITTLE_ENDIAN)

        for (i in 0 until embeddingDim) {
            floatArray[i] = buffer.float
        }
        return floatArray
    }

    /**
     * 检查向量是否有效
     */
    fun isEmbeddingValid(): Boolean =
        embedding?.size == embeddingDim * 4 &&
            embeddingStatus == STATUS_COMPLETED &&
            embeddingDim == BGE_VECTOR_DIM

    /**
     * 检查记忆是否已过期
     */
    fun isExpired(): Boolean =
        expiresAt != null && expiresAt < System.currentTimeMillis()

    /**
     * 获取剩余生存时间 (毫秒)
     */
    fun getRemainingTtl(): Long? =
        expiresAt?.let { it - System.currentTimeMillis() }

    /**
     * 转换为MemoryTier枚举
     */
    fun getTierEnum(): MemoryTier =
        MemoryTier.valueOf(tier)

    /**
     * 重写equals方法
     * ByteArray需要特殊处理
     */
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as MemoryRecordEntity

        if (id != other.id) return false
        if (userId != other.userId) return false
        if (tier != other.tier) return false
        if (createdAt != other.createdAt) return false
        if (expiresAt != other.expiresAt) return false
        if (importance != other.importance) return false
        if (embedding != null) {
            if (other.embedding == null) return false
            if (!embedding.contentEquals(other.embedding)) return false
        } else if (other.embedding != null) return false
        if (embeddingDim != other.embeddingDim) return false
        if (embeddingStatus != other.embeddingStatus) return false
        if (payloadJson != other.payloadJson) return false
        if (contentLength != other.contentLength) return false
        if (modelVersion != other.modelVersion) return false
        if (generationTimeMs != other.generationTimeMs) return false
        if (updatedAt != other.updatedAt) return false

        return true
    }

    /**
     * 重写hashCode方法
     * ByteArray需要特殊处理
     */
    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + userId.hashCode()
        result = 31 * result + tier.hashCode()
        result = 31 * result + createdAt.hashCode()
        result = 31 * result + (expiresAt?.hashCode() ?: 0)
        result = 31 * result + importance
        result = 31 * result + (embedding?.contentHashCode() ?: 0)
        result = 31 * result + embeddingDim
        result = 31 * result + embeddingStatus.hashCode()
        result = 31 * result + payloadJson.hashCode()
        result = 31 * result + contentLength
        result = 31 * result + modelVersion.hashCode()
        result = 31 * result + (generationTimeMs?.hashCode() ?: 0)
        result = 31 * result + updatedAt.hashCode()
        return result
    }
}

/**
 * 记忆向量搜索结果
 * 包含相似度分数的搜索结果
 */
data class MemoryVectorSearchResult(
    val memoryId: String,
    val similarity: Float,
    val entity: MemoryRecordEntity,
)

/**
 * 记忆统计信息查询结果
 * 用于监控和分析记忆使用情况
 */
data class MemoryStatsResult(
    val tier: String,
    val count: Int,
    val avgImportance: Float,
    val totalSize: Long,
    val oldestTime: Long?,
    val newestTime: Long?,
)

/**
 * 工具函数：将FloatArray转换为ByteArray
 * 与MessageEmbeddingEntity保持兼容
 */
fun FloatArray.toMemoryByteArray(): ByteArray {
    val buffer = java.nio.ByteBuffer.allocate(this.size * 4)
    buffer.order(java.nio.ByteOrder.LITTLE_ENDIAN)

    for (float in this) {
        buffer.putFloat(float)
    }
    return buffer.array()
}
