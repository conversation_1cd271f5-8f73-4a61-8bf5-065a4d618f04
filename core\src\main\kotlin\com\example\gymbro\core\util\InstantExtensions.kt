package com.example.gymbro.core.util

import kotlinx.datetime.Instant
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime

/**
 * Instant扩展函数
 * 提供Instant类型的实用扩展方法
 */

/**
 * 将Instant转换为从1970-01-01T00:00:00Z起的毫秒数
 *
 * kotlinx.datetime.Instant已经内置了toEpochMilliseconds()方法
 * 此类仅作为兼容性兜底，详见 kotlinx-datetime issue #78
 *
 * 使用示例:
 * val instant = Clock.System.now()
 * val millis = instant.toEpochMilliseconds() // 直接使用内置方法
 *
 * @return 毫秒时间戳
 */
// Instant已具有toEpochMilliseconds()方法，此处不需要再次实现

/**
 * 兼容性方法，确保在所有上下文中都能正确解析toEpochMilliseconds
 * 此方法仅作为显式声明，实际上kotlinx.datetime.Instant已经内置了toEpochMilliseconds()方法
 *
 * 注意：在大多数情况下，应直接使用Instant.toEpochMilliseconds()
 *
 * @return 毫秒时间戳
 */
@Deprecated("使用内置的toEpochMilliseconds()方法", ReplaceWith("toEpochMilliseconds()"))
fun Instant.toEpochMillisecondsCompat(): Long = this.toEpochMilliseconds()

/**
 * 将Instant转换为格式化的字符串
 *
 * @param pattern 时间格式模式
 * @param timeZone 时区，默认为系统时区
 * @return 格式化后的时间字符串
 */
fun Instant.format(
    pattern: String = "yyyy-MM-dd HH:mm:ss",
    timeZone: TimeZone = TimeZone.currentSystemDefault(),
): String {
    return this.toLocalDateTime(timeZone).toString()
}

/**
 * 获取时间戳的毫秒值
 *
 * @return 时间戳毫秒值
 */
fun Instant.toMillis(): Long = toEpochMilliseconds()
