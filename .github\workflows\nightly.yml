name: Nightly Regression Pipeline (Modern v2.0)

on:
  # schedule:
  #   # 每天凌晨2点运行 (UTC) - MVP: 禁用定时任务以节省GitHub Actions配额
  #   - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      test_scope:
        description: '测试范围 (定时任务默认为 full)'
        required: true
        default: 'full'
        type: choice
        options:
          - full
          - security-only
          - coverage-only

# 【修复点 5】 添加权限
permissions:
   contents: read
   actions: read
   security-events: write # For CodeQL

env:
  # 现代化Gradle配置 + UTF-8强制支持
  GRADLE_OPTS: -Dorg.gradle.daemon=false -Dorg.gradle.workers.max=2 -Dfile.encoding=UTF-8
  JAVA_TOOL_OPTIONS: -Dfile.encoding=UTF-8
  LC_ALL: C.UTF-8
  JAVA_VERSION: '17'
  # 强制Build Scan发布
  GRADLE_SCAN: true
  # 默认 scope 为 full, 用于 schedule 触发
  TEST_SCOPE: ${{ inputs.test_scope || 'full' }}


jobs:
  # 安全扫描
  security-scan:
    name: Security & Dependency Scan
    runs-on: ubuntu-latest
     # 【修复点 1】 使用 env 或 (inputs || 'full')
    if: env.TEST_SCOPE != 'coverage-only'
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: "设置JDK 17 (UTF-8优化)"
        uses: actions/setup-java@v4
        with:
          java-version: ${{ env.JAVA_VERSION }}
          distribution: 'temurin'
        env:
          JAVA_OPTS: -Dfile.encoding=UTF-8

      - name: 现代化Gradle缓存
        uses: gradle/gradle-build-action@v3
        with:
           # cache-read-only: false # default is false
           gradle-home-cache-cleanup: true

      - name: 缓存OWASP数据库
        uses: actions/cache@v4
        with:
          path: ~/.gradle/dependency-check-data
          # hash key should include all files affecting dependencies
          key: owasp-${{ runner.os }}-${{ hashFiles('**/*.gradle*', '**/gradle.properties', '**/*.versions.toml') }}
          restore-keys: |
            owasp-${{ runner.os }}-

      - name: 赋予gradlew执行权限
        run: chmod +x gradlew

      - name: Create google-services.json
        run: echo "$FIREBASE_SERVICE_ACCOUNT" > app/google-services.json
        env:
          FIREBASE_SERVICE_ACCOUNT: ${{ secrets.FIREBASE_SERVICE_ACCOUNT }}
        continue-on-error: true

      - name: 🔒 运行OWASP依赖安全扫描
         # 假设 gradle task 叫 dependencyCheckAnalyze
        run: ./gradlew dependencyCheckAnalyze --scan --no-configuration-cache
        env:
          GRADLE_OPTS: ${{ env.GRADLE_OPTS }}
          NVD_API_KEY: ${{ secrets.NVD_API_KEY }}
        continue-on-error: true # OWASP often finds issues, don't fail build, but capture report

      - name: 🔍 初始化 CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: java # java covers Kotlin
          queries: security-and-quality

      # 【修复点 6】 添加 AutoBuild
      - name: 🏗️ CodeQL AutoBuild
        uses: github/codeql-action/autobuild@v3

      - name: 🔍 执行CodeQL分析
        uses: github/codeql-action/analyze@v3
        with:
           # category allows distinguishing results if multiple languages/configs exist
          category: "/language:java-kotlin"

      - name: 📊 上传安全扫描报告
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: security-reports-${{ github.sha }}
          path: |
            **/build/reports/dependency-check-report.*
          retention-days: 30
          if-no-files-found: warn # 【修复点 8】

  # 全量测试覆盖率
  full-coverage:
    name: Full Test Coverage Analysis
    runs-on: ubuntu-latest
    # 【修复点 1】
    if: env.TEST_SCOPE != 'security-only'
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: "设置JDK 17 (UTF-8优化)"
        uses: actions/setup-java@v4
        with:
           java-version: ${{ env.JAVA_VERSION }}
           distribution: 'temurin'
        env:
           JAVA_OPTS: -Dfile.encoding=UTF-8

      - name: 现代化Gradle缓存
        uses: gradle/gradle-build-action@v3
        with:
          gradle-home-cache-cleanup: true

      - name: 赋予gradlew执行权限
        run: chmod +x gradlew

      - name: Create google-services.json
        run: echo "$FIREBASE_SERVICE_ACCOUNT" > app/google-services.json
        env:
          FIREBASE_SERVICE_ACCOUNT: ${{ secrets.FIREBASE_SERVICE_ACCOUNT }}
        continue-on-error: true

      # 假设 testDebugUnitTest 运行所有模块的测试
      - name: 🧪 运行全量测试套件并生成覆盖率报告
        # 合并运行测试和生成报告, 确保 task 依赖正确
        run: ./gradlew testDebugUnitTest jacocoTestReport jacocoMergedReport coverageCheck --scan --no-configuration-cache --continue
        env:
          GRADLE_OPTS: ${{ env.GRADLE_OPTS }}
        continue-on-error: true # Allow coverage check failure but still upload report

      - name: 📊 上传覆盖率到Codecov
        uses: codecov/codecov-action@v4
        # Ensure path exists even if gradle task fails/skipped
        if: always() && hashFiles('./build/reports/jacoco/jacocoMergedReport/jacocoMergedReport.xml') != ''
        with:
          # Path might need adjustment based on your project structure
          files: ./build/reports/jacoco/jacocoMergedReport/jacocoMergedReport.xml
          fail_ci_if_error: false
          verbose: true
        env:
           CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }} # Use env var for token

      - name: 📊 上传覆盖率报告
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: coverage-reports-${{ github.sha }}
          path: |
            **/build/reports/jacoco/
            **/build/jacoco/
            **/build/reports/tests/
          retention-days: 30
          if-no-files-found: warn # 【修复点 8】

  # Firebase Test Lab全量回归 (MVP: 禁用以避免账单)
  firebase-regression:
    name: Firebase Test Lab Full Regression
    runs-on: ubuntu-latest
     # 【修复点 1】 只在 full scope 运行
    if: false  # MVP: 禁用Firebase Test Lab以避免账单
    # 【修复点 2】 修正 outputs 来源
    outputs:
      status: ${{ steps.analyze-results.outputs.status }} # 统一名称并指向 analyze-results
      device-count: ${{ steps.analyze-results.outputs.device-count }} # 指向 analyze-results
      pass-rate: ${{ steps.analyze-results.outputs.pass-rate }} # 指向 analyze-results
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 设置JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: ${{ env.JAVA_VERSION }}
          distribution: 'temurin'

      - name: 现代化Gradle缓存
        uses: gradle/gradle-build-action@v3
        with:
           gradle-home-cache-cleanup: true

      - name: 赋予gradlew执行权限
        run: chmod +x gradlew

      - name: 创建local.properties
        run: echo "sdk.dir=$ANDROID_HOME" > local.properties

      - name: Create google-services.json
        run: echo "$FIREBASE_SERVICE_ACCOUNT" > app/google-services.json
        env:
          FIREBASE_SERVICE_ACCOUNT: ${{ secrets.FIREBASE_SERVICE_ACCOUNT }}
        continue-on-error: true

      - name: "🏗️ 构建Debug APKs (用于回归测试)"
         # Build both app and test APKs if needed
        run: ./gradlew assembleDebug assembleAndroidTest --scan --no-configuration-cache
        env:
          GRADLE_OPTS: ${{ env.GRADLE_OPTS }}
          GYMBRO_API_KEY: ${{ secrets.GYMBRO_API_KEY }}
          FIREBASE_API_KEY: ${{ secrets.FIREBASE_API_KEY }}

      - name: 设置Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          version: 'latest'
          project_id: ${{ secrets.FIREBASE_PROJECT_ID }}
          service_account_key: ${{ secrets.FIREBASE_SERVICE_ACCOUNT }}
          export_default_credentials: true

      - name: 🧪 运行Firebase Test Lab全量回归测试
        id: ftl-regression
        run: |
           # Use set -e to fail fast on critical errors before gcloud
           set -e
           APK_PATH="app/build/outputs/apk/debug/app-debug.apk"
           TEST_APK_PATH="app/build/outputs/apk/androidTest/debug/app-debug-androidTest.apk"
           RESULTS_BUCKET="gs://${{ secrets.FIREBASE_PROJECT_ID }}-ftl-results"
           TIMESTAMP=$(date +%Y%m%d-%H%M%S)

           if [ ! -f "$APK_PATH" ]; then
             echo "::error::❌ APK文件不存在: $APK_PATH"
             exit 1 # Exit, so continue-on-error doesn't hide this critical failure
           fi
           echo "🧪 开始Firebase Test Lab回归测试..."
           echo "📱 APK: $APK_PATH"

           # 运行Robo测试
           echo "🤖 运行Robo测试..."
           # Use --no-user-output-enabled and check exit code if needed, or rely on JSON
           gcloud firebase test android run \
             --type=robo \
             --app="$APK_PATH" \
             --device=model=Pixel4,version=30 \
             --device=model=Pixel6,version=33 \
             --device=model=Pixel7,version=34 \
             --timeout=15m \
             --robo-directives=username_resource=com.example.gymbro.app:id/username,password_resource=com.example.gymbro.app:id/password \
             --results-bucket="$RESULTS_BUCKET" \
             --results-dir="nightly-robo-$TIMESTAMP" \
             --format=json > robo_results.json 2>robo_error.log || echo "Robo test command failed"

           # 运行instrumentation测试
           if [ -f "$TEST_APK_PATH" ]; then
             echo "🧪 运行Instrumentation测试..."
              gcloud firebase test android run \
               --type=instrumentation \
               --app="$APK_PATH" \
               --test="$TEST_APK_PATH" \
                --device=model=Pixel6,version=33 \
                --device=model=Pixel7,version=34 \
               --timeout=20m \
               --results-bucket="$RESULTS_BUCKET" \
               --results-dir="nightly-inst-$TIMESTAMP" \
               --format=json > instrumentation_results.json 2>inst_error.log || echo "Instrumentation test command failed"
           else
              echo "::warning::⚠️ 未找到Instrumentation测试APK，跳过: $TEST_APK_PATH"
           fi
            echo "DEVICE_COUNT=5" >> $GITHUB_ENV # Example count for summary
           echo "✅ Firebase Test Lab命令执行完成 (查看JSON结果)"
        continue-on-error: true # Allow gcloud command to fail but proceed to analysis

      - name: 📊 分析回归测试结果
        id: analyze-results # 【修复点 2】 添加 ID
        if: always() # 【修复点 4】 即使上一步失败也要运行
        run: |
           TOTAL_TESTS=0
           PASSED_TESTS=0
           DEVICE_COUNT=${{ env.DEVICE_COUNT || 0 }} # Get count from previous step env

           # 【修复点 4】 鲁棒性分析
           process_results() {
             local file=$1
             local test_type=$2
              # 检查文件存在且非空
             if [ -f "$file" ] && [ -s "$file" ]; then
                echo "📊 分析 $test_type 结果 ($file)..."
                 # 使用 ? 和 // empty 容错, 2>/dev/null 忽略 jq 错误
                OUTCOMES=$(jq -r '.[]? | .outcome? // empty' "$file" 2>/dev/null || echo "")
                for outcome in $OUTCOMES; do
                   if [ -n "$outcome" ]; then # Ensure outcome is not empty
                      TOTAL_TESTS=$((TOTAL_TESTS + 1))
                      # FTL outcome can be "SUCCESS", check your exact JSON
                      if [[ "$outcome" == "SUCCESS" || "$outcome" == "PASSED" ]]; then
                         PASSED_TESTS=$((PASSED_TESTS + 1))
                      fi
                    fi
                done
             else
                 echo "::warning::⚠️ $test_type 结果文件 $file 不存在或为空。"
             fi
           }

           process_results "robo_results.json" "Robo"
           process_results "instrumentation_results.json" "Instrumentation"

           PASS_RATE=0
           STATUS="failure" # Default to failure

           if [ $TOTAL_TESTS -gt 0 ]; then
             PASS_RATE=$((PASSED_TESTS * 100 / TOTAL_TESTS))
              if [ $PASS_RATE -ge 95 ]; then STATUS="success";
              elif [ $PASS_RATE -ge 80 ]; then STATUS="warning";
              else STATUS="failure"; fi
               echo "Result: $STATUS - Rate: $PASS_RATE% ($PASSED_TESTS/$TOTAL_TESTS)"
           elif [[ "${{ steps.ftl-regression.outcome }}" == "failure" ]]; then
                echo "Result: failure - FTL step failed and no results found."
                STATUS="failure"
           else
               # No tests ran but step didn't fail (e.g. no test apk)
                echo "Result: success - No tests executed."
                STATUS="success"
                PASS_RATE=100
           fi

           # 【修复点 2】 在此 step 输出
           echo "pass-rate=$PASS_RATE" >> $GITHUB_OUTPUT
           echo "status=$STATUS" >> $GITHUB_OUTPUT # 统一名称
           echo "device-count=$DEVICE_COUNT" >> $GITHUB_OUTPUT

      - name: 📊 上传FTL回归测试报告
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: ftl-regression-results-${{ github.run_id }}
          path: |
            robo_results.json
            instrumentation_results.json
            *.log
          retention-days: 30
          if-no-files-found: warn # 【修复点 8】

      - name: 📋 FTL回归测试摘要
        if: always() # 【修复点 4】
        run: |
          # 【修复点 2】 从 analyze-results 获取 output
          echo "## 🧪 Firebase Test Lab全量回归测试结果" >> $GITHUB_STEP_SUMMARY
          echo "- **通过率**: ${{ steps.analyze-results.outputs.pass-rate }}%" >> $GITHUB_STEP_SUMMARY
          echo "- **状态**: ${{ steps.analyze-results.outputs.status }}" >> $GITHUB_STEP_SUMMARY
          echo "- [Firebase控制台](https://console.firebase.google.com/project/${{ secrets.FIREBASE_PROJECT_ID }}/testlab/histories/)" >> $GITHUB_STEP_SUMMARY

  # 触发E2E测试 (如果启用)
  trigger-e2e-tests:
    name: Trigger E2E Tests
    runs-on: ubuntu-latest
    needs: [security-scan, full-coverage]
    if: env.TEST_SCOPE == 'full' && github.ref == 'refs/heads/master'
    steps:
      - name: 触发E2E测试工作流
        uses: actions/github-script@v7
        with:
          script: |
            await github.rest.actions.createWorkflowDispatch({
              owner: context.repo.owner,
              repo: context.repo.repo,
              workflow_id: 'e2e-testing.yml',
              ref: 'master',
              inputs: {
                test_scope: 'full'
              }
            });

  # 夜间报告汇总
  nightly-summary:
    name: Nightly Report Summary
    runs-on: ubuntu-latest
    needs: [security-scan, full-coverage, firebase-regression, trigger-e2e-tests]
    if: always()
    steps:
       - name: 📋 生成夜间报告摘要
         id: summary
         run: |
           # Helper function for status icons
           get_icon() {
              case "$1" in
                 success) echo "✅";; skipped) echo "⏭️";; failure) echo "❌";; cancelled) echo "🚫";; *) echo "⚠️";;
              esac
           }
           # Get results, default to 'unknown' if empty
           SEC_RES="${{ needs.security-scan.result }}"
           COV_RES="${{ needs.full-coverage.result }}"
           FTL_RES="${{ needs.firebase-regression.result }}"
           # 【修复点 3】 使用正确的 output name: status
           FTL_STATUS="${{ needs.firebase-regression.outputs.status }}"
           FTL_RATE="${{ needs.firebase-regression.outputs.pass-rate }}"
           FTL_COUNT="${{ needs.firebase-regression.outputs.device-count }}"

           # Determine overall status
           OVERALL_STATUS="✅ 总体状态：夜间回归测试通过"
           # Check if any job that was supposed to run, actually failed
           if [[ ("$SEC_RES" == "failure") || \
                 ("$COV_RES" == "failure") || \
                 ("$FTL_RES" == "failure") || \
                 ( "${{ env.TEST_SCOPE }}" == "full" && "$FTL_STATUS" == "failure" ) # Check FTL internal status too
              ]]; then
               OVERALL_STATUS="⚠️ 总体状态：发现问题，需要关注"
           fi

           # Build Summary Content
           CONTENT="# 🌙 GymBro 夜间回归报告\n"
           CONTENT+="**报告时间**: $(date '+%Y-%m-%d %H:%M:%S %Z')\n"
           CONTENT+="**测试范围**: ${{ env.TEST_SCOPE }}\n"
           CONTENT+="\n## 📊 测试结果\n"
           CONTENT+="- **安全扫描**: $(get_icon $SEC_RES) $SEC_RES\n"
           CONTENT+="- **覆盖率分析**: $(get_icon $COV_RES) $COV_RES\n"
           CONTENT+="- **Firebase回归**: $(get_icon $FTL_RES) $FTL_RES\n"
           # Conditionally add FTL details if job ran and output exists
           if [[ "$FTL_RES" != "skipped" && -n "$FTL_STATUS" ]]; then
              CONTENT+="  - FTL 状态: $FTL_STATUS (通过率: ${FTL_RATE:-N/A}%, 设备: ${FTL_COUNT:-N/A})\n"
           fi
           CONTENT+="\n## 🔗 相关链接\n"
           CONTENT+="- [Action 运行详情](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})\n"
           CONTENT+="- [Codecov仪表板](https://codecov.io/gh/${{ github.repository }})\n"
           CONTENT+="- [Firebase Test Lab](https://console.firebase.google.com/project/${{ secrets.FIREBASE_PROJECT_ID }}/testlab/histories/)\n"
           CONTENT+="\n$OVERALL_STATUS\n"

           # Output for GITHUB_STEP_SUMMARY (multiline)
           echo "$CONTENT" >> $GITHUB_STEP_SUMMARY
           # Output for Slack (replace newlines for simpler JSON, or use jq)
           # echo "slack_message<<EOF"$'\n'"$CONTENT"$'\n'EOF >> $GITHUB_OUTPUT
           # Using env for jq is safer
           echo "SUMMARY_CONTENT<<EOF" >> $GITHUB_ENV
           echo "$CONTENT" >> $GITHUB_ENV
           echo "EOF" >> $GITHUB_ENV


       - name: 📧 发送Slack通知
         # 只在配置了 secret 且不是手动触发时发送, 或按需调整
         if: always() && env.TEST_SCOPE == 'full' && secrets.SLACK_WEBHOOK_URL != ''
         run: |
           echo "📤 发送Slack通知..."
           # 【修复点 7】 使用 jq 安全构建 JSON payload
           PAYLOAD=$(echo "${{ env.SUMMARY_CONTENT }}" | jq -R -s --arg title "🌙 GymBro 夜间回归报告 - Run ${{ github.run_id }}" '{ text: $title, blocks: [ { type: "section", text: { type: "mrkdwn", text: . } } ] }')

           curl -X POST -H 'Content-type: application/json' \
              --data "$PAYLOAD" \
              "${{ secrets.SLACK_WEBHOOK_URL }}" || echo "::warning::Slack通知发送失败"
