package com.example.gymbro.core.ai.prompt.registry

import android.content.Context
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json
import timber.log.Timber
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

/**
 * PromptRegistry诊断工具
 *
 * 用于独立测试和验证standard.json等配置文件的加载过程
 * 可以在不依赖完整系统的情况下快速定位问题
 */
@Singleton
class PromptRegistryDiagnostics @Inject constructor(
    private val context: Context,
) {

    private val json = Json {
        ignoreUnknownKeys = true
        isLenient = true
    }

    /**
     * 运行完整的诊断流程
     */
    fun runFullDiagnostics(): DiagnosticsResult {
        Timber.i("🔧 开始PromptRegistry全面诊断...")

        val results = mutableListOf<DiagnosticStep>()

        // 步骤1：Assets文件检查
        results.add(checkAssetsFiles())

        // 步骤2：本地文件检查
        results.add(checkLocalFiles())

        // 步骤3：JSON解析测试
        results.add(testJsonParsing("standard"))

        // 步骤4：配置完整性验证
        results.add(validateConfigCompleteness("standard"))

        // 步骤5：文件复制测试
        results.add(testFileCopy("standard.json"))

        // 步骤6：版本一致性检查
        results.add(checkVersionConsistency("standard"))

        val overallSuccess = results.all { it.success }

        Timber.i("🔧 诊断完成，总体结果: ${if (overallSuccess) "✅ 成功" else "❌ 失败"}")

        return DiagnosticsResult(
            overallSuccess = overallSuccess,
            steps = results,
            summary = generateSummary(results),
        )
    }

    /**
     * 步骤1：检查Assets文件
     */
    private fun checkAssetsFiles(): DiagnosticStep {
        return try {
            val assetFiles = context.assets.list("prompts") ?: emptyArray()
            val standardExists = assetFiles.contains("standard.json")

            if (!standardExists) {
                DiagnosticStep(
                    name = "Assets文件检查",
                    success = false,
                    message = "standard.json在assets中不存在",
                    details = "可用文件: ${assetFiles.joinToString(", ")}",
                )
            } else {
                val content = context.assets.open("prompts/standard.json").use {
                    it.readBytes().toString(Charsets.UTF_8)
                }

                DiagnosticStep(
                    name = "Assets文件检查",
                    success = true,
                    message = "standard.json存在且可读",
                    details = "文件大小: ${content.length}字符，前100字符: ${content.take(100)}",
                )
            }
        } catch (e: Exception) {
            DiagnosticStep(
                name = "Assets文件检查",
                success = false,
                message = "Assets访问失败",
                details = "错误: ${e.message}",
            )
        }
    }

    /**
     * 步骤2：检查本地文件
     */
    private fun checkLocalFiles(): DiagnosticStep {
        return try {
            val promptsDir = File(context.filesDir, "prompts")
            val standardFile = File(promptsDir, "standard.json")

            if (!promptsDir.exists()) {
                return DiagnosticStep(
                    name = "本地文件检查",
                    success = false,
                    message = "prompts目录不存在",
                    details = "路径: ${promptsDir.absolutePath}",
                )
            }

            if (!standardFile.exists()) {
                return DiagnosticStep(
                    name = "本地文件检查",
                    success = false,
                    message = "standard.json本地文件不存在",
                    details = "目录内容: ${promptsDir.listFiles()?.map { it.name }?.joinToString(", ") ?: "空"}",
                )
            }

            val content = standardFile.readText()
            DiagnosticStep(
                name = "本地文件检查",
                success = true,
                message = "standard.json本地文件存在且可读",
                details = "文件大小: ${content.length}字符，前100字符: ${content.take(100)}",
            )
        } catch (e: Exception) {
            DiagnosticStep(
                name = "本地文件检查",
                success = false,
                message = "本地文件访问失败",
                details = "错误: ${e.message}",
            )
        }
    }

    /**
     * 步骤3：JSON解析测试
     */
    private fun testJsonParsing(configId: String): DiagnosticStep {
        return try {
            // 首先从assets读取
            val assetsContent = context.assets.open("prompts/$configId.json").use {
                it.readBytes().toString(Charsets.UTF_8)
            }

            // 尝试解析
            val config = json.decodeFromString<PromptConfig>(assetsContent)

            // 验证关键字段
            val issues = mutableListOf<String>()
            if (config.id != configId) issues.add("ID不匹配: ${config.id} != $configId")
            if (config.systemPrompt.isBlank()) issues.add("systemPrompt为空")
            if (config.systemPrompt.length < 100) {
                issues.add(
                    "systemPrompt过短: ${config.systemPrompt.length}字符",
                )
            }
            if (config.displayName.isBlank()) issues.add("displayName为空")

            if (issues.isEmpty()) {
                DiagnosticStep(
                    name = "JSON解析测试",
                    success = true,
                    message = "JSON解析成功，配置完整",
                    details = "ID: ${config.id}, DisplayName: ${config.displayName}, SystemPrompt长度: ${config.systemPrompt.length}",
                )
            } else {
                DiagnosticStep(
                    name = "JSON解析测试",
                    success = false,
                    message = "JSON解析成功但配置有问题",
                    details = "问题: ${issues.joinToString(", ")}",
                )
            }
        } catch (e: Exception) {
            DiagnosticStep(
                name = "JSON解析测试",
                success = false,
                message = "JSON解析失败",
                details = "错误: ${e.message}",
            )
        }
    }

    /**
     * 步骤4：配置完整性验证
     */
    private fun validateConfigCompleteness(configId: String): DiagnosticStep {
        return try {
            val assetsContent = context.assets.open("prompts/$configId.json").use {
                it.readBytes().toString(Charsets.UTF_8)
            }
            val config = json.decodeFromString<PromptConfig>(assetsContent)

            // 检查关键内容
            val checks = mutableMapOf<String, Boolean>()
            checks["包含ThinkingBox"] = config.systemPrompt.contains("ThinkingBox")
            checks["包含GymBro"] = config.systemPrompt.contains("GymBro")
            checks["包含meta_instructions"] = config.systemPrompt.contains("meta_instructions")
            checks["包含protocol_rules"] = config.systemPrompt.contains("protocol_rules")
            checks["长度超过1000字符"] = config.systemPrompt.length > 1000

            val passedChecks = checks.count { it.value }
            val totalChecks = checks.size

            DiagnosticStep(
                name = "配置完整性验证",
                success = passedChecks == totalChecks,
                message = "完整性检查: $passedChecks/$totalChecks 通过",
                details = checks.map { "${it.key}: ${if (it.value) "✅" else "❌"}" }.joinToString(", "),
            )
        } catch (e: Exception) {
            DiagnosticStep(
                name = "配置完整性验证",
                success = false,
                message = "完整性验证失败",
                details = "错误: ${e.message}",
            )
        }
    }

    /**
     * 步骤5：文件复制测试
     */
    private fun testFileCopy(fileName: String): DiagnosticStep {
        return try {
            val targetFile = File(context.filesDir, "prompts/$fileName")

            // 删除现有文件（如果存在）
            if (targetFile.exists()) {
                targetFile.delete()
            }

            // 确保目录存在
            targetFile.parentFile?.mkdirs()

            // 执行复制
            context.assets.open("prompts/$fileName").use { input ->
                targetFile.outputStream().use { output ->
                    input.copyTo(output)
                }
            }

            // 验证复制结果
            if (!targetFile.exists()) {
                return DiagnosticStep(
                    name = "文件复制测试",
                    success = false,
                    message = "复制后文件不存在",
                    details = "目标路径: ${targetFile.absolutePath}",
                )
            }

            val copiedContent = targetFile.readText()
            val originalContent = context.assets.open("prompts/$fileName").use {
                it.readBytes().toString(Charsets.UTF_8)
            }

            if (copiedContent != originalContent) {
                DiagnosticStep(
                    name = "文件复制测试",
                    success = false,
                    message = "复制内容不一致",
                    details = "原始: ${originalContent.length}字符, 复制: ${copiedContent.length}字符",
                )
            } else {
                DiagnosticStep(
                    name = "文件复制测试",
                    success = true,
                    message = "文件复制成功，内容一致",
                    details = "文件大小: ${copiedContent.length}字符",
                )
            }
        } catch (e: Exception) {
            DiagnosticStep(
                name = "文件复制测试",
                success = false,
                message = "文件复制失败",
                details = "错误: ${e.message}",
            )
        }
    }

    /**
     * 步骤6：版本一致性检查
     */
    private fun checkVersionConsistency(configId: String): DiagnosticStep {
        return try {
            val fileName = "$configId.json"
            val localFile = File(context.filesDir, "prompts/$fileName")

            if (!localFile.exists()) {
                return DiagnosticStep(
                    name = "版本一致性检查",
                    success = false,
                    message = "本地文件不存在，无法比较版本",
                    details = "文件路径: ${localFile.absolutePath}",
                )
            }

            // 读取本地和assets文件内容
            val localContent = localFile.readText()
            val assetsContent = context.assets.open("prompts/$fileName").use {
                it.readBytes().toString(Charsets.UTF_8)
            }

            // 比较哈希值
            val localHash = localContent.hashCode()
            val assetsHash = assetsContent.hashCode()

            if (localHash == assetsHash) {
                DiagnosticStep(
                    name = "版本一致性检查",
                    success = true,
                    message = "本地文件与Assets版本一致",
                    details = "哈希值: $localHash, 文件大小: ${localContent.length}字符",
                )
            } else {
                // 详细比较差异
                val localLines = localContent.lines()
                val assetsLines = assetsContent.lines()
                val diffCount = minOf(localLines.size, assetsLines.size).let { minSize ->
                    (0 until minSize).count { i -> localLines[i] != assetsLines[i] }
                }

                DiagnosticStep(
                    name = "版本一致性检查",
                    success = false,
                    message = "本地文件与Assets版本不一致",
                    details = buildString {
                        appendLine("本地哈希: $localHash (${localContent.length}字符)")
                        appendLine("Assets哈希: $assetsHash (${assetsContent.length}字符)")
                        appendLine("不同行数: $diffCount")
                        appendLine("本地行数: ${localLines.size}")
                        appendLine("Assets行数: ${assetsLines.size}")
                    }.trim(),
                )
            }
        } catch (e: Exception) {
            DiagnosticStep(
                name = "版本一致性检查",
                success = false,
                message = "版本一致性检查失败",
                details = "错误: ${e.message}",
            )
        }
    }

    /**
     * 生成诊断摘要
     */
    private fun generateSummary(steps: List<DiagnosticStep>): String {
        return buildString {
            appendLine("🔧 PromptRegistry诊断摘要")
            appendLine("=".repeat(40))

            val successCount = steps.count { it.success }
            val totalCount = steps.size

            appendLine("总体结果: $successCount/$totalCount 步骤成功")
            appendLine()

            steps.forEach { step ->
                val status = if (step.success) "✅" else "❌"
                appendLine("$status ${step.name}: ${step.message}")
                if (step.details.isNotBlank()) {
                    appendLine("   详情: ${step.details}")
                }
            }

            if (successCount < totalCount) {
                appendLine()
                appendLine("⚠️ 建议修复措施:")
                steps.filter { !it.success }.forEach { step ->
                    when (step.name) {
                        "Assets文件检查" -> appendLine("- 确保prompts/standard.json文件存在于assets目录")
                        "JSON解析测试" -> appendLine("- 检查JSON格式是否正确，验证字段类型匹配")
                        "配置完整性验证" -> appendLine("- 验证standard.json内容完整性，确保包含必要字段")
                        "文件复制测试" -> appendLine("- 检查文件权限和存储空间")
                        "版本一致性检查" -> appendLine("- 调用promptRegistry.checkAndUpdateAllConfigs()更新过期文件")
                    }
                }
            }
        }
    }
}

/**
 * 诊断结果数据类
 */
data class DiagnosticsResult(
    val overallSuccess: Boolean,
    val steps: List<DiagnosticStep>,
    val summary: String,
)

/**
 * 诊断步骤数据类
 */
data class DiagnosticStep(
    val name: String,
    val success: Boolean,
    val message: String,
    val details: String,
)
