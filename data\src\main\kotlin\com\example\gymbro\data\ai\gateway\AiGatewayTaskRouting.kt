package com.example.gymbro.data.ai.gateway

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.features.FeatureErrors
import com.example.gymbro.core.network.config.AiTaskType
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.coach.config.AiProviderManager
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * AiGateway任务路由扩展
 *
 * 集成现有的AiProviderManager和AiGateway系统
 * 根据任务类型智能选择最优AI提供商
 */
@Singleton
class AiGatewayTaskRouting
@Inject
constructor(
    private val aiGateway: AiGateway,
    private val aiProviderManager: AiProviderManager,
) {
    /**
     * 根据任务类型路由AI请求
     *
     * @param userId 用户ID
     * @param taskType AI任务类型
     * @param priority 请求优先级
     * @return 网关决策结果
     */
    suspend fun routeByTaskType(
        userId: String,
        taskType: AiTaskType,
        priority: RequestPriority = RequestPriority.NORMAL,
    ): ModernResult<GatewayDecision> {
        Timber.d("🎯 任务类型路由: taskType=$taskType, userId=$userId")

        return try {
            // 1. 根据任务类型确定最优提供商
            val preferredProvider = getPreferredProviderForTask(taskType)
            Timber.d("🎯 任务类型 $taskType 的首选提供商: $preferredProvider")

            // 2. 尝试切换到首选提供商
            val switchSuccess = aiProviderManager.switchToProvider(preferredProvider)
            if (!switchSuccess) {
                Timber.w("⚠️ 切换到首选提供商失败: $preferredProvider")
            }

            // 3. 将任务类型映射到请求类型
            val requestType = mapTaskTypeToRequestType(taskType)

            // 4. 使用现有的AiGateway进行路由
            val gatewayResult = aiGateway.routeRequest(userId, requestType, priority)

            // 5. 增强决策结果，添加任务类型信息
            when (gatewayResult) {
                is ModernResult.Success -> {
                    val enhancedDecision =
                        gatewayResult.data.copy(
                            metadata =
                            gatewayResult.data.metadata +
                                mapOf(
                                    "task_type" to taskType.name,
                                    "preferred_provider" to preferredProvider,
                                    "switch_success" to switchSuccess,
                                ),
                        )
                    ModernResult.Success(enhancedDecision)
                }
                is ModernResult.Error -> gatewayResult
                is ModernResult.Loading -> gatewayResult
            }
        } catch (e: Exception) {
            Timber.e(e, "🚨 任务类型路由异常: taskType=$taskType")
            ModernResult.Error(
                FeatureErrors.CoachError.processingFailed(
                    operationName = "AiGatewayTaskRouting.routeByTaskType",
                    message = UiText.DynamicString("任务路由失败"),
                    processType = "task_routing",
                    reason = e.message ?: "unknown_error",
                    cause = e,
                    metadataMap =
                    mapOf(
                        "task_type" to taskType.name,
                        "user_id" to userId,
                    ),
                ),
            )
        }
    }

    /**
     * 根据任务类型获取首选提供商
     */
    private fun getPreferredProviderForTask(taskType: AiTaskType): String =
        when (taskType) {
            AiTaskType.TITLE_GENERATION -> "gemini" // Gemini擅长标题生成
            AiTaskType.SUMMARY -> "gemini" // Gemini擅长摘要
            AiTaskType.CHAT -> "deepseek" // DeepSeek擅长对话
            AiTaskType.TRAINING_PLAN -> "deepseek" // DeepSeek擅长训练计划
            AiTaskType.NUTRITION_ADVICE -> "deepseek" // DeepSeek擅长营养建议
        }

    /**
     * 将任务类型映射到现有的请求类型
     */
    private fun mapTaskTypeToRequestType(taskType: AiTaskType): AiRequestType =
        when (taskType) {
            AiTaskType.CHAT -> AiRequestType.CHAT
            AiTaskType.TITLE_GENERATION -> AiRequestType.COMPLETION
            AiTaskType.SUMMARY -> AiRequestType.COMPLETION
            AiTaskType.TRAINING_PLAN -> AiRequestType.CHAT
            AiTaskType.NUTRITION_ADVICE -> AiRequestType.CHAT
        }

    /**
     * 获取任务类型的降级策略
     */
    fun getFallbackProvidersForTask(taskType: AiTaskType): List<String> =
        when (taskType) {
            AiTaskType.TITLE_GENERATION -> listOf("gemini", "deepseek", "openai")
            AiTaskType.SUMMARY -> listOf("gemini", "deepseek", "openai")
            AiTaskType.CHAT -> listOf("deepseek", "openai", "gemini")
            AiTaskType.TRAINING_PLAN -> listOf("deepseek", "openai", "gemini")
            AiTaskType.NUTRITION_ADVICE -> listOf("deepseek", "openai", "gemini")
        }
}
