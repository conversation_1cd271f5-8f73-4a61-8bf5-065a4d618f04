---
type: "always_apply"
---

# GymBro Module Dependencies & Architecture Rules

## Module Overview (21 Modules)

### Core Modules (Foundation)
- **:core** - Error handling (ModernResult<T>), global Timber logging, utilities,prmotbuilder
- **:core-arch** - MVI base components (BaseMviViewModel, MviContract)
- **:core-network** - WebSocket, REST API, network monitoring
- **:core-ml** - AI/ML engine (BGE vectors, TensorFlow Lite)

### Business Logic
- **:domain** - Pure Kotlin UseCase interfaces, entities (uses Logger interface)
- **:data** - Repository implementations, Room database, API clients
- **:shared-models** - Pure DTO classes with kotlinx.serialization

### Feature Modules (Presentation)
- **:features:profile** - ⭐ API architecture standard (ProfileNavigatable, ProfileFeatureApi)
- **:features:coach** - MVI 2.0 implementation, AI chat with streaming
- **:features:auth** - Authentication flows
- **:features:workout** - workout management
- **:features:home** - Dashboard and navigation
- **:features:subscription** - Billing and premium features
  -- **:features:thinkingbox** - Ai Stream
- **:features:exercise-library** - Exercise database and search

### UI & Navigation
- **:designSystem** - Compose components, themes, tokens
- **:navigation** - Route definitions and navigation logic
- **:di** - Hilt dependency injection modules

### Infrastructure
- **:worker** - Background tasks (WorkManager)
- **:benchmark** - Performance testing
- **:utils** - Development tools and scripts
- **:config** - Configuration management
- **:ci-scripts** - CI/CD automation
- **:backup** - Data backup and recovery

## Dependency Rules

### Strict Constraints
```
:app → :features:* → :domain ← :data
     → :designSystem
     → :navigation
     → :di
```

### Forbidden Dependencies
- Features CANNOT depend on other features directly
- Domain CANNOT depend on Android framework
- Data CANNOT depend on presentation layer
- Core modules CANNOT depend on feature modules

### Required Dependencies
- All features MUST depend on :domain and :core
- MVI features MUST depend on :core-arch
- AI features MUST depend on :core-ml
- Network features MUST depend on :core-network

## Architecture Patterns

### MVI 2.0 Standard (Profile Module Reference)
```kotlin
// Contract definition
object FeatureContract {
    data class State(val isLoading: Boolean = false, val error: UiText? = null)
    sealed interface Intent
    sealed interface Effect
}

// Reducer (pure function)
class FeatureReducer @Inject constructor() {
    fun reduce(intent: Intent, state: State): ReduceResult<State, Effect>
}

// EffectHandler (side effects)
class FeatureEffectHandler @Inject constructor() {
    suspend fun handle(intent: Intent, state: State): Flow<Effect>
}

// ViewModel (coordination)
class FeatureViewModel @Inject constructor(
    private val reducer: FeatureReducer,
    private val effectHandler: FeatureEffectHandler
) : BaseMviViewModel<Intent, State, Effect>(initialState)
```

### API Design Pattern (Profile Module Standard)
```kotlin
// Navigation contract
interface FeatureNavigatable {
    val route: String
    fun registerGraph(navGraphBuilder: NavGraphBuilder, navController: NavController)
}

// Feature service API
interface FeatureApi {
    fun observeData(): Flow<DataModel>
    suspend fun performAction(): ModernResult<Unit, ModernDataError>
}
```

## Module Responsibilities

### Error Handling (:core)
- Use ModernResult<T, E> for all operations
- ModernDataError for data layer errors
- UiText for user-facing messages
- Global Timber logging configuration

### Networking (:core-network)
- WebSocket management with reconnection
- REST API clients (Retrofit)
- Network state monitoring
- OpenAI-compatible chat API

### AI/ML (:core-ml)
- BGE vector search engine
- TensorFlow Lite model inference
- Semantic similarity calculations
- Local AI model management

### Data Persistence (:data)
- Repository pattern implementation
- Room database with FTS5 search
- Local-first data strategy
- API response caching

### Autosave (:core/autosave)
- Automatic state persistence
- Draft management
- Recovery mechanisms
- Background synchronization

### Dependency Injection (:di)
- Hilt module organization
- Repository bindings
- UseCase provisioning
- Scope management (@Singleton, @ViewModelScoped)

## Package Structure

### Standard Package Layout
```
com.example.gymbro.{module}/
├── api/                    # Public interfaces (features only)
├── internal/               # Internal implementations
│   ├── presentation/       # UI layer (MVI components)
│   ├── data/              # Data layer implementations
│   └── di/                # Module-specific DI
└── {domain_specific}/     # Domain-specific packages
```

### Import Conventions
- Use absolute imports: `com.example.gymbro.core.error.ModernResult`
- Features import from domain: `com.example.gymbro.domain.{module}.repository.{Name}Repository`
- Internal visibility for implementations
- Public API through interfaces only

## Quality Standards

### Testing Requirements
- Domain layer: 90%+ coverage (pure Kotlin, easily testable)
- Data layer: 85%+ coverage (repository implementations)
- Feature layer: 80%+ coverage (MVI components)

### Performance Baselines
- MVI state updates: ≤16ms
- Intent processing: ≤100ms
- Effect handling: ≤50ms
- Network requests: ≤1000ms

### Code Quality
- Zero compilation warnings
- Detekt static analysis passing
- KDoc coverage for public APIs
- Consistent error handling patterns
