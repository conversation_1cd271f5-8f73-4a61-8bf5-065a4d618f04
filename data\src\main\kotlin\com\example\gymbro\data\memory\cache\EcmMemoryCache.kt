package com.example.gymbro.data.memory.cache

import com.example.gymbro.core.logging.Logger
import com.example.gymbro.shared.models.memory.MemoryRecord
import com.example.gymbro.shared.models.memory.MemoryTier
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import javax.inject.Inject
import javax.inject.Singleton

/**
 * ECM内存缓存实现
 *
 * 实现临时上下文记忆(Ephemeral Context Memory)的内存级存储
 * 基于memory.md设计文档：TTL 10分钟，内存存储，最多保留10条记忆
 * 参考data层现有cache模式，使用线程安全的内存管理
 */
@Singleton
class EcmMemoryCache @Inject constructor(
    private val logger: Logger,
) {

    companion object {
        /**
         * ECM记忆最大数量限制
         */
        private const val MAX_ECM_MEMORIES = 10

        /**
         * ECM记忆TTL (10分钟)
         */
        private const val ECM_TTL_MS = 10 * 60 * 1000L
    }

    /**
     * 用户记忆缓存存储
     * Key: userId, Value: 该用户的记忆列表(按时间顺序)
     */
    private val memoryCache = mutableMapOf<String, MutableList<MemoryRecord>>()

    /**
     * 线程安全保护
     */
    private val cacheMutex = Mutex()

    /**
     * 添加记忆到ECM缓存
     *
     * @param userId 用户ID
     * @param memory 记忆记录
     */
    suspend fun addMemory(userId: String, memory: MemoryRecord) {
        cacheMutex.withLock {
            logger.d("EcmMemoryCache", "添加ECM记忆: userId=$userId, memoryId=${memory.id}")

            // 验证记忆层级
            if (memory.tier != MemoryTier.ECM) {
                logger.w("EcmMemoryCache", "尝试添加非ECM记忆到ECM缓存: tier=${memory.tier}")
                return
            }

            // 获取或创建用户记忆列表
            val userMemories = memoryCache.getOrPut(userId) { mutableListOf() }

            // 检查记忆是否已存在（基于ID）
            val existingIndex = userMemories.indexOfFirst { it.id == memory.id }
            if (existingIndex >= 0) {
                // 更新现有记忆
                userMemories[existingIndex] = memory
                logger.d("EcmMemoryCache", "更新现有ECM记忆: ${memory.id}")
            } else {
                // 添加新记忆
                userMemories.add(memory)
                logger.d("EcmMemoryCache", "添加新ECM记忆: ${memory.id}")
            }

            // 维护最大数量限制（保留最新的记忆）
            while (userMemories.size > MAX_ECM_MEMORIES) {
                val removedMemory = userMemories.removeAt(0) // 移除最旧的记忆
                logger.d("EcmMemoryCache", "移除过期ECM记忆: ${removedMemory.id}")
            }

            logger.d("EcmMemoryCache", "当前用户ECM记忆数量: ${userMemories.size}")
        }
    }

    /**
     * 批量添加记忆到ECM缓存
     *
     * @param userId 用户ID
     * @param memories 记忆记录列表
     */
    suspend fun addMemories(userId: String, memories: List<MemoryRecord>) {
        memories.forEach { memory ->
            addMemory(userId, memory)
        }
    }

    /**
     * 获取用户的ECM记忆
     *
     * @param userId 用户ID
     * @param maxResults 最大返回数量，默认5
     * @return 用户的ECM记忆列表（按时间倒序，最新的在前）
     */
    suspend fun getMemories(userId: String, maxResults: Int = 5): List<MemoryRecord> {
        return cacheMutex.withLock {
            val userMemories = memoryCache[userId] ?: emptyList()

            // 过滤未过期的记忆
            val validMemories = userMemories.filter { memory ->
                !memory.isExpired()
            }

            // 如果有记忆过期，清理它们
            if (validMemories.size < userMemories.size) {
                memoryCache[userId] = validMemories.toMutableList()
                logger.d("EcmMemoryCache", "清理过期ECM记忆: ${userMemories.size - validMemories.size}条")
            }

            // 返回最新的记忆（倒序，取最近的maxResults条）
            val resultMemories = validMemories.takeLast(maxResults).reversed()

            logger.d("EcmMemoryCache", "获取ECM记忆: userId=$userId, 返回${resultMemories.size}条")
            resultMemories
        }
    }

    /**
     * 根据查询文本搜索ECM记忆
     *
     * @param userId 用户ID
     * @param query 查询文本
     * @param maxResults 最大返回数量
     * @return 匹配的记忆列表
     */
    suspend fun searchMemories(
        userId: String,
        query: String,
        maxResults: Int = 3,
    ): List<MemoryRecord> {
        return cacheMutex.withLock {
            val userMemories = memoryCache[userId] ?: emptyList()

            // 简单文本匹配搜索（不使用向量搜索）
            val matchingMemories = userMemories.filter { memory ->
                !memory.isExpired() && memory.payload.toString().contains(query, ignoreCase = true)
            }

            // 返回最相关的记忆（按时间倒序）
            val resultMemories = matchingMemories.takeLast(maxResults).reversed()

            logger.d("EcmMemoryCache", "搜索ECM记忆: query='$query', 返回${resultMemories.size}条")
            resultMemories
        }
    }

    /**
     * 根据记忆ID获取特定记忆
     *
     * @param userId 用户ID
     * @param memoryId 记忆ID
     * @return 记忆记录，如果不存在或已过期则返回null
     */
    suspend fun getMemoryById(userId: String, memoryId: String): MemoryRecord? {
        return cacheMutex.withLock {
            val userMemories = memoryCache[userId] ?: emptyList()
            val memory = userMemories.find { it.id == memoryId }

            if (memory != null && memory.isExpired()) {
                logger.d("EcmMemoryCache", "ECM记忆已过期: $memoryId")
                return null
            }

            logger.d("EcmMemoryCache", "获取ECM记忆: memoryId=$memoryId, found=${memory != null}")
            memory
        }
    }

    /**
     * 删除特定记忆
     *
     * @param userId 用户ID
     * @param memoryId 记忆ID
     * @return 是否成功删除
     */
    suspend fun removeMemory(userId: String, memoryId: String): Boolean {
        return cacheMutex.withLock {
            val userMemories = memoryCache[userId] ?: return false
            val removed = userMemories.removeIf { it.id == memoryId }

            logger.d("EcmMemoryCache", "删除ECM记忆: memoryId=$memoryId, success=$removed")
            removed
        }
    }

    /**
     * 清除用户的所有ECM记忆
     *
     * @param userId 用户ID
     * @return 清除的记忆数量
     */
    suspend fun clearUserMemories(userId: String): Int {
        return cacheMutex.withLock {
            val userMemories = memoryCache.remove(userId)
            val clearedCount = userMemories?.size ?: 0

            logger.d("EcmMemoryCache", "清除用户ECM记忆: userId=$userId, 清除${clearedCount}条")
            clearedCount
        }
    }

    /**
     * 清除所有过期记忆
     *
     * @return 清除的记忆数量
     */
    suspend fun cleanupExpiredMemories(): Int {
        return cacheMutex.withLock {
            var totalCleaned = 0

            memoryCache.entries.removeAll { (userId, memories) ->
                val originalSize = memories.size
                memories.removeAll { it.isExpired() }
                val cleaned = originalSize - memories.size
                totalCleaned += cleaned

                if (cleaned > 0) {
                    logger.d("EcmMemoryCache", "用户 $userId 清除过期ECM记忆: ${cleaned}条")
                }

                // 如果用户没有记忆了，移除整个用户条目
                memories.isEmpty()
            }

            logger.d("EcmMemoryCache", "ECM缓存清理完成: 总计清除${totalCleaned}条过期记忆")
            totalCleaned
        }
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    suspend fun getCacheStats(): EcmCacheStats {
        return cacheMutex.withLock {
            val totalUsers = memoryCache.size
            val totalMemories = memoryCache.values.sumOf { it.size }
            val expiredMemories = memoryCache.values.sumOf { memories ->
                memories.count { it.isExpired() }
            }

            EcmCacheStats(
                totalUsers = totalUsers,
                totalMemories = totalMemories,
                expiredMemories = expiredMemories,
                averageMemoriesPerUser = if (totalUsers > 0) totalMemories.toFloat() / totalUsers else 0f,
            )
        }
    }

    /**
     * 检查缓存是否包含特定用户的记忆
     *
     * @param userId 用户ID
     * @return 是否包含该用户的记忆
     */
    suspend fun hasUserMemories(userId: String): Boolean {
        return cacheMutex.withLock {
            val userMemories = memoryCache[userId]
            val hasValidMemories = userMemories?.any { !it.isExpired() } == true

            logger.d("EcmMemoryCache", "检查用户ECM记忆: userId=$userId, has=$hasValidMemories")
            hasValidMemories
        }
    }
}

/**
 * ECM缓存统计信息
 */
data class EcmCacheStats(
    val totalUsers: Int,
    val totalMemories: Int,
    val expiredMemories: Int,
    val averageMemoriesPerUser: Float,
)
