package com.example.gymbro.core.ml.impl

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.features.FeatureErrors
import com.example.gymbro.core.ml.interfaces.VectorSearchEngine
import com.example.gymbro.core.ml.model.CandidateVector
import com.example.gymbro.core.ml.model.SearchEngineStats
import com.example.gymbro.core.ml.model.SearchWeightConfig
import com.example.gymbro.core.ml.model.VectorSearchResult
import com.example.gymbro.core.ui.text.UiText
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.sqrt

/**
 * 内存向量搜索引擎实现 - Core-ML Layer
 *
 * 纯算法实现，使用内存存储向量数据，支持：
 * - 基于余弦相似度的快速搜索
 * - 混合搜索（向量+元数据+时间权重）
 * - 批量搜索
 * - 增量向量添加
 */
@Singleton
class InMemoryVectorSearchEngine @Inject constructor() : VectorSearchEngine {

    private val storedVectors = ConcurrentHashMap<String, StoredVector>()
    private val mutex = Mutex()
    private var totalSearchTime = 0L
    private var searchCount = 0L

    /**
     * 存储的向量数据结构
     */
    private data class StoredVector(
        val vector: FloatArray,
        val metadata: Map<String, Any>,
        val addedTimestamp: Long = System.currentTimeMillis(),
    ) {
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false
            other as StoredVector
            return vector.contentEquals(other.vector) && metadata == other.metadata
        }

        override fun hashCode(): Int {
            return vector.contentHashCode() * 31 + metadata.hashCode()
        }
    }

    override suspend fun searchSimilar(
        queryVector: FloatArray,
        candidates: List<CandidateVector>,
        topK: Int,
        threshold: Float,
    ): ModernResult<List<VectorSearchResult>> {
        return try {
            val startTime = System.currentTimeMillis()

            if (queryVector.isEmpty()) {
                return ModernResult.Error(
                    FeatureErrors.CoachError.invalidInput(
                        operationName = "InMemoryVectorSearchEngine.searchSimilar",
                        message = UiText.DynamicString("查询向量不能为空"),
                        inputType = "empty_query_vector",
                        value = "size=0",
                    ),
                )
            }

            // 计算相似度
            val results = candidates.mapNotNull { candidate ->
                val similarity = cosineSimilarity(queryVector, candidate.vector)
                if (similarity >= threshold) {
                    VectorSearchResult(
                        candidateId = candidate.id,
                        similarity = similarity,
                        distance = 1.0f - similarity,
                        metadata = candidate.metadata,
                        searchStrategy = "cosine_similarity",
                    )
                } else {
                    null
                }
            }
                .sortedByDescending { it.similarity }
                .take(topK)

            // 记录性能统计
            val elapsedTime = System.currentTimeMillis() - startTime
            updateSearchStats(elapsedTime)

            Timber.d("InMemoryVectorSearchEngine: 相似度搜索完成 results=${results.size}, time=${elapsedTime}ms")
            ModernResult.Success(results)
        } catch (e: Exception) {
            Timber.e(e, "InMemoryVectorSearchEngine: 相似度搜索异常")
            ModernResult.Error(
                FeatureErrors.CoachError.processingFailed(
                    operationName = "InMemoryVectorSearchEngine.searchSimilar",
                    message = UiText.DynamicString("向量相似度搜索失败"),
                    processType = "vector_search",
                    reason = "search_exception",
                    cause = e,
                    metadataMap = mapOf(
                        "query_vector_dim" to queryVector.size,
                        "candidates_count" to candidates.size,
                        "top_k" to topK,
                        "threshold" to threshold,
                    ),
                ),
            )
        }
    }

    override suspend fun hybridSearch(
        queryVector: FloatArray,
        candidates: List<CandidateVector>,
        weightConfig: SearchWeightConfig,
        topK: Int,
    ): ModernResult<List<VectorSearchResult>> {
        return try {
            val startTime = System.currentTimeMillis()

            if (queryVector.isEmpty()) {
                return ModernResult.Error(
                    FeatureErrors.CoachError.invalidInput(
                        operationName = "InMemoryVectorSearchEngine.hybridSearch",
                        message = UiText.DynamicString("查询向量不能为空"),
                        inputType = "empty_query_vector",
                        value = "size=0",
                    ),
                )
            }

            val results = candidates.map { candidate ->
                // 1. 向量相似度分数
                val vectorSimilarity = cosineSimilarity(queryVector, candidate.vector)

                // 2. 元数据分数（简化实现：基于标签匹配）
                val metadataScore = calculateMetadataScore(candidate.metadata)

                // 3. 时间新旧分数（如果有时间戳）
                val recencyScore = calculateRecencyScore(candidate.metadata)

                // 4. 加权组合分数
                val hybridScore = vectorSimilarity * weightConfig.vectorWeight +
                    metadataScore * weightConfig.metadataWeight +
                    recencyScore * weightConfig.recencyWeight

                VectorSearchResult(
                    candidateId = candidate.id,
                    similarity = hybridScore,
                    distance = 1.0f - hybridScore,
                    metadata = candidate.metadata + mapOf(
                        "vector_similarity" to vectorSimilarity,
                        "metadata_score" to metadataScore,
                        "recency_score" to recencyScore,
                    ),
                    searchStrategy = "hybrid_search",
                )
            }
                .sortedByDescending { it.similarity }
                .take(topK)

            val elapsedTime = System.currentTimeMillis() - startTime
            updateSearchStats(elapsedTime)

            Timber.d("InMemoryVectorSearchEngine: 混合搜索完成 results=${results.size}, time=${elapsedTime}ms")
            ModernResult.Success(results)
        } catch (e: Exception) {
            Timber.e(e, "InMemoryVectorSearchEngine: 混合搜索异常")
            ModernResult.Error(
                FeatureErrors.CoachError.processingFailed(
                    operationName = "InMemoryVectorSearchEngine.hybridSearch",
                    message = UiText.DynamicString("混合向量搜索失败"),
                    processType = "hybrid_search",
                    reason = "search_exception",
                    cause = e,
                    metadataMap = mapOf(
                        "query_vector_dim" to queryVector.size,
                        "candidates_count" to candidates.size,
                        "weight_config" to weightConfig,
                    ),
                ),
            )
        }
    }

    override suspend fun batchSearch(
        queries: List<FloatArray>,
        candidates: List<CandidateVector>,
        topK: Int,
    ): ModernResult<List<List<VectorSearchResult>>> {
        return try {
            val startTime = System.currentTimeMillis()

            if (queries.isEmpty()) {
                return ModernResult.Error(
                    FeatureErrors.CoachError.invalidInput(
                        operationName = "InMemoryVectorSearchEngine.batchSearch",
                        message = UiText.DynamicString("查询向量列表不能为空"),
                        inputType = "empty_queries_list",
                        value = "size=0",
                    ),
                )
            }

            val batchResults = mutableListOf<List<VectorSearchResult>>()

            for (queryVector in queries) {
                val searchResult = searchSimilar(queryVector, candidates, topK, 0.0f)
                when (searchResult) {
                    is ModernResult.Success -> batchResults.add(searchResult.data)
                    is ModernResult.Error -> return ModernResult.Error(searchResult.error)
                    is ModernResult.Loading -> batchResults.add(emptyList())
                }
            }

            val elapsedTime = System.currentTimeMillis() - startTime
            updateSearchStats(elapsedTime)

            Timber.d("InMemoryVectorSearchEngine: 批量搜索完成 queries=${queries.size}, time=${elapsedTime}ms")
            ModernResult.Success(batchResults)
        } catch (e: Exception) {
            Timber.e(e, "InMemoryVectorSearchEngine: 批量搜索异常")
            ModernResult.Error(
                FeatureErrors.CoachError.processingFailed(
                    operationName = "InMemoryVectorSearchEngine.batchSearch",
                    message = UiText.DynamicString("批量向量搜索失败"),
                    processType = "batch_search",
                    reason = "search_exception",
                    cause = e,
                    metadataMap = mapOf(
                        "queries_count" to queries.size,
                        "candidates_count" to candidates.size,
                    ),
                ),
            )
        }
    }

    override suspend fun addVectors(vectors: List<CandidateVector>): ModernResult<Unit> {
        return try {
            mutex.withLock {
                vectors.forEach { candidate ->
                    storedVectors[candidate.id] = StoredVector(
                        vector = candidate.vector,
                        metadata = candidate.metadata,
                    )
                }
            }

            Timber.d("InMemoryVectorSearchEngine: 添加向量完成 count=${vectors.size}")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "InMemoryVectorSearchEngine: 添加向量异常")
            ModernResult.Error(
                FeatureErrors.CoachError.processingFailed(
                    operationName = "InMemoryVectorSearchEngine.addVectors",
                    message = UiText.DynamicString("添加向量到搜索引擎失败"),
                    processType = "vector_indexing",
                    reason = "add_vectors_exception",
                    cause = e,
                    metadataMap = mapOf("vectors_count" to vectors.size),
                ),
            )
        }
    }

    override fun getSearchStats(): SearchEngineStats {
        return SearchEngineStats(
            totalVectors = storedVectors.size,
            vectorDimension = storedVectors.values.firstOrNull()?.vector?.size ?: 0,
            averageSearchTimeMs = if (searchCount > 0) totalSearchTime / searchCount else 0L,
            indexType = "in_memory_cosine",
            memoryUsageMB = estimateMemoryUsage(),
        )
    }

    // ========== 私有辅助方法 ==========

    /**
     * 计算余弦相似度
     */
    private fun cosineSimilarity(a: FloatArray, b: FloatArray): Float {
        if (a.size != b.size) {
            Timber.w("InMemoryVectorSearchEngine: 向量维度不匹配 ${a.size} vs ${b.size}")
            return 0f
        }

        return try {
            var dotProduct = 0.0f
            var normA = 0.0f
            var normB = 0.0f

            for (i in a.indices) {
                dotProduct += a[i] * b[i]
                normA += a[i] * a[i]
                normB += b[i] * b[i]
            }

            normA = sqrt(normA)
            normB = sqrt(normB)

            if (normA == 0.0f || normB == 0.0f) {
                0.0f
            } else {
                dotProduct / (normA * normB)
            }
        } catch (e: Exception) {
            Timber.e(e, "InMemoryVectorSearchEngine: 计算余弦相似度异常")
            0f
        }
    }

    /**
     * 计算元数据分数
     */
    private fun calculateMetadataScore(metadata: Map<String, Any>): Float {
        // 简化实现：基于元数据的丰富程度
        return when {
            metadata.isEmpty() -> 0.0f
            metadata.size >= 5 -> 1.0f
            else -> metadata.size / 5.0f
        }
    }

    /**
     * 计算时间新旧分数
     */
    private fun calculateRecencyScore(metadata: Map<String, Any>): Float {
        val timestamp = metadata["timestamp"] as? Long ?: return 0.5f
        val now = System.currentTimeMillis()
        val age = now - timestamp
        val maxAge = 7 * 24 * 60 * 60 * 1000L // 7天

        return when {
            age <= 0 -> 1.0f
            age >= maxAge -> 0.0f
            else -> 1.0f - (age.toFloat() / maxAge)
        }
    }

    /**
     * 更新搜索统计
     */
    private fun updateSearchStats(elapsedTime: Long) {
        totalSearchTime += elapsedTime
        searchCount++
    }

    /**
     * 估算内存使用量
     */
    private fun estimateMemoryUsage(): Float {
        val vectorCount = storedVectors.size
        val avgVectorDim = storedVectors.values.firstOrNull()?.vector?.size ?: 0
        // 粗略估算：每个float 4字节 + 元数据开销
        val bytesPerVector = avgVectorDim * 4 + 200 // 200字节元数据开销
        return (vectorCount * bytesPerVector) / (1024 * 1024).toFloat()
    }
}
