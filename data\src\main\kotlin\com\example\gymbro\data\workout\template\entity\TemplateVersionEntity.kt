package com.example.gymbro.data.workout.template.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * 模板版本数据库实体 - Room ORM映射
 *
 * Phase1: BaseTemplate架构升级 - 版本控制数据持久化
 * 实现不可变版本快照的数据库存储
 *
 * @property id 版本唯一标识符(主键)
 * @property templateId 关联的WorkoutTemplate ID(外键)
 * @property versionNumber 版本号(递增序列)
 * @property contentJson 序列化的WorkoutTemplate完整内容
 * @property createdAt 版本创建时间戳(毫秒)
 * @property description 版本描述信息
 * @property isAutoSaved 区分手动发布版本和自动保存版本
 */
@Entity(
    tableName = "template_versions",
    foreignKeys = [
        ForeignKey(
            entity = TemplateEntity::class,
            parentColumns = ["id"],
            childColumns = ["templateId"],
            onDelete = ForeignKey.CASCADE, // 模板删除时级联删除所有版本
        ),
    ],
    indices = [
        Index(value = ["templateId"]), // 优化按模板查询版本历史
        Index(value = ["templateId", "versionNumber"], unique = true), // 确保模板内版本号唯一
        Index(value = ["createdAt"]), // 优化时间范围查询
        Index(value = ["isAutoSaved"]), // 优化过滤自动保存版本
    ],
)
data class TemplateVersionEntity(
    @PrimaryKey
    val id: String,

    val templateId: String,

    val versionNumber: Int,

    /**
     * 序列化的WorkoutTemplate JSON内容
     * 使用kotlinx.serialization进行序列化/反序列化
     */
    val contentJson: String,

    val createdAt: Long,

    val description: String? = null,

    val isAutoSaved: Boolean = false,
) {
    companion object {
        /**
         * 表名常量
         */
        const val TABLE_NAME = "template_versions"

        /**
         * 字段名常量
         */
        const val COLUMN_ID = "id"
        const val COLUMN_TEMPLATE_ID = "templateId"
        const val COLUMN_VERSION_NUMBER = "versionNumber"
        const val COLUMN_CONTENT_JSON = "contentJson"
        const val COLUMN_CREATED_AT = "createdAt"
        const val COLUMN_DESCRIPTION = "description"
        const val COLUMN_IS_AUTO_SAVED = "isAutoSaved"
    }

    /**
     * 是否为发布版本
     */
    val isPublished: Boolean
        get() = !isAutoSaved

    /**
     * 获取版本标签
     */
    fun getVersionLabel(): String {
        return "v$versionNumber${if (isAutoSaved) " (Auto)" else ""}"
    }

    /**
     * 检查内容JSON是否有效
     */
    fun hasValidContent(): Boolean {
        return contentJson.isNotBlank() && contentJson.startsWith("{")
    }
}
