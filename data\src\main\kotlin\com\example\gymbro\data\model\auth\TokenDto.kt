package com.example.gymbro.data.model.auth

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Token数据传输对象
 * 用于网络API响应
 */
@Serializable
data class TokenDto(
    @SerialName("access_token")
    val accessToken: String,

    @SerialName("refresh_token")
    val refreshToken: String,

    @SerialName("token_type")
    val tokenType: String,

    @SerialName("expires_in")
    val expiresIn: Long,

    @SerialName("issued_at")
    val issuedAt: Long? = null,

    @SerialName("user_id")
    val userId: String,

    @SerialName("scope")
    val scope: String? = null,
)
