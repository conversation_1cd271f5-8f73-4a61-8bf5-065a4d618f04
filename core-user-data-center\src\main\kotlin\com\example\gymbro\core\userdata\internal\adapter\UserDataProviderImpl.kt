package com.example.gymbro.core.userdata.internal.adapter

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.userdata.api.UserDataCenterApi
import com.example.gymbro.core.userdata.api.model.UserDataState
import com.example.gymbro.domain.coach.model.ai.UserAiContext
import com.example.gymbro.domain.user.repository.UserDataProvider
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * UserDataProvider接口的实现
 *
 * 作为适配器模式的实现，将UserDataCenterApi适配为domain层的UserDataProvider接口。
 * 这样既保持了Clean Architecture的依赖倒置原则，又实现了功能集成。
 *
 * 核心职责：
 * - 将UnifiedUserData转换为UserAiContext
 * - 处理UserDataState的各种状态
 * - 提供响应式的用户数据流
 * - 错误处理和日志记录
 */
@Singleton
class UserDataProviderImpl
    @Inject
    constructor(
        private val userDataCenterApi: UserDataCenterApi,
        private val logger: Logger,
    ) : UserDataProvider {
        companion object {
            private const val TAG = "UserDataProviderImpl"
        }

        /**
         * 获取当前用户的AI上下文数据
         */
        override suspend fun getCurrentUserAiContext(): ModernResult<UserAiContext?> =
            try {
                // 🔥 【关闭TOKEN-FLOW日志】UserDataProviderImpl工作正常，关闭详细日志减少噪音
                logger.d(TAG, "🔍 开始获取当前用户AI上下文")

                when (val result = userDataCenterApi.getCurrentUserData()) {
                    is ModernResult.Success -> {
                        val unifiedUserData = result.data
                        if (unifiedUserData != null) {
                            val userAiContext = convertToUserAiContext(unifiedUserData)
                            // 🔥 【关闭TOKEN-FLOW日志】用户数据获取成功
                            logger.d(TAG, "✅ 成功转换用户数据为AI上下文: userId=${userAiContext.userId}")
                            ModernResult.Success(userAiContext)
                        } else {
                            // 🔥 【关闭TOKEN-FLOW日志】用户数据为空
                            logger.d(TAG, "⚠️ 用户数据为空，返回null")
                            ModernResult.Success(null)
                        }
                    }
                    is ModernResult.Error -> {
                        // 🔥 【保留错误日志】用户数据获取失败
                        logger.w(TAG, "⚠️ [TOKEN-FLOW] [UserDataProviderImpl] 获取用户数据失败: ${result.error}")
                        ModernResult.Error(result.error)
                    }
                    is ModernResult.Loading -> {
                        // 🔥 【关闭TOKEN-FLOW日志】用户数据加载中
                        logger.d(TAG, "⏳ 用户数据正在加载中")
                        ModernResult.Loading
                    }
                }
            } catch (e: Exception) {
                logger.e(e, TAG, "获取用户AI上下文时发生异常")
                ModernResult.Error(
                    com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                        operationName = "getCurrentUserAiContext",
                        message =
                            com.example.gymbro.core.ui.text.UiText
                                .DynamicString("获取用户数据失败"),
                        recoverable = true,
                    ),
                )
            }

        /**
         * 观察用户AI上下文数据的变化
         */
        override fun observeUserAiContext(): Flow<UserAiContext?> {
            logger.d(TAG, "开始观察用户AI上下文变化")

            return userDataCenterApi.observeUserData().map { userDataState ->
                when (userDataState) {
                    is UserDataState.Success -> {
                        val userAiContext = convertToUserAiContext(userDataState.data)
                        logger.d(TAG, "用户数据更新: userId=${userAiContext.userId}, complete=${userAiContext.isComplete}")
                        userAiContext
                    }
                    is UserDataState.Syncing -> {
                        val userAiContext = convertToUserAiContext(userDataState.currentData)
                        logger.d(TAG, "用户数据同步中: userId=${userAiContext.userId}")
                        userAiContext
                    }
                    is UserDataState.Error -> {
                        logger.w(TAG, "用户数据错误: ${userDataState.error}")
                        userDataState.partialData?.let { convertToUserAiContext(it) }
                    }
                    is UserDataState.Loading -> {
                        logger.d(TAG, "用户数据加载中")
                        null
                    }
                    is UserDataState.Empty -> {
                        logger.d(TAG, "用户数据为空")
                        null
                    }
                }
            }
        }

        /**
         * 将UnifiedUserData转换为UserAiContext
         */
        private fun convertToUserAiContext(unifiedUserData: com.example.gymbro.core.userdata.api.model.UnifiedUserData): UserAiContext =
            UserAiContext(
                userId = unifiedUserData.userId,
                displayName = unifiedUserData.displayName,
                fitnessLevel = unifiedUserData.fitnessLevel,
                height = unifiedUserData.height,
                weight = unifiedUserData.weight,
                gender = unifiedUserData.gender,
                fitnessGoals = unifiedUserData.fitnessGoals,
                workoutDays = unifiedUserData.workoutDays,
                allowPartnerMatching = unifiedUserData.allowPartnerMatching,
                bio = unifiedUserData.bio,
                totalActivityCount = unifiedUserData.totalActivityCount,
                weeklyActiveMinutes = unifiedUserData.weeklyActiveMinutes,
                lastUpdated = unifiedUserData.lastUpdated,
                contextVersion = unifiedUserData.dataVersion,
            )
    }
