{"action": "opened", "number": 1, "pull_request": {"id": 1, "number": 1, "title": "MVP Test PR", "body": "Testing MVP CI/CD pipeline with ACT", "user": {"login": "developer", "id": 1}, "base": {"ref": "main", "sha": "abc123", "repo": {"name": "GymBro", "full_name": "team/GymBro"}}, "head": {"ref": "feature/mvp-test", "sha": "def456", "repo": {"name": "GymBro", "full_name": "team/GymBro"}}, "changed_files": ["domain/src/main/kotlin/com/example/gymbro/domain/usecase/TestUseCase.kt", "data/src/main/kotlin/com/example/gymbro/data/repository/TestRepository.kt", "features/coach/src/main/kotlin/com/example/gymbro/coach/CoachScreen.kt", "features/workout/src/main/kotlin/com/example/gymbro/workout/WorkoutScreen.kt"]}, "repository": {"name": "GymBro", "full_name": "team/GymBro", "owner": {"login": "team", "id": 1}, "default_branch": "main"}, "sender": {"login": "developer", "id": 1}}