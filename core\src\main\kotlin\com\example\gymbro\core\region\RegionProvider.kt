package com.example.gymbro.core.region

import kotlinx.coroutines.flow.StateFlow

/**
 * 地区提供者接口
 *
 * 为整个应用提供统一的地区信息访问接口。
 * 所有模块都应该通过此接口获取用户地区信息，而不是直接访问检测逻辑。
 */
interface RegionProvider {

    /**
     * 用户地区枚举
     */
    enum class UserRegion {
        /** 中国大陆地区 */
        CN,

        /** 国际地区（包括港澳台） */
        INTERNATIONAL,
    }

    /**
     * 地区信息状态流
     *
     * 其他模块可以订阅此StateFlow来获取地区变化通知
     */
    val regionState: StateFlow<UserRegion?>

    /**
     * 获取当前检测到的地区
     *
     * @return 当前地区，如果尚未检测则返回null
     */
    fun getCurrentRegion(): UserRegion?

    /**
     * 检查是否为中国大陆地区
     */
    fun isChinaRegion(): Boolean

    /**
     * 检查是否为国际地区
     */
    fun isInternationalRegion(): Boolean
}
