# Core User Data Center

## 📋 概述

`core-user-data-center` 是 GymBro 应用的用户数据统一管理模块，作为用户数据的单一真实来源（SSOT），解决了 Auth、Profile、Coach 模块间的数据分散和不一致问题。

**🎯 重大更新**：已成功实现 Room 数据库持久化，通过创新的依赖注入架构解决了循环依赖问题，同时保持了 Clean Architecture 原则。

## 🎯 核心功能

### 统一数据管理
- **单一数据源**：所有用户数据通过 UserDataCenter 统一管理
- **Room 持久化**：基于真正的 Room 数据库提供可靠的数据持久化
- **响应式数据流**：通过 Flow 提供实时数据变化通知
- **数据合并**：自动合并认证数据（`AuthUser`）和用户资料（`UserProfile`）为 `UnifiedUserData`
- **状态管理**：完整的同步状态管理（`SyncStatus`: SYNCED/PENDING/ERROR）

### 架构创新
- **循环依赖解决**：通过适配器模式避免 core-user-data-center → data 的直接依赖
- **Hilt 多实现支持**：使用限定符（`@SimpleUserDataSource`, `@RoomUserDataSource`, `@DefaultUserDataSource`）支持多种实现
- **Clean Architecture**：严格遵循依赖倒置原则，保持模块间的清晰边界
- **可测试性**：提供简化的内存实现用于单元测试，Room 实现用于集成测试

### 模块集成
- **Auth 模块**：登录/注册成功后自动同步用户数据
- **Profile 模块**：通过 UserDataService 接口获取和更新用户资料
- **Coach 模块**：获取用户上下文数据用于 AI 个性化

### 🆕 最新架构改进（2025-01-14）
- **UserDataService 接口**：在 domain 层新增 UserDataService 接口，遵循依赖倒置原则
- **数据持久化修复**：解决了 PersonalInfo 编辑数据重置问题，确保用户输入正确保存
- **预设数据清理**：移除了硬编码测试数据，避免覆盖用户真实输入
- **循环依赖优化**：通过接口隔离彻底解决了 domain ↔ core-user-data-center 的循环依赖

## 🏗️ 架构设计

### 核心组件

```
core-user-data-center/
├── api/                          # 对外接口层
│   ├── UserDataCenterApi.kt      # 主要 API 接口
│   └── model/
│       ├── UnifiedUserData.kt    # 统一用户数据模型
│       └── SyncStatus.kt         # 同步状态枚举
├── internal/                     # 内部实现层
│   ├── api/
│   │   └── UserDataCenterApiImpl.kt    # API 实现
│   ├── repository/
│   │   ├── UserDataRepository.kt       # 数据仓库接口
│   │   └── UserDataRepositoryImpl.kt   # 数据仓库实现（使用 @DefaultUserDataSource）
│   ├── datasource/               # 数据源抽象层
│   │   ├── UserLocalDataSource.kt      # 本地数据源接口
│   │   └── UserLocalDataSourceSimpleImpl.kt  # 简化内存实现（@SimpleUserDataSource）
│   ├── dao/                      # 抽象 DAO 接口层
│   │   ├── UserDataDao.kt        # 用户认证数据 DAO 接口
│   │   └── UserProfileDao.kt     # 用户资料数据 DAO 接口
│   ├── adapter/
│   │   └── UserDataProviderImpl.kt     # Domain 层适配器
│   ├── mapper/
│   │   └── UserDataMapper.kt           # 数据转换映射器
│   ├── synchronizer/
│   │   └── UserDataSynchronizer.kt     # 数据同步器
│   └── di/
│       ├── UserDataCenterModule.kt     # 依赖注入配置（简化实现）
│       └── UserDataSourceQualifiers.kt # Hilt 限定符定义

di/ (模块)                        # 真正的 Room 实现层
├── core/userdata/
│   ├── UserDataDaoAdapter.kt     # UserDao → UserDataDao 适配器
│   ├── UserProfileDaoAdapter.kt  # UserProfileDao → UserProfileDao 适配器
│   ├── UserLocalDataSourceRoomImpl.kt  # Room 实现（@RoomUserDataSource）
│   ├── UserDataExtensions.kt     # 数据转换扩展方法
│   └── UserDataCenterDiModule.kt # Room 实现的依赖注入配置
```

## 🔧 Hilt 依赖注入解决方案

### 问题背景
在实现 Room 数据库持久化时，我们遇到了 Hilt 依赖注入冲突：
- `core-user-data-center` 模块绑定了 `UserLocalDataSourceSimpleImpl`
- `di` 模块绑定了 `UserLocalDataSourceRoomImpl`
- 导致 `UserLocalDataSource is bound multiple times` 错误

### 解决方案：Hilt 限定符
使用 Hilt 限定符区分不同的实现：

```kotlin
@SimpleUserDataSource    // 简化内存实现（测试用）
@RoomUserDataSource      // Room 数据库实现
@DefaultUserDataSource   // 默认实现（指向 Room）
```

### 实现架构
```
Application
    ↓ @DefaultUserDataSource
UserDataRepositoryImpl
    ↓
UserLocalDataSourceRoomImpl (di 模块)
    ↓
UserDataDaoAdapter + UserProfileDaoAdapter (di 模块)
    ↓
UserDao + UserProfileDao (data 模块)
    ↓
Room Database
```

### 使用方式
```kotlin
// 生产环境 - 自动使用 Room 实现
@Inject constructor(
    @DefaultUserDataSource private val dataSource: UserLocalDataSource
)

// 测试环境 - 使用简化实现
@Inject constructor(
    @SimpleUserDataSource private val dataSource: UserLocalDataSource
)
```

### 数据流架构

```
认证成功 → AuthRepository → UserDataCenter.syncAuthData()
    ↓
UserDataRepositoryImpl (@DefaultUserDataSource)
    ↓
UserLocalDataSourceRoomImpl (di 模块)
    ↓
UserDataDaoAdapter + UserProfileDaoAdapter
    ↓
Room Database (UserDao + UserProfileDao)
    ↓
Flow 数据更新 → 所有消费模块自动获取最新数据
    ↓
Profile/Coach 模块 → 响应式数据更新
```

## 🔧 使用方式

### 1. 依赖注入

在模块的 `build.gradle.kts` 中添加依赖：

```kotlin
dependencies {
    implementation(project(":core-user-data-center"))
}
```

### 2. 注入 UserDataCenterApi

```kotlin
@HiltViewModel
class YourViewModel @Inject constructor(
    private val userDataCenterApi: UserDataCenterApi
) : ViewModel() {

    // 观察用户数据变化
    val userData = userDataCenterApi.observeUserData()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = UserDataState.Loading
        )
}
```

### 3. 获取用户数据

```kotlin
// 响应式获取用户数据
userDataCenterApi.observeUserData().collect { state ->
    when (state) {
        is UserDataState.Success -> {
            val userData = state.data
            // 使用用户数据
        }
        is UserDataState.Loading -> {
            // 显示加载状态
        }
        is UserDataState.Error -> {
            // 处理错误
        }
        // ... 其他状态
    }
}

// 一次性获取用户数据
val result = userDataCenterApi.getCurrentUserData()
```

### 4. 更新用户数据

```kotlin
// 同步认证数据（通常在登录后调用）
userDataCenterApi.syncAuthData(authUser)

// 更新用户资料（通过 Profile 模块）
userDataCenterApi.updateUserProfile(userProfile)
```

## 🔄 数据状态管理

### UserDataState 状态

- **Loading**：数据加载中
- **Success**：数据加载成功
- **Error**：数据加载失败（可能包含部分数据）
- **Empty**：用户未登录或数据为空
- **Syncing**：数据同步中（包含当前数据）

### 状态转换

```
Empty → Loading → Success
  ↓       ↓         ↓
  ←─── Error ←─── Syncing
```

## 🎨 设计原则

### Clean Architecture
- **依赖倒置**：domain 层定义接口，data 层实现
- **单一职责**：每个组件职责明确
- **开闭原则**：易于扩展，无需修改现有代码

### 响应式编程
- **StateFlow**：提供响应式数据流
- **自动更新**：数据变化时自动通知所有消费者
- **内存缓存**：基于内存的高性能缓存

### 模块化设计
- **接口隔离**：通过接口暴露功能
- **依赖注入**：使用 Hilt 管理依赖
- **模块解耦**：各模块通过接口交互

## 📊 集成状态

### ✅ 已集成模块

- **Auth 模块**：登录/注册后自动同步用户数据
- **Profile 模块**：通过 UserDataCenter 管理用户资料
- **Coach 模块**：获取用户上下文用于 AI 个性化

### 🔄 数据流验证

- ✅ Auth → UserDataCenter → Profile：数据流正常
- ✅ Profile → UserDataCenter → Coach：数据流正常
- ✅ 响应式更新：所有模块自动获取最新数据

## 🧪 测试

### 单元测试
- `UserDataRepositoryImpl` 测试
- `UserDataMapper` 测试
- `UserDataSynchronizer` 测试

### 集成测试
- Auth → UserDataCenter → Profile 数据流测试
- Coach 模块获取用户数据测试

## 📚 相关文档

- [用户数据储存方案](docs/userdata-center保存方案.md)
- [上下文交接文档](docs/上下文交接.md)
- [循环依赖解决方案](docs/解决循环依赖.md)
- [Hilt 依赖注入解决方案](docs/Hilt-Dependency-Injection-Solution.md) ⭐ **最新**

## ✅ 已完成功能

- [x] **Room 数据库持久化**：基于真正的 Room 数据库提供可靠的数据持久化
- [x] **循环依赖解决**：通过适配器模式避免模块间的循环依赖
- [x] **Hilt 多实现支持**：使用限定符支持多种 UserLocalDataSource 实现
- [x] **统一数据管理**：作为用户数据的单一真实来源（SSOT）
- [x] **响应式数据流**：通过 Flow 提供实时数据变化通知
- [x] **Clean Architecture**：严格遵循依赖倒置原则和模块分离
- [x] **可测试性**：提供简化实现用于单元测试，Room 实现用于集成测试

## 🔮 未来规划

- [ ] 远程数据同步（与服务器同步）
- [ ] 数据加密和安全增强
- [ ] 性能优化和缓存策略
- [ ] 数据迁移工具
- [ ] 冲突解决机制优化

---

**版本**: 2.0.0 🎉
**状态**: ✅ 生产就绪 - Room 持久化已完成
**重大更新**:
- ✅ 解决了 Hilt 依赖注入冲突
- ✅ 实现了真正的 Room 数据库持久化
- ✅ 保持了 Clean Architecture 原则
- ✅ 避免了循环依赖问题

**最后更新**: 2025-01-14
