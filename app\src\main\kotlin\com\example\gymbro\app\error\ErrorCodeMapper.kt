package com.example.gymbro.app.error

import com.example.gymbro.core.error.ErrorCode
import com.example.gymbro.core.ui.text.UiText

/**
 * ErrorCode映射器
 *
 * 将core层定义的ErrorCode映射到app层的string资源
 * 实现Clean Architecture的Error-code mapping pattern
 * 支持本地化和维护UI文本的统一管理
 *
 * <AUTHOR> Team
 * @since 2.0.0
 */
object ErrorCodeMapper {

    /**
     * 将ErrorCode映射为UiText.StringResource
     *
     * @param errorCode core层定义的错误代码
     * @param vararg params 字符串参数(用于格式化)
     * @return UiText.StringResource实例
     */
    fun mapToUiText(errorCode: ErrorCode, vararg params: Any): UiText {
        return when (errorCode.category) {
            "network" -> mapNetworkError(errorCode, params)
            "auth" -> mapAuthError(errorCode, params)
            "data" -> mapDataError(errorCode, params)
            "validation" -> mapValidationError(errorCode, params)
            "business" -> mapBusinessError(errorCode, params)
            "system" -> mapSystemError(errorCode, params)
            "payment" -> mapPaymentError(errorCode, params)
            else -> UiText.StringResource(
                resId = com.example.gymbro.designSystem.R.string.error_generic,
                args = emptyList(),
            )
        }
    }

    /**
     * 映射网络错误
     */
    private fun mapNetworkError(errorCode: ErrorCode, params: Array<out Any>): UiText {
        return when (errorCode) {
            ErrorCode.NETWORK_RETRY_EXHAUSTED,
            ErrorCode.NETWORK_FALLBACK_CACHE,
            ErrorCode.NETWORK_OFFLINE_MODE,
            ErrorCode.NETWORK_UNSTABLE,
            ->
                UiText.StringResource(
                    resId = com.example.gymbro.designSystem.R.string.network_not_available,
                    args = emptyList(),
                )

            ErrorCode.NETWORK_CONNECTION_FAILED,
            ErrorCode.NETWORK_EXCEPTION,
            ->
                createUiText(
                    resId = com.example.gymbro.designSystem.R.string.login_error_network,
                    params = params,
                )

            else -> UiText.StringResource(
                resId = com.example.gymbro.designSystem.R.string.network_not_available,
                args = emptyList(),
            )
        }
    }

    /**
     * 映射认证错误
     */
    private fun mapAuthError(errorCode: ErrorCode, params: Array<out Any>): UiText {
        return when (errorCode) {
            ErrorCode.AUTH_FAILED ->
                UiText.StringResource(
                    resId = com.example.gymbro.designSystem.R.string.error_login_failed,
                    args = emptyList(),
                )
            ErrorCode.AUTH_SESSION_EXPIRED ->
                UiText.StringResource(
                    resId = com.example.gymbro.designSystem.R.string.auth_token_expired,
                    args = emptyList(),
                )
            ErrorCode.AUTH_FORBIDDEN ->
                UiText.StringResource(
                    resId = com.example.gymbro.designSystem.R.string.auth_unauthorized,
                    args = emptyList(),
                )
            else ->
                UiText.StringResource(
                    resId = com.example.gymbro.designSystem.R.string.error_login_failed,
                    args = emptyList(),
                )
        }
    }

    /**
     * 映射数据错误
     */
    private fun mapDataError(errorCode: ErrorCode, params: Array<out Any>): UiText {
        return when (errorCode) {
            ErrorCode.DATA_NOT_FOUND,
            ErrorCode.DATA_NOT_FOUND_WITH_ID,
            ->
                createUiText(
                    resId = com.example.gymbro.designSystem.R.string.error_generic,
                    params = params,
                )
            else ->
                UiText.StringResource(
                    resId = com.example.gymbro.designSystem.R.string.error_generic,
                    args = emptyList(),
                )
        }
    }

    /**
     * 映射验证错误
     */
    private fun mapValidationError(errorCode: ErrorCode, params: Array<out Any>): UiText {
        return when (errorCode) {
            ErrorCode.VALIDATION_REQUIRED ->
                createUiText(
                    resId = com.example.gymbro.designSystem.R.string.field_required,
                    params = params,
                )
            ErrorCode.VALIDATION_FORMAT_INVALID ->
                createUiText(
                    resId = com.example.gymbro.designSystem.R.string.invalid_format,
                    params = params,
                )
            else ->
                UiText.StringResource(
                    resId = com.example.gymbro.designSystem.R.string.general_validation_error,
                    args = emptyList(),
                )
        }
    }

    /**
     * 映射业务错误
     */
    private fun mapBusinessError(errorCode: ErrorCode, params: Array<out Any>): UiText {
        return UiText.StringResource(
            resId = com.example.gymbro.designSystem.R.string.error_generic,
            args = emptyList(),
        )
    }

    /**
     * 映射系统错误
     */
    private fun mapSystemError(errorCode: ErrorCode, params: Array<out Any>): UiText {
        return UiText.StringResource(
            resId = com.example.gymbro.designSystem.R.string.error_generic,
            args = emptyList(),
        )
    }

    /**
     * 映射支付错误
     */
    private fun mapPaymentError(errorCode: ErrorCode, params: Array<out Any>): UiText {
        // 所有支付错误都使用通用错误消息
        return UiText.StringResource(
            resId = com.example.gymbro.designSystem.R.string.error_generic,
            args = emptyList(),
        )
    }

    /**
     * 创建带参数的UiText
     */
    private fun createUiText(resId: Int, params: Array<out Any>): UiText {
        return if (params.isNotEmpty()) {
            UiText.StringResource(
                resId = resId,
                args = params.map { it.toString() },
            )
        } else {
            UiText.StringResource(
                resId = resId,
                args = emptyList(),
            )
        }
    }

    /**
     * 获取ErrorCode对应的恢复建议
     *
     * @param errorCode 错误代码
     * @return 恢复建议的UiText
     */
    fun getRecoverySuggestion(errorCode: ErrorCode): UiText? {
        return when (errorCode.category) {
            "network" -> UiText.StringResource(
                resId = com.example.gymbro.designSystem.R.string.retry,
                args = emptyList(),
            )
            "auth" -> UiText.DynamicString("请重新登录")
            "payment" -> UiText.StringResource(
                resId = com.example.gymbro.designSystem.R.string.retry,
                args = emptyList(),
            )
            "validation" -> UiText.StringResource(
                resId = com.example.gymbro.designSystem.R.string.retry,
                args = emptyList(),
            )
            else -> null
        }
    }

    /**
     * 批量映射ErrorCode列表
     *
     * @param errorCodes 错误代码列表
     * @return UiText列表
     */
    fun mapErrorCodes(errorCodes: List<ErrorCode>): List<UiText> {
        return errorCodes.map { mapToUiText(it) }
    }

    /**
     * 根据错误分类获取通用错误消息
     *
     * @param category 错误分类
     * @return 通用错误消息
     */
    fun getGenericErrorForCategory(category: String): UiText {
        return when (category) {
            "network" -> UiText.StringResource(
                resId = com.example.gymbro.designSystem.R.string.network_not_available,
                args = emptyList(),
            )
            "auth" -> UiText.StringResource(
                resId = com.example.gymbro.designSystem.R.string.error_login_failed,
                args = emptyList(),
            )
            "data" -> UiText.StringResource(
                resId = com.example.gymbro.designSystem.R.string.error_generic,
                args = emptyList(),
            )
            "validation" -> UiText.StringResource(
                resId = com.example.gymbro.designSystem.R.string.general_validation_error,
                args = emptyList(),
            )
            "business" -> UiText.StringResource(
                resId = com.example.gymbro.designSystem.R.string.error_generic,
                args = emptyList(),
            )
            "system" -> UiText.StringResource(
                resId = com.example.gymbro.designSystem.R.string.error_generic,
                args = emptyList(),
            )
            "payment" -> UiText.StringResource(
                resId = com.example.gymbro.designSystem.R.string.error_generic,
                args = emptyList(),
            )
            else -> UiText.StringResource(
                resId = com.example.gymbro.designSystem.R.string.error_generic,
                args = emptyList(),
            )
        }
    }
}
