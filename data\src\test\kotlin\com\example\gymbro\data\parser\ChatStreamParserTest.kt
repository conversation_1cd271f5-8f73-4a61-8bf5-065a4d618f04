package com.example.gymbro.data.parser

import com.example.gymbro.data.coach.parser.ChatStreamParser
import com.example.gymbro.domain.coach.model.StreamEvent
import org.junit.Assert.*
import org.junit.Test

/**
 * ChatStreamParser单元测试 - TDD实现
 *
 * 测试覆盖：
 * 1. 正常标签解析
 * 2. 边界情况处理
 * 3. 错误格式容错
 * 4. 状态追踪
 */
class ChatStreamParserTest {
    private val parser = ChatStreamParser()
    private val sessionId = "session_123"
    private val userMessageId = "user_456"
    private val aiResponseId = "ai_789"

    @Test
    fun `parse step start tag should return StepStarted event`() {
        // Given
        val line = """<step id="search" title="🔍 搜索相关信息">"""

        // When
        val events = parser.parse(line, sessionId, userMessageId, aiResponseId)

        // Then
        assertEquals(1, events.size)
        val event = events.first() as StreamEvent.StepStarted
        assertEquals("search", event.stepId)
        assertEquals("🔍 搜索相关信息", event.title)
        assertEquals(sessionId, event.sessionId)
        assertEquals(userMessageId, event.userMessageId)
        assertEquals(aiResponseId, event.aiResponseId)
    }

    @Test
    fun `parse step end tag should return StepEnded event`() {
        // Given
        val line = """</step>"""
        val currentStepId = "search"

        // When
        val events = parser.parse(line, sessionId, userMessageId, aiResponseId, currentStepId = currentStepId)

        // Then
        assertEquals(1, events.size)
        val event = events.first() as StreamEvent.StepEnded
        assertEquals("search", event.stepId)
        assertEquals(sessionId, event.sessionId)
    }

    @Test
    fun `parse final start tag should return FinalStarted event`() {
        // Given
        val line = """<final type="markdown">"""

        // When
        val events = parser.parse(line, sessionId, userMessageId, aiResponseId)

        // Then
        assertEquals(1, events.size)
        val event = events.first() as StreamEvent.FinalStarted
        assertEquals("markdown", event.type)
        assertEquals(sessionId, event.sessionId)
    }

    @Test
    fun `parse final end tag should return FinalEnded event`() {
        // Given
        val line = """</final>"""
        val currentFinalType = "markdown"

        // When
        val events = parser.parse(
            line,
            sessionId,
            userMessageId,
            aiResponseId,
            currentFinalType = currentFinalType,
        )

        // Then
        assertEquals(1, events.size)
        val event = events.first() as StreamEvent.FinalEnded
        assertEquals("markdown", event.type)
    }

    @Test
    fun `parse regular content should return Chunk event`() {
        // Given
        val line = "这是一段普通的AI回复内容"

        // When
        val events = parser.parse(line, sessionId, userMessageId, aiResponseId)

        // Then
        assertEquals(1, events.size)
        val event = events.first() as StreamEvent.Chunk
        assertEquals("这是一段普通的AI回复内容", event.content)
    }

    @Test
    fun `parse mixed content with tag and text should return multiple events`() {
        // Given
        val line = """<step id="analyze" title="分析问题">开始分析用户需求"""

        // When
        val events = parser.parse(line, sessionId, userMessageId, aiResponseId)

        // Then
        assertEquals(2, events.size)
        assertTrue(events[0] is StreamEvent.StepStarted)
        assertTrue(events[1] is StreamEvent.Chunk)
        assertEquals("开始分析用户需求", (events[1] as StreamEvent.Chunk).content)
    }

    @Test
    fun `parse malformed tag should return Chunk event for entire line`() {
        // Given - 缺少引号的错误标签
        val line = """<step id=search title=搜索>"""

        // When
        val events = parser.parse(line, sessionId, userMessageId, aiResponseId)

        // Then
        assertEquals(1, events.size)
        val event = events.first() as StreamEvent.Chunk
        assertEquals(line, event.content)
    }

    @Test
    fun `parse empty line should return empty list`() {
        // Given
        val line = ""

        // When
        val events = parser.parse(line, sessionId, userMessageId, aiResponseId)

        // Then
        assertTrue(events.isEmpty())
    }

    @Test
    fun `parse whitespace only line should return empty list`() {
        // Given
        val line = "   \t  \n  "

        // When
        val events = parser.parse(line, sessionId, userMessageId, aiResponseId)

        // Then
        assertTrue(events.isEmpty())
    }

    @Test
    fun `parse step end without current step should return Chunk event`() {
        // Given
        val line = """</step>"""
        // currentStepId = null (默认)

        // When
        val events = parser.parse(line, sessionId, userMessageId, aiResponseId)

        // Then
        assertEquals(1, events.size)
        val event = events.first() as StreamEvent.Chunk
        assertEquals(line, event.content)
    }

    @Test
    fun `parse final end without current final type should return Chunk event`() {
        // Given
        val line = """</final>"""
        // currentFinalType = null (默认)

        // When
        val events = parser.parse(line, sessionId, userMessageId, aiResponseId)

        // Then
        assertEquals(1, events.size)
        val event = events.first() as StreamEvent.Chunk
        assertEquals(line, event.content)
    }
}
