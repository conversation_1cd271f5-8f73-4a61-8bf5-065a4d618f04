package com.example.gymbro.core.ml.di

import com.example.gymbro.core.ai.tokenizer.TokenizerService
import com.example.gymbro.core.ml.tokenizer.OpenAiTokenizer
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Core-ML模块的绑定配置
 *
 * 提供接口到实现的绑定，覆盖默认实现
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class CoreMlBindsModule {

    /**
     * 🔥 P0功能：绑定OpenAI Tokenizer服务
     * 覆盖core模块中的CharacterBasedTokenizerService
     * 提供真实的OpenAI tokenizer功能
     */
    @Binds
    @Singleton
    abstract fun bindTokenizerService(
        openAiTokenizer: OpenAiTokenizer,
    ): TokenizerService
}
