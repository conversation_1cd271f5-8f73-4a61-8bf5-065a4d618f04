package com.example.gymbro.designSystem.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.platform.LocalInspectionMode
import com.example.gymbro.designSystem.theme.tokens.LocalSpacing
import com.example.gymbro.designSystem.theme.tokens.rememberGymBroTypography
import com.example.gymbro.designSystem.theme.tokens.rememberMapleMono
import com.example.gymbro.designSystem.theme.tokens.rememberSpacing

/**
 * 检查当前上下文是否已经在GymBroTheme中
 * 使用一个标记来追踪以避免重复嵌套
 */
private val insideGymBroTheme = ThreadLocal<Boolean>()

/**
 * 使组件包装器，用于确保组件在GymBroTheme环境中渲染
 * 如果当前已经在GymBroTheme环境中，则直接显示内容
 * 否则，应用GymBroTheme
 *
 * @param darkTheme 是否使用深色主题，默认会遵循系统设置
 * @param content 要显示的内容
 */
@Composable
fun gymBroThemeWrapper(
    darkTheme: Boolean? = null,
    content: @Composable () -> Unit,
) {
    // 检查当前是否已经在GymBroTheme环境中
    val alreadyInGymBroTheme = insideGymBroTheme.get() == true

    // 如果已经在主题环境中，直接显示内容避免重复包装
    if (alreadyInGymBroTheme) {
        // 直接显示内容，无需再次包装
        content()
    } else {
        // 标记正在GymBroTheme环境中
        insideGymBroTheme.set(true)

        // 当在设计预览中，确保spacing可用
        if (LocalInspectionMode.current) {
            val mapleMono = rememberMapleMono()
            val typography = rememberGymBroTypography()
            val spacing = rememberSpacing() // 使用密度感知的spacing

            CompositionLocalProvider(
                LocalSpacing provides spacing,
                LocalGymBroFontFamily provides mapleMono,
            ) {
                MaterialTheme(
                    typography = typography,
                    content = content,
                )
            }
        } else {
            // 应用完整主题
            GymBroTheme(
                darkTheme = darkTheme ?: isSystemInDarkTheme(),
                content = content,
            )
            // 重置标记，确保状态一致
            insideGymBroTheme.set(false)
        }
    }
}
