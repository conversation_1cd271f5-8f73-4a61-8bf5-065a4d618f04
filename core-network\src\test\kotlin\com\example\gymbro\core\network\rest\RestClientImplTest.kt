package com.example.gymbro.core.network.rest

import com.example.gymbro.core.network.monitor.NetworkMonitor
import com.example.gymbro.core.network.monitor.NetworkState
import io.mockk.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.runTest
import okhttp3.OkHttpClient
import okhttp3.mockwebserver.MockResponse
import okhttp3.mockwebserver.MockWebServer
import org.junit.After
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * RestClientImpl V2 单元测试
 *
 * 测试拦截器链、ApiResult封装和网络状态处理
 */
class RestClientImplTest {

    private lateinit var mockWebServer: MockWebServer
    private lateinit var okHttpClient: OkHttpClient
    private lateinit var config: RestConfig
    private lateinit var networkMonitor: NetworkMonitor
    private lateinit var client: RestClientImpl

    @Before
    fun setup() {
        mockWebServer = MockWebServer()
        mockWebServer.start()

        okHttpClient = OkHttpClient.Builder()
            .connectTimeout(1, java.util.concurrent.TimeUnit.SECONDS)
            .readTimeout(1, java.util.concurrent.TimeUnit.SECONDS)
            .build()

        config = RestConfig(
            connectTimeoutSec = 5,
            readTimeoutSec = 5,
            writeTimeoutSec = 5,
            apiKey = "test-api-key",
            enableRetry = true,
            maxRetries = 2,
            retryDelayMs = 100, // 快速测试
            enableLogging = true,
        )

        networkMonitor = mockk<NetworkMonitor> {
            every { networkState } returns MutableStateFlow(NetworkState.Available())
            every { isOnline } returns true
        }

        client = RestClientImpl(okHttpClient, config, networkMonitor)
    }

    @After
    fun teardown() {
        mockWebServer.shutdown()
    }

    @Test
    fun `test successful GET request`() = runTest {
        // 模拟成功响应
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(200)
                .setBody("Success response"),
        )

        val result = client.get(mockWebServer.url("/test").toString())

        assertTrue(result.isSuccess)
        assertEquals("Success response", result.getOrNull())
    }

    @Test
    fun `test successful POST request`() = runTest {
        // 模拟成功响应
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(201)
                .setBody("Created"),
        )

        val result = client.post(
            mockWebServer.url("/test").toString(),
            """{"test": "data"}""",
        )

        assertTrue(result.isSuccess)
        assertEquals("Created", result.getOrNull())
    }

    @Test
    fun `test HTTP error response`() = runTest {
        // 模拟HTTP错误
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(404)
                .setBody("Not Found"),
        )

        val result = client.get(mockWebServer.url("/test").toString())

        assertTrue(result.isError)
        val error = result.errorOrNull()
        assertTrue(error is ApiError.Client)
        assertEquals(404, (error as ApiError.Client).code)
    }

    @Test
    fun `test server error with retry`() = runTest {
        // 模拟服务器错误，然后成功
        mockWebServer.enqueue(MockResponse().setResponseCode(502))
        mockWebServer.enqueue(MockResponse().setResponseCode(502))
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(200)
                .setBody("Success after retry"),
        )

        val result = client.get(mockWebServer.url("/test").toString())

        assertTrue(result.isSuccess)
        assertEquals("Success after retry", result.getOrNull())

        // 验证重试了3次（原始请求 + 2次重试）
        assertEquals(3, mockWebServer.requestCount)
    }

    @Test
    fun `test network offline error`() = runTest {
        // 模拟网络离线
        every { networkMonitor.networkState } returns MutableStateFlow(NetworkState.Unavailable)
        every { networkMonitor.isOnline } returns false

        val result = client.get(mockWebServer.url("/test").toString())

        assertTrue(result.isError)
        val error = result.errorOrNull()
        assertTrue(error is ApiError.Offline)
    }

    @Test
    fun `test PUT request`() = runTest {
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(200)
                .setBody("Updated"),
        )

        val result = client.put(
            mockWebServer.url("/test").toString(),
            """{"update": "data"}""",
        )

        assertTrue(result.isSuccess)
        assertEquals("Updated", result.getOrNull())
    }

    @Test
    fun `test DELETE request`() = runTest {
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(204)
                .setBody(""),
        )

        val result = client.delete(mockWebServer.url("/test").toString())

        assertTrue(result.isSuccess)
        assertEquals("", result.getOrNull())
    }

    @Test
    fun `test request with custom headers`() = runTest {
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(200)
                .setBody("Success"),
        )

        val headers = mapOf(
            "Custom-Header" to "custom-value",
            "Another-Header" to "another-value",
        )

        val result = client.get(mockWebServer.url("/test").toString(), headers)

        assertTrue(result.isSuccess)

        // 验证请求头
        val request = mockWebServer.takeRequest()
        assertEquals("custom-value", request.getHeader("Custom-Header"))
        assertEquals("another-value", request.getHeader("Another-Header"))
        assertEquals("Bearer test-api-key", request.getHeader("Authorization"))
    }

    @Test
    fun `test legacy methods compatibility`() = runTest {
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(200)
                .setBody("Legacy success"),
        )

        val result = client.getLegacy(mockWebServer.url("/test").toString())

        assertEquals("Legacy success", result)
    }

    @Test
    fun `test legacy methods with error`() = runTest {
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(500)
                .setBody("Server Error"),
        )

        try {
            client.getLegacy(mockWebServer.url("/test").toString())
            assertTrue(false, "Should have thrown exception")
        } catch (e: RuntimeException) {
            assertTrue(e.message!!.contains("服务器错误"))
        }
    }

    @Test
    fun `test ApiResult mapping`() = runTest {
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(200)
                .setBody("Original"),
        )

        val result = client.get(mockWebServer.url("/test").toString())

        val mapped = result.map { it.uppercase() }
        assertTrue(mapped.isSuccess)
        assertEquals("ORIGINAL", mapped.getOrNull())
    }

    @Test
    fun `test ApiResult fold`() = runTest {
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(404)
                .setBody("Not Found"),
        )

        val result = client.get(mockWebServer.url("/test").toString())

        val folded = result.fold(
            onSuccess = { "Success: $it" },
            onError = { "Error: ${it.getDescription()}" },
        )

        assertTrue(folded.startsWith("Error:"))
        assertTrue(folded.contains("404"))
    }
}
