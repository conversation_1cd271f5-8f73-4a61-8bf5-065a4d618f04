package com.example.gymbro.data.workout.stats.entity

import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import com.example.gymbro.domain.workout.model.stats.DailyStats
import kotlinx.datetime.LocalDate

/**
 * 日级统计数据实体 - Room数据库实体
 * 
 * 基于Clean Architecture设计，用于存储每日训练统计数据。
 * 该实体对应daily_stats表，提供高效的统计数据查询和聚合功能。
 * 
 * 设计特点：
 * - 复合主键：userId + date 确保每用户每日唯一记录
 * - 索引优化：支持高效的时间范围查询
 * - 数据完整性：包含所有必要的统计指标
 * - 扩展性：预留未来功能字段
 * 
 * 表结构：
 * - 主键：userId + date
 * - 索引：userId, date, createdAt, updatedAt
 * - 外键：planId (可选，关联训练计划)
 */
@Entity(
    tableName = "daily_stats",
    primaryKeys = ["userId", "date"],
    indices = [
        Index(value = ["userId"]),
        Index(value = ["date"]),
        Index(value = ["userId", "date"]),
        Index(value = ["planId"]),
        Index(value = ["createdAt"]),
        Index(value = ["updatedAt"])
    ]
)
data class DailyStatsEntity(
    // 复合主键
    val userId: String,
    val date: String, // LocalDate序列化为ISO字符串 (YYYY-MM-DD)
    
    // 基础统计数据
    val completedSessions: Int = 0,
    val completedExercises: Int = 0,
    val completedSets: Int = 0,
    val totalReps: Int = 0,
    val totalWeight: Double = 0.0,
    
    // 训练质量指标
    val avgRpe: Float? = null,
    val sessionDurationSec: Int = 0,
    
    // 关联信息
    val planId: String? = null,
    val dayOfWeek: Int = 1, // 1-7 (Monday-Sunday)
    
    // 扩展统计指标
    val caloriesBurned: Int? = null,
    val averageHeartRate: Int? = null,
    
    // 时间戳
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) {
    
    /**
     * 转换为Domain模型
     */
    fun toDomainModel(): DailyStats {
        return DailyStats(
            userId = userId,
            date = LocalDate.parse(date),
            completedSessions = completedSessions,
            completedExercises = completedExercises,
            completedSets = completedSets,
            totalReps = totalReps,
            totalWeight = totalWeight,
            avgRpe = avgRpe,
            sessionDurationSec = sessionDurationSec,
            planId = planId,
            dayOfWeek = dayOfWeek,
            caloriesBurned = caloriesBurned,
            averageHeartRate = averageHeartRate,
            createdAt = createdAt,
            updatedAt = updatedAt
        )
    }
    
    companion object {
        /**
         * 从Domain模型创建Entity
         */
        fun fromDomainModel(domainModel: DailyStats): DailyStatsEntity {
            return DailyStatsEntity(
                userId = domainModel.userId,
                date = domainModel.date.toString(),
                completedSessions = domainModel.completedSessions,
                completedExercises = domainModel.completedExercises,
                completedSets = domainModel.completedSets,
                totalReps = domainModel.totalReps,
                totalWeight = domainModel.totalWeight,
                avgRpe = domainModel.avgRpe,
                sessionDurationSec = domainModel.sessionDurationSec,
                planId = domainModel.planId,
                dayOfWeek = domainModel.dayOfWeek,
                caloriesBurned = domainModel.caloriesBurned,
                averageHeartRate = domainModel.averageHeartRate,
                createdAt = domainModel.createdAt,
                updatedAt = domainModel.updatedAt
            )
        }
        
        /**
         * 创建空的统计实体
         */
        fun empty(userId: String, date: LocalDate): DailyStatsEntity {
            return DailyStatsEntity(
                userId = userId,
                date = date.toString(),
                dayOfWeek = date.dayOfWeek.value
            )
        }
    }
}

/**
 * 统计聚合结果实体
 * 用于复杂的SQL聚合查询结果
 */
data class StatsAggregationResult(
    val totalSessions: Int,
    val totalExercises: Int,
    val totalSets: Int,
    val totalReps: Int,
    val totalWeight: Double,
    val totalDuration: Int,
    val avgRpe: Float?,
    val firstDate: String,
    val lastDate: String
)

/**
 * 时间范围统计结果
 * 用于周/月/年统计查询
 */
data class TimeRangeStatsResult(
    val userId: String,
    val startDate: String,
    val endDate: String,
    val totalSessions: Int,
    val totalWeight: Double,
    val totalDuration: Int,
    val avgSessionsPerDay: Float,
    val avgWeightPerSession: Float,
    val avgDurationPerSession: Float
)
