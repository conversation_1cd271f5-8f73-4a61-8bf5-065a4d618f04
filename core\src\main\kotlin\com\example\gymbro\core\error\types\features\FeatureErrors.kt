package com.example.gymbro.core.error.types.features

import com.example.gymbro.core.error.types.ErrorCategory
import com.example.gymbro.core.error.types.ErrorSeverity
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.StandardKeys
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.ui.text.UiText

/**
 * 应用功能相关错误类型集合
 *
 * 包含AI教练、营养管理、社交功能、用户管理和锻炼管理相关的错误处理
 * 从 DomainErrors.kt 迁移而来，专门处理功能层错误
 */
object FeatureErrors {
    /**
     * AI教练错误类
     */
    object CoachError {
        /**
         * 创建AI教练限制超出错误
         */
        fun limitExceeded(
            operationName: String = "CoachError.limitExceeded",
            message: UiText = UiText.DynamicString("AI教练使用限制已达到"),
            limitType: String = "session",
            currentCount: Int? = null,
            maxAllowed: Int? = null,
            timeWindow: String? = null,
            quotaType: String? = null,
            resetTime: String? = null,
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError {
            val metadataWithQuota =
                metadataMap.toMutableMap().apply {
                    put(StandardKeys.ERROR_SUBTYPE.key, limitType)
                    currentCount?.let { put(StandardKeys.QUOTA_USED.key, it) }
                    maxAllowed?.let { put(StandardKeys.QUOTA_LIMIT.key, it) }
                    timeWindow?.let { put("time_window", it) }
                    quotaType?.let { put("quota_type", it) }
                    resetTime?.let { put("reset_time", it) }
                }
            return ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.AI.QuotaExceeded,
                category = ErrorCategory.SYSTEM,
                uiMessage = message,
                severity = ErrorSeverity.WARNING,
                recoverable = true,
                cause = cause,
                metadataMap = metadataWithQuota,
            )
        }

        /**
         * 创建AI教练输入无效错误
         */
        fun invalidInput(
            operationName: String = "CoachError.invalidInput",
            message: UiText = UiText.DynamicString("输入数据无效"),
            inputType: String = "unknown",
            value: String? = null,
            supportedValues: List<String>? = null,
            missingFields: List<String>? = null,
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError {
            val metadataWithInput =
                metadataMap.toMutableMap().apply {
                    put(StandardKeys.ERROR_SUBTYPE.key, inputType)
                    value?.let { put("input_value", it) }
                    supportedValues?.let { put("supported_values", it.joinToString(",")) }
                    missingFields?.let { put("missing_fields", it.joinToString(",")) }
                }
            return ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.Validation.Format,
                category = ErrorCategory.SYSTEM,
                uiMessage = message,
                severity = ErrorSeverity.WARNING,
                recoverable = true,
                cause = cause,
                metadataMap = metadataWithInput,
            )
        }

        /**
         * 创建AI教练处理错误
         */
        fun processingFailed(
            operationName: String = "CoachError.processingFailed",
            message: UiText = UiText.DynamicString("AI教练处理失败"),
            processType: String = "unknown",
            reason: String = "unknown",
            analysisType: String? = null,
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError {
            val metadataWithProcess =
                metadataMap.toMutableMap().apply {
                    put(StandardKeys.ERROR_SUBTYPE.key, processType)
                    put("failure_reason", reason)
                    analysisType?.let { put("analysis_type", it) }
                }
            return ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.AI.ServiceUnavailable,
                category = ErrorCategory.SYSTEM,
                uiMessage = message,
                severity = ErrorSeverity.ERROR,
                recoverable = false,
                cause = cause,
                metadataMap = metadataWithProcess,
            )
        }

        /**
         * 创建AI教练配置错误
         */
        fun configurationError(
            operationName: String = "CoachError.configurationError",
            message: UiText = UiText.DynamicString("AI教练配置错误"),
            configType: String = "unknown",
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError {
            val metadataWithConfig =
                metadataMap.toMutableMap().apply {
                    put(StandardKeys.ERROR_SUBTYPE.key, configType)
                }
            return ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.AI.ConfigurationInvalid,
                category = ErrorCategory.SYSTEM,
                uiMessage = message,
                severity = ErrorSeverity.ERROR,
                recoverable = false,
                cause = cause,
                metadataMap = metadataWithConfig,
            )
        }
    }

    /**
     * 用户管理错误类
     */
    object UserError {
        /**
         * 创建用户资料无效错误
         */
        fun invalidProfile(
            operationName: String = "UserError.invalidProfile",
            message: UiText = UiText.DynamicString("用户资料无效"),
            fieldName: String = "unknown",
            fieldValue: String? = null,
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError {
            val metadataWithProfile =
                metadataMap.toMutableMap().apply {
                    put("invalid_field", fieldName)
                    fieldValue?.let { put("field_value", it) }
                }
            return ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.Validation.Format,
                category = ErrorCategory.VALIDATION,
                uiMessage = message,
                severity = ErrorSeverity.WARNING,
                recoverable = true,
                cause = cause,
                metadataMap = metadataWithProfile,
            )
        }

        /**
         * 创建用户权限错误
         */
        fun permissionDenied(
            operationName: String = "UserError.permissionDenied",
            message: UiText = UiText.DynamicString("用户权限不足"),
            requiredPermission: String = "unknown",
            userRole: String = "unknown",
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError {
            val metadataWithPermission =
                metadataMap.toMutableMap().apply {
                    put("required_permission", requiredPermission)
                    put("user_role", userRole)
                }
            return ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.Auth.Unauthorized,
                category = ErrorCategory.AUTH,
                uiMessage = message,
                severity = ErrorSeverity.ERROR,
                recoverable = false,
                cause = cause,
                metadataMap = metadataWithPermission,
            )
        }

        /**
         * 创建用户不存在错误
         */
        fun notFound(
            operationName: String = "UserError.notFound",
            message: UiText = UiText.DynamicString("用户不存在"),
            userId: String = "unknown",
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError =
            BusinessErrors.BusinessError.notFound(
                operationName = operationName,
                message = message,
                entityType = "user",
                entityId = userId,
                cause = cause,
                metadataMap = metadataMap,
            )
    }

    /**
     * 锻炼管理错误类
     */
    object WorkoutError {
        /**
         * 创建锻炼记录无效错误
         */
        fun invalidRecord(
            operationName: String = "WorkoutError.invalidRecord",
            message: UiText = UiText.DynamicString("锻炼记录无效"),
            recordType: String = "unknown",
            invalidField: String = "unknown",
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError {
            val metadataWithRecord =
                metadataMap.toMutableMap().apply {
                    put("record_type", recordType)
                    put("invalid_field", invalidField)
                }
            return ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.Validation.Format,
                category = ErrorCategory.VALIDATION,
                uiMessage = message,
                severity = ErrorSeverity.WARNING,
                recoverable = true,
                cause = cause,
                metadataMap = metadataWithRecord,
            )
        }

        /**
         * 创建锻炼同步错误
         */
        fun syncFailed(
            operationName: String = "WorkoutError.syncFailed",
            message: UiText = UiText.DynamicString("锻炼数据同步失败"),
            syncType: String = "unknown",
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError =
            DataErrors.SyncError.failed(
                operationName = operationName,
                message = message,
                syncType = syncType,
                cause = cause,
                metadataMap = metadataMap,
            )
    }
}
