package com.example.gymbro.core.ai.prompt.config

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.serialization.Serializable
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 增强的Prompt配置管理器
 *
 * 支持：
 * - 系统提示词模式切换（4种模式）
 * - A/B测试能力
 * - SharedPreferences持久化
 * - 远程配置预留接口
 *
 * @since 618重构
 */
@Singleton
class PromptConfigManager @Inject constructor(
    @ApplicationContext private val context: Context,
) {

    companion object {
        private const val PREFS_NAME = "prompt_config_prefs"
        private const val KEY_CURRENT_MODE = "current_prompt_mode"
        private const val KEY_AB_TEST_GROUP = "ab_test_group"
        private const val KEY_CUSTOM_CONFIGS = "custom_configs"

        // 4种系统提示词模式
        const val MODE_STANDARD = "standard"
        const val MODE_LAYERED = "layered"
        const val MODE_BLANK = "blank"
        const val MODE_PIPELINE = "pipeline"
    }

    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val json = Json {
        ignoreUnknownKeys = true
        prettyPrint = true
    }

    // 当前激活的模式
    private val _currentMode = MutableStateFlow(loadCurrentMode())
    val currentMode: StateFlow<String> = _currentMode.asStateFlow()

    // 当前配置
    private val _currentConfig = MutableStateFlow(createDefaultConfig())
    val currentConfig: StateFlow<PromptBuilderConfig> = _currentConfig.asStateFlow()

    // 可用配置
    private val availableConfigs = mutableMapOf<String, PromptBuilderConfig>()

    // 系统提示词模式配置
    private val promptModes = mutableMapOf<String, PromptModeConfig>()

    init {
        initializePresetConfigs()
        initializePromptModes()
        loadCustomConfigs()
        Timber.d("PromptConfigManager: 初始化完成，当前模式=$_currentMode")
    }

    /**
     * 切换系统提示词模式
     *
     * @param mode 模式名称（standard/layered/blank/pipeline）
     * @return 是否切换成功
     *
     * @since 618重构
     */
    fun switchMode(mode: String): Boolean {
        if (mode !in promptModes) {
            Timber.w("PromptConfigManager: 无效的模式 $mode")
            return false
        }

        _currentMode.value = mode
        prefs.edit { putString(KEY_CURRENT_MODE, mode) }

        Timber.i("PromptConfigManager: 切换到模式 $mode")
        return true
    }

    /**
     * 获取当前系统提示词
     *
     * @since 618重构
     */
    fun getCurrentSystemPrompt(): String {
        val modeConfig = promptModes[_currentMode.value]
            ?: promptModes[MODE_STANDARD]!!
        return modeConfig.systemPrompt
    }

    /**
     * 获取实验性提示词（A/B测试）
     *
     * @param userId 用户ID
     * @return 根据用户分组返回的系统提示词
     *
     * @since 618重构
     */
    fun getExperimentalPrompt(userId: String): String {
        // 获取或分配A/B测试组
        val group = getOrAssignABTestGroup(userId)

        // 根据分组返回不同的提示词
        val mode = when (group) {
            0 -> MODE_STANDARD
            1 -> MODE_LAYERED
            2 -> MODE_PIPELINE
            else -> MODE_BLANK
        }

        val modeConfig = promptModes[mode]!!
        Timber.d("PromptConfigManager: 用户${userId}分配到组$group，使用模式$mode")

        return modeConfig.systemPrompt
    }

    /**
     * 获取所有可用的模式
     *
     * @since 618重构
     */
    fun getAvailableModes(): List<PromptModeConfig> {
        return promptModes.values.toList()
    }

    /**
     * 注册自定义配置
     *
     * @since 618重构
     */
    fun registerCustomConfig(key: String, config: PromptBuilderConfig) {
        availableConfigs[key] = config
        saveCustomConfigs()
        Timber.d("PromptConfigManager: 注册自定义配置 $key")
    }

    /**
     * 注册自定义提示词模式
     *
     * @since 618重构
     */
    fun registerCustomMode(modeConfig: PromptModeConfig) {
        promptModes[modeConfig.id] = modeConfig
        Timber.d("PromptConfigManager: 注册自定义模式 ${modeConfig.id}")
    }

    /**
     * 远程配置更新（预留接口）
     *
     * @since 618重构
     */
    suspend fun fetchRemoteConfigs() {
        // TODO: 实现远程配置拉取
        Timber.d("PromptConfigManager: 远程配置更新（待实现）")
    }

    /**
     * 获取配置版本
     *
     * @since 618重构
     */
    fun getConfigVersion(mode: String): String {
        return promptModes[mode]?.version ?: "1.0.0"
    }

    /* =============== 原有方法保留（向后兼容） =============== */

    fun getCurrentConfig(): PromptBuilderConfig = _currentConfig.value

    fun switchToConfig(configKey: String): Boolean {
        val config = availableConfigs[configKey]
        return if (config != null) {
            _currentConfig.value = config
            Timber.i("PromptConfigManager: 切换到配置 $configKey")
            true
        } else {
            Timber.w("PromptConfigManager: 配置未找到 $configKey")
            false
        }
    }

    fun getAvailableConfigs(): Map<String, PromptBuilderConfig> = availableConfigs.toMap()

    fun getConfigForScenario(scenario: PromptScenario): PromptBuilderConfig {
        return when (scenario) {
            PromptScenario.FAST_RESPONSE -> availableConfigs["fast"] ?: createFastConfig()
            PromptScenario.HIGH_QUALITY -> availableConfigs["quality"] ?: createHighQualityConfig()
            PromptScenario.COST_OPTIMIZED -> availableConfigs["cost"] ?: createCostOptimizedConfig()
            PromptScenario.BALANCED -> availableConfigs["balanced"] ?: createBalancedConfig()
            PromptScenario.DEFAULT -> availableConfigs["default"] ?: createDefaultConfig()
        }
    }

    /* =============== 私有辅助方法 =============== */

    private fun loadCurrentMode(): String {
        return prefs.getString(KEY_CURRENT_MODE, MODE_STANDARD) ?: MODE_STANDARD
    }

    private fun getOrAssignABTestGroup(userId: String): Int {
        val key = "$KEY_AB_TEST_GROUP:$userId"
        return if (prefs.contains(key)) {
            prefs.getInt(key, 0)
        } else {
            // 简单的哈希分组算法
            val group = userId.hashCode() % 4
            prefs.edit { putInt(key, group) }
            group
        }
    }

    private fun initializePromptModes() {
        // 从assets加载4种模式的配置
        promptModes[MODE_STANDARD] = PromptModeConfig(
            id = MODE_STANDARD,
            displayName = "标准模式",
            description = "官方标准配置",
            version = "1.0.0",
            systemPrompt = loadPromptFromAssets(MODE_STANDARD),
        )

        promptModes[MODE_LAYERED] = PromptModeConfig(
            id = MODE_LAYERED,
            displayName = "分层模式",
            description = "结构化分层提示",
            version = "1.0.0",
            systemPrompt = loadPromptFromAssets(MODE_LAYERED),
        )

        promptModes[MODE_BLANK] = PromptModeConfig(
            id = MODE_BLANK,
            displayName = "空白模式",
            description = "最简化提示",
            version = "1.0.0",
            systemPrompt = loadPromptFromAssets(MODE_BLANK),
        )

        promptModes[MODE_PIPELINE] = PromptModeConfig(
            id = MODE_PIPELINE,
            displayName = "流水线模式",
            description = "5步思考流程",
            version = "1.0.0",
            systemPrompt = loadPromptFromAssets(MODE_PIPELINE),
        )
    }

    private fun loadPromptFromAssets(mode: String): String {
        return try {
            val inputStream = context.assets.open("prompts/$mode.json")
            val jsonString = inputStream.bufferedReader().use { it.readText() }
            val config = json.decodeFromString<PromptJsonConfig>(jsonString)
            config.systemPrompt
        } catch (e: Exception) {
            Timber.e(e, "PromptConfigManager: 加载提示词失败 $mode")
            "你是专业健身AI教练GymBro。" // 默认提示词
        }
    }

    private fun saveCustomConfigs() {
        try {
            val configsJson = json.encodeToString(availableConfigs)
            prefs.edit { putString(KEY_CUSTOM_CONFIGS, configsJson) }
        } catch (e: Exception) {
            Timber.e(e, "PromptConfigManager: 保存自定义配置失败")
        }
    }

    private fun loadCustomConfigs() {
        try {
            val configsJson = prefs.getString(KEY_CUSTOM_CONFIGS, null) ?: return
            // TODO: 反序列化自定义配置
        } catch (e: Exception) {
            Timber.e(e, "PromptConfigManager: 加载自定义配置失败")
        }
    }

    private fun initializePresetConfigs() {
        availableConfigs["default"] = createDefaultConfig()
        availableConfigs["fast"] = createFastConfig()
        availableConfigs["quality"] = createHighQualityConfig()
        availableConfigs["cost"] = createCostOptimizedConfig()
        availableConfigs["balanced"] = createBalancedConfig()
    }

    private fun createDefaultConfig(): PromptBuilderConfig {
        return PromptBuilderConfig(
            version = "1.0",
            maxTokens = 3000,
            contextTemplatesK = 5,
            recentHistoryN = 10,
            truncationStrategy = TruncationStrategy.PRIORITY_BASED,
        )
    }

    private fun createFastConfig() = PromptBuilderConfig(
        version = "1.0-fast",
        maxTokens = 2000,
        contextTemplatesK = 3,
        recentHistoryN = 5,
        truncationStrategy = TruncationStrategy.RECENT_FIRST,
    )

    private fun createHighQualityConfig() = PromptBuilderConfig(
        version = "1.0-quality",
        maxTokens = 4000,
        contextTemplatesK = 8,
        recentHistoryN = 15,
        truncationStrategy = TruncationStrategy.PRIORITY_BASED,
    )

    private fun createCostOptimizedConfig() = PromptBuilderConfig(
        version = "1.0-cost",
        maxTokens = 1500,
        contextTemplatesK = 2,
        recentHistoryN = 3,
        truncationStrategy = TruncationStrategy.RECENT_FIRST,
    )

    private fun createBalancedConfig() = PromptBuilderConfig(
        version = "1.0-balanced",
        maxTokens = 2500,
        contextTemplatesK = 5,
        recentHistoryN = 8,
        truncationStrategy = TruncationStrategy.BALANCED,
    )
}

/**
 * 提示词模式配置
 *
 * @since 618重构
 */
@Serializable
data class PromptModeConfig(
    val id: String,
    val displayName: String,
    val description: String,
    val version: String,
    val systemPrompt: String,
    val outputFormat: String? = null,
    val constraints: List<String> = emptyList(),
    val capabilities: List<String> = emptyList(),
    val enableThinking: Boolean = false,
)

/**
 * JSON配置格式
 *
 * @since 618重构
 */
@Serializable
data class PromptJsonConfig(
    val id: String,
    val displayName: String,
    val description: String,
    val version: String,
    val systemPrompt: String,
    val outputFormat: String? = null,
    val constraints: List<String> = emptyList(),
    val capabilities: List<String> = emptyList(),
    val role: String? = null,
    val enableThinking: Boolean = false,
    val thinkingFormat: String? = null,
)
