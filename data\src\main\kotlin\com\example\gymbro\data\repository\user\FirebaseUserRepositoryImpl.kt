package com.example.gymbro.data.repository.user

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.remote.firebase.auth.AuthDataSource
import com.example.gymbro.domain.profile.repository.user.FirebaseUserRepository
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation for retrieving Firebase user data.
 */
@Singleton
class FirebaseUserRepositoryImpl
@Inject
constructor(
    private val authDataSource: AuthDataSource,
) : FirebaseUserRepository {
    /**
     * 检查当前用户是否已登录到Firebase
     * @return 包含登录状态的结果
     */
    override suspend fun isUserLoggedIn(): ModernResult<Boolean> =
        try {
            val isLoggedIn = authDataSource.isUserLoggedInSynchronously()
            ModernResult.Success(isLoggedIn)
        } catch (e: Exception) {
            Timber.e(e, "检查用户登录状态失败")
            ModernResult.Error(
                DataErrors.DataError.access(
                    operationName = "FirebaseUserRepo.isUserLoggedIn",
                    message = UiText.DynamicString("检查用户登录状态失败: ${e.message ?: "未知错误"}"),
                    cause = e,
                ),
            )
        }

    /**
     * 获取当前用户的Firebase UID
     * @return 包含UID的结果
     */
    override suspend fun getCurrentUserUid(): ModernResult<String?> =
        try {
            val uid = authDataSource.getCurrentUserId()
            ModernResult.Success(uid)
        } catch (e: Exception) {
            Timber.e(e, "获取用户UID失败")
            ModernResult.Error(
                DataErrors.DataError.access(
                    operationName = "FirebaseUserRepo.getCurrentUserUid",
                    message = UiText.DynamicString("获取用户ID失败: ${e.message ?: "未知错误"}"),
                    cause = e,
                ),
            )
        }
}
