是的——**WebSocket 通道和所有 REST/HTTP-2 请求都统一走 OkHttp 5.x 的同一底层 I/O 栈**。
下面把“用 OkHttp 同时承载双向 WS 流 + 普通 HTTP 调用”关键信息再压缩总结一下，方便你照表落地。

---

## 1. 连接分配一览

| 通道         | 作用                   | OkHttp API                              | 复用策略                                                      |
|------------|----------------------|-----------------------------------------|-----------------------------------------------------------|
| **LLM 流**  | token 流 / tool\_call | `OkHttpClient.newWebSocket()`           | **专用 client**：`wsClient`<br>仅 1 条长连，`maxIdle=1`           |
| **REST**   | Profile、订阅、配置…       | `Retrofit.Builder().client(okHttpRest)` | **共享池 client**：`restClient`<br>`maxIdle=8 / 5 min`、HTTP-2 |
| **CDN/静态** | 图片、JSON、模型包          | 同 `restClient` + `Cache(50 MB)`         | 走 OkHttp 缓存                                               |

---

## 2. 单例 OkHttp 客户端代码

```kotlin
// Ws 专用
val wsClient: OkHttpClient = OkHttpClient.Builder()
    .pingInterval(cfg.basePingSec.toLong(), TimeUnit.SECONDS)
    .callTimeout(30, TimeUnit.SECONDS)
    .connectionPool(ConnectionPool(1, cfg.basePingSec * 2L, TimeUnit.SECONDS))
    .build()

// REST 共用
val restClient: OkHttpClient = OkHttpClient.Builder()
    .connectTimeout(cfg.connectTimeoutSec.toLong(), TimeUnit.SECONDS)
    .readTimeout(cfg.readTimeoutSec.toLong(), TimeUnit.SECONDS)
    .addInterceptor(AuthInterceptor(cfg.apiKey))
    .addInterceptor(NetworkStatusInterceptor(netWatchdog))
    .addInterceptor(RetryInterceptor(cfg.retryCount))
    .connectionPool(ConnectionPool(8, 5, TimeUnit.MINUTES))
    .cache(Cache(cacheDir, 50L * 1024 * 1024))
    .build()
```

> \*pinSet 同一套证书钉扎即可。
> \*若你想完全共池，也可让 WS 和 REST 共用一个 OkHttpClient ——只要注意 `readTimeout=0` 会影响所有调用，这就是分离的理由。

---

## 3. WebSocket 工作流 (关键钩子)

```kotlin
wsClient.newWebSocket(request, object : WebSocketListener() {
    override fun onOpen(ws: WebSocket, r: Response) {
        state.tryEmit(OPEN)
        retryDelay = cfg.backoffStartMs
    }
    override fun onMessage(ws: WebSocket, text: String) {
        json.decodeFromString<WsFrame>(text)?.also { sender.trySend(it.toEvent()) }
    }
    override fun onFailure(ws: WebSocket, t: Throwable, r: Response?) {
        state.tryEmit(RECOVERING)
        scheduleReconnect()          // 指数退避
    }
    override fun onClosed(ws: WebSocket, code: Int, reason: String) {
        state.tryEmit(DISCONNECTED)
    }
})
```

* `scheduleReconnect()` 读 `cfg.backoffCapMs / maxRetry`, fallbackUrl 等。
* 每成功解析一帧立即 `tokenStore.put(sessionId, idx)`，实现断点续传。

---

## 4. 典型 DI 装配（示例）

```kotlin
@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {

    @Provides fun provideOkHttpWs(cfg: WsConfig): OkHttpClient = wsClient(cfg)
    @Provides fun provideOkHttpRest(cfg: RestConfig): OkHttpClient = restClient(cfg)

    @Provides fun provideLlmStream(
        ok: OkHttpClient,
        json: Json,
        cfg: WsConfig
    ): LlmStreamClient = LlmStreamClientImpl(ok, json, cfg)

    @Provides fun provideRetrofit(
        ok: OkHttpClient,
        json: Json,
        cfg: RestConfig
    ): Retrofit = Retrofit.Builder()
        .client(ok)
        .baseUrl(cfg.baseUrl)
        .addConverterFactory(json.asConverterFactory("application/json".toMediaType()))
        .build()
}
```

---

## 5. 弱网 / 断网自愈

1. `NetworkWatchdog` → 广播 `offline / online`
2. `NetworkStatusInterceptor` 在 offline 直接 `throw IOException("offline")` → `ApiResult.Offline`
3. `WsService` 在 `offline` 状态进入 `PAUSE`，收到 `online` + back-off 到期再 `connect()`
4. `WorkManager` 离线队列保障 REST 写操作在恢复后重放（方案 II）。

---

**结论**：
*是的，整套方案完全依赖 OkHttp——用 WebSocket API 撑 LLM 长流，用普通 `Call`/Retrofit 撑 HTTP/2。*
只要把你的 **Ping/Pong、指数退避、断点续传** 落到上述 WsService，REST 走拦截器链+重试，你就拥有「低延迟 + 可恢复」的双向高效网络栈。
