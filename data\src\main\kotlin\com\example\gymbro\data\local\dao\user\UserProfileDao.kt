package com.example.gymbro.data.local.dao.user

import androidx.room.*
import com.example.gymbro.data.local.entity.user.UserProfileEntity
import kotlinx.coroutines.flow.Flow

/**
 * 用户资料数据访问对象
 *
 * 提供对UserProfileEntity的完整CRUD操作，支持用户个人信息的持久化存储
 *
 * 设计原则：
 * - 使用Flow进行响应式数据观察
 * - 支持Upsert操作避免冲突
 * - 提供事务支持确保数据一致性
 */
@Dao
interface UserProfileDao {

    /**
     * 根据用户ID获取用户资料
     * @param userId 用户ID
     * @return 用户资料实体Flow，不存在则发射null
     */
    @Query("SELECT * FROM user_profiles WHERE userId = :userId")
    fun getUserProfile(userId: String): Flow<UserProfileEntity?>

    /**
     * 根据用户ID同步获取用户资料
     * @param userId 用户ID
     * @return 用户资料实体，不存在则返回null
     */
    @Query("SELECT * FROM user_profiles WHERE userId = :userId")
    suspend fun getUserProfileSync(userId: String): UserProfileEntity?

    /**
     * 插入或更新用户资料
     * 使用REPLACE策略，如果用户已存在则覆盖
     * @param profile 用户资料实体
     * @return 插入的行ID
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrUpdateUserProfile(profile: UserProfileEntity): Long

    /**
     * 更新用户资料
     * @param profile 用户资料实体
     * @return 受影响的行数
     */
    @Update
    suspend fun updateUserProfile(profile: UserProfileEntity): Int

    /**
     * 删除指定用户的资料
     * @param userId 用户ID
     * @return 删除的行数
     */
    @Query("DELETE FROM user_profiles WHERE userId = :userId")
    suspend fun deleteUserProfile(userId: String): Int

    /**
     * 获取所有用户资料（用于调试和管理）
     * @return 所有用户资料实体的Flow
     */
    @Query("SELECT * FROM user_profiles ORDER BY lastUpdated DESC")
    fun getAllUserProfiles(): Flow<List<UserProfileEntity>>

    /**
     * 检查用户资料是否存在
     * @param userId 用户ID
     * @return 是否存在
     */
    @Query("SELECT COUNT(*) > 0 FROM user_profiles WHERE userId = :userId")
    suspend fun userProfileExists(userId: String): Boolean

    /**
     * 更新用户最后活跃时间
     * @param userId 用户ID
     * @param lastUpdated 最后更新时间戳
     * @return 受影响的行数
     */
    @Query("UPDATE user_profiles SET lastUpdated = :lastUpdated WHERE userId = :userId")
    suspend fun updateLastActiveTime(userId: String, lastUpdated: Long): Int

    /**
     * 批量插入或更新用户资料（用于数据同步）
     * @param profiles 用户资料实体列表
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrUpdateUserProfiles(profiles: List<UserProfileEntity>)

    /**
     * 清空所有用户资料（用于登出或重置）
     * @return 删除的行数
     */
    @Query("DELETE FROM user_profiles")
    suspend fun clearAllUserProfiles(): Int

    // === RAG集成查询方法 ===

    /**
     * 获取用户Profile摘要（用于RAG）
     * @param userId 用户ID
     * @return Profile摘要文本
     */
    @Query("SELECT profileSummary FROM user_profiles WHERE userId = :userId")
    suspend fun getProfileSummary(userId: String): String?

    /**
     * 更新用户Profile向量数据
     * @param userId 用户ID
     * @param summary Profile摘要
     * @param vector 向量嵌入
     * @param vectorCreatedAt 向量创建时间
     * @return 受影响的行数
     */
    @Query(
        """
        UPDATE user_profiles
        SET profileSummary = :summary,
            vector = :vector,
            vectorCreatedAt = :vectorCreatedAt,
            lastUpdated = :vectorCreatedAt
        WHERE userId = :userId
    """,
    )
    suspend fun updateProfileVector(
        userId: String,
        summary: String,
        vector: ByteArray,
        vectorCreatedAt: Long,
    ): Int

    /**
     * 检查用户Profile是否有向量数据
     * @param userId 用户ID
     * @return 是否有向量数据
     */
    @Query("SELECT vector IS NOT NULL FROM user_profiles WHERE userId = :userId")
    suspend fun hasProfileVector(userId: String): Boolean?
}
