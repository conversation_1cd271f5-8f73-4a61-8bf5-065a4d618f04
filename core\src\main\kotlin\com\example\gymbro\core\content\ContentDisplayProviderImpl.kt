package com.example.gymbro.core.content

import com.example.gymbro.core.region.RegionProvider
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 内容展示提供者实现
 *
 * 基于RegionProvider提供统一的内容展示配置
 * 这是各模块获取展示内容的唯一入口，避免重复的地区判断逻辑
 *
 * 容错策略：在RegionProvider未初始化时提供合理的默认值（国际区域），
 * 确保应用能正常运行，符合静默后台处理的设计原则
 *
 * 配置外部化：
 * 当前实现使用硬编码配置以保证简单性和可靠性。
 * 未来可考虑将配置移至外部文件（如assets/config.json）或远程配置服务。
 *
 * 优化建议：
 * - 可将配置移至 assets/region_config.json
 * - 可集成 Firebase Remote Config 进行动态配置
 * - 可使用 buildConfigField 进行编译时配置
 */
@Singleton
class ContentDisplayProviderImpl @Inject constructor(
    private val regionProvider: RegionProvider,
) : ContentDisplayProvider {

    companion object {
        // 配置常量 - 便于将来外部化
        private object Config {
            // 中国区配置
            object CN {
                const val CURRENCY = "CNY"
                const val CURRENCY_SYMBOL = "¥"
                const val MONTHLY_PRICE = 25.0
                const val YEARLY_PRICE = 250.0
                const val DEFAULT_LANGUAGE = "zh"
                const val LOCALE = "zh_CN"
                val PAYMENT_METHODS = listOf("ALIPAY", "WECHAT")
                val AVAILABLE_LANGUAGES = listOf("zh", "en")
                val COACH_FEATURES = listOf("chat", "workout_plan", "nutrition_advice")
                const val COACH_WELCOME_KEY = "ai_coach_welcome_message_cn"
                const val REGION_NAME = "中国"
            }

            // 国际区配置
            object INTERNATIONAL {
                const val CURRENCY = "USD"
                const val CURRENCY_SYMBOL = "$"
                const val MONTHLY_PRICE = 9.99
                const val YEARLY_PRICE = 99.99
                const val DEFAULT_LANGUAGE = "en"
                const val LOCALE = "en_US"
                val PAYMENT_METHODS = listOf("GOOGLE_PAY", "PAYPAL")
                val AVAILABLE_LANGUAGES = listOf("en", "zh")
                val COACH_FEATURES = listOf("chat", "workout_plan", "nutrition_advice")
                const val COACH_WELCOME_KEY = "ai_coach_welcome_message_en"
                const val REGION_NAME = "International"
            }
        }
    }

    /**
     * 安全获取当前区域，带容错处理
     */
    private fun getSafeCurrentRegion(): RegionProvider.UserRegion {
        return try {
            regionProvider.getCurrentRegion() ?: RegionProvider.UserRegion.INTERNATIONAL
        } catch (e: Exception) {
            Timber.d("RegionProvider未初始化或出错，使用默认国际区域: ${e.message}")
            RegionProvider.UserRegion.INTERNATIONAL
        }
    }

    /**
     * 获取价格展示配置
     */
    override fun getPriceConfig(): PriceDisplayConfig {
        return when (getSafeCurrentRegion()) {
            RegionProvider.UserRegion.CN -> PriceDisplayConfig(
                currency = Config.CN.CURRENCY,
                currencySymbol = Config.CN.CURRENCY_SYMBOL,
                monthlyPrice = Config.CN.MONTHLY_PRICE,
                yearlyPrice = Config.CN.YEARLY_PRICE,
                regionCode = "CN",
            )
            RegionProvider.UserRegion.INTERNATIONAL -> PriceDisplayConfig(
                currency = Config.INTERNATIONAL.CURRENCY,
                currencySymbol = Config.INTERNATIONAL.CURRENCY_SYMBOL,
                monthlyPrice = Config.INTERNATIONAL.MONTHLY_PRICE,
                yearlyPrice = Config.INTERNATIONAL.YEARLY_PRICE,
                regionCode = "INTERNATIONAL",
            )
        }
    }

    /**
     * 获取支付方式配置
     * 返回简单的字符串列表，避免对domain模块的依赖
     */
    override fun getPaymentMethods(): List<String> {
        return when (getSafeCurrentRegion()) {
            RegionProvider.UserRegion.CN -> Config.CN.PAYMENT_METHODS
            RegionProvider.UserRegion.INTERNATIONAL -> Config.INTERNATIONAL.PAYMENT_METHODS
        }
    }

    /**
     * 获取语言展示配置
     */
    override fun getLanguageConfig(): LanguageDisplayConfig {
        return when (getSafeCurrentRegion()) {
            RegionProvider.UserRegion.CN -> LanguageDisplayConfig(
                defaultLanguage = Config.CN.DEFAULT_LANGUAGE,
                availableLanguages = Config.CN.AVAILABLE_LANGUAGES,
                locale = Config.CN.LOCALE,
            )
            RegionProvider.UserRegion.INTERNATIONAL -> LanguageDisplayConfig(
                defaultLanguage = Config.INTERNATIONAL.DEFAULT_LANGUAGE,
                availableLanguages = Config.INTERNATIONAL.AVAILABLE_LANGUAGES,
                locale = Config.INTERNATIONAL.LOCALE,
            )
        }
    }

    /**
     * 获取AI教练配置
     */
    override fun getCoachConfig(): CoachDisplayConfig {
        return when (getSafeCurrentRegion()) {
            RegionProvider.UserRegion.CN -> CoachDisplayConfig(
                defaultLanguage = Config.CN.DEFAULT_LANGUAGE,
                supportedFeatures = Config.CN.COACH_FEATURES,
                welcomeMessageKey = Config.CN.COACH_WELCOME_KEY,
            )
            RegionProvider.UserRegion.INTERNATIONAL -> CoachDisplayConfig(
                defaultLanguage = Config.INTERNATIONAL.DEFAULT_LANGUAGE,
                supportedFeatures = Config.INTERNATIONAL.COACH_FEATURES,
                welcomeMessageKey = Config.INTERNATIONAL.COACH_WELCOME_KEY,
            )
        }
    }

    /**
     * 获取应用通用展示配置
     */
    override fun getAppDisplayConfig(): AppDisplayConfig {
        return when (getSafeCurrentRegion()) {
            RegionProvider.UserRegion.CN -> AppDisplayConfig(
                regionCode = "CN",
                regionName = Config.CN.REGION_NAME,
                defaultCurrency = Config.CN.CURRENCY,
                defaultLanguage = Config.CN.DEFAULT_LANGUAGE,
                isChineseRegion = true,
            )
            RegionProvider.UserRegion.INTERNATIONAL -> AppDisplayConfig(
                regionCode = "INTERNATIONAL",
                regionName = Config.INTERNATIONAL.REGION_NAME,
                defaultCurrency = Config.INTERNATIONAL.CURRENCY,
                defaultLanguage = Config.INTERNATIONAL.DEFAULT_LANGUAGE,
                isChineseRegion = false,
            )
        }
    }
}
