package com.example.gymbro.data.memory.dao

import androidx.room.*
import com.example.gymbro.data.memory.entity.MemoryRecordEntity
import com.example.gymbro.data.memory.entity.MemoryStatsResult
import com.example.gymbro.data.memory.entity.MemoryTierCount
import kotlinx.coroutines.flow.Flow

/**
 * 记忆记录数据访问对象
 *
 * 基于Memory System设计，支持四层记忆金字塔的数据库操作
 * 参考MessageEmbeddingDao模式，提供完整的CRUD和搜索功能
 */
@Dao
interface MemoryRecordDao {
    // ==================== 基础 CRUD 操作 ====================

    /**
     * 插入记忆记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMemory(memory: MemoryRecordEntity): Long

    /**
     * 批量插入记忆记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMemories(memories: List<MemoryRecordEntity>): List<Long>

    /**
     * 更新记忆记录
     */
    @Update
    suspend fun updateMemory(memory: MemoryRecordEntity)

    /**
     * 根据ID获取记忆记录
     */
    @Query("SELECT * FROM memory_records WHERE id = :memoryId")
    suspend fun getMemoryById(memoryId: String): MemoryRecordEntity?

    /**
     * 删除记忆记录
     */
    @Query("DELETE FROM memory_records WHERE id = :memoryId")
    suspend fun deleteMemory(memoryId: String)

    // ==================== 分层查询操作 ====================

    /**
     * 根据用户ID和记忆层级获取记忆
     */
    @Query(
        "SELECT * FROM memory_records WHERE user_id = :userId AND tier = :tier ORDER BY created_at DESC LIMIT :limit",
    )
    suspend fun getMemoriesByTier(userId: String, tier: String, limit: Int): List<MemoryRecordEntity>

    /**
     * 根据用户ID和时间范围获取记忆
     */
    @Query(
        "SELECT * FROM memory_records WHERE user_id = :userId AND created_at BETWEEN :startTime AND :endTime ORDER BY created_at DESC",
    )
    suspend fun getMemoriesByTimeRange(
        userId: String,
        startTime: Long,
        endTime: Long,
    ): List<MemoryRecordEntity>

    /**
     * 根据用户ID和重要性获取记忆
     */
    @Query(
        "SELECT * FROM memory_records WHERE user_id = :userId AND importance >= :minImportance ORDER BY importance DESC, created_at DESC LIMIT :limit",
    )
    suspend fun getMemoriesByImportance(
        userId: String,
        minImportance: Int,
        limit: Int,
    ): List<MemoryRecordEntity>

    /**
     * 观察用户记忆变化
     */
    @Query(
        "SELECT * FROM memory_records WHERE user_id = :userId AND (:tier IS NULL OR tier = :tier) ORDER BY created_at DESC",
    )
    fun observeMemories(userId: String, tier: String? = null): Flow<List<MemoryRecordEntity>>

    // ==================== 向量搜索操作 ====================

    /**
     * 获取用于向量搜索的嵌入记忆
     */
    @Query(
        """
        SELECT * FROM memory_records
        WHERE user_id = :userId
        AND embedding_status = 'COMPLETED'
        AND embedding IS NOT NULL
        AND (:tier IS NULL OR tier = :tier)
        ORDER BY created_at DESC
        LIMIT :limit
    """,
    )
    suspend fun getEmbeddingsForSearch(
        userId: String,
        tier: String? = null,
        limit: Int = 1000,
    ): List<MemoryRecordEntity>

    // ==================== 清理和维护操作 ====================

    /**
     * 删除过期记忆
     */
    @Query("DELETE FROM memory_records WHERE expires_at IS NOT NULL AND expires_at < :currentTime")
    suspend fun deleteExpiredMemories(currentTime: Long): Int

    /**
     * 删除用户的特定层级记忆
     */
    @Query("DELETE FROM memory_records WHERE user_id = :userId AND tier = :tier")
    suspend fun deleteMemoriesByTier(userId: String, tier: String): Int

    /**
     * 删除低重要性且长期未使用的记忆
     */
    @Query(
        """
        DELETE FROM memory_records
        WHERE user_id = :userId
        AND importance = 1
        AND created_at < :cutoffTime
    """,
    )
    suspend fun deleteStaleMemories(userId: String, cutoffTime: Long): Int

    // ==================== 统计和监控 ====================

    /**
     * 获取用户记忆统计信息
     */
    @Query(
        """
        SELECT
            tier,
            COUNT(*) as count,
            AVG(importance) as avgImportance,
            SUM(content_length) as totalSize,
            MIN(created_at) as oldestTime,
            MAX(created_at) as newestTime
        FROM memory_records
        WHERE user_id = :userId
        GROUP BY tier
    """,
    )
    suspend fun getMemoryStatsByUser(userId: String): List<MemoryStatsResult>

    /**
     * 获取记忆总数统计
     */
    @Query("SELECT COUNT(*) FROM memory_records WHERE user_id = :userId")
    suspend fun getTotalMemoryCount(userId: String): Int

    /**
     * 获取各层级记忆数量
     */
    @Query("SELECT tier, COUNT(*) as count FROM memory_records WHERE user_id = :userId GROUP BY tier")
    suspend fun getMemoryCountsByTier(userId: String): List<MemoryTierCount>

    /**
     * 检查记忆是否存在
     */
    @Query("SELECT EXISTS(SELECT 1 FROM memory_records WHERE id = :memoryId)")
    suspend fun memoryExists(memoryId: String): Boolean

    /**
     * 获取用户最新记忆时间
     */
    @Query("SELECT MAX(created_at) FROM memory_records WHERE user_id = :userId")
    suspend fun getLastMemoryTime(userId: String): Long?

    // ==================== 向量状态管理 ====================

    /**
     * 更新嵌入状态
     */
    @Query(
        "UPDATE memory_records SET embedding_status = :status, updated_at = :updatedAt WHERE id = :memoryId",
    )
    suspend fun updateEmbeddingStatus(
        memoryId: String,
        status: String,
        updatedAt: Long = System.currentTimeMillis(),
    )

    /**
     * 获取待处理的记忆ID列表
     */
    @Query(
        """
        SELECT id FROM memory_records
        WHERE embedding_status IN ('PENDING', 'FAILED')
        AND tier IN ('DWM', 'GIM')
        ORDER BY created_at DESC
        LIMIT :limit
    """,
    )
    suspend fun getPendingEmbeddingIds(limit: Int = 100): List<String>

    /**
     * 批量更新向量生成时间
     */
    @Query(
        "UPDATE memory_records SET generation_time_ms = :timeMs, updated_at = :updatedAt WHERE id = :memoryId",
    )
    suspend fun updateGenerationTime(
        memoryId: String,
        timeMs: Long,
        updatedAt: Long = System.currentTimeMillis(),
    )
}
