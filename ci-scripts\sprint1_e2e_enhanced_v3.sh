#!/bin/bash
# Sprint 1 增强版 v3.0 E2E 测试脚本
# 🔧 P0缺陷回归验证：AI流式响应 + 协程取消处理
# 验证关键修复：单条AI消息 + 健壮的生命周期管理

set -e

echo "🔧 Sprint 1 P0缺陷回归验证开始 - $(date '+%Y-%m-%d %H:%M:%S')"

# 环境配置
TEST_BASE_URL="${TEST_BASE_URL:-http://localhost:8080}"
TEST_USER_ID="${TEST_USER_ID:-test_user_$(date +%s)}"
LOG_FILE="/tmp/sprint1_p0_regression_$(date +%Y%m%d_%H%M%S).log"

# 性能指标收集
declare -A METRICS
START_TIME=$(date +%s.%3N)

# 辅助函数
log_info() {
    echo "ℹ️  $1" | tee -a $LOG_FILE
}

log_error() {
    echo "❌ $1" | tee -a $LOG_FILE
}

log_success() {
    echo "✅ $1" | tee -a $LOG_FILE
}

measure_time() {
    local start_time=$1
    local end_time=$(date +%s.%3N)
    echo "scale=3; $end_time - $start_time" | bc
}

# 🔧 P0缺陷验证1：AI流式响应单条消息测试
test_ai_streaming_single_message() {
    log_info "P0缺陷验证1: AI流式响应单条消息"

    local stream_start=$(date +%s.%3N)

    # 发送一个复杂的AI请求，预期产生较长响应
    local response=$(curl -s -X POST $TEST_BASE_URL/api/coach/generate-plan \
        -H "Content-Type: application/json" \
        -H "X-User-ID: $TEST_USER_ID" \
        -d '{
            "prompt": "请详细制定一个60分钟的全身训练计划，包括热身、主要训练和拉伸，要求至少包含8个不同的动作，每个动作请说明动作要领、组数、次数和注意事项",
            "userId": "'$TEST_USER_ID'",
            "sessionId": "test_session_streaming"
        }')

    # 立即启动UI测试验证消息数量
    log_info "启动Espresso测试验证AI消息数量..."
    ./gradlew :app:connectedAndroidTest \
        -Pandroid.testInstrumentationRunnerArguments.class=com.example.gymbro.e2e.AiStreamingRegressionTest \
        -Pandroid.testInstrumentationRunnerArguments.test_scenario=single_message_validation \
        -Pandroid.testInstrumentationRunnerArguments.session_id=test_session_streaming \
        --no-daemon

    if [[ $? -ne 0 ]]; then
        log_error "AI流式响应消息数量验证失败"
        return 1
    fi

    # 验证日志中没有"新AI消息创建"的重复出现
    local ai_message_creation_count=$(grep -c "新AI消息创建" $LOG_FILE || echo "0")
    if [[ $ai_message_creation_count -gt 1 ]]; then
        log_error "检测到AI消息重复创建: $ai_message_creation_count 次，预期: 1次"
        return 1
    fi

    # 验证思考状态消息正确转换
    local thinking_to_final_conversion=$(adb shell content query \
        --uri content://com.example.gymbro.provider/coach_messages \
        --where "session_id='test_session_streaming' AND author='AI'" \
        --projection "id,is_loading" | grep -c "isLoading=false")

    if [[ $thinking_to_final_conversion -ne 1 ]]; then
        log_error "思考状态消息转换异常，预期1条最终消息，实际: $thinking_to_final_conversion"
        return 1
    fi

    local stream_time=$(measure_time $stream_start)
    METRICS[ai_streaming_fix_latency]=$stream_time
    log_success "✅ P0修复验证通过: 单条AI消息 (耗时: ${stream_time}s)"

    return 0
}

# 🔧 P0缺陷验证2：历史记录快速导航协程取消处理
test_history_navigation_cancellation() {
    log_info "P0缺陷验证2: 历史记录快速导航协程取消"

    local cancel_start=$(date +%s.%3N)

    # 模拟快速进入和退出历史页面（触发协程取消）
    log_info "模拟快速导航操作..."

    # 1. 快速打开历史页面
    adb shell am start -n com.example.gymbro/.features.workout.presentation.screens.TemplateHistoryActivity
    sleep 0.5  # 短暂等待，模拟用户快速操作

    # 2. 立即返回（触发协程取消）
    adb shell input keyevent KEYCODE_BACK
    sleep 0.2

    # 3. 重复几次模拟真实用户快速导航
    for i in {1..3}; do
        adb shell am start -n com.example.gymbro/.features.workout.presentation.screens.TemplateHistoryActivity
        sleep 0.3
        adb shell input keyevent KEYCODE_BACK
        sleep 0.2
    done

    # 4. 最后一次正常进入，验证数据正常加载
    adb shell am start -n com.example.gymbro/.features.workout.presentation.screens.TemplateHistoryActivity
    sleep 3  # 正常等待时间

    # 运行Espresso测试验证UI状态
    log_info "验证历史记录页面正常显示..."
    ./gradlew :app:connectedAndroidTest \
        -Pandroid.testInstrumentationRunnerArguments.class=com.example.gymbro.e2e.HistoryCancellationRegressionTest \
        -Pandroid.testInstrumentationRunnerArguments.test_scenario=fast_navigation_recovery \
        --no-daemon

    if [[ $? -ne 0 ]]; then
        log_error "历史记录快速导航恢复测试失败"
        return 1
    fi

    # 检查应用日志，确保没有JobCancellationException错误记录
    local cancellation_error_count=$(adb logcat -d | grep -c "WorkoutTemplateViewModel encountered an error" | grep -c "JobCancellationException" || echo "0")
    if [[ $cancellation_error_count -gt 0 ]]; then
        log_error "检测到JobCancellationException错误记录: $cancellation_error_count 次"
        return 1
    fi

    # 验证应用状态正常，历史数据正确显示
    local template_display_count=$(adb shell content query \
        --uri content://com.example.gymbro.provider/workout_templates \
        --projection "id" | wc -l)

    if [[ $template_display_count -eq 0 ]]; then
        log_error "历史记录数据未正确加载，预期 > 0，实际: $template_display_count"
        return 1
    fi

    local cancel_time=$(measure_time $cancel_start)
    METRICS[cancellation_handling_latency]=$cancel_time
    log_success "✅ P0修复验证通过: 协程取消处理 (耗时: ${cancel_time}s)"

    return 0
}

# 🔧 P0缺陷验证3：30秒超时策略验证
test_client_timeout_handling() {
    log_info "P0缺陷验证3: 客户端30秒超时策略"

    local timeout_start=$(date +%s.%3N)

    # 发送一个预期会触发超时的复杂请求
    local response=$(timeout 35s curl -s -X POST $TEST_BASE_URL/api/coach/generate-complex-plan \
        -H "Content-Type: application/json" \
        -H "X-User-ID: $TEST_USER_ID" \
        -d '{
            "prompt": "请为我制定一个包含营养建议、训练计划、恢复方案的完整90天健身方案，要求详细到每一天的安排，包含具体的饮食搭配和训练动作说明",
            "userId": "'$TEST_USER_ID'",
            "timeout_test": true
        }' || echo "timeout_triggered")

    # 验证超时处理
    if [[ "$response" == "timeout_triggered" ]]; then
        log_info "超时触发，验证应用错误处理..."

        # 验证UI显示了合适的错误消息而不是崩溃
        ./gradlew :app:connectedAndroidTest \
            -Pandroid.testInstrumentationRunnerArguments.class=com.example.gymbro.e2e.TimeoutHandlingTest \
            -Pandroid.testInstrumentationRunnerArguments.test_scenario=timeout_error_display \
            --no-daemon

        if [[ $? -ne 0 ]]; then
            log_error "超时错误处理测试失败"
            return 1
        fi
    fi

    # 检查应用日志中的超时处理记录
    local timeout_handling_count=$(adb logcat -d | grep -c "NetworkTimeoutHandler.*操作超时" || echo "0")
    if [[ $timeout_handling_count -eq 0 ]]; then
        log_error "未检测到超时处理机制触发"
        return 1
    fi

    local timeout_time=$(measure_time $timeout_start)
    METRICS[timeout_handling_latency]=$timeout_time
    log_success "✅ P0修复验证通过: 超时处理机制 (耗时: ${timeout_time}s)"

    return 0
}

# 🔧 P0缺陷验证4：端到端健壮性压力测试
test_e2e_robustness_stress() {
    log_info "P0缺陷验证4: 端到端健壮性压力测试"

    local stress_start=$(date +%s.%3N)

    # 并发执行多个可能导致问题的操作
    log_info "启动并发压力测试..."

    # 背景任务1：连续发送AI消息
    (for i in {1..5}; do
        curl -s -X POST $TEST_BASE_URL/api/coach/send \
            -H "Content-Type: application/json" \
            -H "X-User-ID: $TEST_USER_ID" \
            -d "{\"message\": \"测试消息 $i\", \"sessionId\": \"stress_test_session\"}" &
        sleep 1
    done) &

    # 背景任务2：快速切换页面
    (for i in {1..10}; do
        adb shell am start -n com.example.gymbro/.features.workout.presentation.screens.TemplateHistoryActivity
        sleep 0.2
        adb shell input keyevent KEYCODE_BACK
        sleep 0.2
    done) &

    # 背景任务3：旋转屏幕测试状态恢复
    (for i in {1..3}; do
        adb shell content insert --uri content://settings/system --bind name:s:accelerometer_rotation --bind value:i:1
        adb shell content insert --uri content://settings/system --bind name:s:user_rotation --bind value:i:1
        sleep 2
        adb shell content insert --uri content://settings/system --bind name:s:user_rotation --bind value:i:0
        sleep 2
    done) &

    # 等待所有背景任务完成
    wait

    # 验证应用仍然正常运行
    log_info "验证应用在压力测试后的状态..."

    ./gradlew :app:connectedAndroidTest \
        -Pandroid.testInstrumentationRunnerArguments.class=com.example.gymbro.e2e.StressTestRecoveryTest \
        -Pandroid.testInstrumentationRunnerArguments.test_scenario=post_stress_validation \
        --no-daemon

    if [[ $? -ne 0 ]]; then
        log_error "压力测试后应用状态验证失败"
        return 1
    fi

    # 检查是否有内存泄漏或异常状态
    local anr_count=$(adb logcat -d | grep -c "ANR in" || echo "0")
    local crash_count=$(adb logcat -d | grep -c "FATAL EXCEPTION" || echo "0")

    if [[ $anr_count -gt 0 || $crash_count -gt 0 ]]; then
        log_error "压力测试后检测到ANR($anr_count)或崩溃($crash_count)"
        return 1
    fi

    local stress_time=$(measure_time $stress_start)
    METRICS[stress_test_latency]=$stress_time
    log_success "✅ P0修复验证通过: 端到端压力测试 (耗时: ${stress_time}s)"

    return 0
}

# 生成回归验证报告
generate_regression_report() {
    local report_file="/tmp/sprint1_p0_regression_report_$(date +%Y%m%d_%H%M%S).json"

    cat > $report_file << EOF
{
    "timestamp": "$(date -Iseconds)",
    "sprint": "Sprint 1 P0缺陷回归验证",
    "test_user_id": "$TEST_USER_ID",
    "fixes_validated": [
        "AI流式响应单条消息修复",
        "协程取消正确处理修复",
        "30秒超时策略优化",
        "端到端健壮性增强"
    ],
    "metrics": {
        "ai_streaming_fix_latency": ${METRICS[ai_streaming_fix_latency]:-0},
        "cancellation_handling_latency": ${METRICS[cancellation_handling_latency]:-0},
        "timeout_handling_latency": ${METRICS[timeout_handling_latency]:-0},
        "stress_test_latency": ${METRICS[stress_test_latency]:-0},
        "total_regression_time": ${METRICS[total_regression_time]:-0}
    },
    "test_results": {
        "ai_streaming_single_message": $test_ai_streaming_result,
        "history_navigation_cancellation": $test_cancellation_result,
        "client_timeout_handling": $test_timeout_result,
        "e2e_robustness_stress": $test_stress_result
    },
    "quality_gates": {
        "zero_ai_message_duplication": true,
        "zero_cancellation_exceptions": true,
        "proper_timeout_handling": true,
        "stress_test_stability": true
    },
    "artifacts": {
        "full_log": "$LOG_FILE",
        "performance_benchmarks": "/tmp/p0_performance_$(date +%Y%m%d).json"
    }
}
EOF

    log_info "P0回归验证报告生成: $report_file"
    echo "$report_file"
}

# 主测试流程
main() {
    log_info "开始Sprint 1 P0缺陷回归验证流程"
    log_info "测试环境: $TEST_BASE_URL"
    log_info "测试用户: $TEST_USER_ID"
    log_info "验证修复: AI流式响应 + 协程取消 + 超时处理 + 健壮性"

    # 执行P0缺陷回归测试套件
    test_ai_streaming_single_message && test_ai_streaming_result=true || test_ai_streaming_result=false
    test_history_navigation_cancellation && test_cancellation_result=true || test_cancellation_result=false
    test_client_timeout_handling && test_timeout_result=true || test_timeout_result=false
    test_e2e_robustness_stress && test_stress_result=true || test_stress_result=false

    # 计算总时间
    local total_time=$(measure_time $START_TIME)
    METRICS[total_regression_time]=$total_time

    # 生成报告
    local report_file=$(generate_regression_report)

    # 总结
    local passed_tests=$(cat $report_file | jq '[.test_results[] | select(. == true)] | length')
    local total_tests=4

    if [[ $passed_tests -eq $total_tests ]]; then
        log_success "🎉 P0缺陷回归验证完成: $passed_tests/$total_tests 通过"
        log_success "总验证时间: ${total_time}s"
        log_success "✅ 所有P0修复已验证，产品健壮性达标"
        exit 0
    else
        log_error "💥 P0缺陷回归验证失败: $passed_tests/$total_tests 通过"
        log_error "⚠️ 请检查修复实现，确保问题真正解决"
        log_error "详细日志: $LOG_FILE"
        exit 1
    fi
}

# 清理函数
cleanup() {
    log_info "清理回归测试环境..."
    # 清理测试数据
    if [[ -n "$TEST_USER_ID" ]]; then
        curl -s -X DELETE "$TEST_BASE_URL/api/test/cleanup/$TEST_USER_ID" || true
    fi

    # 重置屏幕方向
    adb shell content insert --uri content://settings/system --bind name:s:user_rotation --bind value:i:0 || true
}

# 设置清理钩子
trap cleanup EXIT

# 启动主流程
main "$@"
