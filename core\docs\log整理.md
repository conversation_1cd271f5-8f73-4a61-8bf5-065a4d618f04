<file_map>
D:/GymBro/GymBro
├── core
│   └── src
│       └── main
│           └── kotlin
│               └── com
│                   └── example
│                       └── gymbro
│                           └── core
│                               ├── logging
│                               │   ├── GymBroTimberLogger.kt
│                               │   ├── Logger.kt
│                               │   ├── LoggingConfig.kt
│                               │   ├── LoggingController.kt
│                               │   ├── LoggingModule.kt
│                               │   ├── SelectiveDebugTree.kt
│                               │   ├── SensitiveDataFilter.kt
│                               │   ├── TimberLogger.kt
│                               │   ├── TimberManager.kt
│                               │   ├── TimberTrees.kt
│                               │   └── TokenFlowDebugger.kt
│                               └── util
│                                   └── viewmodel
│                                       └── ViewModelLogging.kt
├── core-network
│   └── src
│       └── main
│           └── kotlin
│               └── com
│                   └── example
│                       └── gymbro
│                           └── core
│                               └── network
│                                   └── rest
│                                       └── interceptors
│                                           └── SafeLoggingInterceptor.kt
├── data
│   └── src
│       └── main
│           └── kotlin
│               └── com
│                   └── example
│                       └── gymbro
│                           └── data
│                               └── local
│                                   └── datastore
│                                       └── LoginType.kt
├── designSystem
│   └── src
│       └── main
│           ├── kotlin
│           │   └── com
│           │       └── example
│           │           └── gymbro
│           │               └── designSystem
│           │                   ├── component
│           │                   │   └── NumberPickerDialog.kt
│           │                   └── components
│           │                       ├── animations
│           │                       │   └── GymBroLogo.kt
│           │                       ├── GymBroInputDialogs.kt
│           │                       ├── GymBroLoginButton.kt
│           │                       └── SocialLoginButton.kt
│           └── res
│               └── drawable
│                   └── ic_app_logo.xml
├── domain
│   └── src
│       └── main
│           └── kotlin
│               └── com
│                   └── example
│                       └── gymbro
│                           └── domain
│                               └── auth
│                                   ├── model
│                                   │   └── LoginType.kt
│                                   └── usecase
│                                       ├── phone
│                                       │   └── LoginWithPhoneUseCase.kt
│                                       ├── LoginAnonymouslyUseCase.kt
│                                       ├── LoginWithEmailUseCase.kt
│                                       ├── LoginWithGoogleUseCase.kt
│                                       └── LogoutUseCase.kt
├── features
│   ├── auth
│   │   └── src
│   │       └── main
│   │           ├── kotlin
│   │           │   └── com
│   │           │       └── example
│   │           │           └── gymbro
│   │           │               └── features
│   │           │                   └── auth
│   │           │                       └── ui
│   │           │                           ├── components
│   │           │                           │   ├── AnimatedLoginButton.kt
│   │           │                           │   └── EmailLoginForm.kt
│   │           │                           ├── login
│   │           │                           │   ├── components
│   │           │                           │   │   ├── AuthBackgroundLayer.kt
│   │           │                           │   │   ├── AuthButtonSection.kt
│   │           │                           │   │   └── AuthLogoSection.kt
│   │           │                           │   ├── AuthLoginConstants.kt
│   │           │                           │   └── AuthLoginScreen.kt
│   │           │                           └── phone
│   │           │                               └── PhoneLoginScreen.kt
│   │           └── res
│   │               └── drawable
│   │                   └── app_logo.xml
│   ├── coach
│   │   └── src
│   │       └── main
│   │           └── kotlin
│   │               └── com
│   │                   └── example
│   │                       └── gymbro
│   │                           └── features
│   │                               └── coach
│   │                                   ├── aicoach
│   │                                   │   └── internal
│   │                                   │       └── components
│   │                                   │           └── GymBroLogoBackground.kt
│   │                                   ├── history
│   │                                   │   └── internal
│   │                                   │       └── components
│   │                                   │           └── ConversationManagementDialog.kt
│   │                                   └── shared
│   │                                       └── utils
│   │                                           └── DetailedRecompositionLogger.kt
│   ├── profile
│   │   └── src
│   │       └── main
│   │           └── kotlin
│   │               └── com
│   │                   └── example
│   │                       └── gymbro
│   │                           └── features
│   │                               └── profile
│   │                                   └── internal
│   │                                       └── presentation
│   │                                           └── personal_info
│   │                                               └── PersonalInfoDialogs.kt
│   ├── thinkingbox
│   │   └── src
│   │       └── main
│   │           └── kotlin
│   │               └── com
│   │                   └── example
│   │                       └── gymbro
│   │                           └── features
│   │                               └── thinkingbox
│   │                                   ├── domain
│   │                                   │   └── logger
│   │                                   │       └── RawThinkingLogger.kt
│   │                                   ├── internal
│   │                                   │   └── logging
│   │                                   │       └── TBLog.kt
│   │                                   └── logging
│   │                                       └── ThinkingBoxLogTree.kt
│   └── workout
│       └── src
│           └── main
│               └── kotlin
│                   └── com
│                       └── example
│                           └── gymbro
│                               └── features
│                                   └── workout
│                                       ├── plan
│                                       │   └── internal
│                                       │       └── components
│                                       │           └── CalendarJsonDialog.kt
│                                       ├── session
│                                       │   └── internal
│                                       │       └── components
│                                       │           ├── CompletionDialog.kt
│                                       │           └── RestTimerDialog.kt
│                                       ├── shared
│                                       │   └── components
│                                       │       ├── AddExerciseDialog.kt
│                                       │       ├── ApplyPlanDialog.kt
│                                       │       ├── CalendarDialog.kt
│                                       │       └── WorkoutPickerDialog.kt
│                                       └── template
│                                           └── ui
│                                               └── components
│                                                   └── CrashRecoveryDialog.kt
└── gradle
└── build-logic
├── src
│   └── main
│       ├── kotlin
│       │   └── com
│       │       └── example
│       │           └── gymbro
│       │               └── buildlogic
│       │                   ├── utils
│       │                   │   ├── DependencyExtensions.kt
│       │                   │   └── VersionAccessor.kt
│       │                   ├── AndroidApplicationConventionPlugin.kt
│       │                   ├── AndroidLibraryConventionPlugin.kt
│       │                   ├── ComposeConventionPlugin.kt
│       │                   ├── CoreDependenciesConventionPlugin.kt
│       │                   ├── DataModuleConventionPlugin.kt
│       │                   ├── DetektConventionPlugin.kt
│       │                   ├── DomainModuleConventionPlugin.kt
│       │                   ├── FeatureLibraryConventionPlugin.kt
│       │                   ├── FirebaseConventionPlugin.kt
│       │                   ├── HiltConventionPlugin.kt
│       │                   ├── JacocoConventionPlugin.kt
│       │                   ├── KotlinJvmConventionPlugin.kt
│       │                   ├── KotlinSerializationConventionPlugin.kt
│       │                   ├── KtlintConventionPlugin.kt
│       │                   ├── OfficialTestingStandardPlugin.kt
│       │                   ├── PerformanceOptimizationPlugin.kt
│       │                   ├── QualityConventionPlugin.kt
│       │                   ├── RoomConventionPlugin.kt
│       │                   ├── SecurityConventionPlugin.kt
│       │                   └── TestingConventionPlugin.kt
│       └── resources
│           └── testSkeletons
│               ├── coach.kt.template
│               ├── core.kt.template
│               ├── data.kt.template
│               ├── design_system.kt.template
│               ├── di.kt.template
│               ├── domain.kt.template
│               ├── feature.kt.template
│               ├── subscription.kt.template
│               └── viewmodel.kt.template
├── build.gradle.kts
└── settings.gradle.kts

</file_map>

<file_contents>
File: D:/GymBro/GymBro/features/workout/src/main/kotlin/com/example/gymbro/features/workout/shared/components/CalendarDialog.kt
```kotlin
package com.example.gymbro.features.workout.shared.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ViewModule
import androidx.compose.material.icons.filled.ViewWeek
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.example.gymbro.designSystem.components.extensions.gymBroClickable
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.workoutColors
import com.example.gymbro.domain.workout.model.calendar.CalendarItem
import com.example.gymbro.domain.workout.model.calendar.CalendarItemType
import kotlinx.datetime.*

/**
 * 日历日期信息
 */
data class CalendarDayInfo(
    val date: LocalDate,
    val hasWorkout: Boolean = false,
    val isCompleted: Boolean = false,
    val planName: String? = null,
)

/**
 * 日历视图模式
 */
enum class CalendarDialogViewMode {
    MONTH, // 月视图
    WEEK, // 周视图
    COMPACT, // 紧凑视图
}

/**
 * Domain CalendarItem 到 CalendarDayInfo 适配器
 */
fun List<CalendarItem>.toCalendarDayInfo(date: LocalDate): CalendarDayInfo {
    val hasWorkout = this.isNotEmpty()
    val isCompleted = this.any { it.isCompleted }
    val planName = this.firstOrNull()?.name

    return CalendarDayInfo(
        date = date,
        hasWorkout = hasWorkout,
        isCompleted = isCompleted,
        planName = planName,
    )
}

/**
 * 增强版训练日历Dialog
 *
 * 支持：
 * - Domain层CalendarItem模型
 * - 拖拽功能
 * - 视图模式切换
 * - AI智能排程
 * - 触觉反馈
 */
@Composable
fun EnhancedWorkoutCalendarDialog(
    isVisible: Boolean,
    selectedDate: LocalDate?,
    calendarItems: Map<LocalDate, List<CalendarItem>>, // 使用Domain层模型
    onDateSelected: (LocalDate) -> Unit,
    onDismiss: () -> Unit,
    onTodayClick: () -> Unit,
    modifier: Modifier = Modifier,
    // === 新增功能参数 ===
    viewMode: CalendarDialogViewMode = CalendarDialogViewMode.MONTH,
    onViewModeChanged: (CalendarDialogViewMode) -> Unit = {},
    onItemClick: (CalendarItem) -> Unit = {},
    onItemDragStart: (CalendarItem) -> Unit = {},
    onItemDragEnd: (CalendarItem, LocalDate?) -> Unit = { _, _ -> },
    onAddCustomWorkout: (LocalDate) -> Unit = {},
    isDragging: Boolean = false,
    draggedItem: CalendarItem? = null,
    supportAI: Boolean = false,
) {
    if (!isVisible) return

    val hapticFeedback = LocalHapticFeedback.current

    // 转换为兼容的数据格式
    val calendarData = remember(calendarItems) {
        calendarItems.mapValues { (date, items) ->
            items.toCalendarDayInfo(date)
        }
    }

    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = MaterialTheme.shapes.large,
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp),
            ) {
                // === 增强的标题栏 ===
                EnhancedCalendarHeader(
                    selectedDate = selectedDate ?: Clock.System.todayIn(TimeZone.currentSystemDefault()),
                    viewMode = viewMode,
                    onViewModeChanged = { mode ->
                        hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                        onViewModeChanged(mode)
                    },
                    onTodayClick = {
                        hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                        onTodayClick()
                    },
                    onDismiss = onDismiss,
                    supportAI = supportAI,
                )

                // === 日历网格 ===
                when (viewMode) {
                    CalendarDialogViewMode.MONTH -> {
                        CalendarGrid(
                            selectedDate = selectedDate,
                            calendarData = calendarData,
                            onDateSelected = { date ->
                                hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                onDateSelected(date)
                            },
                        )
                    }
                    CalendarDialogViewMode.WEEK -> {
                        WeekCalendarGrid(
                            selectedDate = selectedDate,
                            calendarItems = calendarItems,
                            onDateSelected = onDateSelected,
                            onItemClick = onItemClick,
                            onItemDragStart = onItemDragStart,
                            onItemDragEnd = onItemDragEnd,
                            isDragging = isDragging,
                            draggedItem = draggedItem,
                        )
                    }
                    CalendarDialogViewMode.COMPACT -> {
                        CompactCalendarGrid(
                            selectedDate = selectedDate,
                            calendarData = calendarData,
                            onDateSelected = onDateSelected,
                        )
                    }
                }

                // === Function Call操作面板 ===
                if (selectedDate != null) {
                    CalendarActionPanel(
                        selectedDate = selectedDate,
                        onAddCustomWorkout = {
                            hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                            onAddCustomWorkout(selectedDate)
                        },
                        supportAI = supportAI,
                    )
                }
            }
        }
    }
}

/**
 * 增强的日历头部组件
 */
@Composable
private fun EnhancedCalendarHeader(
    selectedDate: LocalDate,
    viewMode: CalendarDialogViewMode,
    onViewModeChanged: (CalendarDialogViewMode) -> Unit,
    onTodayClick: () -> Unit,
    onDismiss: () -> Unit,
    supportAI: Boolean,
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp),
    ) {
        // 主标题行
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Column {
                Text(
                    text = "${selectedDate.year}年",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                )
                Text(
                    text = "${selectedDate.monthNumber}月",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.workoutColors.accentSecondary,
                )
            }

            Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                TextButton(onClick = onTodayClick) {
                    Text("今日")
                }
                TextButton(onClick = onDismiss) {
                    Text("关闭")
                }
            }
        }

        // 视图模式切换行
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // 视图模式切换按钮
            Row(
                horizontalArrangement = Arrangement.spacedBy(4.dp),
            ) {
                ViewModeButton(
                    icon = Icons.Default.ViewModule,
                    text = "月",
                    isSelected = viewMode == CalendarDialogViewMode.MONTH,
                    onClick = { onViewModeChanged(CalendarDialogViewMode.MONTH) },
                )

                ViewModeButton(
                    icon = Icons.Default.ViewWeek,
                    text = "周",
                    isSelected = viewMode == CalendarDialogViewMode.WEEK,
                    onClick = { onViewModeChanged(CalendarDialogViewMode.WEEK) },
                )

                ViewModeButton(
                    icon = Icons.Default.ViewModule,
                    text = "紧凑",
                    isSelected = viewMode == CalendarDialogViewMode.COMPACT,
                    onClick = { onViewModeChanged(CalendarDialogViewMode.COMPACT) },
                )
            }

            // AI状态指示器
            if (supportAI) {
                Surface(
                    shape = MaterialTheme.shapes.small,
                    color = MaterialTheme.colorScheme.tertiaryContainer,
                    modifier = Modifier.padding(4.dp),
                ) {
                    Text(
                        text = "AI",
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.colorScheme.onTertiaryContainer,
                        modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp),
                    )
                }
            }
        }
    }
}

/**
 * 视图模式切换按钮
 */
@Composable
private fun ViewModeButton(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit,
) {
    FilterChip(
        selected = isSelected,
        onClick = onClick,
        label = { Text(text) },
        leadingIcon = {
            Icon(
                imageVector = icon,
                contentDescription = null,
                modifier = Modifier.size(16.dp),
            )
        },
    )
}

/**
 * 周视图日历网格
 */
@Composable
private fun WeekCalendarGrid(
    selectedDate: LocalDate?,
    calendarItems: Map<LocalDate, List<CalendarItem>>,
    onDateSelected: (LocalDate) -> Unit,
    onItemClick: (CalendarItem) -> Unit,
    onItemDragStart: (CalendarItem) -> Unit,
    onItemDragEnd: (CalendarItem, LocalDate?) -> Unit,
    isDragging: Boolean,
    draggedItem: CalendarItem?,
) {
    val today = Clock.System.todayIn(TimeZone.currentSystemDefault())
    val startOfWeek = selectedDate?.let { date ->
        val dayOfWeek = date.dayOfWeek.ordinal
        date.minus(dayOfWeek, DateTimeUnit.DAY)
    } ?: today.minus(today.dayOfWeek.ordinal, DateTimeUnit.DAY)

    val weekDays = (0..6).map { startOfWeek.plus(it, DateTimeUnit.DAY) }

    Column {
        // 星期标题
        WeekDayHeaders()

        // 周视图内容
        LazyVerticalGrid(
            columns = GridCells.Fixed(7),
            modifier = Modifier.height(200.dp),
            verticalArrangement = Arrangement.spacedBy(4.dp),
            horizontalArrangement = Arrangement.spacedBy(4.dp),
        ) {
            items(weekDays) { date ->
                WeekDayItem(
                    date = date,
                    items = calendarItems[date] ?: emptyList(),
                    isSelected = date == selectedDate,
                    isToday = date == today,
                    onDateClick = { onDateSelected(date) },
                    onItemClick = onItemClick,
                    onItemDragStart = onItemDragStart,
                    onItemDragEnd = onItemDragEnd,
                    isDragging = isDragging,
                    draggedItem = draggedItem,
                )
            }
        }
    }
}

/**
 * 周视图日期项
 */
@Composable
private fun WeekDayItem(
    date: LocalDate,
    items: List<CalendarItem>,
    isSelected: Boolean,
    isToday: Boolean,
    onDateClick: () -> Unit,
    onItemClick: (CalendarItem) -> Unit,
    onItemDragStart: (CalendarItem) -> Unit,
    onItemDragEnd: (CalendarItem, LocalDate?) -> Unit,
    isDragging: Boolean,
    draggedItem: CalendarItem?,
) {
    Column(
        modifier = Modifier
            .gymBroClickable(pressed = false)
            .fillMaxWidth()
            .padding(2.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        // 日期数字
        Text(
            text = date.dayOfMonth.toString(),
            style = MaterialTheme.typography.bodySmall,
            fontWeight = if (isToday || isSelected) FontWeight.Bold else FontWeight.Normal,
            color = when {
                isToday -> MaterialTheme.colorScheme.primary
                isSelected -> MaterialTheme.colorScheme.onPrimaryContainer
                else -> MaterialTheme.colorScheme.onSurface
            },
        )

        // 项目列表
        items.take(2).forEach { item -> // 最多显示2个项目
            CalendarItemMiniChip(
                item = item,
                isDragging = isDragging && draggedItem?.id == item.id,
                onClick = { onItemClick(item) },
                onDragStart = { onItemDragStart(item) },
            )
        }

        if (items.size > 2) {
            Text(
                text = "+${items.size - 2}",
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
            )
        }
    }
}

/**
 * 迷你日历项目芯片
 */
@Composable
private fun CalendarItemMiniChip(
    item: CalendarItem,
    isDragging: Boolean,
    onClick: () -> Unit,
    onDragStart: () -> Unit,
) {
    val chipColor = when (item.type) {
        CalendarItemType.TEMPLATE -> MaterialTheme.colorScheme.primary
        CalendarItemType.PLAN -> MaterialTheme.colorScheme.secondary
        CalendarItemType.CUSTOM_WORKOUT -> MaterialTheme.colorScheme.tertiary
        CalendarItemType.REST_DAY -> MaterialTheme.colorScheme.outline
    }

    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .height(16.dp)
            .gymBroClickable(pressed = false),
        shape = MaterialTheme.shapes.extraSmall,
        color = chipColor.copy(alpha = if (isDragging) 0.5f else 0.2f),
        onClick = onClick,
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center,
        ) {
            Text(
                text = if (item.name.length > 4) "${item.name.take(3)}..." else item.name,
                style = MaterialTheme.typography.labelSmall,
                color = chipColor,
                maxLines = 1,
            )
        }
    }
}

/**
 * 紧凑日历网格
 */
@Composable
private fun CompactCalendarGrid(
    selectedDate: LocalDate?,
    calendarData: Map<LocalDate, CalendarDayInfo>,
    onDateSelected: (LocalDate) -> Unit,
) {
    val today = Clock.System.todayIn(TimeZone.currentSystemDefault())
    val currentMonth = selectedDate?.month ?: today.month
    val currentYear = selectedDate?.year ?: today.year

    // 只显示当前周
    val startOfWeek = (selectedDate ?: today).let { date ->
        val dayOfWeek = date.dayOfWeek.ordinal
        date.minus(dayOfWeek, DateTimeUnit.DAY)
    }
    val weekDays = (0..6).map { startOfWeek.plus(it, DateTimeUnit.DAY) }

    Column {
        WeekDayHeaders()

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly,
        ) {
            weekDays.forEach { date ->
                CalendarDayItem(
                    date = date,
                    isCurrentMonth = date.month == currentMonth,
                    isSelected = date == selectedDate,
                    isToday = date == today,
                    dayInfo = calendarData[date],
                    onClick = { onDateSelected(date) },
                    modifier = Modifier.weight(1f),
                )
            }
        }
    }
}

/**
 * Function Call操作面板
 */
@Composable
private fun CalendarActionPanel(
    selectedDate: LocalDate,
    onAddCustomWorkout: () -> Unit,
    supportAI: Boolean,
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant,
        ),
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp),
        ) {
            Text(
                text = "操作面板",
                style = MaterialTheme.typography.labelMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
            )

            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
            ) {
                // 添加自定义训练
                OutlinedButton(
                    onClick = onAddCustomWorkout,
                    modifier = Modifier.weight(1f),
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp),
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("添加训练")
                }

                // AI智能排程（如果支持）
                if (supportAI) {
                    Button(
                        onClick = { /* TODO: AI智能排程 */ },
                        modifier = Modifier.weight(1f),
                    ) {
                        Text("AI排程")
                    }
                }
            }
        }
    }
}

// === 保持原有的组件兼容性 ===

/**
 * 原始训练日历Dialog - 保持向后兼容
 */
@Composable
fun WorkoutCalendarDialog(
    isVisible: Boolean,
    selectedDate: LocalDate?,
    calendarData: Map<LocalDate, CalendarDayInfo>,
    onDateSelected: (LocalDate) -> Unit,
    onDismiss: () -> Unit,
    onTodayClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    if (!isVisible) return

    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = MaterialTheme.shapes.large,
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp),
            ) {
                // 标题栏
                CalendarHeader(
                    selectedDate = selectedDate ?: Clock.System.todayIn(TimeZone.currentSystemDefault()),
                    onTodayClick = onTodayClick,
                    onDismiss = onDismiss,
                )

                // 日历网格
                CalendarGrid(
                    selectedDate = selectedDate,
                    calendarData = calendarData,
                    onDateSelected = onDateSelected,
                )
            }
        }
    }
}

/**
 * 日历头部组件
 */
@Composable
private fun CalendarHeader(
    selectedDate: LocalDate,
    onTodayClick: () -> Unit,
    onDismiss: () -> Unit,
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Column {
            Text(
                text = "${selectedDate.year}年",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
            )
            Text(
                text = "${selectedDate.monthNumber}月",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.workoutColors.accentSecondary,
            )
        }

        Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
            TextButton(onClick = onTodayClick) {
                Text("今日")
            }
            TextButton(onClick = onDismiss) {
                Text("关闭")
            }
        }
    }
}

/**
 * 日历网格组件
 */
@Composable
private fun CalendarGrid(
    selectedDate: LocalDate?,
    calendarData: Map<LocalDate, CalendarDayInfo>,
    onDateSelected: (LocalDate) -> Unit,
) {
    val today = Clock.System.todayIn(TimeZone.currentSystemDefault())
    val currentMonth = selectedDate?.month ?: today.month
    val currentYear = selectedDate?.year ?: today.year

    // 计算当月第一天和最后一天
    val firstDayOfMonth = LocalDate(currentYear, currentMonth, 1)
    // 修复闰年计算：动态判断是否为闰年
    val isLeapYear = currentYear % 4 == 0 && (currentYear % 100 != 0 || currentYear % 400 == 0)
    val lastDayOfMonth = LocalDate(currentYear, currentMonth, currentMonth.length(isLeapYear))

    // 计算网格开始日期（包含上月末尾日期）
    val firstDayOfWeek = firstDayOfMonth.dayOfWeek.ordinal
    val startDate = firstDayOfMonth.minus(firstDayOfWeek, DateTimeUnit.DAY)

    // 生成6周的日期（42天）
    val dates =
        (0 until 42).map { offset ->
            startDate.plus(offset, DateTimeUnit.DAY)
        }

    Column {
        // 星期标题
        WeekDayHeaders()

        // 日期网格
        LazyVerticalGrid(
            columns = GridCells.Fixed(7),
            modifier = Modifier.height(300.dp),
            verticalArrangement = Arrangement.spacedBy(4.dp),
            horizontalArrangement = Arrangement.spacedBy(4.dp),
        ) {
            items(dates) { date ->
                CalendarDayItem(
                    date = date,
                    isCurrentMonth = date.month == currentMonth,
                    isSelected = date == selectedDate,
                    isToday = date == today,
                    dayInfo = calendarData[date],
                    onClick = { onDateSelected(date) },
                )
            }
        }
    }
}

/**
 * 星期标题行
 */
@Composable
private fun WeekDayHeaders() {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly,
    ) {
        val weekDays = listOf("日", "一", "二", "三", "四", "五", "六")
        weekDays.forEach { day ->
            Text(
                text = day,
                modifier = Modifier.weight(1f),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.labelMedium,
                color = MaterialTheme.workoutColors.accentSecondary,
            )
        }
    }
}

/**
 * 日历日期项组件
 */
@Composable
private fun CalendarDayItem(
    date: LocalDate,
    isCurrentMonth: Boolean,
    isSelected: Boolean,
    isToday: Boolean,
    dayInfo: CalendarDayInfo?,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val backgroundColor =
        when {
            isSelected -> MaterialTheme.workoutColors.accentPrimary
            isToday -> MaterialTheme.workoutColors.accentPrimary.copy(alpha = 0.3f)
            dayInfo?.hasWorkout == true -> MaterialTheme.workoutColors.accentSecondary.copy(alpha = 0.2f)
            else -> MaterialTheme.colorScheme.surface
        }

    val contentColor =
        when {
            isSelected -> Color.White
            isToday -> MaterialTheme.workoutColors.accentPrimary
            !isCurrentMonth -> MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
            else -> MaterialTheme.colorScheme.onSurface
        }

    Card(
        onClick = onClick,
        modifier =
        modifier
            .size(40.dp)
            .aspectRatio(1f),
        colors =
        CardDefaults.cardColors(
            containerColor = backgroundColor,
            contentColor = contentColor,
        ),
        elevation =
        CardDefaults.cardElevation(
            defaultElevation = if (isSelected || isToday) 4.dp else 0.dp,
        ),
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center,
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Text(
                    text = date.dayOfMonth.toString(),
                    style = MaterialTheme.typography.bodySmall,
                    fontWeight = if (isToday || isSelected) FontWeight.Bold else FontWeight.Normal,
                )

                // 训练指示器
                if (dayInfo?.hasWorkout == true) {
                    Box(
                        modifier =
                        Modifier
                            .size(4.dp),
                        contentAlignment = Alignment.Center,
                    ) {
                        Card(
                            modifier = Modifier.size(4.dp),
                            colors =
                            CardDefaults.cardColors(
                                containerColor =
                                if (dayInfo.isCompleted) {
                                    MaterialTheme.workoutColors.accentPrimary
                                } else {
                                    MaterialTheme.workoutColors.accentSecondary
                                },
                            ),
                        ) {}
                    }
                }
            }
        }
    }
}

@GymBroPreview
@Composable
private fun WorkoutCalendarDialogPreview() {
    GymBroTheme {
        val today = Clock.System.todayIn(TimeZone.currentSystemDefault())
        val sampleData =
            mapOf(
                today to CalendarDayInfo(
                    date = today,
                    hasWorkout = true,
                    isCompleted = true,
                    planName = "推胸训练",
                ),
                today.plus(1, DateTimeUnit.DAY) to CalendarDayInfo(
                    date = today.plus(1, DateTimeUnit.DAY),
                    hasWorkout = true,
                    isCompleted = false,
                    planName = "拉背训练",
                ),
                today.plus(3, DateTimeUnit.DAY) to CalendarDayInfo(
                    date = today.plus(3, DateTimeUnit.DAY),
                    hasWorkout = true,
                    isCompleted = false,
                    planName = "腿部训练",
                ),
            )

        WorkoutCalendarDialog(
            isVisible = true,
            selectedDate = today,
            calendarData = sampleData,
            onDateSelected = {},
            onDismiss = {},
            onTodayClick = {},
        )
    }
}

```

File: D:/GymBro/GymBro/designSystem/src/main/kotlin/com/example/gymbro/designSystem/components/GymBroInputDialogs.kt
```kotlin
package com.example.gymbro.designSystem.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme

/**
 * 🎯 GymBro 通用文本输入对话框
 * 遵循 MVI-Architecture-Performance-Guide.md 最佳实践
 *
 * 特性：
 * - 无状态设计，状态完全由外部 ViewModel 管理
 * - 集成 GymBro 设计系统
 * - 统一的样式和行为
 * - 支持输入验证和错误显示
 * - 字符长度限制支持
 *
 * @param show 是否显示对话框
 * @param title 对话框标题
 * @param value 当前输入值
 * @param onValueChange 输入值变化回调
 * @param onDismiss 取消回调
 * @param onConfirm 确认回调
 * @param modifier 修饰符
 * @param label 输入框标签
 * @param placeholder 占位符文本
 * @param isError 是否显示错误状态
 * @param errorMessage 错误消息
 * @param maxLength 最大字符长度
 * @param enabled 是否启用
 * @param confirmText 确认按钮文本
 * @param dismissText 取消按钮文本
 */
@Composable
fun GymBroTextEditDialog(
    show: Boolean,
    title: UiText,
    value: String,
    onValueChange: (String) -> Unit,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit,
    modifier: Modifier = Modifier,
    label: UiText? = null,
    placeholder: UiText? = null,
    isError: Boolean = false,
    errorMessage: UiText? = null,
    maxLength: Int? = null,
    enabled: Boolean = true,
    confirmText: UiText = UiText.DynamicString("确定"),
    dismissText: UiText = UiText.DynamicString("取消"),
) {
    if (show) {
        AlertDialog(
            onDismissRequest = onDismiss,
            title = {
                Text(
                    text = title.asString(),
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface,
                )
            },
            text = {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.spacedBy(12.dp),
                ) {
                    GymBroInputField(
                        value = value,
                        onValueChange = onValueChange,
                        label = label?.let { { Text(it.asString()) } },
                        placeholder = placeholder?.asString() ?: "",
                        modifier = Modifier.fillMaxWidth(),
                        isError = isError,
                        errorMessage = errorMessage,
                        enabled = enabled,
                        maxLength = maxLength,
                        singleLine = true,
                    )

                    // 字符计数显示
                    if (maxLength != null) {
                        Text(
                            text = "${value.length}/$maxLength",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.align(Alignment.End),
                        )
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = onConfirm,
                    enabled = enabled && !isError,
                ) {
                    Text(
                        text = confirmText.asString(),
                        color = if (enabled && !isError) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                        },
                    )
                }
            },
            dismissButton = {
                TextButton(onClick = onDismiss) {
                    Text(
                        text = dismissText.asString(),
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                    )
                }
            },
            modifier = modifier,
            containerColor = MaterialTheme.colorScheme.surface,
            tonalElevation = 6.dp,
        )
    }
}

/**
 * 🎯 GymBro 通用数值输入对话框
 * 专门用于数值（身高、体重、年龄等）输入
 *
 * @param show 是否显示对话框
 * @param title 对话框标题
 * @param value 当前数值（字符串格式）
 * @param onValueChange 数值变化回调
 * @param onDismiss 取消回调
 * @param onConfirm 确认回调
 * @param modifier 修饰符
 * @param label 输入框标签
 * @param unit 单位文本（如"kg", "cm"）
 * @param minValue 最小值
 * @param maxValue 最大值
 * @param decimalPlaces 小数位数
 * @param isError 是否显示错误状态
 * @param errorMessage 错误消息
 * @param enabled 是否启用
 * @param confirmText 确认按钮文本
 * @param dismissText 取消按钮文本
 */
@Composable
fun GymBroNumberEditDialog(
    show: Boolean,
    title: UiText,
    value: String,
    onValueChange: (String) -> Unit,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit,
    modifier: Modifier = Modifier,
    label: UiText? = null,
    unit: UiText? = null,
    minValue: Double? = null,
    maxValue: Double? = null,
    decimalPlaces: Int = 0,
    isError: Boolean = false,
    errorMessage: UiText? = null,
    enabled: Boolean = true,
    confirmText: UiText = UiText.DynamicString("确定"),
    dismissText: UiText = UiText.DynamicString("取消"),
) {
    if (show) {
        AlertDialog(
            onDismissRequest = onDismiss,
            title = {
                Text(
                    text = title.asString(),
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface,
                )
            },
            text = {
                numberField(
                    value = value,
                    onValueChange = onValueChange,
                    label = label ?: UiText.DynamicString("请输入数值"),
                    modifier = Modifier.fillMaxWidth(),
                    isError = isError,
                    errorMessage = errorMessage,
                    enabled = enabled,
                    minValue = minValue,
                    maxValue = maxValue,
                    decimalPlaces = decimalPlaces,
                    unit = unit,
                )
            },
            confirmButton = {
                val isValidNumber = value.toDoubleOrNull() != null
                TextButton(
                    onClick = onConfirm,
                    enabled = enabled && !isError && isValidNumber,
                ) {
                    Text(
                        text = confirmText.asString(),
                        color = if (enabled && !isError && isValidNumber) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                        },
                    )
                }
            },
            dismissButton = {
                TextButton(onClick = onDismiss) {
                    Text(
                        text = dismissText.asString(),
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                    )
                }
            },
            modifier = modifier,
            containerColor = MaterialTheme.colorScheme.surface,
            tonalElevation = 6.dp,
        )
    }
}

/**
 * 🎯 GymBro 通用单选对话框
 * 用于性别、健身水平等单选场景
 *
 * @param T 选项类型
 * @param show 是否显示对话框
 * @param title 对话框标题
 * @param options 可选项列表
 * @param selectedOption 当前选中项
 * @param onOptionSelected 选项选择回调
 * @param onDismiss 取消回调
 * @param onConfirm 确认回调
 * @param getDisplayText 获取选项显示文本的函数
 * @param modifier 修饰符
 * @param enabled 是否启用
 * @param confirmText 确认按钮文本
 * @param dismissText 取消按钮文本
 */
@Composable
fun <T> GymBroSingleChoiceDialog(
    show: Boolean,
    title: UiText,
    options: List<T>,
    selectedOption: T?,
    onOptionSelected: (T) -> Unit,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit,
    getDisplayText: (T) -> String,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    confirmText: UiText = UiText.DynamicString("确定"),
    dismissText: UiText = UiText.DynamicString("取消"),
) {
    if (show) {
        AlertDialog(
            onDismissRequest = onDismiss,
            title = {
                Text(
                    text = title.asString(),
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface,
                )
            },
            text = {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(max = 400.dp)
                        .verticalScroll(rememberScrollState()),
                ) {
                    options.forEach { option ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable(enabled = enabled) {
                                    onOptionSelected(option)
                                }
                                .padding(vertical = 12.dp, horizontal = 4.dp),
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            RadioButton(
                                selected = selectedOption == option,
                                onClick = { if (enabled) onOptionSelected(option) },
                                enabled = enabled,
                                colors = RadioButtonDefaults.colors(
                                    selectedColor = MaterialTheme.colorScheme.primary,
                                    unselectedColor = MaterialTheme.colorScheme.onSurfaceVariant,
                                ),
                            )
                            Spacer(modifier = Modifier.width(16.dp))
                            Text(
                                text = getDisplayText(option),
                                style = MaterialTheme.typography.bodyLarge,
                                color = if (enabled) {
                                    MaterialTheme.colorScheme.onSurface
                                } else {
                                    MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                                },
                            )
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = onConfirm,
                    enabled = enabled && selectedOption != null,
                ) {
                    Text(
                        text = confirmText.asString(),
                        color = if (enabled && selectedOption != null) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                        },
                    )
                }
            },
            dismissButton = {
                TextButton(onClick = onDismiss) {
                    Text(
                        text = dismissText.asString(),
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                    )
                }
            },
            modifier = modifier,
            containerColor = MaterialTheme.colorScheme.surface,
            tonalElevation = 6.dp,
        )
    }
}

/**
 * 🎯 GymBro 通用多选对话框
 * 用于训练日等多选场景
 *
 * @param T 选项类型
 * @param show 是否显示对话框
 * @param title 对话框标题
 * @param options 可选项列表
 * @param selectedOptions 当前选中项集合
 * @param onOptionsChanged 选项变化回调
 * @param onDismiss 取消回调
 * @param onConfirm 确认回调
 * @param getDisplayText 获取选项显示文本的函数
 * @param modifier 修饰符
 * @param enabled 是否启用
 * @param confirmText 确认按钮文本
 * @param dismissText 取消按钮文本
 */
@Composable
fun <T> GymBroMultiChoiceDialog(
    show: Boolean,
    title: UiText,
    options: List<T>,
    selectedOptions: Set<T>,
    onOptionsChanged: (Set<T>) -> Unit,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit,
    getDisplayText: (T) -> String,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    confirmText: UiText = UiText.DynamicString("确定"),
    dismissText: UiText = UiText.DynamicString("取消"),
) {
    if (show) {
        AlertDialog(
            onDismissRequest = onDismiss,
            title = {
                Text(
                    text = title.asString(),
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface,
                )
            },
            text = {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(max = 400.dp)
                        .verticalScroll(rememberScrollState()),
                ) {
                    options.forEach { option ->
                        val isSelected = selectedOptions.contains(option)
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable(enabled = enabled) {
                                    val newSelection = if (isSelected) {
                                        selectedOptions - option
                                    } else {
                                        selectedOptions + option
                                    }
                                    onOptionsChanged(newSelection)
                                }
                                .padding(vertical = 12.dp, horizontal = 4.dp),
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            Checkbox(
                                checked = isSelected,
                                onCheckedChange = { checked ->
                                    if (enabled) {
                                        val newSelection = if (checked) {
                                            selectedOptions + option
                                        } else {
                                            selectedOptions - option
                                        }
                                        onOptionsChanged(newSelection)
                                    }
                                },
                                enabled = enabled,
                                colors = CheckboxDefaults.colors(
                                    checkedColor = MaterialTheme.colorScheme.primary,
                                    uncheckedColor = MaterialTheme.colorScheme.onSurfaceVariant,
                                ),
                            )
                            Spacer(modifier = Modifier.width(16.dp))
                            Text(
                                text = getDisplayText(option),
                                style = MaterialTheme.typography.bodyLarge,
                                color = if (enabled) {
                                    MaterialTheme.colorScheme.onSurface
                                } else {
                                    MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                                },
                            )
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = onConfirm,
                    enabled = enabled,
                ) {
                    Text(
                        text = confirmText.asString(),
                        color = if (enabled) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                        },
                    )
                }
            },
            dismissButton = {
                TextButton(onClick = onDismiss) {
                    Text(
                        text = dismissText.asString(),
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                    )
                }
            },
            modifier = modifier,
            containerColor = MaterialTheme.colorScheme.surface,
            tonalElevation = 6.dp,
        )
    }
}

// === Preview 组件 ===

@GymBroPreview
@Composable
private fun GymBroTextEditDialogPreview() {
    GymBroTheme {
        var showDialog by remember { mutableStateOf(true) }
        var text by remember { mutableStateOf("示例文本") }

        GymBroTextEditDialog(
            show = showDialog,
            title = UiText.DynamicString("编辑昵称"),
            value = text,
            onValueChange = { text = it },
            onDismiss = { showDialog = false },
            onConfirm = { showDialog = false },
            label = UiText.DynamicString("请输入昵称"),
            maxLength = 20,
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroNumberEditDialogPreview() {
    GymBroTheme {
        var showDialog by remember { mutableStateOf(true) }
        var number by remember { mutableStateOf("175") }

        GymBroNumberEditDialog(
            show = showDialog,
            title = UiText.DynamicString("编辑身高"),
            value = number,
            onValueChange = { number = it },
            onDismiss = { showDialog = false },
            onConfirm = { showDialog = false },
            label = UiText.DynamicString("请输入身高"),
            unit = UiText.DynamicString("cm"),
            minValue = 50.0,
            maxValue = 300.0,
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroSingleChoiceDialogPreview() {
    GymBroTheme {
        var showDialog by remember { mutableStateOf(true) }
        val options = listOf("男", "女", "其他", "不愿说")
        var selected by remember { mutableStateOf("男") }

        GymBroSingleChoiceDialog(
            show = showDialog,
            title = UiText.DynamicString("选择性别"),
            options = options,
            selectedOption = selected,
            onOptionSelected = { selected = it },
            onDismiss = { showDialog = false },
            onConfirm = { showDialog = false },
            getDisplayText = { it },
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroMultiChoiceDialogPreview() {
    GymBroTheme {
        var showDialog by remember { mutableStateOf(true) }
        val options = listOf("周一", "周二", "周三", "周四", "周五", "周六", "周日")
        var selected by remember { mutableStateOf(setOf("周一", "周三", "周五")) }

        GymBroMultiChoiceDialog(
            show = showDialog,
            title = UiText.DynamicString("选择训练日"),
            options = options,
            selectedOptions = selected,
            onOptionsChanged = { selected = it },
            onDismiss = { showDialog = false },
            onConfirm = { showDialog = false },
            getDisplayText = { it },
        )
    }
}

```

File: D:/GymBro/GymBro/features/auth/src/main/kotlin/com/example/gymbro/features/auth/ui/login/AuthLoginScreen.kt
```kotlin
package com.example.gymbro.features.auth.ui.login

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.ThemeMode
import com.example.gymbro.designSystem.components.animations.GymBroTypeWriter
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.components.extras.GymBroLogo
import com.example.gymbro.designSystem.components.extras.StarRingCanvas
import com.example.gymbro.designSystem.components.gymBroThemeToggleButton
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.StarRingThemePresets
import com.example.gymbro.designSystem.theme.motion.GymBroMotionConfig
import com.example.gymbro.designSystem.theme.motion.ProvideGymBroMotionConfig
import com.example.gymbro.designSystem.theme.starRingTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.features.auth.ui.animation.AuthAnimSpec
import com.example.gymbro.features.auth.ui.login.components.AuthBackgroundLayer
import com.example.gymbro.features.auth.ui.login.components.AuthButtonSection

/**
 * Auth登录主屏幕 - 现代化设计
 *
 * 按照UI落位.md的坐标规范设计：
 * - 跳过按钮 (85%-95%, 3%-8%)
 * - 应用名称 (50%, 20%)
 * - 版本信息 (50%, 26%)
 * - 装饰图形 (50%, 35%)
 * - 标语文本 (50%, 45%)
 * - 按钮组 (10%-90%, 55%-85%)
 * - 法律声明 (50%, 95%)
 */
@Composable
fun AuthLoginScreen(
    modifier: Modifier = Modifier,
    onLoginSuccess: () -> Unit = {},
    onRegisterClick: () -> Unit = {},
    onPhoneLoginClick: () -> Unit = {},
    onSkipClick: () -> Unit = {},
    onWeChatLoginClick: () -> Unit = {},
    onGoogleLoginClick: () -> Unit = {},
    onAnonymousLoginClick: () -> Unit = {},
    isLoading: Boolean = false,
) {
    // 🎯 主题状态管理：支持手动切换主题
    var currentThemeMode by remember { mutableStateOf(ThemeMode.SYSTEM) }
    val systemDarkTheme = isSystemInDarkTheme()

    // 根据主题模式计算实际的深色主题状态
    val isDarkTheme = remember(currentThemeMode, systemDarkTheme) {
        when (currentThemeMode) {
            ThemeMode.LIGHT -> false
            ThemeMode.DARK -> true
            ThemeMode.SYSTEM -> systemDarkTheme
        }
    }

    // 主题切换回调
    val onThemeToggle = remember {
        {
            currentThemeMode = when (currentThemeMode) {
                ThemeMode.LIGHT -> ThemeMode.DARK
                ThemeMode.DARK -> ThemeMode.SYSTEM
                ThemeMode.SYSTEM -> ThemeMode.LIGHT
            }
        }
    }

    AuthLoginScreenContent(
        modifier = modifier,
        isDarkTheme = isDarkTheme,
        currentThemeMode = currentThemeMode,
        onThemeToggle = onThemeToggle,
        isLoading = isLoading,
        onLoginSuccess = onLoginSuccess,
        onRegisterClick = onRegisterClick,
        onPhoneLoginClick = onPhoneLoginClick,
        onSkipClick = onSkipClick,
        onWeChatLoginClick = onWeChatLoginClick,
        onGoogleLoginClick = onGoogleLoginClick,
        onAnonymousLoginClick = onAnonymousLoginClick,
    )
}

/**
 * Auth登录屏幕内容 - 现代化实现
 * 严格按照UI落位.md的坐标系统布局
 */
@Composable
fun AuthLoginScreenContent(
    modifier: Modifier = Modifier,
    isDarkTheme: Boolean,
    currentThemeMode: ThemeMode,
    onThemeToggle: () -> Unit,
    isLoading: Boolean,
    onLoginSuccess: () -> Unit,
    onRegisterClick: () -> Unit,
    onPhoneLoginClick: () -> Unit,
    onSkipClick: () -> Unit,
    onWeChatLoginClick: () -> Unit,
    onGoogleLoginClick: () -> Unit,
    onAnonymousLoginClick: () -> Unit,
) {
    // 🎯 性能监控：简化重组跟踪
    val recompositionCounter = remember { mutableStateOf(0) }
    LaunchedEffect(Unit) {
        recompositionCounter.value++
    }

    // 🎯 修复：恢复颜色动画，确保正确的颜色映射
    val backgroundColorAnimated by animateColorAsState(
        targetValue = if (isDarkTheme) Tokens.Color.Gray950 else Tokens.Color.Gray000,
        animationSpec = AuthAnimSpec.colorTransitionAnimation(),
        label = "BackgroundColor",
    )

    val contentColorAnimated by animateColorAsState(
        targetValue = if (isDarkTheme) Tokens.Color.Gray300 else Tokens.Color.Gray950,
        animationSpec = AuthAnimSpec.colorTransitionAnimation(),
        label = "ContentColor",
    )

    // 次要文字颜色 - 修复映射：浅色主题用深色文字
    val secondaryTextColor by animateColorAsState(
        targetValue = if (isDarkTheme) Tokens.Color.Gray500 else Tokens.Color.Gray700,
        animationSpec = AuthAnimSpec.colorTransitionAnimation(),
        label = "SecondaryTextColor",
    )

    // 法律声明文字颜色 - 修复映射：浅色主题用深色文字
    val legalTextColor by animateColorAsState(
        targetValue = if (isDarkTheme) Tokens.Color.Gray400 else Tokens.Color.Gray600,
        animationSpec = AuthAnimSpec.colorTransitionAnimation(),
        label = "LegalTextColor",
    )

    // Logo区域进入动画 - 修复：从0开始动画
    val logoEnterAnimation by animateFloatAsState(
        targetValue = 1f,
        animationSpec =
        AuthAnimSpec.logoEnterAnimation(
            delayMillis = AuthLoginConstants.LOGO_ENTER_DELAY_MS,
        ),
        label = "LogoEnterAnimation",
    )

    // 🎯 状态栏高度自适应 - 全屏显示时需要补偿状态栏高度
    val statusBarHeight = WindowInsets.statusBars.asPaddingValues().calculateTopPadding()

    ProvideGymBroMotionConfig(
        config = GymBroMotionConfig(
            enableAnimations = true,
            enableBreathing = true,
            enableInfiniteAnimations = true,
            enableMicroInteractions = true,
        ),
    ) {
        Box(
            modifier = modifier
                .fillMaxSize()
                .background(backgroundColorAnimated),
        ) {
            // 背景层：优化的背景组件
            AuthBackgroundLayer(
                isDarkTheme = isDarkTheme,
                modifier = Modifier.align(Alignment.BottomCenter),
            )

            // 🎯 右上角主题切换按钮 - 添加状态栏高度补偿
            Box(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(
                        top = statusBarHeight + 16.dp,
                        end = 16.dp,
                    ),
            ) {
                gymBroThemeToggleButton(
                    currentThemeMode = currentThemeMode,
                    onThemeToggle = onThemeToggle,
                    modifier = Modifier
                        .alpha(logoEnterAnimation),
                )
            }

            // 主内容区域 - 严格按照坐标布局，添加状态栏高度补偿
            GrokStyleMainContent(
                logoEnterAnimation = logoEnterAnimation,
                contentColor = contentColorAnimated,
                secondaryTextColor = secondaryTextColor,
                isLoading = isLoading,
                isDarkTheme = isDarkTheme,
                statusBarHeight = statusBarHeight,
                onPhoneLoginClick = onPhoneLoginClick,
                onGoogleLoginClick = onGoogleLoginClick,
                onAnonymousLoginClick = onAnonymousLoginClick,
                onLoginSuccess = onLoginSuccess,
            )

            // [法律声明] - 位置: 底部中心 (50%, ~95%)
            Text(
                text = "继续即表示您同意 条款 和 隐私政策",
                color = legalTextColor,
                fontSize = MaterialTheme.typography.bodySmall.fontSize,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(
                        bottom = Tokens.Spacing.Medium,
                        start = Tokens.Spacing.Large,
                        end = Tokens.Spacing.Large,
                    ),
            )
        }
    }
}

/**
 * 现代化主内容区域
 * 按照精确坐标落位系统布局：
 * - 应用名称: (50%, ~22%)
 * - 版本信息: (50%, ~30%)
 * - 装饰图形: (50%, ~35%)
 * - 应用标语: (50%, ~45%)
 * - 按钮组: y轴55%至85%
 * - 法律声明: (50%, ~95%)
 */
@Composable
private fun GrokStyleMainContent(
    logoEnterAnimation: Float,
    contentColor: androidx.compose.ui.graphics.Color,
    secondaryTextColor: androidx.compose.ui.graphics.Color,
    isLoading: Boolean,
    isDarkTheme: Boolean,
    statusBarHeight: androidx.compose.ui.unit.Dp,
    onPhoneLoginClick: () -> Unit,
    onGoogleLoginClick: () -> Unit,
    onAnonymousLoginClick: () -> Unit,
    onLoginSuccess: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val density = LocalDensity.current
    val enterOffsetPx = with(density) { AuthLoginConstants.CONTENT_ENTER_OFFSET.toPx() }

    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center,
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = statusBarHeight), // 添加状态栏高度补偿
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            // 1. [应用名称] 标题 - 位置: (50%, ~22%) - 使用金属质感LOGO
            Box(
                modifier =
                Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(0.22f),
                contentAlignment = Alignment.BottomCenter,
            ) {
                GymBroLogo(
                    modifier =
                    Modifier
                        .alpha(logoEnterAnimation)
                        .graphicsLayer {
                            translationY = (1f - logoEnterAnimation) * -enterOffsetPx
                        },
                    animated = true,
                    useHdr = false,
                    style = MaterialTheme.typography.displayLarge.copy(fontSize = 72.sp),
                    glowEffect = false,
                )
            }

            // 2. [版本信息] 副标题 - 位置: (50%, ~30%)
            Box(
                modifier =
                Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(0.10f),
                // 30% - 22% = 8%的空间分配
                contentAlignment = Alignment.Center,
            ) {
                Text(
                    text = UiText.DynamicString("Android Beta").asString(),
                    fontSize = MaterialTheme.typography.titleMedium.fontSize,
                    color = secondaryTextColor,
                    textAlign = TextAlign.Center,
                    modifier =
                    Modifier
                        .alpha(logoEnterAnimation)
                        .graphicsLayer {
                            translationY = (1f - logoEnterAnimation) * -enterOffsetPx
                        },
                )
            }

            // 3. [装饰图形] - 位置: (50%, ~35%)
            Box(
                modifier =
                Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(0.125f),
                // 35% - 30% = 5%的空间分配
                contentAlignment = Alignment.Center,
            ) {
                Box(
                    modifier =
                    Modifier
                        .size(120.dp) // 适中的装饰尺寸
                        .alpha(logoEnterAnimation)
                        .graphicsLayer {
                            translationY = (1f - logoEnterAnimation) * -enterOffsetPx
                        },
                    contentAlignment = Alignment.Center,
                ) {
                    // 🎯 StarRing旋转动画：添加外部旋转控制
                    val infiniteTransition = rememberInfiniteTransition(label = "StarRingRotation")
                    val rotationAngle by infiniteTransition.animateFloat(
                        initialValue = 0f,
                        targetValue = 360f,
                        animationSpec = infiniteRepeatable(
                            animation = tween(
                                durationMillis = 60000, // 60秒一圈
                                easing = LinearEasing,
                            ),
                            repeatMode = RepeatMode.Restart,
                        ),
                        label = "StarRingRotationAngle",
                    )

                    starRingTheme(
                        spec = if (isDarkTheme) {
                            StarRingThemePresets.HighContrastDark
                        } else {
                            StarRingThemePresets.HighContrastLight
                        },
                    ) {
                        StarRingCanvas(
                            modifier = Modifier
                                .fillMaxSize()
                                .graphicsLayer { rotationZ = rotationAngle },
                            enableAnimation = true,
                        )
                    }
                }
            }

            // 4. [应用标语] 文本 - 位置: (50%, ~45%)
            Box(
                modifier =
                Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(0.20f),
                // 45% - 35% = 10%的空间分配
                contentAlignment = Alignment.Center,
            ) {
                // 🎯 修复层级问题：确保打字机动画在正确层级显示
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .alpha(logoEnterAnimation)
                        .graphicsLayer {
                            translationY = (1f - logoEnterAnimation) * -enterOffsetPx
                        },
                    contentAlignment = Alignment.Center,
                ) {
                    GymBroTypeWriter(
                        text = UiText.DynamicString("Understand the universe"),
                        textColor = secondaryTextColor,
                        fontSize = 18.sp,
                        enableAnimation = true,
                        autoStart = true,
                        showCursor = true,
                        cursorChar = '_',
                        typingSpeed = 60L, // 更快的打字速度
                        blinkSpeed = 300L, // 更快的闪烁
                        textAlign = TextAlign.Center,
                    )
                }
            }

            // 5. [交互按钮组] - 位置: y轴55%至85% (30%的垂直空间)
            Box(
                modifier =
                Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(0.75f),
                // 占用剩余空间的大部分 (55%-85%区域)
                contentAlignment = Alignment.TopCenter,
            ) {
                AuthButtonSection(
                    isLoading = isLoading,
                    contentColor = contentColor,
                    onPhoneLoginClick = onPhoneLoginClick,
                    onGoogleLoginClick = onGoogleLoginClick,
                    onAnonymousLoginClick = onAnonymousLoginClick,
                    onLoginSuccess = onLoginSuccess,
                    modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(horizontal = Tokens.Spacing.Large)
                        .alpha(logoEnterAnimation)
                        .graphicsLayer {
                            translationY = (1f - logoEnterAnimation) * enterOffsetPx
                        },
                )
            }
        }
    }
}

// === @GymBroPreview 预览组件 ===

@GymBroPreview
@Composable
private fun AuthLoginScreenPreview() {
    GymBroTheme {
        AuthLoginScreenContent(
            isDarkTheme = true,
            currentThemeMode = ThemeMode.DARK,
            onThemeToggle = {},
            isLoading = false,
            onLoginSuccess = {},
            onRegisterClick = {},
            onPhoneLoginClick = {},
            onSkipClick = {},
            onWeChatLoginClick = {},
            onGoogleLoginClick = {},
            onAnonymousLoginClick = {},
        )
    }
}

@GymBroPreview
@Composable
private fun AuthLoginScreenLightPreview() {
    GymBroTheme {
        AuthLoginScreenContent(
            isDarkTheme = false,
            currentThemeMode = ThemeMode.LIGHT,
            onThemeToggle = {},
            isLoading = false,
            onLoginSuccess = {},
            onRegisterClick = {},
            onPhoneLoginClick = {},
            onSkipClick = {},
            onWeChatLoginClick = {},
            onGoogleLoginClick = {},
            onAnonymousLoginClick = {},
        )
    }
}

@GymBroPreview
@Composable
private fun AuthLoginScreenLoadingPreview() {
    GymBroTheme {
        AuthLoginScreenContent(
            isDarkTheme = true,
            currentThemeMode = ThemeMode.SYSTEM,
            onThemeToggle = {},
            isLoading = true,
            onLoginSuccess = {},
            onRegisterClick = {},
            onPhoneLoginClick = {},
            onSkipClick = {},
            onWeChatLoginClick = {},
            onGoogleLoginClick = {},
            onAnonymousLoginClick = {},
        )
    }
}

```

File: D:/GymBro/GymBro/features/auth/src/main/kotlin/com/example/gymbro/features/auth/ui/phone/PhoneLoginScreen.kt
```kotlin
package com.example.gymbro.features.auth.ui.phone

import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.R
import com.example.gymbro.designSystem.components.GymBroButton
import com.example.gymbro.designSystem.components.GymBroButtonDefaults
import com.example.gymbro.designSystem.components.animations.GymBroTypeWriter
import com.example.gymbro.designSystem.components.extras.StarRingCanvas
import com.example.gymbro.designSystem.components.GymBroInputField
import com.example.gymbro.designSystem.components.phoneField
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme

import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.features.auth.ui.animation.AuthAnimSpec
// 移除了独立的时间基础主题，使用全局主题
import com.example.gymbro.features.auth.ui.login.AuthLoginConstants

/**
 * 手机号登录屏幕 - 现代化设计
 *
 * 特性：
 * - 星环背景动画
 * - 时间基础主题切换
 * - Logo区域进入动画
 * - 统一的按钮样式和颜色（与AuthLoginScreen保持一致）
 * - 简洁的布局设计
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PhoneLoginScreen(
    modifier: Modifier = Modifier,
    onBackClick: () -> Unit = {},
    onLoginSuccess: () -> Unit = {},
    isLoading: Boolean = false,
) {
    // 使用全局主题，不需要独立的时间基础主题
    PhoneLoginScreenContent(
        modifier = modifier,
        isLoading = isLoading,
        onBackClick = onBackClick,
        onLoginSuccess = onLoginSuccess,
    )
}

/**
 * 手机登录屏幕内容 - 简洁现代化设计
 * 与AuthLoginScreen保持一致的布局风格
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun PhoneLoginScreenContent(
    modifier: Modifier = Modifier,
    isLoading: Boolean,
    onBackClick: () -> Unit,
    onLoginSuccess: () -> Unit,
) {
    var phoneNumber by remember { mutableStateOf("") }
    var verificationCode by remember { mutableStateOf("") }
    var isCodeSent by remember { mutableStateOf(false) }

    val density = LocalDensity.current
    val enterOffsetPx = with(density) { AuthLoginConstants.CONTENT_ENTER_OFFSET.toPx() }

    // 使用Material3主题颜色，与全局主题保持一致
    val backgroundColorAnimated = MaterialTheme.colorScheme.background
    val contentColorAnimated = MaterialTheme.colorScheme.onBackground

    // Logo区域进入动画
    val logoEnterAnimation by animateFloatAsState(
        targetValue = 1f,
        animationSpec = AuthAnimSpec.logoEnterAnimation(delayMillis = 200),
        label = "LogoEnterAnimation",
    )

    Box(
        modifier =
        modifier
            .fillMaxSize()
            .background(backgroundColorAnimated)
            .windowInsetsPadding(WindowInsets.statusBars),
    ) {
        // TopAppBar
        TopAppBar(
            title = {
                Text(
                    text = UiText.DynamicString("手机号登录").asString(),
                    color = contentColorAnimated,
                    fontSize = 20.sp,
                    fontWeight = FontWeight.SemiBold,
                )
            },
            navigationIcon = {
                // 与home页面个人资料按键位置保持一致的返回按钮
                IconButton(
                    onClick = onBackClick,
                    modifier = Modifier, // 确保与HomeTopBar的navigationIcon对齐
                ) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                        contentDescription = "返回",
                        tint = contentColorAnimated,
                    )
                }
            },
            colors =
            TopAppBarDefaults.topAppBarColors(
                containerColor = androidx.compose.ui.graphics.Color.Transparent,
                titleContentColor = contentColorAnimated,
                navigationIconContentColor = contentColorAnimated,
            ),
        )

        // 主内容区域 - 与AuthLoginScreen保持一致的布局
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center,
        ) {
            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                // 为TopAppBar预留空间 (0% - 8%)
                Spacer(modifier = Modifier.fillMaxHeight(0.08f))

                // 1. [应用名称] 标题 - 位置: (50%, ~22%)
                Box(
                    modifier =
                    Modifier
                        .fillMaxWidth()
                        .fillMaxHeight(0.20f),
                    // 22% - 8% = 14%的空间
                    contentAlignment = Alignment.BottomCenter,
                ) {
                    Text(
                        text = UiText.StringResource(R.string.app_name, emptyList()).asString(),
                        fontSize = 72.sp,
                        fontWeight = FontWeight.Bold,
                        color = contentColorAnimated,
                        textAlign = TextAlign.Center,
                        modifier =
                        Modifier
                            .alpha(logoEnterAnimation)
                            .graphicsLayer {
                                translationY = (1f - logoEnterAnimation) * -enterOffsetPx
                            },
                    )
                }

                // 2. [版本信息] 副标题 - 位置: (50%, ~30%)
                Box(
                    modifier =
                    Modifier
                        .fillMaxWidth()
                        .fillMaxHeight(0.11f),
                    // 30% - 22% = 8%的空间
                    contentAlignment = Alignment.Center,
                ) {
                    Text(
                        text = UiText.DynamicString("手机验证登录").asString(),
                        fontSize = 18.sp,
                        color = contentColorAnimated.copy(alpha = 0.7f),
                        textAlign = TextAlign.Center,
                        modifier =
                        Modifier
                            .alpha(logoEnterAnimation)
                            .graphicsLayer {
                                translationY = (1f - logoEnterAnimation) * -enterOffsetPx
                            },
                    )
                }

                // 3. [装饰图形] - 位置: (50%, ~35%)
                Box(
                    modifier =
                    Modifier
                        .fillMaxWidth()
                        .fillMaxHeight(0.08f),
                    // 35% - 30% = 5%的空间
                    contentAlignment = Alignment.Center,
                ) {
                    Box(
                        modifier =
                        Modifier
                            .size(120.dp)
                            .alpha(logoEnterAnimation)
                            .graphicsLayer {
                                translationY = (1f - logoEnterAnimation) * -enterOffsetPx
                            },
                        contentAlignment = Alignment.Center,
                    ) {
                        val infiniteTransition = rememberInfiniteTransition(label = "StarRingRotation")
                        val rotationAngle by infiniteTransition.animateFloat(
                            initialValue = 0f,
                            targetValue = 360f,
                            animationSpec = AuthAnimSpec.starRingRotationAnimation,
                            label = "StarRingRotationAngle",
                        )

                        StarRingCanvas(
                            modifier =
                            Modifier
                                .fillMaxSize()
                                .graphicsLayer { rotationZ = rotationAngle },
                            enableAnimation = false,
                        )
                    }
                }

                // 4. [应用标语] 文本 - 位置: (50%, ~45%)
                Box(
                    modifier =
                    Modifier
                        .fillMaxWidth()
                        .fillMaxHeight(0.14f),
                    // 45% - 35% = 10%的空间
                    contentAlignment = Alignment.Center,
                ) {
                    GymBroTypeWriter(
                        text = UiText.DynamicString(if (isCodeSent) "输入验证码" else "快速安全登录"),
                        textColor = contentColorAnimated.copy(alpha = 0.8f),
                        fontSize = 18.sp,
                        modifier =
                        Modifier
                            .alpha(logoEnterAnimation)
                            .graphicsLayer {
                                translationY = (1f - logoEnterAnimation) * -enterOffsetPx
                            },
                    )
                }

                // 5. [交互表单区域] - 位置: y轴55%至85%
                Box(
                    modifier =
                    Modifier
                        .fillMaxWidth()
                        .weight(1f),
                    // 占用剩余空间
                    contentAlignment = Alignment.TopCenter,
                ) {
                    // Phone Login Form - 现代化组件
                    if (isLoading) {
                        CircularProgressIndicator(color = contentColorAnimated)
                    } else {
                        Column(
                            modifier =
                            Modifier
                                .fillMaxWidth()
                                .padding(horizontal = Tokens.Spacing.Large),
                            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Large),
                        ) {
                            if (!isCodeSent) {
                                // Phone Number Input - 升级到SmartInput
                                phoneField(
                                    phoneNumber = phoneNumber,
                                    onPhoneNumberChange = { phoneNumber = it },
                                    modifier = Modifier.fillMaxWidth(),
                                )

                                // Send Code Button
                                GymBroButton(
                                    onClick = { isCodeSent = true },
                                    text = UiText.DynamicString("获取验证码"),
                                    modifier =
                                    Modifier
                                        .fillMaxWidth()
                                        .height(60.dp),
                                    enabled = phoneNumber.length >= 11,
                                    colors =
                                    GymBroButtonDefaults.colors(
                                        containerColor = Tokens.Color.CTAPrimary,
                                        contentColor = Tokens.Color.Gray950,
                                    ),
                                )

                                // Back Button
                                GymBroButton(
                                    onClick = onBackClick,
                                    text = UiText.DynamicString("返回登录"),
                                    modifier =
                                    Modifier
                                        .fillMaxWidth()
                                        .height(60.dp),
                                    colors =
                                    GymBroButtonDefaults.secondaryColors(),
                                )
                            } else {
                                // Verification Code Input
                                GymBroInputField(
                                    value = verificationCode,
                                    onValueChange = { verificationCode = it },
                                    label = { Text("验证码") },
                                    placeholder = "请输入验证码",
                                    modifier = Modifier.fillMaxWidth(),
                                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                )

                                // Login Button
                                GymBroButton(
                                    onClick = onLoginSuccess,
                                    text = UiText.DynamicString("登录"),
                                    modifier =
                                    Modifier
                                        .fillMaxWidth()
                                        .height(60.dp),
                                    enabled = verificationCode.length == 6,
                                    colors =
                                    GymBroButtonDefaults.colors(
                                        containerColor = Tokens.Color.CTAPrimary,
                                        contentColor = Tokens.Color.Gray950,
                                    ),
                                )

                                // Resend Code Button
                                GymBroButton(
                                    onClick = { /* 重新发送验证码逻辑 */ },
                                    text = UiText.DynamicString("重新发送验证码"),
                                    modifier =
                                    Modifier
                                        .fillMaxWidth()
                                        .height(60.dp),
                                    colors =
                                    GymBroButtonDefaults.outlinedColors(),
                                )

                                // Back Button
                                GymBroButton(
                                    onClick = onBackClick,
                                    text = UiText.DynamicString("重新输入手机号"),
                                    modifier =
                                    Modifier
                                        .fillMaxWidth()
                                        .height(60.dp),
                                    colors =
                                    GymBroButtonDefaults.secondaryColors(),
                                )
                            }
                        }
                    }
                }
            }

            // [法律声明] - 位置: 底部中心 (50%, ~95%)
            Text(
                text = "继续即表示您同意 条款 和 隐私政策",
                color = contentColorAnimated.copy(alpha = 0.6f),
                fontSize = 12.sp,
                textAlign = TextAlign.Center,
                modifier =
                Modifier
                    .align(Alignment.BottomCenter)
                    .padding(
                        bottom = Tokens.Spacing.Medium,
                        start = Tokens.Spacing.Large,
                        end = Tokens.Spacing.Large,
                    ),
            )
        }
    }
}

// === @GymBroPreview 预览组件 ===

@GymBroPreview
@Composable
private fun PhoneLoginScreenPreview() {
    GymBroTheme {
        PhoneLoginScreenContent(
            isLoading = false,
            onBackClick = {},
            onLoginSuccess = {},
        )
    }
}

@GymBroPreview
@Composable
private fun PhoneLoginScreenLightPreview() {
    GymBroTheme(darkTheme = false) {
        PhoneLoginScreenContent(
            isLoading = false,
            onBackClick = {},
            onLoginSuccess = {},
        )
    }
}

@GymBroPreview
@Composable
private fun PhoneLoginScreenCodeSentPreview() {
    GymBroTheme {
        var phoneNumber by remember { mutableStateOf("13800138000") }
        var verificationCode by remember { mutableStateOf("") }
        var isCodeSent by remember { mutableStateOf(true) }

        PhoneLoginScreenContent(
            isLoading = false,
            onBackClick = {},
            onLoginSuccess = {},
        )
    }
}

@GymBroPreview
@Composable
private fun PhoneLoginScreenLoadingPreview() {
    GymBroTheme {
        PhoneLoginScreenContent(
            isLoading = true,
            onBackClick = {},
            onLoginSuccess = {},
        )
    }
}

```

File: D:/GymBro/GymBro/features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/logging/ThinkingBoxLogTree.kt
```kotlin
package com.example.gymbro.features.thinkingbox.logging

import android.util.Log
import timber.log.Timber
import kotlinx.coroutines.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong

/**
 * ThinkingBox 专用日志树
 *
 * 实现日志瘦身，过滤低优先级的 token 级别日志
 */
class ThinkingBoxLogTree : Timber.DebugTree() {

    companion object {
        // ThinkingBox 相关的标签前缀
        private val THINKING_TAGS = setOf(
            "THINKINGBOX",
            "THINKINGBOX-RAW",
            "THINKINGBOX-MONITOR",
            "THINKINGBOX-HISTORY",
            "XML-PARSER",
            "ThinkingBoxFacade",
            "ThinkingMLGuardrail",
            "TB-RAW",
            "TB-FILTER",
            "TB-SEM",
            "TB-MAP",
            "TB-EVT",
            "TB-STATE",
            "TB-DB",
            "TB-UI",
            "AI-STREAM",
            "AI-RAW",
            // 🔥 新增：Coach 模块调试标签
            "REDUCER-DEBUG",
            "VIEWMODEL-DEBUG",
            "EFFECT-DEBUG",
            "USECASE-DEBUG",
            "ChatSessio...addMessage", // Repository 层日志标签
            "AiCoachViewModel",
            "MessagingReducerHandler",
            "SessionEffectHandler",
            "ChatSessionManagementUseCase",
            "ChatSessionRepositoryImpl",
            // 🔥 新增：Template保存调试标签
            "BUTTON-SAVE",
            "BUTTON-TEST",
            "UI-TEST",
            "UI-FEEDBACK",
            "TemplateEditReducer",
            "TemplateEditViewModel",
            "TemplateSaver",
            "TemplateScreen",
            "TemplateViewModel",
            "TemplateEffectHandler",
        )

        // 高频日志标签，需要特殊处理
        private val HIGH_FREQUENCY_TAGS = setOf(
            "THINKINGBOX-RAW",
            "XML-PARSER",
            "TB-RAW",
            "AI-STREAM",
            "AI-RAW",
        )
    }

    override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
        // 🔥 日志聚合处理 - 支持所有高频标签和优先级
        when {
            // 1. TB-RAW 高频日志聚合 (所有优先级)
            tag == "TB-RAW" -> {
                LogAggregatorManager.getAggregator("TB-RAW").append(message)
                return
            }

            // 2. AI-STREAM 高频日志聚合 (所有优先级，包括ERROR)
            tag == "AI-STREAM" -> {
                LogAggregatorManager.getAggregator("AI-STREAM").append(message)
                return
            }

            // 3. AI-RAW 高频日志聚合
            tag == "AI-RAW" -> {
                LogAggregatorManager.getAggregator("AI-RAW").append(message)
                return
            }

            // 4. THINKINGBOX-RAW 高频日志聚合
            tag == "THINKINGBOX-RAW" -> {
                LogAggregatorManager.getAggregator("THINKINGBOX-RAW").append(message)
                return
            }

            // 5. 其他TB相关高频标签聚合
            isHighFrequencyTag(tag) && (priority == Log.DEBUG || priority == Log.VERBOSE) -> {
                LogAggregatorManager.getAggregator(tag ?: "UNKNOWN").append(message)
                return
            }

            // 6. 非 ThinkingBox 相关的日志，只显示 INFO 及以上
            !isThinkingBoxTag(tag) && priority < Log.INFO -> return

            // 7. Token 级别的日志特殊处理
            isTokenLevelLog(message) && priority == Log.DEBUG -> {
                // 转换为 VERBOSE 级别，默认过滤
                super.log(Log.VERBOSE, tag, message, t)
                return
            }
        }

        // 其他日志正常输出
        super.log(priority, tag, message, t)
    }

    /**
     * 检查是否为 ThinkingBox 相关标签
     */
    private fun isThinkingBoxTag(tag: String?): Boolean {
        if (tag == null) return false
        return THINKING_TAGS.any { thinkingTag ->
            tag.startsWith(thinkingTag, ignoreCase = true)
        }
    }

    /**
     * 检查是否为高频日志标签
     */
    private fun isHighFrequencyTag(tag: String?): Boolean {
        if (tag == null) return false
        return HIGH_FREQUENCY_TAGS.any { highFreqTag ->
            tag.startsWith(highFreqTag, ignoreCase = true)
        }
    }

    /**
     * 检查是否为 Token 级别的日志
     */
    private fun isTokenLevelLog(message: String): Boolean {
        return message.contains("token", ignoreCase = true) ||
            message.contains("收到语义事件", ignoreCase = true) ||
            message.contains("发送内容", ignoreCase = true) ||
            message.contains("解析事件", ignoreCase = true)
    }

    /**
     * 检查是否为 DEBUG 构建
     */
    private fun isDebugBuild(): Boolean {
        return try {
            // 这里可以根据实际的 BuildConfig 来判断
            // 暂时返回 true，在生产环境中应该返回 BuildConfig.DEBUG
            true
        } catch (e: Exception) {
            false
        }
    }

    override fun createStackElementTag(element: StackTraceElement): String? {
        // 为 ThinkingBox 相关的类添加特殊标签前缀
        val className = element.className
        return when {
            className.contains("thinkingbox", ignoreCase = true) -> {
                "THINKINGBOX-${super.createStackElementTag(element)}"
            }
            else -> super.createStackElementTag(element)
        }
    }
}

/**
 * ThinkingBox 日志配置工具
 */
object ThinkingBoxLogConfig {

    /**
     * 配置 ThinkingBox 日志系统 (v2.0 - 支持日志聚合)
     */
    fun configure() {
        // 移除默认的日志树
        Timber.uprootAll()

        // 植入 ThinkingBox 专用日志树
        Timber.plant(ThinkingBoxLogTree())

        // 🔥 修复递归调用：直接使用Android Log
        android.util.Log.i("ThinkingBoxLogConfig", "ThinkingBox 日志系统已配置 (支持TB-RAW/AI-STREAM聚合)")
    }

    /**
     * 强制刷新所有日志聚合器
     */
    fun flushAllAggregators() {
        LogAggregatorManager.cleanup()
        // 🔥 修复递归调用：直接使用Android Log
        android.util.Log.d("ThinkingBoxLogConfig", "所有日志聚合器已刷新")
    }

    /**
     * 启用详细日志（调试模式）
     */
    fun enableVerboseLogging() {
        Timber.uprootAll()
        Timber.plant(object : Timber.DebugTree() {
            override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
                // 调试模式下显示所有日志
                super.log(priority, tag, message, t)
            }
        })

        android.util.Log.d("ThinkingBoxLogConfig", "ThinkingBox 详细日志已启用")
    }

    /**
     * 启用生产模式日志（只显示重要信息）
     */
    fun enableProductionLogging() {
        Timber.uprootAll()
        Timber.plant(object : Timber.DebugTree() {
            override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
                // 生产模式只显示 INFO 及以上级别
                if (priority >= Log.INFO) {
                    super.log(priority, tag, message, t)
                }
            }
        })

        android.util.Log.i("ThinkingBoxLogConfig", "ThinkingBox 生产模式日志已启用")
    }

    /**
     * 获取日志统计信息
     */
    fun getLogStats(): LogStats {
        // 这里可以实现日志统计功能
        return LogStats(
            totalLogs = 0,
            debugLogs = 0,
            infoLogs = 0,
            warningLogs = 0,
            errorLogs = 0,
        )
    }
}

/**
 * 日志统计数据类
 */
data class LogStats(
    val totalLogs: Int,
    val debugLogs: Int,
    val infoLogs: Int,
    val warningLogs: Int,
    val errorLogs: Int,
) {
    override fun toString(): String {
        return "LogStats(total=$totalLogs, debug=$debugLogs, info=$infoLogs, warn=$warningLogs, error=$errorLogs)"
    }
}

/**
 * 日志聚合器 - 实现≥200 token才落一条日志的机制
 *
 * 🔥 核心功能：
 * - TB-RAW: ≥200 token 或 ≥1000ms 触发聚合输出
 * - AI-STREAM: ≥50条消息 或 ≥2000ms 触发聚合输出 (频率较低)
 * - AI-RAW: ≥100 token 或 ≥1500ms 触发聚合输出
 */
class LogAggregator(
    private val tag: String,
    private val tokenThreshold: Int = 200,
    private val timeThresholdMs: Long = 1000L,
    private val messageCountThreshold: Int = 50  // 新增：消息数量阈值
) {
    private val buffer = StringBuilder()
    private val tokenCount = AtomicLong(0)
    private val messageCount = AtomicLong(0)  // 新增：消息计数器
    private var lastFlushTime = System.currentTimeMillis()
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    /**
     * 添加日志内容
     */
    fun append(message: String) {
        synchronized(buffer) {
            buffer.append(message).append(" ")

            // 简单token计数：按空格分词
            val tokens = message.split("\\s+".toRegex()).size
            val currentTokens = tokenCount.addAndGet(tokens.toLong())
            val currentMessages = messageCount.incrementAndGet()
            val currentTime = System.currentTimeMillis()

            // 检查是否需要刷新 - 支持多种触发条件
            if (currentTokens >= tokenThreshold ||
                currentMessages >= messageCountThreshold ||
                (currentTime - lastFlushTime) >= timeThresholdMs) {
                flush()
            }
        }
    }

    /**
     * 强制刷新缓冲区
     */
    fun flush() {
        synchronized(buffer) {
            if (buffer.isNotEmpty()) {
                val content = buffer.toString().trim()
                val tokens = tokenCount.get()
                val messages = messageCount.get()

                // 🔥 修复递归调用：直接使用Android Log，避免通过Timber造成递归
                android.util.Log.i("$tag-AGGREGATED", "🔍 [聚合] ${messages}条消息/${tokens}个token: ${content.take(200)}${if(content.length > 200) "..." else ""}")

                // 清空缓冲区
                buffer.clear()
                tokenCount.set(0)
                messageCount.set(0)
                lastFlushTime = System.currentTimeMillis()
            }
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        flush()
        scope.cancel()
    }
}

/**
 * 日志聚合器管理器
 */
object LogAggregatorManager {
    private val aggregators = ConcurrentHashMap<String, LogAggregator>()

    /**
     * 获取或创建聚合器 - 根据标签类型使用不同配置
     */
    fun getAggregator(tag: String): LogAggregator {
        return aggregators.computeIfAbsent(tag) { createAggregatorForTag(it) }
    }

    /**
     * 根据标签类型创建合适的聚合器
     */
    private fun createAggregatorForTag(tag: String): LogAggregator {
        return when (tag) {
            "TB-RAW" -> LogAggregator(
                tag = tag,
                tokenThreshold = 200,
                timeThresholdMs = 1000L,
                messageCountThreshold = 100
            )
            "AI-STREAM" -> LogAggregator(
                tag = tag,
                tokenThreshold = 100,
                timeThresholdMs = 2000L,
                messageCountThreshold = 20  // AI-STREAM频率较低，20条消息就聚合
            )
            "AI-RAW" -> LogAggregator(
                tag = tag,
                tokenThreshold = 150,
                timeThresholdMs = 1500L,
                messageCountThreshold = 50
            )
            "THINKINGBOX-RAW" -> LogAggregator(
                tag = tag,
                tokenThreshold = 200,
                timeThresholdMs = 1000L,
                messageCountThreshold = 80
            )
            else -> LogAggregator(
                tag = tag,
                tokenThreshold = 200,
                timeThresholdMs = 1000L,
                messageCountThreshold = 50
            )
        }
    }

    /**
     * 清理所有聚合器
     */
    fun cleanup() {
        aggregators.values.forEach { it.cleanup() }
        aggregators.clear()
    }
}

```

File: D:/GymBro/GymBro/core/src/main/kotlin/com/example/gymbro/core/logging/LoggingConfig.kt
```kotlin
package com.example.gymbro.core.logging

import android.util.Log
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 【重构】统一日志配置管理器
 *
 * 功能：
 * - 模块级别的日志控制
 * - 运行时动态调整日志级别
 * - 生产环境和开发环境的不同策略
 * - 性能优化的日志过滤
 */
@Singleton
class LoggingConfig
    @Inject
    constructor() {
        /**
         * 模块日志配置
         */
        data class ModuleLogConfig(
            val enabled: Boolean = true,
            val minLevel: Int = Log.DEBUG,
            val tags: Set<String> = emptySet(),
            val sampleRate: Int = 1, // 采样率：1=全量，10=每10条记录1条
        )

        /**
         * 环境类型
         */
        enum class Environment {
            DEVELOPMENT,
            STAGING,
            PRODUCTION,
        }

        // 当前环境
        @Volatile
        private var currentEnvironment = Environment.DEVELOPMENT

        // 模块配置映射
        private val moduleConfigs = ConcurrentHashMap<String, ModuleLogConfig>()

        // 全局日志开关
        @Volatile
        private var globalEnabled = true

        init {
            setupDefaultConfigs()
        }

        /**
         * 设置默认配置
         */
        private fun setupDefaultConfigs() {
            // ThinkingBox 模块配置
            moduleConfigs[MODULE_THINKING_BOX] =
                ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.INFO, // 🔥 默认只显示 INFO 及以上
                    tags = setOf("TB-ERROR", "TB-STATE", "TB-UI"), // 只保留关键标签
                    sampleRate = 1,
                )

            // Coach 模块配置
            moduleConfigs[MODULE_COACH] =
                ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.DEBUG,
                    tags = setOf("COACH-AI", "COACH-UI", "COACH-ERROR"),
                    sampleRate = 1,
                )

            // Workout 模块配置
            moduleConfigs[MODULE_WORKOUT] =
                ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.DEBUG,
                    tags = setOf("WORKOUT-SESSION", "WORKOUT-ERROR"),
                    sampleRate = 1,
                )

            // Core 模块配置
            moduleConfigs[MODULE_CORE] =
                ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.INFO,
                    tags = setOf("NETWORK", "DATABASE", "AUTH", "ERROR", "TOKEN-FLOW"), // 🔥 添加TOKEN-FLOW支持
                    sampleRate = 1,
                )
        }

        /**
         * 设置环境
         */
        fun setEnvironment(environment: Environment) {
            currentEnvironment = environment
            applyEnvironmentSettings()
        }

        /**
         * 应用环境设置
         */
        private fun applyEnvironmentSettings() {
            when (currentEnvironment) {
                Environment.DEVELOPMENT -> {
                    // 开发环境：适度的日志输出
                    updateModuleConfig(
                        MODULE_THINKING_BOX,
                        ModuleLogConfig(
                            enabled = true,
                            minLevel = Log.INFO, // 🔥 ThinkingBox 只显示重要信息
                            tags = setOf("TB-ERROR", "TB-STATE", "TB-UI"),
                            sampleRate = 1,
                        ),
                    )
                }

                Environment.STAGING -> {
                    // 测试环境：更多日志用于调试
                    updateModuleConfig(
                        MODULE_THINKING_BOX,
                        ModuleLogConfig(
                            enabled = true,
                            minLevel = Log.DEBUG,
                            tags = setOf("TB-ERROR", "TB-STATE", "TB-UI", "TB-MERMAID"),
                            sampleRate = 5, // 采样输出
                        ),
                    )
                }

                Environment.PRODUCTION -> {
                    // 生产环境：只记录错误和关键信息
                    moduleConfigs.forEach { (module, _) ->
                        updateModuleConfig(
                            module,
                            ModuleLogConfig(
                                enabled = true,
                                minLevel = Log.WARN,
                                tags = setOf("ERROR", "CRASH"),
                                sampleRate = 1,
                            ),
                        )
                    }
                }
            }
        }

        /**
         * 更新模块配置
         */
        fun updateModuleConfig(
            module: String,
            config: ModuleLogConfig,
        ) {
            moduleConfigs[module] = config
        }

        /**
         * 获取模块配置
         */
        fun getModuleConfig(module: String): ModuleLogConfig? = moduleConfigs[module]

        /**
         * 检查是否应该记录日志
         */
        fun shouldLog(
            module: String,
            tag: String?,
            priority: Int,
        ): Boolean {
            if (!globalEnabled) return false

            val config = moduleConfigs[module] ?: return false
            if (!config.enabled) return false
            if (priority < config.minLevel) return false

            // 检查标签过滤
            if (config.tags.isNotEmpty() && tag != null) {
                val shouldLogByTag =
                    config.tags.any { allowedTag ->
                        tag.contains(allowedTag, ignoreCase = true)
                    }
                if (!shouldLogByTag) return false
            }

            return true
        }

        /**
         * 全局开关控制
         */
        fun setGlobalEnabled(enabled: Boolean) {
            globalEnabled = enabled
        }

        /**
         * 获取当前环境
         */
        fun getCurrentEnvironment(): Environment = currentEnvironment

        /**
         * 🔥 ThinkingBox 专用：关闭详细日志
         */
        fun disableThinkingBoxVerboseLogs() {
            updateModuleConfig(
                MODULE_THINKING_BOX,
                ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.WARN, // 只显示警告和错误
                    tags = setOf("TB-ERROR"), // 只保留错误标签
                    sampleRate = 1,
                ),
            )
        }

        /**
         * 🔥 ThinkingBox 专用：启用调试模式
         */
        fun enableThinkingBoxDebugLogs() {
            updateModuleConfig(
                MODULE_THINKING_BOX,
                ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.DEBUG,
                    tags = setOf("TB-ERROR", "TB-STATE", "TB-UI", "TB-MERMAID", "TB-FINAL"),
                    sampleRate = 10, // 采样输出，避免刷屏
                ),
            )
        }

        /**
         * 🔥 TOKEN-FLOW 专用：启用流式响应调试模式
         */
        fun enableTokenFlowDebugLogs() {
            // Core 模块：启用 TOKEN-FLOW 调试
            updateModuleConfig(
                MODULE_CORE,
                ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.DEBUG,
                    tags = setOf("NETWORK", "DATABASE", "AUTH", "ERROR", "TOKEN-FLOW"),
                    sampleRate = 1, // 全量输出，用于调试流式响应
                ),
            )

            // ThinkingBox 模块：启用相关调试
            updateModuleConfig(
                MODULE_THINKING_BOX,
                ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.DEBUG,
                    tags = setOf("TB-ERROR", "TB-STATE", "TB-UI", "TB-RAW", "TOKEN-FLOW"),
                    sampleRate = 1, // 全量输出
                ),
            )

            // Coach 模块：启用AI相关调试
            updateModuleConfig(
                MODULE_COACH,
                ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.DEBUG,
                    tags = setOf("COACH-AI", "COACH-UI", "COACH-ERROR", "TOKEN-FLOW"),
                    sampleRate = 1,
                ),
            )
        }

        /**
         * 🔥 TOKEN-FLOW 专用：关闭流式响应调试模式
         */
        fun disableTokenFlowDebugLogs() {
            // 恢复默认配置
            setupDefaultConfigs()
        }

        /**
         * 🔥 TOKEN-FLOW 专用：静音模式 - 只显示TOKEN-FLOW和错误日志
         */
        fun enableTokenFlowOnlyMode() {
            // Core 模块：只显示 TOKEN-FLOW 和错误
            updateModuleConfig(
                MODULE_CORE,
                ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.DEBUG,
                    tags = setOf("TOKEN-FLOW", "ERROR"),
                    sampleRate = 1,
                ),
            )

            // Coach 模块：只显示 TOKEN-FLOW 和错误
            updateModuleConfig(
                MODULE_COACH,
                ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.DEBUG,
                    tags = setOf("TOKEN-FLOW", "COACH-ERROR"),
                    sampleRate = 1,
                ),
            )

            // ThinkingBox 模块：只显示错误
            updateModuleConfig(
                MODULE_THINKING_BOX,
                ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.ERROR,
                    tags = setOf("TB-ERROR"),
                    sampleRate = 1,
                ),
            )

            // Workout 模块：只显示错误
            updateModuleConfig(
                MODULE_WORKOUT,
                ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.ERROR,
                    tags = setOf("WORKOUT-ERROR"),
                    sampleRate = 1,
                ),
            )
        }

        companion object {
            // 模块常量
            const val MODULE_THINKING_BOX = "ThinkingBox"
            const val MODULE_COACH = "Coach"
            const val MODULE_WORKOUT = "Workout"
            const val MODULE_CORE = "Core"
            const val MODULE_DESIGN_SYSTEM = "DesignSystem"

            // 标签常量
            object Tags {
                // ThinkingBox 标签
                const val TB_ERROR = "TB-ERROR"
                const val TB_STATE = "TB-STATE"
                const val TB_UI = "TB-UI"
                const val TB_MERMAID = "TB-MERMAID"
                const val TB_FINAL = "TB-FINAL"
                const val TB_RAW = "TB-RAW"

                // Coach 标签
                const val COACH_AI = "COACH-AI"
                const val COACH_UI = "COACH-UI"
                const val COACH_ERROR = "COACH-ERROR"

                // Core 标签
                const val NETWORK = "NETWORK"
                const val DATABASE = "DATABASE"
                const val AUTH = "AUTH"
                const val ERROR = "ERROR"
            }
        }
    }

/**
 * 🔥 【重构】模块感知的 Timber Tree
 *
 * 根据模块配置自动过滤日志
 */
class ModuleAwareTree(
    private val loggingConfig: LoggingConfig,
) : Timber.DebugTree() {
    private val sampleCounters = ConcurrentHashMap<String, Int>()

    override fun log(
        priority: Int,
        tag: String?,
        message: String,
        t: Throwable?,
    ) {
        val module = determineModule(tag)

        // 检查是否应该记录
        if (!loggingConfig.shouldLog(module, tag, priority)) {
            return
        }

        // 采样控制
        val config = loggingConfig.getModuleConfig(module)
        if (config != null && config.sampleRate > 1) {
            val key = "$module-$tag"
            val count = sampleCounters.merge(key, 1) { old, _ -> old + 1 } ?: 1
            if (count % config.sampleRate != 0) {
                return
            }
        }

        super.log(priority, tag, message, t)
    }

    /**
     * 根据标签确定模块
     */
    private fun determineModule(tag: String?): String =
        when {
            tag?.startsWith("TB-") == true -> LoggingConfig.MODULE_THINKING_BOX
            tag?.startsWith("COACH-") == true -> LoggingConfig.MODULE_COACH
            tag?.startsWith("WORKOUT-") == true -> LoggingConfig.MODULE_WORKOUT
            tag?.contains("NETWORK") == true -> LoggingConfig.MODULE_CORE
            tag?.contains("DATABASE") == true -> LoggingConfig.MODULE_CORE
            tag?.contains("AUTH") == true -> LoggingConfig.MODULE_CORE
            else -> LoggingConfig.MODULE_CORE
        }
}

```

File: D:/GymBro/GymBro/features/coach/src/main/kotlin/com/example/gymbro/features/coach/history/internal/components/ConversationManagementDialog.kt
```kotlin
package com.example.gymbro.features.coach.history.internal.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.features.coach.history.HistoryContract
import com.example.gymbro.features.coach.shared.model.Conversation

/**
 * 对话管理对话框
 *
 * 功能特性：
 * - 重命名对话
 * - 删除对话
 * - 导出对话
 * - 批量管理操作
 */
@Composable
internal fun ConversationManagementDialog(
    conversation: Conversation,
    onDismiss: () -> Unit,
    onRename: (String) -> Unit,
    onDelete: () -> Unit,
    onExport: (HistoryContract.ExportFormat) -> Unit,
    modifier: Modifier = Modifier,
) {
    var showRenameDialog by remember { mutableStateOf(false) }
    var showDeleteConfirmation by remember { mutableStateOf(false) }
    var showExportDialog by remember { mutableStateOf(false) }

    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = Tokens.Elevation.Medium
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(Tokens.Spacing.Large),
                verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium)
            ) {
                // 标题
                Text(
                    text = "管理对话",
                    style = MaterialTheme.typography.headlineSmall.copy(
                        fontWeight = FontWeight.SemiBold
                    ),
                    color = MaterialTheme.colorScheme.onSurface
                )

                // 对话信息
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(Tokens.Spacing.Medium),
                        verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small)
                    ) {
                        Text(
                            text = conversation.displayTitle,
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        Text(
                            text = "${conversation.messageCount} 条消息 • ${conversation.getRelativeTime()}",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }

                // 操作按钮
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small)
                ) {
                    // 重命名
                    ManagementActionItem(
                        icon = Icons.Default.Edit,
                        title = "重命名对话",
                        description = "修改对话标题",
                        onClick = { showRenameDialog = true }
                    )

                    // 导出
                    ManagementActionItem(
                        icon = Icons.Default.FileDownload,
                        title = "导出对话",
                        description = "保存对话记录到文件",
                        onClick = { showExportDialog = true }
                    )

                    // 删除
                    ManagementActionItem(
                        icon = Icons.Default.Delete,
                        title = "删除对话",
                        description = "永久删除此对话",
                        onClick = { showDeleteConfirmation = true },
                        isDestructive = true
                    )
                }

                // 取消按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("取消")
                    }
                }
            }
        }
    }

    // 重命名对话框
    if (showRenameDialog) {
        RenameConversationDialog(
            currentTitle = conversation.title,
            onConfirm = { newTitle ->
                onRename(newTitle)
                showRenameDialog = false
                onDismiss()
            },
            onDismiss = { showRenameDialog = false }
        )
    }

    // 删除确认对话框
    if (showDeleteConfirmation) {
        DeleteConfirmationDialog(
            conversationTitle = conversation.displayTitle,
            onConfirm = {
                onDelete()
                showDeleteConfirmation = false
                onDismiss()
            },
            onDismiss = { showDeleteConfirmation = false }
        )
    }

    // 导出格式选择对话框
    if (showExportDialog) {
        ExportFormatDialog(
            onFormatSelected = { format ->
                onExport(format)
                showExportDialog = false
                onDismiss()
            },
            onDismiss = { showExportDialog = false }
        )
    }
}

/**
 * 管理操作项组件
 */
@Composable
private fun ManagementActionItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    title: String,
    description: String,
    onClick: () -> Unit,
    isDestructive: Boolean = false,
    modifier: Modifier = Modifier,
) {
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .selectable(
                selected = false,
                onClick = onClick
            ),
        shape = MaterialTheme.shapes.medium,
        color = MaterialTheme.colorScheme.surface
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Medium),
            horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                tint = if (isDestructive) {
                    MaterialTheme.colorScheme.error
                } else {
                    MaterialTheme.colorScheme.onSurfaceVariant
                },
                modifier = Modifier.size(24.dp)
            )

            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(2.dp)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyLarge,
                    color = if (isDestructive) {
                        MaterialTheme.colorScheme.error
                    } else {
                        MaterialTheme.colorScheme.onSurface
                    }
                )
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.size(20.dp)
            )
        }
    }
}

/**
 * 重命名对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun RenameConversationDialog(
    currentTitle: String,
    onConfirm: (String) -> Unit,
    onDismiss: () -> Unit,
) {
    var newTitle by remember { mutableStateOf(currentTitle) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("重命名对话") },
        text = {
            OutlinedTextField(
                value = newTitle,
                onValueChange = { newTitle = it },
                label = { Text("对话标题") },
                singleLine = true,
                modifier = Modifier.fillMaxWidth()
            )
        },
        confirmButton = {
            TextButton(
                onClick = { onConfirm(newTitle.trim()) },
                enabled = newTitle.trim().isNotBlank() && newTitle.trim() != currentTitle
            ) {
                Text("确认")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 删除确认对话框
 */
@Composable
private fun DeleteConfirmationDialog(
    conversationTitle: String,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit,
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        icon = {
            Icon(
                imageVector = Icons.Default.Warning,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.error
            )
        },
        title = { Text("删除对话") },
        text = {
            Text("确定要删除对话「$conversationTitle」吗？此操作无法撤销。")
        },
        confirmButton = {
            Button(
                onClick = onConfirm,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.error
                )
            ) {
                Text("删除")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 导出格式选择对话框
 */
@Composable
private fun ExportFormatDialog(
    onFormatSelected: (HistoryContract.ExportFormat) -> Unit,
    onDismiss: () -> Unit,
) {
    var selectedFormat by remember { mutableStateOf(HistoryContract.ExportFormat.MARKDOWN) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("选择导出格式") },
        text = {
            Column {
                HistoryContract.ExportFormat.values().forEach { format ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .selectable(
                                selected = selectedFormat == format,
                                onClick = { selectedFormat = format }
                            )
                            .padding(vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = selectedFormat == format,
                            onClick = { selectedFormat = format }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Column {
                            Text(
                                text = when (format) {
                                    HistoryContract.ExportFormat.MARKDOWN -> "Markdown (.md)"
                                    HistoryContract.ExportFormat.JSON -> "JSON (.json)"
                                    HistoryContract.ExportFormat.TXT -> "纯文本 (.txt)"
                                },
                                style = MaterialTheme.typography.bodyLarge
                            )
                            Text(
                                text = when (format) {
                                    HistoryContract.ExportFormat.MARKDOWN -> "保留格式的富文本"
                                    HistoryContract.ExportFormat.JSON -> "结构化数据格式"
                                    HistoryContract.ExportFormat.TXT -> "简单的纯文本格式"
                                },
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = { onFormatSelected(selectedFormat) }) {
                Text("导出")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/resources/testSkeletons/coach.kt.template
```template
package {{PACKAGE}}

import app.cash.turbine.test
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.compose.ui.test.onNodeWithText
import androidx.compose.ui.test.performClick
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.onNodeWithContentDescription
import androidx.test.ext.junit.runners.AndroidJUnit4
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.flow.flowOf
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config
import org.robolectric.annotation.LooperMode
import kotlin.test.*
import io.mockk.*
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.design_system.theme.GymBroTheme
import com.example.gymbro.domain.model.coach.CoachMessage
import com.example.gymbro.domain.usecase.coach.AiCoachUseCase
import com.example.gymbro.domain.repository.coach.AICoachRepository
import kotlinx.datetime.Instant
import timber.log.Timber

/**
 * Generated by genTest for GymBro Coach Module
 * ✔️ 专门针对AI教练功能的测试模板
 * ✔️ 支持Flow测试、Mock对象、UI测试
 * ✔️ 遵循Clean Architecture + MVVM原则
 * ✔️ 使用ModernResult统一错误处理
 * ✔️ 包含AI对话、建议生成等特有测试场景
 */
@OptIn(ExperimentalCoroutinesApi::class)
@RunWith(AndroidJUnit4::class)
@Config(sdk = [31])
@LooperMode(LooperMode.Mode.PAUSED)
class {{CLASS}}Test {

    @get:Rule
    val composeTestRule = createComposeRule()

    private val testScope = TestScope()
    private val mockRepository = mockk<AICoachRepository>()
    private val mockUseCase = mockk<AiCoachUseCase>()

    @Test
    fun `{{CLASS}} should handle AI message flow correctly`() = testScope.runTest {
        // Given - 准备AI消息数据
        val userMessage = "帮我制定训练计划"
        val aiResponse = CoachMessage.AiMessage(
            id = "test-id",
            content = "我来为您制定个性化训练计划",
            timestamp = Instant.parse("2025-01-30T10:00:00Z"),
            messageType = CoachMessage.MessageType.SUGGESTION
        )

        // Mock UseCase返回成功结果
        coEvery { mockUseCase.sendMessage(userMessage) } returns flowOf(
            ModernResult.Loading,
            ModernResult.Success(aiResponse)
        )

        // When & Then - 测试消息流
        mockUseCase.sendMessage(userMessage).test {
            assertEquals(ModernResult.Loading, awaitItem())
            val successResult = awaitItem() as ModernResult.Success
            assertEquals(aiResponse, successResult.data)
            awaitComplete()
        }

        // 验证UseCase被正确调用
        coVerify { mockUseCase.sendMessage(userMessage) }
    }

    @Test
    fun `{{CLASS}} should handle AI service errors gracefully`() = testScope.runTest {
        // Given - 准备错误场景
        val userMessage = "测试消息"
        val errorMessage = "AI服务暂时不可用"

        coEvery { mockUseCase.sendMessage(userMessage) } returns flowOf(
            ModernResult.Loading,
            ModernResult.Error(
                com.example.gymbro.core.error.types.ModernDataError(
                    operation = "sendMessage",
                    errorType = com.example.gymbro.core.error.types.GlobalErrorType.AI_SERVICE_ERROR
                )
            )
        )

        // When & Then - 测试错误处理
        mockUseCase.sendMessage(userMessage).test {
            assertEquals(ModernResult.Loading, awaitItem())
            val errorResult = awaitItem() as ModernResult.Error
            assertEquals(com.example.gymbro.core.error.types.GlobalErrorType.AI_SERVICE_ERROR, errorResult.error.errorType)
            awaitComplete()
        }
    }

    @Test
    fun `{{CLASS}} UI should display chat messages correctly`() {
        // Given - 准备UI状态
        val messages = listOf(
            CoachMessage.UserMessage(
                id = "user-1",
                content = "你好",
                timestamp = Instant.parse("2025-01-30T10:00:00Z")
            ),
            CoachMessage.AiMessage(
                id = "ai-1", 
                content = "您好！我是您的AI健身教练",
                timestamp = Instant.parse("2025-01-30T10:01:00Z"),
                messageType = CoachMessage.MessageType.GREETING
            )
        )

        // When - 渲染UI
        composeTestRule.setContent {
            GymBroTheme {
                // TODO: 根据实际{{CLASS}}调整参数
                {{CLASS}}(
                    messages = messages,
                    isLoading = false,
                    onSendMessage = {},
                    onClearChat = {}
                )
            }
        }

        // Then - 验证消息显示
        composeTestRule.onNodeWithText("你好").assertIsDisplayed()
        composeTestRule.onNodeWithText("您好！我是您的AI健身教练").assertIsDisplayed()
    }

    @Test
    fun `{{CLASS}} should handle loading state correctly`() {
        // Given & When
        composeTestRule.setContent {
            GymBroTheme {
                {{CLASS}}(
                    messages = emptyList(),
                    isLoading = true,
                    onSendMessage = {},
                    onClearChat = {}
                )
            }
        }

        // Then - 验证加载状态显示
        composeTestRule.onNodeWithContentDescription("AI正在思考中").assertIsDisplayed()
    }

    @Test
    fun `{{CLASS}} should handle user interactions correctly`() {
        // Given
        var messageSent = false
        var chatCleared = false

        // When
        composeTestRule.setContent {
            GymBroTheme {
                {{CLASS}}(
                    messages = emptyList(),
                    isLoading = false,
                    onSendMessage = { messageSent = true },
                    onClearChat = { chatCleared = true }
                )
            }
        }

        // Then - 测试发送消息交互
        composeTestRule.onNodeWithContentDescription("发送消息").performClick()
        assertTrue(messageSent, "应该触发发送消息回调")

        // 测试清除对话交互
        composeTestRule.onNodeWithContentDescription("清除对话").performClick()
        assertTrue(chatCleared, "应该触发清除对话回调")
    }

    @Test
    fun `{{CLASS}} should use UiText for localization`() {
        // Given
        val localizedText = UiText.StringResource(
            com.example.gymbro.design_system.R.string.coach_welcome_message
        )

        // When
        composeTestRule.setContent {
            GymBroTheme {
                {{CLASS}}(
                    welcomeText = localizedText,
                    messages = emptyList(),
                    isLoading = false,
                    onSendMessage = {},
                    onClearChat = {}
                )
            }
        }

        // Then - 验证本地化文本显示
        composeTestRule.onNodeWithText(localizedText.asString()).assertIsDisplayed()
    }

    @Test
    fun `{{CLASS}} should handle time display with kotlinx datetime`() {
        // Given
        val messageTime = Instant.parse("2025-01-30T10:00:00Z")
        val message = CoachMessage.UserMessage(
            id = "test-id",
            content = "测试消息",
            timestamp = messageTime
        )

        // When
        composeTestRule.setContent {
            GymBroTheme {
                {{CLASS}}(
                    messages = listOf(message),
                    isLoading = false,
                    onSendMessage = {},
                    onClearChat = {}
                )
            }
        }

        // Then - 验证时间格式显示（具体格式根据实际需求调整）
        composeTestRule.onNodeWithText("10:00").assertIsDisplayed()
    }

    @Test
    fun `{{CLASS}} should log interactions with Timber`() {
        // Given
        val testMessage = "测试日志记录"
        
        // When - 执行需要日志记录的操作
        // TODO: 根据实际{{CLASS}}的日志记录逻辑调整
        Timber.d("Coach interaction: $testMessage")
        
        // Then - 验证日志记录（在实际测试中可能需要Mock Timber）
        assertTrue(true, "日志记录功能应该正常工作")
    }

    // 清理Mock对象
    @org.junit.After
    fun tearDown() {
        clearAllMocks()
    }
}

// 强制性 @Preview 注解 - GymBro 项目要求
@androidx.compose.ui.tooling.preview.Preview(name = "Light", showBackground = true)
@androidx.compose.ui.tooling.preview.Preview(
    name = "Dark", 
    uiMode = android.content.res.Configuration.UI_MODE_NIGHT_YES, 
    showBackground = true
)
@androidx.compose.ui.tooling.preview.Preview(name = "Large Font", fontScale = 1.3f, showBackground = true)
@androidx.compose.runtime.Composable
fun {{CLASS}}Preview() {
    GymBroTheme {
        {{CLASS}}(
            messages = listOf(
                CoachMessage.UserMessage(
                    id = "preview-user",
                    content = "预览用户消息",
                    timestamp = Instant.parse("2025-01-30T10:00:00Z")
                ),
                CoachMessage.AiMessage(
                    id = "preview-ai",
                    content = "预览AI回复消息",
                    timestamp = Instant.parse("2025-01-30T10:01:00Z"),
                    messageType = CoachMessage.MessageType.SUGGESTION
                )
            ),
            isLoading = false,
            onSendMessage = {},
            onClearChat = {}
        )
    }
}

```

File: D:/GymBro/GymBro/features/workout/src/main/kotlin/com/example/gymbro/features/workout/plan/internal/components/CalendarJsonDialog.kt
```kotlin
package com.example.gymbro.features.workout.plan.internal.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.workoutColors
import com.example.gymbro.shared.models.workout.CalendarEntryData
import com.example.gymbro.shared.models.workout.PlanCalendarData
import com.example.gymbro.shared.models.workout.PlanCalendarInfo
import kotlinx.coroutines.delay
import kotlinx.serialization.json.Json

/**
 * Calendar JSON输出对话框
 *
 * 🎯 核心功能：显示和操作calendar.json数据
 * 基于08_Plan层改造设计.md的要求：
 * - 显示格式化的calendar.json数据
 * - 支持复制到剪贴板
 * - 支持分享功能
 * - 支持导出文件
 * - 遵循Function Call输出模式
 *
 * @param calendarData Calendar JSON数据
 * @param planName 计划名称
 * @param onDismiss 关闭对话框回调
 * @param onShare 分享回调
 * @param onExport 导出回调
 *
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0 (Plan层改造设计)
 */
@Composable
fun CalendarJsonDialog(
    calendarData: PlanCalendarData,
    planName: String,
    onDismiss: () -> Unit,
    onShare: () -> Unit = {},
    onExport: () -> Unit = {},
    modifier: Modifier = Modifier,
) {
    val clipboardManager = LocalClipboardManager.current
    val scrollState = rememberScrollState()

    // 格式化JSON数据
    val jsonString =
        remember(calendarData) {
            Json {
                prettyPrint = true
                ignoreUnknownKeys = true
            }.encodeToString(calendarData)
        }

    var showCopySuccess by remember { mutableStateOf(false) }

    Dialog(
        onDismissRequest = onDismiss,
        properties =
            DialogProperties(
                usePlatformDefaultWidth = false,
                dismissOnBackPress = true,
                dismissOnClickOutside = true,
            ),
    ) {
        Card(
            modifier =
                modifier
                    .fillMaxWidth(0.95f)
                    .fillMaxHeight(0.8f),
            colors =
                CardDefaults.cardColors(
                    containerColor = MaterialTheme.workoutColors.cardBackground,
                ),
        ) {
            Column(
                modifier = Modifier.fillMaxSize(),
            ) {
                // 标题栏
                Row(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Column {
                        Text(
                            text = "📅 Calendar JSON",
                            style = MaterialTheme.typography.titleLarge,
                            color = MaterialTheme.workoutColors.accentSecondary,
                        )
                        Text(
                            text = planName,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.workoutColors.accentSecondary,
                        )
                    }

                    IconButton(onClick = onDismiss) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "关闭",
                            tint = MaterialTheme.workoutColors.accentSecondary,
                        )
                    }
                }

                Divider(color = MaterialTheme.workoutColors.cardBorder)

                // 统计信息
                Row(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                    horizontalArrangement = Arrangement.spacedBy(16.dp),
                ) {
                    StatCard(
                        title = "日历条目",
                        value = calendarData.calendar_entries.size.toString(),
                        icon = Icons.Default.CalendarMonth,
                        modifier = Modifier.weight(1f),
                    )

                    StatCard(
                        title = "计划名称",
                        value = calendarData.plan_info.plan_name,
                        icon = Icons.Default.FitnessCenter,
                        modifier = Modifier.weight(1f),
                    )

                    StatCard(
                        title = "开始日期",
                        value = calendarData.calendar_entries.firstOrNull()?.date ?: "未设置",
                        icon = Icons.Default.DateRange,
                        modifier = Modifier.weight(1f),
                    )
                }

                // JSON内容
                Card(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .weight(1f)
                            .padding(horizontal = 16.dp),
                    colors =
                        CardDefaults.cardColors(
                            containerColor = MaterialTheme.workoutColors.cardBorder,
                        ),
                ) {
                    Column(
                        modifier = Modifier.fillMaxSize(),
                    ) {
                        Row(
                            modifier =
                                Modifier
                                    .fillMaxWidth()
                                    .padding(12.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            Text(
                                text = "JSON数据",
                                style = MaterialTheme.typography.titleSmall,
                                color = MaterialTheme.workoutColors.accentSecondary,
                            )

                            TextButton(
                                onClick = {
                                    clipboardManager.setText(AnnotatedString(jsonString))
                                    showCopySuccess = true
                                },
                            ) {
                                Icon(
                                    imageVector = Icons.Default.ContentCopy,
                                    contentDescription = null,
                                    modifier = Modifier.size(16.dp),
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                Text(
                                    text = if (showCopySuccess) "已复制" else "复制",
                                    style = MaterialTheme.typography.labelMedium,
                                )
                            }
                        }

                        Divider(color = MaterialTheme.workoutColors.cardBorder)

                        Text(
                            text = jsonString,
                            style =
                                MaterialTheme.typography.bodySmall.copy(
                                    fontFamily = FontFamily.Monospace,
                                ),
                            color = MaterialTheme.workoutColors.accentSecondary,
                            modifier =
                                Modifier
                                    .fillMaxSize()
                                    .padding(12.dp)
                                    .verticalScroll(scrollState),
                        )
                    }
                }

                // 操作按钮
                Row(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                ) {
                    OutlinedButton(
                        onClick = onShare,
                        modifier = Modifier.weight(1f),
                    ) {
                        Icon(
                            imageVector = Icons.Default.Share,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp),
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = "分享",
                            style = MaterialTheme.typography.labelMedium,
                        )
                    }

                    Button(
                        onClick = onExport,
                        modifier = Modifier.weight(1f),
                        colors =
                            ButtonDefaults.buttonColors(
                                containerColor = MaterialTheme.workoutColors.accentPrimary,
                                contentColor = MaterialTheme.workoutColors.aiCoachText,
                            ),
                    ) {
                        Icon(
                            imageVector = Icons.Default.Download,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp),
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = "导出",
                            style = MaterialTheme.typography.labelMedium,
                        )
                    }
                }
            }
        }
    }

    // 复制成功提示
    LaunchedEffect(showCopySuccess) {
        if (showCopySuccess) {
            delay(2000)
            showCopySuccess = false
        }
    }
}

/**
 * 统计卡片组件
 */
@Composable
private fun StatCard(
    title: String,
    value: String,
    icon: ImageVector,
    modifier: Modifier = Modifier,
) {
    Card(
        modifier = modifier,
        colors =
            CardDefaults.cardColors(
                containerColor = MaterialTheme.workoutColors.aiCoachBackground,
            ),
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.workoutColors.accentPrimary,
                modifier = Modifier.size(20.dp),
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = value,
                style = MaterialTheme.typography.titleSmall,
                color = MaterialTheme.workoutColors.aiCoachText,
            )

            Text(
                text = title,
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.workoutColors.aiCoachText,
            )
        }
    }
}

// === Preview组件 ===

@GymBroPreview
@Composable
private fun CalendarJsonDialogPreview() {
    GymBroTheme {
        // 示例数据
        val sampleData =
            PlanCalendarData(
                plan_info =
                    PlanCalendarInfo(
                        plan_id = "sample_plan_1",
                        plan_name = "示例训练计划",
                        description = "这是一个示例训练计划",
                        total_days = 7,
                        workout_days = 5,
                        rest_days = 2,
                        created_at = System.currentTimeMillis(),
                        updated_at = System.currentTimeMillis(),
                    ),
                calendar_entries =
                    listOf(
                        CalendarEntryData(
                            date = "2024-01-01",
                            day_number = 1,
                            is_rest_day = false,
                            template_ids = listOf("template_1"),
                            workout_count = 1,
                            notes = "胸部训练",
                            estimated_duration = 60,
                            is_completed = false,
                        ),
                    ),
            )

        CalendarJsonDialog(
            calendarData = sampleData,
            planName = "示例训练计划",
            onDismiss = {},
            onShare = {},
            onExport = {},
        )
    }
}

```

File: D:/GymBro/GymBro/core/src/main/kotlin/com/example/gymbro/core/util/viewmodel/ViewModelLogging.kt
```kotlin
package com.example.gymbro.core.util.viewmodel

import com.example.gymbro.core.error.recovery.getRecoveryStrategy
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import timber.log.Timber

/**
 * ViewModel日志记录工具
 *
 * 提供统一的ViewModel日志记录功能，确保错误日志格式一致，
 * 方便问题排查和分析。
 */
object ViewModelLogging {
    /**
     * 记录操作开始
     *
     * @param viewModelName ViewModel名称
     * @param operation 操作名称
     * @param params 可选的操作参数
     */
    fun logOperationStart(
        viewModelName: String,
        operation: String,
        params: Map<String, Any?> = emptyMap(),
    ) {
        val paramsString = formatParams(params)
        Timber.Forest.d("[$viewModelName] 开始执行: $operation $paramsString")
    }

    /**
     * 记录操作结果
     *
     * @param viewModelName ViewModel名称
     * @param operation 操作名称
     * @param result 操作结果
     */
    fun <T> logOperationResult(
        viewModelName: String,
        operation: String,
        result: ModernResult<T>,
    ) {
        when (result) {
            is ModernResult.Success -> {
                Timber.Forest.d("[$viewModelName] 操作成功: $operation")
            }
            is ModernResult.Error -> {
                logError(viewModelName, operation, result.error)
            }
            is ModernResult.Loading -> {
                // 通常不需要记录Loading状态
            }
        }
    }

    /**
     * 记录错误
     *
     * @param viewModelName ViewModel名称
     * @param operation 操作名称
     * @param error 错误对象
     */
    fun logError(
        viewModelName: String,
        operation: String,
        error: ModernDataError,
    ) {
        val errorMessage = error.message
        val errorType = error.errorType
        val errorCategory = error.category
        val errorMeta = error.metadataMap.let { formatParams(it) }

        val causeTrace =
            error.cause
                ?.stackTraceToString()
                ?.lineSequence()
                ?.take(3)
                ?.joinToString("\n") ?: "无异常栈"

        Timber.Forest.e(
            """
            [$viewModelName] 操作失败: $operation
            | 错误类型: $errorType
            | 错误分类: $errorCategory
            | 错误消息: $errorMessage
            | 错误元数据: $errorMeta
            | 异常栈:
            $causeTrace
            """.trimIndent(),
        )
    }

    /**
     * 记录恢复尝试
     *
     * @param viewModelName ViewModel名称
     * @param operation 操作名称
     * @param error 错误对象
     */
    fun logRecoveryAttempt(
        viewModelName: String,
        operation: String,
        error: ModernDataError,
    ) {
        val recoveryStrategy = error.getRecoveryStrategy<Any>()
        Timber.Forest.d(
            """
            [$viewModelName] 尝试恢复操作: $operation
            | 错误类型: ${error.errorType}
            | 错误分类: ${error.category}
            | 恢复策略: ${recoveryStrategy?.javaClass?.simpleName ?: "无可用策略"}
            """.trimIndent(),
        )
    }

    /**
     * 记录恢复结果
     *
     * @param viewModelName ViewModel名称
     * @param operation 操作名称
     * @param success 恢复是否成功
     * @param error 错误对象，可选
     */
    fun logRecoveryResult(
        viewModelName: String,
        operation: String,
        success: Boolean,
        error: ModernDataError? = null,
    ) {
        if (success) {
            Timber.Forest.d("[$viewModelName] 恢复成功: $operation")
        } else {
            Timber.Forest.w(
                """
                [$viewModelName] 恢复失败: $operation
                | 错误类型: ${error?.errorType ?: "未知"}
                | 错误分类: ${error?.category ?: "未知"}
                """.trimIndent(),
            )
        }
    }

    /**
     * 记录状态更新
     *
     * @param viewModelName ViewModel名称
     * @param oldState 旧状态
     * @param newState 新状态
     * @param source 状态更新来源
     */
    fun logStateUpdate(
        viewModelName: String,
        oldState: Any?,
        newState: Any,
        source: String,
    ) {
        if (oldState != newState) {
            Timber.Forest.v("[$viewModelName] 状态更新 (来源: $source)")

            // 仅在调试构建中记录详细状态差异
            if (isDebugBuild()) {
                val oldStateString = oldState?.toString() ?: "null"
                val newStateString = newState.toString()

                if (oldStateString != newStateString) {
                    Timber.Forest.v("状态从:\n$oldStateString\n变为:\n$newStateString")
                }
            }
        }
    }

    /**
     * 记录用户操作
     *
     * @param viewModelName ViewModel名称
     * @param action 用户操作
     * @param params 操作参数
     */
    fun logUserAction(
        viewModelName: String,
        action: String,
        params: Map<String, Any?> = emptyMap(),
    ) {
        val paramsString = formatParams(params)
        Timber.Forest.i("[$viewModelName] 用户操作: $action $paramsString")
    }

    /**
     * 记录生命周期事件
     *
     * @param viewModelName ViewModel名称
     * @param event 生命周期事件
     */
    fun logLifecycleEvent(
        viewModelName: String,
        event: String,
    ) {
        Timber.Forest.v("[$viewModelName] 生命周期: $event")
    }

    /**
     * 格式化参数为字符串
     *
     * @param params 参数映射
     * @return 格式化后的参数字符串
     */
    private fun formatParams(params: Map<String, Any?>): String {
        if (params.isEmpty()) return ""

        return params.entries.joinToString(
            prefix = "{ ",
            postfix = " }",
            separator = ", ",
        ) { (key, value) ->
            val valueStr =
                when (value) {
                    null -> "null"
                    is String -> "\"$value\""
                    is Collection<*> -> "[size=${value.size}]"
                    is Map<*, *> -> "{size=${value.size}}"
                    is ByteArray -> "byte[${value.size}]"
                    else -> value.toString()
                }
            "$key=$valueStr"
        }
    }

    /**
     * 判断当前是否为调试构建
     */
    private fun isDebugBuild(): Boolean {
        // 实际项目中，这应该使用BuildConfig.DEBUG或其他机制
        // 由于我们无法直接访问BuildConfig，先使用返回true替代
        return true
    }

    /**
     * 为ViewModel提供的简化日志记录扩展函数
     */

    /**
     * 记录操作开始
     */
    fun <S : BaseUiState> BaseViewModel<S>.logOperationStart(
        operation: String,
        params: Map<String, Any?> = emptyMap(),
    ) {
        logOperationStart(this::class.java.simpleName, operation, params)
    }

    /**
     * 记录操作结果
     */
    fun <S : BaseUiState, T> BaseViewModel<S>.logOperationResult(
        operation: String,
        result: ModernResult<T>,
    ) {
        logOperationResult(this::class.java.simpleName, operation, result)
    }

    /**
     * 记录错误
     */
    fun <S : BaseUiState> BaseViewModel<S>.logError(
        operation: String,
        error: ModernDataError,
    ) {
        logError(this::class.java.simpleName, operation, error)
    }

    /**
     * 记录恢复尝试
     */
    fun <S : BaseUiState> BaseViewModel<S>.logRecoveryAttempt(
        operation: String,
        error: ModernDataError,
    ) {
        logRecoveryAttempt(this::class.java.simpleName, operation, error)
    }

    /**
     * 记录恢复结果
     */
    fun <S : BaseUiState> BaseViewModel<S>.logRecoveryResult(
        operation: String,
        success: Boolean,
        error: ModernDataError? = null,
    ) {
        logRecoveryResult(this::class.java.simpleName, operation, success, error)
    }

    /**
     * 记录状态更新
     */
    fun <S : BaseUiState> BaseViewModel<S>.logStateUpdate(
        oldState: Any?,
        newState: Any,
        source: String,
    ) {
        logStateUpdate(this::class.java.simpleName, oldState, newState, source)
    }

    /**
     * 记录用户操作
     */
    fun <S : BaseUiState> BaseViewModel<S>.logUserAction(
        action: String,
        params: Map<String, Any?> = emptyMap(),
    ) {
        logUserAction(this::class.java.simpleName, action, params)
    }
}

```

File: D:/GymBro/GymBro/core/src/main/kotlin/com/example/gymbro/core/logging/TimberManager.kt
```kotlin
package com.example.gymbro.core.logging

import android.annotation.SuppressLint
import android.util.Log
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 【重构】增强的 Timber 日志管理器
 *
 * 统一管理Timber日志系统的初始化、配置和控制。
 * 集成模块级别的日志控制和性能优化。
 */
@Singleton
class TimberManager
    @Inject
    constructor(
        private val loggingConfig: LoggingConfig,
    ) {
        /**
         * 初始化日志系统
         *
         * @param isDebug 是否为调试模式
         * @param environment 环境类型
         */
        fun initialize(
            isDebug: Boolean,
            environment: LoggingConfig.Environment = LoggingConfig.Environment.DEVELOPMENT,
        ) {
            // 设置环境
            loggingConfig.setEnvironment(environment)

            // 清除所有已有的Tree
            Timber.uprootAll()

            // 根据环境安装适当的Tree
            when {
                isDebug -> {
                    // 开发环境：使用模块感知的Tree
                    Timber.plant(ModuleAwareTree(loggingConfig))
                    Timber.tag("LOG-MANAGER").i("🔥 开发环境日志系统已启动 - 模块感知模式")
                }

                environment == LoggingConfig.Environment.PRODUCTION -> {
                    // 生产环境：只记录错误
                    Timber.plant(ProductionTree())
                    Timber.tag("LOG-MANAGER").i("🔥 生产环境日志系统已启动 - 仅错误模式")
                }

                else -> {
                    // 测试环境：适度日志
                    Timber.plant(StagingTree(loggingConfig))
                    Timber.tag("LOG-MANAGER").i("🔥 测试环境日志系统已启动 - 适度日志模式")
                }
            }
        }

        /**
         * 🔥 快速配置：关闭 ThinkingBox 详细日志
         */
        fun disableThinkingBoxVerboseLogs() {
            loggingConfig.disableThinkingBoxVerboseLogs()
            Timber.tag("LOG-MANAGER").i("🔥 ThinkingBox 详细日志已关闭")
        }

        /**
         * 🔥 快速配置：启用 ThinkingBox 调试日志
         */
        fun enableThinkingBoxDebugLogs() {
            loggingConfig.enableThinkingBoxDebugLogs()
            Timber.tag("LOG-MANAGER").i("🔥 ThinkingBox 调试日志已启用")
        }

        /**
         * 🔥 快速配置：启用 TOKEN-FLOW 调试日志
         */
        fun enableTokenFlowDebugLogs() {
            loggingConfig.enableTokenFlowDebugLogs()
            Timber.tag("LOG-MANAGER").i("🔥 TOKEN-FLOW 调试日志已启用")
        }

        /**
         * 🔥 快速配置：关闭 TOKEN-FLOW 调试日志
         */
        fun disableTokenFlowDebugLogs() {
            loggingConfig.disableTokenFlowDebugLogs()
            Timber.tag("LOG-MANAGER").i("🔥 TOKEN-FLOW 调试日志已关闭")
    }

        /**
         * 运行时切换环境
         */
        fun switchEnvironment(environment: LoggingConfig.Environment) {
            loggingConfig.setEnvironment(environment)
            Timber.tag("LOG-MANAGER").i("🔥 已切换到环境: $environment")
        }

        /**
         * 获取当前配置信息
         */
        fun getCurrentConfig(): String {
            val env = loggingConfig.getCurrentEnvironment()
        val tbConfig = loggingConfig.getModuleConfig(LoggingConfig.MODULE_THINKING_BOX)
        return "Environment: $env, ThinkingBox: ${tbConfig?.enabled}, MinLevel: ${tbConfig?.minLevel}"
    }

    /**
     * 在调试模式下打印日志堆栈，方便故障排查
     */
    @SuppressLint("TimberArgCount")
    fun dumpLogStack() {
        Timber.d(LOG_STACK_INDICATOR, "Current log stack:")
        val stackTrace = Thread.currentThread().stackTrace
        var startIndex = 0

        // 跳过一些内部框架调用
        for (i in stackTrace.indices) {
            if (stackTrace[i].className.contains("TimberManager")) {
                startIndex = i + 1
                break
            }
        }

        // 打印有用的堆栈信息
        for (i in startIndex until stackTrace.size) {
            val element = stackTrace[i]
            if (element.className.startsWith("dalvik.") ||
                element.className.startsWith("java.") ||
                element.className.startsWith("android.")
            ) {
                break
            }
            Timber.d(
                LOG_STACK_INDICATOR,
                "↳ %s.%s(%s:%d)",
                element.className,
                element.methodName,
                element.fileName,
                element.lineNumber,
            )
        }
    }

    /**
     * 设置全局日志标签过滤器
     *
     * @param tagFilter 标签过滤函数
     */
    fun setGlobalTagFilter(tagFilter: (String?) -> String) {
        globalTagFilter = tagFilter
    }

    companion object {
        private const val LOG_STACK_INDICATOR = "LogStack"

        // 全局标签过滤器
        @Volatile
        private var globalTagFilter: ((String?) -> String)? = null

        /**
         * 应用全局标签过滤器
         */
        internal fun applyTagFilter(
            tag: String?,
        ): String = globalTagFilter?.invoke(tag) ?: tag ?: "GymBro"

        /**
         * 基于当前类名生成的标签
         */
        @JvmStatic
        fun tagWithClassName(): String {
            val trace = Thread.currentThread().stackTrace
            var relevantIndex = 0

            // 找到调用处
            for (i in trace.indices) {
                val className = trace[i].className
                if (!className.contains("Logger") &&
                    !className.contains("Timber") &&
                    !className.contains("TimberManager")
                ) {
                    relevantIndex = i
                        break
                    }
                }

                // 从完整类名中提取简短类名
                val fullClassName = trace[relevantIndex].className
                return fullClassName.substring(fullClassName.lastIndexOf('.') + 1)
            }
        }
    }

/**
 * 🔥 【新增】生产环境专用 Tree
 * 只记录错误和崩溃信息
 */
class ProductionTree : Timber.Tree() {
    override fun isLoggable(
        tag: String?,
        priority: Int,
    ): Boolean {
        // 生产环境只记录错误和断言
        return priority >= Log.ERROR
    }

    override fun log(
        priority: Int,
        tag: String?,
        message: String,
        t: Throwable?,
    ) {
        if (!isLoggable(tag, priority)) return

        // 过滤敏感信息
        val sanitizedMessage = SensitiveDataFilter.filterSensitiveData(message)
        val finalTag = tag ?: "GymBro"

        // 记录到系统日志
        when (priority) {
            Log.ERROR -> Log.e(finalTag, sanitizedMessage, t)
            Log.ASSERT -> Log.wtf(finalTag, sanitizedMessage, t)
        }

        // 这里可以集成崩溃报告服务
        if (t != null) {
            reportCrash(finalTag, sanitizedMessage, t)
        }
    }

    private fun reportCrash(
        tag: String,
        message: String,
        throwable: Throwable,
    ) {
        // 集成 Firebase Crashlytics 或其他崩溃报告服务
        // 暂时留空，可以后续集成
    }
}

/**
 * 🔥 【新增】测试环境专用 Tree
 * 适度的日志输出，支持调试但不会刷屏
 */
class StagingTree(
    private val loggingConfig: LoggingConfig,
) : Timber.DebugTree() {
    override fun log(
        priority: Int,
        tag: String?,
        message: String,
        t: Throwable?,
    ) {
        // 测试环境：INFO 及以上 + 特定调试标签
        if (priority < Log.INFO) {
            // 只允许特定的调试标签
            if (tag == null || !isAllowedDebugTag(tag)) {
                return
            }
        }

        super.log(priority, tag, message, t)
    }

    private fun isAllowedDebugTag(tag: String): Boolean {
        return tag.contains("ERROR") ||
            tag.contains("STATE") ||
            tag.contains("UI") ||
            tag.contains("NETWORK") ||
            tag.contains("AUTH")
    }
}

/**
 * Timber日志标签助手
 *
 * 使用示例：
 * ```
* Timber.tag(TimberTags.NETWORK).d("API request completed")
* Timber.tag(TimberTags.DATABASE).i("Database updated")
* ```
*/
object TimberTags {
/** 网络相关日志 */
const val NETWORK = "Network"

    /** 数据库相关日志 */
    const val DATABASE = "Database"

    /** 用户界面相关日志 */
    const val UI = "UI"

    /** 认证相关日志 */
    const val AUTH = "Auth"

    /** 同步相关日志 */
    const val SYNC = "Sync"

    /** 性能相关日志 */
    const val PERFORMANCE = "Performance"

    /** 业务逻辑相关日志 */
    const val BUSINESS = "Business"

    /** 系统相关日志 */
    const val SYSTEM = "System"
}

```

File: D:/GymBro/GymBro/features/workout/src/main/kotlin/com/example/gymbro/features/workout/template/ui/components/CrashRecoveryDialog.kt
```kotlin
package com.example.gymbro.features.workout.template.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.theme.tokens.workoutColors
import com.example.gymbro.domain.workout.model.TemplateDraft
import com.example.gymbro.features.workout.template.cache.RecoveryResult
import com.example.gymbro.shared.models.workout.WorkoutTemplateDto
import java.text.SimpleDateFormat
import java.util.*

/**
 * 崩溃恢复对话框 - P4阶段新增
 *
 * 🎯 功能:
 * - 检测未保存草稿并提供恢复选项
 * - 显示恢复项目的详细信息
 * - 支持批量恢复和选择性恢复
 * - 用户友好的恢复体验
 *
 * 🏗️ 架构原则:
 * - 使用 designSystem 主题令牌
 * - Clean Architecture + MVI 2.0模式
 * - 无障碍访问支持
 */
@Composable
fun CrashRecoveryDialog(
    recoveryItems: List<RecoveryItem>,
    onRecoverItem: (RecoveryItem) -> Unit,
    onRecoverAll: () -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = false,
            usePlatformDefaultWidth = false,
        ),
    ) {
        Card(
            modifier = modifier
                .fillMaxWidth(0.9f)
                .fillMaxHeight(0.8f),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.workoutColors.cardBackground,
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = Tokens.Elevation.Large,
            ),
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(Tokens.Spacing.Large),
            ) {
                // 标题
                Text(
                    text = "发现未保存的内容",
                    style = MaterialTheme.typography.headlineSmall,
                    color = MaterialTheme.workoutColors.accentSecondary,
                    fontWeight = FontWeight.Bold,
                )

                Spacer(modifier = Modifier.height(Tokens.Spacing.Small))

                // 描述
                Text(
                    text = "检测到以下未保存的模板或草稿，您可以选择恢复它们：",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Tokens.Color.Gray600,
                )

                Spacer(modifier = Modifier.height(Tokens.Spacing.Large))

                // 恢复项目列表
                LazyColumn(
                    modifier = Modifier.weight(1f),
                    verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
                ) {
                    items(recoveryItems) { item ->
                        RecoveryItemCard(
                            item = item,
                            onRecover = { onRecoverItem(item) },
                        )
                    }
                }

                Spacer(modifier = Modifier.height(Tokens.Spacing.Large))

                // 操作按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
                ) {
                    // 取消按钮
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = MaterialTheme.workoutColors.accentSecondary,
                        ),
                        border = androidx.compose.foundation.BorderStroke(
                            width = 1.dp,
                            color = MaterialTheme.workoutColors.cardBorder,
                        ),
                    ) {
                        Text("跳过")
                    }

                    // 全部恢复按钮
                    Button(
                        onClick = onRecoverAll,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.workoutColors.accentPrimary,
                            contentColor = Tokens.Color.Gray950,
                        ),
                    ) {
                        Text("恢复全部")
                    }
                }
            }
        }
    }
}

/**
 * 恢复项目卡片
 */
@Composable
private fun RecoveryItemCard(
    item: RecoveryItem,
    onRecover: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.workoutColors.cardBackground,
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = Tokens.Elevation.Small,
        ),
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Medium),
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Column(
                    modifier = Modifier.weight(1f),
                ) {
                    // 名称
                    Text(
                        text = item.name,
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.workoutColors.accentSecondary,
                        fontWeight = FontWeight.Medium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                    )

                    Spacer(modifier = Modifier.height(Tokens.Spacing.Tiny))

                    // 类型和时间
                    Text(
                        text = "${item.type} • ${formatTime(item.lastModified)}",
                        style = MaterialTheme.typography.bodySmall,
                        color = Tokens.Color.Gray600,
                    )

                    // 描述（如果有）
                    if (item.description.isNotBlank()) {
                        Spacer(modifier = Modifier.height(Tokens.Spacing.Tiny))
                        Text(
                            text = item.description,
                            style = MaterialTheme.typography.bodySmall,
                            color = Tokens.Color.Gray600,
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis,
                        )
                    }
                }

                Spacer(modifier = Modifier.width(Tokens.Spacing.Medium))

                // 恢复按钮
                Button(
                    onClick = onRecover,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.workoutColors.accentPrimary,
                        contentColor = Tokens.Color.Gray950,
                    ),
                    contentPadding = PaddingValues(
                        horizontal = Tokens.Spacing.Medium,
                        vertical = Tokens.Spacing.Small,
                    ),
                ) {
                    Text(
                        text = "恢复",
                        style = MaterialTheme.typography.labelMedium,
                    )
                }
            }

            // 崩溃恢复标识
            if (item.isCrashRecovery) {
                Spacer(modifier = Modifier.height(Tokens.Spacing.Small))
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Icon(
                        imageVector = Icons.Default.Warning,
                        contentDescription = "崩溃恢复",
                        tint = Tokens.Color.Warning,
                        modifier = Modifier.size(Tokens.Icon.Small),
                    )
                    Spacer(modifier = Modifier.width(Tokens.Spacing.Tiny))
                    Text(
                        text = "可能因应用崩溃而未保存",
                        style = MaterialTheme.typography.labelSmall,
                        color = Tokens.Color.Warning,
                    )
                }
            }
        }
    }
}

/**
 * 恢复项目数据类
 */
data class RecoveryItem(
    val id: String,
    val name: String,
    val description: String,
    val type: String, // "模板" 或 "草稿"
    val lastModified: Long,
    val isCrashRecovery: Boolean,
    val template: WorkoutTemplateDto? = null,
    val draft: TemplateDraft? = null,
)

/**
 * 从 RecoveryResult 创建 RecoveryItem
 */
fun RecoveryResult.toRecoveryItem(): RecoveryItem? {
    return when (this) {
        is RecoveryResult.Found -> RecoveryItem(
            id = template.id,
            name = template.name,
            description = template.description,
            type = "模板",
            lastModified = template.updatedAt,
            isCrashRecovery = isCrashRecovery,
            template = template,
        )
        is RecoveryResult.FoundDraft -> RecoveryItem(
            id = draft.id,
            name = draft.name,
            description = draft.description ?: "",
            type = "草稿",
            lastModified = draft.updatedAt.toEpochMilliseconds(),
            isCrashRecovery = isCrashRecovery,
            draft = draft,
        )
        is RecoveryResult.NotFound,
        is RecoveryResult.Error,
        -> null
    }
}

/**
 * 格式化时间显示
 */
private fun formatTime(timestamp: Long): String {
    val now = System.currentTimeMillis()
    val diff = now - timestamp

    return when {
        diff < 60 * 1000 -> "刚刚"
        diff < 60 * 60 * 1000 -> "${diff / (60 * 1000)}分钟前"
        diff < 24 * 60 * 60 * 1000 -> "${diff / (60 * 60 * 1000)}小时前"
        else -> {
            val formatter = SimpleDateFormat("MM-dd HH:mm", Locale.getDefault())
            formatter.format(Date(timestamp))
        }
    }
}

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/resources/testSkeletons/subscription.kt.template
```template
package {{PACKAGE}}

import app.cash.turbine.test
import io.mockk.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.*
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import org.junit.After
import org.junit.Before
import org.junit.Test
import kotlin.test.*

// GymBro Core imports
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ErrorCategory
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.core.logging.Logger

// Subscription Domain imports
import com.example.gymbro.domain.model.subscription.*
import com.example.gymbro.domain.model.subscription.status.SubscriptionStatus
import com.example.gymbro.domain.model.subscription.plan.*
import com.example.gymbro.domain.model.subscription.payment.*
import com.example.gymbro.domain.repository.subscription.SubscriptionRepository
import com.example.gymbro.domain.repository.UserSessionManager

/**
 * Generated by genTest for GymBro Subscription Module
 * ✔️ 使用 ModernResult<T> 统一错误处理
 * ✔️ 使用 UiText 而非硬编码字符串
 * ✔️ 使用 kotlinx.datetime.Instant 处理时间
 * ✔️ 使用 Turbine 测试 Flow 发射序列
 * ✔️ 使用 MockK 进行依赖模拟
 * ✔️ 遵循 Clean Architecture + MVVM 原则
 * ✔️ 包含订阅模块特有的测试数据和场景
 */
@OptIn(ExperimentalCoroutinesApi::class)
class {{CLASS}}Test {

    // Test Dependencies - 根据被测试类的实际依赖调整
    private val mockSubscriptionRepository = mockk<SubscriptionRepository>()
    private val mockUserSessionManager = mockk<UserSessionManager>()
    private val mockLogger = mockk<Logger>()
    
    // Test Dispatcher
    private val testDispatcher = UnconfinedTestDispatcher()
    
    // Test Data - 订阅模块通用测试数据
    private val testUserId = "test_user_123"
    private val testSubscriptionId = "sub_123456"
    private val testPlanId = "monthly_premium"
    
    private val testSubscriptionPlan = SubscriptionPlan(
        id = testPlanId,
        name = UiText.DynamicString("高级月度计划"),
        description = UiText.DynamicString("每月自动续费的高级功能"),
        price = 29.99,
        currencyCode = "CNY",
        interval = PlanInterval.MONTHLY,
        type = PlanType.PREMIUM,
        trialPeriodDays = 7
    )
    
    private val testSubscription = Subscription(
        id = testSubscriptionId,
        userId = testUserId,
        planId = testPlanId,
        status = SubscriptionStatus.ACTIVE,
        planType = PlanType.PREMIUM,
        startDate = Clock.System.now(),
        endDate = Clock.System.now().plus(kotlinx.datetime.DateTimeUnit.DAY, 30),
        autoRenew = true,
        createdAt = Clock.System.now(),
        updatedAt = Clock.System.now()
    )
    
    private val testPaymentMethod = PaymentMethod.ALIPAY
    private val testRegion = Region(
        code = "CN",
        name = "China",
        currencyCode = "CNY",
        locale = "zh_CN"
    )
    
    // System Under Test - 根据实际被测试类调整
    private lateinit var {{CLASS_LOWER}}: {{CLASS}}

    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)
        
        // 配置Mock行为 - 根据被测试类的依赖调整
        every { mockLogger.d(any()) } just Runs
        every { mockLogger.e(any(), any()) } just Runs
        
        // 初始化被测试对象 - 根据实际构造函数调整
        {{CLASS_LOWER}} = {{CLASS}}(
            subscriptionRepository = mockSubscriptionRepository,
            userSessionManager = mockUserSessionManager,
            logger = mockLogger
        )
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
        clearAllMocks()
    }

    @Test
    fun `{{CLASS}} should handle successful subscription operation`() = runTest {
        // Given - 准备成功场景的测试数据
        val expectedResult = ModernResult.Success(testSubscription)
        coEvery { 
            mockSubscriptionRepository.createSubscription(any()) 
        } returns ModernResult.Success(testSubscriptionId)
        
        // When - 执行被测试的操作
        val result = {{CLASS_LOWER}}.createSubscription(testUserId, testPlanId)
        
        // Then - 验证结果
        assertTrue(result is ModernResult.Success)
        assertEquals(testSubscriptionId, (result as ModernResult.Success).data)
        
        // 验证依赖调用
        coVerify { mockSubscriptionRepository.createSubscription(any()) }
        verify { mockLogger.d(any()) }
    }

    @Test
    fun `{{CLASS}} should handle subscription error correctly`() = runTest {
        // Given - 准备错误场景
        val testError = ModernDataError(
            operationName = "createSubscription",
            errorType = GlobalErrorType.Subscription.General,
            category = ErrorCategory.BUSINESS,
            uiMessage = UiText.DynamicString("订阅创建失败")
        )
        coEvery { 
            mockSubscriptionRepository.createSubscription(any()) 
        } returns ModernResult.Error(testError)
        
        // When - 执行操作
        val result = {{CLASS_LOWER}}.createSubscription(testUserId, testPlanId)
        
        // Then - 验证错误处理
        assertTrue(result is ModernResult.Error)
        val error = (result as ModernResult.Error).error
        assertEquals(GlobalErrorType.Subscription.General, error.errorType)
        assertEquals("订阅创建失败", (error.uiMessage as UiText.DynamicString).value)
        
        // 验证错误日志
        verify { mockLogger.e(any(), any()) }
    }

    @Test
    fun `{{CLASS}} should handle payment method selection correctly`() = runTest {
        // Given - 准备支付方式测试数据
        val availablePaymentMethods = listOf(
            PaymentMethod.ALIPAY,
            PaymentMethod.WECHAT,
            PaymentMethod.GOOGLE_PAY
        )
        
        // When & Then - 根据实际业务逻辑调整
        // 这里是示例，需要根据实际的{{CLASS}}功能调整
        assertTrue(availablePaymentMethods.contains(testPaymentMethod))
    }

    @Test
    fun `{{CLASS}} should handle subscription status changes`() = runTest {
        // Given - 准备状态变更测试
        val statusFlow = flowOf(
            ModernResult.Loading,
            ModernResult.Success(SubscriptionStatus.ACTIVE)
        )
        
        // When & Then - 使用Turbine测试Flow
        statusFlow.test {
            assertEquals(ModernResult.Loading, awaitItem())
            val successItem = awaitItem() as ModernResult.Success
            assertEquals(SubscriptionStatus.ACTIVE, successItem.data)
            awaitComplete()
        }
    }

    @Test
    fun `{{CLASS}} should handle time operations with kotlinx datetime`() = runTest {
        // Given - 时间相关测试
        val testStartTime = Instant.parse("2025-01-30T10:00:00Z")
        val testEndTime = testStartTime.plus(kotlinx.datetime.DateTimeUnit.DAY, 30)
        
        // When & Then - 验证时间处理
        assertTrue(testEndTime > testStartTime)
        assertEquals(30, testEndTime.minus(testStartTime).inWholeDays)
    }

    @Test
    fun `{{CLASS}} should validate subscription plan correctly`() = runTest {
        // Given - 计划验证测试
        val validPlan = testSubscriptionPlan
        val invalidPlan = testSubscriptionPlan.copy(price = -1.0)
        
        // When & Then - 验证计划有效性
        assertTrue(validPlan.price > 0)
        assertFalse(invalidPlan.price > 0)
        assertEquals(PlanType.PREMIUM, validPlan.type)
    }

    @Test
    fun `{{CLASS}} should handle region-specific logic correctly`() = runTest {
        // Given - 区域相关测试
        val chinaRegion = testRegion
        val usRegion = Region("US", "United States", "USD", "en_US")
        
        // When & Then - 验证区域逻辑
        assertEquals("CNY", chinaRegion.currencyCode)
        assertEquals("USD", usRegion.currencyCode)
        assertTrue(chinaRegion.code != usRegion.code)
    }

    // TODO: 根据实际{{CLASS}}的功能添加更多特定测试
    // 例如：
    // - 如果是ViewModel，添加UI状态测试
    // - 如果是Repository，添加数据源测试
    // - 如果是UseCase，添加业务逻辑测试
    // - 如果是UI组件，添加Compose测试
}

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/kotlin/com/example/gymbro/buildlogic/OfficialTestingStandardPlugin.kt
```kotlin
package com.example.gymbro.buildlogic

import com.android.build.gradle.BaseExtension
import com.example.gymbro.buildlogic.utils.libs
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.tasks.testing.Test
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.dependencies
import org.gradle.kotlin.dsl.withType

/**
 * 基于Android官方测试样本的标准化测试插件
 *
 * 参考资料：
 * - https://github.com/android/testing-samples
 * - https://context7.com/android/testing-samples/llms.txt
 *
 * 官方标准化特性：
 * 1. 分离JVM单元测试和Android集成测试
 * 2. 优化测试执行性能
 * 3. 标准化依赖版本管理
 * 4. 自动化测试报告生成
 * 5. CI/CD友好的测试配置
 *
 * 使用方式：
 * 在模块的build.gradle.kts中应用：
 * plugins {
 *     id("gymbro.testing.official")
 * }
 */
class OfficialTestingStandardPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            // 检测Android模块的多种方式
            val isAndroidModule = project.plugins.hasPlugin("com.android.application") ||
                    project.plugins.hasPlugin("com.android.library")

            // 立即配置Android设置（避免生命周期问题）
            if (isAndroidModule) {
                configureAndroidTestSettings()
            }

            // 延迟执行确保其他插件已应用
            afterEvaluate {
                // 配置官方推荐的依赖
                configureOfficialTestDependencies(isAndroidModule)

                // 配置测试任务优化
                configureTestTaskOptimizations()
            }
        }
    }

    /**
     * 配置Android测试设置
     * 必须在apply阶段配置以避免生命周期问题
     */
    private fun Project.configureAndroidTestSettings() {
        extensions.configure<BaseExtension> {
            // 官方推荐：测试选项配置
            testOptions {
                // JVM单元测试配置 (对应 ./gradlew test)
                unitTests {
                    isIncludeAndroidResources = true
                    isReturnDefaultValues = true
                }

                // 官方推荐：设备测试配置 (对应 ./gradlew connectedAndroidTest)
                execution = "ANDROIDX_TEST_ORCHESTRATOR"
                animationsDisabled = true
            }

            // 官方推荐：构建类型配置
            buildTypes {
                getByName("debug") {
                    isTestCoverageEnabled = true
                    isMinifyEnabled = false
                    proguardFiles(getDefaultProguardFile("proguard-android.txt"))
                }
            }

            // 官方推荐：编译选项
            compileOptions {
                sourceCompatibility = org.gradle.api.JavaVersion.VERSION_17
                targetCompatibility = org.gradle.api.JavaVersion.VERSION_17
            }
        }
    }

    /**
     * 配置官方推荐的测试依赖
     * 基于官方样本的精确版本控制
     */
    private fun Project.configureOfficialTestDependencies(isAndroidModule: Boolean) {
        dependencies {
            // ========== 官方标准：JVM单元测试依赖 ==========
            // 对应命令：./gradlew test

            // 核心JUnit框架
            add("testImplementation", libs.findLibrary("junit").get())
            add("testImplementation", libs.findLibrary("kotlin-test").get())

            // 协程测试支持
            add("testImplementation", libs.findLibrary("kotlinx-coroutines-test").get())

            // Mock框架
            add("testImplementation", libs.findLibrary("mockk").get())

            // 流测试工具
            add("testImplementation", libs.findLibrary("turbine").get())

            if (isAndroidModule) {
                // ========== 官方标准：Android集成测试依赖 ==========
                // 对应命令：./gradlew connectedAndroidTest

                // 官方Espresso核心 (版本：3.5.1)
                add("androidTestImplementation", libs.findLibrary("androidx-espresso-core").get())
                add("androidTestImplementation", libs.findLibrary("androidx-junit").get())
                add("androidTestImplementation", libs.findLibrary("androidx-test-core").get())

                // 官方Compose UI测试
                add("androidTestImplementation", libs.findLibrary("compose-ui-test-junit4").get())

                // 官方推荐：调试工具
                add("debugImplementation", libs.findLibrary("compose-ui-test-manifest").get())

                // ========== 官方标准：Robolectric支持 ==========
                // 在JVM上运行Android测试
                add("testImplementation", libs.findLibrary("robolectric").get())
                add("testImplementation", libs.findLibrary("androidx-test-core").get())
                add("testImplementation", libs.findLibrary("androidx-junit").get())

                // ========== 官方标准：架构组件测试 ==========
                add("testImplementation", libs.findLibrary("androidx-arch-core-testing").get())
                add("androidTestImplementation", libs.findLibrary("androidx-arch-core-testing").get())

                // ========== 官方标准：Room数据库测试 ==========
                add("testImplementation", libs.findLibrary("androidx-room-testing").get())
                add("androidTestImplementation", libs.findLibrary("androidx-room-testing").get())

                // ========== 官方标准：Hilt依赖注入测试 ==========
                if (plugins.hasPlugin("com.google.devtools.ksp")) {
                    add("testImplementation", libs.findLibrary("hilt-android-testing").get())
                    add("androidTestImplementation", libs.findLibrary("hilt-android-testing").get())
                    add("kspTest", libs.findLibrary("hilt-compiler").get())
                    add("kspAndroidTest", libs.findLibrary("hilt-compiler").get())
                }
            }
        }
    }

    /**
     * 配置测试任务优化
     * 基于官方性能最佳实践
     */
    private fun Project.configureTestTaskOptimizations() {
        // 配置JVM测试任务优化
        tasks.withType<Test> {
            // 官方推荐：JVM设置优化
            maxHeapSize = "2048m"
            jvmArgs = listOf(
                "-XX:MaxMetaspaceSize=512m",
                "-XX:+HeapDumpOnOutOfMemoryError",
                "-Dfile.encoding=UTF-8",
                "-Duser.timezone=UTC"
            )

            // 官方推荐：并行测试执行
            maxParallelForks = (Runtime.getRuntime().availableProcessors() / 2).coerceAtLeast(1)

            // 官方推荐：测试日志配置
            testLogging {
                events("passed", "skipped", "failed", "standardOut", "standardError")
                showExceptions = true
                showCauses = true
                showStackTraces = true
                exceptionFormat = org.gradle.api.tasks.testing.logging.TestExceptionFormat.FULL
            }

            // 官方推荐：系统属性配置
            systemProperty("junit.jupiter.execution.parallel.enabled", "true")
            systemProperty("junit.jupiter.execution.parallel.mode.default", "concurrent")

            // 官方推荐：JUnit平台使用
            useJUnitPlatform()
        }

        // 创建官方推荐的测试任务
        createOfficialTestTasks()
    }

    /**
     * 创建官方推荐的测试任务
     * 基于 https://github.com/android/testing-samples 的任务配置
     */
    private fun Project.createOfficialTestTasks() {
        // 快速单元测试任务（官方推荐）
        tasks.register("testQuick") {
            group = "verification"
            description = "运行快速单元测试 (官方推荐工作流)"
            dependsOn("test")
        }

        // 完整测试套件任务（官方推荐）
        tasks.register("testFull") {
            group = "verification"
            description = "运行完整测试套件 (官方推荐工作流)"

            if (plugins.hasPlugin("com.android.application") ||
                plugins.hasPlugin("com.android.library")) {
                dependsOn("test", "connectedAndroidTest")
            } else {
                dependsOn("test")
            }
        }

        // 测试覆盖率任务（官方推荐）
        tasks.register("testCoverage") {
            group = "verification"
            description = "生成测试覆盖率报告 (官方推荐工作流)"
            dependsOn("test", "jacocoTestReport")
        }

        // CI友好的测试任务（官方推荐）
        tasks.register("testCI") {
            group = "verification"
            description = "CI环境测试任务 (官方推荐工作流)"

            // 设置CI环境的JVM参数
            doFirst {
                System.setProperty("java.awt.headless", "true")
                System.setProperty("testQuick", "true")
            }

            dependsOn("testQuick")
        }
    }
}

```

File: D:/GymBro/GymBro/core/src/main/kotlin/com/example/gymbro/core/logging/SensitiveDataFilter.kt
```kotlin
package com.example.gymbro.core.logging

/**
 * 敏感信息过滤器
 *
 * 用于过滤日志中的敏感信息，防止隐私泄露
 * 支持邮箱、手机号、密码、令牌等敏感信息的过滤
 */
object SensitiveDataFilter {

    // 邮箱地址正则表达式
    private val EMAIL_PATTERN = Regex(
        "[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}",
        RegexOption.IGNORE_CASE,
    )

    // 手机号正则表达式（支持国际格式）
    private val PHONE_PATTERN = Regex(
        "\\+?[1-9]\\d{1,14}",
        RegexOption.IGNORE_CASE,
    )

    // 中国手机号正则表达式
    private val CHINA_PHONE_PATTERN = Regex(
        "1[3-9]\\d{9}",
        RegexOption.IGNORE_CASE,
    )

    // 密码相关正则表达式
    private val PASSWORD_PATTERNS = arrayOf(
        Regex("password[=:]\\s*[^\\s,;]+", RegexOption.IGNORE_CASE),
        Regex("pwd[=:]\\s*[^\\s,;]+", RegexOption.IGNORE_CASE),
        Regex("passwd[=:]\\s*[^\\s,;]+", RegexOption.IGNORE_CASE),
    )

    // 令牌相关正则表达式
    private val TOKEN_PATTERNS = arrayOf(
        Regex("token[=:]\\s*[^\\s,;]+", RegexOption.IGNORE_CASE),
        Regex("accessToken[=:]\\s*[^\\s,;]+", RegexOption.IGNORE_CASE),
        Regex("refreshToken[=:]\\s*[^\\s,;]+", RegexOption.IGNORE_CASE),
        Regex("idToken[=:]\\s*[^\\s,;]+", RegexOption.IGNORE_CASE),
        Regex("bearer\\s+[a-zA-Z0-9._-]+", RegexOption.IGNORE_CASE),
    )

    // 密钥相关正则表达式
    private val KEY_PATTERNS = arrayOf(
        Regex("key[=:]\\s*[^\\s,;]+", RegexOption.IGNORE_CASE),
        Regex("apiKey[=:]\\s*[^\\s,;]+", RegexOption.IGNORE_CASE),
        Regex("secretKey[=:]\\s*[^\\s,;]+", RegexOption.IGNORE_CASE),
        Regex("privateKey[=:]\\s*[^\\s,;]+", RegexOption.IGNORE_CASE),
    )

    // 秘密相关正则表达式
    private val SECRET_PATTERNS = arrayOf(
        Regex("secret[=:]\\s*[^\\s,;]+", RegexOption.IGNORE_CASE),
        Regex("clientSecret[=:]\\s*[^\\s,;]+", RegexOption.IGNORE_CASE),
    )

    // 信用卡号正则表达式
    private val CREDIT_CARD_PATTERN = Regex(
        "\\b(?:\\d{4}[\\s-]?){3}\\d{4}\\b",
        RegexOption.IGNORE_CASE,
    )

    // 身份证号正则表达式（中国）
    private val ID_CARD_PATTERN = Regex(
        "\\b[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx]\\b",
        RegexOption.IGNORE_CASE,
    )

    /**
     * 过滤消息中的敏感信息
     *
     * @param message 原始消息
     * @return 过滤后的消息
     */
    fun filterSensitiveData(message: String): String {
        var filteredMessage = message

        // 过滤邮箱地址
        filteredMessage = filteredMessage.replace(EMAIL_PATTERN, "***@***.***")

        // 过滤手机号
        filteredMessage = filteredMessage.replace(PHONE_PATTERN, "***-***-****")
        filteredMessage = filteredMessage.replace(CHINA_PHONE_PATTERN, "***-****-****")

        // 过滤密码
        PASSWORD_PATTERNS.forEach { pattern ->
            filteredMessage = filteredMessage.replace(pattern) { matchResult ->
                val prefix = matchResult.value.substringBefore('=', matchResult.value.substringBefore(':'))
                when {
                    '=' in matchResult.value -> "$prefix=***"
                    ':' in matchResult.value -> "$prefix:***"
                    else -> "password=***"
                }
            }
        }

        // 过滤令牌
        TOKEN_PATTERNS.forEach { pattern ->
            filteredMessage = filteredMessage.replace(pattern) { matchResult ->
                val prefix = matchResult.value.substringBefore('=', matchResult.value.substringBefore(':'))
                when {
                    '=' in matchResult.value -> "$prefix=***"
                    ':' in matchResult.value -> "$prefix:***"
                    matchResult.value.startsWith("bearer", ignoreCase = true) -> "Bearer ***"
                    else -> "token=***"
                }
            }
        }

        // 过滤密钥
        KEY_PATTERNS.forEach { pattern ->
            filteredMessage = filteredMessage.replace(pattern) { matchResult ->
                val prefix = matchResult.value.substringBefore('=', matchResult.value.substringBefore(':'))
                when {
                    '=' in matchResult.value -> "$prefix=***"
                    ':' in matchResult.value -> "$prefix:***"
                    else -> "key=***"
                }
            }
        }

        // 过滤秘密
        SECRET_PATTERNS.forEach { pattern ->
            filteredMessage = filteredMessage.replace(pattern) { matchResult ->
                val prefix = matchResult.value.substringBefore('=', matchResult.value.substringBefore(':'))
                when {
                    '=' in matchResult.value -> "$prefix=***"
                    ':' in matchResult.value -> "$prefix:***"
                    else -> "secret=***"
                }
            }
        }

        // 过滤信用卡号
        filteredMessage = filteredMessage.replace(CREDIT_CARD_PATTERN, "****-****-****-****")

        // 过滤身份证号
        filteredMessage = filteredMessage.replace(ID_CARD_PATTERN, "******************")

        return filteredMessage
    }

    /**
     * 检查消息是否包含敏感信息
     *
     * @param message 要检查的消息
     * @return 如果包含敏感信息返回true，否则返回false
     */
    fun containsSensitiveData(message: String): Boolean {
        return EMAIL_PATTERN.containsMatchIn(message) ||
            PHONE_PATTERN.containsMatchIn(message) ||
            CHINA_PHONE_PATTERN.containsMatchIn(message) ||
            PASSWORD_PATTERNS.any { it.containsMatchIn(message) } ||
            TOKEN_PATTERNS.any { it.containsMatchIn(message) } ||
            KEY_PATTERNS.any { it.containsMatchIn(message) } ||
            SECRET_PATTERNS.any { it.containsMatchIn(message) } ||
            CREDIT_CARD_PATTERN.containsMatchIn(message) ||
            ID_CARD_PATTERN.containsMatchIn(message)
    }

    /**
     * 获取敏感信息类型列表
     *
     * @param message 要检查的消息
     * @return 包含的敏感信息类型列表
     */
    fun getSensitiveDataTypes(message: String): List<String> {
        val types = mutableListOf<String>()

        if (EMAIL_PATTERN.containsMatchIn(message)) types.add("邮箱")
        if (PHONE_PATTERN.containsMatchIn(message) || CHINA_PHONE_PATTERN.containsMatchIn(message)) {
            types.add(
                "手机号",
            )
        }
        if (PASSWORD_PATTERNS.any { it.containsMatchIn(message) }) types.add("密码")
        if (TOKEN_PATTERNS.any { it.containsMatchIn(message) }) types.add("令牌")
        if (KEY_PATTERNS.any { it.containsMatchIn(message) }) types.add("密钥")
        if (SECRET_PATTERNS.any { it.containsMatchIn(message) }) types.add("秘密")
        if (CREDIT_CARD_PATTERN.containsMatchIn(message)) types.add("信用卡")
        if (ID_CARD_PATTERN.containsMatchIn(message)) types.add("身份证")

        return types
    }
}

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/kotlin/com/example/gymbro/buildlogic/TestingConventionPlugin.kt
```kotlin
package com.example.gymbro.buildlogic

import com.android.build.gradle.BaseExtension
import com.example.gymbro.buildlogic.utils.androidTestImplementation
import com.example.gymbro.buildlogic.utils.debugImplementation
import com.example.gymbro.buildlogic.utils.libs
import com.example.gymbro.buildlogic.utils.testImplementation
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.dependencies

/**
 * GymBro项目测试依赖统一管理插件
 *
 * 根据测试施工方案V2.0，统一管理所有测试相关依赖：
 * - 单元测试框架 (JUnit4, MockK)
 * - 协程测试 (Coroutines Test)
 * - Android测试 (Espresso, Compose Test)
 * - 数据库测试 (Room Test)
 * - JaCoCo测试覆盖率统计
 *
 * 基于Android官方测试样本最佳实践：
 * https://github.com/android/testing-samples
 * - 明确分离单元测试和集成测试
 * - 标准化依赖配置
 * - 自动化测试选项配置
 * - 性能优化的测试执行
 */
class TestingConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            // 应用JaCoCo插件以支持测试覆盖率
            pluginManager.apply(JacocoConventionPlugin::class.java)

            // 检测Android模块并立即配置
            val isAndroidModule = plugins.hasPlugin("com.android.application") ||
                    plugins.hasPlugin("com.android.library") ||
                    extensions.findByName("android") != null

            // 立即配置Android测试选项（避免生命周期问题）
            if (isAndroidModule) {
                configureAndroidTestOptions()
            }

            // 延迟执行以确保其他插件已经应用
            afterEvaluate {
                dependencies {
                    // ========== 官方推荐：JVM单元测试 (src/test/) ==========
                    configureUnitTestDependencies()

                    if (isAndroidModule) {
                        // ========== 官方推荐：Android集成测试 (src/androidTest/) ==========
                        configureInstrumentationTestDependencies()

                        // ========== 官方推荐：调试工具 ==========
                        configureDebugDependencies()
                    }

                    // ========== 现代测试工具链（官方推荐版本） ==========
                    configureModernTestingTools()

                    // ========== 条件性依赖（基于项目配置） ==========
                    if (isAndroidModule) {
                        configureAndroidSpecificTestDependencies()
                    }
                }
            }
        }
    }

    /**
     * 配置Android测试选项
     * 基于官方testing-samples的testOptions配置
     */
    private fun Project.configureAndroidTestOptions() {
        extensions.configure<BaseExtension> {
            testOptions {
                // 官方推荐：启用Android资源用于JVM单元测试
                unitTests {
                    isIncludeAndroidResources = true
                    isReturnDefaultValues = true
                }

                // 官方推荐：优化测试执行
                execution = "ANDROIDX_TEST_ORCHESTRATOR"

                // 官方推荐：设备测试配置
                managedDevices {
                    // 可以在这里配置虚拟设备用于自动化测试
                }
            }

            // 官方推荐：测试覆盖率配置
            buildTypes {
                getByName("debug") {
                    isTestCoverageEnabled = true
                }
            }
        }
    }

    /**
     * 配置JVM单元测试依赖
     * 对应官方 ./gradlew test 命令执行的测试
     */
    private fun Project.configureUnitTestDependencies() {
        dependencies {
            // ========== 核心测试框架 ==========
            add("testImplementation", libs.findBundle("testing-unit").get())

            // ========== 官方推荐：Kotlin测试支持 ==========
            add("testImplementation", libs.findLibrary("kotlin-test").get())

            // ========== 官方推荐：协程测试 ==========
            add("testImplementation", libs.findLibrary("kotlinx-coroutines-test").get())

            // ========== 官方推荐：网络测试（MockWebServer等） ==========
            add("testImplementation", libs.findLibrary("okhttp").get())
        }
    }

    /**
     * 配置Android集成测试依赖
     * 对应官方 ./gradlew connectedAndroidTest 命令执行的测试
     */
    private fun Project.configureInstrumentationTestDependencies() {
        dependencies {
            // ========== 官方Espresso核心 ==========
            add("androidTestImplementation", libs.findLibrary("androidx-espresso-core").get())
            add("androidTestImplementation", libs.findLibrary("androidx-junit").get())
            add("androidTestImplementation", libs.findLibrary("androidx-test-core").get())

            // ========== 官方推荐：设备测试支持 ==========
            add("androidTestImplementation", libs.findLibrary("androidx-monitor").get())

            // ========== 官方推荐：Kotlin测试支持 ==========
            add("androidTestImplementation", libs.findLibrary("kotlin-test").get())

            // ========== 官方推荐：协程测试 ==========
            add("androidTestImplementation", libs.findLibrary("kotlinx-coroutines-test").get())

            // ========== 官方推荐：UI测试框架 ==========
            add("androidTestImplementation", libs.findLibrary("compose-ui-test-junit4").get())

            // ========== 官方推荐：网络测试 ==========
            add("androidTestImplementation", libs.findLibrary("okhttp").get())
        }
    }

    /**
     * 配置调试工具依赖
     * 官方推荐的调试和开发辅助工具
     */
    private fun Project.configureDebugDependencies() {
        dependencies {
            // ========== 官方推荐：Compose调试工具 ==========
            add("debugImplementation", libs.findLibrary("compose-ui-test-manifest").get())
            add("debugImplementation", libs.findLibrary("compose-ui-tooling").get())
        }
    }

    /**
     * 配置现代测试工具链
     * 基于官方最新推荐的测试工具和版本
     */
    private fun Project.configureModernTestingTools() {
        dependencies {
            // ========== 官方推荐：现代断言库 ==========
            add("testImplementation", libs.findLibrary("turbine").get())

            // ========== 官方推荐：架构组件测试 ==========
            add("testImplementation", libs.findLibrary("androidx-arch-core-testing").get())

            // ========== 官方推荐：Mock框架 ==========
            add("testImplementation", libs.findLibrary("mockk").get())
            add("testImplementation", libs.findLibrary("mockito-core").get())
            add("testImplementation", libs.findLibrary("mockito-kotlin").get())
        }
    }

    /**
     * 配置Android特定的测试依赖
     * 根据项目配置动态添加的依赖
     */
    private fun Project.configureAndroidSpecificTestDependencies() {
        dependencies {
            // ========== Robolectric测试支持 ==========
            // 官方推荐：在JVM上运行Android测试
            add("testImplementation", libs.findLibrary("robolectric").get())
            add("testImplementation", libs.findLibrary("androidx-test-core").get())
            add("testImplementation", libs.findLibrary("androidx-junit").get())
            add("testImplementation", libs.findLibrary("androidx-arch-core-testing").get())

            // ========== 数据库测试 ==========
            // 官方推荐：Room数据库测试
            add("testImplementation", libs.findLibrary("androidx-room-testing").get())
            add("androidTestImplementation", libs.findLibrary("androidx-room-testing").get())

            // ========== Hilt测试支持 ==========
            // 官方推荐：依赖注入测试
            if (plugins.hasPlugin("com.google.devtools.ksp")) {
                add("testImplementation", libs.findLibrary("hilt-android-testing").get())
                add("androidTestImplementation", libs.findLibrary("hilt-android-testing").get())

                // KSP处理器
                add("kspTest", libs.findLibrary("hilt-compiler").get())
                add("kspAndroidTest", libs.findLibrary("hilt-compiler").get())
            }
        }
    }
}

```

File: D:/GymBro/GymBro/features/profile/src/main/kotlin/com/example/gymbro/features/profile/internal/presentation/personal_info/PersonalInfoDialogs.kt
```kotlin
package com.example.gymbro.features.profile.internal.presentation.personal_info

import androidx.compose.material3.AlertDialog
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.text.font.FontWeight
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.GymBroMultiChoiceDialog
import com.example.gymbro.designSystem.components.GymBroNumberEditDialog
import com.example.gymbro.designSystem.components.GymBroSingleChoiceDialog
import com.example.gymbro.designSystem.components.GymBroTextEditDialog
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.domain.profile.model.user.FitnessGoal
import com.example.gymbro.domain.profile.model.user.WorkoutDay
import com.example.gymbro.domain.profile.model.user.enums.FitnessLevel
import com.example.gymbro.domain.profile.model.user.enums.Gender
import com.example.gymbro.features.profile.internal.presentation.base.ModernUserProfileViewModelMVI
import com.example.gymbro.features.profile.internal.presentation.contract.ProfileContract
import com.example.gymbro.features.profile.internal.presentation.util.ProfileStrings
import kotlinx.collections.immutable.toImmutableList

/**
 * 个人信息对话框组件 - internal实现细节
 *
 * 此组件被标记为internal，外部模块无法直接访问
 * 管理所有个人信息编辑对话框
 */
@Composable
internal fun PersonalInfoDialogs(
    state: ProfileContract.State,
    viewModel: ModernUserProfileViewModelMVI,
) {
    // 文本输入对话框 - 昵称
    if (state.showDisplayNameDialog) {
        GymBroTextEditDialog(
            show = true,
            title = ProfileStrings.displayName,
            value = state.tempDisplayName ?: "",
            onValueChange = { viewModel.dispatch(ProfileContract.Intent.UpdateTempDisplayName(it)) },
            onDismiss = { viewModel.dispatch(ProfileContract.Intent.DismissDialog) },
            onConfirm = { viewModel.dispatch(ProfileContract.Intent.ConfirmDisplayName) },
            label = ProfileStrings.displayNameHint,
            maxLength = 20,
        )
    }

    // 文本输入对话框 - 用户名
    if (state.showUsernameDialog) {
        GymBroTextEditDialog(
            show = true,
            title = UiText.DynamicString("用户名"),
            value = state.tempUsername ?: "",
            onValueChange = { viewModel.dispatch(ProfileContract.Intent.UpdateTempUsername(it)) },
            onDismiss = { viewModel.dispatch(ProfileContract.Intent.DismissDialog) },
            onConfirm = { viewModel.dispatch(ProfileContract.Intent.ConfirmUsername) },
            label = UiText.DynamicString("请输入用户名"),
            maxLength = 30,
        )
    }

    // 文本输入对话框 - 邮箱
    if (state.showEmailDialog) {
        GymBroTextEditDialog(
            show = true,
            title = UiText.DynamicString("邮箱"),
            value = state.tempEmail ?: "",
            onValueChange = { viewModel.dispatch(ProfileContract.Intent.UpdateTempEmail(it)) },
            onDismiss = { viewModel.dispatch(ProfileContract.Intent.DismissDialog) },
            onConfirm = { viewModel.dispatch(ProfileContract.Intent.ConfirmEmail) },
            label = UiText.DynamicString("请输入邮箱地址"),
            maxLength = 50,
        )
    }

    // 数字输入对话框 - 身高
    if (state.showHeightDialog) {
        GymBroNumberEditDialog(
            show = true,
            title = ProfileStrings.height,
            value = state.tempHeight ?: "",
            onValueChange = { viewModel.dispatch(ProfileContract.Intent.UpdateTempHeight(it)) },
            onDismiss = { viewModel.dispatch(ProfileContract.Intent.DismissDialog) },
            onConfirm = { viewModel.dispatch(ProfileContract.Intent.ConfirmHeight) },
            label = ProfileStrings.heightHint,
            unit = UiText.DynamicString("cm"),
            minValue = 50.0,
            maxValue = 300.0,
        )
    }

    // 数字输入对话框 - 体重
    if (state.showWeightDialog) {
        GymBroNumberEditDialog(
            show = true,
            title = ProfileStrings.weight,
            value = state.tempWeight ?: "",
            onValueChange = { viewModel.dispatch(ProfileContract.Intent.UpdateTempWeight(it)) },
            onDismiss = { viewModel.dispatch(ProfileContract.Intent.DismissDialog) },
            onConfirm = { viewModel.dispatch(ProfileContract.Intent.ConfirmWeight) },
            label = ProfileStrings.weightHint,
            unit = UiText.DynamicString("kg"),
            minValue = 20.0,
            maxValue = 300.0,
        )
    }

    // 性别选择对话框
    if (state.showGenderDialog) {
        GymBroSingleChoiceDialog(
            show = true,
            title = ProfileStrings.gender,
            options = Gender.values().toList(),
            selectedOption = state.tempGender,
            onOptionSelected = { viewModel.dispatch(ProfileContract.Intent.UpdateTempGender(it)) },
            onDismiss = { viewModel.dispatch(ProfileContract.Intent.DismissDialog) },
            onConfirm = { viewModel.dispatch(ProfileContract.Intent.ConfirmGender) },
            getDisplayText = { it.getDisplayName() },
        )
    }

    // 健身水平选择对话框
    if (state.showFitnessLevelDialog) {
        GymBroSingleChoiceDialog(
            show = true,
            title = ProfileStrings.fitnessLevel,
            options = FitnessLevel.values().toList(),
            selectedOption = state.tempFitnessLevel,
            onOptionSelected = { viewModel.dispatch(ProfileContract.Intent.UpdateTempFitnessLevel(it)) },
            onDismiss = { viewModel.dispatch(ProfileContract.Intent.DismissDialog) },
            onConfirm = { viewModel.dispatch(ProfileContract.Intent.ConfirmFitnessLevel) },
            getDisplayText = { it.getDisplayName() },
        )
    }

    GymBroSingleChoiceDialog(
        show = state.showGoalsDialog,
        title = ProfileStrings.fitnessGoalsDialog,
        options = FitnessGoal.values().toList(),
        selectedOption = state.tempGoal,
        onOptionSelected = { viewModel.dispatch(ProfileContract.Intent.UpdateTempGoal(it)) },
        onDismiss = { viewModel.dispatch(ProfileContract.Intent.DismissDialog) },
        onConfirm = { viewModel.dispatch(ProfileContract.Intent.ConfirmGoal) },
        getDisplayText = { it.getDisplayName() },
    )

    GymBroMultiChoiceDialog(
        show = state.showWorkoutDaysDialog,
        title = ProfileStrings.workoutDaysDialog,
        options = WorkoutDay.values().toList(),
        selectedOptions = state.tempWorkoutDays.toSet(),
        onOptionsChanged = {
            viewModel.dispatch(ProfileContract.Intent.UpdateTempWorkoutDays(it.toList().toImmutableList()))
        },
        onDismiss = { viewModel.dispatch(ProfileContract.Intent.DismissDialog) },
        onConfirm = { viewModel.dispatch(ProfileContract.Intent.ConfirmWorkoutDays) },
        getDisplayText = { it.getDisplayName() },
    )

    // 头像编辑对话框（简化实现）
    if (state.showAvatarEditDialog) {
        AlertDialog(
            onDismissRequest = { viewModel.dispatch(ProfileContract.Intent.DismissDialog) },
            title = {
                Text(
                    text = ProfileStrings.changeAvatar.asString(),
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface,
                )
            },
            text = {
                Text(
                    text = ProfileStrings.avatarEditComingSoon.asString(),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                )
            },
            confirmButton = {
                TextButton(
                    onClick = { viewModel.dispatch(ProfileContract.Intent.DismissDialog) },
                ) {
                    Text(
                        text = ProfileStrings.confirm.asString(),
                        color = MaterialTheme.colorScheme.primary,
                    )
                }
            },
            containerColor = MaterialTheme.colorScheme.surface,
            tonalElevation = Tokens.Elevation.Small,
        )
    }
}

```

File: D:/GymBro/GymBro/features/auth/src/main/kotlin/com/example/gymbro/features/auth/ui/components/AnimatedLoginButton.kt
```kotlin
package com.example.gymbro.features.auth.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.GymBroButton
import com.example.gymbro.designSystem.components.GymBroButtonDefaults
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.motion.MotionDurations
import com.example.gymbro.designSystem.theme.motion.MotionEasings
import com.example.gymbro.designSystem.theme.motion.MotionSpecs
import com.example.gymbro.designSystem.theme.tokens.Tokens

/**
 * 动画登录按钮类型
 * 按照Auth UI Style Guide定义的按钮层级
 */
enum class LoginButtonType {
    /** 主按钮：手机登录，最高优先级 */
    PRIMARY,

    /** 次级按钮：Google、匿名登录 */
    SECONDARY,
}

/**
 * 动画登录按钮组件 - 迁移到新设计系统
 *
 * 特性：
 * - 按压缩放动画 (使用MotionSpecs.tweenXS微交互)
 * - 进入动画支持 (使用MotionSpecs.tweenS延迟进入)
 * - 主题自适应色彩 (使用Tokens.Color系统)
 * - 无障碍支持
 *
 * @param text 按钮文字
 * @param onClick 点击回调
 * @param modifier Modifier参数 (必须为第一个参数)
 * @param type 按钮类型，决定样式层级
 * @param enabled 是否启用
 * @param enterAnimationIndex 进入动画索引，用于错开显示
 */
@Composable
fun AnimatedLoginButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    type: LoginButtonType = LoginButtonType.SECONDARY,
    enabled: Boolean = true,
    enterAnimationIndex: Int = 0,
) {
    // 交互状态管理
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()

    // 按压缩放动画 - 使用新Token系统
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.97f else 1.0f, // BUTTON_SCALE_PRESSED / BUTTON_SCALE_DISABLED
        animationSpec = MotionSpecs.tweenXS(),
        label = "ButtonPressScale",
    )

    // 进入动画 - 使用新Token系统
    val enterAnimation by animateFloatAsState(
        targetValue = 1f,
        animationSpec =
        tween(
            durationMillis = MotionDurations.S,
            delayMillis = 100 + (enterAnimationIndex * 80), // 错开进入
            easing = MotionEasings.STANDARD,
        ),
        label = "ButtonEnterAnimation",
    )

    // 使用新设计系统的GymBroButton
    GymBroButton(
        onClick = onClick,
        text = UiText.DynamicString(text),
        modifier =
        modifier
            .scale(scale * enterAnimation),
        enabled = enabled,
        colors =
        when (type) {
            LoginButtonType.PRIMARY ->
                GymBroButtonDefaults.colors(
                    containerColor = Tokens.Color.CTAPrimary,
                    contentColor = Tokens.Color.Gray950,
                )
            LoginButtonType.SECONDARY ->
                GymBroButtonDefaults.secondaryColors(
                    containerColor = Tokens.Color.Gray200,
                    contentColor = Tokens.Color.Gray800,
                )
        },
    )
}

/**
 * 手机登录按钮
 * 使用PRIMARY类型，最高视觉优先级
 */
@Composable
fun PhoneLoginButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    enterAnimationIndex: Int = 0,
) {
    AnimatedLoginButton(
        text = "手机号登录",
        onClick = onClick,
        modifier = modifier,
        type = LoginButtonType.PRIMARY,
        enabled = enabled,
        enterAnimationIndex = enterAnimationIndex,
    )
}

/**
 * Google登录按钮
 * 使用SECONDARY类型
 */
@Composable
fun GoogleLoginButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    enterAnimationIndex: Int = 1,
) {
    AnimatedLoginButton(
        text = "使用Google登录",
        onClick = onClick,
        modifier = modifier,
        type = LoginButtonType.SECONDARY,
        enabled = enabled,
        enterAnimationIndex = enterAnimationIndex,
    )
}

/**
 * 匿名登录按钮
 * 使用SECONDARY类型
 */
@Composable
fun AnonymousLoginButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    enterAnimationIndex: Int = 2,
) {
    AnimatedLoginButton(
        text = "匿名登录",
        onClick = onClick,
        modifier = modifier,
        type = LoginButtonType.SECONDARY,
        enabled = enabled,
        enterAnimationIndex = enterAnimationIndex,
    )
}

// === @GymBroPreview 预览组件 ===

@GymBroPreview
@Composable
private fun AnimatedLoginButtonPrimaryPreview() {
    GymBroTheme {
        Column(
            modifier =
            Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Large),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            AnimatedLoginButton(
                text = "手机号登录",
                onClick = {},
                modifier =
                Modifier
                    .fillMaxWidth()
                    .height(Tokens.Spacing.Massive),
                type = LoginButtonType.PRIMARY,
            )
        }
    }
}

@GymBroPreview
@Composable
private fun AnimatedLoginButtonSecondaryPreview() {
    GymBroTheme {
        Column(
            modifier =
            Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Large),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            AnimatedLoginButton(
                text = "使用Google登录",
                onClick = {},
                modifier =
                Modifier
                    .fillMaxWidth()
                    .height(Tokens.Spacing.Massive),
                type = LoginButtonType.SECONDARY,
            )
        }
    }
}

@GymBroPreview
@Composable
private fun LoginButtonsGroupPreview() {
    GymBroTheme {
        Column(
            modifier =
            Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Large),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            PhoneLoginButton(
                onClick = {},
                modifier =
                Modifier
                    .fillMaxWidth()
                    .height(Tokens.Spacing.Massive),
            )

            GoogleLoginButton(
                onClick = {},
                modifier =
                Modifier
                    .fillMaxWidth()
                    .height(Tokens.Spacing.Massive),
            )

            AnonymousLoginButton(
                onClick = {},
                modifier =
                Modifier
                    .fillMaxWidth()
                    .height(Tokens.Spacing.Massive),
            )
        }
    }
}

@GymBroPreview
@Composable
private fun AnimatedLoginButtonDisabledPreview() {
    GymBroTheme {
        Column(
            modifier =
            Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Large),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            AnimatedLoginButton(
                text = "禁用状态",
                onClick = {},
                modifier =
                Modifier
                    .fillMaxWidth()
                    .height(Tokens.Spacing.Massive),
                type = LoginButtonType.PRIMARY,
                enabled = false,
            )
        }
    }
}

```

File: D:/GymBro/GymBro/core/src/main/kotlin/com/example/gymbro/core/logging/TimberTrees.kt
```kotlin
package com.example.gymbro.core.logging

import timber.log.Timber
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * 日志优先级常量
 *
 * 提供与 Android Log 类相同的常量值，但不依赖于 Android API。
 * 这使得代码可以在非 Android 环境中使用相同的逻辑。
 */
object LogPriority {
    const val VERBOSE = 2
    const val DEBUG = 3
    const val INFO = 4
    const val WARN = 5
    const val ERROR = 6
    const val ASSERT = 7
}

/**
 * Timber 树基类
 *
 * 提供基本功能，同时避免依赖特定平台API。
 */
abstract class BaseTree : Timber.Tree() {
    /**
     * 确定是否应记录具有给定标签和优先级的消息
     */
    override fun isLoggable(
        tag: String?,
        priority: Int,
    ): Boolean {
        return true // 默认记录所有日志，子类可覆盖此方法进行筛选
    }

    /**
     * 过滤敏感信息
     */
    protected fun sanitizeMessage(message: String): String = SensitiveDataFilter.filterSensitiveData(message)
}

/**
 * 调试树
 *
 * 用于开发和测试环境，输出详细日志信息。
 */
class DebugTree : BaseTree() {
    private val logBuffer = ConcurrentLinkedQueue<LogEntry>()
    private val MAX_BUFFER_SIZE = 100

    override fun log(
        priority: Int,
        tag: String?,
        message: String,
        t: Throwable?,
    ) {
        val effectiveTag = TimberManager.applyTagFilter(tag)

        // 添加到循环缓冲区
        addToBuffer(priority, effectiveTag, message, t)

        // 记录日志（具体实现由平台提供）
        performLog(priority, effectiveTag, message, t)
    }

    /**
     * 执行实际日志记录
     *
     * 此方法在不同平台上有不同实现
     */
    protected fun performLog(
        priority: Int,
        tag: String,
        message: String,
        t: Throwable?,
    ) {
        // 基本实现，子类可重写以使用平台特定功能
        val priorityChar =
            when (priority) {
                LogPriority.VERBOSE -> 'V'
                LogPriority.DEBUG -> 'D'
                LogPriority.INFO -> 'I'
                LogPriority.WARN -> 'W'
                LogPriority.ERROR -> 'E'
                LogPriority.ASSERT -> 'A'
                else -> '?'
            }

        val throwableMessage =
            t?.let {
                "\n${it.javaClass.name}: ${it.message}\n${it.stackTraceToString()}"
            } ?: ""

        // 使用Timber记录日志，避免直接使用println
        // 注意：这里是Timber Tree的基础实现，在实际Android环境中会被Android Log替代
        System.out.println("[$priorityChar] $tag: $message$throwableMessage")
    }

    /**
     * 添加日志条目到循环缓冲区
     */
    private fun addToBuffer(
        priority: Int,
        tag: String,
        message: String,
        t: Throwable?,
    ) {
        logBuffer.add(LogEntry(System.currentTimeMillis(), priority, tag, message, t))

        // 保持缓冲区大小限制
        while (logBuffer.size > MAX_BUFFER_SIZE) {
            logBuffer.poll()
        }
    }
}

/**
 * 发布树
 *
 * 用于生产环境，只记录重要日志并支持错误报告。
 */

@Suppress("ktlint:standard:no-consecutive-comments")
/**
 * 发布树
 *
 * 用于生产环境，只记录重要日志并支持错误报告。
 *
 * 特点：
 * 1. 只记录警告和错误级别的日志
 * 2. 过滤敏感信息
 * 3. 支持错误上报
 * 4. 维护循环缓冲区用于调试
 */
open class ReleaseTree : BaseTree() {
    private val logBuffer = ConcurrentLinkedQueue<LogEntry>()
    private val MAX_BUFFER_SIZE = 50

    override fun isLoggable(
        tag: String?,
        priority: Int,
    ): Boolean {
        // 只允许警告和错误级别的日志
        return priority >= LogPriority.WARN
    }

    override fun log(
        priority: Int,
        tag: String?,
        message: String,
        t: Throwable?,
    ) {
        if (!isLoggable(tag, priority)) {
            return
        }

        val effectiveTag = TimberManager.applyTagFilter(tag ?: "GymBro")
        val sanitizedMessage = sanitizeMessage(message)

        // 添加到循环缓冲区
        addToBuffer(priority, effectiveTag, sanitizedMessage, t)

        when (priority) {
            LogPriority.ERROR -> {
                // 记录错误日志并上报
                performLog(priority, effectiveTag, sanitizedMessage, t)
                reportError(effectiveTag, sanitizedMessage, t)
            }
            LogPriority.ASSERT -> {
                // 记录致命错误并立即上报
                performLog(priority, effectiveTag, sanitizedMessage, t)
                reportFatalError(effectiveTag, sanitizedMessage, t)
            }
            else -> {
                // 记录警告日志
                performLog(priority, effectiveTag, sanitizedMessage, t)
            }
        }
    }

    /**
     * 执行实际日志记录
     */
    protected open fun performLog(
        priority: Int,
        tag: String,
        message: String,
        t: Throwable?,
    ) {
        val priorityChar =
            when (priority) {
                LogPriority.WARN -> 'W'
                LogPriority.ERROR -> 'E'
                LogPriority.ASSERT -> 'A'
                else -> '?'
            }

        val throwableMessage =
            t?.let {
                "\n${it.javaClass.name}: ${it.message}\n${it.stackTraceToString()}"
            } ?: ""

        // 使用System.out.println而非println，明确表示这是基础实现
        // 在Android环境中会被AndroidReleaseTree的Log.println替代
        System.out.println("[$priorityChar] $tag: $message$throwableMessage")
    }

    /**
     * 添加日志条目到循环缓冲区
     */
    private fun addToBuffer(
        priority: Int,
        tag: String,
        message: String,
        t: Throwable?,
    ) {
        logBuffer.add(LogEntry(System.currentTimeMillis(), priority, tag, message, t))

        // 保持缓冲区大小限制
        while (logBuffer.size > MAX_BUFFER_SIZE) {
            logBuffer.poll()
        }
    }

    /**
     * 上报错误信息
     */
    protected open fun reportError(
        tag: String,
        message: String,
        t: Throwable?,
    ) {
        // 平台无关的基本实现，子类可重写以实现特定平台的错误上报
    }

    /**
     * 上报致命错误
     */
    protected open fun reportFatalError(
        tag: String,
        message: String,
        t: Throwable?,
    ) {
        // 平台无关的基本实现，子类可重写以实现特定平台的致命错误上报
    }
}

/**
 * 日志条目数据类
 */
data class LogEntry(
    val timestamp: Long,
    val priority: Int,
    val tag: String,
    val message: String,
    val throwable: Throwable?,
)

```

File: D:/GymBro/GymBro/core/src/main/kotlin/com/example/gymbro/core/logging/SelectiveDebugTree.kt
```kotlin
package com.example.gymbro.core.logging

import android.util.Log
import timber.log.Timber
import kotlin.math.max

/**
 * 可选择性的调试树
 * 支持按标签过滤和采样率控制，避免日志噪音
 *
 * 🎯 基于"最小可诊断"原则设计：
 * - 默认只显示关键链路日志
 * - 支持运行时开关和采样
 * - 避免Token级别的刷屏
 */
class SelectiveDebugTree(
    private var enabledTags: Set<String> = setOf(
        "XML-PARSER",
        "PHASE-EXTRACTOR",
        "REDUCER",
        "UI",
        "BUTTON-TEST",        // 🔥 按钮点击测试
        "INTENT-TEST",        // 🔥 Intent处理测试
        "TemplateEditViewModel", // 🔥 ViewModel日志
        "TemplateEditReducer",   // 🔥 Reducer日志
        "TemplateSaver",         // 🔥 保存器日志
        "TemplateAutoSaveManager", // 🔥 AutoSave日志
        "TemplateViewModel",     // 🔥 模板ViewModel日志
        "TemplateEffectHandler", // 🔥 模板EffectHandler日志
        "TemplateScreen",        // 🔥 模板Screen日志
    ),
    private var sampleRate: Int = 1,
    private var minPriority: Int = Log.DEBUG,
) : Timber.DebugTree() {

    private val counter = mutableMapOf<String, Int>()

    // 🔥 Token 采样配置
    private var tokenSampleRate: Int = 50 // Token 采样率：每50个token记录1个
    private val tokenCounters = mutableMapOf<String, Int>()

    @Volatile private var active = true // 支持运行时开关

    /**
     * 启用指定标签的日志
     */
    fun enable(vararg tags: String) {
        enabledTags = tags.toSet()
    }

    /**
     * 禁用所有日志
     */
    fun disableAll() {
        enabledTags = emptySet()
    }

    /**
     * 设置采样率
     * @param n 采样率，1=全量，50=每50条打印1条
     */
    fun setSampleRate(n: Int) {
        sampleRate = max(1, n)
    }

    /**
     * 设置最小日志级别
     */
    fun setMinPriority(priority: Int) {
        minPriority = priority
    }

    /**
     * 运行时开关
     */
    fun setActive(on: Boolean) {
        active = on
    }

    /**
     * Token 采样记录（避免日志泛滥）
     */
    fun logToken(tag: String, token: String) {
        if (!active) return

        val count = tokenCounters.getOrPut(tag) { 0 } + 1
        tokenCounters[tag] = count

        // 🔥 立即记录包含关键标签的 token
        if (token.contains("<think") || token.contains("<title") || token.contains("<final") ||
            token.contains("</think") || token.contains("</title") || token.contains("</final")
        ) {
            super.log(Log.WARN, "THINKING-EVENTS", "🎯 关键标签 #$count: ${token.replace("\n", "\\n")}", null)
            return
        }

        // 🔥 采样记录普通 token
        if (count % tokenSampleRate == 0) {
            super.log(Log.VERBOSE, tag, "📊 Token采样 #$count: ${token.replace("\n", "\\n")}", null)
        }
    }

    override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
        // 🔥 运行时开关检查
        if (!active) return

        // 检查优先级
        if (priority < minPriority) return

        // 检查标签
        if (tag == null) return
        if (enabledTags.isNotEmpty() && tag !in enabledTags) return

        // 采样控制
        val hit = counter.merge(tag, 1) { old, _ -> old + 1 } ?: 1
        if (hit % sampleRate != 0) return

        super.log(priority, tag, message, t)
    }

    /**
     * 获取当前配置信息
     */
    fun getConfig(): String {
        return "tags=${enabledTags.joinToString()}, sample=$sampleRate, minPriority=$minPriority"
    }

    /**
     * 预设配置：最小可诊断模式
     */
    fun enableMinimalDiagnostic() {
        enabledTags = setOf("XML-PARSER", "PHASE-EXTRACTOR", "REDUCER", "UI")
        sampleRate = 1
        minPriority = Log.DEBUG
    }

    /**
     * 预设配置：精准调试模式（只显示关键事件）
     * 🔥 修复：只显示最重要的两个文件的日志
     */
    fun enableTokenDebug() {
        enabledTags = setOf(
            "PROMPT-BUILDER", // LayeredPromptBuilder 系统提示词构建
            "AI-COACH-RAW", // AICoachRepositoryImpl 发送接收RAW信息
        )
        sampleRate = 1 // 全量输出关键事件
        minPriority = Log.DEBUG
    }

    /**
     * 预设配置：静音模式
     */
    fun enableSilentMode() {
        disableAll()
    }
}

/**
 * 全局可控日志实例
 * 在Application中初始化，支持运行时控制
 */
object LogController {
    lateinit var selectiveTree: SelectiveDebugTree
        private set

    /**
     * 初始化日志系统
     * 在Application.onCreate()中调用
     */
    fun initialize(debugMode: Boolean = true) {
        if (debugMode) {
            selectiveTree = SelectiveDebugTree()
            Timber.plant(selectiveTree)

            // 打印初始配置
            Timber.tag("LOG").i("日志系统已启动: ${selectiveTree.getConfig()}")
        }
    }

    /**
     * 快速切换到最小可诊断模式
     */
    fun enableMinimalDiagnostic() {
        if (::selectiveTree.isInitialized) {
            selectiveTree.enableMinimalDiagnostic()
            Timber.tag("LOG").i("切换到最小可诊断模式")
        }
    }

    /**
     * 快速切换到Token调试模式
     */
    fun enableTokenDebug() {
        if (::selectiveTree.isInitialized) {
            selectiveTree.enableTokenDebug()
            Timber.tag("LOG").i("切换到Token调试模式（采样）")
        }
    }

    /**
     * 快速切换到静音模式
     */
    fun enableSilentMode() {
        if (::selectiveTree.isInitialized) {
            selectiveTree.enableSilentMode()
            Timber.tag("LOG").i("切换到静音模式")
        }
    }

    /**
     * 自定义配置
     */
    fun configure(tags: Array<String>, sampleRate: Int = 1) {
        if (::selectiveTree.isInitialized) {
            selectiveTree.enable(*tags)
            selectiveTree.setSampleRate(sampleRate)
            Timber.tag("LOG").i("自定义配置: ${selectiveTree.getConfig()}")
        }
    }
}

```

File: D:/GymBro/GymBro/designSystem/src/main/kotlin/com/example/gymbro/designSystem/component/NumberPickerDialog.kt
```kotlin
package com.example.gymbro.designSystem.component

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import kotlinx.coroutines.launch

/**
 * 数字选择器对话框
 *
 * 用于训练数据输入的专用数字选择器：
 * - 重量选择（0.5kg 递增，支持小数）
 * - 次数选择（整数，1-50范围）
 * - RPE选择（1-10整数）
 *
 * 特性：
 * - 滚轮式选择体验
 * - 自动居中对齐
 * - haptic反馈
 * - 单位显示
 */
@Composable
fun NumberPickerDialog(
    title: String,
    value: Float,
    unit: String = "",
    isInteger: Boolean = false,
    range: ClosedFloatingPointRange<Float> = 0f..200f,
    step: Float = if (isInteger) 1f else 0.5f,
    onValueChange: (Float) -> Unit,
    onDismiss: () -> Unit,
) {
    val values = remember(range, step, isInteger) {
        generateSequence(range.start) { current ->
            val next = current + step
            if (next <= range.endInclusive) next else null
        }.toList()
    }

    val initialIndex = remember(value, values) {
        values.indexOfFirst {
            if (isInteger) {
                it.toInt() == value.toInt()
            } else {
                kotlin.math.abs(it - value) < 0.01f
            }
        }.coerceAtLeast(0)
    }

    var selectedIndex by remember { mutableIntStateOf(initialIndex) }
    val listState = rememberLazyListState(initialFirstVisibleItemIndex = initialIndex)
    val coroutineScope = rememberCoroutineScope()

    // 自动滚动到选中项
    LaunchedEffect(selectedIndex) {
        coroutineScope.launch {
            listState.animateScrollToItem(selectedIndex)
        }
    }

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
        ),
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight(),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface,
            ),
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                // 标题
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface,
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 当前值显示
                Text(
                    text = "${formatValue(values[selectedIndex], isInteger)} $unit",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary,
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 数字选择器
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp),
                ) {
                    LazyColumn(
                        state = listState,
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(vertical = 80.dp),
                        verticalArrangement = Arrangement.spacedBy(4.dp),
                    ) {
                        items(values.size) { index ->
                            val isSelected = index == selectedIndex

                            Text(
                                text = formatValue(values[index], isInteger),
                                style = if (isSelected) {
                                    MaterialTheme.typography.titleLarge
                                } else {
                                    MaterialTheme.typography.bodyLarge
                                },
                                fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                                color = if (isSelected) {
                                    MaterialTheme.colorScheme.primary
                                } else {
                                    MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                                },
                                textAlign = TextAlign.Center,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .selectable(
                                        selected = isSelected,
                                        onClick = {
                                            selectedIndex = index
                                            onValueChange(values[index])
                                        },
                                    )
                                    .padding(vertical = 8.dp),
                            )
                        }
                    }

                    // 选择指示器
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(2.dp)
                            .align(Alignment.Center)
                            .padding(horizontal = 32.dp),
                    ) {
                        Divider(
                            color = MaterialTheme.colorScheme.primary,
                            thickness = 2.dp,
                        )
                    }

                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(2.dp)
                            .align(Alignment.Center)
                            .offset(y = 40.dp)
                            .padding(horizontal = 32.dp),
                    ) {
                        Divider(
                            color = MaterialTheme.colorScheme.primary,
                            thickness = 2.dp,
                        )
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                // 按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                ) {
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                    ) {
                        Text("取消")
                    }

                    Button(
                        onClick = {
                            onValueChange(values[selectedIndex])
                            onDismiss()
                        },
                        modifier = Modifier.weight(1f),
                    ) {
                        Text("确定")
                    }
                }
            }
        }
    }
}

/**
 * 格式化数值显示
 */
private fun formatValue(value: Float, isInteger: Boolean): String {
    return if (isInteger) {
        value.toInt().toString()
    } else {
        if (value == value.toInt().toFloat()) {
            value.toInt().toString()
        } else {
            "%.1f".format(value)
        }
    }
}

// 预览组件
@GymBroPreview
@Composable
private fun NumberPickerDialogPreview() {
    GymBroTheme {
        NumberPickerDialog(
            title = "设置重量",
            value = 20.5f,
            unit = "kg",
            isInteger = false,
            range = 0f..100f,
            step = 0.5f,
            onValueChange = {},
            onDismiss = {},
        )
    }
}

@GymBroPreview
@Composable
private fun NumberPickerDialogRpePreview() {
    GymBroTheme {
        NumberPickerDialog(
            title = "设置RPE",
            value = 7f,
            unit = "RPE",
            isInteger = true,
            range = 1f..10f,
            step = 1f,
            onValueChange = {},
            onDismiss = {},
        )
    }
}

```

File: D:/GymBro/GymBro/core/src/main/kotlin/com/example/gymbro/core/logging/LoggingController.kt
```kotlin
package com.example.gymbro.core.logging

import android.util.Log
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 【新增】日志控制器
 *
 * 提供运行时日志控制功能，方便调试和性能优化
 */
@Singleton
class LoggingController
    @Inject
    constructor(
        private val timberManager: TimberManager,
        private val loggingConfig: LoggingConfig,
    ) {
        /**
         * 快速配置预设
         */
        enum class LoggingPreset {
            SILENT, // 静音模式：只记录错误
            MINIMAL, // 最小模式：只记录关键信息
            NORMAL, // 正常模式：适度日志
            DEBUG, // 调试模式：详细日志
            VERBOSE, // 详细模式：所有日志
            TOKEN_FLOW_DEBUG, // 🔥 TOKEN-FLOW 调试模式：专门用于调试流式响应
        }

        /**
         * 应用日志预设
         */
        fun applyPreset(preset: LoggingPreset) {
            when (preset) {
                LoggingPreset.SILENT -> {
                    // 静音模式：只记录错误
                    loggingConfig.updateModuleConfig(
                        LoggingConfig.MODULE_THINKING_BOX,
                        LoggingConfig.ModuleLogConfig(
                            enabled = true,
                            minLevel = Log.ERROR,
                            tags = setOf("TB-ERROR"),
                            sampleRate = 1,
                        ),
                    )
                    Timber.tag("LOG-CTRL").i("🔇 已切换到静音模式")
                }

                LoggingPreset.MINIMAL -> {
                    // 最小模式：只记录关键信息
                    timberManager.disableThinkingBoxVerboseLogs()
                    Timber.tag("LOG-CTRL").i("🔕 已切换到最小日志模式")
                }

                LoggingPreset.NORMAL -> {
                    // 正常模式：适度日志
                    loggingConfig.updateModuleConfig(
                        LoggingConfig.MODULE_THINKING_BOX,
                        LoggingConfig.ModuleLogConfig(
                            enabled = true,
                            minLevel = Log.INFO,
                            tags =
                                setOf(
                                    "TB-ERROR",
                                    "TB-STATE",
                                    "TB-UI",
                                ),
                            sampleRate = 1,
                        ),
                    )
                    Timber.tag("LOG-CTRL").i("🔔 已切换到正常日志模式")
                }

                LoggingPreset.DEBUG -> {
                    // 调试模式：详细日志（采样）
                    timberManager.enableThinkingBoxDebugLogs()
                    Timber.tag("LOG-CTRL").i("🔊 已切换到调试模式")
                }

                LoggingPreset.VERBOSE -> {
                    // 详细模式：所有日志
                    loggingConfig.updateModuleConfig(
                        LoggingConfig.MODULE_THINKING_BOX,
                        LoggingConfig.ModuleLogConfig(
                            enabled = true,
                            minLevel = Log.VERBOSE,
                            tags = setOf(), // 空集合表示允许所有标签
                            sampleRate = 1,
                        ),
                    )
                    Timber.tag("LOG-CTRL").i("📢 已切换到详细模式")
                }

                LoggingPreset.TOKEN_FLOW_DEBUG -> {
                    // 🔥 TOKEN-FLOW 调试模式：专门用于调试流式响应
                    timberManager.enableTokenFlowDebugLogs()
                    Timber.tag("LOG-CTRL").i("🚀 已切换到 TOKEN-FLOW 调试模式")
                }
            }
        }

        /**
         * 🔥 ThinkingBox 专用控制
         */
        object ThinkingBox {
            /**
             * 完全关闭 ThinkingBox 日志
             */
            fun disable() {
                val controller =
                    LoggingController::class.java.getDeclaredField("INSTANCE").get(null) as? LoggingController
                controller?.loggingConfig?.updateModuleConfig(
                    LoggingConfig.MODULE_THINKING_BOX,
                    LoggingConfig.ModuleLogConfig(
                        enabled = false,
                        minLevel = Log.ERROR,
                        tags = emptySet(),
                        sampleRate = 1,
                    ),
                )
                Timber.tag("LOG-CTRL").i("🚫 ThinkingBox 日志已完全关闭")
            }

            /**
             * 只显示错误日志
             */
            fun errorsOnly() {
                val controller =
                    LoggingController::class.java.getDeclaredField("INSTANCE").get(null) as? LoggingController
                controller?.loggingConfig?.updateModuleConfig(
                    LoggingConfig.MODULE_THINKING_BOX,
                    LoggingConfig.ModuleLogConfig(
                        enabled = true,
                        minLevel = Log.ERROR,
                        tags = setOf("TB-ERROR"),
                        sampleRate = 1,
                    ),
                )
                Timber.tag("LOG-CTRL").i("⚠️ ThinkingBox 只显示错误日志")
            }

            /**
             * 启用状态跟踪日志
             */
            fun enableStateTracking() {
                val controller =
                    LoggingController::class.java.getDeclaredField("INSTANCE").get(null) as? LoggingController
                controller?.loggingConfig?.updateModuleConfig(
                    LoggingConfig.MODULE_THINKING_BOX,
                    LoggingConfig.ModuleLogConfig(
                        enabled = true,
                        minLevel = Log.INFO,
                        tags =
                            setOf(
                                "TB-ERROR",
                                "TB-STATE",
                            ),
                        sampleRate = 1,
                    ),
                )
                Timber.tag("LOG-CTRL").i("📊 ThinkingBox 状态跟踪已启用")
            }
        }

        /**
         * 获取当前日志状态
         */
        fun getCurrentStatus(): String {
            val tbConfig = loggingConfig.getModuleConfig(LoggingConfig.MODULE_THINKING_BOX)
            val env = loggingConfig.getCurrentEnvironment()

            return buildString {
                appendLine("🔍 日志系统状态:")
                appendLine("环境: $env")
                appendLine("ThinkingBox: ${if (tbConfig?.enabled == true) "启用" else "禁用"}")
                appendLine("最小级别: ${getLevelName(tbConfig?.minLevel ?: Log.ERROR)}")
                appendLine("允许标签: ${tbConfig?.tags?.joinToString(", ") ?: "无"}")
                appendLine("采样率: ${tbConfig?.sampleRate ?: 1}")
            }
        }

        /**
         * 获取日志级别名称
         */
        private fun getLevelName(level: Int): String =
            when (level) {
                Log.VERBOSE -> "VERBOSE"
                Log.DEBUG -> "DEBUG"
                Log.INFO -> "INFO"
                Log.WARN -> "WARN"
                Log.ERROR -> "ERROR"
                Log.ASSERT -> "ASSERT"
                else -> "UNKNOWN"
            }

        /**
         * 性能监控：获取日志统计
         */
        fun getLogStats(): String {
            // 这里可以添加日志统计功能
            return "日志统计功能待实现"
        }

        companion object {
            // 单例实例，用于静态访问
            @Volatile
            private var INSTANCE: LoggingController? = null

            /**
             * 设置实例（由 Hilt 注入时调用）
             */
            internal fun setInstance(instance: LoggingController) {
                INSTANCE = instance
            }

            /**
             * 获取实例
             */
            fun getInstance(): LoggingController? = INSTANCE
        }
    }

```

File: D:/GymBro/GymBro/features/coach/src/main/kotlin/com/example/gymbro/features/coach/shared/utils/DetailedRecompositionLogger.kt
```kotlin
package com.example.gymbro.features.coach.shared.utils

import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import timber.log.Timber

/**
 * �� 详细重组日志记录器 - 增强版
 *
 * 用于精确定位高频重组的源头，提供详细的调用栈和重组原因分析
 */
object DetailedRecompositionLogger {

    private val recompositionCounts = mutableMapOf<String, Int>()
    private val lastRecompositionTime = mutableMapOf<String, Long>()
    private val stateHistory = mutableMapOf<String, MutableList<String>>()

    /**
     * 🔥 增强版重组监控 - 包含状态变化追踪
     */
    @Composable
    fun LogRecompositionWithStateTracking(
        componentName: String,
        stateSnapshot: Map<String, Any?>,
        enableStackTrace: Boolean = false,
        logThreshold: Int = 10, // 每N次重组记录一次详细日志
    ) {
        val currentTime = System.currentTimeMillis()
        val count = recompositionCounts.getOrDefault(componentName, 0) + 1
        recompositionCounts[componentName] = count

        val lastTime = lastRecompositionTime[componentName] ?: currentTime
        val timeDiff = currentTime - lastTime
        lastRecompositionTime[componentName] = currentTime

        // 🎯 状态变化追踪
        val stateString = stateSnapshot.entries.joinToString(", ") { "${it.key}=${it.value}" }
        val history = stateHistory.getOrPut(componentName) { mutableListOf() }

        val stateChanged = history.lastOrNull() != stateString
        if (stateChanged) {
            history.add(stateString)
            if (history.size > 5) history.removeAt(0) // 只保留最近5次状态
        }

        SideEffect {
            val frequency = if (timeDiff > 0) 1000f / timeDiff else 0f

            // 🔥 高频组件每N次记录一次详细日志
            if (count % logThreshold == 0 || count <= 20 || stateChanged) {
                Timber.w(
                    """
                    🔄 RECOMPOSITION ANALYSIS:
                    ├─ Component: $componentName
                    ├─ Count: $count (Threshold: every $logThreshold)
                    ├─ Time Diff: ${timeDiff}ms
                    ├─ Frequency: ${"%.1f".format(frequency)}/sec
                    ├─ State Changed: $stateChanged
                    ├─ Current State: $stateString
                    ├─ State History: ${history.takeLast(3).joinToString(" → ")}
                    ${if (enableStackTrace) "├─ Stack: ${getSimplifiedStackTrace()}" else ""}
                    └─ Timestamp: $currentTime
                    """.trimIndent(),
                )
            }

            // 🚨 高频警告 - 更频繁的警告
            when {
                count == 50 -> Timber.e(
                    "🚨 MODERATE RECOMPOSITION: $componentName reached $count recompositions",
                )
                count == 100 -> Timber.e(
                    "🚨 HIGH RECOMPOSITION: $componentName reached $count recompositions",
                )
                count >= 200 && count % 100 == 0 -> Timber.e(
                    "🚨 CRITICAL RECOMPOSITION: $componentName has $count recompositions!",
                )
                count >= 1000 && count % 500 == 0 -> {
                    Timber.e("🚨🚨 EXTREME RECOMPOSITION: $componentName has $count recompositions!")
                    Timber.e("Recent state changes: ${history.takeLast(5).joinToString(" → ")}")
                }
            }
        }
    }

    /**
     * 原有的简化版本 - 保持兼容性
     */
    @Composable
    fun LogRecomposition(
        componentName: String,
        extraInfo: String = "",
        enableStackTrace: Boolean = false,
    ) {
        LogRecompositionWithStateTracking(
            componentName = componentName,
            stateSnapshot = mapOf("info" to extraInfo),
            enableStackTrace = enableStackTrace,
            logThreshold = 25, // 高频组件每25次记录一次
        )
    }

    /**
     * 🎯 简化的调用栈 - 只显示关键信息
     */
    private fun getSimplifiedStackTrace(): String {
        return Thread.currentThread().stackTrace
            .take(8)
            .filter { it.className.contains("gymbro") }
            .joinToString(
                "\n│   ",
            ) { "${it.className.substringAfterLast('.')}.${it.methodName}:${it.lineNumber}" }
    }

    /**
     * 🔍 状态变化分析报告
     */
    fun getStateChangeReport(): String {
        return buildString {
            appendLine("📊 STATE CHANGE ANALYSIS:")
            stateHistory.entries
                .sortedByDescending { recompositionCounts[it.key] ?: 0 }
                .forEach { (component, history) ->
                    val count = recompositionCounts[component] ?: 0
                    appendLine("├─ $component ($count recompositions):")
                    history.takeLast(3).forEach { state ->
                        appendLine("│   └─ $state")
                    }
                }
        }
    }

    /**
     * 获取重组统计报告
     */
    fun getRecompositionReport(): String {
        return buildString {
            appendLine("📊 RECOMPOSITION REPORT:")
            recompositionCounts.entries
                .sortedByDescending { it.value }
                .forEach { (component, count) ->
                    val severity = when {
                        count >= 1000 -> "🚨🚨 CRITICAL"
                        count >= 200 -> "🚨 HIGH"
                        count >= 50 -> "⚠️ MODERATE"
                        else -> "✅ NORMAL"
                    }
                    appendLine("├─ $component: $count times $severity")
                }
            appendLine("└─ Total components monitored: ${recompositionCounts.size}")
        }
    }

    /**
     * 清除统计数据
     */
    fun clearStats() {
        recompositionCounts.clear()
        lastRecompositionTime.clear()
        stateHistory.clear()
    }
}

/**
 * 🎯 便捷的重组监控扩展函数
 */
@Composable
fun Modifier.logRecomposition(
    componentName: String,
    extraInfo: String = "",
): Modifier {
    DetailedRecompositionLogger.LogRecomposition(componentName, extraInfo)
    return this
}

/**
 * 🔥 状态追踪版重组监控扩展函数
 */
@Composable
fun Modifier.logRecompositionWithState(
    componentName: String,
    stateSnapshot: Map<String, Any?>,
    enableStackTrace: Boolean = false,
): Modifier {
    DetailedRecompositionLogger.LogRecompositionWithStateTracking(
        componentName = componentName,
        stateSnapshot = stateSnapshot,
        enableStackTrace = enableStackTrace,
    )
    return this
}

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/kotlin/com/example/gymbro/buildlogic/AndroidLibraryConventionPlugin.kt
```kotlin
package com.example.gymbro.buildlogic

import com.android.build.api.dsl.LibraryExtension
import com.example.gymbro.buildlogic.utils.libs
import com.example.gymbro.buildlogic.utils.version
import org.gradle.api.DefaultTask
import org.gradle.api.JavaVersion
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.file.RegularFileProperty
import org.gradle.api.tasks.InputFile
import org.gradle.api.tasks.TaskAction
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.register
import org.gradle.kotlin.dsl.withType
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

class AndroidLibraryConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            with(pluginManager) {
                apply("com.android.library")
                apply("org.jetbrains.kotlin.android")
                apply("com.google.devtools.ksp")
                // 🚀 Library模块不需要APK大小优化，移除以提升构建速度
            }

            extensions.configure<LibraryExtension> {
                compileSdk = libs.version("compileSdk").toInt()

                defaultConfig {
                    minSdk = libs.version("minSdk").toInt()
                    testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
                    consumerProguardFiles("consumer-rules.pro")
                }

                buildTypes {
                    release {
                        isMinifyEnabled = false
                        proguardFiles(
                            getDefaultProguardFile("proguard-android-optimize.txt"),
                            "proguard-rules.pro"
                        )
                    }
                }

                compileOptions {
                    sourceCompatibility = JavaVersion.VERSION_17
                    targetCompatibility = JavaVersion.VERSION_17
                }

                buildFeatures {
                    // Compose will be enabled by ComposeConventionPlugin when needed
                    buildConfig = false
                    androidResources = false
                    resValues = false
                }

                // 🚀 Packaging配置已移至PerformanceOptimizationPlugin
            }

            tasks.withType<KotlinCompile>().configureEach {
                compilerOptions {
                    jvmTarget.set(org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_17)
                }
            }

            // 注册 genTest 任务 - GymBro 测试模板生成器
            tasks.register<GenTestSkeleton>("genTest") {
                group = "verification"
                description = "Generate test skeleton from Kotlin source for GymBro project"

                // 延迟配置，避免配置阶段错误
                val targetProperty = providers.gradleProperty("genTestTarget")
                if (targetProperty.isPresent) {
                    targetFile.set(layout.projectDirectory.file(targetProperty.get()))
                }
            }
        }
    }
}

/**
 * GymBro 项目测试骨架生成任务
 *
 * 基于模块路径自动选择对应的测试模板：
 * - core -> core.kt.template (property-based 测试)
 * - domain -> domain.kt.template (UseCase + Flow 测试)
 * - data -> data.kt.template (Repository + Room + Network 测试)
 * - di -> di.kt.template (Hilt 依赖注入测试)
 * - features -> feature.kt.template (Compose UI + ViewModel 测试)
 *
 * 使用方法：
 * ./gradlew :module:genTest -PgenTestTarget=src/main/kotlin/path/to/SourceFile.kt
 */
abstract class GenTestSkeleton : DefaultTask() {

    @get:InputFile
    abstract val targetFile: RegularFileProperty

    // 在配置阶段获取项目信息，避免Configuration cache问题
    private val projectPath: String = project.path
    private val projectDir: java.io.File = project.projectDir
    private val rootDir: java.io.File = project.rootDir

    @TaskAction
    fun generate() {
        // 检查是否提供了目标文件参数
        if (!targetFile.isPresent) {
            error("Pass -PgenTestTarget=path/to/SourceFile.kt")
        }

        val sourceFile = targetFile.asFile.get()

        // 检查源文件是否存在
        if (!sourceFile.exists()) {
            error("Source file does not exist: ${sourceFile.absolutePath}")
        }

        // 提取类名和包名
        val className = sourceFile.nameWithoutExtension
        val packageName = extractPackageName(sourceFile)

        // 检测模块类型
        val moduleType = detectModuleType(projectPath)

        // 加载并处理模板
        val template = loadTemplate(moduleType)
            .replace("{{PACKAGE}}", packageName)
            .replace("{{CLASS}}", className)
            .replace("{{DATE}}", java.time.LocalDate.now().toString())
            .replace("{{AUTHOR}}", System.getProperty("user.name", "GymBro Developer"))

        // 确定测试文件输出路径
        val testDir = if (moduleType == "di" || moduleType == "data" || moduleType == "feature") {
            // Android 集成测试
            projectDir.resolve("src/androidTest/kotlin/${packageName.replace('.', '/')}")
        } else {
            // JVM 单元测试
            projectDir.resolve("src/test/kotlin/${packageName.replace('.', '/')}")
        }

        val outputFile = testDir.resolve("${className}Test.kt")

        // 生成测试文件
        if (!outputFile.exists()) {
            testDir.mkdirs()
            outputFile.writeText(template)
            println("✅ Generated ${outputFile.relativeTo(rootDir)}")
            println("📝 Template: ${moduleType}.kt.template")
            println("🎯 Test type: ${if (moduleType in listOf("di", "data", "feature")) "androidTest" else "test"}")
        } else {
            println("⚠️  Test already exists: ${outputFile.relativeTo(rootDir)}")
        }
    }

    private fun extractPackageName(sourceFile: java.io.File): String {
        return sourceFile.readLines()
            .firstOrNull { it.trim().startsWith("package ") }
            ?.removePrefix("package ")
            ?.trim()
            ?.removeSuffix(";")
            ?: error("Cannot find package declaration in ${sourceFile.name}")
    }

    private fun detectModuleType(projectPath: String): String {
        return when {
            projectPath.contains(":core") -> "core"
            projectPath.contains(":domain") -> "domain"
            projectPath.contains(":data") -> "data"
            projectPath.contains(":di") -> "di"
            projectPath.contains(":feature") || projectPath.contains("features") -> "feature"
            projectPath.contains(":designSystem") -> "core" // 设计系统当作 core 处理
            else -> "core" // 默认使用 core 模板
        }
    }

    private fun loadTemplate(moduleType: String): String {
        val templatePath = "testSkeletons/${moduleType}.kt.template"
        return javaClass.classLoader
            .getResourceAsStream(templatePath)
            ?.bufferedReader()
            ?.use { it.readText() }
            ?: error("Cannot find template: $templatePath")
    }
}

```

File: D:/GymBro/GymBro/features/coach/src/main/kotlin/com/example/gymbro/features/coach/aicoach/internal/components/GymBroLogoBackground.kt
```kotlin
package com.example.gymbro.features.coach.aicoach.internal.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.motion.CoachMotionValues

/**
 * GymBro背景G字母组件
 *
 * 实现UI布局总方案中要求的"背景G字母"效果
 * 提供低调的视觉背景，不干扰主要内容的显示
 *
 * 设计特点：
 * - 大号G字母作为视觉背景
 * - 极低透明度，不干扰前景内容
 * - 居中显示，与品牌风格一致
 * - 响应主题变化
 *
 * @param modifier 修饰符
 * @param alpha 背景透明度，默认使用token值
 * @param fontSize 字体大小，默认240sp
 */
@Composable
internal fun GymBroLogoBackground(
    modifier: Modifier = Modifier,
    alpha: Float = CoachMotionValues.Alpha.PANEL_BACKGROUND, // 使用设计系统token
    fontSize: Float = 240f,
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center,
    ) {
        Text(
            text = "G",
            fontSize = fontSize.sp,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onSurface,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .alpha(alpha)
                .offset(y = (-20).dp), // 稍微向上偏移，优化视觉平衡
        )
    }
}

/**
 * 带动画效果的GymBroLogoBackground
 *
 * 支持透明度动画变化，可以在页面加载时产生淡入效果
 *
 * @param modifier 修饰符
 * @param isVisible 是否可见，控制动画状态
 * @param fontSize 字体大小
 */
@Composable
internal fun AnimatedGymBroLogoBackground(
    modifier: Modifier = Modifier,
    isVisible: Boolean = true,
    fontSize: Float = 240f,
) {
    val animatedAlpha by animateFloatAsState(
        targetValue = if (isVisible) CoachMotionValues.Alpha.PANEL_BACKGROUND else 0f,
        animationSpec = androidx.compose.animation.core.tween(
            durationMillis = 1000, // 较慢的淡入效果
            easing = androidx.compose.animation.core.FastOutSlowInEasing,
        ),
        label = "logo_background_alpha",
    )

    GymBroLogoBackground(
        modifier = modifier,
        alpha = animatedAlpha,
        fontSize = fontSize,
    )
}

/**
 * 多层次G字母背景
 *
 * 创建更丰富的背景层次效果，支持多个G字母的组合
 *
 * @param modifier 修饰符
 * @param layerCount 层次数量，默认3层
 */
@Composable
internal fun LayeredGymBroLogoBackground(
    modifier: Modifier = Modifier,
    layerCount: Int = 3,
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center,
    ) {
        repeat(layerCount) { index ->
            val scale = 1f - (index * 0.15f) // 每层缩小15%
            val alpha = CoachMotionValues.Alpha.PANEL_BACKGROUND * (1f - index * 0.3f) // 每层透明度递减

            Text(
                text = "G",
                fontSize = (240f * scale).sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .alpha(alpha.coerceAtLeast(0.05f)) // 确保最小透明度
                    .offset(
                        x = (index * 4).dp, // 每层稍微偏移
                        y = (-20 + index * 2).dp,
                    ),
            )
        }
    }
}

/**
 * 可自定义的GymBro背景组件
 *
 * 支持自定义字符、颜色和样式
 *
 * @param modifier 修饰符
 * @param character 显示的字符，默认"G"
 * @param color 文字颜色，默认使用主题色
 * @param alpha 透明度
 * @param fontSize 字体大小
 * @param fontWeight 字体粗细
 */
@Composable
internal fun CustomGymBroBackground(
    modifier: Modifier = Modifier,
    character: String = "G",
    color: Color = MaterialTheme.colorScheme.onSurface,
    alpha: Float = CoachMotionValues.Alpha.PANEL_BACKGROUND,
    fontSize: Float = 240f,
    fontWeight: FontWeight = FontWeight.Bold,
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center,
    ) {
        Text(
            text = character,
            fontSize = fontSize.sp,
            fontWeight = fontWeight,
            color = color,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .alpha(alpha)
                .offset(y = (-20).dp),
        )
    }
}

// === 预览组件 ===

@GymBroPreview
@Composable
private fun GymBroLogoBackgroundPreview() {
    GymBroTheme {
        Surface(
            modifier = Modifier.size(300.dp),
            color = MaterialTheme.colorScheme.background,
        ) {
            GymBroLogoBackground()
        }
    }
}

@GymBroPreview
@Composable
private fun AnimatedGymBroLogoBackgroundPreview() {
    GymBroTheme {
        Surface(
            modifier = Modifier.size(300.dp),
            color = MaterialTheme.colorScheme.background,
        ) {
            AnimatedGymBroLogoBackground(
                isVisible = true,
            )
        }
    }
}

@GymBroPreview
@Composable
private fun LayeredGymBroLogoBackgroundPreview() {
    GymBroTheme {
        Surface(
            modifier = Modifier.size(300.dp),
            color = MaterialTheme.colorScheme.background,
        ) {
            LayeredGymBroLogoBackground()
        }
    }
}

@GymBroPreview
@Composable
private fun CustomGymBroBackgroundPreview() {
    GymBroTheme {
        Surface(
            modifier = Modifier.size(300.dp),
            color = MaterialTheme.colorScheme.background,
        ) {
            CustomGymBroBackground(
                character = "💪",
                fontSize = 180f,
                alpha = 0.3f,
            )
        }
    }
}

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/kotlin/com/example/gymbro/buildlogic/QualityConventionPlugin.kt
```kotlin
package com.example.gymbro.buildlogic

import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.*

/**
 * GymBro项目代码质量统一管理插件
 *
 * 整合所有代码质量检查工具：
 * - Detekt: 静态代码分析
 * - Ktlint: 代码格式检查
 * - JaCoCo: 代码覆盖率统计
 * - 提供统一的质量检查任务
 * - 零警告标准配置
 */
class QualityConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            // 应用所有质量检查插件
            with(pluginManager) {
                apply("gymbro.detekt")
                apply("gymbro.ktlint")
                apply("gymbro.jacoco")
            }

            // 创建统一的质量检查任务
            tasks.register("qualityCheck") {
                description = "运行所有代码质量检查（Detekt + Ktlint + JaCoCo）"
                group = "verification"

                // 按照CICD文档建议的顺序：ktlintCheck -> detekt -> assembleDebug
                dependsOn(
                    "ktlintCheck",      // 先执行格式检查
                    "detektAll",        // 再执行静态分析
                    "jacocoTestReport"  // 最后执行覆盖率统计
                )

                // 确保任务执行顺序
                tasks.findByName("detektAll")?.mustRunAfter("ktlintCheck")
                tasks.findByName("jacocoTestReport")?.mustRunAfter("detektAll")

                doLast {
                    val buildDir = project.layout.buildDirectory.get().asFile
                    println("=== ✅ GymBro 代码质量检查完成 ===")
                    println("✅ Ktlint 格式检查: 通过")
                    println("✅ Detekt 静态分析: 通过")
                    println("✅ JaCoCo 覆盖率统计: 完成")
                    println("📊 查看报告:")
                    println("   - Ktlint: ${buildDir}/reports/ktlint/")
                    println("   - Detekt: ${buildDir}/reports/detekt/")
                    println("   - JaCoCo: ${buildDir}/reports/jacoco/")
                    println("===============================")
                }
            }

            // 创建质量检查验证任务（包含覆盖率验证）
            tasks.register("qualityVerify") {
                description = "运行代码质量检查并验证覆盖率阈值"
                group = "verification"

                dependsOn(
                    "qualityCheck",
                    "jacocoCoverageVerification"
                )

                doLast {
                    println("=== GymBro 代码质量验证完成 ===")
                    println("✅ 所有质量检查通过")
                    println("✅ 覆盖率阈值验证通过")
                    println("🎉 代码质量达到生产标准")
                    println("===============================")
                }
            }

            // 创建代码格式化任务
            tasks.register("formatCode") {
                description = "格式化所有代码（Ktlint + Detekt）"
                group = "formatting"

                dependsOn(
                    "ktlintFormat"
                )

                // 可选：包含Detekt格式化（谨慎使用）
                // dependsOn("detektFormat")

                doLast {
                    println("=== GymBro 代码格式化完成 ===")
                    println("✅ Ktlint 格式化: 完成")
                    println("💡 提示: 请检查格式化后的代码变更")
                    println("=============================")
                }
            }

            // 创建快速质量检查任务（跳过测试）
            tasks.register("qualityCheckFast") {
                description = "快速代码质量检查（跳过测试和覆盖率）"
                group = "verification"

                dependsOn(
                    "detektAll",
                    "ktlintCheck"
                )

                doLast {
                    println("=== GymBro 快速质量检查完成 ===")
                    println("✅ Detekt 静态分析: 通过")
                    println("✅ Ktlint 格式检查: 通过")
                    println("⚠️  跳过了测试覆盖率检查")
                    println("===============================")
                }
            }

            // 创建CI专用质量检查任务（符合文档流程）
            tasks.register("ciCheck") {
                description = "CI环境完整检查（ktlint -> detekt -> danger -> build）"
                group = "verification"

                dependsOn(
                    "ktlintCheck",
                    "detektAll"
                    // danger-kotlin 由CI workflow单独执行
                )

                // 确保执行顺序
                tasks.findByName("detektAll")?.mustRunAfter("ktlintCheck")

                doLast {
                    println("=== 🚀 GymBro CI 检查完成 ===")
                    println("✅ 符合CICD文档标准流程")
                    println("✅ 代码质量达到生产标准")
                    println("==============================")
                }
            }

            // 创建质量报告汇总任务
            tasks.register("qualityReport") {
                description = "生成代码质量报告汇总"
                group = "reporting"

                dependsOn("qualityCheck")

                doLast {
                    val buildDir = layout.buildDirectory.get().asFile
                    val reportsDir = buildDir.resolve("reports")

                    println("=== GymBro 代码质量报告汇总 ===")
                    println("📊 报告位置:")

                    // 检查并列出所有报告
                    listOf(
                        "detekt/detekt.html" to "Detekt 静态分析",
                        "ktlint/ktlintMainSourceSetCheck.html" to "Ktlint 格式检查",
                        "jacoco/test/html/index.html" to "JaCoCo 覆盖率"
                    ).forEach { (path, name) ->
                        val reportFile = reportsDir.resolve(path)
                        if (reportFile.exists()) {
                            println("   ✅ $name: file://${reportFile.absolutePath}")
                        } else {
                            println("   ❌ $name: 报告未生成")
                        }
                    }

                    println("===============================")
                }
            }

            // 在Android项目中的特殊配置
            afterEvaluate {
                val isAndroidModule = project.plugins.hasPlugin("com.android.application") ||
                        project.plugins.hasPlugin("com.android.library")

                if (isAndroidModule) {
                    // Android项目特定的质量检查配置
                    tasks.named("qualityCheck") {
                        // 可以添加Android特定的检查
                        // 例如：dependsOn("lintDebug")
                    }
                }
            }
        }
    }
}

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/kotlin/com/example/gymbro/buildlogic/JacocoConventionPlugin.kt
```kotlin
package com.example.gymbro.buildlogic

import com.example.gymbro.buildlogic.utils.libs
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.tasks.testing.Test
import org.gradle.kotlin.dsl.*
import org.gradle.testing.jacoco.plugins.JacocoPlugin
import org.gradle.testing.jacoco.plugins.JacocoPluginExtension
import org.gradle.testing.jacoco.tasks.JacocoReport
import org.gradle.testing.jacoco.tasks.JacocoCoverageVerification

/**
 * GymBro项目JaCoCo测试覆盖率统一管理插件
 *
 * 根据项目规范设置测试覆盖率要求：
 * - Domain层: >= 90%
 * - Data层: >= 80%
 * - ViewModel: >= 75%
 * - 其他模块: >= 70%
 */
class JacocoConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            // 直接应用JaCoCo插件（核心插件）
            apply(plugin = "jacoco")

            // 配置JaCoCo版本
            extensions.configure<JacocoPluginExtension> {
                toolVersion = libs.findVersion("jacoco").get().toString()
            }

            // 检测模块类型以设置不同的覆盖率要求
            val moduleName = project.name
            val coverageThreshold = when {
                moduleName.contains("domain") -> 0.90
                moduleName.contains("data") -> 0.80
                moduleName.contains("viewmodel") || project.path.contains("features") -> 0.75
                else -> 0.70
            }

            // 配置测试任务启用JaCoCo
            tasks.withType<Test>().configureEach {
                // 只对JVM测试使用JUnitPlatform，Android测试使用不同的配置
                if (!name.contains("android", ignoreCase = true)) {
                    useJUnitPlatform()
                }
                finalizedBy("jacocoTestReport")
            }

            // 为Android模块创建JaCoCo测试报告任务
            tasks.register("jacocoTestReport", JacocoReport::class) {
                description = "Generate JaCoCo test coverage report"
                group = "verification"

                dependsOn(tasks.withType<Test>())

                reports {
                    xml.required.set(true)
                    html.required.set(true)
                    csv.required.set(false)
                }

                // 配置执行数据文件
                executionData.setFrom(
                    fileTree(layout.buildDirectory.dir("jacoco")).include("**/*.exec")
                )

                // 配置源码目录
                sourceDirectories.setFrom(
                    files("src/main/kotlin", "src/main/java")
                )

                // 配置类文件目录并排除不需要测试覆盖率的文件
                classDirectories.setFrom(
                    files(
                        layout.buildDirectory.dir("tmp/kotlin-classes/debug"),
                        layout.buildDirectory.dir("tmp/kotlin-classes/release")
                    ).map {
                        fileTree(it) {
                            exclude(
                                "**/R.class",
                                "**/R\$*.class",
                                "**/BuildConfig.*",
                                "**/Manifest*.*",
                                "**/*Test*.*",
                                "android/**/*.*",
                                "**/databinding/**/*.*",
                                "**/di/**/*.*", // DI模块通常不需要测试
                                "**/*_Factory.*",
                                "**/*_MembersInjector.*",
                                "**/*Module.*",
                                "**/*Module\$*.*"
                            )
                        }
                    }
                )
            }

            // 为Android模块创建覆盖率验证任务
            tasks.register("jacocoCoverageVerification", JacocoCoverageVerification::class) {
                description = "Verify JaCoCo test coverage"
                group = "verification"

                dependsOn("jacocoTestReport")

                violationRules {
                    rule {
                        limit {
                            minimum = coverageThreshold.toBigDecimal()
                        }
                    }

                    // 针对特定包的规则
                    rule {
                        element = "PACKAGE"
                        includes = listOf("com.example.gymbro.*.domain.*")
                        limit {
                            counter = "INSTRUCTION"
                            value = "COVEREDRATIO"
                            minimum = 0.90.toBigDecimal()
                        }
                    }

                    rule {
                        element = "PACKAGE"
                        includes = listOf("com.example.gymbro.*.data.*")
                        limit {
                            counter = "INSTRUCTION"
                            value = "COVEREDRATIO"
                            minimum = 0.80.toBigDecimal()
                        }
                    }
                }

                // 配置执行数据文件
                executionData.setFrom(
                    fileTree(layout.buildDirectory.dir("jacoco")).include("**/*.exec")
                )

                // 配置源码目录
                sourceDirectories.setFrom(
                    files("src/main/kotlin", "src/main/java")
                )

                // 使用与报告相同的排除规则
                classDirectories.setFrom(
                    files(
                        layout.buildDirectory.dir("tmp/kotlin-classes/debug"),
                        layout.buildDirectory.dir("tmp/kotlin-classes/release")
                    ).map {
                        fileTree(it) {
                            exclude(
                                "**/R.class",
                                "**/R\$*.class",
                                "**/BuildConfig.*",
                                "**/Manifest*.*",
                                "**/*Test*.*",
                                "android/**/*.*",
                                "**/databinding/**/*.*",
                                "**/di/**/*.*",
                                "**/*_Factory.*",
                                "**/*_MembersInjector.*",
                                "**/*Module.*",
                                "**/*Module\$*.*"
                            )
                        }
                    }
                )
            }



            // 创建合并覆盖率报告任务（用于多模块项目）
            if (project == rootProject) {
                tasks.register("jacocoMergedReport", JacocoReport::class) {
                    description = "生成合并的JaCoCo覆盖率报告"
                    group = "verification"

                    // 收集所有子项目的执行数据
                    executionData.setFrom(
                        subprojects.map {
                            fileTree("${it.layout.buildDirectory.get().asFile}/jacoco/").include("**/*.exec")
                        }
                    )

                    // 收集所有子项目的源码和类文件
                    sourceDirectories.setFrom(
                        subprojects.map { "${it.projectDir}/src/main/kotlin" }
                    )

                    classDirectories.setFrom(
                        subprojects.map { "${it.layout.buildDirectory.get().asFile}/classes/kotlin/main" }
                    )

                    reports {
                        xml.required.set(true)
                        html.required.set(true)
                        csv.required.set(false)
                    }

                    onlyIf {
                        executionData.files.any { it.exists() }
                    }
                }
            }
        }
    }
}

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/kotlin/com/example/gymbro/buildlogic/DetektConventionPlugin.kt
```kotlin
package com.example.gymbro.buildlogic

import com.example.gymbro.buildlogic.utils.libs
import io.gitlab.arturbosch.detekt.Detekt
import io.gitlab.arturbosch.detekt.DetektCreateBaselineTask
import io.gitlab.arturbosch.detekt.extensions.DetektExtension
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.*

/**
 * GymBro项目Detekt静态代码分析统一管理插件
 *
 * 配置统一的Kotlin静态代码分析规则：
 * - 基于项目自定义detekt.yml配置
 * - 支持baseline文件管理
 * - 生成多种格式报告
 * - 零警告标准配置
 */
class DetektConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            // 应用Detekt插件
            pluginManager.apply("io.gitlab.arturbosch.detekt")

            // 配置Detekt扩展
            extensions.configure<DetektExtension> {
                // 使用项目根目录的配置文件
                config.setFrom(rootProject.files("config/detekt/detekt.yml"))

                // 使用baseline文件（如果存在）
                baseline = rootProject.file("config/detekt/baseline.xml")

                // 启用并行执行
                parallel = true

                // 构建失败时的行为
                buildUponDefaultConfig = true
                allRules = false

                // 在CI环境中更严格
                ignoreFailures = !project.hasProperty("ci")

                // 自动修正（谨慎使用）
                autoCorrect = false

                // 配置源文件
                source.setFrom(
                    files(
                        "src/main/kotlin",
                        "src/main/java",
                        "src/test/kotlin",
                        "src/test/java"
                    )
                )
            }

            // 配置Detekt任务
            tasks.withType<Detekt>().configureEach {
                description = "运行Detekt静态代码分析"
                group = "verification"

                // 设置JVM目标版本（直接赋值）
                jvmTarget = "17"

                // 配置报告
                reports {
                    html.required.set(true)
                    xml.required.set(true)
                    txt.required.set(false)
                    sarif.required.set(false)
                    md.required.set(false)

                    // 自定义报告位置
                    html.outputLocation.set(layout.buildDirectory.file("reports/detekt/detekt.html"))
                    xml.outputLocation.set(layout.buildDirectory.file("reports/detekt/detekt.xml"))
                }

                // 排除生成的代码
                exclude("**/generated/**", "**/build/**", "**/resources/**")

                // 包含源文件
                include("**/*.kt", "**/*.kts")

                // 安全设置类路径 - 使用正确的可解析配置
                try {
                    // 使用 runtimeClasspath 替代 compileClasspath
                    val runtimeClasspath = configurations.findByName("runtimeClasspath")
                    if (runtimeClasspath?.isCanBeResolved == true) {
                        classpath.setFrom(runtimeClasspath)
                    }
                } catch (e: Exception) {
                    // 如果没有可用的类路径配置，保持为空
                    logger.warn("无法设置 Detekt 类路径: ${e.message}")
                }

                // 在CI环境中启用更严格的检查
                if (project.hasProperty("ci")) {
                    ignoreFailures = false
                }
            }

            // 配置baseline创建任务
            tasks.withType<DetektCreateBaselineTask>().configureEach {
                description = "创建或更新Detekt baseline文件"
                group = "verification"

                // 设置JVM目标版本（直接赋值）
                jvmTarget = "17"

                // 设置baseline文件位置
                baseline.set(rootProject.file("config/detekt/baseline.xml"))
            }

            // 创建便利任务
            tasks.register("detektAll") {
                description = "运行所有源集的Detekt检查"
                group = "verification"
                dependsOn(tasks.withType<Detekt>())

                doLast {
                    println("=== GymBro Detekt 静态分析完成 ===")
                    println("HTML报告位置：${layout.buildDirectory.get().asFile}/reports/detekt/detekt.html")
                    println("XML报告位置：${layout.buildDirectory.get().asFile}/reports/detekt/detekt.xml")
                    println("================================")
                }
            }

            // 创建格式化任务（如果启用autoCorrect）
            tasks.register("detektFormat") {
                description = "运行Detekt自动格式化（谨慎使用）"
                group = "formatting"

                doFirst {
                    logger.warn("注意：Detekt自动格式化可能会修改源代码，请确保已备份")
                }

                doLast {
                    tasks.withType<Detekt>().forEach { detektTask ->
                        detektTask.autoCorrect = true
                    }
                }

                finalizedBy(tasks.withType<Detekt>())
            }

            // 创建baseline更新任务
            tasks.register("updateDetektBaseline") {
                description = "更新Detekt baseline文件"
                group = "verification"
                dependsOn(tasks.withType<DetektCreateBaselineTask>())

                doLast {
                    println("=== Detekt Baseline 已更新 ===")
                    println("Baseline文件：${rootProject.file("config/detekt/baseline.xml")}")
                    println("请提交更新的baseline文件到版本控制")
                    println("=============================")
                }
            }

            // 在Android项目中的特殊配置
            afterEvaluate {
                val isAndroidModule = project.plugins.hasPlugin("com.android.application") ||
                        project.plugins.hasPlugin("com.android.library")

                if (isAndroidModule) {
                    tasks.withType<Detekt>().configureEach {
                        // Android项目特定排除
                        exclude(
                            "**/R.kt",
                            "**/BuildConfig.kt",
                            "**/Manifest.kt",
                            "**/androidTest/**",
                            "**/debug/**",
                            "**/release/**"
                        )
                    }
                }
            }
        }
    }
}

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/resources/testSkeletons/viewmodel.kt.template
```template
package {{PACKAGE}}

import app.cash.turbine.test
import io.mockk.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.UnconfinedTestDispatcher
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Before
import org.junit.Test
import kotlin.test.*

// GymBro Core imports
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ErrorCategory
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.core.logging.Logger
import kotlinx.datetime.Instant

/**
 * Generated by genTest for GymBro ViewModel Module
 * ✔️ 使用 TestScope 和 runTest 处理协程
 * ✔️ 使用 Turbine 测试 StateFlow 发射序列
 * ✔️ 使用 ModernResult<T> 统一错误处理
 * ✔️ 使用 MockK 进行依赖模拟
 * ✔️ 遵循 Clean Architecture + MVVM 原则
 * ✔️ 基础ViewModel测试结构，需要根据实际功能调整
 */
@OptIn(ExperimentalCoroutinesApi::class)
class {{CLASS}}Test {

    // Test Dependencies - 根据被测试ViewModel的实际依赖调整
    private val mockLogger = mockk<Logger>()
    // TODO: 添加其他必要的mock依赖，例如：
    // private val mockUseCase = mockk<SomeUseCase>()
    
    // Test Scope and Dispatcher
    private val testScope = TestScope()
    private val testDispatcher = UnconfinedTestDispatcher()
    
    // Test Data - 根据实际需要调整
    private val testData = "test_data"
    
    // System Under Test - 根据实际被测试ViewModel调整
    private lateinit var viewModel: {{CLASS}}

    @Before
    fun setup() {
        // 配置Mock行为
        every { mockLogger.d(any<String>()) } just Runs
        every { mockLogger.w(any<String>()) } just Runs
        every { mockLogger.e(any<String>(), any<Throwable>()) } just Runs
        
        // 初始化被测试对象 - 根据实际构造函数调整
        // viewModel = {{CLASS}}(
        //     useCase = mockUseCase,
        //     logger = mockLogger
        // )
        
        // TODO: 取消注释并根据实际构造函数调整上面的代码
    }

    @After
    fun tearDown() {
        clearAllMocks()
    }

    @Test
    fun `{{CLASS}} should have correct initial state`() = testScope.runTest {
        // Given - ViewModel已初始化
        
        // When - 获取初始状态
        // val initialState = viewModel.uiState.value
        
        // Then - 验证初始状态
        // TODO: 根据实际{{CLASS}}的初始状态实现此测试
        assertTrue(true, "请根据实际{{CLASS}}初始状态实现此测试")
    }

    @Test
    fun `{{CLASS}} should handle loading state correctly`() = testScope.runTest {
        // Given - 准备loading场景
        // TODO: 根据实际{{CLASS}}功能添加测试数据

        // When & Then - 使用Turbine测试StateFlow
        // viewModel.uiState.test {
        //     assertEquals(ExpectedInitialState, awaitItem())
        //     
        //     viewModel.handleEvent(SomeEvent)
        //     
        //     assertEquals(LoadingState, awaitItem())
        //     assertEquals(SuccessState, awaitItem())
        // }
        
        // TODO: 根据实际{{CLASS}}状态管理实现此测试
        assertTrue(true, "请根据实际{{CLASS}}状态管理实现此测试")
    }

    @Test
    fun `{{CLASS}} should handle error state correctly`() = testScope.runTest {
        // Given - 准备错误场景
        // TODO: 准备错误场景测试数据

        // When & Then - 验证错误处理
        // TODO: 调用{{CLASS}}方法验证错误处理
        assertTrue(true, "请根据实际{{CLASS}}错误处理逻辑实现此测试")
    }

    @Test
    fun `{{CLASS}} should handle user events correctly`() = testScope.runTest {
        // Given - 准备用户事件测试
        // TODO: 根据实际{{CLASS}}事件类型添加测试

        // When - 触发用户事件
        // viewModel.handleEvent(SomeUserEvent)

        // Then - 验证状态变化
        // TODO: 验证事件处理后的状态变化
        assertTrue(true, "请根据实际{{CLASS}}事件处理实现此测试")
    }

    @Test
    fun `{{CLASS}} should handle UiText correctly`() = testScope.runTest {
        // Given - UiText测试
        val testText = UiText.DynamicString("测试文本")
        
        // When & Then - 验证UiText处理
        assertEquals("测试文本", testText.value)
        assertTrue(testText is UiText.DynamicString)
    }

    @Test
    fun `{{CLASS}} should handle time operations with kotlinx datetime`() = testScope.runTest {
        // Given - 时间相关测试
        val testTime = Instant.parse("2025-01-30T10:00:00Z")
        val currentTime = kotlinx.datetime.Clock.System.now()
        
        // When & Then - 验证时间处理
        assertTrue(currentTime >= testTime)
        assertNotNull(testTime)
    }

    // TODO: 根据实际{{CLASS}}的功能添加更多特定测试
    // 例如：
    // - StateFlow状态变化测试
    // - 用户交互事件测试
    // - UseCase调用测试
    // - 错误处理和恢复测试
    // - 生命周期相关测试
}

```

File: D:/GymBro/GymBro/features/auth/src/main/kotlin/com/example/gymbro/features/auth/ui/login/components/AuthButtonSection.kt
```kotlin
package com.example.gymbro.features.auth.ui.login.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AccountCircle
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Phone
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.features.auth.ui.login.AuthLoginConstants

/**
 * Auth按钮区域组件 - 13阶灰阶层次设计
 * 统一管理登录按钮和加载状态
 * 采用现代化设计，使用主题配色系统
 */
@Composable
fun AuthButtonSection(
    isLoading: Boolean,
    contentColor: androidx.compose.ui.graphics.Color,
    onPhoneLoginClick: () -> Unit,
    onGoogleLoginClick: () -> Unit,
    onAnonymousLoginClick: () -> Unit,
    onLoginSuccess: () -> Unit,
    modifier: Modifier = Modifier,
) {
    if (isLoading) {
        // 优化的加载指示器
        CircularProgressIndicator(
            color = contentColor,
            modifier =
            modifier.semantics {
                contentDescription = AuthLoginConstants.LOADING_CONTENT_DESCRIPTION
            },
        )
    } else {
        // 登录按钮组 - 使用13阶灰阶层次
        Column(
            modifier = modifier.fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Large), // 增大间距
        ) {
            // 1. 手机登录按钮 (主要登录方式) - 使用最深层级
            AuthModernButton(
                onClick = onPhoneLoginClick,
                text = UiText.DynamicString("手机号登录").asString(),
                icon = {
                    Icon(
                        imageVector = Icons.Default.Phone,
                        contentDescription = "手机登录",
                        modifier = Modifier.size(24.dp),
                        tint = Tokens.Color.Gray950, // 纯白图标
                    )
                },
                backgroundColor = Tokens.Color.Gray100, // 深炭黑
                contentColor = Tokens.Color.Gray950, // 纯白文字
                modifier =
                Modifier
                    .fillMaxWidth()
                    .height(60.dp),
            )

            // 2. Continue with Google 按钮 - 次级层次
            AuthModernButton(
                onClick = onGoogleLoginClick,
                text = UiText.DynamicString("Continue with Google").asString(),
                icon = {
                    Icon(
                        imageVector = Icons.Default.AccountCircle,
                        contentDescription = "Google Logo",
                        modifier = Modifier.size(24.dp),
                        tint = Tokens.Color.Gray800, // 银灰图标
                    )
                },
                backgroundColor = Tokens.Color.Gray200, // 炭灰
                contentColor = Tokens.Color.Gray800, // 银灰文字
                modifier =
                Modifier
                    .fillMaxWidth()
                    .height(60.dp),
            )

            // 3. 匿名登录 - 第三层级
            AuthModernButton(
                onClick = {
                    onAnonymousLoginClick()
                    onLoginSuccess()
                },
                text = UiText.DynamicString("匿名登录").asString(),
                icon = {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "匿名登录",
                        modifier = Modifier.size(24.dp),
                        tint = Tokens.Color.Gray600, // 暖灰图标
                    )
                },
                backgroundColor = Tokens.Color.Gray300, // 深石墨
                contentColor = Tokens.Color.Gray700, // 浅石墨文字
                modifier =
                Modifier
                    .fillMaxWidth()
                    .height(60.dp),
            )
        }
    }
}

/**
 * Auth现代化按钮组件 - 13阶灰阶层次设计
 * 支持灵活的颜色配置，创造视觉层次
 */
@Composable
private fun AuthModernButton(
    onClick: () -> Unit,
    text: String,
    icon: @Composable () -> Unit,
    backgroundColor: Color,
    contentColor: Color,
    modifier: Modifier = Modifier,
) {
    Button(
        onClick = onClick,
        colors =
        ButtonDefaults.buttonColors(
            containerColor = backgroundColor,
            contentColor = contentColor,
        ),
        shape = RoundedCornerShape(Tokens.Radius.Large), // 使用主题圆角
        elevation =
        ButtonDefaults.buttonElevation(
            defaultElevation = 0.dp,
            pressedElevation = 2.dp, // 轻微按压反馈
        ),
        modifier = modifier,
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            icon()
            Text(
                text = text,
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = contentColor,
            )
        }
    }
}

```

File: D:/GymBro/GymBro/features/workout/src/main/kotlin/com/example/gymbro/features/workout/session/internal/components/RestTimerDialog.kt
```kotlin
package com.example.gymbro.features.workout.session.internal.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Timer
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.GymBroButton
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.components.numberField
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.theme.tokens.workoutColors

/**
 * 休息计时器设置对话框
 *
 * 提供预设时间选择和自定义时间输入
 */
@Composable
internal fun RestTimerDialog(
    onDismiss: () -> Unit,
    onSetTimer: (Int) -> Unit,
    modifier: Modifier = Modifier,
) {
    var selectedDuration by remember { mutableIntStateOf(90) } // 默认90秒
    var customDuration by remember { mutableStateOf("") }
    val presetDurations = listOf(60, 90, 120, 180, 300) // 1分、1.5分、2分、3分、5分

    AlertDialog(
        onDismissRequest = onDismiss,
        icon = {
            Icon(
                imageVector = Icons.Default.Timer,
                contentDescription = null,
                tint = MaterialTheme.workoutColors.restState,
                modifier = Modifier.size(32.dp),
            )
        },
        title = {
            Text(
                text = UiText.DynamicString("设置休息时间").asString(),
                textAlign = TextAlign.Center,
                fontWeight = FontWeight.Bold,
            )
        },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
            ) {
                // 预设时间选项
                Text(
                    text = UiText.DynamicString("选择预设时间").asString(),
                    style = MaterialTheme.typography.labelLarge,
                    fontWeight = FontWeight.Medium,
                )

                Column(
                    verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.XSmall),
                ) {
                    presetDurations.forEach { duration ->
                        Row(
                            modifier =
                            Modifier
                                .fillMaxWidth()
                                .selectable(
                                    selected = selectedDuration == duration,
                                    onClick = {
                                        selectedDuration = duration
                                        customDuration = ""
                                    },
                                    role = Role.RadioButton,
                                ).padding(vertical = Tokens.Spacing.XSmall),
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            RadioButton(
                                selected = selectedDuration == duration,
                                onClick = null, // handled by Row's selectable
                            )
                            Spacer(modifier = Modifier.width(Tokens.Spacing.Small))
                            Text(
                                text = formatDurationText(duration),
                                style = MaterialTheme.typography.bodyMedium,
                            )
                        }
                    }
                }

                HorizontalDivider()

                // 自定义时间输入
                Text(
                    text = UiText.DynamicString("或自定义时间（秒）").asString(),
                    style = MaterialTheme.typography.labelLarge,
                    fontWeight = FontWeight.Medium,
                )

                numberField(
                    value = customDuration,
                    onValueChange = { value ->
                        customDuration = value
                        // 如果有自定义输入，清除预设选择
                        if (value.isNotEmpty()) {
                            selectedDuration = -1
                        }
                    },
                    label = UiText.DynamicString("休息时间"),
                    placeholder = UiText.DynamicString("例如：120"),
                    unit = UiText.DynamicString("秒"),
                    minValue = 1.0,
                    maxValue = 3600.0,
                    decimalPlaces = 0,
                    modifier = Modifier.fillMaxWidth(),
                )
            }
        },
        confirmButton = {
            GymBroButton(
                text = UiText.DynamicString("开始休息"),
                onClick = {
                    val duration =
                        when {
                            customDuration.isNotEmpty() -> customDuration.toIntOrNull() ?: 90
                            selectedDuration > 0 -> selectedDuration
                            else -> 90
                        }
                    onSetTimer(duration)
                },
                enabled = selectedDuration > 0 || customDuration.isNotEmpty(),
            )
        },
        dismissButton = {
            OutlinedButton(
                onClick = onDismiss,
            ) {
                Text(UiText.DynamicString("取消").asString())
            }
        },
        modifier = modifier,
    )
}

/**
 * 格式化时长显示文本
 */
private fun formatDurationText(seconds: Int): String =
    when {
        seconds < 60 -> "${seconds}秒"
        seconds % 60 == 0 -> "${seconds / 60}分钟"
        else -> {
            val minutes = seconds / 60
            val remainingSeconds = seconds % 60
            "${minutes}分${remainingSeconds}秒"
        }
    }

// === Preview组件 ===

@GymBroPreview
@Composable
private fun RestTimerDialogPreview() {
    GymBroTheme {
        RestTimerDialog(
            onDismiss = { },
            onSetTimer = { },
        )
    }
}

```

File: D:/GymBro/GymBro/features/auth/src/main/kotlin/com/example/gymbro/features/auth/ui/login/components/AuthLogoSection.kt
```kotlin
package com.example.gymbro.features.auth.ui.login.components

import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.foundation.layout.*
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.R
import com.example.gymbro.designSystem.components.animations.GymBroTypeWriter
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.components.extras.GymBroLogo
import com.example.gymbro.designSystem.components.extras.StarRingCanvas
import com.example.gymbro.features.auth.ui.animation.AuthAnimSpec
import com.example.gymbro.features.auth.ui.login.AuthLoginConstants

/**
 * 升级版Logo区域组件 - 使用新的GymBroLogo系统
 *
 * 特点：
 * - 集成GymBroAuthLogo组件，提供专业品牌展示
 * - 保持原有星环动画效果
 * - 支持打字机效果和副标题
 * - 完全兼容现有设计系统
 */
@Composable
fun AuthLogoSection(
    contentColor: androidx.compose.ui.graphics.Color,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier =
        modifier
            .size(AuthLoginConstants.LOGO_SIZE)
            .semantics {
                contentDescription = AuthLoginConstants.LOGO_CONTENT_DESCRIPTION
            },
        contentAlignment = Alignment.Center,
    ) {
        // 优化的星环背景
        OptimizedStarRing()

        // 新的GymBro Logo系统
        EnhancedAppTitleSection(
            contentColor = contentColor,
        )
    }
}

/**
 * 优化的星环组件
 * 使用remember优化动画创建
 */
@Composable
private fun OptimizedStarRing(
    modifier: Modifier = Modifier,
) {
    // 使用remember缓存无限动画，避免重复创建
    val infiniteTransition = rememberInfiniteTransition(label = "StarRingRotation")
    val rotationAngle by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = AuthAnimSpec.starRingRotationAnimation,
        label = "StarRingRotationAngle",
    )

    // 使用remember缓存旋转修饰符
    val rotationModifier =
        remember(rotationAngle) {
            Modifier.graphicsLayer { rotationZ = rotationAngle }
        }

    StarRingCanvas(
        modifier =
        modifier
            .size(AuthLoginConstants.LOGO_SIZE)
            .then(rotationModifier),
        enableAnimation = false, // 使用外部动画控制
    )
}

/**
 * 增强版应用标题区域组件
 * 使用新的GymBroAuthLogo系统，提供专业的品牌展示
 */
@Composable
private fun EnhancedAppTitleSection(
    contentColor: androidx.compose.ui.graphics.Color,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
    ) {
        // 使用金属质感GymBroLogo组件
        GymBroLogo(
            modifier = Modifier,
            animated = true,
            useHdr = false,
            style = MaterialTheme.typography.displayMedium.copy(color = contentColor),
            glowEffect = false,
        )

        Spacer(modifier = Modifier.height(AuthLoginConstants.TITLE_SUBTITLE_SPACING))

        // 保持原有的打字机效果
        GymBroTypeWriter(
            text = UiText.DynamicString("开始真正的健身"),
            textColor = contentColor.copy(alpha = AuthLoginConstants.TYPEWRITER_ALPHA),
            fontSize = AuthLoginConstants.TYPEWRITER_FONT_SIZE,
        )
    }
}

/**
 * 传统应用标题区域组件 - 保持向后兼容
 * 兼容新Token系统颜色
 */
@Composable
private fun AppTitleSection(
    contentColor: androidx.compose.ui.graphics.Color,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
    ) {
        // 应用名称
        Text(
            text = UiText.StringResource(R.string.app_name, emptyList()).asString(),
            fontSize = AuthLoginConstants.APP_NAME_FONT_SIZE,
            fontWeight = FontWeight.Bold,
            color = contentColor,
            textAlign = TextAlign.Center,
            lineHeight = AuthLoginConstants.APP_NAME_LINE_HEIGHT,
        )

        Spacer(modifier = Modifier.height(AuthLoginConstants.LOGO_TITLE_SPACING))

        // 副标题
        Text(
            text = UiText.StringResource(R.string.app_subtitle, emptyList()).asString(),
            fontSize = AuthLoginConstants.SUBTITLE_FONT_SIZE,
            color = contentColor.copy(alpha = AuthLoginConstants.SUBTITLE_ALPHA),
            textAlign = TextAlign.Center,
        )

        Spacer(modifier = Modifier.height(AuthLoginConstants.TITLE_SUBTITLE_SPACING))

        // 打字机效果
        GymBroTypeWriter(
            text = UiText.DynamicString("开始真正的健身"),
            textColor = contentColor.copy(alpha = AuthLoginConstants.TYPEWRITER_ALPHA),
            fontSize = AuthLoginConstants.TYPEWRITER_FONT_SIZE,
        )
    }
}

```

File: D:/GymBro/GymBro/core/src/main/kotlin/com/example/gymbro/core/logging/GymBroTimberLogger.kt
```kotlin
package com.example.gymbro.core.logging

import android.util.Log
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * GymBro Timber日志系统
 *
 * 统一管理Timber日志配置和初始化，提供标准化的日志标签和使用指南。
 * 整合了ReleaseTree和TimberLogger的功能，简化日志管理。
 */
@Singleton
class GymBroTimberLogger
@Inject
constructor() {
    /**
     * 初始化Timber日志系统
     *
     * @param isDebug 是否为调试模式
     */
    fun initialize(isDebug: Boolean) {
        // 先清除所有已有的Tree
        Timber.uprootAll()

        // 安装适合当前环境的Tree
        if (isDebug) {
            Timber.plant(Timber.DebugTree())
        } else {
            Timber.plant(GymBroReleaseTree())
        }
    }

    companion object {
        // 日志标签常量
        object Tags {
            const val NETWORK = "Network"
            const val DATABASE = "Database"
            const val UI = "UI"
            const val AUTH = "Auth"
            const val SYNC = "Sync"
            const val WORKOUT = "Workout"
            const val NUTRITION = "Nutrition"
            const val PROFILE = "Profile"
        }

        /**
         * 日志使用规范：
         *
         * 1. 使用标签区分不同模块的日志
         *    例如：Timber.tag(Tags.NETWORK).d("API request completed")
         *
         * 2. 使用格式化字符串，避免字符串拼接
         *    正确：Timber.d("User %s logged in", username)
         *    错误：Timber.d("User " + username + " logged in")
         *
         * 3. 异常日志：异常对象作为第一个参数
         *    正确：Timber.e(exception, "Failed to load data")
         *    错误：Timber.e("Failed to load data: " + exception)
         *
         * 4. 日志级别选择：
         *    - Timber.v(): 详细日志，仅用于开发阶段（生产环境不输出）
         *    - Timber.d(): 调试信息，用于开发调试（生产环境不输出）
         *    - Timber.i(): 重要信息，如功能完成、状态变更等
         *    - Timber.w(): 警告信息，出现错误但不影响功能
         *    - Timber.e(): 错误信息，影响功能的严重错误
         *    - Timber.wtf(): 致命错误，不应该发生的严重错误
         *
         * 5. 安全注意事项：
         *    - 不要记录敏感信息（密码、令牌等）
         *    - 避免在循环中过度记录日志
         *    - 在重要状态变更处添加日志
         *    - 为复杂操作添加开始/结束日志
         */
    }
}

/**
 * 生产环境使用的Timber Tree实现
 *
 * 特点：
 * 1. 只记录警告和错误级别的日志
 * 2. 过滤敏感信息
 * 3. 支持错误上报集成
 */
class GymBroReleaseTree : Timber.Tree() {
    override fun isLoggable(
        tag: String?,
        priority: Int,
    ): Boolean {
        // 只允许警告和错误级别的日志
        return priority >= Log.WARN
    }

    override fun log(
        priority: Int,
        tag: String?,
        message: String,
        t: Throwable?,
    ) {
        if (!isLoggable(tag, priority)) {
            return
        }

        // 过滤敏感信息
        val sanitizedMessage = sanitizeMessage(message)
        val finalTag = tag ?: "GymBro"

        // 根据优先级处理日志
        when (priority) {
            Log.ERROR -> {
                Timber.tag(finalTag).e(t, sanitizedMessage)
                reportError(finalTag, sanitizedMessage, t)
            }
            Log.ASSERT -> {
                Log.wtf(finalTag, sanitizedMessage, t)
                reportFatalError(finalTag, sanitizedMessage, t)
            }
            else -> {
                Log.w(finalTag, sanitizedMessage, t)
            }
        }
    }

    /**
     * 过滤敏感信息
     */
    private fun sanitizeMessage(message: String): String = SensitiveDataFilter.filterSensitiveData(message)

    /**
     * 上报错误信息
     *
     * 可由应用层实现实际上报逻辑
     */
    @Suppress("UNUSED_PARAMETER")
    private fun reportError(
        tag: String,
        message: String,
        throwable: Throwable?,
    ) {
        // 应用层可扩展，例如集成Firebase Crashlytics
        // TODO: 实现错误上报逻辑
    }

    /**
     * 上报致命错误
     *
     * 可由应用层实现实际上报逻辑
     */
    @Suppress("UNUSED_PARAMETER")
    private fun reportFatalError(
        tag: String,
        message: String,
        throwable: Throwable?,
    ) {
        // 应用层可扩展，例如集成Firebase Crashlytics并标记为非恢复性错误
        // TODO: 实现致命错误上报逻辑
    }
}

```

File: D:/GymBro/GymBro/core/src/main/kotlin/com/example/gymbro/core/logging/TimberLogger.kt
```kotlin
package com.example.gymbro.core.logging

import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Timber日志器实现
 *
 * 实现Logger接口，将日志调用委托给Timber框架。
 * 提供统一的日志接口，解耦具体的日志框架实现。
 */
@Singleton
class TimberLogger @Inject constructor() : Logger {

    override fun v(message: String, vararg args: Any?) {
        Timber.v(message, *args)
    }

    override fun v(t: Throwable?, message: String, vararg args: Any?) {
        Timber.v(t, message, *args)
    }

    override fun v(t: Throwable?) {
        Timber.v(t)
    }

    override fun d(message: String, vararg args: Any?) {
        Timber.d(message, *args)
    }

    override fun d(t: Throwable?, message: String, vararg args: Any?) {
        Timber.d(t, message, *args)
    }

    override fun d(t: Throwable?) {
        Timber.d(t)
    }

    override fun i(message: String, vararg args: Any?) {
        Timber.i(message, *args)
    }

    override fun i(t: Throwable?, message: String, vararg args: Any?) {
        Timber.i(t, message, *args)
    }

    override fun i(t: Throwable?) {
        Timber.i(t)
    }

    override fun w(message: String, vararg args: Any?) {
        Timber.w(message, *args)
    }

    override fun w(t: Throwable?, message: String, vararg args: Any?) {
        Timber.w(t, message, *args)
    }

    override fun w(t: Throwable?) {
        Timber.w(t)
    }

    override fun e(message: String, vararg args: Any?) {
        Timber.e(message, *args)
    }

    override fun e(t: Throwable?, message: String, vararg args: Any?) {
        Timber.e(t, message, *args)
    }

    override fun e(t: Throwable?) {
        Timber.e(t)
    }

    override fun wtf(message: String, vararg args: Any?) {
        Timber.wtf(message, *args)
    }

    override fun wtf(t: Throwable?, message: String, vararg args: Any?) {
        Timber.wtf(t, message, *args)
    }

    override fun wtf(t: Throwable?) {
        Timber.wtf(t)
    }

    override fun tag(tag: String): Logger {
        return TaggedTimberLogger(tag)
    }
}

/**
 * 带标签的Timber日志器
 *
 * 为特定标签创建的日志器实例，所有日志调用都会带上指定的标签。
 */
private class TaggedTimberLogger(private val tag: String) : Logger {

    override fun v(message: String, vararg args: Any?) {
        Timber.tag(tag).v(message, *args)
    }

    override fun v(t: Throwable?, message: String, vararg args: Any?) {
        Timber.tag(tag).v(t, message, *args)
    }

    override fun v(t: Throwable?) {
        Timber.tag(tag).v(t)
    }

    override fun d(message: String, vararg args: Any?) {
        Timber.tag(tag).d(message, *args)
    }

    override fun d(t: Throwable?, message: String, vararg args: Any?) {
        Timber.tag(tag).d(t, message, *args)
    }

    override fun d(t: Throwable?) {
        Timber.tag(tag).d(t)
    }

    override fun i(message: String, vararg args: Any?) {
        Timber.tag(tag).i(message, *args)
    }

    override fun i(t: Throwable?, message: String, vararg args: Any?) {
        Timber.tag(tag).i(t, message, *args)
    }

    override fun i(t: Throwable?) {
        Timber.tag(tag).i(t)
    }

    override fun w(message: String, vararg args: Any?) {
        Timber.tag(tag).w(message, *args)
    }

    override fun w(t: Throwable?, message: String, vararg args: Any?) {
        Timber.tag(tag).w(t, message, *args)
    }

    override fun w(t: Throwable?) {
        Timber.tag(tag).w(t)
    }

    override fun e(message: String, vararg args: Any?) {
        Timber.tag(tag).e(message, *args)
    }

    override fun e(t: Throwable?, message: String, vararg args: Any?) {
        Timber.tag(tag).e(t, message, *args)
    }

    override fun e(t: Throwable?) {
        Timber.tag(tag).e(t)
    }

    override fun wtf(message: String, vararg args: Any?) {
        Timber.tag(tag).wtf(message, *args)
    }

    override fun wtf(t: Throwable?, message: String, vararg args: Any?) {
        Timber.tag(tag).wtf(t, message, *args)
    }

    override fun wtf(t: Throwable?) {
        Timber.tag(tag).wtf(t)
    }

    override fun tag(tag: String): Logger {
        return TaggedTimberLogger(tag)
    }
}

```

File: D:/GymBro/GymBro/core-network/src/main/kotlin/com/example/gymbro/core/network/rest/interceptors/SafeLoggingInterceptor.kt
```kotlin
package com.example.gymbro.core.network.rest.interceptors

import com.example.gymbro.core.network.security.PiiSanitizer
import okhttp3.Interceptor
import okhttp3.Response
import okio.Buffer
import timber.log.Timber
import java.nio.charset.Charset

/**
 * 安全的日志拦截器
 *
 * 提供网络请求日志记录，同时保护敏感信息
 * 集成PII数据脱敏处理，确保生产环境日志安全合规
 */
class SafeLoggingInterceptor(
    private val isDebugMode: Boolean = false,
    private val isProduction: Boolean = !isDebugMode,
) : Interceptor {

    companion object {
        private val SENSITIVE_HEADERS = setOf(
            "authorization",
            "cookie",
            "set-cookie",
            "x-api-key",
            "api-key",
        )

        private val SENSITIVE_BODY_KEYS = setOf(
            "password",
            "token",
            "secret",
            "key",
            "authorization",
        )
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val startTime = System.currentTimeMillis()

        if (isDebugMode) {
            logRequest(request)
        }

        val response = try {
            chain.proceed(request)
        } catch (e: Exception) {
            val duration = System.currentTimeMillis() - startTime
            Timber.e(e, "🚨 网络请求失败: ${request.method} ${request.url} (${duration}ms)")
            throw e
        }

        val duration = System.currentTimeMillis() - startTime

        if (isDebugMode) {
            logResponse(response, duration)
        } else {
            // 生产模式只记录基本信息
            Timber.d("🌐 ${request.method} ${request.url} - ${response.code} (${duration}ms)")
        }

        return response
    }

    private fun logRequest(request: okhttp3.Request) {
        Timber.d("🚀 --> ${request.method} ${request.url}")

        // 记录头部（过滤敏感信息）
        request.headers.forEach { (name, value) ->
            if (name.lowercase() in SENSITIVE_HEADERS) {
                Timber.v("📋 $name: ***")
            } else {
                Timber.v("📋 $name: $value")
            }
        }

        // 记录请求体（如果存在且不太大）
        request.body?.let { body ->
            if (body.contentLength() in 1..8192) { // 最大8KB
                val buffer = Buffer()
                body.writeTo(buffer)
                val charset = body.contentType()?.charset(Charset.forName("UTF-8")) ?: Charset.forName("UTF-8")
                val content = buffer.readString(charset)

                val sanitizedContent = sanitizeContent(content)
                Timber.v("📝 Request Body: $sanitizedContent")
            } else {
                Timber.v("📝 Request Body: [${body.contentLength()} bytes]")
            }
        }

        Timber.d("🚀 --> END ${request.method}")
    }

    private fun logResponse(response: Response, duration: Long) {
        Timber.d("🏁 <-- ${response.code} ${response.message} ${response.request.url} (${duration}ms)")

        // 记录响应头部
        response.headers.forEach { (name, value) ->
            if (name.lowercase() in SENSITIVE_HEADERS) {
                Timber.v("📋 $name: ***")
            } else {
                Timber.v("📋 $name: $value")
            }
        }

        // 记录响应体（如果存在且不太大）
        response.body?.let { body ->
            val contentLength = body.contentLength()
            if (contentLength in 1..8192) { // 最大8KB
                val source = body.source()
                source.request(Long.MAX_VALUE)
                val buffer = source.buffer

                val charset = body.contentType()?.charset(Charset.forName("UTF-8")) ?: Charset.forName("UTF-8")
                val content = buffer.clone().readString(charset)

                val sanitizedContent = sanitizeContent(content)
                Timber.v("📝 Response Body: $sanitizedContent")
            } else {
                Timber.v("📝 Response Body: [$contentLength bytes]")
            }
        }

        Timber.d("🏁 <-- END HTTP")
    }

    /**
     * 清理敏感内容
     * 使用PiiSanitizer进行全面的敏感信息脱敏
     */
    private fun sanitizeContent(content: String): String {
        // 首先进行JSON字段脱敏
        var sanitized = PiiSanitizer.sanitizeJsonFields(content, isProduction)

        // 然后进行通用内容脱敏（邮箱、手机号等）
        sanitized = PiiSanitizer.sanitizeContent(sanitized, isProduction)

        // 兼容原有的简单字段过滤（作为备用）
        SENSITIVE_BODY_KEYS.forEach { key ->
            val regex = """"$key"\s*:\s*"[^"]*"""".toRegex(RegexOption.IGNORE_CASE)
            sanitized = sanitized.replace(regex, """"$key":"***"""")
        }

        return sanitized
    }
}

```

File: D:/GymBro/GymBro/designSystem/src/main/kotlin/com/example/gymbro/designSystem/components/GymBroLoginButton.kt
```kotlin
package com.example.gymbro.designSystem.components

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens

/**
 * 通用登录/操作按钮，支持文本、可选图标（Vector或Painter）和自定义颜色。
 *
 * @param text 按钮上显示的文本 (UiText)
 * @param onClick 点击事件回调
 * @param modifier Modifier修饰符
 * @param iconVector 可选的矢量图标 (ImageVector)
 * @param iconPainter 可选的绘制图标 (Painter) - 不能与iconVector同时提供
 * @param iconContentDescription 图标的可访问性描述 (UiText)，如果提供了图标则建议设置
 * @param backgroundColor 按钮背景色
 * @param contentColor 按钮内容（文本和图标）颜色
 * @param enabled 是否启用按钮
 * @param isLoading 是否显示加载指示器
 */
@Composable
fun loginButton(
    text: UiText,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    iconVector: ImageVector? = null,
    iconPainter: Painter? = null,
    iconContentDescription: UiText? = null,
    backgroundColor: Color = MaterialTheme.colorScheme.primary,
    contentColor: Color = MaterialTheme.colorScheme.onPrimary,
    enabled: Boolean = true,
    isLoading: Boolean = false,
) {
    // Ensure only one icon type is provided
    check(iconVector == null || iconPainter == null) {
        "LoginButton cannot accept both an ImageVector and a Painter. Provide only one."
    }

    Button(
        onClick = onClick,
        modifier =
        modifier
            .fillMaxWidth()
            .height(Tokens.Spacing.Massive),
        enabled = enabled && !isLoading,
        shape = RoundedCornerShape(Tokens.Radius.Button),
        colors =
        ButtonDefaults.buttonColors(
            containerColor = backgroundColor,
            contentColor = contentColor,
            disabledContainerColor = backgroundColor.copy(alpha = 0.5f),
            disabledContentColor = contentColor.copy(alpha = 0.5f),
        ),
    ) {
        val description = iconContentDescription?.asString()

        // Conditionally display icon based on provided type
        when {
            iconVector != null -> {
                Icon(
                    imageVector = iconVector,
                    contentDescription = description,
                    modifier = Modifier.size(ButtonDefaults.IconSize),
                )
                Spacer(Modifier.size(ButtonDefaults.IconSpacing))
            }
            iconPainter != null -> {
                Icon(
                    painter = iconPainter,
                    contentDescription = description,
                    modifier = Modifier.size(ButtonDefaults.IconSize),
                )
                Spacer(Modifier.size(ButtonDefaults.IconSpacing))
            }
        }

        if (isLoading) {
            CircularProgressIndicator(
                color = contentColor,
                modifier = Modifier.height(Tokens.Spacing.Large),
            )
        } else {
            Text(
                text = text.asString(),
                style =
                MaterialTheme.typography.bodyLarge.copy(
                    fontWeight = FontWeight.Bold,
                ),
            )
        }
    }
}

@GymBroPreview
@Composable
private fun loginButtonPrimaryPreview() {
    GymBroTheme {
        loginButton(
            text = UiText.DynamicString("登录"),
            onClick = { },
        )
    }
}

@GymBroPreview
@Composable
private fun loginButtonSecondaryPreview() {
    GymBroTheme {
        loginButton(
            text = UiText.DynamicString("注册"),
            onClick = { },
            backgroundColor = MaterialTheme.colorScheme.secondary,
            contentColor = MaterialTheme.colorScheme.onSecondary,
        )
    }
}

@GymBroPreview
@Composable
private fun loginButtonLoadingPreview() {
    GymBroTheme {
        loginButton(
            text = UiText.DynamicString("登录中..."),
            onClick = { },
            isLoading = true,
        )
    }
}

@GymBroPreview
@Composable
private fun loginButtonDisabledPreview() {
    GymBroTheme {
        loginButton(
            text = UiText.DynamicString("禁用按钮"),
            onClick = { },
            enabled = false,
        )
    }
}

```

File: D:/GymBro/GymBro/core/src/main/kotlin/com/example/gymbro/core/logging/TokenFlowDebugger.kt
```kotlin
package com.example.gymbro.core.logging

import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * TOKEN-FLOW 调试器
 *
 * 专门用于调试 ThinkingBox 流式响应问题的工具类
 */
@Singleton
class TokenFlowDebugger
    @Inject
    constructor(
        private val timberManager: TimberManager,
        private val loggingController: LoggingController,
    ) {
        companion object {
            private const val TAG = "TokenFlowDebugger"
        }

        /**
         * 启用 TOKEN-FLOW 调试模式
         *
         * 这将启用所有与流式响应相关的详细日志
         */
        fun enableDebugMode() {
            try {
                loggingController.applyPreset(LoggingController.LoggingPreset.TOKEN_FLOW_DEBUG)

                Timber.tag(TAG).i("🚀 TOKEN-FLOW 调试模式已启用")
                Timber.tag(TAG).i("📋 现在将显示以下日志：")
                Timber.tag(TAG).i("  - 🔗 SSE连接建立")
                Timber.tag(TAG).i("  - 📨 SSE事件接收")
                Timber.tag(TAG).i("  - 🔍 SSE数据解析")
                Timber.tag(TAG).i("  - 🚀 Token路由")
                Timber.tag(TAG).i("  - 🎯 ConversationScope发射")
                Timber.tag(TAG).i("  - 📥 ThinkingBox接收")

                printUsageInstructions()
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "❌ 启用TOKEN-FLOW调试模式失败")
            }
        }

        /**
         * 关闭 TOKEN-FLOW 调试模式
         */
        fun disableDebugMode() {
            try {
                timberManager.disableTokenFlowDebugLogs()
                Timber.tag(TAG).i("🔒 TOKEN-FLOW 调试模式已关闭")
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "❌ 关闭TOKEN-FLOW调试模式失败")
            }
        }

        /**
         * 打印使用说明
         */
        private fun printUsageInstructions() {
            val instructions =
                buildString {
                    appendLine("=== TOKEN-FLOW 调试使用说明 ===")
                    appendLine()
                    appendLine("🔍 关键日志标签过滤命令：")
                    appendLine("adb logcat | grep TOKEN-FLOW")
                    appendLine()
                    appendLine("📋 调试步骤：")
                    appendLine("1. 发送AI消息")
                    appendLine("2. 查找 '🔗 [AdaptiveStreamClient] SSE连接已建立'")
                    appendLine("3. 查找 '📨 [AdaptiveStreamClient] 收到SSE事件'")
                    appendLine("4. 查找 '🚀 [AiCoachViewModel] 收到token'")
                    appendLine("5. 查找 '🎯 [ConversationScope] 准备发射token'")
                    appendLine("6. 查找 '📥 [parseTokenStream] 收到chunk'")
                    appendLine()
                    appendLine("❌ 如果某个步骤的日志缺失，说明问题在该环节")
                    appendLine()
                    appendLine("=== 使用说明结束 ===")
                }

            Timber.tag(TAG).i(instructions)
        }

        /**
         * 检查当前调试状态
         */
        fun checkDebugStatus(): String =
            try {
                val config = timberManager.getCurrentConfig()
                "TOKEN-FLOW 调试状态: $config"
            } catch (e: Exception) {
                "❌ 无法获取调试状态: ${e.message}"
            }

        /**
         * 快速诊断工具
         */
        fun quickDiagnose() {
            Timber.tag(TAG).i("🔍 开始快速诊断...")

            // 检查日志配置
            val status = checkDebugStatus()
            Timber.tag(TAG).i("📊 $status")

            // 提供诊断建议
            val suggestions =
                buildString {
                    appendLine("💡 快速诊断建议：")
                    appendLine("1. 确保已启用TOKEN-FLOW调试模式")
                    appendLine("2. 发送简单消息测试：'你好'")
                    appendLine("3. 观察日志输出，找到断点位置")
                    appendLine("4. 根据断点位置定位问题")
                }

            Timber.tag(TAG).i(suggestions)
        }

        /**
         * 生成日志过滤脚本
         */
        fun generateLogFilterScript(): String =
            buildString {
                appendLine("#!/bin/bash")
                appendLine("# TOKEN-FLOW 日志过滤脚本")
                appendLine()
                appendLine("echo '=== TOKEN-FLOW 调试日志 ==='")
                appendLine("adb logcat -c  # 清除旧日志")
                appendLine("adb logcat | grep -E 'TOKEN-FLOW|AdaptiveStreamClient|AiCoachViewModel|ConversationScope|parseTokenStream'")
            }
    }

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/kotlin/com/example/gymbro/buildlogic/PerformanceOptimizationPlugin.kt
```kotlin
package com.example.gymbro.buildlogic

import com.android.build.api.dsl.ApplicationExtension
import com.android.build.api.dsl.LibraryExtension
import com.example.gymbro.buildlogic.utils.libs
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.dependencies
import org.gradle.kotlin.dsl.withType
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

/**
 * 🚀 GymBro APK大小优化插件
 *
 * 专注于APK大小优化，包括：
 * - 代码混淆和压缩
 * - 资源压缩和过滤
 * - Bundle分割
 * - 无用文件排除
 *
 * 目标：减少APK大小30-50%
 */
class PerformanceOptimizationPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            // 只对Application模块应用APK大小优化
            pluginManager.withPlugin("com.android.application") {
                configureApkSizeOptimization()
            }
        }
    }

    /**
     * 🎯 只在制作APK时启用的大小优化配置
     *
     * 检测构建任务：
     * - assembleRelease: 制作Release APK
     * - bundleRelease: 制作AAB包
     * - 其他情况不启用优化，保持开发构建速度
     */
    private fun Project.configureApkSizeOptimization() {
        extensions.configure<ApplicationExtension> {
            buildTypes {
                release {
                    // 📦 检测是否启用APK优化
                    // 方法1: 通过Gradle属性控制 -PenableApkOptimization=true
                    val enableApkOptimization = project.findProperty("enableApkOptimization")?.toString()?.toBoolean() ?: false

                    // 方法2: 检测APK构建任务
                    val requestedTasks = gradle.startParameter.taskNames
                    val isApkBuildTask = requestedTasks.any { taskName ->
                        taskName.matches(Regex(".*assemble.*Release.*", RegexOption.IGNORE_CASE)) ||
                        taskName.matches(Regex(".*bundle.*Release.*", RegexOption.IGNORE_CASE)) ||
                        taskName.equals("assembleRelease", ignoreCase = true) ||
                        taskName.equals("bundleRelease", ignoreCase = true)
                    }

                    val isApkBuild = enableApkOptimization || isApkBuildTask

                    if (isApkBuild) {
                        // 📦 APK大小优化核心配置（仅APK构建时）
                        isMinifyEnabled = true          // 代码混淆压缩 (-30%代码大小)
                        isShrinkResources = true        // 资源压缩 (-20%资源大小)
                        isCrunchPngs = true            // PNG压缩 (-10%图片大小)

                        logger.lifecycle("🚀 APK大小优化已启用 - 检测到APK构建任务")
                    } else {
                        // 📦 开发构建：关闭优化，保持构建速度
                        isMinifyEnabled = false
                        isShrinkResources = false
                        isCrunchPngs = false

                        logger.lifecycle("⚡ 开发模式 - APK优化已关闭，保持构建速度")
                    }

                    proguardFiles(
                        getDefaultProguardFile("proguard-android-optimize.txt"),
                        "proguard-rules.pro"
                    )
                }
            }

            // 📦 Bundle分割优化（仅影响Google Play分发）
            bundle {
                language.enableSplit = true     // 语言分割 (-40%下载大小)
                density.enableSplit = true      // 密度分割 (-30%下载大小)
                abi.enableSplit = true          // ABI分割 (-50%下载大小)
            }

            // 📦 资源过滤优化（始终启用，影响较小）
            androidResources {
                // 只保留中英文资源
                localeFilters += listOf("en", "zh-rCN")  // (-60%语言资源)
            }

            // 📦 无用文件排除（始终启用，影响较小）
            packaging {
                resources {
                    excludes += listOf(
                        "/META-INF/{AL2.0,LGPL2.1}",
                        "META-INF/*.kotlin_module",
                        "META-INF/INDEX.LIST",
                        "META-INF/DEPENDENCIES",
                        "META-INF/LICENSE*",
                        "META-INF/NOTICE*"
                    )
                }
            }
        }
    }


}

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/resources/testSkeletons/domain.kt.template
```template
package {{PACKAGE}}

import io.mockk.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Before
import org.junit.Test
import kotlin.test.*

// GymBro Core imports
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ErrorCategory
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.core.logging.Logger
import kotlinx.datetime.Instant

/**
 * Generated by genTest for GymBro Domain Module
 * ✔️ 使用 TestScope 和 runTest 处理协程
 * ✔️ 使用 ModernResult<T> 统一错误处理
 * ✔️ 使用 MockK 进行依赖模拟
 * ✔️ 遵循 Clean Architecture 原则
 * ✔️ 基础测试结构，需要根据实际功能调整
 */
@OptIn(ExperimentalCoroutinesApi::class)
class {{CLASS}}Test {

    // Test Dependencies - 根据被测试类的实际依赖调整
    private val mockLogger = mockk<Logger>()
    // TODO: 添加其他必要的mock依赖，例如：
    // private val mockRepository = mockk<SomeRepository>()

    // Test Scope
    private val testScope = TestScope()

    // Test Data - 根据实际需要调整
    private val testData = "test_data"

    // System Under Test - 根据实际被测试类调整
    private lateinit var useCase: {{CLASS}}

    @Before
    fun setup() {
        // 配置Mock行为
        every { mockLogger.d(any<String>()) } just Runs
        every { mockLogger.w(any<String>()) } just Runs
        every { mockLogger.e(any<String>(), any<Throwable>()) } just Runs

        // 初始化被测试对象 - 根据实际构造函数调整
        // useCase = {{CLASS}}(
        //     repository = mockRepository,
        //     logger = mockLogger
        // )

        // TODO: 取消注释并根据实际构造函数调整上面的代码
    }

    @After
    fun tearDown() {
        clearAllMocks()
    }

    @Test
    fun `{{CLASS}} should handle basic functionality correctly`() = testScope.runTest {
        // Given - 准备测试数据
        // TODO: 根据实际{{CLASS}}功能添加测试数据

        // When - 执行被测试的操作
        // TODO: 调用实际的{{CLASS}}方法

        // Then - 验证结果
        // TODO: 验证结果
        assertTrue(true, "请根据实际{{CLASS}}功能实现此测试")
    }

    @Test
    fun `{{CLASS}} should handle error cases correctly`() = testScope.runTest {
        // Given - 准备错误场景
        // TODO: 准备错误场景测试数据

        // When & Then - 执行操作并验证错误处理
        // TODO: 调用{{CLASS}}方法验证错误处理
        assertTrue(true, "请根据实际{{CLASS}}错误处理逻辑实现此测试")
    }

    @Test
    fun `{{CLASS}} should handle UiText correctly`() = testScope.runTest {
        // Given - UiText测试
        val testText = UiText.DynamicString("测试文本")

        // When & Then - 验证UiText处理
        assertEquals("测试文本", testText.value)
        assertTrue(testText is UiText.DynamicString)
    }

    @Test
    fun `{{CLASS}} should handle time operations with kotlinx datetime`() = testScope.runTest {
        // Given - 时间相关测试
        val testTime = Instant.parse("2025-01-30T10:00:00Z")
        val currentTime = kotlinx.datetime.Clock.System.now()

        // When & Then - 验证时间处理
        assertTrue(currentTime >= testTime)
        assertNotNull(testTime)
    }

    // TODO: 根据实际{{CLASS}}的功能添加更多特定测试
    // 例如：
    // - 如果是UseCase，添加业务逻辑测试
    // - 如果返回Flow，使用Turbine测试Flow发射序列
    // - 如果有复杂的错误处理，添加各种错误场景测试
}

```

File: D:/GymBro/GymBro/gradle/build-logic/build.gradle.kts
```kotlin
plugins {
    `kotlin-dsl`
}

group = "com.example.gymbro.buildlogic"

java {
    toolchain {
        languageVersion.set(JavaLanguageVersion.of(17))
    }
}

repositories {
    google()
    mavenCentral()
    gradlePluginPortal()
}

dependencies {
    implementation(libs.android.gradlePlugin)
    implementation(libs.kotlin.gradlePlugin)
    implementation(libs.ksp.gradlePlugin)
    implementation(libs.hilt.gradlePlugin)
    // Compose plugin is part of Kotlin plugin, no separate dependency needed

    // Kotlin Serialization plugin
    implementation("org.jetbrains.kotlin:kotlin-serialization:2.1.10")

    // CI/CD & Quality Tools
    implementation("org.jlleitschuh.gradle:ktlint-gradle:12.1.1")
    implementation("org.owasp:dependency-check-gradle:11.1.0")
    implementation("io.gitlab.arturbosch.detekt:detekt-gradle-plugin:1.23.8")
}

gradlePlugin {
    plugins {
        register("androidApplication") {
            id = "gymbro.android.application"
            implementationClass = "com.example.gymbro.buildlogic.AndroidApplicationConventionPlugin"
        }
        register("androidLibrary") {
            id = "gymbro.android.library"
            implementationClass = "com.example.gymbro.buildlogic.AndroidLibraryConventionPlugin"
        }
        register("featureLibrary") {
            id = "gymbro.feature.library"
            implementationClass = "com.example.gymbro.buildlogic.FeatureLibraryConventionPlugin"
        }
        register("hiltLibrary") {
            id = "gymbro.hilt.library"
            implementationClass = "com.example.gymbro.buildlogic.HiltConventionPlugin"
        }
        register("composeLibrary") {
            id = "gymbro.compose.library"
            implementationClass = "com.example.gymbro.buildlogic.ComposeConventionPlugin"
        }
        register("domainModule") {
            id = "gymbro.domain.module"
            implementationClass = "com.example.gymbro.buildlogic.DomainModuleConventionPlugin"
        }
        register("dataModule") {
            id = "gymbro.data.module"
            implementationClass = "com.example.gymbro.buildlogic.DataModuleConventionPlugin"
        }
        register("testingLibrary") {
            id = "gymbro.testing.library"
            implementationClass = "com.example.gymbro.buildlogic.TestingConventionPlugin"
        }
        register("kotlinSerialization") {
            id = "gymbro.kotlin.serialization"
            implementationClass = "com.example.gymbro.buildlogic.KotlinSerializationConventionPlugin"
        }
        register("coreDependencies") {
            id = "gymbro.core.dependencies"
            implementationClass = "com.example.gymbro.buildlogic.CoreDependenciesConventionPlugin"
        }
        register("firebase") {
            id = "gymbro.firebase"
            implementationClass = "com.example.gymbro.buildlogic.FirebaseConventionPlugin"
        }
        register("room") {
            id = "gymbro.room"
            implementationClass = "com.example.gymbro.buildlogic.RoomConventionPlugin"
        }
        register("kotlinJvm") {
            id = "gymbro.kotlin.jvm"
            implementationClass = "com.example.gymbro.buildlogic.KotlinJvmConventionPlugin"
        }
        register("jacoco") {
            id = "gymbro.jacoco"
            implementationClass = "com.example.gymbro.buildlogic.JacocoConventionPlugin"
        }
        register("ktlint") {
            id = "gymbro.ktlint"
            implementationClass = "com.example.gymbro.buildlogic.KtlintConventionPlugin"
        }
        register("security") {
            id = "gymbro.security"
            implementationClass = "com.example.gymbro.buildlogic.SecurityConventionPlugin"
        }
        register("detekt") {
            id = "gymbro.detekt"
            implementationClass = "com.example.gymbro.buildlogic.DetektConventionPlugin"
        }
        register("quality") {
            id = "gymbro.quality"
            implementationClass = "com.example.gymbro.buildlogic.QualityConventionPlugin"
        }
        register("testing-official") {
            id = "gymbro.testing.official"
            implementationClass = "com.example.gymbro.buildlogic.OfficialTestingStandardPlugin"
        }
        register("performanceOptimization") {
            id = "gymbro.performance.optimization"
            implementationClass = "com.example.gymbro.buildlogic.PerformanceOptimizationPlugin"
        }
    }
}

```

File: D:/GymBro/GymBro/features/auth/src/main/kotlin/com/example/gymbro/features/auth/ui/components/EmailLoginForm.kt
```kotlin
package com.example.gymbro.features.auth.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.GymBroButton
import com.example.gymbro.designSystem.components.GymBroButtonDefaults
import com.example.gymbro.designSystem.components.emailField
import com.example.gymbro.designSystem.components.passwordField
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens

/**
 * 邮箱登录表单组件 - 迁移到新设计系统
 */
@Composable
fun EmailLoginForm(
    onLoginClick: () -> Unit,
    onRegisterClick: () -> Unit,
    onPhoneLoginClick: () -> Unit,
    onForgotPasswordClick: () -> Unit,
    isLoading: Boolean = false,
    modifier: Modifier = Modifier,
) {
    var email by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    val isFormValid = email.isNotBlank() && password.isNotBlank()

    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Large),
    ) {
        // Email Input - 升级到SmartInput系统
        emailField(
            value = email,
            onValueChange = { email = it },
            modifier = Modifier.fillMaxWidth(),
        )

        // Password Input - 升级到SmartInput系统
        passwordField(
            value = password,
            onValueChange = { password = it },
            modifier = Modifier.fillMaxWidth(),
        )

        Spacer(modifier = Modifier.height(Tokens.Spacing.Small))

        // Login Button - 使用新设计系统组件
        GymBroButton(
            onClick = onLoginClick,
            text = UiText.DynamicString("登录"),
            modifier = Modifier.fillMaxWidth(),
            enabled = isFormValid && !isLoading,
            isLoading = isLoading,
            colors = GymBroButtonDefaults.colors(
                containerColor = Tokens.Color.CTAPrimary,
                contentColor = Tokens.Color.Gray950,
            ),
        )

        // Register Button - 使用新设计系统组件
        GymBroButton(
            onClick = onRegisterClick,
            text = UiText.DynamicString("注册新账户"),
            modifier = Modifier.fillMaxWidth(),
            colors = GymBroButtonDefaults.secondaryColors(),
        )

        // Phone Login Button - 使用新设计系统组件
        GymBroButton(
            onClick = onPhoneLoginClick,
            text = UiText.DynamicString("手机号登录"),
            modifier = Modifier.fillMaxWidth(),
            colors = GymBroButtonDefaults.outlinedColors(),
        )

        // 忘记密码按钮
        TextButton(
            onClick = onForgotPasswordClick,
            modifier = Modifier.align(Alignment.CenterHorizontally),
        ) {
            Text(
                text = "忘记密码？",
                color = Tokens.Color.BrandPrimary,
            )
        }
    }
}

// === 预览组件 ===

@GymBroPreview
@Composable
private fun EmailLoginFormPreview() {
    GymBroTheme {
        EmailLoginForm(
            onLoginClick = {},
            onRegisterClick = {},
            onPhoneLoginClick = {},
            onForgotPasswordClick = {},
        )
    }
}

@GymBroPreview
@Composable
private fun EmailLoginFormLightPreview() {
    GymBroTheme {
        EmailLoginForm(
            onLoginClick = {},
            onRegisterClick = {},
            onPhoneLoginClick = {},
            onForgotPasswordClick = {},
        )
    }
}

@GymBroPreview
@Composable
private fun EmailLoginFormEmptyPreview() {
    GymBroTheme {
        EmailLoginForm(
            onLoginClick = {},
            onRegisterClick = {},
            onPhoneLoginClick = {},
            onForgotPasswordClick = {},
        )
    }
}

@GymBroPreview
@Composable
private fun EmailLoginFormLoadingPreview() {
    GymBroTheme {
        EmailLoginForm(
            onLoginClick = {},
            onRegisterClick = {},
            onPhoneLoginClick = {},
            onForgotPasswordClick = {},
            isLoading = true,
        )
    }
}

```

File: D:/GymBro/GymBro/designSystem/src/main/kotlin/com/example/gymbro/designSystem/components/animations/GymBroLogo.kt
```kotlin
package com.example.gymbro.designSystem.components.animations

import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.components.extensions.gymBroBreathing
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.motion.MotionDurations

/**
 * GymBro Logo组件 - 使用统一动画系统
 *
 * 特性：
 * - 统一的呼吸动画效果
 * - 支持可选副标题
 * - 动画配置可控制
 * - 响应系统动画缩放设置
 *
 * @param title 主标题文本
 * @param subtitle 副标题文本（可选）
 * @param modifier Modifier修饰符
 * @param titleColor 主标题颜色
 * @param subtitleColor 副标题颜色
 * @param style 文本样式
 * @param enableAnimation 是否启用呼吸动画
 */
@Composable
fun GymBroLogo(
    title: UiText,
    subtitle: UiText? = null,
    modifier: Modifier = Modifier,
    titleColor: Color = MaterialTheme.colorScheme.onBackground,
    subtitleColor: Color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.8f),
    style: TextStyle =
        MaterialTheme.typography.displaySmall.copy(
            fontWeight = FontWeight.Bold,
        ),
    enableAnimation: Boolean = true,
) {
    val displayText =
        if (subtitle != null) {
            "${title.asString()}\n${subtitle.asString()}"
        } else {
            title.asString()
        }

    val animationModifier =
        if (enableAnimation) {
            Modifier.gymBroBreathing(
                amplitude = 16.dp, // BREATHING_AMPLITUDE_MEDIUM
                duration = MotionDurations.L,
            )
        } else {
            Modifier
        }

    Text(
        text = displayText,
        textAlign = TextAlign.Center,
        color = titleColor,
        style = style,
        lineHeight = style.fontSize * 1.1f,
        modifier = modifier.then(animationModifier),
    )
}

/**
 * 简化版GymBro Logo，只显示主标题
 *
 * @param title 标题文本
 * @param modifier Modifier修饰符
 * @param titleColor 标题颜色
 * @param style 文本样式
 * @param enableAnimation 是否启用动画
 */
@Composable
fun GymBroLogoSimple(
    title: UiText,
    modifier: Modifier = Modifier,
    titleColor: Color = MaterialTheme.colorScheme.onBackground,
    style: TextStyle =
        MaterialTheme.typography.displaySmall.copy(
            fontWeight = FontWeight.Bold,
        ),
    enableAnimation: Boolean = true,
) {
    GymBroLogo(
        title = title,
        subtitle = null,
        modifier = modifier,
        titleColor = titleColor,
        style = style,
        enableAnimation = enableAnimation,
    )
}

// === Preview组件 ===

@GymBroPreview
@Composable
private fun GymBroLogoPreview() {
    GymBroTheme {
        GymBroLogo(
            title = UiText.DynamicString("GymBro"),
            subtitle = UiText.DynamicString("Android Beta"),
            titleColor = Color.White,
            subtitleColor = Color.White.copy(alpha = 0.8f),
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroLogoSimplePreview() {
    GymBroTheme {
        GymBroLogoSimple(
            title = UiText.DynamicString("GymBro"),
            titleColor = Color.Black,
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroLogoNoAnimationPreview() {
    GymBroTheme {
        GymBroLogo(
            title = UiText.DynamicString("GymBro"),
            subtitle = UiText.DynamicString("Static Mode"),
            enableAnimation = false,
        )
    }
}

```

File: D:/GymBro/GymBro/features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/internal/logging/TBLog.kt
```kotlin
package com.example.gymbro.features.thinkingbox.internal.logging

import timber.log.Timber
import java.util.concurrent.atomic.AtomicInteger

/**
 * TBLog - ThinkingBox 统一日志系统
 *
 * 🎯 目标：一张日志即可还原每批数据的完整旅程
 * 🔑 关键思路：批次-ID + 阶段标识 + 内容摘要
 *
 * 基于用户提供的专业日志落位方案实现
 */
object TBLog {

    /**
     * 全局日志开关
     * Release 默认关闭，Debug 默认开启
     */
    var enabled = true // 默认开启，可通过代码控制

    /**
     * 批次ID生成器 - 用于追踪同一Flow<String>源的完整旅程
     */
    private val batchId = AtomicInteger(0)

    /**
     * Token批量处理间隔（毫秒）
     * 每80ms批量log，避免逐字轰炸Logcat
     */
    const val TOKEN_BATCH_MS = 80L

    /**
     * 生成新的批次ID
     */
    fun nextBatchId(): Int = batchId.incrementAndGet()

    /**
     * 重置批次ID（用于测试或新会话）
     */
    fun resetBatchId() {
        batchId.set(0)
    }

    /**
     * 条件日志记录 - 只在enabled=true时执行
     */
    inline fun log(tag: String, level: LogLevel = LogLevel.DEBUG, block: () -> String) {
        if (enabled) {
            val message = block()
            when (level) {
                LogLevel.VERBOSE -> Timber.tag(tag).v(message)
                LogLevel.DEBUG -> Timber.tag(tag).d(message)
                LogLevel.INFO -> Timber.tag(tag).i(message)
                LogLevel.WARN -> Timber.tag(tag).w(message)
                LogLevel.ERROR -> Timber.tag(tag).e(message)
            }
        }
    }

    /**
     * 日志级别枚举
     */
    enum class LogLevel {
        VERBOSE, DEBUG, INFO, WARN, ERROR
    }
}

/**
 * 日志标签常量 - 统一命名约定
 */
object TBTags {
    const val RAW = "TB-RAW"        // Parser收到原始分片
    const val FILTER = "TB-FILTER"  // 过滤后纯文本/控制标签
    const val SEM = "TB-SEM"        // SemanticEvent
    const val MAP = "TB-MAP"        // DomainMapper事件流
    const val EVT = "TB-EVT"        // ThinkingEvent
    const val STATE = "TB-STATE"    // Reducer新状态
    const val DB = "TB-DB"          // History落库
    const val UI = "TB-UI"          // UI关键渲染
}

/**
 * 字符串扩展 - 日志脱敏和格式化
 */
fun String.sanitizeForLog(): String = try {
    this
        // 控制标签白名单：只保留thinking, final, phase:, title, checkpoint
        .replace(Regex("<(?!/?(thinking|final|phase:|title|checkpoint))[^>]*>"), "[TAG]")
        // 敏感信息脱敏
        .replace(Regex("apikey\\s*[:=]\\s*\\w+", RegexOption.IGNORE_CASE), "apikey=***")
        .replace(Regex("token\\s*[:=]\\s*\\w+", RegexOption.IGNORE_CASE), "token=***")
        // 极长段落截断
        .let { if (it.length > 120) "${it.take(120)}…" else it }
} catch (e: Exception) {
    "[SANITIZE_ERROR]"
}

/**
 * 批次格式化 - 统一的批次ID格式
 */
fun formatBatch(batchId: Int): String = "(batch=%03d)".format(batchId)

/**
 * 内容摘要 - 生成内容的简短摘要
 */
fun String.toContentSummary(maxLen: Int = 20): String =
    if (this.length <= maxLen) this
    else "${this.take(maxLen)}…"

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/resources/testSkeletons/feature.kt.template
```template
package {{PACKAGE}}

import androidx.compose.ui.test.junit4.createComposeRule
import androidx.compose.ui.test.onNodeWithText
import androidx.compose.ui.test.performClick
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.onNodeWithContentDescription
import org.junit.Rule
import org.junit.Test
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.tooling.preview.Preview
import kotlin.test.*
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.design_system.theme.GymBroTheme
import kotlinx.datetime.Instant

/**
 * Generated by genTest for GymBro Feature Module
 * ✔️ 使用 Compose UI 测试框架
 * ✔️ 包含强制性 @Preview 注解
 * ✔️ 使用 Material3 和 GymBroTheme
 * ✔️ 使用 UiText 而非硬编码字符串
 * ✔️ 遵循 MVVM 模式和状态提升原则
 */
class {{CLASS}}Test {

    @get:Rule
    val composeTestRule = createComposeRule()

    @Test
    fun `{{CLASS}} displays initial state correctly`() {
        composeTestRule.setContent {
            GymBroTheme {
                {{CLASS}}Screen(
                    uiState = {{CLASS}}UiState(),
                    onEvent = {}
                )
            }
        }

        // 验证初始UI状态
        composeTestRule.onNodeWithText("Welcome").assertIsDisplayed()
    }

    @Test
    fun `{{CLASS}} handles user interactions correctly`() {
        var eventTriggered = false

        composeTestRule.setContent {
            GymBroTheme {
                {{CLASS}}Screen(
                    uiState = {{CLASS}}UiState(),
                    onEvent = { eventTriggered = true }
                )
            }
        }

        // 模拟用户交互
        composeTestRule.onNodeWithText("Action Button")
            .assertIsDisplayed()
            .performClick()

        assert(eventTriggered) { "Event should be triggered on click" }
    }

    @Test
    fun `{{CLASS}} displays loading state correctly`() {
        composeTestRule.setContent {
            GymBroTheme {
                {{CLASS}}Screen(
                    uiState = {{CLASS}}UiState(isLoading = true),
                    onEvent = {}
                )
            }
        }

        composeTestRule.onNodeWithContentDescription("Loading").assertIsDisplayed()
    }

    @Test
    fun `{{CLASS}} displays error state with UiText`() {
        val errorMessage = UiText.DynamicString("Test error message")

        composeTestRule.setContent {
            GymBroTheme {
                {{CLASS}}Screen(
                    uiState = {{CLASS}}UiState(errorMessage = errorMessage),
                    onEvent = {}
                )
            }
        }

        composeTestRule.onNodeWithText("Test error message").assertIsDisplayed()
    }

    @Test
    fun `{{CLASS}} handles time display with kotlinx.datetime`() {
        val testTime = Instant.parse("2025-01-30T10:00:00Z")

        composeTestRule.setContent {
            GymBroTheme {
                {{CLASS}}Screen(
                    uiState = {{CLASS}}UiState(lastUpdate = testTime),
                    onEvent = {}
                )
            }
        }

        // 验证时间显示（具体格式根据实际需求调整）
        composeTestRule.onNodeWithText("Last updated").assertIsDisplayed()
    }
}

// 强制性 @Preview 注解 - GymBro 项目要求
@Preview(name = "Light", showBackground = true)
@Preview(name = "Dark", uiMode = android.content.res.Configuration.UI_MODE_NIGHT_YES, showBackground = true)
@Preview(name = "Large Font", fontScale = 1.3f, showBackground = true)
@Composable
fun {{CLASS}}Preview() {
    GymBroTheme {
        {{CLASS}}Screen(
            uiState = {{CLASS}}UiState(),
            onEvent = {}
        )
    }
}

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/kotlin/com/example/gymbro/buildlogic/SecurityConventionPlugin.kt
```kotlin
package com.example.gymbro.buildlogic

import com.example.gymbro.buildlogic.utils.libs
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.*

/**
 * GymBro项目安全扫描统一管理插件
 *
 * 配置OWASP依赖安全扫描配置：
 * - 依赖安全漏洞扫描
 * - 生成安全报告
 * - 配置漏洞数据库更新
 */
class SecurityConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            // 只在根项目应用OWASP依赖检查
            if (project == rootProject) {
                pluginManager.apply("org.owasp.dependencycheck")

                configure<org.owasp.dependencycheck.gradle.extension.DependencyCheckExtension> {
                    // 基础扫描配置
                    autoUpdate = true

                    // 报告格式
                    formats = listOf("ALL")

                    // 输出目录
                    outputDirectory = "${layout.buildDirectory.get().asFile}/reports/owasp"

                    // 配置数据目录
                    data {
                        directory = "${gradle.gradleUserHomeDir}/dependency-check-data"
                    }

                    // 抑制误报（根据需要配置）
                    suppressionFile = "${projectDir}/config/owasp/suppressions.xml"

                    // 失败阈值：CVSS评分大于等于7.0时构建失败
                    failBuildOnCVSS = 7.0f

                    // 基础分析器配置
                    analyzers {
                        // 启用Java相关分析器
                        jarEnabled = true
                        archiveEnabled = true

                        // 禁用不需要的分析器
                        assemblyEnabled = false // .NET相关
                        nuspecEnabled = false   // NuGet相关
                        nugetconfEnabled = false
                        nodeEnabled = false     // Node.js相关
                        cocoapodsEnabled = false // iOS相关
                        swiftEnabled = false     // Swift相关
                        bundleAuditEnabled = false // Ruby相关
                        composerEnabled = false    // PHP相关
                        cpanEnabled = false        // Perl相关
                        autoconfEnabled = false    // Autotools相关
                        opensslEnabled = false     // OpenSSL相关
                        cmakeEnabled = false       // CMake相关
                    }

                    // 配置NVD API（如果有API密钥）
                    nvd {
                        apiKey = System.getenv("NVD_API_KEY") ?: ""
                        // 如果没有API密钥，设置更长的延迟避免限流
                        if (apiKey.isEmpty()) {
                            delay = 4000 // 4秒延迟
                        }
                    }
                }

                // 创建便利任务
                tasks.register("securityCheck") {
                    description = "运行完整的安全扫描"
                    group = "verification"
                    dependsOn("dependencyCheckAnalyze")

                    doLast {
                        println("=== GymBro 安全扫描完成 ===")
                        println("安全报告位置：${layout.buildDirectory.get().asFile}/reports/owasp/")
                        println("请查看生成的HTML报告了解详细信息")
                        println("==========================")
                    }
                }

                // 配置依赖检查任务
                tasks.named("dependencyCheckAnalyze") {
                    description = "分析项目依赖的安全漏洞"

                    // 在CI环境中，让安全检查更严格
                    doFirst {
                        if (System.getenv("CI") == "true") {
                            logger.lifecycle("CI环境检测到，启用严格安全检查模式")
                        }
                    }
                }

                // 创建更新数据库的任务
                tasks.register("updateSecurityDatabase") {
                    description = "更新OWASP漏洞数据库"
                    group = "security"
                    dependsOn("dependencyCheckUpdate")

                    doLast {
                        println("=== 安全数据库更新完成 ===")
                    }
                }
            }
        }
    }
}

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/resources/testSkeletons/design_system.kt.template
```template
package {{PACKAGE}}

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.design_system.theme.GymBroTheme
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config
import org.robolectric.annotation.LooperMode
import kotlin.test.assertTrue
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull

/**
 * {{CLASS}}组件测试
 *
 * 测试覆盖：
 * - 基本渲染测试
 * - 交互测试
 * - 主题测试
 * - UiText处理测试
 * - @Preview注解验证
 */
@RunWith(AndroidJUnit4::class)
@Config(sdk = [31])
@LooperMode(LooperMode.Mode.PAUSED)
class {{CLASS}}Test {

    @get:Rule
    val composeTestRule = createComposeRule()

    @Test
    fun should_render_correctly() {
        // Given
        // TODO: 配置测试参数

        // When
        composeTestRule.setContent {
            GymBroTheme {
                // TODO: 使用适当参数渲染{{CLASS}}组件
            }
        }

        // Then
        // TODO: 验证组件正确渲染，例如：
        // composeTestRule.onNodeWithText("期望文本").assertExists()
        // composeTestRule.onNodeWithTag("组件标签").assertIsDisplayed()
    }

    @Test
    fun should_handle_user_interaction() {
        // Given
        var interactionTriggered = false

        // When
        composeTestRule.setContent {
            GymBroTheme {
                // TODO: 渲染组件并配置交互回调
                // 例如：onClick = { interactionTriggered = true }
            }
        }

        // Then
        // TODO: 执行交互操作并验证结果，例如：
        // composeTestRule.onNodeWithText("按钮文本").performClick()
        // assertTrue(interactionTriggered, "交互回调应该被触发")
    }

    @Test
    fun should_apply_theme_correctly() {
        // 测试在不同主题下的正确渲染

        // Light Theme
        composeTestRule.setContent {
            GymBroTheme(darkTheme = false) {
                // TODO: 渲染组件
            }
        }
        // TODO: 验证亮色主题下的渲染

        // Dark Theme
        composeTestRule.setContent {
            GymBroTheme(darkTheme = true) {
                // TODO: 渲染组件
            }
        }
        // TODO: 验证暗色主题下的渲染
    }

    @Test
    fun should_handle_UiText_correctly() {
        // Given
        val testText = "测试文本"

        // When
        composeTestRule.setContent {
            GymBroTheme {
                // TODO: 使用UiText.DynamicString(testText)渲染组件
            }
        }

        // Then
        // TODO: 验证UiText文本正确显示
        // composeTestRule.onNodeWithText(testText).assertExists()
    }

    @Test
    fun should_handle_state_changes() {
        // TODO: 测试组件对状态变化的响应
        // 例如：加载状态、启用/禁用状态、展开/折叠状态等
    }

    @Test
    fun should_handle_edge_cases() {
        // TODO: 测试边缘情况
        // 例如：空内容、极长文本、无效输入等
    }

    // TODO: 添加更多特定于此组件的测试用例
}

```

File: D:/GymBro/GymBro/designSystem/src/main/res/drawable/ic_app_logo.xml
```xml
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="120dp"
    android:height="120dp"
    android:viewportWidth="120"
    android:viewportHeight="120">

    <!-- Background circle -->
    <path
        android:fillColor="#FF6B35"
        android:pathData="M60,60m-50,0a50,50 0,1 1,100 0a50,50 0,1 1,-100 0" />

    <!-- Dumbbell icon -->
    <group android:translateX="60" android:translateY="60">
        <!-- Left weight -->
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M-25,-8L-25,8L-20,8L-20,-8Z" />
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M-30,-5L-30,5L-25,5L-25,-5Z" />

        <!-- Bar -->
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M-20,-2L20,-2L20,2L-20,2Z" />

        <!-- Right weight -->
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M20,-8L20,8L25,8L25,-8Z" />
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M25,-5L25,5L30,5L30,-5Z" />
    </group>

    <!-- Letter "G" path -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M50,85 Q47,82 47,87 Q47,92 50,95 Q53,98 58,98 Q63,98 66,95 Q68,93 68,90 L65,90 L65,93 Q63,95 58,95 Q53,95 50,92 Q47,89 47,87 Q47,85 50,82 Q53,79 58,79 Q63,79 66,82 L68,80 Q65,77 58,77 Q53,77 50,80 Q47,83 47,87 Q47,91 50,94 Q53,97 58,97 Q63,97 66,94 Q69,91 69,87 L62,87 L62,89 L67,89 Q67,92 64,95 Q61,98 58,98" />

    <!-- Letter "B" path -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M72,77 L72,98 L82,98 Q85,98 87,96 Q89,94 89,91 Q89,88 87,86 Q85,84 82,84 Q85,84 87,82 Q89,80 89,77 Q89,74 87,72 Q85,70 82,70 L72,70 L72,77 M75,77 L75,73 L82,73 Q84,73 85,74 Q86,75 86,77 Q86,79 85,80 Q84,81 82,81 L75,81 L75,77 M75,84 L82,84 Q84,84 85,85 Q86,86 86,88 Q86,90 85,91 Q84,92 82,92 L75,92 L75,84" />
</vector>

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/resources/testSkeletons/data.kt.template
```template
package {{PACKAGE}}

import androidx.room.Room
import androidx.test.core.app.ApplicationProvider
import kotlinx.coroutines.test.runTest
import okhttp3.mockwebserver.MockResponse
import okhttp3.mockwebserver.MockWebServer
import org.junit.After
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import kotlinx.datetime.Instant
import kotlinx.serialization.json.Json

/**
 * Generated by genTest for GymBro Data Module
 * ✔️ 使用 Room in-memory 数据库测试
 * ✔️ 使用 MockWebServer 模拟网络请求
 * ✔️ 使用 ModernResult<T> 统一错误处理
 * ✔️ 使用 kotlinx.serialization 而非 Moshi
 * ✔️ 遵循 Repository 模式和 Clean Architecture
 */
class {{CLASS}}Test {

    private lateinit var database: GymBroDatabase
    private val mockServer = MockWebServer()
    private val json = Json { ignoreUnknownKeys = true }

    @Before
    fun setUp() {
        database = Room.inMemoryDatabaseBuilder(
            ApplicationProvider.getApplicationContext(),
            GymBroDatabase::class.java
        ).allowMainThreadQueries().build()
        mockServer.start()
    }

    @After
    fun tearDown() {
        database.close()
        mockServer.shutdown()
    }

    @Test
    fun `{{CLASS}} loads data from remote and caches locally`() = runTest {
        // Arrange
        val mockResponse = MockResponse()
            .setBody("""{"id": "test", "name": "Test Data"}""")
            .setResponseCode(200)
        mockServer.enqueue(mockResponse)

        val repository = {{CLASS}}(
            dao = database.dao(),
            api = createMockApi(mockServer.url("/")),
            json = json
        )

        // Act
        val result = repository.loadData("testId")

        // Assert
        assertEquals(ModernResult.Success(expectedData), result)
        assertEquals(expectedData, database.dao().getData("testId"))
    }

    @Test
    fun `{{CLASS}} handles network errors correctly`() = runTest {
        // Arrange
        mockServer.enqueue(MockResponse().setResponseCode(500))
        val repository = {{CLASS}}(
            dao = database.dao(),
            api = createMockApi(mockServer.url("/")),
            json = json
        )

        // Act
        val result = repository.loadData("testId")

        // Assert
        val errorResult = result as ModernResult.Error
        assertEquals(NetworkErrorType, errorResult.error.errorType)
    }

    @Test
    fun `{{CLASS}} processes UiText in database operations`() = runTest {
        val testText = UiText.DynamicString("Test message")
        val repository = {{CLASS}}(
            dao = database.dao(),
            api = createMockApi(mockServer.url("/")),
            json = json
        )

        val result = repository.saveText(testText)
        assertEquals(ModernResult.Success(Unit), result)
    }

    @Test
    fun `{{CLASS}} handles time with kotlinx.datetime`() = runTest {
        val testTime = Instant.parse("2025-01-30T10:00:00Z")
        val repository = {{CLASS}}(
            dao = database.dao(),
            api = createMockApi(mockServer.url("/")),
            json = json
        )

        val result = repository.saveTime(testTime)
        assertEquals(ModernResult.Success(Unit), result)
    }

    private fun createMockApi(baseUrl: okhttp3.HttpUrl): ApiService {
        // TODO: 实现 API 服务 mock
        return MockApiService()
    }
}

```

File: D:/GymBro/GymBro/features/auth/src/main/kotlin/com/example/gymbro/features/auth/ui/login/AuthLoginConstants.kt
```kotlin
package com.example.gymbro.features.auth.ui.login

import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.gymbro.designSystem.R
import com.example.gymbro.designSystem.theme.tokens.Tokens

/**
 * Auth登录屏幕常量配置 - 迁移到新设计系统
 * 集中管理所有设计Token引用，消除硬编码魔数
 */
object AuthLoginConstants {
    // === 尺寸常量 - 使用Token系统 ===

    /** Logo区域大小 */
    val LOGO_SIZE = Tokens.Spacing.Massive * 5 // 320dp equivalent

    /** 按钮高度 */
    val BUTTON_HEIGHT = Tokens.Spacing.Massive // 64dp

    /** 按钮间距 */
    val BUTTON_SPACING = Tokens.Spacing.Medium // 16dp

    /** 水平内边距 */
    val HORIZONTAL_PADDING = Tokens.Spacing.Large // 24dp

    /** 顶部内边距 */
    val TOP_PADDING = Tokens.Spacing.XLarge + Tokens.Spacing.Small // 40dp

    /** Logo下方间距 */
    val LOGO_BOTTOM_SPACING = Tokens.Spacing.XXLarge + Tokens.Spacing.XLarge // 80dp

    /** 底部间距 */
    val BOTTOM_SPACING = Tokens.Spacing.XXLarge + Tokens.Spacing.XLarge // 80dp

    /** Logo标题间距 */
    val LOGO_TITLE_SPACING = Tokens.Spacing.Small // 8dp

    /** 标题副标题间距 */
    val TITLE_SUBTITLE_SPACING = Tokens.Spacing.Medium // 16dp

    // === 文字大小 ===

    /** 应用名称字体大小 */
    val APP_NAME_FONT_SIZE = 52.sp

    /** 应用名称行高 */
    val APP_NAME_LINE_HEIGHT = 56.sp

    /** 副标题字体大小 */
    val SUBTITLE_FONT_SIZE = 16.sp

    /** 打字机效果字体大小 */
    val TYPEWRITER_FONT_SIZE = 16.sp

    // === 透明度常量 ===

    /** 副标题透明度 */
    const val SUBTITLE_ALPHA = 0.7f

    /** 打字机效果透明度 */
    const val TYPEWRITER_ALPHA = 0.7f

    // === 动画常量 - 使用AnimToken系统 ===

    /** Logo进入动画延迟时间 */
    const val LOGO_ENTER_DELAY_MS = 200

    /** 内容进入偏移量 */
    val CONTENT_ENTER_OFFSET = 160.dp // 相当于 Tokens.Spacing.Massive * 2.5

    /** 背景图片宽高比 */
    const val BACKGROUND_ASPECT_RATIO = 856f / 456f

    // === 背景图片资源 ===

    /** 深色主题背景图片 */
    val DARK_BACKGROUND_DRAWABLE = com.example.gymbro.designSystem.R.drawable.auth_globe_dark

    /** 浅色主题背景图片 */
    val LIGHT_BACKGROUND_DRAWABLE = com.example.gymbro.designSystem.R.drawable.auth_globe_light

    // === 可访问性描述 ===

    /** 背景图片内容描述 */
    const val BACKGROUND_CONTENT_DESCRIPTION = "健身应用登录背景，展示现代化的地球网格效果"

    /** Logo区域内容描述 */
    const val LOGO_CONTENT_DESCRIPTION = "GymBro应用标志，带有旋转星环装饰效果"

    /** 加载状态内容描述 */
    const val LOADING_CONTENT_DESCRIPTION = "正在处理登录请求，请稍候"
}

```

File: D:/GymBro/GymBro/domain/src/main/kotlin/com/example/gymbro/domain/auth/usecase/phone/LoginWithPhoneUseCase.kt
```kotlin
package com.example.gymbro.domain.auth.usecase.phone

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.repository.AuthRepository
import com.example.gymbro.domain.model.auth.AuthUser
import com.example.gymbro.domain.model.auth.Credentials
import com.example.gymbro.domain.shared.base.modern.ModernUseCase
import kotlinx.coroutines.CoroutineDispatcher
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 手机号登录用例
 *
 * 该用例封装了手机号登录的业务逻辑。通常在验证码验证成功后调用。
 *
 * @property authRepository 认证仓库
 * @property dispatcher 协程调度器
 * @property logger 日志记录器
 */
@Singleton
class LoginWithPhoneUseCase
@Inject
constructor(
    private val authRepository: AuthRepository,
    @IoDispatcher dispatcher: CoroutineDispatcher,
    logger: Logger,
) : ModernUseCase<LoginWithPhoneUseCase.Params, AuthUser>(dispatcher, logger) {

    /**
     * 参数数据类
     */
    data class Params(
        val phoneNumber: String,
        val verificationCode: String,
        val verificationId: String,
    )

    /**
     * 执行手机号登录的业务逻辑
     *
     * @param parameters 包含手机号、验证码和验证ID的参数
     * @return 认证用户信息或错误结果
     */
    override suspend fun execute(parameters: Params): ModernResult<AuthUser> {
        val validationResult = validateParameters(parameters)
        if (validationResult is ModernResult.Error) {
            return validationResult
        }

        logger.d("开始手机号登录: ${parameters.phoneNumber}")

        val credentials = Credentials.Phone(
            phoneNumber = parameters.phoneNumber,
            verificationCode = parameters.verificationCode,
            verificationId = parameters.verificationId,
        )

        return authRepository.loginWithPhone(credentials)
    }

    /**
     * 验证参数
     */
    private fun validateParameters(params: Params): ModernResult<Unit> {
        if (params.phoneNumber.isBlank()) {
            return ModernResult.Error(
                DataErrors.Validation.required(
                    field = "phoneNumber",
                    operationName = "validateParameters",
                ),
            )
        }

        if (params.verificationCode.isBlank()) {
            return ModernResult.Error(
                DataErrors.Validation.required(
                    field = "verificationCode",
                    operationName = "validateParameters",
                ),
            )
        }

        if (params.verificationId.isBlank()) {
            return ModernResult.Error(
                DataErrors.Validation.required(
                    field = "verificationId",
                    operationName = "validateParameters",
                ),
            )
        }

        // 验证码格式验证（通常是6位数字）
        val codeRegex = Regex("^\\d{6}$")
        if (!params.verificationCode.matches(codeRegex)) {
            return ModernResult.Error(
                DataErrors.Validation.formatError(
                    field = "verificationCode",
                    operationName = "validateParameters",
                    message = UiText.DynamicString("验证码格式错误，请输入6位数字验证码"),
                ),
            )
        }

        return ModernResult.Success(Unit)
    }
}

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/kotlin/com/example/gymbro/buildlogic/AndroidApplicationConventionPlugin.kt
```kotlin
package com.example.gymbro.buildlogic

import com.android.build.api.dsl.ApplicationExtension
import com.example.gymbro.buildlogic.utils.coreLibraryDesugaring
import com.example.gymbro.buildlogic.utils.libs
import com.example.gymbro.buildlogic.utils.version
import org.gradle.api.JavaVersion
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.dependencies
import org.gradle.kotlin.dsl.withType
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

class AndroidApplicationConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            with(pluginManager) {
                apply("com.android.application")
                apply("org.jetbrains.kotlin.android")
                apply("com.google.devtools.ksp")
                apply("com.google.dagger.hilt.android")
                // 🚀 应用性能优化插件
                apply("gymbro.performance.optimization")
                // Compose plugin will be applied separately
            }

            extensions.configure<ApplicationExtension> {
                compileSdk = libs.version("compileSdk").toInt()

                defaultConfig {
                    applicationId = "com.example.gymbro"
                    minSdk = libs.version("minSdk").toInt()
                    targetSdk = libs.version("targetSdk").toInt()
                    versionCode = 1
                    versionName = "1.0"
                    testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
                    // 🚀 性能优化配置已移至PerformanceOptimizationPlugin
                }

                buildTypes {
                    debug {
                        // 🚀 基础Debug配置，性能优化已移至PerformanceOptimizationPlugin
                        buildConfigField("String", "API_BASE_URL", "\"https://debug.api.gymbro.com\"")
                        buildConfigField("Boolean", "ENABLE_LOGGING", "true")
                    }
                    release {
                        // 🚀 基础Release配置，性能优化已移至PerformanceOptimizationPlugin
                        buildConfigField("String", "API_BASE_URL", "\"https://api.gymbro.com\"")
                        buildConfigField("Boolean", "ENABLE_LOGGING", "false")
                    }
                }

                compileOptions {
                    sourceCompatibility = JavaVersion.VERSION_17
                    targetCompatibility = JavaVersion.VERSION_17
                    isCoreLibraryDesugaringEnabled = true
                }

                buildFeatures {
                    compose = true
                    buildConfig = true
                    resValues = true
                }

                // 🚀 Packaging配置已移至PerformanceOptimizationPlugin
            }

            // Hilt配置通过插件自动处理

            // 配置KSP
            extensions.configure<com.google.devtools.ksp.gradle.KspExtension> {
                arg("room.schemaLocation", "$projectDir/schemas")
                arg("room.incremental", "true")
                arg("room.expandProjection", "true")
            }

            tasks.withType<KotlinCompile>().configureEach {
                compilerOptions {
                    jvmTarget.set(org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_17)
                }
            }

            // 添加核心依赖
            coreLibraryDesugaring(libs.findLibrary("desugarJdkLibs").get())

            // 添加Hilt依赖
            dependencies {
                add("implementation", libs.findLibrary("hilt-android").get())
                add("ksp", libs.findLibrary("hilt-compiler").get())
            }
        }
    }
}

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/kotlin/com/example/gymbro/buildlogic/KtlintConventionPlugin.kt
```kotlin
package com.example.gymbro.buildlogic

import com.example.gymbro.buildlogic.utils.libs
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.*

/**
 * GymBro项目Ktlint代码格式统一管理插件
 *
 * 配置统一的Kotlin代码格式检查规则：
 * - 基于官方Kotlin编码规范
 * - 自定义项目特定规则
 * - 支持自动格式化
 */
class KtlintConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            // 应用Ktlint插件
            pluginManager.apply("org.jlleitschuh.gradle.ktlint")

            // 配置Ktlint扩展
            configure<org.jlleitschuh.gradle.ktlint.KtlintExtension> {
                // 使用版本目录中定义的Ktlint版本
                version.set(libs.findVersion("ktlint-gradle").get().toString().removePrefix("12.1.1").let { "0.50.0" })

                // 启用Android特定规则
                android.set(true)

                // 输出格式
                outputToConsole.set(true)
                outputColorName.set("RED")

                // 忽略失败，在CI中设置为false
                ignoreFailures.set(project.hasProperty("ci").not())

                // 启用实验性规则
                enableExperimentalRules.set(false)

                // 配置过滤器
                filter {
                    exclude("**/generated/**")
                    exclude("**/build/**")
                    exclude("**/resources/**")
                    exclude("**/*.kts")
                    include("**/kotlin/**")
                    include("**/java/**")
                }

                // 注意：从 ktlint 0.48+ 开始，disabledRules 已弃用
                // 如需禁用特定规则，可在 .editorconfig 文件中配置
                // 或使用 ktlintCheck.exclude() 方法
            }

            // 为不同类型的源文件配置Ktlint
            tasks.named("ktlintMainSourceSetCheck") {
                description = "运行Ktlint检查主源码"
            }

            tasks.named("ktlintTestSourceSetCheck") {
                description = "运行Ktlint检查测试代码"
            }

            // 如果是Android项目，配置Android源集
            afterEvaluate {
                val isAndroidModule = project.plugins.hasPlugin("com.android.application") ||
                        project.plugins.hasPlugin("com.android.library") ||
                        project.extensions.findByName("android") != null

                if (isAndroidModule) {
                    configure<org.jlleitschuh.gradle.ktlint.KtlintExtension> {
                        // Android项目特定配置
                        filter {
                            exclude("**/R.kt")
                            exclude("**/BuildConfig.kt")
                            exclude("**/Manifest.kt")
                            exclude("**/androidTest/**")
                        }
                    }
                }
            }

            // 创建便利任务
            tasks.register("ktlintFormatAll") {
                description = "格式化所有Kotlin代码"
                group = "formatting"
                dependsOn("ktlintFormat")
            }

            tasks.register("ktlintCheckAll") {
                description = "检查所有Kotlin代码格式"
                group = "verification"
                dependsOn("ktlintCheck")
            }
        }
    }
}

```

File: D:/GymBro/GymBro/domain/src/main/kotlin/com/example/gymbro/domain/auth/usecase/LoginWithEmailUseCase.kt
```kotlin
package com.example.gymbro.domain.auth.usecase

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.repository.AuthRepository
import com.example.gymbro.domain.model.auth.AuthUser
import com.example.gymbro.domain.model.auth.Credentials
import com.example.gymbro.domain.shared.base.modern.ModernUseCase
import kotlinx.coroutines.CoroutineDispatcher
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 邮箱密码登录用例
 *
 * 该用例封装了邮箱密码登录的业务逻辑，包括参数验证和错误处理。
 *
 * @property authRepository 认证仓库
 * @property dispatcher 协程调度器
 * @property logger 日志记录器
 */
@Singleton
class LoginWithEmailUseCase
@Inject
constructor(
    private val authRepository: AuthRepository,
    @IoDispatcher dispatcher: CoroutineDispatcher,
    logger: Logger,
) : ModernUseCase<LoginWithEmailUseCase.Params, AuthUser>(dispatcher, logger) {

    /**
     * 参数数据类
     */
    data class Params(
        val email: String,
        val password: String,
    )

    /**
     * 执行邮箱登录的业务逻辑
     *
     * @param parameters 包含邮箱和密码的参数
     * @return 认证用户信息或错误结果
     */
    override suspend fun execute(parameters: Params): ModernResult<AuthUser> {
        val validationResult = validateParameters(parameters)
        if (validationResult is ModernResult.Error) {
            return validationResult
        }

        logger.d("开始邮箱登录: ${parameters.email}")

        val credentials = Credentials.EmailPassword(parameters.email, parameters.password)
        return authRepository.loginWithEmailPassword(credentials)
    }

    /**
     * 验证登录参数
     */
    private fun validateParameters(params: Params): ModernResult<Unit> {
        if (params.email.isBlank()) {
            return ModernResult.Error(
                DataErrors.Validation.required(
                    field = "email",
                    operationName = "validateParameters",
                ),
            )
        }

        if (params.password.isBlank()) {
            return ModernResult.Error(
                DataErrors.Validation.required(
                    field = "password",
                    operationName = "validateParameters",
                ),
            )
        }

        // 简单的邮箱格式验证
        val emailRegex = Regex("^[A-Za-z0-9+_.-]+@(.+)$")
        if (!params.email.matches(emailRegex)) {
            return ModernResult.Error(
                DataErrors.Validation.formatError(
                    field = "email",
                    operationName = "validateParameters",
                    message = UiText.DynamicString("邮箱格式错误"),
                ),
            )
        }

        return ModernResult.Success(Unit)
    }
}

```

File: D:/GymBro/GymBro/features/workout/src/main/kotlin/com/example/gymbro/features/workout/session/internal/components/CompletionDialog.kt
```kotlin
package com.example.gymbro.features.workout.session.internal.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.EmojiEvents
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.GymBroButton
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.theme.tokens.workoutColors

/**
 * 完成训练确认对话框
 *
 * 显示训练完成确认，包括：
 * - 恭喜信息
 * - 训练统计摘要
 * - 确认和取消按钮
 */
@Composable
internal fun CompletionDialog(
    onConfirm: () -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        icon = {
            Icon(
                imageVector = Icons.Default.EmojiEvents,
                contentDescription = null,
                tint = MaterialTheme.workoutColors.completedState,
                modifier = Modifier.size(48.dp),
            )
        },
        title = {
            Text(
                text = UiText.DynamicString("恭喜！").asString(),
                textAlign = TextAlign.Center,
                fontWeight = FontWeight.Bold,
            )
        },
        text = {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
            ) {
                Text(
                    text = UiText.DynamicString("您已完成本次训练").asString(),
                    style = MaterialTheme.typography.bodyLarge,
                    textAlign = TextAlign.Center,
                )

                Text(
                    text = UiText.DynamicString("确认要结束训练并保存数据吗？").asString(),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.workoutColors.accentSecondary,
                    textAlign = TextAlign.Center,
                )
            }
        },
        confirmButton = {
            GymBroButton(
                text = UiText.DynamicString("完成训练"),
                onClick = onConfirm,
            )
        },
        dismissButton = {
            OutlinedButton(
                onClick = onDismiss,
            ) {
                Text(UiText.DynamicString("继续训练").asString())
            }
        },
        modifier = modifier,
    )
}

// === Preview组件 ===

@GymBroPreview
@Composable
private fun CompletionDialogPreview() {
    GymBroTheme {
        CompletionDialog(
            onConfirm = { },
            onDismiss = { },
        )
    }
}

```

File: D:/GymBro/GymBro/features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/domain/logger/RawThinkingLogger.kt
```kotlin
package com.example.gymbro.features.thinkingbox.domain.logger

import com.example.gymbro.features.thinkingbox.internal.logging.TBLog
import com.example.gymbro.features.thinkingbox.internal.logging.TBTags
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 原始思考内容日志记录器
 *
 * 用于记录<think>...</think>内的完整推理草稿
 * 在DEBUG模式下记录到日志，Release模式下为空实现
 */
interface RawThinkingLogger {
    /**
     * 追加思考内容块
     */
    fun append(text: String)

    /**
     * 关闭当前思考会话
     */
    fun close()

    /**
     * 重置日志状态
     */
    fun reset()
}

/**
 * DEBUG模式下的实际日志实现
 */
@Singleton
class DebugRawThinkingLogger @Inject constructor() : RawThinkingLogger {
    
    private val contentBuffer = StringBuilder()
    private var sessionId: String? = null
    private var isActive = false

    override fun append(text: String) {
        if (!isActive) {
            // 开始新的思考会话
            sessionId = "think_${System.currentTimeMillis()}"
            isActive = true
            TBLog.log(TBTags.RAW, TBLog.LogLevel.DEBUG) {
                "开始记录原始思考内容 [session=$sessionId]"
            }
        }
        
        contentBuffer.append(text)
        
        // 实时记录内容块
        TBLog.log(TBTags.RAW, TBLog.LogLevel.DEBUG) {
            "RawThinking[$sessionId]: +${text.length}字符 '${text.take(50)}${if (text.length > 50) "..." else ""}'"
        }
    }

    override fun close() {
        if (isActive) {
            val finalContent = contentBuffer.toString()
            TBLog.log(TBTags.RAW, TBLog.LogLevel.INFO) {
                "完整原始思考内容 [session=$sessionId, 总长度=${finalContent.length}字符]:\n$finalContent"
            }
            
            // 清理状态
            contentBuffer.clear()
            sessionId = null
            isActive = false
        }
    }

    override fun reset() {
        contentBuffer.clear()
        sessionId = null
        isActive = false
        TBLog.log(TBTags.RAW, TBLog.LogLevel.DEBUG) {
            "RawThinkingLogger状态已重置"
        }
    }
}

/**
 * Release模式下的空实现
 */
@Singleton
class NoopRawThinkingLogger @Inject constructor() : RawThinkingLogger {
    override fun append(text: String) {
        // 空实现
    }

    override fun close() {
        // 空实现
    }

    override fun reset() {
        // 空实现
    }
}

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/resources/testSkeletons/di.kt.template
```template
package {{PACKAGE}}

import dagger.hilt.android.testing.HiltAndroidTest
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.UninstallModules
import org.junit.Rule
import org.junit.Test
import kotlin.test.assertNotNull
import javax.inject.Inject
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText

/**
 * Generated by genTest for GymBro DI Module
 * ✔️ 使用 Hilt 测试框架验证依赖图
 * ✔️ 使用 @UninstallModules 替换生产模块
 * ✔️ 验证所有绑定都能正确解析
 * ✔️ 遵循 Clean Architecture 的 DI 原则
 */
@HiltAndroidTest
@UninstallModules({{CLASS}}::class) // 如果需要替换生产模块
class {{CLASS}}Test {

    @get:Rule
    val hiltRule = HiltAndroidRule(this)

    // 注入需要测试的依赖
    @Inject
    lateinit var targetComponent: TargetComponent // TODO 替换为实际组件

    @Test
    fun `{{CLASS}} resolves all required bindings`() {
        hiltRule.inject()

        // 验证关键依赖都已正确注入
        assertNotNull(targetComponent)
        assertNotNull(targetComponent.repository)
        assertNotNull(targetComponent.useCase)
    }

    @Test
    fun `{{CLASS}} provides singleton instances consistently`() {
        hiltRule.inject()

        val instance1 = targetComponent.singletonService
        val instance2 = targetComponent.singletonService

        // 验证单例模式
        assert(instance1 === instance2) { "Singleton instances should be the same" }
    }

    @Test
    fun `{{CLASS}} binds Repository interfaces to implementations`() {
        hiltRule.inject()

        // 验证 Repository 接口正确绑定到实现类
        val repository = targetComponent.repository
        assertNotNull(repository)

        // 验证 Repository 能够处理 ModernResult
        assert(repository is RepositoryImpl) { "Repository should be bound to implementation" }
    }

    @Test
    fun `{{CLASS}} provides proper error handling components`() {
        hiltRule.inject()

        val errorHandler = targetComponent.errorHandler
        assertNotNull(errorHandler)

        // 验证错误处理器能处理 UiText
        val testError = UiText.DynamicString("Test error")
        val result = errorHandler.handleError(testError)
        assertNotNull(result)
    }

    @Test
    fun `{{CLASS}} configures coroutine dispatchers correctly`() {
        hiltRule.inject()

        val dispatchers = targetComponent.dispatchers
        assertNotNull(dispatchers.io)
        assertNotNull(dispatchers.main)
        assertNotNull(dispatchers.default)
    }
}

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/resources/testSkeletons/core.kt.template
```template
package {{PACKAGE}}

import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Nested
import kotlin.test.*
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import kotlinx.datetime.Instant

/**
 * Generated by genTest for GymBro Core Module
 * ✔️ 使用 JUnit 5 测试框架
 * ✔️ 使用 ModernResult<T> 统一错误处理
 * ✔️ 使用 UiText 而非硬编码字符串
 * ✔️ 使用 kotlinx.datetime.Instant 而非 System.currentTimeMillis
 * ✔️ 基础测试结构，需要根据实际功能调整
 */
@DisplayName("{{CLASS}} 测试")
class {{CLASS}}Test {

    @Nested
    @DisplayName("基础功能测试")
    inner class BasicFunctionalityTest {

        @Test
        @DisplayName("{{CLASS}} 应该正确处理有效输入")
        fun `should handle valid input correctly`() {
            // Given
            // TODO: 根据实际{{CLASS}}功能添加测试数据

            // When
            // TODO: 调用实际的{{CLASS}}方法

            // Then
            // TODO: 验证结果
            assertTrue(true, "请根据实际{{CLASS}}功能实现此测试")
        }

        @Test
        @DisplayName("{{CLASS}} 应该正确处理边界条件")
        fun `should handle edge cases correctly`() {
            // Given
            // TODO: 准备边界条件测试数据

            // When & Then
            // TODO: 调用{{CLASS}}方法验证不抛出异常
            assertTrue(true, "请根据实际{{CLASS}}边界条件实现此测试")
        }
    }

    @Nested
    @DisplayName("错误处理测试")
    inner class ErrorHandlingTest {

        @Test
        @DisplayName("{{CLASS}} 应该正确处理无效输入")
        fun `should handle invalid input gracefully`() {
            // Given
            // TODO: 准备无效输入

            // When & Then
            // TODO: 验证错误处理
            assertTrue(true, "请根据实际{{CLASS}}错误处理逻辑实现此测试")
        }
    }

    @Test
    @DisplayName("{{CLASS}} 基本实例化测试")
    fun `should create instance successfully`() {
        // Given & When & Then
        // TODO: 根据{{CLASS}}是否为object、class或扩展函数调整此测试
        // 如果是object: {{CLASS}}
        // 如果是class: {{CLASS}}()
        // 如果是扩展函数: "test".someExtensionMethod()
        assertTrue(true, "请根据实际{{CLASS}}类型实现此测试")
    }
}

```

File: D:/GymBro/GymBro/features/workout/src/main/kotlin/com/example/gymbro/features/workout/shared/components/ApplyPlanDialog.kt
```kotlin
package com.example.gymbro.features.workout.shared.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.R
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme

/**
 * ApplyPlanDialog组件所需的UiText文本资源
 * 为应用计划对话框提供统一的文本处理机制
 */
data class ApplyPlanDialogUiTexts(
    val applyPlanToCalendar: UiText,
    val startDateForPlan: UiText,
    val numberOfWeeks: UiText,
    val applyToCalendar: UiText,
)

/**
 * 应用计划对话框组件
 */
@Composable
fun ApplyPlanDialog(
    uiTexts: ApplyPlanDialogUiTexts,
    onDismiss: () -> Unit,
    onApply: (startDate: String, weeks: Int) -> Unit,
    modifier: Modifier = Modifier,
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(uiTexts.applyPlanToCalendar.asString())
        },
        text = {
            Column {
                Text(uiTexts.startDateForPlan.asString())
                Spacer(modifier = Modifier.height(8.dp))
                Text(uiTexts.numberOfWeeks.asString())
            }
        },
        confirmButton = {
            Button(
                onClick = { onApply("2024-01-01", 4) },
            ) {
                Text(uiTexts.applyToCalendar.asString())
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        },
    )
}

@GymBroPreview
@Composable
private fun applyPlanDialogPreview() {
    GymBroTheme {
        ApplyPlanDialog(
            uiTexts =
            ApplyPlanDialogUiTexts(
                applyPlanToCalendar = UiText.stringResource(R.string.apply_plan_to_calendar),
                startDateForPlan = UiText.stringResource(R.string.start_date_for_plan),
                numberOfWeeks = UiText.stringResource(R.string.how_many_weeks_to_apply),
                applyToCalendar = UiText.stringResource(R.string.apply_to_calendar),
            ),
            onDismiss = {},
            onApply = { _, _ -> },
        )
    }
}

```

File: D:/GymBro/GymBro/features/auth/src/main/kotlin/com/example/gymbro/features/auth/ui/login/components/AuthBackgroundLayer.kt
```kotlin
package com.example.gymbro.features.auth.ui.login.components

import androidx.compose.animation.Crossfade
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import com.example.gymbro.features.auth.ui.animation.AuthAnimSpec
import com.example.gymbro.features.auth.ui.login.AuthLoginConstants

/**
 * 优化的背景层组件
 * 实现真正的主题背景切换，包含背景色和背景图片
 */
@Composable
fun AuthBackgroundLayer(
    isDarkTheme: Boolean,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(if (isDarkTheme) Color.Black else Color.White),
    ) {
        // 背景图片层
        Crossfade(
            targetState = isDarkTheme,
            animationSpec = AuthAnimSpec.backgroundCrossFadeAnimation(),
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(AuthLoginConstants.BACKGROUND_ASPECT_RATIO)
                .align(Alignment.BottomCenter),
            label = "BackgroundCrossFade",
        ) { isDark ->
            ThemeBackgroundImage(
                isDarkTheme = isDark,
            )
        }
    }
}

/**
 * 主题背景图片组件
 * 根据主题切换不同的背景图片，无蒙层效果
 */
@Composable
private fun ThemeBackgroundImage(
    isDarkTheme: Boolean,
    modifier: Modifier = Modifier,
) {
    // 根据主题选择对应的背景图片
    val backgroundDrawable = if (isDarkTheme) {
        AuthLoginConstants.DARK_BACKGROUND_DRAWABLE
    } else {
        AuthLoginConstants.LIGHT_BACKGROUND_DRAWABLE
    }

    Image(
        painter = painterResource(backgroundDrawable),
        contentDescription = null, // 装饰性图片，无需描述
        modifier = modifier
            .fillMaxSize()
            .semantics {
                contentDescription = AuthLoginConstants.BACKGROUND_CONTENT_DESCRIPTION
            },
        contentScale = ContentScale.Crop,
        // 移除alpha参数，去掉蒙层效果
    )
}

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/kotlin/com/example/gymbro/buildlogic/DataModuleConventionPlugin.kt
```kotlin
package com.example.gymbro.buildlogic

import com.example.gymbro.buildlogic.utils.implementation
import com.example.gymbro.buildlogic.utils.ksp
import com.example.gymbro.buildlogic.utils.libs
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.dependencies

/**
 * 数据层模块插件
 *
 * 为数据层模块提供统一的配置：
 * - Android Library 基础配置
 * - Room 数据库
 * - 网络请求（Retrofit + OkHttp）
 * - 数据存储（DataStore）
 * - 序列化（Kotlinx Serialization）
 * - 测试依赖
 */
class DataModuleConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            with(pluginManager) {
                apply("gymbro.android.library")
                apply("gymbro.kotlin.serialization")
                apply("gymbro.testing.library")
            }

            // 配置KSP for Room
            extensions.configure<com.google.devtools.ksp.gradle.KspExtension> {
                arg("room.schemaLocation", "$projectDir/schemas")
                arg("room.incremental", "true")
                arg("room.expandProjection", "true")
            }

            dependencies {
                // 项目模块
                add("implementation", project(":core"))
                add("implementation", project(":domain"))

                // Room数据库
                add("implementation", libs.findLibrary("androidx-room-runtime").get())
                add("implementation", libs.findLibrary("androidx-room-ktx").get())
                add("ksp", libs.findLibrary("androidx-room-compiler").get())

                // 网络
                add("implementation", libs.findLibrary("retrofit").get())
                add("implementation", libs.findLibrary("retrofit-converter-gson").get())
                add("implementation", libs.findLibrary("okhttp-logging-interceptor").get())

                // DataStore
                add("implementation", libs.findLibrary("androidx-datastore-preferences").get())

                // 协程
                add("implementation", libs.findLibrary("kotlinx-coroutines-core").get())
                add("implementation", libs.findLibrary("kotlinx-coroutines-android").get())

                // 测试
                add("testImplementation", libs.findLibrary("androidx-room-testing").get())
                add("androidTestImplementation", libs.findLibrary("androidx-room-testing").get())
            }
        }
    }
}

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/kotlin/com/example/gymbro/buildlogic/CoreDependenciesConventionPlugin.kt
```kotlin
package com.example.gymbro.buildlogic

import com.example.gymbro.buildlogic.utils.implementation
import com.example.gymbro.buildlogic.utils.libs
import org.gradle.api.Plugin
import org.gradle.api.Project

/**
 * 核心依赖插件
 *
 * 提供项目中常用的核心依赖：
 * - AndroidX 核心库
 * - Kotlin 协程
 * - 网络库
 * - 图片加载
 * - 工具库
 */
class CoreDependenciesConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            // 核心库
            implementation(libs.findLibrary("androidx-core-ktx").get())
            implementation(libs.findLibrary("androidx-appcompat").get())
            implementation(libs.findLibrary("material").get())
            implementation(libs.findLibrary("androidx-activity-ktx").get())
            implementation(libs.findLibrary("androidx-lifecycle-runtime-ktx").get())

            // Coroutines & Collections
            implementation(libs.findLibrary("kotlinx-coroutines-core").get())
            implementation(libs.findLibrary("kotlinx-coroutines-android").get())
            implementation(libs.findLibrary("kotlinx-coroutines-play-services").get())

            // 🔥 MVI性能关键：不可变集合，确保Compose智能重组
            implementation(libs.findLibrary("kotlinx-collections-immutable").get())

            // DataStore
            implementation(libs.findLibrary("androidx-datastore-preferences").get())
            implementation(libs.findLibrary("androidx-datastore-core").get())

            // WorkManager
            implementation(libs.findLibrary("androidx-work-runtime-ktx").get())

            // 网络
            implementation(libs.findLibrary("retrofit").get())
            implementation(libs.findLibrary("retrofit-converter-gson").get())
            implementation(libs.findLibrary("okhttp").get())
            implementation(libs.findLibrary("okhttp-logging-interceptor").get())
            implementation(libs.findLibrary("gson").get())

            // 图片加载
            implementation(libs.findLibrary("coil-compose").get())
            implementation(libs.findLibrary("coil-svg").get())
            implementation(libs.findLibrary("coil-gif").get())

            // 日志
            implementation(libs.findLibrary("timber").get())
        }
    }
}

```

File: D:/GymBro/GymBro/domain/src/main/kotlin/com/example/gymbro/domain/auth/usecase/LoginWithGoogleUseCase.kt
```kotlin
package com.example.gymbro.domain.auth.usecase

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.domain.auth.repository.AuthRepository
import com.example.gymbro.domain.model.auth.AuthUser
import com.example.gymbro.domain.model.auth.Credentials
import com.example.gymbro.domain.shared.base.modern.ModernUseCase
import kotlinx.coroutines.CoroutineDispatcher
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Google第三方登录用例
 *
 * 该用例封装了Google登录的业务逻辑，包括ID token验证和错误处理。
 *
 * @property authRepository 认证仓库
 * @property dispatcher 协程调度器
 * @property logger 日志记录器
 */
@Singleton
class LoginWithGoogleUseCase
@Inject
constructor(
    private val authRepository: AuthRepository,
    @IoDispatcher dispatcher: CoroutineDispatcher,
    logger: Logger,
) : ModernUseCase<LoginWithGoogleUseCase.Params, AuthUser>(dispatcher, logger) {

    /**
     * 参数数据类
     */
    data class Params(
        val idToken: String,
    )

    /**
     * 执行Google登录的业务逻辑
     *
     * @param parameters 包含Google ID token的参数
     * @return 认证用户信息或错误结果
     */
    override suspend fun execute(parameters: Params): ModernResult<AuthUser> {
        val validationResult = validateParameters(parameters)
        if (validationResult is ModernResult.Error) {
            return validationResult
        }

        logger.d("开始Google登录")

        val credentials = Credentials.ThirdParty("google", parameters.idToken)
        return authRepository.loginWithThirdParty(credentials)
    }

    /**
     * 验证登录参数
     */
    private fun validateParameters(params: Params): ModernResult<Unit> {
        if (params.idToken.isBlank()) {
            return ModernResult.Error(
                DataErrors.Validation.required(
                    field = "idToken",
                    operationName = "validateParameters",
                ),
            )
        }

        return ModernResult.Success(Unit)
    }
}

```

File: D:/GymBro/GymBro/designSystem/src/main/kotlin/com/example/gymbro/designSystem/components/SocialLoginButton.kt
```kotlin
package com.example.gymbro.designSystem.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.extensions.asString

/**
 * 社交登录按钮组件
 */
@Composable
fun socialLoginButton(
    onClick: () -> Unit,
    text: UiText,
    icon: ImageVector,
    modifier: Modifier = Modifier,
    isLoading: Boolean = false,
    enabled: Boolean = true,
    colors: ButtonColors = ButtonDefaults.outlinedButtonColors(),
) {
    OutlinedButton(
        onClick = onClick,
        modifier =
        modifier
            .fillMaxWidth()
            .height(52.dp),
        enabled = enabled && !isLoading,
        colors = colors,
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center,
        ) {
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(24.dp),
                    strokeWidth = 2.dp,
                )
            } else {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    modifier = Modifier.size(24.dp),
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(text.asString())
            }
        }
    }
}

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/kotlin/com/example/gymbro/buildlogic/ComposeConventionPlugin.kt
```kotlin
package com.example.gymbro.buildlogic

import com.android.build.api.dsl.LibraryExtension
import com.android.build.api.dsl.ApplicationExtension
import com.example.gymbro.buildlogic.utils.androidTestImplementation
import com.example.gymbro.buildlogic.utils.debugImplementation
import com.example.gymbro.buildlogic.utils.implementation
import com.example.gymbro.buildlogic.utils.libs
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.dependencies

class ComposeConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            // Compose plugin should be applied in the module's build.gradle.kts

            // Enable compose build feature
            afterEvaluate {
                extensions.findByType(LibraryExtension::class.java)?.apply {
                    buildFeatures {
                        compose = true
                        // 保持其他 buildFeatures 设置不变，只设置 compose
                    }
                }
                extensions.findByType(ApplicationExtension::class.java)?.apply {
                    buildFeatures {
                        compose = true
                        // 保持其他 buildFeatures 设置不变，只设置 compose
                    }
                }
            }

            dependencies {
                val bom = platform(libs.findLibrary("compose-bom").get())
                add("implementation", bom)
                add("androidTestImplementation", bom)
            }

            // 使用 libs.versions.toml 中的 compose-core bundle
            implementation(libs.findBundle("compose-core").get())

            // 调试依赖 - 使用bundle简化配置
            debugImplementation(libs.findBundle("compose-debug").get())

            // 测试依赖 - 使用bundle简化配置
            androidTestImplementation(libs.findBundle("compose-test").get())
        }
    }
}

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/kotlin/com/example/gymbro/buildlogic/utils/DependencyExtensions.kt
```kotlin
package com.example.gymbro.buildlogic.utils

import org.gradle.api.Project
import org.gradle.api.artifacts.VersionCatalog
import org.gradle.kotlin.dsl.dependencies

/**
 * 依赖管理扩展函数
 * 提供统一的依赖添加方法
 */

/**
 * 添加实现依赖
 */
internal fun Project.implementation(dependencyNotation: Any) {
    dependencies {
        add("implementation", dependencyNotation)
    }
}

/**
 * 添加KSP依赖
 */
internal fun Project.ksp(dependencyNotation: Any) {
    dependencies {
        add("ksp", dependencyNotation)
    }
}

/**
 * 添加测试实现依赖
 */
internal fun Project.testImplementation(dependencyNotation: Any) {
    dependencies {
        add("testImplementation", dependencyNotation)
    }
}

/**
 * 添加Android测试实现依赖
 */
internal fun Project.androidTestImplementation(dependencyNotation: Any) {
    dependencies {
        add("androidTestImplementation", dependencyNotation)
    }
}

/**
 * 添加调试实现依赖
 */
internal fun Project.debugImplementation(dependencyNotation: Any) {
    dependencies {
        add("debugImplementation", dependencyNotation)
    }
}

/**
 * 添加核心库去糖化依赖
 */
internal fun Project.coreLibraryDesugaring(dependencyNotation: Any) {
    dependencies {
        add("coreLibraryDesugaring", dependencyNotation)
    }
}

/**
 * 批量添加实现依赖
 */
internal fun Project.implementations(vararg dependencyNotations: Any) {
    dependencies {
        dependencyNotations.forEach { notation ->
            add("implementation", notation)
        }
    }
}

```

File: D:/GymBro/GymBro/core/src/main/kotlin/com/example/gymbro/core/logging/LoggingModule.kt
```kotlin
package com.example.gymbro.core.logging

import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 日志模块的依赖注入配置
 *
 * 提供日志相关组件的依赖注入。
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class LoggingModule {

    /**
     * 绑定Logger接口到TimberLogger实现
     */
    @Binds
    @Singleton
    abstract fun bindLogger(impl: TimberLogger): Logger

    companion object {
        /**
         * 🔥 【重构】提供日志配置管理器
         */
        @Provides
        @Singleton
        fun provideLoggingConfig(): LoggingConfig = LoggingConfig()

        /**
         * 🔥 【重构】提供增强的 TimberManager
         */
        @Provides
        @Singleton
        fun provideTimberManager(loggingConfig: LoggingConfig): TimberManager = TimberManager(loggingConfig)

        /**
         * 🔥 【新增】提供日志控制器
         */
        @Provides
        @Singleton
        fun provideLoggingController(
            timberManager: TimberManager,
            loggingConfig: LoggingConfig,
        ): LoggingController {
            val controller = LoggingController(timberManager, loggingConfig)
            LoggingController.setInstance(controller)
            return controller
        }

        /**
         * 提供适用于生产环境的ReleaseTree
         */
        @Provides
        @Singleton
        fun provideReleaseTree(): ReleaseTree = ReleaseTree()
    }
}

```

File: D:/GymBro/GymBro/domain/src/main/kotlin/com/example/gymbro/domain/auth/usecase/LoginAnonymouslyUseCase.kt
```kotlin
package com.example.gymbro.domain.auth.usecase

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.domain.auth.repository.AuthRepository
import com.example.gymbro.domain.model.auth.AuthUser
import com.example.gymbro.domain.shared.base.modern.ModernUseCaseNoParams
import kotlinx.coroutines.CoroutineDispatcher
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 匿名登录用例
 *
 * 该用例封装了匿名登录的业务逻辑。匿名登录允许用户在不提供任何凭证的情况下
 * 获得临时访问权限，后续可以升级为正式账户。
 *
 * @property authRepository 认证仓库
 * @property dispatcher 协程调度器
 * @property logger 日志记录器
 */
@Singleton
class LoginAnonymouslyUseCase
@Inject
constructor(
    private val authRepository: AuthRepository,
    @IoDispatcher dispatcher: CoroutineDispatcher,
    logger: Logger,
) : ModernUseCaseNoParams<AuthUser>(dispatcher, logger) {

    /**
     * 执行匿名登录的业务逻辑
     *
     * @return 匿名用户信息或错误结果
     */
    override suspend fun execute(): ModernResult<AuthUser> {
        logger.d("开始匿名登录")
        return authRepository.loginAnonymously()
    }
}

```

File: D:/GymBro/GymBro/features/auth/src/main/res/drawable/app_logo.xml
```xml
<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
  android:width="24dp"
  android:height="24dp"
  android:viewportWidth="24"
  android:viewportHeight="24"
  android:tint="?attr/colorPrimary">
  <!-- 简化版的徽标矢量图 -->
  <path
    android:fillColor="@android:color/white"
    android:pathData="M20.57,14.86L22,13.43 20.57,12 17,15.57 8.43,7 12,3.43 10.57,2 9.14,3.43 7.71,2 5.57,4.14 4.14,2.71 2.71,4.14l1.43,1.43L2,7.71l1.43,1.43L2,10.57 3.43,12 7,8.43 15.57,17 12,20.57 13.43,22l1.43,-1.43L16.29,22l2.14,-2.14 1.43,1.43 1.43,-1.43 -1.43,-1.43L22,16.29z" />
</vector>

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/kotlin/com/example/gymbro/buildlogic/FirebaseConventionPlugin.kt
```kotlin
package com.example.gymbro.buildlogic

import com.example.gymbro.buildlogic.utils.implementation
import com.example.gymbro.buildlogic.utils.libs
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies

/**
 * Firebase 插件
 * 
 * 提供 Firebase 相关的配置和依赖：
 * - Google Services 插件
 * - Firebase Crashlytics 插件
 * - Firebase BOM 和核心服务
 */
class FirebaseConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            with(pluginManager) {
                apply("com.google.gms.google-services")
                apply("com.google.firebase.crashlytics")
            }

            dependencies {
                val bom = platform(libs.findLibrary("firebase-bom").get())
                add("implementation", bom)
            }

            // Firebase 核心服务
            implementation(libs.findLibrary("firebase-analytics-ktx").get())
            implementation(libs.findLibrary("firebase-auth-ktx").get())
            implementation(libs.findLibrary("firebase-crashlytics-ktx").get())
            implementation(libs.findLibrary("firebase-firestore-ktx").get())
            implementation(libs.findLibrary("firebase-database-ktx").get())
            implementation(libs.findLibrary("firebase-storage-ktx").get())
            implementation(libs.findLibrary("firebase-functions-ktx").get())
        }
    }
}

```

File: D:/GymBro/GymBro/core/src/main/kotlin/com/example/gymbro/core/logging/Logger.kt
```kotlin
package com.example.gymbro.core.logging

/**
 * Generic Logger interface for dependency injection.
 * Allows decoupling from specific logging frameworks like Timber in domain/data layers.
 */
interface Logger {
    fun v(message: String, vararg args: Any?)
    fun v(t: Throwable?, message: String, vararg args: Any?)
    fun v(t: Throwable?)

    fun d(message: String, vararg args: Any?)
    fun d(t: Throwable?, message: String, vararg args: Any?)
    fun d(t: Throwable?)

    fun i(message: String, vararg args: Any?)
    fun i(t: Throwable?, message: String, vararg args: Any?)
    fun i(t: Throwable?)

    fun w(message: String, vararg args: Any?)
    fun w(t: Throwable?, message: String, vararg args: Any?)
    fun w(t: Throwable?)

    fun e(message: String, vararg args: Any?)
    fun e(t: Throwable?, message: String, vararg args: Any?)
    fun e(t: Throwable?)

    fun wtf(message: String, vararg args: Any?)
    fun wtf(t: Throwable?, message: String, vararg args: Any?)
    fun wtf(t: Throwable?)

    fun tag(tag: String): Logger
}

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/kotlin/com/example/gymbro/buildlogic/RoomConventionPlugin.kt
```kotlin
package com.example.gymbro.buildlogic

import com.example.gymbro.buildlogic.utils.implementation
import com.example.gymbro.buildlogic.utils.ksp
import com.example.gymbro.buildlogic.utils.libs
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure

/**
 * Room 数据库插件
 *
 * 提供 Room 数据库相关的配置和依赖：
 * - KSP 插件
 * - Room 运行时和编译器
 * - Room KTX 扩展
 */
class RoomConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            with(pluginManager) {
                apply("com.google.devtools.ksp")
            }

            // 配置KSP for Room
            extensions.configure<com.google.devtools.ksp.gradle.KspExtension> {
                arg("room.schemaLocation", "$projectDir/schemas")
                arg("room.incremental", "true")
                arg("room.expandProjection", "true")
            }

            // Room 依赖
            implementation(libs.findLibrary("androidx-room-runtime").get())
            implementation(libs.findLibrary("androidx-room-ktx").get())
            ksp(libs.findLibrary("androidx-room-compiler").get())
        }
    }
}

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/kotlin/com/example/gymbro/buildlogic/FeatureLibraryConventionPlugin.kt
```kotlin
package com.example.gymbro.buildlogic

import com.example.gymbro.buildlogic.utils.implementation
import com.example.gymbro.buildlogic.utils.libs
import org.gradle.api.Plugin
import org.gradle.api.Project

/**
 * 功能模块插件
 *
 * 为功能模块提供统一的配置：
 * - Android Library 基础配置
 * - Compose 支持
 * - Hilt 依赖注入
 * - 核心依赖（Kotlin、AndroidX、工具库等）
 */
class FeatureLibraryConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            with(pluginManager) {
                apply("gymbro.android.library")
                apply("gymbro.compose.library")
                apply("gymbro.hilt.library")
                apply("gymbro.testing.library")
                apply("gymbro.core.dependencies") // 🔥 应用核心依赖插件，包含kotlinx-collections-immutable
            }

            // 添加功能模块特有的依赖（避免重复CoreDependencies中已有的）
            implementation(libs.findLibrary("androidx-lifecycle-viewmodel-ktx").get())
            implementation(libs.findLibrary("androidx-lifecycle-runtime-compose").get())
            implementation(libs.findLibrary("androidx-navigation-compose").get())
        }
    }
}

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/kotlin/com/example/gymbro/buildlogic/DomainModuleConventionPlugin.kt
```kotlin
package com.example.gymbro.buildlogic

import com.example.gymbro.buildlogic.utils.implementation
import com.example.gymbro.buildlogic.utils.libs
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies

/**
 * 领域层模块插件
 * 
 * 为领域层模块提供统一的配置：
 * - Android Library 基础配置
 * - 核心依赖（Kotlin、协程）
 * - 测试依赖
 * - 不包含Android特定依赖
 */
class DomainModuleConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            with(pluginManager) {
                apply("gymbro.android.library")
                apply("gymbro.testing.library")
            }

            dependencies {
                // 项目模块
                add("implementation", project(":core"))

                // Kotlin核心依赖
                add("implementation", libs.findLibrary("kotlin-stdlib").get())
                add("implementation", libs.findLibrary("kotlinx-coroutines-core").get())
                
                // 时间处理
                add("implementation", libs.findLibrary("kotlinx-datetime").get())
            }
        }
    }
}

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/kotlin/com/example/gymbro/buildlogic/HiltConventionPlugin.kt
```kotlin
package com.example.gymbro.buildlogic

import com.example.gymbro.buildlogic.utils.implementation
import com.example.gymbro.buildlogic.utils.ksp
import com.example.gymbro.buildlogic.utils.libs
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.dependencies

class HiltConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            with(pluginManager) {
                apply("com.google.dagger.hilt.android")
                apply("com.google.devtools.ksp")
            }

            // Hilt配置通过插件自动处理

            // 添加Hilt依赖
            dependencies {
                add("implementation", libs.findLibrary("hilt-android").get())
                add("ksp", libs.findLibrary("hilt-compiler").get())
                add("implementation", libs.findLibrary("hilt-navigation-compose").get())
                add("implementation", libs.findLibrary("androidx-hilt-work").get())
                add("ksp", libs.findLibrary("androidx-hilt-compiler").get())
            }
        }
    }
}

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/kotlin/com/example/gymbro/buildlogic/KotlinJvmConventionPlugin.kt
```kotlin
package com.example.gymbro.buildlogic

import com.example.gymbro.buildlogic.utils.implementation
import com.example.gymbro.buildlogic.utils.libs
import com.example.gymbro.buildlogic.utils.testImplementation
import org.gradle.api.JavaVersion
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.withType
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

class KotlinJvmConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            with(pluginManager) {
                apply("org.jetbrains.kotlin.jvm")
            }

            // 配置Java版本兼容性
            tasks.withType<KotlinCompile>().configureEach {
                compilerOptions {
                    jvmTarget.set(org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_17)
                }
            }

            // 添加基础依赖
            implementation(libs.findLibrary("kotlin-stdlib").get())
            testImplementation(libs.findLibrary("kotlin-test").get())
        }
    }
}

```

File: D:/GymBro/GymBro/domain/src/main/kotlin/com/example/gymbro/domain/auth/usecase/LogoutUseCase.kt
```kotlin
package com.example.gymbro.domain.auth.usecase

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.domain.auth.repository.AuthRepository
import com.example.gymbro.domain.shared.base.modern.ModernUseCaseNoParams
import kotlinx.coroutines.CoroutineDispatcher
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Use case for logging out the current user.
 */
@Singleton
class LogoutUseCase
@Inject
constructor(
    private val authRepository: AuthRepository,
    @IoDispatcher dispatcher: CoroutineDispatcher,
    logger: Logger,
) : ModernUseCaseNoParams<Unit>(dispatcher, logger) {
    /**
     * Executes the use case.
     * @return A [ModernResult] indicating success or failure.
     */
    override suspend fun execute(): ModernResult<Unit> {
        return authRepository.logout()
    }
}

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/kotlin/com/example/gymbro/buildlogic/utils/VersionAccessor.kt
```kotlin
package com.example.gymbro.buildlogic.utils

import org.gradle.api.Project
import org.gradle.api.artifacts.VersionCatalog
import org.gradle.api.artifacts.VersionCatalogsExtension
import org.gradle.kotlin.dsl.getByType

/**
 * 版本访问器工具类
 * 提供统一的版本号访问方法
 */
internal val Project.libs: VersionCatalog
    get() = extensions.getByType<VersionCatalogsExtension>().named("libs")

/**
 * 获取版本号的扩展函数
 */
internal fun VersionCatalog.version(alias: String): String = findVersion(alias).get().toString()

/**
 * 获取库依赖的扩展函数
 */
internal fun VersionCatalog.library(alias: String) = findLibrary(alias).get()

/**
 * 获取插件的扩展函数
 */
internal fun VersionCatalog.plugin(alias: String) = findPlugin(alias).get().get().pluginId

```

File: D:/GymBro/GymBro/features/workout/src/main/kotlin/com/example/gymbro/features/workout/shared/components/AddExerciseDialog.kt
```kotlin
package com.example.gymbro.features.workout.shared.components

import com.example.gymbro.core.ui.text.UiText

/**
 * AddExerciseDialog组件所需的UiText文本资源
 * 为动作添加对话框提供统一的文本处理机制
 */
data class AddExerciseDialogUiTexts(
    val selectExercise: UiText,
    val close: UiText,
    val dumbbell: UiText,
    val barbell: UiText,
    val machine: UiText,
    val bodyweight: UiText,
    val resistanceBand: UiText,
    val kettlebell: UiText,
    val noMatchingExercisesFound: UiText,
    val searchExercises: UiText,
    val muscleGroupTabsUiTexts: MuscleGroupTabsUiTexts,
    val equipmentFilterBarUiTexts: EquipmentFilterBarUiTexts,
)

```

File: D:/GymBro/GymBro/features/workout/src/main/kotlin/com/example/gymbro/features/workout/shared/components/WorkoutPickerDialog.kt
```kotlin
package com.example.gymbro.features.workout.shared.components

import com.example.gymbro.core.ui.text.UiText

/**
 * TemplatePickerDialog组件所需的UiText文本资源
 * 为模板选择对话框提供统一的文本处理机制
 */
data class TemplatePickerDialogUiTexts(
    val selectWorkoutTemplateTitle: UiText,
    val close: UiText,
    val noWorkoutTemplates: UiText,
    val createTemplateToStartJourney: UiText,
    val createTemplate: UiText,
    val restDay: UiText,
    val createdAtDate: UiText,
    val createNewTemplateContentDescription: UiText,
    val createNewTemplate: UiText,
)

```

File: D:/GymBro/GymBro/data/src/main/kotlin/com/example/gymbro/data/local/datastore/LoginType.kt
```kotlin
package com.example.gymbro.data.local.datastore

/**
 * 登录类型枚举
 * 定义了应用支持的所有登录类型
 */
enum class LoginType {
    EMAIL, // 邮箱登录
    PHONE, // 手机号登录
    GOOGLE, // 谷歌登录
    WECHAT, // 微信登录
    FACEBOOK, // Facebook登录
    APPLE, // Apple登录
    ANONYMOUS, // 匿名登录
    UNKNOWN, // 未知登录方式
    NONE, // 未登录
}

```

File: D:/GymBro/GymBro/gradle/build-logic/src/main/kotlin/com/example/gymbro/buildlogic/KotlinSerializationConventionPlugin.kt
```kotlin
package com.example.gymbro.buildlogic

import com.example.gymbro.buildlogic.utils.implementation
import com.example.gymbro.buildlogic.utils.libs
import org.gradle.api.Plugin
import org.gradle.api.Project

class KotlinSerializationConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            with(pluginManager) {
                apply("org.jetbrains.kotlin.plugin.serialization")
            }

            implementation(libs.findLibrary("kotlinx-serialization-json").get())
        }
    }
}

```

File: D:/GymBro/GymBro/domain/src/main/kotlin/com/example/gymbro/domain/auth/model/LoginType.kt
```kotlin
package com.example.gymbro.domain.model.auth

/**
 * 登录类型枚举
 * 描述用户的登录方式
 */
enum class LoginType {
    NONE,
    EMAIL,
    PHONE,
    GOOGLE,
    WECHAT,
    FACEBOOK,
    APPLE,
    ANONYMOUS,
    UNKNOWN,
}
```

File: D:/GymBro/GymBro/gradle/build-logic/settings.gradle.kts
```kotlin
dependencyResolutionManagement {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
    versionCatalogs {
        create("libs") {
            from(files("../libs.versions.toml"))
        }
    }
}

rootProject.name = "build-logic"

```

</file_contents>


<user_instructions>
You are tasked to implement a feature. Instructions are as follows:

Instructions for the output format:
- Output code without descriptions, unless it is important.
- Minimize prose, comments and empty lines.
- Only show the relevant code that needs to be modified. Use comments to represent the parts that are not modified.
- Make it easy to copy and paste.
- Consider other possibilities to achieve the result, do not be limited by the prompt.
  </user_instructions>
