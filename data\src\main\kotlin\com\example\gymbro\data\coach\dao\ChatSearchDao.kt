package com.example.gymbro.data.coach.dao

import androidx.room.*
import com.example.gymbro.data.local.entity.ChatRaw
import com.example.gymbro.data.local.entity.ChatVec
import com.example.gymbro.data.local.model.ChatSearchResult
import com.example.gymbro.data.local.model.FtsHit
import com.example.gymbro.data.local.model.PendingMessage

/**
 * 聊天搜索数据访问对象
 *
 * 提供FTS5全文搜索和VSS向量搜索的数据库操作
 * 基于三表分离设计：ChatRaw + ChatFts + ChatVec
 */
@Dao
interface ChatSearchDao {
    /**
     * FTS5全文搜索 - 升级实现
     *
     * 🔥 升级到FTS5支持更精确的BM25评分
     * 使用FTS5原生的bm25()函数进行相关性评分
     *
     * @param query 搜索查询，支持FTS5语法
     * @param limit 搜索结果限制
     * @return FTS5搜索结果，使用BM25评分排序
     */
    @Query(
        """
        SELECT cr.id, cr.session_id, cr.role, cr.content, cr.timestamp, cr.metadata,
               bm25(chat_fts) AS score
        FROM chat_fts
        JOIN chat_raw cr ON chat_fts.rowid = cr.id
        WHERE chat_fts MATCH :query
        ORDER BY score
        LIMIT :limit
    """,
    )
    suspend fun ftsSearch(
        query: String,
        limit: Int = 40,
    ): List<ChatSearchResult>

    /**
     * FTS4会话内全文搜索 - 兼容性修复
     *
     * 🔥 P0修复：移除bm25()函数调用，改用时间戳排序
     *
     * @param sessionId 会话ID
     * @param query 搜索查询
     * @param limit 搜索结果限制
     * @return FTS4搜索结果，使用时间戳排序
     */
    @Query(
        """
        SELECT cr.id, cr.session_id, cr.role, cr.content, cr.timestamp, cr.metadata,
               bm25(chat_fts) AS score
        FROM chat_fts
        JOIN chat_raw cr ON chat_fts.rowid = cr.id
        WHERE chat_fts MATCH :query AND cr.session_id = :sessionId
        ORDER BY score
        LIMIT :limit
    """,
    )
    suspend fun ftsSearchInSession(
        sessionId: String,
        query: String,
        limit: Int = 40,
    ): List<ChatSearchResult>

    /**
     * FTS4搜索命中结果（仅返回ID和评分）
     * 用于混合搜索的第一阶段
     *
     * 🔥 P0修复：移除bm25()函数调用，使用固定评分
     */
    @Query(
        """
        SELECT cr.id, 1.0 AS bm25Score
        FROM chat_fts
        JOIN chat_raw cr ON chat_fts.rowid = cr.id
        WHERE chat_fts MATCH :query
        ORDER BY cr.timestamp DESC
        LIMIT :limit
    """,
    )
    suspend fun ftsHits(
        query: String,
        limit: Int = 40,
    ): List<FtsHit>

    /**
     * VSS向量搜索 - 获取候选向量用于内存计算
     *
     * 基于架构清理要求，这个方法现在返回所有候选向量，
     * 真正的相似度计算在Repository层使用VectorUtils进行，
     * 与RagContextRetrievalUseCase保持一致的实现方式
     *
     * @param queryVector 查询向量（用于验证查询有效性）
     * @param limit 搜索结果限制
     * @return 候选向量结果，需要在上层进行相似度计算
     */
    @Query(
        """
        SELECT cr.id, cr.session_id, cr.role, cr.content, cr.timestamp, cr.metadata,
               0.0 AS score
        FROM chat_vec cv
        JOIN chat_raw cr ON cv.id = cr.id
        WHERE cv.embedding IS NOT NULL
          AND LENGTH(:queryVector) > 0
        ORDER BY cr.timestamp DESC
        LIMIT :limit
    """,
    )
    suspend fun vectorSearch(
        queryVector: ByteArray,
        limit: Int = 50,
    ): List<ChatSearchResult>

    /**
     * 获取向量数据用于相似度计算
     *
     * 基于架构清理要求，提供与RagContextRetrievalUseCase一致的向量获取方式
     * 返回向量数据供上层进行真正的相似度计算
     *
     * @param limit 候选向量数量限制
     * @return 包含向量数据的结果列表
     */
    @Query(
        """
        SELECT cv.id, cv.embedding, cr.session_id, cr.role, cr.content, cr.timestamp, cr.metadata
        FROM chat_vec cv
        JOIN chat_raw cr ON cv.id = cr.id
        WHERE cv.embedding IS NOT NULL
        ORDER BY cr.timestamp DESC
        LIMIT :limit
    """,
    )
    suspend fun getVectorCandidates(limit: Int = 1000): List<VectorCandidate>

    /**
     * VSS会话内向量搜索 - 获取候选向量用于内存计算
     *
     * 基于架构清理要求，这个方法现在返回会话内的候选向量，
     * 真正的相似度计算在Repository层使用VectorUtils进行
     *
     * @param sessionId 会话ID
     * @param queryVector 查询向量（用于验证查询有效性）
     * @param limit 搜索结果限制
     * @return 候选向量结果，需要在上层进行相似度计算
     */
    @Query(
        """
        SELECT cr.id, cr.session_id, cr.role, cr.content, cr.timestamp, cr.metadata,
               0.0 AS score
        FROM chat_vec cv
        JOIN chat_raw cr ON cv.id = cr.id
        WHERE cv.embedding IS NOT NULL
          AND LENGTH(:queryVector) > 0
          AND cr.session_id = :sessionId
        ORDER BY cr.timestamp DESC
        LIMIT :limit
    """,
    )
    suspend fun vectorSearchInSession(
        sessionId: String,
        queryVector: ByteArray,
        limit: Int = 50,
    ): List<ChatSearchResult>

    /**
     * VSS搜索命中结果（返回ID和向量数据用于相似度计算）
     * 用于混合搜索的第一阶段
     *
     * 基于架构清理要求，返回向量数据供上层计算真正的相似度
     */
    @Query(
        """
        SELECT cv.id, cv.embedding, 0.0 AS similarity
        FROM chat_vec cv
        WHERE cv.embedding IS NOT NULL
          AND LENGTH(:queryVector) > 0
        ORDER BY cv.created_at DESC
        LIMIT :limit
    """,
    )
    suspend fun vssHits(
        queryVector: ByteArray,
        limit: Int = 50,
    ): List<VssHitWithVector>

    /**
     * 根据ID列表获取聊天详情
     * 用于混合搜索的第二阶段
     *
     * @param ids 消息ID列表
     * @return 聊天消息详情列表
     */
    @Query(
        """
        SELECT cr.id, cr.session_id, cr.role, cr.content, cr.timestamp, cr.metadata,
               0.0 AS score
        FROM chat_raw cr
        WHERE cr.id IN (:ids)
        ORDER BY cr.timestamp DESC
    """,
    )
    suspend fun getChatDetails(ids: List<Long>): List<ChatSearchResult>

    /**
     * 插入聊天原始数据
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertChatRaw(chatRaw: ChatRaw): Long

    /**
     * 批量插入聊天原始数据
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertChatRawBatch(chatRaws: List<ChatRaw>): List<Long>

    /**
     * 插入向量数据
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertChatVector(chatVec: ChatVec)

    /**
     * 批量插入向量数据
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertBatchVectors(chatVecs: List<ChatVec>)

    /**
     * 获取待向量化的聊天内容
     * 用于后台向量化任务
     */
    @Query(
        """
        SELECT cr.*
        FROM chat_raw cr
        LEFT JOIN chat_vec cv ON cr.id = cv.id
        WHERE cv.id IS NULL
        ORDER BY cr.timestamp DESC
        LIMIT :limit
    """,
    )
    suspend fun getPendingEmbeddings(limit: Int = 64): List<ChatRaw>

    /**
     * 获取待向量化的消息（根据ID列表）
     * 用于VectorizationWorker
     */
    @Query(
        """
        SELECT cr.id, cr.content,
               CASE WHEN cv.id IS NOT NULL THEN 1 ELSE 0 END AS hasVector
        FROM chat_raw cr
        LEFT JOIN chat_vec cv ON cr.id = cv.id
        WHERE cr.id IN (:messageIds)
        ORDER BY cr.timestamp DESC
    """,
    )
    suspend fun getPendingVectorization(messageIds: List<Long>): List<PendingMessage>

    /**
     * 批量更新向量数据
     * 用于VectorizationWorker
     */
    suspend fun updateVectorsBatch(updates: List<Pair<Long, ByteArray>>) {
        updates.forEach { (messageId, vectorBytes) ->
            insertChatVector(
                ChatVec(
                    id = messageId,
                    embedding = vectorBytes,
                    embeddingDim = vectorBytes.size / 4, // 假设每个float占4字节
                    createdAt = System.currentTimeMillis(),
                ),
            )
        }
    }

    /**
     * 清理旧的聊天数据
     *
     * @param cutoffTime 截止时间戳
     */
    @Query(
        """
        DELETE FROM chat_raw
        WHERE timestamp < :cutoffTime
    """,
    )
    suspend fun cleanupOldChatData(cutoffTime: Long)

    /**
     * 清理旧的向量数据
     *
     * @param cutoffTime 截止时间戳
     */
    @Query(
        """
        DELETE FROM chat_vec
        WHERE created_at < :cutoffTime
    """,
    )
    suspend fun cleanupOldVectorData(cutoffTime: Long)

    /**
     * 获取聊天数据统计信息
     */
    @Query(
        """
        SELECT
            COUNT(*) as total_messages,
            COUNT(CASE WHEN role = 'user' THEN 1 END) as user_messages,
            COUNT(CASE WHEN role = 'assistant' THEN 1 END) as assistant_messages,
            MIN(timestamp) as earliest_message,
            MAX(timestamp) as latest_message
        FROM chat_raw
    """,
    )
    suspend fun getChatStatistics(): ChatStatistics

    /**
     * 获取向量化进度
     */
    @Query(
        """
        SELECT
            (SELECT COUNT(*) FROM chat_raw) as total_messages,
            (SELECT COUNT(*) FROM chat_vec) as vectorized_messages
    """,
    )
    suspend fun getVectorizationProgress(): VectorizationProgress
}

/**
 * 聊天统计信息
 */
data class ChatStatistics(
    @ColumnInfo(name = "total_messages")
    val totalMessages: Int,
    @ColumnInfo(name = "user_messages")
    val userMessages: Int,
    @ColumnInfo(name = "assistant_messages")
    val assistantMessages: Int,
    @ColumnInfo(name = "earliest_message")
    val earliestMessage: Long,
    @ColumnInfo(name = "latest_message")
    val latestMessage: Long,
)

/**
 * 向量化进度
 */
data class VectorizationProgress(
    @ColumnInfo(name = "total_messages")
    val totalMessages: Int,
    @ColumnInfo(name = "vectorized_messages")
    val vectorizedMessages: Int,
) {
    val progress: Float
        get() = if (totalMessages > 0) vectorizedMessages.toFloat() / totalMessages else 0f
}

/**
 * 向量候选结果
 *
 * 基于架构清理要求，用于统一向量搜索实现
 * 包含向量数据供上层进行真正的相似度计算
 */
data class VectorCandidate(
    val id: Long,
    val embedding: ByteArray,
    @ColumnInfo(name = "session_id")
    val sessionId: String,
    val role: String,
    val content: String,
    val timestamp: Long,
    val metadata: String?,
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as VectorCandidate

        if (id != other.id) return false
        if (!embedding.contentEquals(other.embedding)) return false
        if (sessionId != other.sessionId) return false
        if (role != other.role) return false
        if (content != other.content) return false
        if (timestamp != other.timestamp) return false
        if (metadata != other.metadata) return false

        return true
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + embedding.contentHashCode()
        result = 31 * result + sessionId.hashCode()
        result = 31 * result + role.hashCode()
        result = 31 * result + content.hashCode()
        result = 31 * result + timestamp.hashCode()
        result = 31 * result + (metadata?.hashCode() ?: 0)
        return result
    }
}

/**
 * VSS搜索命中结果（包含向量数据）
 *
 * 基于架构清理要求，用于统一向量搜索实现
 * 包含向量数据供上层进行真正的相似度计算
 */
data class VssHitWithVector(
    val id: Long,
    val embedding: ByteArray,
    val similarity: Float,
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as VssHitWithVector

        if (id != other.id) return false
        if (!embedding.contentEquals(other.embedding)) return false
        if (similarity != other.similarity) return false

        return true
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + embedding.contentHashCode()
        result = 31 * result + similarity.hashCode()
        return result
    }
}
