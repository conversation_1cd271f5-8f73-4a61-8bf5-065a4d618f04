package com.example.gymbro.app.version

import android.content.Intent
import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.SystemUpdate
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.shared.base.version.VersionAccessResult

/**
 * 强制更新页面
 *
 * 当应用版本过低需要强制更新时显示此页面
 */
@Composable
fun ForceUpdateScreen(
    updateInfo: VersionAccessResult.ForceUpdate,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current

    Box(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background),
        contentAlignment = Alignment.Center,
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface,
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(16.dp),
            ) {
                // 更新图标
                Icon(
                    imageVector = Icons.Default.SystemUpdate,
                    contentDescription = "需要更新",
                    modifier = Modifier.size(64.dp),
                    tint = MaterialTheme.colorScheme.primary,
                )

                // 标题
                Text(
                    text = "需要更新应用",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface,
                    textAlign = TextAlign.Center,
                )

                // 更新消息
                Text(
                    text = updateInfo.updateMessage.asString(),
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center,
                    lineHeight = 24.sp,
                )

                // 版本信息
                VersionInfoCard(
                    currentVersion = updateInfo.currentVersion,
                    requiredVersion = updateInfo.requiredVersion,
                )

                Spacer(modifier = Modifier.height(8.dp))

                // 更新按钮
                UpdateButton(
                    downloadUrl = updateInfo.downloadUrl,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(48.dp),
                )

                // 说明文字
                Text(
                    text = "更新后即可正常使用所有功能",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
                    textAlign = TextAlign.Center,
                )
            }
        }
    }
}

/**
 * 版本信息卡片组件
 */
@Composable
private fun VersionInfoCard(
    currentVersion: String,
    requiredVersion: String,
    modifier: Modifier = Modifier,
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f),
        ),
        shape = RoundedCornerShape(8.dp),
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp),
        ) {
            VersionRow(label = "当前版本：", version = currentVersion, isError = true)
            VersionRow(label = "要求版本：", version = requiredVersion, isError = false)
        }
    }
}

/**
 * 版本信息行组件
 */
@Composable
private fun VersionRow(
    label: String,
    version: String,
    isError: Boolean,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
        )
        Text(
            text = version,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium,
            color = if (isError) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.primary,
        )
    }
}

/**
 * 更新按钮组件
 */
@Composable
private fun UpdateButton(
    downloadUrl: String,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current

    Button(
        onClick = { handleUpdateClick(context, downloadUrl) },
        modifier = modifier,
        enabled = downloadUrl.isNotBlank(),
        colors = ButtonDefaults.buttonColors(
            containerColor = MaterialTheme.colorScheme.primary,
        ),
        shape = RoundedCornerShape(8.dp),
    ) {
        Text(
            text = "立即更新",
            style = MaterialTheme.typography.bodyLarge,
            fontWeight = FontWeight.Medium,
        )
    }
}

/**
 * 处理更新按钮点击事件
 */
private fun handleUpdateClick(context: android.content.Context, downloadUrl: String) {
    if (downloadUrl.isNotBlank()) {
        try {
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(downloadUrl))
            context.startActivity(intent)
        } catch (e: Exception) {
            android.util.Log.e("ForceUpdateScreen", "无法打开下载链接: $downloadUrl", e)
            // 可以在这里添加Toast提示用户无法打开链接
        }
    }
}

/**
 * UiText扩展函数，用于在Composable中显示文本
 */
@Composable
private fun UiText.asString(): String {
    return when (this) {
        is UiText.DynamicString -> this.value
        is UiText.StringResource -> "请更新到最新版本以继续使用" // 简化处理
        else -> "请更新应用"
    }
}
