package com.example.gymbro.core.error

import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 跨模块错误处理器
 *
 * 负责处理跨模块之间的错误传播、上下文保持和恢复建议
 * 提供统一的错误处理机制，确保错误信息在模块间正确传递
 */
@Singleton
class CrossModuleErrorHandler @Inject constructor(
    private val modernErrorHandler: ModernErrorHandler,
) {

    // 错误事件流，用于跨模块错误通知
    private val _errorEvents = MutableSharedFlow<CrossModuleErrorEvent>()
    val errorEvents: Flow<CrossModuleErrorEvent> = _errorEvents.asSharedFlow()

    /**
     * 处理跨模块错误传播
     *
     * @param error 原始错误
     * @param sourceModule 错误来源模块
     * @param targetModule 错误目标模块
     * @param context 错误上下文信息
     * @return 增强的错误信息
     */
    suspend fun propagateError(
        error: ModernDataError,
        sourceModule: String,
        targetModule: String,
        context: Map<String, Any> = emptyMap(),
    ): ModernDataError {
        val enhancedError = enhanceErrorWithCrossModuleContext(
            error = error,
            sourceModule = sourceModule,
            targetModule = targetModule,
            context = context,
        )

        // 发送跨模块错误事件
        val errorEvent = CrossModuleErrorEvent(
            error = enhancedError,
            sourceModule = sourceModule,
            targetModule = targetModule,
            timestamp = System.currentTimeMillis(),
            context = context,
        )

        _errorEvents.emit(errorEvent)

        Timber.w(
            enhancedError,
            "跨模块错误传播: $sourceModule -> $targetModule, 操作: ${error.operationName}",
        )

        return enhancedError
    }

    /**
     * 增强错误信息，添加跨模块上下文
     */
    private fun enhanceErrorWithCrossModuleContext(
        error: ModernDataError,
        sourceModule: String,
        targetModule: String,
        context: Map<String, Any>,
    ): ModernDataError {
        val enhancedMetadata = error.metadataMap.toMutableMap().apply {
            put("sourceModule", sourceModule)
            put("targetModule", targetModule)
            put("crossModuleContext", context)
            put("errorPropagationTime", System.currentTimeMillis())
        }

        val enhancedUiMessage = generateCrossModuleUiMessage(error, sourceModule, targetModule)

        return error.copy(
            operationName = "${sourceModule}_to_${targetModule}_${error.operationName}",
            uiMessage = enhancedUiMessage,
            metadataMap = enhancedMetadata,
            recoverable = determineCrossModuleRecoverability(error, sourceModule, targetModule),
        )
    }

    /**
     * 生成跨模块用户友好的错误消息
     */
    private fun generateCrossModuleUiMessage(
        error: ModernDataError,
        sourceModule: String,
        targetModule: String,
    ): UiText {
        return when {
            sourceModule == "coach" && targetModule == "workout" -> {
                when {
                    error.operationName.contains("draft", ignoreCase = true) ->
                        UiText.DynamicString("AI生成的内容无法传递到训练模块，请重试或检查网络连接")
                    error.operationName.contains("navigation", ignoreCase = true) ->
                        UiText.DynamicString("无法跳转到训练编辑器，请稍后重试")
                    else -> UiText.DynamicString("AI教练与训练模块通信失败，请重试")
                }
            }
            sourceModule == "workout" && targetModule == "coach" -> {
                when {
                    error.operationName.contains("feedback", ignoreCase = true) ->
                        UiText.DynamicString("训练反馈无法发送给AI教练，将在网络恢复后自动重试")
                    error.operationName.contains("context", ignoreCase = true) ->
                        UiText.DynamicString("无法向AI教练发送训练上下文，请重试")
                    else -> UiText.DynamicString("训练模块与AI教练通信失败，请重试")
                }
            }
            else -> error.uiMessage ?: UiText.DynamicString("模块间通信失败，请重试")
        }
    }

    /**
     * 确定跨模块错误的可恢复性
     */
    private fun determineCrossModuleRecoverability(
        error: ModernDataError,
        sourceModule: String,
        targetModule: String,
    ): Boolean {
        return when {
            // 网络相关错误通常可恢复
            error.operationName.contains("network", ignoreCase = true) -> true
            error.operationName.contains("sync", ignoreCase = true) -> true

            // 导航错误通常可恢复
            error.operationName.contains("navigation", ignoreCase = true) -> true

            // 数据传输错误通常可恢复
            error.operationName.contains("draft", ignoreCase = true) -> true
            error.operationName.contains("feedback", ignoreCase = true) -> true

            // 其他情况保持原有可恢复性
            else -> error.recoverable
        }
    }

    /**
     * 获取跨模块错误的恢复建议
     */
    fun getRecoveryStrategy(
        error: ModernDataError,
        sourceModule: String,
        targetModule: String,
    ): CrossModuleRecoveryStrategy {
        return when {
            error.operationName.contains("network", ignoreCase = true) ->
                CrossModuleRecoveryStrategy.RETRY_WITH_NETWORK_CHECK

            error.operationName.contains("navigation", ignoreCase = true) ->
                CrossModuleRecoveryStrategy.RETRY_NAVIGATION

            error.operationName.contains("draft", ignoreCase = true) ->
                CrossModuleRecoveryStrategy.RETRY_WITH_CACHE_FALLBACK

            error.operationName.contains("sync", ignoreCase = true) ->
                CrossModuleRecoveryStrategy.QUEUE_FOR_LATER_SYNC

            else -> CrossModuleRecoveryStrategy.SHOW_ERROR_AND_RETRY
        }
    }

    /**
     * 处理跨模块操作结果，自动处理错误传播
     */
    suspend fun <T> handleCrossModuleResult(
        result: ModernResult<T>,
        sourceModule: String,
        targetModule: String,
        context: Map<String, Any> = emptyMap(),
    ): ModernResult<T> {
        return when (result) {
            is ModernResult.Success -> result
            is ModernResult.Loading -> result
            is ModernResult.Error -> {
                val enhancedError = propagateError(
                    error = result.error,
                    sourceModule = sourceModule,
                    targetModule = targetModule,
                    context = context,
                )
                ModernResult.Error(enhancedError)
            }
        }
    }
}

/**
 * 跨模块错误事件
 */
data class CrossModuleErrorEvent(
    val error: ModernDataError,
    val sourceModule: String,
    val targetModule: String,
    val timestamp: Long,
    val context: Map<String, Any>,
)

/**
 * 跨模块恢复策略
 */
enum class CrossModuleRecoveryStrategy {
    /** 重试并检查网络连接 */
    RETRY_WITH_NETWORK_CHECK,

    /** 重试导航操作 */
    RETRY_NAVIGATION,

    /** 重试并使用缓存回退 */
    RETRY_WITH_CACHE_FALLBACK,

    /** 排队等待后续同步 */
    QUEUE_FOR_LATER_SYNC,

    /** 显示错误并提供重试选项 */
    SHOW_ERROR_AND_RETRY,

    /** 降级到离线模式 */
    FALLBACK_TO_OFFLINE_MODE,
}
