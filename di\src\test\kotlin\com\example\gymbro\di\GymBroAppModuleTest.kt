package com.example.gymbro.di

import android.content.Context
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.gymbro.core.coroutine.DefaultDispatcher
import com.example.gymbro.core.coroutine.MainDispatcher
import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.ModernErrorHandler
import com.example.gymbro.core.logging.Logger
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import dagger.hilt.android.testing.HiltTestApplication
import kotlinx.coroutines.CoroutineDispatcher
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import javax.inject.Inject
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * GymBroAppModule 测试
 *
 * 验证主聚合模块的依赖注入配置正确性
 * 测试所有核心依赖能够成功注入
 */
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class GymBroAppModuleTest {
    @get:Rule
    val hiltRule = HiltAndroidRule(this)

    // 注入核心依赖进行验证
    @Inject
    @IoDispatcher
    lateinit var ioDispatcher: CoroutineDispatcher

    @Inject
    @DefaultDispatcher
    lateinit var defaultDispatcher: CoroutineDispatcher

    @Inject
    @MainDispatcher
    lateinit var mainDispatcher: CoroutineDispatcher

    @Inject
    lateinit var modernErrorHandler: ModernErrorHandler

    @Inject
    lateinit var logger: Logger

    @Before
    fun setUp() {
        hiltRule.inject()
    }

    @Test
    fun `应该成功注入协程调度器`() {
        // Given & When - 注入在setUp中完成

        // Then
        assertNotNull(ioDispatcher, "IO调度器应该成功注入")
        assertNotNull(defaultDispatcher, "默认调度器应该成功注入")
        assertNotNull(mainDispatcher, "主线程调度器应该成功注入")
    }

    @Test
    fun `应该成功注入错误处理器`() {
        // Given & When - 注入在setUp中完成

        // Then
        assertNotNull(modernErrorHandler, "错误处理器应该成功注入")

        // 验证错误处理器功能
        val testError = RuntimeException("测试错误")
        val errorMessage = modernErrorHandler.handleThrowable(testError)
        assertNotNull(errorMessage, "错误处理应该返回有效消息")
    }

    @Test
    fun `应该成功注入日志器`() {
        // Given & When - 注入在setUp中完成

        // Then
        assertNotNull(logger, "日志器应该成功注入")

        // 验证日志器功能
        logger.d("GymBroAppModuleTest", "测试日志输出")
        // 日志不会抛出异常即为成功
        assertTrue(true, "日志器应该正常工作")
    }

    @Test
    fun `应该能获取应用上下文`() {
        // Given & When
        val context = ApplicationProvider.getApplicationContext<Context>()

        // Then
        assertNotNull(context, "应用上下文应该可用")
        assertTrue(context is HiltTestApplication, "应该是Hilt测试应用上下文")
    }

    @Test
    fun `模块聚合应该无冲突`() {
        // Given - 所有模块已通过GymBroAppModule聚合

        // When & Then - 如果能成功注入所有依赖，说明无冲突
        assertNotNull(ioDispatcher, "IO调度器注入无冲突")
        assertNotNull(defaultDispatcher, "默认调度器注入无冲突")
        assertNotNull(mainDispatcher, "主线程调度器注入无冲突")
        assertNotNull(modernErrorHandler, "错误处理器注入无冲突")
        assertNotNull(logger, "日志器注入无冲突")

        // 验证不同调度器实例是正确的
        assertTrue(
            ioDispatcher.toString().contains("IO") ||
                ioDispatcher.toString().contains("Dispatcher"),
            "IO调度器应该是正确的类型",
        )
    }
}
