package com.example.gymbro.data.autosave.strategy

import com.example.gymbro.core.autosave.AutoSaveManager
import com.example.gymbro.core.autosave.strategy.AutoSaveStrategy
import com.example.gymbro.core.logging.Logger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 防抖保存策略实现
 *
 * 🎯 功能特性：
 * - 数据变更后延迟保存
 * - 如果在延迟期间有新的变更则重新计时
 * - 最低延迟1秒，默认1.5秒
 * - 适用于频繁变更的数据
 *
 * @param T 要保存的数据类型
 * @param delayMs 防抖延迟（毫秒）
 * @param logger 日志记录器
 */
class ThrottledSaveStrategy<T : Any> @Inject constructor(
    private val logger: Logger,
    delayMs: Long = 1500L,
) : AutoSaveStrategy<T> {

    // 确保延迟不小于1秒
    private val delayMs: Long = maxOf(delayMs, AutoSaveManager.MIN_SAVE_INTERVAL_MS)

    private var saveJob: Job? = null
    private var saveCallback: (suspend () -> Result<Unit>)? = null
    private var errorCallback: ((Throwable) -> Unit)? = null
    private var scope: CoroutineScope? = null
    private var lastSaveTime: Long = 0L

    override fun start(
        scope: CoroutineScope,
        onSave: suspend () -> Result<Unit>,
        onError: (Throwable) -> Unit,
    ) {
        this.scope = scope
        this.saveCallback = onSave
        this.errorCallback = onError

        logger.d("ThrottledSaveStrategy", "防抖保存策略已启动，延迟: ${this.delayMs}ms")
    }

    override fun onDataChanged(oldData: T?, newData: T) {
        val scope = this.scope ?: return
        val saveCallback = this.saveCallback ?: return
        val errorCallback = this.errorCallback ?: return

        // 取消之前的保存任务
        saveJob?.cancel()

        logger.d("ThrottledSaveStrategy", "数据已变更，启动防抖延迟")

        // 启动新的延迟保存任务
        saveJob = scope.launch {
            delay(delayMs)

            // 检查1秒最低间隔限制
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastSaveTime >= AutoSaveManager.MIN_SAVE_INTERVAL_MS) {
                try {
                    logger.d("ThrottledSaveStrategy", "开始防抖保存")
                    val result = saveCallback()

                    result.fold(
                        onSuccess = {
                            lastSaveTime = currentTime
                            logger.d("ThrottledSaveStrategy", "防抖保存成功")
                        },
                        onFailure = { error ->
                            logger.e(error, "防抖保存失败")
                            errorCallback(error)
                        },
                    )
                } catch (e: Exception) {
                    logger.e(e, "防抖保存异常")
                    errorCallback(e)
                }
            } else {
                logger.d("ThrottledSaveStrategy", "防抖保存被限制，距离上次保存不足1秒")
                // 重新启动延迟，确保最终能够保存
                val remainingDelay = AutoSaveManager.MIN_SAVE_INTERVAL_MS - (currentTime - lastSaveTime)
                delay(remainingDelay)

                // 递归调用，确保最终保存
                onDataChanged(oldData, newData)
            }
        }
    }

    override fun stop() {
        saveJob?.cancel()
        saveJob = null
        saveCallback = null
        errorCallback = null
        scope = null
        lastSaveTime = 0L

        logger.d("ThrottledSaveStrategy", "防抖保存策略已停止")
    }

    companion object {
        /**
         * 创建防抖保存策略实例
         */
        fun <T : Any> create(
            logger: Logger,
            delayMs: Long = 1500L,
        ): ThrottledSaveStrategy<T> {
            return ThrottledSaveStrategy(logger, delayMs)
        }
    }
}
