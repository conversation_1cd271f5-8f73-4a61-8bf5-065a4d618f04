package com.example.gymbro.designSystem.components

import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.theme.GymBroTheme

/**
 * 数值输入字段组件，用于重量、次数、时间等数值输入
 *
 * @param value 当前数值（字符串格式）
 * @param onValueChange 数值变化回调
 * @param label 字段标签
 * @param modifier 修饰符
 * @param isError 是否显示错误状态
 * @param errorMessage 错误消息
 * @param enabled 是否启用
 * @param placeholder 占位文本
 * @param minValue 最小值限制
 * @param maxValue 最大值限制
 * @param decimalPlaces 小数位数（0表示整数）
 * @param unit 单位文本（如"kg", "次", "秒"）
 * @param keyboardOptions 键盘选项
 */
@Composable
fun numberField(
    value: String,
    onValueChange: (String) -> Unit,
    label: UiText,
    modifier: Modifier = Modifier,
    isError: Boolean = false,
    errorMessage: UiText? = null,
    enabled: Boolean = true,
    placeholder: UiText? = null,
    minValue: Double? = null,
    maxValue: Double? = null,
    decimalPlaces: Int = 0,
    unit: UiText? = null,
    keyboardOptions: KeyboardOptions = KeyboardOptions(
        keyboardType = KeyboardType.Number,
        imeAction = ImeAction.Done,
    ),
) {
    // 验证输入值
    val isValidNumber = value.isEmpty() || value.toDoubleOrNull() != null
    val numberValue = value.toDoubleOrNull()

    // 检查范围
    val isInRange = when {
        numberValue == null -> true
        minValue != null && numberValue < minValue -> false
        maxValue != null && numberValue > maxValue -> false
        else -> true
    }

    GymBroInputField(
        value = value,
        onValueChange = { newValue ->
            // 只允许数字、小数点和负号
            val filteredValue = newValue.filter { it.isDigit() || it == '.' || it == '-' }

            // 验证小数位数
            val parts = filteredValue.split('.')
            val validValue = if (parts.size <= 2) {
                if (parts.size == 2 && parts[1].length > decimalPlaces) {
                    "${parts[0]}.${parts[1].take(decimalPlaces)}"
                } else {
                    filteredValue
                }
            } else {
                value // 保持原值
            }

            onValueChange(validValue)
        },
        label = { Text(text = label.asString()) },
        placeholder = placeholder?.asString() ?: "",
        modifier = modifier,
        isError = isError || !isValidNumber || !isInRange,
        errorMessage = when {
            !isValidNumber -> UiText.DynamicString("请输入有效数字")
            !isInRange -> {
                when {
                    minValue != null && maxValue != null ->
                        UiText.DynamicString("请输入 $minValue - $maxValue 之间的数字")
                    minValue != null ->
                        UiText.DynamicString("请输入大于等于 $minValue 的数字")
                    maxValue != null ->
                        UiText.DynamicString("请输入小于等于 $maxValue 的数字")
                    else -> null
                }
            }
            else -> errorMessage
        },
        keyboardOptions = keyboardOptions,
        enabled = enabled,
        trailingIcon = unit?.let {
            {
                Text(
                    text = it.asString(),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                )
            }
        },
    )
}

@Preview
@Composable
private fun numberFieldPreview() {
    GymBroTheme {
        var value by remember { mutableStateOf("65.5") }

        numberField(
            value = value,
            onValueChange = { value = it },
            label = UiText.DynamicString("体重"),
            unit = UiText.DynamicString("kg"),
            minValue = 30.0,
            maxValue = 200.0,
            decimalPlaces = 1,
        )
    }
}

@Preview(name = "Basic Number Field", showBackground = true)
@Composable
private fun numberFieldBasicPreview() {
    GymBroTheme {
        var value by remember { mutableStateOf("") }
        numberField(
            value = value,
            onValueChange = { value = it },
            label = UiText.DynamicString("数量"),
        )
    }
}

@Preview(name = "With Unit", showBackground = true)
@Composable
private fun numberFieldWithUnitPreview() {
    GymBroTheme {
        var value by remember { mutableStateOf("") }
        numberField(
            value = value,
            onValueChange = { value = it },
            label = UiText.DynamicString("重量"),
            unit = UiText.DynamicString("kg"),
        )
    }
}

@Preview(name = "With Range", showBackground = true)
@Composable
private fun numberFieldWithRangePreview() {
    GymBroTheme {
        var value by remember { mutableStateOf("") }
        numberField(
            value = value,
            onValueChange = { value = it },
            label = UiText.DynamicString("年龄"),
            minValue = 18.0,
            maxValue = 100.0,
        )
    }
}

@Preview(name = "Decimal Places", showBackground = true)
@Composable
private fun numberFieldDecimalPreview() {
    GymBroTheme {
        var value by remember { mutableStateOf("") }
        numberField(
            value = value,
            onValueChange = { value = it },
            label = UiText.DynamicString("体重"),
            decimalPlaces = 1,
            unit = UiText.DynamicString("kg"),
        )
    }
}

@Preview(name = "Error State", showBackground = true)
@Composable
private fun numberFieldErrorPreview() {
    GymBroTheme {
        var value by remember { mutableStateOf("") }
        numberField(
            value = value,
            onValueChange = { value = it },
            label = UiText.DynamicString("无效数字"),
            isError = true,
            errorMessage = UiText.DynamicString("请输入有效的数字"),
        )
    }
}

@Preview(name = "Disabled", showBackground = true)
@Composable
private fun numberFieldDisabledPreview() {
    GymBroTheme {
        numberField(
            value = "42",
            onValueChange = {},
            label = UiText.DynamicString("禁用字段"),
            enabled = false,
        )
    }
}
