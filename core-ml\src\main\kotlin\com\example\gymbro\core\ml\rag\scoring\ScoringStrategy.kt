package com.example.gymbro.core.ml.rag.scoring

import javax.inject.Inject
import javax.inject.Singleton

/**
 * RAG搜索结果评分策略接口
 *
 * 基于 history_code_review.md 建议，将评分逻辑从RagContextRetrievalUseCase中提取，
 * 实现评分算法的可替换性和可测试性。
 */
interface ScoringStrategy {

    /**
     * 计算综合分数
     *
     * @param vectorScore 向量相似度分数 [0.0-1.0]
     * @param keywordScore 关键词匹配分数 [0.0-1.0]
     * @param hybridWeight 混合权重 [0.0-1.0]，0.0=纯关键词，1.0=纯向量
     * @param timestamp 消息时间戳
     * @param isCurrentSession 是否为当前会话
     * @return 综合分数
     */
    fun calculateCombinedScore(
        vectorScore: Float,
        keywordScore: Float,
        hybridWeight: Float,
        timestamp: Long,
        isCurrentSession: Boolean,
    ): Float
}

/**
 * 默认评分策略实现
 *
 * 基于混合搜索 + 时间衰减 + 会话惩罚的综合评分算法：
 * - 混合分数：向量相似度和关键词匹配的加权平均
 * - 时间衰减：30天半衰期的指数衰减
 * - 会话惩罚：当前会话内容降权50%
 * 🔥 修复：添加@Singleton注解以匹配SingletonComponent作用域
 */
@Singleton
class DefaultScoringStrategy @Inject constructor() : ScoringStrategy {

    companion object {
        private const val TIME_DECAY_HALF_LIFE_DAYS = 30f
        private const val CURRENT_SESSION_PENALTY = 0.5f
        private const val MILLIS_PER_DAY = 24 * 60 * 60 * 1000f
    }

    override fun calculateCombinedScore(
        vectorScore: Float,
        keywordScore: Float,
        hybridWeight: Float,
        timestamp: Long,
        isCurrentSession: Boolean,
    ): Float {
        // 基础混合分数
        val baseScore = vectorScore * hybridWeight + keywordScore * (1 - hybridWeight)

        // 时间衰减因子（最近的消息权重更高）
        val daysSinceMessage = (System.currentTimeMillis() - timestamp) / MILLIS_PER_DAY
        val timeDecay = kotlin.math.exp(-daysSinceMessage / TIME_DECAY_HALF_LIFE_DAYS)

        // 当前会话惩罚（避免检索当前会话的内容）
        val sessionPenalty = if (isCurrentSession) CURRENT_SESSION_PENALTY else 1.0f

        return baseScore * timeDecay * sessionPenalty
    }
}

/**
 * 简化评分策略（仅混合分数，无时间衰减）
 *
 * 适用于需要更简单、更可预测的评分场景
 */
class SimpleScoringStrategy : ScoringStrategy {

    override fun calculateCombinedScore(
        vectorScore: Float,
        keywordScore: Float,
        hybridWeight: Float,
        timestamp: Long,
        isCurrentSession: Boolean,
    ): Float {
        return vectorScore * hybridWeight + keywordScore * (1 - hybridWeight)
    }
}
