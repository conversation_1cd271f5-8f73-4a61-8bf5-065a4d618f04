package com.example.gymbro.designSystem.components.base.layout

import androidx.compose.foundation.layout.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier

/**
 * 统一的状态栏自适应布局组件
 *
 * 为GymBro应用提供统一的状态栏空间处理
 * 所有页面都应该使用这个组件来确保一致的状态栏适配
 *
 * 使用方式：
 * ```kotlin
 * StatusBarAdaptiveLayout {
 *     // 页面内容
 * }
 * ```
 */
@Composable
fun StatusBarAdaptiveLayout(
    modifier: Modifier = Modifier,
    content: @Composable ColumnScope.() -> Unit,
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .statusBarsPadding(), // 🎯 统一状态栏适配
    ) {
        content()
    }
}

/**
 * 带有导航栏适配的布局组件
 * 适用于需要同时处理状态栏和导航栏的页面
 */
@Composable
fun SystemBarsAdaptiveLayout(
    modifier: Modifier = Modifier,
    content: @Composable ColumnScope.() -> Unit,
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .systemBarsPadding(), // 🎯 统一系统栏适配
    ) {
        content()
    }
}

/**
 * 仅状态栏适配的Modifier扩展
 * 用于需要手动控制布局的场景
 */
val Modifier.gymBroStatusBarsPadding: Modifier
    get() = this.statusBarsPadding()

/**
 * 系统栏适配的Modifier扩展
 * 用于需要手动控制布局的场景
 */
val Modifier.gymBroSystemBarsPadding: Modifier
    get() = this.systemBarsPadding()
