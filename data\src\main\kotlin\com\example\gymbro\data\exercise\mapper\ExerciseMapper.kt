package com.example.gymbro.data.exercise.mapper

import com.example.gymbro.core.resources.ResourceProvider
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.core.ui.text.asStringExt
import com.example.gymbro.data.exercise.local.entity.ExerciseEntity
import com.example.gymbro.domain.exercise.model.Exercise
import com.example.gymbro.domain.exercise.model.ExerciseEquipment
import com.example.gymbro.domain.exercise.model.ExerciseMuscle
import com.example.gymbro.domain.exercise.model.getDisplayName
import com.example.gymbro.shared.models.exercise.ExerciseDto
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Exercise映射器
 * 负责ExerciseDto和Exercise之间的双向转换
 *
 * 遵循Clean Architecture原则：
 * - DTO用于数据传输和存储
 * - Domain模型用于业务逻辑
 */
@Singleton
class ExerciseMapper
@Inject
constructor(
    private val resourceProvider: ResourceProvider,
) {
    /**
     * 将ExerciseDto转换为Domain模型
     * @param dto 数据传输对象
     * @return Domain模型
     */
    fun toDomain(dto: ExerciseDto): Exercise =
        Exercise(
            id = dto.id,
            name = UiText.DynamicString(dto.name),
            muscleGroup = dto.muscleGroup,
            equipment = dto.equipment,
            description = UiText.DynamicString(dto.description ?: ""),
            imageUrl = dto.imageUrl,
            videoUrl = dto.videoUrl,
            defaultSets = dto.defaultSets,
            defaultReps = dto.defaultReps,
            defaultWeight = dto.defaultWeight,
            steps = dto.steps.map { UiText.DynamicString(it) },
            tips = dto.tips.map { UiText.DynamicString(it) },
            userId = dto.userId,
            isCustom = dto.isCustom,
            isFavorite = dto.isFavorite,
            difficultyLevel = dto.difficultyLevel,
            calories = dto.calories,
            targetMuscles = dto.targetMuscles,
            instructions = dto.instructions.map { UiText.DynamicString(it) },
            genderNotes = emptyList(), // TODO: 映射性别注释
            mediaAssets = emptyList(), // TODO: 映射媒体资源
            exerciseMuscles = createExerciseMuscles(dto),
            exerciseEquipments = createExerciseEquipments(dto),
            createdAt = dto.createdAt ?: System.currentTimeMillis(),
            updatedAt = dto.updatedAt ?: System.currentTimeMillis(),
            createdByUserId = dto.createdByUserId,
        )

    /**
     * 将Domain模型转换为ExerciseDto
     * @param domain Domain模型
     * @return 数据传输对象
     */
    fun toDto(domain: Exercise): ExerciseDto =
        ExerciseDto(
            id = domain.id,
            name = domain.name.asStringExt(resourceProvider),
            muscleGroup = domain.muscleGroup,
            equipment = domain.equipment,
            description = domain.description.asStringExt(resourceProvider),
            imageUrl = domain.imageUrl,
            videoUrl = domain.videoUrl,
            defaultSets = domain.defaultSets,
            defaultReps = domain.defaultReps,
            defaultWeight = domain.defaultWeight,
            steps = domain.steps.map { it.asStringExt(resourceProvider) },
            tips = domain.tips.map { it.asStringExt(resourceProvider) },
            userId = domain.userId,
            isCustom = domain.isCustom,
            isFavorite = domain.isFavorite,
            difficultyLevel = domain.difficultyLevel,
            calories = domain.calories,
            targetMuscles = domain.targetMuscles,
            instructions = domain.instructions.map { it.asStringExt(resourceProvider) },
            embedding = null, // TODO: 从向量引擎获取
            createdAt = domain.createdAt ?: System.currentTimeMillis(),
            updatedAt = domain.updatedAt ?: System.currentTimeMillis(),
            createdByUserId = domain.createdByUserId,
        )

    /**
     * 批量转换DTO列表到Domain列表
     */
    fun toDomainList(dtos: List<ExerciseDto>): List<Exercise> = dtos.map { toDomain(it) }

    /**
     * 批量转换Domain列表到DTO列表
     */
    fun toDtoList(domains: List<Exercise>): List<ExerciseDto> = domains.map { toDto(it) }

    // ==================== Entity转换方法 ====================

    /**
     * 将ExerciseEntity转换为Domain模型
     * @param entity Room实体
     * @return Domain模型
     */
    fun toDomain(entity: ExerciseEntity): Exercise =
        Exercise(
            id = entity.id,
            name = UiText.DynamicString(entity.name),
            muscleGroup = entity.muscleGroup,
            equipment = entity.equipment,
            description = UiText.DynamicString(entity.description ?: ""),
            imageUrl = entity.imageUrl,
            videoUrl = entity.videoUrl,
            defaultSets = entity.defaultSets,
            defaultReps = entity.defaultReps,
            defaultWeight = entity.defaultWeight ?: 0f,
            steps = entity.steps.map { UiText.DynamicString(it) },
            tips = entity.tips.map { UiText.DynamicString(it) },
            userId = entity.userId,
            isCustom = entity.isCustom,
            isFavorite = entity.isFavorite,
            difficultyLevel = entity.difficultyLevel,
            calories = entity.calories ?: 0,
            targetMuscles = entity.targetMuscles,
            instructions = entity.instructions.map { UiText.DynamicString(it) },
            genderNotes = emptyList(), // TODO: 映射性别注释
            mediaAssets = emptyList(), // TODO: 映射媒体资源
            exerciseMuscles = createExerciseMusclesFromEntity(entity),
            exerciseEquipments = createExerciseEquipmentsFromEntity(entity),
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt,
            createdByUserId = entity.createdByUserId ?: "",
        )

    /**
     * 将Domain模型转换为ExerciseEntity
     * @param domain Domain模型
     * @return Room实体
     */
    fun toEntity(domain: Exercise): ExerciseEntity =
        ExerciseEntity(
            id = domain.id,
            name = domain.name.asStringExt(resourceProvider),
            muscleGroup = domain.muscleGroup,
            equipment = domain.equipment,
            description = domain.description.asStringExt(resourceProvider),
            imageUrl = domain.imageUrl,
            videoUrl = domain.videoUrl,
            defaultSets = domain.defaultSets,
            defaultReps = domain.defaultReps,
            defaultWeight = domain.defaultWeight,
            steps = domain.steps.map { it.asStringExt(resourceProvider) },
            tips = domain.tips.map { it.asStringExt(resourceProvider) },
            userId = domain.userId,
            isCustom = domain.isCustom,
            isFavorite = domain.isFavorite,
            difficultyLevel = domain.difficultyLevel,
            calories = domain.calories,
            targetMuscles = domain.targetMuscles,
            instructions = domain.instructions.map { it.asStringExt(resourceProvider) },
            embedding = null, // TODO: 从向量引擎获取
            createdAt = domain.createdAt ?: System.currentTimeMillis(),
            updatedAt = domain.updatedAt ?: System.currentTimeMillis(),
            createdByUserId = domain.createdByUserId,
        )

    /**
     * 批量转换Entity列表到Domain列表
     */
    fun toDomainListFromEntity(
        entities: List<ExerciseEntity>,
    ): List<Exercise> = entities.map { toDomain(it) }

    /**
     * 批量转换Domain列表到Entity列表
     */
    fun toEntityList(domains: List<Exercise>): List<ExerciseEntity> = domains.map { toEntity(it) }

    /**
     * 创建ExerciseMuscle列表
     * 基于DTO的肌群信息创建详细的肌肉激活数据
     */
    private fun createExerciseMuscles(dto: ExerciseDto): List<ExerciseMuscle> {
        val muscles = mutableListOf<ExerciseMuscle>()

        // 主要肌群
        muscles.add(
            ExerciseMuscle(
                exerciseId = dto.id,
                muscleGroup = dto.muscleGroup,
                role = com.example.gymbro.domain.exercise.model.MuscleRole.PRIMARY,
                isPrimary = true,
                activationLevel = 5,
            ),
        )

        // 辅助肌群
        dto.targetMuscles.forEach { muscle ->
            if (muscle != dto.muscleGroup) {
                muscles.add(
                    ExerciseMuscle(
                        exerciseId = dto.id,
                        muscleGroup = muscle,
                        role = com.example.gymbro.domain.exercise.model.MuscleRole.SECONDARY,
                        isPrimary = false,
                        activationLevel = 3,
                    ),
                )
            }
        }

        return muscles
    }

    /**
     * 创建ExerciseEquipment列表
     * 基于DTO的器械信息创建详细的器械需求数据
     */
    private fun createExerciseEquipments(dto: ExerciseDto): List<ExerciseEquipment> =
        dto.equipment.mapIndexed { index, equipment ->
            ExerciseEquipment(
                exerciseId = dto.id,
                equipment = equipment,
                isRequired = index == 0, // 第一个器械为必需
                importance =
                if (index == 0) {
                    com.example.gymbro.domain.exercise.model.EquipmentImportance.REQUIRED
                } else {
                    com.example.gymbro.domain.exercise.model.EquipmentImportance.OPTIONAL
                },
                alternatives = emptyList(), // TODO: 添加替代器械逻辑
                notes = "器械：${equipment.getDisplayName()}",
            )
        }

    // ==================== Entity辅助方法 ====================

    /**
     * 创建ExerciseMuscle列表（从Entity）
     * 基于Entity的肌群信息创建详细的肌肉激活数据
     */
    private fun createExerciseMusclesFromEntity(entity: ExerciseEntity): List<ExerciseMuscle> {
        val muscles = mutableListOf<ExerciseMuscle>()

        // 主要肌群
        muscles.add(
            ExerciseMuscle(
                exerciseId = entity.id,
                muscleGroup = entity.muscleGroup,
                role = com.example.gymbro.domain.exercise.model.MuscleRole.PRIMARY,
                isPrimary = true,
                activationLevel = 5,
            ),
        )

        // 辅助肌群
        entity.targetMuscles.forEach { muscle ->
            if (muscle != entity.muscleGroup) {
                muscles.add(
                    ExerciseMuscle(
                        exerciseId = entity.id,
                        muscleGroup = muscle,
                        role = com.example.gymbro.domain.exercise.model.MuscleRole.SECONDARY,
                        isPrimary = false,
                        activationLevel = 3,
                    ),
                )
            }
        }

        return muscles
    }

    /**
     * 创建ExerciseEquipment列表（从Entity）
     * 基于Entity的器械信息创建详细的器械需求数据
     */
    private fun createExerciseEquipmentsFromEntity(entity: ExerciseEntity): List<ExerciseEquipment> =
        entity.equipment.mapIndexed { index, equipment ->
            ExerciseEquipment(
                exerciseId = entity.id,
                equipment = equipment,
                isRequired = index == 0, // 第一个器械为必需
                importance =
                if (index == 0) {
                    com.example.gymbro.domain.exercise.model.EquipmentImportance.REQUIRED
                } else {
                    com.example.gymbro.domain.exercise.model.EquipmentImportance.OPTIONAL
                },
                alternatives = emptyList(), // TODO: 添加替代器械逻辑
                notes = "器械：${equipment.getDisplayName()}",
            )
        }
}
