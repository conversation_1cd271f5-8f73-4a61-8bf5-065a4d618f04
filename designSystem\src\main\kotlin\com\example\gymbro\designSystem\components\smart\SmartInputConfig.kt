package com.example.gymbro.designSystem.components.smart

import androidx.compose.material.icons.automirrored.filled.Send
import androidx.compose.ui.text.input.KeyboardType

/**
 * SmartInput配置系统
 * 提供自动保存、验证、动画、建议等智能功能的配置
 */
data class SmartInputConfig(
    val autoSave: AutoSave? = null,
    val validation: Validation? = null,
    val animation: Animation = Animation(),
    val suggestions: Suggestions? = null,
    val actionButton: ActionButton? = null,
) {

    /**
     * 自动保存配置
     */
    data class AutoSave(
        val key: String,
        val enabled: Boolean = true,
        val debounceMs: Long = 500L,
    )

    /**
     * 验证配置
     */
    data class Validation(
        val rules: List<ValidationRule> = emptyList(),
        val showErrorsInstantly: Boolean = false,
        val enabled: Boolean = true,
    )

    /**
     * 动画配置
     */
    data class Animation(
        val enabled: Boolean = true,
        val duration: Long = 300L,
        val focusEnabled: Boolean = true,
        val errorEnabled: Boolean = true,
    )

    /**
     * 建议配置
     */
    data class Suggestions(
        val enabled: Boolean = true,
        val maxVisible: Int = 5,
        val debounceMs: Long = 300L,
    )

    /**
     * 动作按钮配置
     */
    sealed class ActionButton {
        data class Send(
            val onClick: () -> Unit,
            val enabled: Boolean = true,
        ) : ActionButton()

        data class Custom(
            val iconRes: String,
            val onClick: () -> Unit,
            val enabled: Boolean = true,
        ) : ActionButton()
    }

    companion object {
        /**
         * 预设配置：基础输入
         */
        fun basic(saveKey: String? = null): SmartInputConfig {
            return SmartInputConfig(
                autoSave = saveKey?.let { AutoSave(it) },
            )
        }

        /**
         * 预设配置：聊天输入
         */
        fun chat(saveKey: String): SmartInputConfig {
            return SmartInputConfig(
                autoSave = AutoSave(saveKey),
                suggestions = Suggestions(enabled = true),
                actionButton = ActionButton.Send(onClick = {}),
                animation = Animation(focusEnabled = true),
            )
        }

        /**
         * 预设配置：搜索输入
         */
        fun search(): SmartInputConfig {
            return SmartInputConfig(
                suggestions = Suggestions(enabled = true, maxVisible = 8),
                animation = Animation(enabled = true),
            )
        }

        /**
         * 预设配置：表单输入
         */
        fun form(saveKey: String, validationRules: List<ValidationRule> = emptyList()): SmartInputConfig {
            return SmartInputConfig(
                autoSave = AutoSave(saveKey),
                validation = Validation(rules = validationRules, showErrorsInstantly = true),
                animation = Animation(errorEnabled = true),
            )
        }

        /**
         * 智能配置推断：从现有参数自动推断最佳配置
         */
        fun fromContext(
            placeholder: String,
            keyboardType: KeyboardType,
            singleLine: Boolean,
        ): SmartInputConfig {
            return when {
                // 聊天类输入
                placeholder.contains("聊天", ignoreCase = true) ||
                    placeholder.contains("消息", ignoreCase = true) ||
                    placeholder.contains("说点什么", ignoreCase = true) -> {
                    chat("smart_chat_${placeholder.hashCode()}")
                }

                // 搜索类输入
                placeholder.contains("搜索", ignoreCase = true) ||
                    placeholder.contains("查找", ignoreCase = true) -> {
                    search()
                }

                // 表单类输入
                keyboardType == KeyboardType.Email ||
                    keyboardType == KeyboardType.Password ||
                    placeholder.contains("输入", ignoreCase = true) -> {
                    form("smart_form_${placeholder.hashCode()}")
                }

                // 默认基础配置
                else -> basic("smart_input_${placeholder.hashCode()}")
            }
        }
    }
}

/**
 * 验证规则抽象类
 */
sealed class ValidationRule {
    abstract fun validate(input: String): ValidationError?

    data class Required(val message: String = "此字段为必填项") : ValidationRule() {
        override fun validate(input: String): ValidationError? {
            return if (input.isBlank()) ValidationError(message) else null
        }
    }

    data class MinLength(
        val minLength: Int,
        val message: String = "最少需要 $minLength 个字符",
    ) : ValidationRule() {
        override fun validate(input: String): ValidationError? {
            return if (input.length < minLength) ValidationError(message) else null
        }
    }

    data class MaxLength(
        val maxLength: Int,
        val message: String = "最多只能输入 $maxLength 个字符",
    ) : ValidationRule() {
        override fun validate(input: String): ValidationError? {
            return if (input.length > maxLength) ValidationError(message) else null
        }
    }

    data class Email(val message: String = "请输入有效的邮箱地址") : ValidationRule() {
        override fun validate(input: String): ValidationError? {
            val emailPattern = Regex("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}")
            return if (input.isNotBlank() && !emailPattern.matches(input)) {
                ValidationError(message)
            } else {
                null
            }
        }
    }

    data class Phone(val message: String = "请输入有效的手机号码") : ValidationRule() {
        override fun validate(input: String): ValidationError? {
            val phonePattern = Regex("^1[3-9]\\d{9}$")
            return if (input.isNotBlank() && !phonePattern.matches(input)) {
                ValidationError(message)
            } else {
                null
            }
        }
    }

    data class NumberRange(
        val min: Double? = null,
        val max: Double? = null,
        val message: String = when {
            min != null && max != null -> "请输入 $min - $max 之间的数字"
            min != null -> "请输入大于等于 $min 的数字"
            max != null -> "请输入小于等于 $max 的数字"
            else -> "请输入有效数字"
        },
    ) : ValidationRule() {
        override fun validate(input: String): ValidationError? {
            if (input.isBlank()) return null

            val number = input.toDoubleOrNull() ?: return ValidationError("请输入有效数字")

            return when {
                min != null && number < min -> ValidationError(message)
                max != null && number > max -> ValidationError(message)
                else -> null
            }
        }
    }

    data class Custom(
        val pattern: String,
        val message: String = "输入格式不正确",
    ) : ValidationRule() {
        override fun validate(input: String): ValidationError? {
            val regex = Regex(pattern)
            return if (input.isNotBlank() && !regex.matches(input)) {
                ValidationError(message)
            } else {
                null
            }
        }
    }
}

/**
 * 验证错误类
 */
data class ValidationError(
    val message: String,
    val field: String? = null,
)
