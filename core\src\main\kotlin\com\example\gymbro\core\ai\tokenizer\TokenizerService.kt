package com.example.gymbro.core.ai.tokenizer

/**
 * Tokenizer服务接口
 *
 * 提供文本分词和Token计算功能的抽象接口
 * 具体实现在 core-ml 模块中，避免循环依赖
 */
interface TokenizerService {

    /**
     * 计算文本的 token 数量
     *
     * @param text 输入文本
     * @param modelType 可选的模型类型标识
     * @return token 数量
     */
    fun countTokens(text: String, modelType: String? = null): Int

    /**
     * 对文本进行编码，返回 token IDs
     *
     * @param text 输入文本
     * @param modelType 可选的模型类型标识
     * @return token IDs 列表
     */
    fun encode(text: String, modelType: String? = null): List<Int>

    /**
     * 对 token IDs 进行解码，返回文本
     *
     * @param tokenIds token IDs 列表
     * @param modelType 可选的模型类型标识
     * @return 解码后的文本
     */
    fun decode(tokenIds: List<Int>, modelType: String? = null): String

    /**
     * 截断文本到指定的 token 数量
     *
     * @param text 输入文本
     * @param maxTokens 最大 token 数量
     * @param modelType 可选的模型类型标识
     * @return 截断后的文本
     */
    fun truncateToTokenLimit(text: String, maxTokens: Int, modelType: String? = null): String

    /**
     * 检查 tokenizer 是否可用
     *
     * @return true 如果 tokenizer 正常工作
     */
    fun isAvailable(): Boolean

    /**
     * 获取 tokenizer 类型
     *
     * @return tokenizer 类型标识
     */
    fun getType(): TokenizerType
}

/**
 * Tokenizer 类型枚举
 */
enum class TokenizerType {
    /**
     * 基于字符数的简单估算
     */
    CHARACTER_BASED,

    /**
     * 基于单词数的估算
     */
    WORD_BASED,

    /**
     * BGE 模型的分词器
     */
    BGE_TOKENIZER,

    /**
     * OpenAI 官方 tokenizer (JTokkit)
     */
    OPENAI_TOKENIZER,
}

/**
 * 简单的字符基础 Tokenizer 实现
 * 作为降级策略使用
 */
class CharacterBasedTokenizerService : TokenizerService {

    companion object {
        private const val CHARS_PER_TOKEN = 4
    }

    override fun countTokens(text: String, modelType: String?): Int {
        return (text.length + CHARS_PER_TOKEN - 1) / CHARS_PER_TOKEN
    }

    override fun encode(text: String, modelType: String?): List<Int> {
        // 简化实现：返回字符的ASCII值
        return text.take(100).map { it.code }
    }

    override fun decode(tokenIds: List<Int>, modelType: String?): String {
        // 简化实现：将ASCII值转回字符
        return tokenIds.mapNotNull {
            if (it in 32..126) it.toChar() else null
        }.joinToString("")
    }

    override fun truncateToTokenLimit(text: String, maxTokens: Int, modelType: String?): String {
        val maxChars = maxTokens * CHARS_PER_TOKEN
        return if (text.length <= maxChars) {
            text
        } else {
            text.take(maxChars) + "..."
        }
    }

    override fun isAvailable(): Boolean = true

    override fun getType(): TokenizerType = TokenizerType.CHARACTER_BASED
}

/**
 * 模型类型常量
 */
object ModelTypes {
    const val GPT_4 = "gpt-4"
    const val GPT_4_TURBO = "gpt-4-turbo"
    const val GPT_3_5_TURBO = "gpt-3.5-turbo"
    const val TEXT_EMBEDDING_ADA_002 = "text-embedding-ada-002"
    const val TEXT_DAVINCI_003 = "text-davinci-003"
}
