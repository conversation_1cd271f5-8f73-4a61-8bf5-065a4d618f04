package com.example.gymbro.core.ai.prompt.memory

import com.example.gymbro.core.ai.prompt.builder.CoreChatMessage

/**
 * Memory系统集成器接口
 *
 * 负责将4层记忆系统（ECM/DWM/UPM/GIM）集成到Prompt构建流程中
 * 遵循Clean Architecture原则，core层只定义接口，data层提供实现
 *
 * @since 618重构 - 架构修复
 */
interface MemoryIntegrator {

    /**
     * 构建Memory相关的ChatMessage列表
     *
     * @param context Memory上下文，包含查询信息和用户ID
     * @param tokenBudget 可用的token预算，默认1000
     * @return 格式化后的tool消息列表
     *
     * @since 618重构
     */
    suspend fun buildMemoryMessages(
        context: MemoryContext?,
        tokenBudget: Int = 1000,
    ): List<CoreChatMessage>
}

/**
 * Memory上下文
 *
 * @property userId 用户ID
 * @property query 查询关键词
 * @property contextType 上下文类型
 * @property metadata 额外元数据
 *
 * @since 618重构
 */
data class MemoryContext(
    val userId: String,
    val query: String,
    val contextType: MemoryContextType = MemoryContextType.GENERAL,
    val metadata: Map<String, Any> = emptyMap(),
)

/**
 * Memory上下文类型
 *
 * @since 618重构
 */
enum class MemoryContextType {
    CONVERSATION, // 对话上下文
    TRAINING, // 训练相关
    PROFILE, // 用户档案
    GENERAL, // 通用上下文
}
