package com.example.gymbro.core.arch.mvi

import androidx.compose.runtime.Immutable
import com.example.gymbro.core.ui.text.UiText

/**
 * MVI架构 - UI状态抽象 v6.0-OPTIMIZED
 *
 * 🔥 v6.0核心改进：
 * 1. @Immutable强制要求 - 保证重组性能优化
 * 2. LazyColumn优化 - 内置items管理和key稳定性
 * 3. 流式内容支持 - 专为AI思考框等场景设计
 * 4. ModernResult集成 - 标准化错误处理
 * 5. 派生状态优化 - 减少不必要的重组
 *
 * 所有Feature的State都应遵循此契约，确保性能和一致性
 *
 * @since v6.0 - 性能优化与项目标准集成
 */
@Immutable
interface UiState

/**
 * 通用UI状态基类 - 强制性能优化
 *
 * 🔥 核心改进：
 * - @Immutable强制要求
 * - UiText标准错误处理
 * - 派生状态支持
 */
@Immutable
interface BaseUiState : UiState {
    val isLoading: Boolean
    val error: UiText?
    val isEmpty: Boolean
}

/**
 * 🔥 新增：Box+LazyColumn优化的状态基类
 * 专为Box+LazyColumn+Surface架构设计
 *
 * 核心特性：
 * - 稳定的items和key管理
 * - 重组优化
 * - 滚动状态管理
 */
@Immutable
interface BoxListUiState<T> : BaseUiState {
    val items: List<T>
    val isRefreshing: Boolean
    val hasMore: Boolean
    val scrollToTop: Boolean
    val scrollToBottom: Boolean

    override val isEmpty: Boolean
        get() = items.isEmpty() && !isLoading

    /**
     * 🔥 性能优化：为LazyColumn提供稳定的key生成
     * 子类应实现此方法以确保列表性能
     */
    fun getItemKey(item: T): String

    /**
     * 🔥 派生状态：避免在Composable中计算
     */
    val itemCount: Int
        get() = items.size

    /**
     * 🔥 派生状态：列表状态快速判断
     */
    val hasItems: Boolean
        get() = items.isNotEmpty()
}

/**
 * 🔥 新增：流式内容状态 - 专为AI思考框等场景设计
 * 支持实时内容更新和流式渲染
 */
@Immutable
interface StreamingUiState<T> : BaseUiState {
    val content: T?
    val isStreaming: Boolean
    val streamingProgress: Float
    val streamingId: String?

    /**
     * 🔥 流式内容特有状态
     */
    val canCancelStreaming: Boolean
        get() = isStreaming && streamingId != null

    /**
     * 🔥 派生状态：内容完整性检查
     */
    val hasPartialContent: Boolean
        get() = content != null && isStreaming
}

/**
 * 简化的基础状态实现 - v6.0优化版
 *
 * 🔥 改进：
 * - @Immutable保证性能
 * - UiText错误处理
 * - 便捷的状态创建方法
 */
@Immutable
data class SimpleUiState(
    override val isLoading: Boolean = false,
    override val error: UiText? = null,
    override val isEmpty: Boolean = true,
) : BaseUiState {

    companion object {
        /**
         * 🔥 便捷方法：创建加载状态
         */
        fun loading(): SimpleUiState = SimpleUiState(
            isLoading = true,
            isEmpty = false,
        )

        /**
         * 🔥 便捷方法：创建错误状态
         */
        fun error(message: UiText): SimpleUiState = SimpleUiState(
            error = message,
            isEmpty = true,
        )

        /**
         * 🔥 便捷方法：创建成功状态
         */
        fun success(): SimpleUiState = SimpleUiState(
            isEmpty = false,
        )
    }
}

/**
 * 🔥 新增：Box+LazyColumn优化的列表状态实现
 * 为Profile、Workout、动作库模块提供高性能列表管理
 */
@Immutable
data class BoxListState<T>(
    override val items: List<T> = emptyList(),
    override val isLoading: Boolean = false,
    override val error: UiText? = null,
    override val isRefreshing: Boolean = false,
    override val hasMore: Boolean = false,
    override val scrollToTop: Boolean = false,
    override val scrollToBottom: Boolean = false,
    private val keySelector: (T) -> String,
) : BoxListUiState<T> {

    override fun getItemKey(item: T): String = keySelector(item)

    companion object {
        /**
         * 🔥 便捷方法：创建空列表状态
         */
        fun <T> empty(keySelector: (T) -> String): BoxListState<T> = BoxListState(
            keySelector = keySelector,
        )

        /**
         * 🔥 便捷方法：创建加载状态
         */
        fun <T> loading(keySelector: (T) -> String): BoxListState<T> = BoxListState(
            isLoading = true,
            keySelector = keySelector,
        )

        /**
         * 🔥 便捷方法：创建带数据的状态
         */
        fun <T> withItems(
            items: List<T>,
            keySelector: (T) -> String,
            hasMore: Boolean = false,
        ): BoxListState<T> = BoxListState(
            items = items,
            hasMore = hasMore,
            keySelector = keySelector,
        )
    }
}

/**
 * 🔥 新增：流式内容状态实现
 * 专为AI思考框、实时消息等场景优化
 */
@Immutable
data class StreamingState<T>(
    override val content: T? = null,
    override val isLoading: Boolean = false,
    override val error: UiText? = null,
    override val isStreaming: Boolean = false,
    override val streamingProgress: Float = 0f,
    override val streamingId: String? = null,
) : StreamingUiState<T> {

    override val isEmpty: Boolean
        get() = content == null && !isLoading

    companion object {
        /**
         * 🔥 便捷方法：创建空状态
         */
        fun <T> empty(): StreamingState<T> = StreamingState()

        /**
         * 🔥 便捷方法：开始流式传输
         */
        fun <T> startStreaming(streamingId: String): StreamingState<T> = StreamingState(
            isStreaming = true,
            streamingId = streamingId,
        )

        /**
         * 🔥 便捷方法：更新流式内容
         */
        fun <T> updateStreaming(
            content: T,
            progress: Float,
            streamingId: String,
        ): StreamingState<T> = StreamingState(
            content = content,
            isStreaming = true,
            streamingProgress = progress,
            streamingId = streamingId,
        )

        /**
         * 🔥 便捷方法：完成流式传输
         */
        fun <T> completeStreaming(content: T): StreamingState<T> = StreamingState(
            content = content,
            isStreaming = false,
            streamingProgress = 1f,
        )
    }
}

/**
 * 列表类型的UI状态 - 兼容性保持
 *
 * 🔥 v6.0: 推荐使用BoxListUiState替代此接口
 */
@Immutable
interface ListUiState<T> : BaseUiState {
    val items: List<T>
    val isRefreshing: Boolean
    val hasMore: Boolean

    override val isEmpty: Boolean
        get() = items.isEmpty() && !isLoading
}

/**
 * 详情类型的UI状态 - v6.0优化版
 * 适用于展示单个数据对象的页面
 */
@Immutable
interface DetailUiState<T> : BaseUiState {
    val data: T?
    val isEditing: Boolean
    val hasUnsavedChanges: Boolean

    override val isEmpty: Boolean
        get() = data == null && !isLoading

    /**
     * 🔥 派生状态：编辑状态管理
     */
    val canSave: Boolean
        get() = isEditing && hasUnsavedChanges && !isLoading
}

/**
 * 🔥 新增：详情状态的具体实现
 * 为Profile模块等详情页面提供标准实现
 */
@Immutable
data class DetailState<T>(
    override val data: T? = null,
    override val isLoading: Boolean = false,
    override val error: UiText? = null,
    override val isEditing: Boolean = false,
    override val hasUnsavedChanges: Boolean = false,
) : DetailUiState<T> {

    companion object {
        /**
         * 🔥 便捷方法：创建加载状态
         */
        fun <T> loading(): DetailState<T> = DetailState(
            isLoading = true,
        )

        /**
         * 🔥 便捷方法：创建带数据的状态
         */
        fun <T> withData(data: T): DetailState<T> = DetailState(
            data = data,
        )

        /**
         * 🔥 便捷方法：进入编辑模式
         */
        fun <T> editing(data: T): DetailState<T> = DetailState(
            data = data,
            isEditing = true,
        )
    }
}

/**
 * 🔥 新增：状态构建器 - 便捷的状态创建工具
 * 提供DSL风格的状态构建
 */
object StateBuilder {

    /**
     * 构建BoxListState
     */
    inline fun <T> boxList(
        noinline keySelector: (T) -> String,
        crossinline builder: BoxListStateBuilder<T>.() -> Unit = {},
    ): BoxListState<T> {
        val stateBuilder = BoxListStateBuilder(keySelector)
        stateBuilder.builder()
        return stateBuilder.build()
    }

    /**
     * 构建StreamingState
     */
    inline fun <T> streaming(
        crossinline builder: StreamingStateBuilder<T>.() -> Unit = {},
    ): StreamingState<T> {
        val stateBuilder = StreamingStateBuilder<T>()
        stateBuilder.builder()
        return stateBuilder.build()
    }
}

/**
 * BoxListState构建器
 */
class BoxListStateBuilder<T>(private val keySelector: (T) -> String) {
    var items: List<T> = emptyList()
    var isLoading: Boolean = false
    var error: UiText? = null
    var isRefreshing: Boolean = false
    var hasMore: Boolean = false
    var scrollToTop: Boolean = false
    var scrollToBottom: Boolean = false

    fun build(): BoxListState<T> = BoxListState(
        items = items,
        isLoading = isLoading,
        error = error,
        isRefreshing = isRefreshing,
        hasMore = hasMore,
        scrollToTop = scrollToTop,
        scrollToBottom = scrollToBottom,
        keySelector = keySelector,
    )
}

/**
 * StreamingState构建器
 */
class StreamingStateBuilder<T> {
    var content: T? = null
    var isLoading: Boolean = false
    var error: UiText? = null
    var isStreaming: Boolean = false
    var streamingProgress: Float = 0f
    var streamingId: String? = null

    fun build(): StreamingState<T> = StreamingState(
        content = content,
        isLoading = isLoading,
        error = error,
        isStreaming = isStreaming,
        streamingProgress = streamingProgress,
        streamingId = streamingId,
    )
}
