package com.example.gymbro.data.local.entity.auth

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * Token实体类
 * 用于存储认证令牌数据
 */
@Entity(tableName = "tokens")
data class TokenEntity(
    @PrimaryKey
    val id: Int = 1, // 固定ID为1，因为只存一个Token
    val accessToken: String,
    val refreshToken: String,
    val tokenType: String,
    val expiresIn: Long,
    val issuedAt: Long,
    val userId: String,
    val scope: String? = null,
)
