package com.example.gymbro.core.network.config

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 简化网络配置管理器
 *
 * 🎯 简化原则：
 * 1. 唯一配置数据流 - 全局只有一个NetworkConfig实例在生效
 * 2. 热切换机制 - 配置变更立即传播到所有网络客户端
 * 3. 简化日志 - 减少噪音，只记录关键变更
 * 4. 移除复杂溯源 - 简化状态管理
 */
@Singleton
class NetworkConfigManager @Inject constructor() {

    // 唯一配置数据流 - 全局只允许这一个实例
    private val _config = MutableStateFlow(createInitialConfig())
    val config: StateFlow<NetworkConfig> = _config.asStateFlow()

    // 🗑️ 移除配置变更历史记录 - 简化状态管理

    init {
        Timber.d("🔧 NetworkConfigManager 初始化")
    }

    /**
     * 🔥 简化配置切换
     *
     * @param newConfig 新的网络配置
     * @param reason 切换原因
     */
    fun switchProvider(newConfig: NetworkConfig, reason: String = "配置切换") {
        // 配置验证
        if (!newConfig.validate()) {
            Timber.e("🚨 配置验证失败: $reason")
            return
        }

        // 检查是否真的有变化
        if (_config.value == newConfig) {
            Timber.v("🔄 配置无变化，跳过切换")
            return
        }

        // 立即更新配置
        _config.value = newConfig
        Timber.d("✅ 配置已切换: $reason")
    }

    /**
     * 🔥 获取当前生效配置（供外部查询）
     */
    fun getCurrentConfig(): NetworkConfig = _config.value

    // 🗑️ 移除配置变更历史查询 - 简化状态管理

    /**
     * 🔄 重置到默认配置
     */
    fun resetToDefault(reason: String = "重置到默认") {
        switchProvider(NetworkConfig.createDefault(), reason)
    }

    /**
     * 记录配置变更日志 - 工业级溯源
     */
    private fun logConfigChange(
        action: String,
        newConfig: NetworkConfig,
        reason: String,
        oldConfig: NetworkConfig? = null,
    ) {
        val timestamp = getCurrentTimestamp()

        Timber.d("🔧 [$action] 网络配置变更 - $timestamp")
        Timber.d("  📋 变更原因: $reason")
        Timber.d("  🆕 新配置:")
        Timber.d("    - WebSocket URL: ${newConfig.getSecureWsUrl()}")
        Timber.d("    - REST URL: ${newConfig.getSecureRestUrl()}")
        Timber.d("    - API密钥: ${maskApiKey(newConfig.apiKey)}")
        Timber.d("    - 连接超时: ${newConfig.connectTimeoutSec}s")
        Timber.d("    - Ping间隔: ${newConfig.pingSec}s")
        Timber.d("    - 最大重连: ${newConfig.maxReconnect}")

        if (oldConfig != null) {
            Timber.d("  🔄 配置变更对比:")
            if (oldConfig.wsBase != newConfig.wsBase) {
                Timber.d("    - WebSocket: ${oldConfig.wsBase} → ${newConfig.wsBase}")
            }
            if (oldConfig.restBase != newConfig.restBase) {
                Timber.d("    - REST: ${oldConfig.restBase} → ${newConfig.restBase}")
            }
            if (oldConfig.apiKey != newConfig.apiKey) {
                Timber.d("    - API密钥: ${maskApiKey(oldConfig.apiKey)} → ${maskApiKey(newConfig.apiKey)}")
            }
        }

        Timber.d("  ⚡ 下游影响: 所有网络客户端将收到配置变更并重连")
    }

    // 🗑️ 移除配置变更历史记录 - 简化状态管理

    /**
     * 创建初始配置
     */
    private fun createInitialConfig(): NetworkConfig {
        return NetworkConfig.createDefault().copy(
            enableDebugLogging = true, // 初始启用调试日志
        )
    }

    /**
     * 生成时间戳
     */
    private fun getCurrentTimestamp(): String {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"))
    }

    /**
     * 遮盖API密钥（安全考虑）
     */
    private fun maskApiKey(apiKey: String): String {
        return when {
            apiKey.isEmpty() -> "❌ 空值"
            apiKey.length <= 8 -> "sk-***"
            else -> "${apiKey.take(8)}***${apiKey.takeLast(4)}"
        }
    }
}

/**
 * 配置变更记录（用于溯源和调试）
 */
data class ConfigChangeRecord(
    val timestamp: String,
    val oldConfig: NetworkConfig?,
    val newConfig: NetworkConfig,
    val reason: String,
)

/**
 * 🔧 网络连接溯源日志工具
 * 供网络客户端在实际连接时调用，确保"实际连接"与"预期配置"一致
 */
object NetworkConnectionLogger {

    /**
     * 记录实际连接日志
     * 必须在每次真正发起网络连接时调用
     */
    fun logActualConnection(
        type: String, // "WebSocket" 或 "REST"
        actualUrl: String,
        actualApiKey: String,
        provider: String = "unknown",
        user: String = "unknown",
    ) {
        val timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"))

        Timber.d("🔗 [$type] 实际网络连接 - $timestamp")
        Timber.d("  📡 实际URL: $actualUrl")
        Timber.d("  🔑 实际密钥: ${maskApiKey(actualApiKey)}")
        Timber.d("  🏷️ Provider: $provider")
        Timber.d("  👤 用户: $user")
        Timber.d("  ⚠️ 注意：此为实际生效配置，非预期配置")
    }

    /**
     * 记录连接状态变化
     */
    fun logConnectionStateChange(
        type: String,
        fromState: String,
        toState: String,
        reason: String,
    ) {
        val timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"))

        Timber.d("🔄 [$type] 连接状态变化 - $timestamp")
        Timber.d("  🔄 状态: $fromState → $toState")
        Timber.d("  📋 原因: $reason")
    }

    private fun maskApiKey(apiKey: String): String {
        return when {
            apiKey.isEmpty() -> "❌ 空值"
            apiKey.length <= 8 -> "sk-***"
            else -> "${apiKey.take(8)}***${apiKey.takeLast(4)}"
        }
    }
}
