package com.example.gymbro.data.ai.api

import com.example.gymbro.data.remote.subscription.dto.RegionalPricingDto
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Path

/**
 * 定价API接口
 * 提供与订阅价格和支付方式相关的网络请求方法
 */
interface PricingApi {
    /**
     * 获取指定区域的定价信息
     * @param region 区域代码
     * @return 区域定价DTO列表
     */
    @GET("pricing/regions/{region}")
    suspend fun getPricingForRegion(
        @Path("region") region: String,
    ): Response<List<RegionalPricingDto>>
}
