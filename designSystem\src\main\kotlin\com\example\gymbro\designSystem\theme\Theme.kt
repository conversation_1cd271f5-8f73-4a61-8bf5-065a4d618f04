package com.example.gymbro.designSystem.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.text.font.FontFamily
import androidx.core.view.WindowCompat
import com.example.gymbro.designSystem.theme.tokens.ColorTokens // 🔥 修复：添加ColorTokens导入
import com.example.gymbro.designSystem.theme.tokens.CornerRadius
import com.example.gymbro.designSystem.theme.tokens.DarkThemeColors
import com.example.gymbro.designSystem.theme.tokens.Elevation
import com.example.gymbro.designSystem.theme.tokens.GrayScale // 🔥 【主题修复】添加GrayScale导入
import com.example.gymbro.designSystem.theme.tokens.LightThemeColors
import com.example.gymbro.designSystem.theme.tokens.LocalProfileColors
import com.example.gymbro.designSystem.theme.tokens.LocalSpacing
import com.example.gymbro.designSystem.theme.tokens.ProfileColors
import com.example.gymbro.designSystem.theme.tokens.rememberGymBroTypography
import com.example.gymbro.designSystem.theme.tokens.rememberMapleMono
import com.example.gymbro.designSystem.theme.tokens.rememberSpacing

// 创建一个CompositionLocal来保存字体家族
private val LocalFontFamily = compositionLocalOf<FontFamily> { FontFamily.Default }

// 创建一个CompositionLocal来保存GymBro字体家族
val LocalGymBroFontFamily = compositionLocalOf<FontFamily> { FontFamily.Default }

// 创建一个CompositionLocal来保存当前主题状态（是否为暗色主题）
val LocalDarkTheme = compositionLocalOf<Boolean> { false }

// 提供主题状态的函数
object AppTheme {
    val isDarkTheme: Boolean
        @Composable
        @ReadOnlyComposable
        get() = LocalDarkTheme.current
}

// 提供字体家族的函数
object AppFont {
    val current: FontFamily
        @Composable
        @ReadOnlyComposable
        get() = LocalFontFamily.current
}

/**
 * 高级深色主题配色方案
 * 采用13级精致灰阶系统，营造高端科技感
 */
private val PremiumDarkColorScheme =
    darkColorScheme(
        // 主色系 - 铂金银，高级感十足
        primary = DarkThemeColors.primary,
        onPrimary = DarkThemeColors.onPrimary,
        primaryContainer = DarkThemeColors.primaryContainer,
        onPrimaryContainer = DarkThemeColors.onPrimaryContainer,
        // 次色系 - 暖灰调，营造温和氛围
        secondary = DarkThemeColors.secondary,
        onSecondary = DarkThemeColors.onSecondary,
        secondaryContainer = DarkThemeColors.secondaryContainer,
        onSecondaryContainer = DarkThemeColors.onSecondaryContainer,
        // 第三色系 - 浅石墨，精致层次
        tertiary = DarkThemeColors.tertiary,
        onTertiary = DarkThemeColors.onTertiary,
        tertiaryContainer = DarkThemeColors.tertiaryContainer,
        onTertiaryContainer = DarkThemeColors.onTertiaryContainer,
        // 错误色系 - 优雅红色
        error = DarkThemeColors.error,
        onError = DarkThemeColors.onError,
        errorContainer = DarkThemeColors.errorContainer,
        onErrorContainer = DarkThemeColors.onErrorContainer,
        // 背景色系 - 纯黑背景，深炭黑表面
        background = DarkThemeColors.background,
        onBackground = DarkThemeColors.onBackground,
        surface = DarkThemeColors.surface,
        onSurface = DarkThemeColors.onSurface,
        // 表面变体 - 炭灰色，丰富层次
        surfaceVariant = DarkThemeColors.surfaceVariant,
        onSurfaceVariant = DarkThemeColors.onSurfaceVariant,
        surfaceTint = DarkThemeColors.surfaceTint,
        // 轮廓色系 - 石墨色边框
        outline = DarkThemeColors.outline,
        outlineVariant = DarkThemeColors.outlineVariant,
        // 反转色系
        inverseSurface = DarkThemeColors.inverseSurface,
        inverseOnSurface = DarkThemeColors.inverseOnSurface,
        inversePrimary = DarkThemeColors.inversePrimary,
        // 遮罩
        scrim = DarkThemeColors.scrim,
    )

/**
 * 科技风格(GROK)深色主题配色方案
 * 基于workout模块的蓝色系强调色，与训练页面保持一致
 */
private val GrokDarkColorScheme =
    darkColorScheme(
        primary = Color(0xFF3F6CF3), // workout模块的Grok蓝色强调色
        onPrimary = Color(0xFFFFFFFF), // 白色文本
        background = Color(0xFF1A1A1A), // 与workout一致的深灰背景
        onBackground = Color(0xFFFFFFFF), // 白色文本
        surface = Color(0xFF2A2A2A), // 与workout一致的卡片背景
        onSurface = Color(0xFFFFFFFF), // 白色文本
        surfaceVariant = Color(0xFF2A2A2A), // 表面变体
        onSurfaceVariant = Color(0xFFB0B0B0), // 中灰色文本
        outline = Color(0xFF3A3A3A), // 边框颜色
    )

/**
 * 科技风格(GROK)浅色主题配色方案
 * 基于workout模块的蓝色系强调色，与训练页面保持一致
 */
private val GrokLightColorScheme =
    lightColorScheme(
        primary = Color(0xFF3F6CF3), // workout模块的Grok蓝色强调色
        onPrimary = Color(0xFFFFFFFF), // 白色文本
        background = Color(0xFFFFFFFF), // 纯白背景
        onBackground = Color(0xFF000000), // 黑色文本
        surface = Color(0xFFF5F5F5), // 与workout一致的浅灰表面
        onSurface = Color(0xFF000000), // 黑色文本
        surfaceVariant = Color(0xFFF0F0F0), // 表面变体
        onSurfaceVariant = Color(0xFF666666), // 中灰色文本
        outline = Color(0xFFE0E0E0), // 边框颜色
    )

/**
 * 简约风格(CHATGPT)深色主题配色方案
 * 基于workout模块的绿色系强调色，与训练页面保持一致
 */
private val ChatGPTDarkColorScheme =
    darkColorScheme(
        primary = Color(0xFF00BC80), // workout模块的ChatGPT绿色强调色
        onPrimary = Color(0xFFFFFFFF), // 白色文本
        background = Color(0xFF212121), // 与workout一致的深灰背景
        onBackground = Color(0xFFE2E8F0), // 浅色文本
        surface = Color(0xFF2A2A2A), // 与workout一致的表面颜色
        onSurface = Color(0xFFE2E8F0), // 浅色文本
        surfaceVariant = Color(0xFF2A2A2A), // 表面变体
        onSurfaceVariant = Color(0xFF9CA3AF), // 灰色文本
        outline = Color(0xFF374151), // 边框颜色
    )

/**
 * 简约风格(CHATGPT)浅色主题配色方案
 * 基于workout模块的绿色系强调色，与训练页面保持一致
 */
private val ChatGPTLightColorScheme =
    lightColorScheme(
        primary = Color(0xFF00BC80), // workout模块的ChatGPT绿色强调色
        onPrimary = Color(0xFFFFFFFF), // 白色文本
        background = Color(0xFFFFFFFF), // 纯白背景
        onBackground = Color(0xFF2D3748), // 深色文本
        surface = Color(0xFFF8F9FA), // 与workout一致的浅灰表面
        onSurface = Color(0xFF2D3748), // 深色文本
        surfaceVariant = Color(0xFFEDF2F7), // 表面变体
        onSurfaceVariant = Color(0xFF6B7280), // 灰色文本
        outline = Color(0xFFE5E7EB), // 边框颜色
    )

/**
 * DeepSeek风格深色主题配色方案
 * 基于workout模块的蓝色系强调色，与训练页面保持一致
 */
private val DeepSeekDarkColorScheme =
    darkColorScheme(
        primary = Color(0xFF4285F4), // workout模块的DeepSeek蓝色强调色
        onPrimary = Color(0xFFFFFFFF), // 白色文本
        background = Color(0xFF1E1E1E), // 与workout一致的深灰背景
        onBackground = Color(0xFFFFFFFF), // 白色文本
        surface = Color(0xFF2A2A2A), // 与workout一致的表面颜色
        onSurface = Color(0xFFFFFFFF), // 白色文本
        surfaceVariant = Color(0xFF2A2A2A), // 表面变体
        onSurfaceVariant = Color(0xFFB0B0B0), // 灰色文本
        outline = Color(0xFF2A2A2A), // 边框颜色
    )

/**
 * DeepSeek风格浅色主题配色方案
 * 基于workout模块的蓝色系强调色，与训练页面保持一致
 */
private val DeepSeekLightColorScheme =
    lightColorScheme(
        primary = Color(0xFF4285F4), // workout模块的DeepSeek蓝色强调色
        onPrimary = Color(0xFFFFFFFF), // 白色文本
        background = Color(0xFFFFFFFF), // 纯白背景
        onBackground = Color(0xFF000000), // 黑色文本
        surface = Color(0xFFF0F4F9), // 与workout一致的浅蓝灰表面
        onSurface = Color(0xFF000000), // 黑色文本
        surfaceVariant = Color(0xFFF0F4F9), // 表面变体
        onSurfaceVariant = Color(0xFF5F6368), // 灰色文本
        outline = Color(0xFFD8E2ED), // 边框颜色
    )

/**
 * 优雅浅色主题配色方案
 * 采用珍珠白和淡银色调，营造清新高级感
 */
private val ElegantLightColorScheme =
    lightColorScheme(
        // 主色系 - 深银色，适合浅色背景
        primary = LightThemeColors.primary,
        onPrimary = LightThemeColors.onPrimary,
        primaryContainer = LightThemeColors.primaryContainer,
        onPrimaryContainer = LightThemeColors.onPrimaryContainer,
        // 次色系 - 暖灰调，保持一致性
        secondary = LightThemeColors.secondary,
        onSecondary = LightThemeColors.onSecondary,
        secondaryContainer = LightThemeColors.secondaryContainer,
        onSecondaryContainer = LightThemeColors.onSecondaryContainer,
        // 第三色系 - 中性灰
        tertiary = LightThemeColors.tertiary,
        onTertiary = LightThemeColors.onTertiary,
        tertiaryContainer = LightThemeColors.tertiaryContainer,
        onTertiaryContainer = LightThemeColors.onTertiaryContainer,
        // 错误色系 - 深红色，适合浅色背景
        error = LightThemeColors.error,
        onError = LightThemeColors.onError,
        errorContainer = LightThemeColors.errorContainer,
        onErrorContainer = LightThemeColors.onErrorContainer,
        // 背景色系 - 纯白背景，清新优雅
        background = LightThemeColors.background,
        onBackground = LightThemeColors.onBackground,
        surface = LightThemeColors.surface,
        onSurface = LightThemeColors.onSurface,
        // 表面变体 - 珍珠白，微妙层次
        surfaceVariant = LightThemeColors.surfaceVariant,
        onSurfaceVariant = LightThemeColors.onSurfaceVariant,
        surfaceTint = LightThemeColors.surfaceTint,
        // 轮廓色系 - 浅石墨边框
        outline = LightThemeColors.outline,
        outlineVariant = LightThemeColors.outlineVariant,
        // 反转色系
        inverseSurface = LightThemeColors.inverseSurface,
        inverseOnSurface = LightThemeColors.inverseOnSurface,
        inversePrimary = LightThemeColors.inversePrimary,
        // 遮罩
        scrim = LightThemeColors.scrim,
    )

// 创建主题相关的令牌，供UI组件使用
data class ThemeTokens(
    val cornerRadius: CornerRadius = CornerRadius(),
    val elevation: Elevation = Elevation(),
)

// 创建CompositionLocal以提供ThemeTokens
val LocalThemeTokens = androidx.compose.runtime.staticCompositionLocalOf { ThemeTokens() }

// 为MaterialTheme添加token扩展属性
val MaterialTheme.token: ThemeTokens
    @Composable
    get() = LocalThemeTokens.current

// 为MaterialTheme添加workoutColors扩展属性
val MaterialTheme.workoutColors: com.example.gymbro.designSystem.theme.tokens.ColorTokens.WorkoutColors
    @Composable
    get() = com.example.gymbro.designSystem.theme.tokens.ColorTokens.WorkoutColors

// === Coach页面主题扩展 ===

/**
 * Coach页面专用颜色方案
 * 基于13阶GrayScale系统，提供优化的ChatGPT风格配色
 * 自动适配深色/浅色主题，提升美观度
 */
data class CoachColorScheme(
    // 背景层次
    val backgroundPrimary: androidx.compose.ui.graphics.Color,
    val backgroundSecondary: androidx.compose.ui.graphics.Color,
    val backgroundElevated: androidx.compose.ui.graphics.Color,
    val backgroundInput: androidx.compose.ui.graphics.Color,
    // 文本层次
    val textPrimary: androidx.compose.ui.graphics.Color,
    val textSecondary: androidx.compose.ui.graphics.Color,
    val textTertiary: androidx.compose.ui.graphics.Color,
    val textPlaceholder: androidx.compose.ui.graphics.Color,
    // 图标层次
    val iconPrimary: androidx.compose.ui.graphics.Color,
    val iconSecondary: androidx.compose.ui.graphics.Color,
    val iconTertiary: androidx.compose.ui.graphics.Color,
    // 边框和分割线
    val borderPrimary: androidx.compose.ui.graphics.Color,
    val borderSecondary: androidx.compose.ui.graphics.Color,
    val dividerPrimary: androidx.compose.ui.graphics.Color,
    val dividerSecondary: androidx.compose.ui.graphics.Color,
    // 交互元素
    val chipBackground: androidx.compose.ui.graphics.Color,
    val chipBackgroundHover: androidx.compose.ui.graphics.Color,
    val chipBorder: androidx.compose.ui.graphics.Color,
    // 品牌色点缀
    val accentPrimary: androidx.compose.ui.graphics.Color,
    val accentSecondary: androidx.compose.ui.graphics.Color,
    val accentCTA: androidx.compose.ui.graphics.Color,
    // AI特效
    val aiStreamingStart: androidx.compose.ui.graphics.Color,
    val aiStreamingEnd: androidx.compose.ui.graphics.Color,
    val aiSuggestionHighlight: androidx.compose.ui.graphics.Color,
    val aiLoadingIndicator: androidx.compose.ui.graphics.Color,
    val aiPulseEffect: androidx.compose.ui.graphics.Color,
) {
    companion object {
        /**
         * 创建 Grok 主题风格的 Coach 颜色方案
         */
        fun createGrokTheme(isDark: Boolean): CoachColorScheme = CoachColorScheme(
            // 背景层次 - Grok 科技风格，使用tokens
            backgroundPrimary = if (isDark) {
                GrayScale.Gray000 // 🔥 【背景统一】深色主题使用纯黑色
            } else {
                GrayScale.Gray950 // 🔥 【背景统一】浅色主题使用纯白色
            },
            backgroundSecondary = if (isDark) {
                GrayScale.Gray200 // 🔥 修复：使用tokens替代硬编码
            } else {
                GrayScale.Gray850 // 🔥 修复：使用tokens替代硬编码
            },
            backgroundElevated = if (isDark) {
                GrayScale.Gray000 // 🔥 【背景统一】与backgroundPrimary保持一致
            } else {
                GrayScale.Gray950 // 🔥 【背景统一】与backgroundPrimary保持一致
            },
            backgroundInput = if (isDark) {
                GrayScale.Gray200 // 🔥 修复：使用tokens替代硬编码
            } else {
                GrayScale.Gray850 // 🔥 修复：使用tokens替代硬编码
            },
            // 文本层次 - 使用tokens确保一致性
            textPrimary = if (isDark) {
                GrayScale.Gray950 // 🔥 修复：使用tokens替代硬编码
            } else {
                GrayScale.Gray000 // 🔥 修复：使用tokens替代硬编码
            },
            textSecondary = if (isDark) {
                GrayScale.Gray850 // 🔥 修复：使用tokens替代硬编码
            } else {
                GrayScale.Gray400 // 🔥 修复：使用tokens替代硬编码
            },
            textTertiary = if (isDark) {
                GrayScale.Gray700 // 🔥 修复：使用tokens替代硬编码
            } else {
                ColorTokens.Gray600 // 🔥 修复：使用tokens替代硬编码
            },
            textPlaceholder = if (isDark) {
                ColorTokens.Gray500 // 🔥 修复：使用tokens替代硬编码
            } else {
                ColorTokens.Gray600 // 🔥 修复：使用tokens替代硬编码
            },
            // 图标层次 - 与文本保持一致的视觉层次
            iconPrimary = if (isDark) {
                ColorTokens.Gray850 // 🔥 修复：使用tokens替代硬编码
            } else {
                ColorTokens.Gray300 // 🔥 修复：使用tokens替代硬编码
            },
            iconSecondary = if (isDark) {
                ColorTokens.Gray700 // 🔥 修复：使用tokens替代硬编码
            } else {
                ColorTokens.Gray400 // 🔥 修复：使用tokens替代硬编码
            },
            iconTertiary = if (isDark) {
                ColorTokens.Gray500 // 🔥 修复：使用tokens替代硬编码
            } else {
                ColorTokens.Gray600 // 🔥 修复：使用tokens替代硬编码
            },
            // 边框和分割线 - 微妙而精致的分界
            borderPrimary = if (isDark) {
                ColorTokens.Gray300 // 🔥 修复：使用tokens替代硬编码
            } else {
                ColorTokens.Gray850 // 🔥 修复：使用tokens替代硬编码
            },
            borderSecondary = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFF2A2A2A,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFFF0F0F0)
            },
            dividerPrimary = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFF3A3A3A,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFFE5E5E5)
            },
            dividerSecondary = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFF2A2A2A,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFFF0F0F0)
            },
            // 交互元素
            chipBackground = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFF2A2A2A,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFFF0F0F3)
            },
            chipBackgroundHover = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFF3A3A3A,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFFE5E5E8)
            },
            chipBorder = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFF3A3A3A,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFFD0D0D3)
            },
            // 品牌色点缀 - Grok 橙色系
            accentPrimary = androidx.compose.ui.graphics.Color(0xFF3F6CF3),
            accentSecondary = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFF707070,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFF909090)
            },
            accentCTA = androidx.compose.ui.graphics.Color(0xFF3F6CF3),
            // AI特效
            aiStreamingStart = androidx.compose.ui.graphics.Color(0xFF3F6CF3),
            aiStreamingEnd = androidx.compose.ui.graphics.Color(0xFF6B8AFF),
            aiSuggestionHighlight = androidx.compose.ui.graphics.Color(0xFF3F6CF3).copy(alpha = 0.1f),
            aiLoadingIndicator = androidx.compose.ui.graphics.Color(0xFF3F6CF3),
            aiPulseEffect = androidx.compose.ui.graphics.Color(0xFF3F6CF3).copy(alpha = 0.3f),
        )

        /**
         * 创建 ChatGPT 主题风格的 Coach 颜色方案
         */
        fun createChatGPTTheme(isDark: Boolean): CoachColorScheme = CoachColorScheme(
            // 背景层次 - ChatGPT 简约风格，使用tokens
            backgroundPrimary = if (isDark) {
                GrayScale.Gray000 // 🔥 【背景统一】深色主题使用纯黑色
            } else {
                GrayScale.Gray950 // 🔥 【背景统一】浅色主题使用纯白色
            },
            backgroundSecondary = if (isDark) {
                GrayScale.Gray200 // 🔥 修复：使用GrayScale替代ColorTokens
            } else {
                GrayScale.Gray850 // 🔥 修复：使用GrayScale替代ColorTokens
            },
            backgroundElevated = if (isDark) {
                GrayScale.Gray000 // 🔥 【背景统一】与backgroundPrimary保持一致
            } else {
                GrayScale.Gray950 // 🔥 【背景统一】与backgroundPrimary保持一致
            },
            backgroundInput = if (isDark) {
                GrayScale.Gray200 // 🔥 修复：使用GrayScale替代ColorTokens
            } else {
                GrayScale.Gray850 // 🔥 修复：使用GrayScale替代ColorTokens
            },
            // 文本层次
            textPrimary = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFFE2E8F0,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFF2D3748)
            },
            textSecondary = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFFCBD5E0,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFF4A5568)
            },
            textTertiary = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFFA0AEC0,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFF718096)
            },
            textPlaceholder = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFF718096,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFF9CA3AF)
            },
            // 图标层次
            iconPrimary = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFFE2E8F0,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFF374151)
            },
            iconSecondary = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFFCBD5E0,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFF6B7280)
            },
            iconTertiary = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFFA0AEC0,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFF9CA3AF)
            },
            // 边框和分割线
            borderPrimary = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFF4A5568,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFFE5E7EB)
            },
            borderSecondary = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFF2D3748,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFFF3F4F6)
            },
            dividerPrimary = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFF4A5568,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFFE5E7EB)
            },
            dividerSecondary = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFF2D3748,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFFF3F4F6)
            },
            // 交互元素
            chipBackground = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFF374151,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFFF3F4F6)
            },
            chipBackgroundHover = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFF4B5563,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFFE5E7EB)
            },
            chipBorder = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFF6B7280,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFFD1D5DB)
            },
            // 品牌色点缀 - ChatGPT 绿色系
            accentPrimary = androidx.compose.ui.graphics.Color(0xFF00BC80),
            accentSecondary = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFF9CA3AF,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFF6B7280)
            },
            accentCTA = androidx.compose.ui.graphics.Color(0xFF00BC80),
            // AI特效
            aiStreamingStart = androidx.compose.ui.graphics.Color(0xFF00BC80),
            aiStreamingEnd = androidx.compose.ui.graphics.Color(0xFF34D399),
            aiSuggestionHighlight = androidx.compose.ui.graphics.Color(0xFF00BC80).copy(alpha = 0.1f),
            aiLoadingIndicator = androidx.compose.ui.graphics.Color(0xFF00BC80),
            aiPulseEffect = androidx.compose.ui.graphics.Color(0xFF00BC80).copy(alpha = 0.3f),
        )

        /**
         * 创建 DeepSeek 主题风格的 Coach 颜色方案
         */
        fun createDeepSeekTheme(isDark: Boolean): CoachColorScheme = CoachColorScheme(
            // 背景层次 - DeepSeek 深度思考风格，使用tokens
            backgroundPrimary = if (isDark) {
                GrayScale.Gray000 // 🔥 【背景统一】深色主题使用纯黑色
            } else {
                GrayScale.Gray950 // 🔥 【背景统一】浅色主题使用纯白色
            },
            backgroundSecondary = if (isDark) {
                GrayScale.Gray200 // 🔥 修复：使用GrayScale替代ColorTokens
            } else {
                GrayScale.Gray850 // 🔥 修复：使用GrayScale替代ColorTokens
            },
            backgroundElevated = if (isDark) {
                GrayScale.Gray000 // 🔥 【背景统一】与backgroundPrimary保持一致
            } else {
                GrayScale.Gray950 // 🔥 【背景统一】与backgroundPrimary保持一致
            },
            backgroundInput = if (isDark) {
                GrayScale.Gray200 // 🔥 修复：使用GrayScale替代ColorTokens
            } else {
                GrayScale.Gray850 // 🔥 修复：使用GrayScale替代ColorTokens
            },
            // 文本层次
            textPrimary = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFFFFFFFF,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFF000000)
            },
            textSecondary = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFFE0E0E0,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFF5F6368)
            },
            textTertiary = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFFB0B0B0,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFF80868B)
            },
            textPlaceholder = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFF808080,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFF9AA0A6)
            },
            // 图标层次
            iconPrimary = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFFE0E0E0,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFF202124)
            },
            iconSecondary = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFFB0B0B0,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFF5F6368)
            },
            iconTertiary = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFF808080,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFF80868B)
            },
            // 边框和分割线
            borderPrimary = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFF3C4043,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFFD8E2ED)
            },
            borderSecondary = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFF2A2A2A,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFFE8F0FE)
            },
            dividerPrimary = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFF3C4043,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFFD8E2ED)
            },
            dividerSecondary = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFF2A2A2A,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFFE8F0FE)
            },
            // 交互元素
            chipBackground = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFF3C4043,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFFE8F0FE)
            },
            chipBackgroundHover = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFF4A4A4A,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFFD2E3FC)
            },
            chipBorder = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFF5F6368,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFFCEDBE8)
            },
            // 品牌色点缀 - DeepSeek 蓝色系
            accentPrimary = androidx.compose.ui.graphics.Color(0xFF4285F4),
            accentSecondary = if (isDark) {
                androidx.compose.ui.graphics.Color(
                    0xFFB0B0B0,
                )
            } else {
                androidx.compose.ui.graphics.Color(0xFF5F6368)
            },
            accentCTA = androidx.compose.ui.graphics.Color(0xFF4285F4),
            // AI特效
            aiStreamingStart = androidx.compose.ui.graphics.Color(0xFF4285F4),
            aiStreamingEnd = androidx.compose.ui.graphics.Color(0xFF1A73E8),
            aiSuggestionHighlight = androidx.compose.ui.graphics.Color(0xFF4285F4).copy(alpha = 0.1f),
            aiLoadingIndicator = androidx.compose.ui.graphics.Color(0xFF4285F4),
            aiPulseEffect = androidx.compose.ui.graphics.Color(0xFF4285F4).copy(alpha = 0.3f),
        )
    }
}

/**
 * 为MaterialTheme添加coachTheme扩展属性
 * 根据当前主题自动选择对应的Coach颜色配置
 */
val MaterialTheme.coachTheme: CoachColorScheme
    @Composable
    get() {
        val isDark = LocalDarkTheme.current
        val themeConfig = rememberThemeConfig()
        return when (themeConfig.themeStyle) {
            com.example.gymbro.core.theme.ThemeStyle.GROK -> CoachColorScheme.createGrokTheme(isDark)
            com.example.gymbro.core.theme.ThemeStyle.CHATGPT -> CoachColorScheme.createChatGPTTheme(isDark)
            com.example.gymbro.core.theme.ThemeStyle.DEEPSEEK -> CoachColorScheme.createDeepSeekTheme(isDark)
        }
    }

/**
 * GymBro应用主题 v2.0
 * 集成13级精致灰阶系统，提供高级的深浅主题体验
 *
 * @param darkTheme 是否使用深色主题
 * @param dynamicColor 是否启用动态颜色（Android 12+）
 * @param content 主题包装的内容
 */
@Composable
fun gymBroTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    // 默认启用动态取色，但保证深浅主题的高级感
    dynamicColor: Boolean = true,
    content: @Composable () -> Unit,
) {
    // 选择合适的颜色方案
    val colorScheme =
        when {
            // Android 12+ 支持动态颜色
            dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
                val context = LocalContext.current
                if (darkTheme) {
                    // 动态深色主题，但保持我们的高级感
                    dynamicDarkColorScheme(context).copy(
                        primary = PremiumDarkColorScheme.primary,
                        background = PremiumDarkColorScheme.background,
                        surface = PremiumDarkColorScheme.surface,
                    )
                } else {
                    // 动态浅色主题，保持优雅感
                    dynamicLightColorScheme(context).copy(
                        primary = ElegantLightColorScheme.primary,
                        background = ElegantLightColorScheme.background,
                        surface = ElegantLightColorScheme.surface,
                    )
                }
            }
            // 使用我们的高级配色方案
            darkTheme -> PremiumDarkColorScheme
            else -> ElegantLightColorScheme
        }

    // 设置状态栏样式
    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            WindowCompat.setDecorFitsSystemWindows(window, false)

            // 根据主题调整状态栏图标颜色
            val statusBarIconsLight = !darkTheme
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = statusBarIconsLight

            // 设置状态栏背景为透明，让主题颜色透出
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                @Suppress("DEPRECATION")
                window.statusBarColor = android.graphics.Color.TRANSPARENT
                @Suppress("DEPRECATION")
                window.navigationBarColor = android.graphics.Color.TRANSPARENT
            } else {
                @Suppress("DEPRECATION")
                window.statusBarColor = colorScheme.surface.toArgb()
                @Suppress("DEPRECATION")
                window.navigationBarColor = colorScheme.surface.toArgb()
            }
        }
    }

    // 准备应用特定的主题令牌
    val themeTokens = ThemeTokens()
    val spacing = rememberSpacing()
    val mapleMono = rememberMapleMono()

    // 使用CompositionLocalProvider提供所有主题相关值
    CompositionLocalProvider(
        LocalThemeTokens provides themeTokens,
        LocalSpacing provides spacing,
        LocalGymBroFontFamily provides mapleMono,
        LocalDarkTheme provides darkTheme,
    ) {
        MaterialTheme(
            colorScheme = colorScheme,
            typography = rememberGymBroTypography(),
            content = content,
        )
    }
}

/**
 * GymBroTheme with ThemeConfig support
 * 支持动态主题系统的主题包装器
 *
 * @param themeConfig 主题配置，包含主题风格、颜色模式和动态颜色设置
 * @param content 主题包装的内容
 */
@Composable
fun GymBroTheme(
    themeConfig: com.example.gymbro.core.theme.ThemeConfig? = null,
    content: @Composable () -> Unit,
) {
    val actualConfig =
        themeConfig ?: com.example.gymbro.core.theme.ThemeConfig
            .getDefault()

    val darkTheme = actualConfig.shouldUseDarkTheme(isSystemInDarkTheme())
    val dynamicColor = actualConfig.useDynamicColor

    // 根据主题风格选择颜色方案
    val colorScheme =
        when {
            // Android 12+ 支持动态颜色
            dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
                val context = LocalContext.current
                if (darkTheme) {
                    // 动态深色主题，但保持我们的高级感
                    dynamicDarkColorScheme(context).copy(
                        primary = PremiumDarkColorScheme.primary,
                        background = PremiumDarkColorScheme.background,
                        surface = PremiumDarkColorScheme.surface,
                    )
                } else {
                    // 动态浅色主题，保持优雅感
                    dynamicLightColorScheme(context).copy(
                        primary = ElegantLightColorScheme.primary,
                        background = ElegantLightColorScheme.background,
                        surface = ElegantLightColorScheme.surface,
                    )
                }
            }
            // 根据主题风格选择配色方案
            actualConfig.themeStyle == com.example.gymbro.core.theme.ThemeStyle.GROK -> {
                // 科技风格 - 橙色系配色
                if (darkTheme) GrokDarkColorScheme else GrokLightColorScheme
            }
            actualConfig.themeStyle == com.example.gymbro.core.theme.ThemeStyle.CHATGPT -> {
                // 简约风格 - 绿色系配色
                if (darkTheme) ChatGPTDarkColorScheme else ChatGPTLightColorScheme
            }
            actualConfig.themeStyle == com.example.gymbro.core.theme.ThemeStyle.DEEPSEEK -> {
                // DeepSeek风格 - 蓝色系配色
                if (darkTheme) DeepSeekDarkColorScheme else DeepSeekLightColorScheme
            }
            else -> {
                // 默认配色方案 - 使用科技风格
                if (darkTheme) GrokDarkColorScheme else GrokLightColorScheme
            }
        }

    // 准备应用特定的主题令牌
    val themeTokens = ThemeTokens()
    val spacing = rememberSpacing()
    val mapleMono = rememberMapleMono()

    // 使用CompositionLocalProvider提供所有主题相关值
    CompositionLocalProvider(
        LocalThemeTokens provides themeTokens,
        LocalSpacing provides spacing,
        LocalGymBroFontFamily provides mapleMono,
        LocalDarkTheme provides darkTheme,
        LocalProfileColors provides when (actualConfig.themeStyle) {
            com.example.gymbro.core.theme.ThemeStyle.GROK -> ProfileColors.createGrokTheme(darkTheme)
            com.example.gymbro.core.theme.ThemeStyle.CHATGPT -> ProfileColors.createChatGPTTheme(darkTheme)
            com.example.gymbro.core.theme.ThemeStyle.DEEPSEEK -> ProfileColors.createDeepSeekTheme(darkTheme)
        },
    ) {
        MaterialTheme(
            colorScheme = colorScheme,
            typography = rememberGymBroTypography(),
            content = content,
        )
    }
}

/**
 * PascalCase版本的GymBroTheme，提供向后兼容性
 * 这是gymBroTheme的别名，确保符合Composable函数命名规范
 */
@Composable
fun GymBroTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    dynamicColor: Boolean = true,
    content: @Composable () -> Unit,
) = gymBroTheme(
    darkTheme = darkTheme,
    dynamicColor = dynamicColor,
    content = content,
)
