package com.example.gymbro.data.remote.firebase.auth

import com.example.gymbro.core.error.types.ModernResult
import com.google.firebase.auth.AuthCredential
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.auth.PhoneAuthCredential
import kotlinx.coroutines.flow.Flow

/**
 * 认证数据源接口，定义所有与认证相关的操作
 */
interface AuthDataSource {
    // 基本的认证状态检查
    fun isUserLoggedInSynchronously(): Boolean
    fun isAnonymousUserSynchronously(): Boolean

    // 获取用户相关方法
    suspend fun getCurrentUserId(): String?
    suspend fun getCurrentUser(): ModernResult<FirebaseUser?>
    suspend fun isAnonymousUser(): Boolean

    // 登录方法
    suspend fun loginWithEmail(email: String, password: String): ModernResult<String>
    suspend fun loginWithPhone(credential: PhoneAuthCredential): ModernResult<String>
    suspend fun registerWithEmail(email: String, password: String): ModernResult<String>
    suspend fun registerWithPhone(phone: String, code: String): ModernResult<String>
    suspend fun loginWithGoogle(credential: AuthCredential): ModernResult<String>
    suspend fun loginWithWeChat(code: String): ModernResult<String>
    suspend fun loginAnonymously(): ModernResult<String>
    suspend fun logout(): ModernResult<Unit>

    // 账户关联
    suspend fun linkAccountWithCredential(credential: AuthCredential): ModernResult<Unit>
    suspend fun linkPhoneAccount(phoneNumber: String, code: String): ModernResult<Unit>
    suspend fun linkEmailAccount(email: String, password: String): ModernResult<Unit>
    suspend fun updatePhoneNumber(credential: PhoneAuthCredential): ModernResult<Unit>
    suspend fun updateEmail(email: String): ModernResult<Unit>
    suspend fun isAccountLinked(providerId: String): Boolean
    suspend fun getLinkedAccounts(): List<String>

    // 身份认证
    suspend fun reAuthenticate(credential: AuthCredential): ModernResult<Unit>

    // 电话验证 - 移除Activity依赖
    suspend fun verifyPhoneNumber(
        phoneNumber: String,
        context: PhoneVerificationContext,
        callback: PhoneVerificationCallback,
        forceResendingToken: Any? = null,
    ): ModernResult<Unit>

    suspend fun sendVerificationCode(
        phoneNumber: String,
        context: PhoneVerificationContext,
        callback: PhoneVerificationCallback,
    ): ModernResult<Unit>

    suspend fun resendVerificationCode(
        phoneNumber: String,
        context: PhoneVerificationContext,
        callback: PhoneVerificationCallback,
        resendToken: Any,
    ): ModernResult<Unit>

    fun createPhoneCredential(verificationId: String, code: String): PhoneAuthCredential
    suspend fun verifyPhoneCode(verificationId: String, code: String): ModernResult<PhoneAuthCredential>

    // 认证状态监听 - 简化版本
    fun getAuthStateFlow(): Flow<Boolean>
    fun getAuthState(): Flow<Boolean>
    fun observeAuthState(): Flow<Boolean>

    // 匿名用户升级
    suspend fun upgradeAnonymousWithCredential(credential: AuthCredential): ModernResult<Unit>
    suspend fun upgradeAnonymousWithEmail(email: String, password: String): ModernResult<Unit>
    suspend fun upgradeAnonymousWithGoogle(credential: AuthCredential): ModernResult<Unit>
    suspend fun upgradeAnonymousWithPhone(credential: PhoneAuthCredential): ModernResult<Unit>

    // 电子邮件验证
    suspend fun sendEmailVerificationCode(email: String?): ModernResult<Unit>
    suspend fun verifyEmailCode(code: String): ModernResult<Boolean>
    suspend fun sendEmailVerification(): ModernResult<Unit>
    suspend fun verifyBeforeUpdateEmail(email: String): ModernResult<Unit>

    // 密码重置
    suspend fun resetPassword(email: String): ModernResult<Unit>
    suspend fun updatePassword(newPassword: String): ModernResult<Unit>

    // 账户管理
    suspend fun deleteAccount(): ModernResult<Unit>
    suspend fun getIdToken(forceRefresh: Boolean = false): ModernResult<String>
    suspend fun refreshUserToken(): ModernResult<String>

    // 用户设置备份与恢复
    /**
     * 获取用户的备份设置。
     * @return 返回一个包含设置的Map (如果存在)，或者null/错误。
     */
    suspend fun getBackupSettings(): ModernResult<Map<String, Any>?>

    /**
     * 更新用户的备份设置。
     * @param settings 包含要更新的设置的Map。
     * @return 操作成功或失败的结果。
     */
    suspend fun updateBackupSettings(settings: Map<String, Any>): ModernResult<Unit>
}
