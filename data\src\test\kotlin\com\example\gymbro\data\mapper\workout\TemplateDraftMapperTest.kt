package com.example.gymbro.data.mapper.workout

import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.exercise.model.ExerciseSetDraft
import com.example.gymbro.domain.model.workout.session.WorkoutSessionStatus
import com.example.gymbro.domain.workout.model.DraftSource
import com.example.gymbro.domain.workout.model.SetDraft
import com.example.gymbro.domain.workout.model.TemplateDraft
import kotlinx.datetime.Clock
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * TemplateDraftMapper单元测试
 * 验证AI模板到训练会话的转换逻辑
 */
@DisplayName("TemplateDraft映射器测试")
class TemplateDraftMapperTest {
    @Test
    @DisplayName("AI生成的TemplateDraft应该正确转换为WorkoutSession")
    fun `AI generated template should convert to workout session correctly`() {
        // Given - 创建AI生成的模板草稿
        val now = Clock.System.now()
        val templateDraft =
            TemplateDraft(
                id = "template_ai_001",
                name = "AI胸肌训练计划",
                description = "基于用户需求生成的胸肌训练计划",
                exercises =
                listOf(
                    ExerciseSetDraft(
                        exerciseId = "exercise_bench_press",
                        exerciseName = "杠铃卧推",
                        sets =
                        listOf(
                            SetDraft(setNumber = 1, reps = 8, weight = 60.0),
                            SetDraft(setNumber = 2, reps = 8, weight = 60.0),
                            SetDraft(setNumber = 3, reps = 8, weight = 60.0),
                        ),
                        restTimeSeconds = 120,
                        order = 0,
                    ),
                    ExerciseSetDraft(
                        exerciseId = "exercise_push_ups",
                        exerciseName = "俯卧撑",
                        sets =
                        listOf(
                            SetDraft(setNumber = 1, reps = 15),
                            SetDraft(setNumber = 2, reps = 15),
                            SetDraft(setNumber = 3, reps = 15),
                        ),
                        restTimeSeconds = 90,
                        order = 1,
                    ),
                ),
                source = DraftSource.AI_GENERATED,
                createdAt = now,
                updatedAt = now,
                userId = "user_123",
                aiPrompt = "帮我制定一个胸肌训练计划",
                targetMuscleGroups = listOf("胸肌", "三头肌"),
                estimatedDuration = 45,
                tags = listOf("力量训练", "胸肌"),
            )

        // When - 转换为训练会话
        val workoutSession =
            templateDraft.toWorkoutSession(
                userId = "user_123",
                sessionName = "今日胸肌训练",
            )

        // Then - 验证转换结果
        assertEquals("user_123", workoutSession.userId)
        assertEquals("今日胸肌训练", workoutSession.name)
        assertEquals("基于用户需求生成的胸肌训练计划", workoutSession.description)
        assertEquals("template_ai_001", workoutSession.templateId)
        assertEquals("COACH_AI", workoutSession.source)
        assertEquals(WorkoutSessionStatus.NOT_STARTED, workoutSession.status)
        assertFalse(workoutSession.isCompleted)
        assertEquals(2, workoutSession.exercises.size)

        // 验证练习转换
        val firstExercise = workoutSession.exercises.first()
        assertEquals("exercise_bench_press", firstExercise.exerciseId)
        assertEquals(UiText.DynamicString("杠铃卧推"), firstExercise.name)
        assertEquals(3, firstExercise.sets.size)
        assertEquals(120, firstExercise.restTimeSeconds)
        assertEquals(0, firstExercise.order)

        // 验证组数据
        val firstSet = firstExercise.sets.first()
        assertEquals(1, firstSet["setNumber"])
        assertEquals(8, firstSet["targetReps"])
        assertEquals(60.0, firstSet["targetWeight"])
        assertEquals(false, firstSet["isCompleted"])

        // 验证备注信息包含AI来源
        assertNotNull(workoutSession.notes)
        assertTrue(workoutSession.notes!!.contains("🤖 AI生成训练模板"))
        assertTrue(workoutSession.notes!!.contains("帮我制定一个胸肌训练计划"))
    }

    @Test
    @DisplayName("用户创建的TemplateDraft应该正确标记来源")
    fun `user created template should have correct source marking`() {
        // Given
        val now = Clock.System.now()
        val templateDraft =
            TemplateDraft(
                id = "template_user_001",
                name = "我的训练计划",
                description = "自定义训练计划",
                exercises =
                listOf(
                    ExerciseSetDraft(
                        exerciseId = "exercise_squat",
                        exerciseName = "深蹲",
                        sets = listOf(SetDraft(setNumber = 1, reps = 10)),
                        restTimeSeconds = 60,
                        order = 0,
                    ),
                ),
                source = DraftSource.USER_CREATED,
                createdAt = now,
                updatedAt = now,
                userId = "user_123",
            )

        // When
        val workoutSession = templateDraft.toWorkoutSession(userId = "user_123")

        // Then
        assertEquals("USER_MANUAL", workoutSession.source)
        assertNotNull(workoutSession.notes)
        assertTrue(workoutSession.notes!!.contains("👤 用户创建模板"))
    }

    @Test
    @DisplayName("空练习列表的模板应该无法转换")
    fun `template with empty exercises should not be convertible`() {
        // Given
        val now = Clock.System.now()
        val emptyTemplate =
            TemplateDraft(
                id = "template_empty_001",
                name = "空模板",
                exercises = emptyList(),
                source = DraftSource.USER_CREATED,
                createdAt = now,
                updatedAt = now,
                userId = "user_123",
            )

        // When & Then
        assertFalse(emptyTemplate.canConvertToSession())
    }

    @Test
    @DisplayName("空名称的模板应该无法转换")
    fun `template with blank name should not be convertible`() {
        // Given
        val now = Clock.System.now()
        val blankNameTemplate =
            TemplateDraft(
                id = "template_blank_001",
                name = "",
                exercises =
                listOf(
                    ExerciseSetDraft(
                        exerciseId = "exercise_test",
                        exerciseName = "测试动作",
                        sets = listOf(SetDraft(setNumber = 1, reps = 10)),
                        restTimeSeconds = 60,
                        order = 0,
                    ),
                ),
                source = DraftSource.USER_CREATED,
                createdAt = now,
                updatedAt = now,
                userId = "user_123",
            )

        // When & Then
        assertFalse(blankNameTemplate.canConvertToSession())
    }

    @Test
    @DisplayName("包含无效exerciseId的模板应该无法转换")
    fun `template with invalid exercise id should not be convertible`() {
        // Given
        val now = Clock.System.now()
        val invalidExerciseTemplate =
            TemplateDraft(
                id = "template_invalid_001",
                name = "无效模板",
                exercises =
                listOf(
                    ExerciseSetDraft(
                        exerciseId = "", // 无效的exerciseId
                        exerciseName = "测试动作",
                        sets = listOf(SetDraft(setNumber = 1, reps = 10)),
                        restTimeSeconds = 60,
                        order = 0,
                    ),
                ),
                source = DraftSource.USER_CREATED,
                createdAt = now,
                updatedAt = now,
                userId = "user_123",
            )

        // When & Then
        assertFalse(invalidExerciseTemplate.canConvertToSession())
    }

    @Test
    @DisplayName("创建空训练会话应该有正确的默认值")
    fun `empty workout session should have correct defaults`() {
        // When
        val emptySession =
            createEmptyWorkoutSession(
                userId = "user_123",
                sessionName = "今日训练",
            )

        // Then
        assertEquals("user_123", emptySession.userId)
        assertEquals("今日训练", emptySession.name)
        assertEquals("空训练会话", emptySession.description)
        assertEquals(null, emptySession.templateId)
        assertEquals("USER_MANUAL", emptySession.source)
        assertEquals(WorkoutSessionStatus.NOT_STARTED, emptySession.status)
        assertTrue(emptySession.exercises.isEmpty())
        assertEquals("手动创建的空训练会话", emptySession.notes)
    }

    @Test
    @DisplayName("复杂AI模板应该正确处理目标肌肉群和预估时长")
    fun `complex AI template should handle target muscles and duration correctly`() {
        // Given
        val now = Clock.System.now()
        val complexTemplate =
            TemplateDraft(
                id = "template_complex_001",
                name = "全身训练计划",
                description = "AI生成的全身训练计划",
                exercises =
                listOf(
                    ExerciseSetDraft(
                        exerciseId = "exercise_deadlift",
                        exerciseName = "硬拉",
                        sets = listOf(SetDraft(setNumber = 1, reps = 5, weight = 100.0)),
                        restTimeSeconds = 180,
                        order = 0,
                    ),
                ),
                source = DraftSource.AI_GENERATED,
                createdAt = now,
                updatedAt = now,
                userId = "user_123",
                aiPrompt = "制定一个全身训练计划",
                targetMuscleGroups = listOf("背部", "腿部", "臀部"),
                estimatedDuration = 60,
                difficulty = 4,
                tags = listOf("全身", "力量"),
            )

        // When
        val workoutSession = complexTemplate.toWorkoutSession(userId = "user_123")

        // Then
        assertNotNull(workoutSession.notes)
        assertTrue(workoutSession.notes!!.contains("目标肌肉群：背部, 腿部, 臀部"))
        assertTrue(workoutSession.notes!!.contains("预估时长：60分钟"))
        assertEquals(listOf("全身", "力量"), workoutSession.tags)
    }
}
