package com.example.gymbro.data.remote.firebase.datasource

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.datasource.UserSettingsDataSource
import com.example.gymbro.domain.profile.model.user.settings.UserSettings
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Firebase用户设置数据源
 * 专注于用户设置和偏好数据的Firestore操作
 */
@Singleton
class FirebaseUserSettingsDataSource
@Inject
constructor(
    private val firestoreDataSource: FirestoreDataSource,
) : UserSettingsDataSource {
    companion object {
        private const val USER_SETTINGS_COLLECTION = "user_settings"
        private const val USER_DATA_COLLECTION = "userData"
        private const val CURRENT_DATA_DOC = "current"
    }

    override suspend fun getUserSettings(userId: String): ModernResult<UserSettings?> =
        withContext(
            Dispatchers.IO,
        ) {
            try {
                when (val result = firestoreDataSource.getDocument("$USER_SETTINGS_COLLECTION/$userId")) {
                    is ModernResult.Success -> {
                        val snapshot = result.data
                        val settings =
                            if (snapshot.exists()) {
                                val data = snapshot.data
                                UserSettings(
                                    userId = userId,
                                    language = data?.get("language") as? String ?: "zh",
                                    darkModeEnabled = data?.get("darkModeEnabled") as? Boolean ?: false,
                                    notificationsEnabled = data?.get("notificationsEnabled") as? Boolean ?: true,
                                    autoSync = data?.get("autoSync") as? Boolean ?: true,
                                    backupEnabled = data?.get("backupEnabled") as? Boolean ?: false,
                                    measurementUnit = data?.get("measurementUnit") as? String ?: "metric",
                                )
                            } else {
                                null
                            }
                        ModernResult.Success(settings)
                    }
                    is ModernResult.Error -> throw Exception("获取用户设置失败", result.error.cause)
                    is ModernResult.Loading -> ModernResult.Success(null)
                }
            } catch (e: Exception) {
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "FirebaseUserSettingsDataSource.getUserSettings",
                        message = UiText.DynamicString("获取用户设置失败"),
                        entityType = "UserSettings",
                        cause = e,
                        metadataMap = mapOf("userId" to userId),
                    ),
                )
            }
        }

    override suspend fun saveUserSettings(
        userId: String,
        settings: UserSettings,
    ): ModernResult<Unit> =
        withContext(
            Dispatchers.IO,
        ) {
            try {
                val settingsData =
                    mapOf(
                        "userId" to userId,
                        "language" to settings.language,
                        "darkModeEnabled" to settings.darkModeEnabled,
                        "notificationsEnabled" to settings.notificationsEnabled,
                        "autoSync" to settings.autoSync,
                        "backupEnabled" to settings.backupEnabled,
                        "measurementUnit" to settings.measurementUnit,
                        "updatedAt" to System.currentTimeMillis(),
                    )

                when (
                    val result =
                        firestoreDataSource.setDocument(
                            "$USER_SETTINGS_COLLECTION/$userId",
                            settingsData,
                        )
                ) {
                    is ModernResult.Success -> ModernResult.Success(Unit)
                    is ModernResult.Error -> throw Exception("保存用户设置失败", result.error.cause)
                    is ModernResult.Loading -> throw Exception("保存用户设置超时")
                }
            } catch (e: Exception) {
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "FirebaseUserSettingsDataSource.saveUserSettings",
                        message = UiText.DynamicString("保存用户设置失败"),
                        entityType = "UserSettings",
                        cause = e,
                        metadataMap = mapOf("userId" to userId),
                    ),
                )
            }
        }

    override suspend fun updateUserSettingField(
        userId: String,
        fieldPath: String,
        value: Any,
    ): ModernResult<Unit> =
        withContext(
            Dispatchers.IO,
        ) {
            try {
                val updates =
                    mapOf(
                        fieldPath to value,
                        "updatedAt" to System.currentTimeMillis(),
                    )

                when (
                    val result =
                        firestoreDataSource.updateDocument(
                            "$USER_SETTINGS_COLLECTION/$userId",
                            updates,
                        )
                ) {
                    is ModernResult.Success -> ModernResult.Success(Unit)
                    is ModernResult.Error -> throw Exception("更新用户设置字段失败", result.error.cause)
                    is ModernResult.Loading -> throw Exception("更新用户设置超时")
                }
            } catch (e: Exception) {
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "FirebaseUserSettingsDataSource.updateUserSettingField",
                        message = UiText.DynamicString("更新用户设置失败"),
                        entityType = "UserSettings",
                        cause = e,
                        metadataMap = mapOf("userId" to userId, "fieldPath" to fieldPath),
                    ),
                )
            }
        }

    override suspend fun saveUserData(
        userId: String,
        userPreferences: UserSettings,
    ): ModernResult<Unit> =
        withContext(
            Dispatchers.IO,
        ) {
            try {
                val userDataMap =
                    mapOf(
                        "preferences" to userPreferences,
                        "updatedAt" to System.currentTimeMillis(),
                    )

                val path = "$USER_DATA_COLLECTION/$userId/$CURRENT_DATA_DOC"
                when (val result = firestoreDataSource.setDocument(path, userDataMap)) {
                    is ModernResult.Success -> ModernResult.Success(Unit)
                    is ModernResult.Error -> throw Exception("保存用户偏好数据失败", result.error.cause)
                    is ModernResult.Loading -> throw Exception("保存用户偏好数据超时")
                }
            } catch (e: Exception) {
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "FirebaseUserSettingsDataSource.saveUserData",
                        message = UiText.DynamicString("保存用户偏好数据失败"),
                        entityType = "UserPreferencesData",
                        cause = e,
                        metadataMap = mapOf("userId" to userId),
                    ),
                )
            }
        }

    override suspend fun getUserData(userId: String): ModernResult<UserSettings?> =
        withContext(
            Dispatchers.IO,
        ) {
            try {
                val path = "$USER_DATA_COLLECTION/$userId/$CURRENT_DATA_DOC"
                when (val result = firestoreDataSource.getDocument(path)) {
                    is ModernResult.Success -> {
                        val snapshot = result.data
                        val preferences =
                            if (snapshot.exists()) {
                                snapshot.data?.let { data ->
                                    UserSettings(
                                        userId = userId,
                                        language = data["language"] as? String ?: "zh",
                                        darkModeEnabled = data["darkModeEnabled"] as? Boolean ?: false,
                                        notificationsEnabled = data["notificationsEnabled"] as? Boolean ?: true,
                                        autoSync = data["autoSync"] as? Boolean ?: true,
                                        backupEnabled = data["backupEnabled"] as? Boolean ?: false,
                                        measurementUnit = data["measurementUnit"] as? String ?: "metric",
                                    )
                                }
                            } else {
                                null
                            }
                        ModernResult.Success(preferences)
                    }
                    is ModernResult.Error -> throw Exception("获取用户偏好数据失败", result.error.cause)
                    is ModernResult.Loading -> ModernResult.Success(null)
                }
            } catch (e: Exception) {
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "FirebaseUserSettingsDataSource.getUserData",
                        message = UiText.DynamicString("获取用户偏好数据失败"),
                        entityType = "UserPreferencesData",
                        cause = e,
                        metadataMap = mapOf("userId" to userId),
                    ),
                )
            }
        }
}
