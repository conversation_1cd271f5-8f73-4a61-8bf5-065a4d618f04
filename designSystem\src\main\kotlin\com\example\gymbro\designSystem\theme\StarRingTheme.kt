package com.example.gymbro.designSystem.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.remember
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.graphics.Color
import com.example.gymbro.designSystem.theme.tokens.BrandColors
import com.example.gymbro.designSystem.theme.tokens.GrayScale

/**
 * 星环主题配置数据类
 * 支持主题化配置和动态色彩适配，完美集成GymBro的13级灰阶系统
 */
data class StarRingSpec(
    val ringColor: Color,
    val starColor: Color,
    val accentColor: Color,
    val backgroundStarColor: Color,
    val coreGlowColor: Color,
    val rayColor: Color,
    val rotationPeriodMs: Int,
    val starDensity: Int,
    val ringRadius: Float = 150f,
    val ringStrokeWidth: Float = 8f,
    val enableThemeTransition: Boolean = true,
)

/**
 * 默认星环配置
 * 提供基础配置作为fallback
 */
val LocalStarRingSpec =
    staticCompositionLocalOf {
        StarRingSpec(
            ringColor = GrayScale.Gray400,
            starColor = GrayScale.Gray800,
            accentColor = BrandColors.PlatinumSilver,
            backgroundStarColor = GrayScale.Gray600,
            coreGlowColor = BrandColors.PlatinumSilver,
            rayColor = GrayScale.Gray700,
            rotationPeriodMs = 60000,
            starDensity = 120,
        )
    }

/**
 * 星环主题提供器
 * 智能检测当前主题状态，提供精准的深色/浅色主题适配
 * 完美集成GymBro的设计语言和颜色系统
 */
@Composable
fun starRingTheme(
    spec: StarRingSpec? = null,
    enableAutoThemeDetection: Boolean = true,
    content: @Composable () -> Unit,
) {
    val isDarkTheme = isSystemInDarkTheme()

    val themeSpec =
        remember(spec, isDarkTheme, enableAutoThemeDetection) {
            when {
                spec != null -> spec
                enableAutoThemeDetection -> createThemeAdaptiveStarRingSpec(isDarkTheme)
                else -> createDefaultStarRingSpec(isDarkTheme)
            }
        }

    CompositionLocalProvider(LocalStarRingSpec provides themeSpec) {
        content()
    }
}

/**
 * 创建主题自适应星环配置
 * 根据当前主题状态提供最佳的颜色搭配
 * 使用GymBro的13级灰阶系统确保视觉一致性
 *
 * @param isDarkTheme 当前是否为深色主题
 * @return 适配当前主题的StarRingSpec配置
 */
private fun createThemeAdaptiveStarRingSpec(isDarkTheme: Boolean): StarRingSpec =
    if (isDarkTheme) {
        // 深色主题配置 - 营造深邃太空感
        StarRingSpec(
            ringColor = GrayScale.Gray400.copy(alpha = 0.7f), // 石墨色环，微透明
            starColor = GrayScale.Gray800.copy(alpha = 0.9f), // 银灰色星星，高亮度
            accentColor = BrandColors.PlatinumSilver, // 铂金银强调色
            backgroundStarColor = GrayScale.Gray600.copy(alpha = 0.4f), // 暖灰背景星，低透明度
            coreGlowColor = BrandColors.PlatinumSilver.copy(alpha = 0.8f), // 核心光晕
            rayColor = GrayScale.Gray700.copy(alpha = 0.6f), // 浅石墨射线
            rotationPeriodMs = 60000,
            starDensity = 120,
            ringRadius = 150f,
            ringStrokeWidth = 8f,
            enableThemeTransition = true,
        )
    } else {
        // 浅色主题配置 - 营造优雅科技感
        StarRingSpec(
            ringColor = GrayScale.Gray600.copy(alpha = 0.8f), // 暖灰色环，适中透明度
            starColor = GrayScale.Gray400.copy(alpha = 0.85f), // 石墨色星星，清晰可见
            accentColor = BrandColors.DeepSilver, // 深银色强调色
            backgroundStarColor = GrayScale.Gray700.copy(alpha = 0.3f), // 浅石墨背景星
            coreGlowColor = BrandColors.DeepSilver.copy(alpha = 0.7f), // 核心光晕
            rayColor = GrayScale.Gray500.copy(alpha = 0.5f), // 中性灰射线
            rotationPeriodMs = 60000,
            starDensity = 120,
            ringRadius = 150f,
            ringStrokeWidth = 8f,
            enableThemeTransition = true,
        )
    }

/**
 * 根据Material主题创建星环配置（向后兼容）
 * 保持对现有代码的兼容性，同时提供基础的主题适配
 */
private fun createDefaultStarRingSpec(
    isDarkTheme: Boolean,
): StarRingSpec = createThemeAdaptiveStarRingSpec(isDarkTheme)

/**
 * 获取当前星环主题配置
 * 提供便捷的访问方式
 */
@Composable
fun currentStarRingSpec(): StarRingSpec = LocalStarRingSpec.current

/**
 * 预设主题配置集合
 * 提供常用的主题变体，方便快速应用
 */
object StarRingThemePresets {
    /**
     * 极简深色主题 - 更低调的视觉效果
     */
    val MinimalDark =
        StarRingSpec(
            ringColor = GrayScale.Gray300.copy(alpha = 0.5f),
            starColor = GrayScale.Gray700.copy(alpha = 0.7f),
            accentColor = GrayScale.Gray600,
            backgroundStarColor = GrayScale.Gray500.copy(alpha = 0.2f),
            coreGlowColor = GrayScale.Gray600.copy(alpha = 0.6f),
            rayColor = GrayScale.Gray600.copy(alpha = 0.4f),
            rotationPeriodMs = 80000,
            starDensity = 80,
            enableThemeTransition = true,
        )

    /**
     * 极简浅色主题 - 清新简洁的视觉效果
     */
    val MinimalLight =
        StarRingSpec(
            ringColor = GrayScale.Gray700.copy(alpha = 0.6f),
            starColor = GrayScale.Gray500.copy(alpha = 0.8f),
            accentColor = GrayScale.Gray400,
            backgroundStarColor = GrayScale.Gray600.copy(alpha = 0.25f),
            coreGlowColor = GrayScale.Gray500.copy(alpha = 0.5f),
            rayColor = GrayScale.Gray600.copy(alpha = 0.3f),
            rotationPeriodMs = 80000,
            starDensity = 80,
            enableThemeTransition = true,
        )

    /**
     * 高对比度深色主题 - 适合高对比度显示需求
     */
    val HighContrastDark =
        StarRingSpec(
            ringColor = GrayScale.Gray500.copy(alpha = 0.9f),
            starColor = GrayScale.Gray900.copy(alpha = 1f),
            accentColor = GrayScale.Gray950,
            backgroundStarColor = GrayScale.Gray800.copy(alpha = 0.6f),
            coreGlowColor = GrayScale.Gray950,
            rayColor = GrayScale.Gray800.copy(alpha = 0.8f),
            rotationPeriodMs = 45000,
            starDensity = 140,
            enableThemeTransition = true,
        )

    /**
     * 高对比度浅色主题 - 适合高对比度显示需求
     */
    val HighContrastLight =
        StarRingSpec(
            ringColor = GrayScale.Gray400.copy(alpha = 0.9f),
            starColor = GrayScale.Gray200.copy(alpha = 1f),
            accentColor = GrayScale.Gray100,
            backgroundStarColor = GrayScale.Gray300.copy(alpha = 0.5f),
            coreGlowColor = GrayScale.Gray100,
            rayColor = GrayScale.Gray300.copy(alpha = 0.7f),
            rotationPeriodMs = 45000,
            starDensity = 140,
            enableThemeTransition = true,
        )
}
