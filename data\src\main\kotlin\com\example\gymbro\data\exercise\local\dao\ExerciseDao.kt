package com.example.gymbro.data.exercise.local.dao

import androidx.room.*
import com.example.gymbro.data.exercise.local.entity.ExerciseEntity
import com.example.gymbro.shared.models.exercise.Equipment
import com.example.gymbro.shared.models.exercise.MuscleGroup

/**
 * Exercise数据访问对象
 *
 * 基于plan.md设计：
 * - 支持基本CRUD操作
 * - 官方库版本管理
 * - 批量操作优化
 * - 精确匹配查询
 */
@Dao
interface ExerciseDao {
    /**
     * 根据ID获取单个动作
     */
    @Query("SELECT * FROM exercise WHERE id = :id LIMIT 1")
    suspend fun getById(id: String): ExerciseEntity?

    /**
     * 根据精确名称获取动作
     * 用于Coach模块的精确匹配
     */
    @Query("SELECT * FROM exercise WHERE LOWER(name) = LOWER(:name) LIMIT 1")
    suspend fun getByExactName(name: String): ExerciseEntity?

    /**
     * 获取所有动作
     * 按名称排序
     */
    @Query("SELECT * FROM exercise ORDER BY name ASC")
    suspend fun getAll(): List<ExerciseEntity>

    /**
     * 根据肌群筛选动作
     */
    @Query("SELECT * FROM exercise WHERE muscleGroup = :muscleGroup ORDER BY name ASC")
    suspend fun getByMuscleGroup(muscleGroup: MuscleGroup): List<ExerciseEntity>

    /**
     * 根据器械筛选动作
     */
    @Query("SELECT * FROM exercise WHERE equipment LIKE '%' || :equipment || '%' ORDER BY name ASC")
    suspend fun getByEquipment(equipment: Equipment): List<ExerciseEntity>

    /**
     * 获取用户自定义动作
     */
    @Query("SELECT * FROM exercise WHERE isCustom = 1 AND userId = :userId ORDER BY name ASC")
    suspend fun getCustomExercises(userId: String): List<ExerciseEntity>

    /**
     * 获取官方动作
     */
    @Query("SELECT * FROM exercise WHERE isCustom = 0 ORDER BY name ASC")
    suspend fun getOfficialExercises(): List<ExerciseEntity>

    /**
     * 获取收藏的动作
     */
    @Query("SELECT * FROM exercise WHERE isFavorite = 1 ORDER BY name ASC")
    suspend fun getFavoriteExercises(): List<ExerciseEntity>

    /**
     * 插入单个动作
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(exercise: ExerciseEntity)

    /**
     * 更新单个动作
     */
    @Update
    suspend fun update(exercise: ExerciseEntity)

    /**
     * 根据ID删除动作
     */
    @Query("DELETE FROM exercise WHERE id = :id")
    suspend fun deleteById(id: String)

    /**
     * 插入或更新单个动作
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun upsert(exercise: ExerciseEntity)

    /**
     * 批量插入或更新动作
     * 用于官方库同步
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun upsertBatch(exercises: List<ExerciseEntity>)

    /**
     * 删除动作
     */
    @Query("DELETE FROM exercise WHERE id = :id")
    suspend fun delete(id: String)

    /**
     * 删除用户的所有自定义动作
     */
    @Query("DELETE FROM exercise WHERE isCustom = 1 AND userId = :userId")
    suspend fun deleteUserCustomExercises(userId: String)

    /**
     * 更新动作收藏状态
     */
    @Query("UPDATE exercise SET isFavorite = :isFavorite WHERE id = :id")
    suspend fun updateFavoriteStatus(
        id: String,
        isFavorite: Boolean,
    )

    /**
     * 获取动作总数
     */
    @Query("SELECT COUNT(*) FROM exercise")
    suspend fun getCount(): Int

    /**
     * 获取官方动作总数
     */
    @Query("SELECT COUNT(*) FROM exercise WHERE isCustom = 0")
    suspend fun getOfficialCount(): Int

    /**
     * 获取用户自定义动作总数
     */
    @Query("SELECT COUNT(*) FROM exercise WHERE isCustom = 1 AND userId = :userId")
    suspend fun getCustomCount(userId: String): Int

    // ========== 版本管理相关 ==========

    /**
     * 获取官方库版本号
     * 从metadata表或SharedPreferences获取
     */
    suspend fun getLibraryVersion(): Int {
        // TODO: 实现版本获取逻辑
        // 可以从单独的metadata表或SharedPreferences获取
        return 0
    }

    /**
     * 更新官方库版本号
     */
    suspend fun updateLibraryVersion(version: Int) {
        // TODO: 实现版本更新逻辑
        // 可以更新到metadata表或SharedPreferences
    }

    // ========== 事务操作 ==========

    /**
     * 在事务中执行操作
     * 用于官方库同步的原子性操作
     */
    @Transaction
    suspend fun runInTransaction(block: suspend () -> Unit) {
        block()
    }

    // ========== 向量搜索支持 ==========

    /**
     * 获取所有动作的向量数据
     * 用于内存向量搜索
     */
    @Query("SELECT id, name, embedding FROM exercise WHERE embedding IS NOT NULL")
    suspend fun getAllEmbeddings(): List<ExerciseEmbeddingData>

    /**
     * 更新动作的向量数据
     */
    @Query("UPDATE exercise SET embedding = :embedding WHERE id = :id")
    suspend fun updateEmbedding(
        id: String,
        embedding: FloatArray?,
    )

    // ========== 统计查询 ==========

    /**
     * 获取各肌群的动作数量统计
     */
    @Query(
        """
        SELECT muscleGroup, COUNT(*) as count
        FROM exercise
        GROUP BY muscleGroup
        ORDER BY count DESC
    """,
    )
    suspend fun getMuscleGroupStats(): List<MuscleGroupStat>

    /**
     * 获取各器械的动作数量统计
     */
    @Query(
        """
        SELECT equipment, COUNT(*) as count
        FROM exercise
        GROUP BY equipment
        ORDER BY count DESC
    """,
    )
    suspend fun getEquipmentStats(): List<EquipmentStat>

    // ========== 数据清理 ==========

    /**
     * 清空所有官方动作
     * 用于重新同步
     */
    @Query("DELETE FROM exercise WHERE isCustom = 0")
    suspend fun clearOfficialExercises()

    /**
     * 清空所有数据
     * 用于重置
     */
    @Query("DELETE FROM exercise")
    suspend fun clearAll()
}

/**
 * 向量搜索数据类
 */
data class ExerciseEmbeddingData(
    val id: String,
    val name: String,
    val embedding: FloatArray?,
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as ExerciseEmbeddingData

        if (id != other.id) return false
        if (name != other.name) return false
        if (embedding != null) {
            if (other.embedding == null) return false
            if (!embedding.contentEquals(other.embedding)) return false
        } else if (other.embedding != null) {
            return false
        }

        return true
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + name.hashCode()
        result = 31 * result + (embedding?.contentHashCode() ?: 0)
        return result
    }
}

/**
 * 肌群统计数据类
 */
data class MuscleGroupStat(
    val muscleGroup: MuscleGroup,
    val count: Int,
)

/**
 * 器械统计数据类
 */
data class EquipmentStat(
    val equipment: Equipment,
    val count: Int,
)
