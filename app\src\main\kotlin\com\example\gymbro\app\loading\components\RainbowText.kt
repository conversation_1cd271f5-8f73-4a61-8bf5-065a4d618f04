package com.example.gymbro.app.loading.components

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.TileMode
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.sp
import com.example.gymbro.R
import com.example.gymbro.app.loading.StartupConstants

/**
 * 安全加载字体，失败时降级到默认字体
 */
@Composable
private fun rememberSafeFontFamily(fontFamily: FontFamily?): FontFamily {
    return remember {
        try {
            fontFamily ?: FontFamily(
                androidx.compose.ui.text.font.Font(R.font.font_maple_mono_bold, FontWeight.Bold),
            )
        } catch (e: Exception) {
            FontFamily.Default
        }
    }
}

/**
 * 根据主题获取彩虹色彩
 */
@Composable
private fun rememberRainbowColors(): List<Color> {
    val isDarkTheme = MaterialTheme.colorScheme.surface == MaterialTheme.colorScheme.surfaceVariant
    return remember(isDarkTheme) {
        if (isDarkTheme) {
            listOf(
                Color(0xFFFF6B6B),
                Color(0xFFFFB347),
                Color(0xFFFFEB3B),
                Color(0xFF66BB6A),
                Color(0xFF42A5F5),
                Color(0xFF9575CD),
                Color(0xFFEC407A),
                Color(0xFFC0C0C0),
            )
        } else {
            listOf(
                Color(0xFFE53935),
                Color(0xFFFF9800),
                Color(0xFFFFC107),
                Color(0xFF4CAF50),
                Color(0xFF2196F3),
                Color(0xFF673AB7),
                Color(0xFFE91E63),
                Color(0xFF808080),
            )
        }
    }
}

/**
 * 创建彩虹渐变动画效果
 */
@Composable
private fun rememberRainbowBrush(
    rainbowColors: List<Color>,
    durationMillis: Int,
): Brush {
    val infiniteTransition = rememberInfiniteTransition(label = "RainbowTextTransition")

    val gradientShift by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = durationMillis, easing = LinearEasing),
            repeatMode = RepeatMode.Restart,
        ),
        label = "RainbowGradientShift",
    )

    return remember(gradientShift, rainbowColors) {
        val gradientWidth = StartupConstants.Animation.GRADIENT_WIDTH
        Brush.linearGradient(
            colors = rainbowColors,
            start = Offset(-gradientWidth * gradientShift, 0f),
            end = Offset(gradientWidth * (1f - gradientShift), 0f),
            tileMode = TileMode.Repeated,
        )
    }
}

/**
 * 可复用的彩虹文字组件
 * 支持自定义字符、字体大小、动画时长等参数
 *
 * @param text 要显示的文字
 * @param modifier Modifier
 * @param fontSize 字体大小
 * @param fontFamily 字体族，默认使用Maple字体，失败时降级到系统默认字体
 * @param fontWeight 字体粗细
 * @param durationMillis 动画时长
 */
@Composable
fun RainbowText(
    text: String,
    modifier: Modifier = Modifier,
    fontSize: TextUnit = StartupConstants.Font.LOGO_FONT_SIZE_SP.sp,
    fontFamily: FontFamily? = null,
    fontWeight: FontWeight = FontWeight.Bold,
    durationMillis: Int = StartupConstants.Animation.RAINBOW_GRADIENT_DURATION_MS,
) {
    val safeFontFamily = rememberSafeFontFamily(fontFamily)
    val rainbowColors = rememberRainbowColors()
    val rainbowBrush = rememberRainbowBrush(rainbowColors, durationMillis)

    Text(
        text = text,
        modifier = modifier,
        fontFamily = safeFontFamily,
        fontWeight = fontWeight,
        fontSize = fontSize,
        style = TextStyle(brush = rainbowBrush),
    )
}

/**
 * 专门用于Logo的彩虹文字组件
 * 使用预设的样式和大小
 */
@Composable
fun RainbowLogo(
    modifier: Modifier = Modifier,
    text: String = "G",
) {
    RainbowText(
        text = text,
        modifier = modifier,
        fontSize = StartupConstants.Font.LOGO_FONT_SIZE_SP.sp,
        fontWeight = FontWeight.Bold,
        durationMillis = StartupConstants.Animation.RAINBOW_GRADIENT_DURATION_MS,
    )
}
