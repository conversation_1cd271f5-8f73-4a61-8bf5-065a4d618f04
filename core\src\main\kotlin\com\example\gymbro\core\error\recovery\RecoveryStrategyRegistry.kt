package com.example.gymbro.core.error.recovery

import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.reflect.KClass

/**
 * 全局恢复策略注册表
 *
 * 提供中央注册机制，用于动态查找和应用适合特定错误类型的恢复策略。
 * 支持基于错误类型、错误类别和自定义条件的策略匹配。
 */
@Singleton
class RecoveryStrategyRegistry @Inject constructor() {

    // 保护并发访问的互斥锁
    private val registryMutex = Mutex()

    // 按错误类型存储恢复策略工厂
    private val strategyFactoriesByErrorType =
        mutableMapOf<KClass<out ModernDataError>, MutableList<StrategyFactory<*>>>()

    // 按全局错误类型存储恢复策略工厂
    private val strategyFactoriesByGlobalErrorType =
        mutableMapOf<GlobalErrorType, MutableList<StrategyFactory<*>>>()

    // 通用恢复策略工厂（适用于所有错误）
    private val genericStrategyFactories = mutableListOf<StrategyFactory<*>>()

    /**
     * 注册特定错误类型的恢复策略工厂
     *
     * @param errorClass 错误类型的KClass
     * @param factory 创建恢复策略的工厂函数
     * @param condition 可选的额外匹配条件
     */
    suspend fun <T> registerForErrorType(
        errorClass: KClass<out ModernDataError>,
        factory: (ModernDataError) -> RecoveryStrategy<T>,
        condition: (ModernDataError) -> Boolean = { true },
    ) {
        registryMutex.withLock {
            val factories = strategyFactoriesByErrorType.getOrPut(errorClass) { mutableListOf() }

            @Suppress("UNCHECKED_CAST")
            factories.add(
                StrategyFactory(
                    condition = { error -> errorClass.isInstance(error) && condition(error) },
                    factory = { error -> factory(error) },
                ) as StrategyFactory<*>,
            )

            Timber.d("已为错误类型 ${errorClass.simpleName} 注册恢复策略工厂")
        }
    }

    /**
     * 注册全局错误类型的恢复策略工厂
     *
     * @param globalErrorType 全局错误类型
     * @param factory 创建恢复策略的工厂函数
     * @param condition 可选的额外匹配条件
     */
    suspend fun <T> registerForGlobalErrorType(
        globalErrorType: GlobalErrorType,
        factory: (ModernDataError) -> RecoveryStrategy<T>,
        condition: (ModernDataError) -> Boolean = { true },
    ) {
        registryMutex.withLock {
            val factories = strategyFactoriesByGlobalErrorType.getOrPut(globalErrorType) { mutableListOf() }

            factories.add(
                StrategyFactory(
                    condition = { error -> error.errorType == globalErrorType && condition(error) },
                    factory = factory,
                ) as StrategyFactory<*>,
            )

            Timber.d("已为全局错误类型 $globalErrorType 注册恢复策略工厂")
        }
    }

    /**
     * 注册通用恢复策略工厂
     *
     * @param factory 创建恢复策略的工厂函数
     * @param condition 匹配条件
     */
    suspend fun <T> registerGeneric(
        factory: (ModernDataError) -> RecoveryStrategy<T>,
        condition: (ModernDataError) -> Boolean,
    ) {
        registryMutex.withLock {
            genericStrategyFactories.add(StrategyFactory(condition, factory) as StrategyFactory<*>)
            Timber.d("已注册通用恢复策略工厂")
        }
    }

    /**
     * 为指定错误查找合适的恢复策略
     *
     * @param error 需要恢复的错误
     * @return 合适的恢复策略，如果没有找到则返回null
     */
    @Suppress("UNCHECKED_CAST")
    suspend fun <T> findStrategy(error: ModernDataError): RecoveryStrategy<T>? {
        registryMutex.withLock {
            Timber.d("查找错误恢复策略: ${error.javaClass.simpleName}, 类型: ${error.errorType}")

            // 1. 优先查找特定错误类型的策略
            val errorTypeStrategies = strategyFactoriesByErrorType[error::class]
            if (errorTypeStrategies != null) {
                for (factory in errorTypeStrategies) {
                    if (factory.condition(error)) {
                        Timber.d("找到特定错误类型的恢复策略: ${error::class.simpleName}")
                        return factory.factory(error) as RecoveryStrategy<T>
                    }
                }
            }

            // 2. 然后查找全局错误类型的策略
            val globalTypeStrategies = strategyFactoriesByGlobalErrorType[error.errorType]
            if (globalTypeStrategies != null) {
                for (factory in globalTypeStrategies) {
                    if (factory.condition(error)) {
                        Timber.d("找到全局错误类型的恢复策略: ${error.errorType}")
                        return factory.factory(error) as RecoveryStrategy<T>
                    }
                }
            }

            // 3. 最后查找通用策略
            for (factory in genericStrategyFactories) {
                if (factory.condition(error)) {
                    Timber.d("找到通用恢复策略")
                    return factory.factory(error) as RecoveryStrategy<T>
                }
            }

            Timber.d("没有找到适用的恢复策略")
            return null
        }
    }

    /**
     * 清除所有注册的恢复策略
     */
    suspend fun clearAll() {
        registryMutex.withLock {
            strategyFactoriesByErrorType.clear()
            strategyFactoriesByGlobalErrorType.clear()
            genericStrategyFactories.clear()
            Timber.d("已清除所有恢复策略注册")
        }
    }

    /**
     * 恢复策略工厂
     *
     * 包含匹配条件和策略创建工厂函数
     */
    private class StrategyFactory<T>(
        val condition: (ModernDataError) -> Boolean,
        val factory: (ModernDataError) -> RecoveryStrategy<T>,
    )
}

/**
 * 错误恢复策略提供者
 *
 * 由具体模块实现，用于向全局注册表提供特定领域的恢复策略
 */
interface RecoveryStrategyProvider {
    /**
     * 向注册表注册策略
     *
     * @param registry 全局恢复策略注册表
     */
    suspend fun registerStrategies(registry: RecoveryStrategyRegistry)
}

/**
 * 为ModernDataError扩展函数，通过注册表自动查找恢复策略
 *
 * @param registry 全局恢复策略注册表
 * @return 合适的恢复策略，如果没有找到则返回null
 */
suspend fun <T> ModernDataError.findRecoveryStrategy(
    registry: RecoveryStrategyRegistry,
): RecoveryStrategy<T>? {
    return registry.findStrategy(this)
}

/**
 * 为ModernDataError扩展函数，使用注册表中找到的策略应用恢复
 *
 * @param registry 全局恢复策略注册表
 * @return 恢复结果，如果没有找到策略或恢复失败则返回null
 */
suspend fun <T> ModernDataError.applyRegisteredRecovery(registry: RecoveryStrategyRegistry): T? {
    val strategy = findRecoveryStrategy<T>(registry)
    return strategy?.execute()
}
