package com.example.gymbro.data.workout.template.mapper

import com.example.gymbro.core.error.extensions.toModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.workout.template.entity.ExerciseInTemplateEntity
import com.example.gymbro.data.workout.template.entity.TemplateEntity
import com.example.gymbro.domain.workout.model.template.TemplateExercise
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate

/**
 * TemplateDB 数据映射器
 *
 * 基于 GymBro Clean Architecture 原则
 * 负责 Entity <-> Domain Model 的双向转换
 */

// ==================== WorkoutTemplate 映射 ====================

/**
 * 将 TemplateEntity 转换为 WorkoutTemplate 领域模型
 * v30重构：直接映射版本控制字段
 */
fun TemplateEntity.toDomain(): WorkoutTemplate = WorkoutTemplate.createCompatible(
    id = this.id,
    name = this.name,
    description = this.description,
    targetMuscleGroups = this.targetMuscleGroups,
    difficulty = this.difficulty,
    estimatedDuration = this.estimatedDuration, // 兼容性处理会自动修复 0 -> 30
    userId = this.userId,
    isPublic = this.isPublic,
    isFavorite = this.isFavorite,
    tags = this.tags,
    createdAt = this.createdAt,
    updatedAt = this.updatedAt,
    exercises = emptyList(), // 需要单独加载
    // 🔥 v30重构：直接映射版本控制字段
    usageCount = 0,
    currentVersion = this.currentVersion,
    isDraft = this.isDraft,
    isPublished = this.isPublished,
    lastPublishedAt = this.lastPublishedAt,
)

/**
 * 将 WorkoutTemplate 领域模型转换为 TemplateEntity
 * v30重构：直接映射版本控制字段
 */
fun WorkoutTemplate.toEntity(): TemplateEntity = TemplateEntity(
    id = this.id,
    name = this.name,
    description = this.description,
    targetMuscleGroups = this.targetMuscleGroups ?: emptyList(),
    difficulty = this.difficulty ?: 1,
    estimatedDuration = this.estimatedDuration,
    userId = this.userId,
    isPublic = this.isPublic, // 保留原有字段用于向后兼容
    isFavorite = this.isFavorite,
    tags = this.tags ?: emptyList(),
    createdAt = this.createdAt,
    updatedAt = this.updatedAt,
    // 🔥 v30重构：直接映射版本控制字段
    currentVersion = this.currentVersion,
    isDraft = this.isDraft,
    isPublished = this.isPublished,
    lastPublishedAt = this.lastPublishedAt,
    jsonData = null, // v30不再需要JSON存储版本控制字段
)

/**
 * 安全转换 TemplateEntity 列表为 WorkoutTemplate 列表
 */
fun List<TemplateEntity>.toDomainSafely(): ModernResult<List<WorkoutTemplate>> = try {
    ModernResult.Success(this.map { it.toDomain() })
} catch (e: Exception) {
    ModernResult.Error(
        e.toModernDataError(
            operationName = "TemplateEntity.toDomainSafely",
            uiMessage = UiText.DynamicString("转换模板数据失败"),
        ),
    )
}

/**
 * 安全转换 WorkoutTemplate 列表为 TemplateEntity 列表
 */
fun List<WorkoutTemplate>.toEntitySafely(): ModernResult<List<TemplateEntity>> = try {
    ModernResult.Success(this.map { it.toEntity() })
} catch (e: Exception) {
    ModernResult.Error(
        e.toModernDataError(
            operationName = "WorkoutTemplate.toEntitySafely",
            uiMessage = UiText.DynamicString("转换模板实体失败"),
        ),
    )
}

// ==================== TemplateExercise 映射 ====================

/**
 * 将 ExerciseInTemplateEntity 转换为 TemplateExercise 领域模型
 * 🔥 修复：增强类型转换的容错性，避免数据丢失
 * 🔥 关键修复：解析 notes 中的 customSets 数据，确保数据完整性
 */
fun ExerciseInTemplateEntity.toDomain(): TemplateExercise {
    // 🔥 关键修复：从 notes 中提取 customSets 数据
    val (cleanNotes, customSets) = try {
        if (this.notes?.contains("__CUSTOM_SETS_JSON__:") == true) {
            val marker = "__CUSTOM_SETS_JSON__:"
            val markerIndex = this.notes!!.indexOf(marker)
            if (markerIndex != -1) {
                val beforeMarker = this.notes!!.substring(0, markerIndex).trim()
                val jsonStart = markerIndex + marker.length
                val jsonData = this.notes!!.substring(jsonStart)

                val json = kotlinx.serialization.json.Json {
                    ignoreUnknownKeys = true
                    isLenient = true
                }
                val parsedSets = json.decodeFromString<List<com.example.gymbro.shared.models.workout.TemplateSetDto>>(jsonData)

                // 转换为 Domain 模型的 TemplateSet
                val domainSets = parsedSets.map { set ->
                    com.example.gymbro.domain.workout.model.template.TemplateSet(
                        setNumber = set.setNumber,
                        targetWeight = set.targetWeight,
                        targetReps = set.targetReps,
                        restTimeSeconds = set.restTimeSeconds,
                        targetDuration = set.targetDuration,
                        rpe = set.rpe
                    )
                }

                val actualNotes = if (beforeMarker.isNotBlank()) beforeMarker else null
                actualNotes to domainSets
            } else {
                this.notes to emptyList()
            }
        } else {
            this.notes to emptyList()
        }
    } catch (e: Exception) {
        // 解析失败时保持原始 notes，customSets 为空
        this.notes to emptyList()
    }

    return TemplateExercise(
        id = this.id,
        exerciseId = this.exerciseId,
        name = "", // 需要通过exerciseId查询动作名称
        order = this.order,
        sets = this.sets,
        // 🔥 修复：增强 reps 转换的容错性
        reps = parseRepsFromString(this.repsPerSet),
        // 🔥 修复：确保 restSeconds 不为空
        restSeconds = this.restSeconds,
        // 🔥 修复：增强 weight 转换的容错性
        weight = parseWeightFromString(this.weightSuggestion),
        notes = cleanNotes,
        // 🔥 关键修复：添加解析出的 customSets 数据
        customSets = customSets
    )
}

/**
 * 将 TemplateExercise 领域模型转换为 ExerciseInTemplateEntity
 */
fun TemplateExercise.toEntity(): ExerciseInTemplateEntity = ExerciseInTemplateEntity(
    id = this.id,
    templateId = "", // 需要从上下文设置
    exerciseId = this.exerciseId,
    order = this.order,
    sets = this.sets,
    repsPerSet = this.reps.toString(),
    weightSuggestion = this.weight?.toString(),
    restSeconds = this.restSeconds,
    notes = this.notes,
    superset = false, // 默认值
    supersetGroupId = null, // 默认值
)

/**
 * 安全转换 ExerciseInTemplateEntity 列表为 TemplateExercise 列表
 */
fun List<ExerciseInTemplateEntity>.toTemplateExerciseDomainSafely(): ModernResult<List<TemplateExercise>> = try {
    ModernResult.Success(this.map { it.toDomain() })
} catch (e: Exception) {
    ModernResult.Error(
        e.toModernDataError(
            operationName = "ExerciseInTemplateEntity.toTemplateExerciseDomainSafely",
            uiMessage = UiText.DynamicString("转换模板动作数据失败"),
        ),
    )
}

/**
 * 安全转换 TemplateExercise 列表为 ExerciseInTemplateEntity 列表
 */
fun List<TemplateExercise>.toExerciseInTemplateEntitySafely(): ModernResult<List<ExerciseInTemplateEntity>> = try {
    ModernResult.Success(this.map { it.toEntity() })
} catch (e: Exception) {
    ModernResult.Error(
        e.toModernDataError(
            operationName = "TemplateExercise.toExerciseInTemplateEntitySafely",
            uiMessage = UiText.DynamicString("转换模板动作实体失败"),
        ),
    )
}

// ==================== 复合映射 ====================

/**
 * 将 TemplateEntity 和 ExerciseInTemplateEntity 列表组合为完整的 WorkoutTemplate
 * 修复：使用兼容性创建方法处理旧数据
 */
fun TemplateEntity.toDomainWithExercises(exercises: List<ExerciseInTemplateEntity>): WorkoutTemplate {
    val domainExercises = exercises.map { it.toDomain() }
    return WorkoutTemplate.createCompatible(
        id = this.id,
        name = this.name,
        description = this.description,
        targetMuscleGroups = this.targetMuscleGroups,
        difficulty = this.difficulty,
        estimatedDuration = this.estimatedDuration, // 兼容性处理会自动修复 0 -> 30
        userId = this.userId,
        isPublic = this.isPublic,
        isFavorite = this.isFavorite,
        tags = this.tags,
        createdAt = this.createdAt,
        updatedAt = this.updatedAt,
        exercises = domainExercises,
    )
}

/**
 * 安全组合转换
 */
fun TemplateEntity.toDomainWithExercisesSafely(
    exercises: List<ExerciseInTemplateEntity>,
): ModernResult<WorkoutTemplate> = try {
    ModernResult.Success(this.toDomainWithExercises(exercises))
} catch (e: Exception) {
    ModernResult.Error(
        e.toModernDataError(
            operationName = "TemplateEntity.toDomainWithExercisesSafely",
            uiMessage = UiText.DynamicString("转换模板和动作数据失败"),
        ),
    )
}

// ==================== 类型转换辅助函数 ====================

/**
 * 🔥 增强的 reps 解析函数，支持多种格式
 */
private fun parseRepsFromString(repsPerSet: String?): Int {
    if (repsPerSet.isNullOrBlank()) return 10 // 默认值

    return try {
        // 尝试直接转换为整数
        repsPerSet.toInt()
    } catch (e: NumberFormatException) {
        try {
            // 尝试解析范围格式 "8-12"，取中间值
            val rangeParts = repsPerSet.split("-")
            if (rangeParts.size == 2) {
                val min = rangeParts[0].trim().toInt()
                val max = rangeParts[1].trim().toInt()
                (min + max) / 2 // 取平均值
            } else {
                // 尝试提取数字
                val numberPattern = Regex("""(\d+)""")
                val match = numberPattern.find(repsPerSet)
                match?.groupValues?.get(1)?.toInt() ?: 10
            }
        } catch (e: Exception) {
            timber.log.Timber.w(e, "解析 reps 失败: $repsPerSet，使用默认值 10")
            10 // 默认值
        }
    }
}

/**
 * 🔥 增强的 weight 解析函数，支持多种格式
 */
private fun parseWeightFromString(weightSuggestion: String?): Float? {
    if (weightSuggestion.isNullOrBlank()) return null

    return try {
        // 尝试直接转换为浮点数
        weightSuggestion.toFloat()
    } catch (e: NumberFormatException) {
        try {
            // 移除单位后缀 (kg, KG, 公斤等)
            val cleanWeight = weightSuggestion
                .replace(Regex("""(?i)(kg|公斤|斤)"""), "")
                .trim()

            // 尝试解析范围格式 "50-60"，取中间值
            val rangeParts = cleanWeight.split("-")
            if (rangeParts.size == 2) {
                val min = rangeParts[0].trim().toFloat()
                val max = rangeParts[1].trim().toFloat()
                (min + max) / 2 // 取平均值
            } else {
                // 尝试提取数字
                val numberPattern = Regex("""(\d+(?:\.\d+)?)""")
                val match = numberPattern.find(cleanWeight)
                match?.groupValues?.get(1)?.toFloat()
            }
        } catch (e: Exception) {
            timber.log.Timber.w(e, "解析 weight 失败: $weightSuggestion，返回 null")
            null
        }
    }
}
