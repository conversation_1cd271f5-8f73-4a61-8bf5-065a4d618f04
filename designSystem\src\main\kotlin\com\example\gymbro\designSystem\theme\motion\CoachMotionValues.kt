package com.example.gymbro.designSystem.theme.motion

/**
 * Coach模块专用动画值Token系统
 *
 * 基于input/README.md中的硬编码值进行标准化，提供统一的动画参数定义
 * 确保Coach模块所有组件使用一致的动画效果
 */
object CoachMotionValues {

    // === 缩放动画值 ===
    object Scale {
        // 按压状态缩放值
        const val PRESSED_TAG = 0.96f // ChatGptStyleTag按压缩放（input/README.md）
        const val PRESSED_VOICE_BUTTON = 0.88f // 语音按钮按压缩放（input/README.md）
        const val PRESSED_MODEL_CHIP = 0.96f // ModelChip按压缩放
        const val PRESSED_SEND_BUTTON = 0.95f // 发送按钮按压缩放

        // 悬停状态缩放值
        const val HOVER_TAG = 1.02f // 标签悬停放大（input/README.md）
        const val HOVER_MODEL_CHIP = 1.02f // ModelChip悬停放大
        const val HOVER_VOICE_BUTTON = 1.05f // 语音按钮悬停放大（input/README.md）

        // 特殊状态缩放值
        const val DISABLED = 1.0f // 禁用状态保持原尺寸
        const val NORMAL = 1.0f // 正常状态基准值
        const val VOICE_ACTIVE = 1.1f // 语音按钮激活状态（input/README.md）
    }

    // === 透明度动画值 ===
    object Alpha {
        // 正常透明度
        const val NORMAL = 1.0f // 完全不透明
        const val ENABLED_TEXT = 0.9f // 启用状态文本透明度

        // 禁用透明度
        const val DISABLED = 0.4f // 禁用状态透明度（input/README.md）
        const val DISABLED_TEXT = 0.5f // 禁用状态文本透明度

        // 悬停和交互透明度
        const val HOVER_MIN = 0.7f // 悬停最小透明度（input/README.md）
        const val HOVER_MAX = 1.0f // 悬停最大透明度
        const val PRESS_ALPHA = 0.8f // 按压时透明度

        // 面板和背景透明度
        const val PANEL_BACKGROUND = 0.4f // 面板背景透明度
        const val PANEL_FADE = 0.7f // 面板淡入淡出值（input/README.md）
        const val SURFACE_VARIANT = 0.4f // Surface variant透明度
        const val SURFACE_VARIANT_DISABLED = 0.2f // 禁用状态surface透明度
    }

    // === 旋转动画值 ===
    object Rotation {
        // 微妙旋转效果
        const val ICON_SUBTLE = 2f // 细微图标旋转（input/README.md）
        const val ICON_NORMAL = 5f // 普通图标旋转
        const val ICON_SETTINGS = 30f // 设置图标旋转（input/README.md）

        // 加载旋转
        const val LOADING_FULL = 360f // 完整旋转一圈
        const val LOADING_HALF = 180f // 半圈旋转

        // 特殊效果旋转
        const val VOICE_PULSE = 15f // 语音脉动旋转
        const val THINKING_WOBBLE = 10f // 思考摆动效果
    }

    // === 偏移动画值 ===
    object Offset {
        // 面板滑动距离（dp）
        const val PANEL_SLIDE = 50f // 面板滑动距离（input/README.md）
        const val MODAL_SLIDE = 48f // 模态框滑动距离
        const val PAGE_SLIDE = 32f // 页面滑动距离

        // 消息动画偏移
        const val MESSAGE_ENTER_Y = 24f // 消息进入Y偏移
        const val MESSAGE_ENTER_X = 16f // 消息进入X偏移

        // 输入框动画偏移
        const val INPUT_FOCUS_Y = 2f // 输入框聚焦Y偏移
        const val INPUT_SHAKE_X = 8f // 输入框错误摇摆X偏移
    }

    // === 组件专用动画值 ===
    object Component {
        // ChatGptStyleTag动画值
        object Tag {
            const val PRESS_SCALE = Scale.PRESSED_TAG
            const val HOVER_SCALE = Scale.HOVER_TAG
            const val ENABLED_ALPHA = Alpha.ENABLED_TEXT
            const val DISABLED_ALPHA = Alpha.DISABLED_TEXT
        }

        // VoiceButton动画值
        object VoiceButton {
            const val PRESS_SCALE = Scale.PRESSED_VOICE_BUTTON
            const val HOVER_SCALE = Scale.HOVER_VOICE_BUTTON
            const val ACTIVE_SCALE = Scale.VOICE_ACTIVE
            const val DISABLED_ALPHA = Alpha.DISABLED
        }

        // ModelChip动画值
        object ModelChip {
            const val PRESS_SCALE = Scale.PRESSED_MODEL_CHIP
            const val HOVER_SCALE = Scale.HOVER_MODEL_CHIP
            const val SETTINGS_ROTATION = Rotation.ICON_SETTINGS
            const val SUBTLE_ROTATION = Rotation.ICON_SUBTLE
        }

        // SuggestionPanel动画值
        object SuggestionPanel {
            const val SLIDE_DISTANCE = Offset.PANEL_SLIDE
            const val FADE_ALPHA = Alpha.PANEL_FADE
            const val BACKGROUND_ALPHA = Alpha.PANEL_BACKGROUND
        }

        // ThinkingBox动画值
        object ThinkingBox {
            const val PULSE_ALPHA_MIN = Alpha.HOVER_MIN
            const val PULSE_ALPHA_MAX = Alpha.HOVER_MAX
            const val WOBBLE_ROTATION = Rotation.THINKING_WOBBLE
        }
    }

    // === 语义化动画值 ===
    object Semantic {
        // 微交互效果
        const val MICRO_SCALE_DOWN = 0.98f // 微交互缩小
        const val MICRO_SCALE_UP = 1.02f // 微交互放大
        const val MICRO_ALPHA_FADE = 0.9f // 微交互淡化

        // 强调效果
        const val EMPHASIS_SCALE = 1.05f // 强调缩放
        const val EMPHASIS_ALPHA = 1.0f // 强调透明度
        const val EMPHASIS_ROTATION = 3f // 强调旋转

        // 错误状态
        const val ERROR_SHAKE = 8f // 错误摇摆
        const val ERROR_ALPHA = 0.8f // 错误透明度

        // 成功状态
        const val SUCCESS_BOUNCE = 1.1f // 成功弹跳
        const val SUCCESS_ALPHA = 1.0f // 成功透明度
    }

    // === 动画范围限制 ===
    object Constraints {
        // 缩放范围限制
        const val MIN_SCALE = 0.8f // 最小缩放值
        const val MAX_SCALE = 1.2f // 最大缩放值

        // 透明度范围限制
        const val MIN_ALPHA = 0.1f // 最小透明度
        const val MAX_ALPHA = 1.0f // 最大透明度

        // 旋转范围限制
        const val MAX_ROTATION = 360f // 最大旋转角度
        const val MIN_ROTATION = -360f // 最小旋转角度
    }
}
