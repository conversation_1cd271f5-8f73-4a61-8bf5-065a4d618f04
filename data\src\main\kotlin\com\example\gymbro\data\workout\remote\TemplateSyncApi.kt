package com.example.gymbro.data.workout.remote

import com.example.gymbro.shared.models.workout.WorkoutTemplateDto
import retrofit2.Response
import retrofit2.http.*

/**
 * 训练模板远程同步API接口
 * 按照训练模板.md v1.0设计规范实现
 *
 * 提供云端同步功能，支持双向增量同步
 */
interface TemplateSyncApi {

    /**
     * 获取服务器端模板列表
     * @param userId 用户ID
     * @param lastSyncTime 上次同步时间戳，用于增量同步
     * @return 服务器端的模板列表
     */
    @GET("templates")
    suspend fun getServerTemplates(
        @Query("userId") userId: String,
        @Query("lastSyncTime") lastSyncTime: Long = 0,
    ): Response<List<WorkoutTemplateDto>>

    /**
     * 上传本地模板到服务器
     * @param templates 要上传的模板列表
     * @return 上传结果
     */
    @POST("templates/upload")
    suspend fun uploadTemplates(
        @Body templates: List<WorkoutTemplateDto>,
    ): Response<SyncResult>

    /**
     * 下载服务器模板到本地
     * @param templateIds 要下载的模板ID列表
     * @return 模板数据
     */
    @POST("templates/download")
    suspend fun downloadTemplates(
        @Body templateIds: List<String>,
    ): Response<List<WorkoutTemplateDto>>

    /**
     * 解决同步冲突
     * @param conflicts 冲突模板列表，包含本地和服务器版本
     * @return 解决结果
     */
    @POST("templates/resolve-conflicts")
    suspend fun resolveConflicts(
        @Body conflicts: List<TemplateConflict>,
    ): Response<SyncResult>

    /**
     * 删除服务器端模板
     * @param templateIds 要删除的模板ID列表
     * @return 删除结果
     */
    @DELETE("templates")
    suspend fun deleteServerTemplates(
        @Body templateIds: List<String>,
    ): Response<SyncResult>
}

/**
 * 同步结果响应
 */
data class SyncResult(
    val success: Boolean,
    val message: String,
    val syncedCount: Int = 0,
    val conflictCount: Int = 0,
    val errorCount: Int = 0,
)

/**
 * 模板冲突数据
 */
data class TemplateConflict(
    val templateId: String,
    val localTemplate: WorkoutTemplateDto,
    val serverTemplate: WorkoutTemplateDto,
    val resolution: ConflictResolution,
)

/**
 * 冲突解决策略
 */
enum class ConflictResolution {
    USE_LOCAL, // 使用本地版本
    USE_SERVER, // 使用服务器版本
    USE_LATEST, // 使用最新的版本（基于updatedAt）
    USE_HIGHER_VERSION, // 使用版本号更高的版本
}
