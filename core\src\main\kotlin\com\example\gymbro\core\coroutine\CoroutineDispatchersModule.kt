package com.example.gymbro.core.coroutine

import com.example.gymbro.core.di.qualifiers.DefaultDispatcher
import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.di.qualifiers.MainDispatcher
import com.example.gymbro.core.di.qualifiers.UnconfinedDispatcher
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import javax.inject.Singleton

/**
 * 协程调度器与DI模块
 *
 * 提供不同类型的协程调度器用于不同场景：
 * - IO: 网络请求、数据库操作、文件IO等IO密集型任务
 * - Default: 复杂计算、排序、JSON解析等CPU密集型任务
 * - Main: UI更新和交互
 * - Unconfined: 不需要特定调度的任务，通常用于测试
 *
 * 注意：限定符定义已统一迁移到 core.di.qualifiers.CoroutineQualifiers
 */

/**
 * 提供协程调度器的DI模块
 *
 * 提供各种调度器的单例注入点
 */
@Module
@InstallIn(SingletonComponent::class)
object CoroutineDispatchersModule {

    /**
     * 提供IO调度器
     */
    @IoDispatcher
    @Provides
    @Singleton
    fun provideIoDispatcher(): CoroutineDispatcher = Dispatchers.IO

    /**
     * 提供默认调度器
     */
    @DefaultDispatcher
    @Provides
    @Singleton
    fun provideDefaultDispatcher(): CoroutineDispatcher = Dispatchers.Default

    /**
     * 提供主线程调度器
     */
    @MainDispatcher
    @Provides
    @Singleton
    fun provideMainDispatcher(): CoroutineDispatcher = Dispatchers.Main

    /**
     * 提供非受限调度器
     */
    @UnconfinedDispatcher
    @Provides
    @Singleton
    fun provideUnconfinedDispatcher(): CoroutineDispatcher = Dispatchers.Unconfined
}
