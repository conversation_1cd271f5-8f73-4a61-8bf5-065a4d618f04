package com.example.gymbro.core.error.types.data

import com.example.gymbro.core.error.types.*
import com.example.gymbro.core.ui.text.UiText

/**
 * 数据相关错误类型集合
 *
 * 包含数据访问、同步和验证相关的错误处理
 * 从 DomainErrors.kt 迁移而来，专门处理数据层错误
 */
object DataErrors {
    /**
     * 表示数据操作成功的状态（非错误状态）
     * 注意：这不是一个真正的错误，而是用于表示成功状态
     */
    val Valid =
        ModernDataError(
            operationName = "DataErrors.Valid",
            errorType = GlobalErrorType.Data.General,
            category = ErrorCategory.DATA,
            uiMessage = UiText.Empty,
            severity = ErrorSeverity.INFO,
            recoverable = true,
            cause = null,
            metadataMap = mapOf("status" to "valid"),
        )

    /**
     * 数据错误相关工厂方法
     */
    object DataError {
        /**
         * 创建数据序列化错误
         */
        fun serialization(
            operationName: String = "DataError.serialization",
            message: UiText,
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError =
            ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.Data.Corrupted,
                category = ErrorCategory.DATA,
                uiMessage = message,
                severity = ErrorSeverity.ERROR,
                recoverable = false,
                cause = cause,
                metadataMap = metadataMap,
            )

        /**
         * 创建数据不存在错误
         */
        fun notFound(
            operationName: String = "DataError.notFound",
            message: UiText,
            entityType: String? = null,
            entityId: String? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError {
            val metadataWithEntityInfo =
                metadataMap.toMutableMap().apply {
                    entityType?.let { put(StandardKeys.ENTITY_NAME.key, it) }
                    entityId?.let { put(StandardKeys.ENTITY_ID.key, it) }
                }

            return ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.Data.NotFound,
                category = ErrorCategory.DATA,
                uiMessage = message,
                severity = ErrorSeverity.WARNING,
                recoverable = true,
                cause = null,
                metadataMap = metadataWithEntityInfo,
            )
        }

        /**
         * 创建数据访问错误
         */
        fun access(
            operationName: String = "DataError.access",
            message: UiText,
            entityType: String? = null,
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError {
            val metadataWithEntityInfo =
                metadataMap.toMutableMap().apply {
                    entityType?.let { put(StandardKeys.ENTITY_NAME.key, it) }
                }
            return ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.Data.Access,
                category = ErrorCategory.DATA,
                uiMessage = message,
                severity = ErrorSeverity.ERROR,
                recoverable = false,
                cause = cause,
                metadataMap = metadataWithEntityInfo,
            )
        }

        /**
         * 创建数据约束错误
         */
        fun constraint(
            operationName: String = "DataError.constraint",
            message: UiText,
            constraintType: String? = null,
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError {
            val metadataWithConstraint =
                metadataMap.toMutableMap().apply {
                    constraintType?.let { put(StandardKeys.ERROR_SUBTYPE.key, it) }
                }
            return ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.Data.Constraint,
                category = ErrorCategory.DATA,
                uiMessage = message,
                severity = ErrorSeverity.ERROR,
                recoverable = false,
                cause = cause,
                metadataMap = metadataWithConstraint,
            )
        }

        /**
         * 创建数据创建错误
         */
        fun create(
            operationName: String,
            message: UiText,
            cause: Throwable? = null,
            entityType: String? = null,
            entityId: String? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError {
            val fullMetadata =
                metadataMap.toMutableMap().apply {
                    entityType?.let { put(StandardKeys.ENTITY_NAME.key, it) }
                    entityId?.let { put(StandardKeys.ENTITY_ID.key, it) }
                    put(StandardKeys.ERROR_SUBTYPE.key, "CREATE_ERROR")
                }
            return ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.Data.General,
                category = ErrorCategory.DATA,
                uiMessage = message,
                severity = ErrorSeverity.ERROR,
                recoverable = false,
                cause = cause,
                metadataMap = fullMetadata,
            )
        }

        /**
         * 创建数据更新错误
         */
        fun update(
            operationName: String,
            message: UiText,
            cause: Throwable? = null,
            entityType: String? = null,
            entityId: String? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError {
            val fullMetadata =
                metadataMap.toMutableMap().apply {
                    entityType?.let { put(StandardKeys.ENTITY_NAME.key, it) }
                    entityId?.let { put(StandardKeys.ENTITY_ID.key, it) }
                    put(StandardKeys.ERROR_SUBTYPE.key, "UPDATE_ERROR")
                }
            return ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.Data.General,
                category = ErrorCategory.DATA,
                uiMessage = message,
                severity = ErrorSeverity.ERROR,
                recoverable = false,
                cause = cause,
                metadataMap = fullMetadata,
            )
        }

        /**
         * 创建数据查询错误
         */
        fun query(
            operationName: String,
            message: UiText,
            cause: Throwable? = null,
            entityType: String? = null,
            entityId: String? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError {
            val fullMetadata =
                metadataMap.toMutableMap().apply {
                    entityType?.let { put(StandardKeys.ENTITY_NAME.key, it) }
                    entityId?.let { put(StandardKeys.ENTITY_ID.key, it) }
                    put(StandardKeys.ERROR_SUBTYPE.key, "QUERY_ERROR")
                }
            return ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.Data.General,
                category = ErrorCategory.DATA,
                uiMessage = message,
                severity = ErrorSeverity.ERROR,
                recoverable = true,
                cause = cause,
                metadataMap = fullMetadata,
            )
        }

        /**
         * 创建数据删除错误
         */
        fun delete(
            operationName: String,
            message: UiText,
            cause: Throwable? = null,
            entityType: String? = null,
            entityId: String? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError {
            val fullMetadata =
                metadataMap.toMutableMap().apply {
                    entityType?.let { put(StandardKeys.ENTITY_NAME.key, it) }
                    entityId?.let { put(StandardKeys.ENTITY_ID.key, it) }
                    put(StandardKeys.ERROR_SUBTYPE.key, "DELETE_ERROR")
                }
            return ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.Data.General,
                category = ErrorCategory.DATA,
                uiMessage = message,
                severity = ErrorSeverity.ERROR,
                recoverable = false,
                cause = cause,
                metadataMap = fullMetadata,
            )
        }

        fun unknown(
            operationName: String,
            message: UiText.DynamicString,
            cause: Exception,
        ): ModernDataError = ModernDataError(
            operationName = operationName,
            errorType = GlobalErrorType.Data.General,
            category = ErrorCategory.DATA,
            uiMessage = message,
            severity = ErrorSeverity.ERROR,
            recoverable = false,
            cause = cause,
            metadataMap = mapOf(
                StandardKeys.ERROR_SUBTYPE.key to "unknown_error",
                StandardKeys.TIMESTAMP.key to kotlinx.datetime.Clock.System.now().toEpochMilliseconds(),
            ),
        )
    }

    /**
     * 同步错误，处理数据同步过程中的错误
     */
    class SyncError private constructor(
        val operationName: String,
        val uiMessage: UiText,
        val errorType: GlobalErrorType = GlobalErrorType.Sync.Failed,
        val syncType: String,
        val entityType: String? = null,
        val entityId: String? = null,
        val severity: ErrorSeverity = ErrorSeverity.ERROR,
        val recoverable: Boolean = true,
        val cause: Throwable? = null,
        val metadataMap: Map<String, Any> = emptyMap(),
    ) {
        /**
         * 转换为ModernDataError
         */
        fun toModernDataError(): ModernDataError =
            ModernDataError(
                operationName = operationName,
                errorType = errorType,
                category = ErrorCategory.BUSINESS,
                uiMessage = uiMessage,
                severity = severity,
                recoverable = recoverable,
                cause = cause,
                metadataMap = createMetadataMap(syncType, entityType, entityId, metadataMap),
            )

        companion object {
            /**
             * 创建包含同步详情的元数据映射
             */
            private fun createMetadataMap(
                syncType: String,
                entityType: String?,
                entityId: String?,
                baseMetadata: Map<String, Any>,
            ): Map<String, Any> {
                val resultMap =
                    baseMetadata.toMutableMap().apply {
                        put(StandardKeys.SYNC_TYPE.key, syncType)
                    }
                entityType?.let { resultMap[StandardKeys.ENTITY_NAME.key] = it }
                entityId?.let { resultMap[StandardKeys.ENTITY_ID.key] = it }
                return resultMap
            }

            /**
             * 创建同步失败错误
             */
            fun failed(
                operationName: String = "SyncFailed",
                message: UiText,
                syncType: String,
                entityType: String? = null,
                entityId: String? = null,
                cause: Throwable? = null,
                metadataMap: Map<String, Any> = emptyMap(),
            ): ModernDataError =
                SyncError(
                    operationName = operationName,
                    uiMessage = message,
                    errorType = GlobalErrorType.Sync.Failed,
                    syncType = syncType,
                    entityType = entityType,
                    entityId = entityId,
                    cause = cause,
                    metadataMap = metadataMap,
                ).toModernDataError()

            /**
             * 创建同步冲突错误
             */
            fun conflict(
                operationName: String = "SyncConflict",
                message: UiText,
                syncType: String,
                entityType: String? = null,
                entityId: String? = null,
                cause: Throwable? = null,
                metadataMap: Map<String, Any> = emptyMap(),
            ): ModernDataError =
                SyncError(
                    operationName = operationName,
                    uiMessage = message,
                    errorType = GlobalErrorType.Sync.Conflict,
                    syncType = syncType,
                    entityType = entityType,
                    entityId = entityId,
                    cause = cause,
                    metadataMap = metadataMap,
                ).toModernDataError()
        }
    }

    /**
     * 验证错误相关工厂方法
     */
    object Validation {
        /**
         * 创建包含字段和验证类型的元数据映射
         */
        private fun createMetadataMapWithFieldAndType(
            field: String?,
            validationType: String?,
            baseMetadata: Map<String, Any>,
        ): Map<String, Any> {
            val resultMap = baseMetadata.toMutableMap()
            field?.let { resultMap[StandardKeys.FIELD.key] = it }
            validationType?.let { resultMap[StandardKeys.ERROR_SUBTYPE.key] = it }
            return resultMap
        }

        /**
         * 创建必填字段错误
         */
        fun required(
            field: String? = null,
            operationName: String = "ValidationError.required",
            message: UiText = field?.let { UiText.DynamicString("字段 '$it' 是必填项") } ?: UiText.DynamicString("必填字段缺失"),
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError =
            ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.Validation.Required,
                category = ErrorCategory.VALIDATION,
                uiMessage = message,
                severity = ErrorSeverity.WARNING,
                recoverable = true,
                metadataMap = createMetadataMapWithFieldAndType(field, "required", metadataMap),
            )

        /**
         * 创建无效输入错误
         */
        fun invalidInput(
            field: String? = null,
            operationName: String = "ValidationError.invalidInput",
            message: UiText = field?.let { UiText.DynamicString("字段 '$it' 输入无效") } ?: UiText.DynamicString("输入无效"),
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError =
            ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.Validation.InvalidInput,
                category = ErrorCategory.VALIDATION,
                uiMessage = message,
                severity = ErrorSeverity.WARNING,
                recoverable = true,
                metadataMap = createMetadataMapWithFieldAndType(field, "invalid_input", metadataMap),
            )

        /**
         * 创建字段格式错误
         */
        fun formatError(
            operationName: String = "ValidationError.formatError",
            field: String,
            message: UiText = UiText.DynamicString("字段 '$field' 格式无效"),
        ): ModernDataError =
            ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.Validation.Format,
                category = ErrorCategory.VALIDATION,
                uiMessage = message,
                severity = ErrorSeverity.WARNING,
                recoverable = true,
                metadataMap = createMetadataMapWithFieldAndType(field, "format", emptyMap()),
            )

        /**
         * 创建范围错误
         */
        fun rangeError(
            field: String,
            min: Number? = null,
            max: Number? = null,
            operationName: String = "ValidationError.rangeError",
        ): ModernDataError {
            val metadata = mutableMapOf<String, Any>()
            min?.let { metadata[StandardKeys.MIN.key] = it }
            max?.let { metadata[StandardKeys.MAX.key] = it }
            val message =
                when {
                    min != null && max != null -> UiText.DynamicString("字段 '$field' 的值必须在 $min 和 $max 之间")
                    min != null -> UiText.DynamicString("字段 '$field' 的值必须大于或等于 $min")
                    max != null -> UiText.DynamicString("字段 '$field' 的值必须小于或等于 $max")
                    else -> UiText.DynamicString("字段 '$field' 的值超出有效范围")
                }
            return ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.Validation.Range,
                category = ErrorCategory.VALIDATION,
                uiMessage = message,
                severity = ErrorSeverity.WARNING,
                recoverable = true,
                metadataMap = createMetadataMapWithFieldAndType(field, "range", metadata),
            )
        }

        /**
         * 创建自定义验证错误
         *
         * 这是一个通用的验证错误创建方法，允许完全自定义错误类型和元数据
         *
         * @param operationName 操作名称
         * @param message 错误消息
         * @param field 字段名称，可选
         * @param validationType 验证类型，可选
         * @param errorType 错误类型，默认为InvalidValue
         * @param severity 错误严重程度，默认为WARNING
         * @param recoverable 是否可恢复，默认为true
         * @param cause 原始异常，可选
         * @param metadataMap 额外的元数据，可选
         * @return 创建的ModernDataError实例
         */
        fun customValidationError(
            operationName: String = "ValidationError.custom",
            message: UiText,
            field: String? = null,
            validationType: String? = null,
            errorType: GlobalErrorType = GlobalErrorType.Validation.InvalidValue,
            severity: ErrorSeverity = ErrorSeverity.WARNING,
            recoverable: Boolean = true,
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError =
            ModernDataError(
                operationName = operationName,
                errorType = errorType,
                category = ErrorCategory.VALIDATION,
                uiMessage = message,
                severity = severity,
                recoverable = recoverable,
                cause = cause,
                metadataMap = createMetadataMapWithFieldAndType(field, validationType, metadataMap),
            )

        /**
         * 兼容性方法：创建必填字段错误
         * @deprecated 使用 required() 方法替代
         */
        @Deprecated("使用 required() 方法替代", ReplaceWith("required(field, operationName, message, metadataMap)"))
        fun requiredFieldError(
            field: String? = null,
            operationName: String = "ValidationError.requiredFieldError",
            message: UiText = field?.let { UiText.DynamicString("字段 '$it' 是必填项") } ?: UiText.DynamicString("必填字段缺失"),
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError = required(field, operationName, message, metadataMap)
    }
}
