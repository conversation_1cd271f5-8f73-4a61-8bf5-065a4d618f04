package com.example.gymbro.data.workout.session.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * 会话组数实体 - SessionDB 组数详细记录
 *
 * 基于 05_Final_Database_Architecture.md 和 04_Implementation_Plan_CleanSlate_SessionDB.md 设计
 * 存储训练会话中每组的详细执行数据
 */
@Entity(
    tableName = "session_sets",
    foreignKeys = [
        ForeignKey(
            entity = SessionExerciseEntity::class,
            parentColumns = ["id"],
            childColumns = ["sessionExerciseId"],
            onDelete = ForeignKey.CASCADE,
        ),
    ],
    indices = [
        Index("sessionExerciseId"),
        Index("setNumber"),
        Index("isCompleted"),
        Index("timestamp"),
    ],
)
data class SessionSetEntity(
    @PrimaryKey
    val id: String,

    // 关联信息
    val sessionExerciseId: String,

    // 组数信息
    val setNumber: Int, // 第几组 (1, 2, 3...)

    // 执行数据
    val weight: Double?,
    val weightUnit: String?, // kg, lb
    val reps: Int?,
    val timeSeconds: Int?, // 用于计时类动作
    val rpe: Double?, // 自觉疲劳度(1-10)

    // 状态信息
    val isCompleted: Boolean = false,
    val isWarmupSet: Boolean = false,
    val notes: String?,

    // 时间戳
    val timestamp: Long = System.currentTimeMillis(),
)
