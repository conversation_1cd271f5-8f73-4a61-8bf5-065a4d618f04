package com.example.gymbro.core.autosave

import com.example.gymbro.core.autosave.state.AutoSaveState
import kotlinx.coroutines.flow.StateFlow

/**
 * 统一自动保存管理器接口
 *
 * 🎯 功能特性：
 * - 支持多种保存策略（即时、定时、防抖）
 * - 支持多种存储后端（数据库、缓存、文件）
 * - 统一的缓存恢复机制
 * - 完整的生命周期管理
 * - MVI架构集成支持
 * - 类型安全的泛型设计
 *
 * @param T 要保存的数据类型
 */
interface AutoSaveManager<T : Any> {

    companion object {
        /**
         * 最低保存间隔（毫秒）
         * 防止过于频繁的保存操作
         */
        const val MIN_SAVE_INTERVAL_MS = 1000L // 1秒
    }

    /**
     * 自动保存状态
     */
    val state: StateFlow<AutoSaveState<T>>

    /**
     * 启动自动保存
     *
     * @param initialData 初始数据
     */
    fun start(initialData: T)

    /**
     * 更新数据
     *
     * @param newData 新数据
     */
    fun update(newData: T)

    /**
     * 立即保存
     *
     * @return 保存结果
     */
    suspend fun saveNow(): Result<Unit>

    /**
     * 停止自动保存
     */
    fun stop()

    /**
     * 恢复缓存数据
     */
    fun restoreFromCache()

    /**
     * 丢弃缓存数据
     */
    fun discardCache()

    /**
     * 清除所有数据
     */
    suspend fun clear()

    /**
     * 暂停自动保存
     */
    fun pause()

    /**
     * 恢复自动保存
     */
    fun resume()

    /**
     * 检查是否有未保存的更改
     *
     * @return 是否有未保存的更改
     */
    fun hasUnsavedChanges(): Boolean

    /**
     * 获取最后保存时间
     *
     * @return 最后保存时间戳
     */
    fun getLastSaveTime(): Long

    /**
     * 获取保存次数
     *
     * @return 保存次数
     */
    fun getSaveCount(): Int
}
