package com.example.gymbro.designSystem.theme

import android.content.res.Configuration
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import kotlin.annotation.AnnotationRetention
import kotlin.annotation.AnnotationTarget

/**
 * GymBro应用默认预览注解
 *
 * 提供标准的黑白主题预览配置，包含：
 * - 浅色模式（白色背景）
 * - 深色模式（黑色背景）
 * - 标准API级别和显示设置
 *
 * 使用方法：
 * ```kotlin
 * @GymBroPreview
 * @Composable
 * private fun myComponentPreview() {
 *     GymBroTheme {
 *         MyComponent()
 *     }
 * }
 * ```
 */
@Target(AnnotationTarget.FUNCTION)
@Retention(AnnotationRetention.BINARY)
@Preview(
    name = "浅色模式",
    showBackground = true,
    backgroundColor = 0xFFFFFFFF, // 白色背景
    apiLevel = 34,
    group = "GymBro主题",
)
@Preview(
    name = "深色模式",
    uiMode = Configuration.UI_MODE_NIGHT_YES,
    showBackground = true,
    backgroundColor = 0xFF000000, // 黑色背景
    apiLevel = 34,
    group = "GymBro主题",
)
annotation class GymBroPreview

/**
 * GymBro应用扩展预览注解
 *
 * 包含更多设备和配置的预览选项：
 * - 不同屏幕尺寸
 * - 不同字体缩放
 * - 横竖屏方向
 */
@Target(AnnotationTarget.FUNCTION)
@Retention(AnnotationRetention.BINARY)
@Preview(
    name = "浅色模式 - 手机",
    showBackground = true,
    backgroundColor = 0xFFFFFFFF,
    device = "spec:width=360dp,height=640dp,dpi=480",
    apiLevel = 34,
    group = "GymBro扩展",
)
@Preview(
    name = "深色模式 - 手机",
    uiMode = Configuration.UI_MODE_NIGHT_YES,
    showBackground = true,
    backgroundColor = 0xFF000000,
    device = "spec:width=360dp,height=640dp,dpi=480",
    apiLevel = 34,
    group = "GymBro扩展",
)
@Preview(
    name = "浅色模式 - 手机 (横屏)",
    showBackground = true,
    backgroundColor = 0xFFFFFFFF,
    device = "spec:width=640dp,height=360dp,dpi=480,orientation=landscape",
    apiLevel = 34,
    group = "GymBro扩展",
)
@Preview(
    name = "深色模式 - 手机 (横屏)",
    uiMode = Configuration.UI_MODE_NIGHT_YES,
    showBackground = true,
    backgroundColor = 0xFF000000,
    device = "spec:width=640dp,height=360dp,dpi=480,orientation=landscape",
    apiLevel = 34,
    group = "GymBro扩展",
)
@Preview(
    name = "浅色模式 - 平板",
    showBackground = true,
    backgroundColor = 0xFFFFFFFF,
    device = "spec:width=1280dp,height=800dp,dpi=480",
    apiLevel = 34,
    group = "GymBro扩展",
)
@Preview(
    name = "深色模式 - 平板",
    uiMode = Configuration.UI_MODE_NIGHT_YES,
    showBackground = true,
    backgroundColor = 0xFF000000,
    device = "spec:width=1280dp,height=800dp,dpi=480",
    apiLevel = 34,
    group = "GymBro扩展",
)
@Preview(
    name = "大字体 - 浅色",
    showBackground = true,
    backgroundColor = 0xFFFFFFFF,
    fontScale = 1.5f,
    apiLevel = 34,
    group = "GymBro扩展",
)
@Preview(
    name = "大字体 - 深色",
    uiMode = Configuration.UI_MODE_NIGHT_YES,
    showBackground = true,
    backgroundColor = 0xFF000000,
    fontScale = 1.5f,
    apiLevel = 34,
    group = "GymBro扩展",
)
annotation class GymBroPreviewExtended

/**
 * GymBro应用组件预览注解
 *
 * 专门用于单个组件的快速预览，使用紧凑的显示设置
 */
@Target(AnnotationTarget.FUNCTION)
@Retention(AnnotationRetention.BINARY)
@Preview(
    name = "组件 - 浅色",
    showBackground = true,
    backgroundColor = 0xFFFFFFFF,
    widthDp = 320,
    heightDp = 200,
    apiLevel = 34,
    group = "GymBro组件",
)
@Preview(
    name = "组件 - 深色",
    uiMode = Configuration.UI_MODE_NIGHT_YES,
    showBackground = true,
    backgroundColor = 0xFF000000,
    widthDp = 320,
    heightDp = 200,
    apiLevel = 34,
    group = "GymBro组件",
)
annotation class GymBroComponentPreview

/**
 * 主题化预览包装函数
 *
 * 自动包装组件在GymBroTheme中，并提供Surface背景
 *
 * @param darkTheme 是否使用深色主题
 * @param content 要预览的组件内容
 */
@Composable
fun ThemedPreview(
    darkTheme: Boolean = false,
    content: @Composable () -> Unit,
) {
    GymBroTheme(darkTheme = darkTheme) {
        Surface(
            modifier = Modifier.fillMaxSize(),
            color = MaterialTheme.colorScheme.background,
        ) {
            content()
        }
    }
}

/**
 * 简化的预览包装函数
 *
 * 提供最小化配置的主题包装
 */
@Composable
fun SimplePreview(
    content: @Composable () -> Unit,
) {
    GymBroTheme {
        Box(modifier = Modifier.fillMaxSize()) {
            content()
        }
    }
}

/**
 * 浅色主题预览包装函数
 */
@Preview(name = "浅色模式")
@Composable
private fun lightThemePreviewExample() {
    ThemedPreview(darkTheme = false) {
        // 示例内容 - 在实际使用中替换为你的组件
    }
}

/**
 * 深色主题预览包装函数
 */
@Preview(name = "深色模式")
@Composable
private fun darkThemePreviewExample() {
    ThemedPreview(darkTheme = true) {
        // 示例内容 - 在实际使用中替换为你的组件
    }
}

/**
 * 示例预览函数，展示如何使用GymBroPreview注解
 */
@GymBroPreview
@Composable
private fun gymBroPreviewExample() {
    GymBroTheme {
        Surface {
            // 这里放置你的组件内容
            // 例如：MyComponent()
        }
    }
}

/**
 * 示例扩展预览函数，展示如何使用GymBroPreviewExtended注解
 */
@GymBroPreviewExtended
@Composable
private fun gymBroExtendedPreviewExample() {
    GymBroTheme {
        Surface {
            // 这里放置你的组件内容，支持横屏、平板等多种配置
        }
    }
}

/**
 * 示例组件预览函数，展示如何使用GymBroComponentPreview注解
 */
@GymBroComponentPreview
@Composable
private fun gymBroComponentPreviewExample() {
    GymBroTheme {
        Surface {
            // 这里放置你的小组件内容
        }
    }
}
