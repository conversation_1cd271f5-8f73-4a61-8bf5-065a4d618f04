package com.example.gymbro.core.ai.prompt.trigger

import com.example.gymbro.core.error.types.ModernResult

/**
 * 智能触发引擎接口
 *
 * 实现自动/手动触发策略，提升AI Function Call的触发准确率
 * 基于用户输入智能判断是否需要触发Function Call
 *
 * 触发策略：
 * 1. 用户句子检测：识别包含动作相关关键词的句子
 * 2. ID直查：检测明确的动作ID，直接查询详情
 * 3. Fallback RAG：兜底机制，为缺乏动作数据的回答补充信息
 * 4. 智能推荐：基于上下文推荐相关Function Call
 *
 * 检测规则：
 * - 动作搜索：包含"动作"、"练XXX"、"用XXX"等关键词
 * - 训练会话：包含"开始"、"记录"、"完成"等动作词
 * - 模板生成：包含"模板"、"计划"、"生成"等词汇
 *
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0 (P6阶段动作库Function Call链路)
 */
interface SmartTriggerEngine {

    /**
     * 分析用户输入，判断是否需要触发Function Call
     *
     * @param userInput 用户输入文本
     * @param context 对话上下文
     * @return 触发建议
     */
    suspend fun analyzeTriggerNeed(
        userInput: String,
        context: ConversationContext? = null,
    ): TriggerAnalysisResult

    /**
     * 执行Fallback RAG
     * 为缺乏动作数据的AI回答补充相关信息
     */
    suspend fun executeFallbackRAG(
        query: String,
        maxResults: Int = 3,
    ): ModernResult<List<AiExerciseInfo>>

    /**
     * 获取触发统计信息
     */
    fun getTriggerStats(): TriggerStats
}

/**
 * 对话上下文
 */
data class ConversationContext(
    val lastUserInput: String? = null,
    val lastAIResponse: String? = null,
    val sessionActive: Boolean = false,
    val recentFunctionCalls: List<String> = emptyList(),
)

/**
 * 触发分析结果密封类
 */
sealed class TriggerAnalysisResult {
    /**
     * 无需触发
     */
    data class NoTrigger(val reason: String) : TriggerAnalysisResult()

    /**
     * 触发Function Call
     */
    data class FunctionCall(
        val functionName: String,
        val arguments: Map<String, String>,
        val confidence: Double,
        val reason: String,
    ) : TriggerAnalysisResult()

    /**
     * 直接查询（如ID查询）
     */
    data class DirectQuery(
        val functionName: String,
        val arguments: Map<String, String>,
        val confidence: Double,
        val reason: String,
    ) : TriggerAnalysisResult()

    /**
     * 触发Fallback RAG
     */
    data class FallbackRAG(
        val query: String,
        val confidence: Double,
        val reason: String,
    ) : TriggerAnalysisResult()
}

/**
 * 触发统计信息
 */
data class TriggerStats(
    val totalAnalyses: Long,
    val functionCallTriggers: Long,
    val fallbackRAGTriggers: Long,
    val noTriggers: Long,
    val averageConfidence: Double,
)

/**
 * AI动作信息（简化版本，避免对domain模块的依赖）
 */
data class AiExerciseInfo(
    val id: String,
    val name: String,
    val muscleGroup: String,
    val equipment: String,
    val difficulty: String,
)
