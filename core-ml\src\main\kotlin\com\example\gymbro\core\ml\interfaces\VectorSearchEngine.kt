package com.example.gymbro.core.ml.interfaces

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ml.model.CandidateVector
import com.example.gymbro.core.ml.model.SearchEngineStats
import com.example.gymbro.core.ml.model.SearchWeightConfig
import com.example.gymbro.core.ml.model.VectorSearchResult

/**
 * 向量搜索引擎接口 - Core-ML Layer
 *
 * 负责在向量空间中进行相似度搜索，支持多种搜索策略
 * 这是纯算法抽象接口，不依赖任何上层模块
 */
interface VectorSearchEngine {

    /**
     * 基于查询向量搜索最相似的候选项
     *
     * @param queryVector 查询向量
     * @param candidates 候选向量列表
     * @param topK 返回前K个最相似的结果
     * @param threshold 相似度阈值，低于此值的结果将被过滤
     * @return 搜索结果列表，按相似度降序排列
     */
    suspend fun searchSimilar(
        queryVector: FloatArray,
        candidates: List<CandidateVector>,
        topK: Int = 5,
        threshold: Float = 0.0f,
    ): ModernResult<List<VectorSearchResult>>

    /**
     * 混合搜索：同时考虑向量相似度和其他因子
     *
     * @param queryVector 查询向量
     * @param candidates 候选向量列表
     * @param weightConfig 权重配置
     * @param topK 返回前K个结果
     * @return 混合搜索结果
     */
    suspend fun hybridSearch(
        queryVector: FloatArray,
        candidates: List<CandidateVector>,
        weightConfig: SearchWeightConfig,
        topK: Int = 5,
    ): ModernResult<List<VectorSearchResult>>

    /**
     * 批量向量搜索
     *
     * @param queries 多个查询向量
     * @param candidates 候选向量列表
     * @param topK 每个查询返回的结果数量
     * @return 批量搜索结果
     */
    suspend fun batchSearch(
        queries: List<FloatArray>,
        candidates: List<CandidateVector>,
        topK: Int = 5,
    ): ModernResult<List<List<VectorSearchResult>>>

    /**
     * 添加向量到搜索引擎（用于增量索引）
     *
     * @param vectors 要添加的向量列表
     * @return 添加结果
     */
    suspend fun addVectors(vectors: List<CandidateVector>): ModernResult<Unit>

    /**
     * 获取搜索引擎统计信息
     */
    fun getSearchStats(): SearchEngineStats
}

// 数据类已移至 com.example.gymbro.core.ml.model 包
