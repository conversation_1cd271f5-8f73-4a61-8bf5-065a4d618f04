package com.example.gymbro.data.shared.monitoring

import com.example.gymbro.data.ai.monitoring.AiMetricsCollector
import com.example.gymbro.data.ai.monitoring.MetricsSnapshot
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 指标暴露端点
 *
 * 为 Prometheus 提供指标数据的简单端点
 * Phase 5 Task 4: Grafana 监控设置
 *
 * 注意：在生产环境中，建议使用专门的监控库如 Micrometer
 * 这里提供一个简化的实现用于演示
 */
@Singleton
class MetricsEndpoint
@Inject
constructor(
    private val aiMetricsCollector: AiMetricsCollector,
) {
    /**
     * 生成 Prometheus 格式的指标数据
     *
     * @return Prometheus 格式的指标字符串
     */
    fun getPrometheusMetrics(): String {
        val snapshot = aiMetricsCollector.getMetricsSnapshot()

        return buildString {
            // Goal Match Rate
            appendLine("# HELP gymbro_goal_match_rate AI goal match rate")
            appendLine("# TYPE gymbro_goal_match_rate gauge")
            appendLine("gymbro_goal_match_rate ${snapshot.goalMatchRate}")
            appendLine()

            // Preference Injection Rate
            appendLine("# HELP gymbro_preference_injection_rate User preference injection success rate")
            appendLine("# TYPE gymbro_preference_injection_rate gauge")
            appendLine("gymbro_preference_injection_rate ${snapshot.preferenceInjectionRate}")
            appendLine()

            // AI Response Time
            appendLine("# HELP gymbro_ai_response_time_avg_ms Average AI response time in milliseconds")
            appendLine("# TYPE gymbro_ai_response_time_avg_ms gauge")
            appendLine("gymbro_ai_response_time_avg_ms ${snapshot.averageResponseTimeMs}")
            appendLine()

            appendLine("# HELP gymbro_ai_response_time_max_ms Maximum AI response time in milliseconds")
            appendLine("# TYPE gymbro_ai_response_time_max_ms gauge")
            appendLine("gymbro_ai_response_time_max_ms ${snapshot.maxResponseTimeMs}")
            appendLine()

            // User Preference Coverage
            appendLine("# HELP gymbro_user_preference_coverage_rate User preference coverage rate")
            appendLine("# TYPE gymbro_user_preference_coverage_rate gauge")
            appendLine("gymbro_user_preference_coverage_rate ${snapshot.userPreferenceCoverageRate}")
            appendLine()

            // Total Requests
            appendLine("# HELP gymbro_ai_requests_total Total number of AI requests")
            appendLine("# TYPE gymbro_ai_requests_total counter")
            appendLine("gymbro_ai_requests_total ${snapshot.totalRequests}")
            appendLine()

            // Goal Matches by Type
            appendLine("# HELP gymbro_goal_matches_by_type Goal matches by fitness goal type")
            appendLine("# TYPE gymbro_goal_matches_by_type counter")
            snapshot.goalMatchesByType.forEach { (goal, count) ->
                appendLine("gymbro_goal_matches_by_type{goal=\"$goal\"} $count")
            }
            appendLine()
        }
    }

    /**
     * 获取健康检查状态
     *
     * @return 健康状态信息
     */
    fun getHealthStatus(): HealthStatus {
        val snapshot = aiMetricsCollector.getMetricsSnapshot()

        val isHealthy =
            snapshot.goalMatchRate >= 0.8 &&
                snapshot.preferenceInjectionRate >= 0.9 &&
                snapshot.averageResponseTimeMs < 10000 // 10秒

        return HealthStatus(
            status = if (isHealthy) "UP" else "DOWN",
            goalMatchRate = snapshot.goalMatchRate,
            preferenceInjectionRate = snapshot.preferenceInjectionRate,
            averageResponseTimeMs = snapshot.averageResponseTimeMs,
            checks =
            mapOf(
                "goal_match_rate" to (snapshot.goalMatchRate >= 0.8),
                "preference_injection_rate" to (snapshot.preferenceInjectionRate >= 0.9),
                "response_time" to (snapshot.averageResponseTimeMs < 10000),
            ),
        )
    }

    /**
     * 获取详细的指标报告
     *
     * @return 详细指标报告
     */
    fun getDetailedReport(): DetailedMetricsReport {
        val snapshot = aiMetricsCollector.getMetricsSnapshot()

        return DetailedMetricsReport(
            timestamp = System.currentTimeMillis(),
            goalMatchRate = snapshot.goalMatchRate,
            preferenceInjectionRate = snapshot.preferenceInjectionRate,
            averageResponseTimeMs = snapshot.averageResponseTimeMs,
            maxResponseTimeMs = snapshot.maxResponseTimeMs,
            userPreferenceCoverageRate = snapshot.userPreferenceCoverageRate,
            totalRequests = snapshot.totalRequests,
            goalMatchesByType = snapshot.goalMatchesByType,
            recommendations = generateRecommendations(snapshot),
        )
    }

    /**
     * 生成基于指标的建议
     */
    private fun generateRecommendations(snapshot: MetricsSnapshot): List<String> {
        val recommendations = mutableListOf<String>()

        if (snapshot.goalMatchRate < 0.8) {
            recommendations.add("目标匹配率低于80%，建议优化AI Prompt模板或训练数据")
        }

        if (snapshot.preferenceInjectionRate < 0.9) {
            recommendations.add("偏好注入率低于90%，检查用户偏好获取逻辑")
        }

        if (snapshot.averageResponseTimeMs > 5000) {
            recommendations.add("AI响应时间超过5秒，考虑优化模型或增加缓存")
        }

        if (snapshot.userPreferenceCoverageRate < 0.5) {
            recommendations.add("用户偏好覆盖率低于50%，加强用户引导设置偏好")
        }

        if (recommendations.isEmpty()) {
            recommendations.add("所有指标正常，系统运行良好")
        }

        return recommendations
    }

    /**
     * 记录指标查询日志
     */
    fun logMetricsAccess(
        endpoint: String,
        userAgent: String? = null,
    ) {
        Timber.d("指标端点访问: endpoint=$endpoint, userAgent=$userAgent")
    }
}

/**
 * 健康状态数据类
 */
data class HealthStatus(
    val status: String,
    val goalMatchRate: Double,
    val preferenceInjectionRate: Double,
    val averageResponseTimeMs: Double,
    val checks: Map<String, Boolean>,
)

/**
 * 详细指标报告数据类
 */
data class DetailedMetricsReport(
    val timestamp: Long,
    val goalMatchRate: Double,
    val preferenceInjectionRate: Double,
    val averageResponseTimeMs: Double,
    val maxResponseTimeMs: Double,
    val userPreferenceCoverageRate: Double,
    val totalRequests: Long,
    val goalMatchesByType: Map<String, Long>,
    val recommendations: List<String>,
)
