<?xml version="1.0" encoding="utf-8"?>
<resources>
  <!-- 认证模块字符串资源 -->
  <!-- 账户相关选项 -->
  <string name="already_have_account">已有账号?</string>
  <!-- 匿名用户升级 -->
  <string name="anonymous_upgrade_email_description">请设置邮箱和密码来升级您的匿名账户。升级后，您可以使用邮箱和密码登录，并保留您的所有数据。</string>
  <string name="anonymous_upgrade_phone_description">请输入您的手机号并验证，将匿名账户升级为手机账户。升级后，您可以使用手机号登录，并保留您的所有数据。</string>
  <string name="anonymous_upgrade_title">升级匿名账户</string>
  <!-- App Info -->
  <string name="app_logo_description">应用程序标志</string>
  <string name="auth_consent_message">登录或注册即表示您同意我们的%1$s和%2$s</string> <!-- 重命名自 privacy_policy -->
  <string name="auto_create_account">未注册的手机号将自动创建账号</string>
  <!-- 页面标题和状态 -->
  <string name="checking_auth_status">正在检查登录状态...</string>
  <!-- 验证码相关 -->
  <string name="code_hint">输入验证码</string>
  <string name="code_send_failure">验证码发送失败</string>
  <string name="code_send_success">验证码发送成功</string>
  <string name="confirm_password">确认密码</string>
  <string name="confirm_password_hint">请再次输入密码</string>
  <!-- 客服和支持 -->
  <string name="contact_customer_support">联系客服</string>
  <!-- 匿名和其他选项 -->
  <string name="continue_anonymously">以访客身份继续</string>
  <string name="country_code_label">+86</string>
  <!-- 基础认证字段 -->
  <string name="email">邮箱</string>
  <string name="email_hint"><EMAIL></string>
  <string name="email_upgrade_tab">邮箱升级</string>
  <string name="forgot_password">忘记密码？</string>
  <string name="get_verification_code">获取验证码</string>
  <!-- 数据警告 -->
  <string name="guest_data_warning">匿名账号数据仅保存在本设备，不会同步到云端</string>
  <string name="guest_mode">访客模式</string>
  <string name="guest_user_display_name">游客</string>
  <string name="invalid_verification_code">请输入6位数验证码</string>
  <string name="login">登录</string>
  <string name="login_as_guest">匿名使用</string>
  <string name="login_button">登录</string>
  <string name="login_code_sent">验证码已发送</string>
  <string name="login_title">欢迎回来</string>
  <string name="login_verifying">正在验证…</string>
  <!-- 认证方式选择 -->
  <string name="login_with_email">邮箱登录</string>
  <string name="login_with_facebook">使用脸书账号登录</string>
  <string name="login_with_google">使用Google登录</string>
  <string name="login_with_password">使用密码登录</string>
  <string name="login_with_phone">手机号登录</string>
  <string name="login_with_verification_code">使用验证码登录</string>
  <string name="login_with_wechat">使用微信登录</string>
  <string name="more_login_options">更多登录方式</string>
  <string name="more_options">更多选项</string>
  <string name="no_account">还没有账号？</string>
  <string name="no_account_register">没有账号？去注册</string>
  <!-- 分隔符和装饰 -->
  <string name="or_divider">或</string>
  <string name="other_login_options">其他登录选项</string>
  <string name="password">密码</string>
  <string name="password_hint">请输入密码</string>
  <string name="password_requirements">密码要求：至少6个字符，包含数字和字母</string>
  <!-- 权限请求 -->
  <string name="permission_request_message">为了提供完整功能，应用需要以下权限</string>
  <string name="permission_request_title">需要权限</string>
  <string name="phone_hint">请输入手机号</string>
  <string name="phone_login_title">手机号登录</string>
  <string name="phone_number">手机号码</string>
  <string name="phone_upgrade_tab">手机升级</string>
  <!-- 隐私政策和服务条款 -->
  <string name="privacy_agreement">隐私政策</string> <!-- 保留 -->
  <string name="register">注册</string>
  <string name="register_account">注册账号</string>
  <string name="register_button">注册</string>
  <string name="register_login_option">已有账号? 去登录</string>
  <string name="register_title">创建账号</string>
  <!-- 区域相关 -->
  <string name="region_mismatch_confirm_new">使用 %1$s</string>
  <string name="region_mismatch_ignore">忽略</string>
  <string name="region_mismatch_message">我们检测到您当前的区域 (%2$s) 与之前记录的区域 (%1$s)不符。是否更新到新区域？</string>
  <string name="region_mismatch_title">区域不一致</string>
  <string name="report_issue">报告问题</string>
  <string name="resend_code">重新发送</string>
  <string name="resend_code_countdown">%1$d秒后重新发送</string>
  <string name="resend_code_countdown_short">%1$d s</string>
  <!-- 认证操作按钮 -->
  <string name="send_code">发送验证码</string>
  <string name="send_verification_code">发送验证码</string>
  <string name="terms_agreement">继续操作即表示您同意我们的服务条款和隐私政策</string>
  <string name="terms_of_service">服务条款</string> <!-- 保留 -->
  <string name="upgrade_account_button">升级账户</string>
  <string name="use_email">使用邮箱登录</string>
  <string name="use_phone">使用手机号登录</string>
  <string name="verification_code">验证码</string>
  <string name="verify">验证</string>
  <!-- GymBro UI 相关 -->
  <string name="app_subtitle">Android Beta</string>
  <string name="understand_universe">理解健身宇宙_</string>
</resources>
