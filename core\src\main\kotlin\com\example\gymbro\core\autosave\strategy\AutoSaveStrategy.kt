package com.example.gymbro.core.autosave.strategy

import kotlinx.coroutines.CoroutineScope

/**
 * 自动保存策略接口
 *
 * @param T 要保存的数据类型
 */
interface AutoSaveStrategy<T : Any> {

    /**
     * 启动保存策略
     *
     * @param scope 协程作用域
     * @param onSave 保存回调
     * @param onError 错误回调
     */
    fun start(
        scope: CoroutineScope,
        onSave: suspend () -> Result<Unit>,
        onError: (Throwable) -> Unit,
    )

    /**
     * 数据变更通知
     *
     * @param oldData 旧数据
     * @param newData 新数据
     */
    fun onDataChanged(oldData: T?, newData: T)

    /**
     * 停止保存策略
     */
    fun stop()
}
