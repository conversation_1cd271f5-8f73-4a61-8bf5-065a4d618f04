package com.example.gymbro.data.shared.common

/**
 * 标记敏感数据的注解
 *
 * 用于标记那些包含敏感用户信息的数据字段，这些字段需要额外的处理和保护：
 * 1. 不应该被日志系统记录
 * 2. 可能需要在存储前加密
 * 3. 应当在UI显示时进行掩码处理
 * 4. 可能需要更严格的访问控制
 */
@Target(AnnotationTarget.FIELD, AnnotationTarget.PROPERTY)
@Retention(AnnotationRetention.RUNTIME)
annotation class SensitiveData(
    /**
     * 敏感数据的类型
     */
    val type: SensitiveType = SensitiveType.GENERAL,
)

/**
 * 敏感数据类型
 */
enum class SensitiveType {
    /**
     * 一般敏感数据
     */
    GENERAL,

    /**
     * 财务信息
     */
    FINANCIAL,
}
