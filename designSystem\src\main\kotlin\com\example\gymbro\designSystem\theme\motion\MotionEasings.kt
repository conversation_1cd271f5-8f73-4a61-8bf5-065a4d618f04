package com.example.gymbro.designSystem.theme.motion

import androidx.compose.animation.core.*

/**
 * GymBro动画缓动函数Token系统
 *
 * 提供统一的缓动函数定义，使用标准Material 3定义
 * 确保动画的一致性和流畅性
 */
object MotionEasings {
    // === 标准缓动函数（使用Material 3定义）===
    val STANDARD = FastOutSlowInEasing
    val LINEAR = LinearEasing
    val EMPHASIZE = CubicBezierEasing(0.2f, 0.0f, 0.0f, 1.0f)
    val DECELERATE = FastOutLinearInEasing
    val ACCELERATE = LinearOutSlowInEasing

    // === 语义化缓动函数 ===
    val MICRO_INTERACTION = STANDARD // 微交互使用标准缓动
    val PAGE_TRANSITION = STANDARD // 页面过渡使用标准缓动
    val CONTENT_TRANSITION = STANDARD // 内容切换使用标准缓动
    val DECORATIVE = LINEAR // 装饰性动画使用线性缓动
    val LONG_CYCLE = LINEAR // 长时间循环使用线性缓动

    // === 组件特定缓动函数 ===
    object Component {
        // 按钮动画
        val BUTTON_PRESS = STANDARD
        val BUTTON_HOVER = DECELERATE
        val BUTTON_LOADING = LINEAR

        // 卡片动画
        val CARD_ENTER = STANDARD
        val CARD_HOVER = DECELERATE
        val CARD_PRESS = STANDARD

        // 输入框动画
        val INPUT_FOCUS = STANDARD
        val INPUT_ERROR_SHAKE = EMPHASIZE
        val INPUT_LABEL_FLOAT = STANDARD

        // 页面动画
        val PAGE_ENTER = STANDARD
        val PAGE_EXIT = ACCELERATE
        val MODAL_ENTER = STANDARD
        val MODAL_EXIT = ACCELERATE

        // 进度指示器
        val PROGRESS_ROTATION = LINEAR
        val LOADING_ROTATION = LINEAR

        // 对话框动画
        val DIALOG_ENTER = STANDARD
        val DIALOG_EXIT = ACCELERATE

        // 底部表单动画
        val BOTTOM_SHEET_ENTER = STANDARD
        val BOTTOM_SHEET_EXIT = ACCELERATE
    }

    // === 模块专用缓动函数 ===
    object Profile {
        val THEME_SWITCH = STANDARD
        val THEME_PREVIEW = STANDARD
        val SETTING_ITEM_PRESS = STANDARD
        val SETTING_ITEM_HOVER = DECELERATE
        val AVATAR_CHANGE = STANDARD
        val AVATAR_HOVER = STANDARD
        val HEADER_ENTER = DECELERATE
        val LOADING_INDICATOR = LINEAR
        val LIST_ITEM_ENTER = STANDARD
        val LIST_ITEM_EXIT = ACCELERATE
        val MODAL_SLIDE_IN = STANDARD
        val MODAL_SLIDE_OUT = ACCELERATE
    }

    object Coach {
        val MESSAGE_ENTER = STANDARD
        val MESSAGE_EXIT = STANDARD
        val SEND_BUTTON_ROTATION = LINEAR
        val INPUT_BORDER_ANIMATION = STANDARD
        val INPUT_STREAMING_ANIMATION = STANDARD
        val AI_STREAMING_PULSE = LINEAR
        val AI_TYPING_INDICATOR = STANDARD
        val PAGE_TRANSITION = STANDARD
        val QUICK_TRANSITION = STANDARD
        val BUBBLE_ANIMATION = STANDARD
        val RIPPLE_EFFECT = STANDARD
    }

    object Workout {
        val CARD_ENTER = STANDARD
        val CARD_PRESS = STANDARD
        val NAVIGATION_ENTER = STANDARD
        val NAVIGATION_EXIT = STANDARD
        val CROSS_MODULE_TRANSITION = STANDARD
        val TIMER_ANIMATION = STANDARD
    }

    // === 特殊效果缓动函数 ===
    object Special {
        // 弹性效果
        val BOUNCY = CubicBezierEasing(0.68f, -0.55f, 0.265f, 1.55f)

        // 急停效果
        val SHARP_STOP = CubicBezierEasing(0.4f, 0.0f, 1.0f, 1.0f)

        // 缓慢启动
        val SLOW_START = CubicBezierEasing(0.0f, 0.0f, 0.2f, 1.0f)

        // 快速启动
        val FAST_START = CubicBezierEasing(0.4f, 0.0f, 1.0f, 1.0f)
    }

    // === 自定义缓动函数构建器 ===

    /**
     * 创建自定义三次贝塞尔缓动函数
     */
    fun customCubicBezier(
        x1: Float,
        y1: Float,
        x2: Float,
        y2: Float,
    ): Easing = CubicBezierEasing(x1, y1, x2, y2)

    /**
     * 创建阶梯缓动函数
     */
    fun steps(steps: Int): Easing {
        return Easing { fraction ->
            val step = (fraction * steps).toInt()
            step.toFloat() / steps
        }
    }

    /**
     * 创建反向缓动函数
     */
    fun reverse(easing: Easing): Easing {
        return Easing { fraction ->
            1f - easing.transform(1f - fraction)
        }
    }

    /**
     * 创建组合缓动函数
     */
    fun combine(
        firstEasing: Easing,
        secondEasing: Easing,
        splitPoint: Float = 0.5f,
    ): Easing {
        return Easing { fraction ->
            if (fraction <= splitPoint) {
                firstEasing.transform(fraction / splitPoint) * splitPoint
            } else {
                val adjustedFraction = (fraction - splitPoint) / (1f - splitPoint)
                splitPoint + secondEasing.transform(adjustedFraction) * (1f - splitPoint)
            }
        }
    }
}
