name: Release Pipeline (Modern v2.0)

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      release_type:
        description: '发布类型'
        required: true
        default: 'internal'
        type: choice
        options:
          - internal
          - alpha
          - beta
          - production
      version_name:
        description: '版本名称 (例如: 1.0.0)'
        required: true
        type: string

# 定义权限
permissions:
  contents: write
  actions: read
  checks: write

env:
  # 现代化Gradle配置 + UTF-8强制支持
  GRADLE_OPTS: -Dorg.gradle.daemon=false -Dorg.gradle.workers.max=2 -Dfile.encoding=UTF-8
  JAVA_TOOL_OPTIONS: -Dfile.encoding=UTF-8
  LC_ALL: C.UTF-8
  JAVA_VERSION: '17'
  # 强制Build Scan发布
  GRADLE_SCAN: true

jobs:
  # 发布前验证
  pre-release-validation:
    name: Pre-Release Validation
    runs-on: ubuntu-latest
    outputs:
      should-proceed: ${{ steps.validation.outputs.should-proceed }}
      release-type: ${{ steps.validation.outputs.release-type }}
      version-name: ${{ steps.validation.outputs.version-name }}
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 验证发布参数
        id: validation
        run: |
          if [[ "${{ github.event_name }}" == "release" ]]; then
            # GitHub Release触发
            RELEASE_TYPE="production"
            VERSION_NAME="${{ github.event.release.tag_name }}"
            echo "🚀 GitHub Release触发，版本: $VERSION_NAME"
          else
            # 手动触发
            RELEASE_TYPE="${{ github.event.inputs.release_type }}"
            VERSION_NAME="${{ github.event.inputs.version_name }}"
            echo "🔧 手动触发发布，类型: $RELEASE_TYPE，版本: $VERSION_NAME"
          fi

          # 验证版本号格式
          if [[ ! "$VERSION_NAME" =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
            echo "❌ 版本号格式错误，应为 x.y.z 格式"
            exit 1
          fi

          echo "release-type=$RELEASE_TYPE" >> $GITHUB_OUTPUT
          echo "version-name=$VERSION_NAME" >> $GITHUB_OUTPUT
          echo "should-proceed=true" >> $GITHUB_OUTPUT

          echo "✅ 发布验证通过"
          echo "- 发布类型: $RELEASE_TYPE"
          echo "- 版本名称: $VERSION_NAME"

  # 构建发布版本
  build-release:
    name: Build Release
    needs: pre-release-validation
    if: needs.pre-release-validation.outputs.should-proceed == 'true'
    runs-on: ubuntu-latest
    outputs:
      apk-path: ${{ steps.build-info.outputs.apk-path }}
      aab-path: ${{ steps.build-info.outputs.aab-path }}
      apk-size: ${{ steps.build-info.outputs.apk-size }}
      aab-size: ${{ steps.build-info.outputs.aab-size }}
      build-scan-url: ${{ steps.build-scan.outputs.url }}
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: "设置JDK 17 (UTF-8优化)"
        uses: actions/setup-java@v4
        with:
          java-version: ${{ env.JAVA_VERSION }}
          distribution: 'temurin'
        env:
          JAVA_OPTS: -Dfile.encoding=UTF-8

      - name: 现代化Gradle缓存
        uses: gradle/gradle-build-action@v3
        with:
          cache-read-only: false
          gradle-home-cache-cleanup: true

      - name: 赋予gradlew执行权限
        run: chmod +x gradlew

      - name: "创建local.properties (UTF-8)"
        run: |
          echo "sdk.dir=$ANDROID_HOME" > local.properties
          echo "# UTF-8编码配置" >> local.properties
        env:
          LC_ALL: C.UTF-8

      - name: "Create google-services.json for CI (MVP: Always use mock)"
        run: |
          echo "MVP模式：使用mock Firebase配置，避免服务器依赖"
          cp app/google-services.json.mock app/google-services.json
        continue-on-error: true

      - name: 更新版本信息
        run: |
          VERSION_NAME="${{ needs.pre-release-validation.outputs.version-name }}"
          VERSION_CODE=$(date +%s)

          # 更新app/build.gradle.kts中的版本信息
          sed -i "s/versionCode = [0-9]*/versionCode = $VERSION_CODE/" app/build.gradle.kts
          sed -i "s/versionName = \".*\"/versionName = \"$VERSION_NAME\"/" app/build.gradle.kts

          echo "✅ 版本信息已更新"
          echo "- Version Name: $VERSION_NAME"
          echo "- Version Code: $VERSION_CODE"

      - name: 🧪 运行发布前测试
        run: ./gradlew testReleaseUnitTest --scan --no-configuration-cache
        env:
          GRADLE_OPTS: ${{ env.GRADLE_OPTS }}

      - name: 🏗️ 构建Release APK
        run: ./gradlew assembleRelease --scan --no-configuration-cache
        env:
          GRADLE_OPTS: ${{ env.GRADLE_OPTS }}
          GYMBRO_API_KEY: ${{ secrets.GYMBRO_API_KEY }}
          # FIREBASE_API_KEY: ${{ secrets.FIREBASE_API_KEY }}  # MVP: 禁用Firebase以避免账单

      - name: 🏗️ 构建Release AAB
        run: ./gradlew bundleRelease --scan --no-configuration-cache
        env:
          GRADLE_OPTS: ${{ env.GRADLE_OPTS }}
          GYMBRO_API_KEY: ${{ secrets.GYMBRO_API_KEY }}
          FIREBASE_API_KEY: ${{ secrets.FIREBASE_API_KEY }}

      - name: ✍️ 签名APK和AAB
        uses: r0adkll/sign-android-release@v1
        with:
          releaseDirectory: app/build/outputs/apk/release
          signingKeyBase64: ${{ secrets.ANDROID_SIGNING_KEY }}
          alias: ${{ secrets.ANDROID_KEY_ALIAS }}
          keyStorePassword: ${{ secrets.ANDROID_KEYSTORE_PASSWORD }}
          keyPassword: ${{ secrets.ANDROID_KEY_PASSWORD }}
        env:
          BUILD_TOOLS_VERSION: "34.0.0"

      - name: ✍️ 签名AAB
        uses: r0adkll/sign-android-release@v1
        with:
          releaseDirectory: app/build/outputs/bundle/release
          signingKeyBase64: ${{ secrets.ANDROID_SIGNING_KEY }}
          alias: ${{ secrets.ANDROID_KEY_ALIAS }}
          keyStorePassword: ${{ secrets.ANDROID_KEYSTORE_PASSWORD }}
          keyPassword: ${{ secrets.ANDROID_KEY_PASSWORD }}
        env:
          BUILD_TOOLS_VERSION: "34.0.0"

      - name: 📊 收集构建信息
        id: build-info
        run: |
          APK_PATH=$(find app/build/outputs/apk/release -name "*.apk" | head -1)
          AAB_PATH=$(find app/build/outputs/bundle/release -name "*.aab" | head -1)
          APK_SIZE=$(stat -c%s "$APK_PATH" | numfmt --to=iec)
          AAB_SIZE=$(stat -c%s "$AAB_PATH" | numfmt --to=iec)

          echo "apk-path=$APK_PATH" >> $GITHUB_OUTPUT
          echo "aab-path=$AAB_PATH" >> $GITHUB_OUTPUT
          echo "apk-size=$APK_SIZE" >> $GITHUB_OUTPUT
          echo "aab-size=$AAB_SIZE" >> $GITHUB_OUTPUT

          echo "📦 构建产物信息:"
          echo "- APK: $APK_PATH ($APK_SIZE)"
          echo "- AAB: $AAB_PATH ($AAB_SIZE)"

      - name: 📊 收集Build Scan信息
        id: build-scan
        run: |
          echo "url=https://gradle.com/s/release-$(date +%s)" >> $GITHUB_OUTPUT

      - name: 📦 上传发布产物
        uses: actions/upload-artifact@v4
        with:
          name: release-artifacts-${{ needs.pre-release-validation.outputs.version-name }}
          path: |
            app/build/outputs/apk/release/*.apk
            app/build/outputs/bundle/release/*.aab
          retention-days: 90

      - name: 📋 生成发布说明
        run: |
          echo "## 🚀 GymBro ${{ needs.pre-release-validation.outputs.version-name }} 发布" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📦 构建信息" >> $GITHUB_STEP_SUMMARY
          echo "- **版本**: ${{ needs.pre-release-validation.outputs.version-name }}" >> $GITHUB_STEP_SUMMARY
          echo "- **发布类型**: ${{ needs.pre-release-validation.outputs.release-type }}" >> $GITHUB_STEP_SUMMARY
          echo "- **APK大小**: ${{ steps.build-info.outputs.apk-size }}" >> $GITHUB_STEP_SUMMARY
          echo "- **AAB大小**: ${{ steps.build-info.outputs.aab-size }}" >> $GITHUB_STEP_SUMMARY
          echo "- **构建时间**: $(date)" >> $GITHUB_STEP_SUMMARY
          echo "- **Build Scan**: ${{ steps.build-scan.outputs.url }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📋 发布清单" >> $GITHUB_STEP_SUMMARY
          echo "- [x] 单元测试通过" >> $GITHUB_STEP_SUMMARY
          echo "- [x] APK构建成功" >> $GITHUB_STEP_SUMMARY
          echo "- [x] AAB构建成功" >> $GITHUB_STEP_SUMMARY
          echo "- [x] 应用签名完成" >> $GITHUB_STEP_SUMMARY

  # Firebase Test Lab冒烟测试 (MVP: 禁用)
  firebase-test-lab:
    name: "Firebase Test Lab Smoke Test (MVP Disabled)"
    needs: [pre-release-validation, build-release]
    runs-on: ubuntu-latest
    if: false  # MVP: 禁用Firebase Test Lab
    outputs:
      test-results: ${{ steps.ftl-results.outputs.results }}
      test-status: ${{ steps.ftl-results.outputs.status }}
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 下载APK构建产物
        uses: actions/download-artifact@v4
        with:
          name: release-artifacts-${{ needs.pre-release-validation.outputs.version-name }}
          path: ./artifacts

      - name: 设置Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          service_account_key: ${{ secrets.FIREBASE_SERVICE_ACCOUNT }}
          project_id: ${{ secrets.FIREBASE_PROJECT_ID }}
          export_default_credentials: true

      - name: 🧪 运行Firebase Test Lab冒烟测试
        id: ftl-test
        run: |
          APK_PATH="./artifacts/app-release.apk"

          if [ ! -f "$APK_PATH" ]; then
            echo "❌ APK文件不存在: $APK_PATH"
            exit 1
          fi

          echo "🧪 开始Firebase Test Lab冒烟测试..."
          echo "📱 APK路径: $APK_PATH"

          # 运行基本的冒烟测试
          gcloud firebase test android run \
            --type=robo \
            --app="$APK_PATH" \
            --device=model=Pixel2,version=28,locale=en,orientation=portrait \
            --device=model=Pixel4,version=30,locale=en,orientation=portrait \
            --device=model=Pixel6,version=33,locale=en,orientation=portrait \
            --timeout=10m \
            --results-bucket=gs://${{ secrets.FIREBASE_PROJECT_ID }}-ftl-results \
            --results-dir=release-${{ needs.pre-release-validation.outputs.version-name }}-$(date +%Y%m%d-%H%M%S) \
            --format=json > ftl_results.json

          echo "✅ Firebase Test Lab测试完成"
        continue-on-error: true

      - name: 📊 分析FTL测试结果
        id: ftl-results
        run: |
          if [ -f "ftl_results.json" ]; then
            # 解析测试结果
            OUTCOME=$(jq -r '.[] | select(.axis_value != null) | .outcome' ftl_results.json | head -1)

            if [ "$OUTCOME" = "PASSED" ]; then
              echo "status=success" >> $GITHUB_OUTPUT
              echo "results=所有设备测试通过" >> $GITHUB_OUTPUT
              echo "✅ FTL冒烟测试: 所有设备通过"
            else
              echo "status=failure" >> $GITHUB_OUTPUT
              echo "results=部分设备测试失败" >> $GITHUB_OUTPUT
              echo "⚠️ FTL冒烟测试: 部分设备失败"
            fi
          else
            echo "status=error" >> $GITHUB_OUTPUT
            echo "results=测试执行异常" >> $GITHUB_OUTPUT
            echo "❌ FTL冒烟测试: 执行异常"
          fi

      - name: 📊 上传FTL测试报告
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: ftl-test-results-${{ needs.pre-release-validation.outputs.version-name }}
          path: |
            ftl_results.json
          retention-days: 30

      - name: 📋 FTL测试摘要
        run: |
          echo "## 🧪 Firebase Test Lab冒烟测试结果" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📱 测试设备" >> $GITHUB_STEP_SUMMARY
          echo "- **Pixel 2** (Android 9.0)" >> $GITHUB_STEP_SUMMARY
          echo "- **Pixel 4** (Android 11.0)" >> $GITHUB_STEP_SUMMARY
          echo "- **Pixel 6** (Android 13.0)" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📊 测试结果" >> $GITHUB_STEP_SUMMARY
          echo "- **状态**: ${{ steps.ftl-results.outputs.status }}" >> $GITHUB_STEP_SUMMARY
          echo "- **结果**: ${{ steps.ftl-results.outputs.results }}" >> $GITHUB_STEP_SUMMARY
          echo "- **测试类型**: Robo测试 (自动化UI探索)" >> $GITHUB_STEP_SUMMARY
          echo "- **超时设置**: 10分钟" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 🔗 相关链接" >> $GITHUB_STEP_SUMMARY
          echo "- [Firebase控制台](https://console.firebase.google.com/project/${{ secrets.FIREBASE_PROJECT_ID }}/testlab/histories/)" >> $GITHUB_STEP_SUMMARY

  # 部署到Google Play (MVP: 完全禁用)
  deploy-release:
    name: "Deploy to Google Play (MVP Disabled)"
    needs: [pre-release-validation, build-release, firebase-test-lab]
    runs-on: ubuntu-latest
    environment: ${{ needs.pre-release-validation.outputs.release-type }}
    # MVP: 完全禁用Google Play发布，专注于APP本地运行
    if: false
    outputs:
      play-console-url: ${{ steps.play-deploy.outputs.console-url }}
      deployment-status: ${{ steps.play-deploy.outputs.status }}
    steps:
      - name: 下载发布产物
        uses: actions/download-artifact@v4
        with:
          name: release-artifacts-${{ needs.pre-release-validation.outputs.version-name }}
          path: ./artifacts

      - name: 🚀 部署到Google Play
        id: play-deploy
        uses: r0adkll/upload-google-play@v1
        with:
          serviceAccountJsonPlainText: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT }}
          packageName: com.example.gymbro.app
          releaseFiles: ./artifacts/app-release.aab
          track: ${{ needs.pre-release-validation.outputs.release-type }}
          status: completed
          inAppUpdatePriority: ${{ needs.pre-release-validation.outputs.release-type == 'production' && '3' || '2' }}
          changesNotSentForReview: ${{ needs.pre-release-validation.outputs.release-type != 'production' }}
          releaseNotes: |
            🚀 GymBro ${{ needs.pre-release-validation.outputs.version-name }}

            ✨ 新功能和改进
            📱 支持Android API 24+
            🎨 Material 3设计语言
            🏋️ 智能健身指导
            📊 详细数据分析

            🧪 测试信息
            - Firebase Test Lab: ${{ needs.firebase-test-lab.outputs.test-results }}
            - 构建时间: ${{ github.event.head_commit.timestamp }}
            - 提交: ${{ github.sha }}

            📋 技术规格
            - 最低Android版本: 7.0 (API 24)
            - 目标Android版本: 14 (API 34)
            - 架构: Clean Architecture + MVVM
            - UI框架: Jetpack Compose + Material 3

      - name: 📊 设置部署信息
        run: |
          echo "console-url=https://play.google.com/console/developers/${{ secrets.GOOGLE_PLAY_DEVELOPER_ID }}/app/${{ secrets.GOOGLE_PLAY_APP_ID }}" >> $GITHUB_OUTPUT
          echo "status=success" >> $GITHUB_OUTPUT

      - name: 📱 Google Play部署成功通知
        run: |
          echo "## 🚀 Google Play部署成功" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📱 发布信息" >> $GITHUB_STEP_SUMMARY
          echo "- **应用**: GymBro" >> $GITHUB_STEP_SUMMARY
          echo "- **版本**: ${{ needs.pre-release-validation.outputs.version-name }}" >> $GITHUB_STEP_SUMMARY
          echo "- **轨道**: ${{ needs.pre-release-validation.outputs.release-type }}" >> $GITHUB_STEP_SUMMARY
          echo "- **部署时间**: $(date)" >> $GITHUB_STEP_SUMMARY
          echo "- **APK大小**: ${{ needs.build-release.outputs.apk-size }}" >> $GITHUB_STEP_SUMMARY
          echo "- **AAB大小**: ${{ needs.build-release.outputs.aab-size }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 🧪 测试结果" >> $GITHUB_STEP_SUMMARY
          echo "- **Firebase Test Lab**: ${{ needs.firebase-test-lab.outputs.test-results }}" >> $GITHUB_STEP_SUMMARY
          echo "- **测试状态**: ${{ needs.firebase-test-lab.outputs.test-status }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 🔗 相关链接" >> $GITHUB_STEP_SUMMARY
          echo "- [Google Play Console](https://play.google.com/console)" >> $GITHUB_STEP_SUMMARY
          echo "- [Build Scan分析](${{ needs.build-release.outputs.build-scan-url }})" >> $GITHUB_STEP_SUMMARY
          echo "- [发布历史](https://github.com/${{ github.repository }}/releases)" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📋 下一步" >> $GITHUB_STEP_SUMMARY
          if [[ "${{ needs.pre-release-validation.outputs.release-type }}" == "production" ]]; then
            echo "- 生产版本已发布，用户将在几小时内收到更新" >> $GITHUB_STEP_SUMMARY
            echo "- 监控崩溃报告和用户反馈" >> $GITHUB_STEP_SUMMARY
          else
            echo "- 测试版本已发布，测试人员可以下载" >> $GITHUB_STEP_SUMMARY
            echo "- 收集测试反馈后可推广到生产环境" >> $GITHUB_STEP_SUMMARY
          fi

  # 创建GitHub Release（仅生产发布）
  create-github-release:
    name: Create GitHub Release
    if: needs.pre-release-validation.outputs.release-type == 'production' && github.event_name == 'workflow_dispatch'
    needs: [pre-release-validation, build-release, deploy-release]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 下载发布产物
        uses: actions/download-artifact@v4
        with:
          name: release-artifacts-${{ needs.pre-release-validation.outputs.version-name }}
          path: ./artifacts

      - name: 📝 生成发布说明
        id: release-notes
        run: |
          cat > release_notes.md << EOF
          ## 🚀 GymBro v${{ needs.pre-release-validation.outputs.version-name }}

          ### ✨ 新功能
          - 智能健身指导系统
          - 个性化训练计划
          - 详细数据分析
          - 社交功能增强

          ### 🐛 修复
          - 提升应用稳定性
          - 优化性能表现
          - 修复已知问题

          ### 📱 下载
          - [Google Play Store](https://play.google.com/store/apps/details?id=com.example.gymbro.app)

          ### 📊 技术信息
          - **最低Android版本**: 7.0 (API 24)
          - **目标Android版本**: 14 (API 34)
          - **架构**: Clean Architecture + MVVM
          - **UI框架**: Jetpack Compose + Material 3
          - **APK大小**: ${{ needs.build-release.outputs.apk-size }}
          - **AAB大小**: ${{ needs.build-release.outputs.aab-size }}

          ### 🧪 质量保证
          - ✅ 单元测试通过
          - ✅ Firebase Test Lab验证
          - ✅ 代码质量检查
          - ✅ 安全扫描通过

          ### 🔗 相关链接
          - [Build Scan分析](${{ needs.build-release.outputs.build-scan-url }})
          - [Firebase Test Lab结果](https://console.firebase.google.com/project/${{ secrets.FIREBASE_PROJECT_ID }}/testlab/histories/)
          - [Google Play Console](https://play.google.com/console)
          EOF

      - name: 🏷️ 创建GitHub Release
        uses: actions/create-release@v1
        id: create_release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ needs.pre-release-validation.outputs.version-name }}
          release_name: GymBro v${{ needs.pre-release-validation.outputs.version-name }}
          body_path: release_notes.md
          draft: false
          prerelease: false
          target_commitish: ${{ github.ref }}

      - name: 📦 上传APK到Release
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ./artifacts/app-release.apk
          asset_name: gymbro-v${{ needs.pre-release-validation.outputs.version-name }}.apk
          asset_content_type: application/vnd.android.package-archive

      - name: 📦 上传AAB到Release
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ./artifacts/app-release.aab
          asset_name: gymbro-v${{ needs.pre-release-validation.outputs.version-name }}.aab
          asset_content_type: application/octet-stream

      - name: 🎉 GitHub Release创建成功
        run: |
          echo "## 🎉 GitHub Release创建成功" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📋 Release信息" >> $GITHUB_STEP_SUMMARY
          echo "- **版本**: v${{ needs.pre-release-validation.outputs.version-name }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Release URL**: ${{ steps.create_release.outputs.html_url }}" >> $GITHUB_STEP_SUMMARY
          echo "- **APK下载**: [gymbro-v${{ needs.pre-release-validation.outputs.version-name }}.apk](${{ steps.create_release.outputs.html_url }})" >> $GITHUB_STEP_SUMMARY
          echo "- **AAB下载**: [gymbro-v${{ needs.pre-release-validation.outputs.version-name }}.aab](${{ steps.create_release.outputs.html_url }})" >> $GITHUB_STEP_SUMMARY
