package com.example.gymbro.core.util

fun main() {
    val edgeCaseEmails = mapOf(
        "<EMAIL>" to true, // 最短有效邮箱
        "<EMAIL>" to true, // 域名包含连字符
        "test@123.456.789.123" to false, // IP地址格式（我们的regex不支持）
        "test@[IPv6:2001:db8::1]" to false, // IPv6格式（我们的regex不支持）
        "<EMAIL>" to false, // 本地部分结尾是点
        ".<EMAIL>" to false, // 本地部分开头是点
        "test@domain.c" to false, // 顶级域名太短
    )

    edgeCaseEmails.forEach { (email, expected) ->
        val result = ValidationUtils.isValidEmail(email)
        if (result != expected) {
            println("FAIL: '$email' expected $expected but got $result")
        } else {
            println("PASS: '$email' expected $expected and got $result")
        }
    }
}
