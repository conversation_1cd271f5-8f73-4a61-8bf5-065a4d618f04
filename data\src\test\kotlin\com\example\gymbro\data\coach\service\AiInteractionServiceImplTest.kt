package com.example.gymbro.data.coach.service

import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.coach.model.ai.*
import com.example.gymbro.domain.coach.repository.AiStreamRepository
import com.example.gymbro.domain.coach.repository.ChatRepository
import com.example.gymbro.domain.profile.repository.ProfileRepository
import com.example.gymbro.domain.repository.SearchRepository
import com.example.gymbro.domain.workout.repository.WorkoutDraftRepository
import io.mockk.*
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * AiInteractionService单元测试 - 简化版本
 *
 * 测试覆盖率要求：>85%
 * 遵循v5.0-GOLD标准的TDD开发模式
 */
class AiInteractionServiceImplTest {

    // Mock dependencies
    private val aiStreamRepository = mockk<AiStreamRepository>()
    private val chatRepository = mockk<ChatRepository>()
    private val searchRepository = mockk<SearchRepository>()
    private val profileRepository = mockk<ProfileRepository>()
    private val workoutDraftRepository = mockk<WorkoutDraftRepository>()

    private lateinit var aiInteractionService: AiInteractionServiceImpl

    @Before
    fun setup() {
        clearAllMocks()
        aiInteractionService = AiInteractionServiceImpl(
            aiStreamRepository = aiStreamRepository,
            chatRepository = chatRepository,
            searchRepository = searchRepository,
            profileRepository = profileRepository,
            workoutDraftRepository = workoutDraftRepository,
        )
    }

    @Test
    fun `getAiProviderStatus - 返回提供商状态`() = runTest {
        // When
        val result = aiInteractionService.getAiProviderStatus()

        // Then
        assertTrue(result.isSuccess)
        val providers = result.getOrNull()!!
        assertTrue(providers.isNotEmpty())

        // 验证包含主要AI提供商
        val providerIds = providers.map { it.providerId }
        assertTrue(providerIds.contains("deepseek"))
        assertTrue(providerIds.contains("openai"))
    }

    @Test
    fun `switchAiProvider - 成功切换提供商`() = runTest {
        // Given
        val providerId = "openai"

        // When
        val result = aiInteractionService.switchAiProvider(providerId)

        // Then
        assertTrue(result.isSuccess)
    }

    @Test
    fun `switchAiProvider - 无效提供商ID`() = runTest {
        // Given
        val invalidProviderId = "invalid-provider"

        // When
        val result = aiInteractionService.switchAiProvider(invalidProviderId)

        // Then
        assertTrue(result.isError)
        val error = result.errorOrNull()!!
        assertEquals(GlobalErrorType.Validation.InvalidInput, error.errorType)
    }

    @Test
    fun `getExerciseCorrectionFeedback - 返回模拟反馈`() = runTest {
        // Given
        val userId = "test-user-id"
        val videoPath = "/path/to/video.mp4"
        val exerciseId = "squat-123"

        // When
        val result = aiInteractionService.getExerciseCorrectionFeedback(
            userId = userId,
            videoPath = videoPath,
            exerciseId = exerciseId,
        )

        // Then
        assertTrue(result.isSuccess) // 现在返回模拟反馈
        val feedback = result.getOrNull()!!
        assertTrue(feedback.isNotEmpty())
        assertTrue(feedback.contains("深蹲")) // 基于exerciseId包含squat
    }

    @Test
    fun `generatePersonalizedRecommendations - 基础功能测试`() = runTest {
        // Given
        val userId = "test-user-id"
        val fitnessGoals = listOf(FitnessGoalType.MUSCLE_GAIN)

        // Mock profile data
        coEvery {
            profileRepository.getWorkoutDays()
        } returns listOf(1, 3, 5) // 周一、三、五

        // Mock search result
        every {
            searchRepository.vectorSearch(any())
        } returns ModernResult.Success(emptyList())

        // When
        val result = aiInteractionService.generatePersonalizedRecommendations(
            userId = userId,
            fitnessGoals = fitnessGoals,
        )

        // Then
        assertTrue(result.isSuccess)
        val recommendations = result.getOrNull()!!
        assertTrue(recommendations.isNotEmpty())

        coVerify {
            profileRepository.getWorkoutDays()
        }
        verify {
            searchRepository.vectorSearch(any())
        }
    }
}
