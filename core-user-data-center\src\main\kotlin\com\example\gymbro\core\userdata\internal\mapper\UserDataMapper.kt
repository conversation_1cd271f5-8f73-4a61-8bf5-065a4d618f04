package com.example.gymbro.core.userdata.internal.mapper

import com.example.gymbro.core.userdata.api.model.SyncStatus
import com.example.gymbro.core.userdata.api.model.UnifiedUserData
import com.example.gymbro.domain.model.auth.AuthUser
import com.example.gymbro.domain.profile.model.user.UserProfile
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 用户数据映射器
 *
 * 负责在不同数据模型之间进行转换，确保数据的一致性和完整性。
 * 实现了认证数据、用户资料数据和统一数据模型之间的双向映射。
 *
 * 核心功能：
 * - AuthUser + UserProfile → UnifiedUserData
 * - UnifiedUserData → UserProfile
 * - 数据合并和冲突解决
 * - 字段映射和类型转换
 */
@Singleton
class UserDataMapper @Inject constructor() {

    /**
     * 合并认证数据和用户资料数据为统一数据模型
     *
     * @param authUser 认证用户信息
     * @param userProfile 用户资料信息，可为空
     * @param syncStatus 同步状态
     * @return UnifiedUserData 统一用户数据
     */
    fun combineAuthAndProfile(
        authUser: AuthUser,
        userProfile: UserProfile?,
        syncStatus: SyncStatus = SyncStatus.SYNCED
    ): UnifiedUserData {
        return UnifiedUserData(
            // === 认证数据映射 ===
            userId = authUser.uid,
            email = authUser.email,
            displayName = authUser.displayName ?: userProfile?.displayName,
            isAnonymous = authUser.isAnonymous,
            isEmailVerified = authUser.isEmailVerified,
            phoneNumber = authUser.phoneNumber,
            isPhoneVerified = authUser.isPhoneVerified,

            // === 用户资料数据映射 ===
            username = userProfile?.username,
            height = userProfile?.height,
            weight = userProfile?.weight,
            gender = userProfile?.gender ?: com.example.gymbro.domain.profile.model.user.enums.Gender.OTHER,
            fitnessLevel = userProfile?.fitnessLevel ?: com.example.gymbro.domain.profile.model.user.enums.FitnessLevel.BEGINNER,
            fitnessGoals = userProfile?.fitnessGoals ?: emptyList(),
            workoutDays = userProfile?.workoutDays ?: emptyList(),
            bio = userProfile?.bio,

            // === 统计数据映射 ===
            totalActivityCount = userProfile?.totalActivityCount ?: 0,
            weeklyActiveMinutes = userProfile?.weeklyActiveMinutes ?: 0,
            allowPartnerMatching = userProfile?.allowPartnerMatching ?: false,

            // === 元数据 ===
            lastUpdated = System.currentTimeMillis(),
            syncStatus = syncStatus,
            dataVersion = "1.0"
        )
    }

    /**
     * 将统一用户数据转换为用户资料数据
     *
     * @param unifiedData 统一用户数据
     * @return UserProfile 用户资料数据
     */
    fun toUserProfile(unifiedData: UnifiedUserData): UserProfile {
        return UserProfile(
            userId = unifiedData.userId,
            username = unifiedData.username,
            displayName = unifiedData.displayName,
            email = unifiedData.email,
            phoneNumber = unifiedData.phoneNumber,
            height = unifiedData.height,
            weight = unifiedData.weight,
            gender = unifiedData.gender,
            fitnessLevel = unifiedData.fitnessLevel,
            fitnessGoals = unifiedData.fitnessGoals,
            workoutDays = unifiedData.workoutDays,
            bio = unifiedData.bio,
            totalActivityCount = unifiedData.totalActivityCount,
            weeklyActiveMinutes = unifiedData.weeklyActiveMinutes,
            allowPartnerMatching = unifiedData.allowPartnerMatching
        )
    }

    /**
     * 将统一用户数据转换为认证用户数据
     *
     * @param unifiedData 统一用户数据
     * @return AuthUser 认证用户数据
     */
    fun toAuthUser(unifiedData: UnifiedUserData): AuthUser {
        return AuthUser(
            uid = unifiedData.userId,
            displayName = unifiedData.displayName,
            email = unifiedData.email,
            phoneNumber = unifiedData.phoneNumber,
            isAnonymous = unifiedData.isAnonymous,
            photoUrl = null, // 暂时不支持头像URL
            isEmailVerified = unifiedData.isEmailVerified,
            isPhoneVerified = unifiedData.isPhoneVerified
        )
    }

    /**
     * 更新统一数据中的认证信息
     *
     * @param existing 现有的统一数据
     * @param authUser 新的认证信息
     * @return UnifiedUserData 更新后的统一数据
     */
    fun updateAuthData(existing: UnifiedUserData, authUser: AuthUser): UnifiedUserData {
        return existing.copy(
            // 更新认证相关字段
            email = authUser.email,
            displayName = authUser.displayName ?: existing.displayName,
            isAnonymous = authUser.isAnonymous,
            isEmailVerified = authUser.isEmailVerified,
            phoneNumber = authUser.phoneNumber,
            isPhoneVerified = authUser.isPhoneVerified,

            // 更新元数据
            lastUpdated = System.currentTimeMillis(),
            syncStatus = SyncStatus.SYNCED
        )
    }

    /**
     * 更新统一数据中的用户资料信息
     *
     * @param existing 现有的统一数据
     * @param userProfile 新的用户资料信息
     * @return UnifiedUserData 更新后的统一数据
     */
    fun updateProfileData(existing: UnifiedUserData, userProfile: UserProfile): UnifiedUserData {
        return existing.copy(
            // 更新用户资料相关字段
            username = userProfile.username,
            displayName = userProfile.displayName ?: existing.displayName,
            height = userProfile.height,
            weight = userProfile.weight,
            gender = userProfile.gender,
            fitnessLevel = userProfile.fitnessLevel,
            fitnessGoals = userProfile.fitnessGoals,
            workoutDays = userProfile.workoutDays,
            bio = userProfile.bio,

            // 更新统计数据
            totalActivityCount = userProfile.totalActivityCount,
            weeklyActiveMinutes = userProfile.weeklyActiveMinutes,
            allowPartnerMatching = userProfile.allowPartnerMatching,

            // 更新元数据
            lastUpdated = System.currentTimeMillis(),
            syncStatus = SyncStatus.SYNCED
        )
    }

    /**
     * 合并两个统一用户数据
     *
     * 智能合并策略：
     * - 以最新时间戳的数据为准
     * - 非空字段优先
     * - 统计数据取较大值
     *
     * @param primary 主要数据（通常是现有数据）
     * @param secondary 次要数据（通常是新数据）
     * @return UnifiedUserData 合并后的数据
     */
    fun mergeUnifiedData(primary: UnifiedUserData, secondary: UnifiedUserData): UnifiedUserData {
        // 确保用户ID一致
        require(primary.userId == secondary.userId) {
            "无法合并不同用户的数据: ${primary.userId} vs ${secondary.userId}"
        }

        // 选择更新的数据作为基础
        val (newer, older) = if (secondary.lastUpdated >= primary.lastUpdated) {
            secondary to primary
        } else {
            primary to secondary
        }

        return newer.copy(
            // 合并认证信息（优先使用非空值）
            email = newer.email ?: older.email,
            displayName = newer.displayName ?: older.displayName,
            phoneNumber = newer.phoneNumber ?: older.phoneNumber,

            // 合并用户资料信息（优先使用非空值）
            username = newer.username ?: older.username,
            height = newer.height ?: older.height,
            weight = newer.weight ?: older.weight,
            bio = newer.bio ?: older.bio,

            // 合并健身信息（优先使用非空/非默认值）
            fitnessGoals = if (newer.fitnessGoals.isNotEmpty()) newer.fitnessGoals else older.fitnessGoals,
            workoutDays = if (newer.workoutDays.isNotEmpty()) newer.workoutDays else older.workoutDays,

            // 统计数据取较大值
            totalActivityCount = maxOf(newer.totalActivityCount, older.totalActivityCount),
            weeklyActiveMinutes = maxOf(newer.weeklyActiveMinutes, older.weeklyActiveMinutes),

            // 使用最新的时间戳
            lastUpdated = maxOf(newer.lastUpdated, older.lastUpdated),

            // 同步状态需要重新评估
            syncStatus = if (newer.syncStatus == SyncStatus.SYNCED && older.syncStatus == SyncStatus.SYNCED) {
                SyncStatus.SYNCED
            } else {
                SyncStatus.PENDING_SYNC
            }
        )
    }

    /**
     * 检查两个统一数据是否有实质性差异
     *
     * @param data1 数据1
     * @param data2 数据2
     * @return Boolean 是否有差异
     */
    fun hasSignificantDifferences(data1: UnifiedUserData, data2: UnifiedUserData): Boolean {
        if (data1.userId != data2.userId) return true

        // 检查关键字段是否有差异
        return data1.email != data2.email ||
                data1.displayName != data2.displayName ||
                data1.username != data2.username ||
                data1.height != data2.height ||
                data1.weight != data2.weight ||
                data1.gender != data2.gender ||
                data1.fitnessLevel != data2.fitnessLevel ||
                data1.fitnessGoals != data2.fitnessGoals ||
                data1.workoutDays != data2.workoutDays ||
                data1.bio != data2.bio ||
                data1.allowPartnerMatching != data2.allowPartnerMatching
    }

    /**
     * 创建数据差异报告
     *
     * @param old 旧数据
     * @param new 新数据
     * @return List<String> 差异列表
     */
    fun createDifferenceReport(old: UnifiedUserData, new: UnifiedUserData): List<String> {
        val differences = mutableListOf<String>()

        if (old.email != new.email) {
            differences.add("邮箱: ${old.email} → ${new.email}")
        }
        if (old.displayName != new.displayName) {
            differences.add("显示名称: ${old.displayName} → ${new.displayName}")
        }
        if (old.height != new.height) {
            differences.add("身高: ${old.height} → ${new.height}")
        }
        if (old.weight != new.weight) {
            differences.add("体重: ${old.weight} → ${new.weight}")
        }
        if (old.fitnessLevel != new.fitnessLevel) {
            differences.add("健身水平: ${old.fitnessLevel} → ${new.fitnessLevel}")
        }
        if (old.fitnessGoals != new.fitnessGoals) {
            differences.add("健身目标: ${old.getFormattedGoals()} → ${new.getFormattedGoals()}")
        }

        return differences
    }
}
