package com.example.gymbro.designSystem.components.animations

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import kotlin.math.*

/**
 * 多层动画背景组件 - 专为订阅界面设计的日食风格光环效果
 *
 * 特点：
 * - 高质量日食光环效果，强烈的径向发光
 * - 紫粉色渐变光环，从内到外自然过渡
 * - 深黑色核心，完美的圆形边缘
 * - 呼吸式动画，模拟太阳耀斑和光环强度变化
 * - 完全集成MaterialTheme主题系统，自动适配深浅主题
 * - 多层发光系统，创造逼真的天体光学效果
 * - 平滑旋转动画，增强沉浸感
 * - 可选的星云烟雾效果和日食模式
 *
 * @param modifier 修饰符
 * @param showText 是否显示中心文字（保持向后兼容）
 * @param centerText 中心文字内容
 * @param starCloudVisible 是否显示星云带效果
 * @param eclipseMode 是否启用日食模式（强化光环效果）
 */
@Composable
fun GymBroLayeredAnimationBackground(
    modifier: Modifier = Modifier,
    showText: Boolean = false,
    centerText: String = "GymBro Premium",
    starCloudVisible: Boolean = true,
    eclipseMode: Boolean = false,
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background),
        contentAlignment = Alignment.Center,
    ) {
        // 日食光环效果
        if (starCloudVisible) {
            if (eclipseMode) {
                EclipseRingCanvas(
                    modifier = Modifier.fillMaxSize(),
                )
            } else {
                SmokeRingCanvas(
                    modifier = Modifier.fillMaxSize(),
                )
            }
        }

        // 保持向后兼容的文字显示选项
        if (showText) {
            Text(
                text = centerText,
                style = MaterialTheme.typography.displayMedium.copy(
                    fontSize = 48.sp,
                    fontWeight = FontWeight.ExtraBold,
                    color = MaterialTheme.colorScheme.onBackground,
                ),
            )
        }
    }
}

/**
 * 日食光环Canvas组件
 * 专门实现效果图中的日食视觉效果
 * 特点：强烈的紫粉色径向发光，深黑色核心，天体级视觉品质
 */
@Composable
private fun EclipseRingCanvas(
    modifier: Modifier = Modifier,
) {
    // 使用MaterialTheme颜色系统
    val colorScheme = MaterialTheme.colorScheme
    val isDark = colorScheme.background.luminance() < 0.5f

    // 创建无限动画过渡
    val infiniteTransition = rememberInfiniteTransition(label = "eclipse_ring_transition")

    // 主光环强度呼吸动画 - 更强烈的变化
    val coronaIntensity by infiniteTransition.animateFloat(
        initialValue = 0.6f,
        targetValue = 1.0f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 2800, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse,
        ),
        label = "corona_intensity",
    )

    // 内层光环脉冲
    val innerCorona by infiniteTransition.animateFloat(
        initialValue = 0.8f,
        targetValue = 1.2f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 3500, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse,
        ),
        label = "inner_corona",
    )

    // 外层光环扩散
    val outerGlow by infiniteTransition.animateFloat(
        initialValue = 0.4f,
        targetValue = 0.9f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 4200, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse,
        ),
        label = "outer_glow",
    )

    // 微妙的旋转动画
    val rotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 180000, easing = LinearEasing), // 3分钟一圈
            repeatMode = RepeatMode.Restart,
        ),
        label = "eclipse_rotation",
    )

    Canvas(modifier = modifier) {
        val centerX = size.width / 2f
        val centerY = size.height * 0.40f // 日食中心位置
        val baseRadius = minOf(size.width, size.height) * 0.38f

        // 绘制日食光环效果
        drawEclipseCorona(
            centerX = centerX,
            centerY = centerY,
            baseRadius = baseRadius,
            coronaIntensity = coronaIntensity,
            innerCorona = innerCorona,
            outerGlow = outerGlow,
            rotation = rotation,
            isDark = isDark,
            colorScheme = colorScheme,
        )
    }
}

/**
 * 绘制日食光环效果
 * 模拟真实日食现象的光环和耀斑效果
 */
private fun DrawScope.drawEclipseCorona(
    centerX: Float,
    centerY: Float,
    baseRadius: Float,
    coronaIntensity: Float,
    innerCorona: Float,
    outerGlow: Float,
    rotation: Float,
    isDark: Boolean,
    colorScheme: androidx.compose.material3.ColorScheme,
) {
    // 日食光环颜色系统 - 基于效果图的紫粉色调
    val eclipseColors = listOf(
        Color(0xFFE6E6FA), // 淡紫色 - 最亮
        Color(0xFFDDA0DD), // 梅花色
        Color(0xFFDA70D6), // 兰花紫 - 主要色
        Color(0xFFBA55D3), // 中兰花紫
        Color(0xFF9932CC), // 暗兰花紫
        Color(0xFF8A2BE2), // 蓝紫色
        Color(0xFF4B0082), // 靛蓝色 - 最暗
    )

    // 第一层：最外层大范围发光光晕
    val outerHaloRadius = baseRadius * 2.5f
    for (i in 0..8) {
        val glowRadius = outerHaloRadius - i * 35f
        val glowColor = eclipseColors[minOf(i, eclipseColors.size - 1)]
        val alpha = (0.08f - i * 0.008f) * outerGlow * coronaIntensity

        drawCircle(
            brush = Brush.radialGradient(
                0f to Color.Transparent,
                0.3f to glowColor.copy(alpha = alpha * 0.3f),
                0.6f to glowColor.copy(alpha = alpha * 0.6f),
                0.8f to glowColor.copy(alpha = alpha),
                1f to Color.Transparent,
                center = Offset(centerX, centerY),
                radius = glowRadius,
            ),
            radius = glowRadius,
            center = Offset(centerX, centerY),
        )
    }

    // 第二层：主光环 - 强烈的紫粉色渐变
    val mainCoronaRadius = baseRadius * 1.8f
    drawCircle(
        brush = Brush.radialGradient(
            0f to Color.Transparent,
            0.45f to Color.Transparent,
            0.5f to eclipseColors[0].copy(alpha = 0.15f * innerCorona),
            0.55f to eclipseColors[1].copy(alpha = 0.35f * innerCorona),
            0.62f to eclipseColors[2].copy(alpha = 0.65f * innerCorona),
            0.7f to eclipseColors[3].copy(alpha = 0.8f * innerCorona),
            0.78f to eclipseColors[4].copy(alpha = 0.9f * innerCorona),
            0.85f to eclipseColors[5].copy(alpha = 0.7f * innerCorona),
            0.92f to eclipseColors[6].copy(alpha = 0.4f * innerCorona),
            0.98f to eclipseColors[6].copy(alpha = 0.1f * innerCorona),
            1f to Color.Transparent,
            center = Offset(centerX, centerY),
            radius = mainCoronaRadius,
        ),
        radius = mainCoronaRadius,
        center = Offset(centerX, centerY),
    )

    // 第三层：内层强光环
    val innerCoronaRadius = baseRadius * 1.4f
    drawCircle(
        brush = Brush.radialGradient(
            0f to Color.Transparent,
            0.6f to Color.Transparent,
            0.65f to eclipseColors[0].copy(alpha = 0.25f * coronaIntensity),
            0.72f to eclipseColors[1].copy(alpha = 0.5f * coronaIntensity),
            0.78f to eclipseColors[2].copy(alpha = 0.75f * coronaIntensity),
            0.84f to eclipseColors[3].copy(alpha = 0.9f * coronaIntensity),
            0.9f to eclipseColors[4].copy(alpha = 0.6f * coronaIntensity),
            0.95f to eclipseColors[5].copy(alpha = 0.3f * coronaIntensity),
            1f to Color.Transparent,
            center = Offset(centerX, centerY),
            radius = innerCoronaRadius,
        ),
        radius = innerCoronaRadius,
        center = Offset(centerX, centerY),
    )

    // 第四层：优化的太阳耀斑效果 - 更自然的光线分布
    val flareRadius = baseRadius * 1.05f
    for (i in 0 until 6) { // 减少耀斑数量，让效果更精致
        val angle = (rotation + i * 60f) * PI.toFloat() / 180f
        val flareLength = 60f + sin(rotation * PI.toFloat() / 80f + i) * 25f
        val flareAlpha = (0.3f + sin(angle * 2) * 0.15f) * coronaIntensity

        // 绘制更精致的耀斑光线
        for (j in 0 until 15) {
            val distance = flareRadius + j * (flareLength / 15f)
            val flareSize = (8f - j * 0.3f) * (1f + sin(rotation * PI.toFloat() / 120f + i + j) * 0.2f)
            val currentAlpha = flareAlpha * (1f - j / 15f) * (1f - j / 15f) // 二次衰减

            val flareX = centerX + cos(angle) * distance
            val flareY = centerY + sin(angle) * distance

            drawCircle(
                color = eclipseColors[1].copy(alpha = currentAlpha * 0.5f),
                radius = flareSize,
                center = Offset(flareX, flareY),
            )
        }
    }

    // 第五层：完全透明的中心区域 - 用于显示订阅界面的LOGO和内容
    // 不绘制任何内容，保持中心完全透明
    val coreRadius = baseRadius * 0.95f

    // 第六层：光环内边缘柔和过渡 - 从光环到透明中心的自然渐变
    val innerEdgeRadius = coreRadius * 1.1f
    drawCircle(
        brush = Brush.radialGradient(
            0f to Color.Transparent,
            0.7f to Color.Transparent,
            0.8f to eclipseColors[4].copy(alpha = 0.1f * coronaIntensity),
            0.85f to eclipseColors[3].copy(alpha = 0.15f * coronaIntensity),
            0.9f to eclipseColors[2].copy(alpha = 0.25f * coronaIntensity),
            0.94f to eclipseColors[1].copy(alpha = 0.35f * coronaIntensity),
            0.97f to eclipseColors[0].copy(alpha = 0.4f * coronaIntensity),
            1f to eclipseColors[0].copy(alpha = 0.2f * coronaIntensity),
            center = Offset(centerX, centerY),
            radius = innerEdgeRadius,
        ),
        radius = innerEdgeRadius,
        center = Offset(centerX, centerY),
    )
}

/**
 * 烟雾星环Canvas组件
 * 使用实体圆环和径向渐变完全还原目标图片效果
 * 完全集成MaterialTheme主题系统，响应光暗切换
 * 增加多层旋转动画和发光效果
 */
@Composable
private fun SmokeRingCanvas(
    modifier: Modifier = Modifier,
) {
    // 使用MaterialTheme颜色系统
    val colorScheme = MaterialTheme.colorScheme
    val isDark = colorScheme.background.luminance() < 0.5f

    // 创建无限动画过渡
    val infiniteTransition = rememberInfiniteTransition(label = "smoke_ring_transition")

    // 主圈缓慢旋转动画 - 保持原有速度
    val mainRotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 150000, easing = LinearEasing), // 2.5分钟一圈
            repeatMode = RepeatMode.Restart,
        ),
        label = "main_rotation",
    )

    // 外圈烟雾快速旋转动画 - 比主圈快约1.5倍
    val outerRotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 100000, easing = LinearEasing), // 约1.67分钟一圈
            repeatMode = RepeatMode.Restart,
        ),
        label = "outer_rotation",
    )

    // 核心环强度呼吸动画
    val coreIntensity by infiniteTransition.animateFloat(
        initialValue = 0.8f,
        targetValue = 1.0f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 3000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse,
        ),
        label = "core_intensity",
    )

    // 烟雾密度动画
    val smokeDensity by infiniteTransition.animateFloat(
        initialValue = 0.7f,
        targetValue = 1.0f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 4000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse,
        ),
        label = "smoke_density",
    )

    // 发光强度动画（特别为黑色主题设计）
    val glowIntensity by infiniteTransition.animateFloat(
        initialValue = 0.6f,
        targetValue = 1.0f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 2500, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse,
        ),
        label = "glow_intensity",
    )

    Canvas(modifier = modifier) {
        val centerX = size.width / 2f
        val centerY = size.height * 0.40f // 环的中心位置
        val baseRingRadius = minOf(size.width, size.height) * 0.42f

        // 绘制增强的实体圆环效果
        drawEnhancedSolidRingEffect(
            centerX = centerX,
            centerY = centerY,
            baseRadius = baseRingRadius,
            mainRotation = mainRotation,
            outerRotation = outerRotation,
            coreIntensity = coreIntensity,
            smokeDensity = smokeDensity,
            glowIntensity = glowIntensity,
            isDark = isDark,
            colorScheme = colorScheme,
        )
    }
}

/**
 * 绘制增强的实体圆环效果
 * 使用径向渐变和多层圆环完全还原目标图片效果
 * 增加了旋转动画和发光效果
 * 外圈烟雾附着在主圆环上，内圈具有丰富的灰阶3D效果
 */
private fun DrawScope.drawEnhancedSolidRingEffect(
    centerX: Float,
    centerY: Float,
    baseRadius: Float,
    mainRotation: Float,
    outerRotation: Float,
    coreIntensity: Float,
    smokeDensity: Float,
    glowIntensity: Float,
    isDark: Boolean,
    colorScheme: androidx.compose.material3.ColorScheme,
) {
    // 根据主题获取颜色
    val coreColor = if (isDark) Color.White else Color.Black

    // 定义5种渐进的橙色用于外圈附着效果
    val orangeColors = listOf(
        Color(0xFFFF6B35), // 深橙色
        Color(0xFFFF8C42), // 中深橙色
        Color(0xFFFFA552), // 标准橙色
        Color(0xFFFFBD69), // 中浅橙色
        Color(0xFFFFD280), // 浅橙色
    )

    // 金属渐变颜色系统（集成彩虹悦动主题）
    val metallicColors = if (isDark) {
        listOf(
            Color(0xFFE6E6FA), // 淡紫色
            Color(0xFFDDA0DD), // 梅花色
            Color(0xFFDA70D6), // 兰花紫
            Color(0xFFBA55D3), // 中兰花紫
            Color(0xFF9932CC), // 暗兰花紫
            Color(0xFF8A2BE2), // 蓝紫色
            Color(0xFF9370DB), // 中紫色
        )
    } else {
        listOf(
            Color(0xFF2C2C54), // 深紫灰
            Color(0xFF40407A), // 中紫灰
            Color(0xFF706FD3), // 浅紫色
            Color(0xFF474787), // 深蓝紫
            Color(0xFF596275), // 蓝灰色
            Color(0xFF3C4142), // 深灰色
            Color(0xFF57606F), // 浅灰色
        )
    }

    // 发光效果颜色（深色主题使用紫色系）
    val glowColor = if (isDark) {
        Color(0xFFDA70D6).copy(alpha = 0.8f) // 兰花紫发光
    } else {
        Color.Black.copy(alpha = 0.6f)
    }

    // 第一层：最外层橙色附着烟雾效果 - 改进为真实雾气效果
    val outerHaloRadius = baseRadius + 180f

    // 使用更柔和的雾气效果，模拟真实的烟雾附着
    for (colorIndex in orangeColors.indices) {
        val baseColor = orangeColors[colorIndex]
        val layerOffset = colorIndex * 25f // 减小偏移，让雾气更紧贴

        // 每层雾气的粒子数量和分布
        val particleCount = 80 - colorIndex * 10 // 外层更多粒子
        for (i in 0 until particleCount) {
            val angle = (mainRotation + i * (360f / particleCount) + colorIndex * 15f) * PI.toFloat() / 180f

            // 雾气的自然波动 - 模拟真实烟雾的不规则性
            val waveVariation = sin(angle * 2 + mainRotation * PI.toFloat() / 120f) * (15f + colorIndex * 3f)
            val noiseVariation = sin(angle * 5 + mainRotation * PI.toFloat() / 200f + colorIndex) * (8f + colorIndex * 2f)

            val particleRadius = outerHaloRadius - layerOffset + waveVariation + noiseVariation

            // 雾气粒子大小 - 外层更大更模糊
            val baseSize = 15f + colorIndex * 3f
            val sizeVariation = sin(mainRotation * PI.toFloat() / 150f + i * 0.8f + colorIndex) * (5f + colorIndex)
            val particleSize = baseSize + sizeVariation

            // 雾气透明度 - 更柔和的渐变
            val baseAlpha = (0.12f - colorIndex * 0.02f) // 降低基础透明度
            val alphaVariation = sin(angle * 1.5f + mainRotation * PI.toFloat() / 100f) * 0.06f
            val alpha = (baseAlpha + alphaVariation) * smokeDensity

            val particleX = centerX + cos(angle) * particleRadius
            val particleY = centerY + sin(angle) * particleRadius

            // 使用径向渐变创建更柔和的雾气效果
            drawCircle(
                brush = Brush.radialGradient(
                    0f to baseColor.copy(alpha = alpha * 0.8f),
                    0.6f to baseColor.copy(alpha = alpha * 0.4f),
                    1f to baseColor.copy(alpha = 0f),
                    radius = particleSize,
                ),
                radius = particleSize,
                center = Offset(particleX, particleY),
            )
        }
    }

    // 第二层：核心金属渐变环的发光效果层
    val coreThickness = 45f
    val innerRadius = baseRadius - coreThickness
    val outerRadius = baseRadius + coreThickness

    // 深色主题的紫色发光效果
    if (isDark) {
        // 多层紫色发光
        for (i in 1..6) {
            val glowRadius = outerRadius + i * 18f
            val glowAlpha = (0.12f * glowIntensity / i) * coreIntensity
            val glowColorVariant = when (i) {
                1 -> Color(0xFFE6E6FA).copy(alpha = glowAlpha)
                2 -> Color(0xFFDDA0DD).copy(alpha = glowAlpha)
                3 -> Color(0xFFDA70D6).copy(alpha = glowAlpha)
                4 -> Color(0xFFBA55D3).copy(alpha = glowAlpha)
                5 -> Color(0xFF9932CC).copy(alpha = glowAlpha)
                else -> Color(0xFF8A2BE2).copy(alpha = glowAlpha)
            }

            drawCircle(
                brush = Brush.radialGradient(
                    0f to Color.Transparent,
                    0.6f to glowColorVariant,
                    0.8f to glowColorVariant.copy(alpha = glowAlpha * 0.5f),
                    1f to Color.Transparent,
                    center = Offset(centerX, centerY),
                    radius = glowRadius,
                ),
                radius = glowRadius,
                center = Offset(centerX, centerY),
            )
        }
    }

    // 绘制核心金属渐变环
    drawCircle(
        brush = Brush.radialGradient(
            0f to Color.Transparent,
            0.3f to Color.Transparent,
            0.5f to Color.Transparent,
            0.62f to metallicColors[0].copy(alpha = 0.2f * coreIntensity),
            0.68f to metallicColors[1].copy(alpha = 0.4f * coreIntensity),
            0.75f to metallicColors[2].copy(alpha = 0.7f * coreIntensity),
            0.82f to metallicColors[3].copy(alpha = 0.9f * coreIntensity),
            0.88f to metallicColors[4].copy(alpha = 1.0f * coreIntensity),
            0.92f to metallicColors[5].copy(alpha = 0.9f * coreIntensity),
            0.95f to metallicColors[6].copy(alpha = 0.7f * coreIntensity),
            0.97f to metallicColors[0].copy(alpha = 0.4f * coreIntensity),
            0.99f to metallicColors[1].copy(alpha = 0.2f * coreIntensity),
            0.995f to Color.Transparent,
            1f to Color.Transparent,
            center = Offset(centerX, centerY),
            radius = outerRadius,
        ),
        radius = outerRadius,
        center = Offset(centerX, centerY),
    )

    // 绘制内部空洞，创建真正的环形效果
    drawCircle(
        color = colorScheme.background,
        radius = innerRadius,
        center = Offset(centerX, centerY),
    )

    // 第三层：内部白色区域的微妙灰阶3D效果
    val innerAreaRadius = innerRadius - 10f // 在内部区域创建效果

    // 在内部白色区域添加非常微妙的灰阶层次
    for (layer in 0 until 8) { // 增加层数，创造更细腻的效果
        val layerRadius = innerAreaRadius * (0.9f - layer * 0.1f) // 从外向内递减
        val layerAlpha = 0.03f + layer * 0.008f // 非常浅的透明度

        // 根据主题选择微妙的灰阶颜色
        val subtleGrayColor = if (isDark) {
            Color.Black.copy(alpha = layerAlpha * coreIntensity)
        } else {
            Color.Gray.copy(alpha = layerAlpha * coreIntensity * 0.5f)
        }

        // 为每一层添加旋转的微妙阴影点
        val pointCount = 16 - layer // 外层更多点
        for (i in 0 until pointCount) {
            val angle = (mainRotation * (0.2f + layer * 0.05f) + i * (360f / pointCount)) * PI.toFloat() / 180f
            val pointRadius = layerRadius + sin(angle * 3 + mainRotation * PI.toFloat() / 180f) * (3f + layer)
            val pointSize = (2f + layer * 0.5f) + sin(mainRotation * PI.toFloat() / 120f + i + layer) * 1f
            val pointAlpha = layerAlpha + sin(angle * 2) * 0.01f

            val pointX = centerX + cos(angle) * pointRadius
            val pointY = centerY + sin(angle) * pointRadius

            drawCircle(
                color = subtleGrayColor.copy(alpha = pointAlpha),
                radius = pointSize,
                center = Offset(pointX, pointY),
            )
        }
    }

    // 在中心区域添加非常微妙的径向渐变
    drawCircle(
        brush = Brush.radialGradient(
            0f to Color.Transparent,
            0.3f to if (isDark) Color.Black.copy(alpha = 0.02f * coreIntensity) else Color.Gray.copy(alpha = 0.015f * coreIntensity),
            0.6f to if (isDark) Color.Black.copy(alpha = 0.01f * coreIntensity) else Color.Gray.copy(alpha = 0.008f * coreIntensity),
            1f to Color.Transparent,
            center = Offset(centerX, centerY),
            radius = innerAreaRadius * 0.8f,
        ),
        radius = innerAreaRadius * 0.8f,
        center = Offset(centerX, centerY),
    )

    // 第四层：内环边缘的金属质感3D效果（保持原有效果）
    for (layer in 0 until 5) {
        val layerRadius = innerRadius + (layer + 1) * 2f
        val metalColor = metallicColors[layer % metallicColors.size]
        val layerAlpha = (0.8f - layer * 0.15f) * coreIntensity

        // 为每一层添加旋转的金属光点
        for (i in 0 until (20 - layer * 2)) {
            val angle = (mainRotation * (0.4f + layer * 0.1f) + i * (18f + layer * 3f)) * PI.toFloat() / 180f
            val glowSize = (8f - layer * 1f) + sin(mainRotation * PI.toFloat() / 90f + i + layer) * 1.5f
            val alpha = (0.6f - layer * 0.1f + sin(angle * 2) * 0.2f) * coreIntensity

            val glowX = centerX + cos(angle) * layerRadius
            val glowY = centerY + sin(angle) * layerRadius

            drawCircle(
                color = metalColor.copy(alpha = alpha * layerAlpha),
                radius = glowSize,
                center = Offset(glowX, glowY),
            )
        }
    }

    // 第四层：最内层的高亮金属边缘
    for (i in 0 until 12) {
        val angle = (mainRotation * 0.2f + i * 30f) * PI.toFloat() / 180f
        val highlightRadius = innerRadius + 1f
        val highlightSize = 5f + sin(mainRotation * PI.toFloat() / 60f + i) * 1.5f
        val alpha = (0.95f + sin(angle * 4) * 0.05f) * coreIntensity

        val highlightX = centerX + cos(angle) * highlightRadius
        val highlightY = centerY + sin(angle) * highlightRadius

        val highlightColor = if (isDark) metallicColors[2] else metallicColors[4]

        drawCircle(
            color = highlightColor.copy(alpha = alpha),
            radius = highlightSize,
            center = Offset(highlightX, highlightY),
        )
    }

    // 重新绘制内部空洞，确保干净的边缘
    drawCircle(
        color = colorScheme.background,
        radius = innerRadius,
        center = Offset(centerX, centerY),
    )
}

// === Preview组件 ===

@GymBroPreview
@Composable
private fun GymBroLayeredAnimationBackgroundPreview() {
    GymBroTheme {
        Box(modifier = Modifier.size(400.dp)) {
            GymBroLayeredAnimationBackground(
                showText = false,
                centerText = "",
                starCloudVisible = true,
                eclipseMode = false,
            )
        }
    }
}

@GymBroPreview
@Composable
private fun GymBroLayeredAnimationBackgroundEclipsePreview() {
    GymBroTheme {
        Box(modifier = Modifier.size(400.dp)) {
            GymBroLayeredAnimationBackground(
                showText = false,
                centerText = "",
                starCloudVisible = true,
                eclipseMode = true,
            )
        }
    }
}

@GymBroPreview
@Composable
private fun GymBroLayeredAnimationBackgroundLightThemePreview() {
    GymBroTheme(darkTheme = false) {
        Box(modifier = Modifier.size(400.dp)) {
            GymBroLayeredAnimationBackground(
                showText = false,
                centerText = "",
                starCloudVisible = true,
                eclipseMode = false,
            )
        }
    }
}

@GymBroPreview
@Composable
private fun EclipseRingCanvasPreview() {
    GymBroTheme {
        Box(
            modifier = Modifier
                .size(400.dp)
                .background(MaterialTheme.colorScheme.background),
        ) {
            EclipseRingCanvas(
                modifier = Modifier.fillMaxSize(),
            )
        }
    }
}

@GymBroPreview
@Composable
private fun SmokeRingCanvasPreview() {
    GymBroTheme {
        Box(
            modifier = Modifier
                .size(400.dp)
                .background(MaterialTheme.colorScheme.background),
        ) {
            SmokeRingCanvas(
                modifier = Modifier.fillMaxSize(),
            )
        }
    }
}

@GymBroPreview
@Composable
private fun SmokeRingCanvasLightThemePreview() {
    GymBroTheme(darkTheme = false) {
        Box(
            modifier = Modifier
                .size(400.dp)
                .background(MaterialTheme.colorScheme.background),
        ) {
            SmokeRingCanvas(
                modifier = Modifier.fillMaxSize(),
            )
        }
    }
}
