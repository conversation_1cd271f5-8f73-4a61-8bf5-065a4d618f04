package com.example.gymbro.core.autosave.config

import com.example.gymbro.core.autosave.storage.AutoSaveStorage
import com.example.gymbro.core.autosave.strategy.AutoSaveStrategy

/**
 * 自动保存配置
 *
 * 注意：系统强制执行最低1秒的保存间隔，防止过于频繁的保存操作
 *
 * @param T 要保存的数据类型
 * @param id 唯一标识符
 * @param strategy 保存策略（间隔会自动调整为最低1秒）
 * @param storage 存储后端
 * @param enableRecovery 是否启用缓存恢复
 * @param maxRetries 最大重试次数
 * @param retryDelayMs 重试延迟（毫秒）
 * @param enableLogging 是否启用日志
 */
data class AutoSaveConfig<T : Any>(
    val id: String,
    val strategy: AutoSaveStrategy<T>,
    val storage: AutoSaveStorage<T>,
    val enableRecovery: Boolean = true,
    val maxRetries: Int = 3,
    val retryDelayMs: Long = 1000L,
    val enableLogging: Boolean = true,
) {

    /**
     * 配置构建器
     */
    class Builder<T : Any> {
        var id: String = ""
        var strategy: AutoSaveStrategy<T>? = null
        var storage: AutoSaveStorage<T>? = null
        var enableRecovery: Boolean = true
        var maxRetries: Int = 3
        var retryDelayMs: Long = 1000L
        var enableLogging: Boolean = true

        fun build(): AutoSaveConfig<T> {
            require(id.isNotBlank()) { "ID不能为空" }
            requireNotNull(strategy) { "保存策略不能为空" }
            requireNotNull(storage) { "存储后端不能为空" }

            return AutoSaveConfig(
                id = id,
                strategy = strategy!!,
                storage = storage!!,
                enableRecovery = enableRecovery,
                maxRetries = maxRetries,
                retryDelayMs = retryDelayMs,
                enableLogging = enableLogging,
            )
        }
    }
}

/**
 * 预定义配置
 */
object AutoSaveConfigs {

    /**
     * Profile即时保存配置
     */
    fun <T : Any> profileConfig(
        storage: AutoSaveStorage<T>,
        strategy: AutoSaveStrategy<T>,
    ): AutoSaveConfig<T> {
        return AutoSaveConfig(
            id = "profile_autosave",
            strategy = strategy,
            storage = storage,
            enableRecovery = false, // Profile不需要缓存恢复
            maxRetries = 2,
            retryDelayMs = 500L,
        )
    }

    /**
     * Workout模板缓存配置
     */
    fun <T : Any> workoutTemplateConfig(
        storage: AutoSaveStorage<T>,
        strategy: AutoSaveStrategy<T>,
    ): AutoSaveConfig<T> {
        return AutoSaveConfig(
            id = "workout_template_autosave",
            strategy = strategy,
            storage = storage,
            enableRecovery = true, // 支持缓存恢复
            maxRetries = 3,
            retryDelayMs = 1000L,
        )
    }

    /**
     * 通用缓存配置
     */
    fun <T : Any> cacheConfig(
        id: String,
        storage: AutoSaveStorage<T>,
        strategy: AutoSaveStrategy<T>,
    ): AutoSaveConfig<T> {
        return AutoSaveConfig(
            id = id,
            strategy = strategy,
            storage = storage,
            enableRecovery = true,
            maxRetries = 3,
            retryDelayMs = 1000L,
        )
    }
}
