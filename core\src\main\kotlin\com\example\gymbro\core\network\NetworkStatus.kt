package com.example.gymbro.core.network

/**
 * 网络状态
 *
 * 封装网络连接的当前状态信息。
 */
data class NetworkStatus(
    /** 网络是否可用 */
    val isAvailable: Boolean = false,

    /** 网络连接类型 */
    val type: NetworkType = NetworkType.NONE,

    /** 网络是否计费（例如移动数据） */
    val isMetered: Boolean = false,

    /** 网络速度估计 */
    val networkSpeed: NetworkSpeed = NetworkSpeed.UNKNOWN,

    /** 网络是否受限（例如需要登录门户认证） */
    val isRestricted: Boolean = false,
)
