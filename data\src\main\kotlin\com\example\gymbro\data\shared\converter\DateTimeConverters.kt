package com.example.gymbro.data.shared.converter

import androidx.room.TypeConverter
import kotlinx.datetime.*

/**
 * Room数据库日期时间类型转换器
 *
 * 提供kotlinx.datetime类型与Room数据库支持的基本类型之间的转换。
 * 专门用于处理LocalDate、LocalDateTime、Instant等时间类型。
 *
 * 支持的转换：
 * - LocalDate ↔ String (ISO格式: YYYY-MM-DD)
 * - LocalDateTime ↔ String (ISO格式: YYYY-MM-DDTHH:mm:ss)
 * - Instant ↔ Long (毫秒时间戳)
 *
 * 设计特点：
 * - 使用ISO标准格式确保跨平台兼容性
 * - 空值安全处理
 * - 异常容错机制
 * - 高性能转换
 */
class DateTimeConverters {
    // ==================== LocalDate转换 ====================

    /**
     * LocalDate转String (ISO格式)
     * 格式：YYYY-MM-DD
     */
    @TypeConverter
    fun fromLocalDate(date: LocalDate?): String? = date?.toString()

    /**
     * String转LocalDate
     * 支持ISO格式：YYYY-MM-DD
     */
    @TypeConverter
    fun toLocalDate(dateString: String?): LocalDate? =
        try {
            dateString?.let { LocalDate.parse(it) }
        } catch (e: Exception) {
            null
        }

    // ==================== LocalDateTime转换 ====================

    /**
     * LocalDateTime转String (ISO格式)
     * 格式：YYYY-MM-DDTHH:mm:ss
     */
    @TypeConverter
    fun fromLocalDateTime(dateTime: LocalDateTime?): String? = dateTime?.toString()

    /**
     * String转LocalDateTime
     * 支持ISO格式：YYYY-MM-DDTHH:mm:ss
     */
    @TypeConverter
    fun toLocalDateTime(dateTimeString: String?): LocalDateTime? =
        try {
            dateTimeString?.let { LocalDateTime.parse(it) }
        } catch (e: Exception) {
            null
        }

    // ==================== Instant转换 ====================

    /**
     * Instant转Long (毫秒时间戳)
     */
    @TypeConverter
    fun fromInstant(instant: Instant?): Long? = instant?.toEpochMilliseconds()

    /**
     * Long转Instant
     * 输入：毫秒时间戳
     */
    @TypeConverter
    fun toInstant(timestamp: Long?): Instant? =
        try {
            timestamp?.let { Instant.fromEpochMilliseconds(it) }
        } catch (e: Exception) {
            null
        }

    // ==================== 工具方法 ====================

    companion object {
        /**
         * 获取当前日期
         */
        fun today(): LocalDate =
            kotlinx.datetime.Clock.System
                .now()
                .toLocalDateTime(TimeZone.currentSystemDefault())
                .date

        /**
         * 获取当前时间
         */
        fun now(): LocalDateTime =
            kotlinx.datetime.Clock.System
                .now()
                .toLocalDateTime(TimeZone.currentSystemDefault())

        /**
         * 获取当前时间戳
         */
        fun nowMillis(): Long =
            kotlinx.datetime.Clock.System
                .now()
                .toEpochMilliseconds()

        /**
         * LocalDate转时间戳（当天开始时间）
         */
        fun localDateToMillis(date: LocalDate): Long =
            kotlinx.datetime.Clock.System
                .now()
                .toEpochMilliseconds()

        /**
         * 时间戳转LocalDate
         */
        fun millisToLocalDate(millis: Long): LocalDate =
            Instant
                .fromEpochMilliseconds(millis)
                .toLocalDateTime(TimeZone.currentSystemDefault())
                .date

        /**
         * 验证日期字符串格式
         */
        fun isValidDateString(dateString: String): Boolean =
            try {
                LocalDate.parse(dateString)
                true
            } catch (e: Exception) {
                false
            }

        /**
         * 验证日期时间字符串格式
         */
        fun isValidDateTimeString(dateTimeString: String): Boolean =
            try {
                LocalDateTime.parse(dateTimeString)
                true
            } catch (e: Exception) {
                false
            }
    }
}
