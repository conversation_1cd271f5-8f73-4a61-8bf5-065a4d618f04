package com.example.gymbro.app.di

import com.example.gymbro.app.version.RegionDetectionManager
import com.example.gymbro.core.region.RegionProvider
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 应用级地区检测模块
 *
 * 在app模块中提供RegionProvider的绑定，避免模块循环依赖问题。
 * 这个模块将RegionDetectionManager绑定为RegionProvider的实现。
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class RegionModule {

    /**
     * 绑定RegionProvider实现
     *
     * 使用RegionDetectionManager作为RegionProvider的实现，
     * 提供真实的IP地理位置检测功能
     */
    @Binds
    @Singleton
    abstract fun bindRegionProvider(impl: RegionDetectionManager): RegionProvider
}
