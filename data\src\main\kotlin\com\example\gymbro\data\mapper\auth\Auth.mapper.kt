package com.example.gymbro.data.mapper.auth

import com.example.gymbro.data.local.entity.user.UserCacheEntity
import com.example.gymbro.domain.profile.model.user.User
import com.example.gymbro.domain.profile.model.user.enums.FitnessLevel
import com.example.gymbro.domain.profile.model.user.enums.Gender
import com.example.gymbro.domain.profile.model.user.enums.UserType
import com.example.gymbro.domain.profile.model.user.enums.WeightUnit
import com.example.gymbro.domain.shared.GymBroUser
import com.example.gymbro.domain.subscription.model.plan.SubscriptionPlanType

/**
 * 认证数据映射扩展函数
 *
 * 替代class-based AuthMapper，使用扩展函数实现映射逻辑
 * 符合Clean Architecture原则，无需依赖注入
 *
 * 提供的扩展函数：
 * - AuthDataResult.toUser()
 * - User.toUserCacheEntity()
 * - GymBroUser.toUserCacheEntity()
 * - UserCacheEntity.toUser()
 * - UserCacheEntity.toGymBroUser()
 * - User.toGymBroUser()
 * - SubscriptionType.toPlanType()
 */

/**
 * 将认证数据结果映射为领域模型User
 * 注意：这里需要找到正确的AuthDataResult类型定义
 */
// fun AuthDataResult.toUser(): User - 需要确认AuthDataResult类型后启用

/**
 * 将领域模型User映射为用户缓存实体
 */
fun User.toUserCacheEntity(): UserCacheEntity =
    UserCacheEntity(
        userId = this.userId,
        email = this.email,
        username = this.username ?: "User_${this.userId.take(6)}",
        displayName = this.displayName,
        photoUrl = this.profileImageUrl,
        phoneNumber = this.phoneNumber,
        isAnonymous = this.isAnonymous,
        gender = this.gender.name,
        weight = this.weight,
        weightUnit = this.weightUnit.name,
        fitnessLevel = this.fitnessLevel.ordinal,
        wechatId = this.wechatId,
        bio = this.bio,
        avatar = this.avatar,
        createdAt = this.createdAt,
        lastLoginAt = System.currentTimeMillis(),
        subscriptionPlan = this.subscriptionPlan.name,
        subscriptionExpiryDate = this.subscriptionExpiryDate,
    )

/**
 * 将GymBroUser(旧版用户模型)映射为用户缓存实体
 *
 * 由于GymBroUser现在是User的类型别名，此方法已合并到User.toUserCacheEntity()中
 * 为避免扩展函数冲突，此方法已移除
 */
// fun gymBroUser.toUserCacheEntity(): UserCacheEntity - 已移除，避免与User扩展函数冲突

/**
 * 将用户缓存实体映射为User领域模型
 */
fun UserCacheEntity.toUser(): User {
    // 安全处理枚举类型
    val gender =
        try {
            this.gender?.let { Gender.valueOf(it) } ?: Gender.OTHER
        } catch (e: Exception) {
            Gender.OTHER
        }

    val weightUnit =
        try {
            this.weightUnit?.let { WeightUnit.valueOf(it) } ?: WeightUnit.KG
        } catch (e: Exception) {
            WeightUnit.KG
        }

    val fitnessLevel =
        try {
            FitnessLevel.values()[this.fitnessLevel ?: 0]
        } catch (e: Exception) {
            FitnessLevel.BEGINNER
        }

    val subscriptionPlan =
        try {
            this.subscriptionPlan?.let { SubscriptionPlanType.valueOf(it) } ?: SubscriptionPlanType.FREE
        } catch (e: Exception) {
            SubscriptionPlanType.FREE
        }

    return User(
        userId = this.userId,
        email = this.email,
        phoneNumber = this.phoneNumber,
        wechatId = this.wechatId,
        username = this.username,
        displayName = this.displayName,
        profileImageUrl = this.photoUrl,
        gender = gender,
        weight = this.weight,
        weightUnit = weightUnit,
        fitnessLevel = fitnessLevel,
        avatar = this.avatar,
        bio = this.bio,
        createdAt = this.createdAt ?: System.currentTimeMillis(),
        lastLoginAt = this.lastLoginAt,
        // TODO: 在重制workout模块后重新添加totalWorkoutCount
        // totalWorkoutCount = this.totalWorkoutCount ?: 0,
        weeklyActiveMinutes = this.weeklyActiveMinutes ?: 0,
        likesReceived = this.likesReceived ?: 0,
        isAnonymous = this.isAnonymous ?: false,
        subscriptionPlan = subscriptionPlan,
        subscriptionExpiryDate = this.subscriptionExpiryDate,
        userType = if (this.isAnonymous == true) UserType.ANONYMOUS else UserType.REGISTERED,
    )
}

/**
 * 将用户缓存实体映射为GymBroUser(旧版用户模型)
 *
 * 由于GymBroUser现在是User的类型别名，此方法用于向后兼容
 */
fun UserCacheEntity.toGymBroUser(): GymBroUser {
    // 先转换为User，然后作为GymBroUser返回(类型别名)
    return this.toUser()
}

/**
 * 将User领域模型转换为旧版GymBroUser模型
 *
 * 由于GymBroUser现在是User的类型别名，此方法直接返回User对象
 */
fun User.toGymBroUser(): GymBroUser {
    // 由于GymBroUser是User的类型别名，直接返回即可
    return this
}

// 扩展函数已删除 - 订阅相关UseCase已被清理，此映射函数不再需要
