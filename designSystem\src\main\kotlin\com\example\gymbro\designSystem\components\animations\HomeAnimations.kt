package com.example.gymbro.designSystem.components.animations

import androidx.compose.animation.EnterTransition
import androidx.compose.animation.ExitTransition
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import com.example.gymbro.designSystem.theme.motion.MotionDurations
import com.example.gymbro.designSystem.theme.motion.MotionSprings

/**
 * Home页面动画规范
 *
 * 基于GymBro统一动画Token系统
 * 提供Home页面所有组件的标准化动画效果
 */
object GymBroHomeAnimations {
    /**
     * 页面进入动画
     * 从右侧滑入并淡入
     */
    val pageEnterAnimation: EnterTransition =
        slideInHorizontally(
            animationSpec = tween(MotionDurations.S),
            initialOffsetX = { fullWidth -> fullWidth },
        ) +
            fadeIn(
                animationSpec = tween(MotionDurations.S),
            )

    /**
     * 页面退出动画
     * 向左侧滑出并淡出
     */
    val pageExitAnimation: ExitTransition =
        slideOutHorizontally(
            animationSpec = tween(MotionDurations.S),
            targetOffsetX = { fullWidth -> -fullWidth },
        ) +
            fadeOut(
                animationSpec = tween(MotionDurations.S),
            )

    /**
     * 组件进入动画
     * 缩放进入并淡入
     */
    val componentEnterAnimation: EnterTransition =
        scaleIn(
            animationSpec = MotionSprings.MEDIUM,
            initialScale = 0.9f,
        ) +
            fadeIn(
                animationSpec = tween(MotionDurations.XS),
            )

    /**
     * 组件退出动画
     * 缩放退出并淡出
     */
    val componentExitAnimation: ExitTransition =
        scaleOut(
            animationSpec = tween(MotionDurations.XS),
            targetScale = 0.9f,
        ) +
            fadeOut(
                animationSpec = tween(MotionDurations.XS),
            )

    /**
     * 内容淡入动画规格
     * 使用标准时长，无需Composable上下文
     */
    val contentFadeIn = tween<Float>(MotionDurations.S)

    /**
     * 内容淡出动画规格
     * 使用快速时长，无需Composable上下文
     */
    val contentFadeOut = tween<Float>(MotionDurations.XS)

    /**
     * 弹性动画规格
     * 用于按钮按压等微交互
     */
    val springAnimation = MotionSprings.MEDIUM
}

// === 向后兼容的别名 ===
@Deprecated("使用 GymBroHomeAnimations", ReplaceWith("GymBroHomeAnimations"))
typealias HomeAnimations = GymBroHomeAnimations
