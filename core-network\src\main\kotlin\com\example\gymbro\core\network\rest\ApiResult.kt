package com.example.gymbro.core.network.rest

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import java.io.IOException

/**
 * API结果统一封装
 *
 * 使用Either模式封装API调用结果，提供类型安全的错误处理
 */
sealed interface ApiResult<out T> {
    /**
     * 成功结果
     */
    data class Success<T>(val data: T) : ApiResult<T>

    /**
     * 失败结果
     */
    data class Error(val error: ApiError) : ApiResult<Nothing>

    /**
     * 检查是否为成功结果
     */
    val isSuccess: Boolean get() = this is Success

    /**
     * 检查是否为失败结果
     */
    val isError: Boolean get() = this is Error

    /**
     * 获取成功数据，失败时返回null
     */
    fun getOrNull(): T? = when (this) {
        is Success -> data
        is Error -> null
    }

    /**
     * 获取错误信息，成功时返回null
     */
    fun errorOrNull(): ApiError? = when (this) {
        is Success -> null
        is Error -> error
    }

    /**
     * 映射成功结果
     */
    fun <R> map(transform: (T) -> R): ApiResult<R> = when (this) {
        is Success -> Success(transform(data))
        is Error -> this
    }

    /**
     * 平铺映射
     */
    fun <R> flatMap(transform: (T) -> ApiResult<R>): ApiResult<R> = when (this) {
        is Success -> transform(data)
        is Error -> this
    }

    /**
     * 折叠结果
     */
    fun <R> fold(
        onSuccess: (T) -> R,
        onError: (ApiError) -> R,
    ): R = when (this) {
        is Success -> onSuccess(data)
        is Error -> onError(error)
    }

    companion object {
        /**
         * 创建成功结果
         */
        fun <T> success(data: T): ApiResult<T> = Success(data)

        /**
         * 创建失败结果
         */
        fun <T> error(error: ApiError): ApiResult<T> = Error(error)

        /**
         * 从可能抛出异常的操作创建结果
         */
        inline fun <T> catching(block: () -> T): ApiResult<T> = try {
            Success(block())
        } catch (e: Exception) {
            Error(ApiError.fromException(e))
        }
    }
}

/**
 * API错误类型
 */
sealed interface ApiError {
    /**
     * 网络离线
     */
    data object Offline : ApiError

    /**
     * HTTP错误
     */
    data class Http(val code: Int, val message: String) : ApiError

    /**
     * 解析错误
     */
    data class Parse(val raw: String, val cause: Throwable? = null) : ApiError

    /**
     * 网络错误
     */
    data class Network(val throwable: Throwable) : ApiError

    /**
     * 未知错误
     */
    data class Unknown(val throwable: Throwable) : ApiError

    /**
     * 超时错误
     */
    data class Timeout(val message: String = "Request timeout") : ApiError

    /**
     * 认证错误
     */
    data class Auth(val message: String = "Authentication failed") : ApiError

    /**
     * 服务器错误
     */
    data class Server(val code: Int, val message: String) : ApiError

    /**
     * 客户端错误
     */
    data class Client(val code: Int, val message: String) : ApiError

    companion object {
        /**
         * 从异常创建错误
         */
        fun fromException(exception: Throwable): ApiError = when (exception) {
            is IOException -> Network(exception)
            is java.net.SocketTimeoutException -> Timeout(exception.message ?: "Socket timeout")
            is java.net.UnknownHostException -> Offline
            else -> Unknown(exception)
        }

        /**
         * 从HTTP状态码创建错误
         */
        fun fromHttpCode(code: Int, message: String): ApiError = when (code) {
            in 400..499 -> when (code) {
                401, 403 -> Auth(message)
                else -> Client(code, message)
            }
            in 500..599 -> Server(code, message)
            else -> Http(code, message)
        }
    }

    /**
     * 获取错误描述
     */
    fun getDescription(): String = when (this) {
        is Offline -> "网络连接不可用"
        is Http -> "HTTP错误: $code $message"
        is Parse -> "数据解析失败: $raw"
        is Network -> "网络错误: ${throwable.message}"
        is Unknown -> "未知错误: ${throwable.message}"
        is Timeout -> message
        is Auth -> message
        is Server -> "服务器错误: $code $message"
        is Client -> "客户端错误: $code $message"
    }
}

/**
 * 扩展函数：将Flow转换为ApiResult
 */
fun <T> Flow<T>.asApiResult(): Flow<ApiResult<T>> =
    map<T, ApiResult<T>> { ApiResult.success(it) }
        .catch { emit(ApiResult.error(ApiError.fromException(it))) }

/**
 * 扩展函数：获取成功数据或抛出异常
 */
fun <T> ApiResult<T>.getOrThrow(): T = when (this) {
    is ApiResult.Success -> data
    is ApiResult.Error -> throw RuntimeException(error.getDescription())
}

/**
 * 扩展函数：获取成功数据或返回默认值
 */
fun <T> ApiResult<T>.getOrDefault(default: T): T = when (this) {
    is ApiResult.Success -> data
    is ApiResult.Error -> default
}
