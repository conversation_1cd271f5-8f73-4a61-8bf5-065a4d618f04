package com.example.gymbro.core.arch.mvi

import com.example.gymbro.core.ui.text.UiText

/**
 * MVI架构 - 副作用抽象 v6.0-EXTENDED
 *
 * 🔥 v6.0核心扩展：
 * 1. Profile/Workout专用效果 - 支持具体业务场景
 * 2. Box+LazyColumn架构效果 - 支持创新UI需求
 * 3. 流式内容效果 - 支持AI思考框等实时场景
 * 4. 数据验证效果 - 标准化表单验证
 * 5. 性能优化效果 - 支持LazyColumn滚动控制
 *
 * 用于处理一次性的UI操作，如导航、Toast、Dialog等
 * 与State不同，Effect是一次性的，不会保持状态
 *
 * @since v6.0 - 业务场景深度支持
 */
interface UiEffect

/**
 * 通用系统级副作用 - v6.0保持兼容
 * 这些副作用在所有Feature中都可能使用
 */
sealed class SystemEffect : UiEffect {

    /**
     * 显示错误消息
     */
    data class ShowError(val message: UiText) : SystemEffect()

    /**
     * 显示成功消息
     */
    data class ShowSuccess(val message: UiText) : SystemEffect()

    /**
     * 显示Toast消息
     */
    data class ShowToast(val message: UiText) : SystemEffect()

    /**
     * 导航到指定路由
     */
    data class Navigate(val route: String) : SystemEffect()

    /**
     * 导航返回
     */
    object NavigateBack : SystemEffect()

    /**
     * 导航返回到指定路由
     */
    data class NavigateBackTo(val route: String) : SystemEffect()

    /**
     * 滚动到顶部
     */
    object ScrollToTop : SystemEffect()

    /**
     * 滚动到底部
     */
    object ScrollToBottom : SystemEffect()

    /**
     * 隐藏键盘
     */
    object HideKeyboard : SystemEffect()

    /**
     * 显示确认对话框
     */
    data class ShowConfirmDialog(
        val title: UiText,
        val message: UiText,
        val confirmText: UiText,
        val cancelText: UiText,
    ) : SystemEffect()
}

/**
 * 🔥 新增：Box+LazyColumn架构专用效果
 * 支持Box+LazyColumn+Surface架构的特殊需求
 */
sealed class BoxLayoutEffect : UiEffect {

    /**
     * LazyColumn滚动到指定项目
     */
    data class ScrollToItem(
        val itemKey: String,
        val animated: Boolean = true,
    ) : BoxLayoutEffect()

    /**
     * LazyColumn滚动到指定索引
     */
    data class ScrollToIndex(
        val index: Int,
        val animated: Boolean = true,
    ) : BoxLayoutEffect()

    /**
     * 显示浮动Surface
     */
    data class ShowFloatingSurface(
        val content: UiText,
        val duration: Long = 3000L,
    ) : BoxLayoutEffect()

    /**
     * 隐藏浮动Surface
     */
    object HideFloatingSurface : BoxLayoutEffect()

    /**
     * 触发LazyColumn项目动画
     */
    data class AnimateItemChange(
        val itemKey: String,
        val animationType: ItemAnimationType = ItemAnimationType.HIGHLIGHT,
    ) : BoxLayoutEffect()

    /**
     * 刷新LazyColumn数据
     */
    data class RefreshList(
        val showIndicator: Boolean = true,
    ) : BoxLayoutEffect()
}

/**
 * LazyColumn项目动画类型
 */
enum class ItemAnimationType {
    HIGHLIGHT, // 高亮动画
    SHAKE, // 震动动画
    SCALE, // 缩放动画
    FADE, // 淡入淡出
}

/**
 * 🔥 新增：Profile模块专用效果
 * 为Profile功能提供专门的UI效果支持
 */
sealed class ProfileEffect : UiEffect {

    /**
     * 显示头像选择器
     */
    object ShowAvatarPicker : ProfileEffect()

    /**
     * 显示主题选择器
     */
    object ShowThemeSelector : ProfileEffect()

    /**
     * 显示用户信息编辑表单
     */
    data class ShowEditForm(
        val fieldType: ProfileFieldType,
    ) : ProfileEffect()

    /**
     * 保存用户资料成功
     */
    data class SaveProfileSuccess(
        val message: UiText = UiText.DynamicString("资料保存成功"),
    ) : ProfileEffect()

    /**
     * 显示个人成就
     */
    object ShowAchievements : ProfileEffect()

    /**
     * 显示训练统计
     */
    object ShowTrainingStats : ProfileEffect()

    /**
     * 显示偏好设置
     */
    object ShowPreferences : ProfileEffect()

    /**
     * 用户资料验证失败
     */
    data class ValidationFailed(
        val fieldType: ProfileFieldType,
        val error: UiText,
    ) : ProfileEffect()
}

/**
 * Profile字段类型
 */
enum class ProfileFieldType {
    NAME, // 姓名
    EMAIL, // 邮箱
    PHONE, // 电话
    BIRTHDAY, // 生日
    HEIGHT, // 身高
    WEIGHT, // 体重
    GOAL, // 健身目标
    EXPERIENCE, // 运动经验
}

/**
 * 🔥 新增：Workout模块专用效果
 * 为训练功能提供专门的UI效果支持
 */
sealed class WorkoutEffect : UiEffect {

    /**
     * 开始训练会话
     */
    data class StartWorkoutSession(
        val templateId: String? = null,
    ) : WorkoutEffect()

    /**
     * 暂停训练会话
     */
    object PauseWorkoutSession : WorkoutEffect()

    /**
     * 结束训练会话
     */
    object FinishWorkoutSession : WorkoutEffect()

    /**
     * 显示动作库选择器
     */
    object ShowExerciseLibrary : WorkoutEffect()

    /**
     * 显示组间休息计时器
     */
    data class ShowRestTimer(
        val duration: Int, // 秒数
    ) : WorkoutEffect()

    /**
     * 显示重量记录器
     */
    data class ShowWeightRecorder(
        val exerciseId: String,
        val currentWeight: Float? = null,
    ) : WorkoutEffect()

    /**
     * 显示训练模板创建器
     */
    object ShowTemplateCreator : WorkoutEffect()

    /**
     * 保存训练模板成功
     */
    data class SaveTemplateSuccess(
        val templateName: String,
    ) : WorkoutEffect()

    /**
     * 显示训练历史
     */
    object ShowWorkoutHistory : WorkoutEffect()

    /**
     * 训练数据同步
     */
    data class SyncWorkoutData(
        val showProgress: Boolean = true,
    ) : WorkoutEffect()
}

/**
 * 🔥 新增：ExerciseLibrary模块专用效果
 * 为动作库功能提供专门的UI效果支持
 */
sealed class ExerciseLibraryEffect : UiEffect {

    /**
     * 显示动作详情
     */
    data class ShowExerciseDetail(
        val exerciseId: String,
    ) : ExerciseLibraryEffect()

    /**
     * 显示动作视频
     */
    data class ShowExerciseVideo(
        val videoUrl: String,
    ) : ExerciseLibraryEffect()

    /**
     * 显示肌肉群筛选器
     */
    object ShowMuscleGroupFilter : ExerciseLibraryEffect()

    /**
     * 显示难度筛选器
     */
    object ShowDifficultyFilter : ExerciseLibraryEffect()

    /**
     * 显示器械类型筛选器
     */
    object ShowEquipmentFilter : ExerciseLibraryEffect()

    /**
     * 搜索动作
     */
    data class SearchExercises(
        val query: String,
    ) : ExerciseLibraryEffect()

    /**
     * 添加到收藏
     */
    data class AddToFavorites(
        val exerciseId: String,
        val message: UiText = UiText.DynamicString("已添加到收藏"),
    ) : ExerciseLibraryEffect()

    /**
     * 创建自定义动作
     */
    object CreateCustomExercise : ExerciseLibraryEffect()

    /**
     * 分享动作
     */
    data class ShareExercise(
        val exerciseId: String,
    ) : ExerciseLibraryEffect()
}

/**
 * 🔥 新增：流式内容效果
 * 专为AI思考框、实时消息等场景设计
 */
sealed class StreamingEffect : UiEffect {

    /**
     * 开始流式内容传输
     */
    data class StartStreaming(
        val streamingId: String,
        val contentType: StreamingContentType = StreamingContentType.TEXT,
    ) : StreamingEffect()

    /**
     * 更新流式内容
     */
    data class UpdateStreamingContent(
        val streamingId: String,
        val content: String,
        val progress: Float,
    ) : StreamingEffect()

    /**
     * 完成流式内容传输
     */
    data class CompleteStreaming(
        val streamingId: String,
        val finalContent: String,
    ) : StreamingEffect()

    /**
     * 取消流式内容传输
     */
    data class CancelStreaming(
        val streamingId: String,
        val reason: UiText = UiText.DynamicString("传输已取消"),
    ) : StreamingEffect()

    /**
     * 流式内容错误
     */
    data class StreamingError(
        val streamingId: String,
        val error: UiText,
    ) : StreamingEffect()

    /**
     * 显示流式进度指示器
     */
    data class ShowStreamingProgress(
        val streamingId: String,
        val progress: Float,
    ) : StreamingEffect()
}

/**
 * 流式内容类型
 */
enum class StreamingContentType {
    TEXT, // 文本内容
    MARKDOWN, // Markdown内容
    JSON, // JSON数据
    BINARY, // 二进制数据
}

/**
 * 🔥 新增：数据验证效果
 * 标准化表单验证和数据检查
 */
sealed class ValidationEffect : UiEffect {

    /**
     * 显示字段验证错误
     */
    data class ShowFieldError(
        val fieldName: String,
        val error: UiText,
        val shouldFocus: Boolean = true,
    ) : ValidationEffect()

    /**
     * 清除字段验证错误
     */
    data class ClearFieldError(
        val fieldName: String,
    ) : ValidationEffect()

    /**
     * 显示表单验证摘要
     */
    data class ShowValidationSummary(
        val errors: List<UiText>,
    ) : ValidationEffect()

    /**
     * 表单验证成功
     */
    data class ValidationSuccess(
        val message: UiText = UiText.DynamicString("验证通过"),
    ) : ValidationEffect()

    /**
     * 显示实时验证提示
     */
    data class ShowRealtimeValidation(
        val fieldName: String,
        val isValid: Boolean,
        val hint: UiText? = null,
    ) : ValidationEffect()
}

/**
 * 🔥 新增：权限效果
 * 处理权限请求和管理
 */
sealed class PermissionEffect : UiEffect {

    /**
     * 请求相机权限
     */
    object RequestCameraPermission : PermissionEffect()

    /**
     * 请求存储权限
     */
    object RequestStoragePermission : PermissionEffect()

    /**
     * 请求通知权限
     */
    object RequestNotificationPermission : PermissionEffect()

    /**
     * 显示权限说明对话框
     */
    data class ShowPermissionRationale(
        val permissionType: PermissionType,
        val message: UiText,
    ) : PermissionEffect()

    /**
     * 权限被拒绝
     */
    data class PermissionDenied(
        val permissionType: PermissionType,
        val isPermanentlyDenied: Boolean = false,
    ) : PermissionEffect()

    /**
     * 权限授予成功
     */
    data class PermissionGranted(
        val permissionType: PermissionType,
    ) : PermissionEffect()
}

/**
 * 权限类型
 */
enum class PermissionType {
    CAMERA, // 相机权限
    STORAGE, // 存储权限
    NOTIFICATION, // 通知权限
    LOCATION, // 位置权限
    MICROPHONE, // 麦克风权限
}

/**
 * 🔥 新增：网络效果
 * 处理网络相关的UI反馈
 */
sealed class NetworkEffect : UiEffect {

    /**
     * 显示网络连接状态
     */
    data class ShowNetworkStatus(
        val isConnected: Boolean,
        val networkType: NetworkType? = null,
    ) : NetworkEffect()

    /**
     * 显示数据同步状态
     */
    data class ShowSyncStatus(
        val isSync: Boolean,
        val progress: Float = 0f,
    ) : NetworkEffect()

    /**
     * 网络错误
     */
    data class NetworkError(
        val error: UiText,
        val canRetry: Boolean = true,
    ) : NetworkEffect()

    /**
     * 显示离线模式提示
     */
    data class ShowOfflineMode(
        val message: UiText = UiText.DynamicString("当前处于离线模式"),
    ) : NetworkEffect()
}

/**
 * 网络类型
 */
enum class NetworkType {
    WIFI, // WiFi网络
    MOBILE, // 移动网络
    ETHERNET, // 以太网
    UNKNOWN, // 未知类型
}

/**
 * 🔥 新增：便捷的Effect构建器
 * 提供DSL风格的Effect创建
 */
object EffectBuilder {

    /**
     * 构建Profile效果
     */
    fun profile(action: ProfileEffectBuilder.() -> ProfileEffect): ProfileEffect {
        return ProfileEffectBuilder().action()
    }

    /**
     * 构建Workout效果
     */
    fun workout(action: WorkoutEffectBuilder.() -> WorkoutEffect): WorkoutEffect {
        return WorkoutEffectBuilder().action()
    }

    /**
     * 构建Streaming效果
     */
    fun streaming(action: StreamingEffectBuilder.() -> StreamingEffect): StreamingEffect {
        return StreamingEffectBuilder().action()
    }
}

/**
 * Profile效果构建器
 */
class ProfileEffectBuilder {
    fun showEditForm(fieldType: ProfileFieldType) = ProfileEffect.ShowEditForm(fieldType)
    fun saveSuccess(
        message: UiText = UiText.DynamicString("保存成功"),
    ) = ProfileEffect.SaveProfileSuccess(message)
    fun validationFailed(
        fieldType: ProfileFieldType,
        error: UiText,
    ) = ProfileEffect.ValidationFailed(fieldType, error)
    fun showAvatarPicker() = ProfileEffect.ShowAvatarPicker
    fun showThemeSelector() = ProfileEffect.ShowThemeSelector
}

/**
 * Workout效果构建器
 */
class WorkoutEffectBuilder {
    fun startSession(templateId: String? = null) = WorkoutEffect.StartWorkoutSession(templateId)
    fun showRestTimer(duration: Int) = WorkoutEffect.ShowRestTimer(duration)
    fun showWeightRecorder(
        exerciseId: String,
        currentWeight: Float? = null,
    ) = WorkoutEffect.ShowWeightRecorder(exerciseId, currentWeight)
    fun saveTemplateSuccess(templateName: String) = WorkoutEffect.SaveTemplateSuccess(templateName)
}

/**
 * Streaming效果构建器
 */
class StreamingEffectBuilder {
    fun start(streamingId: String, contentType: StreamingContentType = StreamingContentType.TEXT) =
        StreamingEffect.StartStreaming(streamingId, contentType)
    fun update(streamingId: String, content: String, progress: Float) =
        StreamingEffect.UpdateStreamingContent(streamingId, content, progress)
    fun complete(streamingId: String, finalContent: String) =
        StreamingEffect.CompleteStreaming(streamingId, finalContent)
    fun cancel(streamingId: String, reason: UiText = UiText.DynamicString("取消")) =
        StreamingEffect.CancelStreaming(streamingId, reason)
}
