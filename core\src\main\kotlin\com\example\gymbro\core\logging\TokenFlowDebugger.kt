package com.example.gymbro.core.logging

import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * TOKEN-FLOW 调试器
 *
 * 专门用于调试 ThinkingBox 流式响应问题的工具类
 */
@Singleton
class TokenFlowDebugger
    @Inject
    constructor(
        private val timberManager: TimberManager,
        private val loggingController: LoggingController,
    ) {
        companion object {
            private const val TAG = "TokenFlowDebugger"
        }

        /**
         * 启用 TOKEN-FLOW 调试模式
         *
         * 这将启用所有与流式响应相关的详细日志
         */
        fun enableDebugMode() {
            try {
                loggingController.applyPreset(LoggingController.LoggingPreset.TOKEN_FLOW_DEBUG)

                Timber.tag(TAG).i("🚀 TOKEN-FLOW 调试模式已启用")
                Timber.tag(TAG).i("📋 现在将显示以下日志：")
                Timber.tag(TAG).i("  - 🔗 SSE连接建立")
                Timber.tag(TAG).i("  - 📨 SSE事件接收")
                Timber.tag(TAG).i("  - 🔍 SSE数据解析")
                Timber.tag(TAG).i("  - 🚀 Token路由")
                Timber.tag(TAG).i("  - 🎯 ConversationScope发射")
                Timber.tag(TAG).i("  - 📥 ThinkingBox接收")

                printUsageInstructions()
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "❌ 启用TOKEN-FLOW调试模式失败")
            }
        }

        /**
         * 关闭 TOKEN-FLOW 调试模式
         */
        fun disableDebugMode() {
            try {
                timberManager.disableTokenFlowDebugLogs()
                Timber.tag(TAG).i("🔒 TOKEN-FLOW 调试模式已关闭")
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "❌ 关闭TOKEN-FLOW调试模式失败")
            }
        }

        /**
         * 打印使用说明
         */
        private fun printUsageInstructions() {
            val instructions =
                buildString {
                    appendLine("=== TOKEN-FLOW 调试使用说明 ===")
                    appendLine()
                    appendLine("🔍 关键日志标签过滤命令：")
                    appendLine("adb logcat | grep TOKEN-FLOW")
                    appendLine()
                    appendLine("📋 调试步骤：")
                    appendLine("1. 发送AI消息")
                    appendLine("2. 查找 '🔗 [AdaptiveStreamClient] SSE连接已建立'")
                    appendLine("3. 查找 '📨 [AdaptiveStreamClient] 收到SSE事件'")
                    appendLine("4. 查找 '🚀 [AiCoachViewModel] 收到token'")
                    appendLine("5. 查找 '🎯 [ConversationScope] 准备发射token'")
                    appendLine("6. 查找 '📥 [parseTokenStream] 收到chunk'")
                    appendLine()
                    appendLine("❌ 如果某个步骤的日志缺失，说明问题在该环节")
                    appendLine()
                    appendLine("=== 使用说明结束 ===")
                }

            Timber.tag(TAG).i(instructions)
        }

        /**
         * 检查当前调试状态
         */
        fun checkDebugStatus(): String =
            try {
                val config = timberManager.getCurrentConfig()
                "TOKEN-FLOW 调试状态: $config"
            } catch (e: Exception) {
                "❌ 无法获取调试状态: ${e.message}"
            }

        /**
         * 快速诊断工具
         */
        fun quickDiagnose() {
            Timber.tag(TAG).i("🔍 开始快速诊断...")

            // 检查日志配置
            val status = checkDebugStatus()
            Timber.tag(TAG).i("📊 $status")

            // 提供诊断建议
            val suggestions =
                buildString {
                    appendLine("💡 快速诊断建议：")
                    appendLine("1. 确保已启用TOKEN-FLOW调试模式")
                    appendLine("2. 发送简单消息测试：'你好'")
                    appendLine("3. 观察日志输出，找到断点位置")
                    appendLine("4. 根据断点位置定位问题")
                }

            Timber.tag(TAG).i(suggestions)
        }

        /**
         * 生成日志过滤脚本
         */
        fun generateLogFilterScript(): String =
            buildString {
                appendLine("#!/bin/bash")
                appendLine("# TOKEN-FLOW 日志过滤脚本")
                appendLine()
                appendLine("echo '=== TOKEN-FLOW 调试日志 ==='")
                appendLine("adb logcat -c  # 清除旧日志")
                appendLine("adb logcat | grep -E 'TOKEN-FLOW|AdaptiveStreamClient|AiCoachViewModel|ConversationScope|parseTokenStream'")
            }
    }
