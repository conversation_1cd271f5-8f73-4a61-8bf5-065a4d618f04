package com.example.gymbro.core.error.recovery

import com.example.gymbro.core.ui.text.UiText

/**
 * 错误恢复行动接口
 * 定义用户可执行的恢复行动
 */
interface RecoveryAction {
    /**
     * 恢复行动的标题
     */
    val title: UiText

    /**
     * 恢复行动的描述
     */
    val description: UiText?

    /**
     * 执行恢复行动
     */
    suspend fun execute(): Boolean

    companion object {
        /**
         * 创建简单的恢复行动
         */
        fun create(
            title: UiText,
            description: UiText? = null,
            action: suspend () -> Boolean,
        ): RecoveryAction =
            object : RecoveryAction {
                override val title: UiText = title
                override val description: UiText? = description

                override suspend fun execute(): <PERSON>olean = action()
            }
    }
}
