package com.example.gymbro.data.workout.session.entity

import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * 会话自动保存实体 - SessionDB 自动保存快照
 *
 * 基于 04_Implementation_Plan_CleanSlate_SessionDB.md 设计
 * 存储训练会话的自动保存快照，用于崩溃恢复
 */
@Entity(
    tableName = "session_autosave",
    indices = [
        Index("sessionId"),
        Index("saveTime"),
        Index("saveType"),
        Index("isValid"),
    ],
)
data class SessionAutoSaveEntity(
    @PrimaryKey
    val id: String,

    // 关联信息
    val sessionId: String,

    // 快照信息
    val saveType: String, // AUTO, MANUAL, APP_BACKGROUND, CRASH_RECOVERY
    val saveTime: Long, // 保存时间戳
    val sessionSnapshot: String, // JSON格式的完整会话状态快照

    // 快照元数据
    val progressSnapshot: String, // 简化的进度信息
    val currentState: String, // IN_EXERCISE, IN_REST, BETWEEN_EXERCISES
    val nextAction: String, // 用户下一步应该做什么

    // 快照有效性
    val isValid: Boolean = true, // 快照是否有效
    val expiresAt: Long? = null, // 快照过期时间

    // 时间戳
    val createdAt: Long = System.currentTimeMillis(),
)
