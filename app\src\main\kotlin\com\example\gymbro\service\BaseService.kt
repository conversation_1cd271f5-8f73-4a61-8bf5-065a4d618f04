package com.example.gymbro.service

import android.app.Service
import android.content.Intent
import android.os.IBinder
import androidx.annotation.CallSuper
import com.example.gymbro.core.error.ErrorReporter
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

/**
 * 提供基本生命周期管理和依赖注入支持的服务基类。
 */
@AndroidEntryPoint
abstract class BaseService : Service(), CoroutineScope {

    @Inject
    lateinit var errorReporter: ErrorReporter

    private val job = SupervisorJob()
    override val coroutineContext: CoroutineContext
        get() = Dispatchers.Main + job

    override fun onCreate() {
        super.onCreate()
        injectDependencies()
    }

    /**
     * 强制子类处理绑定逻辑。
     */
    abstract override fun onBind(intent: Intent?): IBinder?

    @CallSuper
    override fun onDestroy() {
        super.onDestroy()
        coroutineContext.cancel()
    }

    /**
     * Dagger Hilt 注入。
     * 虽然使用了 @AndroidEntryPoint，但保留此方法可能有助于理解或未来的手动注入需求。
     */
    protected open fun injectDependencies() {
        // Hilt 会自动处理注入，通常不需要手动调用
        // (applicationContext as? GymBroApp)?.appComponent?.inject(this as BaseService)
    }
}
