package com.example.gymbro.core.ml.model

/**
 * 搜索权重配置 - Core-ML Layer Model
 *
 * @param vectorWeight 向量相似度权重
 * @param metadataWeight 元数据权重
 * @param recencyWeight 时效性权重
 * @param customWeights 自定义权重映射
 */
data class SearchWeightConfig(
    val vectorWeight: Float = 0.7f,
    val metadataWeight: Float = 0.2f,
    val recencyWeight: Float = 0.1f,
    val customWeights: Map<String, Float> = emptyMap(),
) {
    init {
        require(vectorWeight + metadataWeight + recencyWeight <= 1.0f) {
            "权重总和不能超过1.0"
        }
    }
}
