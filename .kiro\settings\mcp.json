{"mcpServers": {"mcp-feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced@latest"], "timeout": 600, "env": {"MCP_DEBUG": "false", "MCP_WEB_PORT": "9999"}, "autoApprove": ["interactive_feedback"]}, "code-reasoning": {"command": "npx", "args": ["-y", "@mettamatt/code-reasoning"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "memory-bank": {"command": "npx", "args": ["@neko0721/memory-bank-mcp"], "env": {}}}}