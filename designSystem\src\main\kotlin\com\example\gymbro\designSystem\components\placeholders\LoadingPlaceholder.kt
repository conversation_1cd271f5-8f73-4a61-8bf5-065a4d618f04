package com.example.gymbro.designSystem.components.placeholders

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme

/**
 * 标准化的加载状态占位符组件
 *
 * 为各种加载场景提供一致的视觉体验，
 * 支持自定义消息和样式配置。
 */
@Composable
fun LoadingPlaceholder(
    modifier: Modifier = Modifier,
    message: String = "加载中...",
    showMessage: Boolean = true,
    indicatorSize: androidx.compose.ui.unit.Dp = 32.dp
) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            CircularProgressIndicator(
                modifier = Modifier.size(indicatorSize),
                strokeWidth = 3.dp,
                color = MaterialTheme.colorScheme.primary
            )

            if (showMessage) {
                Text(
                    text = message,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

/**
 * 简化版加载指示器（适用于列表底部等小区域）
 */
@Composable
fun CompactLoadingIndicator(
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp),
        contentAlignment = Alignment.Center
    ) {
        CircularProgressIndicator(
            modifier = Modifier.size(20.dp),
            strokeWidth = 2.dp,
            color = MaterialTheme.colorScheme.primary
        )
    }
}

@GymBroPreview
@Composable
private fun LoadingPlaceholderPreview() {
    GymBroTheme {
        Surface {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(32.dp)
            ) {
                // 标准加载状态
                LoadingPlaceholder(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp),
                    message = "正在加载历史记录..."
                )

                // 紧凑型加载状态
                CompactLoadingIndicator()
            }
        }
    }
}
