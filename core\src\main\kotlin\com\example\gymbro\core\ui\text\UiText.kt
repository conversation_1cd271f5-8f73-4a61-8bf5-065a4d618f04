@file:Suppress("ktlint:standard:import-ordering")

package com.example.gymbro.core.ui.text

// import androidx.compose.ui.platform.LocalContext // Not directly needed if ResourceProvider handles context
import com.example.gymbro.core.resources.ResourceProvider
import com.example.gymbro.core.error.ErrorCode
import kotlinx.serialization.Serializable

/**
 * 表示在UI中显示的文本
 *
 * 这个类设计用于处理UI层需要显示的文本，完全独立于平台，支持主要类型：
 * 1. 动态字符串 - 直接的文本值，如从网络请求或数据库获取的文本
 * 2. 资源字符串 - 通过ResourceProvider接口获取的字符串资源
 * 3. 错误代码 - P3阶段新增：基于ErrorCode的错误消息，支持Error-code mapping
 *
 * 注意：此文件已从core/util移至core/ui/text，作为包结构优化的一部分
 * P3阶段更新：添加ErrorCode支持，实现Error-code mapping pattern
 */
@Serializable
sealed class UiText {
    /**
     * 代表一个动态字符串，如从网络请求或数据库获取的文本
     */
    @Serializable
    data class DynamicString(
        val value: String,
    ) : UiText()

    /**
     * 代表一个通过ResourceProvider获取的字符串资源
     *
     * @param resId 资源标识符
     * @param args 格式化参数
     */
    @Serializable
    data class StringResource(
        val resId: Int,
        val args: List<String>,
    ) : UiText()

    /**
     * 代表基于ErrorCode的错误文本
     * P3阶段新增：支持Error-code mapping pattern
     * ErrorCode在core层定义，由app层的ErrorCodeMapper映射到具体字符串资源
     *
     * @param errorCode 错误代码
     * @param args 格式化参数
     */
    @Serializable
    data class ErrorCode(
        val errorCode: com.example.gymbro.core.error.ErrorCode,
        val args: List<String> = emptyList(),
    ) : UiText() {
        constructor(
            errorCode: com.example.gymbro.core.error.ErrorCode,
            vararg args: String,
        ) : this(errorCode, args.toList())
    }

    /**
     * 表示空文本
     * 用于表示未提供任何文本内容的情况
     */
    @Serializable
    data object Empty : UiText()

    /**
     * 将UiText转换为字符串 (non-composable, requires ResourceProvider instance)
     *
     * @param resourceProvider 资源提供者，用于获取字符串资源
     * @return 格式化后的字符串
     */
    fun asString(resourceProvider: ResourceProvider): String =
        when (this) {
            is DynamicString -> value
            is StringResource -> resourceProvider.getString(resId, *args.toTypedArray())
            is ErrorCode -> {
                // ErrorCode类型需要通过ErrorCodeMapper处理，这里提供fallback
                // 实际使用中应该通过app层的ErrorCodeMapper进行映射
                "Error: ${errorCode.code} (${errorCode.category})"
            }
            is Empty -> ""
        }

    companion object {
        /**
         * 创建一个字符串资源UiText
         *
         * @param resId 资源标识符
         * @param args 格式化参数
         * @return UiText实例
         */
        fun stringResource(
            resId: Int,
            vararg args: String,
        ): UiText = StringResource(resId, args.toList())

        /**
         * 创建一个ErrorCode类型的UiText
         * P3阶段新增：支持Error-code mapping
         *
         * @param errorCode 错误代码
         * @param args 格式化参数
         * @return UiText.ErrorCode实例
         */
        fun errorCode(
            errorCode: com.example.gymbro.core.error.ErrorCode,
            vararg args: String,
        ): UiText = ErrorCode(errorCode, args.toList())

        /**
         * 创建一个空文本UiText
         *
         * @return Empty UiText实例
         */
        fun empty(): UiText = Empty
    }
}
