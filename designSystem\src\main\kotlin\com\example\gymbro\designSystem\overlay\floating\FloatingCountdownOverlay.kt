package com.example.gymbro.designSystem.overlay.floating

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.*
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.workoutColors
import kotlin.math.roundToInt

/**
 * 悬浮倒计时覆盖层
 *
 * 显示可拖拽、可展开/收缩的悬浮倒计时界面
 * 支持暂停/继续、时间调整、关闭等操作
 */
@Composable
fun FloatingCountdownOverlay(
    service: FloatingCountdownService,
    modifier: Modifier = Modifier,
) {
    val state by service.state.collectAsStateWithLifecycle()

    state?.let { countdown ->
        FloatingCountdownCard(
            state = countdown,
            onDismiss = { service.stopFloating() },
            onTogglePause = { service.togglePause() },
            onAdjustTime = { adjustment -> service.adjustTime(adjustment) },
            modifier = modifier,
        )
    }
}

/**
 * 悬浮倒计时卡片
 *
 * 可拖拽的悬浮卡片，支持展开/收缩状态
 */
@Composable
private fun FloatingCountdownCard(
    state: FloatingCountdownState,
    onDismiss: () -> Unit,
    onTogglePause: () -> Unit,
    onAdjustTime: (Int) -> Unit,
    modifier: Modifier = Modifier,
) {
    var offset by remember { mutableStateOf(Offset.Zero) }
    var isExpanded by remember { mutableStateOf(false) }
    val density = LocalDensity.current

    // 脉冲动画
    val pulseAnimation by rememberInfiniteTransition(label = "pulse").animateFloat(
        initialValue = 1f,
        targetValue = 1.1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = EaseInOut),
            repeatMode = RepeatMode.Reverse,
        ),
        label = "pulse",
    )

    Popup(
        alignment = Alignment.TopEnd,
        properties = PopupProperties(
            dismissOnBackPress = false,
            dismissOnClickOutside = false,
        ),
    ) {
        Card(
            modifier = modifier
                .offset {
                    IntOffset(
                        (offset.x / density.density).roundToInt(),
                        (offset.y / density.density).roundToInt(),
                    )
                }
                .pointerInput(Unit) {
                    detectDragGestures { change, dragAmount ->
                        offset += dragAmount
                    }
                }
                .animateContentSize()
                .clickable { isExpanded = !isExpanded }
                .then(
                    if (state.remainingSeconds <= 10 && state.isRunning) {
                        Modifier.scale(pulseAnimation)
                    } else {
                        Modifier
                    },
                ),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.workoutColors.timerBackground,
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
            shape = RoundedCornerShape(16.dp),
        ) {
            AnimatedContent(
                targetState = isExpanded,
                transitionSpec = {
                    slideInVertically { -it } + fadeIn() togetherWith
                        slideOutVertically { -it } + fadeOut()
                },
                label = "expand_collapse",
            ) { expanded ->
                if (expanded) {
                    ExpandedCountdownContent(
                        state = state,
                        onDismiss = onDismiss,
                        onTogglePause = onTogglePause,
                        onAdjustTime = onAdjustTime,
                    )
                } else {
                    MiniCountdownContent(state = state)
                }
            }
        }
    }
}

/**
 * 迷你倒计时内容
 */
@Composable
private fun MiniCountdownContent(
    state: FloatingCountdownState,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier.padding(12.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp),
    ) {
        // 状态指示器
        Box(
            modifier = Modifier
                .size(8.dp)
                .clip(CircleShape)
                .background(
                    if (state.isRunning) {
                        MaterialTheme.workoutColors.successPrimary
                    } else {
                        MaterialTheme.workoutColors.warningPrimary
                    },
                ),
        )

        // 时间显示
        Text(
            text = state.formattedTimeShort,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.workoutColors.aiCoachText,
        )
    }
}

/**
 * 展开的倒计时内容
 */
@Composable
private fun ExpandedCountdownContent(
    state: FloatingCountdownState,
    onDismiss: () -> Unit,
    onTogglePause: () -> Unit,
    onAdjustTime: (Int) -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier.padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(12.dp),
    ) {
        // 标题和关闭按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                text = state.title,
                style = MaterialTheme.typography.titleSmall,
                color = MaterialTheme.workoutColors.aiCoachText,
                modifier = Modifier.weight(1f),
            )

            IconButton(
                onClick = onDismiss,
                modifier = Modifier.size(24.dp),
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "关闭",
                    tint = MaterialTheme.workoutColors.accentSecondary,
                    modifier = Modifier.size(16.dp),
                )
            }
        }

        // 时间显示
        Text(
            text = state.formattedTime,
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.workoutColors.aiCoachText,
        )

        // 进度条
        LinearProgressIndicator(
            progress = { state.progress },
            modifier = Modifier
                .fillMaxWidth()
                .height(6.dp)
                .clip(CircleShape),
            color = MaterialTheme.workoutColors.accentPrimary,
            trackColor = MaterialTheme.workoutColors.accentSecondary,
        )

        // 控制按钮
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // 减少时间
            IconButton(
                onClick = { onAdjustTime(-30) },
                modifier = Modifier.size(32.dp),
            ) {
                Icon(
                    imageVector = Icons.Default.Remove,
                    contentDescription = "减少30秒",
                    tint = MaterialTheme.workoutColors.accentSecondary,
                    modifier = Modifier.size(16.dp),
                )
            }

            // 暂停/继续
            IconButton(
                onClick = onTogglePause,
                modifier = Modifier.size(40.dp),
            ) {
                Icon(
                    imageVector = if (state.isRunning) Icons.Default.Pause else Icons.Default.PlayArrow,
                    contentDescription = if (state.isRunning) "暂停" else "继续",
                    tint = MaterialTheme.workoutColors.accentPrimary,
                    modifier = Modifier.size(20.dp),
                )
            }

            // 增加时间
            IconButton(
                onClick = { onAdjustTime(30) },
                modifier = Modifier.size(32.dp),
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "增加30秒",
                    tint = MaterialTheme.workoutColors.accentSecondary,
                    modifier = Modifier.size(16.dp),
                )
            }
        }
    }
}

@GymBroPreview
@Composable
private fun FloatingCountdownOverlayPreview() {
    GymBroTheme {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black.copy(alpha = 0.3f)),
        ) {
            // 模拟悬浮倒计时
            FloatingCountdownCard(
                state = FloatingCountdownState(
                    title = "组间休息",
                    totalSeconds = 120,
                    remainingSeconds = 45,
                    isRunning = true,
                ),
                onDismiss = {},
                onTogglePause = {},
                onAdjustTime = {},
            )
        }
    }
}
