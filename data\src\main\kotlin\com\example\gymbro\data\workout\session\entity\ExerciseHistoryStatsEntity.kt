package com.example.gymbro.data.workout.session.entity

import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * 动作历史统计实体 - SessionDB 统计数据表
 *
 * 基于 05_Final_Database_Architecture.md 设计
 * 用于快速查询历史记录和个人最佳成绩
 */
@Entity(
    tableName = "exercise_history_stats",
    indices = [
        Index("userId"),
        Index("exerciseId"),
        Index("lastPerformanceDate"),
        Index("lastUpdated"),
    ],
)
data class ExerciseHistoryStatsEntity(
    @PrimaryKey
    val id: String,

    // 关联信息
    val userId: String,
    val exerciseId: String,

    // 个人最佳记录
    val personalBestWeight: Double?,
    val personalBestReps: Int?,

    // 累计统计
    val totalSetsCompleted: Int,
    val totalVolumeLifted: Double,

    // 时间信息
    val lastPerformanceDate: Long,
    val lastUpdated: Long = System.currentTimeMillis(),
)
