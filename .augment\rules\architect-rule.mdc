---
description: GymBro Architectural Integrity and Design Decision Guidelines
globs:
alwaysApply: false
---

## Clean Architecture Layer Model

```
┌─────────────────────────────────────────┐
│           Presentation Layer            │ ← features/* + app
│  (ViewModel + Compose UI + Navigation)  │   (MVVM + Jetpack Compose)
├─────────────────────────────────────────┤
│             Domain Layer                │ ← domain
│     (UseCase + Repository接口)          │   (纯Kotlin，业务逻辑)
├─────────────────────────────────────────┤
│              Data Layer                 │ ← data
│  (Repository实现 + DataSource)          │   (Room + Retrofit + 映射)
├─────────────────────────────────────────┤
│          Infrastructure Layer           │ ← core + di + design_system
│    (工具类 + DI + UI组件)               │   (ModernResult + UiText + Hilt)
└─────────────────────────────────────────┘
```

**Dependency Direction**: Unidirectional flow only, no reverse or circular dependencies

## Module Architecture Matrix

| Layer | Required Modules | Optional Modules | Dependencies |
|-------|------------------|------------------|--------------|
| **Presentation** | `app`, `features/*` | `navigation`, `utils` | domain, design_system, di |
| **Domain** | `domain` | – | core |
| **Data** | `data` | `remote-*`, `local-*` | core, domain |
| **Infrastructure** | `core`, `di`, `design_system` | `config`, `scripts` | – |

**Module Isolation Rules**:
- Horizontal modules (core, data, domain) cannot depend on same-layer modules
- Feature modules (features/*) cannot depend on each other
- All dependencies must follow layer hierarchy

## Dependency Rules Enforcement

### 1. Unidirectional Dependencies
```
✅ ALLOWED:
Presentation → Domain → Data → Infrastructure
All layers → Core (shared utilities)
All layers → DI (injection only)

❌ PROHIBITED:
Domain → Data (reverse dependency)
Data → Presentation (layer skipping)
Feature → Feature (horizontal coupling)
Any circular dependencies
```

### 2. Dependency Inversion Principle
```kotlin
// ✅ REQUIRED: Interface in Domain, Implementation in Data
// Domain layer
interface ChatRepository {
    suspend fun getChatHistory(): ModernResult<List<ChatMsg>>
}

// Data layer
class ChatRepositoryImpl @Inject constructor(...) : ChatRepository

// DI layer binding
@Binds
abstract fun bindChatRepository(impl: ChatRepositoryImpl): ChatRepository
```

### 3. Interface-First Design
- All Repository interfaces defined in Domain layer
- All UseCase classes depend on Repository interfaces
- All ViewModel classes depend on UseCase interfaces
- Concrete implementations bound in DI layer

## Communication Patterns

### Presentation Layer Communication
```kotlin
// UI → ViewModel: UiEvent (sealed class)
sealed interface ChatUiEvent {
    data class SendMessage(val text: String) : ChatUiEvent
    object ClearHistory : ChatUiEvent
}

// ViewModel → UI: StateFlow<UiState>
data class ChatUiState(
    val messages: List<ChatMsg> = emptyList(),
    val isLoading: Boolean = false,
    val errorMessage: UiText? = null
)

// ViewModel → UseCase: suspend fun / Flow
viewModelScope.launch {
    streamAnswerUseCase(history, newMsg).collect { result ->
        // Handle result
    }
}
```

### Domain Layer Communication
```kotlin
// UseCase → Repository: Interface calls
class StreamAnswerUseCase @Inject constructor(
    private val repository: ChatRepository,  // Interface, not implementation
    @IoDispatcher private val dispatcher: CoroutineDispatcher
) {
    operator fun invoke(
        history: List<ChatMsg>,
        newMsg: ChatMsg
    ): Flow<ModernResult<ChatMsg>> =
        repository.streamAnswer(history, newMsg)
            .flowOn(dispatcher)
}
```

### Data Layer Communication
```kotlin
// Repository → DataSource: Local + Remote coordination
class ChatRepositoryImpl @Inject constructor(
    private val remoteDataSource: ChatRemoteDataSource,
    private val localDataSource: ChatLocalDataSource,
    private val mapper: ChatMapper
) : ChatRepository {
    
    override suspend fun getChatHistory(): ModernResult<List<ChatMsg>> {
        return try {
            val entities = localDataSource.getChatHistory()
            ModernResult.Success(entities.toDomain())
        } catch (e: Exception) {
            ModernResult.Error(e.toModernDataError())
        }
    }
}
```

## Interface Design Standards

### SOLID Principles Enforcement
```kotlin
// ✅ Single Responsibility: One interface, one purpose
interface ChatRepository {
    // Only chat-related operations
}

interface UserRepository {
    // Only user-related operations
}

// ✅ Interface Segregation: Clients depend only on needed methods
interface ReadOnlyChatRepository {
    suspend fun getChatHistory(): ModernResult<List<ChatMsg>>
}

interface WritableChatRepository {
    suspend fun saveChatMessage(msg: ChatMsg): ModernResult<Unit>
}

// ✅ Dependency Inversion: Depend on abstractions
class ChatViewModel @Inject constructor(
    private val getChatHistoryUseCase: GetChatHistoryUseCase  // UseCase interface
)
```

### Repository Interface Standards
```kotlin
// ✅ REQUIRED: Consistent naming pattern
interface {Entity}Repository
interface ChatRepository
interface UserRepository
interface WorkoutRepository

// ✅ REQUIRED: ModernResult return types
suspend fun get{Entity}(): ModernResult<Entity>
suspend fun save{Entity}(entity: Entity): ModernResult<Unit>
fun observe{Entity}(): Flow<ModernResult<Entity>>

// ✅ REQUIRED: Clear operation naming
suspend fun getChatHistory(): ModernResult<List<ChatMsg>>
suspend fun saveChatMessage(msg: ChatMsg): ModernResult<Unit>
suspend fun clearChatHistory(): ModernResult<Unit>
```

### UseCase Interface Standards
```kotlin
// ✅ REQUIRED: Verb-Noun naming pattern
class GetChatHistoryUseCase
class StreamAnswerUseCase
class SaveWorkoutUseCase
class ClearChatHistoryUseCase

// ✅ REQUIRED: Operator invoke pattern
operator fun invoke(params): ModernResult<T>
operator fun invoke(params): Flow<ModernResult<T>>

// ✅ REQUIRED: Single responsibility
class StreamAnswerUseCase @Inject constructor(
    private val repository: ChatRepository
) {
    // Only handles streaming AI answers
    operator fun invoke(
        history: List<ChatMsg>,
        newMsg: ChatMsg
    ): Flow<ModernResult<ChatMsg>>
}
```

## Cross-Cutting Concerns Architecture

### Error Handling Architecture
```kotlin
// Domain Layer: Define error contracts
sealed class ModernResult<out T> {
    data class Success<T>(val data: T) : ModernResult<T>()
    data class Error(val error: ModernDataError) : ModernResult<Nothing>()
    object Loading : ModernResult<Nothing>()
}

// Data Layer: Convert exceptions to domain errors
try {
    val data = api.fetchData()
    ModernResult.Success(data)
} catch (e: Exception) {
    ModernResult.Error(e.toModernDataError(
        operationName = "fetchData",
        defaultUiMessage = UiText.StringResource(R.string.error_fetch)
    ))
}

// Presentation Layer: Handle errors in UI
when (result) {
    is ModernResult.Error -> {
        _uiState.update { 
            it.copy(errorMessage = errorHandler.handleError(result.error))
        }
    }
}
```

### Text Internationalization Architecture
```kotlin
// Domain Layer: Use UiText in entities
data class ChatMsg(
    val text: UiText,  // NOT String
    val createdAt: Instant
)

// Presentation Layer: Create UiText in ViewModel
UiText.DynamicString("Hello")
UiText.StringResource(R.string.greeting)

// UI Layer: Display UiText
@Composable
fun DisplayMessage(message: ChatMsg) {
    Text(text = message.text.asString())
}
```

### Dependency Injection Architecture
```kotlin
// Repository Binding (Abstract Module)
@Module
@InstallIn(SingletonComponent::class)
abstract class RepositoryModule {
    @Binds
    abstract fun bindChatRepository(impl: ChatRepositoryImpl): ChatRepository
}

// Provider Configuration (Object Module)
@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {
    @Provides
    @Singleton
    fun provideJson(): Json = Json {
        ignoreUnknownKeys = true
        coerceInputValues = true
    }
}

// Scope Management
@Singleton  // For truly shared instances
@ViewModelScoped  // For ViewModel-specific dependencies
@ActivityScoped  // For Activity-specific instances
```

## Quality Gates and Architectural Compliance

### Automated Architecture Validation
```bash
# Dependency direction validation
./gradlew checkModuleDependencies

# Layer boundary validation
./gradlew validateLayerBoundaries

# Interface compliance validation
./gradlew checkInterfaceCompliance

# Clean Architecture validation
./gradlew validateCleanArchitecture
```

### Code Quality Thresholds
| Metric | Threshold | Layer | Enforcement |
|--------|-----------|-------|-------------|
| **Test Coverage** | Domain≥90%, Data≥80%, ViewModel≥75% | All | CI/CD Pipeline |
| **Code Quality** | Zero warnings | All | Ktlint + Detekt |
| **Architecture** | No violations | All | Custom Lint Rules |
| **Performance** | Startup <2s, Memory <200MB | App | Performance Tests |

### Implementation Guide Compliance
- **Domain Layer**: Must follow `domain/IMPLEMENTATION_GUIDE.md`
- **Data Layer**: Must follow `data/IMPLEMENTATION_GUIDE.md`
- **DI Layer**: Must follow `di/IMPLEMENTATION_GUIDE.md`
- **Presentation Layer**: Must follow `features/IMPLEMENTATION_GUIDE.md`

## Architecture Evolution Strategy

### Design Decision Process
1. **RFC Creation**: Document architectural changes
2. **Implementation Guide Update**: Update relevant guides
3. **Code Migration**: Implement changes following guides
4. **Validation**: Ensure compliance with quality gates
5. **Documentation**: Update architecture documentation

### Deprecation Process
1. **@Deprecated Annotation**: Mark deprecated APIs
2. **Migration Guide**: Provide clear migration path
3. **Grace Period**: 6-month transition period
4. **Removal**: Remove deprecated code after grace period

### Architecture Review Cycle
- **Monthly**: Review implementation guide compliance
- **Quarterly**: Comprehensive architecture review
- **Annually**: Major architecture evolution planning

## Security Architecture Requirements

### Data Protection
- **Encryption**: Room @Encryption for sensitive data
- **Network**: TLS 1.3 + HSTS for all API calls
- **Storage**: AES-GCM for local data encryption

### Authentication Architecture
- **JWT**: 15-minute expiry with refresh tokens
- **Firebase**: Custom claims for AI token management
- **Token Storage**: Encrypted SharedPreferences only

### API Security
- **HTTPS**: All AI endpoints require HTTPS
- **Rate Limiting**: Implement client-side rate limiting
- **Token Rotation**: Automatic token refresh mechanism

## Performance Architecture

### Startup Optimization
- **Baseline Profiles**: Auto-generated for critical paths
- **Lazy Loading**: Defer non-critical module initialization
- **Hilt**: Optimize dependency injection graph

### Memory Management
- **Lifecycle Awareness**: Proper ViewModel scoping
- **Resource Cleanup**: Automatic resource disposal
- **Memory Monitoring**: LeakCanary integration

### UI Performance
- **Compose Optimization**: Proper state management
- **LazyColumn**: Efficient list rendering
- **Image Loading**: Coil with memory caching

---

**Architecture Compliance**: This file works with implementation guides to ensure Clean Architecture + MVVM compliance across all GymBro modules. All architectural decisions must reference and follow the established implementation guides.
