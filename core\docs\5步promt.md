以下内容分成 **两部分**：

---

## 📝 标签&内容 **规范要求**

| 序号 | 规则                                        | 关键点                                                                                                                                                                                                                                        |
| ---- | ------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1    | **<header/>** *(前端内部产生，模型不输出)*  | 仅彩虹 loading，占位直到收到 `<think>`。                                                                                                                                                                                                      |
| 2    | **\<think>…\</think>** *(可选一次)*         | 预思考草稿。<br> - 允许最多 1 段，**必须**位于 `<thinking>` 之前。<br> - 纯文本，前端以斜体打字机展示。                                                                                                                                       |
| 3    | **\<thinking> … \</thinking>** *(必需一次)* | 主思考块，只能出现一次。                                                                                                                                                                                                                      |
| 4    | **\<phase id="N"> … </phase>**              | - 置于 `<thinking>` 内，**至少 1 段**。<br> - `id` 为唯一递增数字。<br> - 内含：<br>  • **\<title>…\</title>** — 阶段标题，可多次更新同一 id。<br>  • 正文纯文本（无嵌套标签）。<br> - 只有在 *本段 title+正文完整* 时，才开始下一 `<phase>`. |
| 5    | **双重终止检查**                            | 仅当 **同时**检测到 `</thinking>` **和** `<final>` 时，思考框折叠为 SummaryCard。                                                                                                                                                             |
| 6    | **\<final> … </final>**                     | - 完整富文本 Markdown，由外部 *Coach* 模块渲染。<br> - 打字机速率约 **20 字符/秒**。                                                                                                                                                          |
| 7    | **禁止项**                                  | - 任何 `<phase:*>`、`🧠`、`RAW-CHUNK` 等调试标记。<br> - 不得在 XML 外输出可见字符。                                                                                                                                                           |
| 8    | **自检失败处理**                            | 若触发违规（如标签不闭合、非法标记），输出 `<<ParsingError>>` 并终止流。                                                                                                                                                                      |

---

## ✨ 规范合格的 **完整示例**

```xml
<think>
预思考：快速浏览用户目标与上下文…
</think>

<thinking>
  <phase id="1">
    <title>理解</title>
    我已理解用户想要 8 周增肌，并有家用哑铃和引体…
  </phase>

  <phase id="2">
    <title>分析</title>
    评估体重、周训练频次、恢复水平；识别薄弱肌群…
  </phase>

  <phase id="3">
    <title>规划</title>
    拆分上/下肢交替方案；每周 4 练，RPE 7-8…
  </phase>

  <phase id="4">
    <title>制作</title>
    生成具体训练表：<br>
    - Day 1：卧推 4×6-8 …<br>
    - Day 2：深蹲 4×6-8 …
  </phase>
</thinking>

<final>
## 8 周增肌计划大纲
- **周频次**：4
- **总 Volume**：25 – 30 sets/肌群/周
- **饮食**：热量盈余 300 kcal/天
- [ ] 自定义卡路里追踪
==注意==：每周进度照相并在 GymBro 打卡！
</final>
```

**符合点**

* `<think>` 在前，仅一次。
* `<thinking>` 内含 4 个 `<phase>`，`id` 递增且唯一。
* 每段 `<phase>` 拥有 1 个 `<title>` + 完整正文；正文结束后才开启下一段。
* `</thinking>` 紧接 `<final>`，满足折叠触发条件。
* `<final>` 提供 Markdown 富文本，无非法标签。

将以上规范植入解析&前端流程，即可保证：**单卡呈现 → 阶段完整追加 → 折叠 Summary → 富文本终稿** 整链路稳定运行。
