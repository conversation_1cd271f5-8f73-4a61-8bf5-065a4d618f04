# GymBro Core 模块文档导航

> **📅 更新日期**: 2025-06-16  
> **🎯 版本**: v9.0  
> **🏆 状态**: AI Pipeline 系统重构完成

## 🎯 **文档概览**

Core 模块是 GymBro 项目的核心基础设施，包含 AI Pipeline 系统、Prompt 构建、系统层配置等关键组件。

## 📚 **核心文档索引**

### **🔥 AI Pipeline 系统 (v9.0 新增)**

| 文档 | 描述 | 状态 |
|------|------|------|
| **[AI_PIPELINE_SYSTEM.md](AI_PIPELINE_SYSTEM.md)** | AI Pipeline 系统架构文档 | ✅ 完成 |
| **[PIPELINE_REFACTOR_COMPLETE_RECORD.md](PIPELINE_REFACTOR_COMPLETE_RECORD.md)** | PipelinePromptBuilder 重构完整记录 | ✅ 完成 |
| **[5步promt.md](5步promt.md)** | 5步 prompt 构建原始设计文档 | ✅ 完成 |
| **[hard-keyword-function-call.md](hard-keyword-function-call.md)** | 硬性关键词强制函数调用指南 | ✅ 完成 |
| **[pipeline-complete-integration-example.md](pipeline-complete-integration-example.md)** | 完整集成使用示例 | ✅ 完成 |
| **[pipeline-refactor-verification.md](pipeline-refactor-verification.md)** | 重构验证报告 | ✅ 完成 |

### **🏗️ 架构与设计**

| 文档 | 描述 | 状态 |
|------|------|------|
| **[funtioncall集成.md](funtioncall集成.md)** | Function Call 集成规范 | ✅ 完成 |
| **[workflow.md](workflow.md)** | 工作流程记录 | ✅ 完成 |

## 🚀 **快速开始**

### **了解 AI Pipeline 系统**
1. 阅读 **[AI_PIPELINE_SYSTEM.md](AI_PIPELINE_SYSTEM.md)** 了解系统架构
2. 查看 **[pipeline-complete-integration-example.md](pipeline-complete-integration-example.md)** 学习使用方法
3. 参考 **[hard-keyword-function-call.md](hard-keyword-function-call.md)** 了解高级功能

### **深入了解重构过程**
1. 阅读 **[PIPELINE_REFACTOR_COMPLETE_RECORD.md](PIPELINE_REFACTOR_COMPLETE_RECORD.md)** 了解重构全过程
2. 查看 **[5步promt.md](5步promt.md)** 了解原始设计思路
3. 参考 **[pipeline-refactor-verification.md](pipeline-refactor-verification.md)** 了解验证结果

## 🔧 **核心功能**

### **消息分离系统**
- 完全解决 role="system" 消息处理问题
- 确保 AI 不会回显系统指令
- 支持对话历史上下文

### **5步执行器**
- ANALYZE → RETRIEVE → PLAN → GENERATE → VERIFY
- 返回 Flow<PipelineEvent> 事件流
- 支持前端逐步渲染

### **Function Call 集成**
- 完整的 FunctionDescriptor 体系
- 4种 GymBro 专用函数
- 步骤化函数映射

### **硬性关键词**
- !fc_ 前缀强制函数调用
- 支持高级用户直接触发函数
- 完整的安全机制

## 📊 **架构特点**

### **Clean Architecture 合规**
- core 层保持纯净，不包含具体 AI 调用
- 严格的依赖方向控制
- 接口与实现分离

### **生产就绪**
- 编译验证通过
- 完整的文档覆盖
- 丰富的使用示例

### **扩展性强**
- 模块化设计
- 易于添加新功能
- 支持未来扩展

## 🎯 **使用场景**

### **标准对话流程**
```kotlin
pipelineBuilder.executeSteps("我想制定一个增肌训练计划")
```

### **强制函数调用**
```kotlin
pipelineBuilder.executeSteps("!fc_template 创建基础训练模板")
```

### **消息构建**
```kotlin
val messages = pipelineBuilder.buildChatMessages(
    systemLayer = SystemLayer.createGymBroSystem(),
    userInput = "用户输入",
    history = conversationHistory
)
```

## 🏆 **重构成就**

### **v9.0 里程碑**
- ✅ **100% 实现** 5步promt.md 文档要求
- ✅ **完全修复** role="system" 消息处理问题
- ✅ **集成完成** Function Call 和硬性关键词功能
- ✅ **架构合规** Clean Architecture + MVI 2.0 标准
- ✅ **文档完整** 提供完整使用指南和示例

### **技术指标**
- **编译状态**: BUILD SUCCESSFUL
- **文档覆盖**: 100%
- **架构合规**: Clean Architecture + MVI 2.0
- **扩展性**: 高（模块化设计）

## 🔗 **相关链接**

- **[GymBro 项目主页](../../README.md)** - 项目总体介绍
- **[Features 模块文档](../../features/)** - 功能模块文档
- **[Domain 层文档](../../domain/)** - 业务逻辑层文档
- **[Data 层文档](../../data/)** - 数据访问层文档

---

> **💡 提示**: 这个文档索引会随着 Core 模块的发展持续更新，建议收藏以便快速查找相关文档。

> **🚀 核心理念**: "Clean Architecture + MVI 架构，打造企业级 Android 应用标杆"
