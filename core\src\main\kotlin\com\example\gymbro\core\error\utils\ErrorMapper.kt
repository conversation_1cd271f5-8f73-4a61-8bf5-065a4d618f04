package com.example.gymbro.core.error.utils

import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.ui.text.UiText

/**
 * 错误映射器接口
 * 负责将各种异常映射为统一的ModernDataError类型
 *
 * 此接口是核心错误处理系统的重要组成部分，用于:
 * 1. 将各种类型的异常转换为标准的ModernDataError
 * 2. 确保错误消息的一致性和可读性
 * 3. 为错误分配适当的类型和类别
 */
interface ErrorMapper {
    /**
     * 将异常映射为ModernDataError
     *
     * @param exception 要映射的异常
     * @param errorMessage 可选的错误消息，用于覆盖默认消息
     * @return 映射后的ModernDataError
     */
    fun mapExceptionToError(
        exception: Throwable,
        errorMessage: UiText? = null,
    ): ModernDataError

    /**
     * 创建一个表示未知错误的ModernDataError
     *
     * @param message 可选的错误消息
     * @param operationName 操作名称，帮助定位错误来源
     * @return 表示未知错误的ModernDataError
     */
    fun createUnknownError(
        message: UiText? = null,
        operationName: String = "unknown",
    ): ModernDataError
}
