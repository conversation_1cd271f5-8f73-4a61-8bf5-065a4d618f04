package com.example.gymbro.data.shared.sync

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.data.local.dao.user.UserCacheEntityDao
import com.example.gymbro.data.local.entity.user.UserCacheEntity
import com.example.gymbro.data.remote.firebase.auth.AuthDataSource
import com.google.firebase.auth.FirebaseUser
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 认证数据同步服务
 * 负责在远程认证服务和本地存储之间同步用户数据
 */
@Singleton
class AuthDataSyncService
@Inject
constructor(
    private val authDataSource: AuthDataSource,
    private val userCacheEntityDao: UserCacheEntityDao,
) {
    private val coroutineScope = CoroutineScope(Dispatchers.IO)
    private val syncStatus = MutableStateFlow(SyncStatus.IDLE)

    init {
        // 监听认证状态变化，自动同步数据
        observeAuthChanges()
    }

    /**
     * 观察认证状态变化
     */
    private fun observeAuthChanges() {
        coroutineScope.launch {
            authDataSource.getAuthStateFlow().collect { isAuthenticated ->
                if (isAuthenticated) {
                    // 用户已认证，同步数据
                    val userId = authDataSource.getCurrentUserId()
                    if (userId != null) {
                        syncUserData(userId)
                    }
                } else {
                    // 用户未认证，清理数据
                    Timber.d("User not authenticated, clearing local data")
                }
            }
        }
    }

    /**
     * 同步用户数据
     * @param userId 用户ID
     */
    suspend fun syncUserData(userId: String) =
        withContext(Dispatchers.IO) {
            syncStatus.value = SyncStatus.SYNCING
            try {
                // 从远程获取用户数据
                val firebaseUserResult = authDataSource.getCurrentUser()

                when (firebaseUserResult) {
                    is ModernResult.Success -> {
                        val firebaseUser = firebaseUserResult.data
                        if (firebaseUser != null) {
                            // 获取已有的用户缓存（如果存在）
                            val existingUserCache = userCacheEntityDao.getUserCacheById(userId)

                            // 将Firebase用户转换为用户缓存实体
                            val userCacheEntity =
                                mapFirebaseUserToCacheEntity(firebaseUser, existingUserCache)

                            // 保存到本地数据库
                            userCacheEntityDao.upsertUserCache(userCacheEntity)

                            // 同步成功
                            syncStatus.value = SyncStatus.SUCCESS
                            Timber.d("用户数据同步成功: $userId")
                        } else {
                            // 同步失败，无用户数据
                            syncStatus.value = SyncStatus.ERROR
                            Timber.e("同步失败: Firebase用户为null")
                        }
                    }
                    is ModernResult.Error -> {
                        // 同步失败，获取用户数据出错
                        syncStatus.value = SyncStatus.ERROR
                        Timber.e("同步失败: 获取Firebase用户失败 - ${firebaseUserResult.error.uiMessage}")
                    }
                    is ModernResult.Loading -> {
                        // 仍在加载中，保持同步状态
                        Timber.d("Firebase用户数据仍在加载中")
                    }
                }
            } catch (e: Exception) {
                // 同步出错
                syncStatus.value = SyncStatus.ERROR
                Timber.e(e, "用户数据同步失败: $userId")
            }
        }

    /**
     * 将Firebase用户转换为用户缓存实体
     * 保留现有实体的数据，仅更新认证相关的信息
     */
    private fun mapFirebaseUserToCacheEntity(
        firebaseUser: FirebaseUser,
        existingEntity: UserCacheEntity?,
    ): UserCacheEntity {
        val now = System.currentTimeMillis()
        return UserCacheEntity(
            userId = firebaseUser.uid,
            email = firebaseUser.email,
            displayName = firebaseUser.displayName,
            photoUrl = firebaseUser.photoUrl?.toString(),
            phoneNumber = firebaseUser.phoneNumber,
            // 保留现有实体的其他数据或使用默认值
            username = existingEntity?.username ?: "",
            bio = existingEntity?.bio ?: "",
            settingsJson = existingEntity?.settingsJson ?: "{}",
            privacySettingsJson = existingEntity?.privacySettingsJson ?: "{}",
            lastSynced = existingEntity?.lastSynced ?: now,
            isAnonymous = firebaseUser.isAnonymous,
            weeklyActiveMinutes = existingEntity?.weeklyActiveMinutes ?: 0,
            workoutDaysJson = existingEntity?.workoutDaysJson ?: "[]",
            likesReceived = existingEntity?.likesReceived ?: 0,
            isEmailVerified = firebaseUser.isEmailVerified,
            wechatId = existingEntity?.wechatId,
            anonymousId = existingEntity?.anonymousId,
            gender = existingEntity?.gender,
            weight = existingEntity?.weight,
            weightUnit = existingEntity?.weightUnit,
            fitnessLevel = existingEntity?.fitnessLevel,
            fitnessGoalsJson = existingEntity?.fitnessGoalsJson ?: "[]",
            preferredGym = existingEntity?.preferredGym,
            preferredWorkoutTimesJson = existingEntity?.preferredWorkoutTimesJson ?: "[]",
            preferredFoodsJson = existingEntity?.preferredFoodsJson ?: "[]",
            avatar = existingEntity?.avatar,
            allowPartnerMatching = existingEntity?.allowPartnerMatching ?: false,
            lastLoginAt = now,
            createdAt = existingEntity?.createdAt ?: now,
            themeMode = existingEntity?.themeMode ?: "system",
            languageCode = existingEntity?.languageCode ?: "zh-CN",
            measurementSystem = existingEntity?.measurementSystem ?: "metric",
            notificationsEnabled = existingEntity?.notificationsEnabled ?: true,
            soundsEnabled = existingEntity?.soundsEnabled ?: true,
            locationSharingEnabled = existingEntity?.locationSharingEnabled ?: false,
            notificationSettingsJson = existingEntity?.notificationSettingsJson ?: "{}",
            soundSettingsJson = existingEntity?.soundSettingsJson ?: "{}",
            backupSettingsJson = existingEntity?.backupSettingsJson ?: "{}",
            partnerMatchPreferencesJson = existingEntity?.partnerMatchPreferencesJson ?: "{}",
            blockedUsersJson = existingEntity?.blockedUsersJson ?: "[]",
            isSynced = true,
            lastModified = now,
            serverUpdatedAt = existingEntity?.serverUpdatedAt ?: 0L,
            userType = if (firebaseUser.isAnonymous) UserCacheEntity.USER_TYPE_ANONYMOUS else existingEntity?.userType ?: UserCacheEntity.USER_TYPE_REGISTERED,
            subscriptionPlan = existingEntity?.subscriptionPlan ?: UserCacheEntity.SUBSCRIPTION_PLAN_NONE,
            subscriptionExpiryDate = existingEntity?.subscriptionExpiryDate,
        )
    }

    /**
     * 同步状态枚举
     */
    enum class SyncStatus {
        IDLE, // 空闲
        SYNCING, // 同步中
        SUCCESS, // 同步成功
        ERROR, // 同步失败
    }
}
