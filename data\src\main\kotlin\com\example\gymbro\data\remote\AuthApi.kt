package com.example.gymbro.data.remote

import com.example.gymbro.data.model.auth.TokenDto
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * 认证API接口
 * 包含所有认证相关的网络请求
 */
interface AuthApi {

    /**
     * 用户注册
     * @param request 注册请求体
     * @return 包含TokenDto的Response
     */
    @POST("auth/register")
    suspend fun register(@Body request: Map<String, Any>): Response<TokenDto>

    /**
     * 用户登录
     * @param request 登录请求体
     * @return 包含TokenDto的Response
     */
    @POST("auth/login")
    suspend fun login(@Body request: Map<String, Any>): Response<TokenDto>

    /**
     * 使用Google账号登录
     * @param request Google认证请求体
     * @return 包含TokenDto的Response
     */
    @POST("auth/google")
    suspend fun loginWithGoogle(@Body request: Map<String, Any>): Response<TokenDto>

    /**
     * 使用手机号登录
     * @param request 手机号登录请求体（包含phone和firebaseIdToken）
     * @return 包含TokenDto的Response
     */
    @POST("auth/phone")
    suspend fun loginWithPhone(@Body request: Map<String, String>): Response<TokenDto>

    /**
     * 刷新Token
     * @param request 刷新Token请求体
     * @return 包含TokenDto的Response
     */
    @POST("auth/refresh")
    suspend fun refreshToken(@Body request: Map<String, Any>): Response<TokenDto>

    /**
     * 用户登出
     * @param request 登出请求体
     * @return 包含Unit的Response
     */
    @POST("auth/logout")
    suspend fun logout(@Body request: Map<String, Any>): Response<Unit>
}
