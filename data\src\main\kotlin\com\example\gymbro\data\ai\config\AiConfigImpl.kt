package com.example.gymbro.data.ai.config

import com.example.gymbro.domain.coach.config.AiConfig
import javax.inject.Inject
import javax.inject.Named
import javax.inject.Singleton

/**
 * AI配置实现
 *
 * 从依赖注入提供的配置参数读取AI相关配置信息
 * 遵循Clean Architecture原则，位于data层
 */
@Singleton
class AiConfigImpl
@Inject
constructor(
    @Named("google_api_key") private val googleApiKeyValue: String,
    @Named("google_base_url") private val googleBaseUrlValue: String,
    @Named("openai_api_key") private val openaiApiKeyValue: String,
    @Named("openai_base_url") private val openaiBaseUrlValue: String,
    @Named("deepseek_api_key") private val deepseekApiKeyValue: String,
    @Named("deepseek_base_url") private val deepseekBaseUrlValue: String,
    @Named("default_google_model") private val defaultGoogleModelValue: String,
    @Named("default_deepseek_model") private val defaultDeepseekModelValue: String,
    @Named("openai_default_model") private val openaiDefaultModelValue: String,
) : AiConfig {
    override val googleApiKey: String
        get() = googleApiKeyValue

    override val googleBaseUrl: String
        get() = googleBaseUrlValue

    override val openaiApiKey: String
        get() = openaiApiKeyValue

    override val deepseekApiKey: String
        get() = deepseekApiKeyValue

    override val deepseekBaseUrl: String
        get() = deepseekBaseUrlValue

    override val defaultGoogleModel: String
        get() = defaultGoogleModelValue

    override val defaultDeepseekModel: String
        get() = defaultDeepseekModelValue

    override val openaiBaseUrl: String
        get() = openaiBaseUrlValue

    override val openaiDefaultModel: String
        get() = openaiDefaultModelValue
}
