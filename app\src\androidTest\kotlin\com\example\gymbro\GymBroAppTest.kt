package com.example.gymbro

import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import org.junit.Assert.assertEquals
import org.junit.Test
import org.junit.runner.RunWith

/**
 * 应用程序基本测试
 */
@RunWith(AndroidJUnit4::class)
class GymBroAppTest {

    @Test
    fun useAppContext() {
        // 检查应用程序包名是否正确
        val appContext = InstrumentationRegistry.getInstrumentation().targetContext
        assertEquals("com.example.gymbro", appContext.packageName)
    }
}
