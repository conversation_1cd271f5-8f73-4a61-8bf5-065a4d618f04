package com.example.gymbro.data.remote.firebase.auth

import com.example.gymbro.core.error.extensions.toModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.auth.AuthErrors
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.ui.text.UiText
import com.google.firebase.auth.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * Firebase核心认证服务，负责基本的登录、注册、登出操作
 */
@Singleton
class FirebaseAuthService @Inject constructor(
    private val auth: FirebaseAuth,
) {

    /**
     * 同步检查用户是否已登录
     * @return 用户是否已登录
     */
    fun isUserLoggedInSynchronously(): Boolean = auth.currentUser != null

    /**
     * 同步检查当前用户是否为匿名用户
     * @return 当前用户是否是匿名用户
     */
    fun isAnonymousUserSynchronously(): Boolean = auth.currentUser?.isAnonymous == true

    /**
     * 获取当前用户ID，没有登录则返回null
     * @return 当前用户的ID或null
     */
    fun getCurrentUserId(): String? = auth.currentUser?.uid

    /**
     * 获取当前用户对象
     * @return 包含当前Firebase用户对象的ModernResult，或null如果未登录
     */
    fun getCurrentUser(): ModernResult<FirebaseUser?> = try {
        ModernResult.Success(auth.currentUser)
    } catch (e: Exception) {
        Timber.e(e, "获取当前用户失败")
        ModernResult.Error(
            e.toModernDataError(
                operationName = "FirebaseAuthService.getCurrentUser",
                uiMessage = UiText.DynamicString("获取当前用户失败"),
            ),
        )
    }

    /**
     * 检查当前用户是否为匿名用户
     * @return 当前用户是否是匿名用户
     */
    fun isAnonymousUser(): Boolean = auth.currentUser?.isAnonymous == true

    /**
     * 使用邮箱密码登录
     * @param email 邮箱
     * @param password 密码
     * @return 包含用户ID的ModernResult，或包含错误的ModernResult
     */
    suspend fun loginWithEmail(
        email: String,
        password: String,
    ): ModernResult<String> {
        return try {
            val result = suspendCancellableCoroutine<AuthResult> { continuation ->
                auth.signInWithEmailAndPassword(email, password)
                    .addOnSuccessListener { continuation.resume(it) }
                    .addOnFailureListener { continuation.resumeWithException(it) }
            }
            val uid = result.user?.uid ?: throw FirebaseAuthInvalidUserException("User is null", "NULL_USER")
            ModernResult.Success(uid)
        } catch (e: Exception) {
            Timber.e(e, "邮箱登录失败")
            ModernResult.Error(
                DataErrors.DataError.access(
                    operationName = "FirebaseAuthService.loginWithEmail",
                    message = UiText.DynamicString("登录失败"),
                    entityType = "FirebaseAuth",
                    cause = e,
                    metadataMap = mapOf("login_method" to "email"),
                ),
            )
        }
    }

    /**
     * 使用手机凭证登录
     * @param credential 手机认证凭证
     * @return 包含用户ID的ModernResult，或包含错误的ModernResult
     */
    suspend fun loginWithPhone(credential: PhoneAuthCredential): ModernResult<String> {
        return try {
            val result = auth.signInWithCredential(credential).await()
            val user = result.user ?: return ModernResult.Error(
                AuthErrors.AuthError.unknown(
                    operationName = "FirebaseAuthService.loginWithPhone.userNotFound",
                    message = UiText.DynamicString("Authentication error: User not found after phone login"),
                    metadataMap = mapOf("login_method" to "phone"),
                ),
            )
            ModernResult.Success(user.uid)
        } catch (e: Exception) {
            Timber.e(e, "手机登录失败")
            ModernResult.Error(
                e.toModernDataError(
                    operationName = "FirebaseAuthService.loginWithPhone",
                    uiMessage = UiText.DynamicString("手机登录失败"),
                ),
            )
        }
    }

    /**
     * 使用邮箱密码注册
     * @param email 邮箱
     * @param password 密码
     * @return 包含用户ID的ModernResult，或包含错误的ModernResult
     */
    suspend fun registerWithEmail(
        email: String,
        password: String,
    ): ModernResult<String> {
        return withContext(Dispatchers.IO) {
            try {
                val result = suspendCancellableCoroutine<AuthResult> { continuation ->
                    auth.createUserWithEmailAndPassword(email, password)
                        .addOnSuccessListener { continuation.resume(it) }
                        .addOnFailureListener { continuation.resumeWithException(it) }
                }
                val uid = result.user?.uid ?: throw FirebaseAuthInvalidUserException("User is null", "NULL_USER")
                ModernResult.Success(uid)
            } catch (e: Exception) {
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "FirebaseAuthService.registerWithEmail",
                        message = UiText.DynamicString("注册失败"),
                        entityType = "FirebaseAuth",
                        cause = e,
                        metadataMap = mapOf("login_method" to "email"),
                    ),
                )
            }
        }
    }

    /**
     * 使用Google凭证登录
     * @param credential Google认证凭证
     * @return 包含用户ID的ModernResult，或包含错误的ModernResult
     */
    suspend fun loginWithGoogle(credential: AuthCredential): ModernResult<String> {
        return try {
            val result = auth.signInWithCredential(credential).await()
            val user = result.user ?: return ModernResult.Error(
                AuthErrors.AuthError.unknown(
                    operationName = "FirebaseAuthService.loginWithGoogle.userNotFound",
                    message = UiText.DynamicString("Authentication error: User not found after Google login"),
                    metadataMap = mapOf("login_method" to "google"),
                ),
            )
            ModernResult.Success(user.uid)
        } catch (e: Exception) {
            Timber.e(e, "Google登录失败")
            ModernResult.Error(
                e.toModernDataError(
                    operationName = "FirebaseAuthService.loginWithGoogle",
                    uiMessage = UiText.DynamicString("Google登录失败"),
                ),
            )
        }
    }

    /**
     * 使用WeChat登录
     * @param code WeChat授权码
     * @return 包含用户ID的ModernResult，或包含错误的ModernResult
     */
    fun loginWithWeChat(code: String): ModernResult<String> {
        // 注意: Firebase默认不支持WeChat登录，这里只是示例实现
        // 实际项目中需要通过自定义令牌或其他方式集成
        return ModernResult.Error(
            BusinessErrors.BusinessError.rule(
                operationName = "FirebaseAuthService.loginWithWeChat.unsupported",
                message = UiText.DynamicString("WeChat登录未实现"),
                metadataMap = mapOf("feature" to "wechat_login"),
            ),
        )
    }

    /**
     * 匿名登录
     * @return 包含用户UID的成功ModernResult，或包含错误的ModernResult
     */
    suspend fun loginAnonymously(): ModernResult<String> {
        return withContext(Dispatchers.IO) {
            try {
                val result = suspendCancellableCoroutine<AuthResult> { continuation ->
                    auth.signInAnonymously()
                        .addOnSuccessListener { continuation.resume(it) }
                        .addOnFailureListener { continuation.resumeWithException(it) }
                }
                val uid = result.user?.uid ?: throw FirebaseAuthInvalidUserException("User is null", "NULL_USER")
                ModernResult.Success(uid)
            } catch (e: Exception) {
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "FirebaseAuthService.loginAnonymously",
                        message = UiText.DynamicString("匿名登录失败"),
                        entityType = "FirebaseAuth",
                        cause = e,
                    ),
                )
            }
        }
    }

    /**
     * 登出当前用户
     * @return 表示操作成功的ModernResult.Success，或包含错误的ModernResult.Error
     */
    fun logout(): ModernResult<Unit> = try {
        auth.signOut()
        ModernResult.Success(Unit)
    } catch (e: Exception) {
        Timber.e(e, "退出登录失败")
        ModernResult.Error(
            e.toModernDataError(
                operationName = "FirebaseAuthService.logout",
                uiMessage = UiText.DynamicString("退出登录失败"),
            ),
        )
    }

    /**
     * 重新认证当前用户
     * @param credential 用于重新认证的凭证
     * @return 操作成功的ModernResult，或包含错误的ModernResult
     */
    suspend fun reAuthenticate(credential: AuthCredential): ModernResult<Unit> {
        return try {
            val user = auth.currentUser ?: return ModernResult.Error(
                AuthErrors.AuthError.unknown(
                    operationName = "FirebaseAuthService.reauthenticate.notLoggedIn",
                    message = UiText.DynamicString("Reauthentication failed: User not logged in"),
                    metadataMap = mapOf("operation" to "reauthenticate"),
                ),
            )
            user.reauthenticate(credential).await()
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "重新认证失败")
            ModernResult.Error(
                e.toModernDataError(
                    operationName = "FirebaseAuthService.reAuthenticate",
                    uiMessage = UiText.DynamicString("重新认证失败"),
                ),
            )
        }
    }

    /**
     * 获取ID令牌
     * @param forceRefresh 是否强制刷新
     * @return 包含ID令牌的ModernResult
     */
    suspend fun getIdToken(forceRefresh: Boolean): ModernResult<String> {
        return try {
            val user = auth.currentUser ?: return ModernResult.Error(
                AuthErrors.AuthError.unknown(
                    operationName = "FirebaseAuthService.getIdToken.notLoggedIn",
                    message = UiText.DynamicString("用户未登录"),
                    metadataMap = mapOf("operation" to "get_id_token"),
                ),
            )
            val tokenResult = user.getIdToken(forceRefresh).await()
            val token = tokenResult.token
            if (token.isNullOrEmpty()) {
                return ModernResult.Error(
                    AuthErrors.AuthError.unknown(
                        operationName = "FirebaseAuthService.getIdToken.tokenEmpty",
                        message = UiText.DynamicString("获取令牌失败"),
                        metadataMap = mapOf("operation" to "get_id_token"),
                    ),
                )
            }
            ModernResult.Success(token)
        } catch (e: Exception) {
            Timber.e(e, "获取ID令牌失败")
            ModernResult.Error(
                e.toModernDataError(
                    operationName = "FirebaseAuthService.getIdToken",
                    uiMessage = UiText.DynamicString("获取ID令牌失败"),
                ),
            )
        }
    }

    /**
     * 刷新用户令牌
     * @return 包含新令牌的ModernResult
     */
    suspend fun refreshUserToken(): ModernResult<String> = getIdToken(true)

    /**
     * 链接邮箱凭证到当前用户
     * @param uid 用户ID（暂时未使用，保持接口兼容性）
     * @param email 邮箱
     * @param password 密码
     * @return 操作结果
     */
    suspend fun linkEmailCredential(uid: String, email: String, password: String): ModernResult<Unit> {
        return try {
            val user = auth.currentUser ?: return ModernResult.Error(
                AuthErrors.AuthError.unknown(
                    operationName = "FirebaseAuthService.linkEmailCredential.notLoggedIn",
                    message = UiText.DynamicString("用户未登录"),
                    metadataMap = mapOf("operation" to "link_email"),
                ),
            )
            val credential = EmailAuthProvider.getCredential(email, password)
            user.linkWithCredential(credential).await()
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "链接邮箱凭证失败")
            ModernResult.Error(
                e.toModernDataError(
                    operationName = "FirebaseAuthService.linkEmailCredential",
                    uiMessage = UiText.DynamicString("链接邮箱凭证失败"),
                ),
            )
        }
    }

    /**
     * 链接手机凭证到当前用户
     * @param uid 用户ID（暂时未使用，保持接口兼容性）
     * @param verificationId 验证ID
     * @param verificationCode 验证码
     * @return 操作结果
     */
    suspend fun linkPhoneCredential(
        uid: String,
        verificationId: String,
        verificationCode: String,
    ): ModernResult<Unit> {
        return try {
            val user = auth.currentUser ?: return ModernResult.Error(
                AuthErrors.AuthError.unknown(
                    operationName = "FirebaseAuthService.linkPhoneCredential.notLoggedIn",
                    message = UiText.DynamicString("用户未登录"),
                    metadataMap = mapOf("operation" to "link_phone"),
                ),
            )
            val credential = PhoneAuthProvider.getCredential(verificationId, verificationCode)
            user.linkWithCredential(credential).await()
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "链接手机凭证失败")
            ModernResult.Error(
                e.toModernDataError(
                    operationName = "FirebaseAuthService.linkPhoneCredential",
                    uiMessage = UiText.DynamicString("链接手机凭证失败"),
                ),
            )
        }
    }

    /**
     * 将匿名用户升级为邮箱用户
     * @param email 邮箱
     * @param password 密码
     * @return 操作结果
     */
    suspend fun upgradeAnonymousWithEmail(email: String, password: String): ModernResult<Unit> {
        return try {
            val user = auth.currentUser
            if (user == null) {
                return ModernResult.Error(
                    AuthErrors.AuthError.unknown(
                        operationName = "FirebaseAuthService.upgradeAnonymousWithEmail.notLoggedIn",
                        message = UiText.DynamicString("用户未登录"),
                        metadataMap = mapOf("operation" to "upgrade_anonymous"),
                    ),
                )
            }
            if (!user.isAnonymous) {
                return ModernResult.Error(
                    AuthErrors.AuthError.unknown(
                        operationName = "FirebaseAuthService.upgradeAnonymousWithEmail.notAnonymous",
                        message = UiText.DynamicString("当前用户不是匿名用户"),
                        metadataMap = mapOf("operation" to "upgrade_anonymous"),
                    ),
                )
            }

            val credential = EmailAuthProvider.getCredential(email, password)
            user.linkWithCredential(credential).await()
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "匿名用户升级失败")
            ModernResult.Error(
                e.toModernDataError(
                    operationName = "FirebaseAuthService.upgradeAnonymousWithEmail",
                    uiMessage = UiText.DynamicString("匿名用户升级失败"),
                ),
            )
        }
    }

    /**
     * 将匿名用户升级为Google用户
     * @param idToken Google ID token
     * @return 操作结果
     */
    suspend fun upgradeAnonymousWithGoogle(idToken: String): ModernResult<Unit> {
        return try {
            val user = auth.currentUser
            if (user == null) {
                return ModernResult.Error(
                    AuthErrors.AuthError.unknown(
                        operationName = "FirebaseAuthService.upgradeAnonymousWithGoogle.notLoggedIn",
                        message = UiText.DynamicString("用户未登录"),
                        metadataMap = mapOf("operation" to "upgrade_anonymous"),
                    ),
                )
            }
            if (!user.isAnonymous) {
                return ModernResult.Error(
                    AuthErrors.AuthError.unknown(
                        operationName = "FirebaseAuthService.upgradeAnonymousWithGoogle.notAnonymous",
                        message = UiText.DynamicString("当前用户不是匿名用户"),
                        metadataMap = mapOf("operation" to "upgrade_anonymous"),
                    ),
                )
            }

            val credential = GoogleAuthProvider.getCredential(idToken, null)
            user.linkWithCredential(credential).await()
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "匿名用户升级失败")
            ModernResult.Error(
                e.toModernDataError(
                    operationName = "FirebaseAuthService.upgradeAnonymousWithGoogle",
                    uiMessage = UiText.DynamicString("匿名用户升级失败"),
                ),
            )
        }
    }

    /**
     * 观察认证状态变化
     * @return 包含当前FirebaseUser的Flow，用户未登录时返回null
     */
    fun observeAuthState(): Flow<FirebaseUser?> = callbackFlow {
        val authStateListener = FirebaseAuth.AuthStateListener { auth ->
            trySend(auth.currentUser)
        }

        auth.addAuthStateListener(authStateListener)

        // 发送初始状态
        trySend(auth.currentUser)

        awaitClose {
            auth.removeAuthStateListener(authStateListener)
        }
    }
}
