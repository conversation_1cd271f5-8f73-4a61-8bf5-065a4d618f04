package com.example.gymbro.core.error.types

/**
 * 简化的错误类别枚举
 *
 * 将相关错误类型合并为更少的核心类别，减少复杂度
 */
enum class ErrorCategory {
    /**
     * 网络相关错误
     * - 连接失败、超时、DNS解析等
     */
    NETWORK,

    /**
     * 数据相关错误
     * - 数据库操作、缓存、文件系统、数据同步等
     * - 合并了原有的 DATABASE, CACHE, RESOURCE, SYNC
     */
    DATA,

    /**
     * 认证授权相关错误
     * - 登录、权限验证、令牌管理等
     */
    AUTH,

    /**
     * 业务逻辑相关错误
     * - 业务规则、支付、订阅、用户操作、功能访问等
     * - 合并了原有的 BUSINESS, PAYMENT, SUBSCRIPTION, USER, FEATURE
     */
    BUSINESS,

    /**
     * 输入验证相关错误
     * - 表单验证、数据格式检查等
     */
    VALIDATION,

    /**
     * 系统相关错误
     * - 系统内部错误、配置问题、AI服务等
     * - 合并了原有的 SYSTEM, AI_SERVICE
     */
    SYSTEM,

    /**
     * 未知或未分类错误
     */
    UNKNOWN,
}

/**
 * 向后兼容的常量定义
 *
 * @deprecated 建议使用 ErrorCategory 枚举或 ErrorCategoryMapper
 */
@Deprecated("使用 ErrorCategory 枚举替代", ReplaceWith("ErrorCategory"))
object ErrorCategories {
    const val NETWORK = "NETWORK"
    const val DATA = "DATA" // 新的合并类别
    const val AUTH = "AUTH"
    const val BUSINESS = "BUSINESS" // 扩展的业务类别
    const val VALIDATION = "VALIDATION"
    const val SYSTEM = "SYSTEM" // 扩展的系统类别
    const val UNKNOWN = "UNKNOWN"

    // 兼容旧的类别名称
    @Deprecated("已合并到 DATA", ReplaceWith("DATA"))
    const val DATABASE = "DATA"

    @Deprecated("已合并到 DATA", ReplaceWith("DATA"))
    const val CACHE = "DATA"

    @Deprecated("已合并到 DATA", ReplaceWith("DATA"))
    const val RESOURCE = "DATA"

    @Deprecated("已合并到 DATA", ReplaceWith("DATA"))
    const val SYNC = "DATA"

    @Deprecated("已合并到 BUSINESS", ReplaceWith("BUSINESS"))
    const val PAYMENT = "BUSINESS"

    @Deprecated("已合并到 BUSINESS", ReplaceWith("BUSINESS"))
    const val SUBSCRIPTION = "BUSINESS"

    @Deprecated("已合并到 BUSINESS", ReplaceWith("BUSINESS"))
    const val USER = "BUSINESS"

    @Deprecated("已合并到 BUSINESS", ReplaceWith("BUSINESS"))
    const val FEATURE = "BUSINESS"

    @Deprecated("已合并到 SYSTEM", ReplaceWith("SYSTEM"))
    const val AI_SERVICE = "SYSTEM"
}
