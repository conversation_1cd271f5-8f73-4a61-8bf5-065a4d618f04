package com.example.gymbro.data.coach.entity

import androidx.room.*
import com.example.gymbro.data.local.entity.ChatSessionEntity

/**
 * 会话摘要实体
 *
 * 基于 history补充.md 规范设计，实现滑动窗口摘要压缩
 * 用于长期上下文管理和token使用优化
 */
@Entity(
    tableName = "session_summary",
    foreignKeys = [
        ForeignKey(
            entity = ChatSessionEntity::class,
            parentColumns = ["id"],
            childColumns = ["session_id"],
            onDelete = ForeignKey.CASCADE,
        ),
    ],
    indices = [
        Index(value = ["session_id"]),
        Index(value = ["range_start", "range_end"]),
        Index(value = ["created_at"]),
        Index(value = ["summary_type"]),
    ],
)
data class SessionSummaryEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    /**
     * 关联的会话ID
     */
    @ColumnInfo(name = "session_id")
    val sessionId: String,
    /**
     * 摘要范围开始消息ID
     */
    @ColumnInfo(name = "range_start")
    val rangeStart: Long,
    /**
     * 摘要范围结束消息ID
     */
    @ColumnInfo(name = "range_end")
    val rangeEnd: Long,
    /**
     * 摘要内容 (Markdown格式)
     */
    @ColumnInfo(name = "summary_content")
    val summaryContent: String,
    /**
     * 摘要类型
     * SLIDING_WINDOW, FULL_SESSION, MANUAL
     */
    @ColumnInfo(name = "summary_type")
    val summaryType: String = "SLIDING_WINDOW",
    /**
     * 摘要创建时间戳
     */
    @ColumnInfo(name = "created_at")
    val createdAt: Long,
    /**
     * 原始消息数量
     */
    @ColumnInfo(name = "original_message_count")
    val originalMessageCount: Int,
    /**
     * 原始token数量 (估算)
     */
    @ColumnInfo(name = "original_token_count")
    val originalTokenCount: Int,
    /**
     * 摘要token数量 (估算)
     */
    @ColumnInfo(name = "summary_token_count")
    val summaryTokenCount: Int,
    /**
     * 压缩比率 (0.0-1.0)
     */
    @ColumnInfo(name = "compression_ratio")
    val compressionRatio: Float,
    /**
     * 摘要生成模型
     */
    @ColumnInfo(name = "model_used")
    val modelUsed: String = "local-t5-small",
    /**
     * 摘要生成耗时 (毫秒)
     */
    @ColumnInfo(name = "generation_time_ms")
    val generationTimeMs: Long? = null,
    /**
     * 摘要质量评分 (0.0-1.0)
     * 可选的质量评估分数
     */
    @ColumnInfo(name = "quality_score")
    val qualityScore: Float? = null,
) {
    companion object {
        // 摘要类型常量
        const val TYPE_SLIDING_WINDOW = "SLIDING_WINDOW"
        const val TYPE_FULL_SESSION = "FULL_SESSION"
        const val TYPE_MANUAL = "MANUAL"

        // 滑动窗口配置
        const val SLIDING_WINDOW_SIZE = 15 // 每15条消息生成一次摘要
        const val MAX_SUMMARY_TOKEN_COUNT = 200 // 摘要最大token数
        const val TARGET_COMPRESSION_RATIO = 0.3f // 目标压缩比率

        // 模型配置
        const val DEFAULT_MODEL = "local-t5-small"
        const val FALLBACK_MODEL = "gpt-3.5-turbo"
    }

    /**
     * 检查摘要是否有效
     */
    fun isValid(): Boolean =
        summaryContent.isNotBlank() &&
            rangeStart <= rangeEnd &&
            originalMessageCount > 0 &&
            compressionRatio > 0.0f &&
            compressionRatio <= 1.0f

    /**
     * 计算token节省数量
     */
    fun getTokenSavings(): Int = originalTokenCount - summaryTokenCount

    /**
     * 检查是否为滑动窗口摘要
     */
    fun isSlidingWindow(): Boolean = summaryType == TYPE_SLIDING_WINDOW

    /**
     * 检查是否为全会话摘要
     */
    fun isFullSession(): Boolean = summaryType == TYPE_FULL_SESSION

    /**
     * 检查是否为手动摘要
     */
    fun isManual(): Boolean = summaryType == TYPE_MANUAL

    /**
     * 获取摘要的消息范围大小
     */
    fun getMessageRangeSize(): Long = rangeEnd - rangeStart + 1

    /**
     * 检查摘要是否过期
     * 基于创建时间判断是否需要重新生成
     */
    fun isExpired(maxAgeMs: Long = 7 * 24 * 60 * 60 * 1000L): Boolean { // 默认7天
        return System.currentTimeMillis() - createdAt > maxAgeMs
    }

    /**
     * 获取摘要效率评分
     * 综合考虑压缩比率、质量评分和生成时间
     */
    fun getEfficiencyScore(): Float {
        val compressionScore = compressionRatio
        val qualityScore = this.qualityScore ?: 0.5f
        val timeScore =
            if (generationTimeMs != null && generationTimeMs > 0) {
                // 生成时间越短分数越高，最大5秒为满分
                (5000f - generationTimeMs.coerceAtMost(5000L)) / 5000f
            } else {
                0.5f
            }

        return (compressionScore * 0.4f + qualityScore * 0.4f + timeScore * 0.2f)
    }
}

/**
 * 摘要生成请求
 */
data class SummaryGenerationRequest(
    val sessionId: String,
    val rangeStart: Long,
    val rangeEnd: Long,
    val summaryType: String = SessionSummaryEntity.TYPE_SLIDING_WINDOW,
    val modelPreference: String = SessionSummaryEntity.DEFAULT_MODEL,
    val maxTokens: Int = SessionSummaryEntity.MAX_SUMMARY_TOKEN_COUNT,
)

/**
 * 摘要生成结果
 */
data class SummaryGenerationResult(
    val success: Boolean,
    val summary: SessionSummaryEntity? = null,
    val error: String? = null,
    val generationTimeMs: Long,
    val modelUsed: String,
)

/**
 * 上下文注入数据
 * 用于将摘要注入到Prompt中
 */
data class ContextInjectionData(
    val recentMessages: List<String>, // 最近6条原始消息
    val summaries: List<SessionSummaryEntity>, // 最近3个摘要
    val totalTokenCount: Int, // 总token数估算
    val compressionRatio: Float, // 整体压缩比率
) {
    /**
     * 生成上下文注入的Prompt片段
     */
    fun generatePromptFragment(): String {
        val contextBuilder = StringBuilder()

        // 添加摘要上下文
        if (summaries.isNotEmpty()) {
            contextBuilder.append("## 对话历史摘要\n")
            summaries.forEachIndexed { index, summary ->
                contextBuilder.append("### 上下文 ${index + 1}\n")
                contextBuilder.append(summary.summaryContent)
                contextBuilder.append("\n\n")
            }
        }

        // 添加最近消息
        if (recentMessages.isNotEmpty()) {
            contextBuilder.append("## 最近对话\n")
            recentMessages.forEach { message ->
                contextBuilder.append(message)
                contextBuilder.append("\n")
            }
        }

        return contextBuilder.toString()
    }
}
