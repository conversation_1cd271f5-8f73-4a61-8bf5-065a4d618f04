package com.example.gymbro.app.version

import android.content.Context
import android.content.SharedPreferences
import com.example.gymbro.BuildConfig
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.region.RegionProvider
import com.example.gymbro.data.repository.region.RegionDetectionRepository
import com.example.gymbro.domain.shared.version.repository.VersionConfigRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.*
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import timber.log.Timber
import java.util.Locale
import java.util.TimeZone
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.time.Duration.Companion.hours

/**
 * 地区检测管理器
 *
 * 负责检测用户所在地区（CN vs 国际），并为整个应用提供统一的地区标准。
 * 所有模块的内容加载都应该参考此检测结果。
 *
 * 检测策略：
 * 1. IP地理位置检测（主要方式）
 * 2. 系统时区检测（备用方式）
 * 3. 系统语言检测（备用方式）
 * 4. 默认国际区域（兜底方案）
 */
@Singleton
class RegionDetectionManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val regionDetectionRepository: RegionDetectionRepository,
    private val versionConfigRepository: VersionConfigRepository,
) : RegionProvider {

    companion object {
        private const val PREFS_NAME = "region_detection"
        private const val KEY_DETECTED_REGION = "detected_region"
        private const val KEY_DETECTION_TIME = "detection_time"
        private const val KEY_DETECTION_METHOD = "detection_method"
        private const val KEY_CONFIDENCE = "confidence"

        // 缓存有效期：24小时
        private const val CACHE_VALIDITY_HOURS = 24

        // 最小更新间隔（小时）
        private const val MIN_UPDATE_INTERVAL_HOURS = 24
    }

    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

    private val _regionInfoState = MutableStateFlow<RegionInfo?>(null)

    // 为兼容性提供regionInfoState，但只在内部使用
    val regionInfoState: StateFlow<RegionInfo?> = _regionInfoState.asStateFlow()

    // 实现RegionProvider接口
    override val regionState: StateFlow<RegionProvider.UserRegion?> =
        _regionInfoState.map { regionInfo ->
            regionInfo?.region?.toProviderRegion()
        }.stateIn(
            scope = CoroutineScope(Dispatchers.Main + SupervisorJob()),
            started = SharingStarted.Lazily,
            initialValue = null,
        )

    /**
     * 内部用户地区枚举
     */
    enum class UserRegion {
        /** 中国大陆地区 */
        CN,

        /** 国际地区（包括港澳台） */
        INTERNATIONAL,

        ;

        /**
         * 转换为RegionProvider的UserRegion
         */
        fun toProviderRegion(): RegionProvider.UserRegion {
            return when (this) {
                CN -> RegionProvider.UserRegion.CN
                INTERNATIONAL -> RegionProvider.UserRegion.INTERNATIONAL
            }
        }
    }

    /**
     * 检测方法枚举
     */
    enum class DetectionMethod {
        /** IP地理位置检测 */
        IP_GEOLOCATION,

        /** 系统时区检测 */
        TIMEZONE,

        /** 系统语言检测 */
        LANGUAGE,

        /** 缓存结果 */
        CACHED,

        /** 默认兜底 */
        FALLBACK,
    }

    /**
     * 地区信息数据类
     */
    data class RegionInfo(
        val region: UserRegion,
        val detectedAt: Instant,
        val detectionMethod: DetectionMethod,
        val confidence: Float, // 0.0 - 1.0，检测置信度
    )

    /**
     * 初始化地区检测
     *
     * 应在应用启动时调用，会自动检测用户地区并缓存结果
     */
    suspend fun initializeRegionDetection() {
        try {
            Timber.d("开始初始化地区检测")

            // 首先尝试从缓存加载
            val cachedRegion = loadCachedRegion()
            if (cachedRegion != null && isCacheValid(cachedRegion)) {
                Timber.d("使用缓存的地区信息: ${cachedRegion.region}")
                val cachedWithMethod = cachedRegion.copy(detectionMethod = DetectionMethod.CACHED)
                _regionInfoState.value = cachedWithMethod

                return
            }

            // 缓存无效，重新检测
            val detectedRegion = performRegionDetection()
            _regionInfoState.value = detectedRegion

            // 保存到缓存
            saveRegionToCache(detectedRegion)

            Timber.i(
                "地区检测完成: ${detectedRegion.region}, 方法: ${detectedRegion.detectionMethod}, 置信度: ${detectedRegion.confidence}",
            )
        } catch (e: Exception) {
            Timber.e(e, "地区检测失败，使用默认国际区域")
            val fallbackRegion = RegionInfo(
                region = UserRegion.INTERNATIONAL,
                detectedAt = Clock.System.now(),
                detectionMethod = DetectionMethod.FALLBACK,
                confidence = 0.1f,
            )
            _regionInfoState.value = fallbackRegion

            saveRegionToCache(fallbackRegion)
        }
    }

    /**
     * 执行地区检测
     */
    private suspend fun performRegionDetection(): RegionInfo {
        // 1. 优先尝试IP地理位置检测
        val ipResult = detectByIpGeolocation()
        if (ipResult.confidence > 0.8f) {
            return ipResult
        }

        // 2. 尝试时区检测
        val timezoneResult = detectByTimezone()
        if (timezoneResult.confidence > 0.7f) {
            return timezoneResult
        }

        // 3. 尝试语言检测
        val languageResult = detectByLanguage()
        if (languageResult.confidence > 0.6f) {
            return languageResult
        }

        // 4. 返回置信度最高的结果
        return listOf(ipResult, timezoneResult, languageResult)
            .maxByOrNull { it.confidence } ?: ipResult
    }

    /**
     * 基于IP地理位置检测地区
     */
    private suspend fun detectByIpGeolocation(): RegionInfo {
        return try {
            Timber.d("开始IP地理位置检测")

            val regionResult = regionDetectionRepository.detectCurrentRegion()
            when (regionResult) {
                is ModernResult.Success -> {
                    val regionCode = regionResult.data
                    val region = if (regionCode == "CN") UserRegion.CN else UserRegion.INTERNATIONAL
                    val confidence = 0.9f // IP检测置信度最高

                    Timber.d("IP地理位置检测成功: $regionCode -> $region")
                    RegionInfo(
                        region = region,
                        detectedAt = Clock.System.now(),
                        detectionMethod = DetectionMethod.IP_GEOLOCATION,
                        confidence = confidence,
                    )
                }
                is ModernResult.Error -> {
                    Timber.w("IP地理位置检测失败: ${regionResult.error.message}")
                    RegionInfo(
                        region = UserRegion.INTERNATIONAL,
                        detectedAt = Clock.System.now(),
                        detectionMethod = DetectionMethod.IP_GEOLOCATION,
                        confidence = 0.1f, // 失败时置信度很低
                    )
                }
                is ModernResult.Loading -> {
                    // 不应该发生，但为了安全起见
                    RegionInfo(
                        region = UserRegion.INTERNATIONAL,
                        detectedAt = Clock.System.now(),
                        detectionMethod = DetectionMethod.IP_GEOLOCATION,
                        confidence = 0.1f,
                    )
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "IP地理位置检测异常")
            RegionInfo(
                region = UserRegion.INTERNATIONAL,
                detectedAt = Clock.System.now(),
                detectionMethod = DetectionMethod.IP_GEOLOCATION,
                confidence = 0.1f,
            )
        }
    }

    /**
     * 基于系统时区检测地区
     */
    private fun detectByTimezone(): RegionInfo {
        val timeZone = TimeZone.getDefault()
        val timeZoneId = timeZone.id

        Timber.d("检测到系统时区: $timeZoneId")

        val isChinaTimezone = timeZoneId.contains("Asia/Shanghai") ||
            timeZoneId.contains("Asia/Beijing") ||
            timeZoneId.contains("Asia/Chongqing") ||
            timeZoneId.contains("Asia/Harbin") ||
            timeZoneId.contains("Asia/Kashgar") ||
            timeZoneId.contains("Asia/Urumqi")

        val region = if (isChinaTimezone) UserRegion.CN else UserRegion.INTERNATIONAL
        val confidence = if (isChinaTimezone) 0.8f else 0.3f

        return RegionInfo(
            region = region,
            detectedAt = Clock.System.now(),
            detectionMethod = DetectionMethod.TIMEZONE,
            confidence = confidence,
        )
    }

    /**
     * 基于系统语言检测地区
     */
    private fun detectByLanguage(): RegionInfo {
        val locale = Locale.getDefault()
        val language = locale.language
        val country = locale.country

        Timber.d("检测到系统语言: $language, 国家: $country")

        val isChineseLocale = language == "zh" && country == "CN"
        val region = if (isChineseLocale) UserRegion.CN else UserRegion.INTERNATIONAL
        val confidence = if (isChineseLocale) 0.7f else 0.2f

        return RegionInfo(
            region = region,
            detectedAt = Clock.System.now(),
            detectionMethod = DetectionMethod.LANGUAGE,
            confidence = confidence,
        )
    }

    /**
     * 从缓存加载地区信息
     */
    private fun loadCachedRegion(): RegionInfo? {
        return try {
            val regionName = prefs.getString(KEY_DETECTED_REGION, null) ?: return null
            val detectionTime = prefs.getLong(KEY_DETECTION_TIME, 0L)
            val methodName = prefs.getString(KEY_DETECTION_METHOD, null) ?: return null
            val confidence = prefs.getFloat(KEY_CONFIDENCE, 0f)

            if (detectionTime == 0L) return null

            RegionInfo(
                region = UserRegion.valueOf(regionName),
                detectedAt = Instant.fromEpochMilliseconds(detectionTime),
                detectionMethod = DetectionMethod.valueOf(methodName),
                confidence = confidence,
            )
        } catch (e: Exception) {
            Timber.w(e, "加载缓存地区信息失败")
            null
        }
    }

    /**
     * 保存地区信息到缓存
     */
    private fun saveRegionToCache(regionInfo: RegionInfo) {
        try {
            prefs.edit()
                .putString(KEY_DETECTED_REGION, regionInfo.region.name)
                .putLong(KEY_DETECTION_TIME, regionInfo.detectedAt.toEpochMilliseconds())
                .putString(KEY_DETECTION_METHOD, regionInfo.detectionMethod.name)
                .putFloat(KEY_CONFIDENCE, regionInfo.confidence)
                .apply()

            Timber.d("地区信息已保存到缓存")
        } catch (e: Exception) {
            Timber.w(e, "保存地区信息到缓存失败")
        }
    }

    /**
     * 检查缓存是否有效
     */
    private fun isCacheValid(regionInfo: RegionInfo): Boolean {
        val now = Clock.System.now()
        val cacheAge = now - regionInfo.detectedAt
        val maxAge = CACHE_VALIDITY_HOURS.hours

        return cacheAge < maxAge
    }

    // 实现RegionProvider接口方法
    override fun getCurrentRegion(): RegionProvider.UserRegion? {
        return _regionInfoState.value?.region?.toProviderRegion()
    }

    override fun isChinaRegion(): Boolean {
        return getCurrentRegion() == RegionProvider.UserRegion.CN
    }

    override fun isInternationalRegion(): Boolean {
        return getCurrentRegion() == RegionProvider.UserRegion.INTERNATIONAL
    }

    /**
     * 强制重新检测地区（开发用途）
     *
     * 清除缓存并重新执行检测流程
     */
    suspend fun forceRedetection() {
        Timber.d("强制重新检测地区")
        clearCache()
        initializeRegionDetection()
    }

    /**
     * 手动设置地区（开发用途）
     *
     * 仅在开发环境下使用，用于测试不同地区的功能
     */
    fun setRegionForDevelopment(region: UserRegion) {
        if (BuildConfig.DEBUG) {
            val devRegionInfo = RegionInfo(
                region = region,
                detectedAt = Clock.System.now(),
                detectionMethod = DetectionMethod.FALLBACK,
                confidence = 1.0f,
            )
            _regionInfoState.value = devRegionInfo
            saveRegionToCache(devRegionInfo)
            Timber.d("开发模式：手动设置地区为 $region")
        } else {
            Timber.w("生产环境不允许手动设置地区")
        }
    }

    /**
     * 清除缓存
     */
    private fun clearCache() {
        prefs.edit().clear().apply()
        Timber.d("地区检测缓存已清除")
    }

    private fun isUpdateNeeded(lastUpdate: Instant): Boolean {
        val now = Clock.System.now()
        val timeSinceUpdate = now - lastUpdate
        val minUpdateInterval = MIN_UPDATE_INTERVAL_HOURS.hours
        return timeSinceUpdate >= minUpdateInterval
    }

    private fun getCurrentTime(): Instant = Clock.System.now()
}
