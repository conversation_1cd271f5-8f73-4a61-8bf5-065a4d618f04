package com.example.gymbro.designSystem.components

import androidx.compose.material3.SnackbarDuration
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.extensions.asString

/**
 * 错误消息处理组件，显示错误消息并在显示后触发回调
 */
@Composable
fun errorMessageHandler(
    error: UiText?,
    onErrorShown: () -> Unit,
) {
    val snackbarHostState = LocalSnackbarHostState.current

    // 在Composable上下文中转换UiText为字符串
    val errorMessage = error?.asString()

    LaunchedEffect(error) {
        if (errorMessage != null) {
            snackbarHostState.showSnackbar(
                message = errorMessage,
                duration = SnackbarDuration.Short,
            )
            onErrorShown()
        }
    }
}
