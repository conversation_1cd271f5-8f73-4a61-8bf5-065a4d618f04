package com.example.gymbro.core.error

import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.ui.text.UiText
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.map
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * ErrorReporter的默认实现
 * 使用Timber进行日志记录，并维护最近错误的内存缓存
 */
@Singleton
class DefaultErrorReporter @Inject constructor() : ErrorReporter {
    private val _recentErrors = MutableStateFlow<List<ErrorReport>>(emptyList())
    private val maxErrorHistorySize = 20

    override fun reportError(error: Throwable, tag: String, fatal: Boolean) {
        val logTag = if (tag.isNotEmpty()) tag else "ErrorReporter"
        if (fatal) {
            Timber.e(error, "致命错误[$logTag]: ${error.message}")
        } else {
            Timber.w(error, "非致命错误[$logTag]: ${error.message}")
        }

        addErrorToHistory(ErrorReport(error, tag, fatal = fatal))
    }

    override fun reportBusinessError(error: ModernDataError, tag: String) {
        val logTag = if (tag.isNotEmpty()) tag else "BusinessError"
        Timber.w("业务错误[$logTag]: ${error::class.simpleName}")

        addErrorToHistory(ErrorReport(error, tag))
    }

    override fun reportUserError(message: UiText, tag: String) {
        val logTag = if (tag.isNotEmpty()) tag else "UserError"
        Timber.i("用户错误[$logTag]: $message")

        addErrorToHistory(ErrorReport(message, tag))
    }

    override fun getRecentErrors(): Flow<ErrorReport> {
        // 这里我们使用map而不是transform，因为transform在Flow中有特殊含义
        return _recentErrors.asStateFlow().map { errorList ->
            // 由于接口定义要求返回单个ErrorReport的Flow，这里我们只返回最新的一个
            errorList.lastOrNull() ?: ErrorReport(
                error = "No errors",
                tag = "EmptyError",
            )
        }
    }

    override fun clearErrorHistory() {
        _recentErrors.value = emptyList()
    }

    private fun addErrorToHistory(report: ErrorReport) {
        _recentErrors.value = (_recentErrors.value + report)
            .takeLast(maxErrorHistorySize)
    }
}
