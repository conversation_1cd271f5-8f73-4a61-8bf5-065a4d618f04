package com.example.gymbro.core.error.recovery

import com.example.gymbro.core.error.types.ModernDataError

/**
 * 错误恢复策略接口
 * 定义了错误恢复的通用契约
 *
 * @param T 恢复操作成功时返回的数据类型
 */
interface RecoveryStrategy<T> {
    /**
     * 执行恢复策略
     *
     * @return 如果恢复成功，则返回恢复的数据；如果恢复失败，则返回null
     */
    suspend fun execute(): T?

    companion object {
        /**
         * 创建永远返回null的恢复策略
         */
        fun <T> none(): RecoveryStrategy<T> = object : RecoveryStrategy<T> {
            override suspend fun execute(): T? = null
        }

        /**
         * 创建返回固定值的恢复策略
         */
        fun <T> value(value: T): RecoveryStrategy<T> = object : RecoveryStrategy<T> {
            override suspend fun execute(): T = value
        }
    }
}

/**
 * 给ModernDataError添加恢复策略的扩展函数
 *
 * @param T 恢复操作成功时返回的数据类型
 * @return 关联的恢复策略，如果没有则返回null
 */
fun <T> ModernDataError.getRecoveryStrategy(): RecoveryStrategy<T>? {
    // 现在的实现只返回null，因为实际恢复策略存储机制已经移除
    // 在完整实现中，这应该查询RecoveryStrategyRegistry以获取合适的策略
    return null
}

/**
 * 给ModernDataError添加恢复策略的扩展函数
 *
 * @param strategy 要关联的恢复策略
 * @return 被添加了恢复策略的ModernDataError（这里为this，因为现在是扩展函数而非方法）
 */
fun <T> ModernDataError.withRecoveryStrategy(
    @Suppress("UNUSED_PARAMETER") strategy: RecoveryStrategy<T>,
): ModernDataError {
    // 现在的实现只返回this，因为实际恢复策略存储机制已经移除
    // 在完整实现中，这应该在RecoveryStrategyRegistry中注册策略
    return this
}
