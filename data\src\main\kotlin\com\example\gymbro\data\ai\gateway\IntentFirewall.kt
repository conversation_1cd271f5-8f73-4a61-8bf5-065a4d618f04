package com.example.gymbro.data.ai.gateway

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.features.FeatureErrors
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.util.IpUtils
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 意图防火墙
 *
 * 三层闸门系统的第一层，负责：
 * - 恶意输入检测
 * - IP地址黑名单验证
 * - 输入内容安全检查
 * - 请求频率初步过滤
 */
@Singleton
class IntentFirewall
@Inject
constructor() {
    // 黑名单IP段
    private val blacklistCidrs =
        listOf(
            "10.0.0.0/8", // 内网测试
            "*********/8", // 本地环回
            "***********/16", // 链路本地地址
        )

    // 恶意关键词
    private val maliciousKeywords =
        listOf(
            "DROP TABLE",
            "DELETE FROM",
            "UNION SELECT",
            "<script>",
            "javascript:",
            "eval(",
            "prompt(",
            "alert(",
            "document.cookie",
        )

    // 敏感指令
    private val sensitiveCommands =
        listOf(
            "ignore previous instructions",
            "forget all rules",
            "act as a different",
            "pretend to be",
            "system prompt",
            "你现在是",
            "忽略之前的指令",
            "忘记所有规则",
        )

    /**
     * 检查请求是否应被拦截
     */
    suspend fun checkRequest(
        userInput: String,
        userId: String,
        clientIp: String? = null,
    ): ModernResult<FirewallDecision> {
        Timber.d("IntentFirewall: 开始检查请求 userId=$userId")

        return try {
            // 1. IP黑名单检查
            val ipCheck = checkIpBlacklist(clientIp)
            if (!ipCheck.passed) {
                Timber.w("IntentFirewall: IP被拦截 ip=$clientIp")
                return ModernResult.Error(
                    FeatureErrors.CoachError.processingFailed(
                        operationName = "IntentFirewall.ipBlocked",
                        message = UiText.DynamicString("请求来源受限"),
                        processType = "ip_filter",
                        reason = "blacklisted_ip",
                        metadataMap =
                        mapOf(
                            "client_ip" to (clientIp ?: "unknown"),
                            "firewall_stage" to "ip_check",
                        ),
                    ),
                )
            }

            // 2. 恶意输入检测
            val maliciousCheck = checkMaliciousInput(userInput)
            if (!maliciousCheck.passed) {
                Timber.w("IntentFirewall: 检测到恶意输入")
                return ModernResult.Error(
                    FeatureErrors.CoachError.invalidInput(
                        operationName = "IntentFirewall.maliciousInput",
                        message = UiText.DynamicString("输入内容不符合安全规范"),
                        inputType = "malicious_content",
                        value = userInput.take(50),
                        metadataMap =
                        mapOf(
                            "detected_pattern" to maliciousCheck.reason,
                            "firewall_stage" to "malicious_check",
                        ),
                    ),
                )
            }

            // 3. 敏感指令检测
            val sensitiveCheck = checkSensitiveCommands(userInput)
            if (!sensitiveCheck.passed) {
                Timber.w("IntentFirewall: 检测到敏感指令")
                return ModernResult.Error(
                    FeatureErrors.CoachError.invalidInput(
                        operationName = "IntentFirewall.sensitiveCommand",
                        message = UiText.DynamicString("输入包含不当指令"),
                        inputType = "sensitive_command",
                        value = userInput.take(50),
                        metadataMap =
                        mapOf(
                            "detected_command" to sensitiveCheck.reason,
                            "firewall_stage" to "sensitive_check",
                        ),
                    ),
                )
            }

            // 4. 输入长度检查
            val lengthCheck = checkInputLength(userInput)
            if (!lengthCheck.passed) {
                Timber.w("IntentFirewall: 输入长度超限")
                return ModernResult.Error(
                    FeatureErrors.CoachError.invalidInput(
                        operationName = "IntentFirewall.inputTooLong",
                        message = UiText.DynamicString("输入内容过长，请精简后重试"),
                        inputType = "length_exceeded",
                        value = "length=${userInput.length}",
                        metadataMap =
                        mapOf(
                            "input_length" to userInput.length,
                            "max_allowed" to 2000,
                            "firewall_stage" to "length_check",
                        ),
                    ),
                )
            }

            Timber.d("IntentFirewall: 请求通过所有检查")
            ModernResult.Success(
                FirewallDecision(
                    passed = true,
                    reason = "all_checks_passed",
                    riskLevel = RiskLevel.LOW,
                    metadata =
                    mapOf(
                        "checks_completed" to listOf("ip", "malicious", "sensitive", "length"),
                        "user_id" to userId,
                    ),
                ),
            )
        } catch (e: Exception) {
            Timber.e(e, "IntentFirewall: 检查过程异常")
            ModernResult.Error(
                FeatureErrors.CoachError.processingFailed(
                    operationName = "IntentFirewall.checkException",
                    message = UiText.DynamicString("安全检查服务异常"),
                    processType = "firewall_check",
                    reason = "exception",
                    cause = e,
                    metadataMap =
                    mapOf(
                        "exception_type" to e.javaClass.simpleName,
                        "firewall_stage" to "exception",
                    ),
                ),
            )
        }
    }

    /**
     * IP黑名单检查
     */
    private fun checkIpBlacklist(clientIp: String?): CheckResult {
        if (clientIp.isNullOrBlank()) {
            return CheckResult(passed = true, reason = "no_ip_provided")
        }

        if (!IpUtils.isValidIpv4(clientIp)) {
            return CheckResult(passed = false, reason = "invalid_ip_format")
        }

        val isBlacklisted = IpUtils.isIpInCidrList(clientIp, blacklistCidrs)
        return if (isBlacklisted) {
            CheckResult(passed = false, reason = "ip_blacklisted")
        } else {
            CheckResult(passed = true, reason = "ip_allowed")
        }
    }

    /**
     * 恶意输入检测
     */
    private fun checkMaliciousInput(input: String): CheckResult {
        val lowerInput = input.lowercase()

        for (keyword in maliciousKeywords) {
            if (lowerInput.contains(keyword.lowercase())) {
                return CheckResult(passed = false, reason = "malicious_keyword:$keyword")
            }
        }

        return CheckResult(passed = true, reason = "no_malicious_content")
    }

    /**
     * 敏感指令检测
     */
    private fun checkSensitiveCommands(input: String): CheckResult {
        val lowerInput = input.lowercase()

        for (command in sensitiveCommands) {
            if (lowerInput.contains(command.lowercase())) {
                return CheckResult(passed = false, reason = "sensitive_command:$command")
            }
        }

        return CheckResult(passed = true, reason = "no_sensitive_commands")
    }

    /**
     * 输入长度检查
     */
    private fun checkInputLength(input: String): CheckResult {
        val maxLength = 2000 // 最大允许2000字符

        return if (input.length > maxLength) {
            CheckResult(passed = false, reason = "length_exceeded:${input.length}")
        } else {
            CheckResult(passed = true, reason = "length_acceptable")
        }
    }
}

/**
 * 防火墙决策结果
 */
data class FirewallDecision(
    val passed: Boolean,
    val reason: String,
    val riskLevel: RiskLevel,
    val metadata: Map<String, Any> = emptyMap(),
)

/**
 * 风险等级
 */
enum class RiskLevel {
    LOW,
    MEDIUM,
    HIGH,
    CRITICAL,
}

/**
 * 检查结果
 */
private data class CheckResult(
    val passed: Boolean,
    val reason: String,
)
