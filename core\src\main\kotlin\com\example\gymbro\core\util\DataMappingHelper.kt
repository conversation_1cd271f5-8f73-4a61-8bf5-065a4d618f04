package com.example.gymbro.core.util

import kotlinx.datetime.LocalDate
import kotlinx.datetime.LocalDateTime

/**
 * 数据映射辅助工具类
 *
 * 提供在对象映射过程中处理默认值和转换的辅助函数
 */
object DataMappingHelper {

    /**
     * 在映射过程中使用默认值的辅助函数
     *
     * 允许在映射函数内部使用默认值Map，简化处理缺失字段或空值的场景
     *
     * @param defaultValues 默认值Map，键为字段名，值为默认值
     * @param mappingFunc 执行实际映射的函数，接收defaultValues作为参数
     * @return 映射函数的返回结果
     */
    fun <T> withDefaults(defaultValues: Map<String, Any?>, mappingFunc: (Map<String, Any?>) -> T): T {
        return mappingFunc(defaultValues)
    }

    /**
     * 从Map中获取值，如果不存在或为null则使用默认值
     *
     * @param map 源数据Map
     * @param key 需要获取的键
     * @param defaultValue 默认值
     * @return 获取到的值或默认值
     */
    inline fun <reified T> getOrDefault(map: Map<String, Any?>, key: String, defaultValue: T): T {
        val value = map[key]
        return if (value != null && value is T) {
            value
        } else {
            defaultValue
        }
    }

    /**
     * 从Map中获取非空值，如果不存在、为null或类型不匹配则抛出异常
     *
     * @param map 源数据Map
     * @param key 需要获取的键
     * @return 获取到的值
     * @throws IllegalArgumentException 如果值不存在、为null或类型不匹配
     */
    inline fun <reified T> getRequired(map: Map<String, Any?>, key: String): T {
        val value = map[key]
        return if (value != null && value is T) {
            value
        } else {
            throw IllegalArgumentException(
                "Required mapping value '$key' of type ${T::class.java.simpleName} not found or has wrong type",
            )
        }
    }

    /**
     * 安全地转换值的类型
     *
     * @param value 要转换的值
     * @param defaultValue 转换失败时的默认值
     * @return 转换后的值或默认值
     */
    inline fun <reified T> safeCast(value: Any?, defaultValue: T): T {
        return if (value is T) value else defaultValue
    }

    /**
     * 安全地获取字符串值，如果为null则返回默认值
     *
     * @param value 可能为null的字符串值
     * @param default 当value为null时返回的默认值
     * @return 非null的字符串值
     */
    fun safeString(value: String?, default: String = ""): String {
        return value ?: default
    }

    /**
     * 安全地获取数值，如果为null则返回默认值
     *
     * @param value 可能为null的数值
     * @param default 当value为null时返回的默认值
     * @return 非null的数值
     */
    fun <T : Number> safeNumber(value: T?, default: T): T {
        return value ?: default
    }

    /**
     * 安全地获取整数值，如果为null则返回默认值
     *
     * @param value 可能为null的整数值
     * @param default 当value为null时返回的默认值
     * @return 非null的整数值
     */
    fun safeInt(value: Int?, default: Int = 0): Int {
        return value ?: default
    }

    /**
     * 安全地获取长整数值，如果为null则返回默认值
     *
     * @param value 可能为null的长整数值
     * @param default 当value为null时返回的默认值
     * @return 非null的长整数值
     */
    fun safeLong(value: Long?, default: Long = 0L): Long {
        return value ?: default
    }

    /**
     * 安全地获取双精度浮点数值，如果为null则返回默认值
     *
     * @param value 可能为null的双精度浮点数值
     * @param default 当value为null时返回的默认值
     * @return 非null的双精度浮点数值
     */
    fun safeDouble(value: Double?, default: Double = 0.0): Double {
        return value ?: default
    }

    /**
     * 安全地获取浮点数值，如果为null则返回默认值
     *
     * @param value 可能为null的浮点数值
     * @param default 当value为null时返回的默认值
     * @return 非null的浮点数值
     */
    fun safeFloat(value: Float?, default: Float = 0f): Float {
        return value ?: default
    }

    /**
     * 安全地获取布尔值，如果为null则返回默认值
     *
     * @param value 可能为null的布尔值
     * @param default 当value为null时返回的默认值
     * @return 非null的布尔值
     */
    fun safeBoolean(value: Boolean?, default: Boolean = false): Boolean {
        return value ?: default
    }

    /**
     * 安全地获取LocalDateTime，如果为null则返回默认值
     *
     * @param value 可能为null的LocalDateTime
     * @param default 当value为null时返回的默认值
     * @return 非null的LocalDateTime
     */
    fun safeLocalDateTime(value: LocalDateTime?, default: LocalDateTime? = null): LocalDateTime? {
        return value ?: default
    }

    /**
     * 安全地获取LocalDate，如果为null则返回默认值
     *
     * @param value 可能为null的LocalDate
     * @param default 当value为null时返回的默认值
     * @return 非null的LocalDate
     */
    fun safeLocalDate(value: LocalDate?, default: LocalDate? = null): LocalDate? {
        return value ?: default
    }

    /**
     * 安全地获取列表，如果为null则返回空列表
     *
     * @param value 可能为null的列表
     * @return 非null的列表
     */
    fun <T> safeList(value: List<T>?): List<T> {
        return value ?: emptyList()
    }

    /**
     * 安全地获取Map，如果为null则返回空Map
     *
     * @param value A possibly null map
     * @return 非null的Map
     */
    fun <K, V> safeMap(value: Map<K, V>?): Map<K, V> {
        return value ?: emptyMap()
    }

    /**
     * 安全地将Any类型转换为Map<String, Any?>类型
     *
     * @param value 可能为Map类型的值
     * @return 转换后的Map或空Map
     */
    @Suppress("UNCHECKED_CAST")
    fun safeMapCast(value: Any?): Map<String, Any?> {
        return when (value) {
            is Map<*, *> -> value as? Map<String, Any?> ?: emptyMap()
            else -> emptyMap()
        }
    }
}
