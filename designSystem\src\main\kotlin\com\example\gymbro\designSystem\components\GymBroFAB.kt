package com.example.gymbro.designSystem.components

import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens

/**
 * FAB尺寸Token扩展
 * 为FloatingActionButton定义语义化尺寸Token
 */
private object FABTokens {
    val SmallSize = Tokens.Spacing.XLarge // 40dp for small FAB
    val StandardSize = Tokens.Spacing.Massive // 56dp for standard FAB
    val LargeSize = Tokens.Spacing.Massive + Tokens.Spacing.Medium // 72dp for large FAB

    val SmallIconSize = Tokens.Spacing.Medium + Tokens.Spacing.Tiny // 18dp
    val StandardIconSize = Tokens.Spacing.Large // 24dp
    val LargeIconSize = Tokens.Spacing.XLarge // 32dp
}

/**
 * 统一的FloatingActionButton组件
 *
 * 注意：这是通用的FAB组件，适用于大多数场景。
 * 对于AI教练模块，请使用专门的CoachFab组件，它提供了更丰富的功能：
 * - 循环切换功能
 * - 动态标签显示
 * - 弹性动画效果
 * - ChatGPT风格的黑色主题
 *
 * @param onClick 点击回调
 * @param icon 图标
 * @param modifier 修饰符
 * @param enabled 是否启用
 * @param isLoading 是否显示加载状态
 * @param contentDescription 内容描述（用于无障碍）
 */
@Composable
fun GymBroFAB(
    onClick: () -> Unit,
    icon: ImageVector,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    isLoading: Boolean = false,
    contentDescription: String? = null,
) {
    FloatingActionButton(
        onClick = onClick,
        modifier = modifier,
        containerColor = MaterialTheme.colorScheme.primary,
        contentColor = MaterialTheme.colorScheme.onPrimary,
    ) {
        if (isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.size(FABTokens.StandardIconSize),
                color = MaterialTheme.colorScheme.onPrimary,
                strokeWidth = Tokens.Spacing.Tiny,
            )
        } else {
            Icon(
                imageVector = icon,
                contentDescription = contentDescription,
                modifier = Modifier.size(FABTokens.StandardIconSize),
            )
        }
    }
}

/**
 * 扩展的FloatingActionButton，支持小尺寸
 */
@Composable
fun GymBroSmallFAB(
    onClick: () -> Unit,
    icon: ImageVector,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    isLoading: Boolean = false,
    contentDescription: String? = null,
) {
    FloatingActionButton(
        onClick = onClick,
        modifier = modifier.size(FABTokens.SmallSize),
        containerColor = MaterialTheme.colorScheme.secondary,
        contentColor = MaterialTheme.colorScheme.onSecondary,
    ) {
        if (isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.size(FABTokens.SmallIconSize),
                color = MaterialTheme.colorScheme.onSecondary,
                strokeWidth = Tokens.Spacing.Tiny / 2,
            )
        } else {
            Icon(
                imageVector = icon,
                contentDescription = contentDescription,
                modifier = Modifier.size(FABTokens.SmallIconSize),
            )
        }
    }
}

/**
 * 扩展的FloatingActionButton，支持大尺寸
 */
@Composable
fun GymBroLargeFAB(
    onClick: () -> Unit,
    icon: ImageVector,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    isLoading: Boolean = false,
    contentDescription: String? = null,
) {
    FloatingActionButton(
        onClick = onClick,
        modifier = modifier.size(FABTokens.LargeSize),
        containerColor = MaterialTheme.colorScheme.tertiary,
        contentColor = MaterialTheme.colorScheme.onTertiary,
    ) {
        if (isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.size(FABTokens.LargeIconSize),
                color = MaterialTheme.colorScheme.onTertiary,
                strokeWidth = Tokens.Spacing.XSmall - Tokens.Spacing.Tiny,
            )
        } else {
            Icon(
                imageVector = icon,
                contentDescription = contentDescription,
                modifier = Modifier.size(FABTokens.LargeIconSize),
            )
        }
    }
}

@GymBroPreview
@Composable
private fun GymBroFABPreview() {
    GymBroTheme {
        GymBroFAB(
            onClick = { },
            icon = Icons.Default.Add,
            contentDescription = "添加",
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroFABLoadingPreview() {
    GymBroTheme {
        GymBroFAB(
            onClick = { },
            icon = Icons.Default.Add,
            contentDescription = "添加训练",
            isLoading = true,
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroSmallFABPreview() {
    GymBroTheme {
        GymBroSmallFAB(
            onClick = { },
            icon = Icons.Default.Edit,
            contentDescription = "编辑",
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroLargeFABPreview() {
    GymBroTheme {
        GymBroLargeFAB(
            onClick = { },
            icon = Icons.Default.PlayArrow,
            contentDescription = "开始训练",
        )
    }
}
