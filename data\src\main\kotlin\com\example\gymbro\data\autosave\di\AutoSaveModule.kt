package com.example.gymbro.data.autosave.di

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.data.autosave.AutoSaveRepository
import com.example.gymbro.data.autosave.AutoSaveRepositoryImpl
import com.example.gymbro.data.autosave.GlobalAutoSaveManager
import com.example.gymbro.data.autosave.adapter.CalendarAutoSaveAdapter
import com.example.gymbro.data.autosave.adapter.ProfileAutoSaveAdapter
import com.example.gymbro.data.autosave.adapter.WorkoutAutoSaveAdapter
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineScope
import kotlinx.serialization.json.Json
import javax.inject.Named
import javax.inject.Singleton

/**
 * 自动保存模块依赖注入配置
 *
 * 🎯 功能特性：
 * - 提供统一自动保存系统的所有依赖注入配置
 * - 集成现有的DataStore、Json、Logger等基础设施
 * - 配置AutoSaveRepository和适配器的依赖注入
 * - 使用正确的作用域和生命周期管理
 * - 遵循现有的DI模式和命名约定
 *
 * 架构原则：
 * - 使用@Singleton确保单例模式
 * - 使用@ApplicationScope提供应用级协程作用域
 * - 正确配置@Named限定符避免冲突
 * - 集成现有的DataStore和Json配置
 * - 遵循Clean Architecture的依赖倒置原则
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class AutoSaveModule {
    companion object {
        /**
         * 提供全局自动保存管理器
         *
         * 集成现有的基础设施：
         * - DataStore: 使用统一的用户偏好设置DataStore实例
         * - Json: 使用应用级Json序列化器配置
         * - Logger: 使用统一的日志记录器
         * - ApplicationContext: 用于访问应用上下文
         * - CoroutineScope: 使用应用级协程作用域
         */
        @Provides
        @Singleton
        fun provideGlobalAutoSaveManager(
            @ApplicationContext context: Context,
            dataStore: DataStore<Preferences>,
            json: Json,
            @Named("application_scope") scope: CoroutineScope,
            logger: Logger,
        ): GlobalAutoSaveManager =
            GlobalAutoSaveManager(
                context = context,
                dataStore = dataStore,
                json = json,
                scope = scope,
                logger = logger,
            )

        /**
         * 提供自动保存仓库实现
         *
         * 直接提供AutoSaveRepositoryImpl实例，避免@Binds的循环依赖问题
         */
        @Provides
        @Singleton
        fun provideAutoSaveRepository(
            globalAutoSaveManager: GlobalAutoSaveManager,
            logger: Logger,
        ): AutoSaveRepository =
            AutoSaveRepositoryImpl(
                globalAutoSaveManager = globalAutoSaveManager,
                logger = logger,
            )

        /**
         * 提供Profile自动保存适配器
         *
         * 集成现有的Profile模块UseCase：
         * - UpdateUserProfileUseCase: 用于保存用户资料
         * - GetUserProfileUseCase: 用于获取用户资料
         * - AutoSaveRepository: 用于会话管理
         * - Json: 用于序列化
         * - Logger: 用于日志记录
         */
        @Provides
        @Singleton
        fun provideProfileAutoSaveAdapter(
            autoSaveRepository: AutoSaveRepository,
            updateUserProfileUseCase: com.example.gymbro.domain.profile.usecase.UpdateUserProfileUseCase,
            getUserProfileUseCase: com.example.gymbro.domain.profile.usecase.GetUserProfileUseCase,
            json: Json,
            logger: Logger,
        ): ProfileAutoSaveAdapter =
            ProfileAutoSaveAdapter(
                autoSaveRepository = autoSaveRepository,
                updateUserProfileUseCase = updateUserProfileUseCase,
                getUserProfileUseCase = getUserProfileUseCase,
                json = json,
                logger = logger,
            )

        /**
         * 提供Workout自动保存适配器
         *
         * 集成新的四数据库架构Repository：
         * - TemplateRepository: 用于WorkoutTemplate的CRUD操作
         * - AutoSaveRepository: 用于会话管理
         * - Json: 用于序列化
         * - Logger: 用于日志记录
         */
        @Provides
        @Singleton
        fun provideWorkoutAutoSaveAdapter(
            autoSaveRepository: AutoSaveRepository,
            templateRepository: com.example.gymbro.domain.workout.repository.TemplateRepository,
            json: Json,
            logger: Logger,
        ): WorkoutAutoSaveAdapter =
            WorkoutAutoSaveAdapter(
                autoSaveRepository = autoSaveRepository,
                templateRepository = templateRepository,
                json = json,
                logger = logger,
            )

        /**
         * 提供Calendar自动保存适配器
         *
         * 集成AppDatabase现有的calendar_events表：
         * - CalendarRepository: 用于CalendarItem的CRUD操作
         * - AutoSaveRepository: 用于会话管理
         * - Json: 用于序列化
         * - Logger: 用于日志记录
         */
        @Provides
        @Singleton
        fun provideCalendarAutoSaveAdapter(
            autoSaveRepository: AutoSaveRepository,
            calendarRepository: com.example.gymbro.domain.workout.repository.CalendarRepository,
            json: Json,
            logger: Logger,
        ): CalendarAutoSaveAdapter =
            CalendarAutoSaveAdapter(
                autoSaveRepository = autoSaveRepository,
                calendarRepository = calendarRepository,
                json = json,
                logger = logger,
            )
    }
}
