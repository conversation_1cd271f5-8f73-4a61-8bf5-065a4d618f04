package com.example.gymbro.data.shared.theme

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import com.example.gymbro.core.theme.ThemePersistence
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 主题持久化实现
 * 使用DataStore存储主题配置
 */
@Singleton
class ThemePersistenceImpl
@Inject
constructor(
    private val dataStore: DataStore<Preferences>,
) : ThemePersistence {
    private object Keys {
        val DARK_THEME = booleanPreferencesKey("pref_dark_theme")
        val DYNAMIC_COLOR = booleanPreferencesKey("pref_dynamic_color")
        val COLOR_MODE = stringPreferencesKey("pref_color_mode")
    }

    override suspend fun saveDarkTheme(isDark: Boolean): Boolean =
        try {
            Timber.d("保存深色主题设置: $isDark")
            dataStore.edit { preferences ->
                preferences[Keys.DARK_THEME] = isDark
            }
            Timber.i("深色主题设置保存成功")
            true
        } catch (e: Exception) {
            Timber.e(e, "保存深色主题设置失败")
            false
        }

    override suspend fun saveDynamicColor(enable: Boolean): Boolean =
        try {
            Timber.d("保存动态颜色设置: $enable")
            dataStore.edit { preferences ->
                preferences[Keys.DYNAMIC_COLOR] = enable
            }
            Timber.i("动态颜色设置保存成功")
            true
        } catch (e: Exception) {
            Timber.e(e, "保存动态颜色设置失败")
            false
        }

    override suspend fun saveColorMode(mode: String): Boolean =
        try {
            Timber.d("保存颜色模式设置: $mode")
            dataStore.edit { preferences ->
                preferences[Keys.COLOR_MODE] = mode
            }
            Timber.i("颜色模式设置保存成功")
            true
        } catch (e: Exception) {
            Timber.e(e, "保存颜色模式设置失败")
            false
        }

    override fun darkThemeFlow(): Flow<Boolean> =
        dataStore.data.map { preferences ->
            preferences[Keys.DARK_THEME] ?: false // 默认浅色主题
        }

    override fun dynamicColorFlow(): Flow<Boolean> =
        dataStore.data.map { preferences ->
            preferences[Keys.DYNAMIC_COLOR] ?: true // 默认启用动态颜色
        }

    override fun colorModeFlow(): Flow<String> =
        dataStore.data.map { preferences ->
            preferences[Keys.COLOR_MODE] ?: "system" // 默认跟随系统
        }
}
