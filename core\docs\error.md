> Task :features:workout:compileDebugKotlin FAILED
e: file:///D:/GymBro/GymBro/features/workout/src/main/kotlin/com/example/gymbro/features/workout/session/internal/aiassistant/AiAssistantViewModel.kt:187:25 No parameter with name 'currentModel' found.




**✅ 完整的提示词切换功能：**

1. **UI界面**：`AiCoachTopBar` → `UnifiedSettingsDropdown` 提供切换按钮
2. **ViewModel**：`AiCoachViewModel.switchPromptMode(mode)` 处理切换逻辑
3. **Manager**：`PromptModeManager.switchMode()` 委托给 PromptRegistry
4. **Registry**：`PromptRegistry.switch(id)` 执行实际切换
5. **Builder**：`LayeredPromptBuilder` 使用 `promptRegistry.getSystemPrompt()` 获取当前配置

让我检查一下PromptRegistry的switch方法确保它支持多种提示词：
Read file: core/src/main/kotlin/com/example/gymbro/core/ai/prompt/registry/PromptRegistry.kt

现在让我确认UI界面的切换功能正常工作，查看UnifiedSettingsDropdown：
Search files...

✅ **完美！提示词切换功能确实完整存在！**

**🔍 完整的切换链路验证：**

1. **UI界面**：`AiCoachTopBar` → 设置按钮 → `UnifiedSettingsDropdown` → `PromptSelectionSubmenu`
2. **可用选项**：`["standard", "layered", "pipeline", "blank"]`
3. **切换逻辑**：`onPromptSelected` → `promptRegistry.switch(promptId)`
4. **状态更新**：`PromptRegistry._currentId.value = id`
5. **实时生效**：`LayeredPromptBuilder.getSystemPrompt()` 获取新配置

**🎯 修复总结：**
Ran tool

需要修复domain模块的编译错误！让我修复SendChatMessageAndGetResponseUseCase：
Read file: domain/src/main/kotlin/com/example/gymbro/domain/coach/usecase/SendChatMessageAndGetResponseUseCase.kt


Read file: domain/src/main/kotlin/com/example/gymbro/domain/coach/usecase/SendChatMessageAndGetResponseUseCase.kt


```bash
gradlew :domain:compileDebugKotlin
```
