package com.example.gymbro.data.network

import com.example.gymbro.data.network.adapter.UserProfileApiAdapter
import com.example.gymbro.data.network.adapter.UserProfileRetrofitApi
import com.example.gymbro.shared.models.network.ApiError
import com.example.gymbro.shared.models.network.NetworkResult
import com.example.gymbro.shared.models.user.UserProfileDto
import com.google.gson.Gson
import kotlinx.coroutines.test.runTest
import okhttp3.OkHttpClient
import okhttp3.mockwebserver.MockResponse
import okhttp3.mockwebserver.MockWebServer
import org.junit.After
import org.junit.Before
import org.junit.Test
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * UserProfileApi集成测试
 *
 * 使用MockWebServer验证：
 * 1. NetworkResult<T>转换的正确性
 * 2. HTTP状态码到ApiError的映射
 * 3. 网络异常处理
 * 4. JSON序列化/反序列化
 */
class UserProfileApiIntegrationTest {

    private lateinit var mockWebServer: MockWebServer
    private lateinit var apiService: UserProfileApiAdapter
    private lateinit var gson: Gson

    private val testUserId = "test-user-123"
    private val testUserProfileDto = UserProfileDto(
        userId = testUserId,
        displayName = "Test User",
        email = "<EMAIL>",
        gender = "MALE",
        age = 25,
        height = 175.0,
        heightUnit = "CM",
        weight = 70.0,
        weightUnit = "KG",
        fitnessLevel = "INTERMEDIATE",
        fitnessGoals = emptyList(),
        workoutDays = emptyList(),
        avatarUrl = null,
        createdAt = "2024-01-01T00:00:00Z",
        updatedAt = "2024-01-01T00:00:00Z",
    )

    @Before
    fun setup() {
        mockWebServer = MockWebServer()
        mockWebServer.start()

        gson = Gson()

        val okHttpClient = OkHttpClient.Builder().build()
        val retrofit = Retrofit.Builder()
            .baseUrl(mockWebServer.url("/"))
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create(gson))
            .build()

        val retrofitApi = retrofit.create(UserProfileRetrofitApi::class.java)
        apiService = UserProfileApiAdapter(retrofitApi)
    }

    @After
    fun tearDown() {
        mockWebServer.shutdown()
    }

    @Test
    fun `getUserProfile - 成功响应200`() = runTest {
        // Given
        val responseJson = gson.toJson(testUserProfileDto)
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(200)
                .setBody(responseJson)
                .addHeader("Content-Type", "application/json"),
        )

        // When
        val result = apiService.getUserProfile(testUserId)

        // Then
        assertTrue(result is NetworkResult.Success)
        assertEquals(testUserProfileDto.userId, result.data.userId)
        assertEquals(testUserProfileDto.displayName, result.data.displayName)

        val request = mockWebServer.takeRequest()
        assertEquals("GET", request.method)
        assertEquals("/v1/users/$testUserId/profile", request.path)
    }

    @Test
    fun `updateUserProfile - 成功响应200`() = runTest {
        // Given
        val responseJson = gson.toJson(testUserProfileDto)
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(200)
                .setBody(responseJson)
                .addHeader("Content-Type", "application/json"),
        )

        // When
        val result = apiService.updateUserProfile(testUserId, testUserProfileDto)

        // Then
        assertTrue(result is NetworkResult.Success)
        assertEquals(testUserProfileDto.userId, result.data.userId)

        val request = mockWebServer.takeRequest()
        assertEquals("PUT", request.method)
        assertEquals("/v1/users/$testUserId/profile", request.path)

        // 验证请求体
        val requestBody = gson.fromJson(request.body.readUtf8(), UserProfileDto::class.java)
        assertEquals(testUserProfileDto.displayName, requestBody.displayName)
    }

    @Test
    fun `getUserProfile - 404错误处理`() = runTest {
        // Given
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(404)
                .setBody("{\"error\":\"User not found\"}")
                .addHeader("Content-Type", "application/json"),
        )

        // When
        val result = apiService.getUserProfile(testUserId)

        // Then
        assertTrue(result is NetworkResult.Error)
        assertTrue(result.error is ApiError.Http)
        val httpError = result.error as ApiError.Http
        assertEquals(404, httpError.code)
        assertTrue(httpError.message.contains("资源未找到"))
    }

    @Test
    fun `updateUserProfile - 401未授权错误`() = runTest {
        // Given
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(401)
                .setBody("{\"error\":\"Unauthorized\"}")
                .addHeader("Content-Type", "application/json"),
        )

        // When
        val result = apiService.updateUserProfile(testUserId, testUserProfileDto)

        // Then
        assertTrue(result is NetworkResult.Error)
        assertTrue(result.error is ApiError.Http)
        val httpError = result.error as ApiError.Http
        assertEquals(401, httpError.code)
        assertTrue(httpError.message.contains("未授权访问"))
    }

    @Test
    fun `getUserProfile - 500服务器错误`() = runTest {
        // Given
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(500)
                .setBody("{\"error\":\"Internal Server Error\"}")
                .addHeader("Content-Type", "application/json"),
        )

        // When
        val result = apiService.getUserProfile(testUserId)

        // Then
        assertTrue(result is NetworkResult.Error)
        assertTrue(result.error is ApiError.Http)
        val httpError = result.error as ApiError.Http
        assertEquals(500, httpError.code)
        assertTrue(httpError.message.contains("服务器错误"))
    }

    @Test
    fun `createUserProfile - 成功创建201`() = runTest {
        // Given
        val responseJson = gson.toJson(testUserProfileDto)
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(201)
                .setBody(responseJson)
                .addHeader("Content-Type", "application/json"),
        )

        // When
        val result = apiService.createUserProfile(testUserProfileDto)

        // Then
        assertTrue(result is NetworkResult.Success)
        assertEquals(testUserProfileDto.userId, result.data.userId)

        val request = mockWebServer.takeRequest()
        assertEquals("POST", request.method)
        assertEquals("/v1/users/profile", request.path)
    }

    @Test
    fun `syncUserProfiles - 批量同步成功`() = runTest {
        // Given
        val profiles = listOf(testUserProfileDto)
        val responseJson = gson.toJson(profiles)
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(200)
                .setBody(responseJson)
                .addHeader("Content-Type", "application/json"),
        )

        // When
        val result = apiService.syncUserProfiles(profiles)

        // Then
        assertTrue(result is NetworkResult.Success)
        assertEquals(1, result.data.size)
        assertEquals(testUserProfileDto.userId, result.data[0].userId)

        val request = mockWebServer.takeRequest()
        assertEquals("POST", request.method)
        assertEquals("/v1/users/profiles/batch", request.path)
    }

    @Test
    fun `网络连接失败处理`() = runTest {
        // Given - 关闭服务器模拟网络连接失败
        mockWebServer.shutdown()

        // When
        val result = apiService.getUserProfile(testUserId)

        // Then
        assertTrue(result is NetworkResult.Error)
        // 网络连接失败会被映射为IOException相关的错误
        assertTrue(result.error is ApiError.Http || result.error is ApiError.Unknown)
    }

    @Test
    fun `JSON解析错误处理`() = runTest {
        // Given - 返回无效JSON
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(200)
                .setBody("invalid json")
                .addHeader("Content-Type", "application/json"),
        )

        // When
        val result = apiService.getUserProfile(testUserId)

        // Then
        assertTrue(result is NetworkResult.Error)
        // JSON解析错误会被转换为相应的错误类型
    }

    @Test
    fun `空响应体处理`() = runTest {
        // Given
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(200)
                .setBody("")
                .addHeader("Content-Type", "application/json"),
        )

        // When
        val result = apiService.getUserProfile(testUserId)

        // Then
        assertTrue(result is NetworkResult.Error)
        assertTrue(result.error is ApiError.Parse)
    }
}
