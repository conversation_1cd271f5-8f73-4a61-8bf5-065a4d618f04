package com.example.gymbro.core.network

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 网络监控接口
 *
 * 提供观察网络状态的功能，不依赖特定平台API。
 */
interface NetworkMonitor {
    /**
     * 当前网络状态
     */
    val status: Flow<NetworkStatus>

    /**
     * 网络是否可用
     */
    val isNetworkAvailable: Flow<Boolean>

    /**
     * 当前网络连接类型
     */
    val networkType: Flow<NetworkType>

    /**
     * 当前网络是否计费
     * (例如移动数据网络)
     */
    val isMetered: Flow<Boolean>

    /**
     * 当前网络速度估计
     */
    val networkSpeed: Flow<NetworkSpeed>

    /**
     * 当前网络是否受限
     * (例如网络登录门户需要认证)
     */
    val isRestricted: Flow<Boolean>

    /**
     * 开始监控网络状态
     */
    fun startMonitoring()

    /**
     * 停止监控网络状态
     */
    fun stopMonitoring()
}

/**
 * 默认网络监控实现
 *
 * 提供一个平台无关的默认实现，主要用于测试、开发和非Android环境。
 *
 * 特点：
 * - 始终返回理想的网络状态（WIFI连接且快速）
 * - 不执行实际的网络监控
 * - 适用于单元测试和预览环境
 *
 * 注意：生产环境应使用平台特定的实现（如AndroidNetworkMonitor）
 */
@Singleton
class DefaultNetworkMonitor
@Inject
constructor() : NetworkMonitor {
    private val _coreStatus =
        MutableStateFlow(
            CoreNetworkStatus(
                isAvailable = true,
                type = NetworkType.WIFI,
                isMetered = false,
                networkSpeed = NetworkSpeed.FAST,
                isRestricted = false,
            ),
        )

    override val status: Flow<NetworkStatus> =
        _coreStatus.map { it.toNetworkStatus() }

    override val isNetworkAvailable: Flow<Boolean> =
        _coreStatus.map { it.isAvailable }

    override val networkType: Flow<NetworkType> =
        _coreStatus.map { it.type }

    override val isMetered: Flow<Boolean> =
        _coreStatus.map { it.isMetered }

    override val networkSpeed: Flow<NetworkSpeed> =
        _coreStatus.map { it.networkSpeed }

    override val isRestricted: Flow<Boolean> =
        _coreStatus.map { it.isRestricted }

    override fun startMonitoring() {
        // 默认实现不执行任何操作 - 始终保持理想状态
        // 实际的网络监控应在平台特定的实现中进行
    }

    override fun stopMonitoring() {
        // 默认实现不执行任何操作 - 无需停止监控
        // 实际的网络监控停止应在平台特定的实现中进行
    }

    companion object {
    }
}
