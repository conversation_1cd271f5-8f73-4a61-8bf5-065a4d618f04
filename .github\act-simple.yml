# Simple ACT validation workflow without external dependencies
# For testing ACT functionality when GitHub access is limited

name: Simple ACT Test

on:
  workflow_dispatch:
  push:
    branches: [ main, develop ]

env:
  # Simple environment variables
  GRADLE_OPTS: -Dorg.gradle.daemon=false -Dfile.encoding=UTF-8
  LC_ALL: C.UTF-8

jobs:
  # Basic environment test
  basic-test:
    name: Basic Environment Test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check basic tools
        run: |
          echo "=== Basic Environment Check ==="
          echo "Current directory: $(pwd)"
          echo "User: $(whoami)"
          echo "OS: $(uname -a)"
          echo "Available tools:"
          which java || echo "Java not found"
          which gradle || echo "Gradle not found"
          which git || echo "Git not found"

      - name: Check project structure
        run: |
          echo "=== Project Structure Check ==="
          echo "Checking key directories and files:"
          ls -la
          echo ""
          echo "Checking for gradlew:"
          ls -la gradlew || echo "gradlew not found"
          echo ""
          echo "Checking app directory:"
          ls -la app/ || echo "app directory not found"
          echo ""
          echo "Checking domain directory:"
          ls -la domain/ || echo "domain directory not found"
          echo ""
          echo "Checking data directory:"
          ls -la data/ || echo "data directory not found"

      - name: Check Firebase mock config
        run: |
          echo "=== Firebase Config Check ==="
          if [ -f "app/google-services.json.mock" ]; then
            echo "✅ Mock Firebase config found"
            echo "Creating google-services.json from mock..."
            cp app/google-services.json.mock app/google-services.json
            echo "✅ Firebase config ready"
          else
            echo "❌ Mock Firebase config not found"
            exit 1
          fi

  # Gradle basic test (without Java setup action)
  gradle-test:
    name: Gradle Basic Test
    runs-on: ubuntu-latest
    needs: basic-test
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup mock Firebase config
        run: |
          echo "Setting up mock Firebase config..."
          cp app/google-services.json.mock app/google-services.json

      - name: Test Gradle wrapper
        run: |
          echo "=== Gradle Wrapper Test ==="
          chmod +x gradlew
          echo "Gradle wrapper permissions set"
          
          echo "Testing Gradle version:"
          ./gradlew --version || echo "Gradle version check failed"

      - name: Test basic Gradle tasks
        run: |
          echo "=== Basic Gradle Tasks ==="
          echo "Listing available tasks:"
          ./gradlew tasks --all | head -20 || echo "Task listing failed"
          
          echo ""
          echo "Testing help task:"
          ./gradlew help || echo "Help task failed"

  # Simple build test
  simple-build-test:
    name: Simple Build Test
    runs-on: ubuntu-latest
    needs: gradle-test
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup environment
        run: |
          echo "=== Environment Setup ==="
          cp app/google-services.json.mock app/google-services.json
          chmod +x gradlew
          echo "Environment ready"

      - name: Test project compilation
        run: |
          echo "=== Compilation Test ==="
          echo "Attempting to compile core modules..."
          
          # Try to compile without running tests first
          echo "Testing core module compilation:"
          ./gradlew :core:compileDebugKotlin --no-daemon --stacktrace || echo "Core compilation failed"
          
          echo ""
          echo "Testing domain module compilation:"
          ./gradlew :domain:compileDebugKotlin --no-daemon --stacktrace || echo "Domain compilation failed"
          
          echo ""
          echo "Testing data module compilation:"
          ./gradlew :data:compileDebugKotlin --no-daemon --stacktrace || echo "Data compilation failed"

      - name: Generate test report
        if: always()
        run: |
          echo "=== Test Report ==="
          echo "Simple ACT validation completed"
          echo "Timestamp: $(date)"
          echo "This test validates basic ACT functionality without external dependencies"
          echo ""
          echo "✅ Checked: Project structure"
          echo "✅ Checked: Gradle wrapper"
          echo "✅ Checked: Firebase mock config"
          echo "✅ Attempted: Module compilation"
          echo ""
          echo "MVP Status: ACT tool is working correctly"
