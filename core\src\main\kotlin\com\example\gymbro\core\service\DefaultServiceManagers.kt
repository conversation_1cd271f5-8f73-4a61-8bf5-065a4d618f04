package com.example.gymbro.core.service

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 默认服务生命周期管理器
 *
 * 提供服务生命周期管理的基础实现，不依赖于任何平台特定API。
 */
@Singleton
class DefaultServiceLifecycleManager
@Inject
constructor() : ServiceLifecycleManager {
    private val _serviceStates = MutableStateFlow<Map<String, ServiceLifecycleState>>(emptyMap())
    override val serviceStates: StateFlow<Map<String, ServiceLifecycleState>> = _serviceStates

    private val stateListeners = ConcurrentHashMap<String, Pair<String, ServiceStateChangeListener>>()

    override fun registerService(
        serviceId: String,
        initialState: ServiceLifecycleState,
    ): ServiceOperationResult {
        val currentStates = _serviceStates.value.toMutableMap()

        if (serviceId in currentStates) {
            return ServiceOperationResult.Failure("Service with ID '$serviceId' is already registered")
        }

        currentStates[serviceId] = initialState
        _serviceStates.value = currentStates

        return ServiceOperationResult.Success()
    }

    override fun unregisterService(serviceId: String): ServiceOperationResult {
        val currentStates = _serviceStates.value.toMutableMap()

        if (serviceId !in currentStates) {
            return ServiceOperationResult.Failure("Service with ID '$serviceId' is not registered")
        }

        currentStates.remove(serviceId)
        _serviceStates.value = currentStates

        // Remove all listeners for this service
        stateListeners.entries.removeIf { it.value.first == serviceId }

        return ServiceOperationResult.Success()
    }

    override fun updateServiceState(
        serviceId: String,
        newState: ServiceLifecycleState,
    ): ServiceOperationResult {
        val currentStates = _serviceStates.value.toMutableMap()

        if (serviceId !in currentStates) {
            return ServiceOperationResult.Failure("Service with ID '$serviceId' is not registered")
        }

        val oldState = currentStates[serviceId]
        currentStates[serviceId] = newState
        _serviceStates.value = currentStates

        // Notify listeners
        stateListeners.values
            .filter { it.first == serviceId }
            .forEach { (_, listener) ->
                listener.onStateChanged(serviceId, oldState ?: ServiceLifecycleState.UNKNOWN, newState)
            }

        return ServiceOperationResult.Success()
    }

    override fun observeServiceState(serviceId: String): Flow<ServiceLifecycleState> =
        serviceStates
            .map { states -> states[serviceId] ?: ServiceLifecycleState.UNKNOWN }

    override fun addStateChangeListener(
        serviceId: String,
        listener: ServiceStateChangeListener,
    ): String {
        val listenerId = UUID.randomUUID().toString()
        stateListeners[listenerId] = serviceId to listener
        return listenerId
    }

    override fun removeStateChangeListener(listenerId: String): ServiceOperationResult {
        val removed = stateListeners.remove(listenerId)
        return if (removed != null) {
            ServiceOperationResult.Success()
        } else {
            ServiceOperationResult.Failure("Listener with ID '$listenerId' not found")
        }
    }
}

/**
 * 默认服务管理器（无操作实现）
 *
 * 提供服务管理接口的基础无操作实现，平台无关。
 * 实际应用应替换为特定平台的实现。
 */
open class NoOpServiceManager : ServiceManager {
    override fun startService(
        serviceId: String,
        parameters: Map<String, Any>,
    ): ServiceOperationResult = ServiceOperationResult.Success()

    override fun stopService(serviceId: String): ServiceOperationResult = ServiceOperationResult.Success()

    override fun isServiceRunning(serviceId: String): Boolean = false

    override fun getServiceLifecycleState(serviceId: String): ServiceLifecycleState = ServiceLifecycleState.UNKNOWN
}

/**
 * 默认后台服务管理器（无操作实现）
 *
 * 提供后台服务管理接口的基础无操作实现，平台无关。
 * 实际应用应替换为特定平台的实现。
 */
class NoOpBackgroundServiceManager :
    NoOpServiceManager(),
    IBackgroundServiceManager {
    override fun isNetworkAvailable(): Boolean = true

    override fun enableBackgroundNetwork(): ServiceOperationResult = ServiceOperationResult.Success()

    override fun disableBackgroundNetwork(): ServiceOperationResult = ServiceOperationResult.Success()

    override fun setServicePriority(
        serviceId: String,
        priority: ServicePriority,
    ): ServiceOperationResult = ServiceOperationResult.Success()
}

/**
 * 默认前台服务管理器（无操作实现）
 *
 * 提供前台服务管理接口的基础无操作实现，平台无关。
 * 实际应用应替换为特定平台的实现。
 */
class NoOpForegroundServiceManager :
    NoOpServiceManager(),
    IForegroundServiceManager {
    override fun promoteToForeground(
        serviceId: String,
        notificationConfig: Any?,
    ): ServiceOperationResult = ServiceOperationResult.Success()

    override fun demoteToBackground(serviceId: String): ServiceOperationResult = ServiceOperationResult.Success()
}
