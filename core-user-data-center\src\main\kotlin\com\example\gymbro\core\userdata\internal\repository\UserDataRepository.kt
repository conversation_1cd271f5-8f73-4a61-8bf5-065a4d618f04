package com.example.gymbro.core.userdata.internal.repository

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.userdata.api.model.SyncStatus
import com.example.gymbro.core.userdata.api.model.UnifiedUserData
import com.example.gymbro.domain.model.auth.AuthUser
import com.example.gymbro.domain.profile.model.user.UserProfile
import kotlinx.coroutines.flow.Flow

/**
 * 用户数据仓库接口
 *
 * 定义了用户数据的存储和访问操作，遵循 Repository 模式。
 * 作为 UserDataCenter 的内部组件，负责数据的持久化和缓存管理。
 *
 * 职责：
 * - 用户数据的 CRUD 操作
 * - 数据缓存和同步状态管理
 * - 多数据源的协调和整合
 * - 数据一致性保证
 */
interface UserDataRepository {

    /**
     * 观察用户数据变化
     *
     * @return Flow<UnifiedUserData?> 用户数据流，null 表示用户未登录
     */
    fun observeUserData(): Flow<UnifiedUserData?>

    /**
     * 获取当前用户数据
     *
     * @return ModernResult<UnifiedUserData?> 当前用户数据
     */
    suspend fun getCurrentUserData(): ModernResult<UnifiedUserData?>

    /**
     * 保存用户数据
     *
     * @param userData 要保存的用户数据
     * @return ModernResult<Unit> 保存结果
     */
    suspend fun saveUserData(userData: UnifiedUserData): ModernResult<Unit>

    /**
     * 更新认证数据
     *
     * @param authUser 认证用户信息
     * @return ModernResult<Unit> 更新结果
     */
    suspend fun updateAuthData(authUser: AuthUser): ModernResult<Unit>

    /**
     * 更新用户资料数据
     *
     * @param userId 用户ID
     * @param profileData 用户资料数据
     * @return ModernResult<Unit> 更新结果
     */
    suspend fun updateProfileData(userId: String, profileData: UserProfile): ModernResult<Unit>

    /**
     * 清除用户数据
     *
     * @return ModernResult<Unit> 清除结果
     */
    suspend fun clearUserData(): ModernResult<Unit>

    /**
     * 获取同步状态
     *
     * @param userId 用户ID
     * @return ModernResult<SyncStatus> 同步状态
     */
    suspend fun getSyncStatus(userId: String): ModernResult<SyncStatus>

    /**
     * 更新同步状态
     *
     * @param userId 用户ID
     * @param status 新的同步状态
     * @return ModernResult<Unit> 更新结果
     */
    suspend fun updateSyncStatus(userId: String, status: SyncStatus): ModernResult<Unit>

    /**
     * 检查用户是否存在
     *
     * @param userId 用户ID
     * @return ModernResult<Boolean> 用户是否存在
     */
    suspend fun userExists(userId: String): ModernResult<Boolean>

    /**
     * 创建新用户数据
     *
     * @param authUser 认证用户信息
     * @return ModernResult<UnifiedUserData> 创建的用户数据
     */
    suspend fun createUserData(authUser: AuthUser): ModernResult<UnifiedUserData>

    /**
     * 合并用户数据
     *
     * 将新数据与现有数据合并，解决数据冲突
     *
     * @param existing 现有数据
     * @param new 新数据
     * @return UnifiedUserData 合并后的数据
     */
    fun mergeUserData(existing: UnifiedUserData, new: UnifiedUserData): UnifiedUserData

    /**
     * 验证用户数据完整性
     *
     * @param userData 要验证的用户数据
     * @return List<String> 验证错误列表，空列表表示验证通过
     */
    fun validateUserData(userData: UnifiedUserData): List<String>
}
