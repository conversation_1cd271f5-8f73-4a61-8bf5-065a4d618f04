# Core-ML Module 📊

GymBro项目的机器学习核心模块，提供本地化的文本向量嵌入能力。

## 模块概述

Core-ML模块实现了基于BGE（BAAI General Embedding）模型的本地文本向量化功能，支持中英文混合文本的语义嵌入，为全文搜索和智能推荐提供基础能力。

## 核心特性

- 🎯 **本地推理**: 完全离线的文本向量化，无需网络连接
- 🌏 **中英文支持**: 基于BGE模型，原生支持中英文混合文本
- ⚡ **高性能**: 支持TensorFlow Lite和ONNX Runtime双引擎
- 🔧 **灵活配置**: 可根据设备性能自动选择最优推理引擎
- 📦 **轻量级**: 优化后的模型体积控制在15MB以内

## 模块结构

```
core-ml/
├── src/main/kotlin/com/gymbro/core/ml/
│   ├── embedding/                    # 嵌入引擎
│   │   ├── EmbeddingEngine.kt       # 核心接口
│   │   ├── BgeEmbeddingEngine.kt    # TensorFlow Lite实现
│   │   └── OnnxEmbeddingEngine.kt   # ONNX Runtime实现
│   ├── tokenizer/                   # 分词器
│   │   ├── TokenizationResult.kt    # 分词结果数据类
│   │   └── BgeTokenizer.kt          # BGE分词器实现
│   ├── utils/                       # 工具类
│   │   └── VectorUtils.kt           # 向量处理工具
│   └── factory/                     # 工厂类
│       └── EmbeddingEngineFactory.kt # 引擎工厂
└── src/main/assets/
    ├── bge-small-cn-v1.5.tflite    # BGE模型文件
    └── vocab.txt                    # 词汇表文件
```

## 快速开始

### 1. 基本使用

```kotlin
// 通过工厂创建嵌入引擎
val modelStream = assets.open("bge-small-cn-v1.5.tflite")
val vocabStream = assets.open("vocab.txt")

val engine = EmbeddingEngineFactory.createEngine(
    modelStream = modelStream,
    vocabularyStream = vocabStream
)

// 嵌入单个文本
val embedding = engine.embed("今天的训练很棒！")
println("向量维度: ${embedding.size}")

// 批量嵌入
val texts = listOf(
    "胸肌训练计划",
    "有氧运动指导",
    "营养饮食建议"
)
val embeddings = engine.embedBatch(texts)

// 清理资源
engine.close()
```

### 2. 高级配置

```kotlin
// 自定义配置
val config = EmbeddingEngineFactory.EngineConfig(
    engineType = EmbeddingEngineFactory.EngineType.ONNX_RUNTIME,
    maxSequenceLength = 256,
    embeddingDim = 384,
    enableOptimization = true,
    threadCount = 4
)

val engine = EmbeddingEngineFactory.createEngine(
    modelStream = modelStream,
    vocabularyStream = vocabStream,
    config = config
)
```

### 3. 向量相似度计算

```kotlin
val text1 = "肌肉增长训练"
val text2 = "力量训练计划"

val embedding1 = engine.embed(text1)
val embedding2 = engine.embed(text2)

// 计算余弦相似度
val similarity = VectorUtils.cosineSimilarity(embedding1, embedding2)
println("相似度: $similarity")
```

## 技术实现

### 分词器 (BgeTokenizer)

- 基于WordPiece算法
- 支持中英文混合分词
- 处理特殊token：[CLS], [SEP], [PAD], [UNK]
- 自动序列长度截断和填充

### 推理引擎

#### TensorFlow Lite引擎
- 适用于移动设备优化
- 支持INT8量化模型
- 内存占用较低

#### ONNX Runtime引擎
- 高性能推理
- 支持更大批量处理
- 适合高端设备

### 向量工具 (VectorUtils)

```kotlin
// L2归一化
val normalized = VectorUtils.normalizeL2(vector)

// 相似度计算
val similarity = VectorUtils.cosineSimilarity(vec1, vec2)

// 数据库存储转换
val bytes = VectorUtils.floatArrayToByteArray(vector)
val restored = VectorUtils.byteArrayToFloatArray(bytes)

// Top-K相似搜索
val topSimilar = VectorUtils.findTopKSimilar(query, candidates, k = 10)
```

## 性能指标

### 推理性能
- **单次嵌入延迟**: < 20ms (Pixel 6)
- **批量处理**: 16条文本/批次
- **内存占用**: ~50MB (包含模型)

### 模型规格
- **模型大小**: 15MB (INT8量化)
- **向量维度**: 384维
- **最大序列长度**: 512 tokens
- **词汇表大小**: 21,128个token

## 依赖配置

在模块的`build.gradle.kts`中添加以下依赖：

```kotlin
dependencies {
    // TensorFlow Lite
    implementation("org.tensorflow:tensorflow-lite:2.13.0")
    implementation("org.tensorflow:tensorflow-lite-support:0.4.4")

    // ONNX Runtime (可选)
    implementation("com.microsoft.onnxruntime:onnxruntime-android:1.15.1")

    // Coroutines
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")
}
```

## 集成到数据层

```kotlin
// 在Repository中使用
class ChatSearchRepository @Inject constructor(
    private val embeddingEngine: EmbeddingEngine,
    private val searchDao: SearchDao
) {
    suspend fun searchSimilarChats(query: String): List<ChatEntity> {
        val queryVector = embeddingEngine.embed(query)
        val vectorBytes = VectorUtils.floatArrayToByteArray(queryVector)

        return searchDao.vectorSearch(vectorBytes, limit = 20)
    }
}
```

## 注意事项

1. **模型文件**: 确保将BGE模型和词汇表文件放置在`assets`目录下
2. **线程安全**: 嵌入引擎实例线程安全，可在多线程环境使用
3. **资源管理**: 使用完毕后务必调用`close()`方法释放资源
4. **内存优化**: 低内存设备建议使用较小的序列长度和批处理大小

## 故障排除

### 常见问题

**Q: 模型加载失败**
A: 检查模型文件是否正确放置在assets目录，文件格式是否正确

**Q: 推理速度慢**
A: 尝试切换到ONNX Runtime引擎，或减少序列长度

**Q: 内存不足**
A: 使用`getRecommendedConfig(isLowMemoryDevice = true)`获取低内存配置

## 版本历史

- **v1.0.0**: 初始版本，支持BGE模型和双引擎架构
- 计划中: 支持更多模型格式，优化批处理性能

## 贡献指南

1. 遵循项目的Clean Architecture原则
2. 确保新增功能有对应的单元测试
3. 更新相关文档和示例代码

---

**模块负责人**: GymBro AI团队
**最后更新**: 2025-01-28
