package com.example.gymbro.designSystem.components

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens

/**
 * 通用登录/操作按钮，支持文本、可选图标（Vector或Painter）和自定义颜色。
 *
 * @param text 按钮上显示的文本 (UiText)
 * @param onClick 点击事件回调
 * @param modifier Modifier修饰符
 * @param iconVector 可选的矢量图标 (ImageVector)
 * @param iconPainter 可选的绘制图标 (Painter) - 不能与iconVector同时提供
 * @param iconContentDescription 图标的可访问性描述 (UiText)，如果提供了图标则建议设置
 * @param backgroundColor 按钮背景色
 * @param contentColor 按钮内容（文本和图标）颜色
 * @param enabled 是否启用按钮
 * @param isLoading 是否显示加载指示器
 */
@Composable
fun loginButton(
    text: UiText,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    iconVector: ImageVector? = null,
    iconPainter: Painter? = null,
    iconContentDescription: UiText? = null,
    backgroundColor: Color = MaterialTheme.colorScheme.primary,
    contentColor: Color = MaterialTheme.colorScheme.onPrimary,
    enabled: Boolean = true,
    isLoading: Boolean = false,
) {
    // Ensure only one icon type is provided
    check(iconVector == null || iconPainter == null) {
        "LoginButton cannot accept both an ImageVector and a Painter. Provide only one."
    }

    Button(
        onClick = onClick,
        modifier =
        modifier
            .fillMaxWidth()
            .height(Tokens.Spacing.Massive),
        enabled = enabled && !isLoading,
        shape = RoundedCornerShape(Tokens.Radius.Button),
        colors =
        ButtonDefaults.buttonColors(
            containerColor = backgroundColor,
            contentColor = contentColor,
            disabledContainerColor = backgroundColor.copy(alpha = 0.5f),
            disabledContentColor = contentColor.copy(alpha = 0.5f),
        ),
    ) {
        val description = iconContentDescription?.asString()

        // Conditionally display icon based on provided type
        when {
            iconVector != null -> {
                Icon(
                    imageVector = iconVector,
                    contentDescription = description,
                    modifier = Modifier.size(ButtonDefaults.IconSize),
                )
                Spacer(Modifier.size(ButtonDefaults.IconSpacing))
            }
            iconPainter != null -> {
                Icon(
                    painter = iconPainter,
                    contentDescription = description,
                    modifier = Modifier.size(ButtonDefaults.IconSize),
                )
                Spacer(Modifier.size(ButtonDefaults.IconSpacing))
            }
        }

        if (isLoading) {
            CircularProgressIndicator(
                color = contentColor,
                modifier = Modifier.height(Tokens.Spacing.Large),
            )
        } else {
            Text(
                text = text.asString(),
                style =
                MaterialTheme.typography.bodyLarge.copy(
                    fontWeight = FontWeight.Bold,
                ),
            )
        }
    }
}

@GymBroPreview
@Composable
private fun loginButtonPrimaryPreview() {
    GymBroTheme {
        loginButton(
            text = UiText.DynamicString("登录"),
            onClick = { },
        )
    }
}

@GymBroPreview
@Composable
private fun loginButtonSecondaryPreview() {
    GymBroTheme {
        loginButton(
            text = UiText.DynamicString("注册"),
            onClick = { },
            backgroundColor = MaterialTheme.colorScheme.secondary,
            contentColor = MaterialTheme.colorScheme.onSecondary,
        )
    }
}

@GymBroPreview
@Composable
private fun loginButtonLoadingPreview() {
    GymBroTheme {
        loginButton(
            text = UiText.DynamicString("登录中..."),
            onClick = { },
            isLoading = true,
        )
    }
}

@GymBroPreview
@Composable
private fun loginButtonDisabledPreview() {
    GymBroTheme {
        loginButton(
            text = UiText.DynamicString("禁用按钮"),
            onClick = { },
            enabled = false,
        )
    }
}
