package com.example.gymbro.data.workout.usecase

import com.example.gymbro.domain.workout.repository.PlanRepository
import com.example.gymbro.domain.workout.usecase.PlanProgressStats
import com.example.gymbro.domain.workout.usecase.PlanProgressUseCase
import com.example.gymbro.shared.models.workout.PlanProgressStatus
import com.example.gymbro.shared.models.workout.WorkoutPlan
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

/**
 * Implementation of PlanProgressUseCase
 * 
 * Uses PlanRepository to manage workout plan progress states
 */
class PlanProgressUseCaseImpl @Inject constructor(
    private val planRepository: PlanRepository
) : PlanProgressUseCase {
    
    override suspend fun setDayProgress(
        planId: String,
        dayNumber: Int,
        status: PlanProgressStatus
    ): Result<Unit> = runCatching {
        planRepository.updateDayProgress(planId, dayNumber, status)
    }
    
    override suspend fun toggleCompleted(
        planId: String,
        dayNumber: Int
    ): Result<Unit> = runCatching {
        // Get current progress status
        val plan = planRepository.getPlanById(planId)
            ?: throw IllegalArgumentException("Plan not found: $planId")
        
        val dayPlan = plan.dailySchedule[dayNumber]
            ?: throw IllegalArgumentException("Day $dayNumber not found in plan $planId")
        
        // Toggle logic
        val newStatus = when (dayPlan.progress) {
            PlanProgressStatus.COMPLETED -> PlanProgressStatus.NOT_STARTED
            else -> PlanProgressStatus.COMPLETED
        }
        
        planRepository.updateDayProgress(planId, dayNumber, newStatus)
    }
    
    override fun observePlan(planId: String): Flow<WorkoutPlan> {
        return planRepository.observePlan(planId)
    }
    
    override suspend fun getDayProgress(
        planId: String,
        dayNumber: Int
    ): Result<PlanProgressStatus> = runCatching {
        val plan = planRepository.getPlanById(planId)
            ?: throw IllegalArgumentException("Plan not found: $planId")
        
        val dayPlan = plan.dailySchedule[dayNumber]
            ?: throw IllegalArgumentException("Day $dayNumber not found in plan $planId")
        
        dayPlan.progress
    }
    
    override suspend fun setBulkProgress(
        planId: String,
        progressMap: Map<Int, PlanProgressStatus>
    ): Result<Unit> = runCatching {
        // Validate plan exists
        val plan = planRepository.getPlanById(planId)
            ?: throw IllegalArgumentException("Plan not found: $planId")
        
        // Update each day's progress
        progressMap.forEach { (dayNumber, status) ->
            if (plan.dailySchedule.containsKey(dayNumber)) {
                planRepository.updateDayProgress(planId, dayNumber, status)
            }
        }
    }
    
    override suspend fun setAllDaysProgress(
        planId: String,
        status: PlanProgressStatus
    ): Result<Unit> = runCatching {
        val plan = planRepository.getPlanById(planId)
            ?: throw IllegalArgumentException("Plan not found: $planId")
        
        // Create progress map for all days
        val progressMap = plan.dailySchedule.keys.associateWith { status }
        
        // Use bulk update
        setBulkProgress(planId, progressMap).getOrThrow()
    }
    
    override suspend fun getPlanProgressStats(planId: String): Result<PlanProgressStats> = runCatching {
        val plan = planRepository.getPlanById(planId)
            ?: throw IllegalArgumentException("Plan not found: $planId")
        
        val totalDays = plan.dailySchedule.size
        var completedDays = 0
        var inProgressDays = 0
        var notStartedDays = 0
        
        plan.dailySchedule.values.forEach { dayPlan ->
            when (dayPlan.progress) {
                PlanProgressStatus.COMPLETED -> completedDays++
                PlanProgressStatus.IN_PROGRESS -> inProgressDays++
                PlanProgressStatus.NOT_STARTED -> notStartedDays++
            }
        }
        
        val completionPercentage = if (totalDays > 0) {
            (completedDays.toFloat() / totalDays) * 100f
        } else {
            0f
        }
        
        PlanProgressStats(
            totalDays = totalDays,
            completedDays = completedDays,
            inProgressDays = inProgressDays,
            notStartedDays = notStartedDays,
            completionPercentage = completionPercentage
        )
    }
}
