package com.example.gymbro.core.ml.integration

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ml.impl.BgeWorkoutStateVectorizer
import com.example.gymbro.core.ml.impl.InMemoryVectorSearchEngine
import com.example.gymbro.core.ml.impl.PureMemoryExerciseEngine
import com.example.gymbro.core.ml.interfaces.ExerciseData
import com.example.gymbro.core.ml.model.CandidateVector
import com.example.gymbro.core.ml.service.BgeEmbeddingService
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * AI增强系统集成测试
 *
 * 验证从训练状态向量化到智能搜索的完整流程
 * 确保各个组件能够正确协同工作
 */
class AiEnhancementSystemIntegrationTest {

    private lateinit var bgeEmbeddingService: BgeEmbeddingService
    private lateinit var workoutStateVectorizer: BgeWorkoutStateVectorizer
    private lateinit var vectorSearchEngine: InMemoryVectorSearchEngine
    private lateinit var exerciseMatchingEngine: PureMemoryExerciseEngine

    @BeforeEach
    fun setUp() {
        // Mock BGE嵌入服务
        bgeEmbeddingService = mock()

        // 创建真实实现
        workoutStateVectorizer = BgeWorkoutStateVectorizer(bgeEmbeddingService)
        vectorSearchEngine = InMemoryVectorSearchEngine()
        exerciseMatchingEngine = PureMemoryExerciseEngine()
    }

    @Test
    fun `完整AI增强流程测试 - 从训练状态到智能推荐`() = runTest {
        // === 第一阶段：准备测试数据 ===

        // 模拟BGE向量化结果
        val mockVector = FloatArray(384) { (it % 10) / 10.0f }
        whenever(bgeEmbeddingService.embedText("训练状态: 我正在进行胸部训练，刚完成卧推"))
            .thenReturn(ModernResult.Success(mockVector))

        // === 第二阶段：训练状态向量化 ===

        val workoutStateDescription = "我正在进行胸部训练，刚完成卧推"
        val vectorizedResult = workoutStateVectorizer.vectorizeWorkoutState(workoutStateDescription)

        assertTrue(vectorizedResult is ModernResult.Success)
        val vectorizedState = vectorizedResult.data
        assertEquals(workoutStateDescription, vectorizedState.originalText)
        assertEquals(384, vectorizedState.vectorDim)
        assertTrue(vectorizedState.vector.contentEquals(mockVector))

        // === 第三阶段：准备动作数据库 ===

        val exerciseData = listOf(
            ExerciseData(
                id = "push_up",
                name = "俯卧撑",
                description = "胸部力量训练的基础动作",
                primaryMuscles = listOf("胸大肌"),
                secondaryMuscles = listOf("三角肌前束", "肱三头肌"),
                equipment = "徒手",
                difficulty = "初级",
                instructions = listOf("俯撑位", "下降身体", "推起"),
                vector = FloatArray(384) { (it % 8) / 8.0f },
            ),
            ExerciseData(
                id = "dumbbell_press",
                name = "哑铃卧推",
                description = "使用哑铃进行胸部训练",
                primaryMuscles = listOf("胸大肌"),
                secondaryMuscles = listOf("三角肌前束", "肱三头肌"),
                equipment = "哑铃",
                difficulty = "中级",
                instructions = listOf("仰卧准备", "推举哑铃", "控制下降"),
                vector = FloatArray(384) { (it % 12) / 12.0f },
            ),
            ExerciseData(
                id = "incline_press",
                name = "上斜卧推",
                description = "针对胸大肌上束的训练",
                primaryMuscles = listOf("胸大肌上束"),
                secondaryMuscles = listOf("三角肌前束"),
                equipment = "杠铃",
                difficulty = "中级",
                instructions = listOf("上斜角度", "杠铃推举", "控制节奏"),
                vector = FloatArray(384) { (it % 15) / 15.0f },
            ),
        )

        // 更新动作匹配引擎数据
        val updateResult = exerciseMatchingEngine.updateExercises(exerciseData)
        assertTrue(updateResult is ModernResult.Success)

        // === 第四阶段：向量搜索测试 ===

        val candidates = exerciseData.map { exercise ->
            CandidateVector(
                id = exercise.id,
                vector = exercise.vector!!,
            )
        }

        val searchResult = vectorSearchEngine.searchSimilar(
            queryVector = vectorizedState.vector,
            candidates = candidates,
            topK = 3,
            threshold = 0.0f,
        )

        assertTrue(searchResult is ModernResult.Success)
        val searchData = searchResult.data
        assertTrue(searchData.isNotEmpty())
        assertEquals(3, searchData.size)

        // 验证搜索结果按相似度排序
        for (i in 0 until searchData.size - 1) {
            assertTrue(searchData[i].similarity >= searchData[i + 1].similarity)
        }

        // === 第五阶段：动作匹配引擎测试 ===

        val exerciseSearchResult = exerciseMatchingEngine.searchExercises(
            queryText = "胸部训练",
            topK = 2,
            threshold = 0.5f,
        )

        assertTrue(exerciseSearchResult is ModernResult.Success)
        val exerciseMatches = exerciseSearchResult.data
        assertTrue(exerciseMatches.isNotEmpty())

        // === 第六阶段：推荐系统测试 ===

        val recommendationResult = exerciseMatchingEngine.recommendNextExercises(
            currentState = workoutStateDescription,
            completedExercises = listOf("bench_press"),
            targetMuscleGroups = listOf("胸大肌"),
            topK = 2,
        )

        assertTrue(recommendationResult is ModernResult.Success)
        val recommendations = recommendationResult.data
        assertTrue(recommendations.isNotEmpty())

        // 验证推荐结果包含相关信息
        recommendations.forEach { recommendation ->
            assertNotNull(recommendation.exerciseId)
            assertNotNull(recommendation.exerciseName)
            assertTrue(recommendation.recommendationScore > 0)
            assertNotNull(recommendation.recommendationReason)
        }

        // === 第七阶段：互补动作查找测试 ===

        val complementaryResult = exerciseMatchingEngine.findComplementaryExercises(
            exerciseId = "push_up",
            topK = 2,
        )

        assertTrue(complementaryResult is ModernResult.Success)
        val complementaryExercises = complementaryResult.data
        assertTrue(complementaryExercises.isNotEmpty())

        // === 验证完整性和性能 ===

        // 检查统计信息
        val vectorStats = vectorSearchEngine.getSearchStats()
        assertTrue(vectorStats.totalVectors >= 0)
        assertEquals("in_memory_cosine", vectorStats.indexType)

        val matchingStats = exerciseMatchingEngine.getMatchingStats()
        assertEquals(3, matchingStats.totalExercises)
        assertTrue(matchingStats.averageMatchTimeMs >= 0)

        val vectorizerInfo = workoutStateVectorizer.getVectorizerInfo()
        assertEquals("BGE-WorkoutState", vectorizerInfo.name)
        assertEquals(384, vectorizerInfo.vectorDimension)
    }

    @Test
    fun `向量相似度计算准确性测试`() = runTest {
        // 创建两个相似的向量
        val vector1 = FloatArray(384) { 1.0f }
        val vector2 = FloatArray(384) { 0.9f }

        // Mock BGE服务返回这些向量
        whenever(bgeEmbeddingService.embedText("胸部训练"))
            .thenReturn(ModernResult.Success(vector1))
        whenever(bgeEmbeddingService.embedText("胸肌锻炼"))
            .thenReturn(ModernResult.Success(vector2))

        // 向量化两个相似状态
        val state1Result = workoutStateVectorizer.vectorizeWorkoutState("胸部训练")
        val state2Result = workoutStateVectorizer.vectorizeWorkoutState("胸肌锻炼")

        assertTrue(state1Result is ModernResult.Success)
        assertTrue(state2Result is ModernResult.Success)

        val state1 = state1Result.data
        val state2 = state2Result.data

        // 计算相似度
        val similarity = workoutStateVectorizer.calculateStateSimilarity(state1, state2)

        // 验证相似度在合理范围内
        assertTrue(similarity >= 0.0f)
        assertTrue(similarity <= 1.0f)
        assertTrue(similarity > 0.8f) // 期望高相似度
    }

    @Test
    fun `错误处理和边界条件测试`() = runTest {
        // 测试空查询
        val emptyQueryResult = exerciseMatchingEngine.searchExercises(
            queryText = "",
            topK = 5,
        )
        assertTrue(emptyQueryResult is ModernResult.Error)

        // 测试无效会话ID
        whenever(bgeEmbeddingService.embedText("训练状态: "))
            .thenReturn(ModernResult.Error(mock()))

        val invalidStateResult = workoutStateVectorizer.vectorizeWorkoutState("")
        assertTrue(invalidStateResult is ModernResult.Error)

        // 测试向量维度不匹配
        val vector1 = FloatArray(384) { 1.0f }
        whenever(bgeEmbeddingService.embedText("训练状态: 测试1"))
            .thenReturn(ModernResult.Success(vector1))

        val state1Result = workoutStateVectorizer.vectorizeWorkoutState("测试1")
        assertTrue(state1Result is ModernResult.Success)
        val state1 = state1Result.data

        // 创建维度不匹配的状态
        val state2 = state1.copy(vector = FloatArray(256) { 1.0f }, vectorDim = 256)

        // 计算相似度应该返回0
        val similarity = workoutStateVectorizer.calculateStateSimilarity(state1, state2)
        assertEquals(0.0f, similarity)
    }

    @Test
    fun `批量处理性能测试`() = runTest {
        // 准备大量查询
        val queries = (1..10).map { "训练查询 $it" }

        // Mock BGE服务批量返回
        queries.forEach { query ->
            val mockVector = FloatArray(384) { (it + query.hashCode() % 10) / 10.0f }
            whenever(bgeEmbeddingService.embedText("训练状态: $query"))
                .thenReturn(ModernResult.Success(mockVector))
        }

        // 批量向量化
        val vectorizedStates =
            mutableListOf<ModernResult<com.example.gymbro.core.ml.interfaces.VectorizedWorkoutState>>()
        queries.forEach { query ->
            vectorizedStates.add(workoutStateVectorizer.vectorizeWorkoutState(query))
        }

        // 验证所有向量化都成功
        vectorizedStates.forEach { result ->
            assertTrue(result is ModernResult.Success)
        }

        // 批量动作匹配
        val batchMatchResult = exerciseMatchingEngine.batchMatch(queries, topK = 3)
        assertTrue(batchMatchResult is ModernResult.Success)

        val batchResults = batchMatchResult.data
        assertEquals(queries.size, batchResults.size)
    }
}
