package com.example.gymbro.data.workout.session.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.example.gymbro.data.workout.session.entity.SessionEntity
import kotlinx.coroutines.flow.Flow

/**
 * 会话数据访问对象 - SessionDB DAO
 *
 * 基于 05_Final_Database_Architecture.md 和 04_Implementation_Plan_CleanSlate_SessionDB.md 设计
 * 提供训练会话管理功能
 */
@Dao
interface SessionDao {

    // ==================== 基础 CRUD ====================

    @Query("SELECT * FROM workout_sessions WHERE id = :sessionId")
    suspend fun getSessionById(sessionId: String): SessionEntity?

    @Query("SELECT * FROM workout_sessions WHERE userId = :userId ORDER BY startTime DESC")
    fun getUserSessions(userId: String): Flow<List<SessionEntity>>

    @Query("SELECT * FROM workout_sessions WHERE status = 'IN_PROGRESS' AND userId = :userId")
    suspend fun getActiveSession(userId: String): SessionEntity?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSession(session: SessionEntity)

    @Update
    suspend fun updateSession(session: SessionEntity)

    @Query("DELETE FROM workout_sessions WHERE id = :sessionId")
    suspend fun deleteSession(sessionId: String)

    // ==================== 会话管理 ====================

    @Query("UPDATE workout_sessions SET status = :status WHERE id = :sessionId")
    suspend fun updateSessionStatus(sessionId: String, status: String)

    @Query(
        "UPDATE workout_sessions SET endTime = :endTime, totalDuration = :duration, status = 'COMPLETED' WHERE id = :sessionId",
    )
    suspend fun completeSession(sessionId: String, endTime: Long, duration: Long)

    @Query("UPDATE workout_sessions SET lastAutosaveTime = :saveTime WHERE id = :sessionId")
    suspend fun updateAutoSaveTime(sessionId: String, saveTime: Long)

    // ==================== 历史查询 ====================

    @Query(
        "SELECT * FROM workout_sessions WHERE userId = :userId AND endTime IS NOT NULL ORDER BY endTime DESC LIMIT :limit",
    )
    fun getRecentCompletedSessions(userId: String, limit: Int): Flow<List<SessionEntity>>

    @Query("SELECT COUNT(*) FROM workout_sessions WHERE userId = :userId AND status = 'COMPLETED'")
    suspend fun getCompletedSessionCount(userId: String): Int

    @Query(
        "SELECT * FROM workout_sessions WHERE userId = :userId AND templateId = :templateId AND status = 'COMPLETED' ORDER BY endTime DESC",
    )
    fun getSessionsByTemplate(userId: String, templateId: String): Flow<List<SessionEntity>>

    // ==================== 统计查询 ====================

    @Query(
        "SELECT SUM(totalVolume) FROM workout_sessions WHERE userId = :userId AND status = 'COMPLETED' AND startTime >= :startTime",
    )
    suspend fun getTotalVolumeInPeriod(userId: String, startTime: Long): Double?

    @Query("SELECT AVG(totalDuration) FROM workout_sessions WHERE userId = :userId AND status = 'COMPLETED'")
    suspend fun getAverageSessionDuration(userId: String): Long?

    @Query(
        "SELECT COUNT(*) FROM workout_sessions WHERE userId = :userId AND status = 'COMPLETED' AND startTime >= :startTime AND startTime < :endTime",
    )
    suspend fun getSessionCountInPeriod(userId: String, startTime: Long, endTime: Long): Int
}
