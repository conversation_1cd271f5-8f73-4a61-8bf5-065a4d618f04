package com.example.gymbro.data.mapper.user

import com.example.gymbro.data.local.entity.user.UserSettingsEntity
import com.example.gymbro.domain.profile.model.user.settings.NotificationSettings
import com.example.gymbro.domain.profile.model.user.settings.PrivacySettings
import com.example.gymbro.domain.profile.model.user.settings.SoundSettings
import com.example.gymbro.domain.profile.model.user.settings.UserSettings

/**
 * 用户设置映射扩展函数
 * 负责在领域模型和数据实体间进行转换
 */

/**
 * 将UserSettingsEntity转换为UserSettings领域模型
 */
fun UserSettingsEntity.toDomain(): UserSettings =
    UserSettings(
        userId = userId,
        notificationsEnabled = notificationsEnabled,
        darkModeEnabled = themeMode == "dark",
        // 默认值
        backupEnabled = autoBackupEnabled,
        language = languageCode,
        // 默认值
        measurementUnit = measurementSystem,
        privacySettings = PrivacySettings.getDefault(),
        notifications = NotificationSettings(), // 使用默认通知设置
        sounds =
        SoundSettings(
            timer = soundsEnabled,
            chat = soundsEnabled,
        ),
        lastSyncTimestamp = lastBackupTime,
        lastModifiedTimestamp = lastModified,
    )

/**
 * 将UserSettings领域模型转换为UserSettingsEntity
 */
fun UserSettings.toEntity(): UserSettingsEntity =
    UserSettingsEntity(
        userId = userId,
        themeMode = if (darkModeEnabled) "dark" else "light",
        languageCode = language ?: "zh-CN",
        measurementSystem = measurementUnit,
        notificationsEnabled = notificationsEnabled,
        soundsEnabled = sounds.timer || sounds.chat,
        // 默认值
        dataSharingEnabled = autoSync,
        // 默认值
        autoBackupEnabled = backupEnabled,
        // 默认每周
        lastBackupTime = lastSyncTimestamp,
        // 默认值
        // 默认值
        matchByFitnessLevel = false, // 默认值
        lastModified = lastModifiedTimestamp,
    )

/**
 * 批量转换UserSettingsEntity列表为UserSettings列表
 */
fun List<UserSettingsEntity>.toDomain(): List<UserSettings> = map { it.toDomain() }

/**
 * 批量转换UserSettings列表为UserSettingsEntity列表
 */
fun List<UserSettings>.toEntity(): List<UserSettingsEntity> = map { it.toEntity() }
