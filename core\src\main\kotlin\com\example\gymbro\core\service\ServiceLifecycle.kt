package com.example.gymbro.core.service

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow

/**
 * 服务生命周期管理接口
 *
 * 提供平台无关的服务生命周期管理抽象，使得服务的创建、启动、停止和销毁可以被统一监控和管理。
 */
interface ServiceLifecycleManager {
    /**
     * 所有已注册服务的生命周期状态
     */
    val serviceStates: StateFlow<Map<String, ServiceLifecycleState>>

    /**
     * 注册服务生命周期
     *
     * @param serviceId 服务标识符
     * @param initialState 服务的初始状态
     * @return 操作结果
     */
    fun registerService(
        serviceId: String,
        initialState: ServiceLifecycleState = ServiceLifecycleState.NOT_CREATED,
    ): ServiceOperationResult

    /**
     * 解除注册服务生命周期
     *
     * @param serviceId 服务标识符
     * @return 操作结果
     */
    fun unregisterService(serviceId: String): ServiceOperationResult

    /**
     * 更新服务状态
     *
     * @param serviceId 服务标识符
     * @param newState 新的服务状态
     * @return 操作结果
     */
    fun updateServiceState(
        serviceId: String,
        newState: ServiceLifecycleState,
    ): ServiceOperationResult

    /**
     * 获取特定服务的状态流
     *
     * @param serviceId 服务标识符
     * @return 服务状态的Flow对象
     */
    fun observeServiceState(serviceId: String): Flow<ServiceLifecycleState>

    /**
     * 监听服务状态变化
     *
     * @param serviceId 服务标识符
     * @param listener 状态变化监听器
     * @return 监听器ID，用于取消监听
     */
    fun addStateChangeListener(
        serviceId: String,
        listener: ServiceStateChangeListener,
    ): String

    /**
     * 移除服务状态变化监听器
     *
     * @param listenerId 监听器ID
     * @return 操作结果
     */
    fun removeStateChangeListener(listenerId: String): ServiceOperationResult
}

/**
 * 服务状态变化监听器接口
 */
fun interface ServiceStateChangeListener {
    /**
     * 当服务状态发生变化时调用
     *
     * @param serviceId 服务标识符
     * @param oldState 旧状态
     * @param newState 新状态
     */
    fun onStateChanged(
        serviceId: String,
        oldState: ServiceLifecycleState,
        newState: ServiceLifecycleState,
    )
}

/**
 * 服务生命周期回调接口
 *
 * 服务实现类可以实现此接口以接收生命周期事件。
 */
interface ServiceLifecycleCallbacks {
    /**
     * 服务创建时调用
     *
     * @param parameters 创建参数
     */
    fun onCreate(parameters: Map<String, Any>)
}

/**
 * 基础服务接口
 *
 * 定义服务的基本能力和属性，所有服务类型都应实现此接口。
 */
interface Service {
    /**
     * 服务描述
     */
    val description: String

    /**
     * 服务类型
     */
    val type: ServiceType

    /**
     * 当前服务配置
     */
    val configuration: Map<String, Any>
}

/**
 * 服务类型枚举
 */
enum class ServiceType
