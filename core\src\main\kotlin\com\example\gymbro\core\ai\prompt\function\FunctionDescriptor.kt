package com.example.gymbro.core.ai.prompt.function

/**
 * 通用函数描述，兼容 OpenAI / Azure / 阿里百川 Chat Completion `tools / functions`
 * 基于 funtioncall集成.md 的规范设计
 *
 * 遵循 GymBro 项目的 Clean Architecture + MVI 2.0 架构标准
 */
data class FunctionDescriptor(
    val name: String,
    val description: String,
    val jsonSchema: String, // OpenAI 期待的 parameters 段
)

/**
 * Function Call 调用数据
 * 表示模型决定调用的函数及其参数
 */
data class FunctionCall(
    val name: String,
    val arguments: String, // JSON 格式的参数字符串
)

/**
 * Function Call 结果
 * 表示函数执行后的返回结果
 */
data class FunctionResult(
    val success: Boolean,
    val result: String,
    val error: String? = null,
)
