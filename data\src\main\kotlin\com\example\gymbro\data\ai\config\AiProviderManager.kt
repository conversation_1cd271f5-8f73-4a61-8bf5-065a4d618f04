package com.example.gymbro.data.ai.config

import com.example.gymbro.domain.coach.config.AiConfig
import com.example.gymbro.domain.coach.config.AiProviderConfig
import com.example.gymbro.domain.coach.config.AiProviderManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * AI提供商管理器实现
 */
@Singleton
class AiProviderManagerImpl
@Inject
constructor(
    private val aiConfig: AiConfig,
) : AiProviderManager {
    init {
        // 启动时验证配置
        validateConfigurations()
    }

    private val _currentProvider = MutableStateFlow(getDefaultProvider())
    override val currentProvider: StateFlow<AiProviderConfig> = _currentProvider.asStateFlow()

    override val availableProviders: List<AiProviderConfig> by lazy {
        buildList {
            // Google Gemini配置
            if (aiConfig.googleApiKey.isNotEmpty()) {
                add(
                    GoogleGeminiConfig(
                        apiKey = aiConfig.googleApiKey,
                        baseUrl = aiConfig.googleBaseUrl,
                        defaultModel = aiConfig.defaultGoogleModel,
                        summaryModelConfig = aiConfig.googleSummaryModel,
                    ),
                )
            }

            // DeepSeek配置
            if (aiConfig.deepseekApiKey.isNotEmpty()) {
                add(
                    DeepSeekConfig(
                        apiKey = aiConfig.deepseekApiKey,
                        baseUrl = aiConfig.deepseekBaseUrl,
                        defaultModel = aiConfig.defaultDeepseekModel,
                        summaryModelConfig = aiConfig.deepseekSummaryModel,
                    ),
                )
            }

            // OpenAI配置（如果有的话）
            if (aiConfig.openaiApiKey.isNotEmpty()) {
                add(
                    OpenAiCompatibleConfig(
                        apiKey = aiConfig.openaiApiKey,
                        baseUrl = aiConfig.openaiBaseUrl,
                        defaultModel = aiConfig.openaiDefaultModel,
                        name = "openai",
                        summaryModelConfig = aiConfig.openaiSummaryModel,
                    ),
                )
            }
        }
    }

    override fun switchToProvider(providerName: String): Boolean {
        val provider = getProviderConfig(providerName)
        return if (provider != null) {
            _currentProvider.value = provider
            Timber.d("切换到AI提供商: $providerName")
            true
        } else {
            Timber.w("未找到AI提供商: $providerName")
            false
        }
    }

    override fun getProviderConfig(providerName: String): AiProviderConfig? = availableProviders.find {
        it.name == providerName
    }

    override fun getSummaryProviderConfig(): AiProviderConfig {
        // 优先使用配置指定的摘要提供商
        val preferredProvider = getProviderConfig(aiConfig.summaryProvider)
        if (preferredProvider != null) {
            return preferredProvider
        }

        // 回退策略：Google > DeepSeek > OpenAI
        return when {
            aiConfig.googleApiKey.isNotEmpty() -> GoogleGeminiConfig(
                apiKey = aiConfig.googleApiKey,
                baseUrl = aiConfig.googleBaseUrl,
                defaultModel = aiConfig.defaultGoogleModel,
                summaryModelConfig = aiConfig.googleSummaryModel,
            )
            aiConfig.deepseekApiKey.isNotEmpty() -> DeepSeekConfig(
                apiKey = aiConfig.deepseekApiKey,
                baseUrl = aiConfig.deepseekBaseUrl,
                defaultModel = aiConfig.defaultDeepseekModel,
                summaryModelConfig = aiConfig.deepseekSummaryModel,
            )
            aiConfig.openaiApiKey.isNotEmpty() -> OpenAiCompatibleConfig(
                apiKey = aiConfig.openaiApiKey,
                baseUrl = aiConfig.openaiBaseUrl,
                defaultModel = aiConfig.openaiDefaultModel,
                name = "openai",
                summaryModelConfig = aiConfig.openaiSummaryModel,
            )
            else -> {
                Timber.w("没有配置任何AI API密钥，返回默认摘要配置")
                // 返回Google默认配置作为摘要后备
                GoogleGeminiConfig(
                    apiKey = aiConfig.googleApiKey.ifEmpty { "missing-key" },
                    baseUrl = aiConfig.googleBaseUrl,
                    defaultModel = aiConfig.defaultGoogleModel,
                    summaryModelConfig = aiConfig.googleSummaryModel,
                )
            }
        }
    }

    /**
     * 验证所有AI配置
     */
    private fun validateConfigurations() {
        Timber.d("=== AI Provider Configuration Validation ===")

        // 验证Google配置
        if (aiConfig.googleApiKey.isNotEmpty()) {
            if (aiConfig.googleBaseUrl.isEmpty() || aiConfig.defaultGoogleModel.isEmpty()) {
                Timber.w(
                    "Google配置不完整: baseUrl=${aiConfig.googleBaseUrl}, model=${aiConfig.defaultGoogleModel}",
                )
            } else {
                Timber.d("✅ Google配置完整")
            }
        }

        // 验证OpenAI配置
        if (aiConfig.openaiApiKey.isNotEmpty()) {
            if (aiConfig.openaiBaseUrl.isEmpty() || aiConfig.openaiDefaultModel.isEmpty()) {
                Timber.w("❌ OpenAI配置不完整:")
                Timber.w("  - API Key: ${if (aiConfig.openaiApiKey.isNotEmpty()) "✅" else "❌"}")
                Timber.w(
                    "  - Base URL: ${if (aiConfig.openaiBaseUrl.isNotEmpty()) "✅ ${aiConfig.openaiBaseUrl}" else "❌ Empty"}",
                )
                Timber.w(
                    "  - Model: ${if (aiConfig.openaiDefaultModel.isNotEmpty()) "✅ ${aiConfig.openaiDefaultModel}" else "❌ Empty"}",
                )
            } else {
                Timber.d("✅ OpenAI配置完整: ${aiConfig.openaiBaseUrl} - ${aiConfig.openaiDefaultModel}")
            }
        }

        // 验证DeepSeek配置
        if (aiConfig.deepseekApiKey.isNotEmpty()) {
            if (aiConfig.deepseekBaseUrl.isEmpty() || aiConfig.defaultDeepseekModel.isEmpty()) {
                Timber.w(
                    "DeepSeek配置不完整: baseUrl=${aiConfig.deepseekBaseUrl}, model=${aiConfig.defaultDeepseekModel}",
                )
            } else {
                Timber.d("✅ DeepSeek配置完整")
            }
        }

        Timber.d("=== End Configuration Validation ===")
    }

    /**
     * 获取默认的AI提供商
     * 优先级：DeepSeek > Google Gemini > OpenAI
     */
    private fun getDefaultProvider(): AiProviderConfig =
        when {
            aiConfig.deepseekApiKey.isNotEmpty() ->
                DeepSeekConfig(
                    apiKey = aiConfig.deepseekApiKey,
                    baseUrl = aiConfig.deepseekBaseUrl,
                    defaultModel = aiConfig.defaultDeepseekModel,
                    summaryModelConfig = aiConfig.deepseekSummaryModel,
                )
            aiConfig.googleApiKey.isNotEmpty() ->
                GoogleGeminiConfig(
                    apiKey = aiConfig.googleApiKey,
                    baseUrl = aiConfig.googleBaseUrl,
                    defaultModel = aiConfig.defaultGoogleModel,
                    summaryModelConfig = aiConfig.googleSummaryModel,
                )
            aiConfig.openaiApiKey.isNotEmpty() ->
                OpenAiCompatibleConfig(
                    apiKey = aiConfig.openaiApiKey,
                    baseUrl = aiConfig.openaiBaseUrl,
                    defaultModel = aiConfig.openaiDefaultModel,
                    name = "openai",
                    summaryModelConfig = aiConfig.openaiSummaryModel,
                )
            else -> {
                Timber.w("没有配置任何AI API密钥，使用DeepSeek默认配置")
                // 返回DeepSeek默认配置，避免硬编码
                DeepSeekConfig(
                    apiKey = aiConfig.deepseekApiKey.ifEmpty { "sk-default-key-missing" },
                    defaultModel = aiConfig.defaultDeepseekModel,
                    baseUrl = aiConfig.deepseekBaseUrl,
                    summaryModelConfig = aiConfig.deepseekSummaryModel,
                )
            }
        }
}
