package com.example.gymbro.core.error.extensions

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 统一Flow扩展函数集合
 * 本文件整合了通用Flow处理函数和ModernResult相关的Flow扩展函数
 */

//region 通用Flow扩展

/**
 * 安全地收集Flow，处理开始、每个元素、错误和完成事件
 * 挂起函数版本，适用于协程中直接调用
 *
 * @param onStart 流开始收集时的回调
 * @param onEach 处理每个元素的回调
 * @param onError 处理错误的回调
 * @param onComplete 流收集完成时的回调
 */
suspend fun <T> Flow<T>.collectSafelySuspend(
    onStart: () -> Unit = {},
    onEach: (T) -> Unit,
    onError: (Throwable) -> Unit,
    onComplete: () -> Unit = {},
): Unit =
    try {
        onStart()
        this
            .onCompletion { onComplete() }
            .catch { e ->
                // 紧急修复：正确处理CancellationException
                if (e is kotlinx.coroutines.CancellationException) {
                    throw e // 重新抛出，保持结构化并发语义
                }
                Timber.e(e, "Error collecting flow")
                onError(e)
            }.collect { value ->
                try {
                    onEach(value)
                } catch (e: Throwable) {
                    Timber.e(e, "Error processing flow element")
                    onError(e)
                }
            }
    } catch (e: Throwable) {
        Timber.e(e, "Uncaught exception in collectSafelySuspend")
        onError(e)
    }

/**
 * 安全地收集Flow并返回指定类型的结果
 * 特别适用于需要具体类型结果的场景
 *
 * @param onStart 流开始收集时的回调
 * @param onEach 处理每个元素并返回结果的回调
 * @param onError 处理错误并返回结果的回调
 * @param onComplete 流收集完成时的回调
 * @return 由onEach或onError回调返回的类型R的结果
 */
suspend fun <T, R> Flow<T>.collectSafelyTyped(
    onStart: () -> Unit = {},
    onEach: (T) -> R,
    onError: (Throwable) -> R,
    onComplete: () -> Unit = {},
): R =
    try {
        onStart()
        var result: R? = null
        var hasResult = false

        this
            .onCompletion {
                onComplete()
            }.catch { e ->
                // 紧急修复：正确处理CancellationException
                if (e is kotlinx.coroutines.CancellationException) {
                    throw e // 重新抛出，保持结构化并发语义
                }
                Timber.e(e, "Error collecting flow in collectSafelyTyped")
                result = onError(e)
                hasResult = true
            }.collect { value ->
                try {
                    result = onEach(value)
                    hasResult = true
                } catch (e: Throwable) {
                    Timber.e(e, "Error processing flow element in collectSafelyTyped")
                    if (!hasResult) {
                        result = onError(e)
                        hasResult = true
                    }
                }
            }

        if (hasResult && result != null) {
            result!!
        } else {
            throw IllegalStateException("Flow collected successfully but no result was produced")
        }
    } catch (e: Throwable) {
        Timber.e(e, "Uncaught exception in collectSafelyTyped")
        onError(e)
    }

//endregion

//region ModernResult Flow扩展

/**
 * 将普通Flow转换为ModernResult包装的Flow
 *
 * @param operationName 操作名称，用于错误处理
 * @return 包装成ModernResult的Flow
 */
fun <T> Flow<T>.asModernResultFlow(operationName: String): Flow<ModernResult<T>> =
    flow {
        this@asModernResultFlow
            .onStart { emit(ModernResult.Loading) }
            .catch { error ->
                // 紧急修复：正确处理CancellationException
                if (error is kotlinx.coroutines.CancellationException) {
                    throw error // 重新抛出，保持结构化并发语义
                }
                emit(
                    ModernResult.Error(
                        error.toModernDataError(
                            operationName = operationName,
                        ),
                    ),
                )
            }.collect { value ->
                emit(ModernResult.Success(value))
            }
    }

/**
 * 安全地收集Flow<ModernResult>的结果
 *
 * @param onSuccess 成功回调
 * @param onError 错误回调
 * @param onLoading 加载回调
 */
suspend fun <T> Flow<ModernResult<T>>.collectSafely(
    onSuccess: suspend (T) -> Unit,
    onError: suspend (ModernDataError) -> Unit,
    onLoading: suspend () -> Unit = {},
) {
    collect { result ->
        when (result) {
            is ModernResult.Success -> onSuccess(result.data)
            is ModernResult.Error -> onError(result.error)
            ModernResult.Loading -> onLoading()
        }
    }
}

/**
 * 映射Flow<ModernResult<T>>中的成功结果到新的类型
 *
 * @param transform 转换函数
 * @return 映射后的Flow<ModernResult<R>>
 */
fun <T, R> Flow<ModernResult<T>>.mapResult(
    transform: suspend (T) -> R,
): Flow<ModernResult<R>> =
    map { result ->
        when (result) {
            ModernResult.Loading -> ModernResult.Loading
            is ModernResult.Success -> ModernResult.Success(transform(result.data))
            is ModernResult.Error -> ModernResult.Error(result.error)
        }
    }

/**
 * 映射Flow<ModernResult<T>>中的成功结果到新的ModernResult<R>
 *
 * @param transform 转换函数
 * @return 映射后的Flow<ModernResult<R>>
 */
fun <T, R> Flow<ModernResult<T>>.flatMapResult(
    transform: suspend (T) -> ModernResult<R>,
): Flow<ModernResult<R>> =
    map { result ->
        when (result) {
            ModernResult.Loading -> ModernResult.Loading
            is ModernResult.Success -> transform(result.data)
            is ModernResult.Error -> ModernResult.Error(result.error)
        }
    }

/**
 * 过滤Flow<ModernResult<T>>中的Loading状态
 *
 * @return 过滤后的Flow<ModernResult<T>>
 */
fun <T> Flow<ModernResult<T>>.filterLoading(): Flow<ModernResult<T>> =
    flow {
        collect { result ->
            if (result !is ModernResult.Loading) {
                emit(result)
            }
        }
    }

/**
 * 过滤Flow<ModernResult<T>>中的Error状态
 *
 * @return 过滤后的Flow<ModernResult<T>>
 */
fun <T> Flow<ModernResult<T>>.filterError(): Flow<ModernResult<T>> =
    flow {
        collect { result ->
            if (result !is ModernResult.Error) {
                emit(result)
            }
        }
    }

/**
 * 过滤Flow<ModernResult<T>>，只保留Success状态
 *
 * @return 过滤后的Flow<T>
 */
fun <T> Flow<ModernResult<T>>.filterSuccess(): Flow<T> =
    flow {
        collect { result ->
            if (result is ModernResult.Success) {
                emit(result.data)
            }
        }
    }

/**
 * 安全收集 Flow<ModernResult<T>> 并处理各种结果状态 (ViewModel作用域版本)
 *
 * @param viewModel ViewModel实例，用于在其viewModelScope中启动协程
 * @param onSuccess 处理成功结果的回调
 * @param onLoading 处理加载状态的回调 (可选)
 * @param onError 处理错误结果的回调 (可选)
 */
inline fun <T> Flow<ModernResult<T>>.collectSafely(
    viewModel: ViewModel,
    crossinline onSuccess: suspend (T) -> Unit,
    crossinline onLoading: suspend () -> Unit = {},
    crossinline onError: suspend (ModernDataError) -> Unit = {},
) {
    viewModel.viewModelScope.launch {
        try {
            <EMAIL> { result ->
                when (result) {
                    is ModernResult.Success -> onSuccess(result.data)
                    is ModernResult.Error -> onError(result.error)
                    is ModernResult.Loading -> onLoading()
                }
            }
        } catch (e: Exception) {
            // 紧急修复：正确处理CancellationException
            if (e is kotlinx.coroutines.CancellationException) {
                throw e // 重新抛出，保持结构化并发语义
            }
            Timber.e(e, "Error collecting Flow<ModernResult>")
            onError(
                e.toModernDataError(
                    operationName = "collectSafely",
                    uiMessage = UiText.DynamicString("收集Flow时发生错误"),
                ),
            )
        }
    }
}

/**
 * 从Flow<ModernResult<T>>中提取第一个成功值，如果没有则返回默认值
 *
 * @param defaultValue 如果Flow为空或仅包含错误/加载状态时的默认返回值
 * @return 提取的成功值或默认值
 */
suspend fun <T> Flow<ModernResult<T>>.getFirstSuccessOrDefault(defaultValue: T): T =
    try {
        val result = this.firstOrNull()
        when (result) {
            is ModernResult.Success -> result.data
            else -> defaultValue
        }
    } catch (e: Exception) {
        Timber.e(e, "Error getting first success value from Flow<ModernResult>")
        defaultValue
    }

/**
 * 安全获取Flow<ModernResult<String>>中的字符串或默认值
 *
 * @param defaultValue 默认值
 * @return 包含字符串或默认值的Flow
 */
fun Flow<ModernResult<String>>.getStringOrEmpty(defaultValue: String = ""): Flow<String> =
    this.map { result ->
        when (result) {
            is ModernResult.Success -> result.data
            is ModernResult.Error -> defaultValue
            is ModernResult.Loading -> defaultValue
        }
    }

/**
 * 确保非空结果的扩展函数，将可空类型的 ModernResult<T?> 转换为非空类型的 ModernResult<T>
 * 如果原始数据为 null，则使用提供的默认值
 *
 * @param defaultValue 当数据为 null 时使用的默认值
 * @return 包含非空数据的 Flow<ModernResult<T>>
 */
fun <T : Any> Flow<ModernResult<T?>>.ensureNonNull(defaultValue: T): Flow<ModernResult<T>> =
    this.map { result ->
        when (result) {
            is ModernResult.Success -> {
                ModernResult.Success(result.data ?: defaultValue)
            }
            is ModernResult.Error -> ModernResult.Error(result.error)
            is ModernResult.Loading -> ModernResult.Loading
        }
    }

/**
 * 将非 Flow 类型的 ModernResult<T> 转换为 Flow<ModernResult<T>>
 * 用于统一处理同步和异步结果
 *
 * @return 包含原始结果的 Flow
 */
fun <T> ModernResult<T>.asFlow(): Flow<ModernResult<T>> = flowOf(this)

/**
 * 将任意类型 T 转换为成功状态的 Flow<ModernResult<T>>
 * 适用于需要将简单值包装为 Flow<ModernResult> 的场景
 *
 * @return 包含成功结果的 Flow
 */
fun <T> T.asSuccessFlow(): Flow<ModernResult<T>> = flowOf(ModernResult.Success(this))

//endregion
