package com.example.gymbro.core.logging

import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Timber日志器实现
 *
 * 实现Logger接口，将日志调用委托给Timber框架。
 * 提供统一的日志接口，解耦具体的日志框架实现。
 */
@Singleton
class TimberLogger @Inject constructor() : Logger {

    override fun v(message: String, vararg args: Any?) {
        Timber.v(message, *args)
    }

    override fun v(t: Throwable?, message: String, vararg args: Any?) {
        Timber.v(t, message, *args)
    }

    override fun v(t: Throwable?) {
        Timber.v(t)
    }

    override fun d(message: String, vararg args: Any?) {
        Timber.d(message, *args)
    }

    override fun d(t: Throwable?, message: String, vararg args: Any?) {
        Timber.d(t, message, *args)
    }

    override fun d(t: Throwable?) {
        Timber.d(t)
    }

    override fun i(message: String, vararg args: Any?) {
        Timber.i(message, *args)
    }

    override fun i(t: Throwable?, message: String, vararg args: Any?) {
        Timber.i(t, message, *args)
    }

    override fun i(t: Throwable?) {
        Timber.i(t)
    }

    override fun w(message: String, vararg args: Any?) {
        Timber.w(message, *args)
    }

    override fun w(t: Throwable?, message: String, vararg args: Any?) {
        Timber.w(t, message, *args)
    }

    override fun w(t: Throwable?) {
        Timber.w(t)
    }

    override fun e(message: String, vararg args: Any?) {
        Timber.e(message, *args)
    }

    override fun e(t: Throwable?, message: String, vararg args: Any?) {
        Timber.e(t, message, *args)
    }

    override fun e(t: Throwable?) {
        Timber.e(t)
    }

    override fun wtf(message: String, vararg args: Any?) {
        Timber.wtf(message, *args)
    }

    override fun wtf(t: Throwable?, message: String, vararg args: Any?) {
        Timber.wtf(t, message, *args)
    }

    override fun wtf(t: Throwable?) {
        Timber.wtf(t)
    }

    override fun tag(tag: String): Logger {
        return TaggedTimberLogger(tag)
    }
}

/**
 * 带标签的Timber日志器
 *
 * 为特定标签创建的日志器实例，所有日志调用都会带上指定的标签。
 */
private class TaggedTimberLogger(private val tag: String) : Logger {

    override fun v(message: String, vararg args: Any?) {
        Timber.tag(tag).v(message, *args)
    }

    override fun v(t: Throwable?, message: String, vararg args: Any?) {
        Timber.tag(tag).v(t, message, *args)
    }

    override fun v(t: Throwable?) {
        Timber.tag(tag).v(t)
    }

    override fun d(message: String, vararg args: Any?) {
        Timber.tag(tag).d(message, *args)
    }

    override fun d(t: Throwable?, message: String, vararg args: Any?) {
        Timber.tag(tag).d(t, message, *args)
    }

    override fun d(t: Throwable?) {
        Timber.tag(tag).d(t)
    }

    override fun i(message: String, vararg args: Any?) {
        Timber.tag(tag).i(message, *args)
    }

    override fun i(t: Throwable?, message: String, vararg args: Any?) {
        Timber.tag(tag).i(t, message, *args)
    }

    override fun i(t: Throwable?) {
        Timber.tag(tag).i(t)
    }

    override fun w(message: String, vararg args: Any?) {
        Timber.tag(tag).w(message, *args)
    }

    override fun w(t: Throwable?, message: String, vararg args: Any?) {
        Timber.tag(tag).w(t, message, *args)
    }

    override fun w(t: Throwable?) {
        Timber.tag(tag).w(t)
    }

    override fun e(message: String, vararg args: Any?) {
        Timber.tag(tag).e(message, *args)
    }

    override fun e(t: Throwable?, message: String, vararg args: Any?) {
        Timber.tag(tag).e(t, message, *args)
    }

    override fun e(t: Throwable?) {
        Timber.tag(tag).e(t)
    }

    override fun wtf(message: String, vararg args: Any?) {
        Timber.tag(tag).wtf(message, *args)
    }

    override fun wtf(t: Throwable?, message: String, vararg args: Any?) {
        Timber.tag(tag).wtf(t, message, *args)
    }

    override fun wtf(t: Throwable?) {
        Timber.tag(tag).wtf(t)
    }

    override fun tag(tag: String): Logger {
        return TaggedTimberLogger(tag)
    }
}
