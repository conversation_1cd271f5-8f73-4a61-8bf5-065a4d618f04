package com.example.gymbro.data.autosave.internal

import com.example.gymbro.core.autosave.storage.AutoSaveStorage
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import javax.inject.Inject

/**
 * 数据库存储实现
 *
 * 🎯 功能特性：
 * - 使用Room进行持久化存储
 * - 支持泛型数据类型
 * - 集成ModernResult错误处理
 * - 提供完整的CRUD操作
 *
 * @param T 要存储的数据类型
 * @param saveOperation 保存操作函数
 * @param loadOperation 加载操作函数
 * @param deleteOperation 删除操作函数
 * @param existsOperation 检查存在性操作函数
 * @param logger 日志记录器
 */
class DatabaseStorage<T : Any> @Inject constructor(
    private val saveOperation: suspend (T) -> ModernResult<Unit>,
    private val loadOperation: suspend (String) -> ModernResult<T?>,
    private val deleteOperation: suspend (String) -> ModernResult<Unit>,
    private val existsOperation: suspend (String) -> ModernResult<Boolean>,
    private val logger: Logger,
) : AutoSaveStorage<T> {

    override suspend fun save(id: String, data: T) {
        try {
            logger.d("DatabaseStorage", "开始保存到数据库: $id")

            when (val result = saveOperation(data)) {
                is ModernResult.Success -> {
                    logger.d("DatabaseStorage", "数据已保存到数据库: $id")
                }
                is ModernResult.Error -> {
                    logger.e("DatabaseStorage", "保存到数据库失败: $id, error: ${result.error}")
                    throw Exception("保存到数据库失败: ${result.error}")
                }
                is ModernResult.Loading -> {
                    // 不应该发生
                    logger.w("DatabaseStorage", "保存操作返回Loading状态: $id")
                }
            }
        } catch (e: Exception) {
            logger.e(e, "保存到数据库异常: $id")
            throw e
        }
    }

    override suspend fun restore(id: String): T? {
        return try {
            logger.d("DatabaseStorage", "开始从数据库恢复: $id")

            when (val result = loadOperation(id)) {
                is ModernResult.Success -> {
                    logger.d("DatabaseStorage", "数据已从数据库恢复: $id")
                    result.data
                }
                is ModernResult.Error -> {
                    logger.e("DatabaseStorage", "从数据库恢复失败: $id, error: ${result.error}")
                    null
                }
                is ModernResult.Loading -> {
                    // 不应该发生
                    logger.w("DatabaseStorage", "加载操作返回Loading状态: $id")
                    null
                }
            }
        } catch (e: Exception) {
            logger.e(e, "从数据库恢复异常: $id")
            null
        }
    }

    override suspend fun clear(id: String) {
        try {
            logger.d("DatabaseStorage", "开始从数据库删除: $id")

            when (val result = deleteOperation(id)) {
                is ModernResult.Success -> {
                    logger.d("DatabaseStorage", "数据已从数据库删除: $id")
                }
                is ModernResult.Error -> {
                    logger.e("DatabaseStorage", "从数据库删除失败: $id, error: ${result.error}")
                    throw Exception("从数据库删除失败: ${result.error}")
                }
                is ModernResult.Loading -> {
                    // 不应该发生
                    logger.w("DatabaseStorage", "删除操作返回Loading状态: $id")
                }
            }
        } catch (e: Exception) {
            logger.e(e, "从数据库删除异常: $id")
            throw e
        }
    }

    override suspend fun exists(id: String): Boolean {
        return try {
            logger.d("DatabaseStorage", "检查数据库存在性: $id")

            when (val result = existsOperation(id)) {
                is ModernResult.Success -> {
                    logger.d("DatabaseStorage", "数据库存在性检查完成: $id = ${result.data}")
                    result.data
                }
                is ModernResult.Error -> {
                    logger.e("DatabaseStorage", "数据库存在性检查失败: $id, error: ${result.error}")
                    false
                }
                is ModernResult.Loading -> {
                    // 不应该发生
                    logger.w("DatabaseStorage", "存在性检查返回Loading状态: $id")
                    false
                }
            }
        } catch (e: Exception) {
            logger.e(e, "检查数据库存在性异常: $id")
            false
        }
    }

    override suspend fun getSize(id: String): Long {
        // 数据库存储的大小计算比较复杂，这里返回0
        // 实际项目中可以根据需要实现具体的大小计算逻辑
        return 0L
    }

    companion object {
        /**
         * 创建DatabaseStorage实例
         */
        fun <T : Any> create(
            saveOperation: suspend (T) -> ModernResult<Unit>,
            loadOperation: suspend (String) -> ModernResult<T?>,
            deleteOperation: suspend (String) -> ModernResult<Unit>,
            existsOperation: suspend (String) -> ModernResult<Boolean>,
            logger: Logger,
        ): DatabaseStorage<T> {
            return DatabaseStorage(
                saveOperation = saveOperation,
                loadOperation = loadOperation,
                deleteOperation = deleteOperation,
                existsOperation = existsOperation,
                logger = logger,
            )
        }

        /**
         * 创建简化的DatabaseStorage实例（只需要保存和加载操作）
         */
        fun <T : Any> createSimple(
            saveOperation: suspend (T) -> ModernResult<Unit>,
            loadOperation: suspend (String) -> ModernResult<T?>,
            logger: Logger,
        ): DatabaseStorage<T> {
            return DatabaseStorage(
                saveOperation = saveOperation,
                loadOperation = loadOperation,
                deleteOperation = { _ -> ModernResult.Success(Unit) },
                existsOperation = { id ->
                    // 通过尝试加载来检查存在性
                    when (val result = loadOperation(id)) {
                        is ModernResult.Success -> ModernResult.Success(result.data != null)
                        is ModernResult.Error -> ModernResult.Success(false)
                        is ModernResult.Loading -> ModernResult.Success(false)
                    }
                },
                logger = logger,
            )
        }
    }
}
