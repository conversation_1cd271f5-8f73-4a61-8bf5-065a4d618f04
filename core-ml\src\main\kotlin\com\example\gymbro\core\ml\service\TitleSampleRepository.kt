package com.example.gymbro.core.ml.service

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 标题样本库检索服务
 *
 * 基于BGE向量的语义检索，从预定义的高质量标题样本库中
 * 找到与首条消息语义最相似的标题模板
 */
@Singleton
class TitleSampleRepository @Inject constructor(
    private val bgeEmbeddingService: BgeEmbeddingService,
) {

    // 预定义的高质量标题样本库（生产环境从资产文件或数据库加载）
    private val titleSamples = listOf(
        TitleSample("如何开始健身训练", "健身 训练 开始 新手 指导"),
        TitleSample("制定训练计划", "训练计划 制定 安排 方案"),
        TitleSample("减脂饮食指导", "减脂 饮食 营养 减肥 指导"),
        TitleSample("增肌训练方案", "增肌 肌肉 训练 方案 计划"),
        TitleSample("运动伤病预防", "伤病 预防 安全 运动 保护"),
        TitleSample("器械使用指导", "器械 使用 指导 教学 技巧"),
        TitleSample("有氧运动建议", "有氧 跑步 运动 建议"),
        TitleSample("力量训练技巧", "力量 训练 技巧 重量 方法"),
        TitleSample("拉伸康复指导", "拉伸 康复 恢复 柔韧性"),
        TitleSample("营养补剂咨询", "营养 补剂 蛋白粉 维生素"),
    )

    private var samplesEmbeddings: List<Pair<TitleSample, FloatArray>>? = null

    /**
     * 初始化样本库向量（懒加载）
     */
    private suspend fun ensureSamplesEmbedded(): List<Pair<TitleSample, FloatArray>> {
        if (samplesEmbeddings == null) {
            Timber.d("TitleSampleRepository: 初始化样本库向量化")
            val embeddings = mutableListOf<Pair<TitleSample, FloatArray>>()

            for (sample in titleSamples) {
                val embeddingResult = bgeEmbeddingService.embedText(sample.keywords)
                when (embeddingResult) {
                    is ModernResult.Success -> {
                        embeddings.add(sample to embeddingResult.data)
                    }
                    is ModernResult.Error -> {
                        Timber.w("TitleSampleRepository: 样本向量化失败: ${sample.title}")
                        // 继续处理其他样本
                    }
                    is ModernResult.Loading -> {
                        // Loading状态不应该在这里出现，因为embedText是suspend函数
                        Timber.w("TitleSampleRepository: 意外的Loading状态: ${sample.title}")
                    }
                }
            }

            samplesEmbeddings = embeddings
            Timber.d("TitleSampleRepository: 样本库初始化完成，共${embeddings.size}个样本")
        }

        return samplesEmbeddings!!
    }

    /**
     * 语义检索最相似的标题
     *
     * @param messageText 首条消息文本
     * @param threshold 相似度阈值，默认0.85
     * @return 如果找到相似度>=threshold的标题则返回，否则返回null
     */
    suspend fun findSimilarTitle(
        messageText: String,
        threshold: Float = 0.85f,
    ): ModernResult<String?> = withContext(Dispatchers.Default) {
        try {
            if (messageText.isBlank()) {
                return@withContext ModernResult.Success(null)
            }

            // 1. 向量化输入消息
            val messageEmbeddingResult = bgeEmbeddingService.embedText(messageText)
            if (messageEmbeddingResult is ModernResult.Error) {
                return@withContext messageEmbeddingResult.copy()
            }
            val messageEmbedding = (messageEmbeddingResult as ModernResult.Success).data

            // 2. 确保样本库已向量化
            val samples = ensureSamplesEmbedded()
            if (samples.isEmpty()) {
                Timber.w("TitleSampleRepository: 样本库为空")
                return@withContext ModernResult.Success(null)
            }

            // 3. 计算相似度并找到最佳匹配
            var bestSimilarity = 0f
            var bestTitle: String? = null

            for ((sample, sampleEmbedding) in samples) {
                val similarity = cosineSimilarity(messageEmbedding, sampleEmbedding)
                if (similarity > bestSimilarity) {
                    bestSimilarity = similarity
                    bestTitle = sample.title
                }
            }

            // 4. 检查是否达到阈值
            val result = if (bestSimilarity >= threshold) {
                Timber.d("TitleSampleRepository: 找到匹配标题: '$bestTitle' (相似度: $bestSimilarity)")
                bestTitle
            } else {
                Timber.d("TitleSampleRepository: 未找到匹配标题 (最高相似度: $bestSimilarity < $threshold)")
                null
            }

            ModernResult.Success(result)
        } catch (e: Exception) {
            Timber.e(e, "TitleSampleRepository: 标题检索失败")
            ModernResult.Error(
                com.example.gymbro.core.error.types.features.FeatureErrors.CoachError.processingFailed(
                    operationName = "TitleSampleRepository.findSimilarTitle",
                    message = UiText.DynamicString("标题语义检索失败"),
                    processType = "title_semantic_search",
                    reason = "semantic_search_failed",
                    cause = e,
                ),
            )
        }
    }

    /**
     * 计算余弦相似度
     */
    private fun cosineSimilarity(a: FloatArray, b: FloatArray): Float {
        require(a.size == b.size) { "向量维度不匹配: ${a.size} vs ${b.size}" }

        var dotProduct = 0f
        var normA = 0f
        var normB = 0f

        for (i in a.indices) {
            dotProduct += a[i] * b[i]
            normA += a[i] * a[i]
            normB += b[i] * b[i]
        }

        return if (normA == 0f || normB == 0f) 0f else dotProduct / (kotlin.math.sqrt(normA) * kotlin.math.sqrt(normB))
    }

    /**
     * 标题样本数据类
     */
    private data class TitleSample(
        val title: String, // 标题文本
        val keywords: String, // 关键词（用于向量化）
    )
}
