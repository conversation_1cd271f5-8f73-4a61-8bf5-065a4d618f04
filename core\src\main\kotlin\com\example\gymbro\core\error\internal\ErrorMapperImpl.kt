package com.example.gymbro.core.error.internal

import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.utils.ErrorMapper // Utils import might need adjustment
import com.example.gymbro.core.ui.text.UiText
import retrofit2.HttpException
import timber.log.Timber
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * ErrorMapper接口的实现类，负责将不同类型的异常映射为ModernDataError
 * 提供了对网络、IO、验证、业务和其他异常的处理
 */
@Singleton
class ErrorMapperImpl
@Inject
constructor() : ErrorMapper {
    /**
     * 将异常映射为ModernDataError
     *
     * @param exception 要映射的异常
     * @param errorMessage 可选的错误消息
     * @return 映射后的ModernDataError
     */
    override fun mapExceptionToError(
        exception: Throwable,
        errorMessage: UiText?,
    ): ModernDataError {
        Timber.d("映射异常: %s", exception.javaClass.simpleName)

        // 如果异常已经是ModernDataError，直接返回
        if (exception is ModernDataError) {
            return exception
        }

        // 根据异常类型进行映射
        return when (exception) {
            // 网络连接相关异常
            is SocketTimeoutException -> mapNetworkTimeoutError(exception, errorMessage)
            is UnknownHostException -> mapConnectionError(exception, errorMessage)
            is IOException -> mapNetworkIOError(exception, errorMessage)
            is HttpException -> mapHttpError(exception, errorMessage)

            // 其他异常视为未知异常
            else -> mapUnknownError(exception, errorMessage)
        }
    }

    /**
     * 创建网络超时错误
     */
    private fun mapNetworkTimeoutError(
        exception: SocketTimeoutException,
        defaultUiMessage: UiText?,
    ): ModernDataError {
        val message = defaultUiMessage ?: UiText.DynamicString("网络连接超时，请检查网络设置")
        return ModernDataError(
            operationName = "network_request",
            errorType = GlobalErrorType.Network.Timeout,
            uiMessage = message,
            metadataMap =
            mapOf(
                "exception_type" to "timeout",
                "exception_message" to (exception.message ?: ""),
            ),
        )
    }

    /**
     * 创建网络连接错误
     */
    private fun mapConnectionError(
        exception: UnknownHostException,
        defaultUiMessage: UiText?,
    ): ModernDataError {
        val message = defaultUiMessage ?: UiText.DynamicString("无法连接到服务器，请检查网络设置")
        return ModernDataError(
            operationName = "network_connection",
            errorType = GlobalErrorType.Network.Connection,
            uiMessage = message,
            metadataMap =
            mapOf(
                "exception_type" to "connection",
                "host" to (exception.message ?: "unknown"),
                "exception_message" to (exception.message ?: ""),
            ),
        )
    }

    /**
     * 创建网络IO错误
     */
    private fun mapNetworkIOError(
        exception: IOException,
        defaultUiMessage: UiText?,
    ): ModernDataError {
        val message = defaultUiMessage ?: UiText.DynamicString("网络通信错误，请稍后重试")
        return ModernDataError(
            operationName = "network_io",
            errorType = GlobalErrorType.Network.Connection,
            uiMessage = message,
            metadataMap =
            mapOf(
                "exception_type" to "io",
                "exception_message" to (exception.message ?: ""),
            ),
        )
    }

    /**
     * 创建HTTP错误
     */
    private fun mapHttpError(
        exception: HttpException,
        defaultUiMessage: UiText?,
    ): ModernDataError {
        val statusCode = exception.code()
        val message = defaultUiMessage ?: getHttpErrorMessage(statusCode)

        val errorType =
            when (statusCode) {
                401, 403 -> GlobalErrorType.Auth.Unauthorized
                404 -> GlobalErrorType.Data.NotFound
                in 400..499 -> GlobalErrorType.Network.Connection // 客户端错误，使用Connection替代Client
                in 500..599 -> GlobalErrorType.Network.Connection // 服务器错误，使用Connection替代Server
                else -> GlobalErrorType.Unknown // 未知网络错误
            }

        return ModernDataError(
            operationName = "http_request",
            errorType = errorType,
            uiMessage = message,
            metadataMap =
            mapOf(
                "exception_type" to "http",
                "status_code" to statusCode,
                "error_response" to (exception.response()?.errorBody()?.string() ?: ""),
                "error_category" to (if (statusCode in 400..499) "client" else if (statusCode in 500..599) "server" else "unknown"),
            ),
        )
    }

    /**
     * 创建未知错误
     */
    private fun mapUnknownError(
        exception: Throwable,
        defaultUiMessage: UiText?,
    ): ModernDataError {
        val message = defaultUiMessage ?: UiText.DynamicString("发生未知错误，请稍后重试")
        return ModernDataError(
            operationName = "unknown_operation",
            errorType = GlobalErrorType.Unknown,
            uiMessage = message,
            metadataMap =
            mapOf(
                "exception_type" to exception.javaClass.simpleName,
                "exception_message" to (exception.message ?: ""),
            ),
        )
    }

    /**
     * 根据HTTP状态码获取错误消息
     */
    private fun getHttpErrorMessage(statusCode: Int): UiText =
        when (statusCode) {
            401 -> UiText.DynamicString("请先登录后再操作")
            403 -> UiText.DynamicString("您没有权限执行此操作")
            404 -> UiText.DynamicString("请求的资源不存在")
            in 400..499 -> UiText.DynamicString("请求参数有误，请稍后重试")
            in 500..599 -> UiText.DynamicString("服务器错误，请稍后重试")
            else -> UiText.DynamicString("网络错误，请稍后重试")
        }

    /**
     * 创建一个未知错误
     */
    override fun createUnknownError(
        message: UiText?,
        operationName: String,
    ): ModernDataError =
        ModernDataError(
            operationName = operationName,
            errorType = GlobalErrorType.Unknown,
            uiMessage = message ?: UiText.DynamicString("未知错误"),
            metadataMap = mapOf("source" to "ErrorMapperImpl"),
        )
}
