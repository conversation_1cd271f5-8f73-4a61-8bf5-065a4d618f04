package com.example.gymbro.core.error.utils

import com.example.gymbro.core.error.recovery.RecoveryStrategy
import com.example.gymbro.core.error.recovery.getRecoveryStrategy
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ErrorCategory
import com.example.gymbro.core.error.types.ErrorSeverity
import com.example.gymbro.core.error.types.ModernDataError
// SystemError import is removed
import timber.log.Timber

/**
 * 错误聚合器
 *
 * 提供错误聚合功能，将多个错误合并为一个
 */
object ErrorAggregator {
    /**
     * 将多个错误聚合为一个
     *
     * @param errors 要聚合的错误列表
     * @param message 聚合错误的消息
     * @param keepIndividualErrors 是否在元数据中保留单个错误
     * @param type 错误类型
     * @return 聚合后的错误
     */
    fun aggregate(
        errors: List<ModernDataError>,
        message: UiText? = null,
        keepIndividualErrors: Boolean = true,
        type: GlobalErrorType = determineCommonType(errors),
    ): ModernDataError {
        if (errors.isEmpty()) {
            // Replaced SystemError.internalError with a direct ModernDataError construction
            return ModernDataError(
                operationName = "ErrorAggregator.aggregate.NoErrors",
                errorType = GlobalErrorType.System.Internal,
                category = ErrorCategory.SYSTEM,
                uiMessage = UiText.DynamicString("No errors to aggregate"),
                severity = ErrorSeverity.WARNING,
            )
        }

        if (errors.size == 1) {
            return errors.first()
        }

        // 确定严重性级别
        val severity = determineSeverity(errors)

        // 创建元数据
        val metadataMap = mutableMapOf<String, Any>()
        if (keepIndividualErrors) {
            metadataMap["aggregated_errors"] = errors
        }
        metadataMap["error_count"] = errors.size

        // 创建聚合错误消息
        val errorMessage = message ?: createAggregateMessage(errors)

        return ModernDataError(
            operationName = "ErrorAggregator.aggregate",
            errorType = type,
            category = ErrorCategory.SYSTEM, // 使用SYSTEM替代不存在的AGGREGATED
            uiMessage = errorMessage,
            severity = severity,
            recoverable = anyRecoverable(errors),
            metadataMap = metadataMap,
        )
    }

    /**
     * 确定错误的共同类型
     *
     * @param errors 错误列表
     * @return 共同错误类型
     */
    private fun determineCommonType(errors: List<ModernDataError>): GlobalErrorType {
        if (errors.isEmpty()) return GlobalErrorType.Unknown

        // 尝试找到相同的错误类型
        val commonType =
            errors
                .groupBy { it.errorType }
                .maxByOrNull { it.value.size }
                ?.key

        return commonType ?: GlobalErrorType.Unknown
    }

    /**
     * 确定聚合错误的严重性
     *
     * @param errors 错误列表
     * @return 聚合错误的严重性
     */
    private fun determineSeverity(errors: List<ModernDataError>): ErrorSeverity {
        if (errors.isEmpty()) return ErrorSeverity.ERROR // Default to ERROR if list is empty, though aggregate checks for this

        // 如果有任何严重错误，整体就是严重错误
        if (errors.any { it.severity == ErrorSeverity.CRITICAL }) {
            return ErrorSeverity.CRITICAL
        }

        // 如果有任何错误，整体就是错误
        if (errors.any { it.severity == ErrorSeverity.ERROR }) {
            return ErrorSeverity.ERROR
        }

        // 如果有任何警告，整体就是警告
        if (errors.any { it.severity == ErrorSeverity.WARNING }) {
            return ErrorSeverity.WARNING
        }

        // 默认为INFO
        return ErrorSeverity.INFO
    }

    /**
     * 检查是否有任何错误可恢复
     *
     * @param errors 错误列表
     * @return 是否有可恢复的错误
     */
    private fun anyRecoverable(errors: List<ModernDataError>): Boolean {
        if (errors.isEmpty()) return false
        return errors.any { it.recoverable || it.getRecoveryStrategy<Any>() != null }
    }

    /**
     * 创建聚合错误消息
     *
     * @param errors 错误列表
     * @return 聚合错误消息
     */
    private fun createAggregateMessage(errors: List<ModernDataError>): UiText {
        if (errors.isEmpty()) {
            return UiText.DynamicString("No errors")
        }

        if (errors.size == 1) {
            return errors.first().uiMessage ?: UiText.DynamicString("Error occurred")
        }

        val messageBuilder = StringBuilder("Multiple errors occurred (${errors.size}):")

        // 添加前几个错误的简短描述
        val maxErrors = 3
        errors.take(maxErrors).forEach { error ->
            // Ensure error.message is accessible and correct; assuming uiMessage.string() or similar if needed
            // For now, using a placeholder if direct error.message is problematic.
            val errorDescription = error.uiMessage?.toString() ?: "Error description unavailable"
            messageBuilder.append("\n- $errorDescription")
        }

        // 如果有更多错误，添加省略符
        if (errors.size > maxErrors) {
            messageBuilder.append("\n... and ${errors.size - maxErrors} more")
        }

        return UiText.DynamicString(messageBuilder.toString())
    }

    /**
     * 合并多个恢复策略为一个
     *
     * @param strategies 要合并的恢复策略
     * @return 合并后的恢复策略
     */
    fun <T> combineRecoveryStrategies(strategies: List<RecoveryStrategy<T>>): RecoveryStrategy<T>? {
        if (strategies.isEmpty()) return null
        if (strategies.size == 1) return strategies.first()

        return object : RecoveryStrategy<T> {
            override suspend fun execute(): T? {
                for (strategy in strategies) {
                    try {
                        val result = strategy.execute()
                        if (result != null) {
                            return result
                        }
                    } catch (e: Exception) {
                        Timber.e(e, "恢复策略执行失败: ${e.message}")
                    }
                }
                return null
            }
        }
    }
}
