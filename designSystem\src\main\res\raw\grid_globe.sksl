// RisingGlobe.sksl - 地平线升起的网格行星
// 只显示球面上半部分，底部占屏2/3，顶部带亮环
uniform float2  iResolution;
uniform float   u_time;
uniform float   u_speed;
uniform float3  u_color;
uniform float   u_alpha;

const float PI = 3.14159265;

// 球面光线相交
float2 sphereHit(float3 ro, float3 rd) {
    float b = dot(ro, rd);
    float c = dot(ro, ro) - 1.0;
    float h = b*b - c;
    if (h < 0.0) return float2(-1.0);
    h = sqrt(h);
    return float2(-b - h, -b + h);
}

// 网格生成（调整粗细适配半球压缩）
float grid(float2 uv, float div) {
    float2 g = abs(fract(uv * div) - 0.5);
    return 1.0 - smoothstep(0.0, 0.012, min(g.x, g.y));
}

// 主函数
half4 main(float2 fragCoord) {
    // 归一化坐标，保持正确长宽比
    float2 p = (fragCoord * 2.0 - iResolution) / iResolution.y;

    // 相机推近，让球更"鼓"
    float3 ro = float3(0.0, 0.0, 1.4);
    float3 rd = normalize(float3(p, -1.4));

    // 球心向上偏移，让下半部分在地平线下，只露出上半球
    float3 centerShift = float3(0.0, 0.7, 0.0); // 正值：球心向上移
    float2 hit = sphereHit(ro - centerShift, rd);
    if (hit.x < 0.0) return half4(0.0);

    float3 pos = ro + rd * hit.x - centerShift;
    float3 n   = normalize(pos);

    // 经纬度映射 + 自转
    float lon = atan(pos.z, pos.x) + u_time * u_speed;
    float lat = acos(clamp(pos.y, -1.0, 1.0));
    float2 uv = float2(lon / (2.0 * PI) + 0.5, lat / PI);

    // 双层网格系统
    float g = max(grid(uv, 12.0), 0.5 * grid(uv, 6.0));

    // 边缘亮环（Glare）- 比普通菲涅耳更尖锐
    float rim = pow(1.0 - dot(n, -rd), 4.0);
    rim = smoothstep(0.05, 0.15, rim);   // 只保留最外10%

    // 球面明暗渐变
    float shade = dot(n, float3(0,0,1))*0.25 + 0.75;

    // 合成亮度（增强网格和亮环）
    float lum = (g * 1.4 + rim * 2.0) * shade;

    // Gamma校正
    float3 col = pow(u_color * lum, float3(1.0/2.2));

    // α通道供Bloom使用
    return half4(col, lum * u_alpha);
}
