package com.example.gymbro.data.remote.firebase.auth

import com.example.gymbro.core.error.types.ModernResult
import com.google.firebase.auth.AuthCredential
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.auth.PhoneAuthCredential
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Firebase身份验证数据源实现，作为认证服务的门面，委托给专门的服务组件
 * 保持向后兼容性，确保现有API不变
 */
@Singleton
class FirebaseAuthDataSourceImpl @Inject constructor(
    private val authService: FirebaseAuthService,
    private val phoneVerificationService: PhoneVerificationService,
    private val accountLinkingService: AccountLinkingService,
    private val userBackupService: UserBackupService,
    private val authStateService: AuthStateService,
) : AuthDataSource {

    // === 基本的认证状态检查 ===
    override fun isUserLoggedInSynchronously(): Boolean = authService.isUserLoggedInSynchronously()

    override fun isAnonymousUserSynchronously(): Boolean = authService.isAnonymousUserSynchronously()

    // === 获取用户相关方法 ===
    override suspend fun getCurrentUserId(): String? = authService.getCurrentUserId()

    override suspend fun getCurrentUser(): ModernResult<FirebaseUser?> = authService.getCurrentUser()

    override suspend fun isAnonymousUser(): Boolean = authService.isAnonymousUser()

    // === 登录方法 ===
    override suspend fun loginWithEmail(email: String, password: String): ModernResult<String> =
        authService.loginWithEmail(email, password)

    override suspend fun loginWithPhone(credential: PhoneAuthCredential): ModernResult<String> =
        authService.loginWithPhone(credential)

    override suspend fun registerWithEmail(email: String, password: String): ModernResult<String> =
        authService.registerWithEmail(email, password)

    override suspend fun registerWithPhone(phone: String, code: String): ModernResult<String> =
        phoneVerificationService.registerWithPhone(phone, code, authService)

    override suspend fun loginWithGoogle(credential: AuthCredential): ModernResult<String> =
        authService.loginWithGoogle(credential)

    override suspend fun loginWithWeChat(code: String): ModernResult<String> =
        authService.loginWithWeChat(code)

    override suspend fun loginAnonymously(): ModernResult<String> =
        authService.loginAnonymously()

    override suspend fun logout(): ModernResult<Unit> = authService.logout()

    // === 账户关联 ===
    override suspend fun linkAccountWithCredential(credential: AuthCredential): ModernResult<Unit> =
        accountLinkingService.linkAccountWithCredential(credential)

    override suspend fun linkPhoneAccount(phoneNumber: String, code: String): ModernResult<Unit> =
        accountLinkingService.linkPhoneAccount(phoneNumber, code)

    override suspend fun linkEmailAccount(email: String, password: String): ModernResult<Unit> =
        accountLinkingService.linkEmailAccount(email, password)

    override suspend fun updatePhoneNumber(credential: PhoneAuthCredential): ModernResult<Unit> =
        accountLinkingService.updatePhoneNumber(credential)

    override suspend fun updateEmail(email: String): ModernResult<Unit> =
        accountLinkingService.updateEmail(email)

    override suspend fun isAccountLinked(providerId: String): Boolean =
        accountLinkingService.isAccountLinked(providerId)

    override suspend fun getLinkedAccounts(): List<String> =
        accountLinkingService.getLinkedAccounts()

    // === 身份认证 ===
    override suspend fun reAuthenticate(credential: AuthCredential): ModernResult<Unit> =
        authService.reAuthenticate(credential)

    // === 电话验证 ===
    override suspend fun verifyPhoneNumber(
        phoneNumber: String,
        context: PhoneVerificationContext,
        callback: PhoneVerificationCallback,
        forceResendingToken: Any?,
    ): ModernResult<Unit> = phoneVerificationService.verifyPhoneNumber(
        phoneNumber,
        context,
        callback,
        forceResendingToken,
    )

    override suspend fun sendVerificationCode(
        phoneNumber: String,
        context: PhoneVerificationContext,
        callback: PhoneVerificationCallback,
    ): ModernResult<Unit> = phoneVerificationService.sendVerificationCode(
        phoneNumber,
        context,
        callback,
    )

    override suspend fun resendVerificationCode(
        phoneNumber: String,
        context: PhoneVerificationContext,
        callback: PhoneVerificationCallback,
        resendToken: Any,
    ): ModernResult<Unit> = phoneVerificationService.resendVerificationCode(
        phoneNumber,
        context,
        callback,
        resendToken,
    )

    override fun createPhoneCredential(verificationId: String, code: String): PhoneAuthCredential =
        phoneVerificationService.createPhoneCredential(verificationId, code)

    override suspend fun verifyPhoneCode(verificationId: String, code: String): ModernResult<PhoneAuthCredential> =
        phoneVerificationService.verifyPhoneCode(verificationId, code)

    // === 认证状态监听 - 简化版本 ===
    override fun getAuthStateFlow(): Flow<Boolean> = authStateService.getAuthStateFlow()

    override fun getAuthState(): Flow<Boolean> = authStateService.getAuthState()

    override fun observeAuthState(): Flow<Boolean> = authStateService.observeAuthState()

    // === 匿名用户升级 ===
    override suspend fun upgradeAnonymousWithCredential(credential: AuthCredential): ModernResult<Unit> =
        accountLinkingService.upgradeAnonymousWithCredential(credential)

    override suspend fun upgradeAnonymousWithEmail(email: String, password: String): ModernResult<Unit> =
        accountLinkingService.upgradeAnonymousWithEmail(email, password)

    override suspend fun upgradeAnonymousWithGoogle(credential: AuthCredential): ModernResult<Unit> =
        accountLinkingService.upgradeAnonymousWithGoogle(credential)

    override suspend fun upgradeAnonymousWithPhone(credential: PhoneAuthCredential): ModernResult<Unit> =
        accountLinkingService.upgradeAnonymousWithPhone(credential)

    // === 电子邮件验证 ===
    override suspend fun sendEmailVerificationCode(email: String?): ModernResult<Unit> =
        userBackupService.sendEmailVerificationCode(email)

    override suspend fun verifyEmailCode(code: String): ModernResult<Boolean> =
        userBackupService.verifyEmailCode(code)

    override suspend fun sendEmailVerification(): ModernResult<Unit> =
        userBackupService.sendEmailVerification()

    override suspend fun verifyBeforeUpdateEmail(email: String): ModernResult<Unit> =
        userBackupService.verifyBeforeUpdateEmail(email)

    // === 密码重置 ===
    override suspend fun resetPassword(email: String): ModernResult<Unit> =
        userBackupService.resetPassword(email)

    override suspend fun updatePassword(newPassword: String): ModernResult<Unit> =
        accountLinkingService.updatePassword(newPassword)

    // === 账户管理 ===
    override suspend fun deleteAccount(): ModernResult<Unit> =
        accountLinkingService.deleteAccount()

    override suspend fun getIdToken(forceRefresh: Boolean): ModernResult<String> =
        authService.getIdToken(forceRefresh)

    override suspend fun refreshUserToken(): ModernResult<String> =
        authService.refreshUserToken()

    // === 用户设置备份与恢复 ===
    override suspend fun getBackupSettings(): ModernResult<Map<String, Any>?> =
        userBackupService.getBackupSettings()

    override suspend fun updateBackupSettings(settings: Map<String, Any>): ModernResult<Unit> =
        userBackupService.updateBackupSettings(settings)
}

/**
 * 手机验证事件的密封类
 */
sealed class PhoneAuthEvent
