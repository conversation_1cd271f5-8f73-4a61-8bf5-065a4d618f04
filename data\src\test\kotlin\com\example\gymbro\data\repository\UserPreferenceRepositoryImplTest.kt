package com.example.gymbro.data.repository

import com.example.gymbro.data.local.datastore.PreferencesDataStore
import com.example.gymbro.data.remote.firebase.auth.UserBackupService
import com.example.gymbro.data.repository.user.UserPreferenceRepositoryImpl
import com.example.gymbro.domain.profile.model.user.FitnessGoal
import com.example.gymbro.domain.profile.model.user.FitnessPreference
import com.example.gymbro.domain.profile.model.user.WeekDay
import com.example.gymbro.domain.profile.usecase.GetUserProfileUseCase
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

/**
 * UserPreferenceRepositoryImpl 单元测试
 * 验证DataStore真实现的功能和性能
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class UserPreferenceRepositoryImplTest {
    private lateinit var repository: UserPreferenceRepositoryImpl
    private lateinit var preferencesDataStore: PreferencesDataStore
    private lateinit var getUserProfileUseCase: GetUserProfileUseCase
    private lateinit var userBackupService: UserBackupService

    @BeforeEach
    fun setUp() {
        preferencesDataStore = mockk()
        getUserProfileUseCase = mockk()
        userBackupService = mockk()

        repository =
            UserPreferenceRepositoryImpl(
                getUserProfileUseCase = getUserProfileUseCase,
                preferencesDataStore = preferencesDataStore,
                userBackupService = userBackupService,
            )
    }

    @Test
    fun `current() should return DataStore preference when available`() =
        runTest {
            // Given - DataStore有健身偏好
            val expectedPreference =
                FitnessPreference(
                    primaryGoal = FitnessGoal.MUSCLE_GAIN,
                    workoutDays = setOf(WeekDay.MONDAY, WeekDay.WEDNESDAY, WeekDay.FRIDAY),
                )

            coEvery { preferencesDataStore.getFitnessPreferenceFlow() } returns flowOf(expectedPreference)
            coEvery { userBackupService.getRemoteBackupFlow() } returns flowOf(null)

            // When
            val result = repository.current()

            // Then
            assertEquals(expectedPreference, result)
            assertEquals(FitnessGoal.MUSCLE_GAIN, result.primaryGoal)
            assertEquals(3, result.workoutDays.size)
            assertTrue(result.hasAnyPreference())
        }

    @Test
    fun `current() should return default preference when DataStore fails`() =
        runTest {
            // Given - DataStore抛出异常
            coEvery { preferencesDataStore.getFitnessPreferenceFlow() } throws RuntimeException("DataStore error")
            coEvery { userBackupService.getRemoteBackupFlow() } returns flowOf(null)

            // When
            val result = repository.current()

            // Then
            assertEquals(FitnessPreference(), result)
            assertNull(result.primaryGoal)
            assertTrue(result.workoutDays.isEmpty())
            assertFalse(result.hasAnyPreference())
        }

    @Test
    fun `updateFitnessPreference() should call DataStore update`() =
        runTest {
            // Given
            val preference =
                FitnessPreference(
                    primaryGoal = FitnessGoal.WEIGHT_LOSS,
                    workoutDays = setOf(WeekDay.TUESDAY, WeekDay.THURSDAY),
                )

            coEvery { preferencesDataStore.updateFitnessPreference(any()) } returns Unit

            // When
            repository.updateFitnessPreference(preference)

            // Then
            coVerify(exactly = 1) { preferencesDataStore.updateFitnessPreference(preference) }
        }

    @Test
    fun `updateFitnessGoal() should update only goal keeping workout days`() =
        runTest {
            // Given - 当前有训练日设置
            val currentPreference =
                FitnessPreference(
                    primaryGoal = FitnessGoal.MUSCLE_GAIN,
                    workoutDays = setOf(WeekDay.MONDAY, WeekDay.FRIDAY),
                )
            val newGoal = FitnessGoal.ENDURANCE

            coEvery { preferencesDataStore.getFitnessPreferenceFlow() } returns flowOf(currentPreference)
            coEvery { userBackupService.getRemoteBackupFlow() } returns flowOf(null)
            coEvery { preferencesDataStore.updateFitnessPreference(any()) } returns Unit

            // When
            repository.updateFitnessGoal(newGoal)

            // Then
            coVerify {
                preferencesDataStore.updateFitnessPreference(
                    match {
                        it.primaryGoal == newGoal &&
                            it.workoutDays == currentPreference.workoutDays
                    },
                )
            }
        }

    @Test
    fun `updateWorkoutDays() should update only workout days keeping goal`() =
        runTest {
            // Given - 当前有健身目标设置
            val currentPreference =
                FitnessPreference(
                    primaryGoal = FitnessGoal.STRENGTH,
                    workoutDays = setOf(WeekDay.MONDAY),
                )
            val newWorkoutDays = setOf(WeekDay.TUESDAY, WeekDay.THURSDAY, WeekDay.SATURDAY)

            coEvery { preferencesDataStore.getFitnessPreferenceFlow() } returns flowOf(currentPreference)
            coEvery { userBackupService.getRemoteBackupFlow() } returns flowOf(null)
            coEvery { preferencesDataStore.updateFitnessPreference(any()) } returns Unit

            // When
            repository.updateWorkoutDays(newWorkoutDays)

            // Then
            coVerify {
                preferencesDataStore.updateFitnessPreference(
                    match {
                        it.primaryGoal == currentPreference.primaryGoal &&
                            it.workoutDays == newWorkoutDays
                    },
                )
            }
        }

    @Test
    fun `preferenceFlow should emit distinctUntilChanged values`() =
        runTest {
            // Given - DataStore发出相同值
            val preference = FitnessPreference(primaryGoal = FitnessGoal.GENERAL_FITNESS)

            coEvery { preferencesDataStore.getFitnessPreferenceFlow() } returns flowOf(preference, preference, preference)
            coEvery { userBackupService.getRemoteBackupFlow() } returns flowOf(null)

            // When
            val result = repository.preferenceFlow.first()

            // Then - 应该只收到一个值（distinctUntilChanged生效）
            assertEquals(preference, result)
        }

    @Test
    fun `preferenceFlow should prioritize DataStore over remote backup`() =
        runTest {
            // Given - DataStore和远程备份都有数据
            val dataStorePreference = FitnessPreference(primaryGoal = FitnessGoal.MUSCLE_GAIN)
            val remoteBackup = mapOf("goal" to "weight_loss")

            coEvery { preferencesDataStore.getFitnessPreferenceFlow() } returns flowOf(dataStorePreference)
            coEvery { userBackupService.getRemoteBackupFlow() } returns flowOf(remoteBackup)

            // When
            val result = repository.preferenceFlow.first()

            // Then - 应该使用DataStore的数据
            assertEquals(FitnessGoal.MUSCLE_GAIN, result.primaryGoal)
        }
}
