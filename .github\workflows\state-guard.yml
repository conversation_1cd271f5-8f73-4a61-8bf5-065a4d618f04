name: State Machine Guard - Fail Fast Protection

on:
  pull_request:
    types: [opened, synchronize]
  push:
    branches: [ main, develop ]

env:
  FAIL_FAST: true

jobs:
  detect_state_machine_changes:
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
    - name: Checkout PR branch
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: 🔍 检测状态机变更 - Fail Fast
      id: detect_changes
      run: |
        echo "=== 检测状态机相关变更 ==="

        # 检测状态机核心文件变更
        CHANGED_FILES=$(git diff --name-only origin/${{ github.base_ref || 'main' }}...HEAD)
        echo "变更文件列表："
        echo "$CHANGED_FILES"

        # 检测状态机关键代码模式
        STATE_PATTERNS=(
          "class.*State"
          "enum.*Status"
          "sealed.*State"
          "data class.*UiState"
          "StateFlow"
          "MutableStateFlow"
          "updateLastAiMessage"
          "ensureThinkingMessageExists"
          "transitionToState"
          "MessageStatus\."
          "AiMessageState"
        )

        echo "=== 扫描状态机代码变更 ==="
        STATE_CHANGE_DETECTED=false

        for pattern in "${STATE_PATTERNS[@]}"; do
          if git diff origin/${{ github.base_ref || 'main' }}...HEAD | grep -E "$pattern"; then
            echo "❌ 检测到状态机变更模式: $pattern"
            STATE_CHANGE_DETECTED=true
          fi
        done

        # 检测文件路径模式
        FILE_PATTERNS=(
          ".*ViewModel\.kt$"
          ".*State\.kt$"
          ".*UiState\.kt$"
          ".*/state/.*\.kt$"
          ".*/repository/aicoach/.*\.kt$"
          ".*/retry/.*\.kt$"
        )

        echo "=== 扫描状态机文件变更 ==="
        for pattern in "${FILE_PATTERNS[@]}"; do
          if echo "$CHANGED_FILES" | grep -E "$pattern"; then
            echo "❌ 检测到状态机文件变更: $pattern"
            STATE_CHANGE_DETECTED=true
          fi
        done

        echo "STATE_CHANGE_DETECTED=$STATE_CHANGE_DETECTED" >> $GITHUB_OUTPUT

    - name: 🚨 状态机变更检测 - 强制验证
      if: steps.detect_changes.outputs.STATE_CHANGE_DETECTED == 'true'
      run: |
        echo "🚨 检测到状态机相关变更！"
        echo "根据状态机铁律，必须完成以下验证："
        echo ""
        echo "📋 必须完成的检查项："
        echo "1. ✅ Mermaid状态图已更新"
        echo "2. ✅ 状态转换文档已同步"
        echo "3. ✅ Chaos测试已通过"
        echo "4. ✅ 状态一致性测试已覆盖"
        echo "5. ✅ 监控指标已验证"
        echo ""
        echo "⚠️ 如果未完成上述检查项，CI将强制失败！"

  validate_state_machine_artifacts:
    runs-on: ubuntu-latest
    needs: detect_state_machine_changes
    if: needs.detect_state_machine_changes.outputs.STATE_CHANGE_DETECTED == 'true'

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: 📊 验证Mermaid状态图存在
      run: |
        echo "=== 验证状态机文档完整性 ==="

        # 检查状态机文档是否存在
        if [ ! -f "docs/state_machine.md" ]; then
          echo "❌ 状态机文档缺失: docs/state_machine.md"
          exit 1
        fi

        # 检查Mermaid图是否存在
        if ! grep -q "```mermaid" docs/state_machine.md; then
          echo "❌ 状态机文档缺少Mermaid流程图"
          exit 1
        fi

        # 验证关键状态是否在文档中
        REQUIRED_STATES=("THINKING" "STREAMING" "DONE" "ERROR" "RETRY_THINKING")
        for state in "${REQUIRED_STATES[@]}"; do
          if ! grep -q "$state" docs/state_machine.md; then
            echo "❌ 状态机文档缺少必要状态: $state"
            exit 1
          fi
        done

        echo "✅ 状态机文档验证通过"

    - name: 🧪 验证测试覆盖完整性
      run: |
        echo "=== 验证状态机测试覆盖 ==="

        # 检查Chaos测试是否存在
        if [ ! -f "features/coach/src/androidTest/kotlin/com/example/gymbro/features/coach/ui/AiCoachChaosTest.kt" ]; then
          echo "❌ Chaos测试文件缺失"
          exit 1
        fi

        # 检查关键测试方法是否存在
        REQUIRED_TESTS=(
          "chaosTest_networkTimeouts_shouldMaintainSingleMessage"
          "chaosTest_multipleRetries_shouldNotCreateDuplicateMessages"
          "validateSingleAiMessage"
          "validateStateMachineConsistency"
        )

        for test in "${REQUIRED_TESTS[@]}"; do
          if ! grep -q "$test" features/coach/src/androidTest/kotlin/com/example/gymbro/features/coach/ui/AiCoachChaosTest.kt; then
            echo "❌ 缺少必要的测试方法: $test"
            exit 1
          fi
        done

        echo "✅ 状态机测试覆盖验证通过"

    - name: 📈 验证监控配置存在
      run: |
        echo "=== 验证监控配置完整性 ==="

        # 检查Grafana配置是否存在
        if [ ! -f "config/grafana/ai_state_machine_dashboard.json" ]; then
          echo "❌ Grafana监控配置缺失"
          exit 1
        fi

        # 检查关键指标是否配置
        REQUIRED_METRICS=(
          "ai_message_count"
          "thinking_state_duration"
          "retry_attempt_count"
          "state_transition_error"
        )

        for metric in "${REQUIRED_METRICS[@]}"; do
          if ! grep -q "$metric" config/grafana/ai_state_machine_dashboard.json; then
            echo "❌ 缺少必要的监控指标: $metric"
            exit 1
          fi
        done

        echo "✅ 监控配置验证通过"

  enforce_state_machine_pr_template:
    runs-on: ubuntu-latest
    needs: detect_state_machine_changes
    if: needs.detect_state_machine_changes.outputs.STATE_CHANGE_DETECTED == 'true' && github.event_name == 'pull_request'

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: 🔍 验证PR描述完整性
      env:
        PR_BODY: ${{ github.event.pull_request.body }}
      run: |
        echo "=== 验证PR模板完成情况 ==="

        # 检查PR描述是否包含必要的状态机审查项
        REQUIRED_CHECKLIST=(
          "状态机变更审查"
          "状态流程图"
          "状态一致性保证"
          "并发与原子性"
          "测试覆盖"
          "指标对比"
        )

        MISSING_ITEMS=()
        for item in "${REQUIRED_CHECKLIST[@]}"; do
          if ! echo "$PR_BODY" | grep -q "$item"; then
            MISSING_ITEMS+=("$item")
          fi
        done

        if [ ${#MISSING_ITEMS[@]} -gt 0 ]; then
          echo "❌ PR描述缺少必要的状态机审查项："
          for item in "${MISSING_ITEMS[@]}"; do
            echo "  - $item"
          done
          echo ""
          echo "🔧 请按照PR模板填写完整的状态机变更审查清单！"
          exit 1
        fi

        # 检查是否填写了checked boxes
        if ! echo "$PR_BODY" | grep -q "\[x\]"; then
          echo "❌ 请勾选已完成的审查项（使用 [x] 标记）"
          exit 1
        fi

        echo "✅ PR模板验证通过"

  cross_validation_monitor_test:
    runs-on: ubuntu-latest
    needs: validate_state_machine_artifacts

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Setup JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: 🔄 交叉验证：监控 ↔ 测试
      run: |
        echo "=== 验证监控告警与测试联动 ==="

        # 模拟ai_message_count > 1的场景
        # 这里应该连接到实际的监控系统进行验证
        echo "🧪 模拟状态机异常场景..."

        # 检查测试是否包含监控指标验证
        if ! grep -r "ai_message_count\|thinking_state_duration" features/coach/src/androidTest/; then
          echo "❌ 测试代码缺少监控指标验证"
          exit 1
        fi

        echo "✅ 监控与测试交叉验证通过"

    - name: 🧬 Mutation测试：原子性验证
      run: |
        echo "=== Mutation测试：验证ensureThinkingMessageExists原子性 ==="

        # 检查关键代码是否存在并包含必要的保护机制
        if ! grep -r "ensureThinkingMessageExists" features/coach/src/main/kotlin/; then
          echo "❌ 缺少ensureThinkingMessageExists方法"
          exit 1
        fi

        # 验证是否包含并发保护机制
        if ! grep -r "synchronized\|Mutex\|atomic" features/coach/src/main/kotlin/; then
          echo "⚠️ 建议添加并发保护机制到ensureThinkingMessageExists"
        fi

        echo "✅ 原子性保护验证通过"

  runtime_assertion_check:
    runs-on: ubuntu-latest
    needs: cross_validation_monitor_test

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: 🚨 运行时断言检查
      run: |
        echo "=== 验证运行时断言保护 ==="

        # 检查是否在关键方法中添加了断言
        ASSERTION_PATTERNS=(
          "assert.*size.*1"
          "require.*size"
          "check.*messages"
        )

        ASSERTION_FOUND=false
        for pattern in "${ASSERTION_PATTERNS[@]}"; do
          if grep -r "$pattern" features/coach/src/main/kotlin/; then
            echo "✅ 发现运行时断言: $pattern"
            ASSERTION_FOUND=true
          fi
        done

        if [ "$ASSERTION_FOUND" = false ]; then
          echo "⚠️ 建议在updateLastAiMessage末尾添加assert(messageList.size <= 1)"
          echo "⚠️ 运行时自爆比线上脏数据更安全"
        fi

        echo "✅ 运行时断言检查完成"

  final_state_guard_report:
    runs-on: ubuntu-latest
    needs: [detect_state_machine_changes, validate_state_machine_artifacts, enforce_state_machine_pr_template, cross_validation_monitor_test, runtime_assertion_check]
    if: always()

    steps:
    - name: 📊 状态机防护报告
      run: |
        echo "=== 状态机防护最终报告 ==="
        echo ""
        echo "🔍 变更检测: ${{ needs.detect_state_machine_changes.result }}"
        echo "📊 文档验证: ${{ needs.validate_state_machine_artifacts.result }}"
        echo "📋 PR模板: ${{ needs.enforce_state_machine_pr_template.result }}"
        echo "🔄 交叉验证: ${{ needs.cross_validation_monitor_test.result }}"
        echo "🚨 断言检查: ${{ needs.runtime_assertion_check.result }}"
        echo ""

        if [[ "${{ needs.detect_state_machine_changes.outputs.STATE_CHANGE_DETECTED }}" == "true" ]]; then
          if [[ "${{ needs.validate_state_machine_artifacts.result }}" == "success" &&
                "${{ needs.cross_validation_monitor_test.result }}" == "success" ]]; then
            echo "✅ 状态机变更已通过所有安全检查"
            echo "🛡️ 状态机防护体系正常工作"
          else
            echo "❌ 状态机变更未通过安全检查，禁止合并"
            echo "🚨 触发Fail Fast保护机制"
            exit 1
          fi
        else
          echo "ℹ️ 本次变更未涉及状态机，正常通过"
        fi

    - name: 🚨 状态机铁律提醒
      if: needs.detect_state_machine_changes.outputs.STATE_CHANGE_DETECTED == 'true'
      run: |
        echo ""
        echo "🚨🚨🚨 状态机铁律提醒 🚨🚨🚨"
        echo ""
        echo "1️⃣ 任何状态机相关改动 = 回滚级风险"
        echo "2️⃣ 可观测性必须能推翻主观判断"
        echo "3️⃣ 流程写进脚本，别写进脑子"
        echo ""
        echo "再次出现状态机问题 → 直走Root-Cause Review"
        echo ""
        echo "🛡️ 防护机制已启动，请严格遵循审查流程！"
