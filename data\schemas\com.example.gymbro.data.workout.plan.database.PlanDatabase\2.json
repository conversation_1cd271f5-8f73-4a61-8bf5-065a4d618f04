{"formatVersion": 1, "database": {"version": 2, "identityHash": "398fa00993df16011c534d165f89f543", "entities": [{"tableName": "workout_plans", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `description` TEXT, `userId` TEXT NOT NULL, `targetGoal` TEXT, `difficultyLevel` INTEGER NOT NULL, `estimatedDuration` INTEGER, `isPublic` INTEGER NOT NULL, `isTemplate` INTEGER NOT NULL, `isFavorite` INTEGER NOT NULL, `isAIGenerated` INTEGER NOT NULL, `tags` TEXT NOT NULL, `totalDays` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": false}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "targetGoal", "columnName": "targetGoal", "affinity": "TEXT", "notNull": false}, {"fieldPath": "difficultyLevel", "columnName": "difficultyLevel", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "estimatedDuration", "columnName": "estimatedDuration", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "isPublic", "columnName": "isPublic", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isTemplate", "columnName": "isTemplate", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isFavorite", "columnName": "isFavorite", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isAIGenerated", "columnName": "isAIGenerated", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "tags", "columnName": "tags", "affinity": "TEXT", "notNull": true}, {"fieldPath": "totalDays", "columnName": "totalDays", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_workout_plans_userId", "unique": false, "columnNames": ["userId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_plans_userId` ON `${TABLE_NAME}` (`userId`)"}, {"name": "index_workout_plans_isPublic", "unique": false, "columnNames": ["isPublic"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_plans_isPublic` ON `${TABLE_NAME}` (`isPublic`)"}, {"name": "index_workout_plans_isTemplate", "unique": false, "columnNames": ["isTemplate"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_plans_isTemplate` ON `${TABLE_NAME}` (`isTemplate`)"}, {"name": "index_workout_plans_isFavorite", "unique": false, "columnNames": ["isFavorite"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_plans_isFavorite` ON `${TABLE_NAME}` (`isFavorite`)"}, {"name": "index_workout_plans_createdAt", "unique": false, "columnNames": ["createdAt"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_plans_createdAt` ON `${TABLE_NAME}` (`createdAt`)"}, {"name": "index_workout_plans_updatedAt", "unique": false, "columnNames": ["updatedAt"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_plans_updatedAt` ON `${TABLE_NAME}` (`updatedAt`)"}], "foreignKeys": []}, {"tableName": "plan_days", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `planId` TEXT NOT NULL, `dayNumber` INTEGER NOT NULL, `isRestDay` INTEGER NOT NULL, `notes` TEXT, `orderIndex` INTEGER NOT NULL, `estimatedDuration` INTEGER, `isCompleted` INTEGER NOT NULL, `progress` TEXT NOT NULL, `createdAt` INTEGER NOT NULL, PRIMARY KEY(`id`), FOREIGN KEY(`planId`) REFERENCES `workout_plans`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "planId", "columnName": "planId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "dayNumber", "columnName": "dayNumber", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isRestDay", "columnName": "isRestDay", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "notes", "columnName": "notes", "affinity": "TEXT", "notNull": false}, {"fieldPath": "orderIndex", "columnName": "orderIndex", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "estimatedDuration", "columnName": "estimatedDuration", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "isCompleted", "columnName": "isCompleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "progress", "columnName": "progress", "affinity": "TEXT", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_plan_days_planId", "unique": false, "columnNames": ["planId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_plan_days_planId` ON `${TABLE_NAME}` (`planId`)"}, {"name": "index_plan_days_dayNumber", "unique": false, "columnNames": ["dayNumber"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_plan_days_dayNumber` ON `${TABLE_NAME}` (`dayNumber`)"}, {"name": "index_plan_days_orderIndex", "unique": false, "columnNames": ["orderIndex"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_plan_days_orderIndex` ON `${TABLE_NAME}` (`orderIndex`)"}, {"name": "index_plan_days_isCompleted", "unique": false, "columnNames": ["isCompleted"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_plan_days_isCompleted` ON `${TABLE_NAME}` (`isCompleted`)"}, {"name": "index_plan_days_progress", "unique": false, "columnNames": ["progress"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_plan_days_progress` ON `${TABLE_NAME}` (`progress`)"}], "foreignKeys": [{"table": "workout_plans", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["planId"], "referencedColumns": ["id"]}]}, {"tableName": "plan_templates", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `planDayId` TEXT NOT NULL, `templateId` TEXT NOT NULL, `order` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, PRIMARY KEY(`id`), FOREIGN KEY(`planDayId`) REFERENCES `plan_days`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "planDayId", "columnName": "planDayId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "templateId", "columnName": "templateId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "order", "columnName": "order", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_plan_templates_planDayId", "unique": false, "columnNames": ["planDayId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_plan_templates_planDayId` ON `${TABLE_NAME}` (`planDayId`)"}, {"name": "index_plan_templates_templateId", "unique": false, "columnNames": ["templateId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_plan_templates_templateId` ON `${TABLE_NAME}` (`templateId`)"}, {"name": "index_plan_templates_order", "unique": false, "columnNames": ["order"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_plan_templates_order` ON `${TABLE_NAME}` (`order`)"}], "foreignKeys": [{"table": "plan_days", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["planDayId"], "referencedColumns": ["id"]}]}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '398fa00993df16011c534d165f89f543')"]}}