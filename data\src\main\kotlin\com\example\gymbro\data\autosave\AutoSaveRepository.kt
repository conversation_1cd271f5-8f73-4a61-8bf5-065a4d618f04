package com.example.gymbro.data.autosave

import com.example.gymbro.core.autosave.config.AutoSaveConfig
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 自动保存仓库接口
 *
 * 定义自动保存会话管理的契约
 */
interface AutoSaveRepository {
    /**
     * 创建自动保存会话
     */
    suspend fun <T : Any> createSession(
        config: AutoSaveConfig<T>,
        scope: CoroutineScope,
    ): ModernResult<String>

    /**
     * 获取自动保存会话
     */
    fun <T : Any> getSession(sessionId: String): AutoSaveSession<T>?

    /**
     * 删除自动保存会话
     */
    suspend fun deleteSession(sessionId: String): ModernResult<Unit>

    /**
     * 获取所有活跃会话
     */
    fun getActiveSessions(): Flow<List<AutoSaveSession.SessionInfo>>

    /**
     * 清除所有会话
     */
    suspend fun clearAllSessions(): ModernResult<Unit>
}

/**
 * 自动保存仓库实现
 *
 * 🎯 功能特性：
 * - 管理多个并发的自动保存会话
 * - 支持会话的创建、获取、删除
 * - 提供会话持久化和恢复
 * - 响应式状态更新
 * - 线程安全的会话管理
 *
 * @param globalAutoSaveManager 全局自动保存管理器
 * @param logger 日志记录器
 */
@Singleton
class AutoSaveRepositoryImpl
@Inject
constructor(
    private val globalAutoSaveManager: GlobalAutoSaveManager,
    private val logger: Logger,
) : AutoSaveRepository {
    // 使用ConcurrentHashMap确保线程安全
    private val sessions = ConcurrentHashMap<String, AutoSaveSession<*>>()

    // 用于保护会话操作的互斥锁
    private val sessionMutex = Mutex()

    // 活跃会话状态流
    private val _activeSessions = MutableStateFlow<List<AutoSaveSession.SessionInfo>>(emptyList())

    override suspend fun <T : Any> createSession(
        config: AutoSaveConfig<T>,
        scope: CoroutineScope,
    ): ModernResult<String> =
        sessionMutex.withLock {
            return try {
                logger.d("AutoSaveRepositoryImpl", "创建自动保存会话: ${config.id}")

                // 检查是否已存在相同配置的会话
                val existingSession =
                    sessions.values.find { session ->
                        session.getSessionInfo().configId == config.id
                    }

                if (existingSession != null) {
                    logger.w("AutoSaveRepositoryImpl", "会话已存在: ${config.id}")
                    return ModernResult.Success(existingSession.sessionId)
                }

                // 创建新会话
                val session = AutoSaveSession.create(config, scope, logger)
                sessions[session.sessionId] = session

                // 更新活跃会话列表
                updateActiveSessionsList()

                logger.d("AutoSaveRepositoryImpl", "自动保存会话创建成功: ${session.sessionId}")
                ModernResult.Success(session.sessionId)
            } catch (e: Exception) {
                logger.e(e, "创建自动保存会话失败: ${config.id}")
                ModernResult.Error(
                    com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "AutoSaveRepositoryImpl.createSession",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                        category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                        cause = e,
                    ),
                )
            }
        }

    @Suppress("UNCHECKED_CAST")
    override fun <T : Any> getSession(sessionId: String): AutoSaveSession<T>? =
        try {
            val session = sessions[sessionId] as? AutoSaveSession<T>
            if (session != null) {
                logger.d("AutoSaveRepositoryImpl", "获取自动保存会话成功: $sessionId")
            } else {
                logger.w("AutoSaveRepositoryImpl", "未找到自动保存会话: $sessionId")
            }
            session
        } catch (e: Exception) {
            logger.e(e, "获取自动保存会话失败: $sessionId")
            null
        }

    override suspend fun deleteSession(sessionId: String): ModernResult<Unit> =
        sessionMutex.withLock {
            return try {
                logger.d("AutoSaveRepositoryImpl", "删除自动保存会话: $sessionId")

                val session = sessions[sessionId]
                if (session == null) {
                    logger.w("AutoSaveRepositoryImpl", "会话不存在: $sessionId")
                    return ModernResult.Success(Unit)
                }

                // 停止会话
                session.stop()

                // 从会话映射中移除
                sessions.remove(sessionId)

                // 更新活跃会话列表
                updateActiveSessionsList()

                logger.d("AutoSaveRepositoryImpl", "自动保存会话删除成功: $sessionId")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, "删除自动保存会话失败: $sessionId")
                ModernResult.Error(
                    com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "AutoSaveRepositoryImpl.deleteSession",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                        category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                        cause = e,
                    ),
                )
            }
        }

    override fun getActiveSessions(): Flow<List<AutoSaveSession.SessionInfo>> = _activeSessions.asStateFlow()

    override suspend fun clearAllSessions(): ModernResult<Unit> =
        sessionMutex.withLock {
            return try {
                logger.d("AutoSaveRepositoryImpl", "清除所有自动保存会话")

                // 停止所有会话
                sessions.values.forEach { session ->
                    try {
                        session.stop()
                    } catch (e: Exception) {
                        logger.e(e, "停止会话失败: ${session.sessionId}")
                    }
                }

                // 清除会话映射
                sessions.clear()

                // 更新活跃会话列表
                updateActiveSessionsList()

                logger.d("AutoSaveRepositoryImpl", "所有自动保存会话已清除")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, "清除所有自动保存会话失败")
                ModernResult.Error(
                    com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "AutoSaveRepositoryImpl.clearAllSessions",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                        category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                        cause = e,
                    ),
                )
            }
        }

    /**
     * 获取会话统计信息
     */
    fun getSessionStats(): SessionStats {
        val sessionInfos = sessions.values.map { it.getSessionInfo() }
        return SessionStats(
            totalSessions = sessionInfos.size,
            activeSessions = sessionInfos.count { it.status == AutoSaveSession.SessionStatus.ACTIVE },
            pausedSessions = sessionInfos.count { it.status == AutoSaveSession.SessionStatus.PAUSED },
            errorSessions = sessionInfos.count { it.status == AutoSaveSession.SessionStatus.ERROR },
            totalSaveCount = sessionInfos.sumOf { it.saveCount },
        )
    }

    /**
     * 根据配置ID查找会话
     */
    fun findSessionByConfigId(configId: String): AutoSaveSession<*>? =
        sessions.values.find { session ->
            session.getSessionInfo().configId == configId
        }

    /**
     * 获取指定状态的会话
     */
    fun getSessionsByStatus(status: AutoSaveSession.SessionStatus): List<AutoSaveSession<*>> =
        sessions.values.filter { session ->
            session.getSessionInfo().status == status
        }

    /**
     * 更新活跃会话列表
     */
    private fun updateActiveSessionsList() {
        val sessionInfos = sessions.values.map { it.getSessionInfo() }
        _activeSessions.value = sessionInfos
        logger.d("AutoSaveRepositoryImpl", "活跃会话列表已更新，当前会话数: ${sessionInfos.size}")
    }

    /**
     * 会话统计信息
     */
    data class SessionStats(
        val totalSessions: Int,
        val activeSessions: Int,
        val pausedSessions: Int,
        val errorSessions: Int,
        val totalSaveCount: Int,
    )
}
