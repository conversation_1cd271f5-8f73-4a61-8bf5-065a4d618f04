package com.example.gymbro.designSystem.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import com.example.gymbro.designSystem.theme.tokens.Tokens

/**
 * 健身助手进度指示器组件
 *
 * 标准化的圆形进度指示器，使用应用程序的主题颜色
 *
 * @param modifier 应用于进度指示器的修饰符
 * @param color 进度指示器的颜色，默认为主题的主色
 * @param strokeWidth 进度指示器的线宽
 */
@Composable
fun gymBroProgressIndicator(
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.primary,
    strokeWidth: Dp = Tokens.Spacing.Tiny * 2,
) {
    CircularProgressIndicator(
        modifier = modifier,
        color = color,
        strokeWidth = strokeWidth,
    )
}

/**
 * 健身助手加载视图组件
 *
 * 居中显示的进度指示器，通常用于全屏或容器内的加载状态
 *
 * @param modifier 应用于加载视图的修饰符
 * @param color 进度指示器的颜色，默认为主题的主色
 * @param strokeWidth 进度指示器的线宽
 */
@Composable
fun loadingView(
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.primary,
    strokeWidth: Dp = Tokens.Spacing.Tiny * 2,
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center,
    ) {
        gymBroProgressIndicator(
            color = color,
            strokeWidth = strokeWidth,
        )
    }
}

/**
 * 居中的圆形进度指示器
 *
 * 这是loadingView的别名，用于向后兼容
 * 符合"必备必要存在"原则：复用现有实现而非重复造轮子
 */
@Composable
fun CenteredCircularProgressIndicator(
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.primary,
    strokeWidth: Dp = Tokens.Spacing.Tiny * 2,
) {
    loadingView(
        modifier = modifier,
        color = color,
        strokeWidth = strokeWidth,
    )
}
