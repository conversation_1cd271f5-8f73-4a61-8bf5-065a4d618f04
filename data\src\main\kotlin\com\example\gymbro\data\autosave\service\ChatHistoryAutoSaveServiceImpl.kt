package com.example.gymbro.data.autosave.service

import android.content.Context
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.data.coach.dao.ChatRawDao
import com.example.gymbro.data.coach.dao.ChatSessionDao
import com.example.gymbro.domain.autosave.ChatHistoryAutoSaveService
import com.example.gymbro.domain.coach.model.CoachMessage
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🎯 重构版Chat History自动保存服务 - ROOM直写备份
 *
 * 核心功能：
 * - 改为ROOM直写备份机制，消除DataStore依赖
 * - 保持异步特性，确保实时性
 * - 简化依赖，专注于ROOM操作
 */
@Singleton
class ChatHistoryAutoSaveServiceImpl
@Inject
constructor(
    @ApplicationContext private val context: Context,
    private val chatRawDao: ChatRawDao,
    private val chatSessionDao: ChatSessionDao,
    private val logger: Logger,
) : ChatHistoryAutoSaveService {
    private val activeSessions = ConcurrentHashMap<String, ChatAutoSaveSession>()

    // 🔥 添加类级别的协程作用域，用于自动创建会话时使用
    private val autoSaveScope = CoroutineScope(SupervisorJob())

    companion object {
        const val IMMEDIATE_SAVE_DELAY_MS = 1000L // 1秒防抖延迟
    }

    override suspend fun createChatAutoSave(
        sessionId: String,
        scope: CoroutineScope,
    ): ModernResult<String> =
        try {
            val internalKeyForMap = sessionId
            val operationInstanceId = "chat_autosave_op_${sessionId}_${System.currentTimeMillis()}"

            logger.d(
                "ChatHistoryAutoSaveServiceImpl",
                "[ROOM版] Creating auto-save context for chatSessionId: $internalKeyForMap (Operation: $operationInstanceId)",
            )

            if (activeSessions.containsKey(internalKeyForMap)) {
                logger.w(
                    "ChatHistoryAutoSaveServiceImpl",
                    "[ROOM版] Auto-save context already exists for chatSessionId: $internalKeyForMap. Reusing.",
                )
            }

            val session =
                ChatAutoSaveSession(
                    autoSaveSessionId = internalKeyForMap,
                    chatSessionId = sessionId,
                    scope = scope,
                    chatRawDao = chatRawDao,
                    chatSessionDao = chatSessionDao,
                    logger = logger,
                )

            activeSessions[internalKeyForMap] = session

            logger.d(
                "ChatHistoryAutoSaveServiceImpl",
                "[ROOM版] Auto-save context created/updated for chatSessionId: $internalKeyForMap",
            )
            ModernResult.Success(internalKeyForMap)
        } catch (e: Exception) {
            logger.e(e, "[ROOM版] 创建聊天历史自动保存会话失败")
            ModernResult.Error(
                com.example.gymbro.core.error.types.ModernDataError(
                    operationName = "ChatHistoryAutoSaveServiceImpl.createChatAutoSave",
                    errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                    category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                    cause = e,
                ),
            )
        }

    override suspend fun startChatAutoSave(
        autoSaveSessionId: String,
        chatSessionId: String,
        initialMessage: CoachMessage?,
    ): ModernResult<Unit> {
        return try {
            val session = activeSessions[autoSaveSessionId]
            if (session == null) {
                logger.e("ChatHistoryAutoSaveServiceImpl", "自动保存会话不存在: $autoSaveSessionId")
                return ModernResult.Error(
                    com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "ChatHistoryAutoSaveServiceImpl.startChatAutoSave",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Business.NotFound,
                        category = com.example.gymbro.core.error.types.ErrorCategory.BUSINESS,
                    ),
                )
            }

            session.start(initialMessage)
            logger.d("ChatHistoryAutoSaveServiceImpl", "[ROOM版] 聊天历史自动保存启动成功: $autoSaveSessionId")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            logger.e(e, "[ROOM版] 启动聊天历史自动保存失败: $autoSaveSessionId")
            ModernResult.Error(
                com.example.gymbro.core.error.types.ModernDataError(
                    operationName = "ChatHistoryAutoSaveServiceImpl.startChatAutoSave",
                    errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                    category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                    cause = e,
                ),
            )
        }
    }

    override suspend fun saveMessage(
        sessionId: String,
        message: CoachMessage,
    ): ModernResult<Unit> {
        return try {
            // 🔥 简化架构：直接使用 sessionId，如果会话不存在则自动创建
            val session = activeSessions.getOrPut(sessionId) {
                logger.d("ChatHistoryAutoSaveServiceImpl", "[ROOM版] 自动创建AutoSave会话: $sessionId")
                ChatAutoSaveSession(
                    autoSaveSessionId = sessionId,
                    chatSessionId = sessionId,
                    scope = autoSaveScope,
                    chatRawDao = chatRawDao,
                    chatSessionDao = chatSessionDao,
                    logger = logger,
                )
            }

            session.updateMessage(message)
            logger.d("ChatHistoryAutoSaveServiceImpl", "[ROOM版] 聊天消息已保存到ROOM: $sessionId")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            logger.e(e, "[ROOM版] 保存聊天消息失败: $sessionId")
            ModernResult.Error(
                com.example.gymbro.core.error.types.ModernDataError(
                    operationName = "ChatHistoryAutoSaveServiceImpl.saveMessage",
                    errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                    category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                    cause = e,
                ),
            )
        }
    }

    override suspend fun saveBatchMessages(
        sessionId: String,
        messages: List<CoachMessage>,
    ): ModernResult<Unit> {
        return try {
            // 🔥 简化架构：直接使用 sessionId，如果会话不存在则自动创建
            val session = activeSessions.getOrPut(sessionId) {
                logger.d("ChatHistoryAutoSaveServiceImpl", "[ROOM版] 自动创建AutoSave会话: $sessionId")
                ChatAutoSaveSession(
                    autoSaveSessionId = sessionId,
                    chatSessionId = sessionId,
                    scope = autoSaveScope,
                    chatRawDao = chatRawDao,
                    chatSessionDao = chatSessionDao,
                    logger = logger,
                )
            }

            session.updateBatchMessages(messages)
            logger.d(
                "ChatHistoryAutoSaveServiceImpl",
                "[ROOM版] 批量聊天消息已保存到ROOM: $sessionId, 数量: ${messages.size}",
            )
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            logger.e(e, "[ROOM版] 批量保存聊天消息失败: $sessionId")
            ModernResult.Error(
                com.example.gymbro.core.error.types.ModernDataError(
                    operationName = "ChatHistoryAutoSaveServiceImpl.saveBatchMessages",
                    errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                    category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                    cause = e,
                ),
            )
        }
    }

    override suspend fun saveNow(sessionId: String): ModernResult<Unit> {
        return try {
            // 🔥 简化架构：直接使用 sessionId，如果会话不存在则自动创建
            val session = activeSessions.getOrPut(sessionId) {
                logger.d("ChatHistoryAutoSaveServiceImpl", "[ROOM版] 自动创建AutoSave会话: $sessionId")
                ChatAutoSaveSession(
                    autoSaveSessionId = sessionId,
                    chatSessionId = sessionId,
                    scope = autoSaveScope,
                    chatRawDao = chatRawDao,
                    chatSessionDao = chatSessionDao,
                    logger = logger,
                )
            }

            session.saveNow()
            logger.d("ChatHistoryAutoSaveServiceImpl", "[ROOM版] 聊天历史立即保存完成: $sessionId")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            logger.e(e, "[ROOM版] 聊天历史立即保存失败: $sessionId")
            ModernResult.Error(
                com.example.gymbro.core.error.types.ModernDataError(
                    operationName = "ChatHistoryAutoSaveServiceImpl.saveNow",
                    errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                    category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                    cause = e,
                ),
            )
        }
    }

    override suspend fun pauseAutoSave(sessionId: String): ModernResult<Unit> =
        try {
            val session = activeSessions[sessionId]
            session?.pause()
            logger.d("ChatHistoryAutoSaveServiceImpl", "[ROOM版] 聊天历史自动保存已暂停: $sessionId")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            logger.e(e, "[ROOM版] 暂停聊天历史自动保存失败: $sessionId")
            ModernResult.Error(
                com.example.gymbro.core.error.types.ModernDataError(
                    operationName = "ChatHistoryAutoSaveServiceImpl.pauseAutoSave",
                    errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                    category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                    cause = e,
                ),
            )
        }

    override suspend fun resumeAutoSave(sessionId: String): ModernResult<Unit> =
        try {
            val session = activeSessions[sessionId]
            session?.resume()
            logger.d("ChatHistoryAutoSaveServiceImpl", "[ROOM版] 聊天历史自动保存已恢复: $sessionId")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            logger.e(e, "[ROOM版] 恢复聊天历史自动保存失败: $sessionId")
            ModernResult.Error(
                com.example.gymbro.core.error.types.ModernDataError(
                    operationName = "ChatHistoryAutoSaveServiceImpl.resumeAutoSave",
                    errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                    category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                    cause = e,
                ),
            )
        }

    override suspend fun stopAutoSave(sessionId: String): ModernResult<Unit> =
        try {
            val session = activeSessions[sessionId]
            if (session != null) {
                session.stop()
                activeSessions.remove(sessionId)
            }
            logger.d("ChatHistoryAutoSaveServiceImpl", "[ROOM版] 聊天历史自动保存已停止: $sessionId")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            logger.e(e, "[ROOM版] 停止聊天历史自动保存失败: $sessionId")
            ModernResult.Error(
                com.example.gymbro.core.error.types.ModernDataError(
                    operationName = "ChatHistoryAutoSaveServiceImpl.stopAutoSave",
                    errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                    category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                    cause = e,
                ),
            )
        }

    override suspend fun restoreFromCache(sessionId: String): ModernResult<List<CoachMessage>?> {
        return try {
            // 🔥 简化架构：直接使用 sessionId，如果会话不存在则返回空
            val session = activeSessions[sessionId]
            if (session == null) {
                logger.d("ChatHistoryAutoSaveServiceImpl", "[ROOM版] 会话不存在，返回空缓存: $sessionId")
                return ModernResult.Success(null)
            }

            val cachedMessages = session.restoreFromCache()
            logger.d("ChatHistoryAutoSaveServiceImpl", "[ROOM版] 聊天历史数据恢复: $sessionId")
            ModernResult.Success(cachedMessages)
        } catch (e: Exception) {
            logger.e(e, "[ROOM版] 恢复聊天历史数据失败: $sessionId")
            ModernResult.Error(
                com.example.gymbro.core.error.types.ModernDataError(
                    operationName = "ChatHistoryAutoSaveServiceImpl.restoreFromCache",
                    errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                    category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                    cause = e,
                ),
            )
        }
    }

    override suspend fun discardCache(sessionId: String): ModernResult<Unit> =
        try {
            val session = activeSessions[sessionId]
            session?.discardCache()
            logger.d("ChatHistoryAutoSaveServiceImpl", "[ROOM版] 聊天历史数据清理: $sessionId")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            logger.e(e, "[ROOM版] 清理聊天历史数据失败: $sessionId")
            ModernResult.Error(
                com.example.gymbro.core.error.types.ModernDataError(
                    operationName = "ChatHistoryAutoSaveServiceImpl.discardCache",
                    errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                    category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                    cause = e,
                ),
            )
        }
}
