package com.example.gymbro.data.workout.model

import java.util.*

/**
 * 训练动作数据提供器
 *
 * 提供默认的训练动作数据，用于初始化应用的动作库
 * 符合Data层实施指南，提供静态数据源
 */
object ExerciseDataProvider {
    /**
     * 获取所有默认训练动作
     */
    fun getDefaultExercises(): List<ExerciseData> =
        getChestExercises() + getBackExercises() + getShoulderExercises() +
            getArmExercises() + getLegExercises() + getCoreExercises()

    /**
     * 获取胸部训练动作
     */
    fun getChestExercises(): List<ExerciseData> =
        listOf(
            ExerciseData(
                id = UUID.randomUUID().toString(),
                name = "俯卧撑",
                category = "胸部",
                equipment = "无器械",
                targetMuscles = listOf("胸大肌", "三角肌前束", "肱三头肌"),
                steps =
                listOf(
                    "双手撑地，与肩同宽",
                    "身体保持一条直线",
                    "慢慢下降至胸部接近地面",
                    "用力推起至起始位置",
                ),
                notes = listOf("保持核心收紧", "动作要缓慢控制"),
            ),
            ExerciseData(
                id = UUID.randomUUID().toString(),
                name = "哑铃卧推",
                category = "胸部",
                equipment = "哑铃",
                targetMuscles = listOf("胸大肌", "三角肌前束", "肱三头肌"),
                steps =
                listOf(
                    "仰卧在卧推凳上，双脚踩地",
                    "双手握住哑铃，手臂伸直",
                    "慢慢下降哑铃至胸部两侧",
                    "用力推起至起始位置",
                ),
                notes = listOf("选择合适重量", "保持肩胛骨稳定"),
            ),
            ExerciseData(
                id = UUID.randomUUID().toString(),
                name = "杠铃卧推",
                category = "胸部",
                equipment = "杠铃",
                targetMuscles = listOf("胸大肌", "三角肌前束", "肱三头肌"),
                steps =
                listOf(
                    "仰卧在卧推凳上，双脚踩地",
                    "双手握住杠铃，比肩略宽",
                    "慢慢下降杠铃至胸部",
                    "用力推起至起始位置",
                ),
                notes = listOf("需要保护者协助", "保持杠铃平衡"),
            ),
        )

    /**
     * 获取背部训练动作
     */
    fun getBackExercises(): List<ExerciseData> =
        listOf(
            ExerciseData(
                id = UUID.randomUUID().toString(),
                name = "引体向上",
                category = "背部",
                equipment = "单杠",
                targetMuscles = listOf("背阔肌", "菱形肌", "肱二头肌"),
                steps =
                listOf(
                    "双手握住单杠，比肩略宽",
                    "身体悬挂，双脚离地",
                    "用力拉起身体至下巴过杠",
                    "慢慢下降至起始位置",
                ),
                notes = listOf("如果无法完成，可使用辅助带", "保持身体稳定"),
            ),
            ExerciseData(
                id = UUID.randomUUID().toString(),
                name = "哑铃划船",
                category = "背部",
                equipment = "哑铃",
                targetMuscles = listOf("背阔肌", "菱形肌", "后三角肌"),
                steps =
                listOf(
                    "单膝跪在凳子上，同侧手撑住",
                    "另一只手握住哑铃，自然下垂",
                    "用力拉起哑铃至腰部",
                    "慢慢下降至起始位置",
                ),
                notes = listOf("保持背部挺直", "感受背部肌肉发力"),
            ),
        )

    /**
     * 获取肩部训练动作
     */
    fun getShoulderExercises(): List<ExerciseData> =
        listOf(
            ExerciseData(
                id = UUID.randomUUID().toString(),
                name = "哑铃推举",
                category = "肩部",
                equipment = "哑铃",
                targetMuscles = listOf("三角肌", "肱三头肌"),
                steps =
                listOf(
                    "坐在凳子上，背部挺直",
                    "双手握住哑铃，举至肩部高度",
                    "用力向上推举至手臂伸直",
                    "慢慢下降至起始位置",
                ),
                notes = listOf("不要过度伸展", "保持核心稳定"),
            ),
        )

    /**
     * 获取手臂训练动作
     */
    fun getArmExercises(): List<ExerciseData> =
        listOf(
            ExerciseData(
                id = UUID.randomUUID().toString(),
                name = "哑铃弯举",
                category = "手臂",
                equipment = "哑铃",
                targetMuscles = listOf("肱二头肌"),
                steps =
                listOf(
                    "站立，双手握住哑铃",
                    "手臂自然下垂在身体两侧",
                    "用力弯曲手臂，举起哑铃",
                    "慢慢下降至起始位置",
                ),
                notes = listOf("保持上臂不动", "感受二头肌收缩"),
            ),
        )

    /**
     * 获取腿部训练动作
     */
    fun getLegExercises(): List<ExerciseData> =
        listOf(
            ExerciseData(
                id = UUID.randomUUID().toString(),
                name = "深蹲",
                category = "腿部",
                equipment = "无器械",
                targetMuscles = listOf("股四头肌", "臀大肌", "腘绳肌"),
                steps =
                listOf(
                    "双脚与肩同宽站立",
                    "双手可交叉胸前或伸直前方",
                    "慢慢下蹲至大腿与地面平行",
                    "用力站起至起始位置",
                ),
                notes = listOf("保持膝盖与脚尖方向一致", "下蹲时重心在脚跟"),
            ),
        )

    /**
     * 获取核心训练动作
     */
    fun getCoreExercises(): List<ExerciseData> =
        listOf(
            ExerciseData(
                id = UUID.randomUUID().toString(),
                name = "平板支撑",
                category = "腹部",
                equipment = "无器械",
                targetMuscles = listOf("腹直肌", "腹横肌", "竖脊肌"),
                steps =
                listOf(
                    "俯卧，双肘撑地",
                    "身体保持一条直线",
                    "收紧核心肌群",
                    "保持这个姿势",
                ),
                notes = listOf("不要塌腰或撅臀", "保持自然呼吸"),
            ),
        )
}
