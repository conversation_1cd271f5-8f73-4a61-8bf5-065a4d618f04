package com.example.gymbro.core.error.recovery

import com.example.gymbro.core.error.internal.recovery.CompositeStrategyImpl

/**
 * 组合恢复策略接口，按顺序尝试多个恢复策略
 */
interface CompositeStrategy<T> : RecoveryStrategy<T> {

    companion object {
        /**
         * 创建使用缓存数据的回退策略
         *
         * @param cacheOperation 从缓存获取数据的操作
         * @param remoteOperation 从远程获取数据的操作
         * @param stopOnCacheSuccess 是否在缓存策略成功时停止尝试（默认为true）
         */
        fun <T> cacheFirst(
            cacheOperation: suspend () -> T?,
            remoteOperation: suspend () -> T?,
            stopOnCacheSuccess: Boolean = true,
        ): CompositeStrategy<T> {
            return CompositeStrategyImpl(
                listOf(
                    object : RecoveryStrategy<T> {
                        override suspend fun execute(): T? = cacheOperation()
                    },
                    object : RecoveryStrategy<T> {
                        override suspend fun execute(): T? = remoteOperation()
                    },
                ),
                stopOnSuccess = stopOnCacheSuccess,
            )
        }

        /**
         * 创建组合恢复策略
         *
         * @param strategies 要尝试的恢复策略列表
         * @param stopOnSuccess 是否在第一个成功的策略后停止（默认为true）
         * @param executor 自定义策略执行逻辑（可选）
         */
        fun <T> create(
            strategies: List<RecoveryStrategy<T>>,
            stopOnSuccess: Boolean = true,
            executor: (suspend (List<RecoveryStrategy<T>>) -> T?)? = null,
        ): CompositeStrategy<T> {
            return CompositeStrategyImpl(
                strategies = strategies,
                stopOnSuccess = stopOnSuccess,
                executor = executor,
            )
        }
    }
}
