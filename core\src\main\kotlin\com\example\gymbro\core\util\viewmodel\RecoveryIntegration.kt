package com.example.gymbro.core.util.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.gymbro.core.error.ModernErrorHandler
import com.example.gymbro.core.error.recovery.RecoveryStrategy
import com.example.gymbro.core.error.recovery.getRecoveryStrategy
import com.example.gymbro.core.error.recovery.withRecoveryStrategy
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * ViewModel错误恢复策略集成
 *
 * 提供ViewModel和错误恢复策略之间的集成机制，使应用能够自动从某些错误中恢复。
 * 此集成层简化了错误恢复逻辑的实现，并与现有的ViewModel错误处理机制无缝结合。
 */

/**
 * 处理ModernResult结果，并尝试应用恢复策略
 *
 * 如果ModernResult是错误且该错误包含恢复策略，会自动尝试应用该策略。
 * 扩展了标准的handleInViewModel功能，添加了自动错误恢复能力。
 *
 * @param viewModel 当前ViewModel实例，用于启动协程
 * @param errorHandler 错误处理器，用于获取UI友好的错误消息
 * @param setLoading 设置加载状态的函数
 * @param setSuccess 处理成功结果的函数
 * @param setError 处理错误消息的函数
 * @param onRecoveryAttempt 当尝试恢复时调用的函数
 * @param onRecoverySuccess 当恢复成功时调用的函数
 * @param onRecoveryFailure 当恢复失败时调用的函数
 */
fun <T> ModernResult<T>.handleInViewModelWithRecovery(
    viewModel: ViewModel,
    errorHandler: ModernErrorHandler,
    setLoading: (Boolean) -> Unit,
    setSuccess: (T) -> Unit,
    setError: (UiText?) -> Unit,
    onRecoveryAttempt: (ModernDataError) -> Unit = {},
    onRecoverySuccess: (T) -> Unit = {},
    onRecoveryFailure: (ModernDataError) -> Unit = {},
) {
    setLoading(false)
    when (this) {
        is ModernResult.Success -> {
            setSuccess(data)
            setError(null)
        }
        is ModernResult.Error -> {
            // 检查是否有恢复策略
            val recoveryStrategy = error.getRecoveryStrategy<T>()
            if (recoveryStrategy != null) {
                onRecoveryAttempt(error)
                viewModel.viewModelScope.launch {
                    try {
                        // 尝试应用恢复策略
                        val recoveryResult = applyRecovery(error, recoveryStrategy)
                        when (recoveryResult) {
                            is ModernResult.Success<T> -> {
                                setSuccess(recoveryResult.data)
                                setError(null)
                                onRecoverySuccess(recoveryResult.data)
                            }
                            is ModernResult.Error -> {
                                setError(errorHandler.getUiMessage(recoveryResult.error))
                                onRecoveryFailure(recoveryResult.error)
                            }
                            is ModernResult.Loading -> {
                                setLoading(true)
                            }
                        }
                    } catch (e: Exception) {
                        Timber.e(e, "恢复策略执行失败")
                        setError(errorHandler.getUiMessage(error))
                        onRecoveryFailure(error)
                    }
                }
            } else {
                setError(errorHandler.getUiMessage(error))
            }
        }
        is ModernResult.Loading -> {
            setLoading(true)
            setError(null)
        }
    }
}

/**
 * 在ViewModel中尝试应用错误恢复策略
 *
 * 提供一个简单的方法在ViewModel作用域中尝试错误恢复，
 * 不修改UI状态，只提供恢复成功或失败的回调。
 *
 * @param error 要恢复的错误
 * @param onRecoverySuccess 恢复成功时的回调函数
 * @param onRecoveryFailure 恢复失败时的回调函数
 */
fun <T> ViewModel.applyErrorRecovery(
    error: ModernDataError,
    onRecoverySuccess: (T) -> Unit,
    onRecoveryFailure: (ModernDataError) -> Unit = {},
) {
    viewModelScope.launch {
        try {
            // 获取恢复策略
            val strategy = error.getRecoveryStrategy<T>()
            if (strategy == null) {
                onRecoveryFailure(error)
                return@launch
            }

            // 尝试应用恢复策略
            val recoveryResult = applyRecovery(error, strategy)
            when (recoveryResult) {
                is ModernResult.Success<T> -> {
                    onRecoverySuccess(recoveryResult.data)
                }
                is ModernResult.Error -> {
                    onRecoveryFailure(recoveryResult.error)
                }
                is ModernResult.Loading -> {
                    Timber.d("恢复策略返回Loading状态，这通常不应该发生")
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "应用恢复策略时发生错误")
            onRecoveryFailure(error)
        }
    }
}

/**
 * 为ModernResult添加恢复策略并在ViewModel中应用
 *
 * @param viewModel 当前ViewModel实例
 * @param strategy 要应用的恢复策略
 * @param onSuccess 恢复成功时的回调
 * @param onFailure 恢复失败时的回调
 */
fun <T> ModernResult.Error.withRecoveryInViewModel(
    viewModel: ViewModel,
    strategy: RecoveryStrategy<T>,
    onSuccess: (T) -> Unit,
    onFailure: (ModernDataError) -> Unit = {},
) {
    val errorWithStrategy = error.withRecoveryStrategy(strategy)
    viewModel.viewModelScope.launch {
        try {
            val result = strategy.execute()
            if (result != null) {
                onSuccess(result)
            } else {
                onFailure(errorWithStrategy)
            }
        } catch (e: Exception) {
            Timber.e(e, "恢复策略执行失败")
            onFailure(errorWithStrategy)
        }
    }
}

/**
 * 为ViewModel添加错误恢复感知能力的接口
 *
 * 实现此接口的ViewModel能够提供自定义恢复策略，并处理恢复结果。
 */
interface RecoveryAwareViewModel {
    /**
     * 错误处理器，用于生成UI友好的错误消息
     */
    val errorHandler: ModernErrorHandler

    /**
     * 为指定错误提供恢复策略
     *
     * @param error 需要恢复的错误
     * @return 适用的恢复策略，如果没有合适的策略则返回null
     */
    fun <T> provideRecoveryStrategy(error: ModernDataError): RecoveryStrategy<T>? {
        // 默认使用错误处理器的建议恢复策略
        return errorHandler.suggestRecoveryStrategy(error)
    }

    /**
     * 处理恢复成功的结果
     *
     * @param result 恢复操作的成功结果
     */
    fun <T> handleRecoverySuccess(result: T) {
        // 默认实现为空，子类可以覆盖
    }

    /**
     * 处理恢复失败的情况
     *
     * @param error 恢复失败的错误
     */
    fun handleRecoveryFailure(error: ModernDataError) {
        // 默认实现为空，子类可以覆盖
    }

    /**
     * 为RecoveryAwareViewModel提供的便捷扩展函数
     * 使用ViewModel自定义的恢复策略处理结果
     *
     * @param viewModel 当前ViewModel实例
     * @param setLoading 设置加载状态的函数
     * @param setSuccess 处理成功结果的函数
     * @param setError 处理错误消息的函数
     */
    fun <T> ModernResult<T>.handleWithCustomRecovery(
        viewModel: ViewModel,
        setLoading: (Boolean) -> Unit,
        setSuccess: (T) -> Unit,
        setError: (UiText?) -> Unit,
    ) {
        when (this) {
            is ModernResult.Success -> {
                setLoading(false)
                setSuccess(data)
                setError(null)
            }
            is ModernResult.Error -> {
                setLoading(false)

                // 尝试获取自定义恢复策略
                val strategy = provideRecoveryStrategy<T>(error)

                if (strategy != null) {
                    // 创建带有自定义策略的新错误
                    val errorWithStrategy = error.withRecoveryStrategy(strategy)

                    // 应用恢复策略
                    (viewModel as ViewModel).viewModelScope.launch {
                        try {
                            val result = strategy.execute()
                            if (result != null) {
                                setSuccess(result)
                                setError(null)
                                handleRecoverySuccess(result)
                            } else {
                                setError(errorHandler.getUiMessage(errorWithStrategy))
                                handleRecoveryFailure(errorWithStrategy)
                            }
                        } catch (e: Exception) {
                            Timber.e(e, "恢复策略执行失败")
                            setError(errorHandler.getUiMessage(error))
                            handleRecoveryFailure(error)
                        }
                    }
                } else {
                    setError(errorHandler.getUiMessage(error))
                }
            }
            is ModernResult.Loading -> {
                setLoading(true)
                setError(null)
            }
        }
    }
}

/**
 * 辅助函数，应用恢复策略并返回ModernResult
 */
private suspend fun <T> applyRecovery(
    error: ModernDataError,
    strategy: RecoveryStrategy<T>,
): ModernResult<T> {
    val result = strategy.execute()
    return if (result != null) {
        ModernResult.Success(result)
    } else {
        ModernResult.Error(error)
    }
}

/**
 * 为ViewModel实现基本的恢复能力的类
 *
 * @param T 视图模型处理的数据类型
 */
class RecoveryViewModel<T : Any> :
    ViewModel(),
    RecoveryAwareViewModel {
    override lateinit var errorHandler: ModernErrorHandler

    // 其他相关实现可以在这里添加
}
