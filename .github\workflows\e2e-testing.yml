name: E2E Testing Pipeline (GymBro Core Flow)

on:
  push:
    branches: [ master, develop ]
    paths:
      - 'app/**'
      - 'features/**'
      - 'core/**'
      - 'data/**'
      - 'domain/**'
      - 'ci-scripts/**'
  pull_request:
    branches: [ master, develop ]
    paths:
      - 'app/**'
      - 'features/**'
      - 'core/**'
      - 'data/**'
      - 'domain/**'
  schedule:
    # 每日凌晨4点运行完整E2E测试
    - cron: '0 4 * * *'
  workflow_dispatch:
    inputs:
      test_scope:
        description: 'E2E测试范围'
        required: true
        default: 'sprint1'
        type: choice
        options:
          - sprint0
          - sprint1
          - sprint1-enhanced
          - p0-regression
          - full

permissions:
  contents: write
  actions: write
  checks: write

env:
  # 现代化配置 + UTF-8强制支持
  GRADLE_OPTS: -Dorg.gradle.daemon=false -Dorg.gradle.workers.max=2 -Dfile.encoding=UTF-8
  JAVA_TOOL_OPTIONS: -Dfile.encoding=UTF-8
  LC_ALL: C.UTF-8
  JAVA_VERSION: '17'
  # E2E测试环境配置
  TEST_BASE_URL: 'http://localhost:8080'
  E2E_TIMEOUT: 1800 # 30分钟超时

jobs:
  # Sprint 0 基础流程测试
  sprint0-e2e:
    name: Sprint 0 E2E - AI Chat → Function Call → ActiveWorkout
    runs-on: ubuntu-latest
    if: |
      (inputs.test_scope == 'sprint0' || inputs.test_scope == 'full') ||
      (github.event_name == 'schedule') ||
      (github.event_name == 'push' && contains(github.event.head_commit.message, '[e2e-sprint0]'))
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 设置JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: ${{ env.JAVA_VERSION }}
          distribution: 'temurin'

      - name: 现代化Gradle缓存
        uses: gradle/gradle-build-action@v3
        with:
          gradle-home-cache-cleanup: true

      - name: 构建测试应用
        run: |
          chmod +x gradlew
          ./gradlew assembleDebug --no-configuration-cache
        env:
          GRADLE_OPTS: ${{ env.GRADLE_OPTS }}

      - name: 🚀 运行Sprint 0 E2E测试
        run: |
          chmod +x ci-scripts/e2e_workout_flow.sh
          cd ci-scripts
          timeout ${{ env.E2E_TIMEOUT }} ./e2e_workout_flow.sh
        env:
          TEST_USER_ID: 'github-action-sprint0'
          LOG_LEVEL: 'INFO'

      - name: 📊 上传Sprint 0测试报告
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: sprint0-e2e-reports-${{ github.sha }}
          path: |
            ci-scripts/e2e_test_*.log
            ci-scripts/test_*.log
          retention-days: 7

  # Sprint 1 增强测试
  sprint1-e2e:
    name: Sprint 1 E2E - Function Calling → Workout Session → Data Persistence
    runs-on: ubuntu-latest
    if: |
      (inputs.test_scope == 'sprint1' || inputs.test_scope == 'full') ||
      (github.event_name == 'schedule') ||
      (github.event_name == 'push' && contains(github.event.head_commit.message, '[e2e-sprint1]'))
    outputs:
      test-status: ${{ steps.sprint1-test.outputs.status }}
      performance-metrics: ${{ steps.sprint1-test.outputs.metrics }}
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 设置JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: ${{ env.JAVA_VERSION }}
          distribution: 'temurin'

      - name: 设置JDK并配置Gradle缓存
        uses: gradle/gradle-build-action@v3
        with:
          gradle-home-cache-cleanup: true

      - name: 准备测试环境
        run: |
          chmod +x gradlew
          # 创建测试所需目录
          mkdir -p /tmp/e2e-artifacts
          mkdir -p test/e2e/scenarios
          # 构建debug APK用于测试
          ./gradlew assembleDebug --no-configuration-cache
        env:
          GRADLE_OPTS: ${{ env.GRADLE_OPTS }}

      - name: 🚀 运行Sprint 1基础E2E测试
        id: sprint1-test
        run: |
          chmod +x ci-scripts/sprint1_e2e_enhanced.sh
          cd ci-scripts
          # 设置输出变量
          echo "status=running" >> $GITHUB_OUTPUT
          # 运行测试并捕获结果
          if timeout ${{ env.E2E_TIMEOUT }} ./sprint1_e2e_enhanced.sh; then
            echo "status=success" >> $GITHUB_OUTPUT
            echo "metrics=基础测试通过" >> $GITHUB_OUTPUT
          else
            echo "status=failure" >> $GITHUB_OUTPUT
            echo "metrics=基础测试失败" >> $GITHUB_OUTPUT
            exit 1
          fi
        env:
          TEST_USER_ID: 'github-action-sprint1'
          TEST_BASE_URL: ${{ env.TEST_BASE_URL }}

      - name: 📊 上传Sprint 1测试报告
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: sprint1-e2e-reports-${{ github.sha }}
          path: |
            /tmp/sprint1_e2e_*.log
            /tmp/generated_template.json
            /tmp/created_session.json
            /tmp/sprint1_e2e_report_*.json
          retention-days: 7

  # Sprint 1 增强版测试 (v2.0)
  sprint1-enhanced-e2e:
    name: Sprint 1 Enhanced E2E v2.0 - 负面测试 + 轮询检查 + 数据驱动
    runs-on: ubuntu-latest
    if: |
      (inputs.test_scope == 'sprint1-enhanced' || inputs.test_scope == 'full') ||
      (github.event_name == 'schedule') ||
      (github.event_name == 'push' && contains(github.event.head_commit.message, '[e2e-enhanced]'))
    needs: [sprint1-e2e]
    outputs:
      enhanced-status: ${{ steps.enhanced-test.outputs.status }}
      quality-gates: ${{ steps.enhanced-test.outputs.quality-gates }}
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 设置JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: ${{ env.JAVA_VERSION }}
          distribution: 'temurin'

      - name: 设置JDK并配置Gradle缓存
        uses: gradle/gradle-build-action@v3
        with:
          gradle-home-cache-cleanup: true

      - name: "安装bc计算器 (增强脚本需要)"
        run: sudo apt-get update && sudo apt-get install -y bc

      - name: 准备增强测试环境
        run: |
          chmod +x gradlew
          # 构建debug和test APK
          ./gradlew assembleDebug assembleAndroidTest --no-configuration-cache
          # 创建测试场景目录
          mkdir -p test/e2e/scenarios
          # 创建模拟Python验证脚本 (如果不存在)
          mkdir -p scripts
          cat > scripts/validate_template_schema.py << 'EOF'
          #!/usr/bin/env python3
          import json
          import sys

          def validate_schema(file_path):
              try:
                  with open(file_path, 'r') as f:
                      data = json.load(f)
                  # 基础Schema验证
                  required_fields = ['template', 'exercises']
                  if 'template' in data and 'exercises' in data['template']:
                      exercises = data['template']['exercises']
                      if isinstance(exercises, list) and len(exercises) > 0:
                          print("✅ Schema validation passed")
                          return True
                  print("❌ Schema validation failed")
                  return False
              except Exception as e:
                  print(f"❌ Schema validation error: {e}")
                  return False

          if __name__ == "__main__":
              if len(sys.argv) != 2:
                  sys.exit(1)
              sys.exit(0 if validate_schema(sys.argv[1]) else 1)
          EOF
          chmod +x scripts/validate_template_schema.py
        env:
          GRADLE_OPTS: ${{ env.GRADLE_OPTS }}

      - name: 🚀 运行Sprint 1增强E2E测试 v2.0
        id: enhanced-test
        run: |
          chmod +x ci-scripts/sprint1_e2e_enhanced_v2.sh
          cd ci-scripts
          echo "status=running" >> $GITHUB_OUTPUT
          # 运行增强版测试
          if timeout ${{ env.E2E_TIMEOUT }} ./sprint1_e2e_enhanced_v2.sh; then
            echo "status=success" >> $GITHUB_OUTPUT
            echo "quality-gates=all-passed" >> $GITHUB_OUTPUT
          else
            echo "status=failure" >> $GITHUB_OUTPUT
            echo "quality-gates=some-failed" >> $GITHUB_OUTPUT
            exit 1
          fi
        env:
          TEST_USER_ID: 'github-action-enhanced'
          TEST_BASE_URL: ${{ env.TEST_BASE_URL }}

      - name: 📊 上传增强测试报告
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: sprint1-enhanced-e2e-reports-${{ github.sha }}
          path: |
            /tmp/sprint1_e2e_v2_*.log
            /tmp/sprint1_e2e_report_v2_*.json
            test/e2e/scenarios/test_prompts.txt
          retention-days: 14

  # P0缺陷回归验证 (v3.0)
  p0-regression-testing:
    name: P0 Defect Regression Testing - AI流式响应 + 协程取消修复验证
    runs-on: ubuntu-latest
    # [FIXED] Changed the long single-line `if` condition to a multi-line block for robustness and readability.
    if: |
      (inputs.test_scope == 'p0-regression' || inputs.test_scope == 'full') ||
      (github.event_name == 'push' && contains(github.event.head_commit.message, '[p0-regression]')) ||
      contains(github.event.head_commit.message, 'fix:') ||
      contains(github.event.head_commit.message, 'hotfix:')
    needs: [sprint1-e2e]
    outputs:
      regression-status: ${{ steps.p0-test.outputs.status }}
      fixes-validated: ${{ steps.p0-test.outputs.fixes }}
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 设置JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: ${{ env.JAVA_VERSION }}
          distribution: 'temurin'

      - name: 设置JDK并配置Gradle缓存
        uses: gradle/gradle-build-action@v3
        with:
          gradle-home-cache-cleanup: true

      - name: 安装测试依赖
        run: |
          sudo apt-get update && sudo apt-get install -y bc curl jq
          # 安装adb（用于设备交互测试）
          sudo apt-get install -y android-tools-adb

      - name: 🔧 准备P0回归测试环境
        run: |
          chmod +x gradlew
          # 构建所有必要的APK
          ./gradlew assembleDebug assembleAndroidTest --no-configuration-cache
          # 确保测试脚本可执行
          chmod +x ci-scripts/sprint1_e2e_enhanced_v3.sh
          # 创建测试工件目录
          mkdir -p /tmp/p0-regression-artifacts
        env:
          GRADLE_OPTS: ${{ env.GRADLE_OPTS }}

      - name: 🔧 运行P0缺陷回归验证
        id: p0-test
        run: |
          cd ci-scripts
          echo "status=running" >> $GITHUB_OUTPUT
          # 运行P0回归测试
          if timeout ${{ env.E2E_TIMEOUT }} ./sprint1_e2e_enhanced_v3.sh; then
            echo "status=success" >> $GITHUB_OUTPUT
            echo "fixes=ai-streaming,coroutine-cancellation,timeout-handling,robustness" >> $GITHUB_OUTPUT
            echo "🎉 P0缺陷修复验证通过" >> $GITHUB_STEP_SUMMARY
            echo "- ✅ AI流式响应单条消息修复" >> $GITHUB_STEP_SUMMARY
            echo "- ✅ 协程取消正确处理修复" >> $GITHUB_STEP_SUMMARY
            echo "- ✅ 超时策略优化" >> $GITHUB_STEP_SUMMARY
            echo "- ✅ 端到端健壮性增强" >> $GITHUB_STEP_SUMMARY
          else
            echo "status=failure" >> $GITHUB_OUTPUT
            echo "fixes=incomplete" >> $GITHUB_OUTPUT
            echo "❌ P0缺陷修复验证失败" >> $GITHUB_STEP_SUMMARY
            echo "⚠️ 需要检查修复实现，确保问题真正解决" >> $GITHUB_STEP_SUMMARY
            exit 1
          fi
        env:
          TEST_USER_ID: 'github-action-p0-regression'
          TEST_BASE_URL: ${{ env.TEST_BASE_URL }}

      - name: 📊 上传P0回归测试报告
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: p0-regression-reports-${{ github.sha }}
          path: |
            /tmp/sprint1_p0_regression_*.log
            /tmp/sprint1_p0_regression_report_*.json
            /tmp/p0_performance_*.json
            /tmp/p0-regression-artifacts/*
          retention-days: 30 # P0缺陷报告保留更长时间

  # E2E测试汇总报告
  e2e-summary:
    name: E2E Testing Summary Report
    runs-on: ubuntu-latest
    needs: [sprint0-e2e, sprint1-e2e, sprint1-enhanced-e2e, p0-regression-testing]
    if: always()
    steps:
      - name: 📋 生成E2E测试汇总报告
        run: |
          echo "# 🧪 GymBro E2E测试汇总报告" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**测试时间**: $(date '+%Y-%m-%d %H:%M:%S %Z')" >> $GITHUB_STEP_SUMMARY
          echo "**测试范围**: ${{ inputs.test_scope || 'auto' }}" >> $GITHUB_STEP_SUMMARY
          echo "**触发事件**: ${{ github.event_name }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## 📊 测试结果" >> $GITHUB_STEP_SUMMARY
          echo "| 测试套件 | 状态 | 说明 |" >> $GITHUB_STEP_SUMMARY
          echo "|---------|------|------|" >> $GITHUB_STEP_SUMMARY
          # Sprint 0 结果
          if [[ "${{ needs.sprint0-e2e.result }}" == "success" ]]; then
            echo "| Sprint 0 E2E | ✅ 通过 | AI聊天→Function Call→ActiveWorkout 流程正常 |" >> $GITHUB_STEP_SUMMARY
          elif [[ "${{ needs.sprint0-e2e.result }}" == "skipped" ]]; then
            echo "| Sprint 0 E2E | ⏭️ 跳过 | 未在当前测试范围内 |" >> $GITHUB_STEP_SUMMARY
          else
            echo "| Sprint 0 E2E | ❌ 失败 | 基础流程存在问题，需要修复 |" >> $GITHUB_STEP_SUMMARY
          fi
          # Sprint 1 结果
          if [[ "${{ needs.sprint1-e2e.result }}" == "success" ]]; then
            echo "| Sprint 1 E2E | ✅ 通过 | Function Calling→训练会话→数据持久化 流程正常 |" >> $GITHUB_STEP_SUMMARY
          elif [[ "${{ needs.sprint1-e2e.result }}" == "skipped" ]]; then
            echo "| Sprint 1 E2E | ⏭️ 跳过 | 未在当前测试范围内 |" >> $GITHUB_STEP_SUMMARY
          else
            echo "| Sprint 1 E2E | ❌ 失败 | 核心功能存在问题，需要优先修复 |" >> $GITHUB_STEP_SUMMARY
          fi
          # Sprint 1 Enhanced 结果
          if [[ "${{ needs.sprint1-enhanced-e2e.result }}" == "success" ]]; then
            echo "| Sprint 1 Enhanced E2E v2.0 | ✅ 通过 | 负面测试+轮询检查+数据驱动 全部通过 |" >> $GITHUB_STEP_SUMMARY
          elif [[ "${{ needs.sprint1-enhanced-e2e.result }}" == "skipped" ]]; then
            echo "| Sprint 1 Enhanced E2E v2.0 | ⏭️ 跳过 | 未在当前测试范围内 |" >> $GITHUB_STEP_SUMMARY
          else
            echo "| Sprint 1 Enhanced E2E v2.0 | ❌ 失败 | 高级功能或边界情况存在问题 |" >> $GITHUB_STEP_SUMMARY
          fi
          # P0 回归测试结果
          if [[ "${{ needs.p0-regression-testing.result }}" == "success" ]]; then
            echo "| P0缺陷回归验证 | ✅ 通过 | AI流式响应+协程取消+超时处理+压力测试 全部通过 |" >> $GITHUB_STEP_SUMMARY
          elif [[ "${{ needs.p0-regression-testing.result }}" == "skipped" ]]; then
            echo "| P0缺陷回归验证 | ⏭️ 跳过 | 未在当前测试范围内 |" >> $GITHUB_STEP_SUMMARY
          else
            echo "| P0缺陷回归验证 | ❌ 失败 | P0级缺陷修复验证失败，需要立即修复 |" >> $GITHUB_STEP_SUMMARY
          fi
          echo "" >> $GITHUB_STEP_SUMMARY
          # 总结状态
          if [[ "${{ needs.sprint0-e2e.result }}" != "failure" &&
                "${{ needs.sprint1-e2e.result }}" != "failure" &&
                "${{ needs.sprint1-enhanced-e2e.result }}" != "failure" &&
                "${{ needs.p0-regression-testing.result }}" != "failure" ]]; then
            echo "## ✅ 总体状态：E2E测试通过" >> $GITHUB_STEP_SUMMARY
            echo "🎉 所有E2E测试套件都通过了验证，GymBro核心流程运行正常！" >> $GITHUB_STEP_SUMMARY
            echo "🔧 P0级缺陷修复也已验证通过，产品健壮性达标！" >> $GITHUB_STEP_SUMMARY
          else
            echo "## ⚠️ 总体状态：E2E测试存在失败" >> $GITHUB_STEP_SUMMARY
            echo "🔧 请检查失败的测试套件并修复相关问题。" >> $GITHUB_STEP_SUMMARY
            if [[ "${{ needs.p0-regression-testing.result }}" == "failure" ]]; then
              echo "⚠️ 特别注意：P0级缺陷回归验证失败，需要立即修复！" >> $GITHUB_STEP_SUMMARY
            fi
          fi
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## 📖 下一步行动" >> $GITHUB_STEP_SUMMARY
          echo "- 查看各测试套件的详细日志和报告" >> $GITHUB_STEP_SUMMARY
          echo "- 对于失败的测试，请参考对应的实现指南修复" >> $GITHUB_STEP_SUMMARY
          echo "- 成功的测试表明对应的功能模块工作正常" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## 🔗 相关链接" >> $GITHUB_STEP_SUMMARY
          echo "- [Action运行详情](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})" >> $GITHUB_STEP_SUMMARY
          echo "- [实现指南](../IMPLEMENTATION_GUIDES_OVERVIEW.md)" >> $GITHUB_STEP_SUMMARY

      - name: 设置E2E测试状态
        uses: actions/github-script@v7
        if: github.event_name == 'pull_request'
        with:
          script: |
            const { owner, repo } = context.repo;
            const sha = context.sha;
            // 计算总体状态
            const results = {
              sprint0: '${{ needs.sprint0-e2e.result }}',
              sprint1: '${{ needs.sprint1-e2e.result }}',
              enhanced: '${{ needs.sprint1-enhanced-e2e.result }}',
              p0_regression: '${{ needs.p0-regression-testing.result }}'
            };
            const hasFailures = Object.values(results).includes('failure');
            const state = hasFailures ? 'failure' : 'success';
            const description = hasFailures
              ? 'E2E测试存在失败，请检查详细报告'
              : 'E2E测试全部通过';
            await github.rest.repos.createCommitStatus({
              owner,
              repo,
              sha,
              state,
              description,
              context: 'ci/e2e-testing',
              target_url: `https://github.com/${owner}/${repo}/actions/runs/${{ github.run_id }}`
            });
