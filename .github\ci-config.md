# GymBro CI/CD 配置指南 (MVP版本)

## 🎯 MVP 原则
专注于核心功能：APP构建运行 + coach/workout模块 + 代码质量
暂时禁用：Firebase服务、Google Play发布等服务器相关功能

## 🔐 MVP 必需的GitHub Secrets

### 基础配置（MVP最小化）
在GitHub仓库的 Settings > Secrets and variables > Actions 中添加以下secrets：

#### Android构建相关（可选，用于Release构建）
```
ANDROID_SIGNING_KEY          # Android签名密钥的Base64编码（Release时需要）
ANDROID_KEY_ALIAS           # 密钥别名（Release时需要）
ANDROID_KEYSTORE_PASSWORD   # Keystore密码（Release时需要）
ANDROID_KEY_PASSWORD        # 密钥密码（Release时需要）
```

#### API密钥（MVP暂时不需要）
```
# MVP阶段暂时不需要配置，使用mock配置
# GYMBRO_API_KEY              # GymBro后端API密钥
# FIREBASE_API_KEY            # Firebase API密钥
```

#### Firebase配置（MVP使用mock）
```
# MVP阶段使用mock配置，不需要真实Firebase
# FIREBASE_PROJECT_ID         # Firebase项目ID
# FIREBASE_SERVICE_ACCOUNT    # Firebase服务账号JSON（完整内容）
```

#### Google Play发布（MVP禁用）
```
# MVP阶段完全禁用Google Play发布
# GOOGLE_PLAY_SERVICE_ACCOUNT # Google Play服务账号JSON（完整内容）
```

#### 可选配置（MVP暂时不需要）
```
# SLACK_WEBHOOK               # Slack通知webhook（可选）
# SONAR_TOKEN                # SonarQube token（企业版需要）
```

## 🏗️ 环境配置

### GitHub Environments
在 Settings > Environments 中创建以下环境：

#### 1. internal
- **用途**: 内测版本部署
- **保护规则**: 无需审批
- **环境变量**:
  - `FIREBASE_APP_ID`: Firebase应用ID (启用自动APK分发)
- **Secrets**: 继承仓库级别的所有secrets

#### 2. alpha
- **用途**: Alpha版本部署
- **保护规则**: 可选择需要审批
- **环境变量**:
  - `TRACK=alpha`

#### 3. beta
- **用途**: Beta版本部署
- **保护规则**: 建议需要审批
- **环境变量**:
  - `TRACK=beta`

#### 4. production
- **用途**: 生产版本部署
- **保护规则**: 必须需要审批
- **环境变量**:
  - `TRACK=production`

## 📋 MVP工作流程说明

### 1. pr-validation.yml - PR验证流水线 (MVP优化)
**触发条件**:
- Pull Request到main/master/develop分支

**MVP功能**:
- 模块化变更检测（重点关注coach/workout）
- 并行代码质量检查（Ktlint + Detekt + Android Lint）
- UI质量验证脚本
- 增量单元测试（专注核心模块）
- 增量构建验证
- 使用mock Firebase配置，避免服务器依赖
- 目标时间: < 5分钟

### 2. develop-integration.yml - 分支集成流水线 (MVP优化)
**触发条件**:
- Push到develop/master分支

**MVP功能**:
- 完整CI检查流程（ciCheck）
- 全量单元测试（testAll）
- 测试覆盖率检查（coverageCheck）
- Paparazzi快照测试
- ~~Firebase App Distribution自动分发~~ (MVP禁用)
- 使用mock Firebase配置
- 目标时间: < 10分钟

### 3. release-pipeline.yml - 发布流水线 (MVP简化)
**触发条件**:
- GitHub Release发布
- 手动触发

**MVP功能**:
- 发布前验证和版本检查
- ~~Firebase Test Lab冒烟测试~~ (MVP禁用)
- 构建签名的APK和AAB
- ~~Google Play自动部署~~ (MVP禁用)
- GitHub Release创建
- 使用mock Firebase配置
- 目标时间: < 10分钟

### 4. nightly.yml - 夜间回归测试
**触发条件**:
- 定时触发（每日2:00 UTC）
- 手动触发

**主要功能**:
- CodeQL安全扫描
- OWASP依赖安全检查
- 全量测试覆盖率分析
- Firebase Test Lab全量回归测试
- 触发E2E测试（完整场景）
- Slack通知集成
- 目标时间: < 30分钟

### 5. e2e-testing.yml - E2E端到端测试流水线 (新增)
**触发条件**:
- Push到master/develop分支（核心功能变更）
- Pull Request（核心功能变更）
- 定时触发（每日4:00 UTC）
- 手动触发（支持测试范围选择）

**主要功能**:
- **Sprint 0 E2E**: AI聊天 → Function Call → Activeworkout 基础流程
- **Sprint 1 E2E**: Function Calling → 训练会话 → 数据持久化 完整闭环
- **Sprint 1 Enhanced E2E v2.0**: 负面测试 + 轮询检查 + 数据驱动验证
- 支持多种测试范围（sprint0/sprint1/sprint1-enhanced/full）
- 集成ci-scripts中的现有测试脚本
- 目标时间: < 45分钟（全套）

### 6. benchmark-ci.yml - 性能基准测试
**触发条件**:
- Push到master/develop分支（性能相关变更）
- Pull Request（性能相关变更）
- 定时触发（每日10:00 UTC）

**主要功能**:
- Macrobenchmark性能测试
- 启动时间基准验证
- UI响应性能测试
- 性能回归检测
- 目标时间: < 20分钟

## 🔧 本地开发配置

### 1. 创建local.properties
```properties
sdk.dir=/path/to/android/sdk
GYMBRO_API_KEY=your_api_key_here
FIREBASE_API_KEY=your_firebase_key_here
```

### 2. 签名配置
项目已配置自动签名管理，支持环境变量和local.properties：
```properties
# local.properties中添加
KEYSTORE_FILE=keystore/gymbro-debug.jks
KEYSTORE_PASSWORD=gymbro123
KEY_ALIAS=gymbro-debug
KEY_PASSWORD=gymbro123
```

生产环境通过GitHub Secrets配置：
```
ANDROID_SIGNING_KEY          # Keystore文件Base64编码
ANDROID_KEY_ALIAS           # 密钥别名
ANDROID_KEYSTORE_PASSWORD   # Keystore密码
ANDROID_KEY_PASSWORD        # 密钥密码
```
```

## 📱 Google Play Console配置

### 1. 创建服务账号
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用Google Play Android Developer API
4. 创建服务账号并下载JSON密钥文件
5. 在Google Play Console中授权服务账号

### 2. 配置发布轨道
在Google Play Console中设置：
- **Internal testing**: 内部测试
- **Alpha**: 封闭测试
- **Beta**: 开放测试
- **Production**: 生产发布

## 🚀 MVP快速开始

### 1. 快速MVP检查（推荐）
```batch
# Windows快速检查
scripts\quick-mvp-check.bat

# 或PowerShell综合验证
powershell -ExecutionPolicy Bypass -File scripts\comprehensive-mvp-validation.ps1
```

### 2. ACT本地CI/CD验证（推荐）
```batch
# 使用ACT工具本地验证CI/CD流程
scripts\run-act-mvp.bat

# 或PowerShell ACT验证
powershell -ExecutionPolicy Bypass -File scripts\act-mvp-validation.ps1
```

### 3. Domain+Data重点验证
```powershell
# 专门针对核心模块的深度验证
powershell -ExecutionPolicy Bypass -File scripts\domain-data-focus-test.ps1
```

### 4. 传统验证方式（可选）
```bash
# 配置验证
chmod +x scripts/verify-cicd-config.sh
./scripts/verify-cicd-config.sh

# 本地测试
chmod +x scripts/test-cicd-quick.sh
./scripts/test-cicd-quick.sh
```

### 5. 配置Firebase App Distribution (MVP暂时禁用)
在GitHub仓库 Settings > Environments > internal 中添加变量：
- `FIREBASE_APP_ID`: 您的Firebase应用ID（MVP阶段暂不需要）

## 📖 使用指南

### 日常开发流程
1. **创建功能分支**: `git checkout -b feature/新功能名`
2. **开发并提交**: 遵循Conventional Commits规范
3. **创建PR**: 自动触发pr-checks.yml
4. **代码审查**: 通过所有检查后进行审查
5. **合并到develop**: 触发basic-ci-cd.yml

### 发布流程
1. **合并到main**: 从develop合并到main
2. **手动发布**: 使用release.yml手动触发
3. **创建Release**: 生产版本会自动创建GitHub Release

### 紧急修复流程
1. **创建hotfix分支**: `git checkout -b hotfix/修复名`
2. **快速修复**: 最小化变更
3. **直接发布**: 使用release.yml快速部署

## 🔍 故障排除

### 常见问题

#### 1. 构建失败
- 检查Gradle版本兼容性
- 确认所有依赖版本正确
- 查看构建日志中的具体错误

#### 2. 签名失败
- 验证签名密钥是否正确
- 检查密码是否匹配
- 确认密钥别名正确

#### 3. 部署失败
- 验证Google Play服务账号权限
- 检查包名是否匹配
- 确认版本号递增

#### 4. 测试失败
- 检查测试环境配置
- 确认模拟器版本兼容
- 查看测试日志

### 调试技巧
1. **本地复现**: 在本地运行相同的Gradle命令
2. **查看日志**: 详细查看GitHub Actions日志
3. **分步调试**: 注释部分步骤逐步排查
4. **环境对比**: 对比本地和CI环境差异

## 📊 监控和优化

### 性能监控
- 构建时间趋势
- APK大小变化
- 测试覆盖率
- 代码质量指标

### 优化建议
1. **缓存优化**: 合理使用Gradle缓存
2. **并行构建**: 利用矩阵策略并行执行
3. **增量构建**: 仅构建变更的模块
4. **资源优化**: 定期清理不必要的依赖

## 🔗 相关文档
- [GitHub Actions文档](https://docs.github.com/en/actions)
- [Google Play发布API](https://developers.google.com/android-publisher)
- [Android签名指南](https://developer.android.com/studio/publish/app-signing)
- [Gradle构建优化](https://docs.gradle.org/current/userguide/performance.html)
