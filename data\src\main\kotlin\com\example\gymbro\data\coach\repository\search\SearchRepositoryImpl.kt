package com.example.gymbro.data.coach.repository.search

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.ml.embedding.EmbeddingEngine
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.coach.mapper.SearchMapper
import com.example.gymbro.data.local.dao.SearchDao
import com.example.gymbro.domain.model.search.SearchQuery
import com.example.gymbro.domain.model.search.SearchResult
import com.example.gymbro.domain.repository.SearchRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 搜索仓库实现
 *
 * 集成向量化模型和全文搜索，提供混合搜索能力
 * 基于bge.md的架构设计，实现本地向量化搜索
 */
@Singleton
class SearchRepositoryImpl
@Inject
constructor(
    private val searchDao: SearchDao,
    private val embeddingEngine: EmbeddingEngine,
    private val searchMapper: SearchMapper,
) : SearchRepository {
    private companion object {
        const val DEFAULT_SEMANTIC_WEIGHT = 0.4f
        const val DEFAULT_SEARCH_LIMIT = 40
        const val BATCH_SIZE = 16
    }

    override fun hybridSearch(query: SearchQuery): Flow<ModernResult<List<SearchResult>>> =
        flow {
            emit(ModernResult.Loading)

            try {
                // 1. 并行执行关键词和语义搜索
                val keywordResults = keywordSearch(query)
                val semanticResults = vectorSearch(query)

                // 2. 合并结果
                val combinedResults =
                    when {
                        keywordResults.isError && semanticResults.isError -> {
                            ModernResult.error(
                                DataErrors.DataError.query(
                                    operationName = "SearchRepositoryImpl.hybridSearch",
                                    message = UiText.DynamicString("关键词和语义搜索都失败"),
                                    entityType = "SearchResult",
                                    metadataMap =
                                    mapOf(
                                        "searchType" to "hybrid",
                                        "errorSubtype" to "SEARCH_ENGINE_FAILURE",
                                    ),
                                ),
                            )
                        }
                        else -> {
                            val keywordList = keywordResults.getOrNull() ?: emptyList()
                            val semanticList = semanticResults.getOrNull() ?: emptyList()

                            val merged =
                                combineResults(
                                    keywordList,
                                    semanticList,
                                    query.semanticWeight ?: DEFAULT_SEMANTIC_WEIGHT,
                                )
                            ModernResult.success(merged)
                        }
                    }

                emit(combinedResults)
            } catch (e: Exception) {
                Timber.e(e, "混合搜索失败: query=${query.text}")
                emit(
                    ModernResult.error(
                        DataErrors.DataError.query(
                            operationName = "SearchRepositoryImpl.hybridSearch",
                            message = UiText.DynamicString("搜索执行失败: ${e.message}"),
                            cause = e,
                            entityType = "SearchResult",
                            metadataMap =
                            mapOf(
                                "searchType" to "hybrid",
                                "errorSubtype" to "SEARCH_ENGINE_FAILURE",
                            ),
                        ),
                    ),
                )
            }
        }.catch { throwable ->
            emit(
                ModernResult.error(
                    DataErrors.DataError.query(
                        operationName = "SearchRepositoryImpl.hybridSearch.catch",
                        message = UiText.DynamicString("搜索流异常: ${throwable.message}"),
                        cause = throwable as? Exception,
                        entityType = "SearchResult",
                        metadataMap =
                        mapOf(
                            "searchType" to "hybrid",
                            "errorSubtype" to "SEARCH_ENGINE_FAILURE",
                        ),
                    ),
                ),
            )
        }

    override suspend fun vectorSearch(query: SearchQuery): ModernResult<List<SearchResult>> =
        withContext(Dispatchers.IO) {
            try {
                // 1. 生成查询向量
                val queryVector =
                    withContext(Dispatchers.Default) {
                        embeddingEngine.embed(query.text)
                    }

                // 2. VSS搜索
                val vssHits =
                    searchDao.vectorSearch(
                        queryVector = queryVector,
                        limit = query.limit ?: DEFAULT_SEARCH_LIMIT,
                    )

                // 3. 获取详细信息
                val detailIds = vssHits.map { it.id }
                val details =
                    if (detailIds.isNotEmpty()) {
                        searchDao.getSearchDetails(detailIds)
                    } else {
                        emptyList()
                    }

                // 4. 映射为领域模型
                val results = searchMapper.mapVssToSearchResults(vssHits, details)

                ModernResult.success(results)
            } catch (e: Exception) {
                Timber.e(e, "向量搜索失败: query=${query.text}")
                ModernResult.error(
                    DataErrors.DataError.query(
                        operationName = "SearchRepositoryImpl.vectorSearch",
                        message = UiText.DynamicString("向量搜索执行失败: ${e.message}"),
                        cause = e,
                        entityType = "SearchResult",
                        metadataMap =
                        mapOf(
                            "searchType" to "vector",
                            "errorSubtype" to "VECTOR_SEARCH_FAILURE",
                        ),
                    ),
                )
            }
        }

    override suspend fun keywordSearch(query: SearchQuery): ModernResult<List<SearchResult>> =
        withContext(Dispatchers.IO) {
            try {
                // 1. FTS5全文搜索
                val ftsHits =
                    searchDao.fullTextSearch(
                        query = query.text,
                        limit = query.limit ?: DEFAULT_SEARCH_LIMIT,
                    )

                // 2. 获取详细信息
                val detailIds = ftsHits.map { it.id }
                val details =
                    if (detailIds.isNotEmpty()) {
                        searchDao.getSearchDetails(detailIds)
                    } else {
                        emptyList()
                    }

                // 3. 映射为领域模型
                val results = searchMapper.mapToSearchResults(ftsHits, details)

                ModernResult.success(results)
            } catch (e: Exception) {
                Timber.e(e, "关键词搜索失败: query=${query.text}")
                ModernResult.error(
                    DataErrors.DataError.query(
                        operationName = "SearchRepositoryImpl.keywordSearch",
                        message = UiText.DynamicString("关键词搜索执行失败: ${e.message}"),
                        cause = e,
                        entityType = "SearchResult",
                        metadataMap =
                        mapOf(
                            "searchType" to "keyword",
                            "errorSubtype" to "KEYWORD_SEARCH_FAILURE",
                        ),
                    ),
                )
            }
        }

    override suspend fun indexContent(
        content: String,
        metadata: Map<String, Any>,
    ): ModernResult<Unit> =
        withContext(Dispatchers.IO) {
            try {
                // 1. 生成内容向量
                val contentVector =
                    withContext(Dispatchers.Default) {
                        embeddingEngine.embed(content)
                    }

                // 2. 插入到数据库
                searchDao.insertSearchableContent(
                    content = content,
                    vector = contentVector,
                    metadata = metadata,
                )

                ModernResult.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "内容索引失败: content length=${content.length}")
                ModernResult.error(
                    DataErrors.DataError.create(
                        operationName = "SearchRepositoryImpl.indexContent",
                        message = UiText.DynamicString("内容索引失败: ${e.message}"),
                        cause = e,
                        entityType = "SearchContent",
                        metadataMap =
                        mapOf(
                            "contentLength" to content.length,
                            "errorSubtype" to "INDEXING_FAILURE",
                        ),
                    ),
                )
            }
        }

    override suspend fun indexBatch(
        contents: List<Pair<String, Map<String, Any>>>,
    ): ModernResult<Unit> =
        withContext(Dispatchers.IO) {
            try {
                // 1. 批量生成向量
                val texts = contents.map { it.first }
                val vectors =
                    withContext(Dispatchers.Default) {
                        embeddingEngine.embedBatch(texts)
                    }

                // 2. 批量插入
                val indexData =
                    contents.mapIndexed { index, (content, metadata) ->
                        Triple(content, vectors[index], metadata)
                    }

                searchDao.insertSearchableContentFromIndexData(indexData)

                ModernResult.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "批量索引失败: batch size=${contents.size}")
                ModernResult.error(
                    DataErrors.DataError.create(
                        operationName = "SearchRepositoryImpl.indexBatch",
                        message = UiText.DynamicString("批量索引失败: ${e.message}"),
                        cause = e,
                        entityType = "SearchContent",
                        metadataMap =
                        mapOf(
                            "batchSize" to contents.size,
                            "errorSubtype" to "INDEXING_FAILURE",
                        ),
                    ),
                )
            }
        }

    override suspend fun getSearchSuggestions(prefix: String): ModernResult<List<String>> =
        withContext(Dispatchers.IO) {
            try {
                val suggestions = searchDao.getSearchSuggestions(prefix)
                ModernResult.success(suggestions)
            } catch (e: Exception) {
                Timber.e(e, "获取搜索建议失败: prefix=$prefix")
                ModernResult.error(
                    DataErrors.DataError.query(
                        operationName = "SearchRepositoryImpl.getSearchSuggestions",
                        message = UiText.DynamicString("搜索建议获取失败: ${e.message}"),
                        cause = e,
                        entityType = "SearchSuggestion",
                        metadataMap =
                        mapOf(
                            "prefix" to prefix,
                            "errorSubtype" to "SEARCH_ENGINE_FAILURE",
                        ),
                    ),
                )
            }
        }

    override suspend fun cleanupOldIndices(daysToKeep: Int): ModernResult<Unit> =
        withContext(Dispatchers.IO) {
            try {
                searchDao.cleanupOldSearchData(daysToKeep)
                ModernResult.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "清理旧索引失败: daysToKeep=$daysToKeep")
                ModernResult.error(
                    DataErrors.DataError.update(
                        operationName = "SearchRepositoryImpl.cleanupOldIndices",
                        message = UiText.DynamicString("索引清理失败: ${e.message}"),
                        cause = e,
                        entityType = "SearchIndex",
                        metadataMap =
                        mapOf(
                            "daysToKeep" to daysToKeep,
                            "errorSubtype" to "INDEXING_FAILURE",
                        ),
                    ),
                )
            }
        }

    /**
     * 合并关键词和语义搜索结果
     * 根据bge.md的算法实现融合排序
     */
    private fun combineResults(
        keywordResults: List<SearchResult>,
        semanticResults: List<SearchResult>,
        semanticWeight: Float,
    ): List<SearchResult> {
        // 创建ID到结果的映射
        val keywordMap = keywordResults.associateBy { it.id }
        val semanticMap = semanticResults.associateBy { it.id }
        val allIds = (keywordMap.keys + semanticMap.keys).distinct()

        // 合并和重新打分
        val combinedResults =
            allIds.mapNotNull { id ->
                val keywordResult = keywordMap[id]
                val semanticResult = semanticMap[id]

                when {
                    keywordResult != null && semanticResult != null -> {
                        // 同时在两个结果中，合并分数
                        val combinedScore =
                            (1 - semanticWeight) * keywordResult.score +
                                semanticWeight * semanticResult.score
                        keywordResult.copy(
                            score = combinedScore,
                            matchDetails =
                            keywordResult.matchDetails?.copy(
                                semanticScore = semanticResult.score,
                            ),
                        )
                    }
                    keywordResult != null -> {
                        // 只在关键词结果中
                        keywordResult.copy(score = (1 - semanticWeight) * keywordResult.score)
                    }
                    semanticResult != null -> {
                        // 只在语义结果中
                        semanticResult.copy(score = semanticWeight * semanticResult.score)
                    }
                    else -> null
                }
            }

        // 按分数排序并返回
        return combinedResults
            .sortedByDescending { it.score }
            .take(DEFAULT_SEARCH_LIMIT)
    }
}
