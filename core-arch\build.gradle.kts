plugins {
    id("gymbro.android.library")
    id("gymbro.compose.library")
    id("gymbro.hilt.library")
    id("gymbro.testing.library")
    id("org.jetbrains.kotlin.plugin.compose")
}

android {
    namespace = "com.example.gymbro.core.arch"
    compileSdk = libs.versions.compileSdk.get().toInt()

    defaultConfig {
        minSdk = libs.versions.minSdk.get().toInt()
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = libs.versions.jvmTarget.get()
    }

    buildFeatures {
        compose = true
        buildConfig = false
    }

    lint {
        abortOnError = false
    }
}

dependencies {
    // Core modules
    implementation(project(":core"))
    implementation(project(":domain"))

    // Kotlin 协程 - 使用 libs.versions.toml 中的版本
    implementation(libs.kotlinx.coroutines.core)
    implementation(libs.kotlinx.coroutines.android)

    // Kotlin 反射 - 用于 BaseMviViewModel 的智能加载状态检测
    implementation(libs.kotlin.reflect)

    // ViewModel 和 Lifecycle - 使用 libs.versions.toml 中的版本
    implementation(libs.androidx.lifecycle.viewmodel.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)

    // Compose Dependencies
    implementation(platform(libs.compose.bom)) // Import Compose BOM
    implementation(libs.compose.runtime) // Essential for @Composable and CompositionLocal
    implementation(libs.androidx.lifecycle.viewmodel.compose)
    implementation(libs.androidx.lifecycle.runtime.compose)

    // 日志 - 使用 libs.versions.toml 中的版本
    implementation(libs.timber)

    // Hilt依赖 - 使用 libs.versions.toml 中的版本
    implementation(libs.hilt.android)
    ksp(libs.hilt.compiler)
}
