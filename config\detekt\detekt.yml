# GymBro项目Detekt配置文件 v2.0
# 已根据 GymBro MVI 黄金标准 v6.23 进行强化

build:
  maxIssues: 0
  excludeCorrectable: false

config:
  validation: true
  # 在CI环境中，我们希望将警告视为错误，以保证代码质量
  warningsAsErrors: true
  checkExhaustiveness: true

# 保持您原有的报告和处理器配置
processors:
  active: true
  exclude: ['DetektProgressListener']
console-reports:
  active: true
  exclude: ['ProjectStatisticsReport', 'ComplexityReport', 'NotificationReport', 'FindingsReport', 'FileBasedFindingsReport']
output-reports:
  active: true
  exclude: ['TxtOutputReport', 'XmlOutputReport', 'HtmlOutputReport', 'MdOutputReport']

# MVI 架构与 Clean Architecture 强制规则
# =================================================
# 以下规则是为保证 MVI 模式正确实施而特别配置的

naming:
  active: true
  # MVI 黄金标准：强制类名符合 MVI 模式
  # 例如：FeatureViewModel, FeatureState, FeatureIntent, FeatureEffect, FeatureReducer, FeatureEffectHandler
  ClassNaming:
    active: true
    # 这个正则表达式要求类名以特定的 MVI 后缀结尾，或者是一些通用类的后缀
    classPattern: '[A-Z][a-zA-Z0-9]*(ViewModel|State|Intent|Effect|Reducer|EffectHandler|UseCase|Repository|Source|Dao|Dto|Entity|Screen|Dialog|Activity|Application)$'
    # 排除测试文件和 DTOs
    excludeClassPattern: '.*(Test|Tests|Spec|Fake|Mock)$'

  # MVI 黄金标准：Intent 必须是动词或动名词，结果必须以 Result 结尾
  # Detekt 无法直接检查 Intent 内容，但我们可以规范函数命名
  FunctionNaming:
    active: true
    functionPattern: '[a-z][a-zA-Z0-9]*'
    # 允许 Composable 函数使用大写字母开头
    ignoreAnnotated: ['Composable']
    # 排除测试代码
    excludes: ['**/test/**', '**/androidTest/**']

  # MVI 黄金标准：State 中的属性应该是名词
  VariableNaming:
    active: true
    variablePattern: '[a-z][A-Za-z0-9]*'
    privateVariablePattern: '(_)?[a-z][A-Za-z0-9]*'

style:
  active: true
  # MVI 黄金标准：State 必须是不可变的
  # 1. 强制使用 data class
  UseDataClass:
    active: true
    # 2. 不允许在 data class 中使用 var
    allowVars: false
  # 3. 禁止在 State 中使用可变集合
  # (由 potential-bugs:DoubleMutabilityForCollection 规则覆盖)

  # MVI 黄金标准：Reducer 是纯函数，EffectHandler 处理副作用
  # 此规则防止在不应该出现的地方（如 Reducer）执行网络或数据库调用
  ForbiddenImport:
    active: true
    imports:
      # Reducer 和 State 不应处理 IO 操作
      - 'kotlinx.coroutines.flow.collect'
      - 'retrofit2.*'
      - 'androidx.room.*'
    forbiddenPatterns:
      # 正则表达式，用于禁止在 Reducer 或 State 文件中导入数据层或网络层
      - 'import com.example.gymbro.data.*'
      - 'import com.example.gymbro.core.network.*'

  # MVI 黄金标准：ViewModel 不直接处理 Effect，而是交给 EffectHandler
  ForbiddenMethodCall:
    active: true
    methods:
      - 'kotlin.io.print'
      - 'kotlin.io.println'
      # 禁止在 ViewModel 中直接调用 .collect() 来处理副作用
      - value: 'kotlinx.coroutines.flow.Flow.collect'
        reason: 'ViewModel不应直接收集Flow来处理副作用，请将Effect发送给EffectHandler。'

  # MVI 黄金标准：禁止硬编码 TODO，强制使用规范化任务管理
  ForbiddenComment:
    active: true
    comments:
      - 'FIXME:'
      - 'STOPSHIP:'
      - 'TODO:'
    allowedPatterns: ''

  # 确保代码整洁，行长度与团队规范一致
  MaxLineLength:
    active: true
    maxLineLength: 120
    excludePackageStatements: true
    excludeImportStatements: true

  # 强制使用 val，除非明确需要 var，这与 State 的不可变性原则一致
  VarCouldBeVal:
    active: true
    ignoreLateinitVar: true

  # 禁止使用通配符导入，以保持依赖清晰
  WildcardImport:
    active: true
    excludeImports:
      - 'java.util.*'
      - 'kotlinx.android.synthetic.*'

complexity:
  active: true
  # MVI 黄金标准：Reducer 必须是简单的纯函数，复杂度必须极低
  CyclomaticComplexMethod:
    active: true
    # 全局阈值
    threshold: 15
    # 对 Reducer 的 reduce 方法设置更严格的阈值
    rules:
      - signatures:
          - "**.reduce(..)"
        threshold: 5

  # MVI 黄金标准：ViewModel 应该只做协调工作，不应有复杂逻辑
  LargeClass:
    active: true
    # ViewModel 不应超过 200 行
    rules:
      - signatures:
          - "*ViewModel"
        threshold: 200
    # 其他类的通用阈值
    threshold: 600

  # LongParameterList 对于 State 和 UseCase 可能是正常的，但对于其他函数则不是
  LongParameterList:
    active: true
    functionThreshold: 6
    constructorThreshold: 8
    # 允许数据类和带有注解的构造函数有更多参数
    ignoreDataClasses: true
    ignoreAnnotatedParameter: ['Inject']

potential-bugs:
  active: true
  # MVI 黄金标准：State 中禁止使用可变集合，防止意外修改
  DoubleMutabilityForCollection:
    active: true
    mutableTypes:
      - 'kotlin.collections.MutableList'
      - 'kotlin.collections.MutableMap'
      - 'kotlin.collections.MutableSet'
      - 'java.util.ArrayList'
      - 'java.util.LinkedHashSet'
      - 'java.util.HashSet'
      - 'java.util.LinkedHashMap'
      - 'java.util.HashMap'

  # 配合 MVI 的 State，避免在 ViewModel 之外使用 lateinit
  LateinitUsage:
    active: true
    # 允许在测试和 Dagger/Hilt 组件中使用
    excludes: ['**/test/**', '**/androidTest/**']
    ignoreOnClassesPattern: '.*(Hilt|Dagger|Component).*'

# =================================================
# 通用代码质量规则 (保留并微调您的原有配置)
# =================================================

comments:
  active: true
  # 在团队协作中，建议开启以下规则以保证文档覆盖率
  UndocumentedPublicClass:
    active: false # 建议开启
  UndocumentedPublicFunction:
    active: false # 建议开启

coroutines:
  active: true
  # 强制依赖注入 Dispatcher，便于测试
  InjectDispatcher:
    active: true
  # 禁止在生产代码中使用 GlobalScope
  GlobalCoroutineUsage:
    active: true

empty-blocks:
  active: true
  # 空的函数块通常是代码异味，除非是重写的方法
  EmptyFunctionBlock:
    active: true
    ignoreOverridden: true

exceptions:
  active: true
  # 避免捕获过于宽泛的异常
  TooGenericExceptionCaught:
    active: true
    allowedExceptionNameRegex: '_|(ignore|expected).*'
    exceptionNames:
      - 'Error'
      - 'Exception'
      - 'RuntimeException'
      - 'Throwable'
  # 避免抛出过于宽泛的异常
  TooGenericExceptionThrown:
    active: true
    exceptionNames:
      - 'Error'
      - 'Exception'
      - 'RuntimeException'
      - 'Throwable'

performance:
  active: true
  # 在循环中应避免不必要的对象创建
  ForEachOnRange:
    active: true
  # 使用扩展操作符可能带来性能开销
  SpreadOperator:
    active: true
