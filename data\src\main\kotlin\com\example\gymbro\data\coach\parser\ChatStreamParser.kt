package com.example.gymbro.data.coach.parser

import com.example.gymbro.data.coach.model.DataStreamEvent
import com.example.gymbro.domain.coach.model.StreamEvent
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonArray
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 聊天流解析器 - v5.0-GOLD标准实现
 *
 * 核心功能：
 * 1. 解析SSE响应中的哨兵标签（<step>、</step>、<final>、</final>）
 * 2. 将标签转换为对应的StreamEvent
 * 3. 处理混合内容（标签+文本）
 * 4. 容错处理，确保应用不会因解析错误而崩溃
 *
 * 设计原则：
 * 1. 无状态设计 - Parser本身不维护状态，状态由调用方传入
 * 2. 容错优先 - 解析失败时返回Chunk事件而不是抛异常
 * 3. 性能优化 - 使用预编译正则表达式
 * 4. 完整上下文 - 所有事件都包含完整的ID信息
 *
 * @since Phase 1 - Prompt-Tagged Stream实现
 */
@Singleton
class ChatStreamParser
@Inject
constructor() {
    companion object {
        // 🎯 Task1精简：保留正则表达式用于移除标签，但不再解析内容
        private val STEP_START_REGEX = """<step\s+id="[^"]+"\s+title="[^"]+">""".toRegex()
        private val STEP_END_REGEX = """</step>""".toRegex()
        private val FINAL_START_REGEX = """<final\s+type="[^"]+">""".toRegex()
        private val FINAL_END_REGEX = """</final>""".toRegex()

        // 🔥 新增：<think>标签过滤正则
        private val THINK_START_REGEX = """<think>""".toRegex()
        private val THINK_END_REGEX = """</think>""".toRegex()
        private val THINK_CONTENT_REGEX = """<think>.*?</think>""".toRegex(RegexOption.DOT_MATCHES_ALL)
    }

    // JSON解析器实例
    private val json =
        Json {
            ignoreUnknownKeys = true
            isLenient = true
        }

    /**
     * 🔧 WebSocket响应解析：解析LlmStreamClient返回的原始字符串
     *
     * 🚨 修复：正确解析JSON中的reasoning_content和content字段
     *
     * @param rawResponse WebSocket返回的原始响应字符串
     * @return DataStreamEvent列表
     */
    fun parseStreamResponse(rawResponse: String): List<DataStreamEvent> {
        try {
            if (rawResponse.isBlank()) {
                return emptyList()
            }

            // 检查是否为完成标记
            if (rawResponse.trim() == "[DONE]") {
                return listOf(DataStreamEvent.Done(finishReason = "stop"))
            }

            // 🔥 修复核心问题：正确解析JSON响应
            return try {
                val jsonElement = json.parseToJsonElement(rawResponse)
                val jsonObject = jsonElement.jsonObject

                // 检查是否有finish_reason
                val choices = jsonObject["choices"]?.jsonArray
                val firstChoice = choices?.firstOrNull()?.jsonObject
                val finishReason = firstChoice?.get("finish_reason")?.jsonPrimitive?.content

                if (finishReason != null) {
                    return listOf(DataStreamEvent.Done(finishReason = finishReason))
                }

                // 解析delta中的content和reasoning_content
                val delta = firstChoice?.get("delta")?.jsonObject
                val content = delta?.get("content")?.jsonPrimitive?.content
                val reasoningContent = delta?.get("reasoning_content")?.jsonPrimitive?.content

                // 🔥 关键修复：正确处理null值和"null"字符串
                val actualContent =
                    when {
                        content != null && content != "null" && content.isNotEmpty() -> content
                        reasoningContent != null && reasoningContent != "null" && reasoningContent.isNotEmpty() -> reasoningContent
                        else -> ""
                    }

                if (actualContent.isNotEmpty()) {
                    // 🔥 【修复】不再在ChatStreamParser中过滤<think>标签
                    // 让ThinkingBox模块统一处理所有标签解析，避免重复处理
                    Timber.d("🔥 ChatStreamParser创建原始Chunk事件: content='${actualContent.take(50)}...'")
                    listOf(DataStreamEvent.Chunk(content = actualContent))
                } else {
                    Timber.v("📡 跳过空内容: content='$content', reasoning='$reasoningContent'")
                    emptyList()
                }
            } catch (jsonException: Exception) {
                // 如果不是JSON，可能是纯文本，直接作为内容处理
                println("📡 ChatStreamParser非JSON响应，作为纯文本处理: '${rawResponse.take(50)}...'")
                if (rawResponse.trim().isNotEmpty()) {
                    println("🔥 ChatStreamParser创建纯文本Chunk事件: content='${rawResponse.trim()}'")
                    listOf(DataStreamEvent.Chunk(content = rawResponse.trim()))
                } else {
                    println("📡 ChatStreamParser跳过空纯文本")
                    emptyList()
                }
            }
        } catch (e: Exception) {
            Timber.w(e, "⚠️ 解析WebSocket响应失败: ${rawResponse.take(100)}...")
            return listOf(DataStreamEvent.Error(throwable = e))
        }
    }

    /**
     * 🎯 Task1精简版：解析SSE行内容，移除XML标签并生成Chunk事件
     *
     * @param line SSE响应行
     * @param sessionId 会话ID
     * @param userMessageId 用户消息ID
     * @param aiResponseId AI响应ID
     * @param timestamp 时间戳
     * @return StreamEvent列表，通常只包含一个Chunk事件
     */
    // 🎯 Task1精简：移除不再需要的状态追踪参数
    fun parse(
        line: String,
        sessionId: String,
        userMessageId: String,
        aiResponseId: String,
        timestamp: Long = System.currentTimeMillis(),
    ): List<StreamEvent> {
        try {
            // 跳过空行和仅包含空白字符的行
            if (line.isBlank()) {
                return emptyList()
            }

            val events = mutableListOf<StreamEvent>()
            var remainingContent = line
            val timestamp = System.currentTimeMillis()

            // 🎯 Task1精简：移除复杂的标签解析，直接处理为普通内容
            // 🔥 【修复】不再移除<think>标签，让ThinkingBox模块统一处理
            remainingContent =
                remainingContent
                    .replace(STEP_START_REGEX, "") // 移除<step>标签
                    .replace(STEP_END_REGEX, "") // 移除</step>标签
                    .replace(FINAL_START_REGEX, "") // 移除<final>标签
                    .replace(FINAL_END_REGEX, "") // 移除</final>标签
            // 🔥 【重要修复】注释掉<think>标签的移除，避免重复处理
            // .replace(THINK_CONTENT_REGEX, "") // 移除完整的<think>...</think>内容
            // .replace(THINK_START_REGEX, "") // 移除单独的<think>标签
            // .replace(THINK_END_REGEX, "") // 移除单独的</think>标签

            // 处理剩余的普通内容
            val cleanContent = remainingContent.trim()
            if (cleanContent.isNotEmpty()) {
                events.add(
                    StreamEvent.Chunk(
                        sessionId = sessionId,
                        userMessageId = userMessageId,
                        aiResponseId = aiResponseId,
                        timestamp = timestamp,
                        content = cleanContent,
                    ),
                )
                Timber.d("📝 解析到普通内容: ${cleanContent.take(50)}...")
            }

            return events
        } catch (e: Exception) {
            // 容错处理：解析失败时将整行作为普通内容返回
            Timber.w(e, "⚠️ 解析SSE行失败，将作为普通内容处理: $line")
            return listOf(
                StreamEvent.Chunk(
                    sessionId = sessionId,
                    userMessageId = userMessageId,
                    aiResponseId = aiResponseId,
                    timestamp = System.currentTimeMillis(),
                    content = line,
                ),
            )
        }
    }
}
