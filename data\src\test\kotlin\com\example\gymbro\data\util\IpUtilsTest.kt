package com.example.gymbro.data.util

import org.junit.Assert.*
import org.junit.Test

/**
 * IP工具类测试
 */
class IpUtilsTest {

    @Test
    fun testIpToLong() {
        // 测试正常IP地址
        assertEquals(0L, IpUtils.ipToLong("0.0.0.0"))
        assertEquals(16777216L, IpUtils.ipToLong("*******"))
        assertEquals(3232235777L, IpUtils.ipToLong("***********"))
        assertEquals(4294967295L, IpUtils.ipToLong("***************"))

        // 测试无效IP地址
        assertNull(IpUtils.ipToLong("invalid"))
        assertNull(IpUtils.ipToLong("256.1.1.1"))
        assertNull(IpUtils.ipToLong(""))
    }

    @Test
    fun testParseCidr() {
        // 测试正常CIDR
        val result1 = IpUtils.parseCidr("***********/24")
        assertNotNull(result1)
        assertEquals(3232235776L, result1!!.first) // 网络地址
        assertEquals(3232236031L, result1.second) // 广播地址

        // 测试/32 CIDR（单个IP）
        val result2 = IpUtils.parseCidr("***********/32")
        assertNotNull(result2)
        assertEquals(3232235777L, result2!!.first)
        assertEquals(3232235777L, result2.second)

        // 测试无效CIDR
        assertNull(IpUtils.parseCidr("invalid"))
        assertNull(IpUtils.parseCidr("***********"))
        assertNull(IpUtils.parseCidr("***********/33"))
    }

    @Test
    fun testIsIpInCidr() {
        // 测试IP在CIDR范围内
        assertTrue(IpUtils.isIpInCidr("***********", "***********/24"))
        assertTrue(IpUtils.isIpInCidr("*************", "***********/24"))
        assertTrue(IpUtils.isIpInCidr("***********00", "***********/24"))

        // 测试IP不在CIDR范围内
        assertFalse(IpUtils.isIpInCidr("***********", "***********/24"))
        assertFalse(IpUtils.isIpInCidr("********", "***********/24"))

        // 测试单个IP的CIDR
        assertTrue(IpUtils.isIpInCidr("***********", "***********/32"))
        assertFalse(IpUtils.isIpInCidr("***********", "***********/32"))

        // 测试无效输入
        assertFalse(IpUtils.isIpInCidr("invalid", "***********/24"))
        assertFalse(IpUtils.isIpInCidr("***********", "invalid"))
    }

    @Test
    fun testIsIpInCidrList() {
        val cidrList = listOf(
            "***********/24",
            "10.0.0.0/8",
            "**********/12",
        )

        // 测试匹配第一个CIDR
        assertTrue(IpUtils.isIpInCidrList("***********00", cidrList))

        // 测试匹配第二个CIDR
        assertTrue(IpUtils.isIpInCidrList("********", cidrList))

        // 测试匹配第三个CIDR
        assertTrue(IpUtils.isIpInCidrList("**********", cidrList))

        // 测试不匹配任何CIDR
        assertFalse(IpUtils.isIpInCidrList("*******", cidrList))
        assertFalse(IpUtils.isIpInCidrList("*******", cidrList))

        // 测试空列表
        assertFalse(IpUtils.isIpInCidrList("***********", emptyList()))

        // 测试无效IP
        assertFalse(IpUtils.isIpInCidrList("invalid", cidrList))
    }

    @Test
    fun testIsValidIpv4() {
        // 测试有效IPv4地址
        assertTrue(IpUtils.isValidIpv4("0.0.0.0"))
        assertTrue(IpUtils.isValidIpv4("***********"))
        assertTrue(IpUtils.isValidIpv4("***************"))
        assertTrue(IpUtils.isValidIpv4("*******"))

        // 测试无效IPv4地址
        assertFalse(IpUtils.isValidIpv4("256.1.1.1"))
        assertFalse(IpUtils.isValidIpv4("192.168.1"))
        assertFalse(IpUtils.isValidIpv4("***********.1"))
        assertFalse(IpUtils.isValidIpv4("invalid"))
        assertFalse(IpUtils.isValidIpv4(""))
        assertFalse(IpUtils.isValidIpv4("::1")) // IPv6
    }

    @Test
    fun testChinaIpExamples() {
        // 测试一些已知的中国IP段（基于assets文件中的数据）
        val chinaCidrs = listOf(
            "*******/24",
            "*********/12",
            "********/13",
            "********/22",
            "*********/11",
            "********/22",
        )

        // 测试中国IP
        assertTrue(IpUtils.isIpInCidrList("*********", chinaCidrs))
        assertTrue(IpUtils.isIpInCidrList("*********", chinaCidrs))
        assertTrue(IpUtils.isIpInCidrList("*********", chinaCidrs))

        // 测试非中国IP
        assertFalse(IpUtils.isIpInCidrList("*******", chinaCidrs)) // Google DNS
        assertFalse(IpUtils.isIpInCidrList("*******", chinaCidrs)) // Cloudflare DNS
        assertFalse(IpUtils.isIpInCidrList("**************", chinaCidrs)) // OpenDNS
    }
}
