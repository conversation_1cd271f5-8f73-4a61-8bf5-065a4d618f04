package com.example.gymbro.core.ai.config

/**
 * Prompt构建配置
 * Task2-CoachContext数据中心集成：PromptBuilder配置模型
 *
 * 定义Prompt构建过程中的各种参数和策略，支持版本化和动态调整
 *
 * @property version 配置版本，用于A/B测试和版本控制
 * @property maxTokens 最大Token数限制，确保不超过模型限制
 * @property contextTemplatesK 相关模板检索数量（Top-K）
 * @property recentHistoryN 最近历史记录数量（Last-N）
 * @property truncationStrategy Token截断策略
 * @property delimiter 内容分隔符配置
 * @property priorityWeights 内容优先级权重配置
 * @property systemPromptTemplate 系统提示词模板
 */
data class PromptConfig(
    /**
     * 配置版本
     * 用于A/B测试、版本控制和配置回滚
     */
    val version: String = "1.0",

    /**
     * 最大Token数限制
     * 确保生成的Prompt不超过模型的上下文窗口
     * 预留一定空间给AI响应（通常预留20-30%）
     */
    val maxTokens: Int = 3000,

    /**
     * 相关模板检索数量
     * 向量搜索返回的Top-K相关训练模板数量
     */
    val contextTemplatesK: Int = 5,

    /**
     * 最近历史记录数量
     * 包含在上下文中的最近N轮对话数量
     */
    val recentHistoryN: Int = 10,

    /**
     * Token截断策略
     * 当内容超过maxTokens时的处理策略
     */
    val truncationStrategy: TruncationStrategy = TruncationStrategy.PRIORITY_BASED,

    /**
     * 内容分隔符配置
     * 定义不同内容块之间的分隔符
     */
    val delimiter: DelimiterConfig = DelimiterConfig(),

    /**
     * 内容优先级权重配置
     * 定义不同内容类型的重要性权重，用于截断时的优先级判断
     */
    val priorityWeights: PriorityWeights = PriorityWeights(),

    /**
     * 系统提示词模板
     * 定义AI角色和行为的基础模板
     */
    val systemPromptTemplate: String = DEFAULT_SYSTEM_PROMPT,
) {
    companion object {
        /**
         * 默认系统提示词
         * 定义AI教练的基本角色和行为准则
         */
        val DEFAULT_SYSTEM_PROMPT = """
你是GymBro的专业健身教练AI，具备丰富的健身知识和个性化指导能力。

核心职责：
1. 提供专业、安全、个性化的健身指导
2. 基于用户资料和历史记录给出针对性建议
3. 引用相关训练模板时使用[Source: ID]格式标注来源
4. 保持友好、鼓励的沟通风格

回答要求：
- 基于提供的上下文信息回答问题
- 引用训练模板时必须标注来源ID
- 给出具体、可执行的建议
- 考虑用户的健身水平和目标
- 如果信息不足，主动询问必要细节
        """.trimIndent()

        /**
         * 创建默认配置
         * 用于快速创建标准配置实例
         */
        fun createDefault(): PromptConfig = PromptConfig()

        /**
         * 创建高质量配置
         * 适用于对质量要求较高的场景，使用更多上下文
         */
        fun createHighQuality(): PromptConfig = PromptConfig(
            version = "1.0-hq",
            maxTokens = 4000,
            contextTemplatesK = 8,
            recentHistoryN = 15,
        )

        /**
         * 创建快速配置
         * 适用于对响应速度要求较高的场景，使用较少上下文
         */
        fun createFast(): PromptConfig = PromptConfig(
            version = "1.0-fast",
            maxTokens = 2000,
            contextTemplatesK = 3,
            recentHistoryN = 5,
        )
    }

    /**
     * 验证配置的有效性
     * 检查各项参数是否在合理范围内
     */
    fun validate(): List<String> {
        val errors = mutableListOf<String>()

        if (maxTokens <= 0) {
            errors.add("maxTokens必须大于0")
        }

        if (maxTokens > 8000) {
            errors.add("maxTokens不应超过8000（大多数模型的上下文限制）")
        }

        if (contextTemplatesK < 0) {
            errors.add("contextTemplatesK不能为负数")
        }

        if (recentHistoryN < 0) {
            errors.add("recentHistoryN不能为负数")
        }

        if (version.isBlank()) {
            errors.add("version不能为空")
        }

        return errors
    }

    /**
     * 检查配置是否有效
     */
    val isValid: Boolean
        get() = validate().isEmpty()
}

/**
 * Token截断策略
 * 定义当内容超过Token限制时的处理方式
 */
enum class TruncationStrategy {
    /**
     * 基于优先级的截断
     * 根据内容重要性权重进行智能截断
     */
    PRIORITY_BASED,

    /**
     * 先进先出截断
     * 优先保留最新的内容，删除最旧的内容
     */
    FIFO,

    /**
     * 均匀截断
     * 对各类内容进行等比例截断
     */
    PROPORTIONAL,

    /**
     * 严格截断
     * 直接截断超出部分，不考虑内容完整性
     */
    STRICT,
}

/**
 * 分隔符配置
 * 定义不同内容块之间的分隔符和格式
 */
data class DelimiterConfig(
    /**
     * 主要分隔符
     * 用于分隔不同的主要内容块
     */
    val primaryDelimiter: String = "\n\n---\n\n",

    /**
     * 次要分隔符
     * 用于分隔同类内容中的不同项目
     */
    val secondaryDelimiter: String = "\n\n",

    /**
     * XML标签格式
     * 是否使用XML标签包装内容块
     */
    val useXmlTags: Boolean = true,

    /**
     * 内容标题格式
     * 内容块标题的格式模板
     */
    val titleFormat: String = "### %s",
)

/**
 * 优先级权重配置
 * 定义不同内容类型在截断时的重要性权重
 */
data class PriorityWeights(
    /**
     * 系统提示词权重
     * 最高优先级，通常不会被截断
     */
    val systemPrompt: Double = 1.0,

    /**
     * 用户查询权重
     * 高优先级，用户当前问题最重要
     */
    val userQuery: Double = 0.9,

    /**
     * 用户资料权重
     * 中高优先级，个性化的基础
     */
    val profileSummary: Double = 0.8,

    /**
     * 相关模板权重
     * 中等优先级，提供具体参考
     */
    val relevantTemplates: Double = 0.7,

    /**
     * 最近历史权重
     * 中等优先级，维持对话连贯性
     */
    val recentHistory: Double = 0.6,
) {
    /**
     * 获取所有权重的列表
     * 用于排序和优先级计算
     */
    fun getAllWeights(): List<Pair<String, Double>> = listOf(
        "systemPrompt" to systemPrompt,
        "userQuery" to userQuery,
        "profileSummary" to profileSummary,
        "relevantTemplates" to relevantTemplates,
        "recentHistory" to recentHistory,
    ).sortedByDescending { it.second }
}
