package com.example.gymbro.core.network.monitor

import kotlinx.coroutines.flow.StateFlow

/**
 * 网络状态监控接口
 *
 * 提供网络连接状态的实时监控功能
 */
interface NetworkMonitor {
    /**
     * 当前网络状态
     */
    val networkState: StateFlow<NetworkState>

    /**
     * 当前是否在线
     */
    val isOnline: Boolean

    /**
     * 开始监控网络状态
     */
    fun startMonitoring()

    /**
     * 停止监控网络状态
     */
    fun stopMonitoring()
}

/**
 * 网络状态
 */
sealed interface NetworkState {
    /**
     * 未知状态
     */
    data object Unknown : NetworkState

    /**
     * 网络不可用
     */
    data object Unavailable : NetworkState

    /**
     * 网络可用
     */
    data class Available(
        val type: NetworkType = NetworkType.UNKNOWN,
        val bandwidthKbps: Int = 0,
    ) : NetworkState

    /**
     * 网络连接中
     */
    data object Connecting : NetworkState

    /**
     * 网络连接丢失
     */
    data object Lost : NetworkState
}

/**
 * 网络类型
 */
enum class NetworkType {
    UNKNOWN,
    WIFI,
    CELLULAR,
    ETHERNET,
    VPN,
}

/**
 * 网络事件 - D2阶段扩展
 *
 * 用于NetworkWatchdog的事件流，支持UI Banner显示
 */
sealed interface NetworkEvent {
    val timestamp: Long

    /**
     * 网络已连接
     */
    data class Connected(
        val networkType: NetworkType,
        override val timestamp: Long = System.currentTimeMillis(),
    ) : NetworkEvent

    /**
     * 网络连接不稳定
     */
    data class Losing(
        val reason: NetworkLossReason,
        override val timestamp: Long = System.currentTimeMillis(),
    ) : NetworkEvent

    /**
     * 网络连接丢失
     */
    data class Lost(
        val reason: NetworkLossReason,
        override val timestamp: Long = System.currentTimeMillis(),
    ) : NetworkEvent

    /**
     * 网络连接已恢复
     */
    data class Restored(
        val networkType: NetworkType,
        override val timestamp: Long = System.currentTimeMillis(),
    ) : NetworkEvent

    /**
     * 网络带宽变化 - 按照websock+Okhttp.md核心实现方案
     */
    data class Bandwidth(
        val kbps: Int,
        override val timestamp: Long = System.currentTimeMillis(),
    ) : NetworkEvent {
        /**
         * 判断是否为低带宽
         */
        fun isLowBandwidth(): Boolean = kbps < 300
    }
}

/**
 * 网络丢失原因
 */
enum class NetworkLossReason {
    /**
     * 飞行模式
     */
    AIRPLANE_MODE,

    /**
     * 无信号
     */
    NO_SIGNAL,

    /**
     * 被系统限制
     */
    SYSTEM_RESTRICTED,

    /**
     * WiFi断开
     */
    WIFI_DISCONNECTED,

    /**
     * 蜂窝网络不可用
     */
    CELLULAR_UNAVAILABLE,

    /**
     * 网络超时
     */
    TIMEOUT,

    /**
     * 未知原因
     */
    UNKNOWN,
}

/**
 * 简单的网络监控实现
 * 用于测试和开发环境
 */
class SimpleNetworkMonitor : NetworkMonitor {
    private val _networkState = kotlinx.coroutines.flow.MutableStateFlow<NetworkState>(
        NetworkState.Available(),
    )
    override val networkState: StateFlow<NetworkState> = _networkState

    override val isOnline: Boolean
        get() = networkState.value is NetworkState.Available

    override fun startMonitoring() {
        // 简单实现：假设网络总是可用
        _networkState.value = NetworkState.Available(NetworkType.WIFI)
    }

    override fun stopMonitoring() {
        _networkState.value = NetworkState.Unknown
    }
}
