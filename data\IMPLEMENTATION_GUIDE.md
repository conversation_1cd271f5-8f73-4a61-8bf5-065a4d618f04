# Data层实现指南 (不可改动)

> **📋 权威参考**: 本文档为Data层开发的权威实现指南，基于Clean Architecture + Repository模式
>
> **版本**: v2.1 | **状态**: 🔒 不可改动 | **更新日期**: 2025年01月28日
>
> **🎯 重构状态**: ✅ Data层重构已完成 - 所有Repository接口已匹配Domain层定义，编译无错误，架构完全符合Clean Architecture原则。

---

## 🏗️ Data层架构原则

### 核心职责
- **Repository实现**: 实现Domain层定义的Repository接口
- **数据源协调**: 协调Remote ↔ Local数据源，实现离线优先策略
- **数据转换**: Entity ↔ Domain ↔ DTO三层映射
- **错误处理**: 将各种数据源错误转换为ModernDataError
- **测试覆盖率要求**: ≥ 80%（数据访问层）

### 技术栈现代化
- **序列化**: kotlinx.serialization（替代Moshi）
- **时间处理**: kotlinx.datetime.Instant（替代System.currentTimeMillis）
- **错误处理**: ModernResult + ModernDataError
- **日志记录**: Timber统一日志

---

## 🌐 API模型与序列化

### 现代化DTO设计

```kotlin
/**
 * AI聊天请求DTO - 使用kotlinx.serialization
 */
@Serializable  // ✅ 使用kotlinx.serialization替代Moshi
data class ChatRequestDto(
    @SerialName("model") val model: String = "gemini-2.5-flash-preview-05-20",
    @SerialName("messages") val messages: List<MessageDto>,
    @SerialName("stream") val stream: Boolean = true
)

/**
 * 消息DTO
 */
@Serializable
data class MessageDto(
    @SerialName("role") val role: String,
    @SerialName("content") val content: String
)

/**
 * 流式响应块DTO
 */
@Serializable
data class ChatChunkDto(
    @SerialName("choices") val choices: List<Choice>
) {
    @Serializable
    data class Choice(
        @SerialName("delta") val delta: Delta?
    ) {
        @Serializable
        data class Delta(
            @SerialName("content") val content: String?
        )
    }
}
```

### API接口定义

```kotlin
/**
 * AI服务API接口
 */
interface AICoachApi {
    @POST("v1/chat/completions")
    @Streaming
    suspend fun chatStream(@Body body: ChatRequestDto): Response<ResponseBody>
}
```

---

## 🗄️ Room数据库设计

### Entity设计规范

```kotlin
/**
 * 聊天消息实体 - Room数据库表
 */
@Entity(tableName = "chat_messages")
data class ChatMsgEntity(
    @PrimaryKey val id: String,
    val role: String,
    val text: String,  // 注意：Entity层存储原始字符串，Domain层转换为UiText
    val createdAt: Long  // 存储为时间戳，便于数据库操作
)

/**
 * 聊天消息DAO - 数据访问对象
 */
@Dao
interface ChatDao {
    /**
     * 观察所有聊天消息（按时间排序）
     */
    @Query("SELECT * FROM chat_messages ORDER BY createdAt ASC")
    fun observeAll(): Flow<List<ChatMsgEntity>>

    /**
     * 获取最近的聊天消息
     */
    @Query("SELECT * FROM chat_messages ORDER BY createdAt DESC LIMIT :limit")
    fun recent(limit: Int): Flow<List<ChatMsgEntity>>

    /**
     * 插入或更新聊天消息
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun upsert(vararg msgs: ChatMsgEntity)

    /**
     * 清空所有聊天消息
     */
    @Query("DELETE FROM chat_messages")
    suspend fun clearAll()
}
```

---

## 🔄 数据映射系统

### Mapper设计模式

```kotlin
/**
 * DTO ↔ Domain 映射扩展函数
 */
fun toDomain(): ChatMsg = ChatMsg(
    role = ChatMsg.Role.valueOf(role.uppercase()),
    text = UiText.DynamicString(content)  // ✅ 转换为UiText
)

fun toDto(): MessageDto = MessageDto(
    role = role.name.lowercase(),
    content = text.asString()  // ✅ 从UiText提取字符串
)

/**
 * Entity ↔ Domain 映射扩展函数
 */
fun toDomain(): ChatMsg = ChatMsg(
    id = id,
    role = ChatMsg.Role.valueOf(role.uppercase()),
    text = UiText.DynamicString(text),  // ✅ Entity字符串转UiText
    createdAt = Instant.fromEpochMilliseconds(createdAt)  // ✅ 时间戳转Instant
)

fun toEntity(): ChatMsgEntity = ChatMsgEntity(
    id = id,
    role = role.name.lowercase(),
    text = text.asString(),  // ✅ UiText转字符串存储
    createdAt = createdAt.toEpochMilliseconds()  // ✅ Instant转时间戳
)

/**
 * 批量转换扩展函数
 */
fun toDomain(): List<ChatMsg> = map { toDomain() }
fun toEntity(): List<ChatMsgEntity> = map { toEntity() }
```

---

## 🏪 Repository实现模式

### 现代化Repository实现

```kotlin
/**
 * AI聊天Repository实现
 * 协调远程API和本地缓存，提供统一的数据访问接口
 */
class ChatRepositoryImpl @Inject constructor(
    private val api: AICoachApi,  // ✅ 更新API接口名
    private val dao: ChatDao,
    private val json: Json,  // ✅ 使用kotlinx.serialization的Json
    private val errorHandler: ModernErrorHandler  // ✅ 注入错误处理器
) : ChatRepository {

    override fun streamAnswer(
        history: List<ChatMsg>,
        newMsg: ChatMsg
    ): Flow<ModernResult<ChatMsg>> = channelFlow {  // ✅ 返回ModernResult
        // 发送Loading状态
        send(ModernResult.Loading)

        try {
            // 本地先保存用户消息
            dao.upsert(newMsg.toEntity())

            // 构建请求体
            val req = ChatRequestDto(
                messages = (history + newMsg).map { it.toDto() }  // ✅ 使用mapper
            )

            val resp = api.chatStream(req)
            if (!resp.isSuccessful) {
                val error = resp.toModernDataError()  // ✅ 转换为ModernDataError
                send(ModernResult.Error(error))
                return@channelFlow
            }

            // 逐行读取SSE流
            resp.body()?.byteStream()?.bufferedReader()?.useLines { lines ->
                lines.forEach { raw ->
                    if (raw.startsWith("data: ")) {
                        val jsonStr = raw.removePrefix("data: ")
                        if (jsonStr == "[DONE]") return@useLines

                        try {
                            val chunk = json.decodeFromString<ChatChunkDto>(jsonStr)  // ✅ kotlinx.serialization
                            val piece = chunk.choices.firstOrNull()?.delta?.content ?: ""
                            if (piece.isNotEmpty()) {
                                val msg = ChatMsg(
                                    role = ChatMsg.Role.ASSISTANT,
                                    text = UiText.DynamicString(piece),  // ✅ 使用UiText
                                    createdAt = Clock.System.now()  // ✅ 使用kotlinx.datetime
                                )
                                send(ModernResult.Success(msg))
                                dao.upsert(msg.toEntity())
                            }
                        } catch (e: SerializationException) {
                            Timber.w(e, "Failed to parse SSE chunk: $jsonStr")  // ✅ 使用Timber日志
                        }
                    }
                }
            }
        } catch (e: IOException) {
            val error = ModernDataError(
                operationName = "streamAnswer",
                errorType = GlobalErrorType.NETWORK_ERROR,
                uiMessage = UiText.StringResource(R.string.error_network),
                cause = e
            )
            send(ModernResult.Error(error))
        } catch (e: Exception) {
            val error = ModernDataError(
                operationName = "streamAnswer",
                errorType = GlobalErrorType.UNKNOWN_ERROR,
                uiMessage = UiText.StringResource(R.string.error_unknown),
                cause = e
            )
            send(ModernResult.Error(error))
        }
    }.buffer(Channel.UNLIMITED)   // 背压处理，UI持续消费

    override suspend fun getChatHistory(): ModernResult<List<ChatMsg>> {
        return try {
            val entities = dao.observeAll().first()  // 获取最新数据
            ModernResult.Success(entities.toDomain())
        } catch (e: Exception) {
            val error = ModernDataError(
                operationName = "getChatHistory",
                errorType = GlobalErrorType.DATABASE_ERROR,
                cause = e
            )
            ModernResult.Error(error)
        }
    }

    override suspend fun clearChatHistory(): ModernResult<Unit> {
        return try {
            dao.clearAll()
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            val error = ModernDataError(
                operationName = "clearChatHistory",
                errorType = GlobalErrorType.DATABASE_ERROR,
                cause = e
            )
            ModernResult.Error(error)
        }
    }
}
```

---

## ⚠️ 错误处理系统

### HTTP响应错误转换

```kotlin
/**
 * HTTP响应转ModernDataError扩展函数
 */
private fun toModernDataError(): ModernDataError =
    when (code()) {
        429 -> ModernDataError(
            operationName = "API_CALL",
            errorType = GlobalErrorType.RATE_LIMIT_ERROR,
            uiMessage = UiText.StringResource(R.string.error_rate_limit)
        )
        in 500..599 -> ModernDataError(
            operationName = "API_CALL",
            errorType = GlobalErrorType.SERVER_ERROR,
            uiMessage = UiText.StringResource(R.string.error_server)
        )
        401 -> ModernDataError(
            operationName = "API_CALL",
            errorType = GlobalErrorType.AUTHENTICATION_ERROR,
            uiMessage = UiText.StringResource(R.string.error_auth)
        )
        else -> ModernDataError(
            operationName = "API_CALL",
            errorType = GlobalErrorType.UNKNOWN_ERROR,
            uiMessage = UiText.StringResource(R.string.error_unknown)
        )
    }
```

---

## 📋 Data层最佳实践

### 性能优化策略

| 目标              | 现代化做法                                                                    |
| ----------------- | ----------------------------------------------------------------------------- |
| **避免主线程I/O** | 全部使用 **suspend / Flow** & `flowOn(io)`，确保异步执行                      |
| **流式文本优化**  | `Channel.UNLIMITED` + `buffer()`；UI端配合`lazyListState.animateScrollToItem` |
| **错误处理统一**  | 使用`ModernResult<T>` + `ModernDataError`，提供用户友好的错误信息             |
| **断线重连机制**  | 在`catch`中使用`retry(3) { e is IOException }`；或外层ViewModel控制重试逻辑   |
| **日志记录**      | 使用`Timber`统一日志，便于调试和生产环境监控                                  |

### 开发检查清单

#### ✅ 重构完成的Repository实现
- [x] `AuthRepositoryImpl` - 认证Repository完全重构，匹配Domain接口
- [x] `SubscriptionRepositoryImpl` - 订阅Repository完全重构，支持创建、取消、状态更新
- [x] `TempUserRepositoryImpl` - 用户Repository完全重写，匹配新UserRepository接口
- [x] `ExerciseCrudRepository` - 训练Repository接口匹配，支持搜索、收藏、肌肉群查询
- [x] `ChatSessionRepositoryImpl` - AI对话Repository，已修复UiText使用规范
- [x] `AuthServiceImpl` - 认证Service，已修复UiText使用规范
- [x] `WorkoutServiceImpl` - 训练Service，已修复UiText使用规范

#### 创建新Repository实现时
- [ ] 实现Domain层定义的接口
- [ ] 所有方法返回`ModernResult<T>`
- [ ] 使用适当的Mapper进行数据转换
- [ ] 添加完整的错误处理
- [ ] 使用Timber记录关键操作日志

#### 创建新Entity时
- [ ] 使用Room注解正确标记
- [ ] 字段类型适合数据库存储
- [ ] 添加必要的索引和约束
- [ ] 创建对应的Mapper函数

#### 创建新API接口时
- [ ] 使用kotlinx.serialization注解
- [ ] DTO字段使用@SerialName标记
- [ ] 添加适当的HTTP方法注解
- [ ] 考虑错误响应处理

#### 错误处理检查
- [ ] 所有异常都转换为ModernDataError
- [ ] 提供有意义的operationName
- [ ] 使用适当的GlobalErrorType
- [ ] 提供用户友好的错误消息

---

> **⚠️ 重要提醒**: 本文档为Data层开发的权威指南，确保所有数据访问操作的一致性和可靠性。任何修改都可能影响数据完整性，请通过正式的架构评审流程。
