package com.example.gymbro.data.ai.service

import com.example.gymbro.shared.models.ai.ChatRequest
import com.example.gymbro.shared.models.ai.ChatResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * AI API服务接口
 *
 * 支持OpenAI兼容的聊天完成API
 */
interface AiApiService {
    /**
     * 发送聊天请求（非流式）
     */
    @POST("v1/chat/completions")
    suspend fun chatCompletion(
        @Body request: ChatRequest,
    ): Response<ChatResponse>
}

/**
 * Google Gemini API服务接口
 */
interface GeminiApiService
