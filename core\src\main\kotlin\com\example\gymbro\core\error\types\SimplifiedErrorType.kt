package com.example.gymbro.core.error.types

/**
 * SimplifiedErrorType类型别名
 *
 * Phase 5 错误处理统一：GlobalErrorType即为简化的错误处理系统
 * 不再需要单独的SimplifiedErrorType，GlobalErrorType已经是简化版本
 *
 * 保留此文件仅为向后兼容
 */
@Deprecated(
    message = "使用GlobalErrorType替代SimplifiedErrorType，GlobalErrorType已经是简化版本",
    replaceWith = ReplaceWith("GlobalErrorType", "com.example.gymbro.core.error.types.GlobalErrorType"),
    level = DeprecationLevel.WARNING,
)
typealias SimplifiedErrorType = GlobalErrorType

/**
 * 向后兼容的枚举映射
 * 保留此对象仅为向后兼容，新代码应直接使用GlobalErrorType
 */
@Deprecated(
    message = "直接使用GlobalErrorType及其子类",
    replaceWith = ReplaceWith("GlobalErrorType", "com.example.gymbro.core.error.types.GlobalErrorType"),
    level = DeprecationLevel.WARNING,
)
object SimplifiedErrorTypeCompat {
    /**
     * 网络相关错误 - 使用GlobalErrorType.Network.*
     */
    @Deprecated("使用GlobalErrorType.Network.*", ReplaceWith("GlobalErrorType.Network"))
    val NETWORK = GlobalErrorType.Network::class

    /**
     * 本地存储/数据库错误 - 使用GlobalErrorType.Data.* 或 GlobalErrorType.Database.*
     */
    @Deprecated("使用GlobalErrorType.Data.* 或 GlobalErrorType.Database.*", ReplaceWith("GlobalErrorType.Data"))
    val IO = GlobalErrorType.Data::class

    /**
     * 业务逻辑错误 - 使用GlobalErrorType.Business.*, GlobalErrorType.Auth.*, GlobalErrorType.Validation.*
     */
    @Deprecated(
        "使用GlobalErrorType.Business.*, GlobalErrorType.Auth.*, GlobalErrorType.Validation.*",
        ReplaceWith("GlobalErrorType.Business"),
    )
    val DOMAIN = GlobalErrorType.Business::class

    /**
     * 未知错误 - 使用GlobalErrorType.Unknown
     */
    @Deprecated("使用GlobalErrorType.Unknown", ReplaceWith("GlobalErrorType.Unknown"))
    val UNKNOWN = GlobalErrorType.Unknown::class
}
