package com.example.gymbro.core.ai.prompt.model

/**
 * History RAG 上下文数据
 *
 * 用于封装从 History 模块检索和摘要后的上下文信息
 *
 * @since 618重构
 */
data class HistoryRagContext(
    /**
     * 智能摘要内容
     */
    val summary: String,

    /**
     * 源消息ID列表
     */
    val sourceMessageIds: List<String>,

    /**
     * 嵌入向量ID列表
     */
    val embeddingIds: List<String>,

    /**
     * 使用的算法标识
     */
    val algorithm: String,

    /**
     * 创建时间戳
     */
    val createdAt: Long,

    /**
     * 质量指标
     */
    val qualityMetrics: SummaryQualityMetrics,
) {
    /**
     * 获取质量等级
     */
    val qualityGrade: QualityGrade
        get() = qualityMetrics.qualityGrade

    /**
     * 检查是否为高质量摘要
     */
    val isHighQuality: Boolean
        get() = qualityGrade in listOf(QualityGrade.EXCELLENT, QualityGrade.GOOD)

    /**
     * 获取摘要长度
     */
    val summaryLength: Int
        get() = summary.length

    /**
     * 获取压缩比
     */
    val compressionRatio: Float
        get() = qualityMetrics.compressionRatio
}
