package com.example.gymbro.core.error.types

import com.example.gymbro.core.ui.text.UiText

/**
 * 现代错误数据类，表示应用中的各种错误
 *
 * Phase 5 错误处理统一：使用GlobalErrorType作为统一错误处理系统
 * GlobalErrorType即为简化的错误处理方式，支持四大类：
 * - Network: 网络相关错误
 * - Data/Database: 本地存储/数据库错误
 * - Business/Auth/Validation: 业务逻辑错误
 * - System/Unknown: 系统和未知错误
 *
 * 用于在不同层之间传递错误信息，包含错误类型、类别、UI消息等
 * 继承自Throwable以便在异常处理流程中使用
 */
data class ModernDataError(
    val operationName: String,
    val errorType: GlobalErrorType,
    val category: ErrorCategory = ErrorCategory.UNKNOWN,
    val uiMessage: UiText? = null,
    val metadataMap: Map<String, Any> = emptyMap(),
    val suppressLogging: Boolean = false,
    val severity: ErrorSeverity = ErrorSeverity.ERROR,
    val recoverable: Boolean = false,
    val statusCode: Int? = null,
    override val cause: Throwable? = null,
) : Throwable(uiMessage?.toString() ?: operationName, cause) {

    companion object {
        /**
         * 从GlobalErrorType创建ModernDataError（保持兼容性）
         */
        fun fromGlobalErrorType(
            operationName: String,
            errorType: GlobalErrorType,
            category: ErrorCategory? = null,
            uiMessage: UiText? = null,
            metadataMap: Map<String, Any> = emptyMap(),
            suppressLogging: Boolean = false,
            severity: ErrorSeverity = ErrorSeverity.ERROR,
            recoverable: Boolean = false,
            statusCode: Int? = null,
            cause: Throwable? = null,
        ): ModernDataError {
            return ModernDataError(
                operationName = operationName,
                errorType = errorType,
                category = category ?: ErrorCategory.UNKNOWN,
                uiMessage = uiMessage,
                metadataMap = metadataMap,
                suppressLogging = suppressLogging,
                severity = severity,
                recoverable = recoverable,
                statusCode = statusCode,
                cause = cause,
            )
        }
    }

    /**
     * 获取指定键的元数据值
     */
    @Suppress("UNCHECKED_CAST")
    fun <T> getMetadataValue(
        key: String,
        defaultValue: T,
    ): T {
        val value = metadataMap[key] ?: return defaultValue
        return try {
            value as T
        } catch (e: ClassCastException) {
            defaultValue
        }
    }

    /**
     * 错误代码，用于唯一标识错误类型
     * 简化为：操作名称 + 错误类型
     */
    val errorCode: String
        get() = "${operationName}_${errorType::class.simpleName ?: "Unknown"}"

    /**
     * 获取用户友好的错误消息
     * 重写Throwable的message属性
     */
    override val message: String
        get() = uiMessage?.toString() ?: super.message ?: ""

    /**
     * 将错误转换为字符串表示形式
     * @return 错误的字符串表示形式
     */
    override fun toString(): String =
        "ModernDataError(operationName='$operationName', errorType=$errorType, severity=$severity, " +
            "uiMessage=$uiMessage, recoverable=$recoverable, errorCode='$errorCode', statusCode=$statusCode, metadataMap=$metadataMap)"
}
