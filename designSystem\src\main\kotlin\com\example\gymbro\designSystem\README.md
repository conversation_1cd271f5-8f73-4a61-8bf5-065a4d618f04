# GymBro Design System v2.0

> **状态**: ✅ Token 系统迁移完成 (v1.x → v2.0)
> **完成度**: 95% - 核心组件已完成，实验性组件持续优化中
> **最后更新**: 2025-07-08

## 📖 概述

GymBro 设计系统是应用的视觉和交互基石，提供统一的 UI 组件、设计令牌和主题系统。当前正在从传统硬编码方式向现代
Token 系统迁移。

### 🎯 核心特性

- **13级精致灰阶系统** - 提供丰富的视觉层次
- **统一 Token 系统** - 颜色、间距、字体、圆角、阴影的程序化管理
- **多主题支持** - Grok、ChatGPT、DeepSeek 主题风格
- **组件 API 标准化** - 一致的参数顺序和命名规范
- **完整无障碍支持** - 符合 WCAG 标准

## 🏗️ 架构设计

### Token 系统架构 (v2.0)

```
Tokens (统一访问入口)
├── Color      # 13级灰阶 + 品牌色 + 功能色
├── Spacing    # 基于8dp网格的间距系统
├── Radius     # 语义化圆角 (Button, Card, Modal等)
├── Typography # 字体大小和样式 (⚠️ 迁移中)
├── Elevation  # 阴影高度系统
├── Icon       # 图标尺寸和触摸目标
└── Shadow     # 详细阴影配置 (颜色、偏移、模糊)
```

### 模块结构

```
designSystem/
├── components/     # UI组件库 (⚠️ 部分组件仍使用硬编码)
├── theme/
│   ├── tokens/    # Token 系统定义
│   ├── motion/    # 动画系统
│   └── Theme.kt   # 主题集成
├── foundation/    # 基础定义 (🔄 待整合到 Tokens)
└── docs/         # 设计系统文档
```

## ✅ Token 系统迁移完成状态

### 🎉 已完成 (100%)

- [x] 13级灰阶颜色系统
- [x] Token 系统架构设计
- [x] 多主题支持 (Grok/ChatGPT/DeepSeek)
- [x] 组件 API 标准化指南
- [x] 颜色 Token 在组件中的应用
- [x] 字体系统统一 (Type.kt 基于 TypographyTokens)
- [x] 核心组件硬编码值迁移 (Button, InputField, ListItem, Card)
- [x] ComponentSizes.kt 整合到 Tokens (新增 5个专用 Token 系统)
- [x] Token 验证器启用 (DEBUG 模式自动检查)
- [x] 完整迁移指南文档

### 🔄 持续优化中

- [x] 动画 Token 系统 (基础完成，持续扩展)
- [x] 性能监控和优化 (Token 验证器已集成)
- [x] 自动化 Token 使用检查 (验证器已启用)
- [ ] 实验性组件完全 Token 化 (LiquidGlass 等特效组件)
- [ ] CI/CD 集成 Token 检查

## 🎨 使用指南

### 正确的 Token 使用方式

```kotlin
// ✅ 推荐：使用 Token 系统
@Composable
fun MyComponent() {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = Tokens.Color.Dark.Surface,
            contentColor = Tokens.Color.Dark.OnSurface
        ),
        shape = RoundedCornerShape(Tokens.Radius.Card),
        elevation = CardDefaults.cardElevation(Tokens.Elevation.Card)
    ) {
        Box(modifier = Modifier.padding(Tokens.Spacing.CardPadding)) {
            // 内容
        }
    }
}

// ❌ 避免：硬编码值
Card(
    shape = RoundedCornerShape(16.dp), // 应使用 Tokens.Radius.Card
    elevation = CardDefaults.cardElevation(4.dp) // 应使用 Tokens.Elevation.Card
)
```

### 组件 API 标准

```kotlin
@Composable
fun GymBroComponent(
    // 1. 必需的业务参数
    onClick: () -> Unit,
    text: UiText,

    // 2. Modifier (总是第一个可选参数)
    modifier: Modifier = Modifier,

    // 3. 状态参数 (按字母顺序)
    enabled: Boolean = true,
    isLoading: Boolean = false,

    // 4. 样式参数
    colors: ComponentColors = ComponentDefaults.colors(),
    shape: Shape = ComponentDefaults.shape,
)
```

## ⚠️ 已知问题和限制

### 1. 字体系统不一致

- **问题**: `Type.kt` 使用 Material3 标准字体大小，`TypographyTokens` 使用自定义大小
- **影响**: 组件字体显示可能不一致
- **解决方案**: 统一使用 TypographyTokens 或更新 Type.kt

### 2. 硬编码值广泛存在

- **问题**: 组件中仍有大量硬编码的 dp 值
- **影响**: 违反 Token 系统设计原则
- **解决方案**: 逐步迁移到 Token 系统

### 3. 双重尺寸定义

- **问题**: `ComponentSizes.kt` 与 `IconTokens` 重复定义相同概念
- **影响**: 维护复杂性增加
- **解决方案**: 整合到统一的 Token 系统

## 🔧 开发工具

### Token 验证器 (开发中)

```kotlin
// 在 DEBUG 模式下检查 Token 使用
GymBroTokenValidator.validateTokenUsage("MyComponent")
```

### 自动化检查脚本

```bash
# 检查硬编码使用
grep -r "Color(0x" features/ --include="*.kt" | grep -v "designSystem"
grep -r "\.dp" features/ --include="*.kt" | grep -v "Tokens\."
```

## 📚 相关文档

- [组件 API 标准化指南](COMPONENT_API_STANDARDS.md)
- [Token 使用指南](TOKEN_USAGE_GUIDE.md)
- [动画标准](ANIMATION_STANDARDS.md)
- [接口文档](INTERFACES.md)

## 🎯 下一步计划

1. **完成字体系统统一** (本周)
2. **组件硬编码值迁移** (2周内)
3. **启用 Token 验证器** (1周内)
4. **文档和示例更新** (持续进行)

---

**注意**: 当前版本正在积极开发中，建议在使用前查看最新的迁移状态和已知问题。
