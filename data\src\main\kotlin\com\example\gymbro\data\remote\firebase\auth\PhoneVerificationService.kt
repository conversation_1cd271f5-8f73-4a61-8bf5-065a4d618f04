package com.example.gymbro.data.remote.firebase.auth

import android.app.Activity
import com.example.gymbro.core.error.extensions.toModernDataError
import com.example.gymbro.core.error.types.ErrorSeverity
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.google.firebase.FirebaseException
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.PhoneAuthCredential
import com.google.firebase.auth.PhoneAuthOptions
import com.google.firebase.auth.PhoneAuthProvider
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Firebase手机验证服务，负责手机号验证相关的所有操作
 */
@Singleton
class PhoneVerificationService @Inject constructor(
    private val auth: FirebaseAuth,
) {
    companion object {
        private const val VERIFICATION_TIMEOUT_SECONDS = 60L
    }

    // 临时变量，用于存储当前的验证ID，实际实现应该更健壮
    private var currentVerificationId: String? = null

    /**
     * 验证手机号码，并发送验证码
     * @param phoneNumber 手机号码
     * @param context 验证上下文
     * @param callback 验证状态回调
     * @param forceResendingToken 强制重发令牌，用于重新发送验证码
     * @return 包含操作结果的ModernResult
     */
    fun verifyPhoneNumber(
        phoneNumber: String,
        context: PhoneVerificationContext,
        callback: PhoneVerificationCallback,
        forceResendingToken: Any? = null,
    ): ModernResult<Unit> {
        return try {
            val activity = context.getContext() as? Activity
                ?: return ModernResult.error(
                    ModernDataError(
                        uiMessage = UiText.DynamicString("验证上下文无效"),
                        errorType = GlobalErrorType.Auth.General,
                        severity = ErrorSeverity.ERROR,
                        operationName = "PhoneVerificationService.verifyPhoneNumber.invalidContext",
                    ),
                )

            val firebaseCallbacks = createFirebaseCallbacks(callback)
            val options = PhoneAuthOptions
                .newBuilder(auth)
                .setPhoneNumber(phoneNumber)
                .setTimeout(VERIFICATION_TIMEOUT_SECONDS, TimeUnit.SECONDS)
                .setActivity(activity)
                .setCallbacks(firebaseCallbacks)

            if (forceResendingToken != null) {
                options.setForceResendingToken(forceResendingToken as PhoneAuthProvider.ForceResendingToken)
            }

            PhoneAuthProvider.verifyPhoneNumber(options.build())
            ModernResult.success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "验证手机号码失败 - phone: %s", phoneNumber)
            ModernResult.error(
                e.toModernDataError(
                    operationName = "PhoneVerificationService.verifyPhoneNumber",
                    uiMessage = UiText.DynamicString("验证手机号码失败"),
                ),
            )
        }
    }

    /**
     * 发送验证码到手机
     * @param phoneNumber 手机号码
     * @param context 验证上下文
     * @param callback 验证状态回调
     * @return 包含操作结果的ModernResult
     */
    suspend fun sendVerificationCode(
        phoneNumber: String,
        context: PhoneVerificationContext,
        callback: PhoneVerificationCallback,
    ): ModernResult<Unit> = verifyPhoneNumber(phoneNumber, context, callback)

    /**
     * 重新发送验证码
     * @param phoneNumber 手机号码
     * @param context 验证上下文
     * @param callback 验证状态回调
     * @param resendToken 重发令牌
     * @return 包含操作结果的ModernResult
     */
    suspend fun resendVerificationCode(
        phoneNumber: String,
        context: PhoneVerificationContext,
        callback: PhoneVerificationCallback,
        resendToken: Any,
    ): ModernResult<Unit> = verifyPhoneNumber(phoneNumber, context, callback, resendToken)

    /**
     * 创建手机认证凭证
     * @param verificationId 验证ID
     * @param code 验证码
     * @return 手机认证凭证
     */
    fun createPhoneCredential(verificationId: String, code: String): PhoneAuthCredential {
        return PhoneAuthProvider.getCredential(verificationId, code)
    }

    /**
     * 验证手机验证码
     * @param verificationId 验证ID
     * @param code 验证码
     * @return 包含验证凭证的ModernResult
     */
    fun verifyPhoneCode(verificationId: String, code: String): ModernResult<PhoneAuthCredential> {
        return try {
            if (verificationId.isBlank()) {
                return ModernResult.error(
                    ModernDataError(
                        operationName = "verifyPhoneCode",
                        errorType = GlobalErrorType.Validation.InvalidInput,
                        uiMessage = UiText.DynamicString("验证ID不能为空"),
                        severity = ErrorSeverity.WARNING,
                    ),
                )
            }
            if (code.isBlank()) {
                return ModernResult.error(
                    ModernDataError(
                        operationName = "verifyPhoneCode",
                        errorType = GlobalErrorType.Validation.InvalidInput,
                        uiMessage = UiText.DynamicString("验证码不能为空"),
                        severity = ErrorSeverity.WARNING,
                    ),
                )
            }
            val credential = PhoneAuthProvider.getCredential(verificationId, code)
            ModernResult.success(credential)
        } catch (e: Exception) {
            Timber.e(e, "验证手机验证码失败")
            ModernResult.error(
                ModernDataError(
                    operationName = "verifyPhoneCode",
                    errorType = GlobalErrorType.Auth.General,
                    uiMessage = UiText.DynamicString("验证码无效"),
                    severity = ErrorSeverity.WARNING,
                ),
            )
        }
    }

    /**
     * 使用手机号和验证码进行注册
     * @param phone 用户的手机号码
     * @param code 收到的短信验证码
     * @return 包含用户UID的ModernResult.Success，或包含错误的ModernResult.Error
     */
    suspend fun registerWithPhone(
        phone: String,
        code: String,
        authService: FirebaseAuthService,
    ): ModernResult<String> {
        return try {
            val verificationId = currentVerificationId
            if (verificationId.isNullOrEmpty()) {
                return ModernResult.error(
                    ModernDataError(
                        operationName = "PhoneVerificationService.registerWithPhone.missingVerificationId",
                        errorType = GlobalErrorType.Auth.General,
                        uiMessage = UiText.DynamicString("手机号注册失败: 验证ID缺失"),
                        severity = ErrorSeverity.ERROR,
                        metadataMap = mapOf("phone" to phone),
                    ),
                )
            }
            val credential = PhoneAuthProvider.getCredential(verificationId, code)
            authService.loginWithPhone(credential)
        } catch (e: Exception) {
            Timber.e(e, "手机号注册失败 - phone: %s", phone)
            ModernResult.error(
                e.toModernDataError(
                    operationName = "PhoneVerificationService.registerWithPhone",
                    uiMessage = UiText.DynamicString("手机号注册失败"),
                ),
            )
        }
    }

    /**
     * 创建Firebase回调适配器
     */
    private fun createFirebaseCallbacks(
        callback: PhoneVerificationCallback,
    ): PhoneAuthProvider.OnVerificationStateChangedCallbacks =
        object : PhoneAuthProvider.OnVerificationStateChangedCallbacks() {
            override fun onVerificationCompleted(credential: PhoneAuthCredential) {
                Timber.d("手机验证自动完成")
                callback.onVerificationCompleted(credential)
            }

            override fun onVerificationFailed(e: FirebaseException) {
                Timber.e(e, "手机验证失败")
                callback.onVerificationFailed(e)
            }

            override fun onCodeSent(verificationId: String, token: PhoneAuthProvider.ForceResendingToken) {
                Timber.d("验证码已发送: $verificationId")
                currentVerificationId = verificationId
                callback.onCodeSent(verificationId, token)
            }

            override fun onCodeAutoRetrievalTimeOut(verificationId: String) {
                Timber.d("验证码自动检索超时: $verificationId")
                callback.onVerificationTimeout()
            }
        }
}
