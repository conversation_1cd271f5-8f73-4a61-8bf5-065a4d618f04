### 是否需要改动「只写 Room」的保存策略？——结论先行

> **需要一定改动，但不是把 Room 扔掉**；应当
>
> 1. **把所有写入通路统一收敛到 `UserDataCenter` 层**，避免各 Feature 自己直写 Room；
> 2. **在 UserDataCenter 内继续用 Room 做离线持久化**，再配合「本地-远程双写同步」机制。
>    这样既保留离线能力，又保证以后接入云端／多端同步时不必再改 Feature 代码。

---

## 1  核心原则：**Single Source of Truth + Offline-first**

| 层级                               | 读 / 写责任                                                                            | 说明                                                                   |
| -------------------------------- | ---------------------------------------------------------------------------------- | -------------------------------------------------------------------- |
| **Feature（Auth／Profile／Coach…）** | 只调用 `UserDataCenterApi`<br>- `syncAuthData()`<br>- `updateUserProfile()`           | Feature 不再直接拿 DAO，也不关心 Room 还是 DataStore                             |
| **UserDataCenter**               | · 负责 **落盘（Room）**<br>· 负责 **上云（RemoteDataSource）**<br>· 暴露 `Flow<UnifiedUserData>` | 采用 **Network-Bound Source of Truth** 模式：<br>Room ←→ Remote ↔ Feature |
| **Room（AppDatabase）**            | 保存跨域用户信息                                                                           | 可继续利用现有 DAO/Migration；只是路径 **全部通过 UserDataCenter**                   |
| **Workout 专属 DB**                | 只存训练高频写入（Plan／Session）                                                             | 训练模块通过 `UserDataCenter.observeUserData()` 拿用户资料，无需 JOIN              |

---

## 2  具体改动点

| 现状                                                            | 存在问题         | 调整方案                                                                                                         |
| ------------------------------------------------------------- | ------------ | ------------------------------------------------------------------------------------------------------------ |
| **AuthModule → TokenDao / UserDao** 直接写 Room                  | 多入口写入，未来冲突难控 | Auth 成功后调用 `UserDataCenter.syncAuthData()`，内部再写 Room                                                         |
| **ProfileModule → ProfileDao** 直接写 Room                       | 同上           | `updateUserProfile()` 内统一落库                                                                                  |
| **Coach / Workout** 读取 `ProfileFeatureApi` 获得 `UserAiContext` | 跨库 JOIN、字段漂移 | 改为订阅 `UserDataCenter.observeUserData()`，转成 Prompt                                                            |
| **无远程同步**                                                     | 多端数据不一致      | 在 UserDataCenter 引入 `UserRemoteDataSource`：<br>- 启动时 **pull** 最新用户数据刷新 Room；<br>- 本地变更后 WorkManager **push** |

---

## 3  Room 仍然适合做什么？

1. **离线可用**：移动场景下网络不可用仍能取数。
2. **事务一致性**：用户数据通常一条或几条记录，Room + SQL 事务足够。
3. **Migration 成熟**：我们已经写了迁移脚本（如 `Migration(5,6)`），不用换存储。

> 支持 DataStore 的地方
>
> * 仅存「轻量偏好值」（单位、主题色等），不含业务实体。
> * 通过 `PreferencesDataStore` 注入，无需 Room。

---

## 4  改动后的数据流（保存场景）

```mermaid
sequenceDiagram
    participant AuthVM
    participant UDC as UserDataCenterApi
    participant Room
    participant Remote

    AuthVM->>UDC: syncAuthData(authUser)
    UDC->>Room: INSERT/UPDATE user_profile
    UDC->>Remote: enqueue PushWorker
    Note right of Room: 离线也成功
    Remote-->>UDC: 200 OK (or retry)
```

* **读**：Feature 层一直监听 `UDC.observeUserData()` （Flow）。
* **写**：任何模块写用户信息 → 只调用 UDC，UDC 先写本地，再异步 push 服务器。

---

## 5  实施清单（增量）

1. **抽象所有 DAO 写操作** → `UserLocalDataSource`，只被 UDC 调用。
2. **Feature 替换**

   * Auth 成功 → UDC.syncAuthData()
   * Profile 表单保存 → UDC.updateUserProfile()
3. **加 RemoteDataSource & WorkManager**，实现增量同步。
4. **删／改** 所有直接依赖 `UserDao`、`ProfileDao` 的旧代码。
5. **测试**：离线-上线切换、冲突时间戳合并。

---

### ✅ 这样做，你得到

* **无环依赖**：Feature-→Domain-→UserDataCenter-→Room。
* **可扩展**：远程同步、备份、云端合并都在 UDC 封装。
* **零硬编码**：所有 Room 字段只在 DAO 层出现，Feature 永远只拿 `UnifiedUserData`。
