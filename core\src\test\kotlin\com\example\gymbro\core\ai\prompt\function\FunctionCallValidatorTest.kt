package com.example.gymbro.core.ai.prompt.function

import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * FunctionCallValidator验证机制测试
 *
 * 验证Function Call验证器的完整功能
 * 基于静态分析报告的核心测试用例
 *
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0 (Function Call核心测试)
 */
class FunctionCallValidatorTest {

    private lateinit var validator: FunctionCallValidator

    @Before
    fun setUp() {
        validator = FunctionCallValidator()
    }

    @Test
    fun `test valid function names with gymbro prefix`() {
        // 测试有效的gymbro前缀函数名
        val validFunctionNames = listOf(
            "gymbro.exercise.search",
            "gymbro.template.generate",
            "gymbro.plan.get_detail",
            "gymbro.session.start",
        )

        validFunctionNames.forEach { functionName ->
            val functionCall = FunctionCall(
                name = functionName,
                arguments = "{\"query\": \"test\"}",
            )
            val result = validator.validateFunctionCall(functionCall)
            assertTrue("Function $functionName should be valid", result.isValid)
        }
    }

    @Test
    fun `test valid legacy function names`() {
        // 测试有效的旧版本函数名
        val legacyFunctionNames = listOf(
            "start_workout_session",
            "search_exercises",
            "log_exercise_set",
            "complete_workout_session",
        )

        legacyFunctionNames.forEach { functionName ->
            val functionCall = FunctionCall(
                name = functionName,
                arguments = "{\"sessionId\": \"test123\"}",
            )
            val result = validator.validateFunctionCall(functionCall)
            assertTrue("Legacy function $functionName should be valid", result.isValid)
        }
    }

    @Test
    fun `test invalid function names`() {
        // 测试无效的函数名
        val invalidFunctionNames = listOf(
            "invalid.function.name",
            "random_function",
            "gymbro.unknown.action",
            "",
            "   ",
            "gymbro.",
            ".exercise.search",
        )

        invalidFunctionNames.forEach { functionName ->
            val functionCall = FunctionCall(
                name = functionName,
                arguments = "{\"test\": \"value\"}",
            )
            val result = validator.validateFunctionCall(functionCall)
            assertFalse("Function $functionName should be invalid", result.isValid)
            assertTrue("Error message should be provided", result.message.isNotBlank())
        }
    }

    @Test
    fun `test parameter validation with valid JSON`() {
        // 测试有效的JSON参数格式
        val validJsonArguments = listOf(
            "{}",
            "{\"query\": \"test\"}",
            "{\"sessionId\": \"123\", \"exerciseId\": \"456\"}",
            "{\"name\": \"Test Template\", \"exercises\": []}",
        )

        validJsonArguments.forEach { jsonArgs ->
            val functionCall = FunctionCall(
                name = "gymbro.exercise.search",
                arguments = jsonArgs,
            )
            val result = validator.validateFunctionCall(functionCall)
            assertTrue("JSON arguments should be valid: $jsonArgs", result.isValid)
        }
    }

    @Test
    fun `test parameter validation with invalid JSON`() {
        // 测试无效的JSON参数格式
        val invalidJsonArguments = listOf(
            "invalid json",
            "{incomplete",
            "null",
            "{\"key\": }",
            "{\"key\": \"value\",}",
        )

        invalidJsonArguments.forEach { jsonArgs ->
            val functionCall = FunctionCall(
                name = "gymbro.exercise.search",
                arguments = jsonArgs,
            )
            val result = validator.validateFunctionCall(functionCall)
            assertFalse("JSON arguments should be invalid: $jsonArgs", result.isValid)
            assertTrue("Error message should be provided", result.message.isNotBlank())
        }
    }

    @Test
    fun `test business rule validation`() {
        // 测试业务规则验证
        val functionCall = FunctionCall(
            name = "gymbro.exercise.search",
            arguments = "{\"query\": \"\"}", // 空查询应该被拒绝
        )

        val result = validator.validateFunctionCall(functionCall)
        // 注意：具体的业务规则验证取决于实际实现
        // 这里只是验证验证器能够处理业务规则
        assertNotNull("Validation result should not be null", result)
    }

    @Test
    fun `test validation result structure`() {
        // 测试验证结果的结构
        val validCall = FunctionCall(
            name = "gymbro.exercise.search",
            arguments = "{\"query\": \"test\"}",
        )

        val validResult = validator.validateFunctionCall(validCall)
        assertTrue("Valid call should return valid result", validResult.isValid)
        assertEquals("Valid result should have success message", "验证通过", validResult.message)

        val invalidCall = FunctionCall(
            name = "invalid_function",
            arguments = "{\"test\": \"value\"}",
        )

        val invalidResult = validator.validateFunctionCall(invalidCall)
        assertFalse("Invalid call should return invalid result", invalidResult.isValid)
        assertNotNull("Invalid result should have error message", invalidResult.message)
        assertTrue("Error message should not be empty", invalidResult.message.isNotBlank())
    }
}
