package com.example.gymbro.designSystem.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ExitToApp
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material.icons.filled.Palette
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.GymBroTokenValidator
import com.example.gymbro.designSystem.theme.tokens.Tokens

/**
 * 通用列表项组件
 *
 * 采用标准列表项设计，符合平面化设计风格。
 * 支持UiText国际化，可在多个模块复用。
 *
 * @param title 主标题
 * @param onClick 点击回调，为null时不可点击
 * @param modifier 修饰符
 * @param subtitle 副标题，可选
 * @param leadingIcon 前导图标，可选
 * @param trailingIcon 尾随图标，可选
 * @param isDestructive 是否为破坏性操作（如删除、退出等）
 */
@Composable
fun GymBroListItem(
    title: UiText,
    onClick: (() -> Unit)? = null,
    modifier: Modifier = Modifier,
    subtitle: UiText? = null,
    leadingIcon: ImageVector? = null,
    trailingIcon: ImageVector? = null,
    isDestructive: Boolean = false,
) {
    // Token 使用验证 (仅在 DEBUG 模式下)
    GymBroTokenValidator.validateTokenUsage("GymBroListItem")
    Surface(
        modifier =
            modifier
                .fillMaxWidth()
                .then(
                    if (onClick != null) {
                        Modifier.clickable { onClick() }
                    } else {
                        Modifier
                    },
                ),
        color = MaterialTheme.colorScheme.surface,
    ) {
        Row(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .height(Tokens.ListItem.HeightSmall) // 48.dp - 使用 Token
                    .padding(horizontal = Tokens.Spacing.Medium, vertical = Tokens.Spacing.Small),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium), // 美化方案：Icon→文本 12dp
        ) {
            // 前导图标
            if (leadingIcon != null) {
                Icon(
                    imageVector = leadingIcon,
                    contentDescription = title.asString(),
                    modifier = Modifier.size(Tokens.Spacing.Large),
                    tint =
                        if (isDestructive) {
                            MaterialTheme.colorScheme.error
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        },
                )
            }

            // 文字内容
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.XSmall),
            ) {
                Text(
                    text = title.asString(),
                    style = MaterialTheme.typography.bodyLarge,
                    color =
                        if (isDestructive) {
                            MaterialTheme.colorScheme.error
                        } else {
                            MaterialTheme.colorScheme.onSurface
                        },
                )

                if (subtitle != null) {
                    Text(
                        text = subtitle.asString(),
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                    )
                }
            }

            // 尾随图标 - 美化方案：文本→箭头 8dp
            if (trailingIcon != null) {
                Spacer(modifier = Modifier.width(Tokens.Spacing.Small)) // 8dp间距
                Icon(
                    imageVector = trailingIcon,
                    contentDescription = null,
                    modifier = Modifier.size(Tokens.Spacing.Medium),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant,
                )
            }
        }
    }
}

/**
 * 通用列表分组组件
 *
 * 用于将相关的列表项组织在一起，提供分组标题和统一的视觉容器。
 *
 * @param title 分组标题
 * @param modifier 修饰符
 * @param content 分组内容
 */
@Composable
fun GymBroListGroup(
    title: UiText,
    modifier: Modifier = Modifier,
    content: @Composable ColumnScope.() -> Unit,
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
    ) {
        // 分组标题
        Text(
            text = title.asString(),
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = Tokens.Spacing.XSmall),
        )

        // 分组内容容器 - 美化方案：使用SurfaceVariant背景 + 12dp内边距
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = Tokens.Elevation.Card),
            shape = RoundedCornerShape(Tokens.Radius.Medium), // 12.dp - 使用 Token
            colors =
                CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant,
                ),
        ) {
            Column(
                modifier = Modifier.padding(Tokens.Spacing.Medium), // 12dp内边距
            ) {
                content()
            }
        }
    }
}

/**
 * 列表项分隔线
 *
 * 用于在列表项之间添加分隔线。
 */
@Composable
fun GymBroListDivider(
    modifier: Modifier = Modifier,
) {
    HorizontalDivider(
        modifier = modifier.padding(horizontal = Tokens.Spacing.Medium),
        color = MaterialTheme.colorScheme.outline.copy(alpha = 0.12f),
    )
}

// ===== Preview Functions =====

@GymBroPreview
@Composable
private fun GymBroListItemSimplePreview() {
    GymBroTheme {
        GymBroListItem(
            title = UiText.DynamicString("个人设置"),
            leadingIcon = Icons.Default.Person,
            trailingIcon = Icons.Default.ChevronRight,
            onClick = {},
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroListItemWithSubtitlePreview() {
    GymBroTheme {
        GymBroListItem(
            title = UiText.DynamicString("用户名"),
            subtitle = UiText.DynamicString("健身达人"),
            leadingIcon = Icons.Default.Person,
            trailingIcon = Icons.Default.ChevronRight,
            onClick = {},
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroListItemDestructivePreview() {
    GymBroTheme {
        GymBroListItem(
            title = UiText.DynamicString("退出登录"),
            leadingIcon = Icons.AutoMirrored.Filled.ExitToApp,
            onClick = {},
            isDestructive = true,
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroListGroupPreview() {
    GymBroTheme {
        GymBroListGroup(
            title = UiText.DynamicString("个人设置"),
        ) {
            GymBroListItem(
                title = UiText.DynamicString("个人信息"),
                leadingIcon = Icons.Default.Person,
                trailingIcon = Icons.Default.ChevronRight,
                onClick = {},
            )
            GymBroListDivider()
            GymBroListItem(
                title = UiText.DynamicString("通知设置"),
                leadingIcon = Icons.Default.Notifications,
                trailingIcon = Icons.Default.ChevronRight,
                onClick = {},
            )
            GymBroListDivider()
            GymBroListItem(
                title = UiText.DynamicString("主题设置"),
                subtitle = UiText.DynamicString("跟随系统"),
                leadingIcon = Icons.Default.Palette,
                trailingIcon = Icons.Default.ChevronRight,
                onClick = {},
            )
        }
    }
}

@GymBroPreview
@Composable
private fun GymBroListItemDarkPreview() {
    GymBroTheme(darkTheme = true) {
        Column(
            modifier = Modifier.padding(Tokens.Spacing.Medium),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            GymBroListItem(
                title = UiText.DynamicString("个人信息"),
                subtitle = UiText.DynamicString("管理您的个人资料"),
                leadingIcon = Icons.Default.Person,
                trailingIcon = Icons.Default.ChevronRight,
                onClick = {},
            )
            GymBroListItem(
                title = UiText.DynamicString("退出登录"),
                leadingIcon = Icons.AutoMirrored.Filled.ExitToApp,
                onClick = {},
                isDestructive = true,
            )
        }
    }
}
