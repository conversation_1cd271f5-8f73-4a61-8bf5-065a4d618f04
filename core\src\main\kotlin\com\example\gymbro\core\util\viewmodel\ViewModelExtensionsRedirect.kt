package com.example.gymbro.core.util.viewmodel

/**
 * 此文件用于维护与 com.example.gymbro.core.util.viewmodel 包的兼容性
 * 请使用新位置的扩展函数: com.example.gymbro.core.util.viewmodel.ViewModelExtensions.kt
 *
 * 为避免冲突，该文件仅提供导入说明以引导使用新包中的扩展函数。
 * 必须显式导入新位置的扩展函数才能使用。
 *
 * 示例:
 * ```
 * import com.example.gymbro.core.util.viewmodel.handleInViewModel
 * import com.example.gymbro.core.util.viewmodel.mapToUiState
 * import com.example.gymbro.core.util.viewmodel.getErrorMessage
 * import com.example.gymbro.core.util.viewmodel.handleBySuggestedAction
 * ```
 *
 * @see handleInViewModel
 * @see mapToUiState
 * @see getErrorMessage
 * @see handleBySuggestedAction
 * @see createStateLoader
 */
