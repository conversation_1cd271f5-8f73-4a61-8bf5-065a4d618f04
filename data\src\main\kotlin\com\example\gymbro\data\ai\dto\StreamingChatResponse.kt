package com.example.gymbro.data.ai.dto

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * 流式聊天响应数据模型
 *
 * 用于处理SSE (Server-Sent Events) 流式响应
 * 兼容OpenAI Chat Completions API的流式格式
 */
@Serializable
data class StreamingChatResponse(
    /**
     * 响应ID
     */
    @SerialName("id")
    val id: String,
    /**
     * 响应选择列表
     */
    @SerialName("choices")
    val choices: List<StreamingChoice>,
    /**
     * 创建时间戳
     */
    @SerialName("created")
    val created: Long,
    /**
     * 使用的模型名称
     */
    @SerialName("model")
    val model: String,
    /**
     * 对象类型，通常为"chat.completion.chunk"
     */
    @SerialName("object")
    val objectType: String = "chat.completion.chunk",
)

/**
 * 流式响应选择
 */
@Serializable
data class StreamingChoice(
    /**
     * 增量数据
     */
    @SerialName("delta")
    val delta: StreamingDelta,
    /**
     * 选择索引
     */
    @SerialName("index")
    val index: Int,
    /**
     * 完成原因
     * null表示未完成，"stop"表示正常结束，"length"表示达到最大长度
     */
    @SerialName("finish_reason")
    val finishReason: String? = null,
)

/**
 * 流式增量数据
 * Sprint 0 升级：添加Function Call支持
 * 🔧 硬伤修复：添加 reasoning_content 支持 DeepSeek
 */
@Serializable
data class StreamingDelta(
    /**
     * 增量内容
     * 每个chunk包含一小段文本
     */
    @SerialName("content")
    val content: String? = null,
    /**
     * 推理内容 - DeepSeek 专用字段
     * DeepSeek 会先输出推理过程，然后输出最终内容
     */
    @SerialName("reasoning_content")
    val reasoningContent: String? = null,
    /**
     * 消息角色
     * 通常只在第一个chunk中出现
     */
    @SerialName("role")
    val role: String? = null,
    /**
     * Function Call数据
     * 当AI要求执行函数时出现
     */
    @SerialName("function_call")
    val functionCall: FunctionCallData? = null,
)

/**
 * Function Call数据结构
 */
@Serializable
data class FunctionCallData(
    /**
     * 函数名称
     */
    @SerialName("name")
    val name: String? = null,
    /**
     * 函数参数（JSON字符串）
     */
    @SerialName("arguments")
    val arguments: String? = null,
)

/**
 * 流式响应事件 - 基于最佳实践的事件模型
 * Sprint 0 升级：添加Function Call支持
 * 性能优化：增加 ContentDelta 事件，支持内容过滤和完整文本传递
 */
sealed interface StreamEvent {
    /**
     * Thinking事件 - AI开始思考（串行插入后的首包事件）
     */
    data class Thinking(
        val thinkingId: String,
    ) : StreamEvent

    /**
     * 数据块事件（原始版本，保持向后兼容）
     */
    data class Chunk(
        val id: String,
        val content: String,
    ) : StreamEvent

    /**
     * 内容增量事件 - 性能优化版本
     *
     * @param thinkingId 思考ID（统一主键）
     * @param fullText 完整累积文本（避免UI层字符串拼接）
     * @param append 本次增量内容（用于验证和调试）
     * @param raw 原始内容（包含reasoning_content等）
     * @param visible 可见内容（过滤后的content）
     */
    data class ContentDelta(
        val thinkingId: String, // 改为使用thinkingId作为统一主键
        val fullText: String, // 完整累积文本
        val append: String, // 本次增量
        val raw: String = "", // 原始内容（可选）
        val visible: String = append, // 可见内容（默认等于增量）
    ) : StreamEvent

    /**
     * Function Call事件 - AI要求执行函数调用
     */
    data class FunctionCall(
        val id: String,
        val functionName: String,
        val arguments: String,
        val callId: String,
    ) : StreamEvent

    /**
     * 完成事件 - 增强版本
     *
     * @param thinkingId 思考ID（统一主键）
     * @param fullText 最终完整文本（确保UI显示完整内容）
     * @param finishReason 完成原因
     */
    data class Done(
        val thinkingId: String, // 改为使用thinkingId作为统一主键
        val fullText: String = "", // 最终完整文本
        val finishReason: String? = null,
    ) : StreamEvent

    /**
     * 错误事件
     */
    data class Error(
        val thinkingId: String?, // 添加thinkingId，可能为null（如果是全局错误）
        val throwable: Throwable,
    ) : StreamEvent
}

/**
 * 流式响应事件类型（保持向后兼容）
 */
enum class StreamingEventType {
    /**
     * 数据事件
     */
    DATA,

    /**
     * 完成事件
     */
    DONE,

    /**
     * 错误事件
     */
    ERROR,
}

/**
 * 流式响应事件（保持向后兼容）
 */
data class StreamingEvent(
    /**
     * 事件类型
     */
    val type: StreamingEventType,
    /**
     * 事件数据
     */
    val data: String,
    /**
     * 解析后的响应（仅当type为DATA时有效）
     */
    val response: StreamingChatResponse? = null,
    /**
     * 错误信息（仅当type为ERROR时有效）
     */
    val error: String? = null,
)
