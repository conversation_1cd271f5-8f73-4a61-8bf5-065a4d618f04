package com.example.gymbro.designSystem.theme.motion

import androidx.compose.runtime.*
import androidx.compose.runtime.compositionLocalOf
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

/**
 * GymBro动画策略管理器
 *
 * 提供动画限流、性能监控和自适应质量控制
 * 防止过多无限循环动画同时运行导致的性能问题
 */
object MotionPolicy {

    // === 动画限制配置 ===
    private const val MAX_INFINITE_ANIMATIONS = 3 // 最大同时运行的无限循环动画数量
    private const val MAX_BREATHING_ANIMATIONS = 2 // 最大同时运行的呼吸动画数量
    private const val PERFORMANCE_CHECK_INTERVAL = 1000L // 性能检查间隔（毫秒）

    // === 动画注册表 ===
    private val activeInfiniteAnimations = mutableSetOf<String>()
    private val activeBreathingAnimations = mutableSetOf<String>()
    private val animationMutex = Mutex()

    // === 性能监控 ===
    private var lastFrameTime = 0L
    private var frameCount = 0
    private var averageFrameRate = 60f

    /**
     * 动画质量等级
     */
    enum class AnimationQuality {
        HIGH, // 高质量：所有动画正常运行
        MEDIUM, // 中等质量：限制部分动画
        LOW, // 低质量：只保留关键动画
        MINIMAL, // 最小质量：禁用大部分动画
    }

    /**
     * 当前动画质量等级
     */
    private var _currentQuality by mutableStateOf(AnimationQuality.HIGH)
    val currentQuality: AnimationQuality get() = _currentQuality

    /**
     * 注册无限循环动画
     * @param animationId 动画唯一标识符
     * @return 是否允许运行该动画
     */
    suspend fun registerInfiniteAnimation(animationId: String): Boolean {
        return animationMutex.withLock {
            if (activeInfiniteAnimations.size >= MAX_INFINITE_ANIMATIONS) {
                false
            } else {
                activeInfiniteAnimations.add(animationId)
                true
            }
        }
    }

    /**
     * 注册呼吸动画
     * @param animationId 动画唯一标识符
     * @return 是否允许运行该动画
     */
    suspend fun registerBreathingAnimation(animationId: String): Boolean {
        return animationMutex.withLock {
            if (activeBreathingAnimations.size >= MAX_BREATHING_ANIMATIONS) {
                false
            } else {
                activeBreathingAnimations.add(animationId)
                true
            }
        }
    }

    /**
     * 注销无限循环动画
     */
    suspend fun unregisterInfiniteAnimation(animationId: String) {
        animationMutex.withLock {
            activeInfiniteAnimations.remove(animationId)
        }
    }

    /**
     * 注销呼吸动画
     */
    suspend fun unregisterBreathingAnimation(animationId: String) {
        animationMutex.withLock {
            activeBreathingAnimations.remove(animationId)
        }
    }

    /**
     * 检查是否应该运行动画
     */
    fun shouldRunAnimation(animationType: AnimationType): Boolean {
        return when (animationType) {
            AnimationType.INFINITE -> activeInfiniteAnimations.size < MAX_INFINITE_ANIMATIONS
            AnimationType.BREATHING -> activeBreathingAnimations.size < MAX_BREATHING_ANIMATIONS
            AnimationType.MICRO_INTERACTION -> _currentQuality != AnimationQuality.MINIMAL
            AnimationType.PAGE_TRANSITION -> true // 页面转换动画始终允许
            AnimationType.DECORATIVE -> _currentQuality == AnimationQuality.HIGH
        }
    }

    /**
     * 动画类型枚举
     */
    enum class AnimationType {
        INFINITE, // 无限循环动画
        BREATHING, // 呼吸动画
        MICRO_INTERACTION, // 微交互动画
        PAGE_TRANSITION, // 页面转换动画
        DECORATIVE, // 装饰性动画
    }

    /**
     * 更新性能指标
     */
    fun updatePerformanceMetrics(frameTime: Long) {
        val currentTime = System.currentTimeMillis()

        if (lastFrameTime > 0) {
            val deltaTime = currentTime - lastFrameTime
            if (deltaTime > 0) {
                val currentFrameRate = 1000f / deltaTime
                averageFrameRate = (averageFrameRate * 0.9f + currentFrameRate * 0.1f)

                // 根据帧率调整动画质量
                adjustQualityBasedOnPerformance()
            }
        }

        lastFrameTime = currentTime
        frameCount++
    }

    /**
     * 根据性能调整动画质量
     */
    private fun adjustQualityBasedOnPerformance() {
        _currentQuality = when {
            averageFrameRate >= 55f -> AnimationQuality.HIGH
            averageFrameRate >= 45f -> AnimationQuality.MEDIUM
            averageFrameRate >= 30f -> AnimationQuality.LOW
            else -> AnimationQuality.MINIMAL
        }
    }

    /**
     * 获取当前活跃动画统计
     */
    fun getAnimationStats(): AnimationStats {
        return AnimationStats(
            infiniteAnimations = activeInfiniteAnimations.size,
            breathingAnimations = activeBreathingAnimations.size,
            averageFrameRate = averageFrameRate,
            currentQuality = _currentQuality,
        )
    }

    /**
     * 强制设置动画质量
     */
    fun setAnimationQuality(quality: AnimationQuality) {
        _currentQuality = quality
    }

    /**
     * 重置所有动画注册
     */
    suspend fun resetAllAnimations() {
        animationMutex.withLock {
            activeInfiniteAnimations.clear()
            activeBreathingAnimations.clear()
        }
    }

    /**
     * 动画统计数据
     */
    data class AnimationStats(
        val infiniteAnimations: Int,
        val breathingAnimations: Int,
        val averageFrameRate: Float,
        val currentQuality: AnimationQuality,
    )
}

/**
 * Composable函数：提供动画策略上下文
 */
@Composable
fun ProvideMotionPolicy(
    quality: MotionPolicy.AnimationQuality = MotionPolicy.AnimationQuality.HIGH,
    content: @Composable () -> Unit,
) {
    LaunchedEffect(quality) {
        MotionPolicy.setAnimationQuality(quality)
    }

    DisposableEffect(Unit) {
        onDispose {
            // 清理资源
        }
    }

    content()
}

/**
 * 记住动画策略状态
 */
@Composable
fun rememberMotionPolicyState(): State<MotionPolicy.AnimationStats> {
    var stats by remember { mutableStateOf(MotionPolicy.getAnimationStats()) }

    LaunchedEffect(Unit) {
        // 定期更新统计信息
        kotlinx.coroutines.delay(1000)
        stats = MotionPolicy.getAnimationStats()
    }

    return remember { derivedStateOf { stats } }
}

// === Motion配置系统 ===

/**
 * GymBro动画配置
 * 控制全局动画行为和性能设置
 */
data class GymBroMotionConfig(
    val enableAnimations: Boolean = true,
    val enableBreathing: Boolean = true,
    val enableInfiniteAnimations: Boolean = true,
    val enableMicroInteractions: Boolean = true,
    val animationQuality: MotionPolicy.AnimationQuality = MotionPolicy.AnimationQuality.HIGH,
    val respectSystemAnimationScale: Boolean = true,
    val maxConcurrentAnimations: Int = 5,
)

/**
 * 提供Motion配置的CompositionLocal
 */
val LocalGymBroMotionConfig = compositionLocalOf {
    GymBroMotionConfig()
}

/**
 * 提供Motion配置上下文
 */
@Composable
fun ProvideGymBroMotionConfig(
    config: GymBroMotionConfig = GymBroMotionConfig(),
    content: @Composable () -> Unit,
) {
    CompositionLocalProvider(
        LocalGymBroMotionConfig provides config,
        content = content,
    )
}
