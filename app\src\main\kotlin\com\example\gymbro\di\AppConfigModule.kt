package com.example.gymbro.di

import com.example.gymbro.BuildConfig
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named

/**
 * 应用配置模块
 *
 * 提供从BuildConfig读取的配置参数，覆盖DI模块中的默认值
 */
@Module
@InstallIn(SingletonComponent::class)
object AppConfigModule {

    @Provides
    @Named("google_api_key")
    fun provideGoogleApiKey(): String {
        return BuildConfig.GOOGLE_API_KEY
    }

    @Provides
    @Named("google_base_url")
    fun provideGoogleBaseUrl(): String {
        return BuildConfig.GOOGLE_BASE_URL
    }

    @Provides
    @Named("openai_api_key")
    fun provideOpenaiApiKey(): String {
        return BuildConfig.OPENAI_API_KEY
    }

    @Provides
    @Named("deepseek_api_key")
    fun provideDeepseekApiKey(): String {
        return BuildConfig.DEEPSEEK_API_KEY
    }

    @Provides
    @Named("deepseek_base_url")
    fun provideDeepseekBaseUrl(): String {
        return BuildConfig.DEEPSEEK_BASE_URL
    }

    @Provides
    @Named("default_google_model")
    fun provideDefaultGoogleModel(): String {
        return BuildConfig.DEFAULT_GOOGLE_MODEL
    }

    @Provides
    @Named("default_deepseek_model")
    fun provideDefaultDeepseekModel(): String {
        return BuildConfig.DEFAULT_DEEPSEEK_MODEL
    }

    @Provides
    @Named("openai_default_model")
    fun provideOpenaiDefaultModel(): String {
        return BuildConfig.OPENAI_DEFAULT_MODEL
    }

    @Provides
    @Named("openai_base_url")
    fun provideOpenaiBaseUrl(): String {
        return BuildConfig.OPENAI_BASE_URL
    }

    /**
     * 提供AI基础URL配置
     * 用于AiNetworkModule中的Retrofit配置
     * 默认使用DeepSeek作为主要AI提供商
     */
    @Provides
    @Named("ai_base_url")
    fun provideAiBaseUrl(): String {
        return BuildConfig.DEEPSEEK_BASE_URL
    }
}
