package com.example.gymbro.core.arch.mvi

/**
 * MVI架构 - 用户意图抽象
 *
 * 所有Feature模块的Intent都应该继承此接口
 * 用于表示用户交互产生的意图或系统事件
 *
 * @since v1.1 - MVI基座建设
 */
interface AppIntent

/**
 * 系统级通用意图
 */
sealed class SystemIntent : AppIntent {
    /**
     * 重试操作
     */
    object Retry : SystemIntent()

    /**
     * 清除错误状态
     */
    object ClearError : SystemIntent()

    /**
     * 刷新页面
     */
    object Refresh : SystemIntent()

    /**
     * 导航返回
     */
    object NavigateBack : SystemIntent()
}
