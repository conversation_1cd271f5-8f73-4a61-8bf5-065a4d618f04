package com.example.gymbro.data.coach.dao

import androidx.room.*
import com.example.gymbro.data.local.entity.ChatRaw
import com.example.gymbro.data.local.entity.ChatRawWithVector
import kotlinx.coroutines.flow.Flow

/**
 * 聊天原始数据访问对象
 *
 * 提供聊天消息的基本CRUD操作
 */
@Dao
interface ChatRawDao {
    /**
     * 插入聊天消息
     *
     * @param chatRaw 聊天消息实体
     * @return 插入的消息ID
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertChatMessage(chatRaw: ChatRaw): Long

    /**
     * 批量插入聊天消息
     *
     * @param chatRaws 聊天消息实体列表
     * @return 插入的消息ID列表
     */
    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun insertChatMessages(chatRaws: List<ChatRaw>): List<Long>

    /**
     * 插入thinking占位消息（串行预插入）
     *
     * @param thinking thinking消息实体
     * @return 插入的消息ID
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertThinking(thinking: ChatRaw): Long

    /**
     * 更新聊天消息
     *
     * @param chatRaw 聊天消息实体
     */
    @Update
    suspend fun updateChatMessage(chatRaw: ChatRaw)

    /**
     * 删除聊天消息
     *
     * @param chatRaw 聊天消息实体
     */
    @Delete
    suspend fun deleteChatMessage(chatRaw: ChatRaw)

    /**
     * 根据ID获取聊天消息
     *
     * @param id 消息ID
     * @return 聊天消息实体，如果不存在则返回null
     */
    @Query("SELECT * FROM chat_raw WHERE id = :id")
    suspend fun getChatMessageById(id: Long): ChatRaw?

    /**
     * 🔥 【MessageId一致性修复】根据messageId获取聊天消息
     *
     * @param messageId 消息唯一标识符
     * @return 聊天消息实体，如果不存在则返回null
     */
    @Query("SELECT * FROM chat_raw WHERE message_id = :messageId")
    suspend fun getMessageById(messageId: String): ChatRaw?

    /**
     * 根据会话ID获取聊天消息列表
     *
     * @param sessionId 会话ID
     * @return 聊天消息列表，按时间戳升序排列
     */
    @Query("SELECT * FROM chat_raw WHERE session_id = :sessionId ORDER BY timestamp ASC")
    suspend fun getChatMessagesBySession(sessionId: String): List<ChatRaw>

    /**
     * 根据会话ID获取聊天消息流
     *
     * @param sessionId 会话ID
     * @return 聊天消息流，按时间戳升序排列
     */
    @Query("SELECT * FROM chat_raw WHERE session_id = :sessionId ORDER BY timestamp ASC")
    fun getChatMessagesFlowBySession(sessionId: String): Flow<List<ChatRaw>>

    /**
     * 获取所有聊天消息
     *
     * @return 聊天消息列表，按时间戳降序排列
     */
    @Query("SELECT * FROM chat_raw ORDER BY timestamp DESC")
    suspend fun getAllChatMessages(): List<ChatRaw>

    /**
     * 获取所有聊天消息流
     *
     * @return 聊天消息流，按时间戳降序排列
     */
    @Query("SELECT * FROM chat_raw ORDER BY timestamp DESC")
    fun getAllChatMessagesFlow(): Flow<List<ChatRaw>>

    /**
     * 根据角色获取聊天消息
     *
     * @param role 消息角色（user/assistant）
     * @return 聊天消息列表，按时间戳降序排列
     */
    @Query("SELECT * FROM chat_raw WHERE role = :role ORDER BY timestamp DESC")
    suspend fun getChatMessagesByRole(role: String): List<ChatRaw>

    /**
     * 获取最近的聊天消息
     *
     * @param limit 消息数量限制
     * @return 聊天消息列表，按时间戳降序排列
     */
    @Query("SELECT * FROM chat_raw ORDER BY timestamp DESC LIMIT :limit")
    suspend fun getRecentChatMessages(limit: Int = 50): List<ChatRaw>

    /**
     * 根据时间范围获取聊天消息
     *
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @return 聊天消息列表，按时间戳升序排列
     */
    @Query("SELECT * FROM chat_raw WHERE timestamp BETWEEN :startTime AND :endTime ORDER BY timestamp ASC")
    suspend fun getChatMessagesByTimeRange(
        startTime: Long,
        endTime: Long,
    ): List<ChatRaw>

    /**
     * 获取聊天消息总数
     *
     * @return 消息总数
     */
    @Query("SELECT COUNT(*) FROM chat_raw")
    suspend fun getChatMessageCount(): Int

    /**
     * 根据会话ID获取消息数量
     *
     * @param sessionId 会话ID
     * @return 该会话的消息数量
     */
    @Query("SELECT COUNT(*) FROM chat_raw WHERE session_id = :sessionId")
    suspend fun getChatMessageCountBySession(sessionId: String): Int

    /**
     * 获取所有会话ID
     *
     * @return 会话ID列表，按最新消息时间戳降序排列
     */
    @Query("SELECT DISTINCT session_id FROM chat_raw GROUP BY session_id ORDER BY MAX(timestamp) DESC")
    suspend fun getAllSessionIds(): List<String>

    /**
     * 删除指定会话的所有消息
     *
     * @param sessionId 会话ID
     */
    @Query("DELETE FROM chat_raw WHERE session_id = :sessionId")
    suspend fun deleteChatMessagesBySession(sessionId: String)

    /**
     * 删除指定时间之前的消息
     *
     * @param cutoffTime 截止时间戳
     */
    @Query("DELETE FROM chat_raw WHERE timestamp < :cutoffTime")
    suspend fun deleteOldChatMessages(cutoffTime: Long)

    /**
     * 清空所有聊天消息
     */
    @Query("DELETE FROM chat_raw")
    suspend fun deleteAllChatMessages()

    /**
     * 获取会话的最近消息（用于上下文传递）
     *
     * @param sessionId 会话ID
     * @param limit 消息数量限制，默认10条
     * @return 最近的消息列表，按时间戳升序排列
     */
    @Query("SELECT * FROM chat_raw WHERE session_id = :sessionId ORDER BY timestamp DESC LIMIT :limit")
    suspend fun getRecentMessagesBySession(
        sessionId: String,
        limit: Int = 10,
    ): List<ChatRaw>

    /**
     * 获取会话的消息（分页）
     *
     * @param sessionId 会话ID
     * @param limit 消息数量限制
     * @param offset 偏移量
     * @return 消息列表，按时间戳升序排列
     */
    @Query(
        "SELECT * FROM chat_raw WHERE session_id = :sessionId ORDER BY timestamp ASC LIMIT :limit OFFSET :offset",
    )
    suspend fun getMessagesBySessionPaged(
        sessionId: String,
        limit: Int,
        offset: Int,
    ): List<ChatRaw>

    /**
     * 获取会话的消息（分页，降序）
     *
     * @param sessionId 会话ID
     * @param limit 消息数量限制
     * @param offset 偏移量
     * @return 消息列表，按时间戳降序排列（ChatGPT式体验）
     */
    @Query(
        "SELECT * FROM chat_raw WHERE session_id = :sessionId ORDER BY timestamp DESC LIMIT :limit OFFSET :offset",
    )
    suspend fun getMessagesBySessionPagedDesc(
        sessionId: String,
        limit: Int,
        offset: Int,
    ): List<ChatRaw>

    /**
     * 获取会话的消息流（分页）
     *
     * @param sessionId 会话ID
     * @param limit 消息数量限制
     * @param offset 偏移量
     * @return 消息流，按时间戳升序排列
     */
    @Query(
        "SELECT * FROM chat_raw WHERE session_id = :sessionId ORDER BY timestamp ASC LIMIT :limit OFFSET :offset",
    )
    fun getMessagesFlowBySessionPaged(
        sessionId: String,
        limit: Int,
        offset: Int,
    ): Flow<List<ChatRaw>>

    /**
     * 检查会话是否有消息
     *
     * @param sessionId 会话ID
     * @return 如果会话有消息返回true
     */
    @Query("SELECT COUNT(*) > 0 FROM chat_raw WHERE session_id = :sessionId")
    suspend fun sessionHasMessages(sessionId: String): Boolean

    // ===== Task2-CoachContext数据中心集成：RAG相关查询方法 =====

    /**
     * 获取所有有向量的聊天消息
     * 用于向量搜索，需要JOIN ChatVec表
     *
     * @return 有向量的聊天消息列表
     */
    @Query(
        """
        SELECT cr.id, cr.session_id, cr.role, cr.content, cr.timestamp,
               cr.metadata, cr.message_id, cr.in_reply_to_message_id,
               cr.thinking_nodes, cr.final_markdown, cv.embedding as vector
        FROM chat_raw cr
        INNER JOIN chat_vec cv ON cr.id = cv.id
        ORDER BY cr.timestamp DESC
    """,
    )
    suspend fun getAllChatMessagesWithVectors(): List<ChatRawWithVector>

    /**
     * 根据会话ID获取有向量的聊天消息
     * 用于会话内向量搜索
     *
     * @param sessionId 会话ID
     * @return 该会话中有向量的聊天消息列表
     */
    @Query(
        """
        SELECT cr.id, cr.session_id, cr.role, cr.content, cr.timestamp,
               cr.metadata, cr.message_id, cr.in_reply_to_message_id,
               cr.thinking_nodes, cr.final_markdown, cv.embedding as vector
        FROM chat_raw cr
        INNER JOIN chat_vec cv ON cr.id = cv.id
        WHERE cr.session_id = :sessionId
        ORDER BY cr.timestamp DESC
    """,
    )
    suspend fun getChatMessagesWithVectorsBySession(sessionId: String): List<ChatRawWithVector>

    /**
     * 根据会话ID获取最近的聊天消息
     * 用于获取对话历史上下文
     *
     * @param sessionId 会话ID
     * @param limit 消息数量限制
     * @return 最近的聊天消息列表，按时间戳降序排列
     */
    @Query("SELECT * FROM chat_raw WHERE session_id = :sessionId ORDER BY timestamp DESC LIMIT :limit")
    suspend fun getRecentChatMessagesBySession(
        sessionId: String,
        limit: Int,
    ): List<ChatRaw>

    /**
     * 获取所有没有向量的聊天消息
     * 用于批量向量化索引
     *
     * @return 没有向量的聊天消息列表
     */
    @Query(
        """
        SELECT cr.* FROM chat_raw cr
        LEFT JOIN chat_vec cv ON cr.id = cv.id
        WHERE cv.id IS NULL
        ORDER BY cr.timestamp ASC
    """,
    )
    suspend fun getChatMessagesWithoutVectors(): List<ChatRaw>

    // ===== Autosave集成：事务方法 =====

    /**
     * 事务方法：保存消息并更新会话信息
     *
     * 确保消息插入和会话更新操作的原子性，避免数据不一致的风险
     *
     * @param sessionId 会话ID
     * @param chatRaw 聊天消息实体
     * @param updateMessageCount 是否更新消息数量，默认为true
     * @return 插入的消息ID
     */
    @Transaction
    suspend fun saveMessageWithTransaction(
        sessionId: String,
        chatRaw: ChatRaw,
        updateMessageCount: Boolean = true,
    ): Long {
        // 1. 插入聊天消息
        val insertedId = insertChatMessage(chatRaw)

        // 2. 验证插入结果
        if (insertedId == -1L) {
            throw IllegalStateException(
                "ChatRaw insert failed – sessionId=$sessionId, messageId=${chatRaw.messageId}",
            )
        }

        // 3. 如果需要，更新会话的消息数量
        if (updateMessageCount) {
            val messageCount = getChatMessageCountBySession(sessionId)
            // 注意：这里需要通过Repository层调用ChatSessionDao.updateMessageCount
            // 由于DAO层不应该直接依赖其他DAO，这个操作应该在Repository层完成
        }

        return insertedId
    }

    /**
     * 事务方法：批量保存消息
     *
     * 确保批量消息插入的原子性，要么全部成功，要么全部失败
     *
     * @param sessionId 会话ID
     * @param chatRaws 聊天消息实体列表
     * @return 插入的消息ID列表
     */
    @Transaction
    suspend fun saveBatchMessagesWithTransaction(
        sessionId: String,
        chatRaws: List<ChatRaw>,
    ): List<Long> {
        if (chatRaws.isEmpty()) {
            return emptyList()
        }

        // 1. 批量插入聊天消息
        val insertedIds = insertChatMessages(chatRaws)

        // 2. 验证插入结果
        if (insertedIds.size != chatRaws.size) {
            throw IllegalStateException(
                "Batch ChatRaw insert failed – expected ${chatRaws.size}, got ${insertedIds.size}",
            )
        }

        // 3. 检查是否有插入失败的消息
        val failedInserts = insertedIds.count { it == -1L }
        if (failedInserts > 0) {
            throw IllegalStateException(
                "Batch ChatRaw insert partially failed – $failedInserts out of ${chatRaws.size} failed",
            )
        }

        return insertedIds
    }

    /**
     * 事务方法：保存AI消息并建立关联
     *
     * 专门用于保存AI回复消息，确保消息关联关系的正确性
     *
     * @param sessionId 会话ID
     * @param aiChatRaw AI消息实体
     * @param inReplyToMessageId 回复的用户消息ID
     * @return 插入的消息ID
     */
    @Transaction
    suspend fun saveAiMessageWithTransaction(
        sessionId: String,
        aiChatRaw: ChatRaw,
        inReplyToMessageId: String,
    ): Long {
        // 1. 验证回复关联的消息存在
        val replyToMessage =
            getChatMessagesBySession(sessionId)
                .find { it.messageId == inReplyToMessageId }

        if (replyToMessage == null) {
            throw IllegalArgumentException("Reply target message not found: $inReplyToMessageId")
        }

        // 2. 确保AI消息包含正确的关联信息
        val aiMessageWithReply =
            aiChatRaw.copy(
                inReplyToMessageId = inReplyToMessageId,
            )

        // 3. 插入AI消息
        val insertedId = insertChatMessage(aiMessageWithReply)

        // 4. 验证插入结果
        if (insertedId == -1L) {
            throw IllegalStateException(
                "AI ChatRaw insert failed – sessionId=$sessionId, aiMessageId=${aiChatRaw.messageId}",
            )
        }

        return insertedId
    }
}
