package com.example.gymbro.core.autosave.storage

/**
 * 自动保存存储后端接口
 *
 * @param T 要保存的数据类型
 */
interface AutoSaveStorage<T : Any> {

    /**
     * 保存数据
     *
     * @param id 数据标识符
     * @param data 要保存的数据
     */
    suspend fun save(id: String, data: T)

    /**
     * 恢复数据
     *
     * @param id 数据标识符
     * @return 恢复的数据，如果不存在则返回null
     */
    suspend fun restore(id: String): T?

    /**
     * 清除数据
     *
     * @param id 数据标识符
     */
    suspend fun clear(id: String)

    /**
     * 检查数据是否存在
     *
     * @param id 数据标识符
     * @return 是否存在
     */
    suspend fun exists(id: String): Boolean

    /**
     * 获取数据大小
     *
     * @param id 数据标识符
     * @return 数据大小（字节），如果不存在则返回0
     */
    suspend fun getSize(id: String): Long
}
