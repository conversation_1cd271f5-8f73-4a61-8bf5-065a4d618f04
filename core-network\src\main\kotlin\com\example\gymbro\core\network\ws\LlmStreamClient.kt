package com.example.gymbro.core.network.ws

import com.example.gymbro.shared.models.ai.ChatRequest
import com.example.gymbro.shared.models.network.NetworkResult
import kotlinx.coroutines.flow.Flow

/**
 * 流式AI客户端接口 - 事件总线架构版本
 *
 * 🔥 【事件总线架构】统一接口：所有Token直接发布到TokenBus
 * 调用方无需处理返回值，Token会自动路由到ThinkingBox
 */
interface LlmStreamClient {

    /**
     * 流式AI聊天 - 事件总线架构唯一方法
     *
     * 🔥 【事件总线架构】Token会自动发布到TokenBus，调用方无需处理返回值
     *
     * @param request 强类型的聊天请求
     * @param messageId 消息ID，用于事件总线路由
     * @param offset 断点续传偏移量，用于重连时恢复（P1功能）
     */
    suspend fun streamChatWithMessageId(request: ChatRequest, messageId: String, offset: Int = 0)

    // 🧹 任务类型路由已移至 data/ai/gateway/AiGateway.kt

    /**
     * 检查连接状态 - Stage B强类型版本
     *
     * @return 网络结果封装的连接状态
     */
    suspend fun checkConnection(): NetworkResult<Boolean>

    /**
     * 获取当前配置的基础URL
     *
     * @return API基础URL
     */
    fun getBaseUrl(): String

    /**
     * 暂停WebSocket连接
     * 用于网络状态变化时的连接管理
     */
    fun pause()

    /**
     * 恢复WebSocket连接
     * 用于网络状态恢复时的连接管理
     */
    fun resume()

    /**
     * 获取当前WebSocket状态
     * @return 当前连接状态
     */
    fun getCurrentState(): WsState

    // ===== 向后兼容方法 =====

    // 🗑️ 已清理：streamChatLegacy方法已删除，统一使用强类型streamChat
}

// 🧹 REMOVED: WsState定义已移动到独立的WsState.kt文件
