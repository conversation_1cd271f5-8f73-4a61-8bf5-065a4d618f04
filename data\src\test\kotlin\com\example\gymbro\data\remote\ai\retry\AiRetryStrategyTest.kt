package com.example.gymbro.data.remote.ai.retry

import com.example.gymbro.core.error.types.ModernResult
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import java.net.SocketTimeoutException

/**
 * AiRetryStrategy单元测试
 *
 * 测试重试机制、降级策略和错误率监控
 */
class AiRetryStrategyTest {

    private lateinit var retryStrategy: AiRetryStrategy

    @Before
    fun setup() {
        retryStrategy = AiRetryStrategy()
    }

    @Test
    fun `成功请求_不重试`() = runTest {
        // Given
        var callCount = 0
        val operation: suspend () -> ModernResult<String> = {
            callCount++
            ModernResult.Success("成功")
        }

        // When
        val result = retryStrategy.executeWithRetry("test_operation", operation = operation)

        // Then
        assertTrue("应该成功", result is ModernResult.Success)
        assertEquals("应该只调用一次", 1, callCount)
        assertEquals("返回值应该正确", "成功", (result as ModernResult.Success).data)
    }

    @Test
    fun `网络超时_会重试`() = runTest {
        // Given
        var callCount = 0
        val operation: suspend () -> ModernResult<String> = {
            callCount++
            if (callCount < 3) {
                throw SocketTimeoutException("网络超时")
            } else {
                ModernResult.Success("重试成功")
            }
        }

        // When
        val result = retryStrategy.executeWithRetry("timeout_test", operation = operation)

        // Then
        assertTrue("应该最终成功", result is ModernResult.Success)
        assertEquals("应该重试3次", 3, callCount)
        assertEquals("返回值应该正确", "重试成功", (result as ModernResult.Success).data)
    }

    @Test
    fun `Function Call降级_返回文本消息`() = runTest {
        // When
        val result = retryStrategy.handleFunctionCallFallback(
            functionName = "start_workout_session",
            arguments = "{}",
            originalError = RuntimeException("测试错误"),
        )

        // Then
        assertTrue("应该成功降级", result is ModernResult.Success)
        val fallbackMessage = (result as ModernResult.Success).data
        assertTrue("应该包含降级消息", fallbackMessage.contains("开始训练"))
    }

    @Test
    fun `Function Call错误率统计_正确`() = runTest {
        // Given - 初始错误率应该为0
        assertEquals("初始错误率应该为0", 0.0, retryStrategy.getFunctionCallErrorRate(), 0.01)

        // When - 模拟3次失败
        repeat(3) {
            retryStrategy.handleFunctionCallFallback("test_function", "{}", RuntimeException("测试"))
        }

        // Then
        assertTrue("错误率应该为100%", retryStrategy.getFunctionCallErrorRate() > 0.99)
    }
}
