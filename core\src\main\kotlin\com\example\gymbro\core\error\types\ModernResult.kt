package com.example.gymbro.core.error.types

/**
 * 表示异步操作结果的密封类
 *
 * 包含三种状态：
 * 1. Loading - 操作正在进行中
 * 2. Success - 操作成功，包含结果数据
 * 3. Error - 操作失败，包含错误信息
 */
sealed class ModernResult<out T> {
    /**
     * 表示操作正在进行中
     */
    object Loading : ModernResult<Nothing>()

    /**
     * 表示操作成功
     * @param data 成功结果数据
     */
    data class Success<T>(
        val data: T,
    ) : ModernResult<T>()

    /**
     * 表示操作失败
     * @param error 错误信息
     */
    data class Error(
        val error: ModernDataError,
    ) : ModernResult<Nothing>()

    /**
     * 判断结果是否为Loading状态
     */
    val isLoading: Boolean get() = this is Loading

    /**
     * 判断结果是否为Success状态
     */
    val isSuccess: Boolean get() = this is Success

    /**
     * 判断结果是否为Error状态
     */
    val isError: Boolean get() = this is Error

    /**
     * 获取成功数据，如果不是Success状态则返回null
     */
    fun getOrNull(): T? = (this as? Success)?.data

    /**
     * 获取错误信息，如果不是Error状态则返回null
     */
    fun errorOrNull(): ModernDataError? = (this as? Error)?.error

    /**
     * 映射成功结果到新的类型
     */
    inline fun <R> map(transform: (T) -> R): ModernResult<R> =
        when (this) {
            is Loading -> Loading
            is Success -> Success(transform(data))
            is Error -> Error(error)
        }

    /**
     * 根据结果状态执行不同的操作
     */
    inline fun <R> fold(
        onLoading: () -> R,
        onSuccess: (T) -> R,
        onError: (ModernDataError) -> R,
    ): R =
        when (this) {
            is Loading -> onLoading()
            is Success -> onSuccess(data)
            is Error -> onError(error)
        }

    /**
     * 映射错误结果
     * 如果当前结果是Error，则将转换函数应用于错误并返回新的Error
     * 如果当前结果是Success或Loading，则保持原样
     * @param transform 错误转换函数
     * @return 新的ModernResult实例
     */
    inline fun mapError(transform: (ModernDataError) -> ModernDataError): ModernResult<T> =
        when (this) {
            is Success -> this
            is Error -> Error(transform(error))
            is Loading -> Loading
        }

    /**
     * 平坦映射成功结果
     * 如果当前结果是Success，则将转换函数应用于其数据并返回新的ModernResult
     * 如果当前结果是Error或Loading，则保持原样
     * @param transform 数据转换函数，返回ModernResult
     * @return 新的ModernResult实例
     */
    inline fun <R> flatMap(transform: (T) -> ModernResult<R>): ModernResult<R> =
        when (this) {
            is Success -> transform(data)
            is Error -> this
            is Loading -> Loading
        }

    /**
     * 运行指定代码块，并捕获异常转换为ModernResult
     * @param errorTransform 异常到ModernDataError的转换函数
     * @param block 要执行的代码块
     * @return 代码块成功执行的结果或错误
     */
    companion object {
        /**
         * 创建成功结果
         * @param data 成功的数据
         */
        fun <T> success(data: T): ModernResult<T> = Success(data)

        /**
         * 创建失败结果
         * @param error 错误
         */
        fun <T> error(error: ModernDataError): ModernResult<T> = Error(error)

        /**
         * 创建加载结果
         */
        fun <T> loading(): ModernResult<T> = Loading

        /**
         * 将多个ModernResult合并为一个结果
         * 如果所有结果都是Success，则返回包含所有成功值的列表
         * 如果有任何Error，则返回第一个错误
         * 如果没有Error但有Loading，则返回Loading
         * @param results 要合并的ModernResult列表
         * @return 合并后的结果
         */
        fun <T> combine(results: List<ModernResult<T>>): ModernResult<List<T>> {
            val successData = mutableListOf<T>()
            var hasLoading = false

            for (result in results) {
                when (result) {
                    is Error -> return result
                    is Loading -> hasLoading = true
                    is Success -> successData.add(result.data)
                }
            }

            return if (hasLoading) {
                loading()
            } else {
                success(successData)
            }
        }

        /**
         * 从可能抛出异常的代码中创建ModernResult
         */
        inline fun <T> catch(
            operationName: String,
            transform: (ModernDataError) -> ModernDataError = { it },
            block: () -> T,
        ): ModernResult<T> =
            try {
                Success(block())
            } catch (e: Throwable) {
                if (e is ModernDataError) {
                    Error(transform(e))
                } else {
                    Error(
                        transform(
                            // 使用创建ModernDataError的辅助函数
                            ModernDataError(
                                operationName = operationName,
                                errorType = GlobalErrorType.System.General,
                                category = ErrorCategory.SYSTEM,
                                cause = e,
                            ),
                        ),
                    )
                }
            }
    }
}
