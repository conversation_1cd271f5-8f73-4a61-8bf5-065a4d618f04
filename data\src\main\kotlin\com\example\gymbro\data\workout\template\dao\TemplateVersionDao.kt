package com.example.gymbro.data.workout.template.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.example.gymbro.data.workout.template.entity.TemplateVersionEntity
import kotlinx.coroutines.flow.Flow

/**
 * 模板版本数据访问对象 - Room DAO
 *
 * Phase1: BaseTemplate架构升级 - 版本控制数据操作
 * 实现版本历史的CRUD操作和查询优化
 */
@Dao
interface TemplateVersionDao {

    // ==================== 查询操作 ====================

    /**
     * 获取指定模板的所有版本(按版本号降序)
     */
    @Query(
        """
        SELECT * FROM template_versions
        WHERE templateId = :templateId
        ORDER BY versionNumber DESC
    """,
    )
    fun getVersionsByTemplateId(templateId: String): Flow<List<TemplateVersionEntity>>

    /**
     * 获取指定模板的最新版本
     */
    @Query(
        """
        SELECT * FROM template_versions
        WHERE templateId = :templateId
        ORDER BY versionNumber DESC
        LIMIT 1
    """,
    )
    suspend fun getLatestVersion(templateId: String): TemplateVersionEntity?

    /**
     * 根据版本ID获取特定版本
     */
    @Query("SELECT * FROM template_versions WHERE id = :versionId")
    suspend fun getVersionById(versionId: String): TemplateVersionEntity?

    /**
     * 获取指定模板的发布版本(排除自动保存)
     */
    @Query(
        """
        SELECT * FROM template_versions
        WHERE templateId = :templateId AND isAutoSaved = 0
        ORDER BY versionNumber DESC
    """,
    )
    fun getPublishedVersions(templateId: String): Flow<List<TemplateVersionEntity>>

    /**
     * 获取指定模板的自动保存版本
     */
    @Query(
        """
        SELECT * FROM template_versions
        WHERE templateId = :templateId AND isAutoSaved = 1
        ORDER BY createdAt DESC
    """,
    )
    suspend fun getAutoSavedVersions(templateId: String): List<TemplateVersionEntity>

    /**
     * 获取模板的版本数量
     */
    @Query("SELECT COUNT(*) FROM template_versions WHERE templateId = :templateId")
    suspend fun getVersionCount(templateId: String): Int

    /**
     * 获取指定模板的下一个版本号
     */
    @Query(
        """
        SELECT COALESCE(MAX(versionNumber), 0) + 1
        FROM template_versions
        WHERE templateId = :templateId
    """,
    )
    suspend fun getNextVersionNumber(templateId: String): Int

    // ==================== 插入操作 ====================

    /**
     * 插入新版本
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertVersion(version: TemplateVersionEntity)

    /**
     * 批量插入版本
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertVersions(versions: List<TemplateVersionEntity>)

    // ==================== 删除操作 ====================

    /**
     * 删除指定版本
     */
    @Query("DELETE FROM template_versions WHERE id = :versionId")
    suspend fun deleteVersion(versionId: String)

    /**
     * 删除指定模板的所有自动保存版本
     */
    @Query("DELETE FROM template_versions WHERE templateId = :templateId AND isAutoSaved = 1")
    suspend fun cleanupAutoSavedVersions(templateId: String)

    /**
     * 保留最近N个自动保存版本，删除旧的
     */
    @Query(
        """
        DELETE FROM template_versions
        WHERE templateId = :templateId AND isAutoSaved = 1
        AND id NOT IN (
            SELECT id FROM template_versions
            WHERE templateId = :templateId AND isAutoSaved = 1
            ORDER BY createdAt DESC
            LIMIT :keepCount
        )
    """,
    )
    suspend fun keepRecentAutoSaves(templateId: String, keepCount: Int = 5)

    /**
     * 删除指定模板的所有版本
     */
    @Query("DELETE FROM template_versions WHERE templateId = :templateId")
    suspend fun deleteAllVersions(templateId: String)

    // ==================== 复杂查询操作 ====================

    /**
     * 检查版本号是否已存在
     */
    @Query(
        """
        SELECT COUNT(*) > 0 FROM template_versions
        WHERE templateId = :templateId AND versionNumber = :versionNumber
    """,
    )
    suspend fun versionNumberExists(templateId: String, versionNumber: Int): Boolean

    /**
     * 获取版本历史统计信息
     */
    @Query(
        """
        SELECT
            COUNT(*) as totalVersions,
            COUNT(CASE WHEN isAutoSaved = 0 THEN 1 END) as publishedVersions,
            COUNT(CASE WHEN isAutoSaved = 1 THEN 1 END) as autoSavedVersions,
            MAX(createdAt) as lastVersionAt
        FROM template_versions
        WHERE templateId = :templateId
    """,
    )
    suspend fun getVersionStats(templateId: String): VersionStats?

    /**
     * 原子化版本创建操作
     * 确保版本号唯一性和并发安全
     */
    @Transaction
    suspend fun createVersionSafely(
        templateId: String,
        contentJson: String,
        description: String?,
        isAutoSaved: Boolean,
    ): TemplateVersionEntity {
        val nextVersion = getNextVersionNumber(templateId)
        val versionEntity = TemplateVersionEntity(
            id = "tv_${templateId}_v${nextVersion}_${System.currentTimeMillis()}",
            templateId = templateId,
            versionNumber = nextVersion,
            contentJson = contentJson,
            createdAt = System.currentTimeMillis(),
            description = description,
            isAutoSaved = isAutoSaved,
        )
        insertVersion(versionEntity)
        return versionEntity
    }
}

/**
 * 版本统计信息数据类
 */
data class VersionStats(
    val totalVersions: Int,
    val publishedVersions: Int,
    val autoSavedVersions: Int,
    val lastVersionAt: Long,
)
