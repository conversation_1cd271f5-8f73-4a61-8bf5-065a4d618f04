package com.example.gymbro.core.error.types

import com.example.gymbro.core.ui.text.UiText

/**
 * 数据库操作错误类
 */
class DatabaseError private constructor(
    val operationName: String,
    val uiMessage: UiText,
    val errorType: GlobalErrorType.Database = GlobalErrorType.Database.QueryFailed,
    val severity: ErrorSeverity = ErrorSeverity.ERROR,
    val recoverable: Boolean = false,
    val cause: Throwable? = null,
    val metadataMap: Map<String, Any> = emptyMap(),
    val entityName: String? = null, // Specific to DatabaseError
    val queryDetails: String? = null, // Specific to DatabaseError
) {
    /**
     * 转换为ModernDataError
     */
    fun toModernDataError(): ModernDataError =
        ModernDataError(
            operationName = operationName,
            errorType = errorType,
            category = ErrorCategory.DATA,
            uiMessage = uiMessage,
            severity = severity,
            recoverable = recoverable,
            cause = cause,
            metadataMap = mergeSpecificMetadata(metadataMap, entityName, queryDetails),
        )

    companion object {
        private fun mergeSpecificMetadata(
            baseMetadata: Map<String, Any>,
            entityName: String?,
            queryDetails: String?,
        ): Map<String, Any> =
            baseMetadata.toMutableMap().apply {
                entityName?.let { put(StandardKeys.ENTITY_NAME.key, it) }
                queryDetails?.let { put(StandardKeys.QUERY.key, it) }
            }

        /**
         * 创建DatabaseError并转换为ModernDataError
         */
        private fun create(
            operationName: String,
            uiMessage: UiText,
            errorType: GlobalErrorType.Database = GlobalErrorType.Database.QueryFailed,
            entityName: String? = null,
            queryDetails: String? = null,
            severity: ErrorSeverity = ErrorSeverity.ERROR,
            recoverable: Boolean = false,
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError =
            DatabaseError(
                operationName = operationName,
                uiMessage = uiMessage,
                errorType = errorType,
                severity = severity,
                recoverable = recoverable,
                cause = cause,
                metadataMap = metadataMap,
                entityName = entityName,
                queryDetails = queryDetails,
            ).toModernDataError()
    }
}
