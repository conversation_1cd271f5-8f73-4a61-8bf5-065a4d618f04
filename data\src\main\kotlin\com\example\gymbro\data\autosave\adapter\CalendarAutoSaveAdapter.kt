package com.example.gymbro.data.autosave.adapter

import com.example.gymbro.core.autosave.config.AutoSaveConfig
import com.example.gymbro.core.autosave.storage.AutoSaveStorage
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.data.autosave.AutoSaveRepository
import com.example.gymbro.data.autosave.internal.DatabaseStorage
import com.example.gymbro.data.autosave.strategy.IntervalSaveStrategy
import com.example.gymbro.domain.workout.model.calendar.CalendarItem
import com.example.gymbro.domain.workout.repository.CalendarRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.serialization.json.Json
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Calendar模块自动保存适配器
 *
 * 🎯 功能特性：
 * - 为Calendar模块提供CalendarItem自动保存功能
 * - 集成AppDatabase现有的calendar_events表
 * - 使用定时保存策略（5秒间隔），适合日历拖拽操作
 * - 支持CalendarItem类型的序列化和反序列化
 * - 支持缓存恢复和版本冲突检测
 * - 与WorkoutAction Function Call集成
 *
 * @param autoSaveRepository 自动保存仓库
 * @param calendarRepository 日历仓库
 * @param json Json序列化器
 * @param logger 日志记录器
 */
@Singleton
class CalendarAutoSaveAdapter @Inject constructor(
    private val autoSaveRepository: AutoSaveRepository,
    private val calendarRepository: CalendarRepository,
    private val json: Json,
    private val logger: Logger,
) {

    companion object {
        private const val CONFIG_ID = "calendar_autosave"
        private const val SAVE_INTERVAL_MS = 5000L // 5秒间隔
        private const val SESSION_PREFIX = "calendar_session_"
    }

    /**
     * 创建Calendar自动保存会话
     *
     * @param userId 用户ID
     * @param scope 协程作用域
     * @return 自动保存会话ID
     */
    suspend fun createAutoSave(
        userId: String,
        scope: CoroutineScope,
    ): ModernResult<String> {
        return try {
            logger.d("CalendarAutoSaveAdapter", "创建Calendar自动保存会话: $userId")

            // 创建存储后端
            val storage = createCalendarStorage(userId)

            // 创建保存策略（定时保存，5秒间隔）
            val strategy = IntervalSaveStrategy.create<CalendarItem>(logger, SAVE_INTERVAL_MS)

            // 创建自动保存配置
            val config = AutoSaveConfig(
                id = "$CONFIG_ID$userId",
                strategy = strategy,
                storage = storage,
                enableRecovery = true,
            )

            // 创建会话
            val sessionResult = autoSaveRepository.createSession(config, scope)

            when (sessionResult) {
                is ModernResult.Success -> {
                    logger.d("CalendarAutoSaveAdapter", "Calendar自动保存会话创建成功: ${sessionResult.data}")
                    sessionResult
                }
                is ModernResult.Error -> {
                    logger.e("CalendarAutoSaveAdapter", "创建Calendar自动保存会话失败", sessionResult.error.cause)
                    sessionResult
                }
                is ModernResult.Loading -> {
                    logger.d("CalendarAutoSaveAdapter", "Calendar自动保存会话创建中...")
                    sessionResult
                }
            }
        } catch (e: Exception) {
            logger.e("CalendarAutoSaveAdapter", "创建Calendar自动保存会话异常", e)
            ModernResult.Error(
                ModernDataError.fromGlobalErrorType(
                    operationName = "createAutoSave",
                    errorType = GlobalErrorType.System.General,
                    cause = e,
                ),
            )
        }
    }

    /**
     * 保存日历项目数据
     *
     * @param sessionId 会话ID
     * @param item 日历项目
     * @return 保存结果
     */
    suspend fun saveCalendarItem(
        sessionId: String,
        item: CalendarItem,
    ): ModernResult<Unit> {
        return try {
            logger.d("CalendarAutoSaveAdapter", "保存日历项目: ${item.id}")

            val session = autoSaveRepository.getSession<CalendarItem>(sessionId)
            if (session == null) {
                logger.w("CalendarAutoSaveAdapter", "会话不存在: $sessionId")
                return ModernResult.Error(
                    ModernDataError.fromGlobalErrorType(
                        operationName = "saveCalendarItem",
                        errorType = GlobalErrorType.Data.NotFound,
                    ),
                )
            }

            // 通过会话更新数据
            session.update(item)

            logger.d("CalendarAutoSaveAdapter", "日历项目保存成功: ${item.id}")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            logger.e("CalendarAutoSaveAdapter", "保存日历项目失败: ${item.id}", e)
            ModernResult.Error(
                ModernDataError.fromGlobalErrorType(
                    operationName = "saveCalendarItem",
                    errorType = GlobalErrorType.Data.General,
                    cause = e,
                ),
            )
        }
    }

    /**
     * 恢复日历项目数据
     *
     * @param sessionId 会话ID
     * @return 恢复的日历项目或null
     */
    suspend fun restoreCalendarItem(sessionId: String): ModernResult<CalendarItem?> {
        return try {
            logger.d("CalendarAutoSaveAdapter", "恢复日历项目: $sessionId")

            val session = autoSaveRepository.getSession<CalendarItem>(sessionId)
            if (session == null) {
                logger.w("CalendarAutoSaveAdapter", "会话不存在: $sessionId")
                return ModernResult.Success(null)
            }

            // 通过会话恢复数据
            session.restoreFromCache()
            logger.d("CalendarAutoSaveAdapter", "日历项目恢复成功")
            ModernResult.Success(null) // TODO: 实现正确的恢复逻辑
        } catch (e: Exception) {
            logger.e("CalendarAutoSaveAdapter", "恢复日历项目异常: $sessionId", e)
            ModernResult.Error(
                ModernDataError.fromGlobalErrorType(
                    operationName = "restoreCalendarItem",
                    errorType = GlobalErrorType.Data.General,
                    cause = e,
                ),
            )
        }
    }

    /**
     * 删除自动保存会话
     *
     * @param sessionId 会话ID
     * @return 删除结果
     */
    suspend fun deleteAutoSave(sessionId: String): ModernResult<Unit> {
        return try {
            logger.d("CalendarAutoSaveAdapter", "删除Calendar自动保存会话: $sessionId")

            val deleteResult = autoSaveRepository.deleteSession(sessionId)

            when (deleteResult) {
                is ModernResult.Success -> {
                    logger.d("CalendarAutoSaveAdapter", "Calendar自动保存会话删除成功")
                    deleteResult
                }
                is ModernResult.Error -> {
                    logger.e("CalendarAutoSaveAdapter", "删除Calendar自动保存会话失败", deleteResult.error.cause)
                    deleteResult
                }
                is ModernResult.Loading -> {
                    logger.d("CalendarAutoSaveAdapter", "Calendar自动保存会话删除中...")
                    deleteResult
                }
            }
        } catch (e: Exception) {
            logger.e("CalendarAutoSaveAdapter", "删除Calendar自动保存会话异常: $sessionId", e)
            ModernResult.Error(
                ModernDataError.fromGlobalErrorType(
                    operationName = "deleteAutoSave",
                    errorType = GlobalErrorType.Data.General,
                    cause = e,
                ),
            )
        }
    }

    // === 私有方法 ===

    /**
     * 创建Calendar存储后端
     *
     * @param userId 用户ID
     * @return 存储实例
     */
    private fun createCalendarStorage(userId: String): AutoSaveStorage<CalendarItem> {
        return DatabaseStorage(
            saveOperation = { item ->
                calendarRepository.addCalendarItem(item).map { Unit }
            },
            loadOperation = { itemId ->
                // 使用CalendarRepositoryImpl的扩展方法
                if (calendarRepository is com.example.gymbro.data.workout.repository.CalendarRepositoryImpl) {
                    calendarRepository.getCalendarItemById(itemId)
                } else {
                    ModernResult.Success(null)
                }
            },
            deleteOperation = { itemId ->
                calendarRepository.removeCalendarItem(itemId).map { Unit }
            },
            existsOperation = { itemId ->
                // 使用CalendarRepositoryImpl的扩展方法
                if (calendarRepository is com.example.gymbro.data.workout.repository.CalendarRepositoryImpl) {
                    calendarRepository.itemExists(itemId)
                } else {
                    ModernResult.Success(false)
                }
            },
            logger = logger,
        )
    }
}
