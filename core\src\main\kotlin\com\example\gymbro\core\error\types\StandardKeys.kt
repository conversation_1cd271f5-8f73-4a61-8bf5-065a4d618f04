package com.example.gymbro.core.error.types

// import com.example.gymbro.core.error.types.ModernDataError // 可能会被使用，如果直接引用 ModernDataError
// import com.example.gymbro.core.error.recovery.RecoveryStrategy // 这个导入路径需要修正，如果 RecoveryStrategy 也被移动了

/**
 * 标准元数据键
 *
 * 定义系统中常用的元数据键，提供统一访问点
 * 使用这些标准键替代过度细分的错误类型，提高灵活性和可维护性
 */

object StandardKeys {
    // 错误基础信息
    val ERROR_CODE = MetadataKey<String>("errorCode")
    val ERROR_SUBTYPE = MetadataKey<String>("error_subtype")
    val HTTP_STATUS = MetadataKey<Int>("http_status")

    val RESOURCE_TYPE = MetadataKey<String>("resourceType")
    val ENTITY_NAME = MetadataKey<String>("entity")
    val ENTITY_ID = MetadataKey<String>("entity_id")

    // 操作相关
    val OPERATION = MetadataKey<String>("operation")
    val QUERY = MetadataKey<String>("query")
    val ACTION = MetadataKey<String>("action") // 具体执行的操作动作

    // 验证相关
    val FIELD = MetadataKey<String>("field")
    val VALUE = MetadataKey<Any>("value")
    val MIN = MetadataKey<Number>("min")
    val MAX = MetadataKey<Number>("max")
    val VALIDATION_ERRORS = MetadataKey<Map<String, String>>("validationErrors") // 多字段验证错误

    // 时间相关
    val TIMESTAMP = MetadataKey<Long>("timestamp")
    val EXPIRY_TIME = MetadataKey<Long>("expiryTime") // 过期时间

    // 恢复相关
    // 注意: RecoveryStrategy 的导入路径可能需要更新
    // val RECOVERY_STRATEGY = MetadataKey.of<RecoveryStrategy<*>>("recoveryStrategy")
    val RETRY_COUNT = MetadataKey<Int>("retry_count")
    val MAX_RETRIES = MetadataKey<Int>("maxRetries")

    // 聚合错误相关
    // 注意: ModernDataError 的导入路径可能需要更新
    // val AGGREGATED = MetadataKey.of<List<ModernDataError>>("aggregatedErrors")
    val ERROR_COUNT = MetadataKey<Int>("errorCount")

    // 网络相关
    val URL = MetadataKey<String>("url")
    val METHOD = MetadataKey<String>("method")
    val RESPONSE_BODY = MetadataKey<String>("responseBody")
    val SERVER_NAME = MetadataKey<String>("serverName") // 服务器名称

    val CONFLICTING_ENTITY = MetadataKey<String>("conflictingEntity") // 冲突实体
    val CONFLICT_REASON = MetadataKey<String>("conflictReason") // 冲突原因
    val SYNC_TYPE = MetadataKey<String>("syncType") // 全量/增量同步
    val LAST_SYNC_TIME = MetadataKey<Long>("lastSyncTime") // 上次同步时间

    // 订阅相关
    val SUBSCRIPTION_ID = MetadataKey<String>("subscriptionId") // 订阅ID
    val SUBSCRIPTION_PLAN = MetadataKey<String>("subscriptionPlan") // 订阅计划
    val SUBSCRIPTION_STATUS = MetadataKey<String>("subscriptionStatus") // 订阅状态
    val FEATURE_ID = MetadataKey<String>("featureId") // 功能ID
    val CURRENCY_CODE = MetadataKey<String>("currencyCode") // 货币代码

    // 支付相关
    val PAYMENT_ID = MetadataKey<String>("paymentId") // 支付ID
    val PAYMENT_METHOD = MetadataKey<String>("paymentMethod") // 支付方式
    val TRANSACTION_ID = MetadataKey<String>("transactionId") // 交易ID
    val AMOUNT = MetadataKey<Double>("amount") // 金额
    val GATEWAY_ERROR = MetadataKey<String>("gatewayError") // 支付网关错误

    val QUOTA_LIMIT = MetadataKey<Int>("quotaLimit") // 配额限制
    val QUOTA_USED = MetadataKey<Int>("quotaUsed") // 已使用配额
    val ENTITY_TYPE = MetadataKey<String>("entityType") // 实体类型
    val TEMPLATE_ID = MetadataKey<String>("templateId") // 模板ID
    val EXERCISE_ID = MetadataKey<String>("exerciseId") // 训练动作ID

    // 通用
    val CAUSE = MetadataKey<Throwable>("cause")
    val SOURCE = MetadataKey<String>("source")
    val USER_ID = MetadataKey<String>("user_id")
    val SESSION_ID = MetadataKey<String>("sessionId") // 会话ID
    val MESSAGE = MetadataKey<String>("message") // 一般消息
    val SEVERITY = MetadataKey<String>("severity") // 严重性
    val ERROR_CATEGORY = MetadataKey<String>("errorCategory") // 错误类别
    val ERROR_SEVERITY = MetadataKey<String>("errorSeverity") // 错误严重程度

    val OPERATION_NAME = MetadataKey<String>("operation_name")
    val EXCEPTION = MetadataKey<Throwable>("exception")
    val OPERATION_TYPE = MetadataKey<String>("operation_type")
    val HOST = MetadataKey<String>("host")
    val DETAILS = MetadataKey<String>("details")

    // 认证相关
    val AUTH_PROVIDER = MetadataKey<String>("auth_provider") // 认证提供商
}
