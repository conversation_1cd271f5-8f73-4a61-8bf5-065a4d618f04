package com.example.gymbro.data.exercise.local.database

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.example.gymbro.data.exercise.local.converter.ExerciseTypeConverters
import com.example.gymbro.data.exercise.local.dao.ExerciseDao
import com.example.gymbro.data.exercise.local.dao.ExerciseFtsDao
import com.example.gymbro.data.exercise.local.entity.ExerciseEntity
import com.example.gymbro.data.exercise.local.entity.ExerciseFtsEntity
import com.example.gymbro.data.exercise.local.entity.ExerciseSearchHistoryEntity
import com.example.gymbro.data.exercise.local.entity.ExerciseStatsView
import com.example.gymbro.data.exercise.local.entity.ExerciseUsageStatsEntity

/**
 * Exercise-Library模块数据库
 *
 * 基于plan.md设计：
 * - 独立的Exercise数据库
 * - 支持FTS4全文搜索
 * - 包含统计和搜索历史
 * - 高性能动作检索
 */
@Database(
    entities = [
        ExerciseEntity::class,
        ExerciseFtsEntity::class,
        ExerciseSearchHistoryEntity::class,
        ExerciseUsageStatsEntity::class,
    ],
    views = [
        ExerciseStatsView::class,
    ],
    version = 1,
    exportSchema = true,
)
@TypeConverters(ExerciseTypeConverters::class)
abstract class ExerciseDatabase : RoomDatabase() {

    abstract fun exerciseDao(): ExerciseDao
    abstract fun exerciseFtsDao(): ExerciseFtsDao

    companion object {
        private const val DATABASE_NAME = "exercise_database"

        @Volatile
        private var INSTANCE: ExerciseDatabase? = null

        fun getDatabase(context: Context): ExerciseDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    ExerciseDatabase::class.java,
                    DATABASE_NAME,
                )
                    .build()
                INSTANCE = instance
                instance
            }
        }
    }
}
