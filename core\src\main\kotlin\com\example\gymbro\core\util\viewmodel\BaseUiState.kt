package com.example.gymbro.core.util.viewmodel

import com.example.gymbro.core.error.types.ErrorSeverity
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.ui.text.UiText

/**
 * 基础UI状态接口
 *
 * 定义了所有UI状态应当遵循的基本结构，特别是错误处理相关字段。
 * 所有feature的UI状态类都应当实现这个接口或其子接口。
 */
interface BaseUiState {
    /**
     * 当前UI状态是否处于加载中
     */
    val isLoading: Boolean

    /**
     * 当前UI状态中的错误信息，若无错误则为null
     */
    val error: UiText?

    /**
     * 错误的严重程度，用于决定UI呈现方式
     */
    val errorSeverity: ErrorSeverity?

    /**
     * 错误是否可以被恢复
     */
    val isErrorRecoverable: Boolean

    /**
     * 原始错误对象，主要用于调试和错误恢复
     * 注意：此字段通常不应该直接用于UI展示
     */
    val rawError: ModernDataError?

    /**
     * 错误的全局类型
     */
    val errorType: GlobalErrorType?
}

/**
 * 通用UI状态的默认实现
 *
 * 提供了对BaseUiState接口的基本实现，可作为各feature中UI状态类的基类。
 */
abstract class CommonUiState(
    override val isLoading: Boolean = false,
    override val error: UiText? = null,
    override val errorSeverity: ErrorSeverity? = null,
    override val isErrorRecoverable: Boolean = false,
    override val rawError: ModernDataError? = null,
    override val errorType: GlobalErrorType? = null,
) : BaseUiState

/**
 * 数据加载UI状态接口
 *
 * 扩展了BaseUiState，添加了对特定数据类型的处理。
 * 适用于需要加载和显示单一数据类型的UI状态。
 */
interface DataLoadingUiState<T> : BaseUiState {
    /**
     * 加载的数据，如果处于加载中或出错状态则可能为null
     */
    val data: T?

    /**
     * 数据是否已成功加载
     */
    val hasData: Boolean
}

/**
 * 列表数据UI状态接口
 *
 * 扩展了BaseUiState，添加了对列表数据类型的处理。
 * 适用于需要加载和显示列表数据的UI状态。
 */
interface ListUiState<T> : BaseUiState {
    /**
     * 列表数据，如果处于加载中或出错状态则可能为空列表
     */
    val items: List<T>

    /**
     * 列表是否为空
     */
    val isEmpty: Boolean

    /**
     * 是否正在加载更多内容（例如分页加载）
     */
    val isLoadingMore: Boolean

    /**
     * 是否可以加载更多内容
     */
    val canLoadMore: Boolean
}

/**
 * 表单UI状态接口
 *
 * 扩展了BaseUiState，添加了表单处理相关字段。
 * 适用于需要处理用户输入和表单提交的UI状态。
 */
interface FormUiState : BaseUiState {
    /**
     * 表单字段验证错误，按字段名映射错误消息
     */
    val fieldErrors: Map<String, UiText>

    /**
     * 表单是否有任何验证错误
     */
    val hasFieldErrors: Boolean

    /**
     * 表单是否已提交
     */
    val isSubmitted: Boolean

    /**
     * 表单提交是否成功
     */
    val isSubmitSuccessful: Boolean
}

/**
 * 可操作UI状态接口
 *
 * 扩展了BaseUiState，添加了与用户操作相关的字段。
 * 适用于需要处理用户交互和操作结果的UI状态。
 */
interface ActionableUiState : BaseUiState {
    /**
     * 最近一次操作是否成功
     */
    val isActionSuccessful: Boolean

    /**
     * 最近一次操作的名称或类型
     */
    val lastAction: String?

    /**
     * 成功消息，通常在操作成功后显示
     */
    val successMessage: UiText?
}
