package com.example.gymbro.data.local.entity.user

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * 用户设置实体
 * 存储用户的设置信息，与UserCacheEntity形成一对一关系
 */
@Entity(
    tableName = "user_settings",
    foreignKeys = [
        ForeignKey(
            entity = UserCacheEntity::class,
            parentColumns = ["user_id"],
            childColumns = ["userId"],
            onDelete = ForeignKey.CASCADE,
        ),
    ],
    indices = [
        Index(value = ["userId"], unique = true),
    ],
)
data class UserSettingsEntity(
    @PrimaryKey
    val userId: String,

    // 主题与语言
    val themeMode: String = "system", // "light", "dark", "system"
    val languageCode: String = "zh-CN",
    val measurementSystem: String = "metric", // "metric", "imperial"

    // 通知与声音设置
    val notificationsEnabled: Boolean = true,
    val soundsEnabled: Boolean = true,

    // 隐私设置
    val locationSharingEnabled: Boolean = false,
    val dataSharingEnabled: Boolean = false,
    val allowWorkoutSharing: Boolean = true,

    // 备份设置
    val autoBackupEnabled: Boolean = false,
    val backupFrequency: Int = 7, // 天数
    val lastBackupTime: Long = 0,

    // 匹配设置
    val allowPartnerMatching: Boolean = false,
    val preferredMatchDistance: Int = 10, // 公里
    val matchByFitnessLevel: Boolean = true,

    // 时间戳
    val lastModified: Long = System.currentTimeMillis(),
)
