# App Module - 文件结构

## 📁 目录树结构

```
app
└── src
    ├── main/kotlin/com/example/gymbro
    │   ├── app
    │   │   ├── di/RegionModule.kt
    │   │   ├── error/ErrorCodeMapper.kt
    │   │   ├── extensions
    │   │   │   ├── ContextExtensions.kt
    │   │   │   ├── ModernResultExtensions.kt
    │   │   │   └── UiTextExtensions.kt
    │   │   ├── loading
    │   │   │   ├── components/RainbowText.kt
    │   │   │   ├── LoadingScreen.kt
    │   │   │   ├── LoadingViewModel.kt
    │   │   │   └── StartupConstants.kt
    │   │   ├── notification/NotificationChannelManager.kt
    │   │   └── version
    │   │       ├── AppLockedScreen.kt
    │   │       ├── ForceUpdateScreen.kt
    │   │       └── RegionDetectionManager.kt
    │   ├── di/AppConfigModule.kt
    │   ├── network/AndroidNetworkMonitor.kt
    │   ├── service
    │   │   ├── BackgroundServiceManager.kt
    │   │   ├── BaseService.kt
    │   │   └── ForegroundServiceManager.kt
    │   ├── GymBroApp.kt
    │   └── MainActivity.kt
    └── test/kotlin/com/example/gymbro
        └── ...
```

## 🎯 模块职责

- **核心功能**: 作为应用的入口和组装模块，负责初始化全局服务、设置主题、管理导航，并将所有功能模块集成在一起。
- **关键屏幕**: `MainActivity` - 应用的主Activity，承载所有UI和导航。
- **主要组件**:
    - `GymBroApp`: `Application`类，负责应用的全局初始化。
    - `MainActivity`: 应用的唯一Activity，负责UI和导航的组装。
    - `LoadingScreen`: 应用启动时的加载屏幕。
    - `RegionDetectionManager`: 负责检测用户所在的地区。

## 🔗 依赖关系

- **依赖模块**: `core`, `core-ml`, `domain`, `data`, `di`, `designSystem`, `navigation`, 以及所有的`features`模块。
- **关键功能**: 依赖`navigation`模块进行页面导航，依赖`di`模块进行依赖注入，依赖`designSystem`模块提供UI组件和主题。

## 📋 开发规范

- **应用初始化**: 全局服务的初始化应在`GymBroApp`类中进行，并尽可能地异步执行，避免阻塞主线程。
- **UI实现**: UI的实现应遵循`designSystem`的设计规范，并使用`Jetpack Compose`进行构建。
- **导航管理**: 导航逻辑应在`MainActivity`中统一管理，使用`NavHost`进行页面切换。

## 🚀 开发状态

- **完成功能**: 
    - 应用的全局初始化和依赖注入。
    - 主题管理和动态切换。
    - 页面导航和转场动画。
    - 应用启动时的加载流程和地区检测。
- **进行中功能**: 
    - 优化启动性能，减少白屏时间。
    - 完善错误处理和用户提示。
- **计划功能**: 
    - 支持更多的国际化语言。
    - 增加对平板和折叠屏设备的支持。