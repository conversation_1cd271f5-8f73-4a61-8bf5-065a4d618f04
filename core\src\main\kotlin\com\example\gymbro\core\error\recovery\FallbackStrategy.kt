package com.example.gymbro.core.error.recovery

import com.example.gymbro.core.error.internal.recovery.FallbackStrategyImpl
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult

/**
 * 回退恢复策略接口
 *
 * 当主操作失败时提供备用数据源或默认值
 *
 * @param T 返回数据类型
 */
interface FallbackStrategy<T> : RecoveryStrategy<T> {
    /**
     * 判断是否可以处理指定的错误
     *
     * @param error 需要判断的错误
     * @return 如果可以处理返回true，否则返回false
     */
    fun canHandle(error: ModernDataError): Boolean

    /**
     * 执行回退策略并返回ModernResult
     *
     * @return 包装在ModernResult中的回退结果
     */
    suspend fun executeWithResult(): ModernResult<T>?

    companion object {
        /**
         * 创建使用默认值的回退策略
         *
         * @param defaultValue 默认值
         * @param predicate 可选的判断函数
         */
        fun <T> withDefault(
            defaultValue: T,
            predicate: ((ModernDataError) -> Boolean)? = null,
        ): FallbackStrategy<T> = FallbackStrategyImpl({ defaultValue }, predicate)
    }
}
