package com.example.gymbro.data.local.entity.subscription

import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * 用户订阅信息数据库实体
 * 存储用户的订阅信息，与领域模型Subscription对应
 */
@Entity(
    tableName = "subscriptions",
    indices = [
        Index(value = ["userId"], name = "index_subscriptions_userId"),
        Index(value = ["subscriptionStatus"], name = "index_subscriptions_status"),
        Index(value = ["userId", "subscriptionStatus", "endDate"], name = "index_subscriptions_active"),
        Index(value = ["startDate"], name = "index_subscriptions_startDate"),
        Index(value = ["endDate"], name = "index_subscriptions_endDate"),
        Index(value = ["isSynced"], name = "index_subscriptions_synced"),
    ],
)
data class SubscriptionEntity(
    /**
     * 订阅ID，主键
     */
    @PrimaryKey
    val subscriptionId: String,

    /**
     * 用户ID
     */
    val userId: String,

    /**
     * 计划ID
     */
    val planId: String,

    /**
     * 计划类型，存储为字符串，对应PlanType枚举
     */
    val planType: String,

    /**
     * 订阅状态，存储为字符串，对应SubscriptionStatus枚举
     */
    var subscriptionStatus: String,

    /**
     * 开始日期，使用Long时间戳存储（毫秒）
     */
    val startDate: Long? = null,

    /**
     * 结束日期，使用Long时间戳存储（毫秒），可为空
     */
    var endDate: Long? = null,

    /**
     * 试用期结束日期，使用Long时间戳存储（毫秒），可为空
     */
    val trialEndDate: Long?,

    /**
     * 是否自动续费
     */
    var autoRenew: Boolean = false,

    /**
     * 支付方式，存储为字符串，可以为空
     */
    val paymentMethod: String?,

    /**
     * 相关联的支付ID
     */
    val paymentId: String?,

    /**
     * 取消日期，使用Long时间戳存储（毫秒），可为空
     */
    var canceledAt: Long? = null,

    /**
     * 元数据，存储为JSON字符串
     */
    val metadata: String? = null,

    /**
     * 创建时间，使用Long时间戳存储（毫秒）
     */
    val createdAt: Long? = null,

    /**
     * 更新时间，使用Long时间戳存储（毫秒）
     */
    var updatedAt: Long? = null,

    /**
     * 同步状态
     * 0: 未同步
     * 1: 已同步
     * 2: 同步失败
     */
    val syncStatus: Int = 0,

    /**
     * 是否已同步到服务器
     */
    val isSynced: Boolean = false,

    /**
     * 最后修改时间戳（用于同步与冲突解决）
     */
    val lastModified: Long = System.currentTimeMillis(),
)
