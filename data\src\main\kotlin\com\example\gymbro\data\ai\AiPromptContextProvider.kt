package com.example.gymbro.data.ai

import com.example.gymbro.domain.service.profile.UserPreferencePort
import kotlinx.serialization.json.*
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * AI提示词上下文提供器
 *
 * 负责将用户的健身偏好转换为AI模型可理解的JSON上下文
 * 在AiGateway构建Prompt时注入用户偏好信息
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Singleton
class AiPromptContextProvider
@Inject
constructor(
    private val preferencePort: UserPreferencePort,
    private val json: Json,
) {
    /**
     * 构建用户偏好上下文块
     *
     * 将用户的训练目标和训练日转换为JSON格式的上下文信息
     * 如果用户未设置任何偏好，返回空字符串以减少Token消耗
     *
     * @return JSON格式的用户偏好上下文，如果无偏好则返回空字符串
     */
    suspend fun buildContextBlock(): String {
        return try {
            val preference = preferencePort.current()

            // 如果用户没有设置任何偏好，返回空字符串减少Token消耗
            if (!preference.hasAnyPreference()) {
                Timber.d("用户未设置偏好，跳过上下文注入")
                return ""
            }

            val contextObj =
                buildJsonObject {
                    // 添加训练目标
                    preference.primaryGoal?.let { goal ->
                        put("goal", goal.name.lowercase())
                    }

                    // 添加训练日（排序以确保输出一致性）
                    if (preference.workoutDays.isNotEmpty()) {
                        putJsonArray("workoutDays") {
                            preference.workoutDays
                                .map { it.name.lowercase() }
                                .sorted() // 确保输出顺序一致，避免测试抖动
                                .forEach { dayName ->
                                    add(JsonPrimitive(dayName))
                                }
                        }
                    }
                }

            val contextString = json.encodeToString(contextObj)
            Timber.d("构建用户偏好上下文: $contextString")

            contextString
        } catch (e: Exception) {
            Timber.e(e, "构建用户偏好上下文时发生异常")
            ""
        }
    }

    /**
     * 构建完整的用户偏好提示词块
     *
     * 包含格式化的标题和JSON内容，用于插入到系统提示词中
     *
     * @return 格式化的用户偏好提示词块
     */
    suspend fun buildUserPreferencePromptBlock(): String {
        val contextBlock = buildContextBlock()

        return if (contextBlock.isNotEmpty()) {
            """
                USER_PREFERENCE:
                $contextBlock
            """.trimIndent()
        } else {
            ""
        }
    }

    /**
     * 获取用户偏好的简要描述（用于日志和调试）
     *
     * @return 用户偏好的可读描述
     */
    suspend fun getPreferenceSummary(): String =
        try {
            val preference = preferencePort.current()

            if (!preference.hasAnyPreference()) {
                "无偏好设置"
            } else {
                val goalText = preference.primaryGoal?.name ?: "未设置"
                val daysText =
                    if (preference.workoutDays.isNotEmpty()) {
                        preference.getWorkoutDaysString()
                    } else {
                        "未设置"
                    }
                "目标: $goalText, 训练日: $daysText"
            }
        } catch (e: Exception) {
            Timber.e(e, "获取偏好摘要时发生异常")
            "获取失败"
        }
}
