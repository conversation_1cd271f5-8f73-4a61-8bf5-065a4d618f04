package com.example.gymbro.core.util

import com.example.gymbro.core.resources.ResourceProvider
import kotlinx.datetime.Clock
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.Date
import java.util.Locale
import java.time.Instant as JavaInstant
import kotlinx.datetime.Instant as KotlinInstant

/**
 * 日期时间工具类
 * 提供日期时间转换和处理的各种实用方法
 */
class DateTimeUtils(private val resourceProvider: ResourceProvider) {

    /**
     * 默认时区 - 系统默认
     */
    private val DEFAULT_ZONE_ID = ZoneId.systemDefault()

    companion object {
        /**
         * 将毫秒时间戳转换为ZonedDateTime
         *
         * @param epochMilli 毫秒时间戳
         * @param zoneId 时区，默认使用系统时区
         * @return ZonedDateTime对象
         */
        fun ofEpochMilli(
            epochMilli: Long,
            zoneId: ZoneId = ZoneId.systemDefault(),
        ): ZonedDateTime {
            return ZonedDateTime.ofInstant(
                JavaInstant.ofEpochMilli(epochMilli),
                zoneId,
            )
        }

        /**
         * 将ZonedDateTime转换为毫秒时间戳
         *
         * @return 毫秒时间戳
         */
        fun ZonedDateTime.toEpochMilli(): Long {
            return this.toInstant().toEpochMilli()
        }

        /**
         * 将Date转换为LocalDateTime
         *
         * @param zoneId 时区，默认使用系统时区
         * @return LocalDateTime对象
         */
        fun Date.toLocalDateTime(zoneId: ZoneId = ZoneId.systemDefault()): LocalDateTime {
            return LocalDateTime.ofInstant(this.toInstant(), zoneId)
        }

        /**
         * 将Date转换为LocalDate
         *
         * @param zoneId 时区，默认使用系统时区
         * @return LocalDate对象
         */
        fun Date.toLocalDate(zoneId: ZoneId = ZoneId.systemDefault()): LocalDate {
            return this.toLocalDateTime(zoneId).toLocalDate()
        }

        /**
         * 将LocalDateTime转换为Date
         *
         * @param zoneId 时区，默认使用系统时区
         * @return Date对象
         */
        fun LocalDateTime.toDate(zoneId: ZoneId = ZoneId.systemDefault()): Date {
            return Date.from(this.atZone(zoneId).toInstant())
        }

        /**
         * 将LocalDate转换为Date
         *
         * @param zoneId 时区，默认使用系统时区
         * @return Date对象
         */
        fun LocalDate.toDate(zoneId: ZoneId = ZoneId.systemDefault()): Date {
            return Date.from(this.atStartOfDay(zoneId).toInstant())
        }

        /**
         * 将LocalDateTime格式化为字符串
         *
         * @param pattern 日期格式，默认为"yyyy-MM-dd HH:mm:ss"
         * @return 格式化后的字符串
         */
        fun LocalDateTime.formatString(pattern: String = "yyyy-MM-dd HH:mm:ss"): String {
            return this.format(DateTimeFormatter.ofPattern(pattern))
        }

        /**
         * 将ZonedDateTime格式化为字符串
         *
         * @param pattern 日期格式，默认为"yyyy-MM-dd HH:mm:ss"
         * @return 格式化后的字符串
         */
        fun ZonedDateTime.formatString(pattern: String = "yyyy-MM-dd HH:mm:ss"): String {
            return this.format(DateTimeFormatter.ofPattern(pattern))
        }

        /**
         * 计算两个LocalDateTime之间的毫秒差
         *
         * @param other 另一个LocalDateTime
         * @return 毫秒差值
         */
        fun LocalDateTime.millisUntil(other: LocalDateTime): Long {
            val thisInstant = this.atZone(ZoneId.systemDefault()).toInstant()
            val otherInstant = other.atZone(ZoneId.systemDefault()).toInstant()
            return otherInstant.toEpochMilli() - thisInstant.toEpochMilli()
        }

        /**
         * 获取当前的ZonedDateTime
         *
         * @param zoneId 时区，默认使用系统时区
         * @return 当前ZonedDateTime
         */
        fun now(zoneId: ZoneId = ZoneId.systemDefault()): ZonedDateTime {
            return ZonedDateTime.now(zoneId)
        }

        /**
         * 获取今天的LocalDate
         *
         * @return 今天的LocalDate
         */
        fun today(): LocalDate {
            return LocalDate.now()
        }

        /**
         * 获取当前时间
         */
        fun getCurrentTime(): KotlinInstant = Clock.System.now()

        /**
         * 将Long类型的时间戳转换为Instant，如果为非法值则返回null
         */
        fun Long.toInstantOrNull(): KotlinInstant? = if (this > 0) {
            KotlinInstant.fromEpochMilliseconds(
                this,
            )
        } else {
            null
        }

        /**
         * 将Long类型的时间戳转换为Instant
         */
        fun Long.toInstant(): KotlinInstant = KotlinInstant.fromEpochMilliseconds(this)

        /**
         * 将Instant转换为毫秒时间戳
         */
        fun KotlinInstant.toEpochMillis(): Long = this.toEpochMilliseconds()

        /**
         * 将Kotlin的Instant转为Java的Instant (用于兼容Java时间API)
         */
        fun KotlinInstant.toJavaInstant(): JavaInstant = JavaInstant.ofEpochMilli(this.toEpochMilliseconds())

        /**
         * 将Java的Instant转为Kotlin的Instant (用于兼容Java时间API)
         */
        fun JavaInstant.toKotlinInstant(): KotlinInstant = KotlinInstant.fromEpochMilliseconds(
            this.toEpochMilli(),
        )
    }

    /**
     * 格式化日期时间
     *
     * @param timestamp 时间戳（毫秒）
     * @return 格式化后的日期时间字符串，如"2023年5月9日 14:30"
     */
    fun formatDateTime(timestamp: Long): String {
        val dateTime = ZonedDateTime.ofInstant(JavaInstant.ofEpochMilli(timestamp), DEFAULT_ZONE_ID)
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm", Locale.CHINA))
    }

    /**
     * 格式化日期时间 (Date类型重载)
     */
    fun formatDateTime(date: Date): String {
        return formatDateTime(date.time)
    }

    /**
     * 格式化日期时间 (KotlinInstant类型重载)
     */
    fun formatDateTime(instant: KotlinInstant): String {
        return formatDateTime(instant.toEpochMilliseconds())
    }

    /**
     * 格式化日期时间 (LocalDateTime类型重载)
     */
    fun formatDateTime(localDateTime: LocalDateTime): String {
        return formatDateTime(localDateTime.atZone(DEFAULT_ZONE_ID).toInstant().toEpochMilli())
    }

    /**
     * 格式化日期时间 (JavaInstant类型重载)
     */
    fun formatDateTime(instant: JavaInstant): String {
        return formatDateTime(instant.toEpochMilli())
    }

    /**
     * 格式化日期（仅显示日期部分）
     *
     * @param timestamp 时间戳（毫秒）
     * @return 格式化后的日期字符串，如"2023年5月9日"
     */
    fun formatDate(timestamp: Long): String {
        val date = ZonedDateTime.ofInstant(JavaInstant.ofEpochMilli(timestamp), DEFAULT_ZONE_ID)
        return date.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日", Locale.CHINA))
    }

    /**
     * 格式化日期 (Date类型重载)
     */
    fun formatDate(date: Date): String {
        return formatDate(date.time)
    }

    /**
     * 格式化日期 (KotlinInstant类型重载)
     */
    fun formatDate(instant: KotlinInstant): String {
        return formatDate(instant.toEpochMilliseconds())
    }

    /**
     * 格式化日期 (LocalDateTime类型重载)
     */
    fun formatDate(localDateTime: LocalDateTime): String {
        return formatDate(localDateTime.atZone(DEFAULT_ZONE_ID).toInstant().toEpochMilli())
    }

    /**
     * 格式化日期 (JavaInstant类型重载)
     */
    fun formatDate(instant: JavaInstant): String {
        return formatDate(instant.toEpochMilli())
    }

    /**
     * 格式化相对时间（如"3小时前"、"昨天"等）
     *
     * @param timestamp 时间戳（毫秒）
     * @return 相对时间字符串
     */
    fun formatRelativeTime(timestamp: Long): String {
        val now = ZonedDateTime.now()
        val dateTime = ZonedDateTime.ofInstant(JavaInstant.ofEpochMilli(timestamp), DEFAULT_ZONE_ID)

        val minutesDiff = java.time.Duration.between(dateTime, now).toMinutes()

        return when {
            minutesDiff < 1 -> "刚刚"
            minutesDiff < 60 -> "${minutesDiff}分钟前"
            minutesDiff < 24 * 60 -> "${minutesDiff / 60}小时前"
            minutesDiff < 48 * 60 -> "昨天"
            minutesDiff < 7 * 24 * 60 -> "${minutesDiff / (24 * 60)}天前"
            minutesDiff < 30 * 24 * 60 -> "${minutesDiff / (7 * 24 * 60)}周前"
            minutesDiff < 365 * 24 * 60 -> "${minutesDiff / (30 * 24 * 60)}个月前"
            else -> "${minutesDiff / (365 * 24 * 60)}年前"
        }
    }

    /**
     * 格式化相对时间 (Date类型重载)
     */
    fun formatRelativeTime(date: Date): String {
        return formatRelativeTime(date.time)
    }

    /**
     * 格式化相对时间 (KotlinInstant类型重载)
     */
    fun formatRelativeTime(instant: KotlinInstant): String {
        return formatRelativeTime(instant.toEpochMilliseconds())
    }

    /**
     * 格式化相对时间 (LocalDateTime类型重载)
     */
    fun formatRelativeTime(localDateTime: LocalDateTime): String {
        return formatRelativeTime(localDateTime.atZone(DEFAULT_ZONE_ID).toInstant().toEpochMilli())
    }

    /**
     * 格式化相对时间 (JavaInstant类型重载)
     */
    fun formatRelativeTime(instant: JavaInstant): String {
        return formatRelativeTime(instant.toEpochMilli())
    }
}
