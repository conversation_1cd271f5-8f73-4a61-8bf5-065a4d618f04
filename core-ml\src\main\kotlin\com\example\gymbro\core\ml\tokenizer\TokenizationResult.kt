package com.example.gymbro.core.ml.tokenizer

/**
 * 分词处理结果
 *
 * 封装了BERT/BGE模型所需的输入格式
 */
data class TokenizationResult(
    /**
     * Token ID序列
     */
    val inputIds: IntArray,

    /**
     * 注意力掩码，标识真实token和padding token
     */
    val attentionMask: IntArray,

    /**
     * Token类型ID（用于区分句子A和句子B）
     */
    val tokenTypeIds: IntArray? = null,
) {

    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as TokenizationResult

        if (!inputIds.contentEquals(other.inputIds)) return false
        if (!attentionMask.contentEquals(other.attentionMask)) return false
        if (tokenTypeIds != null) {
            if (other.tokenTypeIds == null) return false
            if (!tokenTypeIds.contentEquals(other.tokenTypeIds)) return false
        } else if (other.tokenTypeIds != null) return false

        return true
    }

    override fun hashCode(): Int {
        var result = inputIds.contentHashCode()
        result = 31 * result + attentionMask.contentHashCode()
        result = 31 * result + (tokenTypeIds?.contentHashCode() ?: 0)
        return result
    }
}
