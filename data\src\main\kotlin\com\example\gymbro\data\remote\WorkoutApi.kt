package com.example.gymbro.data.remote

import retrofit2.http.*

/**
 * 训练API接口
 * 定义与训练相关的网络请求方法
 */
interface WorkoutApi {
    /**
     * 获取用户的所有训练
     * @param userId 用户ID
     * @return 训练数据列表
     */
    @GET("workouts/user/{userId}")
    suspend fun getWorkouts(
        @Path("userId") userId: String,
    ): List<Map<String, Any>>

    /**
     * 保存训练
     * @param workout 训练数据
     * @return 保存后的训练数据（包含服务器生成的ID等信息）
     */
    @POST("workouts")
    suspend fun saveWorkout(
        @Body workout: Map<String, Any>,
    ): Map<String, Any>

    /**
     * 删除训练
     * @param workoutId 训练ID
     */
    @DELETE("workouts/{workoutId}")
    suspend fun deleteWorkout(
        @Path("workoutId") workoutId: String,
    )
}
