package com.example.gymbro.core.util

import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDate
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toInstant
import kotlinx.datetime.toLocalDateTime
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 时间工具类
 * 提供时间格式化和解析的工具方法
 *
 * 注意：此文件已从core/utils移动到core/util，作为包结构优化的一部分
 */
object TimeUtils {
    // 常用日期格式
    private const val DEFAULT_DATE_FORMAT = "yyyy-MM-dd"
    private const val DEFAULT_TIME_FORMAT = "HH:mm:ss"
    private const val DEFAULT_DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss"
    private const val DEFAULT_DATETIME_FORMAT_WITH_MILLIS = "yyyy-MM-dd HH:mm:ss.SSS"
    private const val DEFAULT_TIMEZONE = "Asia/Shanghai"

    // 默认时区
    private val defaultTimeZone = TimeZone.currentSystemDefault()

    /**
     * 格式化日期
     *
     * @param date 日期对象
     * @param pattern 格式模式，默认为"yyyy-MM-dd"
     * @return 格式化后的日期字符串
     */
    fun formatDate(date: Date, pattern: String = DEFAULT_DATE_FORMAT): String {
        return SimpleDateFormat(pattern, Locale.getDefault()).format(date)
    }

    /**
     * 格式化日期 (Long时间戳重载)
     *
     * @param timestamp 毫秒时间戳
     * @param pattern 格式模式，默认为"yyyy-MM-dd"
     * @return 格式化后的日期字符串
     */
    fun formatDate(timestamp: Long, pattern: String = DEFAULT_DATE_FORMAT): String {
        return formatDate(Date(timestamp), pattern)
    }

    /**
     * 格式化日期 (Instant重载)
     *
     * @param instant Instant对象
     * @param pattern 格式模式，默认为"yyyy-MM-dd"
     * @return 格式化后的日期字符串
     */
    fun formatDate(instant: Instant, pattern: String = DEFAULT_DATE_FORMAT): String {
        return formatDate(Date(instant.toEpochMilliseconds()), pattern)
    }

    /**
     * 格式化日期 (LocalDate重载)
     *
     * @param localDate LocalDate对象
     * @param pattern 格式模式，默认为"yyyy-MM-dd"
     * @return 格式化后的日期字符串
     */
    fun formatDate(localDate: LocalDate, pattern: String = DEFAULT_DATE_FORMAT): String {
        val calendar = java.util.Calendar.getInstance()
        calendar.set(localDate.year, localDate.monthNumber - 1, localDate.dayOfMonth)
        return formatDate(calendar.time, pattern)
    }

    /**
     * 格式化日期 (LocalDateTime重载)
     *
     * @param localDateTime LocalDateTime对象
     * @param pattern 格式模式，默认为"yyyy-MM-dd"
     * @return 格式化后的日期字符串
     */
    fun formatDate(localDateTime: LocalDateTime, pattern: String = DEFAULT_DATE_FORMAT): String {
        val calendar = java.util.Calendar.getInstance()
        calendar.set(
            localDateTime.year,
            localDateTime.monthNumber - 1,
            localDateTime.dayOfMonth,
            localDateTime.hour,
            localDateTime.minute,
            localDateTime.second,
        )
        return formatDate(calendar.time, pattern)
    }

    /**
     * 格式化日期时间
     *
     * @param date 日期对象
     * @param pattern 格式模式，默认为"yyyy-MM-dd HH:mm:ss"
     * @return 格式化后的日期时间字符串
     */
    fun formatDateTime(date: Date, pattern: String = DEFAULT_DATETIME_FORMAT): String {
        return SimpleDateFormat(pattern, Locale.getDefault()).format(date)
    }

    /**
     * 格式化日期时间 (Long时间戳重载)
     *
     * @param timestamp 毫秒时间戳
     * @param pattern 格式模式，默认为"yyyy-MM-dd HH:mm:ss"
     * @return 格式化后的日期时间字符串
     */
    fun formatDateTime(timestamp: Long, pattern: String = DEFAULT_DATETIME_FORMAT): String {
        return formatDateTime(Date(timestamp), pattern)
    }

    /**
     * 格式化日期时间 (Instant重载)
     *
     * @param instant Instant对象
     * @param pattern 格式模式，默认为"yyyy-MM-dd HH:mm:ss"
     * @return 格式化后的日期时间字符串
     */
    fun formatDateTime(instant: Instant, pattern: String = DEFAULT_DATETIME_FORMAT): String {
        return formatDateTime(Date(instant.toEpochMilliseconds()), pattern)
    }

    /**
     * 格式化日期时间 (LocalDateTime重载)
     *
     * @param localDateTime LocalDateTime对象
     * @param pattern 格式模式，默认为"yyyy-MM-dd HH:mm:ss"
     * @return 格式化后的日期时间字符串
     */
    fun formatDateTime(localDateTime: LocalDateTime, pattern: String = DEFAULT_DATETIME_FORMAT): String {
        val calendar = java.util.Calendar.getInstance()
        calendar.set(
            localDateTime.year,
            localDateTime.monthNumber - 1,
            localDateTime.dayOfMonth,
            localDateTime.hour,
            localDateTime.minute,
            localDateTime.second,
        )
        return formatDateTime(calendar.time, pattern)
    }

    /**
     * 解析日期字符串
     *
     * @param dateStr 日期字符串
     * @param pattern 格式模式，默认为"yyyy-MM-dd"
     * @return 解析后的Date对象，如果解析失败则返回null
     */
    fun parseDate(dateStr: String, pattern: String = DEFAULT_DATE_FORMAT): Date? {
        return try {
            SimpleDateFormat(pattern, Locale.getDefault()).parse(dateStr)
        } catch (e: Exception) {
            Timber.w("解析日期失败: $dateStr, 格式: $pattern, 错误: ${e.message}")
            null
        }
    }

    /**
     * 解析日期时间字符串
     *
     * @param dateTimeStr 日期时间字符串
     * @param pattern 格式模式，默认为"yyyy-MM-dd HH:mm:ss"
     * @return 解析后的Date对象，如果解析失败则返回null
     */
    fun parseDateTime(dateTimeStr: String, pattern: String = DEFAULT_DATETIME_FORMAT): Date? {
        return try {
            SimpleDateFormat(pattern, Locale.getDefault()).parse(dateTimeStr)
        } catch (e: Exception) {
            Timber.w("解析日期时间失败: $dateTimeStr, 格式: $pattern, 错误: ${e.message}")
            null
        }
    }

    /**
     * 获取当前时间的Instant
     *
     * @return 当前时间的Instant
     */
    fun now(): Instant {
        return Clock.System.now()
    }

    /**
     * 获取当前时间的毫秒时间戳
     *
     * @return 当前时间的毫秒时间戳
     */
    fun currentTimeMillis(): Long {
        return Clock.System.now().toEpochMilliseconds()
    }

    // ===== Instant和Long之间的转换方法 =====

    /**
     * 将Instant转换为毫秒时间戳
     *
     * 注意：Instant已内置toEpochMilliseconds()方法，此方法为显式声明
     *
     * @param instant Instant对象
     * @return 毫秒时间戳，如果输入为null则返回null
     */
    fun instantToEpochMillis(instant: Instant?): Long? {
        return instant?.toEpochMilliseconds()
    }

    /**
     * 将毫秒时间戳转换为Instant
     *
     * @param timestamp 毫秒时间戳
     * @return Instant对象，如果输入为null则返回null
     */
    fun longToInstant(timestamp: Long?): Instant? {
        return timestamp?.let { Instant.fromEpochMilliseconds(it) }
    }

    // ===== LocalDateTime和Long之间的转换方法 =====

    /**
     * 将LocalDateTime转换为毫秒时间戳
     *
     * @param localDateTime LocalDateTime对象
     * @param timeZone 时区，默认为系统默认时区
     * @return 毫秒时间戳，如果输入为null则返回null
     */
    fun localDateTimeToEpochMillis(
        localDateTime: LocalDateTime?,
        timeZone: TimeZone = defaultTimeZone,
    ): Long? {
        return localDateTime?.toInstant(timeZone)?.toEpochMilliseconds()
    }

    /**
     * 将毫秒时间戳转换为LocalDateTime
     *
     * @param timestamp 毫秒时间戳
     * @param timeZone 时区，默认为系统默认时区
     * @return LocalDateTime对象，如果输入为null则返回null
     */
    fun longToLocalDateTime(timestamp: Long?, timeZone: TimeZone = defaultTimeZone): LocalDateTime? {
        return timestamp?.let { Instant.fromEpochMilliseconds(it).toLocalDateTime(timeZone) }
    }

    // ===== LocalDate和Long之间的转换方法 =====

    /**
     * 将LocalDate转换为毫秒时间戳（当天开始时间）
     *
     * @param localDate LocalDate对象
     * @param timeZone 时区，默认为系统默认时区
     * @return 毫秒时间戳，如果输入为null则返回null
     */
    fun localDateToEpochMillis(localDate: LocalDate?, timeZone: TimeZone = defaultTimeZone): Long? {
        if (localDate == null) return null

        val localDateTime = LocalDateTime(
            year = localDate.year,
            monthNumber = localDate.monthNumber,
            dayOfMonth = localDate.dayOfMonth,
            hour = 0,
            minute = 0,
            second = 0,
            nanosecond = 0,
        )

        return localDateTime.toInstant(timeZone).toEpochMilliseconds()
    }

    /**
     * 将毫秒时间戳转换为LocalDate
     *
     * @param timestamp 毫秒时间戳
     * @param timeZone 时区，默认为系统默认时区
     * @return LocalDate对象，如果输入为null则返回null
     */
    fun longToLocalDate(timestamp: Long?, timeZone: TimeZone = defaultTimeZone): LocalDate? {
        return timestamp?.let { Instant.fromEpochMilliseconds(it).toLocalDateTime(timeZone).date }
    }
}
