package com.example.gymbro.data.local.version

import android.content.Context
import android.content.SharedPreferences
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.shared.base.version.RegionVersionConfig
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 版本配置本地存储
 *
 * 使用SharedPreferences存储版本配置信息
 */
@Singleton
class VersionConfigStorage @Inject constructor(
    @ApplicationContext private val context: Context,
) {

    private val prefs: SharedPreferences by lazy {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }

    private val json = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true
    }

    /**
     * 保存区域版本配置
     */
    suspend fun saveRegionConfig(
        config: RegionVersionConfig,
    ): ModernResult<Unit> = withContext(Dispatchers.IO) {
        try {
            val serializable = config.toSerializable()
            val jsonString = json.encodeToString(SerializableRegionVersionConfig.serializer(), serializable)

            prefs.edit()
                .putString(getConfigKey(config.region), jsonString)
                .apply()

            Timber.d("Saved version config for region: ${config.region}")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "Failed to save version config for region: ${config.region}")
            ModernResult.Error(
                com.example.gymbro.core.error.types.ModernDataError(
                    operationName = "saveRegionConfig",
                    errorType = com.example.gymbro.core.error.types.GlobalErrorType.System.General,
                    uiMessage = UiText.DynamicString("保存版本配置失败"),
                    cause = e,
                ),
            )
        }
    }

    /**
     * 获取区域版本配置
     */
    suspend fun getRegionConfig(
        region: String,
    ): ModernResult<RegionVersionConfig?> = withContext(Dispatchers.IO) {
        try {
            val jsonString = prefs.getString(getConfigKey(region), null)

            if (jsonString == null) {
                Timber.d("No version config found for region: $region")
                return@withContext ModernResult.Success(null)
            }

            val serializable = json.decodeFromString(SerializableRegionVersionConfig.serializer(), jsonString)
            val config = serializable.toDomain()

            Timber.d("Loaded version config for region: $region")
            ModernResult.Success(config)
        } catch (e: Exception) {
            Timber.e(e, "Failed to load version config for region: $region")
            ModernResult.Error(
                com.example.gymbro.core.error.types.ModernDataError(
                    operationName = "getRegionConfig",
                    errorType = com.example.gymbro.core.error.types.GlobalErrorType.System.General,
                    uiMessage = UiText.DynamicString("读取版本配置失败"),
                    cause = e,
                ),
            )
        }
    }

    /**
     * 删除区域版本配置
     */
    suspend fun deleteRegionConfig(region: String): ModernResult<Unit> = withContext(Dispatchers.IO) {
        try {
            prefs.edit()
                .remove(getConfigKey(region))
                .apply()

            Timber.d("Deleted version config for region: $region")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "Failed to delete version config for region: $region")
            ModernResult.Error(
                com.example.gymbro.core.error.types.ModernDataError(
                    operationName = "deleteRegionConfig",
                    errorType = com.example.gymbro.core.error.types.GlobalErrorType.System.General,
                    uiMessage = UiText.DynamicString("删除版本配置失败"),
                    cause = e,
                ),
            )
        }
    }

    /**
     * 清除所有版本配置
     */
    suspend fun clearAll(): ModernResult<Unit> = withContext(Dispatchers.IO) {
        try {
            val editor = prefs.edit()

            // 删除所有以CONFIG_PREFIX开头的键
            prefs.all.keys.filter { it.startsWith(CONFIG_PREFIX) }.forEach { key ->
                editor.remove(key)
            }

            editor.apply()

            Timber.d("Cleared all version configs")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "Failed to clear version configs")
            ModernResult.Error(
                com.example.gymbro.core.error.types.ModernDataError(
                    operationName = "clearAll",
                    errorType = com.example.gymbro.core.error.types.GlobalErrorType.System.General,
                    uiMessage = UiText.DynamicString("清除版本配置失败"),
                    cause = e,
                ),
            )
        }
    }

    private fun getConfigKey(region: String): String = "$CONFIG_PREFIX$region"

    companion object {
        private const val PREFS_NAME = "version_config_prefs"
        private const val CONFIG_PREFIX = "region_config_"
    }
}

/**
 * 可序列化的区域版本配置
 */
@Serializable
private data class SerializableRegionVersionConfig(
    val region: String,
    val isLocked: Boolean,
    val lockReason: String? = null,
    val minimumVersion: String? = null,
    val blockedVersions: List<String> = emptyList(),
    val forceUpdateUrl: String? = null,
    val contactInfo: String? = null,
    val lastUpdated: Long = System.currentTimeMillis(),
)

/**
 * 扩展函数：将Domain模型转换为可序列化模型
 */
private fun RegionVersionConfig.toSerializable(): SerializableRegionVersionConfig {
    return SerializableRegionVersionConfig(
        region = region,
        isLocked = isLocked,
        lockReason = lockReason?.let {
            when (it) {
                is UiText.DynamicString -> it.value
                is UiText.StringResource -> "string_resource_${it.resId}" // 简化处理
                else -> it.toString() // 处理其他情况
            }
        },
        minimumVersion = minimumVersion,
        blockedVersions = blockedVersions,
        forceUpdateUrl = forceUpdateUrl,
        contactInfo = contactInfo,
        lastUpdated = lastUpdated,
    )
}

/**
 * 扩展函数：将可序列化模型转换为Domain模型
 */
private fun SerializableRegionVersionConfig.toDomain(): RegionVersionConfig {
    return RegionVersionConfig(
        region = region,
        isLocked = isLocked,
        lockReason = lockReason?.let { UiText.DynamicString(it) },
        minimumVersion = minimumVersion,
        blockedVersions = blockedVersions,
        forceUpdateUrl = forceUpdateUrl,
        contactInfo = contactInfo,
        lastUpdated = lastUpdated,
    )
}
