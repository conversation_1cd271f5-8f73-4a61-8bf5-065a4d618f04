package com.example.gymbro.data.workout.plan.converter

import androidx.room.TypeConverter
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import com.example.gymbro.shared.models.workout.PlanProgressStatus

/**
 * PlanDB 类型转换器
 *
 * 处理 List<String> 等复杂类型的数据库存储转换
 */
class PlanTypeConverters {

    private val json = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true
    }

    @TypeConverter
    fun fromStringList(value: List<String>): String {
        return json.encodeToString(value)
    }

    @TypeConverter
    fun toStringList(value: String): List<String> {
        return try {
            json.decodeFromString<List<String>>(value)
        } catch (e: Exception) {
            emptyList()
        }
    }

    @TypeConverter
    fun fromPlanProgressStatus(status: PlanProgressStatus): String {
        return status.name
    }

    @TypeConverter
    fun toPlanProgressStatus(name: String): PlanProgressStatus {
        return try {
            PlanProgressStatus.valueOf(name)
        } catch (e: Exception) {
            PlanProgressStatus.NOT_STARTED
        }
    }
}
