package com.example.gymbro.data.service

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.coach.model.ai.WorkoutContext
import com.example.gymbro.domain.workout.model.DraftSource
import com.example.gymbro.domain.workout.model.TemplateDraft
import com.example.gymbro.domain.workout.repository.WorkoutDraftRepository
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import kotlinx.datetime.Clock
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * AiInteractionServiceImpl单元测试
 * 验证Function Calling集成功能
 */
class AiInteractionServiceImplTest {
    private lateinit var workoutDraftRepository: WorkoutDraftRepository
    private lateinit var aiInteractionService: AiInteractionServiceImpl
    private val testDispatcher = StandardTestDispatcher()

    @Before
    fun setup() {
        workoutDraftRepository = mockk()
        aiInteractionService =
            AiInteractionServiceImpl(
                workoutDraftRepository = workoutDraftRepository,
                ioDispatcher = testDispatcher,
            )
    }

    @Test
    fun `generateWorkoutTemplateFromPrompt should call repository with enhanced prompt`() =
        runTest(
            testDispatcher,
        ) {
            // Given
            val userId = "test_user_123"
            val prompt = "帮我制定胸肌训练计划"
            val context =
                WorkoutContext(
                    fitnessLevel = "初级",
                    availableEquipment = listOf("哑铃", "杠铃"),
                    timeConstraints = 60,
                    injuryHistory = emptyList(),
                )

            val expectedTemplate =
                TemplateDraft(
                    id = "template_123",
                    name = "胸肌训练计划",
                    description = "AI生成的胸肌训练计划",
                    exercises = emptyList(),
                    source = DraftSource.AI_GENERATED,
                    createdAt = Clock.System.now(),
                    userId = userId,
                )

            // Mock repository response
            coEvery {
                workoutDraftRepository.generateTemplateDraftWithAI(
                    prompt = any(),
                    userId = userId,
                    history = emptyList(),
                )
            } returns flowOf(ModernResult.Success(expectedTemplate))

            // When
            val result =
                aiInteractionService.generateWorkoutTemplateFromPrompt(
                    userId = userId,
                    prompt = prompt,
                    context = context,
                )

            // Then
            assertTrue(result is ModernResult.Success)
            assertEquals(expectedTemplate.id, result.data.id)
            assertEquals(expectedTemplate.name, result.data.name)

            // Verify repository was called with enhanced prompt
            verify {
                workoutDraftRepository.generateTemplateDraftWithAI(
                    prompt =
                    match { enhancedPrompt ->
                        enhancedPrompt.contains(prompt) &&
                            enhancedPrompt.contains("健身水平：初级") &&
                            enhancedPrompt.contains("可用器械：哑铃, 杠铃") &&
                            enhancedPrompt.contains("时间限制：每次训练约60分钟")
                    },
                    userId = userId,
                    history = emptyList(),
                )
            }
        }

    @Test
    fun `generateWorkoutTemplateFromPrompt should handle repository error`() =
        runTest(testDispatcher) {
            // Given
            val userId = "test_user_123"
            val prompt = "帮我制定训练计划"

            // Mock repository error
            coEvery {
                workoutDraftRepository.generateTemplateDraftWithAI(
                    prompt = any(),
                    userId = userId,
                    history = emptyList(),
                )
            } returns flowOf(ModernResult.Error(mockk()))

            // When
            val result =
                aiInteractionService.generateWorkoutTemplateFromPrompt(
                    userId = userId,
                    prompt = prompt,
                    context = null,
                )

            // Then
            assertTrue(result is ModernResult.Error)
        }

    @Test
    fun `buildEnhancedPrompt should include context information`() =
        runTest(testDispatcher) {
            // Given
            val userId = "test_user_123"
            val basePrompt = "制定训练计划"
            val context =
                WorkoutContext(
                    fitnessLevel = "中级",
                    availableEquipment = listOf("器械"),
                    timeConstraints = 45,
                    injuryHistory = listOf("膝盖伤病"),
                )

            val expectedTemplate =
                TemplateDraft(
                    id = "template_123",
                    name = "训练计划",
                    description = "AI生成",
                    exercises = emptyList(),
                    source = DraftSource.AI_GENERATED,
                    createdAt = Clock.System.now(),
                    userId = userId,
                )

            // Mock repository
            coEvery {
                workoutDraftRepository.generateTemplateDraftWithAI(
                    prompt = any(),
                    userId = userId,
                    history = emptyList(),
                )
            } returns flowOf(ModernResult.Success(expectedTemplate))

            // When
            aiInteractionService.generateWorkoutTemplateFromPrompt(
                userId = userId,
                prompt = basePrompt,
                context = context,
            )

            // Then - verify enhanced prompt contains all context info
            verify {
                workoutDraftRepository.generateTemplateDraftWithAI(
                    prompt =
                    match { enhancedPrompt ->
                        enhancedPrompt.contains(basePrompt) &&
                            enhancedPrompt.contains("用户背景信息") &&
                            enhancedPrompt.contains("健身水平：中级") &&
                            enhancedPrompt.contains("可用器械：器械") &&
                            enhancedPrompt.contains("时间限制：每次训练约45分钟") &&
                            enhancedPrompt.contains("伤病史：膝盖伤病")
                    },
                    userId = userId,
                    history = emptyList(),
                )
            }
        }
}
