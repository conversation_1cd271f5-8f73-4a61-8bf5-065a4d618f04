package com.example.gymbro.core.error

import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 网络错误恢复管理器
 *
 * 提供智能的网络错误恢复策略，包括自动重试、降级策略、离线模式等
 * 支持不同类型的网络错误和恢复场景
 *
 * P3阶段更新：使用ErrorCode替换硬编码中文字符串
 * 实现Error-code mapping pattern
 *
 * <AUTHOR> Team
 * @since 1.0.0
 * @updated 2.0.0 (P3阶段 - Error-code mapping)
 */
@Singleton
class NetworkErrorRecoveryManager @Inject constructor() {

    // 网络恢复状态
    private val _recoveryState = MutableStateFlow(NetworkRecoveryState())
    val recoveryState: Flow<NetworkRecoveryState> = _recoveryState.asStateFlow()

    // 重试配置
    private val defaultRetryConfig = RetryConfig(
        maxRetries = 3,
        baseDelayMs = 1000L,
        maxDelayMs = 10000L,
        backoffMultiplier = 2.0,
    )

    /**
     * 执行带网络错误恢复的操作
     *
     * @param operation 要执行的操作
     * @param retryConfig 重试配置
     * @param recoveryStrategy 恢复策略
     * @return 操作结果
     */
    suspend fun <T> executeWithRecovery(
        operation: suspend () -> ModernResult<T>,
        retryConfig: RetryConfig = defaultRetryConfig,
        recoveryStrategy: NetworkRecoveryStrategy = NetworkRecoveryStrategy.AUTO_RETRY,
    ): ModernResult<T> {
        var lastError: ModernDataError? = null
        var attempt = 0

        while (attempt <= retryConfig.maxRetries) {
            try {
                updateRecoveryState { it.copy(isRetrying = attempt > 0, currentAttempt = attempt) }

                val result = operation()

                when (result) {
                    is ModernResult.Success -> {
                        updateRecoveryState {
                            it.copy(
                                isRetrying = false,
                                lastSuccessTime = System.currentTimeMillis(),
                            )
                        }
                        return result
                    }
                    is ModernResult.Error -> {
                        lastError = result.error

                        if (!isNetworkError(result.error) || attempt >= retryConfig.maxRetries) {
                            updateRecoveryState { it.copy(isRetrying = false, lastError = result.error) }
                            return handleFinalError(result.error, recoveryStrategy)
                        }

                        // 计算重试延迟
                        val delay = calculateRetryDelay(attempt, retryConfig)
                        Timber.w(
                            result.error,
                            "网络错误，${delay}ms后重试 (${attempt + 1}/${retryConfig.maxRetries + 1})",
                        )

                        delay(delay)
                        attempt++
                    }
                    is ModernResult.Loading -> {
                        // 继续等待
                    }
                }
            } catch (e: Exception) {
                val error = e.toNetworkError()
                lastError = error

                if (attempt >= retryConfig.maxRetries) {
                    updateRecoveryState { it.copy(isRetrying = false, lastError = error) }
                    return ModernResult.Error(error)
                }

                val delay = calculateRetryDelay(attempt, retryConfig)
                Timber.w(e, "网络异常，${delay}ms后重试 (${attempt + 1}/${retryConfig.maxRetries + 1})")

                delay(delay)
                attempt++
            }
        }

        updateRecoveryState { it.copy(isRetrying = false, lastError = lastError) }
        return ModernResult.Error(lastError ?: createGenericNetworkError())
    }

    /**
     * 判断是否为网络错误
     */
    private fun isNetworkError(error: ModernDataError): Boolean {
        return error.operationName.contains("network", ignoreCase = true) ||
            error.operationName.contains("connection", ignoreCase = true) ||
            error.operationName.contains("timeout", ignoreCase = true) ||
            error.operationName.contains("sync", ignoreCase = true) ||
            error.statusCode in listOf(408, 429, 500, 502, 503, 504)
    }

    /**
     * 计算重试延迟（指数退避）
     */
    private fun calculateRetryDelay(attempt: Int, config: RetryConfig): Long {
        val delay = (config.baseDelayMs * Math.pow(config.backoffMultiplier, attempt.toDouble())).toLong()
        return minOf(delay, config.maxDelayMs)
    }

    /**
     * 处理最终错误（所有重试都失败后）
     */
    private fun <T> handleFinalError(
        error: ModernDataError,
        strategy: NetworkRecoveryStrategy,
    ): ModernResult<T> {
        val enhancedError = when (strategy) {
            NetworkRecoveryStrategy.AUTO_RETRY -> enhanceErrorWithRetryInfo(error)
            NetworkRecoveryStrategy.FALLBACK_TO_CACHE -> enhanceErrorWithCacheInfo(error)
            NetworkRecoveryStrategy.OFFLINE_MODE -> enhanceErrorWithOfflineInfo(error)
            NetworkRecoveryStrategy.USER_NOTIFICATION -> enhanceErrorWithUserNotification(error)
        }

        return ModernResult.Error(enhancedError)
    }

    /**
     * 增强错误信息 - 重试信息
     */
    private fun enhanceErrorWithRetryInfo(error: ModernDataError): ModernDataError {
        return error.copy(
            uiMessage = UiText.ErrorCode(ErrorCode.NETWORK_RETRY_EXHAUSTED),
            recoverable = true,
            metadataMap = error.metadataMap + mapOf(
                "recoveryStrategy" to "AUTO_RETRY",
                "retryCompleted" to true,
                "userAction" to "CHECK_NETWORK_AND_RETRY",
                "errorCode" to ErrorCode.NETWORK_RETRY_EXHAUSTED.code,
            ),
        )
    }

    /**
     * 增强错误信息 - 缓存回退信息
     */
    private fun enhanceErrorWithCacheInfo(error: ModernDataError): ModernDataError {
        return error.copy(
            uiMessage = UiText.ErrorCode(ErrorCode.NETWORK_FALLBACK_CACHE),
            recoverable = true,
            metadataMap = error.metadataMap + mapOf(
                "recoveryStrategy" to "FALLBACK_TO_CACHE",
                "offlineMode" to true,
                "userAction" to "CONTINUE_WITH_CACHE",
                "errorCode" to ErrorCode.NETWORK_FALLBACK_CACHE.code,
            ),
        )
    }

    /**
     * 增强错误信息 - 离线模式信息
     */
    private fun enhanceErrorWithOfflineInfo(error: ModernDataError): ModernDataError {
        return error.copy(
            uiMessage = UiText.ErrorCode(ErrorCode.NETWORK_OFFLINE_MODE),
            recoverable = true,
            metadataMap = error.metadataMap + mapOf(
                "recoveryStrategy" to "OFFLINE_MODE",
                "offlineMode" to true,
                "autoSyncWhenOnline" to true,
                "errorCode" to ErrorCode.NETWORK_OFFLINE_MODE.code,
            ),
        )
    }

    /**
     * 增强错误信息 - 用户通知信息
     */
    private fun enhanceErrorWithUserNotification(error: ModernDataError): ModernDataError {
        return error.copy(
            uiMessage = UiText.ErrorCode(ErrorCode.NETWORK_UNSTABLE),
            recoverable = true,
            metadataMap = error.metadataMap + mapOf(
                "recoveryStrategy" to "USER_NOTIFICATION",
                "requiresUserAction" to true,
                "userAction" to "CHECK_NETWORK_SETTINGS",
                "errorCode" to ErrorCode.NETWORK_UNSTABLE.code,
            ),
        )
    }

    /**
     * 更新恢复状态
     */
    private fun updateRecoveryState(update: (NetworkRecoveryState) -> NetworkRecoveryState) {
        _recoveryState.value = update(_recoveryState.value)
    }

    /**
     * 创建通用网络错误
     */
    private fun createGenericNetworkError(): ModernDataError {
        return ModernDataError(
            operationName = "network_recovery_failed",
            errorType = com.example.gymbro.core.error.types.GlobalErrorType.Network.Connection,
            uiMessage = UiText.ErrorCode(ErrorCode.NETWORK_CONNECTION_FAILED),
            recoverable = true,
            metadataMap = mapOf("errorCode" to ErrorCode.NETWORK_CONNECTION_FAILED.code),
        )
    }
}

/**
 * 异常转网络错误扩展函数
 */
private fun Exception.toNetworkError(): ModernDataError {
    return ModernDataError(
        operationName = "network_exception",
        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Network.Connection,
        uiMessage = UiText.ErrorCode(ErrorCode.NETWORK_EXCEPTION, this.message ?: ""),
        cause = this,
        recoverable = true,
        metadataMap = mapOf("errorCode" to ErrorCode.NETWORK_EXCEPTION.code),
    )
}

/**
 * 网络恢复状态
 */
data class NetworkRecoveryState(
    val isRetrying: Boolean = false,
    val currentAttempt: Int = 0,
    val lastError: ModernDataError? = null,
    val lastSuccessTime: Long? = null,
)

/**
 * 重试配置
 */
data class RetryConfig(
    val maxRetries: Int = 3,
    val baseDelayMs: Long = 1000L,
    val maxDelayMs: Long = 10000L,
    val backoffMultiplier: Double = 2.0,
)

/**
 * 网络恢复策略
 */
enum class NetworkRecoveryStrategy {
    /** 自动重试 */
    AUTO_RETRY,

    /** 回退到缓存 */
    FALLBACK_TO_CACHE,

    /** 离线模式 */
    OFFLINE_MODE,

    /** 用户通知 */
    USER_NOTIFICATION,
}
