package com.example.gymbro.data.remote.firebase.auth

import com.example.gymbro.core.error.extensions.toModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.auth.AuthErrors
import com.example.gymbro.core.ui.text.UiText
import com.google.firebase.auth.*
import kotlinx.coroutines.tasks.await
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Firebase账户关联服务，负责账户绑定和匿名用户升级操作
 */
@Singleton
class AccountLinkingService @Inject constructor(
    private val auth: FirebaseAuth,
) {

    /**
     * 将当前用户账户与给定的凭证关联
     * @param credential 要关联的身份验证凭证
     * @return 操作成功的ModernResult.Success，或包含错误的ModernResult.Error
     */
    suspend fun linkAccountWithCredential(credential: AuthCredential): ModernResult<Unit> {
        return try {
            val user = auth.currentUser ?: return ModernResult.error(
                AuthErrors.AuthError.unknown(
                    operationName = "AccountLinkingService.linkAccount.notLoggedIn",
                    message = UiText.DynamicString("Link account failed: User not logged in"),
                    metadataMap = mapOf("operation" to "link_account"),
                ),
            )
            user.linkWithCredential(credential).await()
            ModernResult.success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "关联账户失败")
            ModernResult.error(
                e.toModernDataError(
                    operationName = "AccountLinkingService.linkAccountWithCredential",
                    uiMessage = UiText.DynamicString("关联账户失败"),
                ),
            )
        }
    }

    /**
     * 将当前用户账户与手机号关联
     * @param phoneNumber 手机号码
     * @param code 短信验证码
     * @return 操作成功的ModernResult.Success，或包含错误的ModernResult.Error
     */
    suspend fun linkPhoneAccount(
        phoneNumber: String,
        code: String,
    ): ModernResult<Unit> = try {
        val credential = PhoneAuthProvider.getCredential("PLACEHOLDER_VERIFICATION_ID", code)
        linkAccountWithCredential(credential)
    } catch (e: Exception) {
        Timber.e(e, "关联手机号失败 - phone: %s", phoneNumber)
        ModernResult.error(
            e.toModernDataError(
                operationName = "AccountLinkingService.linkPhoneAccount",
                uiMessage = UiText.DynamicString("关联手机号失败"),
            ),
        )
    }

    /**
     * 将当前用户账户与邮箱密码关联
     * @param email 邮箱
     * @param password 密码
     * @return 操作成功的ModernResult.Success，或包含错误的ModernResult.Error
     */
    suspend fun linkEmailAccount(
        email: String,
        password: String,
    ): ModernResult<Unit> = try {
        val credential = EmailAuthProvider.getCredential(email, password)
        linkAccountWithCredential(credential)
    } catch (e: Exception) {
        Timber.e(e, "关联邮箱失败 - email: %s", email)
        ModernResult.error(
            e.toModernDataError(
                operationName = "AccountLinkingService.linkEmailAccount",
                uiMessage = UiText.DynamicString("关联邮箱失败"),
            ),
        )
    }

    /**
     * 更新用户手机号
     * @param credential 手机认证凭证
     * @return 操作成功的ModernResult.Success，或包含错误的ModernResult.Error
     */
    suspend fun updatePhoneNumber(credential: PhoneAuthCredential): ModernResult<Unit> {
        return try {
            val user = auth.currentUser ?: return ModernResult.error(
                AuthErrors.AuthError.unknown(
                    operationName = "AccountLinkingService.updatePhoneNumber.notLoggedIn",
                    message = UiText.DynamicString("No user is currently signed in"),
                    metadataMap = mapOf("operation" to "update_phone"),
                ),
            )

            user.updatePhoneNumber(credential).await()
            ModernResult.success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "更新手机号失败")
            ModernResult.error(
                e.toModernDataError(
                    operationName = "AccountLinkingService.updatePhoneNumber",
                    uiMessage = UiText.DynamicString("更新手机号失败"),
                ),
            )
        }
    }

    /**
     * 更新用户邮箱
     * 使用verifyBeforeUpdateEmail替代已废弃的updateEmail方法
     * @param email 新邮箱
     * @return 包含操作结果的ModernResult
     */
    suspend fun updateEmail(email: String): ModernResult<Unit> {
        return try {
            val user = auth.currentUser ?: return ModernResult.error(
                AuthErrors.AuthError.unknown(
                    operationName = "AccountLinkingService.updateEmail.notLoggedIn",
                    message = UiText.DynamicString("No user is currently signed in"),
                    metadataMap = mapOf("operation" to "update_email", "email" to email),
                ),
            )

            // 使用verifyBeforeUpdateEmail替代已废弃的updateEmail方法
            // 这会发送验证邮件到新邮箱，用户确认后邮箱才会更新
            user.verifyBeforeUpdateEmail(email).await()
            ModernResult.success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "更新邮箱失败 - email: %s", email)
            ModernResult.error(
                e.toModernDataError(
                    operationName = "AccountLinkingService.updateEmail",
                    uiMessage = UiText.DynamicString("更新邮箱失败"),
                ),
            )
        }
    }

    /**
     * 检查当前用户是否已关联了指定提供商的账户
     * @param providerId 提供商的ID (例如, `google.com`, `phone`, `password`)
     * @return 如果已关联则返回 `true`，否则返回 `false`
     */
    fun isAccountLinked(providerId: String): Boolean {
        val user = auth.currentUser ?: return false
        return user.providerData.any { it.providerId == providerId }
    }

    /**
     * 获取当前用户关联的所有账户提供商ID列表
     * @return 提供商ID列表，如果用户未登录则为空列表
     */
    fun getLinkedAccounts(): List<String> {
        val user = auth.currentUser ?: return emptyList()
        return user.providerData.map { it.providerId }
    }

    /**
     * 将匿名用户升级为使用凭据的用户
     * @param credential 身份验证凭据
     * @return 操作结果
     */
    suspend fun upgradeAnonymousWithCredential(credential: AuthCredential): ModernResult<Unit> {
        return try {
            val user = auth.currentUser
            if (user == null) {
                return ModernResult.error(
                    AuthErrors.AuthError.unknown(
                        operationName = "AccountLinkingService.upgradeAnonymousWithCredential.notLoggedIn",
                        message = UiText.DynamicString("用户未登录"),
                        metadataMap = mapOf("operation" to "upgrade_anonymous"),
                    ),
                )
            }
            if (!user.isAnonymous) {
                return ModernResult.error(
                    AuthErrors.AuthError.unknown(
                        operationName = "AccountLinkingService.upgradeAnonymousWithCredential.notAnonymous",
                        message = UiText.DynamicString("当前用户不是匿名用户"),
                        metadataMap = mapOf("operation" to "upgrade_anonymous"),
                    ),
                )
            }

            user.linkWithCredential(credential).await()
            ModernResult.success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "匿名用户升级失败")
            ModernResult.error(
                e.toModernDataError(
                    operationName = "AccountLinkingService.upgradeAnonymousWithCredential",
                    uiMessage = UiText.DynamicString("匿名用户升级失败"),
                ),
            )
        }
    }

    /**
     * 将匿名用户升级为邮箱密码用户
     * @param email 邮箱
     * @param password 密码
     * @return 操作结果
     */
    suspend fun upgradeAnonymousWithEmail(email: String, password: String): ModernResult<Unit> {
        val credential = EmailAuthProvider.getCredential(email, password)
        return upgradeAnonymousWithCredential(credential)
    }

    /**
     * 将匿名用户升级为Google用户
     * @param credential Google认证凭据
     * @return 操作结果
     */
    suspend fun upgradeAnonymousWithGoogle(credential: AuthCredential): ModernResult<Unit> {
        return upgradeAnonymousWithCredential(credential)
    }

    /**
     * 将匿名用户升级为手机认证用户
     * @param credential 手机认证凭据
     * @return 操作结果
     */
    suspend fun upgradeAnonymousWithPhone(credential: PhoneAuthCredential): ModernResult<Unit> {
        return upgradeAnonymousWithCredential(credential)
    }

    /**
     * 更新密码
     * @param newPassword 新密码
     * @return 操作结果
     */
    suspend fun updatePassword(newPassword: String): ModernResult<Unit> {
        return try {
            val user = auth.currentUser
            if (user == null) {
                return ModernResult.error(
                    AuthErrors.AuthError.unknown(
                        operationName = "AccountLinkingService.updatePassword.notLoggedIn",
                        message = UiText.DynamicString("用户未登录"),
                        metadataMap = mapOf("operation" to "update_password"),
                    ),
                )
            }
            user.updatePassword(newPassword).await()
            ModernResult.success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "更新密码失败")
            ModernResult.error(
                e.toModernDataError(
                    operationName = "AccountLinkingService.updatePassword",
                    uiMessage = UiText.DynamicString("更新密码失败"),
                ),
            )
        }
    }

    /**
     * 删除账户
     * @return 操作结果
     */
    suspend fun deleteAccount(): ModernResult<Unit> {
        return try {
            val user = auth.currentUser
            if (user == null) {
                return ModernResult.error(
                    AuthErrors.AuthError.unknown(
                        operationName = "AccountLinkingService.deleteAccount.notLoggedIn",
                        message = UiText.DynamicString("用户未登录"),
                        metadataMap = mapOf("operation" to "delete_account"),
                    ),
                )
            }
            user.delete().await()
            ModernResult.success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "删除账户失败")
            ModernResult.error(
                e.toModernDataError(
                    operationName = "AccountLinkingService.deleteAccount",
                    uiMessage = UiText.DynamicString("删除账户失败"),
                ),
            )
        }
    }
}
