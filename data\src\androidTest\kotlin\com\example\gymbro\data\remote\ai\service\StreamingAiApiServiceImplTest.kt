package com.example.gymbro.data.remote.ai.service

import androidx.room.Room
import androidx.test.core.app.ApplicationProvider
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import kotlinx.coroutines.test.runTest
import kotlinx.datetime.Instant
import kotlinx.serialization.json.Json
import okhttp3.mockwebserver.MockResponse
import okhttp3.mockwebserver.MockWebServer
import org.junit.After
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals

/**
 * Generated by genTest for GymBro Data Module
 * ✔️ 使用 Room in-memory 数据库测试
 * ✔️ 使用 MockWebServer 模拟网络请求
 * ✔️ 使用 ModernResult<T> 统一错误处理
 * ✔️ 使用 kotlinx.serialization 而非 Moshi
 * ✔️ 遵循 Repository 模式和 Clean Architecture
 */
class StreamingAiApiServiceImplTest {

    private lateinit var database: GymBroDatabase
    private val mockServer = MockWebServer()
    private val json = Json { ignoreUnknownKeys = true }

    @Before
    fun setUp() {
        database = Room.inMemoryDatabaseBuilder(
            ApplicationProvider.getApplicationContext(),
            GymBroDatabase::class.java,
        ).allowMainThreadQueries().build()
        mockServer.start()
    }

    @After
    fun tearDown() {
        database.close()
        mockServer.shutdown()
    }

    @Test
    fun `StreamingAiApiServiceImpl loads data from remote and caches locally`() = runTest {
        // Arrange
        val mockResponse = MockResponse()
            .setBody("""{"id": "test", "name": "Test Data"}""")
            .setResponseCode(200)
        mockServer.enqueue(mockResponse)

        val repository = StreamingAiApiServiceImpl(
            dao = database.dao(),
            api = createMockApi(mockServer.url("/")),
            json = json,
        )

        // Act
        val result = repository.loadData("testId")

        // Assert
        assertEquals(ModernResult.Success(expectedData), result)
        assertEquals(expectedData, database.dao().getData("testId"))
    }

    @Test
    fun `StreamingAiApiServiceImpl handles network errors correctly`() = runTest {
        // Arrange
        mockServer.enqueue(MockResponse().setResponseCode(500))
        val repository = StreamingAiApiServiceImpl(
            dao = database.dao(),
            api = createMockApi(mockServer.url("/")),
            json = json,
        )

        // Act
        val result = repository.loadData("testId")

        // Assert
        val errorResult = result as ModernResult.Error
        assertEquals(NetworkErrorType, errorResult.error.errorType)
    }

    @Test
    fun `StreamingAiApiServiceImpl processes UiText in database operations`() = runTest {
        val testText = UiText.DynamicString("Test message")
        val repository = StreamingAiApiServiceImpl(
            dao = database.dao(),
            api = createMockApi(mockServer.url("/")),
            json = json,
        )

        val result = repository.saveText(testText)
        assertEquals(ModernResult.Success(Unit), result)
    }

    @Test
    fun `StreamingAiApiServiceImpl handles time with kotlinx.datetime`() = runTest {
        val testTime = Instant.parse("2025-01-30T10:00:00Z")
        val repository = StreamingAiApiServiceImpl(
            dao = database.dao(),
            api = createMockApi(mockServer.url("/")),
            json = json,
        )

        val result = repository.saveTime(testTime)
        assertEquals(ModernResult.Success(Unit), result)
    }

    private fun createMockApi(baseUrl: okhttp3.HttpUrl): ApiService {
        // TODO: 实现 API 服务 mock
        return MockApiService()
    }
}
