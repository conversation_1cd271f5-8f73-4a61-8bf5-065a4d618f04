package com.example.gymbro.core.ai.prompt.builder

import com.example.gymbro.core.ai.prompt.model.*

/**
 * 上下文管理器 - Stage 3: 智能化上下文选择
 *
 * 职责：
 * 1. 从用户档案中提取关键信息
 * 2. 基于相似度算法选择最相关的训练模板
 * 3. 智能截断历史对话，保留重要上下文
 * 4. 根据token预算分配各部分内容
 */
class ContextManager(
    private val config: PromptConfigData,
) {

    companion object {
        // Token分配比例 (基于promt.md的Token Budget Allocation)
        private const val PROFILE_RATIO = 0.15f // Profile信息: 15%
        private const val TEMPLATES_RATIO = 0.50f // 训练模板: 50%
        private const val HISTORY_RATIO = 0.25f // 历史对话: 25%
        private const val RECENT_RATIO = 0.10f // 最近聊天: 10%
    }

    /**
     * 选择相关上下文 - 核心算法
     *
     * @param context 原始上下文数据
     * @param tokenBudget 可用token预算
     * @return 优化后的上下文
     */
    fun selectRelevantContext(
        context: AiContextData,
        tokenBudget: Int,
    ): AiContextData {
        return when (config.truncationStrategy) {
            TruncationStrategyType.PRIORITY_BASED -> selectByPriority(context, tokenBudget)
            TruncationStrategyType.BALANCED -> selectBalanced(context, tokenBudget)
            TruncationStrategyType.RECENT_FIRST -> selectRecentFirst(context, tokenBudget)
        }
    }

    /**
     * 基于优先级的选择策略
     * 优先级：用户档案 > 相关模板 > 关键历史 > 最近对话
     */
    private fun selectByPriority(
        context: AiContextData,
        tokenBudget: Int,
    ): AiContextData {
        val profileTokens = (tokenBudget * PROFILE_RATIO).toInt()
        val templatesTokens = (tokenBudget * TEMPLATES_RATIO).toInt()
        val historyTokens = (tokenBudget * HISTORY_RATIO).toInt()
        val recentTokens = (tokenBudget * RECENT_RATIO).toInt()

        // 1. 选择最相关的K个训练模板
        val selectedTemplates = context.relevantTemplates
            .sortedByDescending { it.similarity }
            .take(config.contextTemplatesK)

        // 2. 智能截断历史对话
        val selectedHistory = context.recentHistory
            .takeLast(config.recentHistoryN)
            .filter { msg ->
                msg.sender == "user" || msg.content.contains("训练", ignoreCase = true)
            }
            .sortedByDescending { msg ->
                // 简单的关键词权重计算
                val keywords = listOf("训练", "健身", "动作", "计划")
                keywords.count { keyword -> msg.content.contains(keyword, ignoreCase = true) }
            }

        return object : AiContextData {
            override val userProfile = context.userProfile
            override val relevantTemplates = selectedTemplates
            override val recentHistory = selectedHistory
            override val relevantHistory = context.relevantHistory
            override val userGoals = context.userGoals
            override val userLimitations = context.userLimitations
        }
    }

    /**
     * 平衡选择策略
     * 在所有类型的上下文间保持平衡
     */
    private fun selectBalanced(
        context: AiContextData,
        tokenBudget: Int,
    ): AiContextData {
        // 简化实现：均匀分配
        val avgTemplates = config.contextTemplatesK / 2
        val avgHistory = config.recentHistoryN / 2

        return object : AiContextData {
            override val userProfile = context.userProfile
            override val relevantTemplates = context.relevantTemplates.take(avgTemplates)
            override val recentHistory = context.recentHistory.takeLast(avgHistory)
            override val relevantHistory = context.relevantHistory.take(avgHistory)
            override val userGoals = context.userGoals.take(3) // 限制目标数量
            override val userLimitations = context.userLimitations.take(5) // 限制限制条件数量
        }
    }

    /**
     * 最近优先策略
     * 优先保留最新的对话和模板
     */
    private fun selectRecentFirst(
        context: AiContextData,
        tokenBudget: Int,
    ): AiContextData {
        return object : AiContextData {
            override val userProfile = context.userProfile
            override val relevantTemplates = context.relevantTemplates.takeLast(config.contextTemplatesK)
            override val recentHistory = context.recentHistory.takeLast(config.recentHistoryN)
            override val relevantHistory = context.relevantHistory.takeLast(config.recentHistoryN)
            override val userGoals = context.userGoals
            override val userLimitations = context.userLimitations
        }
    }

    /**
     * 构建上下文摘要文本
     * 将结构化上下文转换为可读的文本格式
     */
    fun buildContextSummary(context: AiContextData): String {
        val sb = StringBuilder()

        // 用户档案摘要
        sb.appendLine("## 用户档案")
        with(context.userProfile) {
            sb.appendLine("性别: $gender, 年龄: ${age}岁, 身高: ${height}cm, 体重: ${weight}kg")
            sb.appendLine("健身经验: $experience")
            bodyFatPercentage?.let { bf -> sb.appendLine("体脂率: $bf%") }
        }

        // 训练模板摘要
        if (context.relevantTemplates.isNotEmpty()) {
            sb.appendLine("\n## 相关训练模板")
            context.relevantTemplates.forEach { template ->
                sb.appendLine("${template.name} (${template.difficultyLevel})")
                sb.appendLine("目标肌群: ${template.targetMuscles.joinToString()}")
                sb.appendLine(
                    "动作: ${template.exercises.joinToString { it.name + " " + it.sets + "组x" + it.reps }}",
                )
                sb.appendLine("描述: ${template.description}")
                sb.appendLine("相似度: ${"%.1f".format(template.similarity * 100)}%\n")
            }
        }

        return sb.toString()
    }
}
