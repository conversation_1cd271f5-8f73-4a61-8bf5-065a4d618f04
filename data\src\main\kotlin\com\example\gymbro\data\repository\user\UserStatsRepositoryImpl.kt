package com.example.gymbro.data.repository.user

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.local.dao.user.UserCacheEntityDao
import com.example.gymbro.domain.profile.model.user.UserStats
import com.example.gymbro.domain.profile.repository.user.UserStatsRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * UserStatsRepository接口的实现类
 *
 * 提供用户统计数据相关的数据访问功能，包括训练统计、进度跟踪等
 *
 * 注意：使用UserCacheEntityDao作为数据源，从UserCacheEntity中提取统计信息
 */
@Singleton
class UserStatsRepositoryImpl
@Inject
constructor(
    private val userCacheEntityDao: UserCacheEntityDao,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    private val logger: Logger,
) : UserStatsRepository {
    /**
     * 获取用户统计数据流
     *
     * @param userId 用户ID
     * @return 用户统计数据结果流
     */
    override fun getUserStats(userId: String): Flow<ModernResult<UserStats>> =
        userCacheEntityDao
            .observeUserCacheById(userId)
            .map { entity ->
                try {
                    val stats =
                        entity?.let { mapEntityToUserStats(it) }
                            ?: createDefaultUserStats(userId)
                    ModernResult.Success(stats)
                } catch (e: Exception) {
                    logger.e(e, "获取用户统计数据失败: $userId")
                    ModernResult.Error(
                        DataErrors.DataError.access(
                            operationName = "UserStatsRepo.getUserStats.catch",
                            message = UiText.DynamicString("获取用户统计失败"),
                            entityType = "UserStats",
                            cause = e,
                            metadataMap = mapOf("userId" to userId),
                        ),
                    )
                }
            }

    /**
     * 更新用户统计数据
     *
     * @param userId 用户ID
     * @param stats 用户统计数据
     * @return 操作结果
     */
    override suspend fun updateUserStats(
        userId: String,
        stats: UserStats,
    ): ModernResult<Unit> =
        withContext(ioDispatcher) {
            try {
                logger.d("更新用户统计数据: $userId")
                // 获取现有用户实体
                val existingEntity = userCacheEntityDao.getUserCacheById(userId)
                if (existingEntity != null) {
                    // 更新统计字段
                    val updatedEntity =
                        existingEntity.copy(
                            // TODO: 在重制workout模块后重新添加totalWorkoutCount
                            // totalWorkoutCount = stats.totalWorkouts,
                            weeklyActiveMinutes = stats.totalDuration.toInt(),
                            lastModified = System.currentTimeMillis(),
                            isSynced = false,
                        )
                    userCacheEntityDao.updateUserCache(updatedEntity)
                }
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, "更新用户统计数据失败: $userId")
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "UserStatsRepo.updateUserStats",
                        message = UiText.DynamicString("更新用户统计失败"),
                        entityType = "UserStats",
                        cause = e,
                        metadataMap = mapOf("userId" to userId),
                    ),
                )
            }
        }

    /**
     * 记录训练会话
     *
     * @param userId 用户ID
     * @param durationMinutes 训练时长（分钟）
     * @param caloriesBurned 消耗卡路里
     * @return 操作结果
     */
    override suspend fun recordWorkoutSession(
        userId: String,
        durationMinutes: Int,
        caloriesBurned: Int,
    ): ModernResult<Unit> =
        withContext(ioDispatcher) {
            try {
                logger.d("记录训练会话: userId=$userId, duration=$durationMinutes, calories=$caloriesBurned")

                // TODO: 在重制workout模块后重新添加训练计数更新
                // userCacheEntityDao.incrementWorkoutCount(userId)

                // 获取当前实体并更新其他统计信息
                val currentEntity = userCacheEntityDao.getUserCacheById(userId)
                if (currentEntity != null) {
                    val updatedEntity =
                        currentEntity.copy(
                            weeklyActiveMinutes = currentEntity.weeklyActiveMinutes + durationMinutes,
                            lastModified = System.currentTimeMillis(),
                            isSynced = false,
                        )
                    userCacheEntityDao.updateUserCache(updatedEntity)
                }
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, "记录训练会话失败: userId=$userId")
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "UserStatsRepo.recordWorkoutSession",
                        message = UiText.DynamicString("记录训练会话失败"),
                        entityType = "UserStats",
                        cause = e,
                        metadataMap =
                        mapOf(
                            "userId" to userId,
                            "durationMinutes" to durationMinutes.toString(),
                            "caloriesBurned" to caloriesBurned.toString(),
                        ),
                    ),
                )
            }
        }

    /**
     * 将UserCacheEntity映射为UserStats
     *
     * @param entity 用户缓存实体
     * @return 用户统计数据
     */
    private fun mapEntityToUserStats(
        entity: com.example.gymbro.data.local.entity.user.UserCacheEntity,
    ): UserStats =
        UserStats(
            userId = entity.userId,
            // TODO: 在重制workout模块后重新添加totalWorkouts
            // totalWorkouts = entity.totalWorkoutCount,
            totalDuration = entity.weeklyActiveMinutes.toLong(),
            // UserCacheEntity中没有卡路里字段，使用默认值
            // UserCacheEntity中没有连续天数字段，使用默认值
            // TODO: 在重制workout模块后重新添加lastWorkoutDate
            // lastWorkoutDate = entity.lastLoginAt, // 使用最后登录时间作为近似值
            personalBests = emptyMap(), // UserCacheEntity中没有个人最佳记录，使用默认值
            // 可以根据需要计算
            // 可以根据需要计算
            achievements = emptyList(), // UserCacheEntity中没有成就数据，使用默认值
        )

    /**
     * 创建默认用户统计数据
     *
     * @param userId 用户ID
     * @return 默认用户统计数据
     */
    private fun createDefaultUserStats(userId: String): UserStats =
        UserStats(
            userId = userId,
            personalBests = emptyMap(),
            achievements = emptyList(),
        )
}
