name: Branch Integration Pipeline (Modern v2.0)

on:
  push:
    branches: [ develop, master ]
    # 只在代码变更时触发，忽略文档
    paths-ignore:
      - '**.md'
      - 'docs/**'
      - '.gitignore'
      - '.editorconfig'
      # - 'gradle.properties' # 【修复点 1】 移除，不应忽略构建配置变化
  # 【修复点 7】 允许手动触发
  workflow_dispatch:

# 【修复点 5】 定义权限
permissions:
  contents: read
  statuses: write # for createCommitStatus
  actions: read # for downloading artifacts
  checks: write # for check runs

# 【修复点 6】 并发控制
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  # 现代化Gradle配置 + UTF-8强制支持
  GRADLE_OPTS: -Dorg.gradle.daemon=false -Dorg.gradle.workers.max=2 -Dfile.encoding=UTF-8
  JAVA_TOOL_OPTIONS: -Dfile.encoding=UTF-8
  LC_ALL: C.UTF-8
  JAVA_VERSION: '17'
  # 强制Build Scan发布
  GRADLE_SCAN: true
  ARTIFACT_APK_NAME: ${{ github.ref_name }}-apk-${{ github.sha }}
  ARTIFACT_REPORT_NAME: ${{ github.ref_name }}-test-reports-${{ github.sha }}


jobs:
  # 完整质量检查和测试
  full-quality-check:
    name: Full Quality & Test Suite
    runs-on: ubuntu-latest
    # Handle [skip ci] or [ci skip]
    if: "!contains(github.event.head_commit.message, '[skip ci]') && !contains(github.event.head_commit.message, '[ci skip]')"
    outputs:
      build-scan-url: ${{ steps.build-scan.outputs.url }}
      apk-size: ${{ steps.apk-info.outputs.size }}
       # apk-path: ${{ steps.apk-info.outputs.path }} # 【修复点 3】
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 设置JDK 17 (UTF-8优化)
        uses: actions/setup-java@v4
        with:
          java-version: ${{ env.JAVA_VERSION }}
          distribution: 'temurin'
        env:
          JAVA_OPTS: -Dfile.encoding=UTF-8

      - name: 现代化Gradle缓存 (gradle-build-action)
        uses: gradle/gradle-build-action@v3
        with:
          # cache-read-only: false # default
          gradle-home-cache-cleanup: true
          # arguments: --scan # 【修复点 2】 移除冗余参数

      - name: 赋予gradlew执行权限
        run: chmod +x gradlew

      - name: "创建local.properties (UTF-8)"
        run: echo "sdk.dir=$ANDROID_HOME" > local.properties

      - name: "Create google-services.json for CI (MVP: Always use mock)"
        run: |
          echo "MVP模式：使用mock Firebase配置，避免服务器依赖"
          cp app/google-services.json.mock app/google-services.json
        continue-on-error: true

      - name: 🚀 运行完整CI检查流程 (Lint, Ktlint, Detekt)
        # 假设 ciCheck 包含 lint, ktlint, detekt 且失败会中断
        run: ./gradlew ciCheck --scan --no-configuration-cache
        env:
          GRADLE_OPTS: ${{ env.GRADLE_OPTS }}

      # 单元测试/覆盖率/快照 允许失败，但会影响 job.result, 从而阻止 firebase-distribution
      - name: 🧪 运行全量单元测试 & 覆盖率
        # 假设 testDebugUnitTest 和 coverageCheck 有依赖关系, 可以合并
        run: ./gradlew testDebugUnitTest coverageCheck --scan --no-configuration-cache --continue
        env:
          GRADLE_OPTS: ${{ env.GRADLE_OPTS }}
        continue-on-error: true

      - name: 🎨 运行Paparazzi快照测试
        run: |
          echo "运行UI快照测试..."
          if ./gradlew tasks --all | grep -q "recordPaparazziDebug"; then
            ./gradlew recordPaparazziDebug --scan --no-configuration-cache
          else
            echo "::warning::⚠️ Paparazzi task 'recordPaparazziDebug' 未找到，跳过快照测试"
          fi
        env:
          GRADLE_OPTS: ${{ env.GRADLE_OPTS }}
        continue-on-error: true

      - name: 🏗️ 构建Debug APK
        # 只有 ciCheck 成功才会运行到这里
        run: ./gradlew assembleDebug --scan --no-configuration-cache
        env:
          GRADLE_OPTS: ${{ env.GRADLE_OPTS }}
          GYMBRO_API_KEY: ${{ secrets.GYMBRO_API_KEY }}
          FIREBASE_API_KEY: ${{ secrets.FIREBASE_API_KEY }}

      - name: 📊 收集Build Scan信息
        id: build-scan
        run: echo "url=https://gradle.com/s/placeholder-branch" >> $GITHUB_OUTPUT

      - name: 📦 获取APK信息
        id: apk-info
        # if: success() # Only run if build was successful
        run: |
          APK_PATH=$(find app/build/outputs/apk/debug -type f -name "*.apk" | head -1)
          if [ -f "$APK_PATH" ]; then
            # 【修复点 9】 使用 du -sh
            APK_SIZE=$(du -sh "$APK_PATH" | awk '{print $1}')
            # APK_FILENAME=$(basename "$APK_PATH") # 【修复点 3】
            echo "size=$APK_SIZE" >> $GITHUB_OUTPUT
            # echo "path=$APK_PATH" >> $GITHUB_OUTPUT # 【修复点 3】
            # echo "filename=$APK_FILENAME" >> $GITHUB_OUTPUT # 【修复点 3】
            echo "📦 APK构建成功: $APK_PATH ($APK_SIZE)"
          else
            echo "::error::❌ 未找到 APK 文件，构建可能失败"
            exit 1 # Fail the job if APK is missing
          fi

      - name: 📊 上传构建产物
        uses: actions/upload-artifact@v4
        # if: success() # Only upload if build and info steps succeeded
        with:
          name: ${{ env.ARTIFACT_APK_NAME }}
          path: app/build/outputs/apk/debug/*.apk
          retention-days: 7
          if-no-files-found: error # 【修复点 8】 如果APK不存在，应该失败

      - name: 📊 上传测试报告
        uses: actions/upload-artifact@v4
        if: always() # Always upload reports
        with:
          name: ${{ env.ARTIFACT_REPORT_NAME }}
          path: |
            **/build/test-results/
            **/build/reports/
          retention-days: 7
          if-no-files-found: warn # 【修复点 8】

  # Firebase App Distribution部署 (MVP: 可选，失败不影响主流程)
  firebase-distribution:
    name: "Firebase App Distribution (MVP Optional)"
    runs-on: ubuntu-latest
    needs: full-quality-check
    # MVP: 只有当 full-quality-check 完全成功时才运行，且允许失败
    if: needs.full-quality-check.result == 'success' && false  # MVP: 暂时禁用Firebase分发
    steps:
      - name: 下载APK构建产物
        uses: actions/download-artifact@v4
        with:
          name: ${{ env.ARTIFACT_APK_NAME }}
          path: ./artifacts

      # 【修复点 3】 动态查找下载的 APK
      - name: 🔍 查找 APK 文件
        id: find-apk
        run: |
           APK_FILE=$(find ./artifacts -type f -name "*.apk" | head -1)
           if [ -f "$APK_FILE" ]; then
              echo "path=$APK_FILE" >> $GITHUB_OUTPUT
               echo "✅ 找到待部署 APK: $APK_FILE"
           else
               echo "::error::❌ 在 artifacts 目录未找到 APK 文件！"
               exit 1
           fi

      - name: 🚀 部署到Firebase App Distribution
        # 只有配置了FIREBASE_APP_ID时才执行
        if: vars.FIREBASE_APP_ID != ''
        uses: wzieba/Firebase-Distribution-Github-Action@v1.7.0 # Use a specific version
        with:
          appId: ${{ vars.FIREBASE_APP_ID }}
          serviceCredentialsFileContent: ${{ secrets.FIREBASE_SERVICE_ACCOUNT }}
          groups: qa-team,internal-testers
           # 【修复点 3】 使用动态查找的路径
          file: ${{ steps.find-apk.outputs.path }}
          releaseNotes: |
            🚀 GymBro ${{ github.ref_name }} Build

            📝 提交: ${{ github.event.head_commit.message }}
            👤 作者: ${{ github.event.head_commit.author.name }}
            🔗 SHA: [${{ github.sha }}](https://github.com/${{ github.repository }}/commit/${{ github.sha }})
            📅 时间: $(date)

            📊 大小: ${{ needs.full-quality-check.outputs.apk-size }}
            🔗 Build Scan: ${{ needs.full-quality-check.outputs.build-scan-url }}
            ✅ Workflow: [Run ${{ github.run_id }}](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})

            ⚠️ 这是开发版本，仅供内部测试使用。

      - name: ⚠️ Firebase App Distribution跳过通知
        if: vars.FIREBASE_APP_ID == ''
        run: |
          echo "## ⚠️ Firebase App Distribution 已跳过" >> $GITHUB_STEP_SUMMARY
          echo "需要在GitHub环境变量中配置 FIREBASE_APP_ID 才能启用自动分发" >> $GITHUB_STEP_SUMMARY
          echo "APK已构建完成，可手动下载进行测试" >> $GITHUB_STEP_SUMMARY

      - name: 📱 Firebase分发成功通知
        # 只有Firebase分发成功时才显示
        if: vars.FIREBASE_APP_ID != ''
        run: |
          echo "## 🚀 Firebase App Distribution 部署成功" >> $GITHUB_STEP_SUMMARY
          echo "- **版本**: ${{ github.ref_name }} Build" >> $GITHUB_STEP_SUMMARY
          echo "- **APK大小**: ${{ needs.full-quality-check.outputs.apk-size }}" >> $GITHUB_STEP_SUMMARY
          echo "- **分发组**: qa-team, internal-testers" >> $GITHUB_STEP_SUMMARY
          echo "- [Firebase Console](https://console.firebase.google.com/project/${{ secrets.FIREBASE_PROJECT_ID }}/appdistribution)" >> $GITHUB_STEP_SUMMARY


  # 代码质量报告汇总
  quality-summary:
    name: Quality Summary Report
    runs-on: ubuntu-latest
    needs: [full-quality-check, firebase-distribution]
    if: always() # Always run summary
    steps:
      - name: 📋 生成质量报告摘要
        run: |
          # Helper function
          get_status_string() {
            case "$1" in
               success) echo "✅ 通过/成功";; skipped) echo "⏭️ 跳过";; failure) echo "❌ 失败";; cancelled) echo "🚫 取消";; *) echo "⚠️ 未知";;
            esac
          }
          CHECK_RESULT="${{ needs.full-quality-check.result }}"
          DIST_RESULT="${{ needs.firebase-distribution.result }}"

          echo "# 🏗️ ${{ github.ref_name }} 分支集成报告" >> $GITHUB_STEP_SUMMARY
          echo "## 📊 构建结果" >> $GITHUB_STEP_SUMMARY
          echo "- **完整质量检查**: $(get_status_string $CHECK_RESULT)" >> $GITHUB_STEP_SUMMARY
          echo "- **Firebase分发**: $(get_status_string $DIST_RESULT)" >> $GITHUB_STEP_SUMMARY
          echo "## 📱 构建信息" >> $GITHUB_STEP_SUMMARY
          # Only show APK size if check job succeeded
          if [[ "$CHECK_RESULT" == "success" ]]; then
             echo "- **APK大小**: ${{ needs.full-quality-check.outputs.apk-size }}" >> $GITHUB_STEP_SUMMARY
          fi
          echo "- **分支**: ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
          echo "- **提交**: [${{ github.sha }}](https://github.com/${{ github.repository }}/commit/${{ github.sha }})" >> $GITHUB_STEP_SUMMARY
          echo "- **提交信息**: ${{ github.event.head_commit.message }}" >> $GITHUB_STEP_SUMMARY
          echo "- **作者**: ${{ github.event.head_commit.author.name }}" >> $GITHUB_STEP_SUMMARY
          echo "## 🔗 相关链接" >> $GITHUB_STEP_SUMMARY
          echo "- [Action 详情](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})" >> $GITHUB_STEP_SUMMARY
          if [[ "$CHECK_RESULT" == "success" ]]; then
              echo "- [Build Scan分析](${{ needs.full-quality-check.outputs.build-scan-url }})" >> $GITHUB_STEP_SUMMARY
          fi
          if [[ "$DIST_RESULT" == "success" ]]; then
              echo "- [Firebase App Distribution](https://console.firebase.google.com/project/${{ secrets.FIREBASE_PROJECT_ID }}/appdistribution)" >> $GITHUB_STEP_SUMMARY
          fi
          echo "" >> $GITHUB_STEP_SUMMARY

          # 最终状态判断
          if [[ "$CHECK_RESULT" == "skipped" ]]; then
             echo "## ⏭️ 状态：CI 已跳过 ([skip ci])" >> $GITHUB_STEP_SUMMARY
          elif [[ "$CHECK_RESULT" == "success" && "$DIST_RESULT" == "success" ]]; then
             echo "## ✅ 状态：${{ github.ref_name }} 分支集成成功，APK已分发！" >> $GITHUB_STEP_SUMMARY
          elif [[ "$CHECK_RESULT" == "success" && "$DIST_RESULT" == "skipped" ]]; then
             echo "## ✅ 状态：${{ github.ref_name }} 分支集成成功，APK已构建！" >> $GITHUB_STEP_SUMMARY
             echo "💡 提示：配置 FIREBASE_APP_ID 环境变量可启用自动分发" >> $GITHUB_STEP_SUMMARY
          elif [[ "$CHECK_RESULT" == "success" && "$DIST_RESULT" == "failure" ]]; then
             echo "## ⚠️ 状态：构建成功，但 Firebase 分发失败！" >> $GITHUB_STEP_SUMMARY
          else # CHECK_RESULT is failure or cancelled
             echo "## ❌ 状态：质量检查或构建失败，未进行分发。" >> $GITHUB_STEP_SUMMARY
          fi

      - name: ✅ 更新提交状态
        uses: actions/github-script@v7
        # permissions: statuses: write # Defined at top level
        with:
          script: |
            const { owner, repo } = context.repo;
            const sha = context.sha;
            const checkResult = '${{ needs.full-quality-check.result }}';
            const deployResult = '${{ needs.firebase-distribution.result }}';

            let state = 'failure';
            let description = '集成或部署失败，请检查日志';
            const branchName = context.ref.replace('refs/heads/', '');

            // 【修复点 4】 处理 skipped 状态
            if (checkResult === 'skipped') {
               state = 'success'; // Treat skip as success (green check)
               description = `CI on ${branchName} skipped ([skip ci])`;
            } else if (checkResult === 'success' && deployResult === 'success') {
               state = 'success';
               description = `${branchName} 分支集成完成，APK已分发`;
            } else if (checkResult === 'success' && deployResult === 'failure') {
                state = 'failure';
                description = `${branchName} 分支构建成功，但部署失败`;
            } else { // checkResult === 'failure' or 'cancelled'
                 state = 'failure';
                 description = `${branchName} 分支质量检查或构建失败`;
            }

            console.log(`Setting status for ${sha} to ${state}: ${description}`);

            try {
              await github.rest.repos.createCommitStatus({
                owner,
                repo,
                sha,
                state,
                description,
                context: 'ci/branch-integration',
                target_url: `https://github.com/${owner}/${repo}/actions/runs/${{ github.run_id }}`
              });
             } catch (error) {
                console.error("Failed to set commit status:", error);
                // core.setFailed(...) // Optionally fail step
             }
