plugins {
    id("gymbro.android.library")
    kotlin("plugin.serialization")
    id("com.google.devtools.ksp")
    // 🔧 Step 7: CI Gate - Detekt
    id("io.gitlab.arturbosch.detekt")
    // 注意：jacoco可能已经在gymbro.android.library中包含，避免重复
}

android {
    namespace = "com.example.gymbro.core.network"
    compileSdk = libs.versions.compileSdk.get().toInt()

    defaultConfig {
        minSdk = libs.versions.minSdk.get().toInt()

        // 🔥 移除硬编码配置 - 使用统一的AiProviderManager配置管理
        // 保留通用网络配置参数
        buildConfigField("long", "PING_INTERVAL_SEC", "15L")
        buildConfigField("int", "MAX_RECONNECT", "5")
        buildConfigField("boolean", "ENABLE_DEBUG_LOGGING", "true")
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = libs.versions.jvmTarget.get()
    }

    buildFeatures {
        buildConfig = true
    }
}

dependencies {
    // ★ 添加shared-models依赖 - Stage B核心变更
    api(project(":shared-models"))

    // ★ 核心网络栈依赖
    api(libs.okhttp)
    api(libs.okhttp.logging.interceptor)
    implementation("com.squareup.okhttp3:okhttp-sse:4.12.0")
    api(libs.retrofit)
    api(libs.kotlinx.serialization.json)

    // Kotlin 协程 - 网络异步处理必需
    api(libs.kotlinx.coroutines.core)
    api(libs.kotlinx.coroutines.android)

    // 🔧 Step 5: DI整合 - 添加必要的Hilt依赖
    implementation(libs.hilt.android)
    ksp(libs.hilt.compiler)

    // 日志 - 网络调试必需
    implementation(libs.timber)

    // Core模块依赖 - 基础工具类
    implementation(project(":core"))

    // 测试依赖
    testImplementation(libs.junit4)
    testImplementation(libs.kotlinx.coroutines.test)
    testImplementation(libs.mockk)
    testImplementation(libs.truth)
    // 添加kotlin-test用于断言方法
    testImplementation(libs.kotlin.test)
    // 添加MockWebServer用于网络测试
    testImplementation("com.squareup.okhttp3:mockwebserver:4.12.0")

    androidTestImplementation(libs.androidx.test.ext)
    androidTestImplementation(libs.androidx.test.espresso.core)
}

// 🔧 Step 7: CI Gate - Detekt配置
detekt {
    config.setFrom("$projectDir/detekt-rules.yml")
    buildUponDefaultConfig = true
    allRules = false

    reports {
        html.required.set(true)
        xml.required.set(true)
        txt.required.set(false)
        sarif.required.set(false)
        md.required.set(false)
    }
}

// 🔧 Step 7: CI Gate - Jacoco配置 (如果buildlogic中没有配置)
// 注意：如果gymbro.android.library已经包含jacoco配置，则无需重复配置

// 🔧 Step 6: 技术债→测试 - 测试覆盖率验证
// 注意：具体的jacoco配置和覆盖率验证任务可能已经在buildlogic中配置
// 这里只添加模块特定的测试任务

tasks.register("networkModuleTest") {
    dependsOn("testDebugUnitTest")

    doLast {
        println("✅ Core Network模块测试完成")
        println("📊 请查看测试报告以验证覆盖率是否达到80%标准")
    }
}
