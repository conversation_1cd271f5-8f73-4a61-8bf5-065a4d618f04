package com.example.gymbro.designSystem.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.material3.Text
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.compose.ui.test.onNodeWithText
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.gymbro.designSystem.theme.GymBroTheme
import org.junit.Assert.*
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * GymBroScaffold组件测试
 *
 * 测试覆盖：
 * - 基本显示功能
 * - 内容显示
 * - TopBar集成
 * - PaddingValues传递
 */
@RunWith(AndroidJUnit4::class)
class GymBroScaffoldTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    @Test
    fun `GymBroScaffold应该显示内容`() {
        val contentText = "测试内容"

        composeTestRule.setContent {
            GymBroTheme {
                GymBroScaffold(
                    content = { paddingValues ->
                        Column {
                            Text(text = contentText)
                        }
                    },
                )
            }
        }

        composeTestRule
            .onNodeWithText(contentText)
            .assertIsDisplayed()
    }

    @Test
    fun `GymBroScaffold应该支持TopBar`() {
        val topBarText = "顶部标题"
        val contentText = "页面内容"

        composeTestRule.setContent {
            GymBroTheme {
                GymBroScaffold(
                    topBar = {
                        Text(text = topBarText)
                    },
                    content = { paddingValues ->
                        Column {
                            Text(text = contentText)
                        }
                    },
                )
            }
        }

        composeTestRule
            .onNodeWithText(topBarText)
            .assertIsDisplayed()

        composeTestRule
            .onNodeWithText(contentText)
            .assertIsDisplayed()
    }

    @Test
    fun `GymBroScaffold应该传递PaddingValues`() {
        var capturedPaddingValues: PaddingValues? = null

        composeTestRule.setContent {
            GymBroTheme {
                GymBroScaffold(
                    content = { paddingValues ->
                        capturedPaddingValues = paddingValues
                        Text(text = "测试内容")
                    },
                )
            }
        }

        assertNotNull("PaddingValues应该被传递", capturedPaddingValues)
    }

    @Test
    fun `GymBroScaffold应该支持自定义TopBar和内容组合`() {
        val topBarText = "自定义标题"
        val contentText = "自定义内容"

        composeTestRule.setContent {
            GymBroTheme {
                GymBroScaffold(
                    topBar = {
                        Text(text = topBarText)
                    },
                    content = { paddingValues ->
                        Column {
                            Text(text = contentText)
                        }
                    },
                )
            }
        }

        composeTestRule
            .onNodeWithText(topBarText)
            .assertIsDisplayed()

        composeTestRule
            .onNodeWithText(contentText)
            .assertIsDisplayed()
    }
}
