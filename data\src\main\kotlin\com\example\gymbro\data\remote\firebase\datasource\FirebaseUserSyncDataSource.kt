package com.example.gymbro.data.remote.firebase.datasource

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import kotlinx.coroutines.CoroutineDispatcher
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Firebase用户同步数据源
 * 专注于用户数据的同步操作
 */
@Singleton
class FirebaseUserSyncDataSource
@Inject
constructor(
    private val firestoreDataSource: FirestoreDataSource,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
) {
    companion object {
        private const val USERS_COLLECTION = "users"
        private const val ANONYMOUS_USERS_COLLECTION = "anonymous_users"
        private const val SYNC_BATCH_SIZE = 10

        private fun getUserCollectionPath(
            userId: String,
            isAnonymous: <PERSON>olean,
        ): String = if (isAnonymous) "$ANONYMOUS_USERS_COLLECTION/$userId" else "$USERS_COLLECTION/$userId"
    }
}
