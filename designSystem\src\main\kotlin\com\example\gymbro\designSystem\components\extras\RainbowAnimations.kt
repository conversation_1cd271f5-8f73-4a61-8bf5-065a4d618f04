package com.example.gymbro.designSystem.components.extras

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.os.Build
import android.view.Display
import android.view.View
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme

// --- Constants ---
private const val LOGO_ANIM_DURATION = 4000 // 减少到4秒，减少跳跃感
const val STREAMING_ANIM_DURATION = 3000 // 统一文本渲染器专用常量，导出使用
private const val LUMINANCE_ANIM_DURATION_BASE = 4000

// 性能优化配置 - 保持高级效果的同时添加基本限制
private const val MAX_FRAME_RATE = 60 // 60fps限制
private const val MIN_ANIMATION_DURATION = 1000 // 最小动画时长
private const val MAX_ANIMATION_DURATION = 8000 // 最大动画时长

// --- Data Classes & Enums ---

private data class HdrCapability(
    val isHdrSupported: Boolean = false,
    val isWideColorGamut: Boolean = false,
    val maxIntensity: Float = 1.0f,
)

private enum class ColorGamut {
    STANDARD,
    WIDE,
    HDR,
}

// --- HDR Capability Detection ---

/**
 * Safe find Activity from Context
 */
private fun Context.findActivity(): Activity? {
    var context = this
    while (context is ContextWrapper) {
        if (context is Activity) return context
        context = context.baseContext
    }
    return null
}

/**
 * Get Display safely across API levels.
 */
private fun getDisplay(
    context: Context,
    view: View?,
): Display? =
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
        view?.display ?: context.display
    } else {
        @Suppress("DEPRECATION")
        context.findActivity()?.windowManager?.defaultDisplay
    }

/**
 * Detect HDR and Wide Gamut capability.
 * Wrapped in Composable to use LocalContext/LocalView and remember the result.
 */
@Composable
private fun rememberHdrCapability(): HdrCapability {
    val context = LocalContext.current
    val configuration = LocalConfiguration.current
    // Use LocalView to get the actual display the view is attached to
    val view = LocalView.current

    // Remember based on context, view, and config changes
    return remember(context, view, configuration) {
        val display = getDisplay(context, view)
        val hdrCapabilities = display?.hdrCapabilities

        @Suppress("DEPRECATION")
        val isHdr = hdrCapabilities?.supportedHdrTypes?.isNotEmpty() == true
        // Check for wide color gamut (API 26+)
        val isWideGamut =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                configuration.isScreenWideColorGamut
            } else {
                false
            }

        HdrCapability(
            isHdrSupported = isHdr,
            isWideColorGamut = isWideGamut,
            // Adjust max intensity based on capability
            maxIntensity =
            when {
                isHdr -> 2.0f
                isWideGamut -> 1.5f
                else -> 1.2f // Default subtle boost
            },
        )
    }
}

// --- Color Objects ---

object AppGradients {
    // Grayscale
    val grayScale =
        listOf(
            Color(0xFF212121),
            Color(0xFF424242),
            Color(0xFF616161),
            Color(0xFF757575),
            Color(0xFF9E9E9E),
            Color(0xFFBDBDBD),
            Color(0xFFE0E0E0),
            Color(0xFFF5F5F5),
            Color.White,
        )

    // Legacy Rainbow
    val rainbow =
        listOf(
            Color(0xFFFF1744),
            Color(0xFFFF9100),
            Color(0xFFFFEA00),
            Color(0xFF00E676),
            Color(0xFF2979FF),
            Color(0xFFD500F9),
        )

    // Metallic
    private val darkSilver = Color(0xFF7A7A7A)
    private val midSilver = Color(0xFFB4B4B4)
    private val brightSilver = Color(0xFFFBFBFB)

    // Use Color.White, values > 1.0f might be clamped without explicit ColorSpace
    private val peakWhite = Color.White

    val metallicSequence =
        listOf(
            darkSilver,
            midSilver,
            brightSilver,
            peakWhite,
            brightSilver,
            midSilver,
            darkSilver,
        )

    // HDR version (just brighter highlights for simplicity)
    val hdrMetallicSequence =
        listOf(
            Color(0xFF6A6A6A),
            Color(0xFFC4C4C4),
            Color.White,
            Color.White,
            Color.White,
            Color(0xFFC4C4C4),
            Color(0xFF6A6A6A),
        )

    // Non-linear stops for sharp highlight
    val metallicStops = floatArrayOf(0.0f, 0.4f, 0.48f, 0.5f, 0.52f, 0.6f, 1.0f)
}

// Keep alias for backward compatibility if needed, or refactor usages
val RainbowColors = AppGradients
val ModernRainbowColors = AppGradients
val MetallicColors = AppGradients

// --- Brush Creators ---

/**
 * Creates list of colors with adjusted luminance.
 *
 * 修复: 确保强度增强在所有设备上都能应用，不仅限于HDR/Wide Gamut设备
 */
private fun createLuminanceColors(
    baseColors: List<Color>,
    hdrCapability: HdrCapability,
    intensity: Float,
): List<Color> {
    // 【修复】: 只有在强度为1.0且没有HDR支持时才跳过处理
    if (intensity <= 1.0f && !hdrCapability.isHdrSupported && !hdrCapability.isWideColorGamut) {
        return baseColors
    }

    // 【修复】: 强度增强应该在所有情况下应用，不仅限于HDR设备
    // 对于非HDR设备，我们仍然可以在标准色域内进行亮度调整
    val shouldApplyIntensity = intensity > 1.0f

    return baseColors.map { color ->
        if (shouldApplyIntensity) {
            // 【修复】: 在标准显示器上也应用强度增强
            val factor =
                if (hdrCapability.isHdrSupported || hdrCapability.isWideColorGamut) {
                    // HDR/Wide Gamut设备：使用完整强度
                    intensity.coerceAtLeast(1.0f)
                } else {
                    // 标准显示器：限制强度以避免过度饱和，但仍然应用增强
                    intensity.coerceIn(1.0f, 1.5f)
                }

            Color(
                red = (color.red * factor).coerceIn(0f, 1f),
                green = (color.green * factor).coerceIn(0f, 1f),
                blue = (color.blue * factor).coerceIn(0f, 1f),
                alpha = color.alpha,
            )
        } else {
            color
        }
    }
}

/**
 * 优化: 亮度/灰阶动画Brush组件 (原 rememberRainbowBrush)
 * @param colors Base color list (default grayscale)
 */
@Composable // 优化点: 重命名, 使用 Dp 和 LocalDensity
fun rememberLuminanceBrush(
    useAnimate: Boolean = true,
    hdrIntensity: Float = 1.0f,
    speed: Float = 1.0f,
    colors: List<Color> = AppGradients.grayScale,
): Brush {
    val hdrCapability = rememberHdrCapability() // Use composable wrapper
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current // Get density

    val safeHdrIntensity = hdrIntensity.coerceIn(1.0f, hdrCapability.maxIntensity)
    val safeSpeed = speed.coerceIn(0.5f, 3.0f)

    // 优化点: Key 包含 colors 和 safeHdrIntensity
    val colorArray =
        remember(colors, hdrCapability, safeHdrIntensity) {
            createLuminanceColors(colors, hdrCapability, safeHdrIntensity)
        }
    // 优化点: 动画时长计算 - 使用性能配置常量
    val duration =
        remember(safeSpeed) {
            (LUMINANCE_ANIM_DURATION_BASE / safeSpeed).toInt()
                .coerceIn(MIN_ANIMATION_DURATION, MAX_ANIMATION_DURATION)
        }
    // 优化点: 动画 Easing
    val easing =
        remember(safeSpeed) {
            if (safeSpeed > 1.5f) FastOutSlowInEasing else LinearEasing
        }

    val shift by if (useAnimate) {
        rememberInfiniteTransition(label = "stable_luminance_transition")
            .animateFloat(
                initialValue = 0f,
                targetValue = 1f,
                animationSpec =
                infiniteRepeatable(
                    tween(durationMillis = duration, easing = easing),
                ),
                label = "safe_gradient_shift",
            )
    } else {
        remember { mutableStateOf(0f) } // Static state
    }

    // 优化点: 计算像素宽度
    val canvasWidthPx =
        remember(configuration, density) {
            with(density) { configuration.screenWidthDp.dp.toPx() }
        }

    return remember(useAnimate, colorArray, shift, canvasWidthPx) {
        if (useAnimate) {
            // Calculate start/end in Pixels
            val safeStartX = -canvasWidthPx + (shift * canvasWidthPx * 2)
            val safeEndX = safeStartX + canvasWidthPx
            Brush.horizontalGradient(
                colors = colorArray,
                startX = safeStartX,
                endX = safeEndX,
                // TileMode.Repeated could also be an option for continuous feel
            )
        } else {
            Brush.horizontalGradient(colorArray)
        }
    }
}

/**
* 优化: 创建金属质感Brush (合并了 LOGO 和 Streaming 版本)
* @param rotationDurationMillis 动画周期时长
*/
@Composable // 优化点: 合并函数, 使用 Dp 和 LocalDensity
fun rememberMetallicBrush(
    useAnimate: Boolean = true,
    rotationDurationMillis: Int = LOGO_ANIM_DURATION,
    useHdr: Boolean = false,
): Brush {
    val hdrCapability = rememberHdrCapability()
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current

    val metallicColors =
        remember(useHdr, hdrCapability) {
            if (useHdr && hdrCapability.isHdrSupported) {
                AppGradients.hdrMetallicSequence
            } else {
                AppGradients.metallicSequence
            }
        }
    // Ensure stops match colors count
    val colorStops =
        remember(metallicColors) {
            if (metallicColors.size == AppGradients.metallicStops.size) AppGradients.metallicStops else null
        }
    val colorsWithStops =
        remember(colorStops, metallicColors) {
            colorStops?.zip(metallicColors)?.toTypedArray()
        }

    val shift by if (useAnimate) {
        rememberInfiniteTransition(label = "metallic_transition")
            .animateFloat(
                initialValue = 0f,
                targetValue = 1f,
                animationSpec =
                infiniteRepeatable(
                    animation = tween(durationMillis = rotationDurationMillis, easing = LinearEasing),
                    repeatMode = RepeatMode.Reverse, // 使用Reverse避免跳跃
                ),
                label = "metallic_shift",
            )
    } else {
        remember { mutableStateOf(0.5f) } // Static state, center highlight
    }

    // 优化点: 计算像素宽度
    val screenWidthPx =
        remember(configuration, density) {
            with(density) { configuration.screenWidthDp.dp.toPx() }
        }

    return remember(useAnimate, shift, screenWidthPx, colorsWithStops, metallicColors) {
        // Calculate gradient position in Pixels
        val gradientWidth = screenWidthPx * 1.5f // Define gradient width relative to screen
        // Scroll the gradient fully across: start completely off-left, end completely off-right
        val totalDistance = screenWidthPx + gradientWidth
        val startX = (shift * totalDistance) - gradientWidth
        val endX = startX + gradientWidth

        if (colorsWithStops != null) {
            Brush.horizontalGradient(
                colorStops = *colorsWithStops,
                startX = startX,
                endX = endX,
            )
        } else {
            // Fallback if stops don't match colors
            Brush.horizontalGradient(
                colors = metallicColors,
                startX = startX,
                endX = endX,
            )
        }
    }
}

/**
 * 流式文本专用金属质感Brush
 *
 * 这是 rememberMetallicBrush 的专门变体，针对流式文本优化：
 * - 使用更快的动画速度 (STREAMING_ANIM_DURATION)
 * - 适合打字机效果和实时文本显示
 *
 * @param useAnimate 是否启用动画效果
 * @param useHdr 是否使用HDR增强效果
 */
@Composable
fun rememberStreamingMetallicBrush(
    useAnimate: Boolean = true,
    useHdr: Boolean = false,
): Brush =
    rememberMetallicBrush(
        useAnimate = useAnimate,
        rotationDurationMillis = STREAMING_ANIM_DURATION, // 使用流式文本专用的动画时长
        useHdr = useHdr,
    )

// --- Modifiers ---

/**
 * 优化: 绘制渐变边框修饰符 (合并了 modern 和 ifNeeded)
 * 支持HDR渲染和GPU硬件加速, 可选发光效果
 * @param strokeWidth 边框宽度 (Dp)
 * @param cornerRadius 圆角半径 (Dp)
 */
// 优化点: 使用 Dp 类型，合并函数
fun Modifier.drawGradientBorder(
    brush: Brush,
    enable: Boolean = true, // enable as parameter
    strokeWidth: Dp = 2.dp,
    cornerRadius: Dp = 12.dp,
    glowEffect: Boolean = false,
): Modifier =
    if (!enable) {
        this
    } else {
        this.drawBehind {
            // 优化点: 在 drawBehind 作用域内将 Dp 转 Px
            val strokePx = strokeWidth.toPx()
            val cornerPx = cornerRadius.toPx()
            val radius = CornerRadius(cornerPx)

            drawRoundRect(
                brush = brush,
                style = Stroke(width = strokePx),
                cornerRadius = radius,
            )

            if (glowEffect) {
                // Inner glow
                drawRoundRect(
                    brush = brush,
                    style = Stroke(width = strokePx * 0.5f),
                    cornerRadius = radius,
                    alpha = 0.6f,
                )
                // Outer glow
                drawRoundRect(
                    brush = brush,
                    style = Stroke(width = strokePx * 1.5f),
                    cornerRadius = radius,
                    alpha = 0.3f,
                )
            }
        }
    }

// --- Composable Components ---

/**
 * 优化: 亮度/灰阶文字组件 (合并了 ModernRainbowText 和 RainbowText)
 */
@Composable // 优化点: 合并函数，增加 style 参数
fun LuminanceText(
    text: String,
    modifier: Modifier = Modifier,
    style: TextStyle = MaterialTheme.typography.bodyLarge,
    animated: Boolean = true,
    // Defaults match original RainbowText
    hdrIntensity: Float = 1.0f,
    speed: Float = 1.0f,
    glowEffect: Boolean = false,
    // Allow passing custom colors
    colors: List<Color> = AppGradients.grayScale,
) {
    val brush =
        rememberLuminanceBrush(
            useAnimate = animated,
            hdrIntensity = hdrIntensity,
            speed = speed,
            colors = colors,
        )
    // Apply border if glowEffect is true
    val finalModifier =
        modifier.then(
            if (glowEffect) {
                Modifier.drawGradientBorder(
                    brush = brush,
                    enable = true,
                    strokeWidth = 1.dp,
                    glowEffect = true,
                )
            } else {
                Modifier
            },
        )

    Text(
        text = text,
        modifier = finalModifier,
        // Apply brush to text style
        style = style.copy(brush = brush),
    )
}

/**
 * 优化: 抽象金属质感文字组件 (用于 Logo 和 StreamingText)
 */
@Composable
fun MetallicText(
    text: String,
    modifier: Modifier = Modifier,
    animated: Boolean = true,
    useHdr: Boolean = false,
    animationDurationMs: Int, // Required duration
    fontWeight: FontWeight = FontWeight.Normal,
    style: TextStyle = MaterialTheme.typography.bodyLarge,
    glowEffect: Boolean = false,
) {
    val brush =
        rememberMetallicBrush(
            useAnimate = animated,
            rotationDurationMillis = animationDurationMs,
            useHdr = useHdr,
        )
    val finalModifier =
        modifier.then(
            if (glowEffect) {
                Modifier.drawGradientBorder(
                    brush = brush,
                    enable = true,
                    strokeWidth = 1.dp,
                    glowEffect = true,
                )
            } else {
                Modifier
            },
        )
    Text(
        text = text,
        modifier = finalModifier,
        style = style.copy(brush = brush, fontWeight = fontWeight),
    )
}

/**
 * GymBro Logo组件 (基于 MetallicText)
 */
@Composable
fun GymBroLogo(
    modifier: Modifier = Modifier,
    animated: Boolean = true,
    useHdr: Boolean = false,
    style: TextStyle = MaterialTheme.typography.displayMedium,
    glowEffect: Boolean = false,
) {
    // 优化点: 复用 MetallicText
    MetallicText(
        text = "GymBro",
        modifier = modifier,
        animated = animated,
        useHdr = useHdr,
        animationDurationMs = LOGO_ANIM_DURATION, // Use logo duration
        fontWeight = FontWeight.Bold, // Specific style
        style = style,
        glowEffect = glowEffect,
    )
}

/**
 * GymBro流式文字组件 (基于 MetallicText)
 */
@Composable
fun GymBroStreamingText(
    text: String,
    modifier: Modifier = Modifier,
    animated: Boolean = true,
    useHdr: Boolean = false,
    style: TextStyle = MaterialTheme.typography.bodyLarge,
) {
    // 优化点: 复用 MetallicText
    MetallicText(
        text = text,
        modifier = modifier,
        animated = animated,
        useHdr = useHdr,
        animationDurationMs = STREAMING_ANIM_DURATION, // Use streaming duration
        fontWeight = FontWeight.Normal, // Specific style
        style = style,
        glowEffect = false, // No glow for streaming text by default
    )
}

/**
 * 🔥 金属跃动输入框外层包装器
 *
 * 专为解决高频重组问题设计的性能优化组件：
 * 1. 真正的360度绕圈跃动效果
 * 2. 多阶金属颜色渐变
 * 3. 自然阴影效果
 * 4. 严格的性能控制
 *
 * 使用方式：
 * ```
 * MetallicRingInputWrapper {
 *     // 你的输入框内容
 *     TextField(...)
 * }
 * ```
 */
@Composable
fun MetallicRingInputWrapper(
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    animationSpeed: Float = 1.0f, // 动画速度控制
    shadowIntensity: Float = 0.3f, // 阴影强度
    strokeWidth: Dp = 2.dp,
    cornerRadius: Dp = 12.dp,
    useHdr: Boolean = false,
    content: @Composable BoxScope.() -> Unit,
) {
    // 🎯 性能优化：使用全局配置控制动画
    val animationDuration = remember(animationSpeed) {
        (STREAMING_ANIM_DURATION / animationSpeed.coerceIn(0.5f, 3.0f)).toInt()
            .coerceIn(MIN_ANIMATION_DURATION, MAX_ANIMATION_DURATION)
    }

    // 🔄 真正的绕圈动画 - 360度旋转
    val rotationAngle by if (enabled) {
        rememberInfiniteTransition(label = "metallic_ring_rotation").animateFloat(
            initialValue = 0f,
            targetValue = 360f, // 完整360度旋转
            animationSpec = infiniteRepeatable(
                animation = tween(
                    durationMillis = animationDuration,
                    easing = LinearEasing, // 匀速旋转，避免跳跃感
                ),
                repeatMode = RepeatMode.Restart, // 重新开始，实现真正绕圈
            ),
            label = "ring_rotation",
        )
    } else {
        remember { mutableStateOf(0f) }
    }

    // 🎨 金属质感画笔 - 使用缓存优化性能
    val metallicBrush = rememberMetallicBrush(
        useAnimate = enabled,
        rotationDurationMillis = animationDuration,
        useHdr = useHdr,
    )

    // 📊 性能监控（仅在debug模式）
    val recompositions = remember { 0 } // 简化版本，避免跨模块依赖

    Box(
        modifier = modifier
            // 🌟 自然阴影效果
            .drawBehind {
                if (shadowIntensity > 0f) {
                    val shadowRadius = 8.dp.toPx()
                    val shadowOffset = Offset(0f, 4.dp.toPx())

                    // 外层阴影
                    drawRoundRect(
                        color = Color.Black.copy(alpha = shadowIntensity * 0.15f),
                        topLeft = shadowOffset,
                        size = Size(
                            size.width + shadowRadius,
                            size.height + shadowRadius,
                        ),
                        cornerRadius = CornerRadius((cornerRadius + 4.dp).toPx()),
                    )

                    // 内层阴影（更深）
                    drawRoundRect(
                        color = Color.Black.copy(alpha = shadowIntensity * 0.08f),
                        topLeft = shadowOffset * 0.5f,
                        size = Size(
                            size.width + shadowRadius * 0.5f,
                            size.height + shadowRadius * 0.5f,
                        ),
                        cornerRadius = CornerRadius((cornerRadius + 2.dp).toPx()),
                    )
                }
            }
            // 🔄 旋转变换 - 围绕中心旋转
            .graphicsLayer {
                if (enabled) {
                    rotationZ = rotationAngle
                    transformOrigin = androidx.compose.ui.graphics.TransformOrigin.Center
                }
            }
            // 🎨 金属边框效果
            .drawGradientBorder(
                brush = metallicBrush,
                enable = enabled,
                strokeWidth = strokeWidth,
                cornerRadius = cornerRadius,
                glowEffect = false, // 避免过度效果影响性能
            )
            .padding(strokeWidth + 4.dp), // 为边框和阴影留出空间
        content = content,
    )
}

/**
 * 🎯 简化版金属跃动包装器 - 最小性能影响
 *
 * 适用于对性能要求极高的场景
 */
@Composable
fun MinimalMetallicRingWrapper(
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    content: @Composable BoxScope.() -> Unit,
) {
    MetallicRingInputWrapper(
        modifier = modifier,
        enabled = enabled,
        animationSpeed = 0.8f, // 较慢的动画
        shadowIntensity = 0.2f, // 较轻的阴影
        strokeWidth = 1.dp, // 较细的边框
        useHdr = false, // 禁用HDR
        content = content,
    )
}

/**
 * 🚀 高级金属跃动包装器 - 完整视觉效果
 *
 * 适用于重点展示的输入框
 */
@Composable
fun PremiumMetallicRingWrapper(
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    content: @Composable BoxScope.() -> Unit,
) {
    MetallicRingInputWrapper(
        modifier = modifier,
        enabled = enabled,
        animationSpeed = 1.2f, // 较快的动画
        shadowIntensity = 0.4f, // 较重的阴影
        strokeWidth = 3.dp, // 较粗的边框
        useHdr = true, // 启用HDR
        content = content,
    )
}

// ===== Preview Functions =====

@GymBroPreview
@Composable
private fun MetallicRingInputWrapperPreview() {
    GymBroTheme {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(24.dp),
        ) {
            // 标准版本
            MetallicRingInputWrapper(
                enabled = true,
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(56.dp)
                        .background(
                            MaterialTheme.colorScheme.surface,
                            RoundedCornerShape(8.dp),
                        ),
                    contentAlignment = androidx.compose.ui.Alignment.Center,
                ) {
                    Text(
                        text = "标准金属跃动输入框",
                        style = MaterialTheme.typography.bodyLarge,
                    )
                }
            }

            // 简化版本
            MinimalMetallicRingWrapper(
                enabled = true,
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(56.dp)
                        .background(
                            MaterialTheme.colorScheme.surface,
                            RoundedCornerShape(8.dp),
                        ),
                    contentAlignment = androidx.compose.ui.Alignment.Center,
                ) {
                    Text(
                        text = "简化版金属跃动（高性能）",
                        style = MaterialTheme.typography.bodyLarge,
                    )
                }
            }

            // 高级版本
            PremiumMetallicRingWrapper(
                enabled = true,
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(56.dp)
                        .background(
                            MaterialTheme.colorScheme.surface,
                            RoundedCornerShape(8.dp),
                        ),
                    contentAlignment = androidx.compose.ui.Alignment.Center,
                ) {
                    Text(
                        text = "高级金属跃动（完整效果）",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.primary,
                    )
                }
            }

            // 静态版本（对比）
            MetallicRingInputWrapper(
                enabled = false,
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(56.dp)
                        .background(
                            MaterialTheme.colorScheme.surface,
                            RoundedCornerShape(8.dp),
                        ),
                    contentAlignment = androidx.compose.ui.Alignment.Center,
                ) {
                    Text(
                        text = "静态版本（无动画）",
                        style = MaterialTheme.typography.bodyLarge,
                    )
                }
            }
        }
    }
}
