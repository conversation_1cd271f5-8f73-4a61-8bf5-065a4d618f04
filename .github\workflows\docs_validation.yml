name: Docs Validation

on:
  push:
    branches:
      - master
      - develop
  pull_request:
    branches:
      - master
      - develop

jobs:
  validate-docs:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.x'

    - name: Cache pip dependencies
      uses: actions/cache@v4
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements*.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install markdownlint-cli
      run: npm install -g markdownlint-cli

    - name: Cache npm modules
      uses: actions/cache@v4
      with:
        path: ~/.npm
        key: ${{ runner.os }}-npm-${{ hashFiles('package-lock.json') }}
        restore-keys: |
          ${{ runner.os }}-npm-

    - name: Run link validation script
      run: python ci-scripts/validate_links.py

    - name: Run markdown lint
      run: markdownlint **/*.md --ignore node_modules --config .markdownlint.json
