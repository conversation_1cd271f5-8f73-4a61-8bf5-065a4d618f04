---
description: todo,todolist,方案,施工,制作文档,
globs:
alwaysApply: false
---
````markdown
# 📌 [模块/项目] 技术修复方案（精简模板）

> **🚨 核心问题速览 🚨**
> **危机等级**: 🔴/🟠/🟡  **技术债务**: ⬆︎ 描述缺陷/痛点

---

## 1. 问题概要 & 修复大纲
1. **💥 问题-1**：影响 …
2. **💥 问题-2**：影响 …
<!-- ≤ 5 项 -->

**目标状态**
- 功能 / 性能 / 稳定性指标
- 预期架构形态

**修复要点**
- 需重构或新增的核心模块
- 必要的兼容与回滚策略

---

## 2. 架构说明 & 关键接口
### 2.1 总体架构图
<!-- UML / Mermaid 占位符 -->

### 2.2 模块职责
| 模块 | 职责 | 数据流 | 外部依赖 |
|------|------|--------|----------|

### 2.3 关键接口
| 接口 | 所属模块 | 入参 | 出参 | 逻辑说明 |
|------|----------|------|------|----------|

### 2.4 代码质量规范
- **命名**：文件 *snake_case*；变量/方法 *camelCase*
- **注释**：公共接口完整 JSDoc / docstring；内部函数一句功能说明
- **异常**：统一错误码包装；接口参数显式校验
- **测试**：核心路径覆盖率 ≥ 80 %
- **性能记录**：算法或数据结构变更需附 ≤ 10 行基准摘要

---

## 3. 施工方案（阶段化）
| 阶段 | 关键任务 | 主要输出 | 风险控制 |
|------|----------|----------|----------|
| Phase 1 | … | … | … |
| Phase 2 | … | … | … |
| Phase 3 | … | … | … |

---

## 4. 文件清单
```text
/path/to/file.ext    | 🆕/🔄/🗑️ | 功能或变更说明 | 关键接口
````

---

## 5. 交接蓝图 & 下一步

* **交付物**：代码、文档、测试报告、回滚脚本
* **后续方向**：功能扩展 / 性能优化 / 技术债清理
* **开放问题**：待确认事项 → 负责人

```
```
