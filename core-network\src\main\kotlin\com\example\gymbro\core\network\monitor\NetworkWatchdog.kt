package com.example.gymbro.core.network.monitor

import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import timber.log.Timber
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 网络监控狗接口 - D2阶段
 *
 * 提供网络状态变化的事件流，支持UI Banner显示
 * 包含事件节流、状态转换和生命周期管理
 */
@OptIn(kotlinx.coroutines.FlowPreview::class)
interface NetworkWatchdog {
    /**
     * 网络事件流
     * 经过防抖处理，避免频繁状态变化
     */
    val networkEvents: StateFlow<NetworkEvent?>

    /**
     * 当前网络事件
     */
    val currentEvent: NetworkEvent?

    /**
     * 开始监控
     */
    fun startWatching()

    /**
     * 停止监控
     */
    fun stopWatching()

    /**
     * 暂停监控（后台模式）
     */
    fun pauseWatching()

    /**
     * 恢复监控（前台模式）
     */
    fun resumeWatching()

    /**
     * 手动触发网络状态刷新
     */
    fun refreshNetworkState()
}

/**
 * 网络监控狗实现 - D2阶段
 *
 * 基于NetworkMonitor实现事件流处理，包含：
 * - 500ms防抖机制，避免频繁状态变化
 * - 状态转换逻辑 (Connected→Losing→Lost→Restored)
 * - 后台暂停优化，降低电量消耗
 * - 前台主动刷新机制
 */
class NetworkWatchdogImpl(
    private val networkMonitor: NetworkMonitor,
    private val scope: CoroutineScope = CoroutineScope(Dispatchers.Default + SupervisorJob()),
    private val debounceTimeMs: Long = 2000L, // 🎯 增加防抖时间到2秒，减少频繁更新
) : NetworkWatchdog {

    // 事件流状态
    private val _networkEvents = MutableStateFlow<NetworkEvent?>(null)
    override val networkEvents: StateFlow<NetworkEvent?> = _networkEvents.asStateFlow()

    override val currentEvent: NetworkEvent?
        get() = _networkEvents.value

    // 监控状态
    private val isWatching = AtomicBoolean(false)
    private val isPaused = AtomicBoolean(false)
    private var watchingJob: Job? = null

    // 状态缓存
    private var lastNetworkState: NetworkState? = null
    private var lastEventTime: Long = 0L

    init {
        Timber.d("🐕 NetworkWatchdog初始化: debounce=${debounceTimeMs}ms")
    }

    override fun startWatching() {
        if (isWatching.compareAndSet(false, true)) {
            Timber.d("🐕 开始网络监控")
            networkMonitor.startMonitoring()
            startEventProcessing()
        }
    }

    override fun stopWatching() {
        if (isWatching.compareAndSet(true, false)) {
            Timber.d("🐕 停止网络监控")
            watchingJob?.cancel()
            watchingJob = null
            networkMonitor.stopMonitoring()
            _networkEvents.value = null
        }
    }

    override fun pauseWatching() {
        if (!isPaused.compareAndSet(false, true)) return

        Timber.d("🐕 暂停网络监控（后台模式）")
        watchingJob?.cancel()
        watchingJob = null
    }

    override fun resumeWatching() {
        if (!isPaused.compareAndSet(true, false)) return

        Timber.d("🐕 恢复网络监控（前台模式）")
        if (isWatching.get()) {
            startEventProcessing()
            // 主动刷新一次网络状态
            refreshNetworkState()
        }
    }

    override fun refreshNetworkState() {
        if (!isWatching.get() || isPaused.get()) return

        Timber.d("🐕 手动刷新网络状态")
        scope.launch {
            val currentState = networkMonitor.networkState.value
            processNetworkStateChange(currentState, forceUpdate = true)
        }
    }

    /**
     * 开始事件处理 - 应用MVI 2.0背压优化
     */
    @OptIn(kotlinx.coroutines.FlowPreview::class)
    private fun startEventProcessing() {
        watchingJob?.cancel()
        watchingJob = scope.launch {
            networkMonitor.networkState
                .debounce(debounceTimeMs) // 防抖处理
                .distinctUntilChanged() // 去重
                .conflate() // 🎯 MVI 2.0规范：UI Flow使用conflate()背压处理
                .sample(1000) // 🎯 限制最高频率为1秒一次，避免过度更新
                .collect { networkState ->
                    if (!isPaused.get()) {
                        processNetworkStateChange(networkState)
                    }
                }
        }
    }

    /**
     * 处理网络状态变化
     */
    private suspend fun processNetworkStateChange(
        networkState: NetworkState,
        forceUpdate: Boolean = false,
    ) {
        val currentTime = System.currentTimeMillis()

        // 避免过于频繁的状态变化
        if (!forceUpdate && currentTime - lastEventTime < debounceTimeMs) {
            return
        }

        val previousState = lastNetworkState
        lastNetworkState = networkState
        lastEventTime = currentTime

        val event = determineNetworkEvent(previousState, networkState)

        if (event != null) {
            Timber.d("🐕 网络事件: ${event::class.simpleName} (${event.timestamp})")
            _networkEvents.value = event
        }
    }

    /**
     * 确定网络事件类型
     */
    private fun determineNetworkEvent(
        previousState: NetworkState?,
        currentState: NetworkState,
    ): NetworkEvent? {
        return when {
            // 首次连接或从断开状态恢复
            (
                previousState == null ||
                    previousState is NetworkState.Unavailable || previousState is NetworkState.Lost
                ) &&
                currentState is NetworkState.Available -> {
                NetworkEvent.Connected(currentState.type)
            }

            // 从可用状态变为连接中（可能不稳定）
            previousState is NetworkState.Available &&
                currentState is NetworkState.Connecting -> {
                NetworkEvent.Losing(NetworkLossReason.UNKNOWN)
            }

            // 连接完全丢失
            (previousState is NetworkState.Available || previousState is NetworkState.Connecting) &&
                (currentState is NetworkState.Unavailable || currentState is NetworkState.Lost) -> {
                val reason = determineNetworkLossReason(currentState)
                NetworkEvent.Lost(reason)
            }

            // 从丢失状态恢复
            (previousState is NetworkState.Unavailable || previousState is NetworkState.Lost) &&
                currentState is NetworkState.Available -> {
                NetworkEvent.Restored(currentState.type)
            }

            // 网络类型切换（WiFi ↔ 蜂窝）
            previousState is NetworkState.Available &&
                currentState is NetworkState.Available &&
                previousState.type != currentState.type -> {
                NetworkEvent.Connected(currentState.type)
            }

            // 带宽变化 - 按照websock+Okhttp.md核心实现方案
            previousState is NetworkState.Available &&
                currentState is NetworkState.Available &&
                previousState.bandwidthKbps != currentState.bandwidthKbps -> {
                NetworkEvent.Bandwidth(currentState.bandwidthKbps)
            }

            else -> null // 无需处理的状态变化
        }
    }

    /**
     * 确定网络丢失原因
     */
    private fun determineNetworkLossReason(networkState: NetworkState): NetworkLossReason {
        // 简单实现，实际项目中可能需要更复杂的逻辑
        return when (networkState) {
            is NetworkState.Unavailable -> NetworkLossReason.NO_SIGNAL
            is NetworkState.Lost -> NetworkLossReason.TIMEOUT
            else -> NetworkLossReason.UNKNOWN
        }
    }
}
