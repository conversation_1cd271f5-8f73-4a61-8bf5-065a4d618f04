package com.example.gymbro.core.arch.mvi

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.CoroutineName
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import timber.log.Timber

/**
 * MVI架构的基础ViewModel - v7.0-SMART-LOADING
 *
 * 核心改进（v7.0）：
 * 1. 🚀 恢复Effect分发机制 - 支持完整的MVI副作用处理
 * 2. 🔧 ModernResult深度集成 - 自动错误处理和UiText转换
 * 3. ⚡ 性能优化 - StateFlow/SharedFlow缓冲区优化，减少不必要重组
 * 4. 🛡️ 结构化并发 - EffectHandler独立协程作用域，生命周期管理
 * 5. 📊 简化监控 - 保留核心日志，移除过度监控
 * 6. 🎯 智能加载状态管理 - 自动检测和管理加载状态，减少开发错误
 * 7. 🔍 加载状态调试 - 自动诊断加载状态问题，提供详细调试信息
 *
 * MVI数据流：UI → Intent → Reducer → State + Effect → EffectHandler → Intent
 *
 * @param I Intent类型
 * @param S State类型
 * @param E Effect类型
 */
abstract class BaseMviViewModel<I : AppIntent, S : UiState, E : UiEffect>(
    initialState: S,
) : ViewModel() {
    // State管理 - 优化StateFlow性能
    protected val _state = MutableStateFlow(initialState)
    val state: StateFlow<S> = _state.asStateFlow()

    // Effect管理 - 🔥 优化：增加缓冲区，防止Effect丢失，支持背压控制
    protected val _effect =
        MutableSharedFlow<E>(
            replay = 0,
            extraBufferCapacity = 32, // 适中的缓冲区大小
            onBufferOverflow = BufferOverflow.SUSPEND, // 背压控制
        )
    val effect: SharedFlow<E> = _effect.asSharedFlow()

    // 🔥 结构化并发：EffectHandler独立协程作用域
    protected val handlerScope: CoroutineScope =
        viewModelScope.plus(
            SupervisorJob() + CoroutineName("MviEffectScope-${this::class.simpleName}"),
        )

    // 当前状态的快速访问
    protected val currentState: S
        get() = _state.value

    // 🎯 v7.0新增：智能加载状态管理
    private val loadingStateTracker = mutableMapOf<String, Boolean>()
    private var lastLoadingStateCheck = 0L

    /**
     * Reducer实例 - 子类必须提供
     * 负责Intent → State + Effect的转换
     */
    protected abstract val reducer: Reducer<I, S, E>

    /**
     * 分发Intent - MVI架构的统一入口
     *
     * 🔥 v6.0优化：
     * - 恢复完整的Effect处理
     * - 添加ModernResult自动处理
     * - 简化性能监控
     */
    open fun dispatch(intent: I) {
        val intentName = intent::class.simpleName ?: "UnknownIntent"

        // 🔥 调试：添加更多日志
        Timber.d("🎯 [${this::class.simpleName}] Processing: $intentName")

        // 🔥 【噪音日志控制】只在启用MVI调试时输出dispatch日志
        if (com.example.gymbro.core.logging.LoggingConfig.MVI_LOGGING_ENABLED) {
            println("🎯 [DEBUG] BaseMviViewModel.dispatch: Processing $intentName")
        }

        try {
            // 1. 通过Reducer进行状态转换和Effect生成
            if (com.example.gymbro.core.logging.LoggingConfig.MVI_LOGGING_ENABLED) {
                println("🔧 [DEBUG] BaseMviViewModel: 调用 reducer.reduce")
            }
            val result = reducer.reduce(intent, currentState)
            if (com.example.gymbro.core.logging.LoggingConfig.MVI_LOGGING_ENABLED) {
                println("🔧 [DEBUG] BaseMviViewModel: reducer.reduce 完成，effects数量: ${result.effects.size}")
            }

            // 2. 🔥 核心优化：总是更新状态，让StateFlow通知观察者
            val statesEqual = result.newState == currentState
            val stateHashEqual = result.newState.hashCode() == currentState.hashCode()

            if (!statesEqual) {
                _state.value = result.newState
                Timber.v("🔄 [${this::class.simpleName}] State updated by: $intentName")

                // 🔥 【噪音日志控制】只在启用MVI调试时输出
                if (com.example.gymbro.core.logging.LoggingConfig.MVI_LOGGING_ENABLED) {
                    println("🔄 [DEBUG] BaseMviViewModel: 状态已更新 - Intent: $intentName")

                    // 🔥 【调试增强】详细的状态变化信息
                    if (intentName.contains("UpdateThinkingBoxState")) {
                        println("🔍 [DEBUG] ThinkingBox状态变化详情:")
                        println("  - 状态相等: $statesEqual")
                        println("  - 哈希相等: $stateHashEqual")
                        println("  - 新状态类型: ${result.newState::class.simpleName}")
                        println("  - 当前状态类型: ${currentState::class.simpleName}")
                    }
                }

                // 🎯 v7.0新增：智能加载状态检测
                performLoadingStateAnalysis(result.newState, intentName)
            } else {
                // 🔥 【噪音日志控制】只在启用MVI调试时输出
                if (com.example.gymbro.core.logging.LoggingConfig.MVI_LOGGING_ENABLED) {
                    println("🔄 [DEBUG] BaseMviViewModel: 状态无变化 - Intent: $intentName")

                    // 🔥 【调试增强】状态无变化的详细信息
                    if (intentName.contains("UpdateThinkingBoxState")) {
                        println("🔍 [DEBUG] ThinkingBox状态无变化详情:")
                        println("  - 状态相等: $statesEqual")
                        println("  - 哈希相等: $stateHashEqual")
                        println("  - 状态引用相同: ${result.newState === currentState}")
                    }
                }
            }

            // 3. 🔥 恢复Effect处理 - 支持完整的MVI副作用流
            if (result.effects.isNotEmpty()) {
                // 🔥 【噪音日志控制】只在启用MVI调试时输出Effect信息
                if (com.example.gymbro.core.logging.LoggingConfig.MVI_LOGGING_ENABLED) {
                    println("✨ [DEBUG] BaseMviViewModel: 发送 ${result.effects.size} 个 Effect")
                }
                viewModelScope.launch {
                    result.effects.forEach { effect ->
                        _effect.emit(effect)
                        Timber.v("✨ [${this::class.simpleName}] Effect emitted: ${effect::class.simpleName}")
                        // 🔥 【噪音日志控制】只在启用MVI调试时输出Effect发送日志
                        if (com.example.gymbro.core.logging.LoggingConfig.MVI_LOGGING_ENABLED) {
                            println("✨ [DEBUG] BaseMviViewModel: Effect 已发送: ${effect::class.simpleName}")
                        }
                    }
                }
            } else {
                // 🔥 【噪音日志控制】只在启用MVI调试时输出无Effect日志
                if (com.example.gymbro.core.logging.LoggingConfig.MVI_LOGGING_ENABLED) {
                    println("✨ [DEBUG] BaseMviViewModel: 无 Effect 需要发送")
                }
            }
        } catch (e: Exception) {
            // 🔥 ModernResult集成：自动错误处理
            if (com.example.gymbro.core.logging.LoggingConfig.MVI_LOGGING_ENABLED) {
                println("❌ [DEBUG] BaseMviViewModel: dispatch 异常: ${e.message}")
            }
            Timber.e(e, "❌ [${this::class.simpleName}] dispatch 异常")
            handleIntentError(intent, e)
        }
    }

    /**
     * 🔥 新增：ModernResult集成的错误处理
     * 自动将异常转换为用户友好的错误状态
     */
    protected open fun handleIntentError(
        intent: I,
        error: Throwable,
    ) {
        Timber.e("❌ [${this::class.simpleName}] Intent processing failed: ${intent::class.simpleName}", error)

        // 子类可重写此方法来提供特定的错误处理逻辑
        // 默认行为：记录错误但不改变状态
    }

    /**
     * 🔥 新增：便捷的状态更新方法，支持lambda
     */
    protected fun updateState(update: (S) -> S) {
        val newState = update(currentState)
        if (newState != currentState) {
            _state.value = newState
        }
    }

    /**
     * 🔥 新增：便捷的Effect发送方法
     */
    protected fun sendEffect(effect: E) {
        viewModelScope.launch {
            _effect.emit(effect)
        }
    }

    /**
     * 🔥 新增：便捷的多Effect发送方法
     */
    protected fun sendEffects(effects: List<E>) {
        if (effects.isNotEmpty()) {
            viewModelScope.launch {
                effects.forEach { effect ->
                    _effect.emit(effect)
                }
            }
        }
    }

    /**
     * EffectHandler初始化 - 子类在init块中调用
     *
     * 注意：不在构造函数中调用，避免依赖注入未完成的问题
     */
    protected open fun initializeEffectHandler() {
        // 子类可重写此方法来初始化EffectHandler
        // 默认空实现
    }

    // 🎯 v7.0新增：智能加载状态分析
    private fun performLoadingStateAnalysis(newState: S, intentName: String) {
        val currentTime = System.currentTimeMillis()

        // 避免过于频繁的检查（最多每秒一次）
        if (currentTime - lastLoadingStateCheck < 1000) return
        lastLoadingStateCheck = currentTime

        try {
            // 使用反射检测所有isLoading相关的字段
            val loadingFields = detectLoadingFields(newState)

            if (loadingFields.isNotEmpty()) {
                val activeLoadingStates = loadingFields.filter { it.value }
                val totalLoadingFields = loadingFields.size
                val activeLoadingCount = activeLoadingStates.size

                // 更新加载状态跟踪器
                loadingFields.forEach { (fieldName, isLoading) ->
                    loadingStateTracker[fieldName] = isLoading
                }

                // 🔥 【噪音日志控制】只在启用MVI调试时输出加载状态分析
                if (com.example.gymbro.core.logging.LoggingConfig.MVI_LOGGING_ENABLED) {
                    // 输出详细的加载状态信息
                    println("🔍 [SMART-LOADING] [$intentName] 加载状态分析:")
                    println("   📊 总加载字段: $totalLoadingFields, 活跃加载: $activeLoadingCount")

                    if (activeLoadingStates.isNotEmpty()) {
                        println("   ⏳ 当前加载中: ${activeLoadingStates.keys.joinToString(", ")}")

                        // 检测可能的问题
                        detectPotentialLoadingIssues(activeLoadingStates, intentName)
                    } else {
                        println("   ✅ 所有数据加载完成")
                    }
                }
            }
        } catch (e: Exception) {
            // 静默处理反射异常，不影响正常流程
            Timber.w("智能加载状态分析失败: ${e.message}")
        }
    }

    // 🎯 检测State中所有isLoading相关的字段
    private fun detectLoadingFields(state: S): Map<String, Boolean> {
        val loadingFields = mutableMapOf<String, Boolean>()

        try {
            val stateClass = state!!::class
            val fields = stateClass.java.declaredFields

            fields.forEach { field ->
                if (field.type == Boolean::class.java &&
                    (field.name.contains("loading", ignoreCase = true) ||
                     field.name.contains("Loading"))) {

                    field.isAccessible = true
                    val value = field.get(state) as? Boolean ?: false
                    loadingFields[field.name] = value
                }
            }
        } catch (e: Exception) {
            // 如果反射失败，尝试Kotlin属性访问
            try {
                val stateClass = state!!::class
                stateClass.members.forEach { member ->
                    if (member.name.contains("loading", ignoreCase = true) &&
                        member.returnType.classifier == Boolean::class) {

                        val value = member.call(state) as? Boolean ?: false
                        loadingFields[member.name] = value
                    }
                }
            } catch (e2: Exception) {
                Timber.w("无法检测加载状态字段: ${e2.message}")
            }
        }

        return loadingFields
    }

    // 🎯 检测潜在的加载状态问题
    private fun detectPotentialLoadingIssues(activeLoadingStates: Map<String, Boolean>, intentName: String) {
        // 检测长时间加载的状态
        val suspiciousStates = loadingStateTracker.filter { (fieldName, wasLoading) ->
            wasLoading && activeLoadingStates.containsKey(fieldName)
        }

        // 🔥 【噪音日志控制】只在启用MVI调试时输出潜在问题分析
        if (com.example.gymbro.core.logging.LoggingConfig.MVI_LOGGING_ENABLED) {
            if (suspiciousStates.isNotEmpty()) {
                println("   ⚠️  可能的问题: 以下状态可能长时间处于加载中:")
                suspiciousStates.keys.forEach { fieldName ->
                    println("      - $fieldName (建议检查对应的数据加载逻辑)")
                }
            }

            // 检测主要的isLoading字段
            val mainLoadingField = activeLoadingStates.keys.find { it == "isLoading" }
            if (mainLoadingField != null) {
                println("   🚨 主加载状态 'isLoading' 仍为true，这可能阻止用户交互")
                println("      💡 建议: 确保所有数据加载完成后正确重置isLoading状态")
            }
        }
    }

    /**
     * 🎯 v7.0新增：手动触发加载状态诊断
     * 开发者可以在需要时调用此方法来诊断加载状态问题
     */
    protected fun diagnoseLoadingState() {
        // 🔥 【噪音日志控制】只在启用MVI调试时输出手动诊断
        if (com.example.gymbro.core.logging.LoggingConfig.MVI_LOGGING_ENABLED) {
            println("🔍 [SMART-LOADING] 手动诊断加载状态:")
        }
        performLoadingStateAnalysis(currentState, "ManualDiagnosis")
    }

    /**
     * 🎯 v7.0新增：重置所有加载状态跟踪
     * 在某些情况下可能需要清理跟踪状态
     */
    protected fun resetLoadingStateTracking() {
        loadingStateTracker.clear()
        // 🔥 【噪音日志控制】只在启用MVI调试时输出重置信息
        if (com.example.gymbro.core.logging.LoggingConfig.MVI_LOGGING_ENABLED) {
            println("🔄 [SMART-LOADING] 加载状态跟踪已重置")
        }
    }

    override fun onCleared() {
        super.onCleared()
        loadingStateTracker.clear()
        Timber.d("🧹 [${this::class.simpleName}] ViewModel cleared")
    }
}

/**
 * 简化版的BaseMviViewModel - 适用于简单场景
 *
 * 🔥 v6.0优化：
 * - 性能优化的状态管理
 * - 便捷的更新方法
 * - 更好的错误处理
 */
abstract class SimpleBaseMviViewModel<I : Any, S : Any>(
    initialState: S,
) : ViewModel() {
    // State管理
    private val _state = MutableStateFlow(initialState)
    val state: StateFlow<S> = _state.asStateFlow()

    // 当前状态的快速访问
    protected val currentState: S
        get() = _state.value

    /**
     * 简化的Reducer函数 - 子类实现状态转换逻辑
     */
    protected abstract fun reduce(
        currentState: S,
        intent: I,
    ): S

    /**
     * 分发Intent - 简化版实现
     */
    fun dispatch(intent: I) {
        try {
            val newState = reduce(currentState, intent)
            if (newState != currentState) {
                _state.value = newState
                Timber.v("🔄 [${this::class.simpleName}] State updated")
            }
        } catch (e: Exception) {
            Timber.e("❌ [${this::class.simpleName}] Intent processing failed", e)
            // 简化版：静默处理错误，保持当前状态
        }
    }

    /**
     * 🔥 优化：便捷的状态更新方法
     */
    protected fun updateState(newState: S) {
        if (newState != currentState) {
            _state.value = newState
        }
    }

    /**
     * 🔥 优化：便捷的状态更新方法（lambda版）
     */
    protected fun updateState(update: (S) -> S) {
        val newState = update(currentState)
        if (newState != currentState) {
            _state.value = newState
        }
    }
}

/**
 * 🎯 v7.0新增：智能加载状态管理扩展函数
 * 帮助开发者更容易地管理加载状态，减少常见错误
 */

/**
 * 检查State是否有任何加载状态为true
 * 使用示例：if (state.hasAnyLoading()) { /* 显示加载中 */ }
 */
fun Any.hasAnyLoading(): Boolean {
    return try {
        val fields = this::class.java.declaredFields
        fields.any { field ->
            if (field.type == Boolean::class.java &&
                (field.name.contains("loading", ignoreCase = true) ||
                 field.name.contains("Loading"))) {
                field.isAccessible = true
                field.get(this) as? Boolean ?: false
            } else false
        }
    } catch (e: Exception) {
        false
    }
}

/**
 * 获取所有加载状态的详细信息
 * 使用示例：val loadingInfo = state.getLoadingStateInfo()
 */
fun Any.getLoadingStateInfo(): Map<String, Boolean> {
    return try {
        val loadingFields = mutableMapOf<String, Boolean>()
        val fields = this::class.java.declaredFields

        fields.forEach { field ->
            if (field.type == Boolean::class.java &&
                (field.name.contains("loading", ignoreCase = true) ||
                 field.name.contains("Loading"))) {
                field.isAccessible = true
                val value = field.get(this) as? Boolean ?: false
                loadingFields[field.name] = value
            }
        }
        loadingFields
    } catch (e: Exception) {
        emptyMap()
    }
}

/**
 * 检查主要的isLoading字段
 * 使用示例：if (state.isMainLoading()) { /* 阻止用户交互 */ }
 */
fun Any.isMainLoading(): Boolean {
    return try {
        val field = this::class.java.getDeclaredField("isLoading")
        field.isAccessible = true
        field.get(this) as? Boolean ?: false
    } catch (e: Exception) {
        false
    }
}
