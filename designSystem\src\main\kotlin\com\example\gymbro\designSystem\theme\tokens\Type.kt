package com.example.gymbro.designSystem.theme.tokens

import androidx.compose.material3.Typography
import androidx.compose.runtime.Composable
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp
import com.example.gymbro.designSystem.R
// import androidx.compose.ui.platform.LocalContext // No longer needed

// 导入 TypographyTokens 以确保字体系统一致性
// 现在 Type.kt 将基于 TypographyTokens 构建，而不是使用硬编码值

// MapleMono字体族定义
val MapleMono =
    FontFamily(
        Font(R.font.maplemono_light, FontWeight.Light),
        Font(R.font.maplemono_regular, FontWeight.Normal),
        Font(R.font.maplemono_medium, FontWeight.Medium),
        Font(R.font.maplemono_bold, FontWeight.Bold),
        Font(R.font.maplemono_italic, FontWeight.Normal, FontStyle.Italic),
    )

// 使用MapleMono字体作为应用默认字体
@Composable
fun rememberMapleMono(): FontFamily = MapleMono

// 使用MapleMono作为应用默认字体
val DefaultFontFamily = MapleMono

// 基本Typography定义 - 统一使用 TypographyTokens
val Typography =
    Typography(
        displayLarge =
        TextStyle(
            fontFamily = DefaultFontFamily,
            fontWeight = FontWeight.Bold,
                fontSize = TypographyTokens.DisplayLarge, // 32.sp - 与 Token 系统一致
                lineHeight = TypographyTokens.DisplayLarge * 1.25f, // 40.sp - 基于 Token 计算
                letterSpacing = (-0.25).sp,
        ),
        displayMedium =
        TextStyle(
            fontFamily = DefaultFontFamily,
            fontWeight = FontWeight.Normal,
                fontSize = TypographyTokens.Display, // 28.sp
                lineHeight = TypographyTokens.Display * 1.29f, // 36.sp
            letterSpacing = 0.sp,
        ),
        displaySmall =
        TextStyle(
            fontFamily = DefaultFontFamily,
            fontWeight = FontWeight.Normal,
                fontSize = TypographyTokens.HeadlineLarge, // 24.sp
                lineHeight = TypographyTokens.HeadlineLarge * 1.33f, // 32.sp
            letterSpacing = 0.sp,
        ),
        headlineLarge =
        TextStyle(
            fontFamily = DefaultFontFamily,
            fontWeight = FontWeight.SemiBold,
                fontSize = TypographyTokens.Headline, // 20.sp
                lineHeight = TypographyTokens.Headline * 1.4f, // 28.sp
            letterSpacing = 0.sp,
        ),
        headlineMedium =
        TextStyle(
            fontFamily = DefaultFontFamily,
            fontWeight = FontWeight.Normal,
                fontSize = TypographyTokens.BodyLarge, // 18.sp
                lineHeight = TypographyTokens.BodyLarge * 1.33f, // 24.sp
            letterSpacing = 0.sp,
        ),
        headlineSmall =
        TextStyle(
            fontFamily = DefaultFontFamily,
                fontWeight = FontWeight.Normal,
                fontSize = TypographyTokens.BodyMedium, // 16.sp
                lineHeight = TypographyTokens.BodyMedium * 1.5f, // 24.sp
            letterSpacing = 0.sp,
        ),
        titleLarge =
        TextStyle(
            fontFamily = DefaultFontFamily,
                fontWeight = FontWeight.Bold,
                fontSize = TypographyTokens.Headline, // 20.sp - 与 Token 一致
                lineHeight = TypographyTokens.Headline * 1.4f, // 28.sp
            letterSpacing = 0.sp,
        ),
        titleMedium =
        TextStyle(
            fontFamily = DefaultFontFamily,
                fontWeight = FontWeight.Medium,
                fontSize = TypographyTokens.BodyMedium, // 16.sp
                lineHeight = TypographyTokens.BodyMedium * 1.5f, // 24.sp
            letterSpacing = 0.15.sp,
        ),
        titleSmall =
        TextStyle(
            fontFamily = DefaultFontFamily,
                fontWeight = FontWeight.Medium,
                fontSize = TypographyTokens.Body, // 14.sp
            lineHeight = TypographyTokens.Body * 1.43f, // 20.sp
            letterSpacing = 0.1.sp,
        ),
        bodyLarge =
        TextStyle(
                fontFamily = DefaultFontFamily,
                fontWeight = FontWeight.Normal,
                fontSize = TypographyTokens.BodyMedium, // 16.sp
            lineHeight = TypographyTokens.BodyMedium * 1.5f, // 24.sp
            letterSpacing = 0.5.sp,
        ),
        bodyMedium =
        TextStyle(
                fontFamily = DefaultFontFamily,
                fontWeight = FontWeight.Normal,
                fontSize = TypographyTokens.Body, // 14.sp
            lineHeight = TypographyTokens.Body * 1.43f, // 20.sp
            letterSpacing = 0.25.sp,
        ),
        bodySmall =
            TextStyle(
                fontFamily = DefaultFontFamily,
                fontWeight = FontWeight.Normal,
                fontSize = TypographyTokens.Small, // 12.sp
            lineHeight = TypographyTokens.Small * 1.33f, // 16.sp
            letterSpacing = 0.4.sp,
        ),
        labelLarge =
            TextStyle(
                fontFamily = DefaultFontFamily,
                fontWeight = FontWeight.Medium,
            fontSize = TypographyTokens.Body, // 14.sp
            lineHeight = TypographyTokens.Body * 1.43f, // 20.sp
            letterSpacing = 0.1.sp,
            ),
        labelMedium =
            TextStyle(
                fontFamily = DefaultFontFamily,
                fontWeight = FontWeight.Medium,
            fontSize = TypographyTokens.Small, // 12.sp
            lineHeight = TypographyTokens.Small * 1.33f, // 16.sp
            letterSpacing = 0.5.sp,
            ),
        labelSmall =
            TextStyle(
                fontFamily = DefaultFontFamily,
                fontWeight = FontWeight.Medium,
            fontSize = TypographyTokens.Tiny, // 10.sp - 使用 Token 系统
            lineHeight = TypographyTokens.Tiny * 1.6f, // 16.sp
            letterSpacing = 0.5.sp,
        ),
    )

// 使用MapleMono字体应用到Typography
@Composable
fun rememberGymBroTypography(): Typography {
    val mapleMono = rememberMapleMono()

    return Typography(
        displayLarge = Typography.displayLarge.copy(fontFamily = mapleMono),
        displayMedium = Typography.displayMedium.copy(fontFamily = mapleMono),
        displaySmall = Typography.displaySmall.copy(fontFamily = mapleMono),
        headlineLarge = Typography.headlineLarge.copy(fontFamily = mapleMono),
        headlineMedium = Typography.headlineMedium.copy(fontFamily = mapleMono),
        headlineSmall = Typography.headlineSmall.copy(fontFamily = mapleMono),
        titleLarge = Typography.titleLarge.copy(fontFamily = mapleMono),
        titleMedium = Typography.titleMedium.copy(fontFamily = mapleMono),
        titleSmall = Typography.titleSmall.copy(fontFamily = mapleMono),
        bodyLarge = Typography.bodyLarge.copy(fontFamily = mapleMono),
        bodyMedium = Typography.bodyMedium.copy(fontFamily = mapleMono),
        bodySmall = Typography.bodySmall.copy(fontFamily = mapleMono),
        labelLarge = Typography.labelLarge.copy(fontFamily = mapleMono),
        labelMedium = Typography.labelMedium.copy(fontFamily = mapleMono),
        labelSmall = Typography.labelSmall.copy(fontFamily = mapleMono),
    )
}

// Logo特定样式
val LogoTypography =
    TextStyle(
        fontFamily = DefaultFontFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 48.sp,
    )

// Logo专用样式(应用MapleMono字体)
@Composable
fun rememberLogoTypography(): TextStyle = LogoTypography.copy(fontFamily = rememberMapleMono())

// 从Typography.kt导入tiny样式定义
// 不再在此处定义tiny扩展属性，统一使用Typography.kt中的定义

// 添加到GymBroTypography的tiny样式
@Composable
fun rememberTinyStyle(): TextStyle {
    // 直接创建tiny样式而不是从MaterialTheme.typography获取
    return TextStyle(
        fontFamily = rememberMapleMono(),
        fontWeight = FontWeight.Normal,
        fontSize = 10.sp,
        lineHeight = 14.sp,
        letterSpacing = 0.4.sp,
    )
}
