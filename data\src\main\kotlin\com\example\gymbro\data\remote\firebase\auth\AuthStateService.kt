package com.example.gymbro.data.remote.firebase.auth

import com.google.firebase.auth.FirebaseAuth
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Firebase认证状态服务 - 简化版本
 * 负责认证状态Flow管理和监听
 */
@Singleton
class AuthStateService @Inject constructor(
    private val auth: FirebaseAuth,
) {

    /**
     * 获取认证状态流 - 简化为Boolean
     * @return 认证状态Flow (true=已登录, false=未登录)
     */
    fun getAuthStateFlow(): Flow<Boolean> = callbackFlow {
        val authStateListener = FirebaseAuth.AuthStateListener { auth ->
            val currentUser = auth.currentUser
            trySend(currentUser != null)
        }

        auth.addAuthStateListener(authStateListener)

        // 初始状态
        val currentUser = auth.currentUser
        trySend(currentUser != null)

        awaitClose {
            auth.removeAuthStateListener(authStateListener)
        }
    }

    /**
     * 获取认证状态流(别名)
     * @return 认证状态Flow
     */
    fun getAuthState(): Flow<Boolean> = getAuthStateFlow()

    /**
     * 观察认证状态
     * @return 认证状态Flow
     */
    fun observeAuthState(): Flow<Boolean> = getAuthStateFlow()
}
