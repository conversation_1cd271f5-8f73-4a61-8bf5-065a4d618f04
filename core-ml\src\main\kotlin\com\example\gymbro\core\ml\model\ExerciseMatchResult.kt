package com.example.gymbro.core.ml.model

/**
 * 动作匹配类型
 */
enum class MatchType {
    EXACT, // 精确匹配
    FUZZY, // 模糊匹配
    SEMANTIC, // 语义匹配
    SYNONYM, // 同义词匹配
}

/**
 * 动作匹配结果
 *
 * @param exerciseId 动作ID
 * @param exerciseName 动作名称
 * @param similarity 相似度分数 (0.0 到 1.0)
 * @param matchType 匹配类型
 * @param matchedText 匹配到的文本
 */
data class ExerciseMatchResult(
    val exerciseId: String,
    val exerciseName: String,
    val similarity: Float,
    val matchType: MatchType,
    val matchedText: String = "",
)
