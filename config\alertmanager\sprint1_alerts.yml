groups:
- name: sprint1_function_calling
  rules:
  - alert: FunctionCallingHitRateLow
    expr: (rate(func_call_success_total[5m]) / rate(func_call_total[5m]) * 100) < 90
    for: 5m
    labels:
      severity: critical
      component: ai_gateway
      sprint: sprint1
    annotations:
      summary: "Function Calling成功率低于90%阈值"
      description: "当前成功率{{ $value | humanizePercentage }}，低于90%阈值，可能影响AI模板生成质量"
      dashboard_url: "https://grafana.example.com/d/sprint1-monitoring"
      runbook_url: "https://wiki.example.com/runbooks/function-calling-debug"

  - alert: SchemaErrorRateHigh
    expr: (rate(schema_validation_errors_total[5m]) / rate(function_calls_total[5m]) * 100) > 5
    for: 3m
    labels:
      severity: warning
      component: json_schema
      sprint: sprint1
    annotations:
      summary: "JSON Schema错误率超过5%阈值"
      description: "当前错误率{{ $value | humanizePercentage }}，超过5%阈值，需要检查Schema定义或AI模型稳定性"
      dashboard_url: "https://grafana.example.com/d/sprint1-monitoring"
      action_required: "检查最近的Schema更新，重新审视Function定义"

- name: sprint1_performance
  rules:
  - alert: EndToEndLatencyHigh
    expr: histogram_quantile(0.90, rate(end_to_end_latency_seconds_bucket[5m])) > 30
    for: 2m
    labels:
      severity: warning
      component: workout_flow
      sprint: sprint1
    annotations:
      summary: "端到端延迟P90超过30秒"
      description: "P90延迟{{ $value }}秒，超过30秒目标，用户体验受影响"
      dashboard_url: "https://grafana.example.com/d/sprint1-monitoring"

  - alert: ActiveWorkoutCrashRateHigh
    expr: rate(active_workout_crashes_total[5m]) * 3600 > 0.2
    for: 1m
    labels:
      severity: critical
      component: active_workout
      sprint: sprint1
    annotations:
      summary: "ActiveWorkout崩溃率超过0.2%"
      description: "当前崩溃率{{ $value | humanize }}/小时，超过0.2%阈值"
      dashboard_url: "https://grafana.example.com/d/sprint1-monitoring"
      action_required: "立即检查SavedStateHandle和Room事务处理"

- name: sprint1_cost_control
  rules:
  - alert: TokenCostBudgetExceeded
    expr: increase(tokens_cost_usd_total[1d]) > 8.33
    for: 5m
    labels:
      severity: warning
      component: cost_control
      sprint: sprint1
    annotations:
      summary: "Token成本超出日预算$8.33"
      description: "今日Token成本${{ $value | humanize }}，超过$8.33预算（月预算$250÷30天）"
      dashboard_url: "https://grafana.example.com/d/sprint1-monitoring"
      action_required: "检查Token压缩效果，考虑启用更严格的Function fallback"

  - alert: TokenCompressionEfficiencyLow
    expr: (avg_over_time(tokens_func_def_per_request[1h]) / avg_over_time(tokens_func_def_uncompressed_per_request[1h])) > 0.85
    for: 10m
    labels:
      severity: info
      component: token_optimization
      sprint: sprint1
    annotations:
      summary: "Token压缩效果低于预期18%"
      description: "当前压缩比{{ $value | humanizePercentage }}，低于预期82%（18%节省）"
      dashboard_url: "https://grafana.example.com/d/sprint1-monitoring"

- name: sprint1_data_integrity
  rules:
  - alert: SessionDataLossDetected
    expr: increase(session_data_loss_total[5m]) > 0
    for: 0m
    labels:
      severity: critical
      component: session_management
      sprint: sprint1
    annotations:
      summary: "检测到训练会话数据丢失"
      description: "{{ $value }}个会话数据丢失，可能影响用户训练记录"
      dashboard_url: "https://grafana.example.com/d/sprint1-monitoring"
      action_required: "立即检查Room数据库和SavedStateHandle同步"

  - alert: WorkoutSessionStuckInActive
    expr: count(workout_session_active_duration_seconds > 7200) > 0
    for: 5m
    labels:
      severity: warning
      component: session_management
      sprint: sprint1
    annotations:
      summary: "训练会话超过2小时未结束"
      description: "{{ $value }}个会话处于活跃状态超过2小时，可能存在状态管理bug"
      dashboard_url: "https://grafana.example.com/d/sprint1-monitoring"

- name: sprint1_infrastructure
  rules:
  - alert: E2ETestFailure
    expr: increase(e2e_test_failures_total[1h]) > 0
    for: 0m
    labels:
      severity: critical
      component: ci_cd
      sprint: sprint1
    annotations:
      summary: "Sprint 1 E2E测试失败"
      description: "{{ $value }}个E2E测试失败，可能影响发布"
      dashboard_url: "https://grafana.example.com/d/sprint1-monitoring"
      action_required: "检查ci-scripts/sprint1_e2e_enhanced.sh日志"

  - alert: FunctionCallingModelUnavailable
    expr: up{job="ai_gateway"} == 0
    for: 1m
    labels:
      severity: critical
      component: ai_model
      sprint: sprint1
    annotations:
      summary: "AI Gateway服务不可用"
      description: "Function Calling依赖的AI Gateway服务下线"
      dashboard_url: "https://grafana.example.com/d/sprint1-monitoring"
      action_required: "检查OpenAI/DeepSeek API连通性，启用降级策略"

# 告警路由配置
route:
  group_by: ['alertname', 'sprint']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'sprint1-notifications'
  routes:
  - match:
      severity: critical
    receiver: 'sprint1-critical'
    group_wait: 0s
    repeat_interval: 15m
  - match:
      component: cost_control
    receiver: 'sprint1-cost-alerts'
    repeat_interval: 6h

# 告警接收器配置
receivers:
- name: 'sprint1-notifications'
  slack_configs:
  - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
    channel: '#sprint1-alerts'
    title: 'Sprint 1 Alert - {{ .GroupLabels.alertname }}'
    text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
    fields:
    - title: 'Component'
      value: '{{ .GroupLabels.component }}'
      short: true
    - title: 'Severity'
      value: '{{ .GroupLabels.severity }}'
      short: true
    - title: 'Dashboard'
      value: '<{{ .CommonAnnotations.dashboard_url }}|View Dashboard>'
      short: false

- name: 'sprint1-critical'
  slack_configs:
  - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
    channel: '#sprint1-critical'
    title: '🚨 CRITICAL: {{ .GroupLabels.alertname }}'
    text: '{{ range .Alerts }}{{ .Annotations.description }}{{ end }}'
    color: 'danger'
  pagerduty_configs:
  - routing_key: 'YOUR_PAGERDUTY_INTEGRATION_KEY'
    description: 'Sprint 1 Critical Alert: {{ .GroupLabels.alertname }}'

- name: 'sprint1-cost-alerts'
  email_configs:
  - to: '<EMAIL>,<EMAIL>'
    from: '<EMAIL>'
    subject: 'Sprint 1 Token Cost Alert'
    body: |
      Token成本告警详情：
      {{ range .Alerts }}
      - {{ .Annotations.summary }}
      - {{ .Annotations.description }}
      {{ end }}

      请检查Grafana面板：{{ .CommonAnnotations.dashboard_url }}

# 抑制规则（避免告警风暴）
inhibit_rules:
- source_match:
    severity: 'critical'
  target_match:
    severity: 'warning'
  equal: ['alertname', 'component']

# 全局配置
global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'
  slack_api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
