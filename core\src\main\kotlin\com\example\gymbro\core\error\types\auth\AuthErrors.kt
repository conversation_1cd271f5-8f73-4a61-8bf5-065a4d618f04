package com.example.gymbro.core.error.types.auth

import com.example.gymbro.core.error.types.ErrorCategory
import com.example.gymbro.core.error.types.ErrorSeverity
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.StandardKeys
import com.example.gymbro.core.ui.text.UiText

/**
 * 认证相关错误类型集合
 *
 * 包含用户认证、授权和访问控制相关的错误处理
 * 从 DomainErrors.kt 迁移而来，专门处理认证层错误
 */
object AuthErrors {

    /**
     * 认证错误类
     */
    class AuthError private constructor(
        val operationName: String,
        val uiMessage: UiText,
        val errorType: GlobalErrorType.Auth,
        val severity: ErrorSeverity = ErrorSeverity.ERROR,
        val recoverable: Boolean = false,
        val cause: Throwable? = null,
        val errorSubtype: String? = null,
        val userId: String? = null,
        val metadataMap: Map<String, Any> = emptyMap(),
    ) {
        /**
         * 转换为ModernDataError
         */
        fun toModernDataError(): ModernDataError {
            return ModernDataError(
                operationName = operationName,
                errorType = errorType,
                category = ErrorCategory.AUTH,
                uiMessage = uiMessage,
                severity = severity,
                recoverable = recoverable,
                cause = cause,
                metadataMap = createMetadataMap(errorSubtype, userId, metadataMap),
            )
        }

        companion object {
            /**
             * 创建包含认证详情的元数据映射
             */
            private fun createMetadataMap(
                errorSubtype: String?,
                userId: String?,
                baseMetadata: Map<String, Any>,
            ): Map<String, Any> {
                val resultMap = baseMetadata.toMutableMap()
                errorSubtype?.let { resultMap[StandardKeys.ERROR_SUBTYPE.key] = it }
                userId?.let { resultMap[StandardKeys.USER_ID.key] = it }
                return resultMap
            }

            /**
             * 创建未登录错误
             */
            fun notLoggedIn(
                operationName: String = "AuthError.notLoggedIn",
                message: UiText = UiText.DynamicString("用户未登录"),
                cause: Throwable? = null,
                metadataMap: Map<String, Any> = emptyMap(),
            ): ModernDataError = AuthError(
                operationName = operationName,
                uiMessage = message,
                errorType = GlobalErrorType.Auth.Unauthorized,
                errorSubtype = "not_logged_in",
                cause = cause,
                metadataMap = metadataMap,
            ).toModernDataError()

            /**
             * 创建未授权错误
             */
            fun unauthorized(
                operationName: String = "AuthError.unauthorized",
                message: UiText = UiText.DynamicString("用户未授权"),
                userId: String? = null,
                cause: Throwable? = null,
                metadataMap: Map<String, Any> = emptyMap(),
            ): ModernDataError = AuthError(
                operationName = operationName,
                uiMessage = message,
                errorType = GlobalErrorType.Auth.Unauthorized,
                errorSubtype = "unauthorized",
                userId = userId,
                cause = cause,
                metadataMap = metadataMap,
            ).toModernDataError()

            /**
             * 创建令牌过期错误
             */
            fun tokenExpired(
                operationName: String = "AuthError.tokenExpired",
                message: UiText = UiText.DynamicString("认证令牌已过期"),
                userId: String? = null,
                cause: Throwable? = null,
                metadataMap: Map<String, Any> = emptyMap(),
            ): ModernDataError = AuthError(
                operationName = operationName,
                uiMessage = message,
                errorType = GlobalErrorType.Auth.TokenExpired,
                errorSubtype = "token_expired",
                userId = userId,
                cause = cause,
                metadataMap = metadataMap,
            ).toModernDataError()

            /**
             * 创建无效凭证错误
             */
            fun invalidCredentials(
                operationName: String = "AuthError.invalidCredentials",
                message: UiText = UiText.DynamicString("无效的凭证"),
                userId: String? = null,
                cause: Throwable? = null,
                metadataMap: Map<String, Any> = emptyMap(),
            ): ModernDataError = AuthError(
                operationName = operationName,
                uiMessage = message,
                errorType = GlobalErrorType.Auth.InvalidCredentials,
                errorSubtype = "invalid_credentials",
                userId = userId,
                cause = cause,
                metadataMap = metadataMap,
            ).toModernDataError()

            /**
             * 创建无效令牌错误
             */
            fun invalidToken(
                operationName: String = "AuthError.invalidToken",
                message: UiText = UiText.DynamicString("无效的令牌"),
                userId: String? = null,
                cause: Throwable? = null,
                metadataMap: Map<String, Any> = emptyMap(),
            ): ModernDataError = AuthError(
                operationName = operationName,
                uiMessage = message,
                errorType = GlobalErrorType.Auth.TokenExpired,
                errorSubtype = "invalid_token",
                userId = userId,
                cause = cause,
                metadataMap = metadataMap,
            ).toModernDataError()

            /**
             * 创建禁止访问错误
             */
            fun forbidden(
                operationName: String = "AuthError.forbidden",
                message: UiText = UiText.DynamicString("禁止访问"),
                userId: String? = null,
                cause: Throwable? = null,
                metadataMap: Map<String, Any> = emptyMap(),
            ): ModernDataError = AuthError(
                operationName = operationName,
                uiMessage = message,
                errorType = GlobalErrorType.Auth.Forbidden,
                errorSubtype = "forbidden",
                userId = userId,
                cause = cause,
                metadataMap = metadataMap,
            ).toModernDataError()

            /**
             * 创建权限被拒绝错误
             */
            fun permissionDenied(
                operationName: String = "AuthError.permissionDenied",
                message: UiText = UiText.DynamicString("权限被拒绝"),
                userId: String? = null,
                cause: Throwable? = null,
                metadataMap: Map<String, Any> = emptyMap(),
            ): ModernDataError = AuthError(
                operationName = operationName,
                uiMessage = message,
                errorType = GlobalErrorType.Auth.Forbidden,
                errorSubtype = "permission_denied",
                userId = userId,
                cause = cause,
                metadataMap = metadataMap,
            ).toModernDataError()

            /**
             * 创建未找到用户错误
             */
            fun notFound(
                operationName: String = "AuthError.notFound",
                message: UiText = UiText.DynamicString("用户不存在"),
                userId: String? = null,
                cause: Throwable? = null,
                metadataMap: Map<String, Any> = emptyMap(),
            ): ModernDataError = AuthError(
                operationName = operationName,
                uiMessage = message,
                errorType = GlobalErrorType.Auth.General,
                errorSubtype = "user_not_found",
                userId = userId,
                cause = cause,
                metadataMap = metadataMap,
            ).toModernDataError()

            /**
             * 创建通用认证错误
             */
            fun unknown(
                operationName: String = "AuthError.unknown",
                message: UiText = UiText.DynamicString("认证错误"),
                userId: String? = null,
                cause: Throwable? = null,
                metadataMap: Map<String, Any> = emptyMap(),
            ): ModernDataError = AuthError(
                operationName = operationName,
                uiMessage = message,
                errorType = GlobalErrorType.Auth.General,
                errorSubtype = "unknown",
                userId = userId,
                cause = cause,
                metadataMap = metadataMap,
            ).toModernDataError()
        }
    }
}
