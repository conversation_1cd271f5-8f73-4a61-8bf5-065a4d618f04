package com.example.gymbro.data.ai.monitoring

import com.example.gymbro.domain.profile.model.user.FitnessGoal
import com.example.gymbro.domain.profile.model.user.FitnessPreference
import com.example.gymbro.domain.workout.model.TemplateDraft
import timber.log.Timber
import java.time.Duration
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * AI 指标收集器
 *
 * 收集 AI Prompt 相关的监控指标，支持 Grafana 监控面板
 * Phase 5 Task 4: Grafana 监控设置
 *
 * 核心指标：
 * - goal_match_rate: AI 响应与用户目标的匹配率
 * - preference_injection_rate: 用户偏好注入成功率
 * - ai_response_time: AI 响应时间分布
 * - user_preference_coverage: 用户偏好设置覆盖率
 */
@Singleton
class AiMetricsCollector
@Inject
constructor() {
    // 目标匹配计数器
    private val goalMatchCounter = AtomicLong(0)
    private val totalRequestCounter = AtomicLong(0)
    private val goalMatchByType = mutableMapOf<String, AtomicLong>()

    // 偏好注入计数器
    private val preferenceInjectionSuccessCounter = AtomicLong(0)
    private val preferenceInjectionTotalCounter = AtomicLong(0)

    // 响应时间统计
    private val responseTimeSum = AtomicLong(0)
    private val responseTimeCount = AtomicLong(0)
    private val responseTimeMax = AtomicLong(0)

    // 用户偏好覆盖率
    private val usersWithPreferencesCounter = AtomicLong(0)
    private val totalActiveUsersCounter = AtomicLong(0)

    /**
     * 记录目标匹配结果
     *
     * @param userGoal 用户的健身目标
     * @param aiModel AI 模型名称
     * @param matched 是否匹配成功
     */
    fun recordGoalMatch(
        userGoal: String,
        aiModel: String,
        matched: Boolean,
    ) {
        totalRequestCounter.incrementAndGet()

        if (matched) {
            goalMatchCounter.incrementAndGet()
            goalMatchByType.computeIfAbsent(userGoal) { AtomicLong(0) }.incrementAndGet()
        }

        Timber.d("记录目标匹配: goal=$userGoal, model=$aiModel, matched=$matched")
    }

    /**
     * 记录偏好注入结果
     *
     * @param success 是否注入成功
     */
    fun recordPreferenceInjection(success: Boolean) {
        preferenceInjectionTotalCounter.incrementAndGet()

        if (success) {
            preferenceInjectionSuccessCounter.incrementAndGet()
        }

        Timber.d("记录偏好注入: success=$success")
    }

    /**
     * 记录 AI 响应时间
     *
     * @param duration 响应时间
     * @param aiModel AI 模型名称
     * @param requestType 请求类型
     */
    fun recordAiResponseTime(
        duration: Duration,
        aiModel: String,
        requestType: String,
    ) {
        val durationMs = duration.toMillis()

        responseTimeSum.addAndGet(durationMs)
        responseTimeCount.incrementAndGet()

        // 更新最大响应时间
        var currentMax = responseTimeMax.get()
        while (durationMs > currentMax && !responseTimeMax.compareAndSet(currentMax, durationMs)) {
            currentMax = responseTimeMax.get()
        }

        Timber.d("记录AI响应时间: duration=${durationMs}ms, model=$aiModel, type=$requestType")
    }

    /**
     * 记录用户偏好覆盖率
     *
     * @param hasPreferences 用户是否设置了偏好
     */
    fun recordUserPreferenceCoverage(hasPreferences: Boolean) {
        totalActiveUsersCounter.incrementAndGet()

        if (hasPreferences) {
            usersWithPreferencesCounter.incrementAndGet()
        }

        Timber.d("记录用户偏好覆盖: hasPreferences=$hasPreferences")
    }

    /**
     * 分析目标匹配情况
     *
     * @param template AI 生成的训练模板
     * @param preference 用户偏好设置
     * @return 是否匹配用户目标
     */
    fun analyzeGoalMatch(
        template: TemplateDraft,
        preference: FitnessPreference,
    ): Boolean {
        val primaryGoal = preference.primaryGoal ?: return false

        // 获取目标相关的关键词
        val goalKeywords = getGoalKeywords(primaryGoal)

        // 分析模板内容
        val templateContent =
            buildString {
                append(template.name.lowercase())
                append(" ")
                append(template.description?.lowercase() ?: "")
                // 如果有练习列表，也加入分析
                template.exercises.forEach { exercise ->
                    append(" ")
                    // ExerciseSetDraft 没有 name 和 description 属性，跳过
                    // 这里可以根据实际的 ExerciseSetDraft 结构来调整
                }
            }

        // 检查关键词匹配
        val matchedKeywords =
            goalKeywords.filter { keyword ->
                templateContent.contains(keyword)
            }

        // 匹配率阈值：至少匹配50%的关键词
        val matchRate = matchedKeywords.size.toDouble() / goalKeywords.size
        val isMatched = matchRate >= 0.5

        Timber.d(
            "目标匹配分析: goal=${primaryGoal.name}, matchRate=$matchRate, matched=$isMatched, keywords=$matchedKeywords",
        )

        return isMatched
    }

    /**
     * 获取健身目标相关的关键词
     */
    private fun getGoalKeywords(goal: FitnessGoal): List<String> =
        when (goal) {
            FitnessGoal.STRENGTH -> listOf("力量", "杠铃", "深蹲", "硬拉", "卧推")
            FitnessGoal.WEIGHT_LOSS -> listOf("减脂", "减重", "燃脂", "有氧", "跑步")
            FitnessGoal.MUSCLE_GAIN -> listOf("增肌", "肌肉", "健美", "塑形", "重量")
        }

    /**
     * 获取当前指标快照
     * 用于监控系统查询
     */
    fun getMetricsSnapshot(): MetricsSnapshot {
        val totalRequests = totalRequestCounter.get()
        val goalMatches = goalMatchCounter.get()
        val goalMatchRate = if (totalRequests > 0) goalMatches.toDouble() / totalRequests else 0.0

        val totalInjections = preferenceInjectionTotalCounter.get()
        val successfulInjections = preferenceInjectionSuccessCounter.get()
        val injectionRate = if (totalInjections > 0) successfulInjections.toDouble() / totalInjections else 0.0

        val responseCount = responseTimeCount.get()
        val avgResponseTime = if (responseCount > 0) responseTimeSum.get().toDouble() / responseCount else 0.0

        val totalUsers = totalActiveUsersCounter.get()
        val usersWithPrefs = usersWithPreferencesCounter.get()
        val coverageRate = if (totalUsers > 0) usersWithPrefs.toDouble() / totalUsers else 0.0

        return MetricsSnapshot(
            goalMatchRate = goalMatchRate,
            preferenceInjectionRate = injectionRate,
            averageResponseTimeMs = avgResponseTime,
            maxResponseTimeMs = responseTimeMax.get().toDouble(),
            userPreferenceCoverageRate = coverageRate,
            totalRequests = totalRequests,
            goalMatchesByType = goalMatchByType.mapValues { it.value.get() },
        )
    }

    /**
     * 重置所有计数器
     * 仅用于测试
     */
    fun resetMetrics() {
        goalMatchCounter.set(0)
        totalRequestCounter.set(0)
        goalMatchByType.clear()
        preferenceInjectionSuccessCounter.set(0)
        preferenceInjectionTotalCounter.set(0)
        responseTimeSum.set(0)
        responseTimeCount.set(0)
        responseTimeMax.set(0)
        usersWithPreferencesCounter.set(0)
        totalActiveUsersCounter.set(0)

        Timber.d("指标计数器已重置")
    }
}

/**
 * 指标快照数据类
 */
data class MetricsSnapshot(
    val goalMatchRate: Double,
    val preferenceInjectionRate: Double,
    val averageResponseTimeMs: Double,
    val maxResponseTimeMs: Double,
    val userPreferenceCoverageRate: Double,
    val totalRequests: Long,
    val goalMatchesByType: Map<String, Long>,
)
