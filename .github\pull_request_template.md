# Pull Request Template - GymBro

## 📋 变更概述
**简述本次变更的目的和范围**

- [ ] 功能新增
- [ ] Bug修复
- [ ] 性能优化
- [ ] 重构
- [ ] 状态机相关变更 ⚠️

## 🔄 状态机变更审查 (如适用)

### 状态流程图
**请附上状态转换时序图 (PlantUML/Mermaid/截图)**
```mermaid
// 在此处添加状态机流程图
```

### 状态一致性保证
- [ ] **THINKING → STREAMING* → DONE|ERROR** 仅此一条主线，无平行分支
- [ ] 所有状态切换封装在单一StateReducer，UI不直接改列表
- [ ] 每次重试前彻底清理上一次Job，超时计时器与流取消绑定
- [ ] maxRetry超限后走ERROR，不可再次触发重试

### 并发与原子性
- [ ] `ensureThinkingMessageExists()`使用synchronized/Mutex，保证只创建一次
- [ ] `retryCount`、`message.id`在Retry-Scope内保持不变，成功后reset
- [ ] 使用CAS或其他机制确保状态变更原子性

## 🧪 测试覆盖

### 单元测试
- [ ] 模拟**两次超时→一次成功**，断言列表长度==1
- [ ] 覆盖所有状态转换路径
- [ ] 并发重试场景测试

### 集成测试
- [ ] Espresso/Compose UI Test在弱网下运行通过
- [ ] ChaosInterceptor随机超时测试通过
- [ ] 确保无重复Item出现

### 逆向测试链接
**请提供弱网环境测试脚本或录屏**
- 测试环境:
- 测试脚本:
- 录屏链接:

## 📊 指标对比

### UI状态指标
- [ ] 附上`ui_message_count`前后对比截图
- [ ] `retry_attempt`统计正确
- [ ] Prometheus/Grafana报表字段更新

**指标截图**:
<!-- 在此处粘贴before/after截图 -->

## 🔍 代码审查清单

### Repository层
- [ ] StreamEvent.Done必须唯一发送
- [ ] 超时/异常后状态重置完整
- [ ] 并发重试时保持原子性

### ViewModel层
- [ ] distinctUntilChanged粒度正确
- [ ] onCleared时取消流订阅
- [ ] 横竖屏重建时无重复订阅

### UI层
- [ ] DiffCallback以id判断同一条消息
- [ ] payload更新无跳帧
- [ ] RecyclerView缓存无重复绑定

### 异常处理
- [ ] 每个异常路径都有对应的状态清理
- [ ] 错误信息对用户友好
- [ ] 日志记录完整便于排查

## 📚 文档更新
- [ ] 状态机流程图更新到`docs/state_machine.md`
- [ ] API文档更新
- [ ] 架构决策记录(ADR)更新

## 🎬 演示材料
**请提供测试录像或GIF演示关键功能**
- 正常流程演示:
- 异常恢复演示:
- 弱网重试演示:

## ⚠️ 风险评估
- [ ] 向后兼容性确认
- [ ] 性能影响评估
- [ ] 第三方依赖影响分析

## 🔗 相关链接
- Issue: #
- 相关PR: #
- 设计文档:
- 监控仪表盘:

---

## 审查者检查项

### 代码质量 (Code Owner 1 - Domain)
- [ ] 业务逻辑正确
- [ ] 错误处理完整
- [ ] 状态管理原子性
- [ ] 性能考虑充分

### UI/UX (Code Owner 2 - UI)
- [ ] UI状态一致
- [ ] 用户体验流畅
- [ ] 无状态残留
- [ ] 动画效果合理

### 最终确认
- [ ] 所有CI检查通过
- [ ] ChaosInterceptor测试通过
- [ ] 指标监控就绪
- [ ] 准备灰度发布

---

**⚠️ 警告**: 涉及状态机的变更如果上述清单未完整填写，将被强制打回！
