<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 亮色基础主题，继承自 Material3 的 Light 主题 -->
    <style name="Theme.GymBro.Base.Light" parent="Theme.Material3.Light.NoActionBar">
        <!-- 定义 AndroidManifest 或 Activity 窗口本身可能需要的基础属性 -->
        <item name="android:windowBackground">@android:color/white</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        
        <!-- 与 LightColorScheme 匹配的颜色集 -->
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_onPrimaryContainer</item>
        <item name="colorSecondary">@color/md_theme_light_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_light_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_light_onSecondaryContainer</item>
        <item name="colorTertiary">@color/md_theme_light_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_light_onTertiary</item>
        <item name="android:colorBackground">@color/md_theme_light_background</item>
        <item name="colorOnBackground">@color/md_theme_light_onBackground</item>
        <item name="colorSurface">@color/md_theme_light_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_light_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_light_onSurfaceVariant</item>
        <item name="colorOutline">@color/md_theme_light_outline</item>
        <item name="colorError">@color/md_theme_light_error</item>
        <item name="colorOnError">@color/md_theme_light_onError</item>
        <item name="colorErrorContainer">@color/md_theme_light_errorContainer</item>
        <item name="colorOnErrorContainer">@color/md_theme_light_onErrorContainer</item>
        <item name="colorSurfaceInverse">@color/md_theme_light_inverseSurface</item>
        <item name="colorOnSurfaceInverse">@color/md_theme_light_inverseOnSurface</item>
        <item name="colorPrimaryInverse">@color/md_theme_light_inversePrimary</item>
        <item name="colorOutlineVariant">@color/md_theme_light_outlineVariant</item>
        <item name="scrimBackground">@color/md_theme_light_scrim</item>
    </style>

    <!-- 应用的亮色主题，供 AndroidManifest 使用 -->
    <style name="Theme.GymBro.Light" parent="Theme.GymBro.Base.Light" />
    
    <!-- 默认主题，供 AndroidManifest 使用 -->
    <style name="Theme.GymBro" parent="Theme.GymBro.Light" />
</resources>