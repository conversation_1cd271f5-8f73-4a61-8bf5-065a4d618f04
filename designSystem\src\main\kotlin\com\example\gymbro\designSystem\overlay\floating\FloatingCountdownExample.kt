package com.example.gymbro.designSystem.overlay.floating

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.ViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

/**
 * 悬浮倒计时使用示例
 *
 * 展示如何在应用中集成和使用轻量级悬浮倒计时功能
 */

/**
 * 示例ViewModel，展示如何在ViewModel中使用FloatingCountdownService
 */
@HiltViewModel
class FloatingCountdownExampleViewModel @Inject constructor(
    private val floatingCountdownService: FloatingCountdownService,
) : ViewModel() {

    val countdownState = floatingCountdownService.state

    fun startRestTimer(seconds: Int) {
        floatingCountdownService.startFloating(
            title = "组间休息",
            seconds = seconds,
            onComplete = {
                // 倒计时完成时的回调
                // 可以在这里添加震动、声音、通知等
            },
        )
    }

    fun startWorkoutTimer(exerciseName: String, seconds: Int) {
        floatingCountdownService.startFloating(
            title = "训练计时 - $exerciseName",
            seconds = seconds,
            onComplete = {
                // 训练计时完成
            },
        )
    }

    fun stopTimer() {
        floatingCountdownService.stopFloating()
    }

    fun togglePause() {
        floatingCountdownService.togglePause()
    }

    fun adjustTime(adjustment: Int) {
        floatingCountdownService.adjustTime(adjustment)
    }
}

/**
 * 悬浮倒计时示例界面
 */
@Composable
fun FloatingCountdownExample(
    viewModel: FloatingCountdownExampleViewModel = hiltViewModel(),
    modifier: Modifier = Modifier,
) {
    val countdownState by viewModel.countdownState.collectAsStateWithLifecycle()

    Box(modifier = modifier.fillMaxSize()) {
        // 主要内容
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            Text(
                text = "悬浮倒计时示例",
                style = MaterialTheme.typography.headlineMedium,
            )

            Text(
                text = "点击下面的按钮启动不同类型的倒计时",
                style = MaterialTheme.typography.bodyMedium,
            )

            // 控制按钮
            Row(
                horizontalArrangement = Arrangement.spacedBy(12.dp),
            ) {
                Button(
                    onClick = { viewModel.startRestTimer(60) },
                ) {
                    Text("60秒休息")
                }

                Button(
                    onClick = { viewModel.startRestTimer(90) },
                ) {
                    Text("90秒休息")
                }

                Button(
                    onClick = { viewModel.startWorkoutTimer("卧推", 120) },
                ) {
                    Text("2分钟训练")
                }
            }

            // 倒计时控制按钮（仅在有活动倒计时时显示）
            if (countdownState != null) {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.spacedBy(12.dp),
                    ) {
                        Text(
                            text = "倒计时控制",
                            style = MaterialTheme.typography.titleMedium,
                        )

                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                        ) {
                            Button(
                                onClick = { viewModel.togglePause() },
                            ) {
                                Text(if (countdownState?.isRunning == true) "暂停" else "继续")
                            }

                            Button(
                                onClick = { viewModel.adjustTime(-30) },
                            ) {
                                Text("-30秒")
                            }

                            Button(
                                onClick = { viewModel.adjustTime(30) },
                            ) {
                                Text("+30秒")
                            }

                            Button(
                                onClick = { viewModel.stopTimer() },
                            ) {
                                Text("停止")
                            }
                        }
                    }
                }
            }

            // 状态显示
            countdownState?.let { state ->
                Card(
                    modifier = Modifier.fillMaxWidth(),
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp),
                    ) {
                        Text(
                            text = "当前状态",
                            style = MaterialTheme.typography.titleMedium,
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text("标题: ${state.title}")
                        Text("剩余时间: ${state.formattedTime}")
                        Text("总时间: ${state.totalSeconds}秒")
                        Text("运行状态: ${if (state.isRunning) "运行中" else "已暂停"}")
                        Text("进度: ${(state.progress * 100).toInt()}%")
                    }
                }
            }
        }

        // 悬浮倒计时覆盖层
        // 注意：在实际应用中，FloatingCountdownOverlay应该在应用的根级别添加
        // 这里仅作为示例展示
        val floatingCountdownService = LocalFloatingCountdownService.current
        FloatingCountdownOverlay(
            service = floatingCountdownService,
        )
    }
}

/**
 * 简化版示例，展示如何在普通Composable中使用
 */
@Composable
fun SimpleFloatingCountdownExample(
    modifier: Modifier = Modifier,
) {
    // 获取悬浮倒计时服务
    val floatingCountdownService = LocalFloatingCountdownService.current

    Column(
        modifier = modifier.padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp),
    ) {
        Text(
            text = "简单悬浮倒计时示例",
            style = MaterialTheme.typography.headlineMedium,
        )

        Button(
            onClick = {
                floatingCountdownService.startFloating(
                    title = "快速休息",
                    seconds = 30,
                )
            },
        ) {
            Text("启动30秒倒计时")
        }

        Button(
            onClick = {
                floatingCountdownService.stopFloating()
            },
        ) {
            Text("停止倒计时")
        }
    }
}

@GymBroPreview
@Composable
private fun SimpleFloatingCountdownExamplePreview() {
    GymBroTheme {
        SimpleFloatingCountdownExample()
    }
}
