package com.example.gymbro.core.error.types

/**
 * 元数据键使用指南
 *
 * 本文档提供了如何使用元数据替代过度细分错误类型的建议和示例
 */
object MetadataKeyGuide {
    /**
     * 同步错误元数据映射指南
     *
     * 原来的同步错误类型:
     * - Sync.Failed
     * - Sync.DataConflict
     * - Sync.NetworkError
     * - Sync.AuthError
     * - Sync.ServerRejected
     * - Sync.LocalDataError
     * - Sync.VersionMismatch
     * - Sync.Timeout
     * - Sync.Cancelled
     * - Sync.Unknown
     *
     * 简化后，仅保留:
     * - Sync.Failed
     * - Sync.Conflict
     *
     * 使用元数据提供附加信息
     */
    object SyncGuide {
        /**
         * 网络错误导致的同步失败示例
         */
        fun networkSyncFailureExample() =
            mapOf(
                StandardKeys.ERROR_SUBTYPE.key to "network",
                StandardKeys.SYNC_TYPE.key to "incremental",
                StandardKeys.LAST_SYNC_TIME.key to System.currentTimeMillis() - 86400000, // 1天前
            )

        /**
         * 版本不匹配导致的冲突示例
         */
        fun versionMismatchExample() =
            mapOf(
                StandardKeys.ERROR_SUBTYPE.key to "version_mismatch",
                StandardKeys.CONFLICTING_ENTITY.key to "Workout",
                StandardKeys.ENTITY_ID.key to "123",
                StandardKeys.CONFLICT_REASON.key to "remote_newer",
            )
    }

    /**
     * 操作错误元数据映射指南
     *
     * 原来的操作错误类型:
     * - Operation.Create
     * - Operation.Read
     * - Operation.Update
     * - Operation.Delete
     * - Operation.Batch
     * - Operation.Export
     * - Operation.Import
     * - Operation.Search
     * - Operation.Validate
     * - Operation.Process
     * - Operation.Calculate
     * - Operation.Convert
     * - Operation.Upload
     *
     * 简化后，仅保留:
     * - Operation.Create
     * - Operation.Read
     * - Operation.Update
     * - Operation.Delete
     *
     * 使用元数据提供附加信息
     */
    object OperationGuide {
        /**
         * 批量操作失败示例
         */
        fun batchOperationExample() =
            mapOf(
                StandardKeys.ERROR_SUBTYPE.key to "batch",
                StandardKeys.ENTITY_NAME.key to "Workout",
                StandardKeys.ERROR_COUNT.key to 3,
            )

        /**
         * 导入操作失败示例
         */
        fun importOperationExample() =
            mapOf(
                StandardKeys.ERROR_SUBTYPE.key to "import",
                StandardKeys.SOURCE.key to "csv_file",
                StandardKeys.VALIDATION_ERRORS.key to
                    mapOf(
                        "row_3" to "Invalid date format",
                        "row_7" to "Missing required field",
                    ),
            )
    }

    /**
     * 订阅错误元数据映射指南
     *
     * 原来的订阅错误类型:
     * - Subscription.NotSubscribed
     * - Subscription.Expired
     * - Subscription.PaymentFailed
     * - Subscription.ValidationFailed
     * - Subscription.AlreadySubscribed
     * - Subscription.PlanNotFound
     * - Subscription.FreeTrialEnded
     * - Subscription.BillingIssue
     * - Subscription.Cancelled
     * - Subscription.GracePeriodEnded
     * - Subscription.AccessDenied
     * - Subscription.UpgradeFailed
     * - Subscription.DowngradeFailed
     * - Subscription.RefundIssue
     * - Subscription.StoreRejected
     * - ...等
     *
     * 简化后，仅保留:
     * - Subscription.NotSubscribed
     * - Subscription.Expired
     * - Subscription.PaymentFailed
     * - Subscription.ValidationFailed
     *
     * 使用元数据提供附加信息
     */
    object SubscriptionGuide {
        /**
         * 试用期结束示例
         */
        fun trialEndedExample() =
            mapOf(
                StandardKeys.ERROR_SUBTYPE.key to "trial_ended",
                StandardKeys.SUBSCRIPTION_PLAN.key to "premium",
                StandardKeys.EXPIRY_TIME.key to System.currentTimeMillis() - 86400000, // 1天前
            )

        /**
         * 计费问题示例
         */
        fun billingIssueExample() =
            mapOf(
                StandardKeys.ERROR_SUBTYPE.key to "billing_issue",
                StandardKeys.PAYMENT_METHOD.key to "credit_card",
                StandardKeys.GATEWAY_ERROR.key to "insufficient_funds",
            )

        /**
         * 升级失败示例
         */
        fun upgradeFailedExample() =
            mapOf(
                StandardKeys.ERROR_SUBTYPE.key to "upgrade_failed",
                StandardKeys.SUBSCRIPTION_PLAN.key to "basic",
                StandardKeys.SUBSCRIPTION_STATUS.key to "active",
                StandardKeys.TRANSACTION_ID.key to "txn_123456",
            )
    }

    /**
     * 支付错误元数据映射指南
     *
     * 原来的支付错误类型:
     * - Payment.ProcessingFailed
     * - Payment.InvalidPaymentMethod
     * - Payment.InsufficientFunds
     * - Payment.PaymentDeclined
     * - Payment.FraudDetected
     * - Payment.CardExpired
     * - Payment.PaymentCancelled
     * - Payment.RefundFailed
     * - Payment.InvalidAmount
     * - Payment.DuplicatePayment
     * - Payment.GatewayError
     * - Payment.PaymentMethodNotSupported
     * - ...等
     *
     * 简化后，仅保留:
     * - Payment.ProcessingFailed
     * - Payment.InvalidPaymentMethod
     *
     * 使用元数据提供附加信息
     */
    object PaymentGuide {
        /**
         * 资金不足示例
         */
        fun insufficientFundsExample() =
            mapOf(
                StandardKeys.ERROR_SUBTYPE.key to "insufficient_funds",
                StandardKeys.PAYMENT_METHOD.key to "credit_card",
                StandardKeys.AMOUNT.key to 99.99,
                StandardKeys.CURRENCY_CODE.key to "USD",
            )

        /**
         * 卡过期示例
         */
        fun cardExpiredExample() =
            mapOf(
                StandardKeys.ERROR_SUBTYPE.key to "card_expired",
                StandardKeys.PAYMENT_METHOD.key to "credit_card",
                StandardKeys.GATEWAY_ERROR.key to "expired_card",
            )

        /**
         * 网关错误示例
         */
        fun gatewayErrorExample() =
            mapOf(
                StandardKeys.ERROR_SUBTYPE.key to "gateway_error",
                StandardKeys.GATEWAY_ERROR.key to "connection_timeout",
                StandardKeys.TRANSACTION_ID.key to "txn_123456",
            )
    }

    /**
     * 使用示例：如何在错误构造函数中使用元数据
     */
    object UsageExamples {
        /**
         * 简化前：使用特定错误类型
         */
        fun beforeExample_specificErrorType() {
            // 示例代码，不会被实际调用
            /*
            val error = ModernDataError(
                operationName = "fetchUserProfile",
                errorType = GlobalErrorType.Network.Server, // 特定的服务器错误类型
                category = ErrorCategory.NETWORK,
                uiMessage = UiText.DynamicString("服务器错误，请稍后重试")
            )
             */
        }

        /**
         * 简化后：使用通用错误类型 + 元数据
         */
        fun afterExample_metadataBasedError() {
            // 示例代码，不会被实际调用
            /*
            val error = ModernDataError(
                operationName = "fetchUserProfile",
                errorType = GlobalErrorType.Network.Connection, // 使用更通用的连接错误类型
                category = ErrorCategory.NETWORK,
                uiMessage = UiText.DynamicString("服务器错误，请稍后重试"),
                metadataMap = mapOf(
                    StandardKeys.ERROR_SUBTYPE.key to "server",
                    StandardKeys.HTTP_STATUS.key to 500,
                    StandardKeys.SERVER_NAME.key to "api.example.com"
                )
            )
             */
        }

        /**
         * 使用元数据简化订阅错误的例子
         */
        fun subscriptionErrorExample() {
            // 示例代码，不会被实际调用
            /*
            // 原先会创建特定的错误类型 Subscription.FreeTrialEnded
            // 现在使用通用类型 + 元数据
            val error = ModernDataError(
                operationName = "checkFeatureAccess",
                errorType = GlobalErrorType.Subscription.NotSubscribed, // 通用类型：未订阅
                category = ErrorCategory.SUBSCRIPTION,
                uiMessage = UiText.DynamicString("您的免费试用期已结束，请升级以继续使用此功能"),
                metadataMap = mapOf(
                    StandardKeys.ERROR_SUBTYPE.key to "trial_ended",
                    StandardKeys.SUBSCRIPTION_PLAN.key to "premium",
                    StandardKeys.FEATURE_ID.key to "ai_coach",
                    StandardKeys.EXPIRY_TIME.key to 1642342800000L // 试用期结束时间
                )
            )
             */
        }
    }
}
