name: Architecture Guard (Dry-Run)
on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  GRADLE_OPTS: -Dorg.gradle.daemon=false -Dkotlin.incremental=false

jobs:
  arch-guard-dry-run:
    name: Architecture Baseline Check
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'

      - name: Cache Gradle dependencies
        uses: actions/cache@v3
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-

      - name: Make gradlew executable
        run: chmod +x ./gradlew

      # 架构规则检查 - DRY RUN模式
      - name: Feature->Data Dependency Check (DRY-RUN)
        continue-on-error: true
        run: |
          echo "🔍 检查 Features层->Data层违规依赖..."
          ./gradlew detektMain --include="FeatureDataDependencyRule" --max-issues=99999 || true

      - name: ViewModel Size Check (DRY-RUN)
        continue-on-error: true
        run: |
          echo "📏 检查 ViewModel 文件大小..."
          find . -name "*ViewModel.kt" -exec wc -l {} + | awk '$1 > 250 { print "⚠️ " $2 " 超过250行: " $1 "行" }'

      - name: Hardcoded Config Check (DRY-RUN)
        continue-on-error: true
        run: |
          echo "🔧 检查硬编码配置..."
          grep -r "http://" --include="*.kt" . && echo "⚠️ 发现硬编码HTTP URL" || true
          grep -r "https://" --include="*.kt" . && echo "⚠️ 发现硬编码HTTPS URL" || true
          grep -r "timeout.*=" --include="*.kt" . && echo "⚠️ 发现硬编码超时配置" || true

      - name: Stream ID Consistency Check (DRY-RUN)
        continue-on-error: true
        run: |
          echo "🔄 检查 Stream ID 一致性..."
          grep -r "UUID.randomUUID().toString()" --include="*.kt" . && echo "⚠️ 发现Legacy UUID生成" || true

      # 构建验证
      - name: Build Project (DRY-RUN)
        continue-on-error: true
        run: |
          echo "🏗️ 项目构建验证..."
          ./gradlew assembleDebug --stacktrace || true

      # 单元测试
      - name: Unit Tests (DRY-RUN)
        continue-on-error: true
        run: |
          echo "🧪 单元测试执行..."
          ./gradlew testDebugUnitTest --continue || true

      # 输出架构基线报告
      - name: Generate Architecture Baseline Report
        if: always()
        run: |
          echo "📊 架构基线报告 - $(date)" > arch-baseline-report.md
          echo "## 🚨 架构债务检测结果" >> arch-baseline-report.md
          echo "" >> arch-baseline-report.md

          echo "### Features->Data 依赖违规" >> arch-baseline-report.md
          find . -path "*/features/*" -name "*.kt" -exec grep -l "import.*data\..*" {} \; | head -10 >> arch-baseline-report.md || echo "暂无发现" >> arch-baseline-report.md
          echo "" >> arch-baseline-report.md

          echo "### 超大ViewModel文件 (>250行)" >> arch-baseline-report.md
          find . -name "*ViewModel.kt" -exec wc -l {} + | awk '$1 > 250 { print $2 " (" $1 "行)" }' >> arch-baseline-report.md || echo "暂无发现" >> arch-baseline-report.md
          echo "" >> arch-baseline-report.md

          echo "### 硬编码配置检测" >> arch-baseline-report.md
          grep -r "http" --include="*.kt" . | wc -l | awk '{ print "发现 " $1 " 处疑似硬编码URL" }' >> arch-baseline-report.md
          echo "" >> arch-baseline-report.md

          echo "### 报告生成时间: $(date)" >> arch-baseline-report.md

          cat arch-baseline-report.md

      - name: Upload Baseline Report
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: architecture-baseline-report
          path: arch-baseline-report.md

      # 通知机制 (可选)
      - name: Notify Baseline Status
        if: always()
        run: |
          echo "🔔 架构基线检查完成 - DRY-RUN模式"
          echo "📋 详细报告请查看 Artifacts: architecture-baseline-report"
          echo "🎯 下个Sprint将切换为 BLOCKING 模式"
