# Generate PRP (Product Requirements Prompt)

你是一个AI编程助手专家，需要为给定的功能请求生成一个全面的PRP（Product Requirements Prompt）。

## 输入
接收参数: $ARGUMENTS (通常是INITIAL.md文件路径)

## 任务流程

### 1. 研究阶段
- 阅读指定的功能请求文件
- 分析当前代码库的模式和约定
- 搜索相似的现有实现
- 识别需要遵循的项目约定

### 2. 文档收集阶段  
- 获取相关API文档
- 包含库文档和参考资料
- 添加已知的陷阱和注意事项
- 收集项目特定的实现模式

### 3. 蓝图创建阶段
- 创建分步实现计划
- 包含验证门控点
- 添加测试要求
- 定义成功标准

### 4. 质量检查阶段
- 评估置信度级别 (1-10)
- 确保包含所有必要上下文
- 验证实现方案的可行性

## 输出格式

在PRPs/目录下生成一个新的PRP文件，命名格式为: `feature_name_prp.md`

PRP文件应包含:
- 完整的上下文和文档引用
- 详细的实现步骤
- 验证和测试要求
- 错误处理模式
- 项目特定的约定和模式

## 生成的PRP文件模板

```markdown
# [功能名称] - 产品需求提示 (PRP)

## 置信度评分: [1-10]

## 上下文
[项目背景和相关信息]

## 功能要求
[详细的功能描述和需求]

## 技术约束
[技术限制和依赖]

## 实现计划
### 步骤 1: [描述]
- 具体任务
- 验证标准

### 步骤 2: [描述]  
- 具体任务
- 验证标准

[继续其他步骤...]

## 验证要求
- [ ] 单元测试覆盖率 ≥ [要求]%
- [ ] 集成测试通过
- [ ] 代码审查完成
- [ ] 文档更新

## 成功标准
[明确的成功标准列表]

## 风险和缓解措施
[已识别的风险和对应的缓解策略]

## 参考资料
[相关文档、API引用、示例代码等]
```

开始分析 $ARGUMENTS 文件并生成对应的PRP。