package com.example.gymbro.data.workout.plan.repository

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.extensions.safeCatch
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.workout.plan.dao.PlanDao
import com.example.gymbro.data.workout.plan.dao.PlanDayDao
import com.example.gymbro.data.workout.plan.dao.PlanTemplateDao
import com.example.gymbro.data.workout.plan.entity.PlanDayEntity
import com.example.gymbro.data.workout.plan.entity.PlanEntity
import com.example.gymbro.data.workout.plan.entity.PlanTemplateEntity
import com.example.gymbro.data.workout.plan.mapper.toDomain
import com.example.gymbro.data.workout.plan.mapper.toEntity
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.domain.workout.repository.PlanRepository
import com.example.gymbro.domain.workout.repository.TemplateRepository
import com.example.gymbro.domain.workout.usecase.template.TemplateVersionUseCase
import com.example.gymbro.shared.models.workout.PlanProgressStatus
import com.example.gymbro.shared.models.workout.WorkoutPlan
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * PlanRepository 实现 - PlanDB 数据访问
 *
 * 基于 05_Final_Database_Architecture.md 设计
 * 负责训练计划管理，统一使用 safeCatch 和 ModernResult 封装异常
 *
 * ✅ Plan层TemplateVersion适配 - Phase 2实现
 * - 集成TemplateVersionUseCase复用已有成功模式
 * - 实现Template ID到TemplateVersion ID的自动解析
 * - 保持向后兼容性和数据一致性
 */
@Singleton
class PlanRepositoryImpl
    @Inject
    constructor(
        private val planDao: PlanDao,
        private val planDayDao: PlanDayDao,
        private val planTemplateDao: PlanTemplateDao,
        private val templateRepository: TemplateRepository, // 新增：用于获取Plan内模板
        private val templateVersionUseCase: TemplateVersionUseCase, // ✅ 复用已有UseCase
        private val logger: Logger,
        @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    ) : PlanRepository {
        // ==================== Plan 基础操作 ====================

        override suspend fun getPlan(planId: String): ModernResult<WorkoutPlan> =
            withContext(ioDispatcher) {
                safeCatch {
                    Timber.d("获取计划: planId=$planId")
                    val planEntity = planDao.getPlanById(planId)
                        ?: throw NoSuchElementException("Plan not found: $planId")

                    // 加载完整的 dailySchedule
                    val planWithSchedule = loadPlanWithDailySchedule(planEntity)
                    planWithSchedule
                }
            }

        override suspend fun getUserPlans(userId: String): Flow<List<WorkoutPlan>> =
            planDao
                .getPlansByUser(userId)
                .map { entities ->
                    entities.map { loadPlanWithDailySchedule(it) }
                }.flowOn(ioDispatcher)

        override suspend fun savePlan(plan: WorkoutPlan): ModernResult<Unit> =
            withContext(ioDispatcher) {
                safeCatch {
                    Timber.d("保存计划: planId=${plan.id}")

                    // 1. 保存计划基本信息
                    planDao.insertPlan(plan.toEntity())

                    // 2. 删除现有的计划日和模板关联（如果是更新操作）
                    planDayDao.deleteAllPlanDays(plan.id)

                    // 3. 保存dailySchedule到PlanDayEntity和PlanTemplateEntity
                    plan.dailySchedule.forEach { (dayNumber, dayPlan) ->
                        val planDayId = "${plan.id}_day_$dayNumber"

                        // 保存PlanDayEntity
                        val planDayEntity =
                            PlanDayEntity(
                                id = planDayId,
                                planId = plan.id,
                                dayNumber = dayNumber,
                                isRestDay = dayPlan.isRestDay,
                                notes = dayPlan.dayNotes?.toString(),
                                orderIndex = dayNumber,
                                estimatedDuration = dayPlan.estimatedDuration,
                                isCompleted = dayPlan.progress == com.example.gymbro.shared.models.workout.PlanProgressStatus.COMPLETED,
                                progress = dayPlan.progress, // 🔥 关键：保存进度状态
                            )
                        planDayDao.insertPlanDay(planDayEntity)

                        // 保存PlanTemplateEntity（如果不是休息日且有模板）
                        if (!dayPlan.isRestDay && dayPlan.templateIds.isNotEmpty()) {
                            dayPlan.templateIds.forEachIndexed { index, templateId ->
                                val planTemplateEntity =
                                    PlanTemplateEntity(
                                        id = "${planDayId}_template_$index",
                                        planDayId = planDayId,
                                        templateId = templateId,
                                        order = index,
                                    )
                                planTemplateDao.insertPlanTemplate(planTemplateEntity)
                            }
                        }
                    }

                    Timber.d("成功保存计划及其dailySchedule: planId=${plan.id}, 天数=${plan.dailySchedule.size}")
                    Unit
                }
            }

        override suspend fun deletePlan(planId: String): ModernResult<Unit> =
            withContext(ioDispatcher) {
                safeCatch {
                    Timber.d("删除计划: planId=$planId")
                    planDao.deletePlan(planId)
                    // 由于设置了 CASCADE，相关的 PlanDay 和 PlanTemplate 会自动删除
                }
            }

        // ==================== ✅ Phase 2实现：TemplateVersion适配 ====================

        override suspend fun createPlanWithVersionResolution(plan: WorkoutPlan): ModernResult<WorkoutPlan> =
            withContext(ioDispatcher) {
                logger.d("创建Plan并解析TemplateVersion: ${plan.name}")

                // 1. 解析Plan中的Template ID为TemplateVersion ID
                resolvePlanTemplateVersions(plan)
                    .flatMap { resolvedPlan ->
                        // 2. 保存解析后的Plan
                        savePlan(resolvedPlan).map { resolvedPlan }
                    }
            }

        override suspend fun resolvePlanTemplateVersions(plan: WorkoutPlan): ModernResult<WorkoutPlan> =
            withContext(ioDispatcher) {
                // 收集所有需要解析的Template ID
                val allTemplateIds =
                    plan.dailySchedule.values
                        .filter { !it.isRestDay && it.templateIds.isNotEmpty() }
                        .flatMap { it.templateIds }
                        .distinct()

                if (allTemplateIds.isEmpty()) {
                    logger.d("Plan无需解析Template，跳过处理")
                    return@withContext ModernResult.Success(plan)
                }

                logger.d("解析${allTemplateIds.size}个Template为TemplateVersion")

                // ✅ 复用SessionManager的成功模式：批量获取TemplateVersion
                val versionResult =
                    templateVersionUseCase.batchGetLatestVersions.invoke(
                        TemplateVersionUseCase.BatchGetLatestVersionsParams(
                            templateIds = allTemplateIds,
                            autoCreateIfNotExist = true,
                        ),
                    )

                when (versionResult) {
                    is ModernResult.Success -> {
                        val versionMap = versionResult.data

                        // 3. 更新DayPlan中的引用
                        val resolvedSchedule =
                            plan.dailySchedule.mapValues { (_, dayPlan) ->
                                if (dayPlan.isRestDay || dayPlan.templateIds.isEmpty()) {
                                    dayPlan
                                } else {
                                    val resolvedVersionIds =
                                        dayPlan.templateIds.mapNotNull { templateId ->
                                            versionMap[templateId]?.id
                                        }

                                    // ✅ 使用Phase 1新增的工厂方法
                                    dayPlan.copy(
                                        templateVersionIds = resolvedVersionIds,
                                        // 保留原templateIds用于兼容性检查
                                        templateIds = dayPlan.templateIds,
                                    )
                                }
                            }

                        logger.d("成功解析${versionMap.size}个TemplateVersion")
                        ModernResult.Success(plan.copy(dailySchedule = resolvedSchedule))
                    }

                    is ModernResult.Error -> {
                        logger.e("解析TemplateVersion失败: ${versionResult.error}")
                        ModernResult.Error(
                            versionResult.error.copy(
                                operationName = "resolvePlanTemplateVersions",
                                uiMessage = UiText.DynamicString("解析训练模板版本失败"),
                            ),
                        )
                    }

                    is ModernResult.Loading -> {
                        ModernResult.Error(
                            com.example.gymbro.core.error.types.ModernDataError(
                                operationName = "resolvePlanTemplateVersions",
                                errorType = com.example.gymbro.core.error.types.GlobalErrorType.Operation.Timeout,
                                uiMessage = UiText.DynamicString("解析模板版本超时"),
                            ),
                        )
                    }
                }
            }

        override suspend fun getAllPlans(): List<WorkoutPlan> =
            withContext(ioDispatcher) {
                try {
                    logger.d("获取所有Plan用于数据迁移")
                    planDao.getAllPlans().map { it.toDomain() }
                } catch (e: Exception) {
                    logger.e("获取所有Plan失败", e)
                    emptyList()
                }
            }

        override suspend fun searchPlans(
            userId: String,
            query: String,
        ): Flow<List<WorkoutPlan>> =
            planDao
                .searchPlansByName(query)
                .map { entities ->
                    entities.map { it.toDomain() }
                }.flowOn(ioDispatcher)

        override suspend fun getActivePlans(userId: String): Flow<List<WorkoutPlan>> =
            planDao
                .getPlansByUser(userId)
                .map { entities ->
                    entities
                        .filter { it.isPublic } // 假设活跃计划是公开的计划
                        .map { it.toDomain() }
                }.flowOn(ioDispatcher)

        // ==================== Plan 分类操作 ====================

        override suspend fun getFavoritePlans(userId: String): Flow<List<WorkoutPlan>> =
            planDao
                .getFavoritePlans(userId)
                .map { entities ->
                    entities.map { it.toDomain() }
                }.flowOn(ioDispatcher)

        override suspend fun getAIGeneratedPlans(userId: String): Flow<List<WorkoutPlan>> =
            planDao
                .getAIGeneratedPlans(userId)
                .map { entities ->
                    entities.map { it.toDomain() }
                }.flowOn(ioDispatcher)

        override suspend fun togglePlanFavorite(planId: String): ModernResult<Unit> =
            withContext(ioDispatcher) {
                safeCatch {
                    Timber.d("切换计划收藏状态: planId=$planId")

                    // 获取当前计划
                    val currentPlan =
                        planDao.getPlanById(planId)
                            ?: throw NoSuchElementException("Plan not found: $planId")

                    // 切换收藏状态
                    val newFavoriteStatus = !currentPlan.isFavorite
                    planDao.updatePlanFavoriteStatus(planId, newFavoriteStatus)

                    Timber.d("计划收藏状态已更新: planId=$planId, isFavorite=$newFavoriteStatus")
                }
            }

        override suspend fun markPlanAsAIGenerated(planId: String): ModernResult<Unit> =
            withContext(ioDispatcher) {
                safeCatch {
                    Timber.d("标记计划为AI生成: planId=$planId")

                    // 直接更新AI生成状态
                    planDao.updatePlanAIGeneratedStatus(planId, true)

                    Timber.d("计划已标记为AI生成: planId=$planId")
                }
            }

        // ==================== Plan Calendar JSON 输出 ====================

        override suspend fun generatePlanCalendarJson(
            planId: String,
            startDate: String,
        ): ModernResult<com.example.gymbro.shared.models.workout.PlanCalendarData> =
            withContext(ioDispatcher) {
                safeCatch {
                    Timber.d("生成计划calendar.json: planId=$planId, startDate=$startDate")

                    // 获取计划数据
                    val planEntity =
                        planDao.getPlanById(planId)
                            ?: throw NoSuchElementException("Plan not found: $planId")

                    val workoutPlan = planEntity.toDomain()

                    // 转换为calendar.json格式
                    val calendarData = workoutPlan.toCalendarJson(startDate)

                    Timber.d("calendar.json生成成功: planId=$planId, entries=${calendarData.calendar_entries.size}")
                    calendarData
                }
            }

        override suspend fun getPlanTemplates(planId: String): ModernResult<List<WorkoutTemplate>> =
            withContext(ioDispatcher) {
                safeCatch {
                    Timber.d("获取计划内模板: planId=$planId")

                    // 1. 获取计划数据
                    val planEntity =
                        planDao.getPlanById(planId)
                            ?: throw NoSuchElementException("Plan not found: $planId")

                    val workoutPlan = planEntity.toDomain()

                    // 2. 提取所有模板ID
                    val templateIds =
                        workoutPlan.dailySchedule.values
                            .flatMap { it.templateIds }
                            .distinct()

                    if (templateIds.isEmpty()) {
                    Timber.d("计划内无模板")
                    return@safeCatch emptyList<WorkoutTemplate>()
                }

                // 3. 批量获取模板
                val templates = mutableListOf<WorkoutTemplate>()
                templateIds.forEach { templateId ->
                    when (val result = templateRepository.getTemplateById(templateId)) {
                        is ModernResult.Success -> {
                            result.data?.let { templates.add(it) }
                        }

                        is ModernResult.Error -> {
                            Timber.w("获取模板失败: templateId=$templateId, error=${result.error}")
                        }

                        is ModernResult.Loading -> {
                            // 忽略Loading状态
                        }
                    }
                }

                Timber.d("获取计划内模板成功: planId=$planId, templateCount=${templates.size}")
                templates
            }
        }

        // ==================== Progress Management ====================

        override suspend fun getPlanById(planId: String): WorkoutPlan? =
            withContext(ioDispatcher) {
                try {
                    planDao.getPlanById(planId)?.let { loadPlanWithDailySchedule(it) }
                } catch (e: Exception) {
                    logger.e("获取计划失败: planId=$planId", e)
                    null
                }
            }

        override suspend fun updateDayProgress(
            planId: String,
            dayNumber: Int,
            status: PlanProgressStatus
        ) {
            withContext(ioDispatcher) {
                logger.d("更新计划进度: planId=$planId, day=$dayNumber, status=$status")

                // 使用DAO的progress更新方法
                planDayDao.updatePlanDayProgressByNumber(planId, dayNumber, status)

                // 如果状态是COMPLETED，同时更新isCompleted字段以保持兼容性
                if (status == PlanProgressStatus.COMPLETED) {
                    planDayDao.updateCompletionStatus(planId, dayNumber, true)
                } else if (status == PlanProgressStatus.NOT_STARTED) {
                    planDayDao.updateCompletionStatus(planId, dayNumber, false)
                }
            }
        }

        override fun observePlan(planId: String): Flow<WorkoutPlan> =
            planDao.observePlanById(planId)
                .map { entity ->
                    entity?.let { loadPlanWithDailySchedule(it) }
                        ?: throw NoSuchElementException("Plan not found: $planId")
                }
                .flowOn(ioDispatcher)

        // ==================== 私有辅助方法 ====================

        /**
         * 加载计划并构建完整的 dailySchedule
         */
        private suspend fun loadPlanWithDailySchedule(planEntity: PlanEntity): WorkoutPlan {
            // 1. 获取所有 PlanDayEntity
            val planDays = planDayDao.getPlanDaysByPlanSync(planEntity.id)

            // 2. 获取所有 PlanTemplateEntity（需要为每个 planDay 单独查询）
            val allPlanTemplates = mutableListOf<PlanTemplateEntity>()
            planDays.forEach { planDay ->
                val dayTemplates = planTemplateDao.getPlanTemplatesByDaySync(planDay.id)
                allPlanTemplates.addAll(dayTemplates)
            }

            // 3. 按 dayNumber 分组构建 dailySchedule
            val dailySchedule = planDays.associate { planDay ->
                // 获取该天的所有模板ID
                val dayTemplates = allPlanTemplates
                    .filter { it.planDayId == planDay.id }
                    .sortedBy { it.order }
                    .map { it.templateId }

                // 构建 DayPlan
                val dayPlan = com.example.gymbro.shared.models.workout.DayPlan(
                    dayNumber = planDay.dayNumber,
                    templateIds = dayTemplates,
                    isRestDay = planDay.isRestDay,
                    dayNotes = planDay.notes,
                    orderIndex = planDay.orderIndex,
                    estimatedDuration = planDay.estimatedDuration,
                    progress = planDay.progress // 🔥 关键：正确映射进度状态
                )

                planDay.dayNumber to dayPlan
            }

            // 4. 构建完整的 WorkoutPlan
            return planEntity.toDomain().copy(dailySchedule = dailySchedule)
        }
}
