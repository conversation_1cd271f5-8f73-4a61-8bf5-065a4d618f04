package com.example.gymbro.data.ai.function

import com.example.gymbro.shared.models.ai.FunctionDefinition
import com.example.gymbro.shared.models.common.JsonHeader
import kotlinx.serialization.json.*

/**
 * 训练模板Function定义 - EntityWrapper统一表头规范
 * ✅ 升级：强制AI输出EntityWrapper格式，确保JSON表头统一
 * ✅ 增强：添加字段约束、过期保护、大小限制
 */
object WorkoutTemplateFunctionDef {
    /**
     * 生成训练模板Function定义 - EntityWrapper格式
     * 函数名：fc_generate_workout_template
     * 参数结构：EntityWrapper包装，包含统一表头
     */
    val fcGenerateWorkoutTemplate =
        FunctionDefinition(
            name = "fc_generate_workout_template",
            description = "生成训练模板，必须使用EntityWrapper格式包装",
            parameters = buildEntityWrapperTemplateSchema(),
        )

    /**
     * 构建EntityWrapper格式的训练模板JSON Schema
     *
     * EntityWrapper格式：
     * {
     *   "schemaVersion": "1.0.0",
     *   "entity": "TEMPLATE",
     *   "entityVersion": 1,
     *   "expires_at": 1718717400000,
     *   "payload": {
     *     "template_name": "Push Day (Upper Strength)",
     *     "exercises": [
     *       { "exercise_name": "Bench Press", "sets": 5, "reps": 5, "rest_sec": 90 }
     *     ]
     *   }
     * }
     */
    private fun buildEntityWrapperTemplateSchema(): JsonObject =
        buildJsonObject {
            put("type", JsonPrimitive("object"))
            put("additionalProperties", JsonPrimitive(false)) // ✅ 补丁1: 禁止额外字段
            putJsonObject("properties") {
                // EntityWrapper统一表头字段
                putJsonObject("schemaVersion") {
                    put("type", JsonPrimitive("string"))
                    put("enum", buildJsonArray { add(JsonPrimitive(JsonHeader.SCHEMA_VERSION)) })
                    put("description", JsonPrimitive("Schema版本号"))
                }

                putJsonObject("entity") {
                    put("type", JsonPrimitive("string"))
                    put("enum", buildJsonArray { add(JsonPrimitive("TEMPLATE")) })
                    put("description", JsonPrimitive("实体类型"))
                }

                putJsonObject("entityVersion") {
                    put("type", JsonPrimitive("integer"))
                    put("minimum", JsonPrimitive(1))
                    put("description", JsonPrimitive("实体版本号"))
                }

                putJsonObject("expires_at") {
                    // ✅ 补丁2: 幽灵调用保护
                    put("type", JsonPrimitive("integer"))
                    put("format", JsonPrimitive("utc-millis"))
                    put("description", JsonPrimitive("过期时间戳（毫秒）"))
                }

                // 业务数据载荷
                putJsonObject("payload") {
                    put("type", JsonPrimitive("object"))
                    put("additionalProperties", JsonPrimitive(false))
                    putJsonObject("properties") {
                        putJsonObject("template_name") {
                            put("type", JsonPrimitive("string"))
                            put("description", JsonPrimitive("训练模板名称"))
                        }

                        putJsonObject("exercises") {
                            put("type", JsonPrimitive("array"))
                            put("maxItems", JsonPrimitive(JsonHeader.Template.MAX_EXERCISES)) // ✅ 补丁3: 大小限制
                            putJsonObject("items") {
                                put("type", JsonPrimitive("object"))
                                putJsonObject("properties") {
                                    putJsonObject("exercise_name") {
                                        put("type", JsonPrimitive("string"))
                                        put("description", JsonPrimitive("动作名称（如：Bench Press, 卧推）"))
                                    }
                                    putJsonObject("sets") {
                                        put("type", JsonPrimitive("integer"))
                                        put("minimum", JsonPrimitive(1))
                                        put("maximum", JsonPrimitive(10))
                                        put("description", JsonPrimitive("训练组数"))
                                    }
                                    putJsonObject("reps") {
                                        put("type", JsonPrimitive("integer"))
                                        put("minimum", JsonPrimitive(1))
                                        put("maximum", JsonPrimitive(50))
                                        put("description", JsonPrimitive("每组次数"))
                                    }
                                    putJsonObject("target_weight") {
                                        put("type", JsonPrimitive("number"))
                                        put("description", JsonPrimitive("目标重量(kg)，可选"))
                                    }
                                    putJsonObject("rest_sec") {
                                        // ✅ 补丁4: 字段边界
                                        put("type", JsonPrimitive("integer"))
                                        put("minimum", JsonPrimitive(JsonHeader.Template.MIN_REST_SEC))
                                        put("maximum", JsonPrimitive(JsonHeader.Template.MAX_REST_SEC))
                                        put("description", JsonPrimitive("组间休息时间(秒)，可选"))
                                    }
                                }
                                putJsonArray("required") {
                                    add(JsonPrimitive("exercise_name"))
                                    add(JsonPrimitive("sets"))
                                    add(JsonPrimitive("reps"))
                                }
                            }
                        }

                        putJsonObject("description") {
                            put("type", JsonPrimitive("string"))
                            put("description", JsonPrimitive("模板描述，可选"))
                        }
                    }
                    putJsonArray("required") {
                        add(JsonPrimitive("template_name"))
                        add(JsonPrimitive("exercises"))
                    }
                }
            }
            putJsonArray("required") {
                add(JsonPrimitive("schemaVersion"))
                add(JsonPrimitive("entity"))
                add(JsonPrimitive("entityVersion"))
                add(JsonPrimitive("expires_at"))
                add(JsonPrimitive("payload"))
            }
        }

    /**
     * 生成训练模板Function定义
     * - 精简description节省Token
     * - 移除冗余字段
     * - 优化schema结构
     */
    val generateWorkoutFunction =
        FunctionDefinition(
            name = "generate_workout_template",
            description = "生成训练模板", // 精简描述，原版30字符 -> 6字符，节省80%
            parameters = buildWorkoutTemplateSchema(),
        )

    /**
     * 构建精简的训练模板JSON Schema
     * Token优化策略：
     * 1. 移除verbose描述
     * 2. 使用简化字段名
     * 3. 压缩required数组
     */
    private fun buildWorkoutTemplateSchema(): JsonObject =
        buildJsonObject {
            put("type", JsonPrimitive("object"))
            putJsonObject("properties") {
                putJsonObject("template") {
                    put("type", JsonPrimitive("object"))
                    putJsonObject("properties") {
                        // 模板名称 - 必需
                        putJsonObject("name") {
                            put("type", JsonPrimitive("string"))
                            put("description", JsonPrimitive("模板名")) // 3字符 vs 原版"模板名称"4字符
                        }

                        // 动作数组 - 核心字段
                        putJsonObject("exercises") {
                            put("type", JsonPrimitive("array"))
                            putJsonObject("items") {
                                put("type", JsonPrimitive("object"))
                                putJsonObject("properties") {
                                    // 动作代码 - 对应官方动作库
                                    putJsonObject("code") {
                                        put("type", JsonPrimitive("string"))
                                        put("description", JsonPrimitive("动作码")) // 3字符精简
                                    }
                                    // 组数
                                    putJsonObject("sets") {
                                        put("type", JsonPrimitive("integer"))
                                        put("minimum", JsonPrimitive(1))
                                        put("maximum", JsonPrimitive(10))
                                    }
                                    // 次数范围
                                    putJsonObject("reps") {
                                        put("type", JsonPrimitive("string"))
                                        put("description", JsonPrimitive("次数")) // 2字符精简
                                        put("pattern", JsonPrimitive("^\\d+(-\\d+)?$")) // 支持"8"或"8-12"格式
                                    }
                                    // 重量设置
                                    putJsonObject("weight") {
                                        put("type", JsonPrimitive("string"))
                                        put("description", JsonPrimitive("重量")) // 2字符精简
                                        putJsonArray("enum") {
                                            add(JsonPrimitive("bodyweight"))
                                            add(JsonPrimitive("percentage"))
                                            add(JsonPrimitive("fixed"))
                                        }
                                    }
                                }
                                putJsonArray("required") {
                                    add(JsonPrimitive("code"))
                                    add(JsonPrimitive("sets"))
                                    add(JsonPrimitive("reps"))
                                    add(JsonPrimitive("weight"))
                                }
                            }
                        }

                        // 可选字段 - 不在required中，减少Token
                        putJsonObject("description") {
                            put("type", JsonPrimitive("string"))
                            put("description", JsonPrimitive("描述")) // 2字符精简
                        }

                        putJsonObject("difficulty") {
                            put("type", JsonPrimitive("integer"))
                            put("minimum", JsonPrimitive(1))
                            put("maximum", JsonPrimitive(5))
                            put("description", JsonPrimitive("难度")) // 2字符精简
                        }
                    }
                    putJsonArray("required") {
                        add(JsonPrimitive("name"))
                        add(JsonPrimitive("exercises")) // 只有核心字段required，减少Token
                    }
                }
            }
            putJsonArray("required") {
                add(JsonPrimitive("template"))
            }
        }

    /**
     * 超级精简版Function定义 - 紧急降级使用
     * 当Token预算极其紧张时使用（<1K Token场景）
     */
    val minimalWorkoutFunction =
        FunctionDefinition(
            name = "gen_template", // 缩短函数名
            description = "生成模板", // 4字符极简
            parameters =
            buildJsonObject {
                put("type", JsonPrimitive("object"))
                putJsonObject("properties") {
                    putJsonObject("name") {
                        put("type", JsonPrimitive("string"))
                    }
                    putJsonObject("exercises") {
                        put("type", JsonPrimitive("array"))
                        putJsonObject("items") {
                            put("type", JsonPrimitive("object"))
                            putJsonObject("properties") {
                                putJsonObject("code") { put("type", JsonPrimitive("string")) }
                                putJsonObject("sets") { put("type", JsonPrimitive("integer")) }
                                putJsonObject("reps") { put("type", JsonPrimitive("string")) }
                            }
                            putJsonArray("required") {
                                add(JsonPrimitive("code"))
                                add(JsonPrimitive("sets"))
                                add(JsonPrimitive("reps"))
                            }
                        }
                    }
                }
                putJsonArray("required") {
                    add(JsonPrimitive("name"))
                    add(JsonPrimitive("exercises"))
                }
            },
        )

    /**
     * Token计算估算器
     * 用于监控Function定义的Token消耗
     */
    fun estimateTokenUsage(): TokenEstimate {
        val standardJson =
            kotlinx.serialization.json.Json.encodeToString(
                FunctionDefinition.serializer(),
                generateWorkoutFunction,
            )
        val minimalJson =
            kotlinx.serialization.json.Json.encodeToString(
                FunctionDefinition.serializer(),
                minimalWorkoutFunction,
            )

        return TokenEstimate(
            standardTokens = standardJson.length / 4, // 粗略估算：4字符≈1Token
            minimalTokens = minimalJson.length / 4,
            compressionRatio = (minimalJson.length.toDouble() / standardJson.length * 100).toInt(),
        )
    }
}

/**
 * Token使用估算结果
 */
data class TokenEstimate(
    val standardTokens: Int,
    val minimalTokens: Int,
    val compressionRatio: Int, // 压缩比例百分比
)
