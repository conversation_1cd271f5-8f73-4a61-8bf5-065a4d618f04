package com.example.gymbro.core.userdata.internal.datasource

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.userdata.api.model.UnifiedUserData
import com.example.gymbro.domain.model.auth.AuthUser
import com.example.gymbro.domain.profile.model.user.UserProfile
import kotlinx.coroutines.flow.Flow

/**
 * 用户本地数据源接口
 *
 * 根据 userdata-center保存方案.md 的设计，这个接口封装了所有的 Room 数据库操作，
 * 作为 UserDataCenter 与底层数据库之间的抽象层。
 *
 * 设计原则：
 * - 统一数据访问：所有 Room 操作都通过此接口进行
 * - 数据转换：负责 Room 实体与 Domain 模型之间的转换
 * - 事务支持：确保数据操作的原子性
 * - 响应式：通过 Flow 提供实时数据更新
 *
 * 核心职责：
 * - 管理用户认证数据的持久化（UserCacheEntity）
 * - 管理用户资料数据的持久化（UserProfileEntity）
 * - 提供统一的用户数据视图（UnifiedUserData）
 * - 处理数据合并和冲突解决
 */
interface UserLocalDataSource {

    /**
     * 观察统一用户数据
     *
     * 监听 Room 数据库中的用户数据变化，自动合并认证数据和资料数据，
     * 返回统一的用户数据流。
     *
     * @return Flow<UnifiedUserData?> 统一用户数据流，无数据时为 null
     */
    fun observeUserData(): Flow<UnifiedUserData?>

    /**
     * 观察指定用户的数据
     *
     * @param userId 用户ID
     * @return Flow<UnifiedUserData?> 指定用户的数据流
     */
    fun observeUserData(userId: String): Flow<UnifiedUserData?>

    /**
     * 获取当前用户数据
     *
     * 同步获取当前可用的用户数据，不等待数据库查询。
     *
     * @return ModernResult<UnifiedUserData?> 当前用户数据
     */
    suspend fun getCurrentUserData(): ModernResult<UnifiedUserData?>

    /**
     * 获取指定用户数据
     *
     * @param userId 用户ID
     * @return ModernResult<UnifiedUserData?> 指定用户数据
     */
    suspend fun getUserData(userId: String): ModernResult<UnifiedUserData?>

    /**
     * 保存认证数据
     *
     * 将认证用户信息保存到 Room 数据库中。如果用户已存在，则更新认证信息；
     * 如果是新用户，则创建新的用户记录。
     *
     * @param authUser 认证用户信息
     * @return ModernResult<Unit> 保存结果
     */
    suspend fun saveAuthData(authUser: AuthUser): ModernResult<Unit>

    /**
     * 保存用户资料数据
     *
     * 将用户资料信息保存到 Room 数据库中。
     *
     * @param userProfile 用户资料信息
     * @return ModernResult<Unit> 保存结果
     */
    suspend fun saveProfileData(userProfile: UserProfile): ModernResult<Unit>

    /**
     * 更新认证数据
     *
     * 更新现有用户的认证信息。
     *
     * @param authUser 认证用户信息
     * @return ModernResult<Unit> 更新结果
     */
    suspend fun updateAuthData(authUser: AuthUser): ModernResult<Unit>

    /**
     * 更新用户资料数据
     *
     * 更新现有用户的资料信息。
     *
     * @param userProfile 用户资料信息
     * @return ModernResult<Unit> 更新结果
     */
    suspend fun updateProfileData(userProfile: UserProfile): ModernResult<Unit>

    /**
     * 检查用户是否存在
     *
     * @param userId 用户ID
     * @return ModernResult<Boolean> 用户是否存在
     */
    suspend fun userExists(userId: String): ModernResult<Boolean>

    /**
     * 清除所有用户数据
     *
     * 删除所有本地用户数据，通常在用户登出时调用。
     *
     * @return ModernResult<Unit> 清除结果
     */
    suspend fun clearAllUserData(): ModernResult<Unit>

    /**
     * 清除指定用户数据
     *
     * @param userId 用户ID
     * @return ModernResult<Unit> 清除结果
     */
    suspend fun clearUserData(userId: String): ModernResult<Unit>

    /**
     * 获取所有用户ID列表
     *
     * 用于数据迁移和管理。
     *
     * @return ModernResult<List<String>> 用户ID列表
     */
    suspend fun getAllUserIds(): ModernResult<List<String>>

    /**
     * 数据库健康检查
     *
     * 检查数据库连接和数据完整性。
     *
     * @return ModernResult<Boolean> 健康状态
     */
    suspend fun healthCheck(): ModernResult<Boolean>
}
