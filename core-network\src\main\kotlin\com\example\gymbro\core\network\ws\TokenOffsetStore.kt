package com.example.gymbro.core.network.ws

/**
 * Token偏移量存储接口
 *
 * 用于断点续传功能，本地持久化最后接收的token索引
 * 支持断线重连后继续接收数据
 */
interface TokenOffsetStore {
    /**
     * 获取指定会话的最后token索引
     *
     * @param conversationId 会话ID
     * @return 最后接收的token索引，如果没有记录则返回0
     */
    suspend fun getLastTokenIndex(conversationId: String): Int

    /**
     * 更新指定会话的最后token索引
     *
     * @param conversationId 会话ID
     * @param index 最新的token索引
     */
    suspend fun updateLastTokenIndex(conversationId: String, index: Int)

    /**
     * 清除指定会话的token索引记录
     *
     * @param conversationId 会话ID
     */
    suspend fun clearTokenIndex(conversationId: String)

    /**
     * 清除所有token索引记录
     */
    suspend fun clearAllTokenIndexes()
}

/**
 * 内存实现的Token偏移量存储
 *
 * 简单的内存存储实现，用于测试和开发
 * 生产环境建议使用持久化存储（如SharedPreferences或数据库）
 */
class InMemoryTokenOffsetStore : TokenOffsetStore {
    private val tokenIndexMap = mutableMapOf<String, Int>()

    override suspend fun getLastTokenIndex(conversationId: String): Int {
        return tokenIndexMap[conversationId] ?: 0
    }

    override suspend fun updateLastTokenIndex(conversationId: String, index: Int) {
        tokenIndexMap[conversationId] = index
    }

    override suspend fun clearTokenIndex(conversationId: String) {
        tokenIndexMap.remove(conversationId)
    }

    override suspend fun clearAllTokenIndexes() {
        tokenIndexMap.clear()
    }
}
