This file is a merged representation of the entire codebase, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

<additional_info>

</additional_info>

</file_summary>

<directory_structure>
.claude/
  settings.json
  settings.local.json
ai_docs/
  cc_hooks.md
  hook_data_reference.md
examples/
  comprehensive-logging.json
  developer-workflow.json
  minimal-logging.json
  security-focused.json
scripts/
  custom_notifier.py
  format_code.sh
  log_full_data.py
  log_tool_use.py
  session_summary.py
  track_file_changes.py
  validate_bash_command.py
.gitignore
CLAUDE.md
README.md
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path=".claude/settings.local.json">
{
  "hooks": {
    "PreToolUse": [
      {
        "matcher": ".*",
        "hooks": [
          {
            "type": "command",
            "command": "python3 scripts/log_full_data.py pre"
          }
        ]
      },
      {
        "matcher": ".*",
        "hooks": [
          {
            "type": "command",
            "command": "python3 scripts/log_tool_use.py pre"
          }
        ]
      },
      {
        "matcher": "Bash",
        "hooks": [
          {
            "type": "command",
            "command": "python3 scripts/validate_bash_command.py"
          }
        ]
      },
      {
        "matcher": "Write|Edit|MultiEdit",
        "hooks": [
          {
            "type": "command",
            "command": "python3 scripts/track_file_changes.py"
          }
        ]
      }
    ],
    "PostToolUse": [
      {
        "matcher": ".*",
        "hooks": [
          {
            "type": "command",
            "command": "python3 scripts/log_full_data.py post"
          }
        ]
      },
      {
        "matcher": ".*",
        "hooks": [
          {
            "type": "command",
            "command": "python3 scripts/log_tool_use.py post"
          }
        ]
      },
      {
        "matcher": "Write|Edit|MultiEdit",
        "hooks": [
          {
            "type": "command",
            "command": "bash scripts/format_code.sh"
          }
        ]
      }
    ],
    "Notification": [
      {
        "matcher": "",
        "hooks": [
          {
            "type": "command",
            "command": "python3 scripts/log_full_data.py notification"
          },
          {
            "type": "command",
            "command": "python3 scripts/custom_notifier.py"
          }
        ]
      }
    ],
    "Stop": [
      {
        "matcher": "",
        "hooks": [
          {
            "type": "command",
            "command": "python3 scripts/log_full_data.py stop"
          },
          {
            "type": "command",
            "command": "python3 scripts/session_summary.py"
          }
        ]
      }
    ]
  }
}
</file>

<file path="ai_docs/cc_hooks.md">
# Claude Code Hooks Documentation

## Introduction

Claude Code hooks are user-defined shell commands that execute at various points in Claude Code's lifecycle. Hooks provide deterministic control over Claude Code's behavior, ensuring certain actions always happen rather than relying on the LLM to choose to run them.

Example use cases include:

- **Notifications**: Customize how you get notified when Claude Code is awaiting your input or permission to run something.
- **Automatic formatting**: Run `prettier` on .ts files, `gofmt` on .go files, etc. after every file edit.
- **Logging**: Track and count all executed commands for compliance or debugging.
- **Feedback**: Provide automated feedback when Claude Code produces code that does not follow your codebase conventions.
- **Custom permissions**: Block modifications to production files or sensitive directories.

By encoding these rules as hooks rather than prompting instructions, you turn suggestions into app-level code that executes every time it is expected to run.

> **⚠️ WARNING**: Hooks execute shell commands with your full user permissions without confirmation. You are responsible for ensuring your hooks are safe and secure. Anthropic is not liable for any data loss or system damage resulting from hook usage. Review Security Considerations.

## Quickstart

In this quickstart, you'll add a hook that logs the shell commands that Claude Code runs.

**Quickstart Prerequisite**: Install `jq` for JSON processing in the command line.

### Step 1: Open hooks configuration

Run the `/hooks` slash command and select the `PreToolUse` hook event.

`PreToolUse` hooks run before tool calls and can block them while providing Claude feedback on what to do differently.

### Step 2: Add a matcher

Select `+ Add new matcher…` to run your hook only on Bash tool calls.

Type `Bash` for the matcher.

### Step 3: Add the hook

Select `+ Add new hook…` and enter this command:

```bash
jq -r '"\(.tool_input.command) - \(.tool_input.description // "No description")"' >> ~/.claude/bash-command-log.txt
```

### Step 4: Save your configuration

For storage location, select `User settings` since you're logging to your home directory. This hook will then apply to all projects, not just your current project.

Then press Esc until you return to the REPL. Your hook is now registered!

### Step 5: Verify your hook

Run `/hooks` again or check `~/.claude/settings.json` to see your configuration:

```json
"hooks": {
  "PreToolUse": [
    {
      "matcher": "Bash",
      "hooks": [
        {
          "type": "command",
          "command": "jq -r '\"\\(.tool_input.command) - \\(.tool_input.description // \"No description\")\"' >> ~/.claude/bash-command-log.txt"
        }
      ]
    }
  ]
}
```

## Configuration

Claude Code hooks are configured in your settings files:

- `~/.claude/settings.json` - User settings
- `.claude/settings.json` - Project settings
- `.claude/settings.local.json` - Local project settings (not committed)
- Enterprise managed policy settings

### Structure

Hooks are organized by matchers, where each matcher can have multiple hooks:

```json
{
  "hooks": {
    "EventName": [
      {
        "matcher": "ToolPattern",
        "hooks": [
          {
            "type": "command",
            "command": "your-command-here"
          }
        ]
      }
    ]
  }
}
```

- **matcher**: Pattern to match tool names (only applicable for `PreToolUse` and `PostToolUse`)
  - Simple strings match exactly: `Write` matches only the Write tool
  - Supports regex: `Edit|Write` or `Notebook.*`
  - If omitted or empty string, hooks run for all matching events
- **hooks**: Array of commands to execute when the pattern matches
  - `type`: Currently only `"command"` is supported
  - `command`: The bash command to execute

## Hook Events

### PreToolUse

Runs after Claude creates tool parameters and before processing the tool call.

**Common matchers:**
- `Task` - Agent tasks
- `Bash` - Shell commands
- `Glob` - File pattern matching
- `Grep` - Content search
- `Read` - File reading
- `Edit`, `MultiEdit` - File editing
- `Write` - File writing
- `WebFetch`, `WebSearch` - Web operations

### PostToolUse

Runs immediately after a tool completes successfully.

Recognizes the same matcher values as PreToolUse.

### Notification

Runs when Claude Code sends notifications.

### Stop

Runs when Claude Code has finished responding.

## Hook Input

Hooks receive JSON data via stdin containing session information and event-specific data:

```typescript
{
  // Common fields
  session_id: string
  transcript_path: string  // Path to conversation JSON

  // Event-specific fields
  ...
}
```

### PreToolUse Input

The exact schema for `tool_input` depends on the tool.

```json
{
  "session_id": "abc123",
  "transcript_path": "~/.claude/projects/.../00893aaf-19fa-41d2-8238-13269b9b3ca0.jsonl",
  "tool_name": "Write",
  "tool_input": {
    "file_path": "/path/to/file.txt",
    "content": "file content"
  }
}
```

### PostToolUse Input

The exact schema for `tool_input` and `tool_response` depends on the tool.

```json
{
  "session_id": "abc123",
  "transcript_path": "~/.claude/projects/.../00893aaf-19fa-41d2-8238-13269b9b3ca0.jsonl",
  "tool_name": "Write",
  "tool_input": {
    "file_path": "/path/to/file.txt",
    "content": "file content"
  },
  "tool_response": {
    "filePath": "/path/to/file.txt",
    "success": true
  }
}
```

### Notification Input

```json
{
  "session_id": "abc123",
  "transcript_path": "~/.claude/projects/.../00893aaf-19fa-41d2-8238-13269b9b3ca0.jsonl",
  "message": "Task completed successfully",
  "title": "Claude Code"
}
```

### Stop Input

`stop_hook_active` is true when Claude Code is already continuing as a result of a stop hook. Check this value or process the transcript to prevent Claude Code from running indefinitely.

```json
{
  "session_id": "abc123",
  "transcript_path": "~/.claude/projects/.../00893aaf-19fa-41d2-8238-13269b9b3ca0.jsonl",
  "stop_hook_active": true
}
```

## Hook Output

There are two ways for hooks to return output back to Claude Code. The output communicates whether to block and any feedback that should be shown to Claude and the user.

### Simple: Exit Code

Hooks communicate status through exit codes, stdout, and stderr:

- **Exit code 0**: Success. `stdout` is shown to the user in transcript mode (CTRL-R).
- **Exit code 2**: Blocking error. `stderr` is fed back to Claude to process automatically. See per-hook-event behavior below.
- **Other exit codes**: Non-blocking error. `stderr` is shown to the user and execution continues.

#### Exit Code 2 Behavior

| Hook Event | Behavior |
|------------|----------|
| `PreToolUse` | Blocks the tool call, shows error to Claude |
| `PostToolUse` | Shows error to Claude (tool already ran) |
| `Notification` | N/A, shows stderr to user only |
| `Stop` | Blocks stoppage, shows error to Claude |

### Advanced: JSON Output

Hooks can return structured JSON in `stdout` for more sophisticated control:

#### Common JSON Fields

All hook types can include these optional fields:

```json
{
  "continue": true, // Whether Claude should continue after hook execution (default: true)
  "stopReason": "string" // Message shown when continue is false
  "suppressOutput": true, // Hide stdout from transcript mode (default: false)
}
```

If `continue` is false, Claude stops processing after the hooks run.

- For `PreToolUse`, this is different from `"decision": "block"`, which only blocks a specific tool call and provides automatic feedback to Claude.
- For `PostToolUse`, this is different from `"decision": "block"`, which provides automated feedback to Claude.
- For `Stop`, this takes precedence over any `"decision": "block"` output.
- In all cases, `"continue" = false` takes precedence over any `"decision": "block"` output.

`stopReason` accompanies `continue` with a reason shown to the user, not shown to Claude.

#### PreToolUse Decision Control

`PreToolUse` hooks can control whether a tool call proceeds.

- "approve" bypasses the permission system. `reason` is shown to the user but not to Claude.
- "block" prevents the tool call from executing. `reason` is shown to Claude.
- `undefined` leads to the existing permission flow. `reason` is ignored.

```json
{
  "decision": "approve" | "block" | undefined,
  "reason": "Explanation for decision"
}
```

#### PostToolUse Decision Control

`PostToolUse` hooks can control whether a tool call proceeds.

- "block" automatically prompts Claude with `reason`.
- `undefined` does nothing. `reason` is ignored.

```json
{
  "decision": "block" | undefined,
  "reason": "Explanation for decision"
}
```

#### Stop Decision Control

`Stop` hooks can control whether Claude must continue.

- "block" prevents Claude from stopping. You must populate `reason` for Claude to know how to proceed.
- `undefined` allows Claude to stop. `reason` is ignored.

```json
{
  "decision": "block" | undefined,
  "reason": "Must be provided when Claude is blocked from stopping"
}
```

#### JSON Output Example: Bash Command Editing

```python
#!/usr/bin/env python3
import json
import re
import sys

# Define validation rules as a list of (regex pattern, message) tuples
VALIDATION_RULES = [
    (
        r"\bgrep\b(?!.*\|)",
        "Use 'rg' (ripgrep) instead of 'grep' for better performance and features",
    ),
    (
        r"\bfind\s+\S+\s+-name\b",
        "Use 'rg --files | rg pattern' or 'rg --files -g pattern' instead of 'find -name' for better performance",
    ),
]

def validate_command(command: str) -> list[str]:
    issues = []
    for pattern, message in VALIDATION_RULES:
        if re.search(pattern, command):
            issues.append(message)
    return issues

try:
    input_data = json.load(sys.stdin)
except json.JSONDecodeError as e:
    print(f"Error: Invalid JSON input: {e}", file=sys.stderr)
    sys.exit(1)

tool_name = input_data.get("tool_name", "")
tool_input = input_data.get("tool_input", {})
command = tool_input.get("command", "")

if tool_name != "Bash" or not command:
    sys.exit(1)

# Validate the command
issues = validate_command(command)

if issues:
    for message in issues:
        print(f"• {message}", file=sys.stderr)
    # Exit code 2 blocks tool call and shows stderr to Claude
    sys.exit(2)
```

## Working with MCP Tools

Claude Code hooks work seamlessly with Model Context Protocol (MCP) tools. When MCP servers provide tools, they appear with a special naming pattern that you can match in your hooks.

### MCP Tool Naming

MCP tools follow the pattern `mcp__<server>__<tool>`, for example:

- `mcp__memory__create_entities` - Memory server's create entities tool
- `mcp__filesystem__read_file` - Filesystem server's read file tool
- `mcp__github__search_repositories` - GitHub server's search tool

### Configuring Hooks for MCP Tools

You can target specific MCP tools or entire MCP servers:

```json
{
  "hooks": {
    "PreToolUse": [
      {
        "matcher": "mcp__memory__.*",
        "hooks": [
          {
            "type": "command",
            "command": "echo 'Memory operation initiated' >> ~/mcp-operations.log"
          }
        ]
      },
      {
        "matcher": "mcp__.*__write.*",
        "hooks": [
          {
            "type": "command",
            "command": "/home/<USER>/scripts/validate-mcp-write.py"
          }
        ]
      }
    ]
  }
}
```

## Examples

### Code Formatting

Automatically format code after file modifications:

```json
{
  "hooks": {
    "PostToolUse": [
      {
        "matcher": "Write|Edit|MultiEdit",
        "hooks": [
          {
            "type": "command",
            "command": "/home/<USER>/scripts/format-code.sh"
          }
        ]
      }
    ]
  }
}
```

### Notification

Customize the notification that is sent when Claude Code requests permission or when the prompt input has become idle.

```json
{
  "hooks": {
    "Notification": [
      {
        "matcher": "",
        "hooks": [
          {
            "type": "command",
            "command": "python3 ~/my_custom_notifier.py"
          }
        ]
      }
    ]
  }
}
```

## Security Considerations

### Disclaimer

**USE AT YOUR OWN RISK**: Claude Code hooks execute arbitrary shell commands on your system automatically. By using hooks, you acknowledge that:

- You are solely responsible for the commands you configure
- Hooks can modify, delete, or access any files your user account can access
- Malicious or poorly written hooks can cause data loss or system damage
- Anthropic provides no warranty and assumes no liability for any damages resulting from hook usage
- You should thoroughly test hooks in a safe environment before production use

Always review and understand any hook commands before adding them to your configuration.

### Security Best Practices

Here are some key practices for writing more secure hooks:

1. **Validate and sanitize inputs** - Never trust input data blindly
2. **Always quote shell variables** - Use `"$VAR"` not `$VAR`
3. **Block path traversal** - Check for `..` in file paths
4. **Use absolute paths** - Specify full paths for scripts
5. **Skip sensitive files** - Avoid `.env`, `.git/`, keys, etc.

### Configuration Safety

Direct edits to hooks in settings files don't take effect immediately. Claude Code:

1. Captures a snapshot of hooks at startup
2. Uses this snapshot throughout the session
3. Warns if hooks are modified externally
4. Requires review in `/hooks` menu for changes to apply

This prevents malicious hook modifications from affecting your current session.

## Hook Execution Details

- **Timeout**: 60-second execution limit
- **Parallelization**: All matching hooks run in parallel
- **Environment**: Runs in current directory with Claude Code's environment
- **Input**: JSON via stdin
- **Output**:
  - PreToolUse/PostToolUse/Stop: Progress shown in transcript (Ctrl-R)
  - Notification: Logged to debug only (`--debug`)

## Debugging

To troubleshoot hooks:

1. Check if `/hooks` menu displays your configuration
2. Verify that your settings files are valid JSON
3. Test commands manually
4. Check exit codes
5. Review stdout and stderr format expectations
6. Ensure proper quote escaping

Progress messages appear in transcript mode (Ctrl-R) showing:

- Which hook is running
- Command being executed
- Success/failure status
- Output or error messages
</file>

<file path="ai_docs/hook_data_reference.md">
# Claude Code Hook Data Reference

## Overview

Claude Code passes JSON data to hooks via stdin. The data structure varies by hook type and tool being used. **PostToolUse** hooks have the most complete data as they include both input and response.

## Hook Data Availability

| Hook Type | Data Available | Best For |
|-----------|---------------|----------|
| **PreToolUse** | tool_name, tool_input, session_id, transcript_path | Validation, blocking, pre-processing |
| **PostToolUse** | All PreToolUse data + tool_response | Logging, analysis, post-processing |
| **Notification** | message, title, session_id, transcript_path | Custom notifications |
| **Stop** | session_id, transcript_path, stop_hook_active | Session cleanup, summaries |

## Common Fields (All Hooks)

```json
{
  "session_id": "string",          // Unique session identifier
  "transcript_path": "string"      // Path to conversation JSON file
}
```

## PreToolUse Data Structure

```json
{
  "session_id": "abc123",
  "transcript_path": "~/.claude/projects/.../00893aaf-19fa-41d2-8238-13269b9b3ca0.jsonl",
  "tool_name": "ToolName",
  "tool_input": {
    // Tool-specific fields (see below)
  }
}
```

## PostToolUse Data Structure

```json
{
  "session_id": "abc123",
  "transcript_path": "~/.claude/projects/.../00893aaf-19fa-41d2-8238-13269b9b3ca0.jsonl",
  "tool_name": "ToolName",
  "tool_input": {
    // Same as PreToolUse
  },
  "tool_response": {
    // Tool-specific response fields (see below)
  }
}
```

## Tool-Specific Data Structures

### Bash Tool

**tool_input:**
```json
{
  "command": "string",        // The bash command to execute
  "description": "string",    // Description of what the command does
  "timeout": "number"         // Optional timeout in milliseconds
}
```

**tool_response:**
```json
{
  "stdout": "string",         // Command output
  "stderr": "string",         // Error output
  "exit_code": "number",      // Exit code (0 = success)
  "timed_out": "boolean"      // Whether command timed out
}
```

### Read Tool

**tool_input:**
```json
{
  "file_path": "string",      // Absolute path to file
  "limit": "number",          // Optional line limit
  "offset": "number"          // Optional line offset
}
```

**tool_response:**
```json
{
  "content": "string",        // File content
  "lines_read": "number",     // Number of lines read
  "total_lines": "number",    // Total lines in file
  "truncated": "boolean"      // Whether content was truncated
}
```

### Write Tool

**tool_input:**
```json
{
  "file_path": "string",      // Absolute path to file
  "content": "string"         // Content to write
}
```

**tool_response:**
```json
{
  "success": "boolean",       // Whether write succeeded
  "file_path": "string",      // Path to written file
  "bytes_written": "number"   // Number of bytes written
}
```

### Edit Tool

**tool_input:**
```json
{
  "file_path": "string",      // Absolute path to file
  "old_string": "string",     // Text to replace
  "new_string": "string",     // Replacement text
  "replace_all": "boolean"    // Replace all occurrences
}
```

**tool_response:**
```json
{
  "success": "boolean",       // Whether edit succeeded
  "replacements": "number",   // Number of replacements made
  "file_path": "string"       // Path to edited file
}
```

### MultiEdit Tool

**tool_input:**
```json
{
  "file_path": "string",      // Absolute path to file
  "edits": [                  // Array of edit operations
    {
      "old_string": "string",
      "new_string": "string",
      "replace_all": "boolean"
    }
  ]
}
```

**tool_response:**
```json
{
  "success": "boolean",       // Whether all edits succeeded
  "edits_applied": "number",  // Number of edits applied
  "file_path": "string"       // Path to edited file
}
```

### Glob Tool

**tool_input:**
```json
{
  "pattern": "string",        // Glob pattern (e.g., "**/*.js")
  "path": "string"            // Optional directory path
}
```

**tool_response:**
```json
{
  "matches": ["string"],      // Array of matching file paths
  "match_count": "number"     // Number of matches found
}
```

### Grep Tool

**tool_input:**
```json
{
  "pattern": "string",        // Regular expression pattern
  "path": "string",           // Directory to search
  "include": "string"         // File pattern to include
}
```

**tool_response:**
```json
{
  "matches": [                // Array of matches
    {
      "file": "string",
      "line": "number",
      "content": "string"
    }
  ],
  "file_count": "number",     // Number of files with matches
  "match_count": "number"     // Total number of matches
}
```

### Task Tool

**tool_input:**
```json
{
  "description": "string",    // Task description
  "prompt": "string"          // Detailed task prompt
}
```

**tool_response:**
```json
{
  "task_id": "string",        // Unique task identifier
  "status": "string",         // Task status
  "result": "string"          // Task result/output
}
```

### TodoWrite Tool

**tool_input:**
```json
{
  "todos": [                  // Array of todo items
    {
      "id": "string",
      "content": "string",
      "status": "string",     // "pending", "in_progress", "completed"
      "priority": "string"    // "high", "medium", "low"
    }
  ]
}
```

**tool_response:**
```json
{
  "success": "boolean",       // Whether update succeeded
  "todo_count": "number"      // Number of todos in list
}
```

### WebFetch Tool

**tool_input:**
```json
{
  "url": "string",            // URL to fetch
  "prompt": "string"          // Prompt for content analysis
}
```

**tool_response:**
```json
{
  "content": "string",        // Fetched/analyzed content
  "url": "string",            // Actual URL fetched
  "status_code": "number"     // HTTP status code
}
```

### WebSearch Tool

**tool_input:**
```json
{
  "query": "string",          // Search query
  "allowed_domains": ["string"], // Optional domain filter
  "blocked_domains": ["string"]  // Optional domain blocklist
}
```

**tool_response:**
```json
{
  "results": [                // Array of search results
    {
      "title": "string",
      "url": "string",
      "snippet": "string"
    }
  ],
  "result_count": "number"    // Number of results
}
```

### NotebookRead Tool

**tool_input:**
```json
{
  "notebook_path": "string",  // Path to .ipynb file
  "cell_id": "string"         // Optional specific cell ID
}
```

**tool_response:**
```json
{
  "cells": [                  // Array of notebook cells
    {
      "id": "string",
      "type": "string",       // "code" or "markdown"
      "source": "string",
      "outputs": []           // Cell outputs
    }
  ],
  "cell_count": "number"      // Number of cells
}
```

### NotebookEdit Tool

**tool_input:**
```json
{
  "notebook_path": "string",  // Path to .ipynb file
  "cell_id": "string",        // Cell to edit
  "new_source": "string",     // New cell content
  "cell_type": "string",      // "code" or "markdown"
  "edit_mode": "string"       // "replace", "insert", "delete"
}
```

**tool_response:**
```json
{
  "success": "boolean",       // Whether edit succeeded
  "cell_id": "string",        // ID of edited cell
  "notebook_path": "string"   // Path to notebook
}
```

### MCP Tool Pattern

MCP tools follow the naming pattern `mcp__<server>__<tool>`. Their data structures vary by the specific MCP server and tool.

**Example tool_input:**
```json
{
  // Varies by MCP tool
  "custom_field": "value"
}
```

**Example tool_response:**
```json
{
  // Varies by MCP tool
  "result": "value"
}
```

## Notification Hook Data

```json
{
  "session_id": "abc123",
  "transcript_path": "~/.claude/projects/.../00893aaf-19fa-41d2-8238-13269b9b3ca0.jsonl",
  "message": "string",        // Notification message
  "title": "string"           // Notification title (usually "Claude Code")
}
```

## Stop Hook Data

```json
{
  "session_id": "abc123",
  "transcript_path": "~/.claude/projects/.../00893aaf-19fa-41d2-8238-13269b9b3ca0.jsonl",
  "stop_hook_active": "boolean"  // true if already in stop hook (prevent loops)
}
```

## Important Notes

1. **Data Availability**: PostToolUse hooks have the most complete data (input + response)
2. **Field Variability**: Not all fields are always present; use defensive coding
3. **MCP Tools**: Data structures vary significantly for MCP tools
4. **Error States**: tool_response may contain error information instead of success data
5. **Async Operations**: Some tools may have incomplete responses in PostToolUse

## Debugging Tips

To see actual data structures:
1. Use the `log_full_data.py` script to capture all hook data
2. Check `logs/tool-data-structures.jsonl` for raw data
3. Review `logs/tool-data-*.json` for pretty-printed examples
4. Use `jq` to explore the JSON structure interactively

## Example: Exploring Hook Data

```bash
# See all tool names used
cat logs/tool-data-structures.jsonl | jq -r '.parsed_data.tool_name' | sort | uniq

# See all fields for a specific tool
cat logs/tool-data-structures.jsonl | jq 'select(.parsed_data.tool_name == "Bash")'

# Extract all bash commands
cat logs/tool-data-structures.jsonl | jq -r 'select(.parsed_data.tool_name == "Bash") | .parsed_data.tool_input.command'
```
</file>

<file path="examples/comprehensive-logging.json">
{
  "hooks": {
    "PreToolUse": [
      {
        "matcher": "Task",
        "hooks": [
          {
            "type": "command",
            "command": "jq -r '\"[TASK_START] \\(.session_id) | \\(.tool_input | tostring)\"' >> logs/all-tools.jsonl"
          }
        ]
      },
      {
        "matcher": "Bash",
        "hooks": [
          {
            "type": "command",
            "command": "jq -r '{timestamp: now | strftime(\"%Y-%m-%dT%H:%M:%SZ\"), event: \"bash_pre\", session: .session_id, command: .tool_input.command, description: .tool_input.description}' >> logs/bash-audit.jsonl"
          }
        ]
      },
      {
        "matcher": "Glob|Grep",
        "hooks": [
          {
            "type": "command",
            "command": "jq -r '{timestamp: now | strftime(\"%Y-%m-%dT%H:%M:%SZ\"), event: \"search_pre\", session: .session_id, tool: .tool_name, pattern: (.tool_input.pattern // .tool_input.query)}' >> logs/search-audit.jsonl"
          }
        ]
      },
      {
        "matcher": "Read|NotebookRead",
        "hooks": [
          {
            "type": "command",
            "command": "jq -r '{timestamp: now | strftime(\"%Y-%m-%dT%H:%M:%SZ\"), event: \"read_pre\", session: .session_id, file: (.tool_input.file_path // .tool_input.notebook_path)}' >> logs/file-access.jsonl"
          }
        ]
      },
      {
        "matcher": "Write|Edit|MultiEdit|NotebookEdit",
        "hooks": [
          {
            "type": "command",
            "command": "jq -r '{timestamp: now | strftime(\"%Y-%m-%dT%H:%M:%SZ\"), event: \"write_pre\", session: .session_id, tool: .tool_name, file: (.tool_input.file_path // .tool_input.notebook_path)}' >> logs/file-modifications.jsonl"
          }
        ]
      },
      {
        "matcher": "WebFetch|WebSearch",
        "hooks": [
          {
            "type": "command",
            "command": "jq -r '{timestamp: now | strftime(\"%Y-%m-%dT%H:%M:%SZ\"), event: \"web_pre\", session: .session_id, tool: .tool_name, url: .tool_input.url, query: .tool_input.query}' >> logs/web-access.jsonl"
          }
        ]
      },
      {
        "matcher": "TodoWrite",
        "hooks": [
          {
            "type": "command",
            "command": "jq -r '{timestamp: now | strftime(\"%Y-%m-%dT%H:%M:%SZ\"), event: \"todo_update\", session: .session_id, todos: .tool_input.todos}' >> logs/todo-tracking.jsonl"
          }
        ]
      },
      {
        "matcher": "mcp__.*",
        "hooks": [
          {
            "type": "command",
            "command": "jq -r '{timestamp: now | strftime(\"%Y-%m-%dT%H:%M:%SZ\"), event: \"mcp_pre\", session: .session_id, tool: .tool_name, input: .tool_input}' >> logs/mcp-tools.jsonl"
          }
        ]
      }
    ],
    "PostToolUse": [
      {
        "matcher": ".*",
        "hooks": [
          {
            "type": "command",
            "command": "jq -r '{timestamp: now | strftime(\"%Y-%m-%dT%H:%M:%SZ\"), event: \"tool_post\", session: .session_id, tool: .tool_name, success: (.tool_response.success // true), response: .tool_response}' >> logs/all-tools-results.jsonl"
          }
        ]
      },
      {
        "matcher": "Bash",
        "hooks": [
          {
            "type": "command",
            "command": "jq -r 'if (.tool_response.exit_code // 0) != 0 then {timestamp: now | strftime(\"%Y-%m-%dT%H:%M:%SZ\"), event: \"bash_error\", session: .session_id, command: .tool_input.command, exit_code: .tool_response.exit_code, stderr: .tool_response.stderr} else empty end' >> logs/bash-errors.jsonl"
          }
        ]
      }
    ],
    "Notification": [
      {
        "matcher": "",
        "hooks": [
          {
            "type": "command",
            "command": "jq -r '{timestamp: now | strftime(\"%Y-%m-%dT%H:%M:%SZ\"), event: \"notification\", session: .session_id, title: .title, message: .message}' >> logs/notifications.jsonl"
          },
          {
            "type": "command",
            "command": "echo \"$(date '+%Y-%m-%d %H:%M:%S') | $(jq -r '.message')\" | tee -a logs/notification-history.txt"
          }
        ]
      }
    ],
    "Stop": [
      {
        "matcher": "",
        "hooks": [
          {
            "type": "command",
            "command": "jq -r '{timestamp: now | strftime(\"%Y-%m-%dT%H:%M:%SZ\"), event: \"session_stop\", session: .session_id, transcript: .transcript_path, stop_hook_active: .stop_hook_active}' >> logs/sessions.jsonl"
          },
          {
            "type": "command",
            "command": "echo \"\\n=== Session Summary ===\\nSession ID: $(jq -r '.session_id')\\nTranscript: $(jq -r '.transcript_path')\\nTime: $(date)\\n=====================\\n\" >> logs/session-summaries.txt"
          }
        ]
      }
    ]
  }
}
</file>

<file path="examples/developer-workflow.json">
{
  "hooks": {
    "PreToolUse": [
      {
        "matcher": "Write|Edit|MultiEdit",
        "hooks": [
          {
            "type": "command",
            "command": "python3 scripts/check_branch.py"
          }
        ]
      }
    ],
    "PostToolUse": [
      {
        "matcher": "Write|Edit|MultiEdit",
        "hooks": [
          {
            "type": "command",
            "command": "python3 scripts/auto_format.py"
          },
          {
            "type": "command",
            "command": "python3 scripts/run_linters.py"
          },
          {
            "type": "command",
            "command": "python3 scripts/update_tests.py"
          }
        ]
      },
      {
        "matcher": "Bash",
        "hooks": [
          {
            "type": "command",
            "command": "jq -r 'select(.tool_input.command | test(\"npm|yarn|pnpm\")) | {timestamp: now | strftime(\"%Y-%m-%dT%H:%M:%SZ\"), package_command: .tool_input.command}' >> logs/package-commands.jsonl"
          }
        ]
      }
    ],
    "Notification": [
      {
        "matcher": "",
        "hooks": [
          {
            "type": "command",
            "command": "python3 scripts/slack_notifier.py"
          }
        ]
      }
    ],
    "Stop": [
      {
        "matcher": "",
        "hooks": [
          {
            "type": "command",
            "command": "python3 scripts/commit_reminder.py"
          },
          {
            "type": "command",
            "command": "python3 scripts/test_coverage_report.py"
          }
        ]
      }
    ]
  }
}
</file>

<file path="examples/minimal-logging.json">
{
  "hooks": {
    "PreToolUse": [
      {
        "matcher": ".*",
        "hooks": [
          {
            "type": "command",
            "command": "echo \"[$(date)] Tool: $TOOL_NAME\" >> simple.log || true"
          }
        ]
      }
    ]
  }
}
</file>

<file path="examples/security-focused.json">
{
  "hooks": {
    "PreToolUse": [
      {
        "matcher": "Bash",
        "hooks": [
          {
            "type": "command",
            "command": "python3 scripts/security_validator.py"
          }
        ]
      },
      {
        "matcher": "Write|Edit|MultiEdit",
        "hooks": [
          {
            "type": "command",
            "command": "python3 scripts/file_security_check.py"
          }
        ]
      },
      {
        "matcher": "WebFetch|WebSearch",
        "hooks": [
          {
            "type": "command",
            "command": "python3 scripts/url_validator.py"
          }
        ]
      }
    ],
    "PostToolUse": [
      {
        "matcher": "Bash",
        "hooks": [
          {
            "type": "command",
            "command": "jq -r 'if (.tool_response.exit_code // 0) != 0 then {timestamp: now | strftime(\"%Y-%m-%dT%H:%M:%SZ\"), alert: \"COMMAND_FAILED\", command: .tool_input.command, exit_code: .tool_response.exit_code, stderr: .tool_response.stderr} else empty end' >> logs/security-alerts.jsonl"
          }
        ]
      },
      {
        "matcher": ".*",
        "hooks": [
          {
            "type": "command",
            "command": "python3 scripts/audit_logger.py"
          }
        ]
      }
    ]
  }
}
</file>

<file path="scripts/custom_notifier.py">
#!/usr/bin/env python3
"""
Custom notification handler for Claude Code.
Logs notifications and can be extended to send to various notification systems.
"""

import json
import sys
import os
from datetime import datetime
from pathlib import Path

def main():
    try:
        # Read notification data from stdin
        hook_input = json.load(sys.stdin)
    except json.JSONDecodeError:
        sys.exit(0)
    
    # Extract notification details
    message = hook_input.get("message", "")
    title = hook_input.get("title", "Claude Code")
    session_id = hook_input.get("session_id", "unknown")
    
    # Ensure logs directory exists
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Log notification to file
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_entry = f"[{timestamp}] [{session_id}] {title}: {message}\n"
    
    with open(log_dir / "notifications-detailed.log", "a") as f:
        f.write(log_entry)
    
    # Platform-specific notification (optional)
    # You can uncomment and customize these based on your platform:
    
    # macOS notification using osascript
    if sys.platform == "darwin" and os.path.exists("/usr/bin/osascript"):
        # Escape quotes for AppleScript
        escaped_message = message.replace('"', '\\"')
        escaped_title = title.replace('"', '\\"')
        # Uncomment to enable desktop notifications:
        # os.system(f'osascript -e \'display notification "{escaped_message}" with title "{escaped_title}"\'')
    
    # Linux notification using notify-send
    elif sys.platform.startswith("linux") and os.path.exists("/usr/bin/notify-send"):
        # Uncomment to enable desktop notifications:
        # os.system(f'notify-send "{title}" "{message}"')
        pass
    
    # Windows notification (requires win10toast)
    elif sys.platform == "win32":
        # Uncomment and install win10toast to enable:
        # try:
        #     from win10toast import ToastNotifier
        #     toaster = ToastNotifier()
        #     toaster.show_toast(title, message, duration=10)
        # except ImportError:
        #     pass
        pass
    
    sys.exit(0)

if __name__ == "__main__":
    main()
</file>

<file path="scripts/format_code.sh">
#!/bin/bash
#
# Auto-format code based on file extension.
# This script runs after file modifications to ensure consistent formatting.
#

# Read the hook input
HOOK_INPUT=$(cat)

# Extract file path and tool name using jq
FILE_PATH=$(echo "$HOOK_INPUT" | jq -r '.tool_input.file_path // .tool_input.notebook_path // ""')
TOOL_NAME=$(echo "$HOOK_INPUT" | jq -r '.tool_name // ""')

# Only process file modifications
if [[ ! "$TOOL_NAME" =~ ^(Write|Edit|MultiEdit)$ ]]; then
    exit 0
fi

# Skip if no file path
if [ -z "$FILE_PATH" ] || [ "$FILE_PATH" = "null" ]; then
    exit 0
fi

# Log the formatting attempt
echo "[$(date '+%Y-%m-%d %H:%M:%S')] Checking format for: $FILE_PATH" >> logs/formatting.log

# Get file extension
EXT="${FILE_PATH##*.}"
BASENAME=$(basename "$FILE_PATH")

# Skip formatting for certain files
case "$BASENAME" in
    .gitignore|.env*|*.md|*.txt|*.log|*.json|*.yml|*.yaml)
        echo "  Skipping format for $BASENAME" >> logs/formatting.log
        exit 0
        ;;
esac

# Format based on extension
case "$EXT" in
    py)
        # Python formatting
        if command -v black &> /dev/null; then
            echo "  Running black on $FILE_PATH" >> logs/formatting.log
            black "$FILE_PATH" 2>> logs/formatting-errors.log || true
        elif command -v autopep8 &> /dev/null; then
            echo "  Running autopep8 on $FILE_PATH" >> logs/formatting.log
            autopep8 --in-place "$FILE_PATH" 2>> logs/formatting-errors.log || true
        fi
        
        # Python import sorting
        if command -v isort &> /dev/null; then
            echo "  Running isort on $FILE_PATH" >> logs/formatting.log
            isort "$FILE_PATH" 2>> logs/formatting-errors.log || true
        fi
        ;;
        
    js|jsx|ts|tsx)
        # JavaScript/TypeScript formatting
        if command -v prettier &> /dev/null; then
            echo "  Running prettier on $FILE_PATH" >> logs/formatting.log
            prettier --write "$FILE_PATH" 2>> logs/formatting-errors.log || true
        elif command -v eslint &> /dev/null; then
            echo "  Running eslint --fix on $FILE_PATH" >> logs/formatting.log
            eslint --fix "$FILE_PATH" 2>> logs/formatting-errors.log || true
        fi
        ;;
        
    go)
        # Go formatting
        if command -v gofmt &> /dev/null; then
            echo "  Running gofmt on $FILE_PATH" >> logs/formatting.log
            gofmt -w "$FILE_PATH" 2>> logs/formatting-errors.log || true
        fi
        
        if command -v goimports &> /dev/null; then
            echo "  Running goimports on $FILE_PATH" >> logs/formatting.log
            goimports -w "$FILE_PATH" 2>> logs/formatting-errors.log || true
        fi
        ;;
        
    rs)
        # Rust formatting
        if command -v rustfmt &> /dev/null; then
            echo "  Running rustfmt on $FILE_PATH" >> logs/formatting.log
            rustfmt "$FILE_PATH" 2>> logs/formatting-errors.log || true
        fi
        ;;
        
    sh|bash)
        # Shell script formatting
        if command -v shfmt &> /dev/null; then
            echo "  Running shfmt on $FILE_PATH" >> logs/formatting.log
            shfmt -w "$FILE_PATH" 2>> logs/formatting-errors.log || true
        fi
        ;;
        
    *)
        echo "  No formatter configured for .$EXT files" >> logs/formatting.log
        ;;
esac

# Always exit successfully to not block operations
exit 0
</file>

<file path="scripts/log_full_data.py">
#!/usr/bin/env python3
"""
Log complete tool data structures for debugging and exploration.
Shows all available fields in hook inputs.
"""

import json
import sys
from datetime import datetime
from pathlib import Path

def main():
    # Ensure logs directory exists
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Read raw input
    raw_input = sys.stdin.read()
    
    try:
        # Parse JSON
        hook_input = json.loads(raw_input)
        
        # Determine hook type from command line args
        hook_type = "unknown"
        if len(sys.argv) > 1:
            hook_type = sys.argv[1]
        
        # Create detailed log entry
        log_entry = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "hook_type": hook_type,
            "raw_input_length": len(raw_input),
            "parsed_data": hook_input
        }
        
        # Write to detailed log file
        log_file = log_dir / "tool-data-structures.jsonl"
        with log_file.open("a") as f:
            json.dump(log_entry, f, indent=2)
            f.write("\n")
        
        # Also write a pretty-printed version for easier reading
        pretty_file = log_dir / f"tool-data-{hook_type}.json"
        with pretty_file.open("w") as f:
            json.dump(hook_input, f, indent=2)
        
        # Create a human-readable summary
        summary_file = log_dir / "tool-data-summary.log"
        with summary_file.open("a") as f:
            f.write(f"\n{'='*60}\n")
            f.write(f"Timestamp: {datetime.now()}\n")
            f.write(f"Hook Type: {hook_type}\n")
            f.write(f"Tool Name: {hook_input.get('tool_name', 'N/A')}\n")
            f.write(f"Session ID: {hook_input.get('session_id', 'N/A')}\n")
            f.write(f"Available Keys: {', '.join(hook_input.keys())}\n")
            
            # Show tool_input structure
            if 'tool_input' in hook_input:
                f.write(f"Tool Input Keys: {', '.join(hook_input['tool_input'].keys())}\n")
            
            # Show tool_response structure (for post hooks)
            if 'tool_response' in hook_input:
                f.write(f"Tool Response Keys: {', '.join(hook_input['tool_response'].keys())}\n")
            
            f.write(f"{'='*60}\n")
        
    except json.JSONDecodeError as e:
        # Log error
        error_entry = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "error": str(e),
            "raw_input": raw_input[:1000]  # First 1000 chars
        }
        
        error_file = log_dir / "tool-data-errors.jsonl"
        with error_file.open("a") as f:
            json.dump(error_entry, f)
            f.write("\n")
    
    # Always exit successfully
    sys.exit(0)

if __name__ == "__main__":
    main()
</file>

<file path="scripts/log_tool_use.py">
#!/usr/bin/env python3
"""
Universal tool use logger for Claude Code hooks.
Logs all tool usage to a structured JSON format.
"""

import json
import sys
from datetime import datetime
from pathlib import Path

def main():
    # Ensure logs directory exists
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Read hook input from stdin
    try:
        hook_input = json.load(sys.stdin)
    except json.JSONDecodeError as e:
        # Log error and exit gracefully
        with open(log_dir / "hook-errors.log", "a") as f:
            f.write(f"[{datetime.utcnow().isoformat()}Z] JSON decode error in log_tool_use.py: {e}\n")
        sys.exit(0)
    
    # Determine if this is pre or post hook
    hook_type = sys.argv[1] if len(sys.argv) > 1 else "unknown"
    
    # Create log entry
    log_entry = {
        "timestamp": datetime.utcnow().isoformat() + "Z",
        "hook_type": hook_type,
        "session_id": hook_input.get("session_id"),
        "tool_name": hook_input.get("tool_name"),
        "transcript_path": hook_input.get("transcript_path")
    }
    
    # Add tool-specific information
    if hook_type == "pre":
        log_entry["tool_input"] = hook_input.get("tool_input", {})
    elif hook_type == "post":
        log_entry["tool_input"] = hook_input.get("tool_input", {})
        log_entry["tool_response"] = hook_input.get("tool_response", {})
    
    # Write to log file
    log_file = log_dir / "tool-usage.jsonl"
    with log_file.open("a") as f:
        json.dump(log_entry, f)
        f.write("\n")
    
    # Success - no output means continue
    sys.exit(0)

if __name__ == "__main__":
    main()
</file>

<file path="scripts/session_summary.py">
#!/usr/bin/env python3
"""
Generate session summary when Claude Code stops.
Can optionally block stop if tasks are incomplete.
"""

import json
import sys
from datetime import datetime
from pathlib import Path
from collections import Counter

def analyze_session(session_id: str) -> dict:
    """Analyze the session's tool usage."""
    stats = {
        "total_tools": 0,
        "tool_counts": Counter(),
        "file_reads": [],
        "file_writes": [],
        "bash_commands": [],
        "errors": 0
    }
    
    # Read tool usage log if it exists
    tool_log = Path("logs/tool-usage.jsonl")
    if tool_log.exists():
        with open(tool_log) as f:
            for line in f:
                try:
                    entry = json.loads(line.strip())
                    if entry.get("session_id") == session_id:
                        stats["total_tools"] += 1
                        tool_name = entry.get("tool_name", "unknown")
                        stats["tool_counts"][tool_name] += 1
                        
                        # Track specific operations
                        if tool_name == "Read":
                            file_path = entry.get("tool_input", {}).get("file_path")
                            if file_path:
                                stats["file_reads"].append(file_path)
                        elif tool_name in ["Write", "Edit", "MultiEdit"]:
                            file_path = entry.get("tool_input", {}).get("file_path")
                            if file_path:
                                stats["file_writes"].append(file_path)
                        elif tool_name == "Bash":
                            command = entry.get("tool_input", {}).get("command")
                            if command:
                                stats["bash_commands"].append(command)
                        
                        # Check for errors in post hooks
                        if entry.get("hook_type") == "post":
                            response = entry.get("tool_response", {})
                            if not response.get("success", True) or response.get("exit_code", 0) != 0:
                                stats["errors"] += 1
                except:
                    pass
    
    return stats

def check_incomplete_todos(session_id: str) -> list:
    """Check for incomplete todos in the current session."""
    # This is a placeholder - in a real implementation, you might
    # read the TodoRead output from the transcript
    return []

def main():
    try:
        # Read hook input
        hook_input = json.load(sys.stdin)
    except json.JSONDecodeError:
        sys.exit(0)
    
    session_id = hook_input.get("session_id", "unknown")
    transcript_path = hook_input.get("transcript_path", "")
    stop_hook_active = hook_input.get("stop_hook_active", False)
    
    # Don't create infinite loops
    if stop_hook_active:
        sys.exit(0)
    
    # Analyze the session
    stats = analyze_session(session_id)
    
    # Generate summary
    summary = f"""
=== Session Summary ===
Session ID: {session_id}
End Time: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
Total Tool Calls: {stats['total_tools']}
Tool Usage:
"""
    
    for tool, count in stats['tool_counts'].most_common():
        summary += f"  - {tool}: {count}\n"
    
    summary += f"""
Files Read: {len(stats['file_reads'])}
Files Modified: {len(stats['file_writes'])}
Bash Commands: {len(stats['bash_commands'])}
Errors Encountered: {stats['errors']}
Transcript: {transcript_path}
=====================
"""
    
    # Write summary to log
    with open("logs/session-summaries.txt", "a") as f:
        f.write(summary)
    
    # Also create a JSON summary for programmatic access
    json_summary = {
        "session_id": session_id,
        "end_time": datetime.now().isoformat(),
        "stats": {
            "total_tools": stats["total_tools"],
            "tool_counts": dict(stats["tool_counts"]),
            "files_read": len(stats["file_reads"]),
            "files_modified": len(stats["file_writes"]),
            "bash_commands": len(stats["bash_commands"]),
            "errors": stats["errors"]
        },
        "transcript_path": transcript_path
    }
    
    with open("logs/session-summaries.jsonl", "a") as f:
        json.dump(json_summary, f)
        f.write("\n")
    
    # Example: Block stop if there are errors (commented out)
    # if stats['errors'] > 0:
    #     output = {
    #         "decision": "block",
    #         "reason": f"Session had {stats['errors']} errors. Please review before ending."
    #     }
    #     print(json.dumps(output))
    #     sys.exit(0)
    
    # Example: Check for incomplete todos (commented out)
    # incomplete = check_incomplete_todos(session_id)
    # if incomplete:
    #     output = {
    #         "decision": "block",
    #         "reason": f"You have {len(incomplete)} incomplete todos. Complete them?"
    #     }
    #     print(json.dumps(output))
    #     sys.exit(0)
    
    sys.exit(0)

if __name__ == "__main__":
    main()
</file>

<file path="scripts/track_file_changes.py">
#!/usr/bin/env python3
"""
Track file changes for audit purposes.
Creates a detailed log of all file modifications.
"""

import json
import sys
import os
from datetime import datetime
from pathlib import Path

def get_file_info(file_path: str) -> dict:
    """Get file information if it exists."""
    try:
        path = Path(file_path)
        if path.exists():
            stat = path.stat()
            return {
                "exists": True,
                "size": stat.st_size,
                "mode": oct(stat.st_mode),
                "is_dir": path.is_dir(),
                "is_file": path.is_file(),
            }
    except:
        pass
    return {"exists": False}

def main():
    try:
        # Read hook input
        hook_input = json.load(sys.stdin)
    except json.JSONDecodeError:
        sys.exit(0)
    
    tool_name = hook_input.get("tool_name", "")
    if tool_name not in ["Write", "Edit", "MultiEdit", "NotebookEdit"]:
        sys.exit(0)
    
    # Ensure logs directory exists
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Extract file path
    tool_input = hook_input.get("tool_input", {})
    file_path = tool_input.get("file_path") or tool_input.get("notebook_path", "unknown")
    
    # Create detailed log entry
    log_entry = {
        "timestamp": datetime.utcnow().isoformat() + "Z",
        "tool": tool_name,
        "file": file_path,
        "session_id": hook_input.get("session_id"),
        "file_info": get_file_info(file_path),
        "user": os.environ.get("USER", "unknown"),
        "cwd": os.getcwd(),
    }
    
    # Add operation-specific details
    if tool_name == "Write":
        log_entry["operation"] = "create_or_overwrite"
        log_entry["content_length"] = len(tool_input.get("content", ""))
    elif tool_name == "Edit":
        log_entry["operation"] = "edit"
        log_entry["old_string_length"] = len(tool_input.get("old_string", ""))
        log_entry["new_string_length"] = len(tool_input.get("new_string", ""))
        log_entry["replace_all"] = tool_input.get("replace_all", False)
    elif tool_name == "MultiEdit":
        log_entry["operation"] = "multi_edit"
        log_entry["edit_count"] = len(tool_input.get("edits", []))
    
    # Write to audit log
    with open(log_dir / "file-changes-audit.jsonl", "a") as f:
        json.dump(log_entry, f)
        f.write("\n")
    
    # Also write a simple summary to a human-readable log
    summary = f"[{log_entry['timestamp']}] {tool_name}: {file_path} by {log_entry['user']}"
    with open(log_dir / "file-changes-summary.log", "a") as f:
        f.write(summary + "\n")
    
    sys.exit(0)

if __name__ == "__main__":
    main()
</file>

<file path="scripts/validate_bash_command.py">
#!/usr/bin/env python3
"""
Validate bash commands and provide feedback to Claude Code.
Can block dangerous commands and suggest improvements.
"""

import json
import re
import sys

# Define validation rules as (regex pattern, message, is_dangerous) tuples
VALIDATION_RULES = [
    # Performance suggestions
    (r"\bgrep\b(?!.*\|)", "Use 'rg' (ripgrep) instead of 'grep' for better performance and features", False),
    (r"\bfind\s+\S+\s+-name\b", "Use 'rg --files | rg pattern' or 'rg --files -g pattern' instead of 'find -name' for better performance", False),
    (r"\bcat\s+.*\|\s*grep\b", "Use 'rg pattern file' instead of 'cat file | grep pattern'", False),
    
    # Security warnings
    (r"\brm\s+-rf\s+/(?:\s|$)", "DANGER: Attempting to remove root directory!", True),
    (r"\brm\s+-rf\s+~(?:/|$|\s)", "DANGER: Attempting to remove home directory!", True),
    (r"\bdd\s+.*of=/dev/[sh]d[a-z](?:\d|$)", "DANGER: Direct disk write operation detected!", True),
    (r">\s*/dev/[sh]d[a-z]", "DANGER: Attempting to write directly to disk device!", True),
    
    # Insecure practices
    (r"\bcurl\s+.*\s+-k\b", "Security Warning: -k flag disables SSL certificate verification", False),
    (r"\bwget\s+.*--no-check-certificate\b", "Security Warning: --no-check-certificate disables SSL verification", False),
    (r"\bchmod\s+777\b", "Security Warning: chmod 777 gives full permissions to everyone", False),
    (r"\bsudo\s+chmod\s+-R\s+777\b", "DANGER: Recursive chmod 777 is extremely insecure!", True),
    
    # Best practices
    (r"cd\s+&&\s+ls", "Consider using 'ls <directory>' instead of 'cd && ls'", False),
    (r"\|\s*wc\s+-l\b", "Consider using 'rg -c' for counting matches in files", False),
]

def validate_command(command: str) -> tuple[list[str], bool]:
    """
    Validate a command and return (issues, should_block).
    """
    issues = []
    should_block = False
    
    for pattern, message, is_dangerous in VALIDATION_RULES:
        if re.search(pattern, command, re.IGNORECASE):
            issues.append(message)
            if is_dangerous:
                should_block = True
    
    return issues, should_block

def main():
    try:
        # Read input from stdin
        input_data = json.load(sys.stdin)
    except json.JSONDecodeError:
        # If JSON parsing fails, exit silently
        sys.exit(0)
    
    # Check if this is a Bash tool call
    tool_name = input_data.get("tool_name", "")
    if tool_name != "Bash":
        sys.exit(0)
    
    # Get the command
    command = input_data.get("tool_input", {}).get("command", "")
    if not command:
        sys.exit(0)
    
    # Validate the command
    issues, should_block = validate_command(command)
    
    if issues:
        # Output issues to stderr (will be shown to Claude)
        for message in issues:
            print(f"• {message}", file=sys.stderr)
        
        # Exit code 2 blocks the command
        if should_block:
            sys.exit(2)
    
    # Exit code 0 allows the command to proceed
    sys.exit(0)

if __name__ == "__main__":
    main()
</file>

<file path=".gitignore">
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.pytest_cache/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.mypy_cache/
.dmypy.json
dmypy.json
.pyre/

# TypeScript / Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*
.npm
.eslintcache
.tsbuildinfo
*.tsbuildinfo
.next/
out/
dist/
.cache/
.parcel-cache/
.docusaurus
.serverless/
.fusebox/
.dynamodb/
.tern-port
.vscode-test
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
.DS_Store

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
</file>

<file path=".claude/settings.json">
{
  "hooks": {
    "PreToolUse": [
      {
        "matcher": "Bash",
        "hooks": [
          {
            "type": "command",
            "command": "jq -r '\"[\" + (now | strftime(\"%Y-%m-%d %H:%M:%S\")) + \"] Bash command: \" + .tool_input.command' >> logs/bash-commands.log"
          }
        ]
      },
      {
        "matcher": "Write|Edit|MultiEdit",
        "hooks": [
          {
            "type": "command",
            "command": "jq -r '\"[\" + (now | strftime(\"%Y-%m-%d %H:%M:%S\")) + \"] File modification: \" + (.tool_input.file_path // .tool_input.filePath // \"unknown\")' >> logs/file-modifications.log"
          }
        ]
      },
      {
        "matcher": "Read",
        "hooks": [
          {
            "type": "command",
            "command": "jq -r '\"[\" + (now | strftime(\"%Y-%m-%d %H:%M:%S\")) + \"] File read: \" + .tool_input.file_path' >> logs/file-reads.log"
          }
        ]
      }
    ],
    "PostToolUse": [
      {
        "matcher": "Bash",
        "hooks": [
          {
            "type": "command",
            "command": "jq -r '\"[\" + (now | strftime(\"%Y-%m-%d %H:%M:%S\")) + \"] Bash completed: exit_code=\" + (.tool_response.exit_code // 0 | tostring)' >> logs/bash-results.log"
          }
        ]
      },
      {
        "matcher": "Write|Edit|MultiEdit",
        "hooks": [
          {
            "type": "command",
            "command": "jq -r '\"[\" + (now | strftime(\"%Y-%m-%d %H:%M:%S\")) + \"] File operation completed: \" + (.tool_response.success // false | tostring)' >> logs/file-results.log"
          }
        ]
      }
    ],
    "Notification": [
      {
        "matcher": "",
        "hooks": [
          {
            "type": "command",
            "command": "jq -r '\"[\" + (now | strftime(\"%Y-%m-%d %H:%M:%S\")) + \"] Notification: \" + .message' >> logs/notifications.log"
          }
        ]
      }
    ],
    "Stop": [
      {
        "matcher": "",
        "hooks": [
          {
            "type": "command",
            "command": "jq -r '\"[\" + (now | strftime(\"%Y-%m-%d %H:%M:%S\")) + \"] Session ended: \" + .session_id' >> logs/sessions.log"
          }
        ]
      }
    ]
  }
}
</file>

<file path="CLAUDE.md">
# Claude Code Hooks Project Instructions

## Project Overview

This is a Claude Code hooks demonstration project that showcases comprehensive logging, security validation, and workflow automation through hooks.

## Active Hooks

You currently have hooks configured in:
- `.claude/settings.json` - Basic logging with jq commands
- `.claude/settings.local.json` - Advanced Python scripts for validation and logging

These hooks are actively:
1. Logging all your commands to `logs/`
2. Validating bash commands for safety
3. Tracking file modifications
4. Creating session summaries

## Important Behaviors

### Command Validation
- Commands like `rm -rf /` will be BLOCKED by `validate_bash_command.py`
- You'll see validation errors that explain why commands are dangerous
- The hook uses exit code 2 to block execution

### Automatic Logging
- Every tool use is logged to multiple files
- Check `logs/tool-data-summary.log` for human-readable summaries
- Full JSON data is in `logs/tool-data-structures.jsonl`

### File Operations
- All file reads/writes are tracked in audit logs
- Code formatting runs automatically after file modifications (if formatters are installed)
- File changes include metadata like user, timestamp, and operation type

## Working with This Project

### Testing Hooks
To test if hooks are working:
```bash
echo "test" > test.txt  # Will trigger Write hooks
cat test.txt           # Will trigger Read hooks
rm test.txt           # Will trigger Bash hooks
```

### Viewing Logs
Most useful log commands:
```bash
# See recent tool usage
tail -f logs/tool-data-summary.log

# Check bash command history
cat logs/bash-commands.log

# Find errors
grep -i error logs/*.log

# Analyze tool usage
cat logs/tool-usage.jsonl | jq -r .tool_name | sort | uniq -c
```

### Modifying Hooks
1. Edit `.claude/settings.local.json` for changes
2. Restart Claude Code (hooks are cached at startup)
3. Test your changes

### Debugging Hook Issues
If hooks aren't working:
1. Check script permissions: `ls -la scripts/`
2. Test scripts manually: `echo '{}' | python3 scripts/log_tool_use.py pre`
3. Check for JSON errors: `jq . .claude/settings*.json`

## Hook Data Available

When hooks run, they receive:
- `session_id` - Your current session ID
- `transcript_path` - Path to conversation log
- `tool_name` - Name of the tool being used
- `tool_input` - Input parameters (PreToolUse)
- `tool_response` - Results (PostToolUse only)

## Security Notes

- Hooks run with YOUR permissions
- Be careful with hook scripts that modify files
- Validation hooks can prevent dangerous operations
- All logs may contain sensitive information

## Performance Considerations

Current hooks add minimal overhead, but:
- Many hooks can slow operations
- File I/O in hooks affects performance
- Consider disabling verbose logging for large operations

## Extending the System

To add new functionality:
1. Create new scripts in `scripts/`
2. Add hook configuration to settings
3. Test thoroughly before production use
4. Document your additions

## Quick Reference

**Check active hooks:**
```
/hooks
```

**Temporarily disable hooks:**
```bash
mv .claude/settings.local.json .claude/settings.local.json.disabled
# Restart Claude Code
```

**View session summary:**
```bash
tail logs/session-summaries.txt
```

## Troubleshooting

**If you see "command blocked by hook":**
- Check the error message for details
- The command may be dangerous
- Review `scripts/validate_bash_command.py` for rules

**If logs aren't being created:**
- Ensure `logs/` directory exists
- Check script permissions
- Verify `jq` is installed for basic hooks

**If hooks cause errors:**
- Check `logs/tool-data-errors.jsonl`
- Test scripts with sample JSON input
- Review script exit codes

Remember: This project demonstrates hook capabilities. Adapt the configurations and scripts to your specific needs.
</file>

<file path="README.md">
# Claude Code Hooks System

A comprehensive hooks infrastructure for Claude Code that provides automatic logging, security validation, and workflow automation.

## What Are Hooks?

Hooks are shell commands that run automatically at specific points in Claude Code's lifecycle. They receive JSON data via stdin and can control execution flow through exit codes.

```
Claude Code → Hook receives JSON → Your script runs → Exit code controls flow
```

## The 4 Hook Types

### 1. PreToolUse - Before any tool runs
**When:** Right before Claude executes a tool (Bash, Write, Edit, etc.)  
**Can:** Block execution, validate inputs, log attempts  
**Use Cases:**
- 🛡️ Block dangerous commands (`rm -rf /`)
- 📝 Log all commands before execution
- ✅ Validate file paths and permissions
- 🔍 Check branch before allowing file edits

**JSON Fields Available:**
```json
{
  "session_id": "abc123",
  "transcript_path": "~/.claude/projects/.../session.jsonl",  // JSONL file with full conversation history
  "tool_name": "Bash",
  "tool_input": {
    // Tool-specific fields (see examples below)
  }
}
```

### 2. PostToolUse - After tool completes
**When:** Right after a tool finishes (success or failure)  
**Can:** Process results, trigger actions, log outcomes  
**Use Cases:**
- 🎨 Auto-format code after file changes
- 📊 Log command results and exit codes
- 🔔 Alert on failures
- 🧪 Run tests after modifications

**JSON Fields Available:**
```json
{
  "session_id": "abc123",
  "transcript_path": "~/.claude/projects/.../session.jsonl",
  "tool_name": "Bash",
  "tool_input": {
    // Same as PreToolUse
  },
  "tool_response": {
    // Tool-specific response fields (see examples below)
  }
}
```

### 3. Notification - When Claude notifies you
**When:** Claude needs your attention or permission  
**Can:** Customize how you're notified  
**Use Cases:**
- 🔔 Desktop notifications
- 💬 Slack/Discord alerts
- 📱 Mobile push notifications
- 🔊 Sound alerts

**JSON Fields Available:**
```json
{
  "session_id": "abc123",
  "transcript_path": "~/.claude/projects/.../session.jsonl",
  "message": "Claude needs your input...",
  "title": "Claude Code"  // Optional
}
```

### 4. Stop - When Claude finishes responding
**When:** Claude completes its response  
**Can:** Summarize session, block stop if tasks incomplete  
**Use Cases:**
- 📈 Generate session analytics
- ✅ Check for uncommitted changes
- 📋 Create task summaries
- 🚫 Prevent stop if tests failing

**JSON Fields Available:**
```json
{
  "session_id": "abc123",
  "transcript_path": "~/.claude/projects/.../session.jsonl",
  "stop_hook_active": true
}
```

## How Hooks Work

### The Flow: Claude → Hook → Action

1. **You ask Claude to do something** (e.g., "run ls -la")
2. **Claude prepares to use a tool** (Bash in this case)
3. **Before execution:** PreToolUse hook fires
   - Claude pauses and sends JSON data to your hook
   - Your hook script receives the data via stdin
   - Hook can approve (exit 0) or block (exit 2)
4. **Tool executes** (if not blocked)
5. **After execution:** PostToolUse hook fires
   - Hook receives tool results
   - Can trigger follow-up actions
6. **Claude continues** with your request

### Hook Execution Example

When Claude wants to run `rm -rf /`:

```
1. Claude prepares: {"tool_name": "Bash", "tool_input": {"command": "rm -rf /"}}
   ↓
2. PreToolUse hook runs your validation script
   ↓
3. Script detects danger, exits with code 2
   ↓
4. Claude receives: "Dangerous command blocked!"
   ↓
5. Command is NOT executed, Claude tries alternative approach
```

### Exit Codes Matter
- **Exit 0**: "All good, proceed" 
- **Exit 2**: "Stop! Don't run this" (PreToolUse/Stop only)
- **Other**: "FYI there was an error" (but continue)

## Quick Start

### 1. Debug: Log Everything (see what data hooks receive)
```json
{
  "hooks": {
    "PreToolUse": [{
      "matcher": ".*",
      "hooks": [{
        "type": "command",
        "command": "cat >> ~/claude-hooks-debug.jsonl"
      }]
    }]
  }
}
```
This dumps the raw JSON for every tool call - perfect for exploring what data is available!

### 2. Basic Logging (jq)
```json
{
  "hooks": {
    "PreToolUse": [{
      "matcher": "Bash",
      "hooks": [{
        "type": "command",
        "command": "jq -r '.tool_input.command' >> commands.log"
      }]
    }]
  }
}
```

### 2. Security Validation (Python)
```json
{
  "hooks": {
    "PreToolUse": [{
      "matcher": "Bash",
      "hooks": [{
        "type": "command",
        "command": "python3 scripts/validate_bash.py"
      }]
    }]
  }
}
```

### 3. Auto-formatting (Multiple tools)
```json
{
  "hooks": {
    "PostToolUse": [{
      "matcher": "Write|Edit|MultiEdit",
      "hooks": [{
        "type": "command",
        "command": "bash scripts/format_code.sh"
      }]
    }]
  }
}
```

## Data Available to Hooks

### All Hooks Get
- `session_id` - Unique session identifier
- `transcript_path` - Conversation log path

### PreToolUse Gets
- `tool_name` - Which tool is about to run
- `tool_input` - Tool-specific parameters

### PostToolUse Gets
- Everything from PreToolUse
- `tool_response` - Results, exit codes, output

### Tool Examples

**Bash:**
```json
{
  "tool_input": {
    "command": "ls -la",
    "description": "List files"
  },
  "tool_response": {
    "stdout": "file1.txt\nfile2.txt",
    "exit_code": 0
  }
}
```

**Write/Edit:**
```json
{
  "tool_input": {
    "file_path": "/path/to/file.py",
    "content": "print('hello')"  // Write only
  },
  "tool_response": {
    "success": true
  }
}
```

## Configuration

Hooks are configured in:
- `~/.claude/settings.json` - User settings (all projects)
- `.claude/settings.json` - Project settings
- `.claude/settings.local.json` - Local settings (git ignored)

### Matchers
- Exact: `"Bash"` - Only Bash tool
- Multiple: `"Write|Edit"` - Write OR Edit tools
- Pattern: `"Notebook.*"` - All Notebook tools
- All: `".*"` - Every tool

## This Repository

### Pre-built Scripts

| Script                     | Purpose                  | Hook Type       |
| -------------------------- | ------------------------ | --------------- |
| `validate_bash_command.py` | Block dangerous commands | PreToolUse      |
| `log_tool_use.py`          | Log all tool usage       | Pre/PostToolUse |
| `track_file_changes.py`    | Audit file modifications | PreToolUse      |
| `format_code.sh`           | Run formatters           | PostToolUse     |
| `session_summary.py`       | Generate analytics       | Stop            |

### Example Configurations

| File                               | Use Case                  |
| ---------------------------------- | ------------------------- |
| `examples/minimal-logging.json`    | Simple command logging    |
| `examples/security-focused.json`   | Maximum validation        |
| `examples/developer-workflow.json` | Auto-formatting & linting |

### Generated Logs

| Log File                   | Contains                  |
| -------------------------- | ------------------------- |
| `tool-usage.jsonl`         | Every tool call           |
| `bash-commands.log`        | All bash commands         |
| `file-changes-audit.jsonl` | File modification details |
| `session-summaries.txt`    | Session analytics         |

## Common Patterns

### Log Everything
```bash
# See what Claude is doing
tail -f logs/tool-data-summary.log
```

### Block Dangerous Operations
```python
if "production" in file_path:
    print("Cannot modify production files!", file=sys.stderr)
    sys.exit(2)
```

### Conditional Formatting
```bash
case "$FILE_EXT" in
  py) black "$FILE_PATH" ;;
  js|ts) prettier --write "$FILE_PATH" ;;
  go) gofmt -w "$FILE_PATH" ;;
esac
```

## Security Notes

⚠️ **Hooks run with YOUR permissions** - Review all hook scripts carefully!

- Always validate inputs
- Use absolute paths
- Quote shell variables
- Test in safe environment first
</file>

</files>
