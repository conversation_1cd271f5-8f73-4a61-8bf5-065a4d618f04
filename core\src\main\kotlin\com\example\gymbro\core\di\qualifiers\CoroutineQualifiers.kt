package com.example.gymbro.core.di.qualifiers

import javax.inject.Qualifier

/**
 * 协程调度器相关的Qualifier注解
 * 用于在依赖注入中区分不同的CoroutineDispatcher实现
 *
 * 统一管理所有协程相关的限定符，确保全局唯一性
 */

/**
 * 标识IO调度器的限定符
 * 适用于网络请求、数据库操作等IO密集型任务
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class IoDispatcher

/**
 * 标识默认调度器的限定符
 * 适用于普通的后台任务和CPU密集型计算
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class DefaultDispatcher

/**
 * 标识主线程调度器的限定符
 * 适用于UI更新等需要在主线程执行的任务
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class MainDispatcher

/**
 * 标识无限制调度器的限定符
 * 适用于不需要特定线程的任务，通常用于测试
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class UnconfinedDispatcher

/**
 * 标识主线程立即调度器的限定符
 * 适用于需要立即在主线程执行的任务
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class MainImmediateDispatcher
