package com.example.gymbro.data.local.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 搜索内容实体
 *
 * 存储可搜索的内容及其向量表示
 * 支持FTS5全文搜索和VSS向量搜索
 */
@Entity(tableName = "search_content")
data class SearchContentEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,

    /**
     * 文本内容
     * 用于FTS5全文搜索和向量化
     */
    @ColumnInfo(name = "content")
    val content: String,

    /**
     * 内容的向量表示
     * 由BGE模型生成，用于语义搜索
     */
    @ColumnInfo(name = "embedding")
    val embedding: FloatArray? = null,

    /**
     * 元数据
     * 存储额外的结构化信息，如来源、类型等
     */
    @ColumnInfo(name = "metadata")
    val metadata: Map<String, Any> = emptyMap(),

    /**
     * 创建时间戳
     */
    @ColumnInfo(name = "created_at")
    val createdAt: Long = System.currentTimeMillis(),
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as SearchContentEntity

        if (id != other.id) return false
        if (content != other.content) return false
        if (embedding != null) {
            if (other.embedding == null) return false
            if (!embedding.contentEquals(other.embedding)) return false
        } else if (other.embedding != null) return false
        if (metadata != other.metadata) return false
        if (createdAt != other.createdAt) return false

        return true
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + content.hashCode()
        result = 31 * result + (embedding?.contentHashCode() ?: 0)
        result = 31 * result + metadata.hashCode()
        result = 31 * result + createdAt.hashCode()
        return result
    }
}
