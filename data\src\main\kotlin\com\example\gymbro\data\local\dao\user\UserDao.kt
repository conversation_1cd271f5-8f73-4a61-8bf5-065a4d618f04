package com.example.gymbro.data.local.dao.user

import androidx.room.*
import com.example.gymbro.data.local.entity.user.UserCacheEntity
import kotlinx.coroutines.flow.Flow

/**
 * 用户数据访问对象接口
 * 处理与用户相关的数据库操作
 */
@Dao
interface UserDao {
    /**
     * 插入或更新用户数据
     * @param user 用户数据实体
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUser(user: UserCacheEntity)

    /**
     * 更新用户数据
     * @param user 用户数据实体
     */
    @Update
    suspend fun updateUser(user: UserCacheEntity)

    /**
     * 获取用户数据
     * @param userId 用户ID
     * @return 用户数据实体Flow
     */
    @Query("SELECT * FROM users WHERE user_id = :userId")
    fun getUser(userId: String): Flow<UserCacheEntity?>

    /**
     * 获取当前用户
     * 假设当前只有一个用户的情况
     * @return 用户数据实体Flow
     */
    @Query("SELECT * FROM users LIMIT 1")
    fun getCurrentUser(): Flow<UserCacheEntity?>

    /**
     * 删除所有用户数据
     */
    @Query("DELETE FROM users")
    suspend fun deleteUser()

    /**
     * 删除指定用户数据
     * @param userId 用户ID
     */
    @Query("DELETE FROM users WHERE user_id = :userId")
    suspend fun deleteUserById(userId: String)

    /**
     * 检查是否有用户数据存在
     * @return 如果存在用户数据则返回true，否则返回false
     */
    @Query("SELECT EXISTS(SELECT 1 FROM users LIMIT 1)")
    suspend fun hasUser(): Boolean
}
