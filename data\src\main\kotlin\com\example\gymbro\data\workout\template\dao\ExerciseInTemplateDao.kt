package com.example.gymbro.data.workout.template.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.example.gymbro.data.workout.template.entity.ExerciseInTemplateEntity
import kotlinx.coroutines.flow.Flow

/**
 * 模板动作数据访问对象 - TemplateDB DAO
 *
 * 基于 05_Final_Database_Architecture.md 设计
 * 提供模板中动作配置管理功能
 */
@Dao
interface ExerciseInTemplateDao {

    // ==================== 基础 CRUD ====================

    @Query("SELECT * FROM template_exercises WHERE id = :exerciseInTemplateId")
    suspend fun getExerciseInTemplateById(exerciseInTemplateId: String): ExerciseInTemplateEntity?

    @Query("SELECT * FROM template_exercises WHERE templateId = :templateId ORDER BY `order` ASC")
    fun getExercisesInTemplate(templateId: String): Flow<List<ExerciseInTemplateEntity>>

    @Query("SELECT * FROM template_exercises WHERE templateId = :templateId ORDER BY `order` ASC")
    suspend fun getExercisesInTemplateSync(templateId: String): List<ExerciseInTemplateEntity>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertExerciseInTemplate(exerciseInTemplate: ExerciseInTemplateEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertExercisesInTemplate(exercisesInTemplate: List<ExerciseInTemplateEntity>)

    @Update
    suspend fun updateExerciseInTemplate(exerciseInTemplate: ExerciseInTemplateEntity)

    @Query("DELETE FROM template_exercises WHERE id = :exerciseInTemplateId")
    suspend fun deleteExerciseInTemplate(exerciseInTemplateId: String)

    @Query("DELETE FROM template_exercises WHERE templateId = :templateId")
    suspend fun deleteAllExercisesInTemplate(templateId: String)

    // ==================== 特定查询 ====================

    @Query("SELECT * FROM template_exercises WHERE templateId = :templateId AND exerciseId = :exerciseId")
    suspend fun getExerciseInTemplateByExerciseId(
        templateId: String,
        exerciseId: String,
    ): ExerciseInTemplateEntity?

    @Query(
        "SELECT * FROM template_exercises WHERE templateId = :templateId AND superset = 1 ORDER BY supersetGroupId, `order` ASC",
    )
    fun getSupersetExercisesInTemplate(templateId: String): Flow<List<ExerciseInTemplateEntity>>

    @Query(
        "SELECT * FROM template_exercises WHERE templateId = :templateId AND supersetGroupId = :supersetGroupId ORDER BY `order` ASC",
    )
    suspend fun getExercisesInSupersetGroup(
        templateId: String,
        supersetGroupId: String,
    ): List<ExerciseInTemplateEntity>

    // ==================== 统计查询 ====================

    @Query("SELECT COUNT(*) FROM template_exercises WHERE templateId = :templateId")
    suspend fun getExerciseCountInTemplate(templateId: String): Int

    @Query(
        "SELECT COUNT(DISTINCT supersetGroupId) FROM template_exercises WHERE templateId = :templateId AND superset = 1",
    )
    suspend fun getSupersetGroupCount(templateId: String): Int

    @Query("SELECT MAX(`order`) FROM template_exercises WHERE templateId = :templateId")
    suspend fun getMaxOrderInTemplate(templateId: String): Int?

    // ==================== 批量操作 ====================

    @Query(
        "UPDATE template_exercises SET `order` = `order` + 1 WHERE templateId = :templateId AND `order` >= :fromOrder",
    )
    suspend fun shiftExerciseOrdersUp(templateId: String, fromOrder: Int)

    @Query(
        "UPDATE template_exercises SET `order` = `order` - 1 WHERE templateId = :templateId AND `order` > :fromOrder",
    )
    suspend fun shiftExerciseOrdersDown(templateId: String, fromOrder: Int)
}
