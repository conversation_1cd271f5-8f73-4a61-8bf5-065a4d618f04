package com.example.gymbro.core.logging

import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 日志模块的依赖注入配置
 *
 * 提供日志相关组件的依赖注入。
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class LoggingModule {

    /**
     * 绑定Logger接口到TimberLogger实现
     */
    @Binds
    @Singleton
    abstract fun bindLogger(impl: TimberLogger): Logger

    companion object {
        /**
         * 🔥 【重构】提供日志配置管理器
         */
        @Provides
        @Singleton
        fun provideLoggingConfig(): LoggingConfig = LoggingConfig()

        /**
         * 🔥 【重构】提供增强的 TimberManager
         */
        @Provides
        @Singleton
        fun provideTimberManager(loggingConfig: LoggingConfig): TimberManager = TimberManager(loggingConfig)

        /**
         * 🔥 【新增】提供日志控制器
         */
        @Provides
        @Singleton
        fun provideLoggingController(
            timberManager: TimberManager,
            loggingConfig: LoggingConfig,
        ): LoggingController {
            val controller = LoggingController(timberManager, loggingConfig)
            LoggingController.setInstance(controller)
            return controller
        }

        /**
         * 提供适用于生产环境的ReleaseTree
         */
        @Provides
        @Singleton
        fun provideReleaseTree(): ReleaseTree = ReleaseTree()
    }
}
