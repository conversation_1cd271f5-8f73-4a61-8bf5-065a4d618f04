package com.example.gymbro.core.network.ws

import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonPrimitive
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * WebSocket帧格式单元测试
 *
 * 验证帧创建、序列化和类型检查功能
 */
class WsFrameTest {

    private val json = Json {
        ignoreUnknownKeys = true
        isLenient = true
    }

    @Test
    fun `test ping frame creation`() {
        val pingId = "test-ping-id"
        val pingFrame = WsFrame.createPing(pingId)

        assertEquals(WsFrame.TYPE_PING, pingFrame.type)
        assertEquals(pingId, pingFrame.id)
        assertTrue(pingFrame.isPing())
        assertFalse(pingFrame.isPong())
        assertNotNull(pingFrame.timestamp)
    }

    @Test
    fun `test pong frame creation`() {
        val pongId = "test-pong-id"
        val pongFrame = WsFrame.createPong(pongId)

        assertEquals(WsFrame.TYPE_PONG, pongFrame.type)
        assertEquals(pongId, pongFrame.id)
        assertTrue(pongFrame.isPong())
        assertFalse(pongFrame.isPing())
        assertNotNull(pongFrame.timestamp)
    }

    @Test
    fun `test frame type constants`() {
        assertEquals("token", WsFrame.TYPE_TOKEN)
        assertEquals("tool_call", WsFrame.TYPE_TOOL_CALL)
        assertEquals("done", WsFrame.TYPE_DONE)
        assertEquals("error", WsFrame.TYPE_ERROR)
        assertEquals("ping", WsFrame.TYPE_PING)
        assertEquals("pong", WsFrame.TYPE_PONG)
        assertEquals("thinking", WsFrame.TYPE_THINKING)
        assertEquals("progress", WsFrame.TYPE_PROGRESS)
    }

    @Test
    fun `test heartbeat frame detection`() {
        val pingFrame = WsFrame.createPing("test-id")
        val pongFrame = WsFrame.createPong("test-id")
        val tokenFrame = WsFrame(type = WsFrame.TYPE_TOKEN, id = "test-id")

        assertTrue(WsFrame.isHeartbeat(pingFrame))
        assertTrue(WsFrame.isHeartbeat(pongFrame))
        assertFalse(WsFrame.isHeartbeat(tokenFrame))
    }

    @Test
    fun `test data frame detection`() {
        val tokenFrame = WsFrame(type = WsFrame.TYPE_TOKEN, id = "test-id")
        val toolCallFrame = WsFrame(type = WsFrame.TYPE_TOOL_CALL, id = "test-id")
        val thinkingFrame = WsFrame(type = WsFrame.TYPE_THINKING, id = "test-id")
        val progressFrame = WsFrame(type = WsFrame.TYPE_PROGRESS, id = "test-id")
        val pingFrame = WsFrame.createPing("test-id")

        assertTrue(WsFrame.isDataFrame(tokenFrame))
        assertTrue(WsFrame.isDataFrame(toolCallFrame))
        assertTrue(WsFrame.isDataFrame(thinkingFrame))
        assertTrue(WsFrame.isDataFrame(progressFrame))
        assertFalse(WsFrame.isDataFrame(pingFrame))
    }

    @Test
    fun `test control frame detection`() {
        val doneFrame = WsFrame(type = WsFrame.TYPE_DONE, id = "test-id")
        val errorFrame = WsFrame(type = WsFrame.TYPE_ERROR, id = "test-id")
        val tokenFrame = WsFrame(type = WsFrame.TYPE_TOKEN, id = "test-id")

        assertTrue(WsFrame.isControlFrame(doneFrame))
        assertTrue(WsFrame.isControlFrame(errorFrame))
        assertFalse(WsFrame.isControlFrame(tokenFrame))
    }

    @Test
    fun `test frame type checking methods`() {
        val errorFrame = WsFrame(type = WsFrame.TYPE_ERROR, id = "test-id")
        val doneFrame = WsFrame(type = WsFrame.TYPE_DONE, id = "test-id")
        val pingFrame = WsFrame.createPing("test-id")
        val pongFrame = WsFrame.createPong("test-id")

        assertTrue(errorFrame.isError())
        assertFalse(errorFrame.isDone())
        assertFalse(errorFrame.isPing())
        assertFalse(errorFrame.isPong())

        assertTrue(doneFrame.isDone())
        assertFalse(doneFrame.isError())

        assertTrue(pingFrame.isPing())
        assertFalse(pingFrame.isPong())

        assertTrue(pongFrame.isPong())
        assertFalse(pongFrame.isPing())
    }

    @Test
    fun `test frame with token index`() {
        val tokenFrame = WsFrame(
            type = WsFrame.TYPE_TOKEN,
            id = "test-id",
            idx = 42,
            data = JsonPrimitive("test token content"),
        )

        assertEquals(WsFrame.TYPE_TOKEN, tokenFrame.type)
        assertEquals("test-id", tokenFrame.id)
        assertEquals(42, tokenFrame.idx)
        assertNotNull(tokenFrame.data)
    }

    @Test
    fun `test frame serialization`() {
        val originalFrame = WsFrame(
            type = WsFrame.TYPE_TOKEN,
            id = "test-id",
            idx = 123,
            data = JsonPrimitive("test content"),
            timestamp = System.currentTimeMillis(),
        )

        // 序列化
        val serialized = json.encodeToString(WsFrame.serializer(), originalFrame)
        assertNotNull(serialized)
        assertTrue(serialized.contains("test-id"))
        assertTrue(serialized.contains("token"))

        // 反序列化
        val deserializedFrame = json.decodeFromString(WsFrame.serializer(), serialized)
        assertEquals(originalFrame.type, deserializedFrame.type)
        assertEquals(originalFrame.id, deserializedFrame.id)
        assertEquals(originalFrame.idx, deserializedFrame.idx)
        assertEquals(originalFrame.timestamp, deserializedFrame.timestamp)
    }

    @Test
    fun `test error message extraction`() {
        val errorFrame = WsFrame(
            type = WsFrame.TYPE_ERROR,
            id = "test-id",
            data = JsonPrimitive("Test error message"),
        )

        val nonErrorFrame = WsFrame(
            type = WsFrame.TYPE_TOKEN,
            id = "test-id",
        )

        assertNotNull(errorFrame.getErrorMessage())
        assertTrue(errorFrame.getErrorMessage()!!.contains("Test error message"))

        assertEquals(null, nonErrorFrame.getErrorMessage())
    }

    @Test
    fun `test frame with null data`() {
        val frame = WsFrame(
            type = WsFrame.TYPE_PING,
            id = "test-id",
            data = null,
        )

        assertEquals(WsFrame.TYPE_PING, frame.type)
        assertEquals("test-id", frame.id)
        assertEquals(null, frame.data)
        assertEquals(null, frame.getErrorMessage())
    }
}
