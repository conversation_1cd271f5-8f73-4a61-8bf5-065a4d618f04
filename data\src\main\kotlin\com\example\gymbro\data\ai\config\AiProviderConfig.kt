package com.example.gymbro.data.ai.config

import com.example.gymbro.domain.coach.config.AiProviderConfig

/**
 * Google Gemini API配置
 */
data class GoogleGeminiConfig(
    private val apiKey: String,
    override val baseUrl: String,
    override val defaultModel: String, // 移除硬编码，必须从配置注入
    private val summaryModelConfig: String? = null, // 从配置注入的摘要模型
) : AiProviderConfig {
    override val name: String = "google_gemini"
    override val summaryModel: String = summaryModelConfig ?: defaultModel
    override val headers: Map<String, String> =
        mapOf(
            "Content-Type" to "application/json",
            "x-goog-api-key" to apiKey,
        )
    override val maxTokens: Int = 8192
}

/**
 * OpenAI兼容API配置（支持DeepSeek等）
 */
data class OpenAiCompatibleConfig(
    private val apiKey: String,
    override val baseUrl: String,
    override val defaultModel: String,
    override val name: String = "openai_compatible",
    private val summaryModelConfig: String? = null, // 从配置注入的摘要模型
) : AiProviderConfig {
    override val summaryModel: String = summaryModelConfig ?: defaultModel
    override val headers: Map<String, String> =
        mapOf(
            "Content-Type" to "application/json",
            "Authorization" to "Bearer $apiKey",
        )
    override val maxTokens: Int = 32768
}

/**
 * DeepSeek API配置
 */
data class DeepSeekConfig(
    private val apiKey: String,
    override val baseUrl: String,
    override val defaultModel: String, // 移除硬编码，必须从配置注入
    private val summaryModelConfig: String? = null, // 从配置注入的摘要模型
) : AiProviderConfig {
    override val name: String = "deepseek"
    override val summaryModel: String = summaryModelConfig ?: defaultModel
    override val headers: Map<String, String> =
        mapOf(
            "Content-Type" to "application/json",
            "Authorization" to "Bearer $apiKey",
        )
    override val maxTokens: Int = 32768
}
