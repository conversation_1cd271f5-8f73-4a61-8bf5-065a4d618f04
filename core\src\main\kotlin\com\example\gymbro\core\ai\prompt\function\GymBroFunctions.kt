package com.example.gymbro.core.ai.prompt.function

/**
 * GymBro Function Call 域分离架构
 *
 * 基于"域分离 + 路由器统一"的设计理念，将Function Call按业务域组织
 * 解决原有架构中函数混淆、维护困难的问题
 *
 * 设计原则：
 * 1. 域边界清晰：每个域负责特定的业务功能
 * 2. 命名规范统一：gymbro.{domain}.{action} 格式
 * 3. 函数数量控制：每个域1-3个核心函数，覆盖80%需求
 * 4. 参数schema不重叠：避免LLM混淆
 * 5. 返回字段精简：只给必要信息，避免prompt膨胀
 *
 * 业务域划分：
 * - Exercise: 动作库管理 (搜索、详情、创建)
 * - Template: 训练模板管理 (搜索、生成)
 * - Plan: 训练计划管理 (搜索、生成)
 * - Session: 训练会话管理 (开始、记录、完成)
 *
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0 (P6阶段动作库Function Call链路)
 */
object GymBroFunctions {

    /**
     * 动作库域函数集合
     * 负责动作库的搜索、详情获取、创建等功能
     */
    object Exercise {
        /**
         * 动作库搜索函数
         * 支持关键词搜索和多维度筛选
         */
        val FN_SEARCH = FunctionDescriptor(
            name = "gymbro.exercise.search",
            description = "Search exercises by keyword, equipment, muscle group from official/user exercise library",
            jsonSchema = /* language=json */ """
                {
                  "type": "object",
                  "additionalProperties": false,
                  "properties": {
                    "query": {
                      "type": "string",
                      "description": "搜索关键词或自然语言描述，如'练胸'、'哑铃动作'、'初学者适合的动作'"
                    },
                    "top_k": {
                      "type": "integer",
                      "default": 8,
                      "minimum": 1,
                      "maximum": 20,
                      "description": "返回结果数量限制"
                    },
                    "filters": {
                      "type": "object",
                      "properties": {
                        "muscle_group": {
                          "type": "string",
                          "enum": ["CHEST", "SHOULDERS", "BACK", "ARMS", "LEGS", "CORE", "CARDIO", "FULL_BODY"],
                          "description": "目标肌群筛选"
                        },
                        "equipment": {
                          "type": "string",
                          "enum": ["BARBELL", "DUMBBELL", "KETTLEBELL", "CABLE", "MACHINE", "NONE"],
                          "description": "器械类型筛选"
                        },
                        "difficulty": {
                          "type": "string",
                          "enum": ["BEGINNER", "INTERMEDIATE", "ADVANCED"],
                          "description": "难度等级筛选"
                        },
                        "source": {
                          "type": "string",
                          "enum": ["OFFICIAL", "CUSTOM"],
                          "description": "动作来源筛选：官方或用户自定义"
                        }
                      }
                    }
                  },
                  "required": ["query"]
                }
            """,
        )

        /**
         * 动作详情获取函数
         * 根据动作ID获取详细信息
         */
        val FN_GET_DETAIL = FunctionDescriptor(
            name = "gymbro.exercise.get_detail",
            description = "Get detailed information of a specific exercise by ID",
            jsonSchema = /* language=json */ """
                {
                  "type": "object",
                  "additionalProperties": false,
                  "properties": {
                    "exercise_id": {
                      "type": "string",
                      "description": "动作ID，如'off_bench_press_001'或'u_user123_uuid'"
                    },
                    "include_media": {
                      "type": "boolean",
                      "default": false,
                      "description": "是否包含图片视频等媒体信息"
                    },
                    "include_instructions": {
                      "type": "boolean",
                      "default": true,
                      "description": "是否包含动作说明和技巧"
                    }
                  },
                  "required": ["exercise_id"]
                }
            """,
        )

        /**
         * 用户自定义动作创建函数
         * 创建或更新用户的自定义动作
         */
        val FN_UPSERT = FunctionDescriptor(
            name = "gymbro.exercise.upsert",
            description = "Create or update a custom exercise for the user",
            jsonSchema = /* language=json */ """
                {
                  "type": "object",
                  "additionalProperties": false,
                  "properties": {
                    "exercise_data": {
                      "type": "object",
                      "properties": {
                        "name": {
                          "type": "string",
                          "description": "动作名称",
                          "minLength": 1,
                          "maxLength": 100
                        },
                        "description": {
                          "type": "string",
                          "description": "动作描述",
                          "maxLength": 500
                        },
                        "muscle_group": {
                          "type": "string",
                          "enum": ["CHEST", "SHOULDERS", "BACK", "ARMS", "LEGS", "CORE"],
                          "description": "主要训练肌群"
                        },
                        "equipment": {
                          "type": "array",
                          "items": {
                            "type": "string",
                            "enum": ["BARBELL", "DUMBBELL", "KETTLEBELL", "CABLE", "MACHINE", "NONE"]
                          },
                          "description": "使用的器械类型"
                        },
                        "difficulty": {
                          "type": "string",
                          "enum": ["BEGINNER", "INTERMEDIATE", "ADVANCED"],
                          "description": "难度等级"
                        },
                        "instructions": {
                          "type": "array",
                          "items": { "type": "string" },
                          "description": "动作步骤说明"
                        }
                      },
                      "required": ["name", "muscle_group"]
                    }
                  },
                  "required": ["exercise_data"]
                }
            """,
        )
    }

    /**
     * 训练模板域函数集合
     * 负责训练模板的搜索和生成功能
     */
    object Template {
        /**
         * 模板搜索函数
         */
        val FN_SEARCH = FunctionDescriptor(
            name = "gymbro.template.search",
            description = "Search workout templates by keyword, muscle group, or difficulty",
            jsonSchema = /* language=json */ """
                {
                  "type": "object",
                  "additionalProperties": false,
                  "properties": {
                    "query": {
                      "type": "string",
                      "description": "搜索关键词，如'胸部训练'、'新手模板'"
                    },
                    "top_k": {
                      "type": "integer",
                      "default": 5,
                      "minimum": 1,
                      "maximum": 10
                    },
                    "filters": {
                      "type": "object",
                      "properties": {
                        "difficulty": {
                          "type": "string",
                          "enum": ["BEGINNER", "INTERMEDIATE", "ADVANCED"]
                        },
                        "duration_minutes": {
                          "type": "integer",
                          "minimum": 15,
                          "maximum": 180,
                          "description": "训练时长（分钟）"
                        }
                      }
                    }
                  },
                  "required": ["query"]
                }
            """,
        )

        /**
         * 模板生成函数
         */
        val FN_GENERATE = FunctionDescriptor(
            name = "gymbro.template.generate",
            description = "Generate a new workout template based on user requirements",
            jsonSchema = /* language=json */ """
                {
                  "type": "object",
                  "additionalProperties": false,
                  "properties": {
                    "requirements": {
                      "type": "object",
                      "properties": {
                        "target_muscles": {
                          "type": "array",
                          "items": { "type": "string" },
                          "description": "目标肌群列表"
                        },
                        "available_equipment": {
                          "type": "array",
                          "items": { "type": "string" },
                          "description": "可用器械列表"
                        },
                        "difficulty": {
                          "type": "string",
                          "enum": ["BEGINNER", "INTERMEDIATE", "ADVANCED"]
                        },
                        "duration_minutes": {
                          "type": "integer",
                          "minimum": 15,
                          "maximum": 180
                        }
                      },
                      "required": ["target_muscles", "difficulty"]
                    }
                  },
                  "required": ["requirements"]
                }
            """,
        )
    }

    /**
     * 训练计划域函数集合
     * 负责训练计划的搜索、详情获取、创建更新功能
     * 与Exercise和Template保持一致的Function Call架构
     */
    object Plan {
        /**
         * 计划搜索函数
         */
        val FN_SEARCH = FunctionDescriptor(
            name = "gymbro.plan.search",
            description = "Search workout plans by goal, duration, difficulty, or other criteria",
            jsonSchema = /* language=json */ """
                {
                  "type": "object",
                  "additionalProperties": false,
                  "properties": {
                    "query": {
                      "type": "string",
                      "description": "搜索关键词，如'减脂计划'、'增肌计划'、'4周训练'"
                    },
                    "top_k": {
                      "type": "integer",
                      "default": 5,
                      "minimum": 1,
                      "maximum": 10,
                      "description": "返回结果数量限制"
                    },
                    "filters": {
                      "type": "object",
                      "properties": {
                        "plan_type": {
                          "type": "string",
                          "enum": ["CUSTOM", "TEMPLATE", "AI_GENERATED"],
                          "description": "计划类型筛选"
                        },
                        "difficulty": {
                          "type": "string",
                          "enum": ["BEGINNER", "INTERMEDIATE", "ADVANCED"],
                          "description": "难度等级筛选"
                        },
                        "min_days": {
                          "type": "integer",
                          "minimum": 1,
                          "description": "最少天数"
                        },
                        "max_days": {
                          "type": "integer",
                          "maximum": 365,
                          "description": "最多天数"
                        },
                        "is_favorite": {
                          "type": "boolean",
                          "description": "是否只显示收藏的计划"
                        },
                        "is_ai_generated": {
                          "type": "boolean",
                          "description": "是否只显示AI生成的计划"
                        }
                      }
                    }
                  },
                  "required": ["query"]
                }
            """,
        )

        /**
         * 计划详情获取函数
         * 根据计划ID获取详细信息，确保AI能通过ID查看Plan内容
         */
        val FN_GET_DETAIL = FunctionDescriptor(
            name = "gymbro.plan.get_detail",
            description = "Get detailed information of a specific workout plan by ID",
            jsonSchema = /* language=json */ """
                {
                  "type": "object",
                  "additionalProperties": false,
                  "properties": {
                    "plan_id": {
                      "type": "string",
                      "description": "计划ID，如'plan_user123_abc456'或'ai_user123_def789'"
                    },
                    "include_schedule": {
                      "type": "boolean",
                      "default": true,
                      "description": "是否包含详细的训练安排"
                    },
                    "include_calendar_json": {
                      "type": "boolean",
                      "default": false,
                      "description": "是否包含calendar.json格式数据"
                    }
                  },
                  "required": ["plan_id"]
                }
            """,
        )

        /**
         * 计划创建/更新函数
         * 支持创建新计划或更新现有计划，实现读写功能
         */
        val FN_UPSERT = FunctionDescriptor(
            name = "gymbro.plan.upsert",
            description = "Create a new workout plan or update an existing one",
            jsonSchema = /* language=json */ """
                {
                  "type": "object",
                  "additionalProperties": false,
                  "properties": {
                    "plan_id": {
                      "type": "string",
                      "description": "计划ID，如果为空则创建新计划"
                    },
                    "name": {
                      "type": "string",
                      "minLength": 1,
                      "maxLength": 100,
                      "description": "计划名称"
                    },
                    "description": {
                      "type": "string",
                      "maxLength": 500,
                      "description": "计划描述"
                    },
                    "difficulty": {
                      "type": "string",
                      "enum": ["BEGINNER", "INTERMEDIATE", "ADVANCED"],
                      "default": "INTERMEDIATE",
                      "description": "难度等级"
                    },
                    "target_goal": {
                      "type": "string",
                      "description": "训练目标，如'增肌'、'减脂'、'力量'"
                    },
                    "total_days": {
                      "type": "integer",
                      "minimum": 1,
                      "maximum": 365,
                      "description": "计划总天数"
                    },
                    "template_schedule": {
                      "type": "array",
                      "items": {
                        "type": "object",
                        "properties": {
                          "day_number": {
                            "type": "integer",
                            "minimum": 1
                          },
                          "is_rest_day": {
                            "type": "boolean",
                            "default": false
                          },
                          "template_id": {
                            "type": "string",
                            "description": "训练模板ID（非休息日必填）"
                          },
                          "notes": {
                            "type": "string",
                            "description": "当日备注"
                          }
                        },
                        "required": ["day_number", "is_rest_day"]
                      },
                      "description": "训练安排"
                    },
                    "tags": {
                      "type": "array",
                      "items": {
                        "type": "string"
                      },
                      "maxItems": 10,
                      "description": "标签列表"
                    },
                    "is_public": {
                      "type": "boolean",
                      "default": false,
                      "description": "是否公开"
                    }
                  },
                  "required": ["name", "total_days", "template_schedule"]
                }
            """,
        )

        /**
         * 空白计划生成函数（保持向后兼容）
         */
        val FN_GENERATE_BLANK = FunctionDescriptor(
            name = "gymbro.plan.generate_blank",
            description = "Generate a blank workout plan structure for user customization",
            jsonSchema = /* language=json */ """
                {
                  "type": "object",
                  "additionalProperties": false,
                  "properties": {
                    "weeks": {
                      "type": "integer",
                      "minimum": 1,
                      "maximum": 12,
                      "default": 4,
                      "description": "计划周数"
                    },
                    "days_per_week": {
                      "type": "integer",
                      "minimum": 1,
                      "maximum": 7,
                      "default": 3,
                      "description": "每周训练天数"
                    }
                  }
                }
            """,
        )
    }

    /**
     * 训练会话域函数集合
     * 负责训练会话的开始、记录、完成等功能
     */
    object Session {
        /**
         * 开始训练会话函数
         */
        val FN_START = FunctionDescriptor(
            name = "gymbro.session.start",
            description = "Start a new workout session from template or blank",
            jsonSchema = /* language=json */ """
                {
                  "type": "object",
                  "additionalProperties": false,
                  "properties": {
                    "template_id": {
                      "type": "string",
                      "description": "模板ID，如果从模板开始"
                    },
                    "session_name": {
                      "type": "string",
                      "description": "会话名称，如'今日胸部训练'"
                    }
                  }
                }
            """,
        )

        /**
         * 记录组数函数
         */
        val FN_LOG_SET = FunctionDescriptor(
            name = "gymbro.session.log_set",
            description = "Log a set of exercise during workout session",
            jsonSchema = /* language=json */ """
                {
                  "type": "object",
                  "additionalProperties": false,
                  "properties": {
                    "exercise_id": {
                      "type": "string",
                      "description": "动作ID"
                    },
                    "reps": {
                      "type": "integer",
                      "minimum": 1,
                      "description": "次数"
                    },
                    "weight": {
                      "type": "number",
                      "minimum": 0,
                      "description": "重量（kg）"
                    },
                    "notes": {
                      "type": "string",
                      "description": "备注"
                    }
                  },
                  "required": ["exercise_id", "reps"]
                }
            """,
        )

        /**
         * 完成训练会话函数
         */
        val FN_COMPLETE = FunctionDescriptor(
            name = "gymbro.session.complete",
            description = "Complete the current workout session",
            jsonSchema = /* language=json */ """
                {
                  "type": "object",
                  "additionalProperties": false,
                  "properties": {
                    "rating": {
                      "type": "integer",
                      "minimum": 1,
                      "maximum": 5,
                      "description": "训练评分（1-5星）"
                    },
                    "notes": {
                      "type": "string",
                      "description": "训练总结"
                    }
                  }
                }
            """,
        )
    }

    /**
     * 所有动作库域函数列表
     */
    val EXERCISE_FUNCTIONS = listOf(
        Exercise.FN_SEARCH,
        Exercise.FN_GET_DETAIL,
        Exercise.FN_UPSERT,
    )

    /**
     * 所有训练模板域函数列表
     */
    val TEMPLATE_FUNCTIONS = listOf(
        Template.FN_SEARCH,
        Template.FN_GENERATE,
    )

    /**
     * 所有训练计划域函数列表
     */
    val PLAN_FUNCTIONS = listOf(
        Plan.FN_SEARCH,
        Plan.FN_GET_DETAIL,
        Plan.FN_UPSERT,
        Plan.FN_GENERATE_BLANK,
    )

    /**
     * 所有训练会话域函数列表
     */
    val SESSION_FUNCTIONS = listOf(
        Session.FN_START,
        Session.FN_LOG_SET,
        Session.FN_COMPLETE,
    )

    /**
     * 核心函数清单 - 覆盖80%需求的最小函数集合
     */
    val CORE_FUNCTIONS = listOf(
        // 动作库域 (3个函数)
        Exercise.FN_SEARCH,
        Exercise.FN_GET_DETAIL,
        Exercise.FN_UPSERT,

        // 训练模板域 (2个函数)
        Template.FN_SEARCH,
        Template.FN_GENERATE,

        // 训练计划域 (4个函数)
        Plan.FN_SEARCH,
        Plan.FN_GET_DETAIL,
        Plan.FN_UPSERT,
        Plan.FN_GENERATE_BLANK,

        // 训练会话域 (3个函数)
        Session.FN_START,
        Session.FN_LOG_SET,
        Session.FN_COMPLETE,
    )

    /**
     * 所有函数列表
     */
    val ALL_FUNCTIONS = EXERCISE_FUNCTIONS + TEMPLATE_FUNCTIONS + PLAN_FUNCTIONS + SESSION_FUNCTIONS

    /**
     * 获取指定域的函数列表
     */
    fun getFunctionsByDomain(domain: String): List<FunctionDescriptor> {
        return when (domain.lowercase()) {
            "exercise" -> EXERCISE_FUNCTIONS
            "template" -> TEMPLATE_FUNCTIONS
            "plan" -> PLAN_FUNCTIONS
            "session" -> SESSION_FUNCTIONS
            else -> emptyList()
        }
    }

    /**
     * 生成OpenAI格式的函数Schema
     */
    fun generateOpenAISchema(functions: List<FunctionDescriptor> = CORE_FUNCTIONS): List<Map<String, Any>> {
        return functions.map { function ->
            mapOf(
                "type" to "function",
                "function" to mapOf(
                    "name" to function.name,
                    "description" to function.description,
                    "parameters" to kotlinx.serialization.json.Json.parseToJsonElement(function.jsonSchema),
                ),
            )
        }
    }
}
