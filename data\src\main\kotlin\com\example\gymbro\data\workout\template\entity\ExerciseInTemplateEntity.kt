package com.example.gymbro.data.workout.template.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * 模板中的动作实体 - TemplateDB 关联实体
 *
 * 基于 05_Final_Database_Architecture.md 设计
 * 存储模板中包含的动作配置信息
 */
@Entity(
    tableName = "template_exercises",
    foreignKeys = [
        ForeignKey(
            entity = TemplateEntity::class,
            parentColumns = ["id"],
            childColumns = ["templateId"],
            onDelete = ForeignKey.CASCADE,
        ),
    ],
    indices = [
        Index("templateId"),
        Index("exerciseId"),
        Index("order"),
        Index("supersetGroupId"),
    ],
)
data class ExerciseInTemplateEntity(
    @PrimaryKey
    val id: String,

    // 关联信息
    val templateId: String, // 关联的模板ID
    val exerciseId: String, // 引用 ExerciseEntity.id
    val order: Int, // 在模板中的顺序

    // 训练配置
    val sets: Int, // 组数
    val repsPerSet: String, // 每组次数，可能是范围如"8-12"
    val weightSuggestion: String?, // 重量建议，可能是范围或百分比
    val restSeconds: Int, // 组间休息时间（秒）
    val notes: String?, // 备注

    // 超级组配置
    val superset: Boolean = false, // 是否为超级组
    val supersetGroupId: String? = null, // 超级组ID，同一组内的动作连续执行
)
