package com.example.gymbro.data.shared.validation

import com.example.gymbro.data.coach.dao.ChatRawDao
import com.example.gymbro.data.coach.dao.ChatSearchDao
import com.example.gymbro.data.local.database.AppDatabase
import javax.inject.Inject

class DataSyncValidationService @Inject constructor(
    private val appDatabase: AppDatabase,
    private val chatRawDao: ChatRawDao,
    private val chatSearchDao: ChatSearchDao
) {

    suspend fun validateSync(): Boolean {
        val rawCount = chatRawDao.getChatMessageCount()
        val ftsCount = getFtsCount()
        val vecCount = getVecCount()

        return rawCount == ftsCount && rawCount == vecCount
    }

    private fun getFtsCount(): Int {
        val cursor = appDatabase.query("SELECT COUNT(*) FROM chat_fts", null)
        return if (cursor.moveToFirst()) cursor.getInt(0) else 0
    }

    private fun getVecCount(): Int {
        val cursor = appDatabase.query("SELECT COUNT(*) FROM chat_vec", null)
        return if (cursor.moveToFirst()) cursor.getInt(0) else 0
    }
}
