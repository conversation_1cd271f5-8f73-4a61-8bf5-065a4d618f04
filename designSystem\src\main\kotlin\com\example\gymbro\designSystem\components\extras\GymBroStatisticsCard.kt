package com.example.gymbro.designSystem.components.extras

import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.TrendingUp
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.FitnessCenter
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens

/**
 * 通用统计数据卡片组件
 *
 * 采用平面化设计，符合黑白双色基调的克制风格。
 * 支持UiText国际化，可在多个模块复用。
 *
 * @param title 统计项标题
 * @param value 统计数值
 * @param icon 统计项图标
 * @param onClick 点击回调，为null时不可点击
 * @param modifier 修饰符
 * @param isCompact 是否使用紧凑布局（正方形）
 * @param backgroundColor 背景色
 * @param contentColor 内容色
 * @param showTrend 是否显示趋势图标
 * @param trendIcon 趋势图标
 * @param isPlaceholder 是否为未设置状态的占位符样式
 */
@Composable
fun GymBroStatisticsCard(
    title: UiText,
    value: UiText,
    icon: ImageVector,
    onClick: (() -> Unit)? = null,
    modifier: Modifier = Modifier,
    isCompact: Boolean = false,
    backgroundColor: Color = MaterialTheme.colorScheme.primaryContainer,
    contentColor: Color = MaterialTheme.colorScheme.onPrimaryContainer,
    showTrend: Boolean = false,
    trendIcon: ImageVector = Icons.AutoMirrored.Filled.TrendingUp,
    isPlaceholder: Boolean = false,
) {
    if (isPlaceholder) {
        PlaceholderStatisticsCard(
            value = value.asString(),
            label = title.asString(),
            icon = icon,
            modifier = modifier,
            onClick = onClick,
        )
    } else if (isCompact) {
        CompactStatisticsCard(
            value = value.asString(),
            label = title.asString(),
            icon = icon,
            modifier = modifier,
            backgroundColor = backgroundColor,
            contentColor = contentColor,
            onClick = onClick,
        )
    } else {
        StandardStatisticsCard(
            value = value.asString(),
            label = title.asString(),
            icon = icon,
            modifier = modifier,
            backgroundColor = backgroundColor,
            contentColor = contentColor,
            showTrend = showTrend,
            trendIcon = trendIcon,
            onClick = onClick,
        )
    }
}

/**
 * 标准统计卡片（横向布局）
 */
@Composable
private fun StandardStatisticsCard(
    value: String,
    label: String,
    icon: ImageVector,
    modifier: Modifier = Modifier,
    backgroundColor: Color = MaterialTheme.colorScheme.primaryContainer,
    contentColor: Color = MaterialTheme.colorScheme.onPrimaryContainer,
    showTrend: Boolean = false,
    trendIcon: ImageVector = Icons.AutoMirrored.Filled.TrendingUp,
    onClick: (() -> Unit)? = null,
) {
    Card(
        modifier =
        modifier
            .fillMaxWidth()
            .height(64.dp),
        // 美化方案：与菜单项高度对齐
        elevation = CardDefaults.cardElevation(defaultElevation = Tokens.Elevation.Card),
        shape = RoundedCornerShape(12.dp), // 美化方案：与菜单组对齐，使用12dp统一圆角
        colors =
        CardDefaults.cardColors(
            containerColor = backgroundColor,
        ),
        onClick = onClick ?: {}, // 美化方案：使用 Card 的内置波纹效果
    ) {
        Row(
            modifier =
            Modifier
                .fillMaxWidth()
                .padding(vertical = Tokens.Spacing.Small, horizontal = Tokens.Spacing.Large),
            // 美化方案：适应64dp高度
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            // 图标区域
            Surface(
                modifier = Modifier.size(Tokens.Spacing.XLarge), // 美化方案：适应64dp高度，使用32dp图标区域
                shape = RoundedCornerShape(12.dp), // 美化方案：与菜单组对齐，使用12dp统一圆角
                color = contentColor.copy(alpha = 0.15f),
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center,
                ) {
                    Icon(
                        imageVector = icon,
                        contentDescription = label,
                        modifier = Modifier.size(Tokens.Spacing.Large),
                        tint = contentColor,
                    )
                }
            }

            // 数值和标签
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.XSmall),
            ) {
                Text(
                    text = value,
                    style = MaterialTheme.typography.titleLarge, // 美化方案：适应64dp高度，使用较小字体
                    fontWeight = FontWeight.Bold,
                    color = contentColor,
                )
                Text(
                    text = label,
                    style = MaterialTheme.typography.bodyMedium,
                    color = contentColor.copy(alpha = 0.8f),
                )
            }

            // 趋势图标（可选）
            if (showTrend) {
                Surface(
                    modifier = Modifier.size(Tokens.Spacing.XLarge),
                    shape = RoundedCornerShape(Tokens.Radius.Small),
                    color = MaterialTheme.colorScheme.tertiary.copy(alpha = 0.2f),
                ) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center,
                    ) {
                        Icon(
                            imageVector = trendIcon,
                            contentDescription = "趋势",
                            modifier = Modifier.size(Tokens.Spacing.Medium),
                            tint = MaterialTheme.colorScheme.tertiary,
                        )
                    }
                }
            }
        }
    }
}

/**
 * 紧凑型统计卡片（正方形布局）
 */
@Composable
private fun CompactStatisticsCard(
    value: String,
    label: String,
    icon: ImageVector,
    modifier: Modifier = Modifier,
    backgroundColor: Color = MaterialTheme.colorScheme.secondaryContainer,
    contentColor: Color = MaterialTheme.colorScheme.onSecondaryContainer,
    onClick: (() -> Unit)? = null,
) {
    Card(
        modifier = modifier.aspectRatio(1f),
        elevation = CardDefaults.cardElevation(defaultElevation = Tokens.Elevation.Card),
        shape = RoundedCornerShape(Tokens.Radius.Card),
        colors =
        CardDefaults.cardColors(
            containerColor = backgroundColor,
        ),
        onClick = onClick ?: {},
    ) {
        Column(
            modifier =
            Modifier
                .fillMaxSize()
                .padding(Tokens.Spacing.Medium),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceEvenly,
        ) {
            Icon(
                imageVector = icon,
                contentDescription = label,
                modifier = Modifier.size(Tokens.Spacing.XLarge),
                tint = contentColor,
            )

            Text(
                text = value,
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                color = contentColor,
            )

            Text(
                text = label,
                style = MaterialTheme.typography.bodySmall,
                color = contentColor.copy(alpha = 0.8f),
            )
        }
    }
}

/**
 * 占位符统计卡片（未设置状态）
 * 美化方案：分割线式 Placeholder
 */
@Composable
private fun PlaceholderStatisticsCard(
    value: String,
    label: String,
    icon: ImageVector,
    modifier: Modifier = Modifier,
    onClick: (() -> Unit)? = null,
) {
    Surface(
        modifier =
        modifier
            .fillMaxWidth()
            .height(64.dp) // 美化方案：与菜单项高度对齐
            .border(
                width = 1.dp,
                color = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f), // BorderSubtle
                shape = RoundedCornerShape(12.dp), // 美化方案：与菜单组对齐，使用12dp统一圆角
            ).then(
                if (onClick != null) {
                    Modifier.clickable { onClick() } // 美化方案：使用默认波纹效果
                } else {
                    Modifier
                },
            ),
        shape = RoundedCornerShape(12.dp), // 美化方案：与菜单组对齐，使用12dp统一圆角
        color = Color.Transparent, // 美化方案：透明背景
    ) {
        Row(
            modifier =
            Modifier
                .fillMaxWidth()
                .padding(vertical = Tokens.Spacing.Small, horizontal = Tokens.Spacing.Large),
            // 美化方案：适应64dp高度
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            // 图标区域
            Surface(
                modifier = Modifier.size(Tokens.Spacing.XLarge), // 美化方案：适应64dp高度，使用32dp图标区域
                shape = RoundedCornerShape(12.dp), // 美化方案：与菜单组对齐，使用12dp统一圆角
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.08f), // 置灰背景
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center,
                ) {
                    Icon(
                        imageVector = icon,
                        contentDescription = label,
                        modifier = Modifier.size(Tokens.Spacing.Large),
                        tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f), // OnSurfaceDisabled
                    )
                }
            }

            // 数值和标签
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.XSmall),
            ) {
                Text(
                    text = value,
                    style = MaterialTheme.typography.titleLarge, // 美化方案：适应64dp高度，使用较小字体
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f), // OnSurfaceDisabled
                )
                Text(
                    text = label,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f), // OnSurfaceDisabled
                )
            }

            // "+" 角标引导设置
            Surface(
                modifier = Modifier.size(Tokens.Spacing.XLarge),
                shape = RoundedCornerShape(Tokens.Radius.Small),
                color = MaterialTheme.colorScheme.primary.copy(alpha = 0.12f),
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center,
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "点击设置",
                        modifier = Modifier.size(Tokens.Spacing.Medium),
                        tint = MaterialTheme.colorScheme.primary,
                    )
                }
            }
        }
    }
}

// ===== Preview Functions =====

@GymBroPreview
@Composable
private fun GymBroStatisticsCardStandardPreview() {
    GymBroTheme {
        GymBroStatisticsCard(
            title = UiText.DynamicString("训练次数"),
            value = UiText.DynamicString("42"),
            icon = Icons.Default.FitnessCenter,
            showTrend = true,
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroStatisticsCardCompactPreview() {
    GymBroTheme {
        Row(
            modifier = Modifier.padding(Tokens.Spacing.Medium),
            horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            GymBroStatisticsCard(
                title = UiText.DynamicString("训练"),
                value = UiText.DynamicString("42"),
                icon = Icons.Default.FitnessCenter,
                isCompact = true,
                modifier = Modifier.weight(1f),
            )
            GymBroStatisticsCard(
                title = UiText.DynamicString("天数"),
                value = UiText.DynamicString("128"),
                icon = Icons.AutoMirrored.Filled.TrendingUp,
                isCompact = true,
                modifier = Modifier.weight(1f),
            )
        }
    }
}

@GymBroPreview
@Composable
private fun GymBroStatisticsCardDarkPreview() {
    GymBroTheme(darkTheme = true) {
        Column(
            modifier = Modifier.padding(Tokens.Spacing.Medium),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            GymBroStatisticsCard(
                title = UiText.DynamicString("当前体重"),
                value = UiText.DynamicString("68.5kg"),
                icon = Icons.Default.FitnessCenter,
                showTrend = true,
            )
            GymBroStatisticsCard(
                title = UiText.DynamicString("连续签到"),
                value = UiText.DynamicString("365"),
                icon = Icons.AutoMirrored.Filled.TrendingUp,
                backgroundColor = MaterialTheme.colorScheme.secondaryContainer,
                contentColor = MaterialTheme.colorScheme.onSecondaryContainer,
            )
        }
    }
}

@GymBroPreview
@Composable
private fun GymBroStatisticsCardPlaceholderPreview() {
    GymBroTheme {
        Column(
            modifier = Modifier.padding(Tokens.Spacing.Medium),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            // 正常状态
            GymBroStatisticsCard(
                title = UiText.DynamicString("训练次数"),
                value = UiText.DynamicString("42"),
                icon = Icons.Default.FitnessCenter,
                showTrend = true,
            )
            // 未设置状态 - 美化方案的分割线式 Placeholder
            GymBroStatisticsCard(
                title = UiText.DynamicString("训练日设置"),
                value = UiText.DynamicString("未设置"),
                icon = Icons.Default.FitnessCenter,
                isPlaceholder = true,
                onClick = { /* 点击设置 */ },
            )
        }
    }
}
