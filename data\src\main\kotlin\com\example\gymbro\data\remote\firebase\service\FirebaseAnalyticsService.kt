package com.example.gymbro.data.remote.firebase.service

import android.os.Bundle
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.ui.text.UiText
import com.google.firebase.analytics.FirebaseAnalytics
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Firebase分析服务
 * 负责应用事件跟踪和用户行为分析
 */
@Singleton
class FirebaseAnalyticsService @Inject constructor(
    private val firebaseAnalytics: FirebaseAnalytics,
) {

    companion object {
        // 自定义事件名称
        const val EVENT_WORKOUT_STARTED = "workout_started"
        const val EVENT_WORKOUT_COMPLETED = "workout_completed"
        const val EVENT_WORKOUT_PAUSED = "workout_paused"
        const val EVENT_AI_COACH_INTERACTION = "ai_coach_interaction"
        const val EVENT_SUBSCRIPTION_UPGRADED = "subscription_upgraded"
        const val EVENT_PAYMENT_COMPLETED = "payment_completed"
        const val EVENT_FEATURE_ACCESSED = "feature_accessed"
        const val EVENT_SETTINGS_CHANGED = "settings_changed"
        const val EVENT_SYNC_COMPLETED = "sync_completed"
        const val EVENT_ERROR_OCCURRED = "error_occurred"

        // 事件参数
        const val PARAM_WORKOUT_TYPE = "workout_type"
        const val PARAM_WORKOUT_DURATION = "workout_duration"
        const val PARAM_EXERCISE_COUNT = "exercise_count"
        const val PARAM_AI_QUERY_TYPE = "ai_query_type"
        const val PARAM_SUBSCRIPTION_PLAN = "subscription_plan"
        const val PARAM_PAYMENT_METHOD = "payment_method"
        const val PARAM_FEATURE_NAME = "feature_name"
        const val PARAM_SETTING_NAME = "setting_name"
        const val PARAM_SETTING_VALUE = "setting_value"
        const val PARAM_SYNC_TYPE = "sync_type"
        const val PARAM_ERROR_TYPE = "error_type"
        const val PARAM_ERROR_CODE = "error_code"
        const val PARAM_SCREEN_NAME = "screen_name"
        const val PARAM_USER_TYPE = "user_type" // anonymous, registered, premium

        // 用户属性
        const val USER_PROPERTY_SUBSCRIPTION_STATUS = "subscription_status"
        const val USER_PROPERTY_USER_TYPE = "user_type"
    }

    /**
     * 设置用户属性
     */
    fun setUserProperty(name: String, value: String): ModernResult<Unit> {
        return try {
            firebaseAnalytics.setUserProperty(name, value)
            Timber.d("User property set: $name = $value")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "Failed to set user property: $name = $value")
            ModernResult.Error(
                DataErrors.DataError.access(
                    operationName = "FirebaseAnalyticsService.setUserProperty",
                    message = UiText.DynamicString("设置用户属性失败"),
                    entityType = "UserProperty",
                    cause = e,
                    metadataMap = mapOf("propertyName" to name, "propertyValue" to value),
                ),
            )
        }
    }

    /**
     * 记录自定义事件
     * @param eventName 事件名称
     * @param parameters 事件参数，默认为空Map
     */
    fun logEvent(
        eventName: String,
        parameters: Map<String, Any> = emptyMap(),
    ): ModernResult<Unit> {
        return try {
            // 使用正确的Firebase Analytics API
            val bundle = Bundle().apply {
                parameters.forEach { (key, value) ->
                    when (value) {
                        is String -> putString(key, value)
                        is Int -> putInt(key, value)
                        is Long -> putLong(key, value)
                        is Double -> putDouble(key, value)
                        is Boolean -> putBoolean(key, value)
                        else -> putString(key, value.toString())
                    }
                }
            }
            firebaseAnalytics.logEvent(eventName, bundle)
            Timber.d("Event logged: $eventName with parameters: $parameters")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "Failed to log event: $eventName")
            ModernResult.Error(
                DataErrors.DataError.access(
                    operationName = "FirebaseAnalyticsService.logEvent",
                    message = UiText.DynamicString("记录事件失败"),
                    entityType = "AnalyticsEvent",
                    cause = e,
                    metadataMap = mapOf(
                        "eventName" to eventName,
                        "parameterCount" to parameters.size.toString(),
                    ),
                ),
            )
        }
    }

    // === 锻炼相关事件 ===

    // === AI教练相关事件 ===

    // === 订阅和支付相关事件 ===

    // === 应用使用相关事件 ===

    // === 便捷方法 ===
}
