package com.example.gymbro.data.ai.function

import com.example.gymbro.shared.models.common.JsonHeader
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.*

/**
 * 训练计划生成 Function Call 定义 - EntityWrapper统一表头规范
 * ✅ 升级：强制AI输出EntityWrapper格式，确保JSON表头统一
 * ✅ 增强：添加字段约束、过期保护、大小限制
 *
 * Function Name: fc_generate_workout_plan
 * 用途: 生成结构化的训练计划，包含周期安排和渐进策略
 */
object WorkoutPlanFunctionDef {
    const val FUNCTION_NAME = "fc_generate_workout_plan"

    /**
     * Function Call JSON Schema 定义 - EntityWrapper格式
     */
    val schema: JsonObject = buildEntityWrapperPlanSchema()

    /**
     * 构建EntityWrapper格式的训练计划JSON Schema
     */
    private fun buildEntityWrapperPlanSchema(): JsonObject =
        buildJsonObject {
            put("name", FUNCTION_NAME)
            put("description", "生成结构化的训练计划，必须使用EntityWrapper格式包装")
            put(
                "parameters",
                buildJsonObject {
                    put("type", "object")
                    put("additionalProperties", JsonPrimitive(false)) // ✅ 补丁1: 禁止额外字段
                    put(
                        "properties",
                        buildJsonObject {
                            // EntityWrapper统一表头字段
                            put(
                                "schemaVersion",
                                buildJsonObject {
                                    put("type", "string")
                                    put("enum", buildJsonArray { add(JsonHeader.SCHEMA_VERSION) })
                                    put("description", "Schema版本号")
                                },
                            )

                            put(
                                "entity",
                                buildJsonObject {
                                    put("type", "string")
                                    put("enum", buildJsonArray { add("PLAN") })
                                    put("description", "实体类型")
                                },
                            )

                            put(
                                "entityVersion",
                                buildJsonObject {
                                    put("type", "integer")
                                    put("minimum", 1)
                                    put("description", "实体版本号")
                                },
                            )

                            put(
                                "expires_at",
                                buildJsonObject {
                                    // ✅ 补丁2: 幽灵调用保护
                                    put("type", "integer")
                                    put("format", "utc-millis")
                                    put("description", "过期时间戳（毫秒）")
                                },
                            )

                            // 业务数据载荷
                            put(
                                "payload",
                                buildJsonObject {
                                    put("type", "object")
                                    put("additionalProperties", JsonPrimitive(false))
                                    put(
                                        "properties",
                                        buildJsonObject {
                                            // 基础信息
                                            put(
                                                "plan_name",
                                                buildJsonObject {
                                                    put("type", "string")
                                                    put("description", "训练计划名称")
                                                },
                                            )
                                            put(
                                                "description",
                                                buildJsonObject {
                                                    put("type", "string")
                                                    put("description", "计划描述和目标")
                                                },
                                            )
                                            put(
                                                "duration_type",
                                                buildJsonObject {
                                                    put("type", "string")
                                                    put(
                                                        "enum",
                                                        buildJsonArray {
                                                            add("weeks")
                                                            add("days")
                                                        },
                                                    )
                                                    put("description", "计划持续时间类型：weeks(按周) 或 days(按天)")
                                                },
                                            )

                                            // 周期设置 (添加约束限制)
                                            put(
                                                "weeks",
                                                buildJsonObject {
                                                    put("type", "integer")
                                                    put("minimum", 1)
                                                    put("maximum", JsonHeader.Plan.MAX_WEEKS) // ✅ 补丁3: 大小限制
                                                    put("description", "训练周期数(当duration_type=weeks时必填)")
                                                },
                                            )
                                            put(
                                                "days",
                                                buildJsonObject {
                                                    put("type", "integer")
                                                    put("minimum", 1)
                                                    put("maximum", 365)
                                                    put("description", "训练天数(当duration_type=days时必填)")
                                                },
                                            )
                                            put(
                                                "sessions_per_week",
                                                buildJsonObject {
                                                    put("type", "integer")
                                                    put("minimum", 1)
                                                    put("maximum", JsonHeader.Plan.MAX_DAYS_PER_WEEK)
                                                    put("description", "每周训练次数")
                                                },
                                            )

                                            // 训练强度和渐进策略
                                            put(
                                                "difficulty_level",
                                                buildJsonObject {
                                                    put("type", "string")
                                                    put(
                                                        "enum",
                                                        buildJsonArray {
                                                            add("beginner")
                                                            add("intermediate")
                                                            add("advanced")
                                                        },
                                                    )
                                                    put("description", "训练难度级别")
                                                },
                                            )
                                            put(
                                                "progressive_overload",
                                                buildJsonObject {
                                                    put("type", "object")
                                                    put(
                                                        "properties",
                                                        buildJsonObject {
                                                            put(
                                                                "strategy",
                                                                buildJsonObject {
                                                                    put("type", "string")
                                                                    put(
                                                                        "enum",
                                                                        buildJsonArray {
                                                                            add("linear")
                                                                            add("periodized")
                                                                            add("undulating")
                                                                        },
                                                                    )
                                                                    put("description", "渐进策略类型")
                                                                },
                                                            )
                                                            put(
                                                                "increment_percentage",
                                                                buildJsonObject {
                                                                    put("type", "number")
                                                                    put("minimum", 1.0)
                                                                    put("maximum", 20.0)
                                                                    put("description", "每周增长百分比(1-20%)")
                                                                },
                                                            )
                                                        },
                                                    )
                                                    put(
                                                        "required",
                                                        buildJsonArray {
                                                            add("strategy")
                                                        },
                                                    )
                                                },
                                            )

                                            // 训练内容
                                            put(
                                                "workout_focus",
                                                buildJsonObject {
                                                    put("type", "array")
                                                    put(
                                                        "items",
                                                        buildJsonObject {
                                                            put("type", "string")
                                                            put(
                                                                "enum",
                                                                buildJsonArray {
                                                                    add("strength")
                                                                    add("hypertrophy")
                                                                    add("endurance")
                                                                    add("power")
                                                                    add("fat_loss")
                                                                    add("functional")
                                                                    add("rehabilitation")
                                                                },
                                                            )
                                                        },
                                                    )
                                                    put("description", "训练重点(可多选)")
                                                },
                                            )
                                            put(
                                                "target_muscles",
                                                buildJsonObject {
                                                    put("type", "array")
                                                    put(
                                                        "items",
                                                        buildJsonObject {
                                                            put("type", "string")
                                                        },
                                                    )
                                                    put("description", "目标肌肉群")
                                                },
                                            )

                                            // 计划结构
                                            put(
                                                "weekly_structure",
                                                buildJsonObject {
                                                    put("type", "array")
                                                    put(
                                                        "items",
                                                        buildJsonObject {
                                                            put("type", "object")
                                                            put(
                                                                "properties",
                                                                buildJsonObject {
                                                                    put(
                                                                        "week_number",
                                                                        buildJsonObject {
                                                                            put("type", "integer")
                                                                            put("minimum", 1)
                                                                        },
                                                                    )
                                                                    put(
                                                                        "workout_days",
                                                                        buildJsonObject {
                                                                            put("type", "array")
                                                                            put(
                                                                                "items",
                                                                                buildJsonObject {
                                                                                    put("type", "object")
                                                                                    put(
                                                                                        "properties",
                                                                                        buildJsonObject {
                                                                                            put(
                                                                                                "day",
                                                                                                buildJsonObject {
                                                                                                    put(
                                                                                                        "type",
                                                                                                        "string",
                                                                                                    )
                                                                                                    put(
                                                                                                        "enum",
                                                                                                        buildJsonArray {
                                                                                                            add(
                                                                                                                "monday",
                                                                                                            )
                                                                                                            add(
                                                                                                                "tuesday",
                                                                                                            )
                                                                                                            add(
                                                                                                                "wednesday",
                                                                                                            )
                                                                                                            add(
                                                                                                                "thursday",
                                                                                                            )
                                                                                                            add(
                                                                                                                "friday",
                                                                                                            )
                                                                                                            add(
                                                                                                                "saturday",
                                                                                                            )
                                                                                                            add(
                                                                                                                "sunday",
                                                                                                            )
                                                                                                        },
                                                                                                    )
                                                                                                },
                                                                                            )
                                                                                            put(
                                                                                                "template_id",
                                                                                                buildJsonObject {
                                                                                                    put(
                                                                                                        "type",
                                                                                                        "string",
                                                                                                    )
                                                                                                    put(
                                                                                                        "description",
                                                                                                        "训练模板ID(如果使用现有模板)",
                                                                                                    )
                                                                                                },
                                                                                            )
                                                                                            put(
                                                                                                "focus_areas",
                                                                                                buildJsonObject {
                                                                                                    put(
                                                                                                        "type",
                                                                                                        "array",
                                                                                                    )
                                                                                                    put(
                                                                                                        "items",
                                                                                                        buildJsonObject {
                                                                                                            put(
                                                                                                                "type",
                                                                                                                "string",
                                                                                                            )
                                                                                                        },
                                                                                                    )
                                                                                                    put(
                                                                                                        "description",
                                                                                                        "当天训练重点",
                                                                                                    )
                                                                                                },
                                                                                            )
                                                                                            put(
                                                                                                "intensity",
                                                                                                buildJsonObject {
                                                                                                    put(
                                                                                                        "type",
                                                                                                        "string",
                                                                                                    )
                                                                                                    put(
                                                                                                        "enum",
                                                                                                        buildJsonArray {
                                                                                                            add(
                                                                                                                "light",
                                                                                                            )
                                                                                                            add(
                                                                                                                "moderate",
                                                                                                            )
                                                                                                            add(
                                                                                                                "high",
                                                                                                            )
                                                                                                            add(
                                                                                                                "recovery",
                                                                                                            )
                                                                                                        },
                                                                                                    )
                                                                                                    put(
                                                                                                        "description",
                                                                                                        "训练强度",
                                                                                                    )
                                                                                                },
                                                                                            )
                                                                                        },
                                                                                    )
                                                                                    put(
                                                                                        "required",
                                                                                        buildJsonArray {
                                                                                            add("day")
                                                                                            add("focus_areas")
                                                                                            add("intensity")
                                                                                        },
                                                                                    )
                                                                                },
                                                                            )
                                                                        },
                                                                    )
                                                                    put(
                                                                        "deload_week",
                                                                        buildJsonObject {
                                                                            put("type", "boolean")
                                                                            put("description", "是否为减载周")
                                                                        },
                                                                    )
                                                                },
                                                            )
                                                            put(
                                                                "required",
                                                                buildJsonArray {
                                                                    add("week_number")
                                                                    add("workout_days")
                                                                },
                                                            )
                                                        },
                                                    )
                                                    put("description", "每周训练结构")
                                                },
                                            )

                                            // 备注和建议
                                            put(
                                                "notes",
                                                buildJsonObject {
                                                    put("type", "string")
                                                    put("description", "额外注意事项和建议")
                                                },
                                            )
                                            put(
                                                "equipment_needed",
                                                buildJsonObject {
                                                    put("type", "array")
                                                    put(
                                                        "items",
                                                        buildJsonObject {
                                                            put("type", "string")
                                                        },
                                                    )
                                                    put("description", "所需设备清单")
                                                },
                                            )
                                        },
                                    )
                                    put(
                                        "required",
                                        buildJsonArray {
                                            add("plan_name")
                                            add("description")
                                            add("duration_type")
                                            add("sessions_per_week")
                                            add("difficulty_level")
                                            add("workout_focus")
                                            add("weekly_structure")
                                        },
                                    )
                                },
                            )
                        },
                    )
                    put(
                        "required",
                        buildJsonArray {
                            add("schemaVersion")
                            add("entity")
                            add("entityVersion")
                            add("expires_at")
                            add("payload")
                        },
                    )
                },
            )
        }

    /**
     * 验证Function Call参数
     */
    fun validateArguments(arguments: JsonObject): ValidationResult =
        try {
            val planName = arguments["plan_name"]?.toString()
            val durationType = arguments["duration_type"]?.toString()
            val sessionsPerWeek = arguments["sessions_per_week"]?.toString()?.toIntOrNull()
            val workoutFocus = arguments["workout_focus"]
            val weeklyStructure = arguments["weekly_structure"]

            when {
                planName.isNullOrBlank() -> ValidationResult.Error("计划名称不能为空")
                durationType != "weeks" && durationType != "days" -> ValidationResult.Error(
                    "duration_type 必须是 'weeks' 或 'days'",
                )
                sessionsPerWeek == null || sessionsPerWeek !in 1..7 -> ValidationResult.Error(
                    "每周训练次数必须在1-7之间",
                )
                workoutFocus == null -> ValidationResult.Error("workout_focus 不能为空")
                weeklyStructure == null -> ValidationResult.Error("weekly_structure 不能为空")
                durationType == "weeks" && arguments["weeks"] == null -> ValidationResult.Error(
                    "选择周期类型时必须指定weeks参数",
                )
                durationType == "days" && arguments["days"] == null -> ValidationResult.Error(
                    "选择天数类型时必须指定days参数",
                )
                else -> ValidationResult.Success
            }
        } catch (e: Exception) {
            ValidationResult.Error("参数验证异常: ${e.message}")
        }

    /**
     * 参数验证结果
     */
    sealed class ValidationResult {
        object Success : ValidationResult()

        data class Error(
            val message: String,
        ) : ValidationResult()
    }
}

/**
 * 训练计划Function Call参数数据类
 */
@Serializable
data class WorkoutPlanFunctionCallArgs(
    val plan_name: String,
    val description: String,
    val duration_type: String, // "weeks" or "days"
    val weeks: Int? = null,
    val days: Int? = null,
    val sessions_per_week: Int,
    val difficulty_level: String, // "beginner", "intermediate", "advanced"
    val progressive_overload: ProgressiveOverloadConfig? = null,
    val workout_focus: List<String>,
    val target_muscles: List<String>? = null,
    val weekly_structure: List<WeeklyStructure>,
    val notes: String? = null,
    val equipment_needed: List<String>? = null,
)

@Serializable
data class ProgressiveOverloadConfig(
    val strategy: String, // "linear", "periodized", "undulating"
    val increment_percentage: Double? = null,
)

@Serializable
data class WeeklyStructure(
    val week_number: Int,
    val workout_days: List<WorkoutDay>,
    val deload_week: Boolean = false,
)

@Serializable
data class WorkoutDay(
    val day: String, // "monday", "tuesday", etc.
    val template_id: String? = null,
    val focus_areas: List<String>,
    val intensity: String, // "light", "moderate", "high", "recovery"
)
