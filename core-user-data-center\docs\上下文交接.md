# UserDataCenter 项目上下文交接文档

## 📋 项目概述

本项目正在实施 **GymBro 用户数据统一管理方案**，通过创建 `core-user-data-center` 模块作为用户数据的单一真实来源（SSOT），解决 Auth、Profile、Coach 模块间的数据分散和不一致问题。

## ✅ 已完成工作（Steps 1-3）

### Step 1-2: UserDataCenter 模块创建与实现 ✅

#### 核心架构组件
- **API 层**:
  - `UserDataCenterApi.kt`: 8个核心方法的完整接口
  - `UnifiedUserData.kt`: 统一用户数据模型（认证+个人资料）
  - `UserDataState.kt`: 响应式状态管理（Loading/Success/Error/Empty/Syncing）
  - `SyncStatus.kt`: 数据同步状态枚举

- **实现层**:
  - `UserDataRepositoryImpl.kt`: 基于 StateFlow 的内存缓存实现
  - `UserDataCenterApiImpl.kt`: 完整的 API 实现类
  - `UserDataMapper.kt`: 数据转换映射器
  - `UserDataSynchronizer.kt`: 简化版数据同步器

- **依赖注入**:
  - `UserDataCenterModule.kt`: Hilt 模块配置
  - 所有接口正确绑定到实现类

#### 编译状态
- ✅ **编译成功**: `./gradlew :core-user-data-center:compileDebugKotlin`
- ⚠️ 仅有类型转换警告，功能正常

### Step 3: Auth 模块集成 ✅

#### 完成的集成工作
- **依赖配置**:
  - `features/auth/build.gradle.kts`: 添加 core-user-data-center 依赖
  - `data/build.gradle.kts`: 添加 core-user-data-center 依赖

- **AuthRepositoryImpl 集成**:
  - 注入 `UserDataCenterApi` 到构造函数
  - 在所有认证成功场景后自动同步数据：
    - `loginWithEmailPassword()` - 邮箱登录
    - `loginWithThirdParty()` - Google登录
    - `register()` - 用户注册
  - 实现 `syncAuthDataToUserDataCenter()` 异步同步方法

#### 数据流设计
```
用户认证成功 → AuthRepositoryImpl → UserDataCenterApi.syncAuthData() 
→ UserDataRepository → StateFlow 更新 → 所有消费模块自动获取最新数据
```

## 🎯 下一步工作计划（Steps 4-8）

### Step 4: Profile 模块集成 UserDataCenter 🔄

#### 目标
重构 Profile 模块使用 UserDataCenter 作为统一数据源，替代直接的数据库访问。

#### 具体任务
1. **修改 ProfileFeatureApiImpl**:
   - 注入 `UserDataCenterApi` 依赖
   - 重构 `observeUserAiContext()` 方法使用 `userDataCenterApi.observeUserData()`
   - 实现数据转换：`UnifiedUserData` → `UserAiContext`

2. **更新用户资料更新流程**:
   - 修改用户资料保存逻辑调用 `userDataCenterApi.updateProfile()`
   - 确保数据变更自动同步到所有消费模块

3. **依赖配置**:
   - `features/profile/build.gradle.kts`: 添加 core-user-data-center 依赖

#### 关键文件
- `features/profile/src/main/kotlin/com/example/gymbro/features/profile/api/ProfileFeatureApiImpl.kt`
- `features/profile/src/main/kotlin/com/example/gymbro/features/profile/ui/viewmodel/ProfileViewModel.kt`

### Step 5: Coach 模块集成用户上下文 🔄

#### 目标
集成 Coach 模块获取用户上下文数据，实现 AI 个性化交互。

#### 具体任务
1. **修改 SendChatMessageAndGetResponseUseCase**:
   - 注入 `UserDataCenterApi` 依赖
   - 在发送消息前获取用户数据：`userDataCenterApi.getCurrentUserData()`
   - 集成用户上下文到 prompt 构建

2. **更新 LayeredPromptBuilder**:
   - 添加 `buildChatMessagesWithUserContext()` 方法
   - 支持用户数据作为 prompt 上下文层

3. **依赖配置**:
   - `features/coach/build.gradle.kts`: 添加 core-user-data-center 依赖

#### 关键文件
- `domain/src/main/kotlin/com/example/gymbro/domain/coach/usecase/SendChatMessageAndGetResponseUseCase.kt`
- `features/coach/src/main/kotlin/com/example/gymbro/features/coach/ai/prompt/LayeredPromptBuilder.kt`

### Step 6: 完整依赖注入配置 🔄

#### 目标
确保所有模块的依赖注入正确配置，UserDataCenter 在应用启动时正确初始化。

#### 具体任务
1. **应用级别配置**:
   - 确保 `UserDataCenterModule` 在应用启动时加载
   - 验证 `UserDataSynchronizer` 正确启动

2. **模块间依赖验证**:
   - 检查所有模块的 build.gradle.kts 依赖配置
   - 验证 Hilt 依赖图的正确性

### Step 7: 测试套件编写 🔄

#### 目标
编写全面的测试套件验证 UserDataCenter 功能。

#### 具体任务
1. **单元测试**:
   - `UserDataRepositoryImpl` 测试
   - `UserDataMapper` 测试
   - `UserDataSynchronizer` 测试

2. **集成测试**:
   - Auth → UserDataCenter → Profile 数据流测试
   - Coach 模块获取用户数据测试

### Step 8: 文档更新和部署验证 🔄

#### 目标
更新项目文档，验证完整功能。

#### 具体任务
1. **文档更新**:
   - 更新架构文档
   - 创建使用示例
   - 添加 API 文档

2. **部署验证**:
   - 完整编译测试
   - 功能验证
   - 性能测试

## 🔧 技术要点

### 核心设计原则
- **单一数据源（SSOT）**: UserDataCenter 作为所有用户数据的唯一真实来源
- **响应式架构**: 基于 StateFlow 的数据流，自动更新所有消费者
- **非阻塞同步**: 异步数据同步，不影响用户体验
- **容错设计**: 同步失败不影响核心功能

### 关键架构模式
- **Clean Architecture**: API/Internal 分离，依赖倒置
- **MVI 2.0**: 遵循项目既定的架构模式
- **Repository 模式**: 统一数据访问接口
- **Mapper 模式**: 数据模型转换

### 重要注意事项
1. **AuthUser 字段**: 使用 `authUser.uid` 而不是 `authUser.id`
2. **BusinessErrors 路径**: 使用 `com.example.gymbro.core.error.types.business.BusinessErrors`
3. **StateFlow 优化**: 避免在 StateFlow 上使用 `distinctUntilChanged()`
4. **异步同步**: 使用 `CoroutineScope(Dispatchers.IO)` 进行后台同步

## 📁 关键文件路径

### UserDataCenter 模块
- `core-user-data-center/src/main/kotlin/com/example/gymbro/core/userdata/api/UserDataCenterApi.kt`
- `core-user-data-center/src/main/kotlin/com/example/gymbro/core/userdata/internal/repository/UserDataRepositoryImpl.kt`

### 已集成模块
- `data/src/main/kotlin/com/example/gymbro/data/repository/auth/AuthRepositoryImpl.kt`

### 待集成模块
- `features/profile/src/main/kotlin/com/example/gymbro/features/profile/api/ProfileFeatureApiImpl.kt`
- `domain/src/main/kotlin/com/example/gymbro/domain/coach/usecase/SendChatMessageAndGetResponseUseCase.kt`

## 🚀 下一轮对话重点

**立即开始 Step 4**: 重构 Profile 模块集成 UserDataCenter
- 重点关注 `ProfileFeatureApiImpl` 的重构
- 确保 `observeUserAiContext()` 方法正确使用 UserDataCenter
- 验证数据流的完整性和正确性

---

**文档创建时间**: 2025-01-13  
**当前进度**: Steps 1-3 完成，Step 4 待开始  
**下一步**: Profile 模块集成 UserDataCenter
