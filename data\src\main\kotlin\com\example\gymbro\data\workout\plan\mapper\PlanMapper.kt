package com.example.gymbro.data.workout.plan.mapper

import com.example.gymbro.core.error.extensions.toModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.workout.plan.entity.PlanEntity
import com.example.gymbro.shared.models.workout.PlanType
import com.example.gymbro.shared.models.workout.WorkoutPlan

/**
 * PlanDB 数据映射器 - 架构统一版
 *
 * 🎯 架构升级：支持dailySchedule新架构
 * - 统一使用dailySchedule替代templateSchedule
 * - 支持多Template调度
 * - 完整的Entity <-> Domain Model双向转换
 *
 * 基于 GymBro Clean Architecture 原则
 * 负责 Entity <-> Domain Model 的双向转换
 */

// ==================== WorkoutPlan 映射 ====================

/**
 * 将 PlanEntity 转换为 WorkoutPlan 领域模型
 * 注意：dailySchedule需要单独加载和组装
 */
fun PlanEntity.toDomain(): WorkoutPlan = WorkoutPlan(
    id = this.id,
    name = this.name,
    description = this.description,
    userId = this.userId,
    targetGoal = this.targetGoal,
    difficultyLevel = this.difficultyLevel,
    estimatedDuration = this.estimatedDuration,
    planType = PlanType.CUSTOM, // 默认自定义类型
    dailySchedule = emptyMap(), // 需要单独加载
    totalDays = this.totalDays,
    tags = this.tags,
    isPublic = this.isPublic,
    isTemplate = this.isTemplate,
    isFavorite = this.isFavorite,
    createdAt = this.createdAt,
    updatedAt = this.updatedAt,
)

/**
 * 将 WorkoutPlan 领域模型转换为 PlanEntity
 */
fun WorkoutPlan.toEntity(): PlanEntity = PlanEntity(
    id = this.id,
    name = this.name,
    description = this.description?.takeIf { it.isNotEmpty() },
    userId = this.userId,
    targetGoal = this.targetGoal,
    difficultyLevel = this.difficultyLevel,
    estimatedDuration = this.estimatedDuration,
    isPublic = this.isPublic,
    isTemplate = this.isTemplate,
    isFavorite = this.isFavorite,
    tags = this.tags,
    totalDays = this.totalDays,
    createdAt = this.createdAt,
    updatedAt = this.updatedAt,
)

/**
 * 安全转换 PlanEntity 列表为 WorkoutPlan 列表
 */
fun List<PlanEntity>.toDomainSafely(): ModernResult<List<WorkoutPlan>> = try {
    ModernResult.Success(this.map { it.toDomain() })
} catch (e: Exception) {
    ModernResult.Error(
        e.toModernDataError(
            operationName = "PlanEntity.toDomainSafely",
            uiMessage = UiText.DynamicString("转换计划数据失败"),
        ),
    )
}

/**
 * 安全转换 WorkoutPlan 列表为 PlanEntity 列表
 */
fun List<WorkoutPlan>.toEntitySafely(): ModernResult<List<PlanEntity>> = try {
    ModernResult.Success(this.map { it.toEntity() })
} catch (e: Exception) {
    ModernResult.Error(
        e.toModernDataError(
            operationName = "WorkoutPlan.toEntitySafely",
            uiMessage = UiText.DynamicString("转换计划实体失败"),
        ),
    )
}

// ==================== PlanDay 映射 (已废弃) ====================
// 注意：PlanDay映射已迁移到shared-models层统一管理
// 这里保留空实现以避免编译错误

// ==================== PlanTemplate 映射 (已废弃) ====================
// 注意：PlanTemplate映射已迁移到shared-models层统一管理
// 这里保留空实现以避免编译错误

// ==================== 复合映射 (已废弃) ====================
// 注意：复合映射已迁移到shared-models层统一管理
// 这里保留空实现以避免编译错误
