package com.example.gymbro.core.di

import com.example.gymbro.core.di.qualifiers.PlatformIndependent
import com.example.gymbro.core.service.*
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 服务管理依赖注入模块
 *
 * 提供服务管理相关组件的依赖注入
 * 注意：限定符定义已统一迁移到core.di.qualifiers.ServiceQualifiers
 */
@Module
@InstallIn(SingletonComponent::class)
object ServiceModule {
    /**
     * 提供服务生命周期管理器
     */
    @Provides
    @Singleton
    fun provideServiceLifecycleManager(): ServiceLifecycleManager = DefaultServiceLifecycleManager()

    /**
     * 提供平台无关的后台服务管理器
     *
     * 注意：这是一个无操作实现，实际应用应提供平台特定实现
     */
    @Provides
    @Singleton
    @PlatformIndependent
    fun providePlatformIndependentBackgroundServiceManager(): IBackgroundServiceManager = NoOpBackgroundServiceManager()

    /**
     * 提供平台无关的前台服务管理器
     *
     * 注意：这是一个无操作实现，实际应用应提供平台特定实现
     */
    @Provides
    @Singleton
    @PlatformIndependent
    fun providePlatformIndependentForegroundServiceManager(): IForegroundServiceManager = NoOpForegroundServiceManager()

    // 注：平台特定的实现应该在app模块中提供，并使用@PlatformSpecific注解
    // @PlatformSpecific注解定义在core.di.qualifiers.ServiceQualifiers中
}
