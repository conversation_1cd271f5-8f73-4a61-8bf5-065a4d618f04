package com.example.gymbro.data.repository.user

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.data.remote.firebase.datasource.FirebaseUserDataSource
import com.example.gymbro.domain.profile.model.user.BlockedUser
import com.example.gymbro.domain.profile.repository.user.BlockUserRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * BlockUserRepository的实现类
 * 使用Firebase Firestore实现用户屏蔽功能
 */
@Singleton
class BlockUserRepositoryImpl
@Inject
constructor(
    private val firebaseUserDataSource: FirebaseUserDataSource,
) : BlockUserRepository {
    override suspend fun blockUser(
        userId: String,
        targetUserId: String,
    ): ModernResult<Unit> = firebaseUserDataSource.blockUser(userId, targetUserId)

    override suspend fun unblockUser(
        userId: String,
        targetUserId: String,
    ): ModernResult<Unit> = firebaseUserDataSource.unblockUser(userId, targetUserId)

    override fun getBlockedUserIds(userId: String): Flow<List<String>> = firebaseUserDataSource.getBlockedUserIds(
        userId,
    )

    override suspend fun getBlockedUserDetails(
        userId: String,
        blockedUserIds: List<String>,
    ): ModernResult<List<BlockedUser>> = firebaseUserDataSource.getBlockedUserDetails(
        userId,
        blockedUserIds,
    )

    override fun isUserBlocked(
        userId: String,
        targetUserId: String,
    ): Flow<Boolean> = firebaseUserDataSource.isUserBlocked(userId, targetUserId)
}
