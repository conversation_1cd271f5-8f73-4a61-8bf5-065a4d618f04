package com.example.gymbro.data.workout.session.entity

import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * 训练会话实体 - SessionDB 核心实体
 *
 * 基于 05_Final_Database_Architecture.md 和 04_Implementation_Plan_CleanSlate_SessionDB.md 设计
 * 存储训练会话的基本信息和进度状态
 */
@Entity(
    tableName = "workout_sessions",
    indices = [
        Index("userId"),
        Index("templateId"),
        Index("planId"),
        Index("status"),
        Index("startTime"),
        Index("endTime"),
        Index("lastAutosaveTime"),
    ],
)
data class SessionEntity(
    @PrimaryKey
    val id: String,

    // 关联信息
    val userId: String,
    val templateId: String, // 引用 TemplateEntity.id
    val templateVersion: Int? = null, // Template版本号（用于版本控制和差异检测）
    val planId: String?, // 可选，引用 PlanEntity.id

    // 基本信息
    val name: String,
    val status: String, // IN_PROGRESS, COMPLETED, ABORTED, PAUSED

    // 时间信息
    val startTime: Long,
    val endTime: Long?,
    val totalDuration: Long?, // 总训练时长（毫秒）
    val totalVolume: Double?, // 总训练量（重量*次数*组数）
    val caloriesBurned: Int?,

    // 用户评价
    val notes: String?,
    val rating: Int?, // 用户自评（1-5星）

    // 自动保存相关
    val lastAutosaveTime: Long = System.currentTimeMillis(),
)
