package com.example.gymbro.data.model.user

import com.google.gson.annotations.SerializedName

/**
 * 头像上传响应DTO
 * 用于接收头像上传API的响应数据
 */
data class AvatarUploadResponse(
    /**
     * 上传是否成功
     */
    @SerializedName("success")
    val success: Boolean = false,

    /**
     * 上传后的图片URL
     */
    @SerializedName("url")
    val url: String? = null,

    /**
     * 错误信息(如果有)
     */
    @SerializedName("error")
    val error: String? = null,

    /**
     * 图片ID
     */
    @SerializedName("imageId")
    val imageId: String? = null,

    /**
     * 上传时间戳
     */
    @SerializedName("timestamp")
    val timestamp: Long = System.currentTimeMillis(),
)
