package com.example.gymbro.core.network.monitor

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber

/**
 * Android平台网络监控实现 - D2阶段
 *
 * 基于ConnectivityManager实现真实的网络状态监控
 * 支持WiFi、蜂窝网络、以太网等多种网络类型检测
 */
class AndroidNetworkMonitor(
    private val context: Context,
) : NetworkMonitor {

    private val connectivityManager = context.getSystemService(
        Context.CONNECTIVITY_SERVICE,
    ) as ConnectivityManager

    private val _networkState = MutableStateFlow<NetworkState>(NetworkState.Unknown)
    override val networkState: StateFlow<NetworkState> = _networkState.asStateFlow()

    override val isOnline: Boolean
        get() = networkState.value is NetworkState.Available

    private var networkCallback: ConnectivityManager.NetworkCallback? = null
    private var isMonitoring = false

    init {
        Timber.d("🌐 AndroidNetworkMonitor初始化")
    }

    override fun startMonitoring() {
        if (isMonitoring) {
            Timber.d("🌐 网络监控已在运行")
            return
        }

        Timber.d("🌐 开始Android网络监控")
        isMonitoring = true

        // 立即检查当前网络状态
        updateCurrentNetworkState()

        // 注册网络状态变化监听
        registerNetworkCallback()
    }

    override fun stopMonitoring() {
        if (!isMonitoring) return

        Timber.d("🌐 停止Android网络监控")
        isMonitoring = false

        networkCallback?.let { callback ->
            try {
                connectivityManager.unregisterNetworkCallback(callback)
            } catch (e: Exception) {
                Timber.w(e, "🌐 注销网络回调失败")
            }
        }
        networkCallback = null
        _networkState.value = NetworkState.Unknown
    }

    /**
     * 注册网络状态变化回调
     */
    private fun registerNetworkCallback() {
        val networkRequest = NetworkRequest.Builder()
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .addCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
            .build()

        networkCallback = object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                Timber.d("🌐 网络可用: $network")
                updateNetworkState(network)
            }

            override fun onLost(network: Network) {
                Timber.d("🌐 网络丢失: $network")
                _networkState.value = NetworkState.Lost
            }

            override fun onCapabilitiesChanged(
                network: Network,
                networkCapabilities: NetworkCapabilities,
            ) {
                Timber.v("🌐 网络能力变化: $network")
                updateNetworkState(network, networkCapabilities)
            }

            override fun onUnavailable() {
                Timber.d("🌐 网络不可用")
                _networkState.value = NetworkState.Unavailable
            }
        }

        try {
            connectivityManager.registerNetworkCallback(networkRequest, networkCallback!!)
        } catch (e: Exception) {
            Timber.e(e, "🌐 注册网络回调失败")
            _networkState.value = NetworkState.Unavailable
        }
    }

    /**
     * 更新当前网络状态
     */
    private fun updateCurrentNetworkState() {
        try {
            val activeNetwork = connectivityManager.activeNetwork
            if (activeNetwork != null) {
                updateNetworkState(activeNetwork)
            } else {
                _networkState.value = NetworkState.Unavailable
            }
        } catch (e: Exception) {
            Timber.e(e, "🌐 获取当前网络状态失败")
            _networkState.value = NetworkState.Unknown
        }
    }

    /**
     * 更新网络状态
     */
    private fun updateNetworkState(
        network: Network,
        capabilities: NetworkCapabilities? = null,
    ) {
        try {
            val networkCapabilities = capabilities ?: connectivityManager.getNetworkCapabilities(network)

            if (networkCapabilities == null) {
                _networkState.value = NetworkState.Unavailable
                return
            }

            // 检查网络是否有效且可访问互联网
            val hasInternet = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            val isValidated = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)

            if (!hasInternet || !isValidated) {
                _networkState.value = NetworkState.Connecting
                return
            }

            // 确定网络类型
            val networkType = determineNetworkType(networkCapabilities)

            // 估算带宽
            val bandwidthKbps = estimateBandwidth(networkCapabilities)

            _networkState.value = NetworkState.Available(
                type = networkType,
                bandwidthKbps = bandwidthKbps,
            )

            Timber.d("🌐 网络状态更新: type=$networkType, bandwidth=${bandwidthKbps}kbps")
        } catch (e: Exception) {
            Timber.e(e, "🌐 更新网络状态失败")
            _networkState.value = NetworkState.Unknown
        }
    }

    /**
     * 确定网络类型
     */
    private fun determineNetworkType(capabilities: NetworkCapabilities): NetworkType {
        return when {
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> NetworkType.WIFI
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> NetworkType.CELLULAR
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> NetworkType.ETHERNET
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_VPN) -> NetworkType.VPN
            else -> NetworkType.UNKNOWN
        }
    }

    /**
     * 估算网络带宽
     */
    private fun estimateBandwidth(capabilities: NetworkCapabilities): Int {
        return try {
            // Android API 21+支持带宽估算
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                val downstreamKbps = capabilities.linkDownstreamBandwidthKbps
                if (downstreamKbps > 0) downstreamKbps else 0
            } else {
                // 旧版本Android，根据网络类型估算
                when {
                    capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> 50000 // 50Mbps
                    capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> 10000 // 10Mbps
                    capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> 100000 // 100Mbps
                    else -> 0
                }
            }
        } catch (e: Exception) {
            Timber.w(e, "🌐 估算带宽失败")
            0
        }
    }
}
