package com.example.gymbro.data.mapper.user

import com.example.gymbro.core.util.DateTimeConverters
import com.example.gymbro.data.local.entity.user.UserProfileEntity
import com.example.gymbro.domain.profile.model.user.FitnessGoal
import com.example.gymbro.domain.profile.model.user.UserProfile
import com.example.gymbro.domain.profile.model.user.WorkoutDay
import com.example.gymbro.domain.profile.model.user.enums.FitnessLevel
import com.example.gymbro.domain.profile.model.user.enums.Gender
import com.example.gymbro.domain.profile.model.user.enums.WeightUnit
import com.example.gymbro.shared.models.user.UserProfileDto
import kotlinx.datetime.Clock

/**
 * UserProfile映射扩展函数
 *
 * 遵循Clean Architecture + Data层实施指南
 * 使用extension functions替代class-based mapper
 */

/**
 * 将用户资料DTO转换为领域模型
 * 注意：需要确认UserProfileDto类型定义
 */
// fun UserProfileDto.toDomain(): UserProfile - 暂时注释，需要确认类型定义
// 暂时注释掉，等确认类型定义后再启用
/*
private fun convertUserProfileDtoToDomain(userProfileDto: Any): UserProfile {
    // 实现待定，需要确认UserProfileDto类型
    TODO("需要确认UserProfileDto类型定义")
}
*/

/**
 * 将用户资料实体转换为领域模型
 */
fun UserProfileEntity.toDomain(): UserProfile {
    // 安全转换性别枚举
    val gender =
        try {
            this.gender?.let { Gender.valueOf(it.uppercase()) } ?: Gender.OTHER
        } catch (e: IllegalArgumentException) {
            Gender.OTHER
        }

    // 安全转换体重单位枚举
    val weightUnit =
        try {
            this.weightUnit?.let { WeightUnit.valueOf(it.uppercase()) } ?: WeightUnit.KG
        } catch (e: IllegalArgumentException) {
            WeightUnit.KG
        }

    // 安全转换身高单位枚举
    val heightUnit =
        try {
            this.heightUnit?.let {
                com.example.gymbro.domain.profile.model.user.enums.HeightUnit
                    .valueOf(it.uppercase())
            } ?: com.example.gymbro.domain.profile.model.user.enums.HeightUnit.CM
        } catch (e: IllegalArgumentException) {
            com.example.gymbro.domain.profile.model.user.enums.HeightUnit.CM
        }

    // 安全转换健身水平枚举
    val fitnessLevel =
        try {
            this.fitnessLevel?.let { level ->
                when (level) {
                    0 -> FitnessLevel.BEGINNER
                    1 -> FitnessLevel.INTERMEDIATE
                    2 -> FitnessLevel.ADVANCED
                    3 -> FitnessLevel.EXPERT
                    4 -> FitnessLevel.PRO
                    else -> FitnessLevel.BEGINNER
                }
            } ?: FitnessLevel.BEGINNER
        } catch (e: Exception) {
            FitnessLevel.BEGINNER
        }

    // 安全转换健身目标列表
    val fitnessGoals =
        try {
            this.fitnessGoals.mapNotNull { goalName ->
                try {
                    FitnessGoal.valueOf(goalName.uppercase())
                } catch (e: IllegalArgumentException) {
                    null
                }
            }
        } catch (e: Exception) {
            emptyList()
        }

    // 安全转换训练日列表
    val workoutDays =
        try {
            this.workoutDays.mapNotNull { dayName ->
                try {
                    WorkoutDay.valueOf(dayName.uppercase())
                } catch (e: IllegalArgumentException) {
                    null
                }
            }
        } catch (e: Exception) {
            emptyList()
        }

    // 转换时间戳为LocalDateTime
    val joinDate =
        try {
            DateTimeConverters.longToInstant(this.createdAt)?.let { instant ->
                DateTimeConverters.instantToLocalDateTime(instant)
            }
        } catch (e: Exception) {
            null
        }

    val lastActive =
        try {
            DateTimeConverters.longToInstant(this.lastUpdated)?.let { instant ->
                DateTimeConverters.instantToLocalDateTime(instant)
            }
        } catch (e: Exception) {
            null
        }

    return UserProfile(
        userId = this.userId,
        username = this.username,
        displayName = this.displayName,
        email = this.email,
        phoneNumber = this.phoneNumber,
        avatarUrl = this.profileImageUrl,
        bio = this.bio,
        gender = gender,
        height = this.height,
        heightUnit = heightUnit,
        weight = this.weight,
        weightUnit = weightUnit,
        fitnessLevel = fitnessLevel,
        fitnessGoals = fitnessGoals,
        joinDate = joinDate,
        lastActive = lastActive,
        workoutDays = workoutDays,
        allowPartnerMatching = this.allowPartnerMatching,
        totalActivityCount = this.totalWorkoutCount,
        weeklyActiveMinutes = this.weeklyActiveMinutes,
        likesReceived = this.likesReceived,
        isSubscribed = this.hasValidSubscription,
        isAnonymous = this.isAnonymous,
        socialLinks = emptyMap(), // UserProfileEntity中没有socialLinks字段
        isMpOwner = false, // UserProfileEntity中没有isMpOwner字段
    )
}

/**
 * 将用户资料领域模型转换为DTO
 */
fun UserProfile.toDto(): UserProfileDto {
    val lastUpdatedTimestamp =
        this.lastActive?.let { localDateTime ->
            DateTimeConverters.localDateTimeToInstant(localDateTime)?.let { instant ->
                DateTimeConverters.instantToLong(instant)
            }
        } ?: DateTimeConverters.instantToLong(Clock.System.now())

    return UserProfileDto(
        userId = this.userId,
        username = this.username,
        displayName = this.displayName,
        email = this.email,
        phoneNumber = this.phoneNumber,
        avatarUrl = this.avatarUrl,
        bio = this.bio,
        gender = this.gender?.name,
        height = this.height,
        heightUnit = this.heightUnit?.name,
        weight = this.weight,
        weightUnit = this.weightUnit?.name,
        fitnessLevel = this.fitnessLevel?.ordinal,
        fitnessGoals = this.fitnessGoals.map { it.name },
        workoutDays = this.workoutDays.map { it.name },
        allowPartnerMatching = this.allowPartnerMatching,
        totalWorkoutCount = this.totalActivityCount,
        weeklyActiveMinutes = this.weeklyActiveMinutes,
        likesReceived = this.likesReceived,
        isAnonymous = this.isAnonymous,
        isSubscribed = this.isSubscribed,
        lastUpdated = lastUpdatedTimestamp ?: 0L,
    )
}

/**
 * 将用户资料领域模型转换为实体
 */
fun UserProfile.toEntity(): UserProfileEntity =
    UserProfileEntity(
        userId = this.userId,
        username = this.username,
        displayName = this.displayName,
        email = this.email,
        phoneNumber = this.phoneNumber,
        profileImageUrl = this.avatarUrl,
        bio = this.bio,
        gender = this.gender.toStringOrNull(),
        height = this.height,
        heightUnit = this.heightUnit.toStringOrNull(),
        weight = this.weight,
        weightUnit = this.weightUnit.toStringOrNull(),
        fitnessLevel = this.fitnessLevel.toIntOrNull(),
        fitnessGoals = this.fitnessGoals.map { it.name },
        workoutDays = this.workoutDays.map { it.name },
        allowPartnerMatching = this.allowPartnerMatching,
        totalWorkoutCount = 0, // 临时默认值，等workout模块重制后更新
        weeklyActiveMinutes = this.weeklyActiveMinutes,
        likesReceived = this.likesReceived,
        isAnonymous = this.isAnonymous,
        hasValidSubscription = this.isSubscribed,
        lastUpdated =
        DateTimeConverters.instantToLongOrNow(
            this.lastActive?.let { localDateTime ->
                DateTimeConverters.localDateTimeToInstant(localDateTime)
            } ?: Clock.System.now(),
        ),
        createdAt =
        DateTimeConverters.instantToLongOrNow(
            this.joinDate?.let { localDateTime ->
                DateTimeConverters.localDateTimeToInstant(localDateTime)
            } ?: Clock.System.now(),
        ),
    )

/**
 * 私有辅助扩展函数
 */
private fun FitnessLevel?.toIntOrNull(): Int? =
    when (this) {
        FitnessLevel.BEGINNER -> 0
        FitnessLevel.INTERMEDIATE -> 1
        FitnessLevel.ADVANCED -> 2
        FitnessLevel.EXPERT -> 3
        FitnessLevel.PRO -> 4
        FitnessLevel.UNSPECIFIED -> 0 // 映射为初学者级别
        null -> null
    }

private fun Gender?.toStringOrNull(): String? =
    when (this) {
        Gender.MALE -> "MALE"
        Gender.FEMALE -> "FEMALE"
        Gender.OTHER -> "OTHER"
        Gender.PREFER_NOT_TO_SAY -> "PREFER_NOT_TO_SAY"
        Gender.UNSPECIFIED -> "OTHER" // 映射为OTHER
        null -> null
    }

private fun WeightUnit?.toStringOrNull(): String? = this?.name

private fun com.example.gymbro.domain.profile.model.user.enums.HeightUnit?.toStringOrNull(): String? = this?.name
