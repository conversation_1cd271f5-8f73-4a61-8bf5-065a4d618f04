# Mapper移除修复指南

## 📋 修复指南概述
**文档用途**: 提供Mapper类移除过程中常见问题的解决方案
**更新时间**: 2025-01-28
**适用场景**: BaseMapper系统移除实施过程

## 🚨 常见问题与修复方案

### 1. 编译错误修复

#### 1.1 BaseMapper导入错误
**错误信息**:
```
Unresolved reference: BaseMapper
Cannot access 'BaseMapper': it is not visible
```

**修复方案**:
```kotlin
// 错误代码
import com.example.gymbro.core.util.mapper.BaseMapper

class UserMapper : BaseMapper<UserEntity, User> {
    override fun map(from: UserEntity): User = from.toDomain()
}

// 修复后
// 移除import，直接使用扩展函数
fun UserEntity.toDomain(): User {
    return User(
        id = this.id,
        name = this.name,
        // ... 其他映射逻辑
    )
}
```

#### 1.2 类型约束错误
**错误信息**:
```
Type mismatch: inferred type is UserMapperImpl but BaseMapper<UserEntity, User> was expected
```

**修复方案**:
```kotlin
// 错误代码 - 在Repository构造函数中
class UserRepositoryImpl @Inject constructor(
    private val userMapper: BaseMapper<UserEntity, User>
) : UserRepository

// 修复后 - 移除Mapper依赖，直接使用扩展函数
class UserRepositoryImpl @Inject constructor(
    private val userDao: UserDao
) : UserRepository {
    override suspend fun getUser(id: String): User {
        return userDao.getUserById(id).toDomain()
    }
}
```

### 2. DI配置错误修复

#### 2.1 Mapper绑定错误
**错误信息**:
```
[Dagger/MissingBinding] UserMapper cannot be provided without an @Provides- or @Binds-annotated method
```

**修复方案**:
```kotlin
// 错误代码 - 在DI模块中
@Module
abstract class DataModule {
    @Binds
    abstract fun bindUserMapper(
        userMapperImpl: UserMapperImpl
    ): BaseMapper<UserEntity, User>
}

// 修复后 - 移除Mapper绑定
@Module
abstract class DataModule {
    // BaseMapper相关绑定已移除
    // 扩展函数不需要DI绑定

    @Binds
    abstract fun bindUserRepository(
        userRepositoryImpl: UserRepositoryImpl
    ): UserRepository
}
```

#### 2.2 循环依赖错误
**错误信息**:
```
[Dagger/DependencyCycle] Found a dependency cycle
```

**修复方案**:
```kotlin
// 问题分析：Mapper类之间的相互依赖
// 修复：使用扩展函数消除循环依赖

// 错误代码
class UserMapper @Inject constructor(
    private val profileMapper: ProfileMapper
) : BaseMapper<UserEntity, User>

class ProfileMapper @Inject constructor(
    private val userMapper: UserMapper
) : BaseMapper<ProfileEntity, Profile>

// 修复后 - 扩展函数无需依赖注入
fun UserEntity.toDomain(): User {
    return User(
        id = this.id,
        profile = this.profile?.toDomain()  // 直接调用扩展函数
    )
}

fun ProfileEntity.toDomain(): Profile {
    return Profile(
        userId = this.userId,
        // ... 其他字段
    )
}
```

### 3. 运行时错误修复

#### 3.1 空指针异常
**错误信息**:
```
NullPointerException: mapper is null
```

**修复方案**:
```kotlin
// 错误代码 - Repository中Mapper为null
class UserRepositoryImpl @Inject constructor(
    private val userMapper: UserMapper? = null  // Mapper注入失败
) : UserRepository {
    override suspend fun getUser(id: String): User {
        return userMapper!!.map(userEntity)  // NPE风险
    }
}

// 修复后 - 使用扩展函数
class UserRepositoryImpl @Inject constructor(
    private val userDao: UserDao
) : UserRepository {
    override suspend fun getUser(id: String): User {
        val userEntity = userDao.getUserById(id)
        return userEntity.toDomain()  // 扩展函数调用，无NPE风险
    }
}
```

#### 3.2 扩展函数未找到
**错误信息**:
```
Unresolved reference: toDomain
Extension function 'toDomain' is not found
```

**修复方案**:
```kotlin
// 检查导入语句
import com.example.gymbro.data.mapper.user.toDomain

// 或者确保扩展函数在正确的包中定义
// data/mapper/user/UserProfile.mapper.kt
fun UserProfileEntity.toDomain(): UserProfile {
    // 实现
}

// 在Repository中使用
class UserProfileRepositoryImpl @Inject constructor(
    private val dao: UserProfileDao
) : UserProfileRepository {
    override suspend fun getUserProfile(id: String): UserProfile {
        return dao.getUserProfileById(id).toDomain()
    }
}
```

### 4. 测试相关修复

#### 4.1 Mockk测试错误
**错误信息**:
```
MockKException: no answer found for: UserMapper.map(UserEntity)
```

**修复方案**:
```kotlin
// 错误代码 - Mock Mapper类
@Test
fun `test user mapping`() {
    val mockMapper: UserMapper = mockk()
    every { mockMapper.map(any()) } returns testUser

    val result = repository.getUser("123")
    // 测试逻辑
}

// 修复后 - 直接测试扩展函数
@Test
fun `test user entity to domain mapping`() {
    val userEntity = UserEntity(
        id = "123",
        name = "Test User"
    )

    val result = userEntity.toDomain()

    assertEquals("123", result.id)
    assertEquals("Test User", result.name)
}

// Repository测试
@Test
fun `test repository get user`() {
    val userEntity = UserEntity(id = "123", name = "Test")
    every { mockDao.getUserById("123") } returns userEntity

    val result = repository.getUser("123")

    // 验证扩展函数的映射结果
    assertEquals("123", result.id)
    assertEquals("Test", result.name)
}
```

#### 4.2 集成测试修复
**错误信息**:
```
No beans of type 'BaseMapper' found
```

**修复方案**:
```kotlin
// 错误代码 - Spring测试配置
@TestConfiguration
class TestConfig {
    @Bean
    fun userMapper(): BaseMapper<UserEntity, User> {
        return UserMapperImpl()
    }
}

// 修复后 - 移除Mapper Bean配置
@TestConfiguration
class TestConfig {
    // 扩展函数不需要Bean配置
    // 直接在测试中使用扩展函数
}

@Test
fun integrationTest() {
    val entity = UserEntity(id = "123", name = "Test")
    val domain = entity.toDomain()  // 直接使用扩展函数

    // 测试逻辑
}
```

### 5. 性能相关修复

#### 5.1 扩展函数性能优化
**问题**: 频繁的对象创建影响性能

**修复方案**:
```kotlin
// 低效代码 - 每次创建新对象
fun UserEntity.toDomain(): User {
    return User(
        id = this.id,
        profile = UserProfile(  // 每次都创建新的Profile对象
            name = this.name,
            email = this.email
        )
    )
}

// 优化后 - 使用缓存或延迟初始化
fun UserEntity.toDomain(): User {
    return User(
        id = this.id,
        profile = this.profile?.toDomain()  // 仅在需要时转换
    )
}

// 对于大量数据的批量转换
fun List<UserEntity>.toDomainList(): List<User> {
    return this.map { it.toDomain() }  // 使用高效的map操作
}
```

### 6. 架构合规修复

#### 6.1 Domain层Mapper清理
**问题**: Domain层包含数据映射逻辑

**修复方案**:
```kotlin
// 错误 - Domain层包含Mapper
// domain/src/main/kotlin/.../SessionMapper.kt
object SessionMapper {
    fun mapWorkoutToworkout(workout: WorkoutSession): workoutSession {
        // 映射逻辑
    }
}

// 修复后 - 移除Domain层Mapper，在Data层处理
// 删除 domain/.../SessionMapper.kt 文件

// 在Data层添加扩展函数
// data/src/main/kotlin/.../session/SessionMappers.kt
fun WorkoutSessionEntity.toworkoutSession(): workoutSession {
    // 映射逻辑
}
```

#### 6.2 跨层依赖修复
**问题**: 错误的依赖方向

**修复方案**:
```kotlin
// 错误 - Domain依赖Data层Mapper
// domain/usecase/GetUserUseCase.kt
class GetUserUseCase @Inject constructor(
    private val repository: UserRepository,
    private val mapper: UserDataMapper  // 错误：Domain不应依赖Data层
)

// 修复后 - 在Repository中处理映射
// domain/usecase/GetUserUseCase.kt
class GetUserUseCase @Inject constructor(
    private val repository: UserRepository  // 只依赖Repository接口
) {
    suspend operator fun invoke(userId: String): User {
        return repository.getUser(userId)  // Repository内部处理映射
    }
}

// data/repository/UserRepositoryImpl.kt
class UserRepositoryImpl @Inject constructor(
    private val dao: UserDao
) : UserRepository {
    override suspend fun getUser(userId: String): User {
        return dao.getUserById(userId).toDomain()  // 在Data层处理映射
    }
}
```

## 🔧 快速修复命令

### 批量清理导入
```bash
# 查找所有BaseMapper导入
find . -name "*.kt" -exec grep -l "import.*BaseMapper" {} \;

# 批量移除BaseMapper导入
find . -name "*.kt" -exec sed -i '/import.*BaseMapper/d' {} \;
```

### 验证修复结果
```bash
# 编译检查
./gradlew compileKotlin --console=plain

# 测试检查
./gradlew test --console=plain

# 查找剩余引用
grep -r "BaseMapper" . --include="*.kt" --exclude-dir=".gradle"
```

## 📚 参考资源

### 相关文档
- `data/IMPLEMENTATION_GUIDE.md` - Data层实施指南
- `domain/IMPLEMENTATION_GUIDE.md` - Domain层实施指南
- `core/README.md` - Core模块说明

### 扩展函数最佳实践
```kotlin
// 推荐的扩展函数模式
// 1. 明确的命名
fun UserEntity.toDomain(): User

// 2. 反向映射
fun User.toEntity(): UserEntity

// 3. 空值处理
fun UserEntity?.toDomainOrNull(): User? = this?.toDomain()

// 4. 列表转换
fun List<UserEntity>.toDomainList(): List<User> = map { it.toDomain() }

// 5. 非空断言的安全替代
fun UserEntity.toDomainSafe(): User? = runCatching {
    this.toDomain()
}.getOrNull()
```

## ✅ 修复验证清单

### 编译验证
- [ ] 无BaseMapper相关编译错误
- [ ] 所有模块编译通过
- [ ] 无未解析的引用错误

### 功能验证
- [ ] 所有Repository方法正常工作
- [ ] 数据映射功能正确
- [ ] 扩展函数调用正常

### 测试验证
- [ ] 单元测试全部通过
- [ ] 集成测试正常运行
- [ ] Mock测试配置正确

### 架构验证
- [ ] Domain层无Data层依赖
- [ ] 映射逻辑在正确的层
- [ ] DI配置简化且正确

---

**修复成功标准**: 所有验证项通过，系统功能完全正常，代码更加简洁 🔧
