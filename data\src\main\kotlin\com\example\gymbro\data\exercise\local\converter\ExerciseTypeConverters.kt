package com.example.gymbro.data.exercise.local.converter

import androidx.room.TypeConverter
import com.example.gymbro.shared.models.exercise.Equipment
import com.example.gymbro.shared.models.exercise.MuscleGroup
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json

/**
 * Exercise模块的Room类型转换器
 *
 * 负责将复杂类型转换为Room支持的基本类型
 * 支持：List<Equipment>、List<MuscleGroup>、List<String>、FloatArray
 */
class ExerciseTypeConverters {

    private val json = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true
    }

    // ==================== Equipment List ====================

    @TypeConverter
    fun fromEquipmentList(equipmentList: List<Equipment>): String {
        return json.encodeToString(equipmentList)
    }

    @TypeConverter
    fun toEquipmentList(equipmentString: String): List<Equipment> {
        return try {
            json.decodeFromString(equipmentString)
        } catch (e: Exception) {
            emptyList()
        }
    }

    // ==================== MuscleGroup List ====================

    @TypeConverter
    fun fromMuscleGroupList(muscleGroupList: List<MuscleGroup>): String {
        return json.encodeToString(muscleGroupList)
    }

    @TypeConverter
    fun toMuscleGroupList(muscleGroupString: String): List<MuscleGroup> {
        return try {
            json.decodeFromString(muscleGroupString)
        } catch (e: Exception) {
            emptyList()
        }
    }

    // ==================== String List ====================

    @TypeConverter
    fun fromStringList(stringList: List<String>): String {
        return json.encodeToString(stringList)
    }

    @TypeConverter
    fun toStringList(stringListString: String): List<String> {
        return try {
            json.decodeFromString(stringListString)
        } catch (e: Exception) {
            emptyList()
        }
    }

    // ==================== FloatArray ====================

    @TypeConverter
    fun fromFloatArray(floatArray: FloatArray?): String? {
        return floatArray?.let { json.encodeToString(it.toList()) }
    }

    @TypeConverter
    fun toFloatArray(floatArrayString: String?): FloatArray? {
        return floatArrayString?.let {
            try {
                json.decodeFromString<List<Float>>(it).toFloatArray()
            } catch (e: Exception) {
                null
            }
        }
    }

    // ==================== MuscleGroup Single ====================

    @TypeConverter
    fun fromMuscleGroup(muscleGroup: MuscleGroup): String {
        return muscleGroup.name
    }

    @TypeConverter
    fun toMuscleGroup(muscleGroupString: String): MuscleGroup {
        return try {
            MuscleGroup.valueOf(muscleGroupString)
        } catch (e: Exception) {
            MuscleGroup.OTHER
        }
    }
}
