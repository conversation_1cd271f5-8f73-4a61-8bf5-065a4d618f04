# Google Services Mock Configuration

## 📋 概述

这个 `google-services.json.mock` 文件是为了解决 CI/CD 构建中的 Firebase 配置问题而创建的 mock 配置文件。

## 🔧 问题背景

### 原始问题
- 真实的 `app/google-services.json` 文件被 `.gitignore` 忽略（出于安全考虑）
- CI 构建需要一个格式正确的 `google-services.json` 文件才能通过编译
- 之前的解决方案依赖环境变量 `FIREBASE_SERVICE_ACCOUNT`，但在某些 CI 环境中可能失败

### 解决方案
创建一个 mock 版本的 Firebase 配置文件，包含：
- 正确的 JSON 结构
- 虚假但格式正确的配置值
- 与项目包名 `com.example.gymbro` 匹配的配置

## 🚀 使用方式

### 在 GitHub Actions 中
CI workflow 会自动检测并使用适当的配置：

```yaml
- name: Create google-services.json for CI
  run: |
    if [ -n "${{ secrets.FIREBASE_SERVICE_ACCOUNT }}" ]; then
      echo "Using real Firebase config from secrets"
      echo "$FIREBASE_SERVICE_ACCOUNT" > app/google-services.json
    else
      echo "Using mock Firebase config for CI"
      cp app/google-services.json.mock app/google-services.json
    fi
  env:
    FIREBASE_SERVICE_ACCOUNT: ${{ secrets.FIREBASE_SERVICE_ACCOUNT }}
  continue-on-error: true
```

### 在本地开发中
如果你没有真实的 Firebase 配置文件，可以手动复制 mock 文件：

```bash
cp app/google-services.json.mock app/google-services.json
```

## 📁 文件结构

```
app/
├── google-services.json          # 真实配置（被 gitignore）
├── google-services.json.mock     # Mock 配置（提交到 Git）
└── google-services.json.mock.md  # 说明文档
```

## ⚠️ 重要说明

### 安全性
- Mock 文件包含的都是虚假数据，不会泄露真实的 Firebase 配置
- 真实的 Firebase 配置仍然通过 GitHub Secrets 管理
- Mock 文件仅用于编译通过，不能用于实际的 Firebase 功能

### 功能限制
使用 Mock 配置时：
- ✅ 编译和构建正常
- ❌ Firebase Authentication 不可用
- ❌ Firebase Firestore 不可用
- ❌ Firebase Storage 不可用
- ❌ 其他 Firebase 服务不可用

## 🔄 更新指南

### 当项目包名变更时
如果项目包名从 `com.example.gymbro` 变更，需要更新 mock 文件中的：
- `client[0].client_info.android_client_info.package_name`
- `client[0].oauth_client[0].android_info.package_name`

### 当添加新的 Firebase 服务时
如果项目添加了新的 Firebase 服务，可能需要在 mock 文件中添加相应的配置节点。

## 🛠️ 故障排除

### 编译失败：Malformed root json
- 检查 mock 文件的 JSON 格式是否正确
- 使用 `python -m json.tool app/google-services.json.mock` 验证 JSON 格式

### CI 构建失败：File not found
- 确保 mock 文件存在于 `app/` 目录下
- 检查 CI workflow 中的文件复制命令是否正确

### 本地开发问题
- 如果需要真实的 Firebase 功能，请联系项目管理员获取真实的配置文件
- 确保真实配置文件不会被提交到 Git 仓库

## 📞 联系方式

如果遇到与 Firebase 配置相关的问题，请：
1. 检查本文档的故障排除部分
2. 查看 CI 构建日志
3. 联系项目维护者

---

**最后更新**：2025-01-28
**维护者**：GymBro 开发团队
