package com.example.gymbro.designSystem.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Phone
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.unit.dp
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme

/**
 * 认证相关的输入字段组件集合
 * 基于GymBroInputField构建的预设认证输入组件
 */

/**
 * 邮箱输入字段
 */
@Composable
fun emailField(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    isError: Boolean = false,
    errorMessage: UiText? = null,
    enabled: Boolean = true,
    imeAction: ImeAction = ImeAction.Next,
) {
    GymBroInputField(
        value = value,
        onValueChange = onValueChange,
        label = { Text("邮箱") },
        placeholder = "请输入邮箱地址",
        modifier = modifier,
        isError = isError,
        errorMessage = errorMessage,
        enabled = enabled,
        keyboardOptions = KeyboardOptions(
            keyboardType = KeyboardType.Email,
            imeAction = imeAction,
        ),
        leadingIcon = {
            Icon(
                imageVector = Icons.Default.Email,
                contentDescription = "邮箱图标",
            )
        },
    )
}

/**
 * 密码输入字段
 */
@Composable
fun passwordField(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    isError: Boolean = false,
    errorMessage: UiText? = null,
    enabled: Boolean = true,
    imeAction: ImeAction = ImeAction.Done,
    label: String = "密码",
    placeholder: String = "请输入密码",
) {
    GymBroInputField(
        value = value,
        onValueChange = onValueChange,
        label = { Text(label) },
        placeholder = placeholder,
        modifier = modifier,
        isError = isError,
        errorMessage = errorMessage,
        enabled = enabled,
        visualTransformation = PasswordVisualTransformation(),
        keyboardOptions = KeyboardOptions(
            keyboardType = KeyboardType.Password,
            imeAction = imeAction,
        ),
        leadingIcon = {
            Icon(
                imageVector = Icons.Default.Lock,
                contentDescription = "密码图标",
            )
        },
    )
}

/**
 * 确认密码输入字段
 */
@Composable
fun confirmPasswordField(
    value: String,
    onValueChange: (String) -> Unit,
    originalPassword: String,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
) {
    val isError = value.isNotEmpty() && value != originalPassword
    val errorMessage = if (isError) UiText.DynamicString("密码不匹配") else null

    passwordField(
        value = value,
        onValueChange = onValueChange,
        modifier = modifier,
        isError = isError,
        errorMessage = errorMessage,
        enabled = enabled,
        label = "确认密码",
        placeholder = "请再次输入密码",
    )
}

/**
 * 手机号输入字段
 */
@Composable
fun phoneField(
    phoneNumber: String,
    onPhoneNumberChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    isError: Boolean = false,
    errorMessage: UiText? = null,
    enabled: Boolean = true,
) {
    GymBroInputField(
        value = phoneNumber,
        onValueChange = { if (it.length <= 15) onPhoneNumberChange(it) },
        label = { Text("手机号码") },
        placeholder = "请输入手机号码",
        modifier = modifier,
        isError = isError,
        errorMessage = errorMessage,
        enabled = enabled,
        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Phone),
        leadingIcon = {
            Icon(
                imageVector = Icons.Default.Phone,
                contentDescription = "手机号图标",
            )
        },
    )
}

/**
 * 用户名输入字段
 */
@Composable
fun usernameField(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    isError: Boolean = false,
    errorMessage: UiText? = null,
    enabled: Boolean = true,
) {
    GymBroInputField(
        value = value,
        onValueChange = onValueChange,
        label = { Text("用户名") },
        placeholder = "请输入用户名",
        modifier = modifier,
        isError = isError,
        errorMessage = errorMessage,
        enabled = enabled,
        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Text),
        leadingIcon = {
            Icon(
                imageVector = Icons.Default.Person,
                contentDescription = "用户名图标",
            )
        },
    )
}

/**
 * 姓名输入字段
 */
@Composable
fun fullNameField(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    isError: Boolean = false,
    errorMessage: UiText? = null,
    enabled: Boolean = true,
) {
    GymBroInputField(
        value = value,
        onValueChange = onValueChange,
        label = { Text("姓名") },
        placeholder = "请输入姓名",
        modifier = modifier,
        isError = isError,
        errorMessage = errorMessage,
        enabled = enabled,
        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Text),
        leadingIcon = {
            Icon(
                imageVector = Icons.Default.Person,
                contentDescription = "姓名图标",
            )
        },
    )
}

// === Preview组件 ===

@GymBroPreview
@Composable
private fun AuthInputFieldsPreview() {
    GymBroTheme {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            var email by remember { mutableStateOf("") }
            var password by remember { mutableStateOf("") }
            var confirmPassword by remember { mutableStateOf("") }
            var phone by remember { mutableStateOf("") }
            var username by remember { mutableStateOf("") }
            var fullName by remember { mutableStateOf("") }

            emailField(
                value = email,
                onValueChange = { email = it },
            )

            passwordField(
                value = password,
                onValueChange = { password = it },
            )

            confirmPasswordField(
                value = confirmPassword,
                onValueChange = { confirmPassword = it },
                originalPassword = password,
            )

            phoneField(
                phoneNumber = phone,
                onPhoneNumberChange = { phone = it },
            )

            usernameField(
                value = username,
                onValueChange = { username = it },
            )

            fullNameField(
                value = fullName,
                onValueChange = { fullName = it },
            )
        }
    }
}
