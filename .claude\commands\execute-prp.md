# Execute PRP (Product Requirements Prompt)

你是一个AI编程助手，需要执行指定的PRP文件来实现功能。

## 输入
接收参数: $ARGUMENTS (PRPs目录下的PRP文件路径)

## 执行流程

### 1. 加载上下文
- 阅读完整的PRP文件
- 理解所有要求和约束
- 确认技术栈和依赖
- 审查参考资料和示例

### 2. 制定计划
- 使用TodoWrite工具创建详细的任务列表
- 将PRP中的步骤转换为可执行的任务
- 设置任务优先级和依赖关系
- 确认验证标准

### 3. 执行实现
- 按照计划逐步实施每个组件
- 遵循项目的代码约定和架构模式
- 实施适当的错误处理
- 添加必要的日志和调试信息

### 4. 验证和测试
- 运行所有必需的测试
- 执行代码质量检查 (lint, format等)
- 验证功能是否满足要求
- 检查性能和安全性

### 5. 迭代改进
- 修复发现的任何问题
- 优化性能
- 完善错误处理
- 更新文档

### 6. 完成确认
- 确保满足所有成功标准
- 完成最终的代码审查
- 更新相关文档
- 标记任务完成

## 执行原则

### 代码质量标准
- 遵循项目现有的代码风格
- 实施适当的错误处理
- 添加有意义的注释(仅在必要时)
- 确保代码的可读性和可维护性

### 测试要求
- 为新功能编写单元测试
- 实施集成测试(如适用)
- 确保测试覆盖率达到项目标准
- 验证边界条件和错误情况

### 文档要求
- 更新相关的README文件
- 添加API文档(如适用)
- 更新架构文档
- 记录重要的设计决策

## 验证门控

在每个主要步骤完成后，执行以下验证:
1. 代码编译无错误
2. 所有测试通过
3. 代码质量检查通过
4. 功能符合规范要求

## 报告和反馈

在执行过程中:
- 定期报告进度
- 及时报告遇到的问题
- 请求澄清模糊的需求
- 建议改进方案

开始执行 $ARGUMENTS 中指定的PRP。