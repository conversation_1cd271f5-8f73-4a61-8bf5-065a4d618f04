package com.example.gymbro.core.ai.prompt.model

/**
 * 摘要质量指标
 *
 * 用于评估 AI 生成摘要的质量和性能
 *
 * @since 618重构
 */
data class SummaryQualityMetrics(
    /**
     * 压缩比（原文长度/摘要长度）
     */
    val compressionRatio: Float,

    /**
     * 关键词覆盖率（0.0-1.0）
     */
    val keywordCoverage: Float,

    /**
     * 生成延迟（毫秒）
     */
    val generationLatencyMs: Long,

    /**
     * 置信度分数（0.0-1.0）
     */
    val confidenceScore: Float,
) {
    /**
     * 计算质量等级
     */
    val qualityGrade: QualityGrade
        get() = when {
            confidenceScore >= 0.9f && keywordCoverage >= 0.8f -> QualityGrade.EXCELLENT
            confidenceScore >= 0.7f && keywordCoverage >= 0.6f -> QualityGrade.GOOD
            confidenceScore >= 0.5f && keywordCoverage >= 0.4f -> QualityGrade.FAIR
            confidenceScore >= 0.3f -> QualityGrade.POOR
            else -> QualityGrade.VERY_POOR
        }

    /**
     * 检查是否满足质量要求
     */
    val meetsQualityThreshold: Boolean
        get() = qualityGrade in listOf(QualityGrade.EXCELLENT, QualityGrade.GOOD)

    /**
     * 检查是否为快速生成
     */
    val isFastGeneration: Boolean
        get() = generationLatencyMs < 1000L

    /**
     * 检查是否为高效压缩
     */
    val isEfficientCompression: Boolean
        get() = compressionRatio >= 2.0f && compressionRatio <= 10.0f

    /**
     * 获取综合评分（0-100）
     */
    val overallScore: Int
        get() {
            val baseScore = (confidenceScore * 40 + keywordCoverage * 30).toInt()
            val latencyBonus = if (isFastGeneration) 15 else 0
            val compressionBonus = if (isEfficientCompression) 15 else 0
            return (baseScore + latencyBonus + compressionBonus).coerceIn(0, 100)
        }
}
