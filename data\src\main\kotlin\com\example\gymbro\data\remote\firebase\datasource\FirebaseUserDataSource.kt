package com.example.gymbro.data.remote.firebase.datasource

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.resources.ResourceProvider
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.datasource.UserDataSource
import com.example.gymbro.data.remote.firebase.auth.AuthDataSource
import com.example.gymbro.domain.profile.model.user.BlockedUser
import com.example.gymbro.domain.profile.model.user.User
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.atan2
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.sqrt

/**
 * Firebase用户数据源
 * 专注于用户相关的Firestore操作
 * 实现UserDataSource接口，提供基础CRUD操作和查询功能
 */
@Singleton
class FirebaseUserDataSource
@Inject
constructor(
    private val firestoreDataSource: FirestoreDataSource,
    private val authDataSource: AuthDataSource,
    private val resourceProvider: ResourceProvider,
) : UserDataSource {
    companion object {
        private const val USERS_COLLECTION = "users"
        private const val ANONYMOUS_USERS_COLLECTION = "anonymous_users"
        private const val ONLINE_USERS_COLLECTION = "online_users"
        private const val BLOCKED_USERS_COLLECTION = "blocked_users"

        private fun getUserCollectionPath(
            userId: String,
            isAnonymous: Boolean,
        ): String = if (isAnonymous) "$ANONYMOUS_USERS_COLLECTION/$userId" else "$USERS_COLLECTION/$userId"
    }

    // === 基础用户CRUD操作 ===

    override suspend fun getUser(userId: String): ModernResult<User?> =
        withContext(Dispatchers.IO) {
            try {
                val isAnonymous = authDataSource.isAnonymousUser()
                val path = getUserCollectionPath(userId, isAnonymous)

                when (val result = firestoreDataSource.getDocument(path)) {
                    is ModernResult.Success -> {
                        val snapshot = result.data
                        val user =
                            if (snapshot.exists()) {
                                val userData = snapshot.data
                                if (userData != null) {
                                    User(
                                        userId = userId,
                                        email = userData["email"] as? String,
                                        displayName = userData["displayName"] as? String,
                                        phoneNumber = userData["phoneNumber"] as? String,
                                        username = userData["username"] as? String,
                                        createdAt = userData["createdAt"] as? Long ?: 0L,
                                        lastLoginAt = userData["lastLoginAt"] as? Long ?: 0L,
                                        isAnonymous = userData["isAnonymous"] as? Boolean ?: isAnonymous,
                                        profileImageUrl = userData["profileImageUrl"] as? String,
                                    )
                                } else {
                                    null
                                }
                            } else {
                                null
                            }
                        ModernResult.Success(user)
                    }
                    is ModernResult.Error -> throw Exception(
                        "Failed to get user document",
                        result.error.cause,
                    )
                    is ModernResult.Loading -> ModernResult.Success(null)
                }
            } catch (e: Exception) {
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "FirebaseUserDataSource.getUser",
                        message = UiText.DynamicString("获取用户信息失败"),
                        entityType = "User",
                        cause = e,
                        metadataMap = mapOf("userId" to userId),
                    ),
                )
            }
        }

    override suspend fun getUserById(userId: String): ModernResult<User> =
        withContext(Dispatchers.IO) {
            try {
                val userResult = getUser(userId)
                when (userResult) {
                    is ModernResult.Success -> {
                        val user = userResult.data ?: throw Exception("用户不存在")
                        ModernResult.Success(user)
                    }
                    is ModernResult.Error -> throw Exception("获取用户失败", userResult.error.cause)
                    is ModernResult.Loading -> throw Exception("获取用户超时")
                }
            } catch (e: Exception) {
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "FirebaseUserDataSource.getUserById",
                        message = UiText.DynamicString("获取用户信息失败"),
                        entityType = "User",
                        cause = e,
                        metadataMap = mapOf("userId" to userId),
                    ),
                )
            }
        }

    override suspend fun createUser(user: User): ModernResult<String> =
        withContext(Dispatchers.IO) {
            try {
                val userData =
                    mapUserToFirestoreData(user).toMutableMap().apply {
                        put("createdAt", System.currentTimeMillis())
                        put("updatedAt", System.currentTimeMillis())
                    }

                val collection = if (user.isAnonymous) ANONYMOUS_USERS_COLLECTION else USERS_COLLECTION

                val userId =
                    if (user.userId.isBlank()) {
                        // 创建新文档
                        when (val result = firestoreDataSource.addDocument(collection, userData)) {
                            is ModernResult.Success -> result.data.id
                            is ModernResult.Error -> throw Exception("添加用户文档失败", result.error.cause)
                            is ModernResult.Loading -> throw Exception("创建用户超时")
                        }
                    } else {
                        // 使用指定ID创建文档
                        val path = getUserCollectionPath(user.userId, user.isAnonymous)
                        when (val result = firestoreDataSource.setDocument(path, userData)) {
                            is ModernResult.Success -> user.userId
                            is ModernResult.Error -> throw Exception("设置用户文档失败", result.error.cause)
                            is ModernResult.Loading -> throw Exception("创建用户超时")
                        }
                    }
                ModernResult.Success(userId)
            } catch (e: Exception) {
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "FirebaseUserDataSource.createUser",
                        message = UiText.DynamicString("创建用户失败"),
                        entityType = "User",
                        cause = e,
                        metadataMap = mapOf("isAnonymous" to user.isAnonymous.toString()),
                    ),
                )
            }
        }

    override suspend fun updateUser(
        userId: String,
        updates: Map<String, Any?>,
    ): ModernResult<Unit> =
        withContext(
            Dispatchers.IO,
        ) {
            try {
                val isAnonymous = authDataSource.isAnonymousUser()
                val path = getUserCollectionPath(userId, isAnonymous)

                val updatedMap =
                    updates.toMutableMap().apply {
                        this["updatedAt"] = System.currentTimeMillis()
                    }

                @Suppress("UNCHECKED_CAST")
                val safeUpdates: Map<String, Any> = updatedMap.filterValues { it != null } as Map<String, Any>

                when (val result = firestoreDataSource.updateDocument(path, safeUpdates)) {
                    is ModernResult.Success -> ModernResult.Success(Unit)
                    is ModernResult.Error -> throw Exception("更新用户文档失败", result.error.cause)
                    is ModernResult.Loading -> throw Exception("更新用户超时")
                }
            } catch (e: Exception) {
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "FirebaseUserDataSource.updateUser.fields",
                        message = UiText.DynamicString("更新用户信息失败"),
                        entityType = "User",
                        cause = e,
                        metadataMap = mapOf("userId" to userId),
                    ),
                )
            }
        }

    override suspend fun updateUser(user: User): ModernResult<Unit> =
        withContext(Dispatchers.IO) {
            try {
                val path = getUserCollectionPath(user.userId, user.isAnonymous)
                val userData = mapUserToFirestoreData(user)
                val finalUserData =
                    userData.toMutableMap().apply {
                        this["updatedAt"] = System.currentTimeMillis()
                    }

                when (val result = firestoreDataSource.setDocument(path, finalUserData)) {
                    is ModernResult.Success -> ModernResult.Success(Unit)
                    is ModernResult.Error -> throw Exception("设置用户文档失败", result.error.cause)
                    is ModernResult.Loading -> throw Exception("更新用户超时")
                }
            } catch (e: Exception) {
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "FirebaseUserDataSource.updateUser.full",
                        message = UiText.DynamicString("更新用户信息失败"),
                        entityType = "User",
                        cause = e,
                        metadataMap = mapOf("userId" to user.userId),
                    ),
                )
            }
        }

    override suspend fun createOrUpdateUserProfile(user: User): ModernResult<String> =
        withContext(
            Dispatchers.IO,
        ) {
            try {
                val isAnonymous = user.isAnonymous
                val collection = if (isAnonymous) ANONYMOUS_USERS_COLLECTION else USERS_COLLECTION

                val userData = mapUserToFirestoreData(user)
                val finalUserData =
                    userData.toMutableMap().apply {
                        this["updatedAt"] = System.currentTimeMillis()
                        if (!this.containsKey("createdAt")) {
                            this["createdAt"] = System.currentTimeMillis()
                        }
                    }

                val userId =
                    if (user.userId.isBlank()) {
                        when (val result = firestoreDataSource.addDocument(collection, finalUserData)) {
                            is ModernResult.Success -> result.data.id
                            is ModernResult.Error -> throw Exception("添加用户资料失败", result.error.cause)
                            is ModernResult.Loading -> throw Exception("保存用户资料超时")
                        }
                    } else {
                        val path = getUserCollectionPath(user.userId, isAnonymous)
                        when (val result = firestoreDataSource.setDocument(path, finalUserData)) {
                            is ModernResult.Success -> user.userId
                            is ModernResult.Error -> throw Exception("设置用户资料失败", result.error.cause)
                            is ModernResult.Loading -> throw Exception("保存用户资料超时")
                        }
                    }
                ModernResult.Success(userId)
            } catch (e: Exception) {
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "FirebaseUserDataSource.createOrUpdateUserProfile",
                        message = UiText.DynamicString("保存用户资料失败"),
                        entityType = "User",
                        cause = e,
                        metadataMap = mapOf("isAnonymous" to user.isAnonymous.toString()),
                    ),
                )
            }
        }

    override suspend fun deleteUser(userId: String): ModernResult<Unit> =
        withContext(Dispatchers.IO) {
            try {
                val isAnonymous = authDataSource.isAnonymousUser()
                val path = getUserCollectionPath(userId, isAnonymous)

                when (val result = firestoreDataSource.deleteDocument(path)) {
                    is ModernResult.Success -> ModernResult.Success(Unit)
                    is ModernResult.Error -> throw Exception("删除用户文档失败", result.error.cause)
                    is ModernResult.Loading -> throw Exception("删除用户超时")
                }
            } catch (e: Exception) {
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "FirebaseUserDataSource.deleteUser",
                        message = UiText.DynamicString("删除用户失败"),
                        entityType = "User",
                        cause = e,
                        metadataMap = mapOf("userId" to userId),
                    ),
                )
            }
        }

    override fun getUserFlow(userId: String): Flow<User?> =
        firestoreDataSource
            .listenDocument(
                getUserCollectionPath(userId, authDataSource.isAnonymousUserSynchronously()),
            ).map { result ->
                when (result) {
                    is ModernResult.Success -> {
                        val snapshot = result.data
                        if (snapshot.exists()) {
                            val userData = snapshot.data
                            if (userData != null) {
                                try {
                                    User(
                                        userId = userId,
                                        email = userData["email"] as? String,
                                        displayName = userData["displayName"] as? String,
                                        phoneNumber = userData["phoneNumber"] as? String,
                                        username = userData["username"] as? String,
                                        // 安全的数字类型转换
                                        createdAt = (userData["createdAt"] as? Number)?.toLong() ?: 0L,
                                        lastLoginAt = (userData["lastLoginAt"] as? Number)?.toLong() ?: 0L,
                                        isAnonymous = userData["isAnonymous"] as? Boolean ?: authDataSource.isAnonymousUserSynchronously(),
                                        profileImageUrl = userData["profileImageUrl"] as? String,
                                    )
                                } catch (e: Exception) {
                                    Timber.e(e, "解析用户数据失败(flow): %s", userId)
                                    null
                                }
                            } else {
                                null
                            }
                        } else {
                            null
                        }
                    }
                    is ModernResult.Error -> {
                        Timber.w(
                            result.error.cause,
                            "Error in getUserFlow for user: $userId, error: ${result.error.uiMessage?.asString(resourceProvider) ?: "未知错误"}",
                        )
                        null
                    }
                    is ModernResult.Loading -> null
                }
            }

    // === 用户查询功能 ===

    override suspend fun queryUsers(
        field: String,
        value: Any,
    ): ModernResult<List<User>> =
        withContext(
            Dispatchers.IO,
        ) {
            try {
                when (
                    val result =
                        firestoreDataSource.executeQuery(
                            queryBuilder = { fs ->
                                fs
                                    .collection(USERS_COLLECTION) // 通常查询普通用户
                                    .whereEqualTo(field, value)
                            },
                            operationName = "FirebaseUserDataSource.queryUsers.$field",
                        )
                ) {
                    is ModernResult.Success -> {
                        val users =
                            result.data.documents.mapNotNull { doc ->
                                try {
                                    mapFirestoreDataToUser(doc.id, doc.data)
                                } catch (e: Exception) {
                                    Timber.e(e, "Error mapping user document in queryUsers: ${doc.id}")
                                    null
                                }
                            }
                        ModernResult.Success(users)
                    }
                    is ModernResult.Error -> throw Exception("查询用户失败", result.error.cause)
                    is ModernResult.Loading -> ModernResult.Success(emptyList())
                }
            } catch (e: Exception) {
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "FirebaseUserDataSource.queryUsers.$field",
                        message = UiText.DynamicString("查询用户失败"),
                        entityType = "User",
                        cause = e,
                        metadataMap = mapOf("field" to field, "value" to value.toString()),
                    ),
                )
            }
        }

    // === 其他方法的实现 ===
    // ... existing code ...

    // === 文件上传功能 ===

    override suspend fun uploadUserAvatar(
        userId: String,
        avatarFile: File,
    ): ModernResult<String> =
        withContext(
            Dispatchers.IO,
        ) {
            try {
                Timber.d("Firebase上传用户头像: %s", userId)

                // 简化实现，实际应该调用Firebase Storage上传文件
                val fileName = avatarFile.name
                val url = "https://firebasestorage.example.com/users/$userId/avatars/$fileName"

                // 更新用户记录中的头像URL
                val isAnonymous = authDataSource.isAnonymousUser()
                val path = getUserCollectionPath(userId, isAnonymous)

                when (
                    val result =
                        firestoreDataSource.updateDocument(
                            path,
                            mapOf("profileImageUrl" to url),
                        )
                ) {
                    is ModernResult.Success -> ModernResult.Success(url)
                    is ModernResult.Error -> throw Exception("更新头像URL失败", result.error.cause)
                    is ModernResult.Loading -> throw Exception("上传头像超时")
                }
            } catch (e: Exception) {
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "FirebaseUserDataSource.uploadUserAvatar",
                        message = UiText.DynamicString("上传头像失败"),
                        entityType = "User",
                        cause = e,
                        metadataMap = mapOf("userId" to userId, "fileName" to avatarFile.name),
                    ),
                )
            }
        }

    // === 实现剩余接口方法的存根 ===

    override suspend fun getUsers(userIds: List<String>): ModernResult<List<User>> =
        withContext(
            Dispatchers.IO,
        ) {
            try {
                if (userIds.isEmpty()) return@withContext ModernResult.Success(emptyList())

                val users = mutableListOf<User>()
                val batches = userIds.chunked(10)

                for (batch in batches) {
                    if (batch.isEmpty()) continue

                    when (
                        val result =
                            firestoreDataSource.executeQuery(
                                queryBuilder = { fs ->
                                    fs
                                        .collection(USERS_COLLECTION)
                                        .whereIn(
                                            com.google.firebase.firestore.FieldPath
                                                .documentId(),
                                            batch,
                                        )
                                },
                                operationName = "FirebaseUserDataSource.getUsers.batch",
                            )
                    ) {
                        is ModernResult.Success -> {
                            result.data.documents.mapNotNullTo(users) { document ->
                                try {
                                    mapFirestoreDataToUser(document.id, document.data)
                                } catch (e: Exception) {
                                    Timber.e(e, "Error parsing user document in getUsers: ${document.id}")
                                    null
                                }
                            }
                        }
                        is ModernResult.Error -> throw Exception("批量查询用户失败", result.error.cause)
                        is ModernResult.Loading -> {}
                    }
                }
                ModernResult.Success(users)
            } catch (e: Exception) {
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "FirebaseUserDataSource.getUsers",
                        message = UiText.DynamicString("批量获取用户失败"),
                        entityType = "User",
                        cause = e,
                        metadataMap = mapOf("userCount" to userIds.size.toString()),
                    ),
                )
            }
        }

    override suspend fun getNearbyUsers(
        latitude: Double,
        longitude: Double,
        radiusInKm: Double,
    ): ModernResult<List<User>> =
        withContext(
            Dispatchers.IO,
        ) {
            try {
                // 简化实现，实际可能需要使用更复杂的地理位置查询
                val usersList = mutableListOf<User>()

                when (val result = firestoreDataSource.getCollection(USERS_COLLECTION)) {
                    is ModernResult.Success -> {
                        result.data.documents.mapNotNullTo(usersList) { doc ->
                            try {
                                val userData = doc.data
                                if (userData != null) {
                                    // 安全的地理位置数据类型转换
                                    val userLat = (userData["latitude"] as? Number)?.toDouble()
                                    val userLng = (userData["longitude"] as? Number)?.toDouble()

                                    if (userLat != null && userLng != null) {
                                        val distance =
                                            calculateDistance(latitude, longitude, userLat, userLng)
                                        if (distance <= radiusInKm) {
                                            mapFirestoreDataToUser(doc.id, userData)
                                        } else {
                                            null
                                        }
                                    } else {
                                        null
                                    }
                                } else {
                                    null
                                }
                            } catch (e: Exception) {
                                Timber.e(e, "Error parsing user document in getNearbyUsers: ${doc.id}")
                                null
                            }
                        }
                        ModernResult.Success(usersList)
                    }
                    is ModernResult.Error -> throw Exception("获取用户集合失败", result.error.cause)
                    is ModernResult.Loading -> ModernResult.Success(emptyList())
                }
            } catch (e: Exception) {
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "FirebaseUserDataSource.getNearbyUsers",
                        message = UiText.DynamicString("获取附近用户失败"),
                        entityType = "User",
                        cause = e,
                        metadataMap =
                        mapOf(
                            "latitude" to latitude.toString(),
                            "longitude" to longitude.toString(),
                            "radiusInKm" to radiusInKm.toString(),
                        ),
                    ),
                )
            }
        }

    override suspend fun getUsersByGym(gymId: String): ModernResult<List<User>> = queryUsers(
        "preferredGym",
        gymId,
    )

    override suspend fun updateUserOnlineStatus(
        userId: String,
        isOnline: Boolean,
    ): ModernResult<Unit> =
        withContext(
            Dispatchers.IO,
        ) {
            try {
                val onlineData =
                    mapOf(
                        "userId" to userId,
                        "isOnline" to isOnline,
                        "lastActive" to System.currentTimeMillis(),
                    )

                when (
                    val result =
                        firestoreDataSource.setDocument(
                            "$ONLINE_USERS_COLLECTION/$userId",
                            onlineData,
                        )
                ) {
                    is ModernResult.Success -> ModernResult.Success(Unit)
                    is ModernResult.Error -> throw Exception("更新在线状态失败", result.error.cause)
                    is ModernResult.Loading -> throw Exception("更新在线状态超时")
                }
            } catch (e: Exception) {
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "FirebaseUserDataSource.updateUserOnlineStatus",
                        message = UiText.DynamicString("更新在线状态失败"),
                        entityType = "User",
                        cause = e,
                        metadataMap = mapOf("userId" to userId, "isOnline" to isOnline.toString()),
                    ),
                )
            }
        }

    override fun getOnlineUsersFlow(): Flow<List<String>> =
        callbackFlow {
            when (
                val result =
                    firestoreDataSource.executeQuery(
                        queryBuilder = { fs ->
                            fs
                                .collection(ONLINE_USERS_COLLECTION)
                                .whereEqualTo("isOnline", true)
                        },
                        operationName = "FirebaseUserDataSource.getOnlineUsersFlow",
                    )
            ) {
                is ModernResult.Success -> {
                    val userIds = result.data.documents.mapNotNull { it.getString("userId") }
                    trySend(userIds)
                }
                is ModernResult.Error -> {
                    Timber.e(result.error.cause, "Error getting online users flow")
                    close(result.error.cause)
                }
                is ModernResult.Loading -> {
                    trySend(emptyList())
                }
            }
            awaitClose { }
        }

    override suspend fun blockUser(
        userId: String,
        targetUserId: String,
    ): ModernResult<Unit> =
        withContext(
            Dispatchers.IO,
        ) {
            try {
                val blockedUserData =
                    mapOf(
                        "blockedAt" to System.currentTimeMillis(),
                        "targetUserId" to targetUserId,
                    )

                when (
                    val result =
                        firestoreDataSource.setDocument(
                            "$USERS_COLLECTION/$userId/$BLOCKED_USERS_COLLECTION/$targetUserId",
                            blockedUserData,
                        )
                ) {
                    is ModernResult.Success -> ModernResult.Success(Unit)
                    is ModernResult.Error -> throw Exception("屏蔽用户失败", result.error.cause)
                    is ModernResult.Loading -> throw Exception("屏蔽用户超时")
                }
            } catch (e: Exception) {
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "FirebaseUserDataSource.blockUser",
                        message = UiText.DynamicString("屏蔽用户失败"),
                        entityType = "User",
                        cause = e,
                        metadataMap = mapOf("userId" to userId, "targetUserId" to targetUserId),
                    ),
                )
            }
        }

    override suspend fun unblockUser(
        userId: String,
        targetUserId: String,
    ): ModernResult<Unit> =
        withContext(
            Dispatchers.IO,
        ) {
            try {
                when (
                    val result =
                        firestoreDataSource.deleteDocument(
                            "$USERS_COLLECTION/$userId/$BLOCKED_USERS_COLLECTION/$targetUserId",
                        )
                ) {
                    is ModernResult.Success -> ModernResult.Success(Unit)
                    is ModernResult.Error -> throw Exception("解除屏蔽失败", result.error.cause)
                    is ModernResult.Loading -> throw Exception("解除屏蔽超时")
                }
            } catch (e: Exception) {
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "FirebaseUserDataSource.unblockUser",
                        message = UiText.DynamicString("解除屏蔽失败"),
                        entityType = "User",
                        cause = e,
                        metadataMap = mapOf("userId" to userId, "targetUserId" to targetUserId),
                    ),
                )
            }
        }

    override fun getBlockedUserIds(userId: String): Flow<List<String>> =
        callbackFlow {
            when (
                val result =
                    firestoreDataSource.getSubCollection(
                        "$USERS_COLLECTION/$userId",
                        BLOCKED_USERS_COLLECTION,
                    )
            ) {
                is ModernResult.Success -> {
                    val blockedIds = result.data.documents.mapNotNull { it.getString("targetUserId") }
                    trySend(blockedIds)
                }
                is ModernResult.Error -> {
                    Timber.e(result.error.cause, "Error getting blocked users: %s", userId)
                    close(result.error.cause)
                }
                is ModernResult.Loading -> {
                    trySend(emptyList())
                }
            }
            awaitClose { }
        }

    override suspend fun getBlockedUserDetails(
        userId: String,
        blockedUserIds: List<String>,
    ): ModernResult<List<BlockedUser>> =
        withContext(
            Dispatchers.IO,
        ) {
            try {
                if (blockedUserIds.isEmpty()) return@withContext ModernResult.Success(emptyList())

                val blockedUsers = mutableListOf<BlockedUser>()
                val batches = blockedUserIds.chunked(10)

                for (batch in batches) {
                    when (
                        val usersResult =
                            firestoreDataSource.executeQuery(
                                queryBuilder = { fs ->
                                    fs.collection(USERS_COLLECTION).whereIn("userId", batch)
                                },
                                operationName = "FirebaseUserDataSource.getBlockedUserDetails.batch",
                            )
                    ) {
                        is ModernResult.Success -> {
                            for (userDoc in usersResult.data.documents) {
                                try {
                                    val userData = userDoc.data
                                    if (userData != null) {
                                        when (
                                            val blockedResult =
                                                firestoreDataSource.getDocument(
                                                    "$USERS_COLLECTION/$userId/$BLOCKED_USERS_COLLECTION/${userDoc.id}",
                                                )
                                        ) {
                                            is ModernResult.Success -> {
                                                val blockedDoc = blockedResult.data
                                                if (blockedDoc.exists()) {
                                                    val blockedAt = blockedDoc.getLong("blockedAt") ?: System.currentTimeMillis()

                                                    val blockedUser =
                                                        BlockedUser(
                                                            userId = userDoc.id,
                                                            username = userData["username"] as? String ?: "",
                                                            displayName = userData["displayName"] as? String,
                                                            avatarUrl = userData["profileImageUrl"] as? String,
                                                            blockTime = blockedAt,
                                                        )
                                                    blockedUsers.add(blockedUser)
                                                }
                                            }
                                            is ModernResult.Error -> {
                                                Timber.e(
                                                    blockedResult.error.cause,
                                                    "Error getting blocked user document: ${userDoc.id}",
                                                )
                                            }
                                            is ModernResult.Loading -> {}
                                        }
                                    }
                                } catch (e: Exception) {
                                    Timber.e(e, "Error parsing blocked user document: ${userDoc.id}")
                                }
                            }
                        }
                        is ModernResult.Error -> throw Exception("查询用户批次失败", usersResult.error.cause)
                        is ModernResult.Loading -> {}
                    }
                }

                ModernResult.Success(blockedUsers)
            } catch (e: Exception) {
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "FirebaseUserDataSource.getBlockedUserDetails",
                        message = UiText.DynamicString("获取屏蔽用户详情失败"),
                        entityType = "BlockedUser",
                        cause = e,
                        metadataMap =
                        mapOf(
                            "userId" to userId,
                            "blockedUserCount" to blockedUserIds.size.toString(),
                        ),
                    ),
                )
            }
        }

    override fun isUserBlocked(
        userId: String,
        targetUserId: String,
    ): Flow<Boolean> =
        callbackFlow {
            when (
                val result =
                    firestoreDataSource.getDocument(
                        "$USERS_COLLECTION/$userId/$BLOCKED_USERS_COLLECTION/$targetUserId",
                    )
            ) {
                is ModernResult.Success -> {
                    trySend(result.data.exists())
                }
                is ModernResult.Error -> {
                    Timber.e(
                        result.error.cause,
                        "Error checking if user is blocked: %s -> %s",
                        userId,
                        targetUserId,
                    )
                    close(result.error.cause)
                }
                is ModernResult.Loading -> {
                    trySend(false)
                }
            }
            awaitClose { }
        }

    // === 辅助方法 ===

    /**
     * 将User领域模型转换为Firestore数据
     */
    private fun mapUserToFirestoreData(user: User): Map<String, Any?> =
        mapOf(
            "userId" to user.userId,
            "email" to user.email,
            "displayName" to user.displayName,
            "phoneNumber" to user.phoneNumber,
            "username" to user.username,
            "createdAt" to user.createdAt,
            "lastLoginAt" to user.lastLoginAt,
            "isAnonymous" to user.isAnonymous,
            "profileImageUrl" to user.profileImageUrl,
        )

    /**
     * 将Firestore数据转换为User领域模型
     */
    private fun mapFirestoreDataToUser(
        userId: String,
        data: Map<String, Any>?,
    ): User? {
        if (data == null) return null
        return User(
            userId = userId,
            email = data["email"] as? String,
            displayName = data["displayName"] as? String,
            phoneNumber = data["phoneNumber"] as? String,
            username = data["username"] as? String,
            createdAt = (data["createdAt"] as? Number)?.toLong() ?: 0L,
            lastLoginAt = (data["lastLoginAt"] as? Number)?.toLong() ?: 0L,
            isAnonymous = data["isAnonymous"] as? Boolean ?: false,
            profileImageUrl = data["profileImageUrl"] as? String,
        )
    }

    /**
     * 简化的距离计算方法（Haversine公式）
     */
    private fun calculateDistance(
        lat1: Double,
        lng1: Double,
        lat2: Double,
        lng2: Double,
    ): Double {
        val earthRadius = 6371.0 // 地球半径，单位千米
        val dLat = Math.toRadians(lat2 - lat1)
        val dLng = Math.toRadians(lng2 - lng1)
        val a =
            sin(dLat / 2) * sin(dLat / 2) +
                cos(Math.toRadians(lat1)) * cos(Math.toRadians(lat2)) *
                sin(dLng / 2) * sin(dLng / 2)
        val c = 2 * atan2(sqrt(a), sqrt(1 - a))
        return earthRadius * c
    }
}
