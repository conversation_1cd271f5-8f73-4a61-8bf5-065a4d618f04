package com.example.gymbro.core.util.viewmodel

import com.example.gymbro.core.error.types.ErrorSeverity
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.ui.text.UiText

/**
 * 通用UI状态的具体实现
 *
 * 提供了CommonUiState的具体实现，可以直接实例化并在各种需要基础UI状态的场景中使用。
 * 解决了无法直接实例化抽象类CommonUiState的问题。
 */
data class SimpleUiState(
    override val isLoading: Boolean = false,
    override val error: UiText? = null,
    override val errorSeverity: ErrorSeverity? = null,
    override val isErrorRecoverable: Boolean = false,
    override val rawError: ModernDataError? = null,
    override val errorType: GlobalErrorType? = null,
) : CommonUiState(
    isLoading = isLoading,
    error = error,
    errorSeverity = errorSeverity,
    isErrorRecoverable = isErrorRecoverable,
    rawError = rawError,
    errorType = errorType,
)

/**
 * 创建一个空的SimpleUiState实例
 *
 * @return 一个所有字段都设为默认值的SimpleUiState实例
 */
fun emptyUiState(): SimpleUiState = SimpleUiState()

/**
 * 创建一个加载中状态的SimpleUiState实例
 *
 * @return 一个isLoading=true的SimpleUiState实例
 */
fun loadingUiState(): SimpleUiState = SimpleUiState(isLoading = true)

/**
 * 创建一个带错误的SimpleUiState实例
 *
 * @param error 错误消息
 * @param severity 错误严重程度
 * @param isRecoverable 错误是否可恢复
 * @param rawError 原始错误对象
 * @param errorType 错误类型
 * @return 包含指定错误信息的SimpleUiState实例
 */
fun errorUiState(
    error: UiText,
    severity: ErrorSeverity = ErrorSeverity.ERROR,
    isRecoverable: Boolean = false,
    rawError: ModernDataError? = null,
    errorType: GlobalErrorType? = GlobalErrorType.Unknown,
): SimpleUiState =
    SimpleUiState(
        isLoading = false,
        error = error,
        errorSeverity = severity,
        isErrorRecoverable = isRecoverable,
        rawError = rawError,
        errorType = errorType,
    )
