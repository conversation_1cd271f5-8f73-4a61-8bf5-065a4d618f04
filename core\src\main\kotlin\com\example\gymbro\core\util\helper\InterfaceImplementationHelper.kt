package com.example.gymbro.core.util.helper

import com.example.gymbro.core.error.types.ErrorCategory
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import timber.log.Timber

/**
 * 接口实现助手
 *
 * 提供辅助方法，用于简化接口实现过程
 *
 * 注意：此文件已从core/util移至core/util/helper，作为包结构优化的一部分
 */
object InterfaceImplementationHelper {
    /**
     * 创建一个返回未实现错误的ModernResult
     *
     * @param methodName 未实现的方法名
     * @return 包含未实现错误的ModernResult
     */
    fun <T> notImplemented(methodName: String): ModernResult<T> =
        ModernResult.Error(
            ModernDataError(
                operationName = methodName,
                errorType = GlobalErrorType.System.Internal,
                uiMessage = UiText.DynamicString("Method not implemented: $methodName"),
                cause = NotImplementedException(methodName),
                category = ErrorCategory.SYSTEM,
                metadataMap = mapOf("method" to methodName),
            ),
        )

    /**
     * 创建一个发出未实现错误的Flow
     *
     * @param methodName 未实现的方法名
     * @return 发出未实现错误的Flow
     */
    fun <T> notImplementedFlow(methodName: String): Flow<ModernResult<T>> =
        flow {
            emit(notImplemented(methodName))
        }

    /**
     * 创建一个发出null值的Flow，用于临时实现可能返回null的方法
     *
     * @param methodName 未完全实现的方法名（用于日志）
     * @return 发出包含null的成功结果的Flow
     */
    fun <T> nullFlow(methodName: String): Flow<ModernResult<T?>> =
        flow {
            Timber.w("使用了nullFlow临时实现: $methodName")
            emit(ModernResult.Success(null))
        }

    /**
     * 创建一个发出空列表的Flow，用于临时实现返回列表的方法
     *
     * @param methodName 未完全实现的方法名（用于日志）
     * @return 发出包含空列表的成功结果的Flow
     */
    fun <T> emptyListFlow(methodName: String): Flow<ModernResult<List<T>>> =
        flow {
            Timber.w("使用了emptyListFlow临时实现: $methodName")
            emit(ModernResult.Success(emptyList()))
        }

    /**
     * 创建一个发出默认值的Flow，用于临时实现返回特定值的方法
     *
     * @param defaultValue 要返回的默认值
     * @param methodName 未完全实现的方法名（用于日志）
     * @return 发出包含默认值的成功结果的Flow
     */
    fun <T> defaultValueFlow(defaultValue: T, methodName: String): Flow<ModernResult<T>> =
        flow {
            Timber.w("使用了defaultValueFlow临时实现: $methodName")
            emit(ModernResult.Success(defaultValue))
        }

    /**
     * 创建一个发出mock数据的Flow，用于测试或临时实现
     *
     * @param mockData 要发出的mock数据
     * @param methodName 未完全实现的方法名（用于日志）
     * @param isMocked 是否启用mock，默认为true
     * @return 如果isMocked为true，则发出包含mockData的成功结果的Flow；否则发出未实现错误
     */
    fun <T> mockFlow(mockData: T, methodName: String, isMocked: Boolean = true): Flow<ModernResult<T>> =
        flow {
            if (isMocked) {
                Timber.d("使用了mockFlow: $methodName")
                emit(ModernResult.Success(mockData))
            } else {
                emit(notImplemented(methodName))
            }
        }

    /**
     * 表示方法未实现的异常
     *
     * @param methodName 未实现的方法名
     */
    class NotImplementedException(
        methodName: String,
    ) : UnsupportedOperationException("Method not implemented: $methodName")
}
