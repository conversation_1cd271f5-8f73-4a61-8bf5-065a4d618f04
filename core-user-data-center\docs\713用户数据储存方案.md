
1. Create a new UserDataCenter module in core package
Create a new module `core-user-data-center` that will serve as a centralized repository for all user-related data. This module will:
- Define a unified `UserData` model combining auth and profile information
- Provide a `UserDataRepository` interface with implementation
- Follow Clean Architecture principles with clear separation of concerns
- Use StateFlow to emit real-time user data updates
1. Define UserDataCenter API interfaces and models
Create the following components:
- `UserDataCenterApi` interface exposing methods like `observeUserData()`, `updateUserProfile()`, `syncAuthData()`
- `UnifiedUserData` data class containing both auth info (uid, email) and profile info (displayName, fitness data)
- `UserDataState` sealed class for handling loading/success/error states
- Follow the established pattern from ProfileFeatureApi
1. Implement data synchronization logic
Create `UserDataSynchronizer` that:
- Listens to auth state changes from AuthRepository
- Automatically syncs auth data to profile when user registers/signs in
- Maintains data consistency between auth and profile modules
- Handles edge cases like profile creation for new users
1. Update Auth module to publish to UserDataCenter
Modify the Auth module to:
- Inject `UserDataCenterApi` dependency
- Call `syncAuthData()` after successful registration/login
- Pass registration data (email, displayName) to UserDataCenter
- Maintain backward compatibility with existing auth flow
1. Update Profile module to use UserDataCenter
Refactor Profile module to:
- Read initial data from UserDataCenter instead of creating empty profiles
- Update UserDataCenter when profile is modified
- Maintain the existing `ProfileFeatureApi` for compatibility
- Use UserDataCenter as the single source of truth
1. Integrate UserDataCenter with Coach module
Update Coach module to:
- Inject `UserDataCenterApi` instead of separate auth/profile dependencies
- Modify `SendChatMessageAndGetResponseUseCase` to get unified user data
- Update `LayeredPromptBuilder` to accept `UnifiedUserData` for context building
- Create a `UserDataMemoryIntegrator` that formats user data for AI prompts
1. Update dependency injection configuration
Configure Hilt modules to:
- Provide `UserDataCenterApi` as a singleton
- Update existing module dependencies
- Ensure proper initialization order
- Add necessary bindings in the DI graph
1. Add comprehensive testing and documentation
Create:
- Unit tests for UserDataCenter repository and synchronizer
- Integration tests for data flow between modules
- Update module documentation with usage examples
- Add architecture diagram showing the new data flow
用户的建议非常出色！创建 core-user-data-center 模块确实是一个更加优雅和可维护的解决方案。让我制定详细的实施计划：

🎯 Phase 2: 详细集成计划与设计
核心架构设计
1. UserDataCenter 模块架构
2. 核心数据模型设计
3. UserDataCenterApi 接口设计
实施步骤详细计划
Step 1: 创建 core-user-data-center 模块
创建模块目录结构
配置 build.gradle.kts 依赖
定义核心接口和数据模型
实现基础的 Repository 和 API
Step 2: 实现 UserDataSynchronizer
监听 Auth 状态变化
自动同步认证数据到统一存储
处理新用户初始化逻辑
实现数据冲突解决策略
Step 3: 重构 Auth 模块集成
注入 UserDataCenterApi 依赖
在成功认证后调用 syncAuthData()
保持向后兼容性
添加错误处理和重试机制
Step 4: 重构 Profile 模块集成
修改 ProfileFeatureApiImpl 使用 UserDataCenter
更新数据读取和写入逻辑
保持现有 ProfileFeatureApi 接口不变
实现数据转换逻辑
Step 5: 集成 Coach 模块
修改 SendChatMessageAndGetResponseUseCase
更新 LayeredPromptBuilder 支持用户上下文
创建 UserDataMemoryIntegrator 格式化用户数据
实现 AI prompt 的用户个性化
Step 6: 配置依赖注入
创建 UserDataCenterModule
更新各模块的 DI 配置
确保正确的初始化顺序
添加必要的作用域和绑定
Step 7: 测试和验证
编写单元测试和集成测试
验证数据流的正确性
测试并发安全性
性能测试和优化
Step 8: 文档和部署
更新架构文档
创建使用示例
添加迁移指南
部署和监控
关键技术决策
数据存储策略: 使用 ROOM 数据库存储统一用户数据，支持离线访问
同步策略: 采用事件驱动的自动同步，减少手动干预
错误处理: 使用 ModernResult 统一错误处理，支持重试和降级
性能优化: 使用 StateFlow 缓存数据，减少数据库查询
向后兼容: 保持现有 API 接口不变，内部重构实现
这个方案将创建一个真正的用户数据统一管理中心，为整个应用提供一致、可靠的用户数据服务。




下面的方案在**不再引入任何硬编码与 TODO**的前提下，完整解决了 Auth／Profile／Workout 三大模块的数据统一、清理和重构问题，并给出可直接落地的流程与数据流示意图。

---

## 1 核心思路：以 **UserDataCenter** 为单一真实来源（SSOT）

> 方案 2 已经初步描述了创建 `core-user-data-center` 模块、统一数据模型与同步机制等要点。
> 以下是在此基础上的深度补全。

| 组件                       | 关键职责                                                       | 说明                      |
| ------------------------ | ---------------------------------------------------------- | ----------------------- |
| **UnifiedUserData**      | `uid、email` + 完整 Profile                                   | 所有下游仅感知这一数据模型           |
| **UserDataCenterApi**    | `observeUserData()`、`syncAuthData()`、`updateUserProfile()` | 通过 StateFlow 实时推送       |
| **UserDataSynchronizer** | 监听 AuthState，驱动双向同步                                        | 新增冲突解决策略：以服务器时间戳为准，自动合并 |

### 同步流程

1. **注册/登录**成功 → Auth 调用 `UserDataCenterApi.syncAuthData()` 同步基础信息。
2. **UserDataSynchronizer** 检测到用户变更，若 Profile 不存在则触发 `CreateOrUpdateProfileUseCase`。
3. Profile 侧所有更新统一走 `UserDataCenterApi.updateUserProfile()`，确保数据一致。
4. Coach/Memory/Workout 等模块仅订阅 `observeUserData()`，彻底解耦。

---

## 2 数据库整合：让 Workout 安全访问用户信息

### 2.1 单一 **AppDatabase** 持久化用户数据

* 保留 Workout 自己的 **PlanDB / SessionDB / TemplateDB**（大体积、专用表，避免膨胀）。
* **AppDatabase** 只存放跨域信息（User、Token、Settings…）。
* 两类数据库通过 **room-multimodule** 和 **Hilt Qualifier** 注入，互不污染 schema。

### 2.2 跨库关联的实现

| 场景                      | 方案                                                                                                                                                                            |
| ----------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Workout 需要 userId、单位偏好等 | 通过 `UserDataCenter.observeUserData()` 拿到内存对象，无须直接查表                                                                                                                           |
| Workout 想做离线 JOIN（统计）   | 在 WorkoutDB 内建 **DatabaseView `v_user_stats`** 按需物化：<br>`CREATE VIEW v_user_stats AS SELECT u.uid, u.height, u.weight FROM user_profile u;`<br>通过 **Room @DatabaseView** 自动同步 |
| 同步一致性                   | 由 **UserDataSynchronizer** 统一触发 `WorkoutSyncCoordinator`，当用户信息更新时批量刷新视图／缓存                                                                                                    |

> 这样既保住了 Workout 的高并发写入性能，又让其得到最新个人信息；同时无任何硬编码字段。

---

## 3 消除 TODO 与硬编码的执行清单

| 类别            | 动作                                                                         | 工具链                       |
| ------------- | -------------------------------------------------------------------------- | ------------------------- |
| **扫描现存 TODO** | `./gradlew detektTodoReport` → 导出 `build/reports/todo.txt`                 | Detekt + 自定义 TodoProvider |
| **落库迁移脚本**    | 编写 Room `Migration(⟨from⟩, ⟨to⟩)` 完整实现；禁止留下空 `TODO("Not yet implemented")` | KotlinPoet 生成模板           |
| **常量硬编码**     | 引入 `RemoteConfigProvider`／`DataStore Preferences`；所有魔数移入 `ConfigKeys.kt`   | build-time lint           |
| **测试覆盖**      | 单元 + 合并流向集成测试（见 2.2）                                                       | JUnit5 + Turbine          |

执行完毕后 **CI 阶段若仍存在 TODO/HACK 注释直接 fail build**，保证彻底清零。

---

## 4 遗留文件清理与模块重组

1. **移除**：

   * 旧的 *UserLocalDataSourceImpl*、*TempUserRepositoryImpl* 等与 UserDataCenter 重复的实现。
   * `features/profile/**/manual-save` 相关代码（已改自动保存）。
2. **合并**：

   * 把 *profile/internal/api* 与 *profile/api* 下相同接口合并，使用 *api* 作为公开包。
3. **迁移**：

   * 将 *coach/model/ai/UserAiContext.kt* 内的数据获取逻辑替换为 `UnifiedUserData`（详见下一节注入）。
4. **目录&包名**：

   * 新增 `com.example.gymbro.core.userdata.*` 并通过 **Hilt ComponentExtender** 对外暴露。
   * 删除 `workout/cleanup_old_files.md` 中列出的遗留 JSON 导出文件。

---

## 5 Prompt 系统完整接入用户上下文

在 Coach 模块中已列出需要把 Profile 注入 Prompt 的步骤，但缺少真正的数据源；通过 UserDataCenter 可一次性补齐：

```kotlin
// SendChatMessageAndGetResponseUseCase.kt
class SendChatMessageAndGetResponseUseCase @Inject constructor(
    private val userDataCenter: UserDataCenterApi,
    private val layeredPromptBuilder: LayeredPromptBuilder,
    /* 其他依赖 */
) {
    suspend operator fun invoke(params: Params): ChatResult {
        val userData = userDataCenter.observeUserData().first()   // 已是 UnifiedUserData
        val prompt   = layeredPromptBuilder.buildChatMessages(
            userLayer    = params.userInput,
            userData     = userData            // ✅ 注入
        )
        /* ... 生成并返回结果 ... */
    }
}
```

`LayeredPromptBuilder` 则新增 `buildUserContextLayer(unifiedUserData)`；所有格式化逻辑由 **UserDataMemoryIntegrator** 负责，确保 token 预算可控。

---

## 6 流程 & 数据流向示意图

```mermaid
flowchart TD
    subgraph Core
        Auth[Auth\nModule] -- syncAuthData --> UDC[UserDataCenter]
        Profile[Profile\nModule] -- updateUserProfile --> UDC
    end

    UDC -- observeUserData --> Coach[Coach\nPrompt System]
    UDC -- observeUserData --> WorkoutFacade(UnifiedWorkoutDataCenter)

    %% DB Layer
    AppDB[(AppDatabase\nUser, Token, Settings)]
    PlanDB[(PlanDB)]
    SessionDB[(SessionDB)]
    TemplateDB[(TemplateDB)]

    UDC -- dao --> AppDB
    WorkoutFacade -- planDao --> PlanDB
    WorkoutFacade -- sessionDao --> SessionDB
    WorkoutFacade -- templateDao --> TemplateDB

    %% Cross-DB read via view / in-memory
    WorkoutFacade -- user stats view --> AppDB
```

---

## 7 落地节奏（建议）

| 周期        | 里程碑                                                     |
| --------- | ------------------------------------------------------- |
| **第 1 周** | 建立 `core-user-data-center`、迁移模型、打通 Auth → UDC           |
| **第 2 周** | Profile 读写改为走 UDC；完成双向同步测试                              |
| **第 3 周** | Coach/Memory/Workout 订阅 `observeUserData()`；替换 Prompt 层 |
| **第 4 周** | Room 视图 / Migration 脚本上线；废弃旧数据源并删文件                     |
| **第 5 周** | CI 阶段 TODO 扫描阈值设为 0；性能 & 回归测试；文档交付                      |

---

### 你现在可以做什么

1. **新建 `core-user-data-center` 模块** 并复制上述接口 / 同步骨架。
2. 按顺序迁移 Auth、Profile、Coach、Workout 的依赖。
3. 执行 **detektTodoReport**；没有 TODO 才允许合并。

这样即可在不留任何尾巴的情况下完成数据统一、模块解耦与文件清理。



下面这份 **“可落地的代码清理执行手册”** 按 *任务->步骤->交付物* 拆分，覆盖了 **TODO／硬编码剔除、废弃文件删除、接口重组、数据库迁移** 等全部动作。你可以直接据此安排 Jira 或 GitHub issue，Sprint 推进即可。

---

## 0 — 前置准备

| 动作                         | 说明                                                                                                                                                                                                                                                   |
| -------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **接入 Detekt TodoProvider** | 在 `build.gradle` 根模块里启用 `detektPlugins "io.gitlab.arturbosch.detekt:detekt-formatting"` 并配置 `TodoComment`.                                                                                                                                           |
| **新增 CI Gate**             | `.github/workflows/ci.yml` 里新增 `./gradlew detektTodoReport && test -f build/reports/todo.txt && exit 1`；只要还有 TODO/HACK 即 fail。                                                                                                                       |
| **一次性扫描 & 列表化**            | 先跑 `./gradlew detektTodoReport`，得到首批 TODO 清单。示例：<br>• **EnhancedUserBackupService** provider 缺实现  <br>• **TempUserRepositoryImpl** 占位实现  <br>• **DataSourceBindingsModule** 多处 `// TODO` 占位绑定  <br>• **uploadAvatar** 未完成功能  <br>• Workout 统计扩展函数留空  |

> **交付物**：`docs/cleanup/todo_baseline_2025-07-13.md`（自动生成）。

---

## 1 — 废弃实现与占位代码处理

| 目标                                | 步骤                                                                                                                                                                 | 交付物   |
| --------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ----- |
| **TempUserRepositoryImpl → 正式实现** | - 新建 `data/repository/user/UserRepositoryImpl`，实现全部接口并通过 **UserDataCenter** 读写。<br>- 替换 `UserRepositoryModule` 中绑定，从 Temp→正式。<br>- 删除 `TempUserRepositoryImpl.kt`。 | PR #1 |
| **Enhanced*Backup* 占位删除或实现**      | - 若未来 3 个月无备份需求，直接删掉 `provideEnhancedUserBackupService*` 整段注释。<br>- 若要保留，创建 `core/backup/EnhancedUserBackupService` 并补全。                                           | PR #2 |
| **DataSourceBindingsModule 清洗**   | - 创建 `datasource/workout/WorkoutLocalDataSourceImpl` + 接口；完成真正绑定。<br>- 为 `UserLocal/RemoteDataSource` 补接口并绑定；或改为 @Provides factory。<br>- 移除文件内所有 `TODO` 注释。        | PR #3 |

---

## 2 — 接口重组 & 模块化

1. **创建 `core-user-data-center`**

   * 定义 `UnifiedUserData`、`UserDataCenterApi`、`UserDataSynchronizer`。
   * 提供 `observeUserData()` StateFlow。

2. \*\*Profile / Auth / Workout **依赖注入重定向**

   * ProfileFeatureApiImpl 改为从 **UserDataCenterApi** 读取，删除对旧 DAO 的直接依赖。
   * Coach Prompt 通过新 API 注入用户上下文。

3. **DI 图整理**

   * 在 `di/data/DataModule.kt` 里，把 `UserRepositoryModule` 及未来 `UserDataCenterModule` 一并聚合，确保单入口。

---

## 3 — 数据库迁移

| 动作                 | 说明                                                                                                                                                                                        |
| ------------------ | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **合并跨模块用户表**       | 在 **AppDatabase** 新建 `user_profile`、`user_settings` 表；`Workout` 独立表保持不变。                                                                                                                  |
| **Room Migration** | 编写 `Migration(9, 10)`：<br>`sql<br>CREATE TABLE user_profile_new AS SELECT * FROM user_profile; /* 示例 */<br>DROP TABLE user_profile; ALTER TABLE user_profile_new RENAME TO user_profile;` |
| **跨库视图**           | Workout 需要 JOIN 用户资料 → 在 WorkoutDB 创建 `@DatabaseView("SELECT u.uid, u.height, u.weight FROM appdb.user_profile u")`.                                                                      |
| **测试**             | Instrumentation 测试：升级旧版 DB → 插入数据 → 运行 Migration → 校验数据一致。                                                                                                                                |

---

## 4 — 功能 TODO 彻底收尾

| 文件                                       | TODO 说明  | 处理方式                                    |
| ---------------------------------------- | -------- | --------------------------------------- |
| `UserProfileRepositoryImpl.uploadAvatar` | 未实现上传逻辑  | 接入 `MediaUploader`，走后台任务；或删接口。          |
| Workout 统计扩展函数                           | 3 个空实现   | 调用 `SessionRepository` 聚合后端数据；如无需求立即删除。 |

> **完成后**：再次运行 `./gradlew detektTodoReport`，报告应为空；CI 才放行。

---

## 5 — 日志与调试代码清理

* 搜索 pattern `println("🔥` → 全部删除或转 `logger.d`，例如 `UserProfileRepositoryImpl` 诊断输出。
* 搜索 `logger.*("❌"` / `logger.*("🔥"` → 降级为常规 debug 级别或移除。

---

## 6 — 交付节奏（5 周滚动）

| 周      | 内容                                                |
| ------ | ------------------------------------------------- |
| **W1** | 阶段 0 & 1：生成 TODO 清单，完成 TempUserRepository 替换。     |
| **W2** | 阶段 1 续：Backup 占位处理，DataSourceBindingsModule 清洗。   |
| **W3** | 阶段 2：落地 **UserDataCenter**，调整 Profile / Coach 依赖。 |
| **W4** | 阶段 3：Room 合并 & Migration，Workout 视图联表。            |
| **W5** | 阶段 4-5：功能 TODO 完成、日志清理、CI 通过、文档更新。                |

---

## 7 — 成果验收清单

* [ ] `build/reports/todo.txt` 为空
* [ ] `./gradlew testDebugUnitTest` 全绿
* [ ] 数据迁移测试用例通过
* [ ] App 能正常登录、编辑资料、生成运动计划
* [ ] Coach 生成的 Prompt 含最新用户上下文

---

> 🚩 **后续**：完成以上步骤后，将 Git tag `cleanup-user-module-v1.0` 打点，方便回溯；然后正式关闭与用户数据模块相关的遗留 issue。
