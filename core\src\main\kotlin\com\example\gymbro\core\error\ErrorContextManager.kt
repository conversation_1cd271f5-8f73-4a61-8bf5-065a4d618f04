package com.example.gymbro.core.error

import com.example.gymbro.core.error.types.ModernDataError
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 错误上下文管理器
 *
 * 负责管理错误的上下文信息，包括用户操作历史、错误发生的环境、
 * 以及为错误恢复提供必要的上下文数据
 */
@Singleton
class ErrorContextManager @Inject constructor() {

    // 错误上下文存储
    private val errorContexts = ConcurrentHashMap<String, ErrorContext>()

    // 全局错误上下文状态
    private val _globalErrorContext = MutableStateFlow(GlobalErrorContext())
    val globalErrorContext: Flow<GlobalErrorContext> = _globalErrorContext.asStateFlow()

    // 用户操作历史（最近50个操作）
    private val userActionHistory = mutableListOf<UserAction>()
    private val maxHistorySize = 50

    /**
     * 记录用户操作
     *
     * @param action 用户操作
     * @param module 操作所在模块
     * @param details 操作详情
     */
    fun recordUserAction(
        action: String,
        module: String,
        details: Map<String, Any> = emptyMap(),
    ) {
        val userAction = UserAction(
            action = action,
            module = module,
            timestamp = System.currentTimeMillis(),
            details = details,
        )

        synchronized(userActionHistory) {
            userActionHistory.add(userAction)
            if (userActionHistory.size > maxHistorySize) {
                userActionHistory.removeAt(0)
            }
        }

        Timber.d("记录用户操作: $module.$action")
    }

    /**
     * 创建错误上下文
     *
     * @param errorId 错误唯一标识
     * @param error 错误信息
     * @param module 发生错误的模块
     * @param userContext 用户上下文
     * @return 错误上下文
     */
    fun createErrorContext(
        errorId: String,
        error: ModernDataError,
        module: String,
        userContext: Map<String, Any> = emptyMap(),
    ): ErrorContext {
        val recentActions = getRecentUserActions(10)
        val environmentInfo = collectEnvironmentInfo()

        val errorContext = ErrorContext(
            errorId = errorId,
            error = error,
            module = module,
            timestamp = System.currentTimeMillis(),
            userContext = userContext,
            recentUserActions = recentActions,
            environmentInfo = environmentInfo,
            recoveryAttempts = 0,
        )

        errorContexts[errorId] = errorContext
        updateGlobalErrorContext(errorContext)

        Timber.d("创建错误上下文: $errorId in $module")
        return errorContext
    }

    /**
     * 获取错误上下文
     */
    fun getErrorContext(errorId: String): ErrorContext? {
        return errorContexts[errorId]
    }

    /**
     * 更新错误上下文
     */
    fun updateErrorContext(
        errorId: String,
        update: (ErrorContext) -> ErrorContext,
    ): ErrorContext? {
        val currentContext = errorContexts[errorId] ?: return null
        val updatedContext = update(currentContext)
        errorContexts[errorId] = updatedContext

        Timber.d("更新错误上下文: $errorId")
        return updatedContext
    }

    /**
     * 记录恢复尝试
     */
    fun recordRecoveryAttempt(
        errorId: String,
        recoveryStrategy: String,
        success: Boolean,
        details: Map<String, Any> = emptyMap(),
    ) {
        updateErrorContext(errorId) { context ->
            val attempt = RecoveryAttempt(
                strategy = recoveryStrategy,
                timestamp = System.currentTimeMillis(),
                success = success,
                details = details,
            )

            context.copy(
                recoveryAttempts = context.recoveryAttempts + 1,
                recoveryHistory = context.recoveryHistory + attempt,
            )
        }

        Timber.d("记录恢复尝试: $errorId, 策略: $recoveryStrategy, 成功: $success")
    }

    /**
     * 生成错误报告
     */
    fun generateErrorReport(errorId: String): ErrorContextReport? {
        val context = errorContexts[errorId] ?: return null

        return ErrorContextReport(
            errorId = errorId,
            error = context.error,
            module = context.module,
            timestamp = context.timestamp,
            userActionSummary = generateUserActionSummary(context.recentUserActions),
            environmentSummary = generateEnvironmentSummary(context.environmentInfo),
            recoverySummary = generateRecoverySummary(context.recoveryHistory),
            recommendedActions = generateRecommendedActions(context),
        )
    }

    /**
     * 清理过期的错误上下文
     */
    fun cleanupExpiredContexts(maxAgeMs: Long = 24 * 60 * 60 * 1000L) { // 24小时
        val currentTime = System.currentTimeMillis()
        val expiredKeys = errorContexts.entries
            .filter { currentTime - it.value.timestamp > maxAgeMs }
            .map { it.key }

        expiredKeys.forEach { errorContexts.remove(it) }

        if (expiredKeys.isNotEmpty()) {
            Timber.d("清理过期错误上下文: ${expiredKeys.size}个")
        }
    }

    /**
     * 获取最近的用户操作
     */
    private fun getRecentUserActions(count: Int): List<UserAction> {
        return synchronized(userActionHistory) {
            userActionHistory.takeLast(count)
        }
    }

    /**
     * 收集环境信息
     */
    private fun collectEnvironmentInfo(): Map<String, Any> {
        return mapOf(
            "timestamp" to System.currentTimeMillis(),
            "availableMemory" to Runtime.getRuntime().freeMemory(),
            "totalMemory" to Runtime.getRuntime().totalMemory(),
            "activeThreads" to Thread.activeCount(),
        )
    }

    /**
     * 更新全局错误上下文
     */
    private fun updateGlobalErrorContext(errorContext: ErrorContext) {
        _globalErrorContext.value = _globalErrorContext.value.copy(
            lastErrorTime = errorContext.timestamp,
            totalErrors = _globalErrorContext.value.totalErrors + 1,
            errorsByModule = _globalErrorContext.value.errorsByModule.toMutableMap().apply {
                put(errorContext.module, (get(errorContext.module) ?: 0) + 1)
            },
        )
    }

    /**
     * 生成用户操作摘要
     */
    private fun generateUserActionSummary(actions: List<UserAction>): String {
        return actions.takeLast(5).joinToString(" -> ") { "${it.module}.${it.action}" }
    }

    /**
     * 生成环境摘要
     */
    private fun generateEnvironmentSummary(env: Map<String, Any>): String {
        val memory = env["availableMemory"] as? Long ?: 0L
        val total = env["totalMemory"] as? Long ?: 0L
        val memoryUsage = if (total > 0) ((total - memory) * 100 / total) else 0

        return "内存使用率: $memoryUsage%, 活跃线程: ${env["activeThreads"]}"
    }

    /**
     * 生成恢复摘要
     */
    private fun generateRecoverySummary(history: List<RecoveryAttempt>): String {
        if (history.isEmpty()) return "无恢复尝试"

        val successful = history.count { it.success }
        val total = history.size
        return "恢复尝试: $successful/$total 成功"
    }

    /**
     * 生成推荐操作
     */
    private fun generateRecommendedActions(context: ErrorContext): List<String> {
        val recommendations = mutableListOf<String>()

        // 基于错误类型的推荐
        when {
            context.error.operationName.contains("network", ignoreCase = true) -> {
                recommendations.add("检查网络连接")
                recommendations.add("稍后重试")
            }
            context.error.operationName.contains("sync", ignoreCase = true) -> {
                recommendations.add("等待网络恢复后自动同步")
                recommendations.add("检查存储空间")
            }
            context.error.operationName.contains("navigation", ignoreCase = true) -> {
                recommendations.add("返回上一页面重试")
                recommendations.add("重启应用")
            }
        }

        // 基于恢复历史的推荐
        if (context.recoveryAttempts > 2) {
            recommendations.add("联系技术支持")
        }

        return recommendations
    }
}

/**
 * 错误上下文
 */
data class ErrorContext(
    val errorId: String,
    val error: ModernDataError,
    val module: String,
    val timestamp: Long,
    val userContext: Map<String, Any>,
    val recentUserActions: List<UserAction>,
    val environmentInfo: Map<String, Any>,
    val recoveryAttempts: Int,
    val recoveryHistory: List<RecoveryAttempt> = emptyList(),
)

/**
 * 用户操作
 */
data class UserAction(
    val action: String,
    val module: String,
    val timestamp: Long,
    val details: Map<String, Any>,
)

/**
 * 恢复尝试
 */
data class RecoveryAttempt(
    val strategy: String,
    val timestamp: Long,
    val success: Boolean,
    val details: Map<String, Any>,
)

/**
 * 全局错误上下文
 */
data class GlobalErrorContext(
    val lastErrorTime: Long? = null,
    val totalErrors: Int = 0,
    val errorsByModule: Map<String, Int> = emptyMap(),
)

/**
 * 错误上下文报告
 */
data class ErrorContextReport(
    val errorId: String,
    val error: ModernDataError,
    val module: String,
    val timestamp: Long,
    val userActionSummary: String,
    val environmentSummary: String,
    val recoverySummary: String,
    val recommendedActions: List<String>,
)
