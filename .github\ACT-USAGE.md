# 🎯 GymBro ACT 本地CI/CD验证指南

## 📋 概述

使用ACT工具在本地验证GitHub Actions工作流程，专注于MVP核心功能：Domain + Data模块 + 核心构建。

## 🛠️ 环境要求

### 必需工具
- **ACT工具**: `D:\GymBro\act_Windows_x86_64\act.exe`
- **Docker Desktop**: 必须运行中
- **Java 17+**: 用于Gradle构建
- **Git**: 版本控制

### 可选工具
- **PowerShell 5.0+**: 运行PowerShell脚本
- **Windows Terminal**: 更好的终端体验

## 🚀 快速开始

### 方法1: 使用批处理脚本（推荐）
```batch
# 运行交互式ACT验证
scripts\run-act-mvp.bat
```

### 方法2: 使用PowerShell脚本
```powershell
# 运行ACT验证
.\scripts\act-mvp-validation.ps1

# 运行Domain+Data重点测试
.\scripts\domain-data-focus-test.ps1
```

### 方法3: 直接使用ACT命令
```batch
# 验证PR工作流程
..\act_Windows_x86_64\act.exe --workflows .github/workflows/pr-validation.yml

# 验证MVP专用工作流程
..\act_Windows_x86_64\act.exe --workflows .github/workflows/act-mvp.yml

# 干运行模式（仅显示执行计划）
..\act_Windows_x86_64\act.exe --workflows .github/workflows/act-mvp.yml --dryrun
```

## 📁 工作流程文件说明

### 1. `.github/workflows/act-mvp.yml` (推荐)
**专门为ACT本地验证设计的工作流程**
- ✅ 重点关注Domain + Data模块
- ✅ 优化的资源使用（适合本地运行）
- ✅ 快速反馈循环
- ✅ MVP功能验证

**包含的Job:**
- `core-modules-validation`: 核心模块验证
- `mvp-features-validation`: MVP功能模块验证
- `code-quality-check`: 代码质量检查
- `build-verification`: 构建验证

### 2. `.github/workflows/pr-validation.yml`
**标准PR验证工作流程**
- 完整的PR验证流程
- 模块变更检测
- 并行执行多个检查

### 3. `.github/workflows/develop-integration.yml`
**分支集成工作流程**
- 完整的CI检查
- APK构建和分发（MVP中禁用）

## 🎯 重点验证模块

### Domain模块 (最重要)
```bash
# 编译验证
./gradlew :domain:compileDebugKotlin

# 单元测试
./gradlew :domain:testDebugUnitTest

# 代码质量
./gradlew :domain:ktlintCheck :domain:detekt
```

### Data模块 (最重要)
```bash
# 编译验证
./gradlew :data:compileDebugKotlin

# 单元测试
./gradlew :data:testDebugUnitTest

# 代码质量
./gradlew :data:ktlintCheck :data:detekt
```

### MVP功能模块
```bash
# Coach模块
./gradlew :features:coach:compileDebugKotlin

# Workout模块
./gradlew :features:workout:compileDebugKotlin
```

## 📊 验证结果解读

### ✅ 成功标准
- Domain模块编译通过
- Data模块编译通过
- 核心单元测试通过
- 代码质量检查通过
- Debug APK构建成功

### ❌ 失败处理
1. **编译失败**: 检查语法错误和依赖问题
2. **测试失败**: 查看测试报告，修复业务逻辑
3. **质量检查失败**: 运行自动格式化工具
4. **构建失败**: 检查配置和环境变量

## 🔧 常见问题解决

### Docker相关问题
```bash
# 检查Docker状态
docker info

# 启动Docker Desktop
# 在Windows开始菜单中找到Docker Desktop并启动
```

### ACT执行问题
```bash
# 检查ACT版本
..\act_Windows_x86_64\act.exe --version

# 清理Docker镜像（如果空间不足）
docker system prune -f

# 使用更小的Docker镜像
..\act_Windows_x86_64\act.exe --platform ubuntu-latest=catthehacker/ubuntu:act-latest
```

### Gradle构建问题
```bash
# 清理构建缓存
./gradlew clean

# 刷新依赖
./gradlew --refresh-dependencies

# 检查Java版本
java -version
```

## 📋 最佳实践

### 1. 开发流程
```bash
# 1. 修改代码后，先运行本地验证
.\scripts\domain-data-focus-test.ps1

# 2. 运行ACT验证
scripts\run-act-mvp.bat

# 3. 确认通过后，提交代码
git add .
git commit -m "feat: 添加新功能"

# 4. 推送并创建PR
git push origin feature-branch
```

### 2. 性能优化
- 使用`--dryrun`模式快速检查工作流程
- 定期清理Docker镜像和容器
- 使用Gradle缓存加速构建

### 3. 调试技巧
- 使用`--verbose`参数获取详细日志
- 检查`act-artifacts`目录中的构建产物
- 查看Docker容器日志

## 📈 监控指标

### 关键指标
- **Domain模块测试覆盖率**: 目标 ≥90%
- **Data模块测试覆盖率**: 目标 ≥80%
- **构建时间**: 目标 <10分钟
- **代码质量**: 无Ktlint/Detekt错误

### 报告位置
- 测试报告: `*/build/reports/tests/`
- 代码覆盖率: `*/build/reports/jacoco/`
- 代码质量: `*/build/reports/ktlint/`, `*/build/reports/detekt/`

## 🔗 相关文档

- [ACT官方文档](https://nektosact.com)
- [GitHub Actions文档](https://docs.github.com/en/actions)
- [GymBro CI/CD配置指南](.github/ci-config.md)
- [MVP变更总结](.github/MVP-CICD-CHANGES.md)

## 📞 支持

如有问题，请：
1. 检查本文档的常见问题部分
2. 查看ACT和Docker的官方文档
3. 在项目中创建Issue描述问题

---

**维护团队**: DevOps团队  
**最后更新**: 2025-01-28  
**版本**: MVP v1.0
