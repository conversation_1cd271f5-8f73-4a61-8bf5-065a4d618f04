package com.example.gymbro.data.repository.user

import com.example.gymbro.core.common.result.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.data.local.dao.user.UserProfileDao
import com.example.gymbro.data.local.entity.user.UserProfileEntity
import com.example.gymbro.data.network.api.UserProfileApiService
import com.example.gymbro.domain.profile.model.user.UserProfile
import com.example.gymbro.domain.profile.model.user.enums.FitnessLevel
import com.example.gymbro.domain.profile.model.user.enums.Gender
import com.example.gymbro.domain.profile.model.user.enums.WeightUnit
import com.example.gymbro.shared.models.network.ApiError
import com.example.gymbro.shared.models.network.NetworkResult
import com.example.gymbro.shared.models.user.UserProfileDto
import io.mockk.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertIs
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * UserProfileRepositoryImpl单元测试
 *
 * 验证本地优先策略和网络降级机制
 * 测试NetworkResult<T>集成的错误处理
 */
@OptIn(ExperimentalCoroutinesApi::class)
class UserProfileRepositoryImplTest {
    private lateinit var userProfileDao: UserProfileDao
    private lateinit var logger: Logger
    private lateinit var repository: UserProfileRepositoryImpl
    private lateinit var userProfileApiService: UserProfileApiService
    private val testDispatcher = StandardTestDispatcher()
    private val testUserId = "test-user-123"

    private val testUserProfile = UserProfile(
        userId = testUserId,
        displayName = "Test User",
        email = "<EMAIL>",
        gender = Gender.MALE,
        height = 175.0f,
        weight = 70.0f,
        fitnessLevel = com.example.gymbro.domain.profile.model.user.enums.FitnessLevel.INTERMEDIATE,
        fitnessGoals = emptyList(),
        workoutDays = emptyList(),
        avatarUrl = null,
    )

    @Before
    fun setup() {
        userProfileDao = mockk()
        userProfileApiService = mockk()
        logger = mockk(relaxed = true)
        repository =
            UserProfileRepositoryImpl(
                userProfileDao = userProfileDao,
                userProfileApiService = userProfileApiService,
                ioDispatcher = testDispatcher,
                logger = logger,
            )
    }

    @After
    fun tearDown() {
        clearAllMocks()
    }

    @Test
    fun `saveUserProfile should save successfully and return Success`() =
        runTest(testDispatcher) {
            // Given
            val userProfile = createTestUserProfile()
            val expectedInsertId = 1L

            coEvery { userProfileDao.insertOrUpdateUserProfile(any()) } returns expectedInsertId

            // When
            val result = repository.saveUserProfile(userProfile)

            // Then
            assertIs<ModernResult.Success<Unit>>(result)
            coVerify(exactly = 1) { userProfileDao.insertOrUpdateUserProfile(any()) }
            verify { logger.d("UserProfileRepositoryImpl", "开始保存用户资料: ${userProfile.userId}") }
            verify {
                logger.d(
                    "UserProfileRepositoryImpl",
                    "用户资料保存成功: ${userProfile.userId}, insertedId: $expectedInsertId",
                )
            }
        }

    @Test
    fun `saveUserProfile should handle database error and return Error`() =
        runTest(testDispatcher) {
            // Given
            val userProfile = createTestUserProfile()
            val exception = RuntimeException("Database error")

            coEvery { userProfileDao.insertOrUpdateUserProfile(any()) } throws exception

            // When
            val result = repository.saveUserProfile(userProfile)

            // Then
            assertIs<ModernResult.Error>(result)
            verify { logger.e(exception, "保存用户资料失败: ${userProfile.userId}") }
        }

    @Test
    fun `getUserProfile should return user profile when exists`() =
        runTest(testDispatcher) {
            // Given
            val userId = "test_user_123"
            val entity = createTestUserProfileEntity(userId)

            every { userProfileDao.getUserProfile(userId) } returns flowOf(entity)

            // When
            val result = repository.getUserProfile(userId).first()

            // Then
            assertIs<ModernResult.Success<UserProfile?>>(result)
            val profile = result.data
            assertEquals(userId, profile?.userId)
            assertEquals("Test User", profile?.displayName)
            assertEquals(Gender.MALE, profile?.gender)
            verify { logger.d("UserProfileRepositoryImpl", "成功获取用户资料: $userId") }
        }

    @Test
    fun `getUserProfile should return null when user does not exist`() =
        runTest(testDispatcher) {
            // Given
            val userId = "nonexistent_user"

            every { userProfileDao.getUserProfile(userId) } returns flowOf(null)

            // When
            val result = repository.getUserProfile(userId).first()

            // Then
            assertIs<ModernResult.Success<UserProfile?>>(result)
            assertNull(result.data)
            verify { logger.d("UserProfileRepositoryImpl", "用户资料不存在: $userId") }
        }

    @Test
    fun `getUserProfile should handle database error and return Error`() =
        runTest(testDispatcher) {
            // Given
            val userId = "test_user_123"
            val exception = RuntimeException("Database error")

            every { userProfileDao.getUserProfile(userId) } throws exception

            // When
            val result = repository.getUserProfile(userId).first()

            // Then
            assertIs<ModernResult.Error>(result)
            verify { logger.e(exception, "获取用户资料流失败: $userId") }
        }

    @Test
    fun `updateUserProfile - 本地保存成功，网络同步成功`() = runTest(testDispatcher) {
        // Given
        val entity = mockk<UserProfileEntity>()
        every { testUserProfile.toEntity() } returns entity
        coEvery { userProfileDao.insertOrUpdateUserProfile(entity) } returns 1L

        val dto = mockk<UserProfileDto>()
        every { testUserProfile.toDto() } returns dto
        coEvery { userProfileApiService.updateUserProfile(testUserId, dto) } returns
            NetworkResult.Success(dto)

        // When
        val result = repository.updateUserProfile(testUserId, testUserProfile)

        // Then
        assertTrue(result is ModernResult.Success)
        coVerify { userProfileDao.insertOrUpdateUserProfile(entity) }

        // 网络同步是异步的，需要推进协程调度器
        testScheduler.advanceUntilIdle()
        coVerify { userProfileApiService.updateUserProfile(testUserId, dto) }
    }

    @Test
    fun `updateUserProfile - 本地保存成功，网络同步失败（降级策略）`() = runTest(testDispatcher) {
        // Given
        val entity = mockk<UserProfileEntity>()
        every { testUserProfile.toEntity() } returns entity
        coEvery { userProfileDao.insertOrUpdateUserProfile(entity) } returns 1L

        val dto = mockk<UserProfileDto>()
        every { testUserProfile.toDto() } returns dto
        coEvery { userProfileApiService.updateUserProfile(testUserId, dto) } returns
            NetworkResult.Error(ApiError.Http(500, "服务器错误"))

        // When
        val result = repository.updateUserProfile(testUserId, testUserProfile)

        // Then - 本地成功，整体操作仍然成功（降级策略）
        assertTrue(result is ModernResult.Success)
        coVerify { userProfileDao.insertOrUpdateUserProfile(entity) }

        // 验证网络同步尝试
        testScheduler.advanceUntilIdle()
        coVerify { userProfileApiService.updateUserProfile(testUserId, dto) }
    }

    @Test
    fun `updateUserProfile - 本地保存失败，整体操作失败`() = runTest(testDispatcher) {
        // Given
        val entity = mockk<UserProfileEntity>()
        every { testUserProfile.toEntity() } returns entity
        coEvery { userProfileDao.insertOrUpdateUserProfile(entity) } throws RuntimeException("数据库错误")

        // When
        val result = repository.updateUserProfile(testUserId, testUserProfile)

        // Then
        assertTrue(result is ModernResult.Error)
        coVerify { userProfileDao.insertOrUpdateUserProfile(entity) }

        // 本地失败时不应该尝试网络同步
        testScheduler.advanceUntilIdle()
        coVerify(exactly = 0) { userProfileApiService.updateUserProfile(any(), any()) }
    }

    @Test
    fun `getUserProfile - 成功获取用户资料`() = runTest(testDispatcher) {
        // Given
        val entity = mockk<UserProfileEntity>()
        every { entity.toDomain() } returns testUserProfile
        coEvery { userProfileDao.getUserProfileSync(testUserId) } returns entity

        // When
        val result = repository.getUserProfile(testUserId)

        // Then
        assertTrue(result is ModernResult.Success)
        assertEquals(testUserProfile, result.data)
        coVerify { userProfileDao.getUserProfileSync(testUserId) }
    }

    @Test
    fun `getUserProfile - 用户资料不存在`() = runTest(testDispatcher) {
        // Given
        coEvery { userProfileDao.getUserProfileSync(testUserId) } returns null

        // When
        val result = repository.getUserProfile(testUserId)

        // Then
        assertTrue(result is ModernResult.Success)
        assertEquals(null, result.data)
        coVerify { userProfileDao.getUserProfileSync(testUserId) }
    }

    @Test
    fun `observeUserProfile - 成功观察用户资料变化`() = runTest(testDispatcher) {
        // Given
        val entity = mockk<UserProfileEntity>()
        every { entity.toDomain() } returns testUserProfile
        every { userProfileDao.getUserProfile(testUserId) } returns flowOf(entity)

        // When
        val flow = repository.observeUserProfile(testUserId)

        // Then
        flow.collect { result ->
            assertTrue(result is ModernResult.Success)
            assertEquals(testUserProfile, result.data)
        }
        verify { userProfileDao.getUserProfile(testUserId) }
    }

    @Test
    fun `NetworkResult错误处理 - HTTP错误`() = runTest(testDispatcher) {
        // Given
        val entity = mockk<UserProfileEntity>()
        every { testUserProfile.toEntity() } returns entity
        coEvery { userProfileDao.insertOrUpdateUserProfile(entity) } returns 1L

        val dto = mockk<UserProfileDto>()
        every { testUserProfile.toDto() } returns dto
        coEvery { userProfileApiService.updateUserProfile(testUserId, dto) } returns
            NetworkResult.Error(ApiError.Http(404, "用户未找到"))

        // When
        val result = repository.updateUserProfile(testUserId, testUserProfile)

        // Then - 本地成功，网络错误不影响整体结果
        assertTrue(result is ModernResult.Success)

        testScheduler.advanceUntilIdle()
        coVerify { userProfileApiService.updateUserProfile(testUserId, dto) }
    }

    @Test
    fun `NetworkResult错误处理 - 网络离线`() = runTest(testDispatcher) {
        // Given
        val entity = mockk<UserProfileEntity>()
        every { testUserProfile.toEntity() } returns entity
        coEvery { userProfileDao.insertOrUpdateUserProfile(entity) } returns 1L

        val dto = mockk<UserProfileDto>()
        every { testUserProfile.toDto() } returns dto
        coEvery { userProfileApiService.updateUserProfile(testUserId, dto) } returns
            NetworkResult.Error(ApiError.Offline)

        // When
        val result = repository.updateUserProfile(testUserId, testUserProfile)

        // Then - 离线状态下本地优先策略仍然有效
        assertTrue(result is ModernResult.Success)

        testScheduler.advanceUntilIdle()
        coVerify { userProfileApiService.updateUserProfile(testUserId, dto) }
    }

    private fun createTestUserProfile(): UserProfile =
        UserProfile(
            userId = "test_user_123",
            username = "testuser",
            displayName = "Test User",
            email = "<EMAIL>",
            phoneNumber = "+1234567890",
            avatarUrl = "https://example.com/avatar.jpg",
            bio = "Test bio",
            gender = Gender.MALE,
            height = 175.0f,
            weight = 70.0f,
            weightUnit = WeightUnit.KG,
            fitnessLevel = FitnessLevel.INTERMEDIATE,
            fitnessGoals = emptyList(),
            joinDate = null,
            lastActive = null,
            workoutDays = emptyList(),
            allowPartnerMatching = true,
            totalActivityCount = 10,
            weeklyActiveMinutes = 300,
            likesReceived = 5,
            isSubscribed = false,
            isAnonymous = false,
            socialLinks = emptyMap(),
            isMpOwner = false,
        )

    private fun createTestUserProfileEntity(userId: String): UserProfileEntity =
        UserProfileEntity(
            userId = userId,
            username = "testuser",
            displayName = "Test User",
            email = "<EMAIL>",
            phoneNumber = "+1234567890",
            profileImageUrl = "https://example.com/avatar.jpg",
            bio = "Test bio",
            gender = "MALE",
            height = 175.0f,
            heightUnit = "CM",
            weight = 70.0f,
            weightUnit = "KG",
            fitnessLevel = 1, // INTERMEDIATE
            fitnessGoals = emptyList(),
            workoutDays = emptyList(),
            allowPartnerMatching = true,
            totalWorkoutCount = 10,
            weeklyActiveMinutes = 300,
            likesReceived = 5,
            isAnonymous = false,
            hasValidSubscription = false,
            lastUpdated = System.currentTimeMillis(),
            createdAt = System.currentTimeMillis(),
        )
}
