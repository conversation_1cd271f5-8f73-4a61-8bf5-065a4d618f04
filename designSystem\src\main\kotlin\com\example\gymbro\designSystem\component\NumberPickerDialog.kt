package com.example.gymbro.designSystem.component

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import kotlinx.coroutines.launch

/**
 * 数字选择器对话框
 *
 * 用于训练数据输入的专用数字选择器：
 * - 重量选择（0.5kg 递增，支持小数）
 * - 次数选择（整数，1-50范围）
 * - RPE选择（1-10整数）
 *
 * 特性：
 * - 滚轮式选择体验
 * - 自动居中对齐
 * - haptic反馈
 * - 单位显示
 */
@Composable
fun NumberPickerDialog(
    title: String,
    value: Float,
    unit: String = "",
    isInteger: Boolean = false,
    range: ClosedFloatingPointRange<Float> = 0f..200f,
    step: Float = if (isInteger) 1f else 0.5f,
    onValueChange: (Float) -> Unit,
    onDismiss: () -> Unit,
) {
    val values = remember(range, step, isInteger) {
        generateSequence(range.start) { current ->
            val next = current + step
            if (next <= range.endInclusive) next else null
        }.toList()
    }

    val initialIndex = remember(value, values) {
        values.indexOfFirst {
            if (isInteger) {
                it.toInt() == value.toInt()
            } else {
                kotlin.math.abs(it - value) < 0.01f
            }
        }.coerceAtLeast(0)
    }

    var selectedIndex by remember { mutableIntStateOf(initialIndex) }
    val listState = rememberLazyListState(initialFirstVisibleItemIndex = initialIndex)
    val coroutineScope = rememberCoroutineScope()

    // 自动滚动到选中项
    LaunchedEffect(selectedIndex) {
        coroutineScope.launch {
            listState.animateScrollToItem(selectedIndex)
        }
    }

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
        ),
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight(),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface,
            ),
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                // 标题
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface,
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 当前值显示
                Text(
                    text = "${formatValue(values[selectedIndex], isInteger)} $unit",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary,
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 数字选择器
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp),
                ) {
                    LazyColumn(
                        state = listState,
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(vertical = 80.dp),
                        verticalArrangement = Arrangement.spacedBy(4.dp),
                    ) {
                        items(values.size) { index ->
                            val isSelected = index == selectedIndex

                            Text(
                                text = formatValue(values[index], isInteger),
                                style = if (isSelected) {
                                    MaterialTheme.typography.titleLarge
                                } else {
                                    MaterialTheme.typography.bodyLarge
                                },
                                fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                                color = if (isSelected) {
                                    MaterialTheme.colorScheme.primary
                                } else {
                                    MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                                },
                                textAlign = TextAlign.Center,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .selectable(
                                        selected = isSelected,
                                        onClick = {
                                            selectedIndex = index
                                            onValueChange(values[index])
                                        },
                                    )
                                    .padding(vertical = 8.dp),
                            )
                        }
                    }

                    // 选择指示器
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(2.dp)
                            .align(Alignment.Center)
                            .padding(horizontal = 32.dp),
                    ) {
                        Divider(
                            color = MaterialTheme.colorScheme.primary,
                            thickness = 2.dp,
                        )
                    }

                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(2.dp)
                            .align(Alignment.Center)
                            .offset(y = 40.dp)
                            .padding(horizontal = 32.dp),
                    ) {
                        Divider(
                            color = MaterialTheme.colorScheme.primary,
                            thickness = 2.dp,
                        )
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                // 按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                ) {
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                    ) {
                        Text("取消")
                    }

                    Button(
                        onClick = {
                            onValueChange(values[selectedIndex])
                            onDismiss()
                        },
                        modifier = Modifier.weight(1f),
                    ) {
                        Text("确定")
                    }
                }
            }
        }
    }
}

/**
 * 格式化数值显示
 */
private fun formatValue(value: Float, isInteger: Boolean): String {
    return if (isInteger) {
        value.toInt().toString()
    } else {
        if (value == value.toInt().toFloat()) {
            value.toInt().toString()
        } else {
            "%.1f".format(value)
        }
    }
}

// 预览组件
@GymBroPreview
@Composable
private fun NumberPickerDialogPreview() {
    GymBroTheme {
        NumberPickerDialog(
            title = "设置重量",
            value = 20.5f,
            unit = "kg",
            isInteger = false,
            range = 0f..100f,
            step = 0.5f,
            onValueChange = {},
            onDismiss = {},
        )
    }
}

@GymBroPreview
@Composable
private fun NumberPickerDialogRpePreview() {
    GymBroTheme {
        NumberPickerDialog(
            title = "设置RPE",
            value = 7f,
            unit = "RPE",
            isInteger = true,
            range = 1f..10f,
            step = 1f,
            onValueChange = {},
            onDismiss = {},
        )
    }
}
