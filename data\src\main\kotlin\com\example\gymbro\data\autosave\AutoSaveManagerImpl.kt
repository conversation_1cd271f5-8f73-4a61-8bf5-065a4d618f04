package com.example.gymbro.data.autosave

import com.example.gymbro.core.autosave.AutoSaveManager
import com.example.gymbro.core.autosave.config.AutoSaveConfig
import com.example.gymbro.core.autosave.state.AutoSaveState
import com.example.gymbro.core.logging.Logger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * AutoSaveManager的具体实现
 *
 * 🎯 功能特性：
 * - 实现core模块定义的AutoSaveManager接口
 * - 支持多种保存策略（即时、定时、防抖）
 * - 支持多种存储后端（数据库、缓存、文件）
 * - 统一的缓存恢复机制
 * - 完整的生命周期管理
 * - MVI架构集成支持
 *
 * @param T 要保存的数据类型
 * @param config 自动保存配置
 * @param scope 协程作用域
 * @param logger 日志记录器
 */
class AutoSaveManagerImpl<T : Any>(
    private val config: AutoSaveConfig<T>,
    private val scope: CoroutineScope,
    private val logger: Logger,
) : AutoSaveManager<T> {
    private val _state = MutableStateFlow(AutoSaveState<T>())
    override val state: StateFlow<AutoSaveState<T>> = _state.asStateFlow()

    private var currentData: T? = null
    private var strategyJob: Job? = null
    private var isStarted = false
    private var isPaused = false

    override fun start(initialData: T) {
        if (isStarted) {
            logger.w("AutoSaveManagerImpl", "AutoSave已经启动，忽略重复启动: ${config.id}")
            return
        }

        logger.d("AutoSaveManagerImpl", "启动自动保存: ${config.id}")

        currentData = initialData
        isStarted = true
        isPaused = false

        updateState { it.copy(isActive = true, lastUpdateTime = System.currentTimeMillis()) }

        // 启动保存策略
        startStrategy()

        // 尝试恢复缓存数据
        if (config.enableRecovery) {
            scope.launch {
                tryRestoreFromCache()
            }
        }
    }

    override fun update(newData: T) {
        if (!isStarted) {
            logger.w("AutoSaveManagerImpl", "AutoSave未启动，忽略更新: ${config.id}")
            return
        }

        if (isPaused) {
            logger.d("AutoSaveManagerImpl", "AutoSave已暂停，忽略更新: ${config.id}")
            return
        }

        val oldData = currentData
        currentData = newData

        updateState {
            it.copy(
                hasUnsavedChanges = true,
                lastUpdateTime = System.currentTimeMillis(),
            )
        }

        // 通知策略数据已变更
        config.strategy.onDataChanged(oldData, newData)

        logger.d("AutoSaveManagerImpl", "数据已更新: ${config.id}")
    }

    override suspend fun saveNow(): Result<Unit> {
        if (!isStarted) {
            return Result.failure(IllegalStateException("AutoSave未启动"))
        }

        val data = currentData ?: return Result.failure(IllegalStateException("没有数据可保存"))

        return try {
            updateState { it.copy(isSaving = true) }

            config.storage.save(config.id, data)

            val currentTime = System.currentTimeMillis()
            updateState {
                it.copy(
                    isSaving = false,
                    hasUnsavedChanges = false,
                    lastSaveTime = currentTime,
                    saveCount = it.saveCount + 1,
                    error = null,
                    retryCount = 0,
                )
            }

            logger.d("AutoSaveManagerImpl", "立即保存成功: ${config.id}")
            Result.success(Unit)
        } catch (e: Exception) {
            updateState {
                it.copy(
                    isSaving = false,
                    error = e,
                    retryCount = it.retryCount + 1,
                )
            }

            logger.e(e, "立即保存失败: ${config.id}")
            Result.failure(e)
        }
    }

    override fun stop() {
        if (!isStarted) {
            return
        }

        logger.d("AutoSaveManagerImpl", "停止自动保存: ${config.id}")

        isStarted = false
        isPaused = false

        // 停止策略
        config.strategy.stop()
        strategyJob?.cancel()
        strategyJob = null

        updateState { AutoSaveState() }

        currentData = null
    }

    override fun restoreFromCache() {
        if (!config.enableRecovery) {
            logger.w("AutoSaveManagerImpl", "缓存恢复未启用: ${config.id}")
            return
        }

        scope.launch {
            tryRestoreFromCache()
        }
    }

    override fun discardCache() {
        scope.launch {
            try {
                config.storage.clear(config.id)
                updateState { it.copy(cachedVersion = null, showRecoveryDialog = false) }
                logger.d("AutoSaveManagerImpl", "缓存已丢弃: ${config.id}")
            } catch (e: Exception) {
                logger.e(e, "丢弃缓存失败: ${config.id}")
            }
        }
    }

    override suspend fun clear() {
        try {
            config.storage.clear(config.id)
            updateState { AutoSaveState() }
            currentData = null
            logger.d("AutoSaveManagerImpl", "数据已清除: ${config.id}")
        } catch (e: Exception) {
            logger.e(e, "清除数据失败: ${config.id}")
            throw e
        }
    }

    override fun pause() {
        if (!isStarted) {
            return
        }

        isPaused = true
        logger.d("AutoSaveManagerImpl", "自动保存已暂停: ${config.id}")
    }

    override fun resume() {
        if (!isStarted || !isPaused) {
            return
        }

        isPaused = false
        logger.d("AutoSaveManagerImpl", "自动保存已恢复: ${config.id}")
    }

    override fun hasUnsavedChanges(): Boolean = _state.value.hasUnsavedChanges

    override fun getLastSaveTime(): Long = _state.value.lastSaveTime

    override fun getSaveCount(): Int = _state.value.saveCount

    /**
     * 启动保存策略
     */
    private fun startStrategy() {
        config.strategy.start(
            scope = scope,
            onSave = {
                if (isStarted && !isPaused && hasUnsavedChanges()) {
                    saveNow()
                } else {
                    Result.success(Unit)
                }
            },
            onError = { error ->
                updateState {
                    it.copy(
                        error = error,
                        retryCount = it.retryCount + 1,
                    )
                }
                logger.e(error, "自动保存策略错误: ${config.id}")
            },
        )
    }

    /**
     * 尝试从缓存恢复数据
     */
    private suspend fun tryRestoreFromCache() {
        try {
            val cachedData = config.storage.restore(config.id)
            if (cachedData != null) {
                updateState {
                    it.copy(
                        cachedVersion = cachedData,
                        showRecoveryDialog = true,
                    )
                }
                logger.d("AutoSaveManagerImpl", "发现缓存数据: ${config.id}")
            }
        } catch (e: Exception) {
            logger.e(e, "恢复缓存失败: ${config.id}")
        }
    }

    /**
     * 更新状态
     */
    private fun updateState(update: (AutoSaveState<T>) -> AutoSaveState<T>) {
        _state.value = update(_state.value)
    }

    companion object {
        /**
         * 创建AutoSaveManager实例
         */
        fun <T : Any> create(
            config: AutoSaveConfig<T>,
            scope: CoroutineScope,
            logger: Logger,
        ): AutoSaveManager<T> = AutoSaveManagerImpl(config, scope, logger)
    }
}
