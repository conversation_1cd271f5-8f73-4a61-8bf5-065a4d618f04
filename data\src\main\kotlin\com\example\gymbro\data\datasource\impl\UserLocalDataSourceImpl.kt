package com.example.gymbro.data.datasource.impl

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.local.dao.user.UserCacheEntityDao
import com.example.gymbro.data.local.datastore.UserPreferencesDataStore
import com.example.gymbro.data.local.entity.user.UserCacheEntity
import com.example.gymbro.domain.profile.model.user.settings.UserSettings
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 用户本地数据源实现类
 */
@Singleton
class UserLocalDataSourceImpl
@Inject
constructor(
    private val userCacheEntityDao: UserCacheEntityDao,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    private val logger: Logger,
    private val userPreferencesDataStore: UserPreferencesDataStore,
) {
    /**
     * 根据ID获取用户
     */
    suspend fun getUserById(userId: String): ModernResult<UserCacheEntity?> =
        withContext(ioDispatcher) {
            try {
                val user = userCacheEntityDao.getUserCacheById(userId)
                ModernResult.Success(user)
            } catch (e: Exception) {
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "UserLocalDataSourceImpl.getUserById",
                        message = UiText.DynamicString("获取用户失败: 用户ID=$userId"),
                        entityType = "UserCacheEntity",
                        cause = e,
                        metadataMap = mapOf("userId" to userId),
                    ),
                )
            }
        }

    /**
     * 获取所有用户
     */
    fun getUsers(): Flow<List<UserCacheEntity>> = userCacheEntityDao.observeAllUserCaches()

    /**
     * Deletes a user.
     */
    suspend fun deleteUser(userId: String): ModernResult<Unit> =
        withContext(ioDispatcher) {
            try {
                userCacheEntityDao.deleteUserCacheById(userId)
                // Also clear preferences for the deleted user if they are user-specific
                // Assuming preferences are currently global or keyed by something else.
                // If keyed by userId, then: userPreferencesDataStore.clearUserSettings(userId)
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "UserLocalDataSourceImpl.deleteUser",
                        message = UiText.DynamicString("删除用户失败: 用户ID=$userId"),
                        entityType = "UserCacheEntity",
                        cause = e,
                        metadataMap = mapOf("userId" to userId),
                    ),
                )
            }
        }

    suspend fun getUserSettings(userId: String): ModernResult<UserSettings?> =
        withContext(ioDispatcher) {
            try {
                // 获取用户设置，当前实现是全局设置
                val settings = userPreferencesDataStore.userPreferencesFlow.firstOrNull()
                ModernResult.Success(settings)
            } catch (e: Exception) {
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "UserLocalDataSourceImpl.getUserSettings",
                        message = UiText.DynamicString("获取用户设置失败"),
                        entityType = "UserSettings",
                        cause = e,
                        metadataMap =
                        mapOf(
                            "userId" to userId,
                            "source" to "DataStore",
                        ),
                    ),
                )
            }
        }

    suspend fun saveUserSettings(
        userId: String,
        settings: UserSettings,
    ): ModernResult<Unit> =
        withContext(ioDispatcher) {
            try {
                // 更新用户设置，当前实现是全局设置
                userPreferencesDataStore.updateUserSettings(settings)
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                ModernResult.Error(
                    DataErrors.DataError.access(
                        operationName = "UserLocalDataSourceImpl.saveUserSettings",
                        message = UiText.DynamicString("保存用户设置失败"),
                        entityType = "UserSettings",
                        cause = e,
                        metadataMap =
                        mapOf(
                            "userId" to userId,
                            "operationAttempted" to "update",
                            "source" to "DataStore",
                        ),
                    ),
                )
            }
        }
}
