Project Path: src

Source Tree:

```txt
src
└── main
    └── kotlin
        └── com
            └── example
                └── gymbro
                    └── features
                        └── thinkingbox
                            ├── ThinkingBoxExports.kt
                            ├── domain
                            │   ├── bus
                            │   │   └── SemanticEventBus.kt
                            │   ├── guardrail
                            │   │   └── ThinkingMLGuardrail.kt
                            │   ├── interfaces
                            │   │   └── ThinkingBoxInterfaces.kt
                            │   ├── logger
                            │   │   └── RawThinkingLogger.kt
                            │   ├── mapper
                            │   │   └── DomainMapper.kt
                            │   ├── model
                            │   │   ├── ParseState.kt
                            │   │   ├── TagContext.kt
                            │   │   ├── ThinkingContent.kt
                            │   │   └── events
                            │   │       ├── SemanticEvent.kt
                            │   │       └── ThinkingEvent.kt
                            │   ├── parser
                            │   │   ├── StreamingThinkingMLParser.kt
                            │   │   └── XmlStreamScanner.kt
                            │   └── processor
                            │       ├── RawChunkProcessor.kt
                            │       └── RawChunkProcessorDemo.kt
                            ├── history
                            │   └── HistoryMapper.kt
                            ├── internal
                            │   ├── adapter
                            │   │   └── UiStateAdapter.kt
                            │   ├── di
                            │   │   └── ThinkingBoxModule.kt
                            │   ├── history
                            │   │   └── HistorySaver.kt
                            │   ├── logging
                            │   │   └── TBLog.kt
                            │   ├── memory
                            │   │   └── MemoryWriter.kt
                            │   ├── metrics
                            │   │   └── ThinkingBoxMetrics.kt
                            │   ├── mvi
                            │   │   ├── ThinkingBoxFacadeImpl.kt
                            │   │   ├── ThinkingBoxInstance.kt
                            │   │   └── ThinkingBoxManager.kt
                            │   └── presentation
                            │       ├── model
                            │       │   └── RenderableNode.kt
                            │       ├── strings
                            │       │   └── ThinkingBoxStrings.kt
                            │       └── ui
                            │           ├── AIThinkingCard.kt
                            │           ├── AnimationEngine.kt
                            │           ├── AppendingTypewriterText.kt
                            │           ├── FinalRichTextRenderer.kt
                            │           ├── MarkdownRenderer.kt
                            │           ├── MermaidRenderer.kt
                            │           ├── RichTextUtils.kt
                            │           ├── SummaryCard.kt
                            │           ├── ThinkingBoxContainer.kt
                            │           ├── ThinkingHeader.kt
                            │           └── ThinkingStageCard.kt
                            └── logging
                                └── ThinkingBoxLogTree.kt

```

`main\kotlin\com\example\gymbro\features\thinkingbox\ThinkingBoxExports.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox
   2 | 
   3 | /**
   4 |  * ThinkingBox 模块导出接口 (627规范统一版本)
   5 |  *
   6 |  * 🔥 严格遵循627architect.md和627规范统一.md要求：
   7 |  * - 唯一出口：所有对外接口都通过此文件导出
   8 |  * - 单轴滚动架构：只在外层LazyColumn滚动
   9 |  * - 模块边界清晰：只暴露必要的公共接口
  10 |  * - UI层只依赖UiState，完全不可见Event
  11 |  *
  12 |  * 模块职责：
  13 |  * - 接收来自coach模块的AI token流
  14 |  * - 解析<thinking>、<title>、<final>等思考内容标签
  15 |  * - 提供思考过程的UI展示和动画效果
  16 |  * - 支持Mermaid图表渲染和TODO复选框
  17 |  *
  18 |  * 架构原则：
  19 |  * - ThinkingBox专注于思考内容的解析和UI呈现
  20 |  * - 所有AI相关的网络和对话功能由coach模块提供
  21 |  * - 通过Flow<String> aiTokenFlow接收数据，保持模块边界清晰
  22 |  * - 严格遵循627规范统一.md的单轴滚动架构
  23 |  */
  24 | 
  25 | import androidx.compose.runtime.Composable
  26 | import androidx.compose.ui.Modifier
  27 | import com.example.gymbro.core.ai.tokenizer.TokenizerService
  28 | import com.example.gymbro.features.thinkingbox.domain.interfaces.UiState
  29 | import com.example.gymbro.features.thinkingbox.internal.presentation.ui.AnimationEngine
  30 | 
  31 | /**
  32 |  * ThinkingBox - v8 统一架构主要导出组件
  33 |  *
  34 |  * 🔥 v8 核心特性：
  35 |  * - **统一渲染逻辑** - 移除 PreThinkingCard 直接调用，所有内容通过 ThinkingStageCard 渲染
  36 |  * - **isPreThink 参数控制** - 根据 `phase.id == "perthink"` 自动判断样式
  37 |  * - **简化 AIThinkingCard** - 移除复杂的组件切换逻辑
  38 |  * - **统一 Phase 机制** - `<think>` → `phase id="perthink"` 完全统一的处理逻辑
  39 |  *
  40 |  * 🔥 性能优化：
  41 |  * - **perthink 无速度限制** - `animationDelay = 0L`，能多快就多快
  42 |  * - **立即显示** - 标题和内容都无动画延迟
  43 |  * - **保持正式思考体验** - 30ms 打字机效果不变
  44 |  *
  45 |  * @param uiState v8 统一的UI状态
  46 |  * @param modifier 修饰符
  47 |  * @param onToggleSummary 摘要切换回调
  48 |  */
  49 | @Composable
  50 | fun ThinkingBox(
  51 |     uiState: UiState,
  52 |     modifier: Modifier = Modifier,
  53 |     onToggleSummary: () -> Unit = {},
  54 | ) {
  55 |     // 🔥 按照71ThinkingBox-UI.md文档的标准实现
  56 |     // 直接使用AIThinkingCard，它已经包含了完整的UI结构
  57 |     com.example.gymbro.features.thinkingbox.internal.presentation.ui.AIThinkingCard(
  58 |         uiState = uiState,
  59 |         messageId = "thinkingbox-default", // 默认messageId
  60 |         modifier = modifier,
  61 |         onEventSend = { event ->
  62 |             // 处理事件，特别是ToggleSummary
  63 |             if (event is com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent.ToggleSummary) {
  64 |                 onToggleSummary()
  65 |             }
  66 |         },
  67 |     )
  68 | }
  69 | 
  70 | /**
  71 |  * 🔥 Step 2重构：判断AIThinkingCard是否应该显示
  72 |  *
  73 |  * 基于11步状态转换序列，某些步骤不需要显示ThinkingCard
  74 |  * 供Coach模块在LazyColumn中进行条件渲染使用
  75 |  */
  76 | fun shouldShowAIThinkingCard(uiState: UiState): Boolean =
  77 |     com.example.gymbro.features.thinkingbox.internal.presentation.ui
  78 |         .shouldShowAIThinkingCard(uiState)
  79 | 
  80 | /**
  81 |  * AIThinkingCard - v8 统一架构Coach模块集成接口
  82 |  *
  83 |  * 🔥 v8 统一架构特性：
  84 |  * - **统一渲染逻辑** - 移除 PreThinkingCard 直接调用，所有内容通过 ThinkingStageCard 渲染
  85 |  * - **isPreThink 参数控制** - 根据 `phase.id == "perthink"` 自动判断样式
  86 |  * - **简化 AIThinkingCard** - 移除复杂的组件切换逻辑
  87 |  * - **统一 Phase 机制** - `<think>` → `phase id="perthink"` 完全统一的处理逻辑
  88 |  *
  89 |  * 🔥 性能优化：
  90 |  * - **perthink 无速度限制** - `animationDelay = 0L`，能多快就多快
  91 |  * - **立即显示** - 标题和内容都无动画延迟
  92 |  * - **保持正式思考体验** - 30ms 打字机效果不变
  93 |  *
  94 |  * @param uiState v8 统一的UI状态
  95 |  * @param messageId 消息ID，用于Key策略和生命周期管理
  96 |  * @param modifier 修饰符
  97 |  * @param onEventSend 事件发送回调
  98 |  */
  99 | @Composable
 100 | fun AIThinkingCard(
 101 |     uiState: UiState,
 102 |     messageId: String,
 103 |     modifier: Modifier = Modifier,
 104 |     onEventSend: ((com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent) -> Unit)? = null,
 105 | ) {
 106 |     // v8 统一架构：直接调用内部实现
 107 |     com.example.gymbro.features.thinkingbox.internal.presentation.ui.AIThinkingCard(
 108 |         uiState = uiState,
 109 |         messageId = messageId,
 110 |         modifier = modifier,
 111 |         onEventSend = onEventSend,
 112 |     )
 113 | }
 114 | 
 115 | /**
 116 |  * v8 统一架构组件导出
 117 |  *
 118 |  * 🔥 v8 核心组件：
 119 |  * - ThinkingStageCard：v8 统一渲染器，支持 isPreThink 智能样式控制
 120 |  * - 所有内容通过此组件统一渲染，消除复杂切换逻辑
 121 |  * - 注意：ThinkingStageCard 通过内部路径直接访问，不使用 typealias
 122 |  */
 123 | 
 124 | /**
 125 |  * v8 统一架构类型别名导出
 126 |  */
 127 | typealias ThinkingBoxParser = com.example.gymbro.features.thinkingbox.domain.parser.StreamingThinkingMLParser
 128 | typealias ThinkingBoxUiState = UiState
 129 | 
 130 | /**
 131 |  * v8 统一架构接口导出
 132 |  */
 133 | typealias ThinkingBoxInspector = com.example.gymbro.features.thinkingbox.domain.interfaces.ThinkingBoxInspector
 134 | typealias ThinkingBoxFacade = com.example.gymbro.features.thinkingbox.domain.interfaces.ThinkingBoxFacade
 135 | 
 136 | /**
 137 |  * SimpleSummaryText - 简单摘要文本组件导出
 138 |  *
 139 |  * 🔥 【模块边界修复】正确导出SimpleSummaryText组件
 140 |  * 按照总结card.md文档要求，折叠状态只显示简单的"已思考X秒"文本
 141 |  * 没有卡片背景或复杂样式，符合参考图片的简洁设计
 142 |  *
 143 |  * @param elapsed 思考耗时
 144 |  * @param onClick 点击回调，用于展开SummaryCard
 145 |  * @param modifier 修饰符
 146 |  */
 147 | @Composable
 148 | fun SimpleSummaryText(
 149 |     elapsed: kotlin.time.Duration,
 150 |     onClick: () -> Unit,
 151 |     modifier: Modifier = Modifier,
 152 | ) {
 153 |     com.example.gymbro.features.thinkingbox.internal.presentation.ui.SimpleSummaryText(
 154 |         elapsed = elapsed,
 155 |         onClick = onClick,
 156 |         modifier = modifier,
 157 |     )
 158 | }
 159 | 
 160 | /**
 161 |  * FinalRichTextRenderer - v8 富文本渲染器导出
 162 |  *
 163 |  * 🔥 v8 架构：富文本作为独立组件渲染，与思考过程分离
 164 |  * 🔥 新增：支持Token计算器，显示在左下角
 165 |  * 🔥 【超时机制优化】新增打字机效果支持，与phase渲染体验保持一致
 166 |  *
 167 |  * @param markdown 富文本markdown内容
 168 |  * @param tokenizerService 可选的Token计算服务，用于精确计算Token数量
 169 |  * @param enableTypewriter 是否启用打字机效果（默认true）
 170 |  * @param typingSpeed 打字机速度（毫秒/字符）
 171 |  * @param onRenderingComplete 渲染完成回调
 172 |  * @param modifier 修饰符
 173 |  */
 174 | @Composable
 175 | fun FinalRichTextRenderer(
 176 |     markdown: String,
 177 |     tokenizerService: TokenizerService? = null,
 178 |     enableTypewriter: Boolean = true,
 179 |     typingSpeed: Long = AnimationEngine.Durations.TYPEWRITER_CHAR_DELAY,
 180 |     onRenderingComplete: (() -> Unit)? = null,
 181 |     modifier: Modifier = Modifier,
 182 | ) {
 183 |     com.example.gymbro.features.thinkingbox.internal.presentation.ui.FinalRichTextRenderer(
 184 |         markdown = markdown,
 185 |         tokenizerService = tokenizerService,
 186 |         enableTypewriter = enableTypewriter,
 187 |         typingSpeed = typingSpeed,
 188 |         onRenderingComplete = onRenderingComplete,
 189 |         modifier = modifier,
 190 |     )
 191 | }
 192 | 
 193 | /**
 194 |  * 🔥 v8 统一架构总结（基于TREE_v8.md和README_v8.md）：
 195 |  *
 196 |  * **v8 核心特性**：
 197 |  * - **统一渲染逻辑** - 移除 PreThinkingCard 直接调用，所有内容通过 ThinkingStageCard 渲染
 198 |  * - **isPreThink 参数控制** - 根据 `phase.id == "perthink"` 自动判断样式
 199 |  * - **简化 AIThinkingCard** - 移除复杂的组件切换逻辑
 200 |  * - **统一 Phase 机制** - `<think>` → `phase id="perthink"` 完全统一的处理逻辑
 201 |  *
 202 |  * **性能优化**：
 203 |  * - **perthink 无速度限制** - `animationDelay = 0L`，能多快就多快
 204 |  * - **立即显示** - 标题和内容都无动画延迟
 205 |  * - **保持正式思考体验** - 30ms 打字机效果不变
 206 |  *
 207 |  * **架构简化**：
 208 |  * - 代码量减少约50%（从复杂的多组件切换简化为单一统一渲染）
 209 |  * - 维护性提升（统一的渲染逻辑，更容易理解和维护）
 210 |  * - 性能优化（减少不必要的组件渲染和状态管理）
 211 |  */

```

`main\kotlin\com\example\gymbro\features\thinkingbox\domain\bus\SemanticEventBus.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.domain.bus
   2 | 
   3 | import com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent
   4 | import kotlinx.coroutines.channels.Channel
   5 | import kotlinx.coroutines.flow.Flow
   6 | import kotlinx.coroutines.flow.receiveAsFlow
   7 | import timber.log.Timber
   8 | import java.util.concurrent.atomic.AtomicInteger
   9 | 
  10 | /**
  11 |  * SemanticEventBus - 语义事件总线
  12 |  * 
  13 |  * 🔥 修复重复事件问题：单消费者守卫机制
  14 |  * - 防止多个组件同时订阅SemanticEventBus
  15 |  * - 使用AtomicInteger确保只有一个消费者
  16 |  * - 编译期守卫：receiveGuarded()方法强制单消费者模式
  17 |  */
  18 | object SemanticEventBus {
  19 |     
  20 |     // 🔥 内部Channel实例
  21 |     private val _channel = Channel<SemanticEvent>(Channel.UNLIMITED)
  22 |     
  23 |     // 🔥 单消费者守卫：消费者计数器
  24 |     private val consumerCnt = AtomicInteger(0)
  25 |     
  26 |     // 🔥 统一日志标签
  27 |     private const val TAG = "SEMANTIC-BUS"
  28 |     
  29 |     /**
  30 |      * 对外暴露的Flow接口，供DomainMapper层消费
  31 |      * 🔥 注意：不要直接使用这个Flow，使用receiveGuarded()
  32 |      */
  33 |     private val _flow: Flow<SemanticEvent> = _channel.receiveAsFlow()
  34 |     
  35 |     /**
  36 |      * 发送SemanticEvent到总线
  37 |      * 🔥 只有StreamingThinkingMLParser调用此方法
  38 |      */
  39 |     fun send(event: SemanticEvent) {
  40 |         val result = _channel.trySend(event)
  41 |         if (result.isFailure) {
  42 |             Timber.tag(TAG).e("❌ SemanticEvent发送失败: ${result.exceptionOrNull()}")
  43 |         }
  44 |     }
  45 |     
  46 |     /**
  47 |      * 🔥 单消费者守卫：受保护的接收方法
  48 |      * 确保只有一个消费者订阅SemanticEventBus
  49 |      */
  50 |     fun receiveGuarded(): Flow<SemanticEvent> {
  51 |         val currentConsumers = consumerCnt.incrementAndGet()
  52 |         if (currentConsumers > 1) {
  53 |             consumerCnt.decrementAndGet()
  54 |             error("MULTI-CONSUMER! 检测到多个SemanticEventBus消费者: $currentConsumers")
  55 |         }
  56 |         
  57 |         Timber.tag(TAG).i("🔍 SemanticEventBus单消费者启动，消费者计数: $currentConsumers")
  58 |         
  59 |         return _flow
  60 |     }
  61 |     
  62 |     /**
  63 |      * 标记流结束
  64 |      */
  65 |     fun endStream() {
  66 |         _channel.close()
  67 |         Timber.tag(TAG).i("🔚 SemanticEvent流已结束")
  68 |     }
  69 |     
  70 |     /**
  71 |      * 获取当前消费者数量（用于测试和调试）
  72 |      */
  73 |     fun getConsumerCount(): Int = consumerCnt.get()
  74 |     
  75 |     /**
  76 |      * 重置消费者计数（用于测试）
  77 |      */
  78 |     fun resetConsumerCount() {
  79 |         consumerCnt.set(0)
  80 |         Timber.tag(TAG).d("🔄 消费者计数已重置")
  81 |     }
  82 | }
  83 | 
  84 | /**
  85 |  * 🔥 编译期守卫：禁止直接collect SemanticEventBus
  86 |  */
  87 | @Deprecated(
  88 |     message = "Use SemanticEventBus.receiveGuarded() to prevent multi-consumer issues",
  89 |     level = DeprecationLevel.ERROR
  90 | )
  91 | fun Flow<SemanticEvent>.collectDirectly(): Nothing =
  92 |     error("Use SemanticEventBus.receiveGuarded() instead")

```

`main\kotlin\com\example\gymbro\features\thinkingbox\domain\guardrail\ThinkingMLGuardrail.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.domain.guardrail
   2 | 
   3 | import timber.log.Timber
   4 | import java.nio.charset.Charset
   5 | import java.nio.charset.CodingErrorAction
   6 | import javax.inject.Inject
   7 | import javax.inject.Singleton
   8 | 
   9 | /**
  10 |  * ThinkingMLGuardrail - 智能内容修复器
  11 |  *
  12 |  * 🔥 完全重写：按照用户提供的修复逻辑栈实现
  13 |  * 处理 LLM 输出的混合格式，确保解析器能正确处理
  14 |  *
  15 |  * 核心功能：
  16 |  * - 统一 <think>/<thinking> 标签
  17 |  * - 转换 <phase:XXX> 为 <title phase="XXX">
  18 |  * - 清理裸 phase: 标记
  19 |  * - 修复不完整的标签
  20 |  * - 确保 XML 结构完整性
  21 |  */
  22 | @Singleton
  23 | class ThinkingMLGuardrail @Inject constructor() {
  24 | 
  25 |     // 🔥 【新增】半截tag缓冲，解决token流跨边界问题
  26 |     private var chunkBuf = StringBuilder()
  27 | 
  28 |     /**
  29 |      * 🔥 轻量级token级别清理 - 双缓冲方案专用
  30 |      * 只做最基本的字符修复，不做复杂的XML结构修复
  31 |      */
  32 |     fun sanitizeToken(token: String): String {
  33 |         return try {
  34 |             token
  35 |                 .replace("\u0000", "") // 移除null字符
  36 |                 .replace("\uFFFD", "") // 移除替换字符
  37 |                 .replace(Regex("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]"), "") // 移除控制字符
  38 |         } catch (e: Exception) {
  39 |             Timber.tag(TAG).w(e, "Token清理失败")
  40 |             token
  41 |         }
  42 |     }
  43 | 
  44 |     // 🔥 UTF-8 解码器，处理乱码问题
  45 |     private val utf8Decoder = Charset.forName("UTF-8").newDecoder().apply {
  46 |         onMalformedInput(CodingErrorAction.REPLACE)
  47 |         onUnmappableCharacter(CodingErrorAction.REPLACE)
  48 |     }
  49 | 
  50 |     companion object {
  51 |         private const val TAG = "ThinkingMLGuardrail"
  52 | 
  53 |         // 🔥 全量正则表达式常量
  54 |         private val OPEN_THINK = Regex("<\\s*think\\s*>", RegexOption.IGNORE_CASE)
  55 |         private val CLOSE_THINK = Regex("</\\s*think\\s*>", RegexOption.IGNORE_CASE)
  56 | 
  57 |         // 🔥 万能 Phase 捕获：允许多个 '<'，允许缺少闭 '>'
  58 |         private val ANY_PHASE_TAG = Regex("<+\\s*phase:([A-Za-z_]+)\\s*>?", RegexOption.IGNORE_CASE)
  59 |         private val CLOSE_PHASE = Regex("</\\s*phase:[A-Za-z_]+\\s*>", RegexOption.IGNORE_CASE)
  60 | 
  61 |         // 🔥 裸 phase: 标记处理（优先级最高，处理半截标签）
  62 |         private val BARE_PHASE = Regex("phase\\s*:\\s*([A-Za-z_]+)", RegexOption.IGNORE_CASE)
  63 |         private val BROKEN_FINAL = Regex("<\\s*final[^>]*(?<!>)", RegexOption.IGNORE_CASE)
  64 | 
  65 |         // 🔥 重复标签检测
  66 |         private val DUPLICATE_THINKING_OPEN = Regex("(<thinking>\\s*)+", RegexOption.IGNORE_CASE)
  67 |         private val DUPLICATE_THINKING_CLOSE = Regex("(\\s*</thinking>)+", RegexOption.IGNORE_CASE)
  68 |     }
  69 | 
  70 |     suspend fun retry(): Boolean {
  71 |         // 临时实现 - 模拟重试逻辑
  72 |         return true
  73 |     }
  74 | 
  75 |     fun validate(input: String): Boolean {
  76 |         // 临时实现 - 总是返回 true
  77 |         return true
  78 |     }
  79 | 
  80 |     /**
  81 |      * 快速发送方法 - 用于外部调用
  82 |      */
  83 |     fun emit(raw: String) {
  84 |         // 🔥 快速路径：如果内容不含 '<' '>' 直接发送
  85 |         if (!raw.contains('<') && !raw.contains('>')) {
  86 |             emitSafe(raw)
  87 |             return
  88 |         }
  89 | 
  90 |         // 否则走完整的验证修复流程
  91 |         val result = validateAndFix(raw)
  92 |         emitSafe(result.content)
  93 |     }
  94 | 
  95 |     /**
  96 |      * 安全发送方法 - 内部使用
  97 |      */
  98 |     private fun emitSafe(content: String) {
  99 |         // TODO: 集成到实际的发送机制
 100 |         // 这里可以发送到下游的 Parser
 101 |         Timber.tag(TAG).v("发送内容: ${content.take(100)}${if (content.length > 100) "..." else ""}")
 102 |     }
 103 | 
 104 |     /**
 105 |      * 验证并修复内容
 106 |      * 🔥 完全重写：按照用户提供的一次性彻底兜底方案实现
 107 |      * 不论 LLM 打什么旧格式，都转换成前端能吃的 XML
 108 |      */
 109 |     fun validateAndFix(rawContent: String): GuardrailResult {
 110 |         // 🔥 关键日志：只在内容包含标签时记录
 111 |         if (rawContent.contains("<") && rawContent.length > 100) {
 112 |             Timber.tag(TAG).i("GUARDRAIL 处理内容，长度: ${rawContent.length}")
 113 |         }
 114 | 
 115 |         // 🔥 UTF-8 处理：确保字符编码正确
 116 |         val cleanContent = try {
 117 |             // 简单的 UTF-8 验证和清理
 118 |             rawContent.replace("\uFFFD", "?") // 替换替换字符
 119 |                 .replace(Regex("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]"), "") // 移除控制字符
 120 |         } catch (e: Exception) {
 121 |             Timber.tag(TAG).w(e, "UTF-8 清理失败，使用原始内容")
 122 |             rawContent
 123 |         }
 124 | 
 125 |         // 🔥 【修复①】把上一轮遗留 + 新 chunk 拼一起
 126 |         var work = chunkBuf.append(cleanContent).toString()
 127 | 
 128 |         // ① 如果结尾仍缺 '>'，保留到下一轮
 129 |         val lastOpen = work.lastIndexOf('<')
 130 |         if (lastOpen != -1 && work.indexOf('>', lastOpen) == -1) {
 131 |             chunkBuf = StringBuilder(work.substring(lastOpen))
 132 |             work = work.substring(0, lastOpen)
 133 |         } else {
 134 |             chunkBuf.clear()
 135 |         }
 136 | 
 137 |         // ② 🔥 修复 hinking → thinking 拼写错误
 138 |         work = work.replace(Regex("<\\/?hinking", RegexOption.IGNORE_CASE)) { matchResult ->
 139 |             if (matchResult.value.startsWith("</")) "</thinking>" else "<thinking>"
 140 |         }
 141 | 
 142 |         var fixedContent = work
 143 |         val fixes = mutableListOf<String>()
 144 | 
 145 |         // 只在内容较长时记录日志
 146 |         if (rawContent.length > 500) {
 147 |             Timber.d("$TAG: 开始修复内容，原始长度: ${rawContent.length}")
 148 |         }
 149 | 
 150 |         // 🔥 一次性彻底兜底修复
 151 |         try {
 152 |             // 1️⃣ think → thinking (开/闭标签)
 153 |             if (OPEN_THINK.containsMatchIn(fixedContent) || CLOSE_THINK.containsMatchIn(fixedContent)) {
 154 |                 fixedContent = fixedContent
 155 |                     .replace(OPEN_THINK, "<thinking>")
 156 |                     .replace(CLOSE_THINK, "</thinking>")
 157 |                 fixes.add("修复思考标记：<think> → <thinking>")
 158 |             }
 159 | 
 160 |             // 2️⃣ 🔥 修复：只处理第一个phase标签，避免过度修复
 161 |             if (ANY_PHASE_TAG.containsMatchIn(fixedContent)) {
 162 |                 val matches = ANY_PHASE_TAG.findAll(fixedContent).toList()
 163 | 
 164 |                 // 🔥 关键修复：如果发现大量重复标签，说明AI输出异常，只保留第一个
 165 |                 if (matches.size > 10) {
 166 |                     println("GUARDRAIL 警告: 发现 ${matches.size} 个重复phase标签，只保留第一个")
 167 | 
 168 |                     // 找到第一个有效的phase标签
 169 |                     val firstMatch = matches.firstOrNull()
 170 |                     if (firstMatch != null) {
 171 |                         val raw = firstMatch.groupValues[1].uppercase()
 172 |                         val canonical = when (raw) {
 173 |                             "PLAN", "PLANNING" -> "PLANNING"
 174 |                             "ANALYZE", "ANALYSIS" -> "ANALYSIS"
 175 |                             "EXECUTE", "EXECUTION" -> "EXECUTION"
 176 |                             "REVIEW" -> "REVIEW"
 177 |                             "CONCLUDE", "CONCLUSION" -> "CONCLUSION"
 178 |                             else -> raw
 179 |                         }
 180 | 
 181 |                         // 只替换第一个，删除其他重复的
 182 |                         fixedContent = fixedContent.replaceFirst(ANY_PHASE_TAG, "<title phase=\"$canonical\"></title>")
 183 |                         fixedContent = fixedContent.replace(ANY_PHASE_TAG, "") // 删除其他重复的
 184 | 
 185 |                         println("GUARDRAIL 修复: 保留第一个 '${firstMatch.value}' → '<title phase=\"$canonical\"></title>'")
 186 |                     }
 187 |                 } else {
 188 |                     // 正常情况：少量标签，正常替换
 189 |                     println("GUARDRAIL 发现 ${matches.size} 个 phase 标签")
 190 | 
 191 |                     fixedContent = fixedContent.replace(ANY_PHASE_TAG) { matchResult ->
 192 |                         val raw = matchResult.groupValues[1].uppercase()
 193 |                         val canonical = when (raw) {
 194 |                             "PLAN", "PLANNING" -> "PLANNING"
 195 |                             "ANALYZE", "ANALYSIS" -> "ANALYSIS"
 196 |                             "EXECUTE", "EXECUTION" -> "EXECUTION"
 197 |                             "REVIEW" -> "REVIEW"
 198 |                             "CONCLUDE", "CONCLUSION" -> "CONCLUSION"
 199 |                             else -> raw
 200 |                         }
 201 |                         "<title phase=\"$canonical\"></title>"
 202 |                     }
 203 |                 }
 204 |                 fixes.add("修复阶段开标记：<phase:XXX> → <title phase=\"XXX\"></title>")
 205 |             }
 206 | 
 207 |             // 2-b️⃣ 处理裸标记（使用简单的字符串检查避免复杂正则）
 208 |             // 避免使用 look-behind，改用简单的字符串处理
 209 |             val barePhasePattern = Regex("""\bphase\s*:\s*([A-Za-z_]+)\b""", RegexOption.IGNORE_CASE)
 210 |             val matches = barePhasePattern.findAll(fixedContent).toList()
 211 | 
 212 |             for (match in matches.reversed()) { // 从后往前替换，避免索引偏移
 213 |                 val fullMatch = match.value
 214 |                 val phaseName = match.groupValues[1].uppercase()
 215 |                 val startIndex = match.range.first
 216 | 
 217 |                 // 检查是否在尖括号内（简单检查）
 218 |                 val beforeMatch = fixedContent.substring(0, startIndex)
 219 |                 val lastOpenBracket = beforeMatch.lastIndexOf('<')
 220 |                 val lastCloseBracket = beforeMatch.lastIndexOf('>')
 221 | 
 222 |                 // 如果最近的 < 比最近的 > 更靠后，说明在标签内，跳过
 223 |                 if (lastOpenBracket > lastCloseBracket) {
 224 |                     continue
 225 |                 }
 226 | 
 227 |                 // 🔥 替换裸标记，同时应用同义词归一
 228 |                 val canonical = when (phaseName) {
 229 |                     "PLAN", "PLANNING" -> "PLANNING"
 230 |                     "ANALYZE", "ANALYSIS" -> "ANALYSIS"
 231 |                     "EXECUTE", "EXECUTION" -> "EXECUTION"
 232 |                     "REVIEW" -> "REVIEW"
 233 |                     "CONCLUDE", "CONCLUSION" -> "CONCLUSION"
 234 |                     else -> phaseName // 兜底
 235 |                 }
 236 | 
 237 |                 // 🔥 【增强日志】记录裸标记映射
 238 |                 if (phaseName != canonical) {
 239 |                     Timber.tag("GUARDRAIL").i("🔄 裸标记映射: 'phase:$phaseName' → '<title phase=\"$canonical\"></title>'")
 240 |                 }
 241 | 
 242 |                 fixedContent = fixedContent.replaceRange(
 243 |                     match.range,
 244 |                     "<title phase=\"$canonical\"></title>",
 245 |                 )
 246 |                 fixes.add("修复裸 phase: 标记 → <title phase=\"XXX\"></title>（自动闭合）")
 247 |             }
 248 | 
 249 |             // 2-b 🔥 若文本以 <title> 开头，则自动补前置 <thinking>
 250 |             if (fixedContent.startsWith("<title") && !fixedContent.startsWith("<thinking>")) {
 251 |                 fixedContent = "<thinking>\n$fixedContent"
 252 |                 fixes.add("首段自动补前置 <thinking>")
 253 |             }
 254 | 
 255 |             if (CLOSE_PHASE.containsMatchIn(fixedContent)) {
 256 |                 fixedContent = fixedContent.replace(CLOSE_PHASE, "</title>")
 257 |                 fixes.add("修复阶段闭标记：</phase:XXX> → </title>")
 258 |             }
 259 | 
 260 |             // 3️⃣ 移除裸 phase: 标记
 261 |             if (BARE_PHASE.containsMatchIn(fixedContent)) {
 262 |                 fixedContent = fixedContent.replace(BARE_PHASE, "")
 263 |                 fixes.add("移除裸 phase: 标记")
 264 |             }
 265 | 
 266 |             // 4️⃣ 修复残缺的 <final> 标记
 267 |             if (BROKEN_FINAL.containsMatchIn(fixedContent)) {
 268 |                 fixedContent = fixedContent.replace(BROKEN_FINAL, "<final>")
 269 |                 fixes.add("修复残缺的 <final> 标记")
 270 |             }
 271 | 
 272 |             // 5️⃣ 🔥 关键修复：处理重复的 thinking 标签
 273 |             if (DUPLICATE_THINKING_OPEN.containsMatchIn(fixedContent)) {
 274 |                 fixedContent = fixedContent.replace(DUPLICATE_THINKING_OPEN, "<thinking>")
 275 |                 fixes.add("合并重复的 <thinking> 开标签")
 276 |             }
 277 | 
 278 |             if (DUPLICATE_THINKING_CLOSE.containsMatchIn(fixedContent)) {
 279 |                 fixedContent = fixedContent.replace(DUPLICATE_THINKING_CLOSE, "</thinking>")
 280 |                 fixes.add("合并重复的 </thinking> 闭标签")
 281 |             }
 282 | 
 283 |             // 6️⃣ 🔥 确保 <final> 前有 </thinking>
 284 |             if (fixedContent.contains("<final>") && !fixedContent.contains("</thinking>")) {
 285 |                 fixedContent = fixedContent.replace("<final>", "</thinking>\n<final>")
 286 |                 fixes.add("自动补齐缺失的 </thinking> 标记")
 287 |             }
 288 | 
 289 |             // 7️⃣ 确保标记完整性
 290 |             if (fixedContent.contains("<final>") && !fixedContent.contains("</final>")) {
 291 |                 fixedContent += "</final>"
 292 |                 fixes.add("添加缺失的 </final> 标记")
 293 |             }
 294 | 
 295 |             if (fixedContent.contains("<thinking>") && !fixedContent.contains("</thinking>") && !fixedContent.contains("<final>")) {
 296 |                 fixedContent += "</thinking>"
 297 |                 fixes.add("添加缺失的 </thinking> 标记")
 298 |             }
 299 | 
 300 |             // 8️⃣ 清理多余的空白字符
 301 |             fixedContent = fixedContent
 302 |                 .replace(Regex("\\n\\s*\\n\\s*\\n"), "\n\n")
 303 |                 .replace(Regex("^\\s+", RegexOption.MULTILINE), "")
 304 |                 .trim()
 305 |         } catch (e: Exception) {
 306 |             Timber.e(e, "$TAG: 修复过程中发生异常")
 307 |             fixes.add("修复过程异常: ${e.message}")
 308 |         }
 309 | 
 310 |         // 验证修复结果
 311 |         val isValid = fixedContent.isNotBlank() && fixedContent.length < 100000
 312 |         val confidence = when {
 313 |             isValid && fixes.isNotEmpty() -> 0.9f
 314 |             isValid -> 1.0f
 315 |             else -> 0.2f
 316 |         }
 317 | 
 318 |         // 只在有修复或内容较长时记录日志
 319 |         if (fixes.isNotEmpty()) {
 320 |             Timber.i("$TAG: 修复完成，应用了 ${fixes.size} 个修复，最终长度: ${fixedContent.length}")
 321 |         } else if (fixedContent.length > 1000) {
 322 |             Timber.d("$TAG: 验证完成，内容长度: ${fixedContent.length}")
 323 |         }
 324 | 
 325 |         // 🔥 强制 UTF-8 编码修复乱码问题
 326 |         val utf8FixedContent = try {
 327 |             fixedContent.toByteArray(Charsets.UTF_8).toString(Charsets.UTF_8)
 328 |         } catch (e: Exception) {
 329 |             Timber.w(e, "$TAG: UTF-8 编码修复失败，使用原内容")
 330 |             fixedContent
 331 |         }
 332 | 
 333 |         // 🔥 关键日志：只在有修复时记录输出
 334 |         if (fixes.isNotEmpty()) {
 335 |             Timber.tag(TAG).i("GUARDRAIL 修复完成，应用了 ${fixes.size} 个修复，最终长度: ${utf8FixedContent.length}")
 336 |         }
 337 | 
 338 |         return GuardrailResult(
 339 |             isValid = isValid,
 340 |             content = utf8FixedContent,
 341 |             confidence = confidence,
 342 |             errors = if (isValid) emptyList() else listOf(GuardrailError("内容验证失败")),
 343 |             fixes = fixes.map { GuardrailFix(it) },
 344 |         )
 345 |     }
 346 | 
 347 |     /**
 348 |      * 🔥 【新增】分离完整内容和半截tag
 349 |      * 解决token流跨tag边界被切的问题
 350 |      */
 351 |     private fun splitLastIncompleteTag(content: String): Pair<String, String> {
 352 |         if (content.isEmpty()) return Pair("", "")
 353 | 
 354 |         // 查找最后一个可能的半截tag开始位置
 355 |         val lastOpenBracket = content.lastIndexOf('<')
 356 |         if (lastOpenBracket == -1) {
 357 |             return Pair(content, "")
 358 |         }
 359 | 
 360 |         // 检查从最后一个'<'到结尾是否是半截tag
 361 |         val possibleTag = content.substring(lastOpenBracket)
 362 | 
 363 |         // 如果包含'>'，说明是完整的，不需要缓冲
 364 |         if (possibleTag.contains('>')) {
 365 |             return Pair(content, "")
 366 |         }
 367 | 
 368 |         // 检查是否是我们关心的tag开始（简化版本）
 369 |         val isIncompleteTag = possibleTag.length > 1 && possibleTag.length < 15 &&
 370 |             possibleTag.matches(Regex("</?\\s*[a-zA-Z]*", RegexOption.IGNORE_CASE))
 371 | 
 372 |         return if (isIncompleteTag) {
 373 |             // 分离：完整部分 + 半截部分
 374 |             Timber.tag(TAG).v("检测到半截tag: '$possibleTag'，缓冲处理")
 375 |             Pair(content.substring(0, lastOpenBracket), possibleTag)
 376 |         } else {
 377 |             // 不是tag，全部返回
 378 |             Pair(content, "")
 379 |         }
 380 |     }
 381 | }
 382 | 
 383 | /**
 384 |  * GuardrailResult - 验证结果数据类
 385 |  */
 386 | data class GuardrailResult(
 387 |     val isValid: Boolean = true,
 388 |     val content: String = "",
 389 |     val confidence: Float = 1.0f,
 390 |     val errors: List<GuardrailError> = emptyList(),
 391 |     val fixes: List<GuardrailFix> = emptyList(),
 392 |     val errorMessage: String? = null,
 393 | ) {
 394 |     fun hasFixes(): Boolean = fixes.isNotEmpty()
 395 | }
 396 | 
 397 | /**
 398 |  * GuardrailError - 验证错误
 399 |  */
 400 | data class GuardrailError(
 401 |     val message: String,
 402 |     val severity: String = "ERROR",
 403 | ) {
 404 |     fun getShortDescription(): String = message
 405 | }
 406 | 
 407 | /**
 408 |  * GuardrailFix - 修复信息
 409 |  */
 410 | data class GuardrailFix(
 411 |     val description: String,
 412 |     val type: String = "AUTO",
 413 | )

```

`main\kotlin\com\example\gymbro\features\thinkingbox\domain\interfaces\ThinkingBoxInterfaces.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.domain.interfaces
   2 | 
   3 | import androidx.compose.runtime.Immutable
   4 | import kotlin.time.Duration
   5 | import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingStep
   6 | import com.example.gymbro.features.thinkingbox.domain.model.events.Summary
   7 | 
   8 | /**
   9 |  * ThinkingBox模块规范接口定义
  10 |  *
  11 |  * 基于 627规范统一.md 文档实现
  12 |  * 确保所有组件遵循统一的接口契约
  13 |  */
  14 | 
  15 | /**
  16 |  * 所有可复用思考组件统一用此 Props
  17 |  *
  18 |  * 基于 627规范统一.md 第147-150行要求
  19 |  * 所有动画、可测性与可替换性都挂在 ThinkingProps 上
  20 |  */
  21 | interface ThinkingProps {
  22 |     val title: String
  23 |     val content: String
  24 | }
  25 | 
  26 | /**
  27 |  * 公共检查 API（供外部模块调用）
  28 |  *
  29 |  * 基于 627规范统一.md 第116-125行要求
  30 |  * 外部（比如 coach 模块）只持 ThinkingBoxInspector 进行监控，不依赖内部实现
  31 |  */
  32 | interface ThinkingBoxInspector {
  33 |     /** 当前是否处于思考阶段 */
  34 |     val isThinking: Boolean
  35 |     /** 展示中的阶段数量 */
  36 |     val phaseCount: Int
  37 |     /** 已用时（ms） */
  38 |     val elapsedMillis: Long
  39 |     /** 最终富文本（若已结束） */
  40 |     val finalMarkdown: String?
  41 | }
  42 | 
  43 | /**
  44 |  * UI层唯一输入的UiState
  45 |  *
  46 |  * 基于 627规范统一.md 第61-74行要求 + 627UI设计.md 第110-120行要求
  47 |  * 产生位置：ThinkingReducer.reduce(old, ThinkingEvent)
  48 |  * 消费位置：所有 Composable
  49 |  */
  50 | @Immutable
  51 | data class UiState(
  52 |     val phases: List<PhaseUi> = emptyList(),
  53 |     val finalMarkdown: String? = null,
  54 |     val isThinking: Boolean = false,
  55 |     val isCollapsed: Boolean = false,
  56 |     val elapsed: Duration = Duration.ZERO,
  57 | 
  58 |     // 🔥 627UI设计.md 要求的额外属性
  59 |     val showHeader: Boolean = true,
  60 |     val visiblePhases: List<PhaseUi> = emptyList(),
  61 |     val activePhaseId: String? = null, // 🔥 627目标交付：改为String类型匹配PhaseUi.id
  62 |     val isStreaming: Boolean = false,
  63 |     val summary: Summary? = null,
  64 |     val final: String? = null,
  65 | 
  66 |     // 预思考内容：<think>标签内容的灰度预览
  67 |     val preThinking: String? = null,
  68 | 
  69 |     // 🔥 数据流统一新增：持久化相关字段
  70 |     val tokensSnapshot: String = "", // 原始token快照，用于调试/重播
  71 |     val lastFunctionCall: String? = null, // 最后的函数调用JSON
  72 |     val lastMcp: String? = null, // 最后的MCP元数据JSON
  73 |     val persisted: Boolean = false, // 是否已持久化到历史表
  74 | 
  75 |     // 🔥 Step 1重构：11步状态转换序列支持
  76 |     val currentStep: ThinkingStep = ThinkingStep.UserMessage, // 当前所处的步骤
  77 |     val stepTransitionTime: Long = System.currentTimeMillis(), // 步骤转换时间
  78 |     val thinkDetected: Boolean = false, // 是否检测到<think>标签
  79 |     val thinkingStarted: Boolean = false, // 是否检测到<thinking>标签
  80 |     val phaseManagementActive: Boolean = false, // Phase管理是否激活
  81 |     val titleUpdatesEnabled: Boolean = false, // 标题更新是否启用
  82 |     val textStreamingActive: Boolean = false, // 文本流式传输是否激活
  83 |     val completionCollapseTriggered: Boolean = false, // 完成折叠是否触发
  84 |     val summaryToggleEnabled: Boolean = false, // 摘要切换是否启用
  85 |     val richTextRenderingActive: Boolean = false, // 富文本渲染是否激活
  86 |     val currentPhaseId: String? = null, // 当前活跃的Phase ID
  87 |     val lastPhaseCompleted: String? = null, // 最后完成的Phase ID
  88 | ) {
  89 |     /**
  90 |      * 便捷访问属性
  91 |      */
  92 |     val hasContent: Boolean get() = phases.isNotEmpty() || finalMarkdown != null
  93 |     val isCompleted: Boolean get() = !isThinking && finalMarkdown != null
  94 |     val isEmpty: Boolean get() = phases.isEmpty() && finalMarkdown == null
  95 | 
  96 |     /**
  97 |      * 🔥 Step 1重构：11步状态转换辅助方法
  98 |      */
  99 | 
 100 |     /**
 101 |      * 获取当前步骤应该显示的组件类型
 102 |      */
 103 |     fun getCurrentDisplayComponent(): String {
 104 |         return when (currentStep) {
 105 |             ThinkingStep.UserMessage -> "UserMessage"
 106 |             ThinkingStep.LoadingAnimation -> "ThinkingHeader"
 107 |             ThinkingStep.ThinkDetection -> "ThinkingHeader"
 108 |             ThinkingStep.PreThinkingPhase -> "PreThinkingCard"
 109 |             ThinkingStep.ThinkingStart -> "ThinkingHeader"
 110 |             ThinkingStep.PhaseManagement -> "ThinkingStageCard"
 111 |             ThinkingStep.TitleUpdates -> "ThinkingStageCard"
 112 |             ThinkingStep.TextStreaming -> "ThinkingStageCard"
 113 |             ThinkingStep.CompletionCollapse -> "SummaryCard"
 114 |             ThinkingStep.SummaryToggle -> "SummaryCard"
 115 |             ThinkingStep.RichTextRendering -> "FinalRichTextRenderer"
 116 |         }
 117 |     }
 118 | 
 119 |     /**
 120 |      * 判断是否应该显示ThinkingHeader
 121 |      */
 122 |     fun shouldShowThinkingHeader(): Boolean {
 123 |         return when (currentStep) {
 124 |             ThinkingStep.LoadingAnimation,
 125 |             ThinkingStep.ThinkDetection,
 126 |             ThinkingStep.ThinkingStart -> true
 127 |             else -> false
 128 |         }
 129 |     }
 130 | 
 131 |     /**
 132 |      * 判断是否应该显示PreThinkingCard
 133 |      */
 134 |     fun shouldShowPreThinkingCard(): Boolean {
 135 |         return currentStep == ThinkingStep.PreThinkingPhase && !preThinking.isNullOrBlank()
 136 |     }
 137 | 
 138 |     /**
 139 |      * 判断是否应该显示ThinkingStageCard
 140 |      */
 141 |     fun shouldShowThinkingStageCard(): Boolean {
 142 |         return when (currentStep) {
 143 |             ThinkingStep.PhaseManagement,
 144 |             ThinkingStep.TitleUpdates,
 145 |             ThinkingStep.TextStreaming -> phases.isNotEmpty()
 146 |             else -> false
 147 |         }
 148 |     }
 149 | 
 150 |     /**
 151 |      * 判断是否应该显示SummaryCard
 152 |      */
 153 |     fun shouldShowSummaryCard(): Boolean {
 154 |         return when (currentStep) {
 155 |             ThinkingStep.CompletionCollapse,
 156 |             ThinkingStep.SummaryToggle -> summary != null
 157 |             else -> false
 158 |         }
 159 |     }
 160 | 
 161 |     /**
 162 |      * 判断是否应该显示FinalRichTextRenderer
 163 |      */
 164 |     fun shouldShowFinalRichTextRenderer(): Boolean {
 165 |         return currentStep == ThinkingStep.RichTextRendering && !final.isNullOrBlank()
 166 |     }
 167 | 
 168 |     /**
 169 |      * 获取当前步骤的描述（用于调试）
 170 |      */
 171 |     fun getCurrentStepDescription(): String {
 172 |         return "${currentStep.name} (${getCurrentDisplayComponent()})"
 173 |     }
 174 | }
 175 | 
 176 | /**
 177 |  * 阶段UI数据
 178 |  *
 179 |  * 基于 627规范统一.md 第69-73行要求
 180 |  * 已合并完整文本，供UI直接使用
 181 |  */
 182 | @Immutable
 183 | data class PhaseUi(
 184 |     val id: String,                     // 🔥 627目标交付：改为String类型，支持动态phase
 185 |     private val _title: String? = null, // 🔥 627目标交付：允许null，支持动态标题
 186 |     override val content: String = "",  // 已合并完整文本
 187 |     val totalChars: Int = 0,           // 🔥 627目标交付：字符统计
 188 |     val hasTitle: Boolean = false,     // 🔥 627目标交付：标题状态标记
 189 |     val isComplete: Boolean = false,   // 🔥 用户要求：Phase完整性标记
 190 | ) : ThinkingProps {
 191 |     // 🔥 627目标交付：重写title getter确保非null
 192 |     override val title: String get() = _title ?: ""
 193 | 
 194 |     /**
 195 |      * 🔥 最终修复：NEVER mutate PhaseUi in place
 196 |      */
 197 |     fun append(delta: String) = copy(
 198 |         content = content + delta,
 199 |         totalChars = totalChars + delta.length
 200 |     )
 201 | 
 202 |     fun withTitle(newTitle: String) = copy(
 203 |         _title = newTitle,
 204 |         hasTitle = true
 205 |     )
 206 | }
 207 | 
 208 | /**
 209 |  * 打字机组件Props实现
 210 |  *
 211 |  * 基于 627规范统一.md 第152-155行要求
 212 |  */
 213 | @Immutable
 214 | data class TypingTextProps(
 215 |     override val title: String,
 216 |     override val content: String,
 217 |     val isActive: Boolean = false,
 218 |     val typingSpeed: Long = 16L
 219 | ) : ThinkingProps
 220 | 
 221 | /**
 222 |  * ThinkingBox操作API（供外部模块调用）
 223 |  *
 224 |  * 基于 626coach-thinkingbox.md 方案文档实现
 225 |  * 外部（比如 coach 模块）通过 ThinkingBoxFacade 进行操作控制，不依赖内部实现
 226 |  */
 227 | interface ThinkingBoxFacade {
 228 |     /** 🔥 蓝图修复：开始处理并返回UiState流，从RawTokenBus读取 */
 229 |     suspend fun start(): kotlinx.coroutines.flow.Flow<UiState>
 230 |     
 231 |     /** 🔥 新增：带messageId的开始方法，用于History落库关联 */
 232 |     suspend fun startWithMessageId(messageId: String): kotlinx.coroutines.flow.Flow<UiState>
 233 | 
 234 |     /** 提交单个Token到处理流 */
 235 |     fun submit(token: String): Boolean
 236 | 
 237 |     /** 重置思考状态 */
 238 |     suspend fun reset(): Unit
 239 | 
 240 |     /** 标记当前状态已持久化 */
 241 |     fun markPersisted(): Unit
 242 | 
 243 |     /** 发送ThinkingEvent到处理流 */
 244 |     fun sendEvent(event: com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent): Unit
 245 | 
 246 |     /** 获取当前状态 */
 247 |     val currentState: UiState
 248 | }

```

`main\kotlin\com\example\gymbro\features\thinkingbox\domain\logger\RawThinkingLogger.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.domain.logger
   2 | 
   3 | import com.example.gymbro.features.thinkingbox.internal.logging.TBLog
   4 | import com.example.gymbro.features.thinkingbox.internal.logging.TBTags
   5 | import javax.inject.Inject
   6 | import javax.inject.Singleton
   7 | 
   8 | /**
   9 |  * 原始思考内容日志记录器
  10 |  *
  11 |  * 用于记录<think>...</think>内的完整推理草稿
  12 |  * 在DEBUG模式下记录到日志，Release模式下为空实现
  13 |  */
  14 | interface RawThinkingLogger {
  15 |     /**
  16 |      * 追加思考内容块
  17 |      */
  18 |     fun append(text: String)
  19 | 
  20 |     /**
  21 |      * 关闭当前思考会话
  22 |      */
  23 |     fun close()
  24 | 
  25 |     /**
  26 |      * 重置日志状态
  27 |      */
  28 |     fun reset()
  29 | }
  30 | 
  31 | /**
  32 |  * DEBUG模式下的实际日志实现
  33 |  */
  34 | @Singleton
  35 | class DebugRawThinkingLogger @Inject constructor() : RawThinkingLogger {
  36 |     
  37 |     private val contentBuffer = StringBuilder()
  38 |     private var sessionId: String? = null
  39 |     private var isActive = false
  40 | 
  41 |     override fun append(text: String) {
  42 |         if (!isActive) {
  43 |             // 开始新的思考会话
  44 |             sessionId = "think_${System.currentTimeMillis()}"
  45 |             isActive = true
  46 |             TBLog.log(TBTags.RAW, TBLog.LogLevel.DEBUG) {
  47 |                 "开始记录原始思考内容 [session=$sessionId]"
  48 |             }
  49 |         }
  50 |         
  51 |         contentBuffer.append(text)
  52 |         
  53 |         // 实时记录内容块
  54 |         TBLog.log(TBTags.RAW, TBLog.LogLevel.DEBUG) {
  55 |             "RawThinking[$sessionId]: +${text.length}字符 '${text.take(50)}${if (text.length > 50) "..." else ""}'"
  56 |         }
  57 |     }
  58 | 
  59 |     override fun close() {
  60 |         if (isActive) {
  61 |             val finalContent = contentBuffer.toString()
  62 |             TBLog.log(TBTags.RAW, TBLog.LogLevel.INFO) {
  63 |                 "完整原始思考内容 [session=$sessionId, 总长度=${finalContent.length}字符]:\n$finalContent"
  64 |             }
  65 |             
  66 |             // 清理状态
  67 |             contentBuffer.clear()
  68 |             sessionId = null
  69 |             isActive = false
  70 |         }
  71 |     }
  72 | 
  73 |     override fun reset() {
  74 |         contentBuffer.clear()
  75 |         sessionId = null
  76 |         isActive = false
  77 |         TBLog.log(TBTags.RAW, TBLog.LogLevel.DEBUG) {
  78 |             "RawThinkingLogger状态已重置"
  79 |         }
  80 |     }
  81 | }
  82 | 
  83 | /**
  84 |  * Release模式下的空实现
  85 |  */
  86 | @Singleton
  87 | class NoopRawThinkingLogger @Inject constructor() : RawThinkingLogger {
  88 |     override fun append(text: String) {
  89 |         // 空实现
  90 |     }
  91 | 
  92 |     override fun close() {
  93 |         // 空实现
  94 |     }
  95 | 
  96 |     override fun reset() {
  97 |         // 空实现
  98 |     }
  99 | }

```

`main\kotlin\com\example\gymbro\features\thinkingbox\domain\mapper\DomainMapper.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.domain.mapper
   2 | 
   3 | import com.example.gymbro.features.thinkingbox.domain.bus.SemanticEventBus
   4 | import com.example.gymbro.features.thinkingbox.domain.logger.RawThinkingLogger
   5 | import com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent
   6 | import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
   7 | import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingStep
   8 | import com.example.gymbro.features.thinkingbox.internal.logging.TBLog
   9 | import com.example.gymbro.features.thinkingbox.internal.logging.TBTags
  10 | import kotlinx.coroutines.CoroutineScope
  11 | import kotlinx.coroutines.Dispatchers
  12 | import kotlinx.coroutines.launch
  13 | import timber.log.Timber
  14 | import java.util.concurrent.atomic.AtomicBoolean
  15 | import javax.inject.Inject
  16 | import javax.inject.Singleton
  17 | 
  18 | @Singleton
  19 | class DomainMapper
  20 |     @Inject
  21 |     constructor(
  22 |         private val rawThinkingLogger: RawThinkingLogger,
  23 |     ) {
  24 |         // 🔥 【v7修复】事件处理器引用，由Facade在init时设置
  25 |         private var eventHandler: ((ThinkingEvent) -> Unit)? = null
  26 | 
  27 |         // 🔥 【单卡刷新原则】当前phaseId状态跟踪
  28 |         private var currentPhaseId: String? = null
  29 | 
  30 |         // ✅ 【Title内容混淆修复】waitingTitle状态管理
  31 |         private var waitingTitle: Boolean = false
  32 | 
  33 |         // 🔥 【15秒超时修复】跟踪thinking是否已经结束
  34 |         private var thinkingEnded: Boolean = false
  35 | 
  36 |         // 🔥 【循环解析修复】防止重复启动订阅的守卫
  37 |         private val isSubscriptionStarted = AtomicBoolean(false)
  38 | 
  39 |         // 🔥 【多轮对话修复】跟踪当前的messageId，确保事件正确关联
  40 |         private var currentMessageId: String? = null
  41 | 
  42 |         /**
  43 |          * 🔥 【循环解析修复】启动对SemanticEventBus的唯一、持久化订阅
  44 |          * 这个方法应该在ThinkingBoxFacade的init块中被调用一次，防止重复订阅。
  45 |          */
  46 |         fun startSingleConsumerCollection(handler: (ThinkingEvent) -> Unit) {
  47 |             // 🔥 【循环解析修复】防止重复启动订阅
  48 |             if (!isSubscriptionStarted.compareAndSet(false, true)) {
  49 |                 Timber.tag(TBTags.MAP).w("🚨 [循环解析修复] SemanticEventBus订阅已启动，忽略重复调用")
  50 |                 return
  51 |             }
  52 | 
  53 |             this.eventHandler = handler
  54 |             // 使用一个不会被意外取消的Scope
  55 |             CoroutineScope(Dispatchers.IO).launch {
  56 |                 try {
  57 |                     Timber.tag(TBTags.MAP).i("🔥 [循环解析修复] 启动SemanticEventBus唯一订阅")
  58 |                     SemanticEventBus.receiveGuarded().collect { semanticEvent ->
  59 |                         // 每次收到事件，都进行一次无状态的翻译
  60 |                         val thinkingEvents = mapToThinkingEvents(semanticEvent)
  61 |                         thinkingEvents.forEach { eventHandler?.invoke(it) }
  62 |                     }
  63 |                 } catch (e: Exception) {
  64 |                     Timber.tag(TBTags.MAP).e(e, "🚨 [循环解析修复] SemanticEventBus订阅失败")
  65 |                     // 向上层报告错误
  66 |                     eventHandler?.invoke(ThinkingEvent.Error("Mapper subscription failed", e))
  67 |                 }
  68 |             }
  69 |         }
  70 | 
  71 |         /**
  72 |          * 🔥 【v7修复】允许外部直接注入事件，用于Facade的Start事件
  73 |          * 🔥 【多轮对话修复】跟踪Start事件中的messageId
  74 |          */
  75 |         fun sendEvent(event: ThinkingEvent) {
  76 |             // 🔥 【多轮对话修复】如果是Start事件，记录messageId
  77 |             if (event is ThinkingEvent.Start) {
  78 |                 currentMessageId = event.messageId
  79 |                 Timber.tag("MAPPER-MSGID").d("🔥 记录messageId: ${event.messageId}")
  80 |             }
  81 |             eventHandler?.invoke(event)
  82 |         }
  83 | 
  84 |         /**
  85 |          * 🔥 【Phase转换控制】直接更新currentPhaseId，用于同步状态
  86 |          */
  87 |         fun updateCurrentPhaseId(phaseId: String?) {
  88 |             Timber.tag("PHASE-DEBUG").e("🔧 DomainMapper.updateCurrentPhaseId: $currentPhaseId → $phaseId")
  89 |             currentPhaseId = phaseId
  90 |         }
  91 | 
  92 |         /**
  93 |          * 🔥 【v7增强】功能完整的事件映射方法
  94 |          * 保持无状态特性，但提供完整的事件生成逻辑
  95 |          * 🔥 【多轮对话架构升级】改为public，供ThinkingBoxInstance直接调用
  96 |          */
  97 |         fun mapToThinkingEvents(event: SemanticEvent): List<ThinkingEvent> {
  98 |             TBLog.log(TBTags.MAP, TBLog.LogLevel.DEBUG) { "Mapping SemanticEvent: ${event::class.simpleName}" }
  99 | 
 100 |             return when (event) {
 101 |                 is SemanticEvent.TagOpened ->
 102 |                     when (event.name.lowercase()) {
 103 |                         "think" ->
 104 |                             listOf(
 105 |                                 ThinkingEvent.ThinkDetected,
 106 |                                 ThinkingEvent.StepTransition(
 107 |                                     targetStep = ThinkingStep.ThinkDetection,
 108 |                                     trigger = "<think>标签检测",
 109 |                                 ),
 110 |                             )
 111 | 
 112 |                         "thinking" ->
 113 |                             listOf(
 114 |                                 // 🔥 【v7文档修复】<thinking>标签不发送PreThinkClear，只设置currentPhaseId="thinking"
 115 |                                 ThinkingEvent.ThinkingStarted,
 116 |                                 ThinkingEvent.StepTransition(
 117 |                                     targetStep = ThinkingStep.ThinkingStart,
 118 |                                     trigger = "<thinking>标签检测",
 119 |                                 ),
 120 |                             )
 121 | 
 122 |                         "phase" -> {
 123 |                             val phaseId = event.attrs["id"] ?: "unknown"
 124 |                             Timber.tag("PHASE-DEBUG").e("🔍 解析phase标签: id='$phaseId' | attrs=${event.attrs}")
 125 | 
 126 |                             // 🔥 【Phase转换控制】不立即切换，加入等待队列
 127 |                             val previousPhaseId = currentPhaseId
 128 |                             Timber.tag("PHASE-DEBUG").e("🔍 检测到新Phase: $phaseId，当前Phase: $previousPhaseId")
 129 | 
 130 |                             if (phaseId == "perthink") {
 131 |                                 // 🔥 【perthink修复】perthink作为启动，立即切换
 132 |                                 currentPhaseId = phaseId
 133 |                                 Timber
 134 |                                     .tag("PHASE-DEBUG")
 135 |                                     .e("🚀 创建perthink phase: $phaseId | 更新currentPhaseId: $currentPhaseId")
 136 |                                 listOf(
 137 |                                     ThinkingEvent.ThinkDetected, // 检测到思考开始
 138 |                                     ThinkingEvent.PhaseManagementActivated(phaseId), // 🔥 关键：创建perthink思考框
 139 |                                     ThinkingEvent.PhaseDelta(
 140 |                                         // 🔥 【perthink修复】必须创建perthink Phase
 141 |                                         id = phaseId,
 142 |                                         ordinal = System.nanoTime(),
 143 |                                         title = "Bro is thinking", // 🔥 perthink特殊标题
 144 |                                         append = "",
 145 |                                     ),
 146 |                                     ThinkingEvent.StepTransition(
 147 |                                         targetStep = ThinkingStep.PreThinkingPhase,
 148 |                                         trigger = "perthink阶段启动",
 149 |                                     ),
 150 |                                 )
 151 |                             } else {
 152 |                                 // 🔥 【Phase转换控制】正式phase加入等待队列，等待当前phase渲染完成
 153 |                                 Timber
 154 |                                     .tag("PHASE-DEBUG")
 155 |                                     .e("📋 正式Phase加入队列: $phaseId | 当前活跃: $currentPhaseId | 不更新currentPhaseId")
 156 |                                 // 🔥 【关键修复】不要立即更新currentPhaseId，保持当前phase继续接收文本
 157 |                                 // currentPhaseId将在PhaseRenderingComplete时更新
 158 |                                 listOf(
 159 |                                     ThinkingEvent.PhaseQueueAdd(phaseId), // 新事件：加入phase队列，PhaseManagementActivated将在切换时触发
 160 |                                 )
 161 |                             }
 162 |                         }
 163 | 
 164 |                         "title" -> {
 165 |                             // ✅ 【Title内容混淆修复】进入waitingTitle状态
 166 |                             waitingTitle = true
 167 |                             Timber.tag(TBTags.MAP).d("🔥 [Title内容混淆修复] 进入waitingTitle状态")
 168 | 
 169 |                             listOf(
 170 |                                 ThinkingEvent.TitleUpdatesEnabled("", ""),
 171 |                                 ThinkingEvent.StepTransition(
 172 |                                     targetStep = ThinkingStep.TitleUpdates,
 173 |                                     trigger = "<title>标签检测",
 174 |                                 ),
 175 |                             )
 176 |                         }
 177 | 
 178 |                         "final" -> {
 179 |                             // ✅ 【第三个phase消失修复】不立即触发RichTextRendering，只记录final开始
 180 |                             // RichTextRendering应该在</thinking>后或FinalArrived时触发
 181 |                             Timber
 182 |                                 .tag(TBTags.MAP)
 183 |                                 .d("🔥 [第三个phase消失修复] 检测到<final>标签，但不立即切换到RichTextRendering")
 184 |                             emptyList() // 暂时不发送任何事件
 185 |                         }
 186 | 
 187 |                         else -> emptyList()
 188 |                     }
 189 | 
 190 |                 is SemanticEvent.PreThinkChunk -> {
 191 |                     // 🔥 【701finalmermaid大纲修复】<think>内容处理为phase id="perthink"
 192 |                     listOf(
 193 |                         ThinkingEvent.PhaseDelta(
 194 |                             id = "perthink", // 🔥 固定使用perthink作为phase id
 195 |                             ordinal = System.nanoTime(),
 196 |                             title = "Bro is thinking", // 🔥 固定标题
 197 |                             append = event.text,
 198 |                         ),
 199 |                     )
 200 |                 }
 201 | 
 202 |                 is SemanticEvent.TextChunk -> {
 203 |                     // ✅ 【Title内容混淆修复】区分title内容和普通文本内容
 204 |                     val targetPhaseId = currentPhaseId
 205 | 
 206 |                     if (targetPhaseId == null) {
 207 |                         // 🚨 严格模式：没有活跃phase时，拒绝处理文本
 208 |                         Timber
 209 |                             .tag("PHASE-DEBUG")
 210 |                             .e("❌ TextChunk被拒绝：没有活跃phase | 文本长度: ${event.text.length}")
 211 |                         emptyList()
 212 |                     } else if (waitingTitle) {
 213 |                         // ✅ 【Title内容混淆修复】处理title内容
 214 |                         val titleText = event.text.trim()
 215 |                         Timber.tag("PHASE-DEBUG").e("📝 Title归属: '$titleText' → phase: $targetPhaseId")
 216 | 
 217 |                         listOf(
 218 |                             ThinkingEvent.PhaseDelta(
 219 |                                 id = targetPhaseId,
 220 |                                 ordinal = System.nanoTime(),
 221 |                                 title = titleText, // 🔥 关键修复：设置为title而不是append
 222 |                                 append = "", // title事件不追加内容
 223 |                             ),
 224 |                         )
 225 |                     } else {
 226 |                         // ✅ 处理普通文本内容
 227 |                         Timber
 228 |                             .tag("PHASE-DEBUG")
 229 |                             .e("📄 文本归属: ${event.text.length}字符 → phase: $targetPhaseId")
 230 | 
 231 |                         listOf(
 232 |                             ThinkingEvent.PhaseDelta(
 233 |                                 id = targetPhaseId, // 🔥 严格归属：只使用明确的phaseId
 234 |                                 ordinal = System.nanoTime(),
 235 |                                 title = null,
 236 |                                 append = event.text,
 237 |                             ),
 238 |                         )
 239 |                     }
 240 |                 }
 241 | 
 242 |                 is SemanticEvent.TagClosed ->
 243 |                     when (event.name.lowercase()) {
 244 |                         "think" -> {
 245 |                             // 🔥 【Phase转换控制】</think>标签，标记perthink完成
 246 |                             Timber.tag("PHASE-DEBUG").e("✅ </think>检测到，标记perthink完成")
 247 |                             listOf(
 248 |                                 ThinkingEvent.PhaseDelta(
 249 |                                     id = "perthink",
 250 |                                     ordinal = System.nanoTime(),
 251 |                                     title = null,
 252 |                                     append = "",
 253 |                                     isComplete = true, // 🔥 关键：标记perthink完成
 254 |                                 ),
 255 |                                 // 🔥 【perthink特殊处理】不立即发送PhaseRenderingComplete，等待UI层检测
 256 |                                 // perthink需要特殊的完成时机控制
 257 |                             )
 258 |                         }
 259 | 
 260 |                         "thinking" -> {
 261 |                             // 🔥 【15秒超时修复】标记thinking已结束，但不立即启动15秒倒计时
 262 |                             thinkingEnded = true
 263 |                             Timber
 264 |                                 .tag(TBTags.MAP)
 265 |                                 .i("🔥 [15秒超时修复] </thinking>检测到，标记thinking已结束，messageId=$currentMessageId")
 266 | 
 267 |                             listOf(
 268 |                                 ThinkingEvent.ThinkingFinished(
 269 |                                     summary = "思考完成",
 270 |                                     finalMarkdown = "",
 271 |                                     messageId = currentMessageId, // 🔥 【多轮对话修复】传递正确的messageId
 272 |                                 ),
 273 |                                 ThinkingEvent.CompletionCollapseTriggered,
 274 |                                 ThinkingEvent.StepTransition(
 275 |                                     targetStep = ThinkingStep.CompletionCollapse,
 276 |                                     trigger = "</thinking>标签检测",
 277 |                                 ),
 278 |                             )
 279 |                         }
 280 | 
 281 |                         "phase" -> {
 282 |                             // ✅ 【Phase文本管理修复】处理phase关闭，标记当前phase完成
 283 |                             val closedPhaseId = currentPhaseId
 284 |                             Timber
 285 |                                 .tag("PHASE-DEBUG")
 286 |                                 .e("🔍 </phase>标签检测 | currentPhaseId=$closedPhaseId | thinkingEnded=$thinkingEnded")
 287 | 
 288 |                             if (closedPhaseId != null) {
 289 |                                 Timber.tag("PHASE-DEBUG").e("✅ </phase>检测到，标记phase完成: $closedPhaseId")
 290 |                                 // 注意：不立即清空currentPhaseId，等待下一个phase开始
 291 |                                 // 这确保了在phase切换间隙中的文本仍有归属
 292 | 
 293 |                                 val events = mutableListOf<ThinkingEvent>()
 294 | 
 295 |                                 // 标记phase完成
 296 |                                 val completionEvent =
 297 |                                     ThinkingEvent.PhaseDelta(
 298 |                                         id = closedPhaseId,
 299 |                                         ordinal = System.nanoTime(),
 300 |                                         title = null,
 301 |                                         append = "",
 302 |                                         isComplete = true, // 标记phase完成
 303 |                                     )
 304 |                                 events.add(completionEvent)
 305 | 
 306 |                                 // 🔥 【关键修复】立即发送PhaseRenderingComplete事件，不依赖UI层异步检测
 307 |                                 events.add(ThinkingEvent.PhaseRenderingComplete(closedPhaseId))
 308 |                                 Timber.tag("PHASE-DEBUG").e("🔥 立即发送PhaseRenderingComplete: id=$closedPhaseId")
 309 | 
 310 |                                 // 🔥 【超时机制优化】只在确认是最后一个phase时才启动兜底倒计时
 311 |                                 if (thinkingEnded && isLikelyLastPhase(closedPhaseId)) {
 312 |                                     Timber
 313 |                                         .tag("PHASE-DEBUG")
 314 |                                         .e("⏰ 确认最后一个phase完成，启动兜底倒计时: $closedPhaseId")
 315 |                                     events.add(ThinkingEvent.ThinkingFinalDetected)
 316 |                                 } else if (thinkingEnded) {
 317 |                                     Timber
 318 |                                         .tag("PHASE-DEBUG")
 319 |                                         .w("⚠️ thinking已结束但可能还有更多phase，暂不启动倒计时: $closedPhaseId")
 320 |                                 }
 321 | 
 322 |                                 events
 323 |                             } else {
 324 |                                 Timber.tag("PHASE-DEBUG").e("❌ 收到</phase>但没有活跃的phase")
 325 |                                 emptyList()
 326 |                             }
 327 |                         }
 328 | 
 329 |                         "title" -> {
 330 |                             // ✅ 【Title内容混淆修复】退出waitingTitle状态
 331 |                             waitingTitle = false
 332 |                             Timber.tag(TBTags.MAP).d("🔥 [Title内容混淆修复] 退出waitingTitle状态")
 333 |                             emptyList() // title关闭不需要发送特殊事件
 334 |                         }
 335 | 
 336 |                         "final" ->
 337 |                             listOf(
 338 |                                 ThinkingEvent.ThinkingFinalDetected, // 🔥 【Phase转换控制】检测到</final>，开始15秒倒计时
 339 |                                 ThinkingEvent.FinalReady,
 340 |                             )
 341 | 
 342 |                         else -> emptyList()
 343 |                     }
 344 | 
 345 |                 is SemanticEvent.FinalArrived -> {
 346 |                     // ✅ 【Phase文本管理修复】简单处理final内容，恢复原有逻辑
 347 |                     Timber
 348 |                         .tag(TBTags.MAP)
 349 |                         .d("🔥 [Phase文本管理修复] FinalArrived事件，处理final内容，messageId=$currentMessageId")
 350 |                     listOf(
 351 |                         ThinkingEvent.FinalAnswer(
 352 |                             markdown = event.markdown,
 353 |                             sources = emptyList(), // TODO: 从markdown中提取sources
 354 |                             messageId = currentMessageId, // 🔥 【多轮对话修复】传递正确的messageId
 355 |                         ),
 356 |                         ThinkingEvent.RichTextRenderingActivated,
 357 |                         ThinkingEvent.StepTransition(
 358 |                             targetStep = ThinkingStep.RichTextRendering,
 359 |                             trigger = "final内容到达",
 360 |                         ),
 361 |                     )
 362 |                 }
 363 | 
 364 |                 is SemanticEvent.StreamFinished -> listOf(ThinkingEvent.StreamEnd)
 365 |                 is SemanticEvent.ParseErrorEvent -> listOf(ThinkingEvent.Error(event.error.message, null))
 366 |                 else -> emptyList()
 367 |             }
 368 |         }
 369 | 
 370 |         /**
 371 |          * 🔥 【循环解析修复】reset方法重置状态但保持订阅
 372 |          * 🔥 【多轮对话修复】同时重置messageId状态
 373 |          */
 374 |         fun reset() {
 375 |             rawThinkingLogger.reset()
 376 |             // 🔥 【循环解析修复】重置phaseId状态，但保持订阅运行
 377 |         currentPhaseId = null
 378 |         // ✅ 【Title内容混淆修复】重置waitingTitle状态
 379 |         waitingTitle = false
 380 |         // 🔥 【15秒超时修复】重置thinking结束状态
 381 |         thinkingEnded = false
 382 |         // 🔥 【多轮对话修复】重置messageId状态
 383 |         currentMessageId = null
 384 |         Timber.tag(TBTags.MAP).d("🔥 [循环解析修复] DomainMapper状态已重置，订阅保持运行，messageId已清理")
 385 |     }
 386 | 
 387 |     /**
 388 |      * 🔥 【超时机制优化】判断是否可能是最后一个phase
 389 |      *
 390 |      * 启发式判断逻辑：
 391 |      * 1. thinking已经结束（</thinking>已检测到）
 392 |      * 2. 当前phase ID通常是数字，且数字较大（如"3", "4", "5"等）
 393 |      * 3. 或者phase ID包含"final", "conclusion", "summary"等关键词
 394 |      *
 395 |      * 注意：这是一个启发式判断，主要目的是减少不必要的超时启动
 396 |      * 真正的超时取消逻辑在ThinkingBoxInstance中处理
 397 |      */
 398 |     private fun isLikelyLastPhase(phaseId: String?): Boolean {
 399 |         if (phaseId == null) return false
 400 | 
 401 |         // 检查是否包含结束相关的关键词
 402 |         val endingKeywords = listOf("final", "conclusion", "summary", "result", "answer")
 403 |         if (endingKeywords.any { phaseId.lowercase().contains(it) }) {
 404 |             Timber.tag("PHASE-DEBUG").d("🔍 Phase包含结束关键词: $phaseId")
 405 |             return true
 406 |         }
 407 | 
 408 |         // 检查是否是数字ID且数字较大（通常最后的phase ID是较大的数字）
 409 |         val numericId = phaseId.toIntOrNull()
 410 |         if (numericId != null && numericId >= 3) {
 411 |             Timber.tag("PHASE-DEBUG").d("🔍 Phase是较大数字ID: $phaseId")
 412 |             return true
 413 |         }
 414 | 
 415 |         // 默认情况下，如果thinking已结束，认为可能是最后一个phase
 416 |         // 这样可以启动兜底倒计时，但真正的优化在ThinkingBoxInstance中
 417 |         Timber.tag("PHASE-DEBUG").d("🔍 默认判断为可能的最后phase: $phaseId")
 418 |         return true
 419 |     }
 420 | }

```

`main\kotlin\com\example\gymbro\features\thinkingbox\domain\model\ParseState.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.domain.model
   2 | 
   3 | /**
   4 |  * ParseState - Domain 解析状态实体 (sealed class 重构)
   5 |  *
   6 |  * 支持状态机模式，匹配现有代码期望
   7 |  * 基于 631_thinkingbox_final_tree.md 要求重构
   8 |  */
   9 | sealed class ParseState {
  10 |     abstract val timestamp: Long
  11 |     abstract val confidence: Float
  12 | 
  13 |     /**
  14 |      * 解析成功状态
  15 |      */
  16 |     data class Success(
  17 |         val phases: List<ThinkingContent>,
  18 |         override val confidence: Float = 1.0f,
  19 |         override val timestamp: Long = System.currentTimeMillis(),
  20 |         val metadata: Map<String, Any> = emptyMap(),
  21 |     ) : ParseState() {
  22 | 
  23 |         fun getShortDescription(): String = "解析完成 (${phases.size} 个阶段)"
  24 | 
  25 |         fun hasErrors(): Boolean = false
  26 |         fun hasFixes(): Boolean = false
  27 | 
  28 |         fun getErrorSummary(): String = ""
  29 |         fun getFixSummary(): String = ""
  30 | 
  31 |         fun clearErrorHistory(): ParseState = this
  32 |         fun getErrorHistory(): List<ParseError> = emptyList()
  33 |     }
  34 | 
  35 |     /**
  36 |      * 解析失败状态
  37 |      */
  38 |     data class Failed(
  39 |         val error: ParseError,
  40 |         val partialContent: List<ThinkingContent> = emptyList(),
  41 |         override val confidence: Float = 0.0f,
  42 |         override val timestamp: Long = System.currentTimeMillis(),
  43 |     ) : ParseState() {
  44 | 
  45 |         fun getShortDescription(): String = "解析失败: ${error.message}"
  46 | 
  47 |         fun hasErrors(): Boolean = true
  48 |         fun hasFixes(): Boolean = false
  49 | 
  50 |         fun getErrorSummary(): String = error.getDescription()
  51 |         fun getFixSummary(): String = ""
  52 | 
  53 |         fun clearErrorHistory(): ParseState = Success(partialContent, confidence, timestamp)
  54 |         fun getErrorHistory(): List<ParseError> = listOf(error)
  55 |     }
  56 | 
  57 |     /**
  58 |      * 解析恢复状态
  59 |      */
  60 |     data class Recovered(
  61 |         val partial: List<ThinkingContent>,
  62 |         val fixes: List<ParseFix>,
  63 |         val originalError: ParseError,
  64 |         override val confidence: Float = 0.7f,
  65 |         override val timestamp: Long = System.currentTimeMillis(),
  66 |     ) : ParseState() {
  67 | 
  68 |         fun getShortDescription(): String = "已恢复 (${partial.size} 个阶段，${fixes.size} 个修复)"
  69 | 
  70 |         fun hasErrors(): Boolean = false
  71 |         fun hasFixes(): Boolean = fixes.isNotEmpty()
  72 | 
  73 |         fun getErrorSummary(): String = ""
  74 |         fun getFixSummary(): String = fixes.joinToString("; ") { it.description }
  75 | 
  76 |         fun clearErrorHistory(): ParseState = Success(partial, confidence, timestamp)
  77 |         fun getErrorHistory(): List<ParseError> = listOf(originalError)
  78 |     }
  79 | 
  80 |     /**
  81 |      * 解析中状态
  82 |      */
  83 |     data class Parsing(
  84 |         val currentContent: List<ThinkingContent> = emptyList(),
  85 |         val progress: Float = 0f,
  86 |         override val confidence: Float = 0.5f,
  87 |         override val timestamp: Long = System.currentTimeMillis(),
  88 |     ) : ParseState() {
  89 | 
  90 |         fun getShortDescription(): String = "解析中... (${(progress * 100).toInt()}%)"
  91 | 
  92 |         fun hasErrors(): Boolean = false
  93 |         fun hasFixes(): Boolean = false
  94 | 
  95 |         fun getErrorSummary(): String = ""
  96 |         fun getFixSummary(): String = ""
  97 | 
  98 |         fun clearErrorHistory(): ParseState = this
  99 |         fun getErrorHistory(): List<ParseError> = emptyList()
 100 |     }
 101 | 
 102 |     /**
 103 |      * 空闲状态
 104 |      */
 105 |     data class Idle(
 106 |         override val confidence: Float = 1.0f,
 107 |         override val timestamp: Long = System.currentTimeMillis(),
 108 |     ) : ParseState() {
 109 | 
 110 |         fun getShortDescription(): String = "等待解析"
 111 | 
 112 |         fun hasErrors(): Boolean = false
 113 |         fun hasFixes(): Boolean = false
 114 | 
 115 |         fun getErrorSummary(): String = ""
 116 |         fun getFixSummary(): String = ""
 117 | 
 118 |         fun clearErrorHistory(): ParseState = this
 119 |         fun getErrorHistory(): List<ParseError> = emptyList()
 120 |     }
 121 | 
 122 |     companion object {
 123 |         /**
 124 |          * 创建初始状态
 125 |          */
 126 |         fun initial(): ParseState = Idle()
 127 | 
 128 |         /**
 129 |          * 创建完成状态
 130 |          */
 131 |         fun completed(content: List<ThinkingContent>): ParseState = Success(content)
 132 | 
 133 |         /**
 134 |          * 创建错误状态
 135 |          */
 136 |         fun error(error: ParseError): ParseState = Failed(error)
 137 |     }
 138 | }
 139 | 
 140 | /**
 141 |  * ParseState 扩展方法
 142 |  * 为 sealed class 提供统一的方法接口，匹配现有代码期望
 143 |  */
 144 | 
 145 | /**
 146 |  * 检查是否有错误
 147 |  */
 148 | fun ParseState.hasErrors(): Boolean = when (this) {
 149 |     is ParseState.Failed -> true
 150 |     else -> false
 151 | }
 152 | 
 153 | /**
 154 |  * 检查是否有修复
 155 |  */
 156 | fun ParseState.hasFixes(): Boolean = when (this) {
 157 |     is ParseState.Recovered -> true
 158 |     else -> false
 159 | }
 160 | 
 161 | /**
 162 |  * 获取错误摘要
 163 |  */
 164 | fun ParseState.getErrorSummary(): String = when (this) {
 165 |     is ParseState.Failed -> error.getDescription()
 166 |     else -> ""
 167 | }
 168 | 
 169 | /**
 170 |  * 获取修复摘要
 171 |  */
 172 | fun ParseState.getFixSummary(): String = when (this) {
 173 |     is ParseState.Recovered -> fixes.joinToString("; ") { it.description }
 174 |     else -> ""
 175 | }
 176 | 
 177 | /**
 178 |  * 获取简短描述
 179 |  */
 180 | fun ParseState.getShortDescription(): String = when (this) {
 181 |     is ParseState.Success -> getShortDescription()
 182 |     is ParseState.Failed -> getShortDescription()
 183 |     is ParseState.Recovered -> getShortDescription()
 184 |     is ParseState.Parsing -> getShortDescription()
 185 |     is ParseState.Idle -> getShortDescription()
 186 | }
 187 | 
 188 | /**
 189 |  * 清除错误历史
 190 |  */
 191 | fun ParseState.clearErrorHistory(): ParseState = when (this) {
 192 |     is ParseState.Failed -> ParseState.Success(partialContent)
 193 |     is ParseState.Recovered -> ParseState.Success(partial)
 194 |     else -> this
 195 | }
 196 | 
 197 | /**
 198 |  * 获取错误历史
 199 |  */
 200 | fun ParseState.getErrorHistory(): List<ParseError> = when (this) {
 201 |     is ParseState.Failed -> listOf(error)
 202 |     is ParseState.Recovered -> listOf(originalError)
 203 |     else -> emptyList()
 204 | }
 205 | 
 206 | /**
 207 |  * 获取内容
 208 |  */
 209 | fun ParseState.content(): List<ThinkingContent> = when (this) {
 210 |     is ParseState.Success -> phases
 211 |     is ParseState.Failed -> partialContent
 212 |     is ParseState.Recovered -> partial
 213 |     is ParseState.Parsing -> currentContent
 214 |     is ParseState.Idle -> emptyList()
 215 | }
 216 | 
 217 | /**
 218 |  * 解析修复信息
 219 |  */
 220 | data class ParseFix(
 221 |     val type: FixType,
 222 |     val description: String,
 223 |     val appliedAt: Long = System.currentTimeMillis(),
 224 | )
 225 | 
 226 | /**
 227 |  * 解析错误信息
 228 |  */
 229 | data class ParseError(
 230 |     val type: ErrorType,
 231 |     val message: String,
 232 |     val position: Int = -1,
 233 |     val occurredAt: Long = System.currentTimeMillis(),
 234 | ) {
 235 |     fun getDescription(): String = message
 236 | }
 237 | 
 238 | /**
 239 |  * 修复类型
 240 |  */
 241 | enum class FixType {
 242 |     TAG_CORRECTION,
 243 |     CONTENT_SANITIZATION,
 244 |     STRUCTURE_REPAIR,
 245 | }
 246 | 
 247 | /**
 248 |  * 错误类型
 249 |  */
 250 | enum class ErrorType {
 251 |     MALFORMED_XML,
 252 |     INVALID_TAG,
 253 |     CONTENT_OVERFLOW,
 254 |     NETWORK_ERROR,
 255 |     PARSING_ERROR,
 256 |     VALIDATION_ERROR,
 257 |     TIMEOUT_ERROR,
 258 |     UNKNOWN_ERROR,
 259 |     UNKNOWN,
 260 | }
 261 | 
 262 | /**
 263 |  * Guardrail 错误
 264 |  */
 265 | data class GuardrailError(
 266 |     val code: String,
 267 |     val message: String,
 268 |     val severity: ErrorSeverity = ErrorSeverity.MEDIUM,
 269 | )
 270 | 
 271 | /**
 272 |  * 错误严重程度
 273 |  */
 274 | enum class ErrorSeverity {
 275 |     LOW, MEDIUM, HIGH, CRITICAL
 276 | }
 277 | 
 278 | /**
 279 |  * 检查是否为终端状态
 280 |  */
 281 | fun ParseState.isTerminal(): Boolean = when (this) {
 282 |     is ParseState.Success, is ParseState.Failed -> true
 283 |     else -> false
 284 | }
 285 | 
 286 | /**
 287 |  * 检查是否可恢复
 288 |  */
 289 | fun ParseState.isRecoverable(): Boolean = when (this) {
 290 |     is ParseState.Failed -> partialContent.isNotEmpty()
 291 |     is ParseState.Recovered -> true
 292 |     else -> false
 293 | }

```

`main\kotlin\com\example\gymbro\features\thinkingbox\domain\model\TagContext.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.domain.model
   2 | 
   3 | /**
   4 |  * TagContext - XML 标签上下文
   5 |  *
   6 |  * 用于 ThinkingPhaseExtractor 中的标签解析上下文
   7 |  */
   8 | data class TagContext(
   9 |     val tagName: String,
  10 |     val attributes: Map<String, String> = emptyMap(),
  11 |     val depth: Int = 0,
  12 |     val parentTag: String? = null,
  13 |     val position: Int = 0,
  14 | ) {
  15 | 
  16 |     /**
  17 |      * 是否为思考标签
  18 |      */
  19 |     fun isThinkingTag(): Boolean {
  20 |         return tagName in THINKING_TAGS
  21 |     }
  22 | 
  23 |     /**
  24 |      * 是否为阶段标签
  25 |      */
  26 |     fun isPhaseTag(): Boolean {
  27 |         return tagName in PHASE_TAGS
  28 |     }
  29 | 
  30 |     /**
  31 |      * 是否为最终答案标签
  32 |      */
  33 |     fun isFinalTag(): Boolean {
  34 |         return tagName in FINAL_TAGS
  35 |     }
  36 | 
  37 |     companion object {
  38 |         val THINKING_TAGS = setOf("think", "thinking", "thought")
  39 |         val PHASE_TAGS = setOf("phase", "step", "analysis", "reasoning")
  40 |         val FINAL_TAGS = setOf("final", "answer", "conclusion")
  41 |     }
  42 | }

```

`main\kotlin\com\example\gymbro\features\thinkingbox\domain\model\ThinkingContent.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.domain.model
   2 | 
   3 | import androidx.compose.ui.text.AnnotatedString
   4 | 
   5 | /**
   6 |  * ThinkingContent - Domain 内容载体实体
   7 |  *
   8 |  * 表示思考内容的统一载体，支持多种内容类型
   9 |  * 基于 631_thinkingbox_final_tree.md 要求创建
  10 |  */
  11 | sealed class ThinkingContent {
  12 |     abstract val id: String
  13 |     abstract val timestamp: Long
  14 | 
  15 |     /**
  16 |      * 转换为 AnnotatedString
  17 |      */
  18 |     abstract fun toAnnotatedString(): AnnotatedString
  19 | 
  20 |     /**
  21 |      * 文本内容
  22 |      */
  23 |     data class Text(
  24 |         override val id: String,
  25 |         override val timestamp: Long = System.currentTimeMillis(),
  26 |         val content: String,
  27 |         val formatting: TextFormatting = TextFormatting.PLAIN,
  28 |     ) : ThinkingContent() {
  29 | 
  30 |         override fun toAnnotatedString(): AnnotatedString {
  31 |             return when (formatting) {
  32 |                 TextFormatting.PLAIN -> AnnotatedString(content)
  33 |                 TextFormatting.MARKDOWN -> {
  34 |                     // 简单的 Markdown 转换，实际应该使用专门的解析器
  35 |                     AnnotatedString(content)
  36 |                 }
  37 |                 TextFormatting.HTML -> {
  38 |                     // HTML 转换，实际应该使用专门的解析器
  39 |                     AnnotatedString(content)
  40 |                 }
  41 |             }
  42 |         }
  43 |     }
  44 | 
  45 |     /**
  46 |      * 思考阶段内容
  47 |      */
  48 |     data class Phase(
  49 |         override val id: String = java.util.UUID.randomUUID().toString(),
  50 |         override val timestamp: Long = System.currentTimeMillis(),
  51 |         val title: String,
  52 |         val content: String,
  53 |         val stage: ThinkingStage = ThinkingStage.ANALYZING,
  54 |         val keyPoints: List<String> = emptyList(),
  55 |         val status: PhaseStatus = PhaseStatus.IN_PROGRESS,
  56 |     ) : ThinkingContent() {
  57 | 
  58 |         override fun toAnnotatedString(): AnnotatedString {
  59 |             val builder = StringBuilder()
  60 |             builder.append("【$title】\n")
  61 |             builder.append(content)
  62 |             if (keyPoints.isNotEmpty()) {
  63 |                 builder.append("\n\n要点：\n")
  64 |                 keyPoints.forEach { point ->
  65 |                     builder.append("• $point\n")
  66 |                 }
  67 |             }
  68 |             return AnnotatedString(builder.toString())
  69 |         }
  70 |     }
  71 | 
  72 |     /**
  73 |      * 最终答案内容
  74 |      */
  75 |     data class FinalAnswer(
  76 |         override val id: String = java.util.UUID.randomUUID().toString(),
  77 |         override val timestamp: Long = System.currentTimeMillis(),
  78 |         val content: String,
  79 |         val summary: String = "",
  80 |         val confidence: Float = 1.0f,
  81 |     ) : ThinkingContent() {
  82 | 
  83 |         override fun toAnnotatedString(): AnnotatedString {
  84 |             val builder = StringBuilder()
  85 |             if (summary.isNotEmpty()) {
  86 |                 builder.append("摘要：$summary\n\n")
  87 |             }
  88 |             builder.append(content)
  89 |             return AnnotatedString(builder.toString())
  90 |         }
  91 |     }
  92 | 
  93 |     /**
  94 |      * 错误内容
  95 |      */
  96 |     data class Error(
  97 |         override val id: String = java.util.UUID.randomUUID().toString(),
  98 |         override val timestamp: Long = System.currentTimeMillis(),
  99 |         val message: String,
 100 |         val errorType: ErrorType = ErrorType.UNKNOWN_ERROR,
 101 |         val isRecoverable: Boolean = true,
 102 |     ) : ThinkingContent() {
 103 | 
 104 |         override fun toAnnotatedString(): AnnotatedString {
 105 |             return AnnotatedString("错误：$message")
 106 |         }
 107 |     }
 108 | 
 109 |     /**
 110 |      * 元数据内容
 111 |      */
 112 |     data class Metadata(
 113 |         override val id: String = java.util.UUID.randomUUID().toString(),
 114 |         override val timestamp: Long = System.currentTimeMillis(),
 115 |         val key: String,
 116 |         val value: String,
 117 |         val isVisible: Boolean = false,
 118 |     ) : ThinkingContent() {
 119 | 
 120 |         override fun toAnnotatedString(): AnnotatedString {
 121 |             return if (isVisible) {
 122 |                 AnnotatedString("$key: $value")
 123 |             } else {
 124 |                 AnnotatedString("")
 125 |             }
 126 |         }
 127 |     }
 128 | }
 129 | 
 130 | /**
 131 |  * 文本格式化类型
 132 |  */
 133 | enum class TextFormatting {
 134 |     PLAIN,
 135 |     MARKDOWN,
 136 |     HTML,
 137 | }
 138 | 
 139 | /**
 140 |  * 阶段状态 - 重构版本
 141 |  * 匹配现有代码期望，提供向后兼容
 142 |  */
 143 | enum class PhaseStatus {
 144 |     STARTING, // 阶段开始
 145 |     ACTIVE, // 阶段进行中
 146 |     IN_PROGRESS, // 阶段进行中 (别名)
 147 |     COMPLETED, // 阶段完成
 148 |     FAILED, // 阶段失败
 149 |     UNKNOWN, // 未知状态
 150 |     ;
 151 | 
 152 |     companion object {
 153 |         /**
 154 |          * 旧值到新值的映射扩展函数
 155 |          * 保持测试兼容性
 156 |          */
 157 |         fun fromLegacy(legacy: String): PhaseStatus = when (legacy) {
 158 |             "NOT_STARTED" -> STARTING
 159 |             "IN_PROGRESS" -> ACTIVE
 160 |             "COMPLETED" -> COMPLETED
 161 |             "FAILED" -> FAILED
 162 |             else -> STARTING
 163 |         }
 164 | 
 165 |         /**
 166 |          * 新值到旧值的映射
 167 |          * 用于向后兼容
 168 |          */
 169 |         fun PhaseStatus.toLegacy(): String = when (this) {
 170 |             STARTING -> "NOT_STARTED"
 171 |             ACTIVE, IN_PROGRESS -> "IN_PROGRESS"
 172 |             COMPLETED -> "COMPLETED"
 173 |             FAILED -> "FAILED"
 174 |             UNKNOWN -> "UNKNOWN"
 175 |         }
 176 |     }
 177 | }
 178 | 
 179 | // ErrorType 已在 ParseState.kt 中定义，这里不需要重复定义
 180 | 
 181 | /**
 182 |  * 思考阶段类型 - 新架构实现
 183 |  * 替代原有的 ThinkingPhase 枚举，与 ParseState 架构对齐
 184 |  */
 185 | enum class ThinkingStage {
 186 |     ANALYZING, // 分析阶段
 187 |     PLANNING, // 规划阶段
 188 |     EXECUTING, // 执行阶段
 189 |     REVIEWING, // 审查阶段
 190 |     CONCLUDING, // 总结阶段
 191 |     FINAL, // 最终阶段
 192 |     ;
 193 | 
 194 |     /**
 195 |      * 获取阶段显示名称
 196 |      */
 197 |     fun getDisplayName(): String = when (this) {
 198 |         ANALYZING -> "分析阶段"
 199 |         PLANNING -> "规划阶段"
 200 |         EXECUTING -> "执行阶段"
 201 |         REVIEWING -> "审查阶段"
 202 |         CONCLUDING -> "总结阶段"
 203 |         FINAL -> "最终结果"
 204 |     }
 205 | 
 206 |     companion object {
 207 |         /**
 208 |          * 从字符串解析阶段
 209 |          */
 210 |         fun fromString(value: String): ThinkingStage {
 211 |             return when (value.uppercase()) {
 212 |                 "PLAN", "PLANNING" -> PLANNING
 213 |                 "ANALYZE", "ANALYSIS" -> ANALYZING
 214 |                 "IMPLEMENT", "IMPLEMENTATION", "EXECUTE", "EXECUTING" -> EXECUTING
 215 |                 "REVIEW", "REVIEWING" -> REVIEWING
 216 |                 "CONCLUDE", "CONCLUSION", "CONCLUDING" -> CONCLUDING
 217 |                 "FINAL" -> FINAL
 218 |                 else -> ANALYZING // 默认
 219 |             }
 220 |         }
 221 | 
 222 |         /**
 223 |          * 🔥 安全解析阶段，不认识的返回null
 224 |          */
 225 |         fun safeValueOf(name: String): ThinkingStage? {
 226 |             return values().firstOrNull { it.name == name.uppercase() }
 227 |         }
 228 |     }
 229 | }
 230 | 
 231 | // 🔄 LEGACY MIGRATION: ThinkingPhase 和 ParsedThinkingPhase 已迁移到 ParseState 和 ThinkingContent
 232 | // 保留类型别名以确保向后兼容，将在后续版本中移除
 233 | 
 234 | /**
 235 |  * @deprecated 使用 ParseState 替代。此别名将在下个版本中移除。
 236 |  */
 237 | @Deprecated(
 238 |     message = "使用 ParseState 替代 ThinkingPhase",
 239 |     replaceWith = ReplaceWith(
 240 |         "ParseState",
 241 |         "com.example.gymbro.features.thinkingbox.domain.model.ParseState",
 242 |     ),
 243 |     level = DeprecationLevel.WARNING,
 244 | )
 245 | typealias ThinkingPhase = String
 246 | 
 247 | /**
 248 |  * @deprecated 使用 ThinkingContent 替代。此别名将在下个版本中移除。
 249 |  */
 250 | @Deprecated(
 251 |     message = "使用 ThinkingContent 替代 ParsedThinkingPhase",
 252 |     replaceWith = ReplaceWith(
 253 |         "ThinkingContent",
 254 |         "com.example.gymbro.features.thinkingbox.domain.model.ThinkingContent",
 255 |     ),
 256 |     level = DeprecationLevel.WARNING,
 257 | )
 258 | data class ParsedThinkingPhase(
 259 |     val phase: String,
 260 |     val title: String,
 261 |     val content: String,
 262 |     val keyPoints: List<String> = emptyList(),
 263 |     val startTime: Long = System.currentTimeMillis(),
 264 |     val endTime: Long? = null,
 265 | )
 266 | 
 267 | /**
 268 |  * 思考摘要
 269 |  */
 270 | data class ThinkingSummary(
 271 |     val totalPhases: Int,
 272 |     val completedPhases: Int,
 273 |     val mainPoints: List<String>,
 274 |     val conclusion: String,
 275 |     val confidence: Float = 1.0f,
 276 | )
 277 | 
 278 | /**
 279 |  * 阶段摘要
 280 |  */
 281 | data class PhaseSummary(
 282 |     val phase: ThinkingPhase,
 283 |     val title: String,
 284 |     val keyPoints: List<String>,
 285 |     val duration: Long = 0L,
 286 | )
 287 | 
 288 | /**
 289 |  * 阶段渲染状态 - 重构版本
 290 |  * 匹配现有代码调用期望，提供兼容构造函数
 291 |  */
 292 | data class PhaseRenderState(
 293 |     val phase: ParsedThinkingPhase,
 294 |     val isVisible: Boolean = true,
 295 |     val isExpanded: Boolean = false,
 296 |     val renderProgress: Float = 0f,
 297 |     // 新增字段匹配调用期望
 298 |     val status: PhaseStatus = PhaseStatus.STARTING,
 299 |     val content: List<String> = emptyList(),
 300 |     val startTime: Long = System.currentTimeMillis(),
 301 |     val keyPoints: List<String> = emptyList(),
 302 |     val progress: Float = 0f,
 303 |     val title: String = "",
 304 | ) {
 305 |     /**
 306 |      * 兼容构造函数 - 匹配现有调用方期望
 307 |      */
 308 |     constructor(
 309 |         phase: ThinkingPhase,
 310 |         status: PhaseStatus,
 311 |         startTime: Long,
 312 |         content: String,
 313 |         keyPoints: List<String>,
 314 |         progress: Float,
 315 |         title: String = "",
 316 |     ) : this(
 317 |         phase = ParsedThinkingPhase(
 318 |             phase = phase,
 319 |             title = title,
 320 |             content = content,
 321 |             keyPoints = keyPoints,
 322 |             startTime = startTime,
 323 |         ),
 324 |         status = status,
 325 |         content = listOf(content),
 326 |         startTime = startTime,
 327 |         keyPoints = keyPoints,
 328 |         progress = progress,
 329 |         title = title,
 330 |     )
 331 | }
 332 | 
 333 | /**
 334 |  * 提取的阶段 - sealed class 重构
 335 |  * 支持状态机模式，匹配现有代码期望
 336 |  */
 337 | sealed class ExtractedPhase {
 338 |     abstract val phase: ThinkingPhase
 339 |     abstract val timestamp: Long
 340 | 
 341 |     /**
 342 |      * 阶段开始事件
 343 |      */
 344 |     data class PhaseStarted(
 345 |         override val phase: ThinkingPhase,
 346 |         override val timestamp: Long = System.currentTimeMillis(),
 347 |         val title: String = "",
 348 |         val metadata: Map<String, String> = emptyMap(),
 349 |     ) : ExtractedPhase()
 350 | 
 351 |     /**
 352 |      * 阶段进度事件
 353 |      */
 354 |     data class PhaseProgress(
 355 |         override val phase: ThinkingPhase,
 356 |         override val timestamp: Long = System.currentTimeMillis(),
 357 |         val title: String,
 358 |         val content: String,
 359 |         val keyPoints: List<String> = emptyList(),
 360 |         val progress: Float = 0f,
 361 |     ) : ExtractedPhase()
 362 | 
 363 |     /**
 364 |      * 阶段完成事件
 365 |      */
 366 |     data class PhaseCompleted(
 367 |         override val phase: ThinkingPhase,
 368 |         override val timestamp: Long = System.currentTimeMillis(),
 369 |         val title: String,
 370 |         val content: String,
 371 |         val keyPoints: List<String> = emptyList(),
 372 |         val completedPhases: List<ThinkingPhase> = emptyList(),
 373 |     ) : ExtractedPhase()
 374 | 
 375 |     /**
 376 |      * 提取完成事件
 377 |      */
 378 |     data class ExtractionCompleted(
 379 |         override val phase: ThinkingPhase = "FINAL",
 380 |         override val timestamp: Long = System.currentTimeMillis(),
 381 |         val allPhases: List<ExtractedPhase> = emptyList(),
 382 |         val summary: String = "",
 383 |     ) : ExtractedPhase()
 384 | }
 385 | 
 386 | /**
 387 |  * 渐进式渲染状态 - 重构版本
 388 |  * 补全调用期望的属性字段
 389 |  */
 390 | data class ProgressiveRenderState(
 391 |     val allPhases: List<ExtractedPhase> = emptyList(),
 392 |     val currentPhaseIndex: Int = 0,
 393 |     val isComplete: Boolean = false,
 394 |     // 新增字段匹配调用期望
 395 |     val currentNodeIndex: Int = 0,
 396 |     val totalNodes: Int = 0,
 397 |     val isCompleted: Boolean = false,
 398 |     val isProgressing: Boolean = false,
 399 |     val currentPhase: PhaseRenderState? = null,
 400 |     val completedPhases: List<ThinkingPhase> = emptyList(),
 401 |     val isCollapsed: Boolean = false,
 402 |     val summary: ThinkingSummary? = null,
 403 | )

```

`main\kotlin\com\example\gymbro\features\thinkingbox\domain\model\events\SemanticEvent.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.domain.model.events
   2 | 
   3 | /**
   4 |  * SemanticEvent - 语义事件定义（合并ParsingEvent内容）
   5 |  *
   6 |  * 基于 627规范统一.md 第24-36行要求实现
   7 |  * 由 StreamingThinkingMLParser 输出，供 DomainMapper 消费
   8 |  *
   9 |  * 设计原则：
  10 |  * - Parser层输出的语义事件
  11 |  * - 绝不关心UI，纯解析层事件
  12 |  * - 支持流式处理和XML标签解析
  13 |  */
  14 | sealed interface SemanticEvent {
  15 | 
  16 |     /**
  17 |      * XML 标签开始事件
  18 |      * @param name 标签名称 (如 "thinking", "title", "final")
  19 |      * @param attrs 标签属性映射
  20 |      */
  21 |     data class TagOpened(
  22 |         val name: String,
  23 |         val attrs: Map<String, String> = emptyMap(),
  24 |     ) : SemanticEvent
  25 | 
  26 |     /**
  27 |      * 文本内容发现事件
  28 |      * @param text 文本内容（已过滤空白字符）
  29 |      */
  30 |     data class TextChunk(val text: String) : SemanticEvent
  31 | 
  32 |     // 🔥 数据流统一：PreThinkChunk定义移至下方，避免重复
  33 | 
  34 |     /**
  35 |      * XML 标签结束事件
  36 |      * @param name 标签名称
  37 |      * @param attrs 标签属性（用于传递完整性标记等）
  38 |      */
  39 |     data class TagClosed(
  40 |         val name: String,
  41 |         val attrs: Map<String, String> = emptyMap()
  42 |     ) : SemanticEvent
  43 | 
  44 |     /**
  45 |      * 原始思考内容事件
  46 |      *
  47 |      * 用于保留<think>...</think>内的完整推理草稿
  48 |      * 供日志/诊断/复盘使用，不参与UI渲染
  49 |      */
  50 |     data class RawThinking(val content: String) : SemanticEvent
  51 | 
  52 |     /**
  53 |      * 原始思考内容块事件
  54 |      *
  55 |      * 用于流式处理<think>...</think>内的内容块
  56 |      * 供实时日志记录使用，不参与UI渲染
  57 |      */
  58 |     data class RawThinkingChunk(val text: String) : SemanticEvent
  59 | 
  60 |     /**
  61 |      * 预思考内容块事件
  62 |      *
  63 |      * 🔥 关键事件：用于<think>...</think>内容的UI显示
  64 |      * 与RawThinkingChunk不同，这个事件会触发UI更新
  65 |      * 生成ThinkingEvent.PreThinkDelta → UiState.preThinking
  66 |      */
  67 |     data class PreThinkChunk(val text: String) : SemanticEvent
  68 | 
  69 |     /**
  70 |      * 阶段替换事件
  71 |      *
  72 |      * 🔥 FSM架构：对应完整的<phase>...</phase>内容
  73 |      * 只有complete=true的阶段才会发送此事件
  74 |      */
  75 |     data class PhaseReplace(
  76 |         val phase: com.example.gymbro.features.thinkingbox.domain.interfaces.PhaseUi
  77 |     ) : SemanticEvent
  78 | 
  79 |     /**
  80 |      * 最终内容到达事件
  81 |      *
  82 |      * 🔥 FSM架构：对应<final>...</final>内容
  83 |      */
  84 |     data class FinalArrived(
  85 |         val markdown: String
  86 |     ) : SemanticEvent
  87 | 
  88 |     /**
  89 |      * 原始思考内容结束事件
  90 |      *
  91 |      * 标记</think>标签的结束，触发日志关闭
  92 |      */
  93 |     object RawThinkingClosed : SemanticEvent
  94 | 
  95 |     /**
  96 |      * 流结束事件
  97 |      * 表示整个 token 流处理完成
  98 |      */
  99 |     data class StreamFinished(val time: java.time.Instant = java.time.Instant.now()) : SemanticEvent
 100 | 
 101 |     /**
 102 |      * Function Call检测事件
 103 |      * 
 104 |      * 🔥 实时检测AI流式输出中的Function Call指令
 105 |      * 供AiCoachViewModel立即处理和显示Function Call结果
 106 |      */
 107 |     data class FunctionCallDetected(
 108 |         val functionName: String,
 109 |         val arguments: String? = null,
 110 |         val isComplete: Boolean = false
 111 |     ) : SemanticEvent
 112 | 
 113 |     /**
 114 |      * 解析错误事件
 115 |      * @param error 错误详情
 116 |      */
 117 |     data class ParseErrorEvent(
 118 |         val error: com.example.gymbro.features.thinkingbox.domain.model.ParseError,
 119 |     ) : SemanticEvent
 120 | }

```

`main\kotlin\com\example\gymbro\features\thinkingbox\domain\model\events\ThinkingEvent.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.domain.model.events
   2 | 
   3 | import kotlinx.collections.immutable.PersistentMap
   4 | import kotlinx.collections.immutable.persistentMapOf
   5 | 
   6 | /**
   7 |  * ThinkingEvent - 升级包事件模型
   8 |  *
   9 |  * 基于用户提供的「一站式升级包」设计
  10 |  * 实现单一事件流 → Reducer → 不可变状态的架构
  11 |  *
  12 |  * 设计原则：
  13 |  * - 单一真理源：所有状态变化通过事件驱动
  14 |  * - 不可变思想树：每个事件产生新的状态
  15 |  * - 易于调试：清晰的事件类型和数据结构
  16 |  * - 动画友好：支持增量更新和平滑过渡
  17 |  */
  18 | sealed interface ThinkingEvent {
  19 | 
  20 |     /**
  21 |      * 开始思考事件
  22 |      *
  23 |      * @param id 思考会话的唯一标识
  24 |      * @param messageId AI消息ID，用于History落库关联
  25 |      */
  26 |     data class Start(val id: String, val messageId: String? = null) : ThinkingEvent
  27 | 
  28 |     /**
  29 |      * 思考阶段增量事件
  30 |      *
  31 |      * 核心事件：每 10-20ms 收到一次，用于增量构建思考内容
  32 |      *
  33 |      * @param id 阶段唯一标识
  34 |      * @param ordinal 子序号，用于排序和去重
  35 |      * @param title 阶段标题（可选，首次设置后不变）
  36 |      * @param append 追加的文本内容
  37 |      */
  38 |     data class PhaseDelta(
  39 |         val id: String, // phase id
  40 |         val ordinal: Long, // 🔥 627目标交付：换成Long避免溢出
  41 |         val title: String?, // 阶段标题
  42 |         val append: String, // 追加文本
  43 |         val isComplete: Boolean = false, // 🔥 用户方案：Phase完整性标记
  44 |     ) : ThinkingEvent
  45 | 
  46 |     /**
  47 |      * 预思考内容增量事件
  48 |      *
  49 |      * 用于<think>标签内容的灰度预览显示
  50 |      * @param text 追加的预思考文本内容
  51 |      */
  52 |     data class PreThinkDelta(val text: String) : ThinkingEvent
  53 | 
  54 |     /**
  55 |      * 🔥 【v7新增】通用内容块事件
  56 |      *
  57 |      * 用于处理所有文本内容，由Reducer根据当前状态决定如何处理
  58 |      * （标题更新或正文内容追加）
  59 |      *
  60 |      * @param text 文本内容
  61 |      */
  62 |     data class ContentChunk(val text: String) : ThinkingEvent
  63 | 
  64 |     /**
  65 |      * 清空预思考内容事件
  66 |      *
  67 |      * 用于在进入正式思考阶段时清空预思考内容，防止重复显示
  68 |      */
  69 |     object PreThinkClear : ThinkingEvent
  70 | 
  71 |     /**
  72 |      * 阶段替换事件
  73 |      *
  74 |      * 用于解决首 Phase 卡延迟渲染问题
  75 |      * 将默认"思考中..."phase 替换为真正的 Phase 1
  76 |      *
  77 |      * @param oldId 要替换的旧阶段ID（通常是"thinking"）
  78 |      * @param newId 新的阶段ID（真正的phase id）
  79 |      * @param title 新阶段的标题（可选）
  80 |      * @param content 初始内容（默认为空）
  81 |      */
  82 |     data class PhaseReplace(
  83 |         val oldId: String,
  84 |         val newId: String,
  85 |         val title: String? = null,
  86 |         val content: String = "",
  87 |         val messageId: String? = null // 🔥 新增：用于History落库
  88 |     ) : ThinkingEvent
  89 | 
  90 |     /**
  91 |      * 最终答案事件
  92 |      *
  93 |      * 当检测到 <final> 标签时触发
  94 |      *
  95 |      * @param markdown 最终答案的Markdown内容
  96 |      * @param sources 相关来源链接列表（Phase 2新增）
  97 |      * @param messageId AI消息ID，用于History落库关联
  98 |      */
  99 |     data class FinalAnswer(
 100 |         val markdown: String,
 101 |         val sources: List<String> = emptyList(),
 102 |         val messageId: String? = null // 🔥 新增：用于History落库
 103 |     ) : ThinkingEvent
 104 | 
 105 |     /**
 106 |      * 🔥 双事件模型：Final流式内容事件
 107 |      * 在思考过程中实时发送Final内容，但不提升为主视图
 108 |      */
 109 |     data class FinalStreaming(
 110 |         val chunk: String
 111 |     ) : ThinkingEvent
 112 | 
 113 |     /**
 114 |      * 🔥 双事件模型：Final准备就绪事件
 115 |      * 思考完成后发送，告诉UI可以将Final提升为主视图
 116 |      */
 117 |     object FinalReady : ThinkingEvent
 118 | 
 119 |     /**
 120 |      * 🔥 【异步Summary修复】Summary生成完成事件
 121 |      * 在后台线程生成Summary后发送，避免UI停滞
 122 |      */
 123 |     data class SummaryGenerated(
 124 |         val summary: Summary
 125 |     ) : ThinkingEvent
 126 | 
 127 |     /**
 128 |      * Phase 2新增：搜索统计事件
 129 |      *
 130 |      * 用于更新搜索和来源统计数据
 131 |      *
 132 |      * @param searchCount 搜索次数
 133 |      * @param sourceCount 来源数量
 134 |      * @param sources 来源链接列表
 135 |      */
 136 |     data class UpdateStats(
 137 |         val searchCount: Int,
 138 |         val sourceCount: Int,
 139 |         val sources: List<String> = emptyList(),
 140 |     ) : ThinkingEvent
 141 | 
 142 |     /**
 143 |      * SummaryCard新增：生成总结事件
 144 |      *
 145 |      * 当收到FinalAnswer时自动生成Summary
 146 |      *
 147 |      * @param bullets 摘要要点列表
 148 |      * @param sources 来源链接列表
 149 |      */
 150 |     data class GenerateSummary(
 151 |         val bullets: List<String>,
 152 |         val sources: List<String>,
 153 |     ) : ThinkingEvent
 154 | 
 155 |     /**
 156 |      * SummaryCard新增：切换总结展开状态事件
 157 |      */
 158 |     object ToggleSummary : ThinkingEvent
 159 | 
 160 |     // === 🔥 【Phase转换控制】Phase渲染完成等待机制 ===
 161 |     /**
 162 |      * 将phase加入等待队列
 163 |      */
 164 |     data class PhaseQueueAdd(val phaseId: String) : ThinkingEvent
 165 | 
 166 |     /**
 167 |      * UI层通知phase渲染完成
 168 |      */
 169 |     data class PhaseRenderingComplete(val phaseId: String) : ThinkingEvent
 170 | 
 171 |     /**
 172 |      * 检测到</thinking>或</final>标签，开始15秒倒计时
 173 |      */
 174 |     object ThinkingFinalDetected : ThinkingEvent
 175 | 
 176 |     /**
 177 |      * 15秒超时，强制完成最终渲染
 178 |      */
 179 |     object FinalRenderingTimeout : ThinkingEvent
 180 | 
 181 |     /**
 182 |      * 最后一个Phase渲染完成事件
 183 |      *
 184 |      * 🔥 【超时机制优化】用于标识最后一个phase的UI渲染完成
 185 |      * 触发条件：
 186 |      * 1. 当前phase已完成渲染
 187 |      * 2. 没有更多等待的phase在队列中
 188 |      * 3. 系统正在等待最终渲染（isWaitingForFinalRendering=true）
 189 |      *
 190 |      * 作用：立即触发富文本渲染，无需等待15秒超时
 191 |      *
 192 |      * @param phaseId 完成渲染的phase ID
 193 |      */
 194 |     data class LastPhaseRenderingComplete(
 195 |         val phaseId: String,
 196 |     ) : ThinkingEvent
 197 | 
 198 |     /**
 199 |      * 流结束事件
 200 |      *
 201 |      * 标记思考过程完全结束
 202 |      */
 203 |     object StreamEnd : ThinkingEvent
 204 | 
 205 |     /**
 206 |      * 思考完成事件 - 只在</thinking>之后触发
 207 |      *
 208 |      * 用于触发Summary显示和卡片折叠
 209 |      *
 210 |      * @param summary 总结内容
 211 |      * @param finalMarkdown 最终Markdown内容
 212 |      * @param messageId AI消息ID，用于History落库关联
 213 |      */
 214 |     data class ThinkingFinished(
 215 |         val summary: String,
 216 |         val finalMarkdown: String,
 217 |         val messageId: String? = null // 🔥 新增：用于History落库
 218 |     ) : ThinkingEvent
 219 | 
 220 |     /**
 221 |      * 错误事件
 222 |      *
 223 |      * @param msg 错误消息
 224 |      * @param cause 错误原因（可选）
 225 |      */
 226 |     data class Error(
 227 |         val msg: String,
 228 |         val cause: Throwable? = null,
 229 |     ) : ThinkingEvent
 230 | 
 231 |     /**
 232 |      * 🔥 Step 1重构：11步状态转换事件
 233 |      */
 234 | 
 235 |     /**
 236 |      * 状态转换事件
 237 |      *
 238 |      * 用于触发11步状态转换序列中的步骤切换
 239 |      *
 240 |      * @param targetStep 目标步骤
 241 |      * @param trigger 触发原因
 242 |      * @param metadata 附加元数据
 243 |      */
 244 |     data class StepTransition(
 245 |         val targetStep: ThinkingStep,
 246 |         val trigger: String,
 247 |         val metadata: Map<String, Any> = emptyMap()
 248 |     ) : ThinkingEvent
 249 | 
 250 |     /**
 251 |      * Think检测事件
 252 |      *
 253 |      * 当检测到<think>标签时触发
 254 |      */
 255 |     object ThinkDetected : ThinkingEvent
 256 | 
 257 |     /**
 258 |      * Thinking开始事件
 259 |      *
 260 |      * 当检测到<thinking>标签时触发
 261 |      */
 262 |     object ThinkingStarted : ThinkingEvent
 263 | 
 264 |     /**
 265 |      * Phase管理激活事件
 266 |      *
 267 |      * 当开始处理<phase>标签时触发
 268 |      */
 269 |     data class PhaseManagementActivated(val phaseId: String) : ThinkingEvent
 270 | 
 271 |     /**
 272 |      * 标题更新启用事件
 273 |      *
 274 |      * 当检测到<title>标签时触发
 275 |      */
 276 |     data class TitleUpdatesEnabled(val phaseId: String, val title: String) : ThinkingEvent
 277 | 
 278 |     /**
 279 |      * 文本流式传输激活事件
 280 |      *
 281 |      * 当开始流式传输文本内容时触发
 282 |      */
 283 |     data class TextStreamingActivated(val phaseId: String) : ThinkingEvent
 284 | 
 285 |     /**
 286 |      * 完成折叠触发事件
 287 |      *
 288 |      * 当最后一个phase完成时触发
 289 |      */
 290 |     object CompletionCollapseTriggered : ThinkingEvent
 291 | 
 292 |     /**
 293 |      * 摘要切换启用事件
 294 |      *
 295 |      * 当Summary准备就绪时触发
 296 |      */
 297 |     object SummaryToggleEnabled : ThinkingEvent
 298 | 
 299 |     /**
 300 |      * 富文本渲染激活事件
 301 |      *
 302 |      * 当<final>内容准备就绪时触发
 303 |      */
 304 |     object RichTextRenderingActivated : ThinkingEvent
 305 | }
 306 | 
 307 | /**
 308 |  * 阶段聚合数据
 309 |  *
 310 |  * 用于在Reducer中聚合同一阶段的多个PhaseDelta事件
 311 |  */
 312 | data class PhaseAggregate(
 313 |     val title: String,
 314 |     val content: String,
 315 |     val lastOrdinal: Long = 0L, // 🔥 627目标交付：换成Long避免溢出
 316 |     val timestamp: Long = System.currentTimeMillis(),
 317 |     val hasTitle: Boolean = false, // 🔥 627目标交付：标记是否已设置标题，防止首行拆分误判
 318 |     val isComplete: Boolean = false, // 🔥 用户方案：Phase完整性标记
 319 | ) {
 320 |     /**
 321 |      * 追加内容
 322 |      */
 323 |     fun appendContent(append: String, ordinal: Long): PhaseAggregate { // 🔥 627目标交付：换成Long
 324 |         return copy(
 325 |             content = content + append,
 326 |             lastOrdinal = ordinal,
 327 |             timestamp = System.currentTimeMillis(),
 328 |         )
 329 |     }
 330 | 
 331 |     /**
 332 |      * 🔥 627目标交付：更新标题并标记hasTitle
 333 |      */
 334 |     fun updateTitle(newTitle: String): PhaseAggregate {
 335 |         return copy(
 336 |             title = newTitle,
 337 |             hasTitle = true,
 338 |             timestamp = System.currentTimeMillis(),
 339 |         )
 340 |     }
 341 | 
 342 | 
 343 | }
 344 | 
 345 | /**
 346 |  * 总结卡片数据
 347 |  *
 348 |  * 按照总结card.md文档设计的Summary数据结构
 349 |  * 🔥 701finalmermaid大纲.md蓝图要求：添加tokens计数支持
 350 |  */
 351 | data class Summary(
 352 |     val durationText: String, // "1 m 3 s"
 353 |     val searchCnt: Int, // 搜索次数
 354 |     val sourceCnt: Int, // 来源数量
 355 |     val bullets: List<String>, // 1-5条摘要要点
 356 |     val sources: List<String>, // 来源URL列表
 357 |     val isExpanded: Boolean = false, // 展开状态
 358 |     val tokenCount: Int = 0, // 🔥 新增：tokens计数，符合蓝图要求
 359 | )
 360 | 
 361 | /**
 362 |  * ThinkingBox 11步状态转换序列枚举
 363 |  *
 364 |  * 基于701finalmermaid大纲.md第32-43行的UI状态切换序列
 365 |  * 实现完整的XML标签处理流程：<think> → <thinking> → <phase> → <final>
 366 |  */
 367 | enum class ThinkingStep {
 368 |     UserMessage,           // 1. 用户消息显示
 369 |     LoadingAnimation,      // 2. Loading动画 (ThinkingHeader展示thinking动画)
 370 |     ThinkDetection,        // 3. Think检测 (检测到<think>内容)
 371 |     PreThinkingPhase,      // 4. 预思考阶段 (实时刷新预思考内容)
 372 |     ThinkingStart,         // 5. 思考开始 (检测到<thinking>标签)
 373 |     PhaseManagement,       // 6. Phase管理 (<phase id="...">掌管思考框重组刷新)
 374 |     TitleUpdates,          // 7. 标题更新 (<title>标签掌管思考框内title的更新)
 375 |     TextStreaming,         // 8. 文本流式传输 (逐字打印更新直到完成全部内容)
 376 |     CompletionCollapse,    // 9. 完成折叠 (完成最后一个phase→折叠思考框)
 377 |     SummaryToggle,         // 10. 摘要切换 (点击"已完成思考"→呈现summarycard)
 378 |     RichTextRendering      // 11. 富文本渲染 (使用FinalRichTextRenderer)
 379 | }
 380 | 
 381 | /**
 382 |  * 思考UI状态
 383 |  *
 384 |  * 基于事件驱动的不可变状态模型
 385 |  * 🔥 Step 1重构：添加11步状态转换序列支持
 386 |  */
 387 | data class ThinkingUiState(
 388 |     val phases: PersistentMap<String, PhaseAggregate> = persistentMapOf(), // 🔥 627目标交付：使用PersistentMap确保不可变
 389 |     val final: String? = null,
 390 |     val isStreaming: Boolean = true,
 391 |     val error: String? = null,
 392 |     val sessionId: String? = null,
 393 |     // 预思考内容：<think>标签内容的灰度预览
 394 |     val preThinking: String? = null,
 395 |     // Phase 2新增：统计数据
 396 |     val startTime: Long = System.currentTimeMillis(),
 397 |     val searchCount: Int = 0,
 398 |     val sourceCount: Int = 0,
 399 |     val sources: List<String> = emptyList(),
 400 |     // SummaryCard新增：总结数据
 401 |     val summary: Summary? = null,
 402 |     // 🔥 按照用户要求：添加折叠状态
 403 |     val isCollapsed: Boolean = false,
 404 |     // 🔥 修复文本无限刷新：添加最后更新时间用于throttle
 405 |     val lastUpdateTime: Long? = null,
 406 |     // 🔥 627目标交付：version字段用于distinctUntilChanged
 407 |     val version: Long = 0L,
 408 | 
 409 |     // 🔥 数据流统一新增：持久化相关字段
 410 |     val tokensSnapshot: String = "", // 原始token快照，用于调试/重播
 411 |     val lastFunctionCall: String? = null, // 最后的函数调用JSON
 412 |     val lastMcp: String? = null, // 最后的MCP元数据JSON
 413 |     val persisted: Boolean = false, // 是否已持久化到历史表
 414 | 
 415 |     // 🔥 Step 1重构：11步状态转换序列支持
 416 |     val currentStep: ThinkingStep = ThinkingStep.UserMessage, // 当前所处的步骤
 417 |     val stepTransitionTime: Long = System.currentTimeMillis(), // 步骤转换时间
 418 |     val thinkDetected: Boolean = false, // 是否检测到<think>标签
 419 |     val thinkingStarted: Boolean = false, // 是否检测到<thinking>标签
 420 |     val phaseManagementActive: Boolean = false, // Phase管理是否激活
 421 |     val titleUpdatesEnabled: Boolean = false, // 标题更新是否启用
 422 |     val textStreamingActive: Boolean = false, // 文本流式传输是否激活
 423 |     val completionCollapseTriggered: Boolean = false, // 完成折叠是否触发
 424 |     val summaryToggleEnabled: Boolean = false, // 摘要切换是否启用
 425 |     val richTextRenderingActive: Boolean = false, // 富文本渲染是否激活
 426 |     val currentPhaseId: String? = null, // 当前活跃的Phase ID
 427 |     val lastPhaseCompleted: String? = null, // 最后完成的Phase ID
 428 | 
 429 |     // 🔥 【Phase转换控制】Phase渲染完成等待机制
 430 |     val pendingPhaseQueue: List<String> = emptyList(), // 等待切换的phase队列
 431 |     val currentPhaseRenderingComplete: Boolean = true, // 当前phase是否渲染完成
 432 |     val finalRenderingStartTime: Long? = null, // 最终渲染开始时间（15秒倒计时）
 433 |     val isWaitingForFinalRendering: Boolean = false, // 是否在等待最终渲染（15秒内）
 434 | ) {
 435 |     /**
 436 |      * 是否有最终答案
 437 |      */
 438 |     val hasFinal: Boolean get() = final != null
 439 | 
 440 |     /**
 441 |      * 是否完成
 442 |      */
 443 |     val isCompleted: Boolean get() = !isStreaming && hasFinal
 444 | 
 445 |     /**
 446 |      * 所有阶段列表
 447 |      */
 448 |     val allPhases: List<PhaseAggregate> get() = phases.values.toList()
 449 | 
 450 |     /**
 451 |      * 获取可见阶段（用于UI显示）
 452 |      * 🔥 紧急修复：始终显示所有阶段，不受折叠状态影响
 453 |      */
 454 |     fun getVisiblePhases(expanded: Boolean): List<PhaseAggregate> {
 455 |         // 🔥 修复：始终返回所有阶段，让UI层决定显示逻辑
 456 |         return allPhases
 457 |         // 原逻辑：if (hasFinal && !expanded) emptyList() else allPhases
 458 |     }
 459 | 
 460 |     /**
 461 |      * 🔥 Step 1重构：11步状态转换辅助方法
 462 |      */
 463 | 
 464 |     /**
 465 |      * 判断是否可以进入下一个步骤
 466 |      */
 467 |     fun canTransitionTo(nextStep: ThinkingStep): Boolean {
 468 |         return when (nextStep) {
 469 |             ThinkingStep.UserMessage -> true // 总是可以重置到用户消息
 470 |             ThinkingStep.LoadingAnimation -> isStreaming
 471 |             ThinkingStep.ThinkDetection -> isStreaming && !preThinking.isNullOrBlank()
 472 |             ThinkingStep.PreThinkingPhase -> thinkDetected && !preThinking.isNullOrBlank()
 473 |             ThinkingStep.ThinkingStart -> thinkingStarted
 474 |             ThinkingStep.PhaseManagement -> thinkingStarted && phases.isNotEmpty()
 475 |             ThinkingStep.TitleUpdates -> phaseManagementActive && currentPhaseId != null
 476 |             ThinkingStep.TextStreaming -> titleUpdatesEnabled
 477 |             ThinkingStep.CompletionCollapse -> !isStreaming && phases.isNotEmpty()
 478 |             ThinkingStep.SummaryToggle -> completionCollapseTriggered && summary != null
 479 |             ThinkingStep.RichTextRendering -> hasFinal && richTextRenderingActive
 480 |         }
 481 |     }
 482 | 
 483 |     /**
 484 |      * 获取当前步骤应该显示的组件类型
 485 |      */
 486 |     fun getCurrentDisplayComponent(): String {
 487 |         return when (currentStep) {
 488 |             ThinkingStep.UserMessage -> "UserMessage"
 489 |             ThinkingStep.LoadingAnimation -> "ThinkingHeader"
 490 |             ThinkingStep.ThinkDetection -> "ThinkingHeader"
 491 |             ThinkingStep.PreThinkingPhase -> "PreThinkingCard"
 492 |             ThinkingStep.ThinkingStart -> "ThinkingHeader"
 493 |             ThinkingStep.PhaseManagement -> "ThinkingStageCard"
 494 |             ThinkingStep.TitleUpdates -> "ThinkingStageCard"
 495 |             ThinkingStep.TextStreaming -> "ThinkingStageCard"
 496 |             ThinkingStep.CompletionCollapse -> "SummaryCard"
 497 |             ThinkingStep.SummaryToggle -> "SummaryCard"
 498 |             ThinkingStep.RichTextRendering -> "FinalRichTextRenderer"
 499 |         }
 500 |     }
 501 | 
 502 |     /**
 503 |      * 检查状态转换的有效性
 504 |      */
 505 |     fun isValidTransition(from: ThinkingStep, to: ThinkingStep): Boolean {
 506 |         // 定义有效的状态转换路径
 507 |         val validTransitions = mapOf(
 508 |             ThinkingStep.UserMessage to setOf(ThinkingStep.LoadingAnimation),
 509 |             ThinkingStep.LoadingAnimation to setOf(ThinkingStep.ThinkDetection, ThinkingStep.ThinkingStart),
 510 |             ThinkingStep.ThinkDetection to setOf(ThinkingStep.PreThinkingPhase),
 511 |             ThinkingStep.PreThinkingPhase to setOf(ThinkingStep.ThinkingStart),
 512 |             ThinkingStep.ThinkingStart to setOf(ThinkingStep.PhaseManagement),
 513 |             ThinkingStep.PhaseManagement to setOf(ThinkingStep.TitleUpdates, ThinkingStep.CompletionCollapse),
 514 |             ThinkingStep.TitleUpdates to setOf(ThinkingStep.TextStreaming),
 515 |             ThinkingStep.TextStreaming to setOf(ThinkingStep.PhaseManagement, ThinkingStep.CompletionCollapse),
 516 |             ThinkingStep.CompletionCollapse to setOf(ThinkingStep.SummaryToggle, ThinkingStep.RichTextRendering),
 517 |             ThinkingStep.SummaryToggle to setOf(ThinkingStep.RichTextRendering),
 518 |             ThinkingStep.RichTextRendering to emptySet() // 终态
 519 |         )
 520 | 
 521 |         return validTransitions[from]?.contains(to) == true
 522 |     }
 523 | 
 524 |     /**
 525 |      * Phase 2新增：计算思考时长
 526 |      */
 527 |     val thinkingDuration: String get() {
 528 |         val durationMs = if (isStreaming) {
 529 |             System.currentTimeMillis() - startTime
 530 |         } else {
 531 |             // 如果已完成，使用最后一个阶段的时间戳
 532 |             phases.values.lastOrNull()?.timestamp?.let { it - startTime } ?: 0L
 533 |         }
 534 | 
 535 |         val seconds = (durationMs / 1000).toInt()
 536 |         val minutes = seconds / 60
 537 |         val remainingSeconds = seconds % 60
 538 | 
 539 |         return if (minutes > 0) {
 540 |             "$minutes m $remainingSeconds s"
 541 |         } else {
 542 |             "$remainingSeconds s"
 543 |         }
 544 |     }
 545 | 
 546 |     /**
 547 |      * Phase 2新增：获取元数据文本
 548 |      */
 549 |     val metadataText: String get() {
 550 |         return if (searchCount > 0 || sourceCount > 0) {
 551 |             "$searchCount 个搜索 · $sourceCount 个源"
 552 |         } else {
 553 |             "思考中..."
 554 |         }
 555 |     }
 556 | 
 557 |     /**
 558 |      * 🔥 修复4：hasStarted派生属性 - 逻辑简单可靠
 559 |      */
 560 |     val hasStarted: Boolean get() = phases.isNotEmpty()
 561 | }
 562 | 
 563 | /**
 564 |  * 🔥 自增安全锁：确保每次状态变化都触发Compose重组
 565 |  */
 566 | private fun ThinkingUiState.bump(): ThinkingUiState = copy(version = version + 1)
 567 | 
 568 | /**
 569 |  * 事件驱动的Reducer函数
 570 |  *
 571 |  * 将ThinkingEvent转换为新的ThinkingUiState
 572 |  * 遵循不可变数据原则，每次返回新的状态对象
 573 |  */
 574 | fun reduce(state: ThinkingUiState, event: ThinkingEvent): ThinkingUiState {
 575 |     // 🔥 强化调试：Reducer事件处理日志
 576 |     timber.log.Timber.tag("TB-AUDIT").e("🔍 [数据流审计] Reducer处理ThinkingEvent: ${event::class.simpleName}")
 577 | 
 578 |     return when (event) {
 579 |         is ThinkingEvent.Start -> {
 580 |             // 🔥 【状态机修复】Start事件应该触发LoadingAnimation状态
 581 |             val currentTime = System.currentTimeMillis()
 582 |             val newState = state.copy(
 583 |                 sessionId = event.id,
 584 |                 isStreaming = true,
 585 |                 error = null,
 586 |                 phases = persistentMapOf(), // 🔥 627目标交付：使用persistentMapOf()
 587 |                 final = null,
 588 |                 // 🔥 【关键修复】设置正确的初始状态机步骤
 589 |                 currentStep = ThinkingStep.LoadingAnimation,
 590 |                 startTime = currentTime, // 🔥 【P0修复】确保startTime被正确设置
 591 |                 stepTransitionTime = currentTime
 592 |             ).bump() // 🔥 自增安全锁：version++
 593 | 
 594 |             timber.log.Timber.tag("TB-STATE").e("🔥 [703施工方案P0] Start事件处理: UserMessage -> LoadingAnimation, isStreaming=true, startTime=$currentTime")
 595 |             newState
 596 |         }
 597 | 
 598 |         is ThinkingEvent.PhaseDelta -> {
 599 |             // 🔥 用户质疑修复：空append用于Phase切换，不能跳过
 600 |             if (event.append.isEmpty() && event.title == null) {
 601 |                 // 🔥 空append且无title，可能是Phase切换，创建新Phase
 602 |                 val stableId = event.id
 603 |                 val existingPhase = state.phases[stableId]
 604 | 
 605 |                 if (existingPhase == null) {
 606 |                     // 创建新的空Phase，等待后续内容
 607 |                     val newPhase = PhaseAggregate(
 608 |                         title = "",
 609 |                         content = "",
 610 |                         lastOrdinal = event.ordinal,
 611 |                         timestamp = System.currentTimeMillis(),
 612 |                         hasTitle = false
 613 |                     )
 614 | 
 615 |                     val newPhases = state.phases.put(stableId, newPhase)
 616 |                     timber.log.Timber.tag("TB-DEBUG").e("🔍 REDUCE CREATE empty Phase: $stableId")
 617 | 
 618 |                     // 🔥 【状态机修复】即使是空Phase也要触发状态转换
 619 |                     val newCurrentStep = if (state.currentStep == ThinkingStep.LoadingAnimation ||
 620 |                                              state.currentStep == ThinkingStep.UserMessage) {
 621 |                         ThinkingStep.PhaseManagement
 622 |                     } else {
 623 |                         state.currentStep
 624 |                     }
 625 | 
 626 |                     if (newCurrentStep != state.currentStep) {
 627 |                         timber.log.Timber.tag("TB-STATE").i("🔥 [状态机修复] 空PhaseDelta触发状态转换: ${state.currentStep} -> $newCurrentStep")
 628 |                     }
 629 | 
 630 |                     return state.copy(
 631 |                         phases = newPhases,
 632 |                         preThinking = null, // 🔥 Phase切换时清空预思考
 633 |                         // 🔥 【关键修复】设置正确的状态机步骤
 634 |                         currentStep = newCurrentStep,
 635 |                         stepTransitionTime = if (newCurrentStep != state.currentStep) System.currentTimeMillis() else state.stepTransitionTime,
 636 |                         phaseManagementActive = true
 637 |                     ).bump()
 638 |                 } else {
 639 |                     // Phase已存在，跳过空append
 640 |                     timber.log.Timber.tag("TB-DEBUG").e("🔍 REDUCE SKIP empty append for existing Phase: $stableId")
 641 |                     return state
 642 |                 }
 643 |             }
 644 | 
 645 |             // 🔥 【无限重组修复】移除过度节流，允许快速文本追加
 646 |             val currentTime = System.currentTimeMillis()
 647 |             // 🔥 注释掉节流逻辑，因为它可能导致文本丢失和无限重组
 648 |             // val lastUpdateTime = state.lastUpdateTime ?: 0
 649 |             // if (currentTime - lastUpdateTime < 50) { // 50ms最小间隔
 650 |             //     timber.log.Timber.tag("TB-DEBUG").d("🔍 REDUCE THROTTLE PhaseDelta - 更新过于频繁")
 651 |             //     return state
 652 |             // }
 653 | 
 654 |             // 🔥 紧急调试：详细记录PhaseDelta处理
 655 |             timber.log.Timber.tag("TB-DEBUG").e("🚨 [紧急调试] REDUCE PhaseDelta处理开始:")
 656 |             timber.log.Timber.tag("TB-DEBUG").e("  - event.id: '${event.id}'")
 657 |             timber.log.Timber.tag("TB-DEBUG").e("  - event.append: '${event.append.take(100)}${if (event.append.length > 100) "..." else ""}'")
 658 |             timber.log.Timber.tag("TB-DEBUG").e("  - event.append.length: ${event.append.length}")
 659 |             timber.log.Timber.tag("TB-DEBUG").e("  - event.title: '${event.title}'")
 660 |             timber.log.Timber.tag("TB-DEBUG").e("  - event.ordinal: ${event.ordinal}")
 661 |             timber.log.Timber.tag("TB-DEBUG").e("  - event.isComplete: ${event.isComplete}")
 662 |             timber.log.Timber.tag("TB-DEBUG").e("  - 当前state.phases.size: ${state.phases.size}")
 663 |             timber.log.Timber.tag("TB-DEBUG").e("  - 当前state.phases.keys: ${state.phases.keys.toList()}")
 664 | 
 665 |             val stableId = event.id
 666 |             val existingPhase = state.phases[stableId]
 667 | 
 668 |             val phase = if (existingPhase != null) {
 669 |                 // 🔥 按照用户要求：简化逻辑 body = old.body + delta.body, title = delta.title ?: old.title
 670 |                 val newIsComplete = event.isComplete || existingPhase.isComplete
 671 | 
 672 |                 // 🔥 【isComplete追踪】记录完成状态变化
 673 |                 if (event.isComplete && !existingPhase.isComplete) {
 674 |                     timber.log.Timber.tag("PHASE-DEBUG").e("🔥 Reducer标记Phase完成: id=$stableId | isComplete: ${existingPhase.isComplete} → $newIsComplete")
 675 |                 }
 676 | 
 677 |                 PhaseAggregate(
 678 |                     title = event.title ?: existingPhase.title,
 679 |                     content = existingPhase.content + event.append,
 680 |                     lastOrdinal = event.ordinal,
 681 |                     timestamp = System.currentTimeMillis(),
 682 |                     hasTitle = event.title != null || existingPhase.hasTitle,
 683 |                     isComplete = newIsComplete // 🔥 用户方案：完整性标记
 684 |                 )
 685 |             } else {
 686 |                 // 🔥 【无限重组修复】新phase创建，特殊处理perthink标题
 687 |                 val phaseTitle = when {
 688 |                     event.title != null -> event.title
 689 |                     stableId == "perthink" -> "Bro is thinking" // 🔥 perthink特殊标题
 690 |                     else -> ""
 691 |                 }
 692 | 
 693 |                 // 🔥 【isComplete追踪】记录新建phase的完成状态
 694 |                 if (event.isComplete) {
 695 |                     timber.log.Timber.tag("PHASE-DEBUG").e("🔥 Reducer新建完成状态Phase: id=$stableId | isComplete=${event.isComplete}")
 696 |                 }
 697 | 
 698 |                 PhaseAggregate(
 699 |                     title = phaseTitle,
 700 |                     content = event.append,
 701 |                     lastOrdinal = event.ordinal,
 702 |                     timestamp = System.currentTimeMillis(),
 703 |                     hasTitle = event.title != null || stableId == "perthink",
 704 |                     isComplete = event.isComplete // 🔥 用户方案：完整性标记
 705 |                 )
 706 |             }
 707 | 
 708 |             // 🔥 627目标交付：使用PersistentMap.put()确保真正不可变
 709 |             val newPhases = state.phases.put(stableId, phase)
 710 | 
 711 |             // 🔥 【状态机修复】PhaseDelta事件应该触发状态转换到PhaseManagement
 712 |             val newCurrentStep = if (state.currentStep == ThinkingStep.LoadingAnimation ||
 713 |                                      state.currentStep == ThinkingStep.UserMessage) {
 714 |                 ThinkingStep.PhaseManagement
 715 |             } else {
 716 |                 state.currentStep
 717 |             }
 718 | 
 719 |             // 🔥 调试日志：追踪phase更新和状态转换
 720 |             timber.log.Timber.i("🔄 [Reducer] PhaseDelta: id=$stableId, title=${event.title}, append=${event.append.take(20)}...(${event.append.length}字符), totalPhases=${newPhases.size}")
 721 |             timber.log.Timber.i("🔄 [Reducer] Phase内容: title='${phase.title}', content=${phase.content.length}字符")
 722 |             if (newCurrentStep != state.currentStep) {
 723 |                 timber.log.Timber.tag("TB-STATE").i("🔥 [状态机修复] PhaseDelta触发状态转换: ${state.currentStep} -> $newCurrentStep")
 724 |             }
 725 | 
 726 |             state.copy(
 727 |                 phases = newPhases,
 728 |                 lastUpdateTime = currentTime, // 🔥 修复文本无限刷新：更新最后更新时间
 729 |                 // 🔥 【关键修复】设置正确的状态机步骤
 730 |                 currentStep = newCurrentStep,
 731 |                 stepTransitionTime = if (newCurrentStep != state.currentStep) System.currentTimeMillis() else state.stepTransitionTime,
 732 |                 phaseManagementActive = true
 733 |             ).bump() // 🔥 自增安全锁：version++
 734 |         }
 735 | 
 736 |         is ThinkingEvent.PhaseReplace -> {
 737 |             // 🔥 hotfix-1: 强化PhaseReplace事件处理，确保visiblePhases正确更新，防止幽灵残留
 738 |             timber.log.Timber.i("🔄 [Reducer] PhaseReplace 开始执行: ${event.oldId} → ${event.newId}")
 739 | 
 740 |             // 验证旧phase是否存在
 741 |             val oldPhaseExists = state.phases.containsKey(event.oldId)
 742 |             timber.log.Timber.i("🔄 [Reducer] 旧phase存在: $oldPhaseExists")
 743 | 
 744 |             val oldPhase = state.phases[event.oldId]
 745 |             val newPhase = if (oldPhase != null) {
 746 |                 // 🔥 关键修复：Phase切换时清空内容，不合并旧内容
 747 |                 PhaseAggregate(
 748 |                     title = event.title ?: "",  // 使用新标题，不保留旧标题
 749 |                     content = event.content,    // 🔥 修复：只使用新内容，不合并旧内容
 750 |                     lastOrdinal = System.currentTimeMillis(),
 751 |                     timestamp = System.currentTimeMillis(),
 752 |                     hasTitle = event.title != null
 753 |                 )
 754 |             } else {
 755 |                 // 创建新phase
 756 |                 PhaseAggregate(
 757 |                     title = event.title ?: "",
 758 |                     content = event.content,
 759 |                     lastOrdinal = System.currentTimeMillis(),
 760 |                     timestamp = System.currentTimeMillis(),
 761 |                     hasTitle = event.title != null
 762 |                 )
 763 |             }
 764 | 
 765 |             // 🔥 调试断点4：PhaseReplace执行
 766 |             timber.log.Timber.tag("TB-DEBUG").e("🔍 REDUCE PhaseReplace ${event.oldId}→${event.newId} newContent='${event.content.take(20)}...'")
 767 |             timber.log.Timber.tag("TB-DEBUG").e("🔍 REDUCE PhaseReplace oldContent='${oldPhase?.content?.take(50)}...' newContent='${event.content.take(50)}...'")
 768 |             timber.log.Timber.i("🔄 [Reducer] PhaseReplace内容清空: oldContent=${oldPhase?.content?.length ?: 0}字符 → newContent=${event.content.length}字符")
 769 | 
 770 |             // 执行替换：先删除旧phase，再添加新phase
 771 |             val newPhases = state.phases
 772 |                 .remove(event.oldId)
 773 |                 .put(event.newId, newPhase)
 774 | 
 775 |             timber.log.Timber.i("🔄 [Reducer] phases更新: ${state.phases.keys} → ${newPhases.keys}")
 776 | 
 777 |             val newState = state.copy(
 778 |                 phases = newPhases,
 779 |                 preThinking = null  // 🔥 关键修复：PhaseReplace时清空预思考内容，防止重复显示
 780 |             ).bump() // 🔥 自增安全锁：version++
 781 | 
 782 |             // 验证更新结果
 783 |             val oldPhaseRemoved = !newState.phases.containsKey(event.oldId)
 784 |             val newPhaseAdded = newState.phases.containsKey(event.newId)
 785 |             timber.log.Timber.i("🔄 [Reducer] 验证结果: 旧phase已删除=$oldPhaseRemoved, 新phase已添加=$newPhaseAdded")
 786 | 
 787 |             if (!oldPhaseRemoved || !newPhaseAdded) {
 788 |                 timber.log.Timber.e("🚨 [Reducer] PhaseReplace更新失败！可能导致幽灵残留")
 789 |                 // ThinkingBoxMetrics.reportPhaseReplaceFailure() // 需要添加监控类
 790 |             }
 791 | 
 792 |             // 🔥 蓝图优化：AI-STREAM出口日志
 793 |             timber.log.Timber.tag("AI-STREAM").i("Thinking phase=${newState.phases.size}")
 794 |             newState
 795 |         }
 796 | 
 797 |         is ThinkingEvent.FinalAnswer -> {
 798 |             // 🔥 【职责边界修复】设置正确的状态，确保UI逻辑一致
 799 |             timber.log.Timber.i("🔥 [职责边界修复] FinalAnswer到达，设置isStreaming=false, isCollapsed=true")
 800 | 
 801 |             state.copy(
 802 |                 final = event.markdown,
 803 |                 sources = event.sources,
 804 |                 sourceCount = event.sources.size,
 805 |                 isStreaming = false, // 🔥 思考完成，停止流式传输
 806 |                 isCollapsed = true,  // 🔥 【职责边界修复】默认折叠，显示SimpleSummaryText
 807 |                 // summary暂时为null，等待SummaryGenerated事件
 808 |             ).bump() // 🔥 自增安全锁：version++
 809 |         }
 810 | 
 811 |         is ThinkingEvent.UpdateStats -> {
 812 |             state.copy(
 813 |                 searchCount = event.searchCount,
 814 |                 sourceCount = event.sourceCount,
 815 |                 sources = event.sources,
 816 |             )
 817 |         }
 818 | 
 819 |         is ThinkingEvent.GenerateSummary -> {
 820 |             val summary = Summary(
 821 |                 durationText = state.thinkingDuration,
 822 |                 searchCnt = state.searchCount,
 823 |                 sourceCnt = event.sources.size,
 824 |                 bullets = event.bullets,
 825 |                 sources = event.sources,
 826 |                 isExpanded = false,
 827 |             )
 828 |             state.copy(summary = summary)
 829 |         }
 830 | 
 831 |         is ThinkingEvent.ToggleSummary -> {
 832 |             // 🔥 修复折叠逻辑：切换isCollapsed状态，实现富文本呈现后的折叠功能
 833 |             timber.log.Timber.tag("TB-TOGGLE").i("🔥 [ToggleSummary] 当前isCollapsed=${state.isCollapsed}，切换为${!state.isCollapsed}")
 834 |             state.copy(
 835 |                 isCollapsed = !state.isCollapsed,
 836 |                 // 同时更新summary的isExpanded状态保持一致
 837 |                 summary = state.summary?.copy(
 838 |                     isExpanded = state.isCollapsed // 如果当前是折叠的，切换后就是展开的
 839 |                 )
 840 |             ).bump() // 🔥 自增安全锁：version++
 841 |         }
 842 | 
 843 |         // === 🔥 【Phase转换控制】Phase渲染完成等待机制 ===
 844 |         is ThinkingEvent.PhaseQueueAdd -> {
 845 |             timber.log.Timber.tag("PHASE-DEBUG").e("📋 PhaseQueueAdd: ${event.phaseId} | 当前: ${state.currentPhaseId} | 完成: ${state.currentPhaseRenderingComplete} | 队列: ${state.pendingPhaseQueue}")
 846 | 
 847 |             // 🔥 【关键修复】如果当前没有活跃phase或当前phase已完成，立即切换
 848 |             if (state.currentPhaseId == null || state.currentPhaseRenderingComplete) {
 849 |                 timber.log.Timber.tag("PHASE-DEBUG").e("🚀 立即切换到Phase: ${event.phaseId}")
 850 | 
 851 |                 // 立即切换并创建phase
 852 |                 val newPhases = state.phases.put(
 853 |                     event.phaseId,
 854 |                     PhaseAggregate(
 855 |                         title = "", // 等待title标签设置
 856 |                         content = "",
 857 |                         lastOrdinal = System.nanoTime(),
 858 |                         timestamp = System.currentTimeMillis(),
 859 |                         hasTitle = false,
 860 |                         isComplete = false
 861 |                     )
 862 |                 )
 863 | 
 864 |                 state.copy(
 865 |                     currentPhaseId = event.phaseId,
 866 |                     currentPhaseRenderingComplete = false,
 867 |                     phases = newPhases,
 868 |                     phaseManagementActive = true
 869 |                 ).bump()
 870 |             } else {
 871 |                 // 当前phase还在渲染，加入等待队列
 872 |                 timber.log.Timber.tag("PHASE-DEBUG").e("⏳ Phase加入等待队列: ${event.phaseId}")
 873 |                 state.copy(
 874 |                     pendingPhaseQueue = state.pendingPhaseQueue + event.phaseId
 875 |                 ).bump()
 876 |             }
 877 |         }
 878 | 
 879 |         is ThinkingEvent.PhaseRenderingComplete -> {
 880 |             timber.log.Timber.tag("PHASE-DEBUG").e("✅ PhaseRenderingComplete: ${event.phaseId} | 队列: ${state.pendingPhaseQueue}")
 881 | 
 882 |             // 标记当前phase渲染完成，允许切换到下一个phase
 883 |             val newState = state.copy(
 884 |                 currentPhaseRenderingComplete = true,
 885 |                 lastPhaseCompleted = event.phaseId
 886 |             )
 887 | 
 888 |             // 如果有等待中的phase，切换到下一个
 889 |             if (newState.pendingPhaseQueue.isNotEmpty()) {
 890 |                 val nextPhaseId = newState.pendingPhaseQueue.first()
 891 |                 timber.log.Timber.tag("PHASE-DEBUG").e("🔄 切换到下一个Phase: $nextPhaseId | 剩余队列: ${newState.pendingPhaseQueue.drop(1)}")
 892 | 
 893 |                 // 创建新phase并切换
 894 |                 val newPhases = newState.phases.put(
 895 |                     nextPhaseId,
 896 |                     PhaseAggregate(
 897 |                         title = "", // 等待title标签设置
 898 |                         content = "",
 899 |                         lastOrdinal = System.nanoTime(),
 900 |                         timestamp = System.currentTimeMillis(),
 901 |                         hasTitle = false,
 902 |                         isComplete = false
 903 |                     )
 904 |                 )
 905 | 
 906 |                 // 🔥 【修复】切换到正式phase时，触发PhaseManagementActivated逻辑
 907 |                 val finalState = newState.copy(
 908 |                     currentPhaseId = nextPhaseId,
 909 |                     pendingPhaseQueue = newState.pendingPhaseQueue.drop(1),
 910 |                     currentPhaseRenderingComplete = false,
 911 |                     phases = newPhases,
 912 |                     phaseManagementActive = true,
 913 |                     // 🔥 【关键修复】清空预思考内容，触发UI重组刷新到正式phase
 914 |                     preThinking = null,
 915 |                     currentStep = ThinkingStep.PhaseManagement,
 916 |                     stepTransitionTime = System.currentTimeMillis()
 917 |                 ).bump()
 918 | 
 919 |                 timber.log.Timber.tag("PHASE-DEBUG").e("🎯 Phase切换完成: $nextPhaseId 已激活")
 920 |                 finalState
 921 |             } else {
 922 |                 // 🔥 【超时机制优化】没有更多等待的phase，检查是否应该立即进入富文本渲染
 923 |                 if (state.isWaitingForFinalRendering) {
 924 |                     timber.log.Timber.tag("PHASE-DEBUG").e("🏁 最后一个phase完成，立即进入富文本渲染")
 925 | 
 926 |                     // 🔥 【超时机制优化】标记需要取消超时，ThinkingBoxInstance会检测这个状态变化
 927 |                     val optimizedState =
 928 |                         newState
 929 |                             .copy(
 930 |                                 isWaitingForFinalRendering = false,
 931 |                         isStreaming = false, // 立即结束流式传输
 932 |                         currentStep = ThinkingStep.RichTextRendering,
 933 |                                 stepTransitionTime = System.currentTimeMillis(),
 934 |                                 // 🔥 【新增标记】用于ThinkingBoxInstance检测并取消超时
 935 |                                 lastPhaseCompleted = event.phaseId,
 936 |                             ).bump()
 937 | 
 938 |                     timber.log.Timber
 939 |                         .tag("PHASE-DEBUG")
 940 |                         .e("🔥 状态已更新，等待ThinkingBoxInstance取消超时")
 941 |                     optimizedState
 942 |                 } else {
 943 |                     timber.log.Timber.tag("PHASE-DEBUG").e("⏸️ 没有更多等待的phase，保持当前状态")
 944 |                     newState.bump()
 945 |                 }
 946 |             }
 947 |         }
 948 | 
 949 |         is ThinkingEvent.ThinkingFinalDetected -> {
 950 |             timber.log.Timber.tag("TB-PHASE").i("🔥 [Phase转换控制] 检测到</thinking>或</final>标签，开始15秒倒计时")
 951 |             state.copy(
 952 |                 finalRenderingStartTime = System.currentTimeMillis(),
 953 |                 isWaitingForFinalRendering = true
 954 |             ).bump()
 955 |         }
 956 | 
 957 |         is ThinkingEvent.FinalRenderingTimeout -> {
 958 |             timber.log.Timber.tag("TB-PHASE").i("🔥 [Phase转换控制] 15秒超时，强制完成最终渲染")
 959 |             state.copy(
 960 |                 isWaitingForFinalRendering = false,
 961 |                 isStreaming = false // 强制结束流式传输
 962 |             ).bump()
 963 |         }
 964 | 
 965 |         is ThinkingEvent.StreamEnd -> {
 966 |             timber.log.Timber.i("🔥 [Reducer] StreamEnd: 设置isStreaming=false, version=${state.version + 1}")
 967 |             state.copy(
 968 |                 isStreaming = false,
 969 |             ).bump() // 🔥 自增安全锁：version++
 970 |         }
 971 | 
 972 |         is ThinkingEvent.ThinkingFinished -> {
 973 |             // 🔥 修复思考框跳过问题：ThinkingFinished时不自动折叠，让用户看到完整思考过程
 974 |             timber.log.Timber.tag("TB-DEBUG").e("🔍 REDUCE ThinkingFinished summaryLen=${event.summary.length} markdownLen=${event.finalMarkdown.length}")
 975 |             timber.log.Timber.i("🔥 [Reducer] ThinkingFinished: 停止流式传输，显示summary，但不自动折叠")
 976 | 
 977 |             // 🔥 629工作计划.md要求：计算实际思考时间
 978 |             val thinkingDuration = System.currentTimeMillis() - state.startTime
 979 |             val durationText = formatThinkingDuration(thinkingDuration)
 980 | 
 981 |             // 🔥 【统一】直接使用简单估算，避免重复函数
 982 |             val tokenCount =
 983 |                 if (state.tokensSnapshot.isNotBlank()) {
 984 |                     (state.tokensSnapshot.length + 3) / 4
 985 |                 } else {
 986 |                     val totalContent =
 987 |                         state.phases.values.joinToString("") { phase ->
 988 |                             "${phase.title} ${phase.content}"
 989 |                         }
 990 |                     (totalContent.length + 3) / 4
 991 |             }
 992 | 
 993 |             val newState = state.copy(
 994 |                 summary = Summary(
 995 |                     durationText = durationText,
 996 |                     searchCnt = state.searchCount, // 🔥 使用实际搜索统计
 997 |                     sourceCnt = state.sourceCount, // 🔥 使用实际来源统计
 998 |                     bullets = extractBulletPoints(event.summary),
 999 |                     sources = state.sources, // 🔥 使用实际来源列表
1000 |                     tokenCount = tokenCount // 🔥 新增：tokens计数
1001 |                 ),
1002 |                 final = event.finalMarkdown,
1003 |                 isStreaming = false,
1004 |                 isCollapsed = true // ✅ 修复：自动折叠，符合701finalmermaid大纲.md规范
1005 |             ).bump()
1006 | 
1007 |             // 🔥 蓝图优化：AI-STREAM出口日志
1008 |             timber.log.Timber.tag("AI-STREAM").i("Collapsed duration=${durationText}")
1009 |             newState
1010 |         }
1011 | 
1012 |         is ThinkingEvent.Error -> {
1013 |             state.copy(
1014 |                 error = event.msg,
1015 |                 isStreaming = false,
1016 |             ).bump() // 🔥 自增安全锁：version++
1017 |         }
1018 | 
1019 |         is ThinkingEvent.FinalStreaming -> {
1020 |             // 🔥 双事件模型：实时接收Final内容，但不改变UI状态
1021 |             // Final内容在FinalReady时才会被UI显示
1022 |             timber.log.Timber.d("🔥 [Reducer] FinalStreaming: 接收${event.chunk.length}字符，暂不更新UI")
1023 |             state // 不改变状态，等待FinalReady
1024 |         }
1025 | 
1026 |         is ThinkingEvent.FinalReady -> {
1027 |             // 🔥 双事件模型：Final准备就绪，可以提升为主视图
1028 |             timber.log.Timber.i("🔥 [Reducer] FinalReady: Final内容准备就绪，可以提升为主视图")
1029 |             state.copy(
1030 |                 // 可以在这里添加特殊的状态标记，表示Final已准备就绪
1031 |             ).bump()
1032 |         }
1033 | 
1034 |         is ThinkingEvent.SummaryGenerated -> {
1035 |             // 🔥 【异步Summary修复】接收异步生成的Summary
1036 |             timber.log.Timber.i("🔥 [异步Summary修复] 接收到异步生成的Summary")
1037 |             state.copy(
1038 |                 summary = event.summary
1039 |             ).bump()
1040 |         }
1041 | 
1042 |         is ThinkingEvent.PreThinkDelta -> {
1043 |             val newState = state.copy(
1044 |                 preThinking = (state.preThinking ?: "") + event.text,
1045 |                 thinkDetected = true,
1046 |                 currentStep = ThinkingStep.PreThinkingPhase,
1047 |                 stepTransitionTime = System.currentTimeMillis()
1048 |             ).bump()
1049 | 
1050 |             // 🔥 关键内容日志
1051 |             timber.log.Timber.tag("TB-CONTENT").i("PreThinking累积: '${newState.preThinking}' (${newState.preThinking?.length}字符)")
1052 |             newState
1053 |         }
1054 | 
1055 |         is ThinkingEvent.PreThinkClear -> {
1056 |             // 🔥 【UI状态切换序列修复】第5步：<phase>检测时必须清空预思考内容
1057 |             timber.log.Timber.i("🔥 [UI状态切换序列] 第5步：PreThinkClear执行，清空预思考内容，触发fadeOut动画")
1058 |             state.copy(
1059 |                 preThinking = null
1060 |             ).bump()
1061 |         }
1062 | 
1063 |         is ThinkingEvent.ContentChunk -> {
1064 |             val currentPhaseId = state.currentPhaseId ?: return state
1065 | 
1066 |             if (currentPhaseId == "perthink") {
1067 |                 // 🔥 【v7统一方案】处理预思考内容，更新到虚拟Phase的content
1068 |                 val perthinkPhase = state.phases[currentPhaseId] ?: return state
1069 |                 val updatedPhase = perthinkPhase.copy(content = perthinkPhase.content + event.text)
1070 | 
1071 |                 timber.log.Timber.tag("TB-CONTENT").i("预思考内容累积到Phase: '${event.text}' (总长度: ${updatedPhase.content.length}字符)")
1072 |                 state.copy(
1073 |                     phases = state.phases.put(currentPhaseId, updatedPhase),
1074 |                     currentStep = ThinkingStep.PreThinkingPhase,
1075 |                     stepTransitionTime = System.currentTimeMillis()
1076 |                 ).bump()
1077 |             } else {
1078 |                 // 🔥 【v7统一方案】处理正式思考内容
1079 |                 val phase = state.phases[currentPhaseId] ?: return state
1080 | 
1081 |                 val updatedPhase = if(state.titleUpdatesEnabled) {
1082 |                     // 填充标题
1083 |                     phase.copy(title = phase.title + event.text)
1084 |                 } else {
1085 |                     // 填充正文
1086 |                     phase.copy(content = phase.content + event.text)
1087 |                 }
1088 | 
1089 |                 // 首次收到正文，进入TextStreaming步骤
1090 |                 val nextStep = if (!state.textStreamingActive && !state.titleUpdatesEnabled) {
1091 |                     ThinkingStep.TextStreaming
1092 |                 } else {
1093 |                     state.currentStep
1094 |                 }
1095 | 
1096 |                 state.copy(
1097 |                     phases = state.phases.put(currentPhaseId, updatedPhase),
1098 |                     textStreamingActive = if(nextStep == ThinkingStep.TextStreaming) true else state.textStreamingActive,
1099 |                     currentStep = nextStep
1100 |                 ).bump()
1101 |             }
1102 |         }
1103 | 
1104 |         // 🔥 Step 1重构：11步状态转换事件处理
1105 | 
1106 |         is ThinkingEvent.StepTransition -> {
1107 |             timber.log.Timber.tag("TB-STEP").i("🔥 [StepTransition] ${state.currentStep} → ${event.targetStep}, trigger: ${event.trigger}")
1108 | 
1109 |             // 验证状态转换的有效性
1110 |             if (!state.isValidTransition(state.currentStep, event.targetStep)) {
1111 |                 timber.log.Timber.tag("TB-STEP").w("🚨 [StepTransition] 无效的状态转换: ${state.currentStep} → ${event.targetStep}")
1112 |                 return state
1113 |             }
1114 | 
1115 |             state.copy(
1116 |                 currentStep = event.targetStep,
1117 |                 stepTransitionTime = System.currentTimeMillis()
1118 |             ).bump()
1119 |         }
1120 | 
1121 |         is ThinkingEvent.ThinkDetected -> {
1122 |             timber.log.Timber.tag("TB-STEP").i("🔥 [ThinkDetected] 检测到<think>标签")
1123 |             state.copy(
1124 |                 thinkDetected = true,
1125 |                 currentStep = ThinkingStep.ThinkDetection,
1126 |                 stepTransitionTime = System.currentTimeMillis()
1127 |             ).bump()
1128 |         }
1129 | 
1130 |         is ThinkingEvent.ThinkingStarted -> {
1131 |             // 🔥 【v7关键修复】ThinkingStarted 必须清空 preThinking
1132 |             timber.log.Timber.tag("TB-STEP").i("🔥 [v7修复] ThinkingStarted: 清空预思考内容，进入正式思考")
1133 |             state.copy(
1134 |                 thinkingStarted = true,
1135 |                 currentStep = ThinkingStep.ThinkingStart,
1136 |                 preThinking = null, // UI会根据这个null触发fadeOut动画
1137 |                 stepTransitionTime = System.currentTimeMillis()
1138 |             ).bump()
1139 |         }
1140 | 
1141 |         is ThinkingEvent.PhaseManagementActivated -> {
1142 |             if (event.phaseId == "perthink") {
1143 |                 // 🔥 【单卡刷新原则】perthink启动思考框，Phase由PhaseDelta事件创建
1144 |                 timber.log.Timber.tag("TB-STEP").i("🔥 [单卡刷新] perthink阶段启动: phase='perthink'")
1145 |                 state.copy(
1146 |                     thinkDetected = true,
1147 |                     currentPhaseId = "perthink",
1148 |                     currentStep = ThinkingStep.PreThinkingPhase,
1149 |                     stepTransitionTime = System.currentTimeMillis()
1150 |                 ).bump()
1151 |             } else {
1152 |                 // 🔥 【重组刷新逻辑】正式思考阶段，触发UI重组刷新
1153 |                 timber.log.Timber.tag("TB-STEP").i("🔥 [重组刷新] 正式思考阶段: ${event.phaseId}")
1154 |                 state.copy(
1155 |                     phaseManagementActive = true,
1156 |                     currentPhaseId = event.phaseId,
1157 |                     currentStep = ThinkingStep.PhaseManagement,
1158 |                     preThinking = null, // 🔥 关键：清空预思考内容，触发UI重组刷新
1159 |                     stepTransitionTime = System.currentTimeMillis()
1160 |                 ).bump()
1161 |             }
1162 |         }
1163 | 
1164 |         is ThinkingEvent.TitleUpdatesEnabled -> {
1165 |             timber.log.Timber.tag("TB-STEP").i("🔥 [TitleUpdatesEnabled] 标题更新启用: ${event.phaseId} - ${event.title}")
1166 |             state.copy(
1167 |                 titleUpdatesEnabled = true,
1168 |                 currentStep = ThinkingStep.TitleUpdates,
1169 |                 stepTransitionTime = System.currentTimeMillis()
1170 |             ).bump()
1171 |         }
1172 | 
1173 |         is ThinkingEvent.TextStreamingActivated -> {
1174 |             timber.log.Timber.tag("TB-STEP").i("🔥 [TextStreamingActivated] 文本流式传输激活: ${event.phaseId}")
1175 |             state.copy(
1176 |                 textStreamingActive = true,
1177 |                 currentStep = ThinkingStep.TextStreaming,
1178 |                 stepTransitionTime = System.currentTimeMillis()
1179 |             ).bump()
1180 |         }
1181 | 
1182 |         is ThinkingEvent.CompletionCollapseTriggered -> {
1183 |             timber.log.Timber.tag("TB-STEP").i("🔥 [CompletionCollapseTriggered] 完成折叠触发")
1184 |             state.copy(
1185 |                 completionCollapseTriggered = true,
1186 |                 currentStep = ThinkingStep.CompletionCollapse,
1187 |                 stepTransitionTime = System.currentTimeMillis(),
1188 |                 lastPhaseCompleted = state.currentPhaseId
1189 |             ).bump()
1190 |         }
1191 | 
1192 |         is ThinkingEvent.SummaryToggleEnabled -> {
1193 |             timber.log.Timber.tag("TB-STEP").i("🔥 [SummaryToggleEnabled] 摘要切换启用")
1194 |             state.copy(
1195 |                 summaryToggleEnabled = true,
1196 |                 currentStep = ThinkingStep.SummaryToggle,
1197 |                 stepTransitionTime = System.currentTimeMillis()
1198 |             ).bump()
1199 |         }
1200 | 
1201 |         is ThinkingEvent.RichTextRenderingActivated -> {
1202 |             timber.log.Timber.tag("TB-STEP").i("🔥 [RichTextRenderingActivated] 富文本渲染激活")
1203 |             state.copy(
1204 |                 richTextRenderingActive = true,
1205 |                 currentStep = ThinkingStep.RichTextRendering,
1206 |                 stepTransitionTime = System.currentTimeMillis(),
1207 |             ).bump()
1208 |         }
1209 | 
1210 |         is ThinkingEvent.LastPhaseRenderingComplete -> {
1211 |             // 🔥 【超时机制优化】最后一个phase渲染完成，立即进入富文本渲染
1212 |             timber.log.Timber
1213 |                 .tag("PHASE-DEBUG")
1214 |                 .i("🔥 [超时机制优化] LastPhaseRenderingComplete: ${event.phaseId}")
1215 |             timber.log.Timber
1216 |                 .tag("PHASE-DEBUG")
1217 |                 .i("🔥 立即进入富文本渲染，无需等待15秒超时")
1218 | 
1219 |             state
1220 |                 .copy(
1221 |                     isWaitingForFinalRendering = false,
1222 |                     isStreaming = false, // 立即结束流式传输
1223 |                     currentStep = ThinkingStep.RichTextRendering,
1224 |                     stepTransitionTime = System.currentTimeMillis(),
1225 |                     lastPhaseCompleted = event.phaseId,
1226 |                 ).bump()
1227 |         }
1228 | 
1229 | 
1230 |     }
1231 | }
1232 | 
1233 | /**
1234 |  * 辅助函数：从Markdown文本中提取要点
1235 |  * 按照总结card.md文档的要求实现
1236 |  */
1237 | internal fun extractBulletPoints(markdown: String): List<String> {
1238 |     return markdown
1239 |         .split("\n")
1240 |         .filter { line ->
1241 |             val trimmed = line.trim()
1242 |             trimmed.startsWith("- ") ||
1243 |                 trimmed.startsWith("* ") ||
1244 |                 trimmed.matches(Regex("\\d+\\.\\s+.*"))
1245 |         }
1246 |         .map { line ->
1247 |             line.trim()
1248 |                 .removePrefix("- ")
1249 |                 .removePrefix("* ")
1250 |                 .replaceFirst(Regex("^\\d+\\.\\s+"), "")
1251 |         }
1252 |         .filter { it.isNotBlank() }
1253 |         .take(5) // 最多5条要点
1254 | }
1255 | 
1256 | /**
1257 |  * 格式化思考时间
1258 |  *
1259 |  * @param durationMs 思考时长（毫秒）
1260 |  * @return 格式化的时间字符串，如"1分23秒"、"45秒"
1261 |  */
1262 | private fun formatThinkingDuration(durationMs: Long): String {
1263 |     val seconds = (durationMs / 1000).toInt()
1264 |     val minutes = seconds / 60
1265 |     val remainingSeconds = seconds % 60
1266 | 
1267 |     return when {
1268 |         minutes > 0 -> "${minutes}分${remainingSeconds}秒"
1269 |         else -> "${remainingSeconds}秒"
1270 |     }
1271 | }
1272 | 
1273 | // 🔥 【已移除】calculateTokenCountFromState函数 - 重复实现
1274 | // Token计算应该统一使用OpenAiTokenizer，避免重复的估算逻辑

```

`main\kotlin\com\example\gymbro\features\thinkingbox\domain\parser\StreamingThinkingMLParser.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.domain.parser
   2 | 
   3 | import com.example.gymbro.features.thinkingbox.domain.bus.SemanticEventBus
   4 | import com.example.gymbro.features.thinkingbox.domain.guardrail.ThinkingMLGuardrail
   5 | import com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent
   6 | import kotlinx.coroutines.CoroutineDispatcher
   7 | import kotlinx.coroutines.channels.BufferOverflow
   8 | import kotlinx.coroutines.flow.Flow
   9 | import kotlinx.coroutines.flow.buffer
  10 | import kotlinx.coroutines.flow.channelFlow
  11 | import kotlinx.coroutines.flow.mapNotNull
  12 | import kotlinx.coroutines.withContext
  13 | import timber.log.Timber
  14 | import java.util.concurrent.atomic.AtomicBoolean
  15 | import javax.inject.Inject
  16 | import javax.inject.Singleton
  17 | 
  18 | /**
  19 |  * StreamingThinkingMLParser · **v4.0 – 按照thinkingbox解析方案.md实现**
  20 |  * ---------------------------------------------------------------
  21 |  * 实现文档要求的"绝不能再丢Tag"的字符级FSM解析器
  22 |  *
  23 |  * 🔥 核心特性：
  24 |  * • <think>立即变"占位卡" - 0-delay渲染
  25 |  * • 跨chunk标签完整识别 - 2KB滑动窗口
  26 |  * • 缺>自动补全 - 遇到\n或流结束时自动闭合
  27 |  * • 未知标签容忍 - 白名单外的标签转为TextChunk
  28 |  * • 自适应空白压缩 - 连续空白转单空格
  29 |  *
  30 |  * 🎯 按照文档的"四道护栏"实现：
  31 |  * 1. 跨块滑窗 - 2KB字符级FSM
  32 |  * 2. 自动闭合 - 流结束或遇\n时补TagClosed
  33 |  * 3. 未知标签兜底 - 白名单机制
  34 |  * 4. 预思考隔离 - currentPhaseId!=null时才映射正文
  35 |  */
  36 | @Singleton
  37 | class StreamingThinkingMLParser @Inject constructor(
  38 |     private val guardrail: ThinkingMLGuardrail,
  39 |     private val rawChunkProcessor: com.example.gymbro.features.thinkingbox.domain.processor.RawChunkProcessor,
  40 |     private val ioDispatcher: CoroutineDispatcher
  41 | ) {
  42 | 
  43 | 
  44 | 
  45 |     /* ───────────────────── 配置常量 ───────────────────── */
  46 |     private companion object {
  47 |         const val TAG = "StreamingThinkingMLParser"
  48 |     }
  49 | 
  50 |     // 🔥 【V2核心改造】简化状态管理 - 只保留必要的状态标志
  51 |     private var inThinkTag = false        // 是否在<think>标签内，用于PreThinkChunk生成
  52 |     private var inFinalTag = false        // ✅ 新增：是否在<final>标签内，用于FinalArrived生成
  53 |     private val finalContentBuffer = StringBuilder() // ✅ 新增：聚合<final>标签内的内容
  54 | 
  55 |     // 解析控制
  56 |     private val stillParsing = AtomicBoolean(true)
  57 | 
  58 |     // 🔥 【循环解析修复】防止重复监听RawTokenBus的守卫
  59 |     private val isListening = AtomicBoolean(false)
  60 | 
  61 |     // 🔥 系统级修复：新的XML扫描器
  62 |     private val xmlScanner = XmlStreamScanner()
  63 | 
  64 |     /* ───────────────────── 公共API ───────────────────── */
  65 |     /**
  66 |      * 🔥 【循环解析修复】将parse方法改为suspend fun，防止重复监听
  67 |      */
  68 |     suspend fun parse() {
  69 |         // 🔥 【循环解析修复】防止重复监听RawTokenBus
  70 |         if (!isListening.compareAndSet(false, true)) {
  71 |             Timber.tag(TAG).w("🚨 [循环解析修复] Parser已在监听RawTokenBus，忽略重复调用")
  72 |             return
  73 |         }
  74 | 
  75 |         // withContext确保在合适的线程执行
  76 |         withContext(ioDispatcher) {
  77 |             reset()
  78 |             Timber.tag(TAG).i("🔥 [循环解析修复] Parser开始监听RawTokenBus...")
  79 |             try {
  80 |                 // 使用collect，它会挂起直到Flow完成或scope被取消
  81 |                 com.example.gymbro.core.network.bus.RawTokenBus.tokenFlow
  82 |                     .collect { chunk ->
  83 |                         // 你的解析逻辑（feedChunk）已经很好了，保持不变
  84 |                         feedChunk(chunk) { event ->
  85 |                             com.example.gymbro.features.thinkingbox.domain.bus.SemanticEventBus.send(event)
  86 |                         }
  87 |                     }
  88 | 
  89 |                 // 当上游Flow完成时（例如网络断开）
  90 |                 com.example.gymbro.features.thinkingbox.domain.bus.SemanticEventBus.send(SemanticEvent.StreamFinished())
  91 |                 Timber.tag(TAG).i("🔥 [循环解析修复] RawTokenBus流结束，发送StreamFinished事件。")
  92 |             } catch(e: kotlinx.coroutines.CancellationException) {
  93 |                 Timber.tag(TAG).w("🔥 [循环解析修复] Parser监听被取消。")
  94 |                 // 不需要发送StreamFinished，因为这是外部主动取消
  95 |             } catch (t: Throwable) {
  96 |                 Timber.tag(TAG).e(t, "🚨 [循环解析修复] Parser处理异常")
  97 |                 val parseError = com.example.gymbro.features.thinkingbox.domain.model.ParseError(
  98 |                     type = com.example.gymbro.features.thinkingbox.domain.model.ErrorType.PARSING_ERROR,
  99 |                     message = "Parser异常: ${t.message}"
 100 |                 )
 101 |                 com.example.gymbro.features.thinkingbox.domain.bus.SemanticEventBus.send(SemanticEvent.ParseErrorEvent(parseError))
 102 |             } finally {
 103 |                 // 🔥 【循环解析修复】监听结束时重置守卫，允许下次监听
 104 |                 isListening.set(false)
 105 |                 Timber.tag(TAG).i("🔥 [循环解析修复] Parser监听结束，守卫已重置")
 106 |             }
 107 |         }
 108 |     }
 109 | 
 110 |     fun reset() {
 111 |         inThinkTag = false  // 🔥 【v6修复】重置think标签状态
 112 |         inFinalTag = false  // ✅ 新增：重置final标签状态
 113 |         finalContentBuffer.clear() // ✅ 新增：清空final内容缓冲区
 114 |         stillParsing.set(true)
 115 |         xmlScanner.clear()
 116 |         // 🔥 【循环解析修复】注意：不重置isListening，因为监听可能仍在进行
 117 |         Timber.tag(TAG).i("🔥 [循环解析修复] StreamingThinkingMLParser 状态已完全重置，监听状态保持")
 118 |     }
 119 | 
 120 | 
 121 | 
 122 | 
 123 | 
 124 | 
 125 |     /* ───────────────────── FSM核心方法 ───────────────────── */
 126 | 
 127 |     /**
 128 |      * 🔥 系统级修复：使用XmlStreamScanner处理chunk
 129 |      */
 130 |     private suspend fun feedChunk(chunk: String, emit: suspend (SemanticEvent) -> Unit) {
 131 |         try {
 132 |             // 使用新的XML扫描器处理chunk
 133 |             val tokens = xmlScanner.feed(chunk)
 134 | 
 135 |             // 🔥 TB-RAW日志：详细的token解析信息（将被聚合器聚合）
 136 |             Timber.tag("TB-RAW").d("XML扫描结果: ${chunk.length}字符 → ${tokens.size}个Token")
 137 | 
 138 |             Timber.tag(TAG).d("🔍 [系统修复] 处理chunk: ${chunk.length}字符 → ${tokens.size}个Token")
 139 | 
 140 |             // 处理每个Token
 141 |             for (token in tokens) {
 142 |                 // 🔥 TB-RAW日志：记录每个token处理
 143 |                 Timber.tag("TB-RAW").d("处理Token: ${token::class.simpleName} - $token")
 144 | 
 145 |                 when (token) {
 146 |                     is XmlStreamScanner.TagOpen -> {
 147 |                         handleTagOpen(token, emit)
 148 |                     }
 149 |                     is XmlStreamScanner.TagClose -> {
 150 |                         handleTagClose(token, emit)
 151 |                     }
 152 |                     is XmlStreamScanner.Text -> {
 153 |                         handleText(token.content, emit)
 154 |                     }
 155 |                 }
 156 |             }
 157 |         } catch (e: Exception) {
 158 |             Timber.tag(TAG).e(e, "🚨 [系统修复] feedChunk处理失败")
 159 |             // 降级处理：作为纯文本
 160 |             handleText(chunk, emit)
 161 |         }
 162 |     }
 163 | 
 164 | 
 165 | 
 166 |     /**
 167 |      * 🔥 【V2核心改造】处理标签开启 - 上下文感知版本
 168 |      */
 169 |     private suspend fun handleTagOpen(token: XmlStreamScanner.TagOpen, emit: suspend (SemanticEvent) -> Unit) {
 170 |         val tagName = token.name.lowercase()
 171 |         val attrs = token.attributes
 172 | 
 173 |         // ✅ 新增：处理 <final> 标签
 174 |         if (tagName == "final") {
 175 |             inFinalTag = true
 176 |             finalContentBuffer.clear()
 177 |             emit(SemanticEvent.TagOpened(tagName, attrs)) // 保持事件发送，用于触发状态机
 178 |             Timber.tag(TAG).d("🔥 [Final修复] 进入Final模式")
 179 |             return
 180 |         }
 181 | 
 182 |         // 🔥 【v7统一方案】进入<think>标签，生成phase id="perthink"
 183 |         if (tagName == "think") {
 184 |             inThinkTag = true
 185 |             // 🔥 统一处理：将<think>标签转换为phase id="perthink"
 186 |             val perthinkAttrs = mapOf("id" to "perthink")
 187 |             Timber.tag("PHASE-DEBUG").e("🔍 Parser生成perthink: <think> → <phase id='perthink'>")
 188 |             emit(SemanticEvent.TagOpened("phase", perthinkAttrs))
 189 |             return
 190 |         }
 191 | 
 192 |         // 🔥 【v7修复】在预思考模式下，清洗特定标签但保留文本内容
 193 |         if (inThinkTag) {
 194 |             if (tagName.startsWith("phase:")) {
 195 |                 // 🔥 清洗 <phase:PLAN> 类型标签，忽略标签本身
 196 |                 Timber.tag(TAG).d("🔥 [v7修复] 预思考模式：清洗标签 <${token.name}>")
 197 |                 return
 198 |             } else {
 199 |                 // 其他标签也忽略
 200 |                 Timber.tag(TAG).d("🔥 [V2重构] 预思考模式：忽略标签 <${token.name}>")
 201 |                 return
 202 |             }
 203 |         }
 204 | 
 205 |         // 正常处理其他标签
 206 |         if (tagName == "phase") {
 207 |             val phaseId = attrs["id"] ?: "unknown"
 208 |             Timber.tag("PHASE-DEBUG").e("🔍 Parser处理正常phase标签: <phase id='$phaseId'> | attrs=$attrs")
 209 |         }
 210 |         emit(SemanticEvent.TagOpened(token.name, attrs))
 211 |         Timber.tag(TAG).d("🔥 [V2重构] 发送TagOpened事件: ${token.name}")
 212 |     }
 213 | 
 214 |     /**
 215 |      * 🔥 【V2核心改造】处理标签关闭 - 上下文感知版本
 216 |      */
 217 |     private suspend fun handleTagClose(token: XmlStreamScanner.TagClose, emit: suspend (SemanticEvent) -> Unit) {
 218 |         val tagName = token.name.lowercase()
 219 | 
 220 |         // 🔥 【重复检测修复】添加标签关闭的详细追踪
 221 |         if (tagName == "phase") {
 222 |             Timber.tag("PHASE-DEBUG").e("🔍 Parser处理</phase>标签关闭: ${token.name} | 线程=${Thread.currentThread().name}")
 223 |         }
 224 | 
 225 |         // ✅ 新增：处理 </final> 标签
 226 |         if (tagName == "final") {
 227 |             inFinalTag = false
 228 |             // 发送聚合好的 FinalArrived 事件
 229 |             emit(SemanticEvent.FinalArrived(finalContentBuffer.toString()))
 230 |             finalContentBuffer.clear()
 231 |             // 同样发送 TagClosed 事件
 232 |             emit(SemanticEvent.TagClosed(tagName))
 233 |             Timber.tag(TAG).d("🔥 [Final修复] 退出Final模式，发送FinalArrived事件")
 234 |             return
 235 |         }
 236 | 
 237 |         // 🔥 【v7统一方案】退出<think>标签，关闭预思考模式
 238 |         if (tagName == "think") {
 239 |             inThinkTag = false
 240 |             // 🔥 统一处理：将</think>标签转换为</phase>
 241 |             emit(SemanticEvent.TagClosed("phase"))
 242 |             // 发出一个特殊事件，标记<think>块的结束
 243 |             emit(SemanticEvent.RawThinkingClosed)
 244 |             Timber.tag(TAG).d("🔥 [v7统一] 退出预思考模式，关闭phase id='perthink'")
 245 |             return
 246 |         }
 247 | 
 248 |         // 🔥 【v7修复】在预思考模式下，清洗特定闭合标签
 249 |         if (inThinkTag) {
 250 |             if (tagName.startsWith("phase:")) {
 251 |                 // 🔥 清洗 </phase:PLAN> 类型标签
 252 |                 Timber.tag(TAG).d("🔥 [v7修复] 预思考模式：清洗闭合标签 </${token.name}>")
 253 |                 return
 254 |             } else {
 255 |                 // 其他闭合标签也忽略
 256 |                 Timber.tag(TAG).d("🔥 [V2重构] 预思考模式：忽略闭合标签 </${token.name}>")
 257 |                 return
 258 |             }
 259 |         }
 260 | 
 261 |         // 正常处理其他闭合标签
 262 |         if (tagName == "phase") {
 263 |             Timber.tag("PHASE-DEBUG").e("🔍 Parser发送</phase>事件: ${token.name} | 线程=${Thread.currentThread().name}")
 264 |         }
 265 |         emit(SemanticEvent.TagClosed(token.name))
 266 |         Timber.tag(TAG).d("🔥 [V2重构] 发送TagClosed事件: ${token.name}")
 267 |     }
 268 | 
 269 |     /**
 270 |      * 🔥 【V2核心改造】处理文本内容 - 上下文感知版本
 271 |      */
 272 |     private suspend fun handleText(text: String, emit: suspend (SemanticEvent) -> Unit) {
 273 |         if (text.isBlank()) return
 274 | 
 275 |         // ✅ 新增：如果在 final 标签内，则将文本追加到缓冲区
 276 |         if (inFinalTag) {
 277 |             finalContentBuffer.append(text)
 278 |             Timber.tag(TAG).d("🔥 [Final修复] 缓冲Final内容: +${text.length}字符")
 279 |             return // 不再作为 TextChunk 发送
 280 |         }
 281 | 
 282 |         // 🔥 【v7统一方案】统一发送TextChunk，由DomainMapper根据phaseId处理
 283 |         emit(SemanticEvent.TextChunk(text))
 284 |         if (inThinkTag) {
 285 |             Timber.tag(TAG).d("🔥 [v7统一] 预思考模式：发送TextChunk for phase='perthink'")
 286 |         } else {
 287 |             Timber.tag(TAG).d("🔥 [v7统一] 正常模式：发送TextChunk")
 288 |         }
 289 |     }
 290 | 
 291 | 
 292 | 
 293 | 
 294 | 
 295 | }
 296 | 
 297 | /**
 298 |  * 🔥 [多轮对话架构升级]
 299 |  * 流式解析器，现在是一个无状态的、可重入的挂起函数。
 300 |  *
 301 |  * 这个函数替代了原有的有状态的 StreamingThinkingMLParser.parse() 方法，
 302 |  * 实现了完全的状态隔离和可重入性。
 303 |  *
 304 |  * @param messageId 当前对话的ID，用于注入到生成的 SemanticEvent 中。
 305 |  * @param tokens 专属的原始 token 输入流。
 306 |  * @param onEvent 解析出语义事件后的回调。
 307 |  */
 308 | suspend fun parseTokenStream(
 309 |     messageId: String,
 310 |     tokens: Flow<String>,
 311 |     onEvent: suspend (SemanticEvent) -> Unit
 312 | ) {
 313 |     // 每次调用都创建一个新的扫描器，确保无状态
 314 |     val xmlScanner = XmlStreamScanner()
 315 |     val guardrail = ThinkingMLGuardrail()
 316 |     val rawChunkProcessor = com.example.gymbro.features.thinkingbox.domain.processor.RawChunkProcessor()
 317 | 
 318 |     // 局部状态变量，随函数调用结束而销毁
 319 |     var inThinkTag = false
 320 |     var inFinalTag = false
 321 |     val finalContentBuffer = StringBuilder()
 322 | 
 323 |     val TAG = "StreamingParser"
 324 |     Timber.tag(TAG).d("🚀 [$messageId] Starting new stateless parsing process.")
 325 | 
 326 |     try {
 327 |         tokens.collect { chunk ->
 328 |             // 预处理chunk
 329 |             val processedChunk = rawChunkProcessor.preprocess(chunk)
 330 |             val sanitizedChunk = guardrail.sanitizeToken(processedChunk)
 331 | 
 332 |             // 使用XML扫描器处理chunk
 333 |             val xmlTokens = xmlScanner.feed(sanitizedChunk)
 334 | 
 335 |             Timber.tag("TB-RAW").d("[$messageId] XML扫描结果: ${sanitizedChunk.length}字符 → ${xmlTokens.size}个Token")
 336 | 
 337 |             // 处理每个Token
 338 |             for (token in xmlTokens) {
 339 |                 Timber.tag("TB-RAW").d("[$messageId] 处理Token: ${token::class.simpleName} - $token")
 340 | 
 341 |                 when (token) {
 342 |                     is XmlStreamScanner.TagOpen -> {
 343 |                         val tagName = token.name.lowercase()
 344 |                         val attrs = token.attributes
 345 | 
 346 |                         // 处理 <final> 标签
 347 |                         if (tagName == "final") {
 348 |                             inFinalTag = true
 349 |                             finalContentBuffer.clear()
 350 |                             onEvent(SemanticEvent.TagOpened(tagName, attrs))
 351 |                             Timber.tag(TAG).d("🔥 [$messageId] 进入Final模式")
 352 |                             continue
 353 |                         }
 354 | 
 355 |                         // 处理 <think> 标签，转换为 phase id="perthink"
 356 |                         if (tagName == "think") {
 357 |                             inThinkTag = true
 358 |                             val perthinkAttrs = mapOf("id" to "perthink")
 359 |                             Timber.tag("PHASE-DEBUG").e("🔍 [$messageId] Parser生成perthink: <think> → <phase id='perthink'>")
 360 |                             onEvent(SemanticEvent.TagOpened("phase", perthinkAttrs))
 361 |                             continue
 362 |                         }
 363 | 
 364 |                         // 在预思考模式下，清洗特定标签但保留文本内容
 365 |                         if (inThinkTag) {
 366 |                             if (tagName.startsWith("phase:")) {
 367 |                                 Timber.tag(TAG).d("🔥 [$messageId] 预思考模式：清洗标签 <${token.name}>")
 368 |                                 continue
 369 |                             } else {
 370 |                                 Timber.tag(TAG).d("🔥 [$messageId] 预思考模式：忽略标签 <${token.name}>")
 371 |                                 continue
 372 |                             }
 373 |                         }
 374 | 
 375 |                         // 正常处理其他标签
 376 |                         if (tagName == "phase") {
 377 |                             val phaseId = attrs["id"] ?: "unknown"
 378 |                             Timber.tag("PHASE-DEBUG").e("🔍 [$messageId] Parser处理正常phase标签: <phase id='$phaseId'> | attrs=$attrs")
 379 |                         }
 380 |                         onEvent(SemanticEvent.TagOpened(token.name, attrs))
 381 |                         Timber.tag(TAG).d("🔥 [$messageId] 发送TagOpened事件: ${token.name}")
 382 |                     }
 383 | 
 384 |                     is XmlStreamScanner.TagClose -> {
 385 |                         val tagName = token.name.lowercase()
 386 | 
 387 |                         if (tagName == "phase") {
 388 |                             Timber.tag("PHASE-DEBUG").e("🔍 [$messageId] Parser处理</phase>标签关闭: ${token.name}")
 389 |                         }
 390 | 
 391 |                         // 处理 </final> 标签
 392 |                         if (tagName == "final") {
 393 |                             inFinalTag = false
 394 |                             onEvent(SemanticEvent.FinalArrived(finalContentBuffer.toString()))
 395 |                             finalContentBuffer.clear()
 396 |                             onEvent(SemanticEvent.TagClosed(tagName))
 397 |                             Timber.tag(TAG).d("🔥 [$messageId] 退出Final模式，发送FinalArrived事件")
 398 |                             continue
 399 |                         }
 400 | 
 401 |                         // 处理 </think> 标签，转换为 </phase>
 402 |                         if (tagName == "think") {
 403 |                             inThinkTag = false
 404 |                             onEvent(SemanticEvent.TagClosed("phase"))
 405 |                             onEvent(SemanticEvent.RawThinkingClosed)
 406 |                             Timber.tag(TAG).d("🔥 [$messageId] 退出预思考模式，关闭phase id='perthink'")
 407 |                             continue
 408 |                         }
 409 | 
 410 |                         // 在预思考模式下，清洗特定闭合标签
 411 |                         if (inThinkTag) {
 412 |                             if (tagName.startsWith("phase:")) {
 413 |                                 Timber.tag(TAG).d("🔥 [$messageId] 预思考模式：清洗闭合标签 </${token.name}>")
 414 |                                 continue
 415 |                             } else {
 416 |                                 Timber.tag(TAG).d("🔥 [$messageId] 预思考模式：忽略闭合标签 </${token.name}>")
 417 |                                 continue
 418 |                             }
 419 |                         }
 420 | 
 421 |                         // 正常处理其他闭合标签
 422 |                         if (tagName == "phase") {
 423 |                             Timber.tag("PHASE-DEBUG").e("🔍 [$messageId] Parser发送</phase>事件: ${token.name}")
 424 |                         }
 425 |                         onEvent(SemanticEvent.TagClosed(token.name))
 426 |                         Timber.tag(TAG).d("🔥 [$messageId] 发送TagClosed事件: ${token.name}")
 427 |                     }
 428 | 
 429 |                     is XmlStreamScanner.Text -> {
 430 |                         val text = token.content
 431 |                         if (text.isBlank()) continue
 432 | 
 433 |                         // 如果在 final 标签内，则将文本追加到缓冲区
 434 |                         if (inFinalTag) {
 435 |                             finalContentBuffer.append(text)
 436 |                             Timber.tag(TAG).d("🔥 [$messageId] 缓冲Final内容: +${text.length}字符")
 437 |                             continue
 438 |                         }
 439 | 
 440 |                         // 统一发送TextChunk，由DomainMapper根据phaseId处理
 441 |                         onEvent(SemanticEvent.TextChunk(text))
 442 |                         if (inThinkTag) {
 443 |                             Timber.tag(TAG).d("🔥 [$messageId] 预思考模式：发送TextChunk for phase='perthink'")
 444 |                         } else {
 445 |                             Timber.tag(TAG).d("🔥 [$messageId] 正常模式：发送TextChunk")
 446 |                         }
 447 |                     }
 448 |                 }
 449 |             }
 450 |         }
 451 | 
 452 |         onEvent(SemanticEvent.StreamFinished())
 453 |         Timber.tag(TAG).d("✅ [$messageId] Parsing finished.")
 454 |     } catch (e: Exception) {
 455 |         Timber.tag(TAG).e(e, "🚨 [$messageId] Error during parsing.")
 456 |         val parseError = com.example.gymbro.features.thinkingbox.domain.model.ParseError(
 457 |             type = com.example.gymbro.features.thinkingbox.domain.model.ErrorType.PARSING_ERROR,
 458 |             message = "Parser异常: ${e.message}"
 459 |         )
 460 |         onEvent(SemanticEvent.ParseErrorEvent(parseError))
 461 |     }
 462 | }

```

`main\kotlin\com\example\gymbro\features\thinkingbox\domain\parser\XmlStreamScanner.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.domain.parser
   2 | 
   3 | import timber.log.Timber
   4 | 
   5 | /**
   6 |  * XmlStreamScanner - 流式XML扫描器 (v2.0 - 跨chunk解析修复版)
   7 |  *
   8 |  * 🔥 系统级修复：彻底解决跨chunk标签解析问题
   9 |  * - 支持跨chunk的标签识别：<pha + se id="1">
  10 |  * - 双缓冲机制：缓冲不完整的标签直到完整
  11 |  * - Look-ahead策略：避免"残缺tag当纯文本"的问题
  12 |  * - 属性跨行支持：id="1"\n title="分析"
  13 |  */
  14 | class XmlStreamScanner {
  15 | 
  16 |     private val buffer = StringBuilder()
  17 |     private val TAG = "XML-SCANNER"
  18 | 
  19 |     /**
  20 |      * Token类型定义
  21 |      */
  22 |     sealed interface Token
  23 | 
  24 |     data class TagOpen(
  25 |         val name: String,
  26 |         val attributes: Map<String, String> = emptyMap(),
  27 |         val isSelfClosing: Boolean = false
  28 |     ) : Token
  29 | 
  30 |     data class TagClose(val name: String) : Token
  31 | 
  32 |     data class Text(val content: String) : Token
  33 | 
  34 |     /**
  35 |      * 🔥 核心方法：流式处理字符串，返回完整的Token (v2.0 - 跨chunk修复版)
  36 |      *
  37 |      * 双缓冲+look-ahead策略：
  38 |      * - 若一次没找到 `>`, 不输出任何tag，留到下次feed
  39 |      * - 文本安全：chunk断在中间时，tag前部不被误当作文本
  40 |      * - 确保出现 `if (gtIndex == -1) break@loop` 修复跨chunk标签解析
  41 |      */
  42 |     fun feed(str: String): List<Token> {
  43 |         buffer.append(str)
  44 |         val tokens = mutableListOf<Token>()
  45 | 
  46 |         loop@ while (true) {
  47 |             val ltIndex = buffer.indexOf("<")
  48 |             when (ltIndex) {
  49 |                 -1 -> {
  50 |                     // 无tag起点 → 全当文本
  51 |                     if (buffer.isNotEmpty()) {
  52 |                         tokens.add(Text(buffer.toString()))
  53 |                         buffer.clear()
  54 |                     }
  55 |                     break@loop
  56 |                 }
  57 |                 else -> {
  58 |                     if (ltIndex > 0) {
  59 |                         // 先输出ltIndex前的纯文本
  60 |                         val textContent = buffer.substring(0, ltIndex)
  61 |                         if (textContent.isNotEmpty()) {
  62 |                             tokens.add(Text(textContent))
  63 |                         }
  64 |                         buffer.delete(0, ltIndex)
  65 |                     }
  66 | 
  67 |                     // 现在buffer[0]=='<'
  68 |                     val gtIndex = buffer.indexOf(">", startIndex = 1)
  69 |                     if (gtIndex == -1) {
  70 |                         // tag未闭合 → 等下个chunk
  71 |                         // 🔥 修复关键点：确保跨chunk标签等待完整
  72 |                         break@loop
  73 |                     }
  74 | 
  75 |                     // 提取完整标签内容
  76 |                     val tagContent = buffer.substring(1, gtIndex)
  77 |                     val token = parseTag(tagContent)
  78 |                     if (token != null) {
  79 |                         tokens.add(token)
  80 |                     }
  81 | 
  82 |                     buffer.delete(0, gtIndex + 1)
  83 |                 }
  84 |             }
  85 |         }
  86 | 
  87 |         Timber.tag(TAG).v("🔍 [跨chunk修复] Feed处理: 输入${str.length}字符, 输出${tokens.size}个Token, 缓冲剩余${buffer.length}字符")
  88 | 
  89 |         return tokens
  90 |     }
  91 | 
  92 |     /**
  93 |      * 🔥 解析标签内容
  94 |      */
  95 |     private fun parseTag(tagContent: String): Token? {
  96 |         if (tagContent.isEmpty()) return null
  97 | 
  98 |         val trimmed = tagContent.trim()
  99 | 
 100 |         // 处理闭合标签
 101 |         if (trimmed.startsWith("/")) {
 102 |             val tagName = trimmed.substring(1).trim()
 103 |             return TagClose(tagName)
 104 |         }
 105 | 
 106 |         // 处理自闭合标签
 107 |         val isSelfClosing = trimmed.endsWith("/")
 108 |         val contentToProcess = if (isSelfClosing) {
 109 |             trimmed.substring(0, trimmed.length - 1).trim()
 110 |         } else {
 111 |             trimmed
 112 |         }
 113 | 
 114 |         // 解析标签名和属性
 115 |         val parts = contentToProcess.split(" ", limit = 2)
 116 |         val tagName = parts[0]
 117 |         val attributes = if (parts.size > 1) {
 118 |             parseAttributes(parts[1])
 119 |         } else {
 120 |             emptyMap()
 121 |         }
 122 | 
 123 |         return TagOpen(tagName, attributes, isSelfClosing)
 124 |     }
 125 | 
 126 |     /**
 127 |      * 🔥 解析属性 (v2.0 - 支持跨行属性)
 128 |      *
 129 |      * 支持属性跨行：id="1"\n title="分析"
 130 |      */
 131 |     private fun parseAttributes(attrString: String): Map<String, String> {
 132 |         val attributes = mutableMapOf<String, String>()
 133 | 
 134 |         // 🔥 跨行属性支持：使用DOT_MATCHES_ALL模式
 135 |         val regex = """(\w+)="(.*?)"""".toRegex(RegexOption.DOT_MATCHES_ALL)
 136 |         regex.findAll(attrString).forEach { match ->
 137 |             val key = match.groupValues[1]
 138 |             val value = match.groupValues[2]
 139 |             attributes[key] = value
 140 |         }
 141 | 
 142 |         return attributes
 143 |     }
 144 | 
 145 |     /**
 146 |      * 清空缓冲区
 147 |      */
 148 |     fun clear() {
 149 |         buffer.clear()
 150 |     }
 151 | 
 152 |     /**
 153 |      * 获取当前缓冲区内容（用于调试）
 154 |      */
 155 |     fun getBufferContent(): String = buffer.toString()
 156 | 
 157 |     /**
 158 |      * 检查是否有未完成的标签
 159 |      */
 160 |     fun hasIncompleteTag(): Boolean = buffer.contains("<") && !buffer.contains(">")
 161 | }

```

`main\kotlin\com\example\gymbro\features\thinkingbox\domain\processor\RawChunkProcessor.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.domain.processor
   2 | 
   3 | import timber.log.Timber
   4 | import javax.inject.Inject
   5 | import javax.inject.Singleton
   6 | 
   7 | /**
   8 |  * 原始chunk预处理器
   9 |  *
  10 |  * 🎯 P0修复：预清洗非法 `<phase:XXX>` 标签，保证XML合法
  11 |  *
  12 |  * 问题：AI输出 `<phase:PLAN>我们正在<phase:PLAN>帮助...`
  13 |  * 导致：XML解析器把 `<phase:PLAN>` 当成未闭合标签，吞掉其后的文本
  14 |  * 解决：在XML解析前预清洗，移除所有非法标签
  15 |  */
  16 | @Singleton
  17 | class RawChunkProcessor @Inject constructor() {
  18 | 
  19 |     companion object {
  20 |         private const val TAG = "RAW-PROCESSOR"
  21 | 
  22 |         // 🔥 【用户修复】只删除<phase:XXX>标签，保留文本内容
  23 |         // 匹配 <phase:XXX>文本内容</phase:XXX> 并提取文本内容
  24 |         private val PHASE_TAG_WITH_CONTENT = Regex("<phase:([A-Z_]+)>(.*?)</phase:\\1>", RegexOption.DOT_MATCHES_ALL)
  25 | 
  26 |         // 🔥 匹配单独的开始和结束标签
  27 |         private val PHASE_START_TAG = Regex("<phase:[A-Z_]+>")
  28 |         private val PHASE_END_TAG = Regex("</phase:[A-Z_]+>")
  29 | 
  30 |         // 🔥 其他可能的非法标签模式 (保持现有防御)
  31 |         private val OTHER_ILLEGAL_PATTERNS = listOf(
  32 |             Regex("<[a-zA-Z]+:[a-zA-Z_]+>"),   // 匹配 <any:any> 格式，但不包括合法的XML命名空间
  33 |             Regex("<phase\\s+[^>]*>"),           // <phase xxx> 格式
  34 |         )
  35 |     }
  36 | 
  37 |     /**
  38 |      * 预处理原始chunk，清洗非法标签
  39 |      *
  40 |      * 🔥 【用户修复】只删除<phase:XXX>标签，保留文本内容
  41 |      * 例如：<phase:PLAN>我们正在分析</phase:PLAN> → 我们正在分析
  42 |      *
  43 |      * @param rawChunk AI输出的原始chunk
  44 |      * @return 清洗后的合法XML chunk
  45 |      */
  46 |     fun preprocess(rawChunk: String): String {
  47 |         if (rawChunk.isEmpty()) return rawChunk
  48 | 
  49 |         var currentChunk = rawChunk
  50 | 
  51 |         // 🔥 【用户修复】第一步：处理完整的<phase:XXX>文本</phase:XXX>，保留文本内容
  52 |         PHASE_TAG_WITH_CONTENT.findAll(currentChunk).forEach { match ->
  53 |             val fullMatch = match.value
  54 |             val textContent = match.groupValues[2] // 提取文本内容
  55 |             Timber.tag(TAG).i("🔥 [用户修复] 提取phase标签内容: '$fullMatch' → '$textContent'")
  56 |             currentChunk = currentChunk.replace(fullMatch, textContent)
  57 |         }
  58 | 
  59 |         // 🔥 【用户修复】第二步：删除单独的开始和结束标签
  60 |         if (PHASE_START_TAG.containsMatchIn(currentChunk)) {
  61 |             Timber.tag(TAG).w("🔥 [用户修复] 删除phase开始标签")
  62 |             currentChunk = currentChunk.replace(PHASE_START_TAG, "")
  63 |         }
  64 | 
  65 |         if (PHASE_END_TAG.containsMatchIn(currentChunk)) {
  66 |             Timber.tag(TAG).w("🔥 [用户修复] 删除phase结束标签")
  67 |             currentChunk = currentChunk.replace(PHASE_END_TAG, "")
  68 |         }
  69 | 
  70 |         // 🔥 第三步：处理其他非法标签模式
  71 |         OTHER_ILLEGAL_PATTERNS.forEach { pattern ->
  72 |             if (pattern.containsMatchIn(currentChunk)) {
  73 |                 Timber.tag(TAG).w("清洗其他非法标签, pattern: ${pattern.pattern}")
  74 |                 currentChunk = currentChunk.replace(pattern, "")
  75 |             }
  76 |         }
  77 | 
  78 |         // 🔥 验证脚本：记录清洗效果
  79 |         if (rawChunk.length != currentChunk.length) {
  80 |             Timber.tag(TAG).i("🔥 [用户修复] 清洗完成: 原始=${rawChunk.length}字符, 清洗后=${currentChunk.length}字符")
  81 |             if (rawChunk.length != currentChunk.length) {
  82 |                 Timber.tag(TAG).i("🔥 [用户修复] 处理了${rawChunk.length - currentChunk.length}个字符的标签内容")
  83 |             }
  84 |         }
  85 | 
  86 |         return currentChunk
  87 |     }
  88 | 
  89 |     /**
  90 |      * 检查chunk是否包含非法标签
  91 |      */
  92 |     fun hasIllegalTags(chunk: String): Boolean {
  93 |         return PHASE_TAG_WITH_CONTENT.containsMatchIn(chunk) ||
  94 |                PHASE_START_TAG.containsMatchIn(chunk) ||
  95 |                PHASE_END_TAG.containsMatchIn(chunk) ||
  96 |                OTHER_ILLEGAL_PATTERNS.any { it.containsMatchIn(chunk) }
  97 |     }
  98 | 
  99 |     /**
 100 |      * 获取清洗统计信息
 101 |      */
 102 |     fun getCleaningStats(originalChunk: String, cleanedChunk: String): CleaningStats {
 103 |         val removedChars = originalChunk.length - cleanedChunk.length
 104 |         val illegalTagCount = PHASE_TAG_WITH_CONTENT.findAll(originalChunk).count() +
 105 |                              PHASE_START_TAG.findAll(originalChunk).count() +
 106 |                              PHASE_END_TAG.findAll(originalChunk).count() +
 107 |                              OTHER_ILLEGAL_PATTERNS.sumOf { pattern ->
 108 |                                  pattern.findAll(originalChunk).count()
 109 |                              }
 110 | 
 111 |         return CleaningStats(
 112 |             originalLength = originalChunk.length,
 113 |             cleanedLength = cleanedChunk.length,
 114 |             removedChars = removedChars,
 115 |             illegalTagCount = illegalTagCount
 116 |         )
 117 |     }
 118 | }
 119 | 
 120 | /**
 121 |  * 清洗统计信息
 122 |  */
 123 | data class CleaningStats(
 124 |     val originalLength: Int,
 125 |     val cleanedLength: Int,
 126 |     val removedChars: Int,
 127 |     val illegalTagCount: Int
 128 | ) {
 129 |     val cleaningEfficiency: Float = if (originalLength > 0) {
 130 |         removedChars.toFloat() / originalLength.toFloat()
 131 |     } else 0f
 132 | }

```

`main\kotlin\com\example\gymbro\features\thinkingbox\domain\processor\RawChunkProcessorDemo.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.domain.processor
   2 | 
   3 | /**
   4 |  * RawChunkProcessor演示 - 验证用户修复效果
   5 |  * 
   6 |  * 用于手动验证：只删除<phase:XXX>标签，保留文本内容
   7 |  */
   8 | object RawChunkProcessorDemo {
   9 |     
  10 |     @JvmStatic
  11 |     fun main(args: Array<String>) {
  12 |         val processor = RawChunkProcessor()
  13 |         
  14 |         println("=== RawChunkProcessor 用户修复验证 ===")
  15 |         println()
  16 |         
  17 |         // 测试用例1：完整的phase标签
  18 |         testCase(processor, "完整phase标签", "<phase:PLAN>我们正在分析问题</phase:PLAN>")
  19 |         
  20 |         // 测试用例2：混合内容
  21 |         testCase(processor, "混合内容", "<think><phase:PLAN>分析步骤</phase:PLAN>其他思考内容</think>")
  22 |         
  23 |         // 测试用例3：单独的开始标签
  24 |         testCase(processor, "单独开始标签", "<phase:PLAN>一些文本内容")
  25 |         
  26 |         // 测试用例4：单独的结束标签
  27 |         testCase(processor, "单独结束标签", "一些文本内容</phase:PLAN>")
  28 |         
  29 |         // 测试用例5：多个phase标签
  30 |         testCase(processor, "多个phase标签", "<phase:PLAN>第一部分</phase:PLAN>中间文本<phase:EXECUTE>第二部分</phase:EXECUTE>")
  31 |         
  32 |         // 测试用例6：嵌套结构
  33 |         testCase(processor, "嵌套结构", "<think><phase:PLAN>我们需要分析</phase:PLAN>这个问题的核心</think>")
  34 |         
  35 |         // 测试用例7：空的phase标签
  36 |         testCase(processor, "空phase标签", "<phase:PLAN></phase:PLAN>")
  37 |         
  38 |         // 测试用例8：正常XML标签（应该保持不变）
  39 |         testCase(processor, "正常XML标签", "<thinking><phase id=\"1\">正常内容</phase></thinking>")
  40 |         
  41 |         println("=== 验证完成 ===")
  42 |     }
  43 |     
  44 |     private fun testCase(processor: RawChunkProcessor, name: String, input: String) {
  45 |         println("测试: $name")
  46 |         println("输入: '$input'")
  47 |         
  48 |         val result = processor.preprocess(input)
  49 |         println("输出: '$result'")
  50 |         
  51 |         val hasIllegal = processor.hasIllegalTags(input)
  52 |         println("包含非法标签: $hasIllegal")
  53 |         
  54 |         if (input != result) {
  55 |             println("✅ 已处理 (${input.length} → ${result.length} 字符)")
  56 |         } else {
  57 |             println("⚪ 无变化")
  58 |         }
  59 |         
  60 |         println()
  61 |     }
  62 | }

```

`main\kotlin\com\example\gymbro\features\thinkingbox\history\HistoryMapper.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.history
   2 | 
   3 | import com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent
   4 | import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingUiState
   5 | import com.example.gymbro.features.thinkingbox.internal.presentation.model.BlockContent
   6 | import com.example.gymbro.features.thinkingbox.internal.presentation.model.NodeType
   7 | import com.example.gymbro.features.thinkingbox.internal.presentation.model.RenderableNode
   8 | import com.example.gymbro.features.thinkingbox.internal.presentation.model.toAnnotatedString
   9 | import kotlinx.serialization.Serializable
  10 | import kotlinx.serialization.decodeFromString
  11 | import kotlinx.serialization.encodeToString
  12 | import kotlinx.serialization.json.Json
  13 | import timber.log.Timber
  14 | import javax.inject.Inject
  15 | import javax.inject.Singleton
  16 | 
  17 | /**
  18 |  * ThinkingBox 历史映射器
  19 |  *
  20 |  * 负责将思考过程转换为可持久化的格式，支持历史回放功能
  21 |  */
  22 | @Singleton
  23 | class HistoryMapper @Inject constructor() {
  24 | 
  25 |     companion object {
  26 |         private const val TAG = "THINKINGBOX-HISTORY"
  27 |     }
  28 | 
  29 |     private val json = Json {
  30 |         ignoreUnknownKeys = true
  31 |         encodeDefaults = true
  32 |         prettyPrint = false
  33 |     }
  34 | 
  35 |     /**
  36 |      * 将思考节点列表序列化为 JSON 字符串
  37 |      */
  38 |     fun serializeThinkingNodes(nodes: List<RenderableNode>): String? {
  39 |         return try {
  40 |             if (nodes.isEmpty()) {
  41 |                 null
  42 |             } else {
  43 |                 val serializableNodes = nodes.map { it.toSerializable() }
  44 |                 json.encodeToString(serializableNodes)
  45 |             }
  46 |         } catch (e: Exception) {
  47 |             Timber.tag(TAG).e(e, "序列化思考节点失败: ${nodes.size} 个节点")
  48 |             null
  49 |         }
  50 |     }
  51 | 
  52 |     /**
  53 |      * 从 JSON 字符串反序列化思考节点列表
  54 |      */
  55 |     fun deserializeThinkingNodes(jsonString: String?): List<RenderableNode> {
  56 |         return try {
  57 |             if (jsonString.isNullOrBlank()) {
  58 |                 emptyList()
  59 |             } else {
  60 |                 val serializableNodes = json.decodeFromString<List<SerializableRenderableNode>>(jsonString)
  61 |                 serializableNodes.map { it.toRenderableNode() }
  62 |             }
  63 |         } catch (e: Exception) {
  64 |             Timber.tag(TAG).e(e, "反序列化思考节点失败: $jsonString")
  65 |             emptyList()
  66 |         }
  67 |     }
  68 | 
  69 |     /**
  70 |      * 从 SemanticEvent.TagClosed("final") 提取思考节点
  71 |      */
  72 |     fun extractThinkingNodesFromFinalEvent(
  73 |         finalEvent: SemanticEvent.TagClosed,
  74 |         allNodes: List<RenderableNode>,
  75 |     ): List<RenderableNode> {
  76 |         return try {
  77 |             // 过滤出思考相关的节点，排除最终答案节点
  78 |             val thinkingNodes = allNodes.filter { node ->
  79 |                 node.type != NodeType.FINAL_RESULT &&
  80 |                     node.block.content.text.isNotBlank()
  81 |             }
  82 | 
  83 |             Timber.tag(TAG).d("提取思考节点: 总节点 ${allNodes.size}, 思考节点 ${thinkingNodes.size}")
  84 |             thinkingNodes
  85 |         } catch (e: Exception) {
  86 |             Timber.tag(TAG).e(e, "提取思考节点失败")
  87 |             emptyList()
  88 |         }
  89 |     }
  90 | 
  91 |     /**
  92 |      * 检查是否需要保存思考节点
  93 |      */
  94 |     fun shouldSaveThinkingNodes(nodes: List<RenderableNode>): Boolean {
  95 |         return nodes.isNotEmpty() && nodes.any { node ->
  96 |             node.type == NodeType.PHASE &&
  97 |                 node.block.content.text.isNotBlank()
  98 |         }
  99 |     }
 100 | 
 101 |     /**
 102 |      * 检查ThinkingUiState是否可以持久化
 103 |      *
 104 |      * @param thinkingState 思考状态
 105 |      * @return 是否可以持久化
 106 |      */
 107 |     fun canPersist(thinkingState: ThinkingUiState): Boolean {
 108 |         return thinkingState.final != null &&
 109 |                thinkingState.isCompleted &&
 110 |                thinkingState.final!!.isNotBlank()
 111 |     }
 112 | 
 113 |     /**
 114 |      * 将ThinkingUiState转换为历史实体数据
 115 |      *
 116 |      * @param thinkingState 思考状态
 117 |      * @param sessionId 会话ID
 118 |      * @return 历史数据映射
 119 |      */
 120 |     fun toHistoryData(thinkingState: ThinkingUiState, sessionId: String): ThinkingHistoryData {
 121 |         return ThinkingHistoryData(
 122 |             sessionId = sessionId,
 123 |             finalMarkdown = thinkingState.final,
 124 |             summary = thinkingState.final?.take(120),
 125 |             phaseCount = thinkingState.phases.size,
 126 |             thinkingDuration = thinkingState.thinkingDuration,
 127 |             sources = thinkingState.sources,
 128 |             searchCount = thinkingState.searchCount,
 129 |             sourceCount = thinkingState.sourceCount,
 130 |             timestamp = System.currentTimeMillis(),
 131 |             // 🔥 数据流统一：直接使用新的持久化字段
 132 |             functionCall = thinkingState.lastFunctionCall,
 133 |             mcp = thinkingState.lastMcp,
 134 |             rawTokens = thinkingState.tokensSnapshot.takeIf { it.isNotBlank() }
 135 |         )
 136 |     }
 137 | 
 138 |     // 🔥 数据流统一：移除旧的提取方法，直接使用ThinkingUiState中的新字段
 139 | }
 140 | 
 141 | /**
 142 |  * ThinkingBox历史数据
 143 |  *
 144 |  * 基于 626coach-thinkingbox.md 文档要求的完整字段结构
 145 |  */
 146 | data class ThinkingHistoryData(
 147 |     val sessionId: String,
 148 |     val finalMarkdown: String?,
 149 |     val summary: String?,
 150 |     val phaseCount: Int,
 151 |     val thinkingDuration: String,
 152 |     val sources: List<String>,
 153 |     val searchCount: Int,
 154 |     val sourceCount: Int,
 155 |     val timestamp: Long,
 156 |     // 🔥 Phase 3 扩展：Function Call 和 MCP 支持
 157 |     val functionCall: String? = null,  // JSON格式存储
 158 |     val mcp: String? = null,          // JSON格式存储，可为空
 159 |     val rawTokens: String? = null     // 可选的调试信息
 160 | )
 161 | 
 162 | /**
 163 |  * 可序列化的 RenderableNode 数据类
 164 |  */
 165 | @Serializable
 166 | data class SerializableRenderableNode(
 167 |     val id: String,
 168 |     val type: String,
 169 |     val title: String,
 170 |     val content: String,
 171 |     val phase: String? = null,
 172 |     val level: Int = 0,
 173 |     val isExpanded: Boolean = false,
 174 |     val timestamp: Long = System.currentTimeMillis(),
 175 | )
 176 | 
 177 | /**
 178 |  * RenderableNode 扩展函数：转换为可序列化格式
 179 |  */
 180 | private fun RenderableNode.toSerializable(): SerializableRenderableNode {
 181 |     return SerializableRenderableNode(
 182 |         id = this.id,
 183 |         type = this.type.name,
 184 |         title = this.block.title,
 185 |         content = this.block.content.text,
 186 |         phase = this.block.metadata["phase"],
 187 |         level = this.level,
 188 |         isExpanded = this.isExpanded,
 189 |         timestamp = this.timestamp,
 190 |     )
 191 | }
 192 | 
 193 | /**
 194 |  * SerializableRenderableNode 扩展函数：转换为 RenderableNode
 195 |  */
 196 | private fun SerializableRenderableNode.toRenderableNode(): RenderableNode {
 197 |     val metadata = mutableMapOf<String, String>()
 198 |     if (this.phase != null) {
 199 |         metadata["phase"] = this.phase
 200 |     }
 201 | 
 202 |     return RenderableNode(
 203 |         id = this.id,
 204 |         level = this.level,
 205 |         block = BlockContent(
 206 |             title = this.title,
 207 |             content = this.content.toAnnotatedString(),
 208 |             metadata = metadata,
 209 |         ),
 210 |         isExpanded = this.isExpanded,
 211 |         type = NodeType.valueOf(this.type),
 212 |         timestamp = this.timestamp,
 213 |     )
 214 | }
 215 | 
 216 | /**
 217 |  * 历史回放工具类
 218 |  */
 219 | object ThinkingHistoryReplayUtils {
 220 | 
 221 |     /**
 222 |      * 为历史回放准备节点数据
 223 |      */
 224 |     fun prepareNodesForReplay(nodes: List<RenderableNode>): List<RenderableNode> {
 225 |         return nodes.map { node ->
 226 |             // 重置展开状态，让用户可以重新交互
 227 |             node.copy(isExpanded = false)
 228 |         }.sortedBy { it.timestamp }
 229 |     }
 230 | 
 231 |     /**
 232 |      * 检查节点数据的完整性
 233 |      */
 234 |     fun validateNodesIntegrity(nodes: List<RenderableNode>): Boolean {
 235 |         return nodes.all { node ->
 236 |             node.id.isNotBlank() &&
 237 |                 node.block.content.text.isNotBlank() &&
 238 |                 node.timestamp > 0
 239 |         }
 240 |     }
 241 | 
 242 |     /**
 243 |      * 获取思考过程摘要
 244 |      */
 245 |     fun getThinkingProcessSummary(nodes: List<RenderableNode>): String {
 246 |         val phaseNodes = nodes.filter { it.type == NodeType.PHASE }
 247 |         return if (phaseNodes.isEmpty()) {
 248 |             "无思考过程"
 249 |         } else {
 250 |             val phases = phaseNodes.map { it.block.metadata["phase"] ?: "未知阶段" }.distinct()
 251 |             "思考阶段: ${phases.joinToString(" → ")}"
 252 |         }
 253 |     }
 254 | }

```

`main\kotlin\com\example\gymbro\features\thinkingbox\internal\adapter\UiStateAdapter.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.internal.adapter
   2 | 
   3 | import com.example.gymbro.features.thinkingbox.domain.interfaces.PhaseUi
   4 | import com.example.gymbro.features.thinkingbox.domain.interfaces.UiState
   5 | import com.example.gymbro.features.thinkingbox.domain.model.events.PhaseAggregate
   6 | import com.example.gymbro.features.thinkingbox.domain.model.events.Summary
   7 | import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingUiState
   8 | import timber.log.Timber
   9 | import kotlin.time.Duration.Companion.milliseconds
  10 | 
  11 | /**
  12 |  * UiState适配器
  13 |  *
  14 |  * 负责在现有的ThinkingUiState和规范要求的UiState之间进行转换
  15 |  * 确保UI层严格遵循627规范统一.md的接口要求
  16 |  *
  17 |  * 设计原则：
  18 |  * - 保持现有domain层不变
  19 |  * - 在UI层提供规范接口适配
  20 |  * - 确保数据转换的准确性和性能
  21 |  */
  22 | object UiStateAdapter {
  23 |     /**
  24 |      * 将ThinkingUiState转换为规范的UiState
  25 |      *
  26 |      * 转换规则：
  27 |      * - phases: LinkedHashMap<String, PhaseAggregate> → List<PhaseUi>
  28 |      * - final → finalMarkdown
  29 |      * - isStreaming → isThinking (取反逻辑)
  30 |      * - 计算elapsed时间
  31 |      */
  32 |     fun toStandardUiState(
  33 |         thinkingState: ThinkingUiState,
  34 |         isCollapsed: Boolean = false,
  35 |     ): UiState {
  36 |         val hasPreThinking = !thinkingState.preThinking.isNullOrBlank()
  37 |         val phasesList = convertPhasesToPhaseUi(thinkingState.phases, hasPreThinking)
  38 |         val isStreaming = thinkingState.isStreaming
  39 |         val hasPhases = phasesList.isNotEmpty()
  40 |         val hasFinal = thinkingState.final != null
  41 | 
  42 |         // 🔥 单卡模式：只显示最新的一个phase
  43 |         // val shouldCollapse = hasFinal && !isStreaming
  44 |         // val actuallyCollapsed = isCollapsed || shouldCollapse
  45 | 
  46 |         // 🔥 【转换链路验证】：追踪转换过程和数据完整性
  47 |         Timber.tag("TB-AUDIT").i("🔍 [转换链路验证] UiStateAdapter.toStandardUiState()开始")
  48 |         Timber.tag("TB-AUDIT").i("🔍 [转换链路验证] 输入数据: phases=${thinkingState.phases.size}, isStreaming=$isStreaming, hasFinal=$hasFinal")
  49 |         Timber.tag("TB-AUDIT").i("🔍 [转换链路验证] 输入字段: preThinking=${thinkingState.preThinking?.length}, final=${thinkingState.final?.length}, tokensSnapshot=${thinkingState.tokensSnapshot.length}")
  50 | 
  51 |         // 🔥 调试日志：追踪转换过程
  52 |         Timber.tag("TB-ADAPTER").i("🔄 [UiStateAdapter] 转换开始: 输入phases=${thinkingState.phases.size}, isStreaming=$isStreaming, hasFinal=$hasFinal, 单卡模式=enabled")
  53 | 
  54 |         // 🔥 多语言支持：放宽phase ID检查，支持中文和其他语言
  55 |         val inputPhaseIds = thinkingState.phases.keys.toList()
  56 |         val allowedPhases =
  57 |             setOf(
  58 |                 "ANALYSIS",
  59 |                 "PLANNING",
  60 |                 "EXECUTION",
  61 |                 "REVIEW",
  62 |                 "CONCLUSION",
  63 |                 "分析",
  64 |                 "规划",
  65 |                 "执行",
  66 |                 "回顾",
  67 |                 "总结",
  68 |                 "思考",
  69 |                 "计划",
  70 |                 "实施",
  71 |                 "检查",
  72 |                 "结论",
  73 |                 "default_phase",
  74 |             )
  75 |         val unmatchedIds =
  76 |             inputPhaseIds.filterNot {
  77 |                 it.uppercase() in allowedPhases || it.startsWith("CUSTOM_")
  78 |             }
  79 |         if (unmatchedIds.isNotEmpty()) {
  80 |             Timber.tag("TB-ADAPTER").i("🔄 [UiStateAdapter] 发现多语言/自定义phase ID: $unmatchedIds (已支持)")
  81 |         }
  82 | 
  83 |         Timber.tag("TB-ADAPTER").i("🔄 [UiStateAdapter] 转换后phasesList: size=${phasesList.size}, hasPhases=$hasPhases, inputIds=$inputPhaseIds")
  84 | 
  85 |         // 🔥 【Phase跳过修复】移除字符数限制，只要有phases就显示
  86 |         phasesList.isNotEmpty()
  87 | 
  88 |         // 🔥 【折叠思考修复】使用ThinkingUiState中的实际isCollapsed状态
  89 |         val shouldCollapse = thinkingState.isCollapsed
  90 | 
  91 |         val result =
  92 |             UiState(
  93 |                 phases = phasesList,
  94 |                 finalMarkdown = thinkingState.final,
  95 |                 isThinking = isStreaming,
  96 |                 isCollapsed = shouldCollapse, // 🔥 修复：思考完成且有final时折叠
  97 |                 elapsed = calculateElapsed(thinkingState.startTime),
  98 |                 // 🔥 修复折叠：Header显示逻辑 - 确保不与PreThinkingCard重叠
  99 |                 showHeader = isStreaming && phasesList.isEmpty() && thinkingState.final == null && thinkingState.preThinking.isNullOrBlank(),
 100 |                 visiblePhases = phasesList, // 🔥 修复跳动：始终提供phases，由UI层决定显示
 101 |                 activePhaseId = if (isStreaming && phasesList.isNotEmpty()) phasesList.last().id else null,
 102 |                 isStreaming = isStreaming,
 103 |                 // 🔥 修复Summary显示逻辑：只要有phases或final内容就显示Summary
 104 |                 summary = if (!isStreaming && (hasPhases || hasFinal)) createSummary(thinkingState) else null,
 105 |                 final = thinkingState.final,
 106 |                 // 预思考内容：传递给UI进行灰度显示
 107 |                 preThinking = thinkingState.preThinking,
 108 |                 // 🔥 数据流统一：传递持久化相关字段
 109 |                 tokensSnapshot = thinkingState.tokensSnapshot,
 110 |                 lastFunctionCall = thinkingState.lastFunctionCall,
 111 |                 lastMcp = thinkingState.lastMcp,
 112 |                 persisted = thinkingState.persisted,
 113 |                 // 🔥 Step 1重构：11步状态转换字段传递
 114 |                 currentStep = thinkingState.currentStep,
 115 |                 stepTransitionTime = thinkingState.stepTransitionTime,
 116 |                 thinkDetected = thinkingState.thinkDetected,
 117 |                 thinkingStarted = thinkingState.thinkingStarted,
 118 |                 phaseManagementActive = thinkingState.phaseManagementActive,
 119 |                 titleUpdatesEnabled = thinkingState.titleUpdatesEnabled,
 120 |                 textStreamingActive = thinkingState.textStreamingActive,
 121 |                 completionCollapseTriggered = thinkingState.completionCollapseTriggered,
 122 |                 summaryToggleEnabled = thinkingState.summaryToggleEnabled,
 123 |                 richTextRenderingActive = thinkingState.richTextRenderingActive,
 124 |                 currentPhaseId = thinkingState.currentPhaseId,
 125 |                 lastPhaseCompleted = thinkingState.lastPhaseCompleted,
 126 |             )
 127 | 
 128 |         // 🔥 【转换链路验证】：验证转换结果的数据完整性
 129 |         Timber.tag("TB-AUDIT").i("🔍 [转换链路验证] UiStateAdapter.toStandardUiState()完成")
 130 |         Timber.tag("TB-AUDIT").i("🔍 [转换链路验证] 输出数据: phases=${result.phases.size}, visiblePhases=${result.visiblePhases.size}, isThinking=${result.isThinking}")
 131 |         Timber.tag("TB-AUDIT").i("🔍 [转换链路验证] 输出字段: preThinking=${result.preThinking?.length}, finalMarkdown=${result.finalMarkdown?.length}, final=${result.final?.length}")
 132 |         Timber.tag("TB-AUDIT").i("🔍 [转换链路验证] 状态字段: isCollapsed=${result.isCollapsed}, showHeader=${result.showHeader}, activePhaseId=${result.activePhaseId}")
 133 | 
 134 |         // 🔥 【转换链路验证】：验证数据无丢失
 135 |         val inputPhaseCount = thinkingState.phases.size
 136 |         val outputPhaseCount = result.phases.size
 137 |         val inputFinalLength = thinkingState.final?.length ?: 0
 138 |         val outputFinalLength = result.finalMarkdown?.length ?: 0
 139 | 
 140 |         if (inputPhaseCount != outputPhaseCount) {
 141 |             Timber.tag("TB-AUDIT").w("🔍 [转换链路验证] ⚠️ Phase数量不一致: 输入$inputPhaseCount → 输出$outputPhaseCount")
 142 |         }
 143 |         if (inputFinalLength != outputFinalLength) {
 144 |             Timber.tag("TB-AUDIT").w("🔍 [转换链路验证] ⚠️ Final内容长度不一致: 输入$inputFinalLength → 输出$outputFinalLength")
 145 |         }
 146 | 
 147 |         // 🔥 调试日志：追踪转换结果
 148 |         Timber.tag("TB-ADAPTER").i("🔄 [UiStateAdapter] 转换结果: showHeader=${result.showHeader}, visiblePhases=${result.visiblePhases.size}, activePhaseId=${result.activePhaseId}")
 149 |         Timber.tag("TB-ADAPTER").i("🔄 [UiStateAdapter] 关键状态: isStreaming=$isStreaming, hasFinal=$hasFinal, shouldCollapse=$shouldCollapse")
 150 |         result.visiblePhases.forEachIndexed { index, phase ->
 151 |             Timber.tag("TB-ADAPTER").i("🔄 [UiStateAdapter] visiblePhase[$index]: id=${phase.id}, title='${phase.title}', content=${phase.content.length}字符")
 152 |         }
 153 | 
 154 |         return result
 155 |     }
 156 | 
 157 |     /**
 158 |      * 将PhaseAggregate映射转换为PhaseUi列表
 159 |      * 🔥 修复：支持多Phase显示，实现append-only机制
 160 |      */
 161 |     private fun convertPhasesToPhaseUi(
 162 |         phases: kotlinx.collections.immutable.PersistentMap<String, PhaseAggregate>,
 163 |         hasPreThinking: Boolean = false,
 164 |     ): List<PhaseUi> {
 165 |         // 🔥 调试日志：追踪phases转换
 166 |         Timber.tag("TB-ADAPTER").i("🔄 [UiStateAdapter] 转换phases: 输入${phases.size}个, keys=${phases.keys.take(5)}")
 167 | 
 168 |         // 🔥 详细记录每个phase的信息
 169 |         phases.forEach { (key, phase) ->
 170 |             Timber.tag("TB-ADAPTER").i("🔄 [UiStateAdapter] Phase[$key]: title='${phase.title}', content=${phase.content.length}字符")
 171 |         }
 172 | 
 173 |         if (phases.isEmpty()) {
 174 |             Timber.tag("TB-ADAPTER").i("🔄 [UiStateAdapter] 无phase，返回空列表")
 175 |             return emptyList()
 176 |         }
 177 | 
 178 |         // 🔥 【修复跳动】：显示所有phases，避免内容丢失导致的跳动
 179 |         val phasesList = mutableListOf<PhaseUi>()
 180 | 
 181 |         if (phases.isNotEmpty()) {
 182 |             // 🔥 【修复Phase不一致】：显示所有phases，按ordinal排序
 183 |             val sortedPhases = phases.entries.sortedBy { it.value.lastOrdinal }
 184 | 
 185 |             sortedPhases.forEach { (phaseId, phase) ->
 186 |                 val phaseUi = convertSinglePhaseToUi(phaseId, phase, hasPreThinking)
 187 |                 phasesList.add(phaseUi)
 188 |                 Timber.tag("TB-ADAPTER").i("🔥 [修复跳动] 添加phase: id='$phaseId', ordinal=${phase.lastOrdinal}, title='${phase.title}', content=${phase.content.length}字符")
 189 |             }
 190 | 
 191 |             Timber.tag("TB-ADAPTER").i("🔥 [修复跳动] 显示所有phases：${phases.size}个输入phase → ${phasesList.size}个显示phase")
 192 |         }
 193 | 
 194 |         Timber.tag("TB-ADAPTER").i("🔄 [单卡重组实现] 显示${phasesList.size}个phase（单卡重组刷新）")
 195 |         return phasesList
 196 |     }
 197 | 
 198 |     /**
 199 |      * 将单个PhaseAggregate转换为PhaseUi
 200 |      * 🔥 提取的辅助方法，支持智能标题处理
 201 |      */
 202 |     private fun convertSinglePhaseToUi(
 203 |         phaseId: String,
 204 |         phase: PhaseAggregate,
 205 |         hasPreThinking: Boolean = false,
 206 |     ): PhaseUi {
 207 |         // 🔥 智能标题处理：多级回退策略
 208 |         fun hasDisplayableTitle(title: String?): Boolean = title?.isNotBlank() == true
 209 | 
 210 |         // 🔥 智能标题提取：从内容中提取标题
 211 |         fun extractTitleFromContent(content: String): String? {
 212 |             if (content.isBlank()) return null
 213 | 
 214 |             // 尝试提取第一行作为标题（限制长度）
 215 |             val firstLine =
 216 |                 content
 217 |                     .trim()
 218 |                     .lines()
 219 |                     .firstOrNull()
 220 |                     ?.trim()
 221 |             if (firstLine != null && firstLine.length in 2..50 && !firstLine.endsWith("...")) {
 222 |                 // 检查是否像标题（不包含过多标点符号）
 223 |                 val punctuationCount = firstLine.count { it in "。！？，；：" }
 224 |                 if (punctuationCount <= 1) {
 225 |                     return firstLine
 226 |                 }
 227 |             }
 228 | 
 229 |             // 尝试提取关键词作为标题
 230 |             val keywords = listOf("分析", "规划", "设计", "执行", "回顾", "总结", "思考", "计划", "实施", "检查")
 231 |             for (keyword in keywords) {
 232 |                 if (content.contains(keyword)) {
 233 |                     return "$keyword 阶段"
 234 |                 }
 235 |             }
 236 | 
 237 |             return null
 238 |         }
 239 | 
 240 |         val safeTitle =
 241 |             when {
 242 |                 hasDisplayableTitle(phase.title) -> {
 243 |                     // 🔥 用户要求：只使用AI真实回应的标题内容
 244 |                     Timber.tag("TB-ADAPTER").i("🔄 [UiStateAdapter] 使用AI真实标题: '${phase.title}'")
 245 |                     phase.title
 246 |                 }
 247 |                 else -> {
 248 |                     // 🔥 用户要求：不要任何预设内容，只使用AI真实回应
 249 |                     val extractedTitle = extractTitleFromContent(phase.content)
 250 |                     if (extractedTitle != null) {
 251 |                         Timber.tag("TB-ADAPTER").i("🔄 [UiStateAdapter] 从AI内容提取标题: '$extractedTitle'")
 252 |                         extractedTitle
 253 |                     } else {
 254 |                         Timber.tag("TB-ADAPTER").d("🔄 [UiStateAdapter] 无AI标题，返回空")
 255 |                         "" // 🔥 用户要求：不要任何预设内容
 256 |                     }
 257 |                 }
 258 |             }
 259 | 
 260 |         // 🔥 627目标交付：每次都创建全新的PhaseUi实例，确保不可变
 261 |         return PhaseUi(
 262 |             id = phaseId, // 🔥 627目标交付：使用String类型的phaseId
 263 |             _title = safeTitle,
 264 |             content = phase.content,
 265 |             totalChars = phase.content.length, // 🔥 627目标交付：字符统计
 266 |             hasTitle = phase.hasTitle, // 🔥 627目标交付：标题状态
 267 |             isComplete = phase.isComplete, // 🔥 用户方案：使用PhaseAggregate的完整性标记
 268 |         )
 269 |     }
 270 | 
 271 |     /**
 272 |      * 计算已用时间
 273 |      */
 274 |     private fun calculateElapsed(startTime: Long): kotlin.time.Duration {
 275 |         val currentTime = System.currentTimeMillis()
 276 |         val elapsedMs = currentTime - startTime
 277 |         return elapsedMs.milliseconds
 278 |     }
 279 | 
 280 |     /**
 281 |      * 创建摘要信息
 282 |      * 🔥 701finalmermaid大纲.md蓝图要求：Summary负责token计算
 283 |      */
 284 |     private fun createSummary(thinkingState: ThinkingUiState): Summary {
 285 |         val elapsed = calculateElapsed(thinkingState.startTime)
 286 |         val durationText = formatDuration(elapsed.inWholeMilliseconds)
 287 | 
 288 |         // 🔥 【统一】直接使用简单估算，避免重复函数
 289 |         val tokenCount =
 290 |             if (thinkingState.tokensSnapshot.isNotBlank()) {
 291 |                 (thinkingState.tokensSnapshot.length + 3) / 4
 292 |             } else {
 293 |                 val totalContent =
 294 |                     thinkingState.phases.values.joinToString("") { phase ->
 295 |                         "${phase.title} ${phase.content}"
 296 |                     }
 297 |                 (totalContent.length + 3) / 4
 298 |             }
 299 | 
 300 |         return Summary(
 301 |             durationText = durationText,
 302 |             searchCnt = thinkingState.searchCount, // 🔥 使用实际搜索统计
 303 |             sourceCnt = thinkingState.sourceCount, // 🔥 使用实际来源统计
 304 |             bullets = emptyList(), // TODO: 从phases提取要点
 305 |             sources = thinkingState.sources, // 🔥 使用实际来源列表
 306 |             isExpanded = false,
 307 |             tokenCount = tokenCount, // 🔥 新增：tokens计数
 308 |         )
 309 |     }
 310 | 
 311 |     // 🔥 【已移除】calculateTokenCount函数 - 重复实现
 312 |     // Token计算应该统一使用OpenAiTokenizer，避免重复的估算逻辑
 313 | 
 314 |     /**
 315 |      * 格式化持续时间
 316 |      */
 317 |     private fun formatDuration(durationMs: Long): String {
 318 |         val seconds = durationMs / 1000
 319 |         val minutes = seconds / 60
 320 |         val remainingSeconds = seconds % 60
 321 | 
 322 |         return if (minutes > 0) {
 323 |             "${minutes}m ${remainingSeconds}s"
 324 |         } else {
 325 |             "${remainingSeconds}s"
 326 |         }
 327 |     }
 328 | 
 329 |     /**
 330 |      * 从规范UiState提取ThinkingBoxInspector所需的数据
 331 |      */
 332 |     fun extractInspectorData(uiState: UiState): InspectorData =
 333 |         InspectorData(
 334 |             isThinking = uiState.isThinking,
 335 |             phaseCount = uiState.phases.size,
 336 |             elapsedMillis = uiState.elapsed.inWholeMilliseconds,
 337 |             finalMarkdown = uiState.finalMarkdown,
 338 |         )
 339 | 
 340 |     /**
 341 |      * Inspector数据容器
 342 |      */
 343 |     data class InspectorData(
 344 |         val isThinking: Boolean,
 345 |         val phaseCount: Int,
 346 |         val elapsedMillis: Long,
 347 |         val finalMarkdown: String?,
 348 |     )
 349 | }
 350 | 
 351 | /**
 352 |  * 扩展函数：为ThinkingUiState添加便捷的转换方法
 353 |  */
 354 | fun ThinkingUiState.toStandardUiState(isCollapsed: Boolean = false): UiState = UiStateAdapter.toStandardUiState(this, isCollapsed)
 355 | 
 356 | /**
 357 |  * 扩展函数：为PhaseAggregate添加转换为PhaseUi的方法
 358 |  */
 359 | fun PhaseAggregate.toPhaseUi(id: String): PhaseUi =
 360 |     PhaseUi(
 361 |         id = id,
 362 |         _title = this.title.ifBlank { "思考阶段 $id" },
 363 |         content = this.content,
 364 |         totalChars = this.content.length,
 365 |         hasTitle = this.hasTitle,
 366 |     )

```

`main\kotlin\com\example\gymbro\features\thinkingbox\internal\di\ThinkingBoxModule.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.internal.di
   2 | 
   3 | import com.example.gymbro.data.thinkingbox.repository.HistoryRepositoryImpl
   4 | import com.example.gymbro.domain.thinkingbox.repository.HistoryRepository
   5 | import com.example.gymbro.features.thinkingbox.domain.interfaces.ThinkingBoxFacade
   6 | import com.example.gymbro.features.thinkingbox.domain.logger.DebugRawThinkingLogger
   7 | import com.example.gymbro.features.thinkingbox.domain.logger.NoopRawThinkingLogger
   8 | import com.example.gymbro.features.thinkingbox.domain.logger.RawThinkingLogger
   9 | import com.example.gymbro.features.thinkingbox.internal.mvi.ThinkingBoxFacadeImpl
  10 | import com.example.gymbro.features.thinkingbox.internal.mvi.ThinkingBoxManager
  11 | import com.example.gymbro.features.thinkingbox.internal.mvi.DomainMapperFactory
  12 | import dagger.Binds
  13 | import dagger.Module
  14 | import dagger.Provides
  15 | import dagger.hilt.InstallIn
  16 | import dagger.hilt.components.SingletonComponent
  17 | import kotlinx.coroutines.CoroutineScope
  18 | import kotlinx.coroutines.Dispatchers
  19 | import kotlinx.coroutines.SupervisorJob
  20 | import javax.inject.Qualifier
  21 | import javax.inject.Singleton
  22 | 
  23 | /**
  24 |  * ThinkingBoxModule - Hilt 依赖注入模块
  25 |  *
  26 |  * 提供ThinkingBox模块所需的基础依赖
  27 |  */
  28 | @Module
  29 | @InstallIn(SingletonComponent::class)
  30 | abstract class ThinkingBoxModule {
  31 | 
  32 |     /**
  33 |      * 绑定ThinkingBoxFacade接口到实现类
  34 |      */
  35 |     @Binds
  36 |     @Singleton
  37 |     abstract fun bindThinkingBoxFacade(impl: ThinkingBoxFacadeImpl): ThinkingBoxFacade
  38 | 
  39 |     /**
  40 |      * 绑定HistoryRepository接口到实现类
  41 |      */
  42 |     @Binds
  43 |     @Singleton
  44 |     abstract fun bindHistoryRepository(impl: HistoryRepositoryImpl): HistoryRepository
  45 | 
  46 |     /**
  47 |      * 🔥 新增：ThinkingBoxManager 自动注入
  48 |      * 注意：ThinkingBoxManager 已经有 @Inject 构造函数和 @Singleton 注解，
  49 |      * Hilt 会自动处理依赖注入，不需要额外的 @Provides 方法
  50 |      */
  51 | 
  52 |     /**
  53 |      * 🔥 新增：DomainMapperFactory 自动注入
  54 |      * 注意：DomainMapperFactory 已经有 @Inject 构造函数和 @Singleton 注解，
  55 |      * Hilt 会自动处理依赖注入，不需要额外的 @Provides 方法
  56 |      */
  57 | 
  58 |     companion object {
  59 |         // 注意：StreamingThinkingMLParser, ThinkingPhaseExtractor, EventConverter,
  60 |         // OptimizedEventConverter, ThinkingMLGuardrail, ProgressiveRenderer 都已经有 @Inject 构造函数，
  61 |         // 不需要 @Provides 方法，Hilt 会自动处理依赖注入
  62 | 
  63 |         /**
  64 |          * 提供 ThinkingBox 专用的 CoroutineScope
  65 |          */
  66 |         @Provides
  67 |         @Singleton
  68 |         @ThinkingBoxScope
  69 |         fun provideThinkingBoxCoroutineScope(): CoroutineScope = CoroutineScope(
  70 |             SupervisorJob() + Dispatchers.Default,
  71 |         )
  72 | 
  73 |         /**
  74 |          * 提供 RawThinkingLogger 实现
  75 |          * 默认使用DebugRawThinkingLogger，可以通过环境变量或其他方式控制
  76 |          */
  77 |         @Provides
  78 |         @Singleton
  79 |         fun provideRawThinkingLogger(): RawThinkingLogger {
  80 |             // 🔥 简化实现：默认使用Debug版本，可以后续通过配置控制
  81 |             return DebugRawThinkingLogger()
  82 |         }
  83 |     }
  84 | }
  85 | 
  86 | /**
  87 |  * ThinkingBox 模块专用的 CoroutineScope 限定符
  88 |  */
  89 | @Qualifier
  90 | @Retention(AnnotationRetention.BINARY)
  91 | annotation class ThinkingBoxScope

```

`main\kotlin\com\example\gymbro\features\thinkingbox\internal\history\HistorySaver.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.internal.history
   2 | 
   3 | import com.example.gymbro.domain.thinkingbox.repository.HistoryRepository
   4 | import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
   5 | import kotlinx.coroutines.CoroutineScope
   6 | import kotlinx.coroutines.flow.Flow
   7 | import kotlinx.coroutines.launch
   8 | import timber.log.Timber
   9 | import javax.inject.Inject
  10 | import javax.inject.Singleton
  11 | 
  12 | /**
  13 |  * HistorySaver - ThinkingBox历史记录保存器
  14 |  *
  15 |  * 🔥 根据71thinkbox-choach.md方案实现：
  16 |  * - 监听ThinkingEvent，不直接接触RAW token
  17 |  * - 写入ROOM数据库的ChatRaw表
  18 |  * - 支持历史回放和Memory系统对接
  19 |  */
  20 | @Singleton
  21 | class HistorySaver
  22 |     @Inject
  23 |     constructor(
  24 |         private val historyRepository: HistoryRepository,
  25 |         @com.example.gymbro.features.thinkingbox.internal.di.ThinkingBoxScope
  26 |         private val coroutineScope: CoroutineScope,
  27 |     ) {
  28 |         private val TAG = "TB-HISTORY"
  29 | 
  30 |         // 🔥 当前消息状态跟踪
  31 |         private val activeMessages = mutableMapOf<String, ThinkingMessageState>()
  32 | 
  33 |         // 🔥 【多轮对话修复】全局事件流，用于收集来自所有实例的事件
  34 |         private val globalEventFlow = kotlinx.coroutines.flow.MutableSharedFlow<ThinkingEvent>()
  35 | 
  36 |         // 🔥 【多轮对话修复】确保只启动一次全局监听
  37 |         private val isGlobalListenerStarted =
  38 |             java.util.concurrent.atomic
  39 |                 .AtomicBoolean(false)
  40 | 
  41 |         init {
  42 |             // 🔥 【多轮对话修复】在初始化时启动全局事件监听
  43 |             startGlobalEventListener()
  44 |         }
  45 | 
  46 |         /**
  47 |          * 🔥 【多轮对话修复】启动全局事件监听器（只启动一次）
  48 |          */
  49 |         private fun startGlobalEventListener() {
  50 |             if (!isGlobalListenerStarted.compareAndSet(false, true)) {
  51 |                 return // 已经启动，避免重复
  52 |             }
  53 | 
  54 |             coroutineScope.launch {
  55 |                 globalEventFlow.collect { event ->
  56 |                     try {
  57 |                         handleThinkingEvent(event)
  58 |                     } catch (e: Exception) {
  59 |                         Timber.tag(TAG).e(e, "❌ 处理ThinkingEvent失败: ${event::class.simpleName}")
  60 |                     }
  61 |                 }
  62 |             }
  63 | 
  64 |             Timber.tag(TAG).i("🔥 全局ThinkingEvent监听器已启动")
  65 |         }
  66 | 
  67 |         /**
  68 |          * 🔥 【多轮对话修复】注册实例事件流到全局监听器
  69 |          */
  70 |         fun registerInstanceEventFlow(
  71 |             instanceEventFlow: Flow<ThinkingEvent>,
  72 |             messageId: String,
  73 |         ) {
  74 |             coroutineScope.launch {
  75 |                 instanceEventFlow.collect { event ->
  76 |                     // 转发到全局事件流
  77 |                     globalEventFlow.emit(event)
  78 |                 }
  79 |             }
  80 | 
  81 |             Timber.tag(TAG).d("🔥 注册实例事件流: messageId=$messageId")
  82 |         }
  83 | 
  84 |         /**
  85 |          * 🔥 【向后兼容】保留原有的startSaving方法
  86 |          */
  87 |         fun startSaving(thinkingEvents: Flow<ThinkingEvent>) {
  88 |             Timber.tag(TAG).w("⚠️ 使用了废弃的startSaving方法，建议使用registerInstanceEventFlow")
  89 |             coroutineScope.launch {
  90 |                 thinkingEvents.collect { event ->
  91 |                     globalEventFlow.emit(event)
  92 |                 }
  93 |             }
  94 |         }
  95 | 
  96 |         /**
  97 |          * 🔥 【时序修复】完全禁用HistorySaver的自动保存功能
  98 |          *
  99 |          * 所有thinking相关数据的保存都由Coach模块在AI消息保存成功后手动触发
 100 |          * 这样确保时序正确：AI消息先保存，thinking数据后保存
 101 |          */
 102 |         private suspend fun handleThinkingEvent(event: ThinkingEvent) {
 103 |             when (event) {
 104 |                 is ThinkingEvent.Start -> {
 105 |                     // 🔥 【时序修复】禁用自动保存，等待Coach模块手动触发
 106 |                     Timber
 107 |                         .tag(TAG)
 108 |                         .d("🔥 [时序修复] 跳过ThinkingStart自动保存，等待Coach模块手动触发: messageId=${event.messageId}")
 109 |                     // handleThinkingStart(event) // 注释掉自动保存
 110 |                 }
 111 | 
 112 |                 is ThinkingEvent.PhaseReplace -> {
 113 |                     // 🔥 【时序修复】禁用自动保存，等待Coach模块手动触发
 114 |                     Timber
 115 |                         .tag(TAG)
 116 |                         .d("🔥 [时序修复] 跳过PhaseReplace自动保存，等待Coach模块手动触发: messageId=${event.messageId}")
 117 |                     // handlePhaseReplace(event) // 注释掉自动保存
 118 |                 }
 119 | 
 120 |                 is ThinkingEvent.FinalAnswer -> {
 121 |                     // 🔥 【时序修复】禁用自动保存，等待Coach模块手动触发
 122 |                     Timber
 123 |                         .tag(TAG)
 124 |                         .d("🔥 [时序修复] 跳过FinalAnswer自动保存，等待Coach模块手动触发: messageId=${event.messageId}")
 125 |                     // handleFinalAnswer(event) // 注释掉自动保存
 126 |                 }
 127 | 
 128 |                 is ThinkingEvent.ThinkingFinished -> {
 129 |                     // 🔥 【时序修复】禁用自动保存，等待Coach模块手动触发
 130 |                     Timber
 131 |                         .tag(TAG)
 132 |                         .d("🔥 [时序修复] 跳过ThinkingFinished自动保存，等待Coach模块手动触发: messageId=${event.messageId}")
 133 |                     // handleThinkingFinished(event) // 注释掉自动保存
 134 |                 }
 135 | 
 136 |                 else -> {
 137 |                     // 其他事件不需要持久化
 138 |                     Timber.tag(TAG).v("📝 跳过事件: ${event::class.simpleName}")
 139 |                 }
 140 |             }
 141 |         }
 142 | 
 143 |         /**
 144 |          * 处理思考开始事件
 145 |          */
 146 |         private suspend fun handleThinkingStart(event: ThinkingEvent.Start) {
 147 |             val messageId = event.messageId ?: event.id // 使用messageId或fallback到sessionId
 148 |             val startTime = System.currentTimeMillis()
 149 | 
 150 |             // 🔥 记录消息状态
 151 |             activeMessages[messageId] =
 152 |                 ThinkingMessageState(
 153 |                     messageId = messageId,
 154 |                     startedAt = startTime,
 155 |                     status = "thinking",
 156 |                 )
 157 | 
 158 |             // 🔥 写入ROOM数据库
 159 |             historyRepository.insertThinkingMessage(
 160 |                 messageId = messageId,
 161 |                 status = "thinking",
 162 |                 startedAt = startTime,
 163 |                 finishedAt = null,
 164 |                 durationMs = null,
 165 |                 tokenCount = null,
 166 |             )
 167 | 
 168 |             Timber.tag(TAG).d("📝 思考开始记录: $messageId")
 169 |         }
 170 | 
 171 |         /**
 172 |          * 处理阶段替换事件
 173 |          */
 174 |         private suspend fun handlePhaseReplace(event: ThinkingEvent.PhaseReplace) {
 175 |             val messageId = event.messageId
 176 |             if (messageId == null) {
 177 |                 Timber.tag(TAG).w("⚠️ PhaseReplace事件缺少messageId，跳过历史记录")
 178 |                 return
 179 |             }
 180 | 
 181 |             // 🔥 写入ROOM数据库
 182 |             historyRepository.insertThinkingPhase(
 183 |                 messageId = messageId,
 184 |                 phaseId = event.newId,
 185 |                 title = event.title ?: "",
 186 |                 content = event.content,
 187 |                 complete = true, // PhaseReplace表示阶段已完成
 188 |             )
 189 | 
 190 |             Timber.tag(TAG).d("📝 阶段记录: $messageId - Phase ${event.newId}: ${event.title}")
 191 |         }
 192 | 
 193 |         /**
 194 |          * 处理最终内容到达事件
 195 |          */
 196 |         private suspend fun handleFinalAnswer(event: ThinkingEvent.FinalAnswer) {
 197 |             val messageId = event.messageId
 198 |             if (messageId == null) {
 199 |                 Timber.tag(TAG).w("⚠️ FinalAnswer事件缺少messageId，跳过历史记录")
 200 |                 return
 201 |             }
 202 | 
 203 |             val markdown = event.markdown
 204 | 
 205 |             // 🔥 写入ROOM数据库
 206 |             historyRepository.insertThinkingFinal(
 207 |                 messageId = messageId,
 208 |                 markdown = markdown,
 209 |             )
 210 | 
 211 |             Timber.tag(TAG).d("📝 最终内容记录: $messageId (${markdown.length} chars)")
 212 |         }
 213 | 
 214 |         /**
 215 |          * 处理思考完成事件
 216 |          */
 217 |         private suspend fun handleThinkingFinished(event: ThinkingEvent.ThinkingFinished) {
 218 |             val messageId = event.messageId
 219 |             if (messageId == null) {
 220 |                 Timber.tag(TAG).w("⚠️ ThinkingFinished事件缺少messageId，跳过历史记录")
 221 |                 return
 222 |             }
 223 | 
 224 |             val finishTime = System.currentTimeMillis()
 225 | 
 226 |             val messageState = activeMessages[messageId]
 227 |             if (messageState != null) {
 228 |                 val duration = finishTime - messageState.startedAt
 229 | 
 230 |             // 🔥 更新ROOM数据库
 231 |             historyRepository.updateThinkingMessage(
 232 |                     messageId = messageId,
 233 |                 status = "completed",
 234 |                 finishedAt = finishTime,
 235 |                 durationMs = duration,
 236 |                 tokenCount = 0, // TODO: 需要从其他地方获取token计数
 237 |             )
 238 | 
 239 |             // 🔥 清理状态
 240 |             activeMessages.remove(messageId)
 241 | 
 242 |             Timber.tag(TAG).d("📝 思考完成记录: $messageId (${duration}ms)")
 243 |         } else {
 244 |             Timber.tag(TAG).w("⚠️ 未找到消息状态: $messageId")
 245 |         }
 246 |     }
 247 | 
 248 |     /**
 249 |      * 清理资源
 250 |      */
 251 |     fun cleanup() {
 252 |         activeMessages.clear()
 253 |         Timber.tag(TAG).d("🧹 HistorySaver已清理")
 254 |     }
 255 | }
 256 | 
 257 | /**
 258 |  * 思考消息状态
 259 |  */
 260 | private data class ThinkingMessageState(
 261 |     val messageId: String,
 262 |     val startedAt: Long,
 263 |     val status: String,
 264 | )

```

`main\kotlin\com\example\gymbro\features\thinkingbox\internal\logging\TBLog.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.internal.logging
   2 | 
   3 | import timber.log.Timber
   4 | import java.util.concurrent.atomic.AtomicInteger
   5 | 
   6 | /**
   7 |  * TBLog - ThinkingBox 统一日志系统
   8 |  *
   9 |  * 🎯 目标：一张日志即可还原每批数据的完整旅程
  10 |  * 🔑 关键思路：批次-ID + 阶段标识 + 内容摘要
  11 |  *
  12 |  * 基于用户提供的专业日志落位方案实现
  13 |  */
  14 | object TBLog {
  15 | 
  16 |     /**
  17 |      * 全局日志开关
  18 |      * Release 默认关闭，Debug 默认开启
  19 |      */
  20 |     var enabled = true // 默认开启，可通过代码控制
  21 | 
  22 |     /**
  23 |      * 批次ID生成器 - 用于追踪同一Flow<String>源的完整旅程
  24 |      */
  25 |     private val batchId = AtomicInteger(0)
  26 | 
  27 |     /**
  28 |      * Token批量处理间隔（毫秒）
  29 |      * 每80ms批量log，避免逐字轰炸Logcat
  30 |      */
  31 |     const val TOKEN_BATCH_MS = 80L
  32 | 
  33 |     /**
  34 |      * 生成新的批次ID
  35 |      */
  36 |     fun nextBatchId(): Int = batchId.incrementAndGet()
  37 | 
  38 |     /**
  39 |      * 重置批次ID（用于测试或新会话）
  40 |      */
  41 |     fun resetBatchId() {
  42 |         batchId.set(0)
  43 |     }
  44 | 
  45 |     /**
  46 |      * 条件日志记录 - 只在enabled=true时执行
  47 |      */
  48 |     inline fun log(tag: String, level: LogLevel = LogLevel.DEBUG, block: () -> String) {
  49 |         if (enabled) {
  50 |             val message = block()
  51 |             when (level) {
  52 |                 LogLevel.VERBOSE -> Timber.tag(tag).v(message)
  53 |                 LogLevel.DEBUG -> Timber.tag(tag).d(message)
  54 |                 LogLevel.INFO -> Timber.tag(tag).i(message)
  55 |                 LogLevel.WARN -> Timber.tag(tag).w(message)
  56 |                 LogLevel.ERROR -> Timber.tag(tag).e(message)
  57 |             }
  58 |         }
  59 |     }
  60 | 
  61 |     /**
  62 |      * 日志级别枚举
  63 |      */
  64 |     enum class LogLevel {
  65 |         VERBOSE, DEBUG, INFO, WARN, ERROR
  66 |     }
  67 | }
  68 | 
  69 | /**
  70 |  * 日志标签常量 - 统一命名约定
  71 |  */
  72 | object TBTags {
  73 |     const val RAW = "TB-RAW"        // Parser收到原始分片
  74 |     const val FILTER = "TB-FILTER"  // 过滤后纯文本/控制标签
  75 |     const val SEM = "TB-SEM"        // SemanticEvent
  76 |     const val MAP = "TB-MAP"        // DomainMapper事件流
  77 |     const val EVT = "TB-EVT"        // ThinkingEvent
  78 |     const val STATE = "TB-STATE"    // Reducer新状态
  79 |     const val DB = "TB-DB"          // History落库
  80 |     const val UI = "TB-UI"          // UI关键渲染
  81 | }
  82 | 
  83 | /**
  84 |  * 字符串扩展 - 日志脱敏和格式化
  85 |  */
  86 | fun String.sanitizeForLog(): String = try {
  87 |     this
  88 |         // 控制标签白名单：只保留thinking, final, phase:, title, checkpoint
  89 |         .replace(Regex("<(?!/?(thinking|final|phase:|title|checkpoint))[^>]*>"), "[TAG]")
  90 |         // 敏感信息脱敏
  91 |         .replace(Regex("apikey\\s*[:=]\\s*\\w+", RegexOption.IGNORE_CASE), "apikey=***")
  92 |         .replace(Regex("token\\s*[:=]\\s*\\w+", RegexOption.IGNORE_CASE), "token=***")
  93 |         // 极长段落截断
  94 |         .let { if (it.length > 120) "${it.take(120)}…" else it }
  95 | } catch (e: Exception) {
  96 |     "[SANITIZE_ERROR]"
  97 | }
  98 | 
  99 | /**
 100 |  * 批次格式化 - 统一的批次ID格式
 101 |  */
 102 | fun formatBatch(batchId: Int): String = "(batch=%03d)".format(batchId)
 103 | 
 104 | /**
 105 |  * 内容摘要 - 生成内容的简短摘要
 106 |  */
 107 | fun String.toContentSummary(maxLen: Int = 20): String =
 108 |     if (this.length <= maxLen) this
 109 |     else "${this.take(maxLen)}…"

```

`main\kotlin\com\example\gymbro\features\thinkingbox\internal\memory\MemoryWriter.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.internal.memory
   2 | 
   3 | import com.example.gymbro.features.thinkingbox.domain.interfaces.UiState
   4 | import kotlinx.coroutines.CoroutineScope
   5 | import kotlinx.coroutines.flow.Flow
   6 | import kotlinx.coroutines.launch
   7 | import timber.log.Timber
   8 | import javax.inject.Inject
   9 | import javax.inject.Singleton
  10 | 
  11 | /**
  12 |  * MemoryWriter - ThinkingBox记忆系统写入器
  13 |  *
  14 |  * 🔥 根据71thinkbox-choach.md方案实现：
  15 |  * - 监听UiState.Final，将markdown内容写入向量库
  16 |  * - 支持Memory系统的语义检索
  17 |  * - 包含元数据：duration、token count等
  18 |  */
  19 | @Singleton
  20 | class MemoryWriter @Inject constructor(
  21 |     private val vectorDatabase: VectorDatabase,
  22 |     private val textEmbedder: TextEmbedder,
  23 |     private val coroutineScope: CoroutineScope
  24 | ) {
  25 | 
  26 |     private val TAG = "TB-MEMORY"
  27 | 
  28 |     /**
  29 |      * 开始监听UiState并写入Memory
  30 |      */
  31 |     fun startWriting(uiStateFlow: Flow<UiState>) {
  32 |         coroutineScope.launch {
  33 |             uiStateFlow.collect { uiState ->
  34 |                 try {
  35 |                     handleUiState(uiState)
  36 |                 } catch (e: Exception) {
  37 |                     Timber.tag(TAG).e(e, "❌ 处理UiState失败")
  38 |                 }
  39 |             }
  40 |         }
  41 |     }
  42 | 
  43 |     /**
  44 |      * 处理UiState变化
  45 |      */
  46 |     private suspend fun handleUiState(uiState: UiState) {
  47 |         // 🔥 只处理Final状态，且有markdown内容
  48 |         if (uiState.finalMarkdown != null && !uiState.finalMarkdown.isBlank()) {
  49 |             writeToMemory(uiState)
  50 |         }
  51 |     }
  52 | 
  53 |     /**
  54 |      * 将Final内容写入Memory向量库
  55 |      */
  56 |     private suspend fun writeToMemory(uiState: UiState) {
  57 |         val markdown = uiState.finalMarkdown ?: return
  58 |         val messageId = "thinking-${System.currentTimeMillis()}" // TODO: 需要从上下文获取真实messageId
  59 | 
  60 |         try {
  61 |             // 🔥 生成文本嵌入向量
  62 |             val embedding = textEmbedder.embed(markdown)
  63 | 
  64 |             // 🔥 准备元数据
  65 |             val metadata = buildMap<String, Any> {
  66 |                 put("messageId", messageId)
  67 |                 put("contentType", "thinking_final")
  68 |                 put("timestamp", System.currentTimeMillis())
  69 | 
  70 |                 // 🔥 添加思考过程元数据
  71 |                 put("duration", uiState.elapsed.inWholeMilliseconds)
  72 |                 put("phaseCount", uiState.phases.size)
  73 | 
  74 |                 // 🔥 添加内容统计
  75 |                 put("contentLength", markdown.length)
  76 |                 put("wordCount", markdown.split("\\s+".toRegex()).size)
  77 | 
  78 |                 // 🔥 添加阶段标题（用于检索）
  79 |                 val phaseTitles = uiState.phases.mapNotNull { it.title }.joinToString(", ")
  80 |                 if (phaseTitles.isNotEmpty()) {
  81 |                     put("phaseTitles", phaseTitles)
  82 |                 }
  83 | 
  84 |                 // 🔥 添加状态信息
  85 |                 put("isCompleted", uiState.isCompleted)
  86 |                 put("isCollapsed", uiState.isCollapsed)
  87 |             }
  88 | 
  89 |             // 🔥 写入向量库
  90 |             vectorDatabase.insert(
  91 |                 id = messageId,
  92 |                 vector = embedding,
  93 |                 metadata = metadata,
  94 |                 content = markdown
  95 |             )
  96 | 
  97 |             Timber.tag(TAG).d("💾 Memory写入成功: $messageId (${markdown.length} chars, ${uiState.phases.size} phases)")
  98 | 
  99 |         } catch (e: Exception) {
 100 |             Timber.tag(TAG).e(e, "❌ Memory写入失败: $messageId")
 101 |         }
 102 |     }
 103 | 
 104 |     /**
 105 |      * 清理资源
 106 |      */
 107 |     fun cleanup() {
 108 |         Timber.tag(TAG).d("🧹 MemoryWriter已清理")
 109 |     }
 110 | }
 111 | 
 112 | /**
 113 |  * 向量数据库接口
 114 |  */
 115 | interface VectorDatabase {
 116 |     suspend fun insert(
 117 |         id: String,
 118 |         vector: FloatArray,
 119 |         metadata: Map<String, Any>,
 120 |         content: String
 121 |     )
 122 | 
 123 |     suspend fun search(
 124 |         vector: FloatArray,
 125 |         limit: Int = 10,
 126 |         threshold: Float = 0.7f
 127 |     ): List<VectorSearchResult>
 128 | }
 129 | 
 130 | /**
 131 |  * 文本嵌入器接口
 132 |  */
 133 | interface TextEmbedder {
 134 |     suspend fun embed(text: String): FloatArray
 135 | }
 136 | 
 137 | /**
 138 |  * 向量搜索结果
 139 |  */
 140 | data class VectorSearchResult(
 141 |     val id: String,
 142 |     val score: Float,
 143 |     val metadata: Map<String, Any>,
 144 |     val content: String
 145 | )

```

`main\kotlin\com\example\gymbro\features\thinkingbox\internal\metrics\ThinkingBoxMetrics.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.internal.metrics
   2 | 
   3 | import timber.log.Timber
   4 | import java.util.concurrent.atomic.AtomicInteger
   5 | import javax.inject.Inject
   6 | import javax.inject.Singleton
   7 | 
   8 | /**
   9 |  * ThinkingBoxMetrics - ThinkingBox 模块监控指标
  10 |  * v3.0-hotfix-1 (2025-06-30)
  11 |  * 
  12 |  * 提供关键路径的监控和错误追踪，用于：
  13 |  * 1. 检测 PhaseReplace 路径执行失败
  14 |  * 2. 监控跨块标签检测命中率
  15 |  * 3. 追踪文本过滤统计
  16 |  * 4. 预警幽灵残留问题
  17 |  */
  18 | @Singleton
  19 | class ThinkingBoxMetrics @Inject constructor() {
  20 |     
  21 |     companion object {
  22 |         private const val TAG = "TB-METRICS"
  23 |         private const val CRASH_REPORT_THRESHOLD = 5 // 每5次错误发送一次崩溃报告
  24 |     }
  25 |     
  26 |     // 计数器
  27 |     private val phaseReplacePathErrorCounter = AtomicInteger(0)
  28 |     private val phaseReplaceFailureCounter = AtomicInteger(0)
  29 |     private val slideWindowHitCounter = AtomicInteger(0)
  30 |     private val textFilterCounter = AtomicInteger(0)
  31 |     private val preThinkClearMissCounter = AtomicInteger(0)
  32 |     private val ghostPhaseCounter = AtomicInteger(0)
  33 |     
  34 |     /**
  35 |      * 报告 PhaseReplace 路径错误
  36 |      * 当 currentPhaseId 不是 "thinking" 时触发 phase 切换
  37 |      */
  38 |     fun reportPhaseReplacePathError() {
  39 |         val count = phaseReplacePathErrorCounter.incrementAndGet()
  40 |         Timber.tag(TAG).e("🚨 PhaseReplace路径错误次数: $count")
  41 |         
  42 |         if (count % CRASH_REPORT_THRESHOLD == 0) {
  43 |             // 每5次错误发送到崩溃监控
  44 |             reportToCrashlytics("PhaseReplace path error occurred $count times")
  45 |         }
  46 |     }
  47 |     
  48 |     /**
  49 |      * 报告 PhaseReplace 执行失败
  50 |      * 当 reduce 函数执行后验证失败时调用
  51 |      */
  52 |     fun reportPhaseReplaceFailure() {
  53 |         val count = phaseReplaceFailureCounter.incrementAndGet()
  54 |         Timber.tag(TAG).e("🚨 PhaseReplace执行失败次数: $count")
  55 |         
  56 |         // PhaseReplace 失败是严重问题，立即报告
  57 |         reportToCrashlytics("PhaseReplace execution failed")
  58 |     }
  59 |     
  60 |     /**
  61 |      * 报告滑窗跨块检测命中
  62 |      * 当滑窗检测到跨块标签时调用
  63 |      */
  64 |     fun reportSlideWindowHit() {
  65 |         val count = slideWindowHitCounter.incrementAndGet()
  66 |         Timber.tag(TAG).d("📊 滑窗跨块检测命中次数: $count")
  67 |         
  68 |         // 每100次命中记录一次统计
  69 |         if (count % 100 == 0) {
  70 |             Timber.tag(TAG).i("📊 滑窗检测统计: 累计命中 $count 次")
  71 |         }
  72 |     }
  73 |     
  74 |     /**
  75 |      * 报告文本过滤统计
  76 |      * 当空白文本被过滤时调用
  77 |      */
  78 |     fun reportTextFiltered() {
  79 |         val count = textFilterCounter.incrementAndGet()
  80 |         Timber.tag(TAG).d("📊 文本过滤次数: $count")
  81 |         
  82 |         // 每50次过滤记录一次统计
  83 |         if (count % 50 == 0) {
  84 |             Timber.tag(TAG).i("📊 文本过滤统计: 累计过滤 $count 次空白文本")
  85 |         }
  86 |     }
  87 |     
  88 |     /**
  89 |      * 报告 PreThinkClear 事件缺失
  90 |      * 当应该发送 PreThinkClear 但没有发送时调用
  91 |      */
  92 |     fun reportPreThinkClearMiss() {
  93 |         val count = preThinkClearMissCounter.incrementAndGet()
  94 |         Timber.tag(TAG).w("🚨 PreThinkClear事件缺失次数: $count")
  95 |         
  96 |         if (count % CRASH_REPORT_THRESHOLD == 0) {
  97 |             reportToCrashlytics("PreThinkClear event missing $count times")
  98 |         }
  99 |     }
 100 |     
 101 |     /**
 102 |      * 报告幽灵 Phase 检测
 103 |      * 当检测到应该被删除但仍存在的 phase 时调用
 104 |      */
 105 |     fun reportGhostPhase(phaseId: String) {
 106 |         val count = ghostPhaseCounter.incrementAndGet()
 107 |         Timber.tag(TAG).e("🚨 检测到幽灵Phase: $phaseId, 累计次数: $count")
 108 |         
 109 |         // 幽灵 Phase 是严重的 UI 问题，立即报告
 110 |         reportToCrashlytics("Ghost phase detected: $phaseId (total: $count)")
 111 |     }
 112 |     
 113 |     /**
 114 |      * 获取当前监控统计
 115 |      */
 116 |     fun getMetricsSnapshot(): MetricsSnapshot {
 117 |         return MetricsSnapshot(
 118 |             phaseReplacePathErrors = phaseReplacePathErrorCounter.get(),
 119 |             phaseReplaceFailures = phaseReplaceFailureCounter.get(),
 120 |             slideWindowHits = slideWindowHitCounter.get(),
 121 |             textFiltered = textFilterCounter.get(),
 122 |             preThinkClearMisses = preThinkClearMissCounter.get(),
 123 |             ghostPhases = ghostPhaseCounter.get()
 124 |         )
 125 |     }
 126 |     
 127 |     /**
 128 |      * 重置所有计数器
 129 |      * 用于测试或新会话开始时
 130 |      */
 131 |     fun reset() {
 132 |         phaseReplacePathErrorCounter.set(0)
 133 |         phaseReplaceFailureCounter.set(0)
 134 |         slideWindowHitCounter.set(0)
 135 |         textFilterCounter.set(0)
 136 |         preThinkClearMissCounter.set(0)
 137 |         ghostPhaseCounter.set(0)
 138 |         
 139 |         Timber.tag(TAG).i("📊 ThinkingBox 监控指标已重置")
 140 |     }
 141 |     
 142 |     /**
 143 |      * 发送到崩溃监控系统
 144 |      * 实际项目中应该集成 Firebase Crashlytics 或其他监控服务
 145 |      */
 146 |     private fun reportToCrashlytics(message: String) {
 147 |         // TODO: 集成实际的崩溃监控服务
 148 |         // Crashlytics.recordException(Exception(message))
 149 |         Timber.tag(TAG).e("💥 [CRASH_REPORT] $message")
 150 |     }
 151 |     
 152 |     /**
 153 |      * 监控指标快照
 154 |      */
 155 |     data class MetricsSnapshot(
 156 |         val phaseReplacePathErrors: Int,
 157 |         val phaseReplaceFailures: Int,
 158 |         val slideWindowHits: Int,
 159 |         val textFiltered: Int,
 160 |         val preThinkClearMisses: Int,
 161 |         val ghostPhases: Int
 162 |     ) {
 163 |         override fun toString(): String {
 164 |             return """
 165 |                 ThinkingBox Metrics Snapshot:
 166 |                 - PhaseReplace Path Errors: $phaseReplacePathErrors
 167 |                 - PhaseReplace Failures: $phaseReplaceFailures
 168 |                 - Slide Window Hits: $slideWindowHits
 169 |                 - Text Filtered: $textFiltered
 170 |                 - PreThinkClear Misses: $preThinkClearMisses
 171 |                 - Ghost Phases: $ghostPhases
 172 |             """.trimIndent()
 173 |         }
 174 |     }
 175 | }
 176 | 
 177 | /**
 178 |  * 监控指标扩展函数
 179 |  * 提供便捷的监控调用方式
 180 |  */
 181 | object ThinkingBoxMetricsExt {
 182 |     
 183 |     /**
 184 |      * 在关键代码块中添加监控
 185 |      */
 186 |     inline fun <T> withMetrics(
 187 |         metrics: ThinkingBoxMetrics,
 188 |         operation: String,
 189 |         block: () -> T
 190 |     ): T {
 191 |         val startTime = System.currentTimeMillis()
 192 |         return try {
 193 |             val result = block()
 194 |             val duration = System.currentTimeMillis() - startTime
 195 |             Timber.tag("TB-METRICS").d("✅ $operation 执行成功，耗时: ${duration}ms")
 196 |             result
 197 |         } catch (e: Exception) {
 198 |             val duration = System.currentTimeMillis() - startTime
 199 |             Timber.tag("TB-METRICS").e("❌ $operation 执行失败，耗时: ${duration}ms, 错误: ${e.message}")
 200 |             throw e
 201 |         }
 202 |     }
 203 |     
 204 |     /**
 205 |      * 验证 PhaseReplace 执行结果
 206 |      */
 207 |     fun verifyPhaseReplace(
 208 |         metrics: ThinkingBoxMetrics,
 209 |         oldId: String,
 210 |         newId: String,
 211 |         oldPhaseExists: Boolean,
 212 |         newPhaseExists: Boolean,
 213 |         oldPhaseRemoved: Boolean,
 214 |         newPhaseAdded: Boolean
 215 |     ) {
 216 |         if (!oldPhaseRemoved && oldPhaseExists) {
 217 |             metrics.reportGhostPhase(oldId)
 218 |         }
 219 |         
 220 |         if (!newPhaseAdded) {
 221 |             metrics.reportPhaseReplaceFailure()
 222 |         }
 223 |         
 224 |         Timber.tag("TB-METRICS").d(
 225 |             "🔍 PhaseReplace验证: $oldId→$newId, " +
 226 |             "旧存在:$oldPhaseExists, 新存在:$newPhaseExists, " +
 227 |             "旧删除:$oldPhaseRemoved, 新添加:$newPhaseAdded"
 228 |         )
 229 |     }
 230 | }

```

`main\kotlin\com\example\gymbro\features\thinkingbox\internal\mvi\ThinkingBoxFacadeImpl.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.internal.mvi
   2 | 
   3 | import com.example.gymbro.features.thinkingbox.domain.interfaces.ThinkingBoxFacade
   4 | import com.example.gymbro.features.thinkingbox.domain.interfaces.UiState
   5 | import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
   6 | import kotlinx.coroutines.flow.Flow
   7 | import timber.log.Timber
   8 | import javax.inject.Inject
   9 | import javax.inject.Singleton
  10 | 
  11 | /**
  12 |  * ThinkingBoxFacade 实现类 - 多轮对话状态隔离版本
  13 |  *
  14 |  * 🔥 v9.0 多轮对话状态隔离架构：
  15 |  * - 使用 ThinkingBoxManager 管理多个实例
  16 |  * - 每个 messageId 对应独立的状态
  17 |  * - 支持并发处理多个对话
  18 |  * - 完全隔离，不会相互干扰
  19 |  *
  20 |  * 设计原则：
  21 |  * - 代理模式：将实际工作委托给 ThinkingBoxInstance
  22 |  * - 状态隔离：每个对话有独立的状态管理
  23 |  * - 资源管理：支持实例生命周期管理
  24 |  * - 向后兼容：保持接口不变
  25 |  */
  26 | @Singleton
  27 | class ThinkingBoxFacadeImpl
  28 |     @Inject
  29 |     constructor(
  30 |         private val manager: ThinkingBoxManager,
  31 |     ) : ThinkingBoxFacade {
  32 |         companion object {
  33 |             private const val TAG = "ThinkingBoxFacade"
  34 |         }
  35 | 
  36 |         override suspend fun start(): Flow<UiState> {
  37 |             Timber.tag(TAG).w("🚨 使用了废弃的 start() 方法，建议使用 startWithMessageId()")
  38 |             // 🔥 为向后兼容，生成临时 messageId
  39 |             val tempMessageId = "temp-${System.currentTimeMillis()}"
  40 |             return startWithMessageId(tempMessageId)
  41 |         }
  42 | 
  43 |         override suspend fun startWithMessageId(messageId: String): Flow<UiState> {
  44 |             Timber.tag(TAG).i("🔥 [多轮对话] 启动ThinkingBox: messageId=$messageId")
  45 | 
  46 |             // 🔥 获取或创建对应的实例（暂时不传递回调，等待Coach模块集成）
  47 |             val instance = manager.getOrCreateInstance(messageId)
  48 | 
  49 |             // 🔥 启动实例并返回状态流
  50 |             return instance.start()
  51 |         }
  52 | 
  53 |         /**
  54 |          * 🔥 【思考内容保存修复】带回调的启动方法
  55 |          */
  56 |         suspend fun startWithMessageId(
  57 |             messageId: String,
  58 |             onAiMessageComplete: ((messageId: String, finalMarkdown: String, thinkingNodes: String?) -> Unit)?,
  59 |         ): Flow<UiState> {
  60 |             Timber.tag(TAG).i("🔥 [思考内容保存] 启动ThinkingBox with callback: messageId=$messageId")
  61 | 
  62 |             // 🔥 获取或创建对应的实例，传递回调
  63 |             val instance = manager.getOrCreateInstance(messageId, onAiMessageComplete)
  64 | 
  65 |             // 🔥 启动实例并返回状态流
  66 |             return instance.start()
  67 |         }
  68 | 
  69 |         override fun sendEvent(event: ThinkingEvent) {
  70 |             Timber.tag(TAG).w("🚨 [多轮对话] sendEvent 需要 messageId，但当前事件没有指定目标实例")
  71 |             // 🔥 对于没有 messageId 的事件，暂时忽略或记录警告
  72 |             // 未来可能需要重新设计事件系统以支持目标实例
  73 |         }
  74 | 
  75 |         override fun submit(token: String): Boolean {
  76 |             Timber.tag(TAG).w("🚨 [多轮对话] submit 方法已废弃，Parser 直接从 RawTokenBus 读取")
  77 |             return true
  78 |         }
  79 | 
  80 |         override suspend fun reset() {
  81 |             Timber.tag(TAG).i("🔥 [多轮对话] 重置所有ThinkingBox实例")
  82 |             manager.clearAllInstances()
  83 |         }
  84 | 
  85 |         override fun markPersisted() {
  86 |             Timber.tag(TAG).w("🚨 [多轮对话] markPersisted 需要 messageId，无法标记特定实例")
  87 |         // 🔥 对于全局操作，暂时忽略或记录警告
  88 |     }
  89 | 
  90 |     override val currentState: UiState
  91 |         get() {
  92 |             Timber.tag(TAG).w("🚨 [多轮对话] currentState 无法确定具体实例，返回空状态")
  93 |             return UiState()
  94 |         }
  95 | 
  96 |     /**
  97 |      * 🔥 新增：获取管理器实例（用于调试和高级操作）
  98 |      */
  99 |     fun getManager(): ThinkingBoxManager = manager
 100 | }

```

`main\kotlin\com\example\gymbro\features\thinkingbox\internal\mvi\ThinkingBoxInstance.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.internal.mvi
   2 | 
   3 | import com.example.gymbro.features.thinkingbox.domain.interfaces.UiState
   4 | import com.example.gymbro.features.thinkingbox.domain.mapper.DomainMapper
   5 | import com.example.gymbro.features.thinkingbox.domain.model.events.*
   6 | import com.example.gymbro.features.thinkingbox.internal.adapter.toStandardUiState
   7 | import kotlinx.coroutines.Job
   8 | import kotlinx.coroutines.flow.Flow
   9 | import kotlinx.coroutines.flow.MutableStateFlow
  10 | import kotlinx.coroutines.flow.map
  11 | import kotlinx.coroutines.launch
  12 | import timber.log.Timber
  13 | import java.util.concurrent.atomic.AtomicBoolean
  14 | 
  15 | /**
  16 |  * ThinkingBoxInstance - 独立的 ThinkingBox 实例
  17 |  *
  18 |  * 🔥 多轮对话状态隔离解决方案：
  19 |  * - 每个 messageId 对应一个独立的实例
  20 |  * - 独立的状态管理，不会相互干扰
  21 |  * - 独立的生命周期管理
  22 |  * - 支持并发处理多个对话
  23 |  *
  24 |  * 🔥 【多轮对话架构升级】集成ConversationScope：
  25 |  * - 使用ConversationScope管理独立的token流和事件流
  26 |  * - 使用无状态的parseTokenStream函数进行解析
  27 |  * - 实现真正的对话隔离和资源管理
  28 |  */
  29 | class ThinkingBoxInstance(
  30 |     private val messageId: String,
  31 |     // 🔥 【多轮对话架构升级】使用ConversationScope替代coroutineScope
  32 |     private val conversationScope: com.example.gymbro.core.network.router.ConversationScope,
  33 |     private val domainMapper: DomainMapper,
  34 |     private val historySaver: com.example.gymbro.features.thinkingbox.internal.history.HistorySaver,
  35 |     // 🔥 【思考内容保存修复】AI消息完成回调
  36 |     private val onAiMessageComplete: ((messageId: String, finalMarkdown: String, thinkingNodes: String?) -> Unit)? = null,
  37 | ) {
  38 |     companion object {
  39 |         private const val TAG = "ThinkingBoxInstance"
  40 |     }
  41 | 
  42 |     // 🔥 独立状态管理 - 每个实例有自己的状态
  43 |     private val _uiState = MutableStateFlow(ThinkingUiState())
  44 |     private val isStarted = AtomicBoolean(false)
  45 |     private var parserJob: Job? = null
  46 |     private var finalRenderingTimeoutJob: Job? = null
  47 | 
  48 |     // 🔥 实例创建时间，用于清理策略
  49 |     private val createdAt = System.currentTimeMillis()
  50 | 
  51 |     // 🔥 事件流 - 独立的事件处理
  52 |     private val thinkingEventFlow = kotlinx.coroutines.flow.MutableSharedFlow<ThinkingEvent>()
  53 | 
  54 |     init {
  55 |         Timber.tag(TAG).d("🔥 创建ThinkingBox实例: messageId=$messageId")
  56 | 
  57 |         // 🔥 建立独立的事件处理管道
  58 |         domainMapper.startSingleConsumerCollection { thinkingEvent ->
  59 |             handleThinkingEvent(thinkingEvent)
  60 |         }
  61 | 
  62 |         // 🔥 【多轮对话修复】注册此实例的事件流到HistorySaver
  63 |         historySaver.registerInstanceEventFlow(thinkingEventFlow, messageId)
  64 |     }
  65 | 
  66 |     /**
  67 |      * 启动此实例的处理流程
  68 |      * 🔥 【多轮对话架构升级】使用ConversationScope和parseTokenStream函数
  69 |      */
  70 |     suspend fun start(): Flow<UiState> {
  71 |         if (!isStarted.compareAndSet(false, true)) {
  72 |             Timber.tag(TAG).w("🚨 实例已启动，忽略重复调用: messageId=$messageId")
  73 |             return _uiState.map { it.toStandardUiState() }
  74 |         }
  75 | 
  76 |         Timber.tag(TAG).i("🔥 启动ThinkingBox实例: messageId=$messageId")
  77 | 
  78 |         // 🔥 取消之前的Parser协程，防止重复监听
  79 |         parserJob?.cancel()
  80 | 
  81 |         // 🔥 重置状态并立即发送Start事件
  82 |         resetInternalState()
  83 |         sendEvent(ThinkingEvent.Start(id = messageId, messageId = messageId))
  84 | 
  85 |         // 🔥 【多轮对话架构升级】启动主处理协程，编排parseTokenStream和DomainMapper
  86 |         parserJob =
  87 |             conversationScope.launch {
  88 |                 try {
  89 |                     Timber.tag(TAG).i("🔥 启动ConversationScope处理协程: messageId=$messageId")
  90 | 
  91 |                     // 🔥 启动解析器，监听 conversationScope.tokens
  92 |                     launch(kotlinx.coroutines.CoroutineName("Parser-$messageId")) {
  93 |                         com.example.gymbro.features.thinkingbox.domain.parser.parseTokenStream(
  94 |                             messageId = messageId,
  95 |                             tokens = conversationScope.tokens,
  96 |                             onEvent = { event ->
  97 |                                 // 🔥 706任务保存：检查FinalArrived事件，触发历史保存
  98 |                                 if (event is SemanticEvent.FinalArrived) {
  99 |                                     handleFinalArrivedEvent(event)
 100 |                                 }
 101 | 
 102 |                                 // 将解析后的事件发射到 conversationScope.events
 103 |                                 conversationScope.emitEvent(event)
 104 |                             },
 105 |                         )
 106 |                     }
 107 | 
 108 |                     // 🔥 启动 DomainMapper，监听 conversationScope.events
 109 |                     launch(kotlinx.coroutines.CoroutineName("Mapper-$messageId")) {
 110 |                         conversationScope.events.collect { event ->
 111 |                             // 🔥 类型安全转换：确保事件是SemanticEvent类型
 112 |                             if (event is SemanticEvent) {
 113 |                                 val thinkingEvents = domainMapper.mapToThinkingEvents(event)
 114 |                                 thinkingEvents.forEach { thinkingEvent ->
 115 |                                     handleThinkingEvent(thinkingEvent)
 116 |                                 }
 117 |                             } else {
 118 |                                 Timber
 119 |                                     .tag(TAG)
 120 |                                     .w("[$messageId] Received non-SemanticEvent: ${event::class.simpleName}")
 121 |                             }
 122 |                         }
 123 |                     }
 124 |                 } catch (e: Exception) {
 125 |                     if (e is kotlinx.coroutines.CancellationException) {
 126 |                         Timber.tag(TAG).w("🔥 ConversationScope job被取消: messageId=$messageId")
 127 |                     } else {
 128 |                         Timber.tag(TAG).e(e, "🚨 ConversationScope处理异常: messageId=$messageId")
 129 |                         sendEvent(ThinkingEvent.Error("ConversationScope crashed: ${e.message}", e))
 130 |                     }
 131 |                 } finally {
 132 |                     isStarted.set(false)
 133 |                     Timber.tag(TAG).i("🚪 ConversationScope job已结束: messageId=$messageId")
 134 |                 }
 135 |             }
 136 | 
 137 |         // 🔥 当 conversationScope 被取消时，parserJob 也会被自动取消
 138 |         conversationScope.coroutineContext[Job]?.invokeOnCompletion {
 139 |             Timber.d("[$messageId] ConversationScope completed, instance processing finished.")
 140 |         }
 141 | 
 142 |         return _uiState.map { it.toStandardUiState() }
 143 |     }
 144 | 
 145 |     /**
 146 |      * 发送事件到此实例
 147 |      */
 148 |     fun sendEvent(event: ThinkingEvent) {
 149 |         domainMapper.sendEvent(event)
 150 |     }
 151 | 
 152 |     /**
 153 |      * 重置此实例的内部状态
 154 |      * 🔥 【多轮对话架构升级】不再需要重置streamingParser，因为使用无状态函数
 155 |      */
 156 |     private fun resetInternalState() {
 157 |         domainMapper.reset()
 158 |         _uiState.value = ThinkingUiState()
 159 |         Timber.tag(TAG).i("🔥 实例状态已重置: messageId=$messageId")
 160 |     }
 161 | 
 162 |     /**
 163 |      * 处理思考事件
 164 |      */
 165 |     private fun handleThinkingEvent(thinkingEvent: ThinkingEvent) {
 166 |         val oldState = _uiState.value
 167 |         _uiState.value = reduce(_uiState.value, thinkingEvent)
 168 |         val newState = _uiState.value
 169 | 
 170 |         // 🔥 关键事件的详细日志
 171 |         if (thinkingEvent is ThinkingEvent.PhaseRenderingComplete ||
 172 |             thinkingEvent is ThinkingEvent.PhaseQueueAdd ||
 173 |             thinkingEvent is ThinkingEvent.PhaseManagementActivated
 174 |         ) {
 175 |             Timber.tag("PHASE-DEBUG").e("🎯 [$messageId] 事件处理: ${thinkingEvent::class.simpleName} | oldPhase=${oldState.currentPhaseId} → newPhase=${newState.currentPhaseId}")
 176 |         }
 177 | 
 178 |         // 🔥 Phase转换控制
 179 |         if (oldState.currentPhaseId != newState.currentPhaseId) {
 180 |             val newPhaseId = newState.currentPhaseId
 181 |             Timber.tag("PHASE-DEBUG").e("🔄 [$messageId] currentPhaseId变化: ${oldState.currentPhaseId} → $newPhaseId")
 182 |             domainMapper.updateCurrentPhaseId(newPhaseId)
 183 |         }
 184 | 
 185 |         // 🔥 处理FinalAnswer事件，异步生成Summary
 186 |         if (thinkingEvent is ThinkingEvent.FinalAnswer) {
 187 |             handleFinalAnswer(thinkingEvent)
 188 |         }
 189 | 
 190 |         // 🔥 处理ThinkingFinalDetected事件，启动15秒倒计时
 191 |         if (thinkingEvent is ThinkingEvent.ThinkingFinalDetected) {
 192 |             startFinalRenderingTimeout()
 193 |         }
 194 | 
 195 |         // 🔥 【超时机制优化】检测最后phase完成并取消超时
 196 |         if (thinkingEvent is ThinkingEvent.PhaseRenderingComplete) {
 197 |             checkAndCancelTimeoutIfLastPhaseComplete(oldState, newState, thinkingEvent.phaseId)
 198 |         }
 199 | 
 200 |         // 🔥 【超时机制优化】检测状态变化，如果从等待最终渲染变为富文本渲染，取消超时
 201 |         if (oldState.isWaitingForFinalRendering &&
 202 |             !newState.isWaitingForFinalRendering &&
 203 |             newState.currentStep == ThinkingStep.RichTextRendering
 204 |         ) {
 205 |             cancelFinalRenderingTimeout("状态变化检测到最后phase完成")
 206 |         }
 207 | 
 208 |         // 🔥 发射事件到共享Flow，供HistorySaver监听
 209 |         conversationScope.launch {
 210 |             thinkingEventFlow.emit(thinkingEvent)
 211 |         }
 212 |     }
 213 | 
 214 |     /**
 215 |      * 处理最终答案事件
 216 |      */
 217 |     private fun handleFinalAnswer(event: ThinkingEvent.FinalAnswer) {
 218 |         conversationScope.launch(kotlinx.coroutines.Dispatchers.Default) {
 219 |             try {
 220 |                 Timber.tag(TAG).i("🔥 [$messageId] 开始异步生成Summary")
 221 |                 val currentState = _uiState.value
 222 |                 val bullets = extractBulletPoints(event.markdown)
 223 |                 // 🔥 【统一】直接使用简单估算，避免重复函数
 224 |                 val tokenCount = (currentState.tokensSnapshot.length + 3) / 4
 225 |                 val summary =
 226 |                     Summary(
 227 |                         durationText = currentState.thinkingDuration,
 228 |                         searchCnt = currentState.searchCount,
 229 |                         sourceCnt = event.sources.size,
 230 |                         bullets = bullets,
 231 |                         sources = event.sources,
 232 |                         isExpanded = false,
 233 |                         tokenCount = tokenCount,
 234 |                     )
 235 | 
 236 |                 Timber.tag(TAG).i("🔥 [$messageId] Summary生成完成，发送SummaryGenerated事件")
 237 |                 sendEvent(ThinkingEvent.SummaryGenerated(summary))
 238 |             } catch (e: Exception) {
 239 |                 Timber.tag(TAG).e(e, "🚨 [$messageId] Summary生成失败")
 240 |             }
 241 |         }
 242 |     }
 243 | 
 244 |     /**
 245 |      * 启动15秒倒计时
 246 |      */
 247 |     private fun startFinalRenderingTimeout() {
 248 |         finalRenderingTimeoutJob?.cancel()
 249 |         Timber.tag(TAG).i("🔥 [$messageId] 启动15秒倒计时")
 250 | 
 251 |         finalRenderingTimeoutJob =
 252 |             conversationScope.launch {
 253 |                 try {
 254 |                     kotlinx.coroutines.delay(15_000L)
 255 |                     Timber.tag(TAG).i("🔥 [$messageId] 15秒倒计时结束，发送FinalRenderingTimeout事件")
 256 |                     sendEvent(ThinkingEvent.FinalRenderingTimeout)
 257 |                 } catch (e: kotlinx.coroutines.CancellationException) {
 258 |                     Timber.tag(TAG).d("🔥 [$messageId] 15秒倒计时被取消")
 259 |                 } catch (e: Exception) {
 260 |                     Timber.tag(TAG).e(e, "🚨 [$messageId] 15秒倒计时异常")
 261 |                 } finally {
 262 |                     finalRenderingTimeoutJob = null
 263 |                 }
 264 |             }
 265 |     }
 266 | 
 267 |     /**
 268 |      * 清理此实例
 269 |      */
 270 |     fun cleanup() {
 271 |         Timber.tag(TAG).d("🔥 清理ThinkingBox实例: messageId=$messageId")
 272 | 
 273 |         parserJob?.cancel()
 274 |         parserJob = null
 275 | 
 276 |         finalRenderingTimeoutJob?.cancel()
 277 |         finalRenderingTimeoutJob = null
 278 | 
 279 |         isStarted.set(false)
 280 |     }
 281 | 
 282 |     /**
 283 |      * 检查实例是否已完成
 284 |      */
 285 |     fun isCompleted(): Boolean {
 286 |         val state = _uiState.value
 287 |         return !state.isStreaming && state.final != null
 288 |     }
 289 | 
 290 |     /**
 291 |      * 检查实例是否超过指定时间
 292 |      */
 293 |     fun isOlderThan(durationMs: Long): Boolean = System.currentTimeMillis() - createdAt > durationMs
 294 | 
 295 |     /**
 296 |      * 获取实例创建时间（用于调试）
 297 |      */
 298 |     fun getCreatedAt(): Long = createdAt
 299 | 
 300 |     /**
 301 |      * 获取当前状态
 302 |      */
 303 |     val currentState: UiState
 304 |         get() = _uiState.value.toStandardUiState()
 305 | 
 306 |     /**
 307 |      * 🔥 706任务保存：处理FinalArrived事件，触发历史保存
 308 |      *
 309 |      * 当AI消息的最终内容到达时，将其保存到历史记录中
 310 |      * 使用现有的HistorySaver进行ThinkingBox特定的历史保存
 311 |      */
 312 |     private fun handleFinalArrivedEvent(event: SemanticEvent.FinalArrived) {
 313 |         conversationScope.launch {
 314 |             try {
 315 |                 Timber
 316 |                     .tag(TAG)
 317 |                     .d("🔥 [706任务保存] 处理FinalArrived事件: messageId=$messageId, content长度=${event.markdown.length}")
 318 | 
 319 |                 // 🔥 【思考内容保存修复】收集完整的思考内容
 320 |                 val currentState = _uiState.value
 321 |                 val thinkingNodes =
 322 |                     try {
 323 |                         // 将思考阶段转换为JSON字符串保存
 324 |                         kotlinx.serialization.json.Json
 325 |                             .encodeToString(currentState.phases)
 326 |                     } catch (e: Exception) {
 327 |                         Timber.tag(TAG).w(e, "序列化思考节点失败")
 328 |                         null
 329 |                     }
 330 | 
 331 |                 // 🔥 【关键修复】通过回调触发包含完整思考内容的AI消息保存
 332 |                 onAiMessageComplete?.invoke(messageId, event.markdown, thinkingNodes)
 333 | 
 334 |                 // 创建对应的ThinkingEvent.FinalAnswer事件
 335 |                 ThinkingEvent.FinalAnswer(
 336 |                     messageId = messageId,
 337 |                     markdown = event.markdown,
 338 |                 )
 339 | 
 340 |                 Timber
 341 |                     .tag(TAG)
 342 |                     .d("✅ [706任务保存] FinalArrived事件已处理，AI消息内容和思考过程已传递给Coach保存: messageId=$messageId")
 343 |             } catch (e: Exception) {
 344 |                 Timber.tag(TAG).e(e, "🚨 [706任务保存] 处理FinalArrived事件异常: messageId=$messageId")
 345 |             }
 346 |         }
 347 |     }
 348 | 
 349 |     /**
 350 |      * 🔥 【超时机制优化】检查并在最后phase完成时取消超时
 351 |      */
 352 |     private fun checkAndCancelTimeoutIfLastPhaseComplete(
 353 |         oldState: ThinkingUiState,
 354 |         newState: ThinkingUiState,
 355 |         completedPhaseId: String,
 356 |     ) {
 357 |         // 检查条件：
 358 |         // 1. 系统正在等待最终渲染
 359 |         // 2. 没有更多等待的phase
 360 |         // 3. 当前phase渲染已完成
 361 |         if (oldState.isWaitingForFinalRendering &&
 362 |             newState.pendingPhaseQueue.isEmpty() &&
 363 |             newState.currentPhaseRenderingComplete
 364 |         ) {
 365 |             Timber.tag(TAG).i("🔥 [$messageId] 检测到最后phase完成: $completedPhaseId")
 366 |             Timber
 367 |                 .tag(TAG)
 368 |                 .i("🔥 [$messageId] 条件满足：等待最终渲染=${oldState.isWaitingForFinalRendering}, 队列为空=${newState.pendingPhaseQueue.isEmpty()}, 渲染完成=${newState.currentPhaseRenderingComplete}")
 369 | 
 370 |             cancelFinalRenderingTimeout("最后phase完成检测")
 371 | 
 372 |             // 🔥 发送LastPhaseRenderingComplete事件用于日志记录
 373 |             conversationScope.launch {
 374 |                 sendEvent(ThinkingEvent.LastPhaseRenderingComplete(completedPhaseId))
 375 |             }
 376 |         }
 377 |     }
 378 | 
 379 |     /**
 380 |      * 🔥 【超时机制优化】取消15秒倒计时
 381 |      */
 382 |     private fun cancelFinalRenderingTimeout(reason: String) {
 383 |         finalRenderingTimeoutJob?.let { job ->
 384 |             if (job.isActive) {
 385 |                 job.cancel()
 386 |                 Timber.tag(TAG).i("🔥 [$messageId] 15秒倒计时已取消: $reason")
 387 |             }
 388 |         }
 389 |         finalRenderingTimeoutJob = null
 390 |     }
 391 | }

```

`main\kotlin\com\example\gymbro\features\thinkingbox\internal\mvi\ThinkingBoxManager.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.internal.mvi
   2 | 
   3 | import com.example.gymbro.features.thinkingbox.domain.mapper.DomainMapper
   4 | import kotlinx.coroutines.CoroutineScope
   5 | import timber.log.Timber
   6 | import java.util.concurrent.ConcurrentHashMap
   7 | import javax.inject.Inject
   8 | import javax.inject.Singleton
   9 | 
  10 | /**
  11 |  * ThinkingBoxManager - ThinkingBox 实例管理器
  12 |  *
  13 |  * 🔥 多轮对话状态隔离解决方案：
  14 |  * - 管理多个 ThinkingBox 实例
  15 |  * - 按 messageId 隔离状态
  16 |  * - 支持实例生命周期管理
  17 |  * - 支持并发处理多个对话
  18 |  */
  19 | @Singleton
  20 | class ThinkingBoxManager @Inject constructor(
  21 |     @com.example.gymbro.features.thinkingbox.internal.di.ThinkingBoxScope
  22 |     private val coroutineScope: CoroutineScope,
  23 |     private val streamingParser: com.example.gymbro.features.thinkingbox.domain.parser.StreamingThinkingMLParser,
  24 |     private val domainMapperFactory: DomainMapperFactory,
  25 |     private val historySaver: com.example.gymbro.features.thinkingbox.internal.history.HistorySaver,
  26 |     // 🔥 【多轮对话架构升级】注入TokenRouter以获取ConversationScope
  27 |     private val tokenRouter: com.example.gymbro.core.network.router.TokenRouter
  28 | ) {
  29 |     companion object {
  30 |         private const val TAG = "ThinkingBoxManager"
  31 |         private const val CLEANUP_INTERVAL_MS = 30 * 60 * 1000L // 30分钟
  32 |     }
  33 | 
  34 |     // 🔥 按 messageId 管理独立实例
  35 |     private val instances = ConcurrentHashMap<String, ThinkingBoxInstance>()
  36 | 
  37 |     /**
  38 |      * 获取或创建 ThinkingBox 实例
  39 |      * 🔥 【多轮对话架构升级】从TokenRouter获取ConversationScope并传递给ThinkingBoxInstance
  40 |      */
  41 |     fun getOrCreateInstance(
  42 |         messageId: String,
  43 |         onAiMessageComplete: ((messageId: String, finalMarkdown: String, thinkingNodes: String?) -> Unit)? = null,
  44 |     ): ThinkingBoxInstance =
  45 |         instances.computeIfAbsent(messageId) { id ->
  46 |             Timber.tag(TAG).d("🔥 创建新的ThinkingBox实例: messageId=$id")
  47 | 
  48 |             // 🔥 【多轮对话架构升级】从TokenRouter获取或创建ConversationScope
  49 |             val conversationScope = tokenRouter.getOrCreateScope(id)
  50 |             Timber.tag(TAG).d("🔥 获取ConversationScope: messageId=$id, scope=${conversationScope.hashCode()}")
  51 | 
  52 |             ThinkingBoxInstance(
  53 |                 messageId = id,
  54 |                 conversationScope = conversationScope,
  55 |                 domainMapper = domainMapperFactory.create(),
  56 |                 historySaver = historySaver,
  57 |                 onAiMessageComplete = onAiMessageComplete,
  58 |             )
  59 |         }
  60 | 
  61 |     /**
  62 |      * 移除指定的实例
  63 |      */
  64 |     fun removeInstance(messageId: String) {
  65 |         instances.remove(messageId)?.let { instance ->
  66 |             Timber.tag(TAG).d("🔥 移除ThinkingBox实例: messageId=$messageId")
  67 |             instance.cleanup()
  68 |         }
  69 |     }
  70 | 
  71 |     /**
  72 |      * 获取当前实例数量
  73 |      */
  74 |     fun getInstanceCount(): Int = instances.size
  75 | 
  76 |     /**
  77 |      * 获取所有活跃的 messageId
  78 |      */
  79 |     fun getActiveMessageIds(): Set<String> = instances.keys.toSet()
  80 | 
  81 |     /**
  82 |      * 清理已完成且超时的实例
  83 |      */
  84 |     fun cleanupCompletedInstances() {
  85 |         val toRemove = mutableListOf<String>()
  86 | 
  87 |         instances.forEach { (messageId, instance) ->
  88 |             if (instance.isCompleted() && instance.isOlderThan(CLEANUP_INTERVAL_MS)) {
  89 |                 toRemove.add(messageId)
  90 |             }
  91 |         }
  92 | 
  93 |         toRemove.forEach { messageId ->
  94 |             removeInstance(messageId)
  95 |             Timber.tag(TAG).d("🔥 清理超时实例: messageId=$messageId")
  96 |         }
  97 | 
  98 |         if (toRemove.isNotEmpty()) {
  99 |             Timber.tag(TAG).i("🔥 清理完成，移除了 ${toRemove.size} 个实例，当前活跃实例: ${instances.size}")
 100 |         }
 101 |     }
 102 | 
 103 |     /**
 104 |      * 强制清理所有实例
 105 |      */
 106 |     fun clearAllInstances() {
 107 |         val count = instances.size
 108 |         instances.values.forEach { it.cleanup() }
 109 |         instances.clear()
 110 |         Timber.tag(TAG).i("🔥 强制清理所有实例，共清理 $count 个实例")
 111 |     }
 112 | 
 113 |     /**
 114 |      * 获取实例状态信息（用于调试）
 115 |      */
 116 |     fun getInstancesInfo(): Map<String, String> {
 117 |         return instances.mapValues { (messageId, instance) ->
 118 |             "completed=${instance.isCompleted()}, age=${System.currentTimeMillis() - instance.getCreatedAt()}ms"
 119 |         }
 120 |     }
 121 | }
 122 | 
 123 | /**
 124 |  * DomainMapperFactory - 创建独立的 DomainMapper 实例
 125 |  *
 126 |  * 🔥 每个 ThinkingBox 实例需要独立的 DomainMapper
 127 |  * 避免状态共享和竞态条件
 128 |  */
 129 | @Singleton
 130 | class DomainMapperFactory @Inject constructor(
 131 |     // 注入创建 DomainMapper 所需的依赖
 132 |     private val rawThinkingLogger: com.example.gymbro.features.thinkingbox.domain.logger.RawThinkingLogger
 133 | ) {
 134 | 
 135 |     /**
 136 |      * 创建新的 DomainMapper 实例
 137 |      */
 138 |     fun create(): DomainMapper {
 139 |         return DomainMapper(rawThinkingLogger)
 140 |     }
 141 | }

```

`main\kotlin\com\example\gymbro\features\thinkingbox\internal\presentation\model\RenderableNode.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.internal.presentation.model
   2 | 
   3 | import androidx.compose.runtime.Immutable
   4 | import androidx.compose.ui.text.AnnotatedString
   5 | 
   6 | /**
   7 |  * RenderableNode - UI 消费模型
   8 |  *
   9 |  * 基于 626_thinkingbox_impl_spec.md 规范实现
  10 |  * 供 LazyColumn 使用的渲染节点模型
  11 |  *
  12 |  * 设计原则：
  13 |  * - 不可变数据结构
  14 |  * - 包含完整渲染信息
  15 |  * - 支持层级结构
  16 |  * - 优化列表性能
  17 |  */
  18 | @Immutable
  19 | data class RenderableNode(
  20 |     /**
  21 |      * 节点唯一标识
  22 |      */
  23 |     val id: String,
  24 | 
  25 |     /**
  26 |      * 层级深度（用于缩进和连线）
  27 |      */
  28 |     val level: Int,
  29 | 
  30 |     /**
  31 |      * 块内容
  32 |      */
  33 |     val block: BlockContent,
  34 | 
  35 |     /**
  36 |      * 是否展开
  37 |      */
  38 |     val isExpanded: Boolean = true,
  39 | 
  40 |     /**
  41 |      * 思考通道
  42 |      */
  43 |     val channel: String = "default",
  44 | 
  45 |     /**
  46 |      * 节点类型
  47 |      */
  48 |     val type: NodeType = NodeType.PHASE,
  49 | 
  50 |     /**
  51 |      * 时间戳
  52 |      */
  53 |     val timestamp: Long = System.currentTimeMillis(),
  54 | 
  55 |     /**
  56 |      * 是否完成
  57 |      */
  58 |     val isCompleted: Boolean = false,
  59 | 
  60 |     /**
  61 |      * 动画状态
  62 |      */
  63 |     val animationState: AnimationState = AnimationState.IDLE,
  64 | 
  65 |     /**
  66 |      * 思考开始时间
  67 |      */
  68 |     val startTime: Long = System.currentTimeMillis(),
  69 | 
  70 |     /**
  71 |      * 思考结束时间
  72 |      */
  73 |     val endTime: Long? = null,
  74 | 
  75 |     /**
  76 |      * Token 数量
  77 |      */
  78 |     val tokenCount: Int = 0,
  79 | ) {
  80 |     /**
  81 |      * 是否可展开
  82 |      */
  83 |     val isExpandable: Boolean get() = block.content.length > 100 || type == NodeType.TOOL_CALL
  84 | 
  85 |     /**
  86 |      * 显示内容（考虑展开状态）
  87 |      */
  88 |     val displayContent: AnnotatedString get() = if (isExpanded || !isExpandable) {
  89 |         block.content
  90 |     } else {
  91 |         AnnotatedString(block.content.text.take(100) + "...")
  92 |     }
  93 | 
  94 |     /**
  95 |      * 思考持续时间（毫秒）
  96 |      */
  97 |     val thinkingDuration: Long get() = if (endTime != null) {
  98 |         endTime - startTime
  99 |     } else {
 100 |         System.currentTimeMillis() - startTime
 101 |     }
 102 | 
 103 |     /**
 104 |      * 格式化的思考时间
 105 |      */
 106 |     val formattedThinkingTime: String get() {
 107 |         val duration = thinkingDuration
 108 |         return when {
 109 |             duration < 1000 -> "${duration}ms"
 110 |             duration < 60000 -> String.format("%.1fs", duration / 1000.0)
 111 |             else -> String.format("%dm %ds", duration / 60000, (duration % 60000) / 1000)
 112 |         }
 113 |     }
 114 | }
 115 | 
 116 | /**
 117 |  * 块内容 - 节点的实际内容
 118 |  */
 119 | @Immutable
 120 | data class BlockContent(
 121 |     /**
 122 |      * 标题
 123 |      */
 124 |     val title: String,
 125 | 
 126 |     /**
 127 |      * 内容（支持富文本）
 128 |      */
 129 |     val content: AnnotatedString,
 130 | 
 131 |     /**
 132 |      * 内容格式
 133 |      */
 134 |     val format: ContentFormat = ContentFormat.MARKDOWN,
 135 | 
 136 |     /**
 137 |      * 元数据
 138 |      */
 139 |     val metadata: Map<String, String> = emptyMap(),
 140 | ) {
 141 |     /**
 142 |      * 纯文本内容
 143 |      */
 144 |     val plainText: String get() = content.text
 145 | 
 146 |     /**
 147 |      * 内容长度
 148 |      */
 149 |     val length: Int get() = content.length
 150 | 
 151 |     /**
 152 |      * 是否为空
 153 |      */
 154 |     val isEmpty: Boolean get() = content.text.isBlank()
 155 | }
 156 | 
 157 | /**
 158 |  * 节点类型枚举
 159 |  */
 160 | enum class NodeType {
 161 |     /**
 162 |      * 思考阶段
 163 |      */
 164 |     PHASE,
 165 | 
 166 |     /**
 167 |      * 工具调用
 168 |      */
 169 |     TOOL_CALL,
 170 | 
 171 |     /**
 172 |      * 最终结果
 173 |      */
 174 |     FINAL_RESULT,
 175 | 
 176 |     /**
 177 |      * 错误信息
 178 |      */
 179 |     ERROR,
 180 | 
 181 |     /**
 182 |      * 普通文本
 183 |      */
 184 |     TEXT,
 185 | 
 186 |     /**
 187 |      * 思考摘要（折叠状态）
 188 |      */
 189 |     SUMMARY,
 190 | }
 191 | 
 192 | /**
 193 |  * 内容格式枚举
 194 |  */
 195 | enum class ContentFormat {
 196 |     /**
 197 |      * Markdown 格式
 198 |      */
 199 |     MARKDOWN,
 200 | 
 201 |     /**
 202 |      * 纯文本
 203 |      */
 204 |     PLAIN_TEXT,
 205 | 
 206 |     /**
 207 |      * HTML 格式
 208 |      */
 209 |     HTML,
 210 | 
 211 |     /**
 212 |      * JSON 格式
 213 |      */
 214 |     JSON,
 215 | }
 216 | 
 217 | /**
 218 |  * 动画状态枚举
 219 |  */
 220 | enum class AnimationState {
 221 |     /**
 222 |      * 空闲状态
 223 |      */
 224 |     IDLE,
 225 | 
 226 |     /**
 227 |      * 进入动画
 228 |      */
 229 |     ENTERING,
 230 | 
 231 |     /**
 232 |      * 退出动画
 233 |      */
 234 |     EXITING,
 235 | 
 236 |     /**
 237 |      * 脉冲动画
 238 |      */
 239 |     PULSING,
 240 | 
 241 |     /**
 242 |      * 震动动画
 243 |      */
 244 |     SHAKING,
 245 | }
 246 | 
 247 | /**
 248 |  * 扩展函数：将字符串转换为 AnnotatedString
 249 |  */
 250 | fun String.toAnnotatedString(): AnnotatedString = AnnotatedString(this)

```

`main\kotlin\com\example\gymbro\features\thinkingbox\internal\presentation\strings\ThinkingBoxStrings.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.internal.presentation.strings
   2 | 
   3 | import com.example.gymbro.core.ui.text.UiText
   4 | 
   5 | /**
   6 |  * ThinkingBox 模块字符串资源
   7 |  *
   8 |  * 基于用户偏好，字符串资源保留在模块内部，不移动到 designSystem
   9 |  * 合并了 ui 包下的完整字符串资源，确保唯一真源
  10 |  */
  11 | object ThinkingBoxStrings {
  12 | 
  13 |     // === 状态描述 ===
  14 |     val statusThinking = UiText.DynamicString("思考中")
  15 |     val statusProcessing = UiText.DynamicString("处理中")
  16 |     val statusComplete = UiText.DynamicString("完成")
  17 |     val statusError = UiText.DynamicString("错误")
  18 |     val statusIdle = UiText.DynamicString("空闲")
  19 | 
  20 |     // === 按钮文本 ===
  21 |     val autoScroll = UiText.DynamicString("自动滚动")
  22 |     val manualScroll = UiText.DynamicString("手动滚动")
  23 |     val viewDetails = UiText.DynamicString("查看详情")
  24 |     val hideDetails = UiText.DynamicString("隐藏详情")
  25 |     val expand = UiText.DynamicString("展开")
  26 |     val collapse = UiText.DynamicString("折叠")
  27 | 
  28 |     // === 卡片标题 ===
  29 |     val summaryTitle = UiText.DynamicString("思考摘要")
  30 |     val finalAnswerTitle = UiText.DynamicString("最终答案")
  31 |     val progressTitle = UiText.DynamicString("思考进度")
  32 |     val detailsTitle = UiText.DynamicString("详细信息")
  33 | 
  34 |     // === 错误信息 ===
  35 |     val errorParsing = UiText.DynamicString("解析错误")
  36 |     val errorNetwork = UiText.DynamicString("网络错误")
  37 |     val errorTimeout = UiText.DynamicString("超时错误")
  38 |     val errorUnknown = UiText.DynamicString("未知错误")
  39 | 
  40 |     // === 提示信息 ===
  41 |     val loading = UiText.DynamicString("加载中...")
  42 |     val empty = UiText.DynamicString("暂无思考内容")
  43 |     val retry = UiText.DynamicString("重试")
  44 | 
  45 |     // === 可访问性描述 ===
  46 |     val iconThinkingDesc = UiText.DynamicString("思考状态图标")
  47 |     val iconCompleteDesc = UiText.DynamicString("完成状态图标")
  48 |     val iconErrorDesc = UiText.DynamicString("错误状态图标")
  49 |     val iconExpandDesc = UiText.DynamicString("展开按钮")
  50 |     val iconCollapseDesc = UiText.DynamicString("折叠按钮")
  51 |     val iconScrollDesc = UiText.DynamicString("滚动控制按钮")
  52 |     val iconCloseDesc = UiText.DynamicString("关闭按钮")
  53 |     val iconRefreshDesc = UiText.DynamicString("刷新按钮")
  54 | 
  55 |     // === 滚动相关 ===
  56 |     val scrollToTop = UiText.DynamicString("滚动到顶部")
  57 |     val scrollToBottom = UiText.DynamicString("滚动到底部")
  58 |     val autoScrollPause = UiText.DynamicString("暂停自动滚动")
  59 |     val autoScrollEnable = UiText.DynamicString("启用自动滚动")
  60 |     val scrollTopShort = UiText.DynamicString("顶部")
  61 |     val scrollBottomShort = UiText.DynamicString("底部")
  62 | 
  63 |     // === 操作相关 ===
  64 |     val copyContent = UiText.DynamicString("复制内容")
  65 |     val shareContent = UiText.DynamicString("分享内容")
  66 |     val close = UiText.DynamicString("关闭")
  67 |     val shareThinkingResult = UiText.DynamicString("分享思考结果")
  68 | 
  69 |     // === Header 相关 ===
  70 |     val retryDesc = UiText.DynamicString("重试")
  71 |     val statusErrorThinking = UiText.DynamicString("思考出错")
  72 |     val statusThinkingActive = UiText.DynamicString("正在思考...")
  73 |     val statusLoadingActive = UiText.DynamicString("加载中...")
  74 |     val statusIdleWaiting = UiText.DynamicString("待机中")
  75 | 
  76 |     // === Summary Card 相关 ===
  77 |     val summaryDesc = UiText.DynamicString("思考摘要")
  78 |     val thinkingCompleted = UiText.DynamicString("思考完成")
  79 |     val thinkingInProgress = UiText.DynamicString("正在思考...")
  80 |     val viewAnswer = UiText.DynamicString("查看答案")
  81 |     val keyThoughts = UiText.DynamicString("关键思考点")
  82 | 
  83 |     // === 统计标签 ===
  84 |     val phaseLabel = UiText.DynamicString("阶段")
  85 |     val toolCallLabel = UiText.DynamicString("工具调用")
  86 |     val totalWordsLabel = UiText.DynamicString("总字数")
  87 |     val durationLabel = UiText.DynamicString("时长")
  88 | 
  89 |     // === 状态文本 ===
  90 |     val thinkingStarted = UiText.DynamicString("开始思考")
  91 |     val thinkingPhaseChanged = UiText.DynamicString("阶段切换")
  92 |     val finalAnswerReady = UiText.DynamicString("最终答案已准备")
  93 | 
  94 |     // === 兼容性别名（保持向后兼容） ===
  95 |     val parseError = errorParsing
  96 |     val networkError = errorNetwork
  97 |     val unknownError = errorUnknown
  98 | 
  99 |     // === 动态字符串（带参数） ===
 100 |     fun progressPercent(percent: Int): UiText {
 101 |         return UiText.DynamicString("$percent%")
 102 |     }
 103 | 
 104 |     fun timeElapsed(time: String): UiText {
 105 |         return UiText.DynamicString("已用时: $time")
 106 |     }
 107 | 
 108 |     fun tokensProcessed(tokens: Int): UiText {
 109 |         return UiText.DynamicString("已处理: $tokens tokens")
 110 |     }
 111 | 
 112 |     fun thinkingCompletedWithNodes(nodeCount: Int): UiText {
 113 |         return UiText.DynamicString("思考完成 ($nodeCount 个节点)")
 114 |     }
 115 | 
 116 |     fun processingWithNodes(nodeCount: Int): UiText {
 117 |         return UiText.DynamicString("处理中 ($nodeCount 个节点)")
 118 |     }
 119 | 
 120 |     fun totalNodesCount(count: Int): UiText {
 121 |         return UiText.DynamicString("共 $count 个节点")
 122 |     }
 123 | 
 124 |     fun formatSeconds(seconds: Long): UiText {
 125 |         return UiText.DynamicString("${seconds}秒")
 126 |     }
 127 | 
 128 |     fun formatMinutesSeconds(minutes: Long, seconds: Long): UiText {
 129 |         return UiText.DynamicString("${minutes}分${seconds}秒")
 130 |     }
 131 | 
 132 |     fun formatHoursMinutes(hours: Long, minutes: Long): UiText {
 133 |         return UiText.DynamicString("${hours}时${minutes}分")
 134 |     }
 135 | }

```

`main\kotlin\com\example\gymbro\features\thinkingbox\internal\presentation\ui\AIThinkingCard.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.internal.presentation.ui
   2 | 
   3 | import androidx.compose.foundation.background
   4 | import androidx.compose.foundation.clickable
   5 | import androidx.compose.foundation.layout.*
   6 | import androidx.compose.foundation.shape.RoundedCornerShape
   7 | import androidx.compose.material3.MaterialTheme
   8 | import androidx.compose.material3.Text
   9 | import androidx.compose.runtime.*
  10 | import androidx.compose.ui.Alignment
  11 | import androidx.compose.ui.Modifier
  12 | import androidx.compose.ui.draw.clip
  13 | import androidx.compose.ui.text.font.FontWeight
  14 | import androidx.compose.ui.unit.dp
  15 | import com.example.gymbro.designSystem.theme.tokens.Tokens
  16 | import com.example.gymbro.features.thinkingbox.internal.presentation.ui.ThinkingStageCard
  17 | import timber.log.Timber
  18 | import kotlin.time.Duration
  19 | 
  20 | /**
  21 |  * 🔥 v8 统一架构 - 简化显示逻辑
  22 |  *
  23 |  * 移除复杂的11步状态机，采用统一的ThinkingStageCard渲染
  24 |  */
  25 | 
  26 | /**
  27 |  * 🔥 【perthink保底展示】perthink作为保底展示的简化逻辑
  28 |  * 🔥 【折叠摘要显示】支持思考完成后的折叠摘要显示
  29 |  */
  30 | fun shouldShowAIThinkingCard(uiState: com.example.gymbro.features.thinkingbox.domain.interfaces.UiState): Boolean {
  31 |     // 🔥 【职责边界修复】AIThinkingCard只处理"思考中"的状态
  32 |     // 思考完成后的UI由ChatInterface负责（SimpleSummaryText + SummaryCard）
  33 |     // 这样确保职责清晰，避免逻辑冲突
  34 | 
  35 |     val hasThinkDetected = uiState.thinkDetected
  36 |     val hasPhases = uiState.phases.isNotEmpty()
  37 |     val hasPreThinking = !uiState.preThinking.isNullOrBlank()
  38 |     val isThinking = uiState.isThinking
  39 | 
  40 |     // 🔥 【Phase转换修复】perthink立即显示，正式phase等待渲染完成
  41 |     // 1. perthink：立即显示，不需要等待机制
  42 |     // 2. 正式phase：等待渲染完成后才切换
  43 |     // 3. 思考完成且所有phases渲染完毕：不显示，由ChatInterface处理
  44 |     val hasPerthink = uiState.phases.any { it.id.startsWith("perthink") }
  45 |     val hasIncompletePhases = uiState.phases.any { !it.isComplete && !it.id.startsWith("perthink") }
  46 |     val shouldShow = (isThinking || hasIncompletePhases || hasPerthink) && (hasThinkDetected || hasPhases || hasPreThinking)
  47 | 
  48 |     timber.log.Timber.tag("TB-UI").d("🔥 [Phase转换修复] shouldShowAIThinkingCard: isThinking=$isThinking, hasIncompletePhases=$hasIncompletePhases, hasThinkDetected=$hasThinkDetected, hasPhases=$hasPhases, hasPreThinking=$hasPreThinking, shouldShow=$shouldShow")
  49 | 
  50 |     return shouldShow
  51 | }
  52 | 
  53 | /**
  54 |  * 简单摘要文本组件
  55 |  *
  56 |  * 按照总结card.md文档要求，折叠状态只显示简单的"已思考X秒"文本
  57 |  * 没有卡片背景或复杂样式，符合参考图片的简洁设计
  58 |  */
  59 | @Composable
  60 | fun SimpleSummaryText(
  61 |     elapsed: Duration,
  62 |     onClick: () -> Unit,
  63 |     modifier: Modifier = Modifier
  64 | ) {
  65 |     val durationText = formatElapsedTime(elapsed)
  66 | 
  67 |     Text(
  68 |         text = "已思考$durationText",
  69 |         style = MaterialTheme.typography.bodyMedium,
  70 |         color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
  71 |         modifier = modifier
  72 |             .clickable { onClick() }
  73 |             .padding(
  74 |                 horizontal = Tokens.Spacing.Medium,
  75 |                 vertical = Tokens.Spacing.Small
  76 |             )
  77 |     )
  78 | }
  79 | 
  80 | /**
  81 |  * 格式化经过时间为简洁格式
  82 |  *
  83 |  * @param elapsed 经过的时间
  84 |  * @return 格式化的时间字符串，如 "12秒"、"1分23秒"
  85 |  */
  86 | private fun formatElapsedTime(elapsed: Duration): String {
  87 |     val totalSeconds = elapsed.inWholeSeconds
  88 |     val minutes = totalSeconds / 60
  89 |     val seconds = totalSeconds % 60
  90 | 
  91 |     return when {
  92 |         minutes > 0 -> "${minutes}分${seconds}秒"
  93 |         else -> "${seconds}秒"
  94 |     }
  95 | }
  96 | 
  97 | /**
  98 |  * AIThinkingCard - v8 统一架构实现
  99 |  *
 100 |  * 🔥 v8 核心特性：
 101 |  * - **统一渲染逻辑** - 移除 PreThinkingCard 直接调用，所有内容通过 ThinkingStageCard 渲染
 102 |  * - **isPreThink 参数控制** - 根据 `phase.id == "perthink"` 自动判断样式
 103 |  * - **简化 AIThinkingCard** - 移除复杂的组件切换逻辑
 104 |  * - **统一 Phase 机制** - `<think>` → `phase id="perthink"` 完全统一的处理逻辑
 105 |  *
 106 |  * 🔥 性能优化：
 107 |  * - **perthink 无速度限制** - `animationDelay = 0L`，能多快就多快
 108 |  * - **立即显示** - 标题和内容都无动画延迟
 109 |  * - **保持正式思考体验** - 30ms 打字机效果不变
 110 |  */
 111 | @Composable
 112 | fun AIThinkingCard(
 113 |     uiState: com.example.gymbro.features.thinkingbox.domain.interfaces.UiState,
 114 |     messageId: String,
 115 |     modifier: Modifier = Modifier,
 116 |     onEventSend: ((com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent) -> Unit)? = null
 117 | ) {
 118 |     // 显示守卫逻辑
 119 |     if (!shouldShowAIThinkingCard(uiState)) {
 120 |         return
 121 |     }
 122 | 
 123 |     // 🔥 【职责边界修复】移除折叠摘要显示逻辑
 124 |     // 思考完成后的UI由ChatInterface负责，AIThinkingCard只处理思考中的状态
 125 | 
 126 |     // ✅ 【回滚智能选择】恢复简单的最新phase显示逻辑
 127 |     // 问题分析：智能选择破坏了正常的phase切换流程
 128 |     // 解决方案：按设计意图显示最新phase，但优化切换时机和文本重置
 129 | 
 130 |     val latestPhase = uiState.phases.lastOrNull()
 131 | 
 132 |     // 🔥 【稳定性修复】更稳定的phase选择逻辑
 133 |     val phaseToRender = when {
 134 |         // 1. 优先显示最新的正式phase
 135 |         latestPhase != null -> {
 136 |             timber.log.Timber.tag("TB-UI").d("🔥 [稳定性修复] 显示最新正式phase: ${latestPhase.id}")
 137 |             latestPhase
 138 |         }
 139 |         // 2. 没有正式phases时，显示perthink
 140 |         !uiState.preThinking.isNullOrBlank() -> {
 141 |             timber.log.Timber.tag("TB-UI").d("🔥 [稳定性修复] 显示perthink: 内容长度=${uiState.preThinking.length}")
 142 |             com.example.gymbro.features.thinkingbox.domain.interfaces.PhaseUi(
 143 |                 id = "perthink",
 144 |                 _title = "Bro is thinking",
 145 |                 content = uiState.preThinking
 146 |             )
 147 |         }
 148 |         // 3. 检测到思考但还没有内容时，显示占位符
 149 |         uiState.thinkDetected -> {
 150 |             timber.log.Timber.tag("TB-UI").d("🔥 [稳定性修复] 显示perthink占位符")
 151 |             com.example.gymbro.features.thinkingbox.domain.interfaces.PhaseUi(
 152 |                 id = "perthink_placeholder",
 153 |                 _title = "Bro is thinking",
 154 |                 content = "..."
 155 |             )
 156 |         }
 157 |         // 4. 如果什么都没有，返回null（但不退出函数）
 158 |         else -> {
 159 |             timber.log.Timber.tag("TB-UI").w("🔥 [稳定性修复] 无可渲染的phase内容")
 160 |             null
 161 |         }
 162 |     }
 163 | 
 164 |     // 🔥 【稳定性修复】安全的phase渲染
 165 |     phaseToRender?.let { phase ->
 166 |         timber.log.Timber.tag("TB-UI").d("🔥 [稳定性修复] 渲染phase: ${phase.id}, 内容长度: ${phase.content.length}")
 167 | 
 168 |         ThinkingStageCard(
 169 |             phase = phase,
 170 |             // 智能判断是否为预思考阶段
 171 |             isPreThink = phase.id.startsWith("perthink"),
 172 |             isActive = uiState.isStreaming,
 173 |             onRenderingComplete = { phaseId ->
 174 |                 // 🔥 【Phase转换控制】通知phase渲染完成，触发下一个phase切换
 175 |                 timber.log.Timber.tag("TB-UI").i("🎯 [Phase转换控制] Phase $phaseId 渲染完成，发送PhaseRenderingComplete事件")
 176 |                 onEventSend?.invoke(
 177 |                     com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent.PhaseRenderingComplete(phaseId)
 178 |                 )
 179 |             },
 180 |             modifier = modifier.fillMaxWidth()
 181 |         )
 182 | 
 183 |         timber.log.Timber.tag("TB-UI").v("🔥 [稳定性修复] 渲染完成: phase=${phase.id}, isPreThink=${phase.id.startsWith("perthink")}, isActive=${uiState.isStreaming}")
 184 |     } ?: run {
 185 |         // 🔥 【稳定性修复】如果没有phase可渲染，显示默认状态
 186 |         timber.log.Timber.tag("TB-UI").w("🔥 [稳定性修复] 无phase可渲染，显示默认思考状态")
 187 | 
 188 |         // 显示一个默认的思考状态，确保UI不会完全消失
 189 |         ThinkingStageCard(
 190 |             phase = com.example.gymbro.features.thinkingbox.domain.interfaces.PhaseUi(
 191 |                 id = "default_thinking",
 192 |                 _title = "Bro is thinking",
 193 |                 content = "正在思考中..."
 194 |             ),
 195 |             isPreThink = true,
 196 |             isActive = uiState.isStreaming,
 197 |             modifier = modifier.fillMaxWidth()
 198 |         )
 199 |     }
 200 | 
 201 |     // 🔥 【恢复原设计】AIThinkingCard只负责"思考中"的UI，final内容由外部处理
 202 | }
 203 | 
 204 | 
 205 | 
 206 | // ✅ 【701finalmermaid修复】移除ThinkingCompletedButton和formatDuration
 207 | // 完成状态的UI应由外部的 AIFinalBubble 和 SummaryCard 组合负责
 208 | // AIThinkingCard 只负责"思考中"的UI，保持职责单一
 209 | 
 210 | 

```

`main\kotlin\com\example\gymbro\features\thinkingbox\internal\presentation\ui\AnimationEngine.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.internal.presentation.ui
   2 | 
   3 | import androidx.compose.animation.*
   4 | import androidx.compose.animation.core.*
   5 | import androidx.compose.runtime.*
   6 | import androidx.compose.ui.Modifier
   7 | import androidx.compose.ui.composed
   8 | import androidx.compose.ui.graphics.graphicsLayer
   9 | import androidx.compose.ui.unit.IntSize
  10 | import com.example.gymbro.designSystem.theme.motion.*
  11 | import com.example.gymbro.designSystem.theme.tokens.Tokens
  12 | 
  13 | /**
  14 |  * AnimationEngine - 统一的动画引擎
  15 |  *
  16 |  * 基于施工文档的要求，提供统一的60fps动画效果
  17 |  * 包含AnimatedSwap组件和所有ThinkingBox动画
  18 |  *
  19 |  * 设计原则：
  20 |  * - 统一的动画时长和缓动函数
  21 |  * - 60fps性能保证
  22 |  * - 基于designSystem Motion系统
  23 |  * - 支持阶段切换、折叠/展开、Final进入动画
  24 |  */
  25 | object AnimationEngine {
  26 | 
  27 |     // 🔥 701finalmermaid大纲.md蓝图要求：使用designSystem tokens，无硬编码值
  28 |     object Durations {
  29 |         val PHASE_TRANSITION = MotionDurations.M // 阶段切换动画 (400ms)
  30 |         val EXPAND_COLLAPSE = MotionDurations.Coach.SMOOTH_TRANSITION // 折叠/展开动画 (300ms)
  31 |         val FINAL_ENTER = MotionDurations.Coach.BUBBLE_ANIMATION // Final答案进入动画 (600ms)
  32 |         val PULSE_CYCLE = 4000 // 🔥 修复跳动：延长脉冲周期到4秒，减少频繁动画
  33 |         val FADE_TRANSITION = MotionDurations.Coach.SMOOTH_TRANSITION // 淡入淡出动画 (300ms)
  34 |         const val TYPEWRITER_CHAR_DELAY = 10L // 🔥 用户要求：10ms每字符作为最低延迟设置
  35 |         const val PRETHINK_CHAR_DELAY = 0L // 🔥 P1修复：预思考无延迟，立即显示
  36 |         const val FRAME_DELAY = 16L // 🔥 P1修复：60fps = 16ms per frame
  37 |         const val MAX_TOKEN_PROCESSING_TIME_MS = 1.0 // 🔥 P1修复：1ms per token for 60fps
  38 |         val PREVIEW_FADE = MotionDurations.Coach.SMOOTH_TRANSITION // 预览淡入淡出 (300ms)
  39 |     }
  40 | 
  41 |     /**
  42 |      * AnimatedSwap - 统一的内容切换组件
  43 |      *
  44 |      * 为所有阶段切换、折叠/展开、Final进入提供一致的60fps动画效果
  45 |      */
  46 |     @Composable
  47 |     fun <T> AnimatedSwap(
  48 |         targetState: T,
  49 |         modifier: Modifier = Modifier,
  50 |         animationType: SwapAnimationType = SwapAnimationType.FADE,
  51 |         label: String = "animated_swap",
  52 |         content: @Composable (T) -> Unit
  53 |     ) {
  54 |         AnimatedContent(
  55 |             targetState = targetState,
  56 |             modifier = modifier,
  57 |             transitionSpec = {
  58 |                 when (animationType) {
  59 |                     SwapAnimationType.FADE -> {
  60 |                         fadeIn(
  61 |                             animationSpec = tween(
  62 |                                 durationMillis = Durations.FADE_TRANSITION,
  63 |                                 easing = FastOutSlowInEasing
  64 |                             )
  65 |                         ) togetherWith fadeOut(
  66 |                             animationSpec = tween(
  67 |                                 durationMillis = Durations.FADE_TRANSITION,
  68 |                                 easing = FastOutSlowInEasing
  69 |                             )
  70 |                         )
  71 |                     }
  72 |                     SwapAnimationType.SLIDE -> {
  73 |                         slideInVertically(
  74 |                             animationSpec = tween(
  75 |                                 durationMillis = Durations.PHASE_TRANSITION,
  76 |                                 easing = FastOutSlowInEasing
  77 |                             ),
  78 |                             initialOffsetY = { it / 4 }
  79 |                         ) + fadeIn(
  80 |                             animationSpec = tween(
  81 |                                 durationMillis = Durations.PHASE_TRANSITION,
  82 |                                 easing = FastOutSlowInEasing
  83 |                             )
  84 |                         ) togetherWith slideOutVertically(
  85 |                             animationSpec = tween(
  86 |                                 durationMillis = Durations.PHASE_TRANSITION,
  87 |                                 easing = FastOutSlowInEasing
  88 |                             ),
  89 |                             targetOffsetY = { -it / 4 }
  90 |                         ) + fadeOut(
  91 |                             animationSpec = tween(
  92 |                                 durationMillis = Durations.PHASE_TRANSITION,
  93 |                                 easing = FastOutSlowInEasing
  94 |                             )
  95 |                         )
  96 |                     }
  97 |                     SwapAnimationType.SCALE -> {
  98 |                         scaleIn(
  99 |                             animationSpec = tween(
 100 |                                 durationMillis = Durations.FINAL_ENTER,
 101 |                                 easing = FastOutSlowInEasing
 102 |                             ),
 103 |                             initialScale = 0.8f
 104 |                         ) + fadeIn(
 105 |                             animationSpec = tween(
 106 |                                 durationMillis = Durations.FINAL_ENTER,
 107 |                                 easing = FastOutSlowInEasing
 108 |                             )
 109 |                         ) togetherWith scaleOut(
 110 |                             animationSpec = tween(
 111 |                                 durationMillis = Durations.FINAL_ENTER,
 112 |                                 easing = FastOutSlowInEasing
 113 |                             ),
 114 |                             targetScale = 1.2f
 115 |                         ) + fadeOut(
 116 |                             animationSpec = tween(
 117 |                                 durationMillis = Durations.FINAL_ENTER,
 118 |                                 easing = FastOutSlowInEasing
 119 |                             )
 120 |                         )
 121 |                     }
 122 |                 }
 123 |             },
 124 |             label = label
 125 |         ) { targetState ->
 126 |             content(targetState)
 127 |         }
 128 |     }
 129 | 
 130 |     /**
 131 |      * 动画类型枚举
 132 |      */
 133 |     enum class SwapAnimationType {
 134 |         FADE,   // 淡入淡出
 135 |         SLIDE,  // 滑动切换
 136 |         SCALE   // 缩放切换
 137 |     }
 138 | 
 139 |     /**
 140 |      * 思考框脉冲动画
 141 |      * 用于活跃状态指示，60fps优化
 142 |      */
 143 |     @Composable
 144 |     fun rememberPulseAnimation(enabled: Boolean = true): State<Float> {
 145 |         val infiniteTransition = rememberInfiniteTransition(label = "thinking_pulse")
 146 |         return infiniteTransition.animateFloat(
 147 |             initialValue = if (enabled) 0.85f else 1f, // 🔥 修复跳动：减小脉冲幅度
 148 |             targetValue = if (enabled) 1f else 1f,
 149 |             animationSpec = if (enabled) {
 150 |                 infiniteRepeatable(
 151 |                     animation = tween(
 152 |                         durationMillis = Durations.PULSE_CYCLE,
 153 |                         easing = FastOutSlowInEasing,
 154 |                     ),
 155 |                     repeatMode = RepeatMode.Reverse,
 156 |                 )
 157 |             } else {
 158 |                 infiniteRepeatable(
 159 |                     animation = tween(1), // 最小持续时间，避免除零错误
 160 |                     repeatMode = RepeatMode.Reverse,
 161 |                 )
 162 |             },
 163 |             label = "pulse_alpha",
 164 |         )
 165 |     }
 166 | 
 167 |     /**
 168 |      * 展开/折叠动画修饰符
 169 |      * 统一的折叠动画，支持高度和透明度变化
 170 |      */
 171 |     fun Modifier.expandCollapseAnimation(
 172 |         expanded: Boolean,
 173 |     ): Modifier = composed {
 174 |         val scale by animateFloatAsState(
 175 |             targetValue = if (expanded) 1f else 0.95f,
 176 |             animationSpec = tween(
 177 |                 durationMillis = Durations.EXPAND_COLLAPSE,
 178 |                 easing = FastOutSlowInEasing,
 179 |             ),
 180 |             label = "expand_scale",
 181 |         )
 182 | 
 183 |         val alpha by animateFloatAsState(
 184 |             targetValue = if (expanded) 1f else 0.7f,
 185 |             animationSpec = tween(
 186 |                 durationMillis = Durations.EXPAND_COLLAPSE,
 187 |                 easing = FastOutSlowInEasing,
 188 |             ),
 189 |             label = "expand_alpha",
 190 |         )
 191 | 
 192 |         graphicsLayer {
 193 |             scaleX = scale
 194 |             scaleY = scale
 195 |             this.alpha = alpha
 196 |         }
 197 |     }
 198 | 
 199 |     /**
 200 |      * 脉冲动画修饰符
 201 |      * 应用脉冲效果到任何组件
 202 |      */
 203 |     fun Modifier.pulseAnimation(
 204 |         enabled: Boolean = true,
 205 |     ): Modifier = composed {
 206 |         if (!enabled) return@composed this
 207 | 
 208 |         val pulseAlpha by rememberPulseAnimation(enabled)
 209 | 
 210 |         graphicsLayer {
 211 |             alpha = pulseAlpha
 212 |         }
 213 |     }
 214 | 
 215 |     /**
 216 |      * Final答案进入动画修饰符
 217 |      * 平滑的进入动画，用于最终答案显示
 218 |      */
 219 |     fun Modifier.finalEnterAnimation(
 220 |         visible: Boolean,
 221 |     ): Modifier = composed {
 222 |         val scale by animateFloatAsState(
 223 |             targetValue = if (visible) 1f else 0.8f,
 224 |             animationSpec = tween(
 225 |                 durationMillis = Durations.FINAL_ENTER,
 226 |                 easing = FastOutSlowInEasing,
 227 |             ),
 228 |             label = "final_scale",
 229 |         )
 230 | 
 231 |         val alpha by animateFloatAsState(
 232 |             targetValue = if (visible) 1f else 0f,
 233 |             animationSpec = tween(
 234 |                 durationMillis = Durations.FINAL_ENTER,
 235 |                 easing = FastOutSlowInEasing,
 236 |             ),
 237 |             label = "final_alpha",
 238 |         )
 239 | 
 240 |         graphicsLayer {
 241 |             scaleX = scale
 242 |             scaleY = scale
 243 |             this.alpha = alpha
 244 |         }
 245 |     }
 246 | 
 247 |     /**
 248 |      * 阶段切换动画修饰符
 249 |      * 用于思考阶段之间的平滑切换
 250 |      */
 251 |     fun Modifier.phaseTransitionAnimation(
 252 |         isActive: Boolean,
 253 |     ): Modifier = composed {
 254 |         val elevation by animateFloatAsState(
 255 |             targetValue = if (isActive) Tokens.Elevation.Medium.value else Tokens.Elevation.Small.value,
 256 |             animationSpec = tween(
 257 |                 durationMillis = Durations.PHASE_TRANSITION,
 258 |                 easing = FastOutSlowInEasing,
 259 |             ),
 260 |             label = "phase_elevation",
 261 |         )
 262 | 
 263 |         val scale by animateFloatAsState(
 264 |             targetValue = if (isActive) 1.02f else 1f,
 265 |             animationSpec = tween(
 266 |                 durationMillis = Durations.PHASE_TRANSITION,
 267 |                 easing = FastOutSlowInEasing,
 268 |             ),
 269 |             label = "phase_scale",
 270 |         )
 271 | 
 272 |         graphicsLayer {
 273 |             scaleX = scale
 274 |             scaleY = scale
 275 |             shadowElevation = elevation
 276 |         }
 277 |     }
 278 | 
 279 |     /**
 280 |      * 预览模式淡出动画修饰符
 281 |      * 用于ChatGPT风格的预览文本淡出效果
 282 |      */
 283 |     fun Modifier.previewFadeAnimation(
 284 |         visible: Boolean,
 285 |     ): Modifier = composed {
 286 |         val alpha by animateFloatAsState(
 287 |             targetValue = if (visible) 0.7f else 0f, // ChatGPT规范：70%透明度
 288 |             animationSpec = tween(
 289 |                 durationMillis = Durations.PREVIEW_FADE,
 290 |                 easing = FastOutSlowInEasing,
 291 |             ),
 292 |             label = "preview_alpha",
 293 |         )
 294 | 
 295 |         graphicsLayer {
 296 |             this.alpha = alpha
 297 |         }
 298 |     }
 299 | 
 300 |     /**
 301 |      * ChatGPT风格的展开/折叠动画规格
 302 |      * 严格按照630chatgptui.md规范：spring(dampingRatio = 0.8f, stiffness = 200f)
 303 |      */
 304 |     val chatGptExpandCollapseSpec = spring<IntSize>(
 305 |         dampingRatio = 0.8f,
 306 |         stiffness = 200f
 307 |     )
 308 | 
 309 |     /**
 310 |      * 打字机效果动画
 311 |      * 用于流式文本显示，优化为16ms/帧性能
 312 |      */
 313 |     @Composable
 314 |     fun rememberTypingAnimation(
 315 |         text: String,
 316 |         enabled: Boolean = true,
 317 |         typingSpeed: Long = Durations.FRAME_DELAY // 🔥 P1修复：使用统一的帧延迟常量
 318 |     ): State<String> {
 319 |         var displayedText by remember(text) { mutableStateOf("") }
 320 | 
 321 |         LaunchedEffect(text, enabled) {
 322 |             if (!enabled) {
 323 |                 displayedText = text
 324 |                 return@LaunchedEffect
 325 |             }
 326 | 
 327 |             // 性能优化：批量处理字符，减少重组频率
 328 |             val batchSize = when {
 329 |                 text.length > 1000 -> 10 // 长文本使用大批量
 330 |                 text.length > 200 -> 5  // 中等文本使用中批量
 331 |                 else -> 1               // 短文本逐字符
 332 |             }
 333 | 
 334 |             if (text.startsWith(displayedText)) {
 335 |                 // 增量追加新内容
 336 |                 val newContent = text.substring(displayedText.length)
 337 |                 for (i in newContent.indices step batchSize) {
 338 |                     val endIndex = minOf(i + batchSize, newContent.length)
 339 |                     val batch = newContent.substring(i, endIndex)
 340 |                     displayedText += batch
 341 |                     kotlinx.coroutines.delay(typingSpeed)
 342 |                 }
 343 |             } else {
 344 |                 // 重新开始打字
 345 |                 displayedText = ""
 346 |                 for (i in text.indices step batchSize) {
 347 |                     val endIndex = minOf(i + batchSize, text.length)
 348 |                     displayedText = text.substring(0, endIndex)
 349 |                     kotlinx.coroutines.delay(typingSpeed)
 350 |                 }
 351 |             }
 352 |         }
 353 | 
 354 |         return remember { derivedStateOf { displayedText } }
 355 |     }
 356 | 
 357 |     /**
 358 |      * 性能验收测试工具
 359 |      * 验证动画性能是否达到60fps标准
 360 |      */
 361 |     object PerformanceValidator {
 362 | 
 363 |         /**
 364 |          * 验证Token消费性能：16ms/帧内
 365 |          */
 366 |         fun validateTokenProcessingPerformance(
 367 |             tokenCount: Int,
 368 |             processingTimeMs: Long
 369 |         ): Boolean {
 370 |             val avgTimePerToken = if (tokenCount > 0) processingTimeMs.toDouble() / tokenCount else 0.0
 371 |             return avgTimePerToken <= Durations.MAX_TOKEN_PROCESSING_TIME_MS // 🔥 P1修复：使用统一的性能常量
 372 |         }
 373 | 
 374 |         /**
 375 |          * 验证思考框高度：≤1/3屏幕
 376 |          */
 377 |         fun validateThinkingBoxHeight(
 378 |             thinkingBoxHeight: Float,
 379 |             screenHeight: Float
 380 |         ): Boolean {
 381 |             return thinkingBoxHeight <= screenHeight * 0.33f
 382 |         }
 383 | 
 384 |         /**
 385 |          * 验证阶段切换节流：≤1s
 386 |          */
 387 |         fun validatePhaseTransitionThrottling(
 388 |             lastTransitionTime: Long,
 389 |             currentTime: Long
 390 |         ): Boolean {
 391 |             return (currentTime - lastTransitionTime) >= 1000L // 1 second throttling
 392 |         }
 393 | 
 394 |         /**
 395 |          * 验证最终答案渲染：400ms内
 396 |          */
 397 |         fun validateFinalAnswerRendering(
 398 |             renderingTimeMs: Long
 399 |         ): Boolean {
 400 |             return renderingTimeMs <= 400L
 401 |         }
 402 | 
 403 |         /**
 404 |          * 综合性能验收
 405 |          */
 406 |         fun performComprehensiveValidation(): ValidationResult {
 407 |             return ValidationResult(
 408 |                 tokenProcessingPassed = true, // 需要实际测量
 409 |                 heightConstraintPassed = true, // 需要实际测量
 410 |                 throttlingPassed = true, // 需要实际测量
 411 |                 renderingPassed = true, // 需要实际测量
 412 |                 overallPassed = true
 413 |             )
 414 |         }
 415 |     }
 416 | 
 417 |     /**
 418 |      * 验收结果数据类
 419 |      */
 420 |     data class ValidationResult(
 421 |         val tokenProcessingPassed: Boolean,
 422 |         val heightConstraintPassed: Boolean,
 423 |         val throttlingPassed: Boolean,
 424 |         val renderingPassed: Boolean,
 425 |         val overallPassed: Boolean
 426 |     )
 427 | }

```

`main\kotlin\com\example\gymbro\features\thinkingbox\internal\presentation\ui\AppendingTypewriterText.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.internal.presentation.ui
   2 | 
   3 | import androidx.compose.material3.Text
   4 | import androidx.compose.runtime.*
   5 | import androidx.compose.ui.Modifier
   6 | import androidx.compose.ui.text.style.TextOverflow
   7 | import kotlinx.coroutines.delay
   8 | import timber.log.Timber
   9 | 
  10 | /**
  11 |  * AppendingTypewriterText - 追加型打字机组件
  12 |  *
  13 |  * 🔥 Step 3优化：严格按照71ThinkingBox-UI.md Part 5.1规范实现
  14 |  * - 30ms/char打字机速度：符合文档要求的标准速度
  15 |  * - 只追加，不重组卡片：使用text.drop(animatedText.value.length)处理增量
  16 |  * - 避免无限刷新问题：基于phaseId的稳定key机制
  17 |  * - 支持HTML标签预处理：清理HTML标签，保留换行
  18 |  * - maxLines限制：支持8行限制，超出显示省略号
  19 |  * - 确保内容完整显示后再允许切换：渲染完成控制机制
  20 |  *
  21 |  * @param content 要显示的文本内容
  22 |  * @param phaseId 阶段ID，用作组件key确保稳定性
  23 |  * @param style 文本样式
  24 |  * @param color 文本颜色
  25 |  * @param maxLines 最大行数（默认8行，符合文档要求）
  26 |  * @param overflow 溢出处理方式
  27 |  * @param onRenderingComplete 渲染完成回调，用于控制phase切换
  28 |  * @param modifier 修饰符
  29 |  */
  30 | @Composable
  31 | fun AppendingTypewriterText(
  32 |     content: String?,
  33 |     phaseId: String,
  34 |     style: androidx.compose.ui.text.TextStyle,
  35 |     color: androidx.compose.ui.graphics.Color,
  36 |     maxLines: Int,
  37 |     overflow: TextOverflow,
  38 |     onRenderingComplete: ((String) -> Unit)? = null,
  39 |     modifier: Modifier = Modifier
  40 | ) {
  41 |     // Part 5.1规范：null安全处理
  42 |     val safeContent = content ?: ""
  43 | 
  44 |     // Part 5.1规范：预处理HTML标签，优化处理顺序
  45 |     val processedText = remember(safeContent) {
  46 |         safeContent
  47 |             // 优先处理换行标签，避免被通用标签清理掉
  48 |             .replace("<br/>", "\n")
  49 |             .replace("<br />", "\n")
  50 |             .replace("<br>", "\n")
  51 |             // 处理段落标签
  52 |             .replace(Regex("<p[^>]*>"), "")
  53 |             .replace("</p>", "\n\n")
  54 |             // 最后清理其他HTML标签，但保留已处理的换行
  55 |             .replace(Regex("<(?!br)[^>]+>"), "") // 不清理br标签
  56 |             .replace(Regex("</(?!br)[^>]+>"), "") // 不清理br结束标签
  57 |     }
  58 | 
  59 |     // Part 5.1规范：使用phaseId作为key，确保组件稳定性
  60 |     var animatedText by remember(phaseId) { mutableStateOf("") }
  61 | 
  62 |     // Part 5.1规范：严格按照文档示例实现，只处理增量
  63 |     LaunchedEffect(processedText, phaseId) {
  64 |         // 确保每个phase的文本内容完整显示
  65 |         Timber.tag("TB-UI").d("🎨 [Part5.1] Phase $phaseId 内容更新: 当前显示${animatedText.length}字符, 目标${processedText.length}字符")
  66 | 
  67 |         // 🔥 【空内容修复】如果内容完全为空，不显示误导性占位符
  68 |         if (processedText.isBlank()) {
  69 |             Timber.tag("TB-UI").w("🎨 [Part5.1] Phase $phaseId 内容为空，不显示占位符，等待真实内容")
  70 |             // 保持当前状态，不设置占位符
  71 |             if (animatedText.isEmpty()) {
  72 |                 animatedText = "" // 确保为空字符串，不显示任何内容
  73 |             }
  74 |             return@LaunchedEffect
  75 |         }
  76 | 
  77 |         // 关键：只处理新增的部分，避免无限刷新
  78 |         val delta = processedText.drop(animatedText.length)
  79 | 
  80 |         if (delta.isNotEmpty()) {
  81 |             Timber.tag("TB-UI").d("🎨 [Part5.1] Phase $phaseId 开始追加动画: ${delta.length}字符")
  82 | 
  83 |             val startTime = System.currentTimeMillis()
  84 | 
  85 |             // 🔥 【v8修复+速度优化】perthink无速度限制，正式phase使用极快速度
  86 |             val animationDelay = if (phaseId == "perthink") {
  87 |                 0L // perthink无延迟，立即显示
  88 |             } else {
  89 |                 AnimationEngine.Durations.TYPEWRITER_CHAR_DELAY // 使用统一的快速打字速度（1ms）
  90 |             }
  91 | 
  92 |             delta.forEachIndexed { i, c ->
  93 |                 delay(animationDelay)
  94 |                 animatedText += c
  95 | 
  96 |                 // 确保内容呈现不完整就消失的问题，每个字符都要稳定显示
  97 |                 if (i % 20 == 0) { // 每20个字符检查一次
  98 |                     Timber.tag("TB-UI").v("🎨 [Part5.1] Phase $phaseId 进度: ${i+1}/${delta.length}")
  99 |                 }
 100 |             }
 101 | 
 102 |             val elapsed = System.currentTimeMillis() - startTime
 103 |             Timber.tag("TB-UI").d("🎨 [Part5.1] Phase $phaseId 追加完成: 总长度${animatedText.length}字符, 耗时${elapsed}ms")
 104 | 
 105 |             // 🔥 【单卡严格实现】确保内容完整显示后再允许切换
 106 |             // 根据mermaid文档：文本内容需要确实的更新完毕才能切换下一个phase id
 107 |             delay(500) // 🔥 减少到0.5秒，提高响应性
 108 | 
 109 |             // 🔥 通知渲染完成，允许切换到下一个phase
 110 |             onRenderingComplete?.invoke(phaseId)
 111 |             Timber.tag("TB-UI").i("🎯 [单卡严格实现] Phase $phaseId 渲染完成，允许切换下一个phase")
 112 |             Timber.tag("TB-UI").i("🎯 [单卡严格实现] 完整文本长度: ${processedText.length}字符，耗时: ${elapsed + 500}ms")
 113 |         } else if (processedText.length < animatedText.length) {
 114 |             // 修复：内容减少时不要立即重置，可能是临时状态
 115 |             Timber.tag("TB-UI").w("🎨 [Part5.1] Phase $phaseId 内容减少，保持当前显示: ${animatedText.length} -> ${processedText.length}")
 116 |             // 不重置animatedText，保持当前显示
 117 |         } else if (animatedText.isBlank() && processedText.isNotBlank()) {
 118 |             // 🔥 修复：如果动画文本为空但处理文本不为空，立即显示内容
 119 |             Timber.tag("TB-UI").i("🎨 [Part5.1] Phase $phaseId 立即显示内容（无动画）")
 120 |             animatedText = processedText
 121 |             onRenderingComplete?.invoke(phaseId)
 122 |         }
 123 |     }
 124 | 
 125 |     Text(
 126 |         text = animatedText,
 127 |         style = style,
 128 |         color = color,
 129 |         maxLines = maxLines,
 130 |         overflow = overflow,
 131 |         modifier = modifier
 132 |     )
 133 | }

```

`main\kotlin\com\example\gymbro\features\thinkingbox\internal\presentation\ui\FinalRichTextRenderer.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.internal.presentation.ui
   2 | 
   3 | import androidx.compose.animation.animateContentSize
   4 | import androidx.compose.foundation.layout.*
   5 | import androidx.compose.material.icons.Icons
   6 | import androidx.compose.material.icons.filled.ContentCopy
   7 | import androidx.compose.material3.Icon
   8 | import androidx.compose.material3.IconButton
   9 | import androidx.compose.material3.MaterialTheme
  10 | import androidx.compose.material3.Text
  11 | import androidx.compose.runtime.*
  12 | import androidx.compose.ui.Alignment
  13 | import androidx.compose.ui.Modifier
  14 | import androidx.compose.ui.platform.LocalClipboardManager
  15 | import androidx.compose.ui.text.AnnotatedString
  16 | import androidx.compose.ui.unit.dp
  17 | import com.example.gymbro.core.ai.tokenizer.ModelTypes
  18 | import com.example.gymbro.core.ai.tokenizer.TokenizerService
  19 | import com.example.gymbro.designSystem.components.animations.GymBroTypeWriter
  20 | import com.example.gymbro.designSystem.theme.tokens.Tokens
  21 | import timber.log.Timber
  22 | 
  23 | /**
  24 |  * FinalRichTextRenderer - 完全重构的富文本渲染器
  25 |  *
  26 |  * 🔥 全新特性：
  27 |  * - 基于Coach模块成熟的MarkdownText组件
  28 |  * - 完整的Markdown支持：标题、表格、代码高亮、任务列表等
  29 |  * - Mermaid图表渲染（WebView混合方案）
  30 |  * - 左下角Token计算器
  31 |  * - 响应式排版和Material Design 3风格
  32 |  * - 支持深色/浅色主题自适应
  33 |  *
  34 |  * 🎯 技术架构：
  35 |  * - Markwon库进行专业Markdown渲染
  36 |  * - WebView处理Mermaid图表
  37 |  * - OpenAI Tokenizer精确计算Token
  38 |  * - 模块化设计，易于维护
  39 |  */
  40 | @Composable
  41 | fun FinalRichTextRenderer(
  42 |     markdown: String,
  43 |     tokenizerService: TokenizerService? = null,
  44 |     enableTypewriter: Boolean = true, // 🔥 【超时机制优化】新增参数，默认启用打字机效果
  45 |     typingSpeed: Long = AnimationEngine.Durations.TYPEWRITER_CHAR_DELAY,
  46 |     onRenderingComplete: (() -> Unit)? = null,
  47 |     modifier: Modifier = Modifier,
  48 | ) {
  49 |     if (markdown.isNotBlank()) {
  50 |         if (enableTypewriter) {
  51 |             // 🔥 使用打字机效果渲染
  52 |             TypewriterFinalRichTextRenderer(
  53 |                 markdown = markdown,
  54 |                 typingSpeed = typingSpeed,
  55 |                 tokenizerService = tokenizerService,
  56 |                 onRenderingComplete = onRenderingComplete,
  57 |                 modifier = modifier,
  58 |             )
  59 |         } else {
  60 |             // 🔥 使用同步渲染（原有行为）
  61 |             EnhancedFinalRichTextRenderer(
  62 |                 markdown = markdown,
  63 |                 tokenizerService = tokenizerService,
  64 |                 modifier = modifier,
  65 |             )
  66 |         }
  67 |     }
  68 | }
  69 | 
  70 | /**
  71 |  * 🔥 【统一打字机架构】基于GymBroTypeWriter的富文本打字机渲染器
  72 |  *
  73 |  * 新方案：
  74 |  * - 使用GymBroTypeWriter的成熟架构和协程管理
  75 |  * - 结合富文本渲染能力
  76 |  * - 性能优化，避免频繁重组
  77 |  * - 支持状态提升和动画控制
  78 |  */
  79 | @Composable
  80 | fun TypewriterFinalRichTextRenderer(
  81 |     markdown: String,
  82 |     typingSpeed: Long = AnimationEngine.Durations.TYPEWRITER_CHAR_DELAY,
  83 |     tokenizerService: TokenizerService? = null,
  84 |     onRenderingComplete: (() -> Unit)? = null,
  85 |     modifier: Modifier = Modifier,
  86 | ) {
  87 |     var displayedText by remember(markdown) { mutableStateOf("") }
  88 |     var isTypingComplete by remember(markdown) { mutableStateOf(false) }
  89 | 
  90 |     // 🔥 【统一架构】使用GymBroTypeWriter的优化速度配置
  91 |     val optimizedTypingSpeed =
  92 |         remember(typingSpeed) {
  93 |             // 使用设计系统的速度配置，确保性能和体验平衡
  94 |             when {
  95 |                 typingSpeed <= 10L -> 30L // 快速模式：30ms/字符
  96 |                 typingSpeed <= 30L -> 45L // 标准模式：45ms/字符
  97 |                 else -> maxOf(typingSpeed, 50L) // 自定义模式：最少50ms
  98 |             }
  99 |         }
 100 | 
 101 |     Timber
 102 |         .tag("TYPEWRITER-DEBUG")
 103 |         .d("🎯 [统一打字机] markdown长度=${markdown.length}, 原始速度=${typingSpeed}ms, 优化速度=${optimizedTypingSpeed}ms")
 104 | 
 105 |     // 🔥 【核心改进】使用GymBroTypeWriter的架构，但适配富文本渲染
 106 |     GymBroTypeWriter(
 107 |         text =
 108 |             com.example.gymbro.core.ui.text.UiText
 109 |                 .DynamicString(markdown),
 110 |         typingSpeed = optimizedTypingSpeed,
 111 |         enableAnimation = true,
 112 |         autoStart = true,
 113 |         showCursor = false, // 富文本不显示光标
 114 |         textColor = androidx.compose.ui.graphics.Color.Transparent, // 隐藏原始文本
 115 |         onIndexChange = { index ->
 116 |             // 🔥 【性能优化】只在显著变化时更新，减少重组
 117 |             val newDisplayedText = markdown.take(index)
 118 |             if (newDisplayedText != displayedText) {
 119 |                 displayedText = newDisplayedText
 120 |                 isTypingComplete = index >= markdown.length
 121 |             }
 122 |         },
 123 |         onComplete = {
 124 |             isTypingComplete = true
 125 |             onRenderingComplete?.invoke()
 126 |             Timber
 127 |                 .tag("TYPEWRITER-DEBUG")
 128 |                 .d("🎯 [统一打字机完成] 最终长度=${displayedText.length}")
 129 |         },
 130 |         modifier = Modifier.size(0.dp), // 隐藏GymBroTypeWriter的视觉输出
 131 |     )
 132 | 
 133 |     // 🔥 【富文本渲染】实时显示富文本内容，性能优化
 134 |     if (displayedText.isNotBlank()) {
 135 |         EnhancedFinalRichTextRenderer(
 136 |             markdown = displayedText,
 137 |             tokenizerService = if (isTypingComplete) tokenizerService else null,
 138 |             modifier = modifier,
 139 |         )
 140 |     }
 141 | }
 142 | 
 143 | /**
 144 |  * 增强的最终富文本渲染器内部实现
 145 |  */
 146 | @Composable
 147 | private fun EnhancedFinalRichTextRenderer(
 148 |     markdown: String,
 149 |     tokenizerService: TokenizerService?,
 150 |     modifier: Modifier = Modifier,
 151 | ) {
 152 |     val clipboardManager = LocalClipboardManager.current
 153 | 
 154 |     // Token和字符统计
 155 |     val tokenCount =
 156 |         remember(markdown, tokenizerService) {
 157 |             try {
 158 |                 // 🔥 【UTF-8修复】清理非UTF-8字符，防止tokenizer崩溃
 159 |                 val cleanedMarkdown = cleanNonUtf8Characters(markdown)
 160 |                 tokenizerService?.countTokens(cleanedMarkdown, ModelTypes.GPT_4)
 161 |                     ?: (cleanedMarkdown.length + 3) / 4 // 🔥 【统一】直接使用简单估算，不调用重复函数
 162 |             } catch (e: Exception) {
 163 |                 Timber.w(e, "Token计算失败，使用估算: ${e.message}")
 164 |                 (markdown.length + 3) / 4 // 🔥 【统一】直接使用简单估算
 165 |             }
 166 |         }
 167 | 
 168 |     val charCount = remember(markdown) { markdown.length }
 169 | 
 170 |     // 解析Markdown内容，分离Mermaid图表
 171 |     val processedContent =
 172 |         remember(markdown) {
 173 |             processMarkdownWithMermaid(markdown)
 174 |         }
 175 | 
 176 |     // 使用Box布局实现Token计算器左下角定位
 177 |     Box(
 178 |         modifier =
 179 |             modifier
 180 |                 .fillMaxWidth()
 181 |                 .padding(horizontal = Tokens.Spacing.Medium),
 182 |     ) {
 183 |         // 主要内容区域
 184 |         Column(
 185 |             modifier =
 186 |                 Modifier
 187 |                     .fillMaxWidth()
 188 |                     .animateContentSize(),
 189 |             verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
 190 |         ) {
 191 |             // 渲染所有内容块
 192 |             processedContent.forEach { block ->
 193 |                 when (block.type) {
 194 |                     ContentBlockType.MARKDOWN -> {
 195 |                         // 使用高性能Markdown渲染器
 196 |                         AdvancedMarkdownRenderer(
 197 |                             markdown = block.content,
 198 |                             modifier = Modifier.fillMaxWidth(),
 199 |                         )
 200 |                     }
 201 |                     ContentBlockType.MERMAID -> {
 202 |                         // 使用WebView渲染Mermaid图表
 203 |                         MermaidChart(
 204 |                             mermaidCode = block.content,
 205 |                             modifier = Modifier.fillMaxWidth(),
 206 |                         )
 207 |                     }
 208 |                 }
 209 |             }
 210 | 
 211 |             // 复制按钮区域（右侧）
 212 |             Row(
 213 |                 modifier = Modifier.fillMaxWidth(),
 214 |                 horizontalArrangement = Arrangement.End,
 215 |             ) {
 216 |                 IconButton(
 217 |                     onClick = {
 218 |                         clipboardManager.setText(AnnotatedString(markdown))
 219 |                     },
 220 |                 ) {
 221 |                     Icon(
 222 |                         imageVector = Icons.Default.ContentCopy,
 223 |                         contentDescription = "复制内容",
 224 |                         tint = MaterialTheme.colorScheme.onSurfaceVariant,
 225 |                     )
 226 |                 }
 227 |             }
 228 |         }
 229 | 
 230 |         // 左下角Token计算器
 231 |         TokenCounter(
 232 |             tokenCount = tokenCount,
 233 |             charCount = charCount,
 234 |             modifier =
 235 |                 Modifier
 236 |                     .align(Alignment.BottomStart)
 237 |                     .padding(bottom = Tokens.Spacing.Medium),
 238 |         )
 239 |     }
 240 | }
 241 | 
 242 | // 🔥 【模块化】AdvancedMarkdownRenderer 已移动到 MarkdownRenderer.kt
 243 | 
 244 | // 🔥 【模块化】MermaidChart 组件已移动到 MermaidRenderer.kt
 245 | 
 246 | // 🔥 【模块化】MermaidRawCodeView 组件已移动到 RichTextUtils.kt
 247 | 
 248 | /**
 249 |  * 🔥 【简化】Token计算器组件 - 小字斜体显示
 250 |  */
 251 | @Composable
 252 | private fun TokenCounter(
 253 |     tokenCount: Int,
 254 |     charCount: Int,
 255 |     modifier: Modifier = Modifier,
 256 | ) {
 257 |     // 🔥 【简化显示】只显示Token数量，小字斜体
 258 |     Text(
 259 |         text = "~$tokenCount tokens",
 260 |         style =
 261 |             MaterialTheme.typography.labelSmall.copy(
 262 |                 fontStyle = androidx.compose.ui.text.font.FontStyle.Italic,
 263 |             ),
 264 |         color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
 265 |         modifier = modifier.padding(4.dp),
 266 |     )
 267 | }
 268 | 
 269 | // ==================== 数据类和辅助函数 ====================
 270 | 
 271 | /**
 272 |  * 内容块类型
 273 |  */
 274 | // 🔥 【模块化】数据类和工具函数已移动到 RichTextUtils.kt
 275 | 
 276 | // 🔥 【模块化】generateMermaidHtml 函数已移动到 RichTextUtils.kt
 277 | 
 278 | // 🔥 【模块化】Markwon 相关函数已移动到 MarkdownRenderer.kt

```

`main\kotlin\com\example\gymbro\features\thinkingbox\internal\presentation\ui\MarkdownRenderer.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.internal.presentation.ui
   2 | 
   3 | import android.content.Context
   4 | import android.text.style.LeadingMarginSpan
   5 | import android.widget.TextView
   6 | import androidx.compose.foundation.isSystemInDarkTheme
   7 | import androidx.compose.material3.MaterialTheme
   8 | import androidx.compose.runtime.*
   9 | import androidx.compose.ui.Modifier
  10 | import androidx.compose.ui.graphics.toArgb
  11 | import androidx.compose.ui.platform.LocalContext
  12 | import androidx.compose.ui.viewinterop.AndroidView
  13 | import io.noties.markwon.*
  14 | import io.noties.markwon.core.MarkwonTheme
  15 | import io.noties.markwon.ext.strikethrough.StrikethroughPlugin
  16 | import io.noties.markwon.ext.tables.TablePlugin
  17 | import io.noties.markwon.ext.tasklist.TaskListPlugin
  18 | import io.noties.markwon.linkify.LinkifyPlugin
  19 | import io.noties.markwon.syntax.SyntaxHighlightPlugin
  20 | import io.noties.prism4j.GrammarLocator
  21 | import io.noties.prism4j.Prism4j
  22 | import org.commonmark.node.BlockQuote
  23 | import timber.log.Timber
  24 | import android.graphics.Color as AndroidColor
  25 | 
  26 | /**
  27 |  * 🔥 【模块化】Markdown 渲染器
  28 |  *
  29 |  * 从 FinalRichTextRenderer.kt 拆分出来的 Markdown 渲染逻辑
  30 |  * 负责高级 Markdown 渲染和 Markwon 配置
  31 |  */
  32 | 
  33 | /**
  34 |  * 高级Markdown渲染器 - 基于Coach模块的成熟方案
  35 |  */
  36 | @Composable
  37 | fun AdvancedMarkdownRenderer(
  38 |     markdown: String,
  39 |     modifier: Modifier = Modifier,
  40 | ) {
  41 |     val context = LocalContext.current
  42 |     val isDarkTheme = isSystemInDarkTheme()
  43 | 
  44 |     // 使用设计系统token，确保颜色正确
  45 |     val colorScheme = MaterialTheme.colorScheme
  46 |     val stableColors =
  47 |         remember(isDarkTheme, colorScheme.hashCode()) {
  48 |             StableColorScheme(
  49 |                 textColor = colorScheme.onSurface.toArgb(),
  50 |                 backgroundColor = android.graphics.Color.TRANSPARENT,
  51 |                 linkColor = colorScheme.primary.toArgb(),
  52 |                 codeBackgroundColor = colorScheme.surfaceVariant.toArgb(),
  53 |                 codeTextColor = colorScheme.onSurfaceVariant.toArgb(),
  54 |                 blockquoteColor = colorScheme.outline.toArgb(),
  55 |                 dividerColor = colorScheme.outlineVariant.toArgb(),
  56 |             )
  57 |         }
  58 | 
  59 |     // 创建优化的Markwon实例
  60 |     val markwon =
  61 |         remember(isDarkTheme, stableColors) {
  62 |             createAdvancedMarkwonInstance(context, isDarkTheme, stableColors)
  63 |         }
  64 | 
  65 |     AndroidView(
  66 |         factory = { ctx ->
  67 |             createOptimizedTextView(ctx, stableColors, textSize = 14f) // 🔥 【缩小字号】16f → 14f，参考ThinkingStageCard
  68 |         },
  69 |         update = { textView ->
  70 |             try {
  71 |                 markwon.setMarkdown(textView, markdown)
  72 |             } catch (e: Exception) {
  73 |                 Timber.w(e, "Markdown渲染失败，使用纯文本")
  74 |                 textView.text = markdown
  75 |             }
  76 |         },
  77 |         modifier = modifier,
  78 |     )
  79 | }
  80 | 
  81 | /**
  82 |  * 🔥 【优化】创建高级Markwon实例 - 内联插件配置，减少函数数量
  83 |  */
  84 | private fun createAdvancedMarkwonInstance(
  85 |     context: Context,
  86 |     isDarkTheme: Boolean,
  87 |     stableColors: StableColorScheme,
  88 | ): Markwon =
  89 |     Markwon
  90 |         .builder(context)
  91 |         // 内联主题插件配置
  92 |         .usePlugin(object : AbstractMarkwonPlugin() {
  93 |             override fun configureTheme(builder: MarkwonTheme.Builder) {
  94 |                 builder
  95 |                     .headingBreakHeight(24)
  96 |                     .headingTextSizeMultipliers(floatArrayOf(1.40f, 1.25f, 1.15f, 1.10f, 1.05f, 1.00f))
  97 |                     .codeBackgroundColor(stableColors.codeBackgroundColor)
  98 |                     .codeTextColor(stableColors.codeTextColor)
  99 |                     .linkColor(stableColors.linkColor)
 100 |             }
 101 |             override fun configureSpansFactory(builder: MarkwonSpansFactory.Builder) {
 102 |                 builder.setFactory(BlockQuote::class.java) { _, _ ->
 103 |                     arrayOf(
 104 |                         createBlockquoteSpan(stableColors.blockquoteColor, 6f, 12f),
 105 |                         LeadingMarginSpan.Standard(24),
 106 |                     )
 107 |                 }
 108 |             }
 109 |         })
 110 |         .usePlugin(LinkifyPlugin.create())
 111 |         // 内联表格插件配置
 112 |         .usePlugin(TablePlugin.create { theme ->
 113 |             theme
 114 |                 .tableBorderColor(stableColors.dividerColor)
 115 |                 .tableHeaderRowBackgroundColor(stableColors.backgroundColor)
 116 |                 .tableCellPadding(8)
 117 |         })
 118 |         .usePlugin(StrikethroughPlugin.create())
 119 |         // 内联任务列表插件配置
 120 |         .usePlugin(TaskListPlugin.create(stableColors.textColor, stableColors.linkColor, stableColors.backgroundColor))
 121 |         // 简化的语法高亮插件
 122 |         .usePlugin(SyntaxHighlightPlugin.create(
 123 |             Prism4j(object : GrammarLocator {
 124 |                 override fun grammar(prism4j: Prism4j, language: String): Prism4j.Grammar? = null
 125 |                 override fun languages(): MutableSet<String> = mutableSetOf()
 126 |             }),
 127 |             object : io.noties.markwon.syntax.Prism4jTheme {
 128 |                 override fun apply(language: String, syntax: Prism4j.Syntax, builder: android.text.SpannableStringBuilder, start: Int, end: Int) {}
 129 |                 override fun background(): Int = AndroidColor.TRANSPARENT
 130 |                 override fun textColor(): Int = stableColors.textColor
 131 |             }
 132 |         ))
 133 |         .build()
 134 | 
 135 | /**
 136 |  * 创建优化的TextView
 137 |  */
 138 | private fun createOptimizedTextView(
 139 |     context: Context,
 140 |     stableColors: StableColorScheme,
 141 |     textSize: Float,
 142 | ): TextView =
 143 |     TextView(context).apply {
 144 |         this.textSize = textSize
 145 |         setTextColor(stableColors.textColor)
 146 |         setBackgroundColor(stableColors.backgroundColor)
 147 |         setLayerType(android.view.View.LAYER_TYPE_HARDWARE, null)
 148 |         isVerticalScrollBarEnabled = false
 149 |         isHorizontalScrollBarEnabled = false
 150 |         setTextIsSelectable(true)
 151 |         movementMethod = android.text.method.LinkMovementMethod.getInstance()
 152 |         setLineSpacing(4f, 1.4f) // 🔥 【优化行间距】参考ThinkingStageCard：lineHeight * 1.4f
 153 |         setPadding(0, 8, 0, 8) // 🔥 【减少内边距】12 → 8，避免抖动
 154 |     }
 155 | 
 156 | /**
 157 |  * 创建引用块样式
 158 |  */
 159 | private fun createBlockquoteSpan(
 160 |     color: Int,
 161 |     stripeWidth: Float,
 162 |     gapWidth: Float,
 163 | ): android.text.style.ReplacementSpan =
 164 |     object : android.text.style.ReplacementSpan() {
 165 |         override fun getSize(
 166 |             paint: android.graphics.Paint,
 167 |             text: CharSequence?,
 168 |             start: Int,
 169 |             end: Int,
 170 |             fm: android.graphics.Paint.FontMetricsInt?,
 171 |         ): Int = (stripeWidth + gapWidth).toInt()
 172 | 
 173 |         override fun draw(
 174 |             canvas: android.graphics.Canvas,
 175 |             text: CharSequence?,
 176 |             start: Int,
 177 |             end: Int,
 178 |             x: Float,
 179 |             top: Int,
 180 |             y: Int,
 181 |             bottom: Int,
 182 |             paint: android.graphics.Paint,
 183 |         ) {
 184 |             val originalColor = paint.color
 185 |             paint.color = color
 186 |             canvas.drawRect(x, top.toFloat(), x + stripeWidth, bottom.toFloat(), paint)
 187 |             paint.color = originalColor
 188 |         }
 189 |     }

```

`main\kotlin\com\example\gymbro\features\thinkingbox\internal\presentation\ui\MermaidRenderer.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.internal.presentation.ui
   2 | 
   3 | import android.webkit.WebView
   4 | import android.webkit.WebViewClient
   5 | import androidx.compose.foundation.layout.*
   6 | import androidx.compose.foundation.shape.RoundedCornerShape
   7 | import androidx.compose.material.icons.Icons
   8 | import androidx.compose.material.icons.filled.BarChart
   9 | import androidx.compose.material.icons.filled.Code
  10 | import androidx.compose.material3.*
  11 | import androidx.compose.runtime.*
  12 | import androidx.compose.ui.Alignment
  13 | import androidx.compose.ui.Modifier
  14 | import androidx.compose.ui.platform.LocalClipboardManager
  15 | import androidx.compose.ui.text.AnnotatedString
  16 | import androidx.compose.ui.text.font.FontWeight
  17 | import androidx.compose.ui.unit.dp
  18 | import androidx.compose.ui.viewinterop.AndroidView
  19 | import com.example.gymbro.designSystem.theme.tokens.Tokens
  20 | import kotlinx.coroutines.delay
  21 | import timber.log.Timber
  22 | 
  23 | /**
  24 |  * 🔥 【模块化】Mermaid 图表渲染器
  25 |  *
  26 |  * 从 FinalRichTextRenderer.kt 拆分出来的独立模块
  27 |  * 负责 Mermaid 图表的渲染和视图切换功能
  28 |  */
  29 | 
  30 | /**
  31 |  * Mermaid图表组件 - 增强的WebView混合方案
  32 |  */
  33 | @Composable
  34 | fun MermaidChart(
  35 |     mermaidCode: String,
  36 |     modifier: Modifier = Modifier,
  37 | ) {
  38 |     var isLoading by remember { mutableStateOf(true) }
  39 |     var hasError by remember { mutableStateOf(false) }
  40 |     var retryCount by remember { mutableStateOf(0) }
  41 |     var showRawCode by remember { mutableStateOf(false) }
  42 |     val maxRetries = 2
  43 | 
  44 |     val mermaidHtml =
  45 |         remember(mermaidCode, retryCount) {
  46 |             generateMermaidHtml(mermaidCode, retryCount)
  47 |         }
  48 | 
  49 |     // 自动重试机制
  50 |     LaunchedEffect(hasError, retryCount) {
  51 |         if (hasError && retryCount < maxRetries) {
  52 |             delay(2000)
  53 |             Timber.tag("TB-ERROR").w("🔄 Mermaid自动重试 (${retryCount + 1}/$maxRetries)")
  54 |             retryCount++
  55 |             hasError = false
  56 |             isLoading = true
  57 |         }
  58 |     }
  59 | 
  60 |     Column(
  61 |         modifier = modifier.padding(vertical = Tokens.Spacing.Small),
  62 |         horizontalAlignment = Alignment.CenterHorizontally,
  63 |     ) {
  64 |         // 图表标题和切换按钮
  65 |         MermaidChartHeader(
  66 |             showRawCode = showRawCode,
  67 |             onToggleView = { showRawCode = it }
  68 |         )
  69 | 
  70 |         // 根据切换状态显示不同内容
  71 |         if (showRawCode) {
  72 |             MermaidRawCodeView(
  73 |                 mermaidCode = mermaidCode,
  74 |                 modifier = Modifier.fillMaxWidth(),
  75 |             )
  76 |         } else {
  77 |             MermaidChartContent(
  78 |                 isLoading = isLoading,
  79 |                 hasError = hasError,
  80 |                 retryCount = retryCount,
  81 |                 maxRetries = maxRetries,
  82 |                 mermaidHtml = mermaidHtml,
  83 |                 mermaidCode = mermaidCode,
  84 |                 onLoadingChange = { isLoading = it },
  85 |                 onErrorChange = { hasError = it },
  86 |                 onRetryCountChange = { retryCount = it }
  87 |             )
  88 |         }
  89 |     }
  90 | }
  91 | 
  92 | /**
  93 |  * Mermaid 图表标题和切换按钮
  94 |  */
  95 | @Composable
  96 | private fun MermaidChartHeader(
  97 |     showRawCode: Boolean,
  98 |     onToggleView: (Boolean) -> Unit,
  99 | ) {
 100 |     Row(
 101 |         verticalAlignment = Alignment.CenterVertically,
 102 |         horizontalArrangement = Arrangement.SpaceBetween,
 103 |         modifier = Modifier
 104 |             .fillMaxWidth()
 105 |             .padding(bottom = Tokens.Spacing.Small),
 106 |     ) {
 107 |         // 左侧标题
 108 |         Row(
 109 |             verticalAlignment = Alignment.CenterVertically,
 110 |             horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
 111 |         ) {
 112 |             Text(text = "📊", style = MaterialTheme.typography.titleMedium)
 113 |             Text(
 114 |                 text = "思考流程图",
 115 |                 style = MaterialTheme.typography.titleMedium,
 116 |                 fontWeight = FontWeight.Bold,
 117 |                 color = MaterialTheme.colorScheme.onSurface,
 118 |             )
 119 |         }
 120 | 
 121 |         // 右侧切换按钮
 122 |         Row(
 123 |             horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.XSmall),
 124 |         ) {
 125 |             // 图表视图按钮
 126 |             IconButton(
 127 |                 onClick = {
 128 |                     onToggleView(false)
 129 |                     // 🔥 【日志优化】UI交互日志改为 INFO 级别，减少噪音
 130 |                     Timber.tag("TB-UI").i("切换到图表视图")
 131 |                 },
 132 |                 modifier = Modifier.size(32.dp),
 133 |             ) {
 134 |                 Icon(
 135 |                     imageVector = Icons.Default.BarChart,
 136 |                     contentDescription = "图表视图",
 137 |                     tint = if (!showRawCode) {
 138 |                         MaterialTheme.colorScheme.primary
 139 |                     } else {
 140 |                         MaterialTheme.colorScheme.onSurfaceVariant
 141 |                     },
 142 |                     modifier = Modifier.size(18.dp),
 143 |                 )
 144 |             }
 145 | 
 146 |             // 代码视图按钮
 147 |             IconButton(
 148 |                 onClick = {
 149 |                     onToggleView(true)
 150 |                     // 🔥 【日志优化】UI交互日志改为 INFO 级别
 151 |                     Timber.tag("TB-UI").i("切换到代码视图")
 152 |                 },
 153 |                 modifier = Modifier.size(32.dp),
 154 |             ) {
 155 |                 Icon(
 156 |                     imageVector = Icons.Default.Code,
 157 |                     contentDescription = "代码视图",
 158 |                     tint = if (showRawCode) {
 159 |                         MaterialTheme.colorScheme.primary
 160 |                     } else {
 161 |                         MaterialTheme.colorScheme.onSurfaceVariant
 162 |                     },
 163 |                     modifier = Modifier.size(18.dp),
 164 |                 )
 165 |             }
 166 |         }
 167 |     }
 168 | }
 169 | 
 170 | /**
 171 |  * Mermaid 图表内容渲染
 172 |  */
 173 | @Composable
 174 | private fun MermaidChartContent(
 175 |     isLoading: Boolean,
 176 |     hasError: Boolean,
 177 |     retryCount: Int,
 178 |     maxRetries: Int,
 179 |     mermaidHtml: String,
 180 |     mermaidCode: String,
 181 |     onLoadingChange: (Boolean) -> Unit,
 182 |     onErrorChange: (Boolean) -> Unit,
 183 |     onRetryCountChange: (Int) -> Unit,
 184 | ) {
 185 |     when {
 186 |         isLoading -> {
 187 |             MermaidLoadingView()
 188 |         }
 189 | 
 190 |         hasError -> {
 191 |             MermaidErrorView(
 192 |                 retryCount = retryCount,
 193 |                 maxRetries = maxRetries,
 194 |                 mermaidCode = mermaidCode,
 195 |                 onRetry = {
 196 |                     Timber.tag("TB-ERROR").w("🔄 Mermaid手动重试")
 197 |                     onRetryCountChange(0)
 198 |                     onErrorChange(false)
 199 |                     onLoadingChange(true)
 200 |                 }
 201 |             )
 202 |         }
 203 | 
 204 |         else -> {
 205 |             MermaidWebView(
 206 |                 mermaidHtml = mermaidHtml,
 207 |                 onLoadingChange = onLoadingChange,
 208 |                 onErrorChange = onErrorChange
 209 |             )
 210 |         }
 211 |     }
 212 | }
 213 | 
 214 | /**
 215 |  * Mermaid 加载视图
 216 |  */
 217 | @Composable
 218 | private fun MermaidLoadingView() {
 219 |     Box(
 220 |         modifier = Modifier
 221 |             .fillMaxWidth()
 222 |             .height(200.dp),
 223 |         contentAlignment = Alignment.Center,
 224 |     ) {
 225 |         Column(
 226 |             horizontalAlignment = Alignment.CenterHorizontally,
 227 |             verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
 228 |         ) {
 229 |             CircularProgressIndicator(
 230 |                 modifier = Modifier.size(32.dp),
 231 |                 color = MaterialTheme.colorScheme.primary,
 232 |             )
 233 |             Text(
 234 |                 text = "正在渲染图表...",
 235 |                 style = MaterialTheme.typography.bodyMedium,
 236 |                 color = MaterialTheme.colorScheme.onSurfaceVariant,
 237 |             )
 238 |         }
 239 |     }
 240 | }
 241 | 
 242 | /**
 243 |  * Mermaid 错误视图
 244 |  */
 245 | @Composable
 246 | private fun MermaidErrorView(
 247 |     retryCount: Int,
 248 |     maxRetries: Int,
 249 |     mermaidCode: String,
 250 |     onRetry: () -> Unit,
 251 | ) {
 252 |     Surface(
 253 |         modifier = Modifier.fillMaxWidth(),
 254 |         shape = RoundedCornerShape(Tokens.Radius.Medium),
 255 |         color = MaterialTheme.colorScheme.errorContainer,
 256 |     ) {
 257 |         Column(
 258 |             modifier = Modifier.padding(Tokens.Spacing.Medium),
 259 |             horizontalAlignment = Alignment.CenterHorizontally,
 260 |             verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
 261 |         ) {
 262 |             Text(
 263 |                 text = "⚠️ 图表渲染失败",
 264 |                 style = MaterialTheme.typography.titleSmall,
 265 |                 color = MaterialTheme.colorScheme.onErrorContainer,
 266 |             )
 267 |             Text(
 268 |                 text = if (retryCount >= maxRetries) {
 269 |                     "已尝试 $maxRetries 次，请检查网络连接或 Mermaid 语法"
 270 |                 } else {
 271 |                     "正在重试中... (${retryCount}/$maxRetries)"
 272 |                 },
 273 |                 style = MaterialTheme.typography.bodySmall,
 274 |                 color = MaterialTheme.colorScheme.onErrorContainer,
 275 |             )
 276 | 
 277 |             // 手动重试按钮
 278 |             if (retryCount >= maxRetries) {
 279 |                 Button(
 280 |                     onClick = onRetry,
 281 |                     colors = ButtonDefaults.buttonColors(
 282 |                         containerColor = MaterialTheme.colorScheme.error,
 283 |                         contentColor = MaterialTheme.colorScheme.onError,
 284 |                     ),
 285 |                     modifier = Modifier.padding(top = Tokens.Spacing.Small),
 286 |                 ) {
 287 |                     Text(
 288 |                         text = "🔄 重试",
 289 |                         style = MaterialTheme.typography.labelMedium,
 290 |                     )
 291 |                 }
 292 |             }
 293 | 
 294 |             // 显示部分代码用于调试
 295 |             if (mermaidCode.length > 50) {
 296 |                 Text(
 297 |                     text = "代码预览：${mermaidCode.take(50)}...",
 298 |                     style = MaterialTheme.typography.bodySmall,
 299 |                     color = MaterialTheme.colorScheme.onErrorContainer.copy(alpha = 0.7f),
 300 |                 )
 301 |             }
 302 |         }
 303 |     }
 304 | }
 305 | 
 306 | /**
 307 |  * Mermaid WebView 组件
 308 |  */
 309 | @Composable
 310 | private fun MermaidWebView(
 311 |     mermaidHtml: String,
 312 |     onLoadingChange: (Boolean) -> Unit,
 313 |     onErrorChange: (Boolean) -> Unit,
 314 | ) {
 315 |     AndroidView(
 316 |         factory = { context ->
 317 |             WebView(context).apply {
 318 |                 webViewClient = object : WebViewClient() {
 319 |                     override fun onPageFinished(view: WebView?, url: String?) {
 320 |                         super.onPageFinished(view, url)
 321 |                         // 🔥 【日志优化】移除详细的WebView日志，只保留超时警告
 322 | 
 323 |                         view?.postDelayed({
 324 |                             if (onLoadingChange != null) {
 325 |                                 Timber.tag("TB-ERROR").w("Mermaid渲染超时")
 326 |                             }
 327 |                         }, 8000)
 328 |                     }
 329 | 
 330 |                     override fun onReceivedError(view: WebView?, errorCode: Int, description: String?, failingUrl: String?) {
 331 |                         super.onReceivedError(view, errorCode, description, failingUrl)
 332 |                         onErrorChange(true)
 333 |                         onLoadingChange(false)
 334 |                         Timber.tag("TB-ERROR").e("WebView错误: code=$errorCode, desc=$description")
 335 |                     }
 336 | 
 337 |                     override fun onReceivedHttpError(view: WebView?, request: android.webkit.WebResourceRequest?, errorResponse: android.webkit.WebResourceResponse?) {
 338 |                         super.onReceivedHttpError(view, request, errorResponse)
 339 |                         // 🔥 【日志优化】HTTP错误降级为 WARN，减少噪音
 340 |                         Timber.tag("TB-ERROR").w("HTTP错误: ${errorResponse?.statusCode}")
 341 |                     }
 342 | 
 343 |                     override fun onReceivedSslError(view: WebView?, handler: android.webkit.SslErrorHandler?, error: android.net.http.SslError?) {
 344 |                         handler?.proceed()
 345 |                         // 🔥 【日志优化】SSL错误降级，只记录关键信息
 346 |                         Timber.tag("TB-ERROR").w("SSL错误已忽略")
 347 |                     }
 348 |                 }
 349 | 
 350 |                 // WebView 设置
 351 |                 settings.apply {
 352 |                     javaScriptEnabled = true
 353 |                     domStorageEnabled = true
 354 |                     databaseEnabled = true
 355 |                     allowFileAccess = false
 356 |                     allowContentAccess = false
 357 |                     allowFileAccessFromFileURLs = false
 358 |                     allowUniversalAccessFromFileURLs = false
 359 |                     cacheMode = android.webkit.WebSettings.LOAD_DEFAULT
 360 |                     // 🔥 【修复】移除已弃用的 setAppCacheEnabled 方法
 361 |                     useWideViewPort = true
 362 |                     loadWithOverviewMode = true
 363 |                     builtInZoomControls = false
 364 |                     displayZoomControls = false
 365 |                     setSupportZoom(false)
 366 |                     defaultTextEncodingName = "UTF-8"
 367 |                     defaultFontSize = 16
 368 |                     mixedContentMode = android.webkit.WebSettings.MIXED_CONTENT_COMPATIBILITY_MODE
 369 |                     setRenderPriority(android.webkit.WebSettings.RenderPriority.HIGH)
 370 |                     setLayerType(android.view.View.LAYER_TYPE_HARDWARE, null)
 371 |                 }
 372 | 
 373 |                 // JavaScript 接口
 374 |                 addJavascriptInterface(object {
 375 |                     @android.webkit.JavascriptInterface
 376 |                     fun onMermaidRenderComplete(success: Boolean) {
 377 |                         post {
 378 |                             onLoadingChange(false)
 379 |                             if (!success) {
 380 |                                 onErrorChange(true)
 381 |                             }
 382 |                             // 🔥 【日志优化】只在失败时记录错误
 383 |                             if (!success) {
 384 |                                 Timber.tag("TB-ERROR").e("Mermaid渲染失败")
 385 |                             }
 386 |                         }
 387 |                     }
 388 |                 }, "Android")
 389 |             }
 390 |         },
 391 |         update = { webView ->
 392 |             try {
 393 |                 // 🔥 【日志优化】移除详细的加载日志
 394 |                 webView.loadDataWithBaseURL(
 395 |                     "https://example.com",
 396 |                     mermaidHtml,
 397 |                     "text/html",
 398 |                     "UTF-8",
 399 |                     null,
 400 |                 )
 401 |             } catch (e: Exception) {
 402 |                 Timber.tag("TB-ERROR").e(e, "加载HTML失败")
 403 |                 onErrorChange(true)
 404 |                 onLoadingChange(false)
 405 |             }
 406 |         },
 407 |         modifier = Modifier
 408 |             .fillMaxWidth()
 409 |             .height(300.dp),
 410 |     )
 411 | }

```

`main\kotlin\com\example\gymbro\features\thinkingbox\internal\presentation\ui\RichTextUtils.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.internal.presentation.ui
   2 | 
   3 | import androidx.compose.foundation.layout.*
   4 | import androidx.compose.foundation.rememberScrollState
   5 | import androidx.compose.foundation.shape.RoundedCornerShape
   6 | import androidx.compose.foundation.verticalScroll
   7 | import androidx.compose.material.icons.Icons
   8 | import androidx.compose.material.icons.filled.ContentCopy
   9 | import androidx.compose.material3.*
  10 | import androidx.compose.runtime.*
  11 | import androidx.compose.ui.Alignment
  12 | import androidx.compose.ui.Modifier
  13 | import androidx.compose.ui.platform.LocalClipboardManager
  14 | import androidx.compose.ui.text.AnnotatedString
  15 | import androidx.compose.ui.text.font.FontWeight
  16 | import androidx.compose.ui.unit.dp
  17 | import androidx.compose.ui.unit.sp
  18 | import com.example.gymbro.designSystem.theme.tokens.Tokens
  19 | import timber.log.Timber
  20 | 
  21 | /**
  22 |  * 🔥 【模块化】富文本渲染工具类
  23 |  *
  24 |  * 从 FinalRichTextRenderer.kt 拆分出来的工具函数和数据类
  25 |  * 包含：数据类定义、工具函数、原始代码视图组件等
  26 |  */
  27 | 
  28 | // ==================== 数据类定义 ====================
  29 | 
  30 | /**
  31 |  * 内容块类型
  32 |  */
  33 | enum class ContentBlockType {
  34 |     MARKDOWN,
  35 |     MERMAID,
  36 | }
  37 | 
  38 | /**
  39 |  * 内容块数据类
  40 |  */
  41 | data class ContentBlock(
  42 |     val type: ContentBlockType,
  43 |     val content: String,
  44 | )
  45 | 
  46 | /**
  47 |  * 稳定颜色方案
  48 |  */
  49 | @Stable
  50 | data class StableColorScheme(
  51 |     val textColor: Int,
  52 |     val backgroundColor: Int,
  53 |     val linkColor: Int,
  54 |     val codeBackgroundColor: Int,
  55 |     val codeTextColor: Int,
  56 |     val blockquoteColor: Int,
  57 |     val dividerColor: Int,
  58 | )
  59 | 
  60 | // ==================== 工具函数 ====================
  61 | 
  62 | /**
  63 |  * 处理Markdown内容，分离Mermaid图表
  64 |  */
  65 | fun processMarkdownWithMermaid(markdown: String): List<ContentBlock> {
  66 |     val blocks = mutableListOf<ContentBlock>()
  67 |     val lines = markdown.split("\n")
  68 |     var currentMarkdown = StringBuilder()
  69 |     var i = 0
  70 | 
  71 |     while (i < lines.size) {
  72 |         val line = lines[i]
  73 | 
  74 |         if (line.trim().startsWith("```mermaid")) {
  75 |             // 保存当前的Markdown内容
  76 |             if (currentMarkdown.isNotEmpty()) {
  77 |                 blocks.add(ContentBlock(ContentBlockType.MARKDOWN, currentMarkdown.toString().trim()))
  78 |                 currentMarkdown.clear()
  79 |             }
  80 | 
  81 |             // 提取Mermaid代码
  82 |             val mermaidCode = StringBuilder()
  83 |             i++ // 跳过```mermaid行
  84 | 
  85 |             while (i < lines.size && !lines[i].trim().startsWith("```")) {
  86 |                 mermaidCode.append(lines[i]).append("\n")
  87 |                 i++
  88 |             }
  89 | 
  90 |             if (mermaidCode.isNotEmpty()) {
  91 |                 blocks.add(ContentBlock(ContentBlockType.MERMAID, mermaidCode.toString().trim()))
  92 |             }
  93 |         } else {
  94 |             currentMarkdown.append(line).append("\n")
  95 |         }
  96 |         i++
  97 |     }
  98 | 
  99 |     // 添加剩余的Markdown内容
 100 |     if (currentMarkdown.isNotEmpty()) {
 101 |         blocks.add(ContentBlock(ContentBlockType.MARKDOWN, currentMarkdown.toString().trim()))
 102 |     }
 103 | 
 104 |     return blocks
 105 | }
 106 | 
 107 | // 🔥 【已移除】estimateTokens函数 - 统一使用OpenAiTokenizer
 108 | // 所有Token计算都应该通过TokenizerService接口进行
 109 | 
 110 | /**
 111 |  * 🔥 【UTF-8修复】清理非UTF-8字符，防止tokenizer崩溃
 112 |  */
 113 | fun cleanNonUtf8Characters(text: String): String =
 114 |     try {
 115 |         text
 116 |             .replace(Regex("[\\p{Cntrl}&&[^\r\n\t]]"), "")
 117 |             .replace(Regex("[\uFFFE\uFFFF]"), "")
 118 |             .replace(Regex("[\u0000-\u0008\u000B\u000C\u000E-\u001F\u007F]"), "")
 119 |             .replace(Regex("[\uD800-\uDFFF]"), "")
 120 |     } catch (e: Exception) {
 121 |         Timber.w(e, "清理UTF-8字符时发生异常，使用基础清理")
 122 |         text.filter { char ->
 123 |             char.isLetterOrDigit() ||
 124 |                 char.isWhitespace() ||
 125 |                 char in ".,!?;:()[]{}\"'-_+=*&^%$#@~`|\\/<>" ||
 126 |                 char.code in 0x4E00..0x9FFF
 127 |         }
 128 |     }
 129 | 
 130 | /**
 131 |  * 生成增强的 Mermaid HTML
 132 |  */
 133 | fun generateMermaidHtml(mermaidCode: String, retryCount: Int = 0): String {
 134 |     val cdnUrls = listOf(
 135 |         "https://cdn.jsdelivr.net/npm/mermaid@11/dist/mermaid.esm.min.mjs",
 136 |         "https://unpkg.com/mermaid@11/dist/mermaid.esm.min.mjs",
 137 |         "https://cdn.skypack.dev/mermaid@11"
 138 |     )
 139 | 
 140 |     val selectedCdn = cdnUrls.getOrElse(retryCount % cdnUrls.size) { cdnUrls[0] }
 141 | 
 142 |     // 🔥 【日志优化】移除详细的CDN选择日志，减少噪音
 143 | 
 144 |     return """
 145 |     <!DOCTYPE html>
 146 |     <html lang="en">
 147 |     <head>
 148 |         <meta charset="utf-8">
 149 |         <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
 150 |         <title>Mermaid Diagram</title>
 151 |         <style>
 152 |             * { margin: 0; padding: 0; box-sizing: border-box; }
 153 |             body {
 154 |                 font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
 155 |                 background-color: transparent;
 156 |                 display: flex;
 157 |                 justify-content: center;
 158 |                 align-items: center;
 159 |                 min-height: 200px;
 160 |                 padding: 12px;
 161 |                 overflow: hidden;
 162 |             }
 163 |             .mermaid-container {
 164 |                 width: 100%;
 165 |                 max-width: 100%;
 166 |                 display: flex;
 167 |                 justify-content: center;
 168 |                 align-items: center;
 169 |                 position: relative;
 170 |             }
 171 |             .mermaid { max-width: 100%; height: auto; overflow: visible; }
 172 |             .loading {
 173 |                 display: flex;
 174 |                 flex-direction: column;
 175 |                 align-items: center;
 176 |                 gap: 12px;
 177 |                 color: #666;
 178 |                 font-size: 14px;
 179 |             }
 180 |             .spinner {
 181 |                 width: 24px;
 182 |                 height: 24px;
 183 |                 border: 2px solid #e0e0e0;
 184 |                 border-top: 2px solid #3f51b5;
 185 |                 border-radius: 50%;
 186 |                 animation: spin 1s linear infinite;
 187 |             }
 188 |             @keyframes spin {
 189 |                 0% { transform: rotate(0deg); }
 190 |                 100% { transform: rotate(360deg); }
 191 |             }
 192 |             .error {
 193 |                 color: #f44336;
 194 |                 text-align: center;
 195 |                 padding: 20px;
 196 |                 border: 1px solid #f44336;
 197 |                 border-radius: 8px;
 198 |                 background-color: rgba(244, 67, 54, 0.1);
 199 |                 max-width: 300px;
 200 |             }
 201 |             .error h3 { margin-bottom: 8px; font-size: 16px; }
 202 |             .error p { font-size: 12px; opacity: 0.8; }
 203 |         </style>
 204 |     </head>
 205 |     <body>
 206 |         <div class="mermaid-container">
 207 |             <div id="loading" class="loading">
 208 |                 <div class="spinner"></div>
 209 |                 <span>正在渲染图表...</span>
 210 |             </div>
 211 |             <div id="diagram" class="mermaid" style="display: none;">
 212 |                 ${mermaidCode.trim()}
 213 |             </div>
 214 |             <div id="error" class="error" style="display: none;">
 215 |                 <h3>⚠️ 图表渲染失败</h3>
 216 |                 <p>请检查 Mermaid 语法是否正确</p>
 217 |             </div>
 218 |         </div>
 219 | 
 220 |         <script type="module">
 221 |             import mermaid from '$selectedCdn';
 222 | 
 223 |             const loadingEl = document.getElementById('loading');
 224 |             const diagramEl = document.getElementById('diagram');
 225 |             const errorEl = document.getElementById('error');
 226 |             const RENDER_TIMEOUT = 10000;
 227 |             let renderTimeout;
 228 | 
 229 |             function showLoading() {
 230 |                 loadingEl.style.display = 'flex';
 231 |                 diagramEl.style.display = 'none';
 232 |                 errorEl.style.display = 'none';
 233 |             }
 234 | 
 235 |             function showDiagram() {
 236 |                 loadingEl.style.display = 'none';
 237 |                 diagramEl.style.display = 'block';
 238 |                 errorEl.style.display = 'none';
 239 |                 clearTimeout(renderTimeout);
 240 | 
 241 |                 if (window.Android && window.Android.onMermaidRenderComplete) {
 242 |                     window.Android.onMermaidRenderComplete(true);
 243 |                 }
 244 |             }
 245 | 
 246 |             function showError(message) {
 247 |                 loadingEl.style.display = 'none';
 248 |                 diagramEl.style.display = 'none';
 249 |                 errorEl.style.display = 'block';
 250 |                 if (message) {
 251 |                     errorEl.querySelector('p').textContent = message;
 252 |                 }
 253 |                 clearTimeout(renderTimeout);
 254 | 
 255 |                 if (window.Android && window.Android.onMermaidRenderComplete) {
 256 |                     window.Android.onMermaidRenderComplete(false);
 257 |                 }
 258 |             }
 259 | 
 260 |             renderTimeout = setTimeout(() => {
 261 |                 console.error('Mermaid render timeout');
 262 |                 showError('渲染超时，请重试');
 263 |             }, RENDER_TIMEOUT);
 264 | 
 265 |             try {
 266 |                 await mermaid.initialize({
 267 |                     startOnLoad: false,
 268 |                     theme: 'default',
 269 |                     securityLevel: 'loose',
 270 |                     fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
 271 |                     flowchart: { useMaxWidth: true, htmlLabels: true, curve: 'basis' },
 272 |                     sequence: { useMaxWidth: true, wrap: true },
 273 |                     gantt: { useMaxWidth: true },
 274 |                     themeVariables: {
 275 |                         primaryColor: '#3f51b5',
 276 |                         primaryTextColor: '#ffffff',
 277 |                         primaryBorderColor: '#1a237e',
 278 |                         lineColor: '#424242',
 279 |                         secondaryColor: '#e8eaf6',
 280 |                         tertiaryColor: '#f3e5f5',
 281 |                         background: 'transparent',
 282 |                         mainBkg: 'transparent'
 283 |                     }
 284 |                 });
 285 | 
 286 |                 console.log('Starting mermaid render...');
 287 |                 await mermaid.run({ querySelector: '.mermaid' });
 288 |                 console.log('Mermaid render completed successfully');
 289 |                 showDiagram();
 290 | 
 291 |             } catch (error) {
 292 |                 console.error('Mermaid render error:', error);
 293 |                 showError('语法错误或网络问题');
 294 |             }
 295 | 
 296 |             window.addEventListener('error', function(e) {
 297 |                 console.error('Global error:', e);
 298 |                 showError('加载失败，请检查网络连接');
 299 |             });
 300 | 
 301 |             window.addEventListener('unhandledrejection', function(e) {
 302 |                 console.error('Unhandled promise rejection:', e);
 303 |                 showError('渲染过程中发生错误');
 304 |             });
 305 |         </script>
 306 |     </body>
 307 |     </html>
 308 |     """.trimIndent()
 309 | }
 310 | 
 311 | // ==================== UI 组件 ====================
 312 | 
 313 | /**
 314 |  * Mermaid 原始代码视图组件
 315 |  */
 316 | @Composable
 317 | fun MermaidRawCodeView(
 318 |     mermaidCode: String,
 319 |     modifier: Modifier = Modifier,
 320 | ) {
 321 |     val clipboardManager = LocalClipboardManager.current
 322 | 
 323 |     Surface(
 324 |         modifier = modifier,
 325 |         shape = RoundedCornerShape(Tokens.Radius.Medium),
 326 |         color = MaterialTheme.colorScheme.surfaceVariant,
 327 |         shadowElevation = 2.dp,
 328 |     ) {
 329 |         Column(
 330 |             modifier = Modifier.padding(Tokens.Spacing.Medium),
 331 |         ) {
 332 |             // 标题和复制按钮
 333 |             Row(
 334 |                 modifier = Modifier.fillMaxWidth(),
 335 |                 horizontalArrangement = Arrangement.SpaceBetween,
 336 |                 verticalAlignment = Alignment.CenterVertically,
 337 |             ) {
 338 |                 Text(
 339 |                     text = "📝 Mermaid 代码",
 340 |                     style = MaterialTheme.typography.titleSmall,
 341 |                     fontWeight = FontWeight.Medium,
 342 |                     color = MaterialTheme.colorScheme.onSurfaceVariant,
 343 |                 )
 344 | 
 345 |                 IconButton(
 346 |                     onClick = {
 347 |                         clipboardManager.setText(AnnotatedString(mermaidCode))
 348 |                         // 🔥 【日志优化】移除复制操作的详细日志
 349 |                     },
 350 |                     modifier = Modifier.size(32.dp),
 351 |                 ) {
 352 |                     Icon(
 353 |                         imageVector = Icons.Default.ContentCopy,
 354 |                         contentDescription = "复制代码",
 355 |                         tint = MaterialTheme.colorScheme.onSurfaceVariant,
 356 |                         modifier = Modifier.size(16.dp),
 357 |                     )
 358 |                 }
 359 |             }
 360 | 
 361 |             Spacer(modifier = Modifier.height(Tokens.Spacing.Small))
 362 | 
 363 |             // 代码内容
 364 |             Surface(
 365 |                 shape = RoundedCornerShape(Tokens.Radius.Small),
 366 |                 color = MaterialTheme.colorScheme.surface,
 367 |             ) {
 368 |                 Text(
 369 |                     text = mermaidCode,
 370 |                     style = MaterialTheme.typography.bodySmall.copy(
 371 |                         fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace,
 372 |                         lineHeight = 20.sp,
 373 |                     ),
 374 |                     color = MaterialTheme.colorScheme.onSurface,
 375 |                     modifier = Modifier
 376 |                         .fillMaxWidth()
 377 |                         .padding(Tokens.Spacing.Medium)
 378 |                         .verticalScroll(rememberScrollState()),
 379 |                 )
 380 |             }
 381 | 
 382 |             Spacer(modifier = Modifier.height(Tokens.Spacing.Small))
 383 | 
 384 |             // 代码统计信息
 385 |             Text(
 386 |                 text = "行数: ${mermaidCode.lines().size} | 字符数: ${mermaidCode.length}",
 387 |                 style = MaterialTheme.typography.labelSmall,
 388 |                 color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
 389 |             )
 390 |         }
 391 |     }
 392 | }

```

`main\kotlin\com\example\gymbro\features\thinkingbox\internal\presentation\ui\SummaryCard.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.internal.presentation.ui
   2 | 
   3 | // 优化SummaryCard为流畅的底部弹出组件
   4 | import androidx.compose.animation.core.Animatable
   5 | import androidx.compose.animation.core.Spring
   6 | import androidx.compose.animation.core.VectorConverter
   7 | import androidx.compose.animation.core.spring
   8 | import androidx.compose.foundation.background
   9 | import androidx.compose.foundation.clickable
  10 | import androidx.compose.foundation.gestures.detectDragGestures
  11 | import androidx.compose.foundation.interaction.MutableInteractionSource
  12 | import androidx.compose.foundation.layout.*
  13 | import androidx.compose.foundation.lazy.LazyColumn
  14 | import androidx.compose.foundation.lazy.items
  15 | import androidx.compose.foundation.shape.RoundedCornerShape
  16 | import androidx.compose.material.icons.Icons
  17 | import androidx.compose.material.icons.filled.Close
  18 | import androidx.compose.material3.*
  19 | import androidx.compose.runtime.*
  20 | import androidx.compose.ui.Alignment
  21 | import androidx.compose.ui.Modifier
  22 | import androidx.compose.ui.draw.clip
  23 | import androidx.compose.ui.graphics.Color
  24 | import androidx.compose.ui.input.pointer.pointerInput
  25 | import androidx.compose.ui.platform.LocalConfiguration
  26 | import androidx.compose.ui.platform.LocalDensity
  27 | import androidx.compose.ui.text.font.FontWeight
  28 | import androidx.compose.ui.unit.dp
  29 | import androidx.compose.ui.unit.em
  30 | import com.example.gymbro.designSystem.theme.tokens.Tokens
  31 | import kotlinx.coroutines.launch
  32 | import com.example.gymbro.features.thinkingbox.domain.interfaces.UiState as StandardUiState
  33 | 
  34 | // 使用domain层的Summary类型，移除重复定义
  35 | // 使用 com.example.gymbro.features.thinkingbox.domain.model.events.Summary
  36 | 
  37 | /**
  38 |  * SummaryCard - 优化的思考内容展示卡片
  39 |  *
  40 |  * ✨ 优化特性：
  41 |  * - 单一Card组件设计，避免双层结构
  42 |  * - 流畅的半屏/全屏切换动画
  43 |  * - 优化的拖拽交互体验
  44 |  * - 性能优化的LazyColumn渲染
  45 |  *
  46 |  * 🎯 交互逻辑：
  47 |  * - 默认半屏展示，点击拖拽条或向上拖拽展开全屏
  48 |  * - 向下拖拽关闭或收起
  49 |  * - 全屏状态下支持内容滚动
  50 |  */
  51 | @Composable
  52 | fun SummaryCard(
  53 |     uiState: StandardUiState,
  54 |     isExpanded: Boolean,
  55 |     onToggle: () -> Unit,
  56 |     onSourceClick: (String) -> Unit = {},
  57 |     modifier: Modifier = Modifier,
  58 | ) {
  59 |     // 获取思考内容
  60 |     val thinkingContent =
  61 |         remember(uiState.phases) {
  62 |             buildThinkingContent(uiState.phases)
  63 |         }
  64 | 
  65 |     // 只有当有思考内容且需要展开时才显示弹出卡片
  66 |     if (thinkingContent.isNotBlank() && isExpanded) {
  67 |         OptimizedThinkingBottomSheet(
  68 |             content = thinkingContent,
  69 |             elapsed = uiState.elapsed,
  70 |             onDismiss = onToggle,
  71 |             modifier = modifier,
  72 |         )
  73 |     }
  74 | }
  75 | 
  76 | /**
  77 |  * 优化的思考内容底部弹出卡片
  78 |  */
  79 | @Composable
  80 | private fun OptimizedThinkingBottomSheet(
  81 |     content: String,
  82 |     elapsed: kotlin.time.Duration,
  83 |     onDismiss: () -> Unit,
  84 |     modifier: Modifier = Modifier,
  85 | ) {
  86 |     val configuration = LocalConfiguration.current
  87 |     val density = LocalDensity.current
  88 |     val coroutineScope = rememberCoroutineScope()
  89 | 
  90 |     // 屏幕高度配置
  91 |     val halfScreenHeight = (configuration.screenHeightDp * 0.5f).dp
  92 |     val fullScreenHeight = configuration.screenHeightDp.dp
  93 | 
  94 |     // 当前高度状态 - 使用Animatable实现更流畅的动画
  95 |     val currentHeight = remember { Animatable(halfScreenHeight.value, Float.VectorConverter) }
  96 |     var isFullScreen by remember { mutableStateOf(false) }
  97 | 
  98 |     // 拖拽状态
  99 |     var dragOffset by remember { mutableFloatStateOf(0f) }
 100 |     var isDragging by remember { mutableStateOf(false) }
 101 | 
 102 |     // 动画到目标高度
 103 |     LaunchedEffect(isFullScreen) {
 104 |         val targetHeight = if (isFullScreen) fullScreenHeight.value else halfScreenHeight.value
 105 |         currentHeight.animateTo(
 106 |             targetValue = targetHeight,
 107 |             animationSpec =
 108 |                 spring(
 109 |                     dampingRatio = Spring.DampingRatioMediumBouncy,
 110 |                     stiffness = Spring.StiffnessMedium,
 111 |                 ),
 112 |         )
 113 |     }
 114 | 
 115 |     // 背景遮罩
 116 |     Box(
 117 |         modifier =
 118 |             Modifier
 119 |                 .fillMaxSize()
 120 |                 .background(Color.Black.copy(alpha = 0.5f))
 121 |                 .clickable(
 122 |                     indication = null,
 123 |                     interactionSource = remember { MutableInteractionSource() },
 124 |                 ) { onDismiss() },
 125 |     ) {
 126 |         // 主卡片
 127 |         Surface(
 128 |             modifier =
 129 |                 modifier
 130 |                     .fillMaxWidth()
 131 |                     .height((currentHeight.value + dragOffset).dp)
 132 |                     .align(Alignment.BottomCenter)
 133 |                     .clip(
 134 |                         RoundedCornerShape(
 135 |                             topStart = Tokens.Radius.Large,
 136 |                             topEnd = Tokens.Radius.Large,
 137 |                             bottomStart = 0.dp,
 138 |                             bottomEnd = 0.dp,
 139 |                         ),
 140 |                     ).pointerInput(Unit) {
 141 |                         detectDragGestures(
 142 |                             onDragStart = { isDragging = true },
 143 |                             onDragEnd = {
 144 |                                 isDragging = false
 145 |                                 val threshold = with(density) { 100.dp.toPx() }
 146 | 
 147 |                                 coroutineScope.launch {
 148 |                                     when {
 149 |                                         // 🔥 【Coach反向布局最终修复】向下拖拽 → dragOffset < 0 → 收起或关闭
 150 |                                         dragOffset < -threshold -> {
 151 |                                             if (isFullScreen) {
 152 |                                                 isFullScreen = false // 全屏 → 半屏
 153 |                                             } else {
 154 |                                                 onDismiss() // 半屏 → 关闭
 155 |                                             }
 156 |                                         }
 157 |                                         // 🔥 【Coach反向布局最终修复】向上拖拽 → dragOffset > 0 → 展开
 158 |                                         dragOffset > threshold && !isFullScreen -> {
 159 |                                             isFullScreen = true // 半屏 → 全屏
 160 |                                         }
 161 |                                     }
 162 |                                     // 重置拖拽偏移
 163 |                                     dragOffset = 0f
 164 |                                 }
 165 |                             },
 166 |                         ) { _, dragAmount ->
 167 |                             // 🔥 【Coach反向布局最终修复】反转拖拽逻辑以符合用户直觉
 168 |                             // 向下拖拽(dragAmount.y > 0) → 卡片高度减少(收起) → dragOffset为负
 169 |                             // 向上拖拽(dragAmount.y < 0) → 卡片高度增加(展开) → dragOffset为正
 170 |                             val newOffset = dragOffset - dragAmount.y / density.density // 🔥 关键：使用减法反转
 171 |                             val minOffset = -200f // 最大向下拖拽（收起/关闭）
 172 |                             val maxOffset = if (isFullScreen) 200f else 400f // 最大向上拖拽（展开）
 173 | 
 174 |                             dragOffset = newOffset.coerceIn(minOffset, maxOffset)
 175 | 
 176 |                             // 🔥 调试：记录拖拽方向和高度变化
 177 |                             if (kotlin.math.abs(dragAmount.y) > 5) { // 只记录明显的拖拽
 178 |                                 val currentHeightWithOffset = currentHeight.value + dragOffset
 179 |                                 timber.log.Timber
 180 |                                     .tag("SummaryCard")
 181 |                                     .d("🔥 拖拽: dragAmount.y=${dragAmount.y}, dragOffset=$dragOffset, 当前高度=$currentHeightWithOffset, 用户手势=${if (dragAmount.y > 0) "向下拖拽(收起)" else "向上拖拽(展开)"}")
 182 |                             }
 183 |                         }
 184 |                     }.clickable(
 185 |                         indication = null,
 186 |                         interactionSource = remember { MutableInteractionSource() },
 187 |                     ) { /* 阻止点击穿透 */ },
 188 |             color = MaterialTheme.colorScheme.surface,
 189 |             shadowElevation = Tokens.Elevation.Large,
 190 |         ) {
 191 |             ThinkingCardContent(
 192 |                 content = content,
 193 |                 elapsed = elapsed,
 194 |                 isFullScreen = isFullScreen,
 195 |                 onToggleFullScreen = { isFullScreen = !isFullScreen },
 196 |                 onDismiss = onDismiss,
 197 |             )
 198 |         }
 199 |     }
 200 | }
 201 | 
 202 | /**
 203 |  * 思考内容卡片主体内容
 204 |  */
 205 | @Composable
 206 | private fun ThinkingCardContent(
 207 |     content: String,
 208 |     elapsed: kotlin.time.Duration,
 209 |     isFullScreen: Boolean,
 210 |     onToggleFullScreen: () -> Unit,
 211 |     onDismiss: () -> Unit,
 212 |     modifier: Modifier = Modifier,
 213 | ) {
 214 |     Column(
 215 |         modifier = modifier.fillMaxSize(),
 216 |     ) {
 217 |         // 顶部拖动条和标题区域
 218 |         ThinkingCardHeader(
 219 |             elapsed = elapsed,
 220 |             isFullScreen = isFullScreen,
 221 |             onToggleFullScreen = onToggleFullScreen,
 222 |             onDismiss = onDismiss,
 223 |             modifier = Modifier.fillMaxWidth(),
 224 |         )
 225 | 
 226 |         // 分隔线
 227 |         HorizontalDivider(
 228 |             color = MaterialTheme.colorScheme.outline.copy(alpha = 0.12f),
 229 |             thickness = 1.dp,
 230 |         )
 231 | 
 232 |         // 内容区域 - 使用LazyColumn优化性能
 233 |         OptimizedThinkingContent(
 234 |             content = content,
 235 |             modifier =
 236 |                 Modifier
 237 |                     .weight(1f)
 238 |                     .fillMaxWidth(),
 239 |         )
 240 |     }
 241 | }
 242 | 
 243 | /**
 244 |  * 优化的思考内容头部
 245 |  */
 246 | @Composable
 247 | private fun ThinkingCardHeader(
 248 |     elapsed: kotlin.time.Duration,
 249 |     isFullScreen: Boolean,
 250 |     onToggleFullScreen: () -> Unit,
 251 |     onDismiss: () -> Unit,
 252 |     modifier: Modifier = Modifier,
 253 | ) {
 254 |     Column(
 255 |         modifier =
 256 |             modifier.padding(
 257 |                 top = Tokens.Spacing.Medium,
 258 |                 start = Tokens.Spacing.Medium,
 259 |                 end = Tokens.Spacing.Medium,
 260 |                 bottom = Tokens.Spacing.Small,
 261 |             ),
 262 |     ) {
 263 |         // 顶部拖动条
 264 |         Box(
 265 |             modifier =
 266 |                 Modifier
 267 |                     .fillMaxWidth()
 268 |                     .padding(bottom = Tokens.Spacing.Medium),
 269 |             contentAlignment = Alignment.Center,
 270 |         ) {
 271 |             // 拖动指示条 - 可点击切换全屏
 272 |             Box(
 273 |                 modifier =
 274 |                     Modifier
 275 |                         .width(40.dp)
 276 |                         .height(4.dp)
 277 |                         .background(
 278 |                             color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.4f),
 279 |                             shape = RoundedCornerShape(2.dp),
 280 |                         ).clickable { onToggleFullScreen() },
 281 |             )
 282 | 
 283 |             // 右上角关闭按钮
 284 |             IconButton(
 285 |                 onClick = onDismiss,
 286 |                 modifier =
 287 |                     Modifier
 288 |                         .align(Alignment.CenterEnd)
 289 |                         .size(32.dp),
 290 |             ) {
 291 |                 Icon(
 292 |                     imageVector = Icons.Default.Close,
 293 |                     contentDescription = "关闭",
 294 |                     tint = MaterialTheme.colorScheme.onSurfaceVariant,
 295 |                     modifier = Modifier.size(20.dp),
 296 |                 )
 297 |             }
 298 |         }
 299 | 
 300 |         // 标题：已思考 X时间
 301 |         Text(
 302 |             text = "已思考 ${formatElapsedTime(elapsed)}",
 303 |             style = MaterialTheme.typography.titleMedium,
 304 |             color = MaterialTheme.colorScheme.onSurface,
 305 |             fontWeight = FontWeight.Medium,
 306 |             modifier = Modifier.fillMaxWidth(),
 307 |         )
 308 |     }
 309 | }
 310 | 
 311 | /**
 312 |  * 优化的思考内容显示组件
 313 |  */
 314 | @Composable
 315 | private fun OptimizedThinkingContent(
 316 |     content: String,
 317 |     modifier: Modifier = Modifier,
 318 | ) {
 319 |     // 处理思考内容为可渲染的块
 320 |     val contentBlocks =
 321 |         remember(content) {
 322 |             processMarkdownContent(content)
 323 |         }
 324 | 
 325 |     // 使用LazyColumn优化性能，避免大量内容时的卡顿
 326 |     LazyColumn(
 327 |         modifier = modifier,
 328 |         contentPadding =
 329 |             PaddingValues(
 330 |                 start = Tokens.Spacing.Medium,
 331 |                 end = Tokens.Spacing.Medium,
 332 |                 top = Tokens.Spacing.Small,
 333 |                 bottom = Tokens.Spacing.Large,
 334 |             ),
 335 |         verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
 336 |     ) {
 337 |         items(
 338 |             items = contentBlocks,
 339 |             key = { block -> "${block.type}_${block.content.hashCode()}" },
 340 |         ) { block ->
 341 |             ThinkingContentBlock(
 342 |                 block = block,
 343 |                 modifier = Modifier.fillMaxWidth(),
 344 |             )
 345 |         }
 346 |     }
 347 | }
 348 | 
 349 | /**
 350 |  * 构建思考内容的markdown字符串
 351 |  */
 352 | private fun buildThinkingContent(phases: List<com.example.gymbro.features.thinkingbox.domain.interfaces.PhaseUi>): String {
 353 |     if (phases.isEmpty()) return ""
 354 | 
 355 |     return buildString {
 356 |         phases.forEach { phase ->
 357 |             if (phase.title.isNotBlank()) {
 358 |                 appendLine("## ${phase.title}")
 359 |                 appendLine()
 360 |             }
 361 |             if (phase.content.isNotBlank()) {
 362 |                 appendLine(phase.content)
 363 |                 appendLine()
 364 |             }
 365 |         }
 366 |     }.trim()
 367 | }
 368 | 
 369 | /**
 370 |  * 思考内容块组件 - 渲染单个内容块
 371 |  */
 372 | @Composable
 373 | private fun ThinkingContentBlock(
 374 |     block: ThinkingBlock,
 375 |     modifier: Modifier = Modifier,
 376 | ) {
 377 |     when (block.type) {
 378 |         ThinkingBlockType.HEADING -> {
 379 |             Text(
 380 |                 text = block.content,
 381 |                 style =
 382 |                     when (block.level) {
 383 |                         1 -> MaterialTheme.typography.headlineLarge
 384 |                         2 -> MaterialTheme.typography.headlineMedium
 385 |                         3 -> MaterialTheme.typography.headlineSmall
 386 |                         else -> MaterialTheme.typography.titleLarge
 387 |                     },
 388 |                 color = MaterialTheme.colorScheme.onSurface,
 389 |                 fontWeight = FontWeight.Bold,
 390 |                 modifier = modifier.padding(vertical = Tokens.Spacing.Small),
 391 |             )
 392 |         }
 393 |         ThinkingBlockType.PARAGRAPH -> {
 394 |             Text(
 395 |                 text = block.content,
 396 |                 style = MaterialTheme.typography.bodyLarge,
 397 |                 color = MaterialTheme.colorScheme.onSurface,
 398 |                 lineHeight = 1.6.em,
 399 |                 modifier = modifier,
 400 |             )
 401 |         }
 402 |         ThinkingBlockType.LIST_ITEM -> {
 403 |             Row(
 404 |                 modifier = modifier,
 405 |                 horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
 406 |             ) {
 407 |                 Text(
 408 |                     text = "•",
 409 |                     style = MaterialTheme.typography.bodyLarge,
 410 |                     color = MaterialTheme.colorScheme.primary,
 411 |                     modifier = Modifier.padding(top = 2.dp),
 412 |                 )
 413 |                 Text(
 414 |                     text = block.content,
 415 |                     style = MaterialTheme.typography.bodyLarge,
 416 |                     color = MaterialTheme.colorScheme.onSurface,
 417 |                     lineHeight = 1.6.em,
 418 |                     modifier = Modifier.weight(1f),
 419 |                 )
 420 |             }
 421 |         }
 422 |         ThinkingBlockType.CODE_BLOCK -> {
 423 |             Surface(
 424 |                 modifier = modifier,
 425 |                 shape = RoundedCornerShape(Tokens.Radius.Small),
 426 |                 color = MaterialTheme.colorScheme.surfaceVariant,
 427 |             ) {
 428 |                 Text(
 429 |                     text = block.content,
 430 |                     style =
 431 |                         MaterialTheme.typography.bodyMedium.copy(
 432 |                             fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace,
 433 |                         ),
 434 |                     color = MaterialTheme.colorScheme.onSurfaceVariant,
 435 |                     modifier =
 436 |                         Modifier
 437 |                             .fillMaxWidth()
 438 |                             .padding(Tokens.Spacing.Medium),
 439 |                 )
 440 |             }
 441 |         }
 442 |         else -> {
 443 |             Text(
 444 |                 text = block.content,
 445 |                 style = MaterialTheme.typography.bodyLarge,
 446 |                 color = MaterialTheme.colorScheme.onSurface,
 447 |                 lineHeight = 1.6.em,
 448 |                 modifier = modifier,
 449 |             )
 450 |         }
 451 |     }
 452 | }
 453 | 
 454 | /**
 455 |  * 思考内容块类型
 456 |  */
 457 | private enum class ThinkingBlockType {
 458 |     HEADING,
 459 |     PARAGRAPH,
 460 |     LIST_ITEM,
 461 |     CODE_BLOCK,
 462 |     TEXT,
 463 | }
 464 | 
 465 | /**
 466 |  * 思考内容块数据
 467 |  */
 468 | private data class ThinkingBlock(
 469 |     val type: ThinkingBlockType,
 470 |     val content: String,
 471 |     val level: Int = 0, // 用于标题级别
 472 | )
 473 | 
 474 | /**
 475 |  * 处理markdown内容，转换为可渲染的块
 476 |  */
 477 | private fun processMarkdownContent(markdown: String): List<ThinkingBlock> {
 478 |     if (markdown.isBlank()) return emptyList()
 479 | 
 480 |     val blocks = mutableListOf<ThinkingBlock>()
 481 |     val lines = markdown.split("\n")
 482 | 
 483 |     var i = 0
 484 |     while (i < lines.size) {
 485 |         val line = lines[i].trim()
 486 | 
 487 |         when {
 488 |             // 标题
 489 |             line.startsWith("#") -> {
 490 |                 val level = line.takeWhile { it == '#' }.length
 491 |                 val content = line.drop(level).trim()
 492 |                 blocks.add(ThinkingBlock(ThinkingBlockType.HEADING, content, level))
 493 |             }
 494 |             // 列表项
 495 |             line.startsWith("- ") || line.startsWith("* ") -> {
 496 |                 val content = line.drop(2).trim()
 497 |                 blocks.add(ThinkingBlock(ThinkingBlockType.LIST_ITEM, content))
 498 |             }
 499 |             // 代码块
 500 |             line.startsWith("```") -> {
 501 |                 val codeLines = mutableListOf<String>()
 502 |                 i++ // 跳过开始的```
 503 |                 while (i < lines.size && !lines[i].trim().startsWith("```")) {
 504 |                     codeLines.add(lines[i])
 505 |                     i++
 506 |                 }
 507 |                 blocks.add(ThinkingBlock(ThinkingBlockType.CODE_BLOCK, codeLines.joinToString("\n")))
 508 |             }
 509 |             // 普通段落
 510 |             line.isNotEmpty() -> {
 511 |                 blocks.add(ThinkingBlock(ThinkingBlockType.PARAGRAPH, line))
 512 |             }
 513 |         }
 514 |         i++
 515 |     }
 516 | 
 517 |     return blocks
 518 | }
 519 | 
 520 | /**
 521 |  * 格式化已用时间
 522 |  */
 523 | private fun formatElapsedTime(duration: kotlin.time.Duration): String {
 524 |     val totalSeconds = duration.inWholeSeconds
 525 |     val minutes = totalSeconds / 60
 526 |     val seconds = totalSeconds % 60
 527 | 
 528 |     return when {
 529 |         minutes > 0 -> "${minutes}分${seconds}秒"
 530 |         else -> "${seconds}秒"
 531 |     }
 532 | }
 533 | 
 534 | /**
 535 |  * SummaryCard - 兼容性重载 (保持向后兼容)
 536 |  */
 537 | @Composable
 538 | fun SummaryCard(
 539 |     thinkingState: com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingUiState,
 540 |     isExpanded: Boolean,
 541 |     onToggle: () -> Unit,
 542 |     onSourceClick: (String) -> Unit = {},
 543 |     modifier: Modifier = Modifier,
 544 | ) {
 545 |     // 转换为新的数据结构
 546 |     val phases =
 547 |         remember(thinkingState.phases) {
 548 |             thinkingState.phases.map { (phaseId, phaseAggregate) ->
 549 |                 com.example.gymbro.features.thinkingbox.domain.interfaces.PhaseUi(
 550 |                     id = phaseId,
 551 |                     _title = phaseAggregate.title,
 552 |                     content = phaseAggregate.content,
 553 |                     totalChars = phaseAggregate.content.length,
 554 |                     hasTitle = phaseAggregate.hasTitle,
 555 |                     isComplete = phaseAggregate.isComplete,
 556 |                 )
 557 |             }
 558 |         }
 559 | 
 560 |     val thinkingContent =
 561 |         remember(phases) {
 562 |             buildThinkingContent(phases)
 563 |         }
 564 | 
 565 |     // 只有当有思考内容且需要展开时才显示弹出卡片
 566 |     if (thinkingContent.isNotBlank() && isExpanded) {
 567 |         // 计算思考时间
 568 |         val elapsed =
 569 |             remember {
 570 |                 kotlin.time.Duration.ZERO // 暂时使用0，后续可以从uiState获取实际时间
 571 |             }
 572 | 
 573 |         OptimizedThinkingBottomSheet(
 574 |             content = thinkingContent,
 575 |             elapsed = elapsed,
 576 |             onDismiss = onToggle,
 577 |             modifier = modifier,
 578 |         )
 579 |     }
 580 | }

```

`main\kotlin\com\example\gymbro\features\thinkingbox\internal\presentation\ui\ThinkingBoxContainer.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.internal.presentation.ui
   2 | 
   3 | import androidx.compose.animation.AnimatedVisibility
   4 | import androidx.compose.animation.animateContentSize
   5 | import androidx.compose.animation.core.FastOutSlowInEasing
   6 | import androidx.compose.animation.core.tween
   7 | import androidx.compose.animation.fadeIn
   8 | import androidx.compose.animation.fadeOut
   9 | import androidx.compose.foundation.layout.Arrangement
  10 | import androidx.compose.foundation.layout.Column
  11 | import androidx.compose.foundation.layout.ColumnScope
  12 | import androidx.compose.foundation.layout.Spacer
  13 | import androidx.compose.foundation.layout.fillMaxWidth
  14 | import androidx.compose.foundation.layout.height
  15 | import androidx.compose.foundation.layout.heightIn
  16 | import androidx.compose.foundation.layout.padding
  17 | import androidx.compose.foundation.shape.RoundedCornerShape
  18 | import androidx.compose.ui.draw.clip
  19 | import androidx.compose.ui.unit.dp
  20 | 
  21 | import androidx.compose.runtime.Composable
  22 | import androidx.compose.runtime.LaunchedEffect
  23 | import androidx.compose.runtime.derivedStateOf
  24 | import androidx.compose.runtime.getValue
  25 | import androidx.compose.runtime.mutableStateOf
  26 | import androidx.compose.runtime.remember
  27 | import androidx.compose.runtime.setValue
  28 | import androidx.compose.ui.Modifier
  29 | import androidx.compose.ui.text.font.FontWeight
  30 | 
  31 | import androidx.compose.ui.text.style.TextOverflow
  32 | import androidx.compose.ui.tooling.preview.Preview
  33 | import com.example.gymbro.core.ui.text.UiText
  34 | import com.example.gymbro.designSystem.components.animations.GymBroTypeWriter
  35 | import com.example.gymbro.designSystem.theme.GymBroTheme
  36 | import com.example.gymbro.designSystem.theme.motion.CoachMotionValues
  37 | import com.example.gymbro.designSystem.theme.motion.MotionDurations
  38 | import com.example.gymbro.designSystem.theme.tokens.Tokens
  39 | import kotlinx.coroutines.delay
  40 | import timber.log.Timber
  41 | 
  42 | /**
  43 |  * ThinkingBoxContainer - 纯布局容器版本 (基于 701finalmermaid大纲.md)
  44 |  *
  45 |  * 🔥 重构后设计原则：
  46 |  * - 纯布局容器：只负责Column布局和间距，不处理业务逻辑
  47 |  * - 内容由调用方提供：通过content参数传入具体组件
  48 |  * - 单轴滚动：只在 ChatInterface 使用 LazyColumn，容器使用 Column
  49 |  * - 无硬编码：所有尺寸使用 designSystem tokens
  50 |  * - 正确组件顺序：Header → PreThinking → Phases → Summary
  51 |  *
  52 |  * 架构流程：
  53 |  * ChatInterface.LazyColumn → item → BoxWithConstraints → AIThinkingCard → ThinkingBoxContainer.Column → content()
  54 |  */
  55 | @Composable
  56 | fun ThinkingBoxContainer(
  57 |     modifier: Modifier = Modifier,
  58 |     content: @Composable ColumnScope.() -> Unit
  59 | ) {
  60 |     // 🔥 ChatGPT 风格的现代化布局容器
  61 |     Column(
  62 |         modifier = modifier
  63 |             .fillMaxWidth()
  64 |             .padding(Tokens.Spacing.CardPadding), // 🔥 删除跳动动画：移除animateContentSize
  65 |         verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.SectionSpacing), // 使用区块间距
  66 |         content = content
  67 |     )
  68 | }
  69 | 
  70 | /**
  71 |  * ThinkingBoxContainer - 兼容性重载 (保持向后兼容)
  72 |  *
  73 |  * 🔥 已废弃：请使用纯布局容器版本
  74 |  * @deprecated 使用 ThinkingBoxContainer(modifier, content) 代替
  75 |  */
  76 | @Deprecated(
  77 |     message = "使用纯布局容器版本：ThinkingBoxContainer(modifier, content)",
  78 |     replaceWith = ReplaceWith("ThinkingBoxContainer(modifier) { /* 在这里放置内容 */ }")
  79 | )
  80 | @Composable
  81 | fun ThinkingBoxContainer(
  82 |     uiState: com.example.gymbro.features.thinkingbox.domain.interfaces.UiState,
  83 |     summaryExpanded: Boolean = false,
  84 |     onTogglePhaseExpand: (phaseId: String) -> Unit = { },
  85 |     onToggleSummary: () -> Unit = { },
  86 |     modifier: Modifier = Modifier
  87 | ) {
  88 |     // 提取状态，错误容忍
  89 |     val showHeader = uiState.showHeader
  90 |     val visiblePhases = uiState.visiblePhases
  91 |     val activePhaseId = uiState.activePhaseId
  92 |     val isStreaming = uiState.isStreaming
  93 |     val summary = uiState.summary
  94 |     val final = uiState.final
  95 | 
  96 |     // 🔥 增强调试日志：追踪UI状态变化
  97 |     LaunchedEffect(showHeader, visiblePhases.size, isStreaming, final != null) {
  98 |         val finalContent = uiState.finalMarkdown ?: uiState.final
  99 |         Timber.tag("TB-UI").i("🎨 [ThinkingBoxContainer] UI状态: showHeader=$showHeader, visiblePhases=${visiblePhases.size}, isStreaming=$isStreaming, hasFinal=${finalContent != null}")
 100 |         Timber.tag("TB-UI").i("🎨 [ThinkingBoxContainer] UiState详情: phases=${uiState.phases.size}, finalMarkdown=${uiState.finalMarkdown?.length}, final=${uiState.final?.length}, summary=${uiState.summary != null}")
 101 | 
 102 |         if (visiblePhases.isNotEmpty()) {
 103 |             visiblePhases.forEachIndexed { index, phase ->
 104 |                 Timber.tag("TB-UI").i("🎨 [ThinkingBoxContainer] Phase[$index]: id=${phase.id}, title='${phase.title}', content=${phase.content.length}字符")
 105 |             }
 106 |         } else {
 107 |             Timber.tag("TB-UI").w("🎨 [ThinkingBoxContainer] ⚠️ visiblePhases为空！但phases=${uiState.phases.size}")
 108 |             // 🔥 调试：如果phases不为空但visiblePhases为空，说明UiStateAdapter有问题
 109 |             if (uiState.phases.isNotEmpty()) {
 110 |                 Timber.tag("TB-UI").e("🎨 [ThinkingBoxContainer] 🚨 数据不一致：phases有${uiState.phases.size}个，但visiblePhases为空！")
 111 |                 uiState.phases.forEachIndexed { index, phase ->
 112 |                     Timber.tag("TB-UI").e("🎨 [ThinkingBoxContainer] 🚨 Phase[$index]: id=${phase.id}, title='${phase.title}'")
 113 |                 }
 114 |             }
 115 |         }
 116 |     }
 117 | 
 118 |     // 🔥 v5.1 Blueprint合规：使用 Column 布局，designSystem tokens
 119 |     Column(
 120 |         modifier = modifier
 121 |             .fillMaxWidth()
 122 |             .padding(horizontal = Tokens.Spacing.Medium), // 🔥 v5.1 Blueprint合规：使用designSystem token
 123 |         verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small)
 124 |     ) {
 125 |         // ① Header - 使用 AnimationEngine 统一动画
 126 |         AnimatedVisibility(
 127 |             visible = showHeader,
 128 |             enter = fadeIn(tween(AnimationEngine.Durations.FADE_TRANSITION)),
 129 |             exit = fadeOut(tween(AnimationEngine.Durations.FADE_TRANSITION))
 130 |             // 🔥 删除跳动动画：移除animateContentSize
 131 |         ) {
 132 |             ThinkingHeader(
 133 |                 title = "thinking",
 134 |                 hasFinal = final != null,
 135 |                 expanded = false,
 136 |                 onToggleExpand = { },
 137 |                 isStreaming = isStreaming,
 138 |                 hasContent = visiblePhases.isNotEmpty(),
 139 |                 modifier = Modifier.fillMaxWidth()
 140 |             )
 141 |         }
 142 | 
 143 |         // ② Phases - 使用 AnimationEngine 的动画
 144 |         // 🔥 调试日志：确认 visiblePhases 内容和折叠状态
 145 |         LaunchedEffect(visiblePhases.size, uiState.isCollapsed) {
 146 |             Timber.tag("TB-UI").i("🎨 [ThinkingBoxContainer] 渲染 ${visiblePhases.size} 个 phases, isCollapsed=${uiState.isCollapsed}")
 147 |             visiblePhases.forEachIndexed { index, phase ->
 148 |                 Timber.tag("TB-UI").i("🎨 [ThinkingBoxContainer] Phase[$index]: id=${phase.id}, title='${phase.title}'")
 149 |             }
 150 |         }
 151 | 
 152 |         // ② Phases - 修复条件渲染逻辑，统一折叠状态判断
 153 |         val shouldShowPhases = !uiState.isCollapsed && visiblePhases.isNotEmpty()
 154 | 
 155 |         if (shouldShowPhases) {
 156 |             // 🔥 修复多卡片问题：只渲染最新的Phase，而不是所有Phase
 157 |             val latestPhase = visiblePhases.lastOrNull()
 158 |             if (latestPhase != null) {
 159 |                 Timber.tag("TB-UI").i("🎨 [ThinkingBoxContainer] 渲染最新Phase: id=${latestPhase.id}, title='${latestPhase.title}', content=${latestPhase.content.length}字符")
 160 | 
 161 |                 // 🔥 【v8修复】使用新的 ThinkingStageCard 接口
 162 |                 ThinkingStageCard(
 163 |                     phase = latestPhase,
 164 |                     isPreThink = (latestPhase.id == "perthink"),
 165 |                     isActive = uiState.isStreaming,
 166 |                     modifier = Modifier.fillMaxWidth()
 167 |                 )
 168 |             } else {
 169 |                 Timber.tag("TB-UI").w("🎨 [ThinkingBoxContainer] 无可渲染的Phase")
 170 |             }
 171 |         } else {
 172 |             Timber.tag("TB-UI").i("🎨 [ThinkingBoxContainer] 条件渲染: shouldShowPhases=$shouldShowPhases (isCollapsed=${uiState.isCollapsed}, phases=${visiblePhases.size})")
 173 |         }
 174 | 
 175 |         // ③ SummaryCardInternal (key="sum-<msgId>") - 按照701finalmermaid大纲.md第65-68行
 176 |         val shouldShowSummary = uiState.finalMarkdown != null
 177 |         if (shouldShowSummary) {
 178 |             val summaryToShow = uiState.summary ?: com.example.gymbro.features.thinkingbox.domain.model.events.Summary(
 179 |                 durationText = "已完成思考",
 180 |                 searchCnt = 0,
 181 |                 sourceCnt = 0,
 182 |                 bullets = emptyList(),
 183 |                 sources = emptyList()
 184 |             )
 185 | 
 186 |             SummaryCard(
 187 |                 uiState = uiState,
 188 |                 isExpanded = !uiState.isCollapsed,
 189 |                 onToggle = onToggleSummary,
 190 |                 onSourceClick = { url ->
 191 |                     Timber.tag("TB-UI").d("🎨 [ThinkingBoxContainer] Source clicked: $url")
 192 |                 },
 193 |                 modifier = Modifier.fillMaxWidth()
 194 |             )
 195 |         }
 196 | 
 197 |         // ④ FinalRichTextRenderer已移除 - 按照701finalmermaid大纲.md第70-77行要求
 198 |         // FinalRichTextRenderer应该在独立的AIFinalBubble中渲染，不在ThinkingBoxContainer内
 199 |         // 这样确保职责分离：ThinkingBoxContainer只负责思考过程，富文本由独立组件负责
 200 |         Timber.tag("TB-UI").i("🎨 [701mermaid合规] ThinkingBoxContainer不再渲染FinalRichTextRenderer，职责分离")
 201 | 
 202 | 
 203 |     }
 204 | }
 205 | 
 206 | /**
 207 |  * ThinkingBoxContainer - 兼容性重载 (保持向后兼容)
 208 |  *
 209 |  * 🔥 已废弃：请使用纯布局容器版本
 210 |  * @deprecated 使用 ThinkingBoxContainer(modifier, content) 代替
 211 |  */
 212 | @Deprecated(
 213 |     message = "使用纯布局容器版本：ThinkingBoxContainer(modifier, content)",
 214 |     replaceWith = ReplaceWith("ThinkingBoxContainer(modifier) { /* 在这里放置内容 */ }")
 215 | )
 216 | @Composable
 217 | fun ThinkingBoxContainer(
 218 |     thinkingState: com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingUiState,
 219 |     modifier: Modifier = Modifier,
 220 | ) {
 221 |     // 转换为规范的UiState
 222 |     val standardUiState = com.example.gymbro.features.thinkingbox.internal.adapter.UiStateAdapter.toStandardUiState(thinkingState)
 223 | 
 224 |     // 调用新的规范版本
 225 |     ThinkingBoxContainer(
 226 |         uiState = standardUiState,
 227 |         modifier = modifier
 228 |     )
 229 | }
 230 | 
 231 | /**
 232 |  * 预览组件
 233 |  */
 234 | @Preview(showBackground = true)
 235 | @Composable
 236 | fun ThinkingBoxContainerPreview() {
 237 |     GymBroTheme {
 238 |         // 模拟Phase列表
 239 |         val mockPhases = listOf(
 240 |             com.example.gymbro.features.thinkingbox.domain.model.events.PhaseAggregate(
 241 |                 title = "分析阶段",
 242 |                 content = "正在分析问题...",
 243 |                 timestamp = System.currentTimeMillis()
 244 |             ),
 245 |             com.example.gymbro.features.thinkingbox.domain.model.events.PhaseAggregate(
 246 |                 title = "规划阶段",
 247 |                 content = "制定解决方案...",
 248 |                 timestamp = System.currentTimeMillis() + 1000
 249 |             )
 250 |         )
 251 | 
 252 |         // 使用UiState预览
 253 |         val mockUiState = com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingUiState(
 254 |             phases = kotlinx.collections.immutable.persistentMapOf(
 255 |                 "phase1" to mockPhases[0],
 256 |                 "phase2" to mockPhases[1]
 257 |             ),
 258 |             isStreaming = true,
 259 |             final = null
 260 |         )
 261 | 
 262 |         ThinkingBoxContainer(
 263 |             thinkingState = mockUiState,
 264 |             modifier = Modifier.padding(Tokens.Spacing.Large),
 265 |         )
 266 |     }
 267 | }
 268 | 
 269 | 

```

`main\kotlin\com\example\gymbro\features\thinkingbox\internal\presentation\ui\ThinkingHeader.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.internal.presentation.ui
   2 | 
   3 | import androidx.compose.animation.*
   4 | import androidx.compose.animation.core.FastOutSlowInEasing
   5 | import androidx.compose.animation.core.tween
   6 | import androidx.compose.foundation.background
   7 | import androidx.compose.foundation.layout.*
   8 | import androidx.compose.foundation.shape.RoundedCornerShape
   9 | import androidx.compose.material3.*
  10 | import androidx.compose.runtime.*
  11 | import androidx.compose.ui.Alignment
  12 | import androidx.compose.ui.Modifier
  13 | import androidx.compose.ui.graphics.graphicsLayer
  14 | import androidx.compose.ui.text.font.FontStyle
  15 | import androidx.compose.ui.text.font.FontWeight
  16 | import androidx.compose.ui.unit.dp
  17 | import com.example.gymbro.designSystem.theme.motion.MotionDurations
  18 | import com.example.gymbro.designSystem.components.extras.LuminanceText
  19 | import com.example.gymbro.designSystem.components.extras.AppGradients
  20 | import com.example.gymbro.designSystem.theme.tokens.Tokens
  21 | import com.example.gymbro.designSystem.theme.motion.CoachMotionValues
  22 | 
  23 | /**
  24 |  * ThinkingHeader - 支持11步状态转换的Header组件
  25 |  *
  26 |  * 🔥 Step 3优化：实现彩虹loading到fade的动画转换
  27 |  * - LoadingAnimation步骤：彩虹"thinking"动画
  28 |  * - ThinkDetection步骤：fade效果"bro is thinking"
  29 |  * - ThinkingStart步骤：静态"starting to think"
  30 |  * - 使用designSystem tokens确保规范合规
  31 |  * - 支持3秒动画时长，符合文档要求
  32 |  */
  33 | @Composable
  34 | fun ThinkingHeader(
  35 |     title: String,
  36 |     hasFinal: Boolean,
  37 |     expanded: Boolean,
  38 |     onToggleExpand: () -> Unit,
  39 |     isStreaming: Boolean = false,
  40 |     hasContent: Boolean = false,
  41 |     thinkingDuration: String = "",
  42 |     metadataText: String = "",
  43 |     hasPreThinking: Boolean = false,
  44 |     modifier: Modifier = Modifier,
  45 | ) {
  46 |     // 🔥 Step 3优化：根据isStreaming状态显示不同的动画效果
  47 |     Box(
  48 |         modifier = modifier
  49 |             .fillMaxWidth()
  50 |             .padding(horizontal = Tokens.Spacing.Medium),
  51 |         contentAlignment = Alignment.CenterStart,
  52 |     ) {
  53 |         // 🔥 Step 3优化：实现彩虹loading到fade的动画转换
  54 |         AnimatedContent(
  55 |             targetState = isStreaming,
  56 |             transitionSpec = {
  57 |                 fadeIn(
  58 |                     animationSpec = tween(
  59 |                         durationMillis = MotionDurations.M,
  60 |                         easing = FastOutSlowInEasing
  61 |                     )
  62 |                 ) togetherWith fadeOut(
  63 |                     animationSpec = tween(
  64 |                         durationMillis = MotionDurations.M,
  65 |                         easing = FastOutSlowInEasing
  66 |                     )
  67 |                 )
  68 |             },
  69 |             label = "ThinkingHeaderAnimation"
  70 |         ) { streaming ->
  71 |             if (streaming) {
  72 |                 // LoadingAnimation步骤：彩虹"thinking"动画
  73 |                 LuminanceText(
  74 |                     text = title,
  75 |                     animated = true,
  76 |                     colors = AppGradients.rainbow,
  77 |                     speed = 1.2f, // 稍快的动画速度，符合3秒动画时长要求
  78 |                     style = MaterialTheme.typography.titleMedium.copy(
  79 |                         fontWeight = FontWeight.SemiBold // 使用designSystem字重
  80 |                     ),
  81 |                     modifier = Modifier.graphicsLayer {
  82 |                         alpha = CoachMotionValues.Alpha.ENABLED_TEXT
  83 |                     }
  84 |                 )
  85 |             } else {
  86 |                 // ThinkDetection/ThinkingStart步骤：fade效果
  87 |                 Text(
  88 |                     text = title,
  89 |                     style = MaterialTheme.typography.titleMedium.copy(
  90 |                         fontWeight = FontWeight.Medium,
  91 |                         fontStyle = if (title.contains("bro is thinking")) FontStyle.Italic else FontStyle.Normal
  92 |                     ),
  93 |                     color = MaterialTheme.colorScheme.onSurfaceVariant,
  94 |                     modifier = Modifier
  95 |                         .graphicsLayer {
  96 |                             alpha = 0.7f // fade效果
  97 |                         }
  98 |                         .animateContentSize() // 平滑的尺寸变化
  99 |                 )
 100 |             }
 101 |         }
 102 |     }
 103 | }

```

`main\kotlin\com\example\gymbro\features\thinkingbox\internal\presentation\ui\ThinkingStageCard.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.internal.presentation.ui
   2 | 
   3 | import androidx.compose.animation.AnimatedVisibility
   4 | import androidx.compose.animation.animateContentSize
   5 | import androidx.compose.animation.core.FastOutSlowInEasing
   6 | import androidx.compose.animation.core.RepeatMode
   7 | import androidx.compose.animation.core.animateFloat
   8 | import androidx.compose.animation.core.infiniteRepeatable
   9 | import androidx.compose.animation.core.rememberInfiniteTransition
  10 | import androidx.compose.animation.core.tween
  11 | import androidx.compose.animation.fadeOut
  12 | import androidx.compose.foundation.background
  13 | import androidx.compose.foundation.border
  14 | import androidx.compose.foundation.clickable
  15 | import androidx.compose.foundation.layout.Box
  16 | import androidx.compose.foundation.layout.Column
  17 | import androidx.compose.foundation.layout.Row
  18 | import androidx.compose.foundation.layout.Spacer
  19 | import androidx.compose.foundation.layout.fillMaxWidth
  20 | import androidx.compose.foundation.layout.height
  21 | import androidx.compose.foundation.layout.heightIn
  22 | import androidx.compose.foundation.layout.padding
  23 | import androidx.compose.foundation.layout.width
  24 | import androidx.compose.foundation.shape.RoundedCornerShape
  25 | import androidx.compose.material.icons.Icons
  26 | import androidx.compose.material.icons.filled.ExpandLess
  27 | import androidx.compose.material.icons.filled.ExpandMore
  28 | import androidx.compose.material3.Card
  29 | import androidx.compose.material3.CardDefaults
  30 | import androidx.compose.material3.HorizontalDivider
  31 | import androidx.compose.material3.Icon
  32 | import androidx.compose.material3.IconButton
  33 | import androidx.compose.material3.MaterialTheme
  34 | import androidx.compose.material3.Text
  35 | import androidx.compose.runtime.saveable.rememberSaveable
  36 | import androidx.compose.ui.platform.LocalConfiguration
  37 | import androidx.compose.ui.text.font.FontStyle
  38 | import androidx.compose.ui.text.style.TextOverflow
  39 | import androidx.compose.ui.unit.dp
  40 | import androidx.compose.ui.unit.em
  41 | import androidx.compose.runtime.Composable
  42 | import androidx.compose.runtime.LaunchedEffect
  43 | import androidx.compose.runtime.getValue
  44 | import androidx.compose.runtime.mutableStateOf
  45 | import androidx.compose.runtime.remember
  46 | import androidx.compose.runtime.rememberUpdatedState
  47 | import androidx.compose.runtime.setValue
  48 | import androidx.compose.ui.Alignment
  49 | import androidx.compose.ui.Modifier
  50 | import com.example.gymbro.designSystem.theme.tokens.Tokens
  51 | import com.example.gymbro.designSystem.components.extras.rememberMetallicBrush
  52 | import com.example.gymbro.features.thinkingbox.domain.interfaces.PhaseUi
  53 | import kotlinx.coroutines.delay
  54 | import timber.log.Timber
  55 | 
  56 | 
  57 | 
  58 | /**
  59 |  * ThinkingStageCard - ChatGPT风格思考阶段卡片（备用方案）
  60 |  *
  61 |  * 🔥 备用方案说明：
  62 |  * - 主方案：AIThinkingCard（严格按照thinkingboxUI.md规范）
  63 |  * - 备用方案：ThinkingStageCard（ChatGPT风格，独立UI实现）
  64 |  * - 接口一致性：与AIThinkingCard保持相同的参数接口
  65 |  * - UI差异：ChatGPT风格的视觉设计和动画效果
  66 |  *
  67 |  * 基于630chatgptui2.md文档规范实现：
  68 |  * - 高度限制：≤33%屏幕高度
  69 |  * - 预思考区域：AnimatedVisibility + fadeOut动画
  70 |  * - 打字机动画：预思考20ms/字符，正文30ms/字符
  71 |  * - 使用Tokens设计系统，严格遵循视觉规范
  72 |  * - 支持展开/折叠功能
  73 |  *
  74 |  * 使用场景：
  75 |  * - 当需要ChatGPT风格的UI效果时
  76 |  * - 作为AIThinkingCard的替代实现
  77 |  * - 测试不同UI风格的效果对比
  78 |  */
  79 | @Composable
  80 | fun ThinkingStageCardLegacy(
  81 |     phaseUi: PhaseUi?, // 🔥 629工作计划.md要求：支持只显示PreThinking的情况
  82 |     preThinking: String? = null, // 🔥 统一接口：预思考内容参数
  83 |     isExpanded: Boolean = true, // 🔥 【接口对齐】：添加默认值，与AIThinkingCard保持一致
  84 |     onToggleExpand: () -> Unit = {}, // 🔥 【接口对齐】：添加默认值，与AIThinkingCard保持一致
  85 |     modifier: Modifier = Modifier,
  86 | ) {
  87 |     // 🔥 1/3屏高度限制
  88 |     val maxHeight = (LocalConfiguration.current.screenHeightDp * 0.33f).dp
  89 | 
  90 |     // 🔥 629工作计划.md要求：处理phaseUi为null的情况（只有PreThinking）
  91 |     if (phaseUi != null) {
  92 |         // 🔥 调试断点5：UI层数据接收
  93 |         LaunchedEffect(phaseUi.id, phaseUi.title, phaseUi.content.length) {
  94 |             println("🔍 UI phase=${phaseUi.id} title='${phaseUi.title}' contentLen=${phaseUi.content.length}")
  95 |             timber.log.Timber.tag("TB-CARD").i("🎨 [ThinkingStageCard] 接收数据: id=${phaseUi.id}, title='${phaseUi.title}', content=${phaseUi.content.length}字符")
  96 |             if (phaseUi.content.isNotEmpty()) {
  97 |                 timber.log.Timber.tag("TB-CARD").d("🎨 [ThinkingStageCard] 内容预览: ${phaseUi.content.take(100)}...")
  98 |             } else {
  99 |                 timber.log.Timber.tag("TB-CARD").w("⚠️ [ThinkingStageCard] 内容为空！")
 100 |             }
 101 |         }
 102 |     }
 103 | 
 104 |     // 🔥 严格按照thinkingboxUI.md标题策略：
 105 |     val displayTitle = if (phaseUi != null) {
 106 |         phaseUi.title.ifBlank {
 107 |             if (phaseUi.id == "thinking") "思考中…" else "未命名阶段"
 108 |         }
 109 |     } else {
 110 |         // 只有PreThinking时的标题
 111 |         "预思考"
 112 |     }
 113 | 
 114 |     // 活跃状态：有内容时为活跃
 115 |     val isActive = phaseUi?.content?.isNotEmpty() ?: (preThinking != null)
 116 | 
 117 |     Card(
 118 |         modifier = modifier
 119 |             .fillMaxWidth()
 120 |             .heightIn(max = maxHeight), // 🔥 删除跳动动画：移除animateContentSize
 121 |         shape = RoundedCornerShape(Tokens.Radius.Medium), // 使用Token圆角
 122 |         elevation = CardDefaults.cardElevation(Tokens.Elevation.Small), // 使用Token阴影
 123 |         colors = CardDefaults.cardColors(
 124 |             containerColor = MaterialTheme.colorScheme.surface // ChatGPT规范颜色
 125 |         )
 126 |     ) {
 127 |         Column(Modifier.padding(Tokens.Spacing.Medium)) { // 使用Token间距
 128 |             // Header部分：StatusDot + Title (无展开按钮)
 129 |             ChatGptThinkingHeader(
 130 |                 title = displayTitle,
 131 |                 phaseId = phaseUi?.id ?: "unknown", // ✅ 【重组刷新修复】传递phaseId，预思考时使用默认值
 132 |                 isActive = isActive
 133 |             )
 134 | 
 135 |             // ❶ 预思考区（灰度小字 + 打字机）
 136 |             preThinking?.takeIf { it.isNotBlank() }?.let { txt ->
 137 |                 Spacer(Modifier.height(Tokens.Spacing.Small))
 138 | 
 139 |                 ChatGptPreThinkingContent(
 140 |                     text = txt,
 141 |                     modifier = Modifier.fillMaxWidth()
 142 |                 )
 143 | 
 144 |                 // 预思考与正式内容的分隔线
 145 |                 if (phaseUi?.content?.isNotEmpty() == true) {
 146 |                     Spacer(Modifier.height(Tokens.Spacing.Small))
 147 |                     HorizontalDivider(
 148 |                         modifier = Modifier.padding(vertical = Tokens.Spacing.XSmall),
 149 |                         color = MaterialTheme.colorScheme.outline
 150 |                     )
 151 |                 }
 152 |             }
 153 | 
 154 |             // ❂ 内容显示：支持Phase内容或PreThinking内容
 155 |             val contentToShow = when {
 156 |                 phaseUi != null && phaseUi.content.isNotEmpty() -> phaseUi.content
 157 |                 preThinking != null -> preThinking
 158 |                 else -> null
 159 |             }
 160 | 
 161 |             if (contentToShow != null) {
 162 |                 Spacer(Modifier.height(Tokens.Spacing.Medium))
 163 | 
 164 |                 // 🔥 629工作计划.md要求：PreThinking使用灰色斜体显示
 165 |                 if (phaseUi == null && preThinking != null) {
 166 |                     // PreThinking专用显示：灰色斜体
 167 |                     Text(
 168 |                         text = contentToShow,
 169 |                         style = MaterialTheme.typography.bodyMedium.copy(
 170 |                             fontStyle = FontStyle.Italic
 171 |                         ),
 172 |                         color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f), // 灰色
 173 |                         lineHeight = MaterialTheme.typography.bodyMedium.lineHeight * 1.4f,
 174 |                         modifier = Modifier.fillMaxWidth()
 175 |                     )
 176 |                 } else {
 177 |                     // 🔥 修复：正常Phase内容直接显示，支持追加写入而非重新渲染
 178 |                     Text(
 179 |                         text = contentToShow,
 180 |                         style = MaterialTheme.typography.bodyMedium,
 181 |                         color = MaterialTheme.colorScheme.onSurface,
 182 |                         lineHeight = MaterialTheme.typography.bodyMedium.lineHeight * 1.4f,
 183 |                         maxLines = 8, // 🔥 按照规范限制最大行数
 184 |                         overflow = TextOverflow.Ellipsis,
 185 |                         modifier = Modifier.fillMaxWidth()
 186 |                     )
 187 |                 }
 188 |             }
 189 |         }
 190 |     }
 191 | }
 192 | 
 193 | /**
 194 |  * ChatGPT风格思考Header组件 (v8版本)
 195 |  * 包含：StatusDot(脉冲) + MetallicText(标题)
 196 |  * ✅ 【重组刷新修复】添加phaseId参数，确保phase切换时标题状态重置
 197 |  */
 198 | @Composable
 199 | private fun ChatGptThinkingHeader(
 200 |     title: String,
 201 |     phaseId: String, // ✅ 【重组刷新修复】新增：用于检测phase切换
 202 |     isActive: Boolean,
 203 |     isPreThink: Boolean = false, // 🔥 【v8新增】预思考样式控制
 204 | ) {
 205 |     Row(
 206 |         modifier = Modifier.fillMaxWidth(),
 207 |         verticalAlignment = Alignment.CenterVertically
 208 |     ) {
 209 |         // StatusDot：●/○ 稳定显示，减少跳动
 210 |         ChatGptStatusDot(
 211 |             isActive = isActive,
 212 |             isPreThink = isPreThink,
 213 |             modifier = Modifier // 🔥 修复跳动：移除脉冲动画
 214 |         )
 215 | 
 216 |         Spacer(Modifier.width(Tokens.Spacing.Small))
 217 | 
 218 |         // MetallicText：金属质感标题，稳定显示
 219 |         ChatGptMetallicText(
 220 |             text = title,
 221 |             phaseId = phaseId, // ✅ 【重组刷新修复】传递phaseId用于状态重置检测
 222 |             isPreThink = isPreThink,
 223 |             modifier = Modifier // 🔥 修复跳动：移除脉冲动画，保持稳定
 224 |         )
 225 |     }
 226 | }
 227 | 
 228 | /**
 229 |  * ChatGPT风格思考内容组件 (v8版本)
 230 |  * 🔥 【v8核心】perthink无速度限制，正式phase保持30ms打字机
 231 |  * ✅ 【重组刷新修复】添加phaseId参数，确保phase切换时文本状态重置
 232 |  */
 233 | @Composable
 234 | private fun ChatGptThinkingContent(
 235 |     text: String,
 236 |     phaseId: String, // ✅ 【重组刷新修复】新增：用于检测phase切换
 237 |     isPreThink: Boolean = false, // 🔥 【v8新增】控制打字速度
 238 |     onTypingFinished: () -> Unit,
 239 | ) {
 240 |     var displayText by remember { mutableStateOf("") }
 241 | 
 242 |     // 🔥 修复：使用原始 text 作为 key，避免因 HTML 处理导致的重组
 243 |     // 不使用 remember 来缓存 processedText，避免重组问题
 244 | 
 245 |     // 🔥 【Phase切换修复】使用稳定的key，避免不必要的重置
 246 |     var lastPhaseId by remember { mutableStateOf(phaseId) }
 247 | 
 248 |     // 🔥 【关键修复】只在text变化时执行，不依赖phaseId避免重置
 249 |     LaunchedEffect(text) {
 250 |         // 在 LaunchedEffect 内部处理 HTML 标签，避免重组
 251 |         val currentText = text
 252 |             .replace("<br>", "\n")
 253 |             .replace("<br/>", "\n")
 254 |             .replace("<br />", "\n")
 255 |             .replace(Regex("<p>"), "")
 256 |             .replace(Regex("</p>"), "\n\n")
 257 |             .replace(Regex("<[^>]+>"), "")
 258 | 
 259 |         timber.log.Timber.tag("TB-CONTENT").i("🎨 [Phase切换修复] 接收文本: ${currentText.length}字符, phaseId: $phaseId")
 260 |         if (currentText.isNotEmpty()) {
 261 |             timber.log.Timber.tag("TB-CONTENT").d("🎨 [Phase切换修复] 文本预览: ${currentText.take(50)}...")
 262 |         } else {
 263 |             timber.log.Timber.tag("TB-CONTENT").w("⚠️ [Phase切换修复] 文本为空！")
 264 |         }
 265 | 
 266 |         // 🔥 【Phase切换修复】只有在phase真正切换且文本变短时才重置
 267 |         if (phaseId != lastPhaseId && currentText.length < displayText.length) {
 268 |             timber.log.Timber.tag("TB-CONTENT").i("🔥 [Phase切换修复] Phase切换且文本变短，重置displayText: $lastPhaseId → $phaseId")
 269 |             displayText = ""
 270 |             lastPhaseId = phaseId
 271 |         } else if (phaseId != lastPhaseId) {
 272 |             timber.log.Timber.tag("TB-CONTENT").i("🔥 [Phase切换修复] Phase切换但保持内容: $lastPhaseId → $phaseId")
 273 |             lastPhaseId = phaseId
 274 |         }
 275 | 
 276 |         // 🔥 【无限重组修复】append-only逻辑：只处理新增的文本
 277 |         if (currentText.length > displayText.length) {
 278 |             val newContent = currentText.substring(displayText.length)
 279 |             timber.log.Timber.tag("TB-CONTENT").i("🎨 [append-only] 新增内容: ${newContent.length}字符")
 280 | 
 281 |             if (isPreThink) {
 282 |                 // 🔥 【v8核心】perthink无速度限制，立即显示新内容
 283 |                 timber.log.Timber.tag("TB-CONTENT").i("🎨 [v8-perthink] 无速度限制，立即追加")
 284 |                 displayText = currentText
 285 |                 onTypingFinished()
 286 |             } else {
 287 |                 // 🔥 【性能优化】正式phase使用10ms每字符的打字机效果
 288 |                 timber.log.Timber.tag("TB-CONTENT").i("🎨 [性能优化] 正式phase开始10ms打字机动画")
 289 |                 newContent.forEachIndexed { index, _ ->
 290 |                     delay(AnimationEngine.Durations.TYPEWRITER_CHAR_DELAY) // 10ms每字符
 291 |                     displayText = displayText + newContent[index]
 292 |                 }
 293 |                 timber.log.Timber.tag("TB-CONTENT").i("🎨 [性能优化] 正式phase打字机动画完成")
 294 |                 onTypingFinished()
 295 |             }
 296 |         } else if (currentText.length == displayText.length) {
 297 |             // 文本长度相同，无需更新
 298 |             timber.log.Timber.tag("TB-CONTENT").d("🎨 [append-only] 文本长度相同，无需更新")
 299 |         } else {
 300 |             // 文本变短了，这种情况不应该发生在append-only模式下
 301 |             timber.log.Timber.tag("TB-CONTENT").w("⚠️ [append-only] 异常：文本变短了！current=${currentText.length}, display=${displayText.length}")
 302 |         }
 303 |     }
 304 | 
 305 |     // 内容区域：根据isPreThink控制样式
 306 |     Text(
 307 |         text = displayText,
 308 |         style = if (isPreThink) {
 309 |             MaterialTheme.typography.bodySmall.copy(fontStyle = FontStyle.Italic) // 🔥 调小一号：bodyMedium → bodySmall
 310 |         } else {
 311 |             MaterialTheme.typography.bodySmall // 🔥 调小一号：bodyMedium → bodySmall
 312 |         },
 313 |         color = if (isPreThink) {
 314 |             MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f) // 预思考用灰色
 315 |         } else {
 316 |             MaterialTheme.colorScheme.onSurface.copy(alpha = 0.9f)
 317 |         },
 318 |         lineHeight = MaterialTheme.typography.bodySmall.lineHeight * 1.4f, // 🔥 更新行高基准
 319 |         modifier = Modifier.fillMaxWidth()
 320 |     )
 321 | }
 322 | 
 323 | 
 324 | 
 325 | /**
 326 |  * ChatGPT风格状态指示器 (v8版本)
 327 |  * ●(活跃) / ○(非活跃) 带脉冲动画
 328 |  */
 329 | @Composable
 330 | private fun ChatGptStatusDot(
 331 |     isActive: Boolean,
 332 |     isPreThink: Boolean = false, // 🔥 【v8新增】预思考样式控制
 333 |     modifier: Modifier = Modifier
 334 | ) {
 335 |     Text(
 336 |         text = if (isActive) "●" else "○",
 337 |         style = MaterialTheme.typography.bodyLarge,
 338 |         color = if (isActive) {
 339 |             if (isPreThink) {
 340 |                 MaterialTheme.colorScheme.outline // 预思考用灰色
 341 |             } else {
 342 |                 MaterialTheme.colorScheme.primary // 正式思考用主色
 343 |             }
 344 |         } else {
 345 |             MaterialTheme.colorScheme.outline
 346 |         },
 347 |         modifier = modifier
 348 |     )
 349 | }
 350 | 
 351 | /**
 352 |  * ChatGPT风格金属质感文本 (v8版本)
 353 |  * 🔥 【v8核心】统一金属质感彩虹跃动动画
 354 |  * - perthink标题：立即显示 + 斜体 + 金属质感动画
 355 |  * - 正式phase标题：30ms清空再打字 + 正常字体 + 金属质感动画
 356 |  * ✅ 【重组刷新修复】添加phaseId参数，确保phase切换时标题状态重置
 357 |  */
 358 | @Composable
 359 | private fun ChatGptMetallicText(
 360 |     text: String,
 361 |     phaseId: String, // ✅ 【重组刷新修复】新增：用于检测phase切换
 362 |     isPreThink: Boolean = false, // 🔥 【v8新增】预思考样式控制
 363 |     modifier: Modifier = Modifier
 364 | ) {
 365 |     var displayText by remember { mutableStateOf("") }
 366 |     val currentText by rememberUpdatedState(text)
 367 | 
 368 |     // ✅ 【Title无限重组修复】添加状态检查，避免重复动画
 369 |     LaunchedEffect(currentText, phaseId) {
 370 |         // 🔥 关键修复：检查是否需要更新，避免无限重组
 371 |         if (displayText == currentText && currentText.isNotEmpty()) {
 372 |             timber.log.Timber.tag("TB-TITLE").v("🔥 [Title无限重组修复] 标题已是最新，跳过更新: '$currentText'")
 373 |             return@LaunchedEffect
 374 |         }
 375 | 
 376 |         if (isPreThink) {
 377 |             // 🔥 【v8核心】perthink标题立即显示，无动画
 378 |             timber.log.Timber.tag("TB-TITLE").i("🎨 [v8-perthink] 标题立即显示: '$currentText'")
 379 |             displayText = currentText
 380 |         } else {
 381 |             // 🔥 【Title固定修复】正式phase使用append-only逻辑，避免重新清空
 382 |             timber.log.Timber.tag("TB-TITLE").i("🎨 [Title追加模式] 开始: '$currentText'")
 383 | 
 384 |             // 🔥 【关键修复】检查是否可以追加，避免重新开始
 385 |             if (currentText.startsWith(displayText) && displayText.isNotEmpty()) {
 386 |                 // 增量追加title内容
 387 |                 val newContent = currentText.substring(displayText.length)
 388 |                 if (newContent.isNotEmpty()) {
 389 |                     timber.log.Timber.tag("TB-TITLE").i("🎨 [Title追加] 追加内容: '$newContent'")
 390 |                     newContent.forEachIndexed { index, _ ->
 391 |                         delay(AnimationEngine.Durations.TYPEWRITER_CHAR_DELAY) // 使用快速打字速度
 392 |                         displayText = displayText + newContent[index]
 393 |                     }
 394 |                     timber.log.Timber.tag("TB-TITLE").i("🎨 [Title追加] 追加完成: '$displayText'")
 395 |                 }
 396 |             } else {
 397 |                 // 新title或不兼容的内容，重新开始（但不清空，直接覆盖）
 398 |                 timber.log.Timber.tag("TB-TITLE").i("🎨 [Title重新开始] 开始: '$currentText'")
 399 |                 displayText = ""
 400 |                 delay(10) // 短暂延迟
 401 | 
 402 |                 if (currentText.isNotEmpty()) {
 403 |                     currentText.forEachIndexed { index, _ ->
 404 |                         delay(AnimationEngine.Durations.TYPEWRITER_CHAR_DELAY) // 使用快速打字速度
 405 |                         displayText = currentText.substring(0, index + 1)
 406 |                     }
 407 |                     timber.log.Timber.tag("TB-TITLE").i("🎨 [Title重新开始] 完成: '$displayText'")
 408 |                 } else {
 409 |                     timber.log.Timber.tag("TB-TITLE").w("⚠️ [Title重新开始] title为空，跳过动画")
 410 |                 }
 411 |             }
 412 |         }
 413 |     }
 414 | 
 415 |     // 🔥 【统一金属质感动画】所有 title 都使用金属质感彩虹跃动动画
 416 |     val metallicBrush = rememberMetallicBrush(
 417 |         useAnimate = true,
 418 |         rotationDurationMillis = 3000, // 3秒一个周期的彩虹跃动
 419 |         useHdr = false
 420 |     )
 421 | 
 422 |     Text(
 423 |         text = displayText,
 424 |         style = MaterialTheme.typography.titleSmall.copy( // 🔥 调小一号：titleMedium → titleSmall
 425 |             fontStyle = if (isPreThink) FontStyle.Italic else FontStyle.Normal,
 426 |             brush = metallicBrush // 🔥 统一的彩虹跃动效果
 427 |         ),
 428 |         modifier = modifier
 429 |     )
 430 | }
 431 | 
 432 | /**
 433 |  * 预思考内容组件 - 灰度显示<think>标签内容
 434 |  */
 435 | @Composable
 436 | private fun ChatGptPreThinkingContent(
 437 |     text: String,
 438 |     modifier: Modifier = Modifier,
 439 | ) {
 440 |     var displayText by remember { mutableStateOf("") }
 441 | 
 442 |     // 🔥 修复：使用原始 text 作为 key，在内部处理 HTML 标签
 443 |     LaunchedEffect(text) {
 444 |         // 在 LaunchedEffect 内部处理 HTML 标签，避免重组
 445 |         val processedText = text
 446 |             .replace("<br>", "\n")
 447 |             .replace("<br/>", "\n")
 448 |             .replace("<br />", "\n")
 449 |             .replace(Regex("<p>"), "")
 450 |             .replace(Regex("</p>"), "\n\n")
 451 |             .replace(Regex("<[^>]+>"), "")
 452 | 
 453 |         if (processedText.isNotEmpty()) {
 454 |             timber.log.Timber.tag("TB-PRETHINK").i("🎨 [PreThinkingContent] 开始预思考动画: ${processedText.length}字符")
 455 |             displayText = ""
 456 |             processedText.forEachIndexed { index, _ ->
 457 |                 delay(AnimationEngine.Durations.PRETHINK_CHAR_DELAY) // 🔥 P1修复：使用统一的预思考打字机速度
 458 |                 displayText = processedText.substring(0, index + 1)
 459 |             }
 460 |             timber.log.Timber.tag("TB-PRETHINK").i("🎨 [PreThinkingContent] 预思考动画完成")
 461 |         }
 462 |     }
 463 | 
 464 |     // 灰度小字显示
 465 |     Text(
 466 |         text = displayText,
 467 |         style = MaterialTheme.typography.bodySmall.copy(
 468 |             fontStyle = FontStyle.Italic,
 469 |             lineHeight = 1.4.em
 470 |         ),
 471 |         color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f), // 灰度显示
 472 |         modifier = modifier
 473 |     )
 474 | }
 475 | 
 476 | /**
 477 |  * ThinkingStageCard - v8统一渲染版本
 478 |  *
 479 |  * 🔥 【v8核心】统一渲染逻辑：
 480 |  * - 不再区分PreThinkingCard和ThinkingStageCard
 481 |  * - 通过isPreThink参数控制样式差异
 482 |  * - perthink无打字速度限制，能多快就多快
 483 |  * - 正式phase保持正常打字机效果
 484 |  */
 485 | @Composable
 486 | fun ThinkingStageCard(
 487 |     phase: com.example.gymbro.features.thinkingbox.domain.interfaces.PhaseUi,
 488 |     isPreThink: Boolean = false, // 🔥 【v8新增】控制预思考样式
 489 |     isActive: Boolean = true,
 490 |     onRenderingComplete: ((String) -> Unit)? = null, // 🔥 【Phase转换修复】渲染完成回调
 491 |     modifier: Modifier = Modifier
 492 | ) {
 493 |     // 🔥 【Phase转换控制】跟踪渲染完成状态，避免重复发送事件
 494 |     var hasNotifiedCompletion by remember(phase.id) { mutableStateOf(false) }
 495 |     // 🔥 1/3屏高度限制
 496 |     val maxHeight = (LocalConfiguration.current.screenHeightDp * 0.33f).dp
 497 | 
 498 |     Card(
 499 |         modifier = modifier
 500 |             .fillMaxWidth()
 501 |             .heightIn(max = maxHeight), // 🔥 删除跳动动画：移除animateContentSize
 502 |         shape = RoundedCornerShape(Tokens.Radius.Medium),
 503 |         elevation = CardDefaults.cardElevation(Tokens.Elevation.Small),
 504 |         colors = CardDefaults.cardColors(
 505 |             containerColor = if (isPreThink) {
 506 |                 MaterialTheme.colorScheme.surfaceVariant // 预思考用不同背景色
 507 |             } else {
 508 |                 MaterialTheme.colorScheme.surface
 509 |             }
 510 |         )
 511 |     ) {
 512 |         Column(Modifier.padding(Tokens.Spacing.Medium)) {
 513 |             // Header部分：StatusDot + Title
 514 |             ChatGptThinkingHeader(
 515 |                 title = if (isPreThink) "Bro is thinking" else phase.title, // 🔥 perthink配置title
 516 |                 phaseId = phase.id, // ✅ 【重组刷新修复】传递phaseId用于状态重置检测
 517 |                 isActive = isActive,
 518 |                 isPreThink = isPreThink
 519 |             )
 520 | 
 521 |             Spacer(Modifier.height(Tokens.Spacing.Small))
 522 | 
 523 |             // 内容部分：根据isPreThink控制样式和速度
 524 |             ChatGptThinkingContent(
 525 |                 text = phase.content,
 526 |                 phaseId = phase.id, // ✅ 【重组刷新修复】传递phaseId用于状态重置检测
 527 |                 isPreThink = isPreThink,
 528 |                 onTypingFinished = {
 529 |                     // 🔥 【Phase转换简化】移除打字机完成时的通知，避免重复
 530 |                     timber.log.Timber.tag("TB-UI").d("🎯 [Phase转换简化] Phase ${phase.id} 打字机完成，等待phase.isComplete")
 531 |                 }
 532 |             )
 533 | 
 534 |             // 🔥 【Phase转换控制】监听phase.isComplete状态变化，并添加详细日志
 535 |             LaunchedEffect(phase.isComplete, phase.id) {
 536 |                 timber.log.Timber.tag("PHASE-DEBUG").e("🔍 UI监听Phase状态: ${phase.id} | isComplete=${phase.isComplete} | hasNotified=$hasNotifiedCompletion")
 537 |                 if (phase.isComplete && !hasNotifiedCompletion) {
 538 |                     hasNotifiedCompletion = true
 539 |                     onRenderingComplete?.invoke(phase.id)
 540 |                     timber.log.Timber.tag("PHASE-DEBUG").e("🎯 UI检测到Phase完成: ${phase.id} | 发送PhaseRenderingComplete事件")
 541 |                 }
 542 |             }
 543 | 
 544 | 
 545 |         }
 546 |     }
 547 | }

```

`main\kotlin\com\example\gymbro\features\thinkingbox\logging\ThinkingBoxLogTree.kt`:

```kt
   1 | package com.example.gymbro.features.thinkingbox.logging
   2 | 
   3 | import android.util.Log
   4 | import timber.log.Timber
   5 | import kotlinx.coroutines.*
   6 | import java.util.concurrent.ConcurrentHashMap
   7 | import java.util.concurrent.atomic.AtomicLong
   8 | 
   9 | /**
  10 |  * ThinkingBox 专用日志树
  11 |  *
  12 |  * 实现日志瘦身，过滤低优先级的 token 级别日志
  13 |  */
  14 | class ThinkingBoxLogTree : Timber.DebugTree() {
  15 | 
  16 |     companion object {
  17 |         // ThinkingBox 相关的标签前缀
  18 |         private val THINKING_TAGS = setOf(
  19 |             "THINKINGBOX",
  20 |             "THINKINGBOX-RAW",
  21 |             "THINKINGBOX-MONITOR",
  22 |             "THINKINGBOX-HISTORY",
  23 |             "XML-PARSER",
  24 |             "ThinkingBoxFacade",
  25 |             "ThinkingMLGuardrail",
  26 |             "TB-RAW",
  27 |             "TB-FILTER",
  28 |             "TB-SEM",
  29 |             "TB-MAP",
  30 |             "TB-EVT",
  31 |             "TB-STATE",
  32 |             "TB-DB",
  33 |             "TB-UI",
  34 |             "AI-STREAM",
  35 |             "AI-RAW",
  36 |             // 🔥 新增：Coach 模块调试标签
  37 |             "REDUCER-DEBUG",
  38 |             "VIEWMODEL-DEBUG",
  39 |             "EFFECT-DEBUG",
  40 |             "USECASE-DEBUG",
  41 |             "ChatSessio...addMessage", // Repository 层日志标签
  42 |             "AiCoachViewModel",
  43 |             "MessagingReducerHandler",
  44 |             "SessionEffectHandler",
  45 |             "ChatSessionManagementUseCase",
  46 |             "ChatSessionRepositoryImpl",
  47 |             // 🔥 新增：Template保存调试标签
  48 |             "BUTTON-SAVE",
  49 |             "BUTTON-TEST",
  50 |             "UI-TEST",
  51 |             "UI-FEEDBACK",
  52 |             "TemplateEditReducer",
  53 |             "TemplateEditViewModel",
  54 |             "TemplateSaver",
  55 |             "TemplateScreen",
  56 |             "TemplateViewModel",
  57 |             "TemplateEffectHandler",
  58 |         )
  59 | 
  60 |         // 高频日志标签，需要特殊处理
  61 |         private val HIGH_FREQUENCY_TAGS = setOf(
  62 |             "THINKINGBOX-RAW",
  63 |             "XML-PARSER",
  64 |             "TB-RAW",
  65 |             "AI-STREAM",
  66 |             "AI-RAW",
  67 |         )
  68 |     }
  69 | 
  70 |     override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
  71 |         // 🔥 日志聚合处理 - 支持所有高频标签和优先级
  72 |         when {
  73 |             // 1. TB-RAW 高频日志聚合 (所有优先级)
  74 |             tag == "TB-RAW" -> {
  75 |                 LogAggregatorManager.getAggregator("TB-RAW").append(message)
  76 |                 return
  77 |             }
  78 | 
  79 |             // 2. AI-STREAM 高频日志聚合 (所有优先级，包括ERROR)
  80 |             tag == "AI-STREAM" -> {
  81 |                 LogAggregatorManager.getAggregator("AI-STREAM").append(message)
  82 |                 return
  83 |             }
  84 | 
  85 |             // 3. AI-RAW 高频日志聚合
  86 |             tag == "AI-RAW" -> {
  87 |                 LogAggregatorManager.getAggregator("AI-RAW").append(message)
  88 |                 return
  89 |             }
  90 | 
  91 |             // 4. THINKINGBOX-RAW 高频日志聚合
  92 |             tag == "THINKINGBOX-RAW" -> {
  93 |                 LogAggregatorManager.getAggregator("THINKINGBOX-RAW").append(message)
  94 |                 return
  95 |             }
  96 | 
  97 |             // 5. 其他TB相关高频标签聚合
  98 |             isHighFrequencyTag(tag) && (priority == Log.DEBUG || priority == Log.VERBOSE) -> {
  99 |                 LogAggregatorManager.getAggregator(tag ?: "UNKNOWN").append(message)
 100 |                 return
 101 |             }
 102 | 
 103 |             // 6. 非 ThinkingBox 相关的日志，只显示 INFO 及以上
 104 |             !isThinkingBoxTag(tag) && priority < Log.INFO -> return
 105 | 
 106 |             // 7. Token 级别的日志特殊处理
 107 |             isTokenLevelLog(message) && priority == Log.DEBUG -> {
 108 |                 // 转换为 VERBOSE 级别，默认过滤
 109 |                 super.log(Log.VERBOSE, tag, message, t)
 110 |                 return
 111 |             }
 112 |         }
 113 | 
 114 |         // 其他日志正常输出
 115 |         super.log(priority, tag, message, t)
 116 |     }
 117 | 
 118 |     /**
 119 |      * 检查是否为 ThinkingBox 相关标签
 120 |      */
 121 |     private fun isThinkingBoxTag(tag: String?): Boolean {
 122 |         if (tag == null) return false
 123 |         return THINKING_TAGS.any { thinkingTag ->
 124 |             tag.startsWith(thinkingTag, ignoreCase = true)
 125 |         }
 126 |     }
 127 | 
 128 |     /**
 129 |      * 检查是否为高频日志标签
 130 |      */
 131 |     private fun isHighFrequencyTag(tag: String?): Boolean {
 132 |         if (tag == null) return false
 133 |         return HIGH_FREQUENCY_TAGS.any { highFreqTag ->
 134 |             tag.startsWith(highFreqTag, ignoreCase = true)
 135 |         }
 136 |     }
 137 | 
 138 |     /**
 139 |      * 检查是否为 Token 级别的日志
 140 |      */
 141 |     private fun isTokenLevelLog(message: String): Boolean {
 142 |         return message.contains("token", ignoreCase = true) ||
 143 |             message.contains("收到语义事件", ignoreCase = true) ||
 144 |             message.contains("发送内容", ignoreCase = true) ||
 145 |             message.contains("解析事件", ignoreCase = true)
 146 |     }
 147 | 
 148 |     /**
 149 |      * 检查是否为 DEBUG 构建
 150 |      */
 151 |     private fun isDebugBuild(): Boolean {
 152 |         return try {
 153 |             // 这里可以根据实际的 BuildConfig 来判断
 154 |             // 暂时返回 true，在生产环境中应该返回 BuildConfig.DEBUG
 155 |             true
 156 |         } catch (e: Exception) {
 157 |             false
 158 |         }
 159 |     }
 160 | 
 161 |     override fun createStackElementTag(element: StackTraceElement): String? {
 162 |         // 为 ThinkingBox 相关的类添加特殊标签前缀
 163 |         val className = element.className
 164 |         return when {
 165 |             className.contains("thinkingbox", ignoreCase = true) -> {
 166 |                 "THINKINGBOX-${super.createStackElementTag(element)}"
 167 |             }
 168 |             else -> super.createStackElementTag(element)
 169 |         }
 170 |     }
 171 | }
 172 | 
 173 | /**
 174 |  * ThinkingBox 日志配置工具
 175 |  */
 176 | object ThinkingBoxLogConfig {
 177 | 
 178 |     /**
 179 |      * 配置 ThinkingBox 日志系统 (v2.0 - 支持日志聚合)
 180 |      */
 181 |     fun configure() {
 182 |         // 移除默认的日志树
 183 |         Timber.uprootAll()
 184 | 
 185 |         // 植入 ThinkingBox 专用日志树
 186 |         Timber.plant(ThinkingBoxLogTree())
 187 | 
 188 |         // 🔥 修复递归调用：直接使用Android Log
 189 |         android.util.Log.i("ThinkingBoxLogConfig", "ThinkingBox 日志系统已配置 (支持TB-RAW/AI-STREAM聚合)")
 190 |     }
 191 | 
 192 |     /**
 193 |      * 强制刷新所有日志聚合器
 194 |      */
 195 |     fun flushAllAggregators() {
 196 |         LogAggregatorManager.cleanup()
 197 |         // 🔥 修复递归调用：直接使用Android Log
 198 |         android.util.Log.d("ThinkingBoxLogConfig", "所有日志聚合器已刷新")
 199 |     }
 200 | 
 201 |     /**
 202 |      * 启用详细日志（调试模式）
 203 |      */
 204 |     fun enableVerboseLogging() {
 205 |         Timber.uprootAll()
 206 |         Timber.plant(object : Timber.DebugTree() {
 207 |             override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
 208 |                 // 调试模式下显示所有日志
 209 |                 super.log(priority, tag, message, t)
 210 |             }
 211 |         })
 212 | 
 213 |         android.util.Log.d("ThinkingBoxLogConfig", "ThinkingBox 详细日志已启用")
 214 |     }
 215 | 
 216 |     /**
 217 |      * 启用生产模式日志（只显示重要信息）
 218 |      */
 219 |     fun enableProductionLogging() {
 220 |         Timber.uprootAll()
 221 |         Timber.plant(object : Timber.DebugTree() {
 222 |             override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
 223 |                 // 生产模式只显示 INFO 及以上级别
 224 |                 if (priority >= Log.INFO) {
 225 |                     super.log(priority, tag, message, t)
 226 |                 }
 227 |             }
 228 |         })
 229 | 
 230 |         android.util.Log.i("ThinkingBoxLogConfig", "ThinkingBox 生产模式日志已启用")
 231 |     }
 232 | 
 233 |     /**
 234 |      * 获取日志统计信息
 235 |      */
 236 |     fun getLogStats(): LogStats {
 237 |         // 这里可以实现日志统计功能
 238 |         return LogStats(
 239 |             totalLogs = 0,
 240 |             debugLogs = 0,
 241 |             infoLogs = 0,
 242 |             warningLogs = 0,
 243 |             errorLogs = 0,
 244 |         )
 245 |     }
 246 | }
 247 | 
 248 | /**
 249 |  * 日志统计数据类
 250 |  */
 251 | data class LogStats(
 252 |     val totalLogs: Int,
 253 |     val debugLogs: Int,
 254 |     val infoLogs: Int,
 255 |     val warningLogs: Int,
 256 |     val errorLogs: Int,
 257 | ) {
 258 |     override fun toString(): String {
 259 |         return "LogStats(total=$totalLogs, debug=$debugLogs, info=$infoLogs, warn=$warningLogs, error=$errorLogs)"
 260 |     }
 261 | }
 262 | 
 263 | /**
 264 |  * 日志聚合器 - 实现≥200 token才落一条日志的机制
 265 |  *
 266 |  * 🔥 核心功能：
 267 |  * - TB-RAW: ≥200 token 或 ≥1000ms 触发聚合输出
 268 |  * - AI-STREAM: ≥50条消息 或 ≥2000ms 触发聚合输出 (频率较低)
 269 |  * - AI-RAW: ≥100 token 或 ≥1500ms 触发聚合输出
 270 |  */
 271 | class LogAggregator(
 272 |     private val tag: String,
 273 |     private val tokenThreshold: Int = 200,
 274 |     private val timeThresholdMs: Long = 1000L,
 275 |     private val messageCountThreshold: Int = 50  // 新增：消息数量阈值
 276 | ) {
 277 |     private val buffer = StringBuilder()
 278 |     private val tokenCount = AtomicLong(0)
 279 |     private val messageCount = AtomicLong(0)  // 新增：消息计数器
 280 |     private var lastFlushTime = System.currentTimeMillis()
 281 |     private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
 282 | 
 283 |     /**
 284 |      * 添加日志内容
 285 |      */
 286 |     fun append(message: String) {
 287 |         synchronized(buffer) {
 288 |             buffer.append(message).append(" ")
 289 | 
 290 |             // 简单token计数：按空格分词
 291 |             val tokens = message.split("\\s+".toRegex()).size
 292 |             val currentTokens = tokenCount.addAndGet(tokens.toLong())
 293 |             val currentMessages = messageCount.incrementAndGet()
 294 |             val currentTime = System.currentTimeMillis()
 295 | 
 296 |             // 检查是否需要刷新 - 支持多种触发条件
 297 |             if (currentTokens >= tokenThreshold ||
 298 |                 currentMessages >= messageCountThreshold ||
 299 |                 (currentTime - lastFlushTime) >= timeThresholdMs) {
 300 |                 flush()
 301 |             }
 302 |         }
 303 |     }
 304 | 
 305 |     /**
 306 |      * 强制刷新缓冲区
 307 |      */
 308 |     fun flush() {
 309 |         synchronized(buffer) {
 310 |             if (buffer.isNotEmpty()) {
 311 |                 val content = buffer.toString().trim()
 312 |                 val tokens = tokenCount.get()
 313 |                 val messages = messageCount.get()
 314 | 
 315 |                 // 🔥 修复递归调用：直接使用Android Log，避免通过Timber造成递归
 316 |                 android.util.Log.i("$tag-AGGREGATED", "🔍 [聚合] ${messages}条消息/${tokens}个token: ${content.take(200)}${if(content.length > 200) "..." else ""}")
 317 | 
 318 |                 // 清空缓冲区
 319 |                 buffer.clear()
 320 |                 tokenCount.set(0)
 321 |                 messageCount.set(0)
 322 |                 lastFlushTime = System.currentTimeMillis()
 323 |             }
 324 |         }
 325 |     }
 326 | 
 327 |     /**
 328 |      * 清理资源
 329 |      */
 330 |     fun cleanup() {
 331 |         flush()
 332 |         scope.cancel()
 333 |     }
 334 | }
 335 | 
 336 | /**
 337 |  * 日志聚合器管理器
 338 |  */
 339 | object LogAggregatorManager {
 340 |     private val aggregators = ConcurrentHashMap<String, LogAggregator>()
 341 | 
 342 |     /**
 343 |      * 获取或创建聚合器 - 根据标签类型使用不同配置
 344 |      */
 345 |     fun getAggregator(tag: String): LogAggregator {
 346 |         return aggregators.computeIfAbsent(tag) { createAggregatorForTag(it) }
 347 |     }
 348 | 
 349 |     /**
 350 |      * 根据标签类型创建合适的聚合器
 351 |      */
 352 |     private fun createAggregatorForTag(tag: String): LogAggregator {
 353 |         return when (tag) {
 354 |             "TB-RAW" -> LogAggregator(
 355 |                 tag = tag,
 356 |                 tokenThreshold = 200,
 357 |                 timeThresholdMs = 1000L,
 358 |                 messageCountThreshold = 100
 359 |             )
 360 |             "AI-STREAM" -> LogAggregator(
 361 |                 tag = tag,
 362 |                 tokenThreshold = 100,
 363 |                 timeThresholdMs = 2000L,
 364 |                 messageCountThreshold = 20  // AI-STREAM频率较低，20条消息就聚合
 365 |             )
 366 |             "AI-RAW" -> LogAggregator(
 367 |                 tag = tag,
 368 |                 tokenThreshold = 150,
 369 |                 timeThresholdMs = 1500L,
 370 |                 messageCountThreshold = 50
 371 |             )
 372 |             "THINKINGBOX-RAW" -> LogAggregator(
 373 |                 tag = tag,
 374 |                 tokenThreshold = 200,
 375 |                 timeThresholdMs = 1000L,
 376 |                 messageCountThreshold = 80
 377 |             )
 378 |             else -> LogAggregator(
 379 |                 tag = tag,
 380 |                 tokenThreshold = 200,
 381 |                 timeThresholdMs = 1000L,
 382 |                 messageCountThreshold = 50
 383 |             )
 384 |         }
 385 |     }
 386 | 
 387 |     /**
 388 |      * 清理所有聚合器
 389 |      */
 390 |     fun cleanup() {
 391 |         aggregators.values.forEach { it.cleanup() }
 392 |         aggregators.clear()
 393 |     }
 394 | }

```