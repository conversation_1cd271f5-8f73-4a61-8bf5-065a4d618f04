package com.example.gymbro.data.autosave.strategy

import com.example.gymbro.core.autosave.AutoSaveManager
import com.example.gymbro.core.autosave.strategy.AutoSaveStrategy
import com.example.gymbro.core.logging.Logger
import kotlinx.coroutines.*
import javax.inject.Inject

/**
 * 定时保存策略实现
 *
 * 🎯 功能特性：
 * - 按固定间隔自动保存
 * - 只有在数据变更时才执行保存
 * - 最低间隔1秒，默认3秒
 * - 支持协程定时器
 *
 * @param T 要保存的数据类型
 * @param intervalMs 保存间隔（毫秒）
 * @param logger 日志记录器
 */
class IntervalSaveStrategy<T : Any>
@Inject
constructor(
    private val logger: Logger,
    intervalMs: Long = 3000L,
) : AutoSaveStrategy<T> {
    // 确保间隔不小于1秒
    private val intervalMs: Long = maxOf(intervalMs, AutoSaveManager.MIN_SAVE_INTERVAL_MS)

    private var saveJob: Job? = null
    private var saveCallback: (suspend () -> Result<Unit>)? = null
    private var errorCallback: ((Throwable) -> Unit)? = null
    private var hasChanges = false
    private var lastSaveTime: Long = 0L

    override fun start(
        scope: CoroutineScope,
        onSave: suspend () -> Result<Unit>,
        onError: (Throwable) -> Unit,
    ) {
        this.saveCallback = onSave
        this.errorCallback = onError

        logger.d("IntervalSaveStrategy", "定时保存策略已启动，间隔: ${this.intervalMs}ms")

        saveJob =
            scope.launch {
                while (isActive) {
                    delay(<EMAIL>)

                    if (hasChanges) {
                        // 检查1秒最低间隔限制
                        val currentTime = System.currentTimeMillis()
                        if (currentTime - lastSaveTime >= AutoSaveManager.MIN_SAVE_INTERVAL_MS) {
                            try {
                                logger.d("IntervalSaveStrategy", "开始定时保存")
                                val result = onSave()

                                result.fold(
                                    onSuccess = {
                                        hasChanges = false
                                        lastSaveTime = currentTime
                                        logger.d("IntervalSaveStrategy", "定时保存成功")
                                    },
                                    onFailure = { error ->
                                        logger.e(error, "定时保存失败")
                                        onError(error)
                                    },
                                )
                            } catch (e: Exception) {
                                logger.e(e, "定时保存异常")
                                onError(e)
                            }
                        } else {
                            logger.d("IntervalSaveStrategy", "定时保存被限制，距离上次保存不足1秒")
                        }
                    }
                }
            }
    }

    override fun onDataChanged(
        oldData: T?,
        newData: T,
    ) {
        hasChanges = true
        logger.d("IntervalSaveStrategy", "数据已变更，标记为需要保存")
    }

    override fun stop() {
        saveJob?.cancel()
        saveJob = null
        saveCallback = null
        errorCallback = null
        hasChanges = false
        lastSaveTime = 0L

        logger.d("IntervalSaveStrategy", "定时保存策略已停止")
    }

    companion object {
        /**
         * 创建定时保存策略实例
         */
        fun <T : Any> create(
            logger: Logger,
            intervalMs: Long = 3000L,
        ): IntervalSaveStrategy<T> = IntervalSaveStrategy(logger, intervalMs)
    }
}
