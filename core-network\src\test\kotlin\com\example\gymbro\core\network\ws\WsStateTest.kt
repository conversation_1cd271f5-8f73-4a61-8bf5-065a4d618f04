package com.example.gymbro.core.network.ws

import org.junit.Test
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * WebSocket状态机单元测试
 *
 * 验证状态转换逻辑和状态检查方法
 */
class WsStateTest {

    @Test
    fun `test isActive method`() {
        // 活跃状态
        assertTrue(WsState.OPEN.isActive())
        assertTrue(WsState.STREAMING.isActive())

        // 非活跃状态
        assertFalse(WsState.INIT.isActive())
        assertFalse(WsState.CONNECTING.isActive())
        assertFalse(WsState.RECONNECTING.isActive())
        assertFalse(WsState.DEAD.isActive())
    }

    @Test
    fun `test isConnected method`() {
        // 已连接状态
        assertTrue(WsState.OPEN.isConnected())
        assertTrue(WsState.STREAMING.isConnected())

        // 未连接状态
        assertFalse(WsState.INIT.isConnected())
        assertFalse(WsState.CONNECTING.isConnected())
        assertFalse(WsState.RECONNECTING.isConnected())
        assertFalse(WsState.DEAD.isConnected())
    }

    @Test
    fun `test isTerminated method`() {
        // 终止状态
        assertTrue(WsState.DEAD.isTerminated())

        // 非终止状态
        assertFalse(WsState.INIT.isTerminated())
        assertFalse(WsState.CONNECTING.isTerminated())
        assertFalse(WsState.OPEN.isTerminated())
        assertFalse(WsState.STREAMING.isTerminated())
        assertFalse(WsState.RECONNECTING.isTerminated())
    }

    @Test
    fun `test canReconnect method`() {
        // 可重连状态
        assertTrue(WsState.INIT.canReconnect())
        assertTrue(WsState.CONNECTING.canReconnect())
        assertTrue(WsState.RECONNECTING.canReconnect())

        // 不可重连状态
        assertFalse(WsState.OPEN.canReconnect())
        assertFalse(WsState.STREAMING.canReconnect())
        assertFalse(WsState.DEAD.canReconnect())
    }

    @Test
    fun `test state transition logic`() {
        // 验证状态转换的逻辑正确性

        // 初始状态应该可以连接
        assertTrue(WsState.INIT.canReconnect())
        assertFalse(WsState.INIT.isConnected())

        // 连接中状态
        assertTrue(WsState.CONNECTING.canReconnect())
        assertFalse(WsState.CONNECTING.isActive())

        // 连接成功状态
        assertTrue(WsState.OPEN.isConnected())
        assertTrue(WsState.OPEN.isActive())
        assertFalse(WsState.OPEN.canReconnect())

        // 流式传输状态
        assertTrue(WsState.STREAMING.isConnected())
        assertTrue(WsState.STREAMING.isActive())
        assertFalse(WsState.STREAMING.canReconnect())

        // 重连状态
        assertTrue(WsState.RECONNECTING.canReconnect())
        assertFalse(WsState.RECONNECTING.isConnected())
        assertFalse(WsState.RECONNECTING.isActive())

        // 死亡状态
        assertTrue(WsState.DEAD.isTerminated())
        assertFalse(WsState.DEAD.canReconnect())
        assertFalse(WsState.DEAD.isConnected())
        assertFalse(WsState.DEAD.isActive())
    }

    @Test
    fun `test state enum values`() {
        val allStates = WsState.values()

        // 验证所有状态都存在
        assertTrue(allStates.contains(WsState.INIT))
        assertTrue(allStates.contains(WsState.CONNECTING))
        assertTrue(allStates.contains(WsState.OPEN))
        assertTrue(allStates.contains(WsState.STREAMING))
        assertTrue(allStates.contains(WsState.RECONNECTING))
        assertTrue(allStates.contains(WsState.DEAD))

        // 验证状态数量
        assertTrue(allStates.size == 6)
    }
}
