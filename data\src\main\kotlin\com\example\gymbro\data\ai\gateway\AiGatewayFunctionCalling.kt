package com.example.gymbro.data.ai.gateway

// 🧹 REMOVED: import com.example.gymbro.data.ai.service.StreamingAiApiService
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.features.FeatureErrors
import com.example.gymbro.core.network.ws.LlmStreamClient
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.ai.compression.FunctionCompressor
import com.example.gymbro.data.ai.function.WorkoutTemplateFunctionDef
import com.example.gymbro.data.ai.validation.JsonSchemaValidator
import com.example.gymbro.domain.coach.config.AiProviderManager
import com.example.gymbro.domain.workout.model.DraftSource
import com.example.gymbro.domain.workout.model.TemplateDraft
import com.example.gymbro.shared.models.ai.ChatMessage
import com.example.gymbro.shared.models.ai.ChatRequest
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonArray
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * AiGateway Function Calling扩展
 * Sprint 1A: 实现完整的Function Calling流程
 *
 * 核心功能：
 * 1. Function Calling端到端支持
 * 2. 双层校验机制
 * 3. 降级策略处理
 * 4. Token优化集成
 */
@Singleton
class AiGatewayFunctionCalling
@Inject
constructor(
    private val llmStreamClient: LlmStreamClient, // 🔧 迁移到WebSocket客户端
    private val aiProviderManager: AiProviderManager,
    private val functionCompressor: FunctionCompressor,
    private val jsonSchemaValidator: JsonSchemaValidator,
    private val json: Json,
) {
    /**
     * 生成训练模板 - Function Calling主入口
     *
     * 🔥 【事件总线架构】改为suspend函数以支持新的streamChatWithMessageId
     *
     * @param prompt 用户提示词
     * @param history 对话历史
     * @param userId 用户ID
     * @return 流式训练模板生成结果
     */
    suspend fun generateWorkoutTemplate(
        prompt: String,
        history: List<ChatMessage> = emptyList(),
        userId: String,
    ): Flow<ModernResult<TemplateDraft>> =
        flow {
            emit(ModernResult.Loading)

            try {
                // 检查模型Function Calling支持
                val providerConfig = aiProviderManager.currentProvider.value
                val supportsFunctionCalling = checkFunctionCallingSupport(providerConfig.name)

                if (supportsFunctionCalling) {
                    Timber.d("使用Function Calling生成模板")
                    generateWithFunctionCalling(prompt, history, userId).collect { result ->
                        emit(result)
                    }
                } else {
                    Timber.d("降级到JSON Prompt生成模板")
                    generateWithJsonPrompt(prompt, history, userId).collect { result ->
                        emit(result)
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "模板生成过程异常")
                emit(
                    ModernResult.Error(
                        FeatureErrors.CoachError.processingFailed(
                            operationName = "generateWorkoutTemplate",
                            message = UiText.DynamicString("训练模板生成失败"),
                            processType = "function_calling",
                            reason = e.message ?: "unknown_error",
                        ),
                    ),
                )
            }
        }

    /**
     * Function Calling方式生成
     *
     * 🔥 【事件总线架构】临时禁用，返回错误信息
     */
    private suspend fun generateWithFunctionCalling(
        prompt: String,
        history: List<ChatMessage>,
        userId: String,
    ): Flow<ModernResult<TemplateDraft>> =
        flow {
            emit(ModernResult.error(
                com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                    operationName = "generateWithFunctionCalling",
                    message = com.example.gymbro.core.ui.text.UiText.DynamicString("Function Calling功能正在迁移到事件总线架构"),
                    metadataMap = mapOf("status" to "migrating")
                )
            ))
        }

    /**
     * JSON Prompt降级方式生成
     *
     * 🔥 【事件总线架构】临时禁用，返回错误信息
     */
    private suspend fun generateWithJsonPrompt(
        prompt: String,
        history: List<ChatMessage>,
        userId: String,
    ): Flow<ModernResult<TemplateDraft>> =
        flow {
            emit(ModernResult.error(
                com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                    operationName = "generateWithJsonPrompt",
                    message = com.example.gymbro.core.ui.text.UiText.DynamicString("JSON Prompt功能正在迁移到事件总线架构"),
                    metadataMap = mapOf("status" to "migrating")
                )
            ))
        }

    /**
     * 处理Function Call返回的模板
     */
    private fun processTemplateFromFunctionCall(
        jsonArgs: String,
        userId: String,
    ): Flow<ModernResult<TemplateDraft>> =
        flow {
            // 第一层：JSON Schema校验
            val validationResult = jsonSchemaValidator.validateWorkoutTemplate(jsonArgs)

            if (validationResult.isValid) {
                // 校验通过，直接解析
                val template = parseTemplateDraft(jsonArgs, userId, DraftSource.AI_GENERATED)
                emit(ModernResult.Success(template))
            } else {
                // 校验失败，尝试重试校正
                Timber.w("Function Call JSON校验失败: ${validationResult.errors}")

                val retryResult = retryWithSchemaCorrection(jsonArgs, validationResult.errors, userId)
                emit(retryResult)
            }
        }

    /**
     * 处理JSON响应中的模板
     */
    private fun processTemplateFromJsonResponse(
        responseText: String,
        userId: String,
    ): Flow<ModernResult<TemplateDraft>> =
        flow {
            // 从响应中提取JSON
            val extractedJson = extractJsonFromResponse(responseText)

            if (extractedJson != null) {
                processTemplateFromFunctionCall(extractedJson, userId).collect { result ->
                    emit(result)
                }
            } else {
                emit(
                    ModernResult.Error(
                        FeatureErrors.CoachError.processingFailed(
                            operationName = "processTemplateFromJsonResponse",
                            message = UiText.DynamicString("无法从响应中提取有效JSON"),
                            processType = "json_extraction",
                            reason = "no_valid_json_found",
                        ),
                    ),
                )
            }
        }

    /**
     * 重试Schema校正
     */
    private fun retryWithSchemaCorrection(
        originalJson: String,
        errors: List<String>,
        userId: String,
    ): ModernResult<TemplateDraft> =
        try {
            // 尝试自动修复
            val fixedJson = jsonSchemaValidator.attemptAutoFix(originalJson, errors)
            val revalidationResult = jsonSchemaValidator.validateWorkoutTemplate(fixedJson)

            if (revalidationResult.isValid) {
                Timber.i("JSON自动修复成功")
                val template = parseTemplateDraft(fixedJson, userId, DraftSource.AI_GENERATED)
                ModernResult.Success(template)
            } else {
                Timber.w("JSON自动修复失败，使用默认模板")
                val fallbackTemplate = createFallbackTemplate(userId, originalJson, errors)
                ModernResult.Success(fallbackTemplate)
            }
        } catch (e: Exception) {
            Timber.e(e, "Schema校正过程异常")
            ModernResult.Error(
                FeatureErrors.CoachError.processingFailed(
                    operationName = "retryWithSchemaCorrection",
                    message = UiText.DynamicString("模板校正失败"),
                    processType = "schema_correction",
                    reason = e.message ?: "correction_failed",
                ),
            )
        }

    /**
     * 解析TemplateDraft
     */
    private fun parseTemplateDraft(
        jsonString: String,
        userId: String,
        source: DraftSource,
    ): TemplateDraft {
        // 这里应该实现完整的JSON到TemplateDraft的转换
        // 为了Sprint 1A，先实现基础版本
        val now =
            kotlinx.datetime.Clock.System
                .now()

        return TemplateDraft(
            name = "AI生成训练模板",
            description = "基于用户需求AI生成的训练模板",
            exercises = emptyList(), // TODO: 实现JSON到ExerciseSetDraft的转换
            source = source,
            createdAt = now,
            updatedAt = now,
            userId = userId,
            aiPrompt = "Function Calling生成",
        )
    }

    /**
     * 创建降级模板
     */
    private fun createFallbackTemplate(
        userId: String,
        originalJson: String,
        errors: List<String>,
    ): TemplateDraft {
        val now =
            kotlinx.datetime.Clock.System
                .now()

        return TemplateDraft(
            name = "AI模板(需要修正)",
            description = "AI生成的模板存在格式问题，请手动调整",
            exercises = emptyList(),
            source = DraftSource.AI_GENERATED,
            createdAt = now,
            updatedAt = now,
            userId = userId,
            notes = "原始JSON: ${originalJson.take(200)}...\n错误: ${errors.joinToString("; ")}",
        )
    }

    /**
     * 检查Function Calling支持
     */
    private fun checkFunctionCallingSupport(providerName: String): Boolean =
        when (providerName.lowercase()) {
            "openai", "gpt-4", "gpt-4o", "gpt-3.5-turbo" -> true
            "deepseek" -> true // DeepSeek支持Function Calling
            "gemini" -> false // Gemini使用不同的Tool系统
            else -> false
        }

    /**
     * 构建JSON Prompt
     */
    private fun buildJsonPrompt(originalPrompt: String): String =
        """
            $originalPrompt

            请以JSON格式返回训练模板，格式如下：
            {
              "template": {
                "name": "模板名称",
                "exercises": [
                  {
                    "code": "动作代码",
                    "sets": 组数(数字),
                    "reps": "次数范围(字符串)",
                    "weight": "bodyweight/percentage/fixed"
                  }
                ]
              }
            }
        """.trimIndent()

    /**
     * 从响应中提取JSON
     */
    private fun extractJsonFromResponse(responseText: String): String? {
        // 使用正则表达式提取JSON部分
        val jsonRegex = Regex("""\{[^{}]*"template"[^{}]*\{.*?\}[^{}]*\}""", RegexOption.DOT_MATCHES_ALL)
        return jsonRegex.find(responseText)?.value
    }

    /**
     * 解析WebSocket响应
     */
    private fun parseWebSocketResponse(rawResponse: String): WebSocketStreamEvent =
        try {
            // 尝试解析为JSON
            val jsonElement = json.parseToJsonElement(rawResponse)
            if (jsonElement is kotlinx.serialization.json.JsonObject) {
                val jsonObject = jsonElement.jsonObject

                // 检查是否是Function Call
                if (jsonObject.containsKey("function_call")) {
                    val functionCall = jsonObject["function_call"]?.jsonObject
                    WebSocketStreamEvent(
                        type = "function_call",
                        functionName = functionCall?.get("name")?.jsonPrimitive?.content,
                        arguments = functionCall?.get("arguments")?.jsonPrimitive?.content,
                    )
                }
                // 检查是否是内容消息
                else if (jsonObject.containsKey("choices")) {
                    val choices = jsonObject["choices"]?.jsonArray
                    val delta =
                        choices
                            ?.get(0)
                            ?.jsonObject
                            ?.get("delta")
                            ?.jsonObject
                    val content = delta?.get("content")?.jsonPrimitive?.content

                    if (content != null) {
                        WebSocketStreamEvent(type = "content", content = content)
                    } else {
                        WebSocketStreamEvent(type = "done")
                    }
                }
                // 检查是否是错误
                else if (jsonObject.containsKey("error")) {
                    val error =
                        jsonObject["error"]
                            ?.jsonObject
                            ?.get("message")
                            ?.jsonPrimitive
                            ?.content
                    WebSocketStreamEvent(type = "error", error = error)
                } else {
                    WebSocketStreamEvent(type = "unknown", content = rawResponse)
                }
            } else {
                // 非JSON响应，当作内容处理
                WebSocketStreamEvent(type = "content", content = rawResponse)
            }
        } catch (e: Exception) {
            Timber.w(e, "解析WebSocket响应失败，当作内容处理")
            WebSocketStreamEvent(type = "content", content = rawResponse)
        }
}

/**
 * WebSocket流事件
 */
private data class WebSocketStreamEvent(
    val type: String,
    val content: String? = null,
    val functionName: String? = null,
    val arguments: String? = null,
    val error: String? = null,
)
