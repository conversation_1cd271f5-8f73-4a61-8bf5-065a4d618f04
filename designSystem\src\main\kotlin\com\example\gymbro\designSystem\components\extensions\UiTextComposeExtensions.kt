package com.example.gymbro.designSystem.components.extensions

import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import com.example.gymbro.core.resources.AndroidResourceProvider
import com.example.gymbro.core.ui.text.UiText

/**
 * UiText的Compose UI扩展
 *
 * 提供与Jetpack Compose相关的UiText扩展方法
 */

/**
 * 在Composable环境中将UiText转换为字符串
 *
 * @return 格式化后的字符串
 */
@Composable
fun UiText.asString(): String {
    val context = LocalContext.current
    val resourceProvider = AndroidResourceProvider(context)

    return when (this) {
        is UiText.StringResource -> {
            // 使用ResourceProvider获取字符串
            resourceProvider.getString(resId, *args.toTypedArray())
        }
        is UiText.DynamicString -> value
        is UiText.ErrorCode -> {
            // ErrorCode类型需要通过ErrorCodeMapper处理，这里提供fallback
            "Error: ${errorCode.code} (${errorCode.category})"
        }
        is UiText.Empty -> ""
    }
}

/**
 * 在Composable环境中将可能为空的UiText转换为字符串，如果为null则返回空字符串
 *
 * @return 格式化后的字符串，如果UiText为null则返回空字符串
 */
@Composable
fun UiText?.asStringOrEmpty(): String = this?.asString() ?: ""
