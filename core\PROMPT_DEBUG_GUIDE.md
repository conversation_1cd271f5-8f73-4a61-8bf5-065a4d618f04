# LayeredPromptBuilder 提示词调试指南

**调试日期**: 2025-06-28  
**调试者**: Claude 4.0 sonnet  
**目标**: 确认LayeredPromptBuilder输出给AI的实际提示词内容  

---

## 🎯 **问题描述**

用户反映PromptRegistry没有传递给PROMPT-BUILDER，提示词还是旧的。需要确认LayeredPromptBuilder到底输出了哪个提示词给AI。

---

## 🔧 **已添加的调试日志**

### **1. 构建开始时的状态检查**
```kotlin
// 在buildChatMessagesWithMemory方法开始处
Timber.tag("PROMPT-BUILDER").e("🔧 开始构建消息，Memory=${memoryContext != null}, Model=${model ?: "default"}")
Timber.tag("PROMPT-BUILDER").e("🔍 [DEBUG] PromptRegistry状态检查开始...")
Timber.tag("PROMPT-BUILDER").e("🔍 [DEBUG] 当前模式: $currentMode")
Timber.tag("PROMPT-BUILDER").e("🔍 [DEBUG] Registry状态: $registryStatus")
```

### **2. 系统提示词获取过程**
```kotlin
// 在获取系统提示词时
Timber.tag("PROMPT-BUILDER").e("🔍 [DEBUG] 准备获取系统提示词...")
val systemPrompt = promptRegistry.getSystemPrompt()
Timber.tag("PROMPT-BUILDER").e("🔍 [DEBUG] 系统提示词获取完成，长度: ${systemPrompt.length}")
```

### **3. 详细的提示词信息**
```kotlin
// 详细的提示词状态
Timber.tag("PROMPT-BUILDER").e("🔍 使用PromptRegistry配置:")
Timber.tag("PROMPT-BUILDER").e("📊 Registry状态: $registryStatus")
Timber.tag("PROMPT-BUILDER").e("🎯 当前配置ID: ${promptRegistry.getCurrentMode()}")
Timber.tag("PROMPT-BUILDER").e("📄 系统提示词长度: ${systemPrompt.length}字符")
Timber.tag("PROMPT-BUILDER").e("📄 提示词前200字符: ${systemPrompt.take(200)}")
Timber.tag("PROMPT-BUILDER").e("📄 提示词哈希值: ${systemPrompt.hashCode()}")
```

### **4. 完整提示词内容输出**
```kotlin
// 在validateSystemPrompt方法中
val chunks = systemPrompt.chunked(500)
chunks.forEachIndexed { index, chunk ->
    Timber.tag("PROMPT-BUILDER").e("📄 系统提示词-第${index + 1}块/${chunks.size}: $chunk")
}
```

---

## 📋 **调试步骤**

### **第一步：启动应用并触发AI对话**
1. 启动GymBro应用
2. 进入AI Coach界面
3. 发送任意消息触发AI响应

### **第二步：查看logcat输出**
使用以下命令过滤关键日志：
```bash
adb logcat -s PROMPT-BUILDER:E
```

### **第三步：关键日志检查点**

#### **A. 构建开始日志**
```
PROMPT-BUILDER: 🔧 开始构建消息，Memory=false, Model=default
PROMPT-BUILDER: 🔍 [DEBUG] PromptRegistry状态检查开始...
PROMPT-BUILDER: 🔍 [DEBUG] 当前模式: standard
PROMPT-BUILDER: 🔍 [DEBUG] Registry状态: [具体状态信息]
```

#### **B. 系统提示词获取日志**
```
PROMPT-BUILDER: 🔍 [DEBUG] 准备获取系统提示词...
PROMPT-BUILDER: 🔍 [DEBUG] 系统提示词获取完成，长度: XXXX
PROMPT-BUILDER: 📄 系统提示词长度: XXXX字符
PROMPT-BUILDER: 📄 提示词前200字符: [实际内容]
PROMPT-BUILDER: 📄 提示词哈希值: XXXXXXXX
```

#### **C. 完整提示词内容**
```
PROMPT-BUILDER: 📄 系统提示词-第1块/X: [第一块内容]
PROMPT-BUILDER: 📄 系统提示词-第2块/X: [第二块内容]
...
```

---

## 🔍 **判断标准**

### **✅ 正常情况**
- 当前模式显示为"standard"
- 系统提示词长度 > 500字符
- 提示词内容包含最新的配置信息
- 哈希值与预期一致

### **❌ 异常情况**
- 当前模式显示为其他值
- 系统提示词长度 < 500字符
- 提示词内容是fallback配置
- 出现"系统提示词异常短"警告

### **🔧 自动修复机制**
如果检测到异常，LayeredPromptBuilder会自动执行：
1. 检查并更新配置文件版本
2. 强制重新加载standard配置
3. 强制同步assets文件

---

## 📊 **预期输出示例**

### **正常的日志输出**
```
PROMPT-BUILDER: 🔧 开始构建消息，Memory=false, Model=default
PROMPT-BUILDER: 🔍 [DEBUG] 当前模式: standard
PROMPT-BUILDER: 📄 系统提示词长度: 2847字符
PROMPT-BUILDER: 📄 提示词前200字符: 你的所有行为都必须严格遵循 `研究 -> 构思 -> 计划 -> 执行 -> 评审` 的核心工作流。现在时间为2025/06/16...
PROMPT-BUILDER: 📄 系统提示词-第1块/6: [完整的第一块内容]
```

### **异常的日志输出**
```
PROMPT-BUILDER: ⚠️ 系统提示词异常短(234字符)，可能使用了fallback配置
PROMPT-BUILDER: 🔄 第一步：检查并更新配置文件版本...
PROMPT-BUILDER: 🔄 第二步：尝试强制重新加载standard配置...
```

---

## 🎯 **下一步行动**

1. **运行应用**: 启动GymBro并触发AI对话
2. **收集日志**: 使用adb logcat收集PROMPT-BUILDER日志
3. **分析结果**: 根据上述标准判断提示词是否正确
4. **确认问题**: 如果提示词确实是旧的，进一步调试PromptRegistry

---

## 📝 **注意事项**

- 所有关键日志都使用ERROR级别，确保在任何日志配置下都能看到
- 提示词内容会分块输出，避免单行过长被截断
- 哈希值可以用来快速判断提示词是否发生变化
- 自动修复机制会在检测到问题时自动尝试修复

通过这些调试日志，我们可以准确确定LayeredPromptBuilder到底输出了什么提示词给AI！
