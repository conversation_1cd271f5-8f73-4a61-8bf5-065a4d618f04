# StarRingCanvas Component Documentation

## Overview

The `StarRingCanvas` is a high-performance animated canvas component that renders a star-ring themed animation with a black hole effect. It features performance optimizations, customizable visual effects, and adaptive rendering based on device capabilities.

## Key Features

- **Performance Optimized**: Automatically adapts animation complexity based on device performance
- **Customizable Black Hole**: Adjustable size, color, gradients, and shadow effects
- **Orbiting Lines**: Configurable lines that orbit around the black hole
- **Flowing Animations**: Two types of flowing line animations (circumnavigating and crossing)
- **Theme Integration**: Supports GymBro's theme system with predefined presets
- **60fps Frame Rate**: Optimized rendering with frame rate limiting
- **Memory Efficient**: Pre-calculated values and object pooling to minimize GC pressure

## Basic Usage

```kotlin
@Composable
fun BasicStarRingCanvas() {
    StarRingCanvas(
        modifier = Modifier.fillMaxSize(),
        enableAnimation = true
    )
}
```

## Advanced Usage Examples

### Custom Black Hole Configuration

```kotlin
@Composable
fun CustomBlackHole() {
    StarRingCanvas(
        modifier = Modifier.fillMaxSize(),
        blackHoleSizeRatio = 0.4f,  // 40% of canvas size
        coreColor = Color(0xFF6A0DAD),  // Purple core
        shadowIntensity = 0.9f,  // Strong shadows
        gradientStops = 20,  // More gradient layers
        enableEventHorizon = true  // Show event horizon effect
    )
}
```

### Custom Orbiting Lines

```kotlin
@Composable
fun CustomOrbitingLines() {
    val customConfig = OrbitingLineConfig(
        lines = listOf(
            // Outer blue ring
            OrbitingLine(
                positionAngle = 0f,
                orbitalRadius = 200f,
                length = 100f,
                thickness = 4f,
                color = Color.Blue,
                opacity = 0.8f,
                animationPhaseOffset = 0f
            ),
            // Inner red ring
            OrbitingLine(
                positionAngle = 180f,
                orbitalRadius = 150f,
                length = 80f,
                thickness = 3f,
                color = Color.Red,
                opacity = 0.7f,
                animationPhaseOffset = 45f
            )
        ),
        animationSpeed = 1.5f,
        enableEllipticalOrbits = true,
        ellipticalRatio = 0.8f
    )
    
    StarRingCanvas(
        modifier = Modifier.fillMaxSize(),
        orbitingLineConfig = customConfig,
        enableAnimation = true
    )
}
```

### Performance Monitoring

```kotlin
@Composable
fun PerformanceMonitoredCanvas() {
    StarRingCanvas(
        modifier = Modifier.fillMaxSize(),
        enableAnimation = true,
        enablePerformanceMonitoring = true  // Enable performance tracking
    )
}
```

### Theme Integration

```kotlin
@Composable
fun ThemedStarRingCanvas() {
    GymBroTheme(darkTheme = true) {
        starRingTheme(spec = StarRingThemePresets.MinimalDark) {
            StarRingCanvas(
                modifier = Modifier.fillMaxSize(),
                enableAnimation = true
            )
        }
    }
}
```

### Static Display (No Animation)

```kotlin
@Composable
fun StaticStarRingCanvas() {
    StarRingCanvas(
        modifier = Modifier.fillMaxSize(),
        enableAnimation = false,  // Disable all animations
        blackHoleSizeRatio = 0.3f,
        numberOfLines = 10
    )
}
```

### External Animation Control

```kotlin
@Composable
fun ControlledAnimation() {
    val animationState = rememberAnimationState(
        isAnimating = false,
        initialRotation = 45f,
        initialShimmer = 0.5f
    )
    
    StarRingCanvas(
        modifier = Modifier.fillMaxSize(),
        animationState = animationState
    )
    
    // Control animation externally
    Button(onClick = { 
        // Toggle animation state
    }) {
        Text("Toggle Animation")
    }
}
```

## API Reference

### StarRingCanvas Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `modifier` | Modifier | Modifier | Modifier for the canvas |
| `canvasSize` | Size | Size.Unspecified | Explicit canvas size |
| `blackHoleSizeRatio` | Float | 0.32f | Black hole size relative to canvas (0.0-1.0) |
| `gradientColors` | List<Color> | [Black, Gray] | Colors for gradient effects |
| `numberOfLines` | Int | 8 | Number of orbiting lines |
| `animationSpeed` | Float | 1.0f | Animation speed multiplier |
| `animationDirection` | Float | 1.0f | Animation direction (1.0 or -1.0) |
| `lineThickness` | Float | 2.0f | Default line thickness |
| `lineColor` | Color | White | Default line color |
| `lineGlowIntensity` | Float | 0.5f | Glow effect intensity |
| `enableAnimation` | Boolean | true | Enable/disable animations |
| `enablePerformanceMonitoring` | Boolean | false | Enable performance tracking |
| `shadowIntensity` | Float | 0.7f | Shadow effect intensity |
| `gradientStops` | Int | 10 | Number of gradient transitions |
| `coreColor` | Color | Black | Black hole core color |
| `enableEventHorizon` | Boolean | true | Show event horizon effect |
| `orbitingLineConfig` | OrbitingLineConfig? | null | Custom line configuration |
| `animationState` | AnimationState | rememberAnimationState() | Animation control state |

### Data Classes

#### OrbitingLine
```kotlin
data class OrbitingLine(
    val positionAngle: Float,       // Starting angle (0-360°)
    val orbitalRadius: Float,       // Distance from center
    val length: Float,              // Line length in pixels
    val thickness: Float,           // Line thickness
    val color: Color,               // Line color
    val opacity: Float,             // Opacity (0.0-1.0)
    val animationPhaseOffset: Float // Animation phase offset
)
```

#### OrbitingLineConfig
```kotlin
data class OrbitingLineConfig(
    val lines: List<OrbitingLine>,
    val animationSpeed: Float = 1.0f,
    val enableEllipticalOrbits: Boolean = false,
    val ellipticalRatio: Float = 1.0f
)
```

#### AnimationState
```kotlin
data class AnimationState(
    val isAnimating: Boolean = true,
    val rotation: Float = 0f,
    val shimmer: Float = 0f
)
```

## Performance Optimization

The component automatically detects device performance and adjusts rendering accordingly:

### Performance Levels

1. **LOW** (< HD resolution)
   - 4 render layers
   - 20 base particles
   - 4-second shimmer cycle
   - Gradients disabled

2. **MEDIUM** (HD to FHD)
   - 6 render layers
   - 35 base particles
   - 3.5-second shimmer cycle
   - Gradients enabled

3. **HIGH** (FHD and above)
   - 8 render layers
   - 50 base particles
   - 3-second shimmer cycle
   - Full effects enabled

### Optimization Techniques

- **Frame Rate Limiting**: Capped at 60fps
- **Object Pooling**: Pre-calculated color lists
- **Derived State**: Efficient state management
- **Selective Rendering**: Only visible segments drawn
- **Canvas Layer Optimization**: Minimal overdraw

## Best Practices

1. **Use appropriate size**: Keep `blackHoleSizeRatio` between 0.2-0.5 for best visual results
2. **Limit orbiting lines**: 8-15 lines provide good visual density without performance impact
3. **Theme integration**: Use `starRingTheme` for consistent styling
4. **Animation control**: Disable animations when not visible to save battery
5. **Performance monitoring**: Enable only during development/debugging

## Troubleshooting

### Animation not smooth
- Check device performance level
- Reduce `numberOfLines`
- Lower `gradientStops`
- Disable `enableEventHorizon`

### High memory usage
- Reduce `blackHoleSizeRatio`
- Lower animation complexity
- Check for memory leaks in parent composables

### Visual artifacts
- Ensure proper theme setup
- Check color contrast ratios
- Verify canvas size is appropriate

## Examples in Context

### Launch Screen
```kotlin
@Composable
fun LaunchScreen() {
    Box(modifier = Modifier.fillMaxSize()) {
        // Background animation
        StarRingCanvas(
            modifier = Modifier.fillMaxSize(),
            blackHoleSizeRatio = 0.4f,
            enableAnimation = true
        )
        
        // Logo overlay
        Image(
            painter = painterResource(R.drawable.logo),
            contentDescription = "Logo",
            modifier = Modifier
                .align(Alignment.Center)
                .size(200.dp)
        )
    }
}
```

### Loading Indicator
```kotlin
@Composable
fun LoadingIndicator() {
    Box(
        modifier = Modifier
            .size(100.dp)
            .clip(CircleShape)
    ) {
        StarRingCanvas(
            modifier = Modifier.fillMaxSize(),
            blackHoleSizeRatio = 0.6f,
            numberOfLines = 4,
            enableAnimation = true
        )
    }
}
```

## Related Components

- `StarRingTheme`: Theme configuration for star-ring components
- `GymBroTheme`: Main application theme
- `GymBroPreview`: Preview annotation for design system components
