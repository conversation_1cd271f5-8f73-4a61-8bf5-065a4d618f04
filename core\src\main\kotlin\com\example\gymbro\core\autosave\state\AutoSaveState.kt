package com.example.gymbro.core.autosave.state

/**
 * 自动保存状态
 *
 * @param T 要保存的数据类型
 */
data class AutoSaveState<T : Any>(
    val isActive: Boolean = false,
    val isSaving: Boolean = false,
    val hasUnsavedChanges: Boolean = false,
    val lastSaveTime: Long = 0L,
    val lastUpdateTime: Long = 0L,
    val saveCount: Int = 0,
    val retryCount: Int = 0,
    val error: Throwable? = null,
    val cachedVersion: T? = null,
    val showRecoveryDialog: Boolean = false,
) {

    /**
     * 是否有错误
     */
    val hasError: Boolean get() = error != null

    /**
     * 是否可以保存
     */
    val canSave: Boolean get() = isActive && !isSaving && hasUnsavedChanges

    /**
     * 是否有缓存可恢复
     */
    val hasRecoverableCache: Boolean get() = cachedVersion != null

    /**
     * 保存状态描述
     */
    val statusDescription: String get() = when {
        !isActive -> "未激活"
        isSaving -> "保存中..."
        hasError -> "保存失败"
        hasUnsavedChanges -> "有未保存的更改"
        else -> "已保存"
    }

    /**
     * 清除错误状态
     */
    fun clearError(): AutoSaveState<T> = copy(error = null, retryCount = 0)

    /**
     * 重置状态
     */
    fun reset(): AutoSaveState<T> = AutoSaveState()
}
