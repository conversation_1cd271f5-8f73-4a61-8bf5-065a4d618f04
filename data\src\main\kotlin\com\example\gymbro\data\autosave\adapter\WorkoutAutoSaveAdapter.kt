package com.example.gymbro.data.autosave.adapter

import com.example.gymbro.core.autosave.config.AutoSaveConfig
import com.example.gymbro.core.autosave.storage.AutoSaveStorage
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.data.autosave.AutoSaveRepository
import com.example.gymbro.data.autosave.AutoSaveSession
import com.example.gymbro.data.autosave.strategy.IntervalSaveStrategy
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.domain.workout.repository.TemplateRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.serialization.json.Json
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Workout模块自动保存适配器
 *
 * 🎯 功能特性：
 * - 为Workout模块提供WorkoutTemplate自动保存功能
 * - 集成新的四数据库架构TemplateRepository
 * - 使用定时保存策略（3秒间隔），适合频繁编辑的场景
 * - 支持WorkoutTemplate类型的序列化和反序列化
 * - 支持缓存恢复和版本冲突检测
 *
 * @param autoSaveRepository 自动保存仓库
 * @param templateRepository 模板仓库
 * @param json Json序列化器
 * @param logger 日志记录器
 */
@Singleton
class WorkoutAutoSaveAdapter @Inject constructor(
    private val autoSaveRepository: AutoSaveRepository,
    private val templateRepository: TemplateRepository,
    private val json: Json,
    private val logger: Logger,
) {

    companion object {
        private const val CONFIG_ID = "workout_autosave"
        private const val SAVE_INTERVAL_MS = 3000L // 3秒间隔
    }

    /**
     * 创建Workout自动保存会话
     *
     * @param templateId 模板ID
     * @param scope 协程作用域
     * @return 自动保存会话ID
     */
    suspend fun createAutoSave(
        templateId: String,
        scope: CoroutineScope,
    ): ModernResult<String> {
        return try {
            logger.d("WorkoutAutoSaveAdapter", "创建Workout自动保存会话: $templateId")

            // 创建存储后端
            val storage = createWorkoutStorage(templateId)

            // 创建保存策略（定时保存，3秒间隔）
            val strategy = IntervalSaveStrategy.create<WorkoutTemplate>(logger, SAVE_INTERVAL_MS)

            // 创建自动保存配置
            val config = AutoSaveConfig(
                id = "$CONFIG_ID$templateId",
                strategy = strategy,
                storage = storage,
                enableRecovery = true,
            )

            // 创建会话
            val sessionResult = autoSaveRepository.createSession(config, scope)

            when (sessionResult) {
                is ModernResult.Success -> {
                    logger.d("WorkoutAutoSaveAdapter", "Workout自动保存会话创建成功: ${sessionResult.data}")
                    sessionResult
                }
                is ModernResult.Error -> {
                    logger.e("WorkoutAutoSaveAdapter", "创建Workout自动保存会话失败", sessionResult.error.cause)
                    sessionResult
                }
                is ModernResult.Loading -> {
                    logger.d("WorkoutAutoSaveAdapter", "Workout自动保存会话创建中...")
                    sessionResult
                }
            }
        } catch (e: Exception) {
            logger.e(e, "创建Workout自动保存会话异常: $templateId")
            ModernResult.Error(
                com.example.gymbro.core.error.types.ModernDataError(
                    operationName = "WorkoutAutoSaveAdapter.createAutoSave",
                    errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                    category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                    cause = e,
                ),
            )
        }
    }

    /**
     * 获取Workout自动保存会话
     *
     * @param sessionId 会话ID
     * @return 自动保存会话
     */
    fun getAutoSaveSession(sessionId: String): AutoSaveSession<WorkoutTemplate>? {
        return try {
            val session = autoSaveRepository.getSession<WorkoutTemplate>(sessionId)
            if (session != null) {
                logger.d("WorkoutAutoSaveAdapter", "获取Workout自动保存会话成功: $sessionId")
            } else {
                logger.w("WorkoutAutoSaveAdapter", "未找到Workout自动保存会话: $sessionId")
            }
            session
        } catch (e: Exception) {
            logger.e(e, "获取Workout自动保存会话失败: $sessionId")
            null
        }
    }

    /**
     * 启动Workout自动保存
     *
     * @param sessionId 会话ID
     * @param templateId 模板ID
     */
    suspend fun startAutoSave(sessionId: String, templateId: String) {
        try {
            logger.d("WorkoutAutoSaveAdapter", "启动Workout自动保存: $sessionId")

            val session = getAutoSaveSession(sessionId)
            if (session == null) {
                logger.e("WorkoutAutoSaveAdapter", "会话不存在，无法启动自动保存: $sessionId")
                return
            }

            // 获取当前模板作为初始数据
            val templateResult = templateRepository.getTemplateById(templateId)
            when (templateResult) {
                is ModernResult.Success -> {
                    val template = templateResult.data
                    if (template != null) {
                        session.start(template)
                        logger.d("WorkoutAutoSaveAdapter", "Workout自动保存启动成功: $sessionId")
                    } else {
                        logger.e("WorkoutAutoSaveAdapter", "模板不存在，无法启动自动保存: $templateId")
                    }
                }
                is ModernResult.Error -> {
                    logger.e("WorkoutAutoSaveAdapter", "获取模板失败，无法启动自动保存", templateResult.error.cause)
                }
                is ModernResult.Loading -> {
                    logger.d("WorkoutAutoSaveAdapter", "正在获取模板...")
                }
            }
        } catch (e: Exception) {
            logger.e(e, "启动Workout自动保存异常: $sessionId")
        }
    }

    /**
     * 更新WorkoutTemplate数据
     *
     * @param sessionId 会话ID
     * @param template 训练模板
     */
    fun updateTemplate(sessionId: String, template: WorkoutTemplate) {
        try {
            val session = getAutoSaveSession(sessionId)
            if (session != null) {
                session.update(template)
                logger.d("WorkoutAutoSaveAdapter", "WorkoutTemplate数据已更新: $sessionId")
            } else {
                logger.w("WorkoutAutoSaveAdapter", "会话不存在，无法更新WorkoutTemplate: $sessionId")
            }
        } catch (e: Exception) {
            logger.e(e, "更新WorkoutTemplate数据失败: $sessionId")
        }
    }

    /**
     * 立即保存WorkoutTemplate
     *
     * @param sessionId 会话ID
     */
    suspend fun saveNow(sessionId: String): Result<Unit> {
        return try {
            val session = getAutoSaveSession(sessionId)
            if (session != null) {
                val result = session.saveNow()
                logger.d("WorkoutAutoSaveAdapter", "WorkoutTemplate立即保存完成: $sessionId")
                result
            } else {
                logger.w("WorkoutAutoSaveAdapter", "会话不存在，无法立即保存: $sessionId")
                Result.failure(IllegalStateException("会话不存在"))
            }
        } catch (e: Exception) {
            logger.e(e, "WorkoutTemplate立即保存失败: $sessionId")
            Result.failure(e)
        }
    }

    /**
     * 暂停自动保存
     *
     * @param sessionId 会话ID
     */
    fun pauseAutoSave(sessionId: String) {
        try {
            val session = getAutoSaveSession(sessionId)
            session?.pause()
            logger.d("WorkoutAutoSaveAdapter", "Workout自动保存已暂停: $sessionId")
        } catch (e: Exception) {
            logger.e(e, "暂停Workout自动保存失败: $sessionId")
        }
    }

    /**
     * 恢复自动保存
     *
     * @param sessionId 会话ID
     */
    fun resumeAutoSave(sessionId: String) {
        try {
            val session = getAutoSaveSession(sessionId)
            session?.resume()
            logger.d("WorkoutAutoSaveAdapter", "Workout自动保存已恢复: $sessionId")
        } catch (e: Exception) {
            logger.e(e, "恢复Workout自动保存失败: $sessionId")
        }
    }

    /**
     * 停止Workout自动保存
     *
     * @param sessionId 会话ID
     */
    suspend fun stopAutoSave(sessionId: String) {
        try {
            val result = autoSaveRepository.deleteSession(sessionId)
            when (result) {
                is ModernResult.Success -> {
                    logger.d("WorkoutAutoSaveAdapter", "Workout自动保存已停止: $sessionId")
                }
                is ModernResult.Error -> {
                    logger.e("WorkoutAutoSaveAdapter", "停止Workout自动保存失败", result.error.cause)
                }
                is ModernResult.Loading -> {
                    logger.d("WorkoutAutoSaveAdapter", "正在停止Workout自动保存...")
                }
            }
        } catch (e: Exception) {
            logger.e(e, "停止Workout自动保存异常: $sessionId")
        }
    }

    /**
     * 恢复缓存数据
     *
     * @param sessionId 会话ID
     */
    fun restoreFromCache(sessionId: String) {
        try {
            val session = getAutoSaveSession(sessionId)
            session?.restoreFromCache()
            logger.d("WorkoutAutoSaveAdapter", "Workout缓存数据恢复: $sessionId")
        } catch (e: Exception) {
            logger.e(e, "恢复Workout缓存数据失败: $sessionId")
        }
    }

    /**
     * 丢弃缓存数据
     *
     * @param sessionId 会话ID
     */
    fun discardCache(sessionId: String) {
        try {
            val session = getAutoSaveSession(sessionId)
            session?.discardCache()
            logger.d("WorkoutAutoSaveAdapter", "Workout缓存数据已丢弃: $sessionId")
        } catch (e: Exception) {
            logger.e(e, "丢弃Workout缓存数据失败: $sessionId")
        }
    }

    /**
     * 创建Workout存储后端
     */
    private fun createWorkoutStorage(templateId: String): AutoSaveStorage<WorkoutTemplate> {
        return object : AutoSaveStorage<WorkoutTemplate> {
            override suspend fun save(id: String, data: WorkoutTemplate) {
                logger.d("WorkoutAutoSaveAdapter", "保存WorkoutTemplate到Repository: ${data.id}")

                val result = templateRepository.saveTemplate(data)
                when (result) {
                    is ModernResult.Success -> {
                        logger.d("WorkoutAutoSaveAdapter", "WorkoutTemplate保存成功: ${data.id}")
                    }
                    is ModernResult.Error -> {
                        logger.e("WorkoutAutoSaveAdapter", "WorkoutTemplate保存失败", result.error.cause)
                        throw Exception("WorkoutTemplate保存失败: ${result.error}")
                    }
                    is ModernResult.Loading -> {
                        logger.d("WorkoutAutoSaveAdapter", "WorkoutTemplate保存中...")
                    }
                }
            }

            override suspend fun restore(id: String): WorkoutTemplate? {
                logger.d("WorkoutAutoSaveAdapter", "从Repository恢复WorkoutTemplate: $templateId")

                val result = templateRepository.getTemplateById(templateId)
                return when (result) {
                    is ModernResult.Success -> {
                        logger.d("WorkoutAutoSaveAdapter", "WorkoutTemplate恢复成功: $templateId")
                        result.data
                    }
                    is ModernResult.Error -> {
                        logger.e("WorkoutAutoSaveAdapter", "WorkoutTemplate恢复失败", result.error.cause)
                        null
                    }
                    is ModernResult.Loading -> {
                        logger.d("WorkoutAutoSaveAdapter", "WorkoutTemplate恢复中...")
                        null
                    }
                }
            }

            override suspend fun clear(id: String) {
                logger.d("WorkoutAutoSaveAdapter", "清除WorkoutTemplate: $id")
                // 可以选择删除模板或只记录日志
                try {
                    templateRepository.deleteTemplate(templateId)
                } catch (e: Exception) {
                    logger.e(e, "清除WorkoutTemplate失败: $templateId")
                }
            }

            override suspend fun exists(id: String): Boolean {
                val result = templateRepository.getTemplateById(templateId)
                return when (result) {
                    is ModernResult.Success -> result.data != null
                    else -> false
                }
            }

            override suspend fun getSize(id: String): Long {
                return try {
                    val template = restore(id)
                    if (template != null) {
                        // TODO: 需要为WorkoutTemplate添加序列化支持
                        // json.encodeToString(template).length.toLong()
                        // 暂时返回估算大小
                        1024L // 1KB估算
                    } else {
                        0L
                    }
                } catch (e: Exception) {
                    0L
                }
            }
        }
    }
}
