package com.example.gymbro.data.integration

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.data.ai.monitoring.AiMetricsCollector
import com.example.gymbro.data.local.datastore.PreferencesDataStore
import com.example.gymbro.data.repository.user.UserPreferenceRepositoryImpl
import com.example.gymbro.data.service.AiInteractionServiceImpl
import com.example.gymbro.domain.profile.model.user.FitnessGoal
import com.example.gymbro.domain.profile.model.user.FitnessPreference
import com.example.gymbro.domain.profile.model.user.WeekDay
import com.example.gymbro.domain.profile.usecase.GetUserProfileUseCase
import com.example.gymbro.domain.workout.model.DraftSource
import com.example.gymbro.domain.workout.model.TemplateDraft
import com.example.gymbro.domain.workout.repository.WorkoutDraftRepository
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import kotlinx.datetime.Clock
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

/**
 * 用户偏好集成测试
 *
 * 测试 DataStore → Repository → Service 的集成流程
 * Phase 5 Task 5: E2E 测试（单元测试版本）
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class UserPreferenceIntegrationTest {
    private lateinit var preferencesDataStore: PreferencesDataStore
    private lateinit var userPreferenceRepository: UserPreferenceRepositoryImpl
    private lateinit var aiInteractionService: AiInteractionServiceImpl
    private lateinit var aiMetricsCollector: AiMetricsCollector
    private lateinit var workoutDraftRepository: WorkoutDraftRepository
    private lateinit var getUserProfileUseCase: GetUserProfileUseCase

    private val testDispatcher = StandardTestDispatcher()
    private val json = Json { ignoreUnknownKeys = true }

    @BeforeEach
    fun setUp() {
        // Mock dependencies
        preferencesDataStore = mockk()
        workoutDraftRepository = mockk()
        getUserProfileUseCase = mockk()

        // Real implementations
        aiMetricsCollector = AiMetricsCollector()

        userPreferenceRepository =
            UserPreferenceRepositoryImpl(
                getUserProfileUseCase = getUserProfileUseCase,
                preferencesDataStore = preferencesDataStore,
                userBackupService = mockk(),
            )

        aiInteractionService =
            AiInteractionServiceImpl(
                workoutDraftRepository = workoutDraftRepository,
                userPreferencePort = userPreferenceRepository,
                aiMetricsCollector = aiMetricsCollector,
                json = json,
                ioDispatcher = testDispatcher,
            )

        // Reset metrics
        aiMetricsCollector.resetMetrics()
    }

    @Test
    fun `integration test - strength workout preference flow`() =
        runTest(testDispatcher) {
            // Given - 力量训练偏好
            val preference =
                FitnessPreference(
                    primaryGoal = FitnessGoal.STRENGTH,
                    workoutDays = setOf(WeekDay.MONDAY, WeekDay.WEDNESDAY, WeekDay.FRIDAY),
                )

            val mockTemplate =
                createMockTemplate(
                    name = "力量训练计划",
                    description = "专注于杠铃深蹲和卧推的力量训练",
                )

            // Mock DataStore behavior
            coEvery { preferencesDataStore.getFitnessPreferenceFlow() } returns flowOf(preference)
            coEvery { preferencesDataStore.updateFitnessPreference(any()) } returns Unit
            coEvery { workoutDraftRepository.generateTemplateDraftWithAI(any(), any(), any()) } returns
                flowOf(ModernResult.Success(mockTemplate))

            // When - 更新偏好并生成模板
            userPreferenceRepository.updateFitnessPreference(preference)

            val storedPreference = userPreferenceRepository.current()
            assertEquals(FitnessGoal.STRENGTH, storedPreference.primaryGoal)

            val result =
                aiInteractionService.generateWorkoutTemplateFromPrompt(
                    userId = "test_user",
                    prompt = "制定力量训练计划",
                    context = null,
                )

            // Then - 验证结果
            assertTrue(result is ModernResult.Success)
            val template = (result as ModernResult.Success).data
            assertEquals("力量训练计划", template.name)

            // 验证目标匹配
            val isMatched = aiMetricsCollector.analyzeGoalMatch(template, preference)
            assertTrue("力量训练模板应该匹配", isMatched)

            // 验证指标收集
            val metrics = aiMetricsCollector.getMetricsSnapshot()
            assertEquals(1, metrics.totalRequests)
            assertEquals(1.0, metrics.goalMatchRate, 0.01)
            assertEquals(1.0, metrics.preferenceInjectionRate, 0.01)
        }

    @Test
    fun `integration test - weight loss preference flow`() =
        runTest(testDispatcher) {
            // Given - 减脂偏好
            val preference =
                FitnessPreference(
                    primaryGoal = FitnessGoal.WEIGHT_LOSS,
                    workoutDays = setOf(WeekDay.TUESDAY, WeekDay.THURSDAY, WeekDay.SATURDAY),
                )

            val mockTemplate =
                createMockTemplate(
                    name = "减脂有氧训练",
                    description = "高强度有氧运动，快速燃脂减重",
                )

            // Mock DataStore behavior
            coEvery { preferencesDataStore.getFitnessPreferenceFlow() } returns flowOf(preference)
            coEvery { preferencesDataStore.updateFitnessPreference(any()) } returns Unit
            coEvery { workoutDraftRepository.generateTemplateDraftWithAI(any(), any(), any()) } returns
                flowOf(ModernResult.Success(mockTemplate))

            // When - 执行减脂流程
            userPreferenceRepository.updateFitnessPreference(preference)

            val result =
                aiInteractionService.generateWorkoutTemplateFromPrompt(
                    userId = "test_user",
                    prompt = "制定减脂计划",
                    context = null,
                )

            // Then - 验证减脂结果
            assertTrue(result is ModernResult.Success)
            val template = (result as ModernResult.Success).data

            val isMatched = aiMetricsCollector.analyzeGoalMatch(template, preference)
            assertTrue("减脂模板应该匹配", isMatched)

            val metrics = aiMetricsCollector.getMetricsSnapshot()
            assertTrue("应该有目标匹配记录", metrics.goalMatchesByType.containsKey("weight_loss"))
        }

    @Test
    fun `integration test - muscle gain preference flow`() =
        runTest(testDispatcher) {
            // Given - 增肌偏好
            val preference =
                FitnessPreference(
                    primaryGoal = FitnessGoal.MUSCLE_GAIN,
                    workoutDays = setOf(WeekDay.MONDAY, WeekDay.TUESDAY, WeekDay.THURSDAY, WeekDay.FRIDAY),
                )

            val mockTemplate =
                createMockTemplate(
                    name = "增肌健美训练",
                    description = "专注于肌肉增长和塑形的重量训练",
                )

            // Mock DataStore behavior
            coEvery { preferencesDataStore.getFitnessPreferenceFlow() } returns flowOf(preference)
            coEvery { preferencesDataStore.updateFitnessPreference(any()) } returns Unit
            coEvery { workoutDraftRepository.generateTemplateDraftWithAI(any(), any(), any()) } returns
                flowOf(ModernResult.Success(mockTemplate))

            // When - 执行增肌流程
            userPreferenceRepository.updateFitnessPreference(preference)

            val result =
                aiInteractionService.generateWorkoutTemplateFromPrompt(
                    userId = "test_user",
                    prompt = "制定增肌计划",
                    context = null,
                )

            // Then - 验证增肌结果
            assertTrue(result is ModernResult.Success)
            val template = (result as ModernResult.Success).data

            val isMatched = aiMetricsCollector.analyzeGoalMatch(template, preference)
            assertTrue("增肌模板应该匹配", isMatched)

            val metrics = aiMetricsCollector.getMetricsSnapshot()
            assertTrue("应该有目标匹配记录", metrics.goalMatchesByType.containsKey("muscle_gain"))
        }

    @Test
    fun `integration test - no preference handling`() =
        runTest(testDispatcher) {
            // Given - 无偏好
            val emptyPreference = FitnessPreference()
            val mockTemplate =
                createMockTemplate(
                    name = "基础训练计划",
                    description = "适合初学者的综合训练",
                )

            // Mock DataStore behavior
            coEvery { preferencesDataStore.getFitnessPreferenceFlow() } returns flowOf(emptyPreference)
            coEvery { workoutDraftRepository.generateTemplateDraftWithAI(any(), any(), any()) } returns
                flowOf(ModernResult.Success(mockTemplate))

            // When - 无偏好情况下生成模板
            val result =
                aiInteractionService.generateWorkoutTemplateFromPrompt(
                    userId = "test_user",
                    prompt = "制定训练计划",
                    context = null,
                )

            // Then - 验证无偏好处理
            assertTrue(result is ModernResult.Success)

            val metrics = aiMetricsCollector.getMetricsSnapshot()
            // 无偏好时，偏好注入应该记录为失败
            assertTrue("应该记录偏好注入状态", metrics.preferenceInjectionRate >= 0)
        }

    @Test
    fun `integration test - metrics collection across multiple requests`() =
        runTest(testDispatcher) {
            // Given - 多个不同的偏好
            val preferences =
                listOf(
                    FitnessPreference(primaryGoal = FitnessGoal.STRENGTH),
                    FitnessPreference(primaryGoal = FitnessGoal.WEIGHT_LOSS),
                    FitnessPreference(primaryGoal = FitnessGoal.MUSCLE_GAIN),
                )

            val mockTemplates =
                listOf(
                    createMockTemplate("力量训练", "杠铃深蹲卧推"),
                    createMockTemplate("减脂训练", "有氧跑步燃脂"),
                    createMockTemplate("增肌训练", "肌肉塑形重量"),
                )

            // When - 执行多个请求
            preferences.forEachIndexed { index, preference ->
                coEvery { preferencesDataStore.getFitnessPreferenceFlow() } returns flowOf(preference)
                coEvery { workoutDraftRepository.generateTemplateDraftWithAI(any(), any(), any()) } returns
                    flowOf(ModernResult.Success(mockTemplates[index]))

                aiInteractionService.generateWorkoutTemplateFromPrompt(
                    userId = "test_user_$index",
                    prompt = "制定训练计划",
                    context = null,
                )
            }

            // Then - 验证指标聚合
            val metrics = aiMetricsCollector.getMetricsSnapshot()
            assertEquals(3, metrics.totalRequests)
            assertTrue("应该有多种目标类型", metrics.goalMatchesByType.size > 0)
            assertTrue("平均响应时间应合理", metrics.averageResponseTimeMs >= 0)
        }

    private fun createMockTemplate(
        name: String,
        description: String,
    ): TemplateDraft {
        val now = Clock.System.now()
        return TemplateDraft(
            id = "mock_template_${System.currentTimeMillis()}",
            name = name,
            description = description,
            exercises = emptyList(),
            source = DraftSource.AI_GENERATED,
            createdAt = now,
            updatedAt = now,
            userId = "test_user",
        )
    }
}
