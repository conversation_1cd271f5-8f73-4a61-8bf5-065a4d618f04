# Core Network Module - 接口契约文档

## 📋 接口概述

Core Network模块提供统一的网络服务接口，支持WebSocket流式通信和REST API调用。所有接口遵循Clean Architecture原则，确保高内聚、低耦合的设计。

## 🔌 WebSocket流式客户端接口

### LlmStreamClient

**职责**: 提供AI流式对话的WebSocket客户端服务

```kotlin
interface LlmStreamClient {
    /**
     * 流式AI聊天 - 强类型版本
     * @param request 强类型的聊天请求
     * @return 原始响应帧的Flow
     */
    fun streamChat(request: ChatRequest): Flow<String>

    /**
     * 检查连接状态
     * @return 网络结果封装的连接状态
     */
    suspend fun checkConnection(): NetworkResult<Boolean>

    /**
     * 获取当前配置的基础URL
     * @return API基础URL
     */
    fun getBaseUrl(): String

    /**
     * 暂停WebSocket连接
     * 用于网络状态变化时的连接管理
     */
    fun pause()

    /**
     * 恢复WebSocket连接
     * 用于网络状态恢复时的连接管理
     */
    fun resume()

    /**
     * 获取当前WebSocket状态
     * @return 当前连接状态
     */
    fun getCurrentState(): WsState
}
```

**实现类**: `LlmStreamClientImpl`
**配置**: `WsConfig`
**状态管理**: `WsState`
**帧格式**: `WsFrame`

### 使用示例

```kotlin
@Inject
lateinit var llmStreamClient: LlmStreamClient

// 流式AI对话
val request = ChatRequest(
    model = "deepseek-chat",
    messages = listOf(ChatMessage("user", "Hello AI")),
    stream = true
)

llmStreamClient.streamChat(request)
    .catch { error -> 
        println("WebSocket error: ${error.message}")
    }
    .collect { response ->
        println("AI Response: $response")
    }
```

## 🌐 REST客户端接口

### RestClient

**职责**: 提供统一的HTTP REST API调用服务

```kotlin
interface RestClient {
    /**
     * GET请求
     * @param url 请求URL
     * @param headers 请求头部
     * @return ApiResult封装的响应内容
     */
    suspend fun get(url: String, headers: Map<String, String> = emptyMap()): ApiResult<String>

    /**
     * POST请求
     * @param url 请求URL
     * @param body 请求体
     * @param headers 请求头部
     * @return ApiResult封装的响应内容
     */
    suspend fun post(url: String, body: String, headers: Map<String, String> = emptyMap()): ApiResult<String>

    /**
     * PUT请求
     * @param url 请求URL
     * @param body 请求体
     * @param headers 请求头部
     * @return ApiResult封装的响应内容
     */
    suspend fun put(url: String, body: String, headers: Map<String, String> = emptyMap()): ApiResult<String>

    /**
     * DELETE请求
     * @param url 请求URL
     * @param headers 请求头部
     * @return ApiResult封装的响应内容
     */
    suspend fun delete(url: String, headers: Map<String, String> = emptyMap()): ApiResult<String>
}
```

**实现类**: `RestClientImpl`
**配置**: `RestConfig`
**结果封装**: `ApiResult<T>`
**拦截器链**: Auth → NetworkStatus → Logging → Retry

### 使用示例

```kotlin
@Inject
lateinit var restClient: RestClient

// GET请求
val result = restClient.get("https://api.example.com/users/123")
when (result) {
    is ApiResult.Success -> {
        println("User data: ${result.data}")
    }
    is ApiResult.Error -> {
        when (result.error) {
            is ApiError.Network -> println("网络错误: ${result.error.message}")
            is ApiError.Http -> println("HTTP错误: ${result.error.code}")
            is ApiError.Offline -> println("离线状态")
        }
    }
}

// POST请求
val postResult = restClient.post(
    url = "https://api.example.com/users",
    body = """{"name": "John", "email": "<EMAIL>"}""",
    headers = mapOf("Content-Type" to "application/json")
)
```

## 📊 网络监控接口

### NetworkMonitor

**职责**: 提供平台无关的网络状态监控

```kotlin
interface NetworkMonitor {
    /**
     * 网络状态流
     * @return 网络状态的Flow
     */
    val status: Flow<NetworkStatus>

    /**
     * 网络可用性流
     * @return 网络是否可用的Flow
     */
    val isNetworkAvailable: Flow<Boolean>

    /**
     * 网络类型流
     * @return 网络类型的Flow
     */
    val networkType: Flow<NetworkType>

    /**
     * 计费网络检测流
     * @return 是否为计费网络的Flow
     */
    val isMetered: Flow<Boolean>

    /**
     * 当前网络状态
     * @return 当前网络状态
     */
    val networkState: StateFlow<NetworkState>
}
```

**实现类**: `AndroidNetworkMonitor`
**状态类型**: `NetworkState`, `NetworkStatus`, `NetworkType`

### NetworkWatchdog

**职责**: 提供网络事件流和UI集成支持

```kotlin
interface NetworkWatchdog {
    /**
     * 网络事件流
     * @return 网络事件的Flow（带防抖处理）
     */
    val events: Flow<NetworkEvent>

    /**
     * 当前网络状态
     * @return 当前网络状态
     */
    val currentState: StateFlow<NetworkState>

    /**
     * 启动监控
     */
    fun start()

    /**
     * 停止监控
     */
    fun stop()
}
```

**事件类型**: `NetworkEvent.Connected`, `NetworkEvent.Lost`, `NetworkEvent.Restored`

### 使用示例

```kotlin
@Inject
lateinit var networkWatchdog: NetworkWatchdog

// 监听网络事件
networkWatchdog.events
    .collect { event ->
        when (event) {
            is NetworkEvent.Connected -> {
                println("网络已连接: ${event.type}")
                // 恢复网络操作
            }
            is NetworkEvent.Lost -> {
                println("网络连接丢失: ${event.reason}")
                // 暂停网络操作
            }
            is NetworkEvent.Restored -> {
                println("网络连接恢复")
                // 重新开始网络操作
            }
        }
    }
```

## 💾 断点续传存储接口

### TokenOffsetStore

**职责**: 提供WebSocket断点续传的token索引存储

```kotlin
interface TokenOffsetStore {
    /**
     * 获取最后的token索引
     * @param conversationId 会话ID
     * @return 最后的token索引
     */
    suspend fun getLastTokenIndex(conversationId: String): Int

    /**
     * 更新最后的token索引
     * @param conversationId 会话ID
     * @param index token索引
     */
    suspend fun updateLastTokenIndex(conversationId: String, index: Int)

    /**
     * 清理过期的索引记录
     * @param maxAge 最大保留时间（毫秒）
     */
    suspend fun cleanupExpiredRecords(maxAge: Long)

    /**
     * 获取所有会话的索引记录
     * @return 会话ID到索引的映射
     */
    suspend fun getAllRecords(): Map<String, Int>
}
```

**实现类**: `InMemoryTokenOffsetStore`

## 📊 API结果封装

### ApiResult<T>

**职责**: 统一的API调用结果封装，支持函数式操作

```kotlin
sealed interface ApiResult<out T> {
    /**
     * 成功结果
     */
    data class Success<T>(val data: T) : ApiResult<T>

    /**
     * 错误结果
     */
    data class Error(val error: ApiError) : ApiResult<Nothing>

    // 函数式操作
    fun <R> map(transform: (T) -> R): ApiResult<R>
    fun <R> flatMap(transform: (T) -> ApiResult<R>): ApiResult<R>
    fun <R> fold(onSuccess: (T) -> R, onError: (ApiError) -> R): R
    
    // 便捷属性
    val isSuccess: Boolean
    val isError: Boolean
    fun getOrNull(): T?
    fun getOrThrow(): T
}
```

### ApiError

**职责**: 统一的API错误类型定义

```kotlin
sealed interface ApiError {
    val message: String
    val cause: Throwable?

    /**
     * 网络错误
     */
    data class Network(
        override val message: String,
        override val cause: Throwable? = null
    ) : ApiError

    /**
     * HTTP错误
     */
    data class Http(
        val code: Int,
        override val message: String,
        override val cause: Throwable? = null
    ) : ApiError

    /**
     * 离线错误
     */
    data class Offline(
        override val message: String = "Network is offline",
        override val cause: Throwable? = null
    ) : ApiError

    /**
     * 认证错误
     */
    data class Auth(
        override val message: String,
        override val cause: Throwable? = null
    ) : ApiError

    /**
     * 解析错误
     */
    data class Parse(
        override val message: String,
        override val cause: Throwable? = null
    ) : ApiError

    /**
     * 未知错误
     */
    data class Unknown(
        override val message: String,
        override val cause: Throwable? = null
    ) : ApiError
}
```

## 🔧 配置接口

### WsConfig

**职责**: WebSocket客户端配置参数

```kotlin
data class WsConfig(
    val baseUrl: String,
    val apiKey: String,
    
    // 心跳配置
    val pingIntervalSec: Int = 15,
    val pongTimeoutSec: Int = 5,
    val maxMissedPongs: Int = 3,
    
    // 重连配置
    val maxReconnectAttempts: Int = 5,
    val baseBackoffMs: Long = 1000,
    val maxBackoffMs: Long = 30000,
    
    // 连接配置
    val connectTimeoutSec: Int = 30,
    val readTimeoutSec: Int = 0, // 0表示无限制
    val writeTimeoutSec: Int = 30,
    
    // 断点续传配置
    val enableOffsetResume: Boolean = true,
    val maxOffsetRetention: Int = 1000
)
```

### RestConfig

**职责**: REST客户端配置参数

```kotlin
data class RestConfig(
    val connectTimeoutSec: Int = 30,
    val readTimeoutSec: Int = 30,
    val writeTimeoutSec: Int = 30,
    val apiKey: String = "",
    val enableRetry: Boolean = true,
    val maxRetries: Int = 2,
    val retryDelayMs: Long = 2000,
    val enableLogging: Boolean = false
)
```

## 🔄 状态和事件类型

### WsState

**职责**: WebSocket连接状态枚举

```kotlin
enum class WsState {
    INIT,           // 初始状态
    CONNECTING,     // 连接中
    OPEN,           // 连接已建立
    STREAMING,      // 流式传输中
    RECONNECTING,   // 重连中
    DEAD;           // 连接死亡

    fun isConnected(): Boolean = this in setOf(OPEN, STREAMING)
    fun canReconnect(): Boolean = this != DEAD
}
```

### NetworkEvent

**职责**: 网络状态变化事件

```kotlin
sealed interface NetworkEvent {
    val timestamp: Long

    data class Connected(
        val type: NetworkType,
        override val timestamp: Long = System.currentTimeMillis()
    ) : NetworkEvent

    data class Lost(
        val reason: NetworkLossReason,
        override val timestamp: Long = System.currentTimeMillis()
    ) : NetworkEvent

    data class Restored(
        val type: NetworkType,
        override val timestamp: Long = System.currentTimeMillis()
    ) : NetworkEvent
}
```

## 🧪 测试接口

### 测试工具接口

```kotlin
// MockWebServer测试支持
interface NetworkTestUtils {
    fun createMockWebServer(): MockWebServer
    fun createTestWsConfig(): WsConfig
    fun createTestRestConfig(): RestConfig
}

// 网络状态模拟
interface NetworkStateMocker {
    fun simulateOffline()
    fun simulateOnline()
    fun simulateSlowNetwork()
}
```

## 📋 接口契约验证

### 单元测试要求

1. **接口实现完整性**: 所有接口方法必须有对应实现
2. **错误处理**: 异常情况必须正确处理和封装
3. **状态一致性**: 状态变化必须符合状态机定义
4. **资源管理**: 连接和资源必须正确释放

### 集成测试要求

1. **端到端流程**: 完整的请求-响应流程测试
2. **网络状态变化**: 网络状态变化时的行为验证
3. **并发安全**: 多线程环境下的线程安全验证
4. **性能指标**: 响应时间和资源使用验证

---

**Core Network模块的接口设计遵循SOLID原则，提供清晰、一致、可测试的API契约。**
