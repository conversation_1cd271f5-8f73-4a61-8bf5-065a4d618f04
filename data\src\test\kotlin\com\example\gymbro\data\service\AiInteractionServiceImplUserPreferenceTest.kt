package com.example.gymbro.data.service

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.profile.model.user.FitnessGoal
import com.example.gymbro.domain.profile.model.user.FitnessPreference
import com.example.gymbro.domain.profile.model.user.WeekDay
import com.example.gymbro.domain.service.profile.UserPreferencePort
import com.example.gymbro.domain.workout.model.TemplateDraft
import com.example.gymbro.domain.workout.repository.WorkoutDraftRepository
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.Json
import org.junit.Before
import org.junit.Test
import kotlin.test.assertTrue

/**
 * AiInteractionServiceImpl用户偏好集成测试
 *
 * 验证Coach模块能够获取真实的用户偏好数据并注入到AI Prompt中
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
class AiInteractionServiceImplUserPreferenceTest {
    private lateinit var aiInteractionService: AiInteractionServiceImpl
    private lateinit var workoutDraftRepository: WorkoutDraftRepository
    private lateinit var userPreferencePort: UserPreferencePort
    private lateinit var json: Json

    private val testDispatcher = StandardTestDispatcher()

    @Before
    fun setup() {
        workoutDraftRepository = mockk()
        userPreferencePort = mockk()
        json = Json { ignoreUnknownKeys = true }

        aiInteractionService =
            AiInteractionServiceImpl(
                workoutDraftRepository = workoutDraftRepository,
                userPreferencePort = userPreferencePort,
                json = json,
                ioDispatcher = testDispatcher,
            )
    }

    @Test
    fun `generateWorkoutTemplateFromPrompt should include USER_PREFERENCE block when user has preferences`() =
        runTest(
            testDispatcher,
        ) {
            // Given - 用户有训练偏好
            val userPreference =
                FitnessPreference(
                    primaryGoal = FitnessGoal.MUSCLE_GAIN,
                    workoutDays = setOf(WeekDay.MONDAY, WeekDay.WEDNESDAY, WeekDay.FRIDAY),
                )

            val expectedTemplate = mockk<TemplateDraft>()

            coEvery { userPreferencePort.current() } returns userPreference
            coEvery {
                workoutDraftRepository.generateTemplateDraftWithAI(
                    prompt = any(),
                    userId = any(),
                    history = any(),
                )
            } returns flowOf(ModernResult.Success(expectedTemplate))

            // When
            aiInteractionService.generateWorkoutTemplateFromPrompt(
                userId = "test_user_123",
                prompt = "帮我制定胸肌训练计划",
                context = null,
            )

            // Then - 验证prompt包含USER_PREFERENCE块
            verify {
                workoutDraftRepository.generateTemplateDraftWithAI(
                    prompt =
                    match { enhancedPrompt ->
                        enhancedPrompt.contains("USER_PREFERENCE:") &&
                            enhancedPrompt.contains("muscle_gain") &&
                            enhancedPrompt.contains("monday") &&
                            enhancedPrompt.contains("wednesday") &&
                            enhancedPrompt.contains("friday")
                    },
                    userId = "test_user_123",
                    history = emptyList(),
                )
            }
        }

    @Test
    fun `generateWorkoutTemplateFromPrompt should skip USER_PREFERENCE block when user has no preferences`() =
        runTest(
            testDispatcher,
        ) {
            // Given - 用户没有训练偏好
            val emptyPreference = FitnessPreference()

            val expectedTemplate = mockk<TemplateDraft>()

            coEvery { userPreferencePort.current() } returns emptyPreference
            coEvery {
                workoutDraftRepository.generateTemplateDraftWithAI(
                    prompt = any(),
                    userId = any(),
                    history = any(),
                )
            } returns flowOf(ModernResult.Success(expectedTemplate))

            // When
            aiInteractionService.generateWorkoutTemplateFromPrompt(
                userId = "test_user_123",
                prompt = "帮我制定胸肌训练计划",
                context = null,
            )

            // Then - 验证prompt不包含USER_PREFERENCE块
            verify {
                workoutDraftRepository.generateTemplateDraftWithAI(
                    prompt =
                    match { enhancedPrompt ->
                        !enhancedPrompt.contains("USER_PREFERENCE:")
                    },
                    userId = "test_user_123",
                    history = emptyList(),
                )
            }
        }

    @Test
    fun `generateWorkoutTemplateFromPrompt should handle user preference errors gracefully`() =
        runTest(
            testDispatcher,
        ) {
            // Given - 获取用户偏好时发生异常
            val expectedTemplate = mockk<TemplateDraft>()

            coEvery { userPreferencePort.current() } throws RuntimeException("偏好获取失败")
            coEvery {
                workoutDraftRepository.generateTemplateDraftWithAI(
                    prompt = any(),
                    userId = any(),
                    history = any(),
                )
            } returns flowOf(ModernResult.Success(expectedTemplate))

            // When
            val result =
                aiInteractionService.generateWorkoutTemplateFromPrompt(
                    userId = "test_user_123",
                    prompt = "帮我制定胸肌训练计划",
                    context = null,
                )

            // Then - 应该成功执行，只是没有用户偏好上下文
            assertTrue(result is ModernResult.Success)

            verify {
                workoutDraftRepository.generateTemplateDraftWithAI(
                    prompt =
                    match { enhancedPrompt ->
                        !enhancedPrompt.contains("USER_PREFERENCE:") &&
                            enhancedPrompt.contains("帮我制定胸肌训练计划")
                    },
                    userId = "test_user_123",
                    history = emptyList(),
                )
            }
        }

    // 注意：复杂的WorkoutContext集成测试暂时跳过，专注于用户偏好集成验证
}
