**核心链路 PRD：Prompt-Safe GymBro 1.0**

---

**一、目标**

1. 不再复读系统指令
2. 流式接口稳定（无数组越界、无并发竞态）
3. 代码粒度清晰，可测试、可迭代

---

**二、顶层流程**

```
User → ViewModel → AICoachRepository
       ↓                    │
ChatSessionRepo ←───────────┘（落库）
       │
AiStreamRepository ──► AdaptiveStreamClient ──► LLM
                      ▲
 LayeredPromptBuilder │（角色分层）
  ▲                   │
PromptRegistry ───────┘（热切换模板）
```

---

**三、核心文件**

| 模块 | Kotlin 文件 | 关键职责 |
| --- | --- | --- |
| prompt | `PromptRegistry.kt` | 读取 / 切换 JSON 套件，暴露 `systemLayer` |
| prompt | `LayeredPromptBuilder.kt` | `buildChatMessages(system, prompt, history)`；内含 Role-Linter |
| ai/network | `AdaptiveStreamClient.kt` | SSE / WebSocket 自动协商、retryWhen 内部实现 |
| repository/ai | `AiStreamRepositoryImpl.kt` | ① 从 Registry + Builder 组 `ChatRequest`<br>② 流式收包→`StreamEvent`<br>③ Atomic 计数 + 指数退避 |
| repository/session | `ChatSessionDaoRepo.kt` | 纯 Room CRUD，事务只包 DB 操作 |
| repository/session | `SessionBackupRepo.kt` | DB 失败兜底写 File（加 FileLock） |
| repository/monitor | `MetricsReporter.kt` | `AtomicLong` 收集，push Prometheus |
| usecase | `AICoachRepositoryImpl.kt` | 把 StreamEvent ↔ CoachMessage，供 VM 订阅 |

> 其余旧的巨型类被拆散，命名遵循“功能-后缀Repo”或“功能-Client”。

---

**四、关键改动清单（补丁式）**

1. **AiStreamRepositoryImpl**
   ```kotlin
   val system = PromptRegistry.getSuite().systemLayer
   val history = buildHistory(sessionId)
   val msgs = layeredPromptBuilder.buildChatMessages(system, prompt, history)
   val chatReq = ChatRequest(model = cfg.defaultModel, messages = msgs.map { it.toShared() }, ...)
   ```
   + `AtomicLong` 替换所有计数器
   + `val delayMs = EXPO_BACKOFF.getOrElse(attempt.toInt()) { 5000L }`

2. **LayeredPromptBuilder**
   - 内嵌 `object RoleLinter`，`check()` 抛异常即回 `ModernResult.Error`
   - 上下文分段 512 token → 多条 `role="tool"`

3. **PromptRegistry**
   - `switch(id)` 原子赋值 + DataStore 持久化
   - `init(context)` 复制 assets → files 并做 SHA 校验

---

**五、测试矩阵**

| 层 | 用例 | 断言 |
| --- | --- | --- |
| Prompt | 单元：`buildChatMessages()` | system 首条唯一 / user 无系统关键词 |
| AI Repo | 单元：数组越界、计数器并发 1000 次 | 无异常，计数准确 |
| Flow | Turbine：流式生成 10k token | 实时收到 Chunk，最后 Done |
| UI | Espresso：切换 Prompt 按钮 | 次轮会话 system 指令变化 |

---

**六、上线节奏**

1. Day-1：合并 PromptBuilder & Registry 补丁 → 灰度 5 %
2. Day-2：Atomic/Backoff 修复 → 全量
3. Day-3：拆 repo 子包 + MetricsReporter 接入
4. Day-4：文档 & CI Role-Linter 规则 → 打标签 v1.0

---

**七、风险 & 兜底**

| 风险 | 监控 | 兜底 |
| --- | --- | --- |
| 模板解析失败 | Crashlytics tag `PRT_PARSE` | Registry 回退 `default` |
| 流式中断 >3 % | Grafana alert | AdaptiveStreamClient 自动降级 SSE→HTTP |
| 计数异常 | Prometheus count ≠ 总请求 | 热修补关掉自定义指标 |

---

**一句话收尾**
把系统层锁进 Registry，把 prompt 组装交给 Builder，把网络流托付给 AdaptiveClient——三板斧敲完，安全、稳定、可扩展，一次到位。
**整理后的核心文件链路 PRD（针对现有 TREE，精简到 Prompt-Safe 关键路径）**

下面只关心「AI 对话」相关代码的最小闭环。其它 auth／workout 等保持现状。

────────────────────────────────────────
**1 顶层依赖流**

```
UI(ViewModel)
  │
  ▼
AICoachRepository            （domain→data）
  │
  │ ①save / query 会话、消息
  ▼                    ▲
ChatSessionDaoRepo ────┘（Room）
  │
  │ ②发起 AI 流
  ▼
AiStreamRepository
  │
  │ ③构造 ChatRequest
  ▼
LayeredPromptBuilder  ←── PromptRegistry
  │                       （热切换模板）
  │ ④网络流
  ▼
AdaptiveStreamClient ──► LLM
```

────────────────────────────────────────
**2 建议子包（仅 AI 聊天域）**

```
data/coach/
├─ session/
│   ├─ dao/                # Room DAO + Entity
│   ├─ repository/
│   │     ├─ ChatSessionDaoRepo.kt      // 纯 CRUD
│   │     └─ SessionBackupRepo.kt       // 兜底写文件
│   └─ mapper/            # Entity ↔ Domain
│
└─ ai/
    ├─ builder/
    │     └─ LayeredPromptBuilder.kt
    ├─ registry/
    │     └─ PromptRegistry.kt
    ├─ network/
    │     └─ AdaptiveStreamClient.kt
    └─ repository/
          ├─ AiStreamRepositoryImpl.kt
          └─ MetricsReporter.kt
```

• **高内聚**：`ai/` 只处理 Prompt + 网络；`session/` 只管持久化。
• **低耦合**：两个子包之间仅通过 `domain` 接口交互，不直接 import 对方内部类。

────────────────────────────────────────
**3 关键文件清单**

| 文件 | 职责 | 备注 |
| ---- | ---- | ---- |
| PromptRegistry.kt | 加载 / 切换 JSON，暴露 `systemLayer` | 新增 `getSuite()` |
| LayeredPromptBuilder.kt | `buildChatMessages()` + Role-Linter | 已实现 |
| AdaptiveStreamClient.kt | SSE/WebSocket + retryWhen | 已实现 |
| AiStreamRepositoryImpl.kt | 从 Registry+Builder 组 `ChatRequest`、收流、计数 | 替换硬编码 SystemLayer |
| ChatSessionDaoRepo.kt | Room CRUD（原 `ChatSessionRepositoryImpl` 精简版） | 只做 DB，不含备份 |
| SessionBackupRepo.kt | DB 失败写 `chat_backup.json`（带 FileLock） | 从原仓库迁移 |
| MetricsReporter.kt | `AtomicLong` 计数 → Prometheus push | 统一指标 |

────────────────────────────────────────
**4 必须打的补丁（阶段一）**

1. **Prompt 注入**
   ```kotlin
   val system = promptRegistry.getSuite().systemLayer
   val history = buildHistory(sessionId)
   val msgs = layeredPromptBuilder.buildChatMessages(system, prompt, history)
   chatRequest = chatRequest.copy(messages = msgs.map { it.toShared() })
   ```

2. **计数器线程安全**
   ```kotlin
   private val totalReq  = AtomicLong()
   private val sse5xx    = AtomicLong()
   private val retryCnt  = AtomicLong()
   ```

3. **重试越界保护**
   ```kotlin
   val delayMs = EXPO_BACKOFF.getOrElse(attempt.toInt()) { 5000L }
   ```

────────────────────────────────────────
**5 推进步骤**

| Day | 动作 |
| --- | ---- |
| D1  | 建立 `session/`、`ai/` 子包；移动文件（IDE Refactor→Move） |
| D2  | 实施补丁 1-3；Role-Linter UT 通过 |
| D3  | 把备份逻辑抽到 `SessionBackupRepo`；`ChatSessionDaoRepo` 精简 |
| D4  | MetricsReporter 接入 + Prometheus push-gateway 验证 |
| D5  | QA 灰度 5 % ，监控 System 泄漏、崩溃 0 |
| D6  | 全量发布，TREE 与 README 更新 |

────────────────────────────────────────
**6 验证指标**

| 指标 | 阈值 |
| ---- | ---- |
| `system_prompt_leak_rate` | 0 % |
| `ai_stream_retry_overflow` | 0 |
| `chat_backup_file_lock_fail` | < 0.1 % |
| 单测覆盖（ai+session 子包） | ≥ 80 % |

────────────────────────────────────────
**7 结论**

• 不新增“工厂类”，却仍保证 Prompt 安全、并发稳定、子包内聚。
• 全改动集中在 AI 聊天域，不触碰其它 200+ 文件。
• 两周迭代即可落地 Prompt-Safe GymBro 1.0 的完整链路。
