# Data Module

> **💾 数据访问层 - Clean Architecture + MVI**
>
> **更新日期**: 2025-07-22 | **版本**: v1.3 - 编译警告修复·代码质量提升

## 📊 Module Statistics

| Category         | Count | Status      |
| ---------------- | ----- | ----------- |
| **Repositories** | 26    | ☑️ Completed |
| **Data Sources** | 4     | ☑️ Completed |
| **DAOs**         | 15    | ☑️ Completed |
| **Mappers**      | 4     | ☑️ Completed |
| **Entities**     | 18    | ☑️ Completed |
| **DI Modules**   | 2     | ☑️ Completed |

## 📁 Module Structure

```
GymBro/data/
├── 📄 README.md
├── 📄 TREE.md
├── 📄 INTERFACES.md
├── 📄 build.gradle.kts
└── 📁 src/main/kotlin/com/example/gymbro/data/
    ├── 📁 ai/              # AI-related data sources and services
    ├── 📁 autosave/        # Generic auto-save mechanism
    ├── 📁 coach/           # Data layer for the AI coach feature
    ├── 📁 datasource/      # Interfaces for local and remote data sources
    ├── 📁 di/              # Dagger Hilt modules
    ├── 📁 exercise/        # Data layer for the exercise feature
    ├── 📁 local/           # Local data source (Room database and DAOs)
    ├── 📁 mapper/          # Mappers for data models
    ├── 📁 memory/          # Data layer for the memory system
    ├── 📁 model/           # Data models
    ├── 📁 network/         # Network-related data sources and services
    ├── 📁 remote/          # Remote data source (Firebase, REST APIs)
    ├── 📁 repository/      # Repository implementations
    ├── 📁 shared/          # Common utilities and services
    ├── 📁 title/           # Data layer for the title generation feature
    ├── 📁 util/            # Utility classes and functions
    └── 📁 workout/         # Data layer for the workout feature
```

## 🏗️ Architecture Overview

The `data` module is responsible for all data operations in the GymBro application. It implements the repository interfaces defined in the `domain` module and provides a clean API for the `domain` module to access and manipulate data.

### Key Responsibilities

- **Data Sources**: Contains the implementations for both local and remote data sources. The local data source is responsible for handling data from the local database (Room), while the remote data source handles data from the network (Firebase, REST APIs, etc.).
- **Repositories**: Implements the repository interfaces defined in the `domain` module. The repositories are responsible for coordinating data from the different data sources and providing a single source of truth for the application.
- **Mappers**: Contains the logic for mapping between the different data models used in the application (e.g., DTOs, entities, and domain models).
- **Database**: Defines the Room database and its DAOs.

## Recent Improvements

### 🔧 v1.3 - 编译警告修复·代码质量提升 (2025-07-22)

#### ✅ 性能优化警告修复
- **JsonSerializer.kt**: 修复Json实例重复创建问题，使用lazy单例模式缓存实例
- **性能提升**: 避免每次调用都创建新Json实例，减少对象创建开销

#### ✅ 弃用API处理
- **AICoachRepositoryImpl.kt**: 为重写弃用方法添加完整@Deprecated注解
- **AiStreamRepositoryImpl.kt**: 提供清晰的迁移路径，引导使用新的streamAiResponse(messages)方法
- **向后兼容**: 保持API兼容性，使用渐进式迁移策略

#### ✅ 代码逻辑优化
- **MessageEmbeddingService.kt**: 修复"Check for instance is always 'false'"逻辑问题
- **类型安全**: 使用安全的ModernResult.Success<*>类型检查，避免ClassCastException
- **错误处理**: 统一使用DataErrors.DataError.unknown()创建标准化错误

#### ✅ API使用规范
- **USAGE_EXAMPLES.kt**: 为delicate API添加@OptIn(kotlinx.coroutines.DelicateCoroutinesApi::class)注解
- **UserPreferenceRepositoryImpl.kt**: 为FlowPreview API添加@OptIn(FlowPreview::class)注解
- **代码规范**: 重构Debouncer类使用CoroutineScope参数，避免GlobalScope

#### ✅ 扩展函数冲突解决
- **ChatSessionMapper.kt**: 修复扩展函数递归调用问题，直接实现转换逻辑
- **类型映射**: 正确映射ChatSessionEntity到ChatSession的所有属性和时间转换

#### 📊 质量指标
- **编译状态**: ✅ BUILD SUCCESSFUL - 零错误零警告
- **架构合规**: ✅ 完全遵循Clean Architecture + MVI 2.0标准
- **性能优化**: ✅ Json实例缓存机制生效
- **API规范**: ✅ 所有实验性API正确标注@OptIn

### 🏗️ v1.2 - UserDataCenter Integration (2025-01-13)

### ✅ v1.1 - Coach Module Data Layer Refactoring (2025-01-05)
- **Unified Conversion Logic**: Consolidated `ChatRaw.toDomainMessage()` implementations into single authoritative source
- **Visibility Improvements**: Updated `ConversationPagingSource` from `internal` to `public` for Clean Architecture compliance
- **Code Deduplication**: Eliminated 4 duplicate conversion implementations across the codebase
- **Architecture Compliance**: Ensured proper data → domain → ui flow patterns
- **Single Source of Truth**: Established `ChatSessionMapper` as the canonical conversion authority
- **Maintainability**: Simplified data layer with unified conversion patterns
- **Type Safety**: Enhanced type safety through consistent conversion logic

## 🔗 UserDataCenter Integration (v1.2)

### 架构重构
Data模块已完成UserDataCenter集成，实现了用户数据管理的现代化重构：

#### 核心变更
- **✅ UserRepositoryImpl**: 创建了基于UserDataCenter的正式实现
- **❌ TempUserRepositoryImpl**: 移除了临时占位实现
- **🔄 数据流统一**: 所有用户数据现在通过UserDataCenter统一管理

#### 实现特点
```kotlin
@Singleton
class UserRepositoryImpl @Inject constructor(
    private val userDataCenterApi: UserDataCenterApi,
    private val logger: Logger
) : UserRepository {
    // 通过UserDataCenter获取用户数据
    // 支持响应式数据更新
    // 完整的错误处理和日志记录
}
```

#### 技术优势
- **统一数据源**: 消除了数据分散和不一致问题
- **响应式架构**: 基于StateFlow的实时数据更新
- **类型安全**: 完整的类型转换和验证
- **错误处理**: 完善的错误状态管理
- **Clean Architecture**: 严格遵循依赖倒置原则

#### 集成验证
- ✅ 编译成功：data模块编译通过
- ✅ 依赖正确：UserDataCenter依赖正确配置
- ✅ 接口实现：完整实现UserRepository接口
- ✅ 类型转换：支持Profile/Workout模块间的类型转换

### 迁移完成状态
- **阶段1**: ✅ 废弃实现清理完成
- **阶段2**: ✅ 接口重组和模块化完成
- **下一步**: 数据库迁移和跨模块视图创建
