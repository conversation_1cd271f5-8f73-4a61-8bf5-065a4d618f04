package com.example.gymbro.data.exercise.initializer

import com.example.gymbro.data.exercise.local.dao.ExerciseDao
import com.example.gymbro.data.exercise.mapper.ExerciseMapper
import io.mockk.*
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals

/**
 * 官方动作初始化器测试
 */
class OfficialExerciseInitializerTest {

    private val exerciseDao = mockk<ExerciseDao>()
    private val exerciseMapper = mockk<ExerciseMapper>()
    private val testDispatcher = StandardTestDispatcher()

    private lateinit var initializer: OfficialExerciseInitializer

    @Before
    fun setup() {
        initializer = OfficialExerciseInitializer(
            exerciseDao = exerciseDao,
            exerciseMapper = exerciseMapper,
            ioDispatcher = testDispatcher,
        )
    }

    @Test
    fun `当数据库已有官方动作时，跳过初始化`() = runTest(testDispatcher) {
        // Given
        coEvery { exerciseDao.getOfficialCount() } returns 5

        // When
        initializer.initializeOfficialExercises()

        // Then
        coVerify(exactly = 1) { exerciseDao.getOfficialCount() }
        coVerify(exactly = 0) { exerciseDao.upsertBatch(any()) }
    }

    @Test
    fun `当数据库为空时，初始化官方动作`() = runTest(testDispatcher) {
        // Given
        coEvery { exerciseDao.getOfficialCount() } returns 0
        coEvery { exerciseMapper.toDomain(any()) } returns mockk()
        coEvery { exerciseMapper.toEntity(any()) } returns mockk()
        coEvery { exerciseDao.upsertBatch(any()) } just Runs

        // When
        initializer.initializeOfficialExercises()

        // Then
        coVerify(exactly = 1) { exerciseDao.getOfficialCount() }
        coVerify(exactly = 1) { exerciseDao.upsertBatch(any()) }
    }

    @Test
    fun `验证官方动作种子数据的正确性`() = runTest(testDispatcher) {
        // Given
        coEvery { exerciseDao.getOfficialCount() } returns 0
        val capturedExercises = slot<List<com.example.gymbro.data.exercise.local.entity.ExerciseEntity>>()
        coEvery { exerciseMapper.toDomain(any()) } returns mockk()
        coEvery { exerciseMapper.toEntity(any()) } returns mockk()
        coEvery { exerciseDao.upsertBatch(capture(capturedExercises)) } just Runs

        // When
        initializer.initializeOfficialExercises()

        // Then
        // 验证插入了正确数量的动作
        val insertedCount = capturedExercises.captured.size
        assertEquals(11, insertedCount, "应该插入11个官方动作")
    }

    @Test
    fun `验证杠铃卧推动作的ID生成正确`() {
        // Given
        val exerciseName = "杠铃卧推"

        // When
        val generatedId = com.example.gymbro.shared.models.exercise.ExerciseDto.generateOfficialId(
            exerciseName,
        )

        // Then
        assert(generatedId.startsWith("off_")) { "官方动作ID应该以'off_'开头" }
        assert(generatedId.length > 4) { "官方动作ID应该包含哈希值" }
    }

    @Test
    fun `验证自定义动作ID生成不会与官方动作冲突`() {
        // Given
        val userId = "test_user_123"

        // When
        val customId = com.example.gymbro.shared.models.exercise.ExerciseDto.generateUserCustomId(userId)
        val officialId = com.example.gymbro.shared.models.exercise.ExerciseDto.generateOfficialId("杠铃卧推")

        // Then
        assert(customId.startsWith("u_")) { "自定义动作ID应该以'u_'开头" }
        assert(officialId.startsWith("off_")) { "官方动作ID应该以'off_'开头" }
        assert(customId != officialId) { "自定义动作ID不应该与官方动作ID冲突" }
        assert(!customId.equals("1")) { "自定义动作ID不应该是硬编码的'1'" }
    }
}
