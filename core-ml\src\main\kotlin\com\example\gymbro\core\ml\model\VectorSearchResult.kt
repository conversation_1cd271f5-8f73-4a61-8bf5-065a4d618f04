package com.example.gymbro.core.ml.model

/**
 * 向量搜索结果 - Core-ML Layer Model
 *
 * @param candidateId 候选项唯一标识
 * @param similarity 相似度分数 (0.0 到 1.0)
 * @param distance 距离分数 (0.0 到 1.0)，通常为 1 - similarity
 * @param metadata 附加元数据
 * @param searchStrategy 搜索策略标识
 */
data class VectorSearchResult(
    val candidateId: String,
    val similarity: Float,
    val distance: Float,
    val metadata: Map<String, Any> = emptyMap(),
    val searchStrategy: String = "cosine_similarity",
)
