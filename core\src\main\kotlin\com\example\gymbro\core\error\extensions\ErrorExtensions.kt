package com.example.gymbro.core.error.extensions

// import com.example.gymbro.core.error.* // Removed wildcard import
// Removed DatabaseError import as factory functions are no longer used
import android.database.sqlite.SQLiteException
import com.example.gymbro.core.error.types.ErrorCategory
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.StandardKeys // Corrected path
import com.example.gymbro.core.ui.text.UiText
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import java.io.IOException
import java.net.ConnectException
import java.net.SocketException
import java.net.SocketTimeoutException
import java.net.UnknownHostException

/**
 * 统一错误扩展函数
 *
 * 本文件合并了原 ErrorExtensions.kt 和 ErrorConversionExtensions.kt 的功能，提供：
 * - 错误类型工厂函数（创建各种类型的错误对象）
 * - Throwable 到 ModernDataError 的转换
 * - 错误类型推断和分类
 */

//region Throwable转换

/**
 * 将Throwable转换为ModernDataError
 *
 * @param operationName 操作名称
 * @param uiMessage 用户界面消息，如果为null则使用默认消息
 * @param metadataMap 元数据映射
 * @return 转换后的ModernDataError
 */
fun Throwable.toModernDataError(
    operationName: String,
    uiMessage: UiText? = null,
    metadataMap: Map<String, Any> = emptyMap(),
): ModernDataError {
    val errorType = inferGlobalErrorType()
    val category =
        when (errorType) {
            is GlobalErrorType.Network -> ErrorCategory.NETWORK
            is GlobalErrorType.Auth -> ErrorCategory.AUTH
            is GlobalErrorType.Business -> ErrorCategory.BUSINESS
            is GlobalErrorType.Data -> ErrorCategory.DATA
            is GlobalErrorType.Operation -> ErrorCategory.SYSTEM
            is GlobalErrorType.System -> ErrorCategory.SYSTEM
            is GlobalErrorType.AI -> ErrorCategory.SYSTEM
            is GlobalErrorType.Subscription -> ErrorCategory.BUSINESS
            else -> ErrorCategory.UNKNOWN
        }

    return ModernDataError(
        operationName = operationName,
        errorType = errorType,
        category = category,
        uiMessage = uiMessage ?: UiText.DynamicString("未知错误"),
        cause = this,
        metadataMap =
        metadataMap +
            mapOf(
                StandardKeys.TIMESTAMP.key to System.currentTimeMillis(),
                StandardKeys.EXCEPTION.key to this,
                StandardKeys.OPERATION_NAME.key to operationName,
            ),
    )
}

/**
 * 根据异常类型推断错误类型
 *
 * @return 适合当前异常的GlobalErrorType
 */
fun Throwable.inferGlobalErrorType(): GlobalErrorType =
    when (this) {
        // 网络错误
        is IOException, is SocketException, is UnknownHostException ->
            GlobalErrorType.Network.Connection
        is SocketTimeoutException, is ConnectException ->
            GlobalErrorType.Network.Timeout

        // 数据库错误
        is SQLiteException ->
            GlobalErrorType.Data.Access

        // 默认
        else -> GlobalErrorType.General.Unknown
    }

/**
 * 根据GlobalErrorType推断ErrorCategory
 *
 * @param errorType 全局错误类型
 * @return 推断的错误类别
 */
private fun inferErrorCategory(errorType: GlobalErrorType): ErrorCategory =
    when (errorType) {
        is GlobalErrorType.Network -> ErrorCategory.NETWORK
        is GlobalErrorType.Database -> ErrorCategory.DATA
        is GlobalErrorType.Auth -> ErrorCategory.AUTH
        is GlobalErrorType.Business -> ErrorCategory.BUSINESS
        is GlobalErrorType.Validation -> ErrorCategory.VALIDATION
        is GlobalErrorType.System -> ErrorCategory.SYSTEM
        is GlobalErrorType.Data -> ErrorCategory.DATA
        is GlobalErrorType.Sync -> ErrorCategory.DATA
        is GlobalErrorType.Operation -> ErrorCategory.SYSTEM
        is GlobalErrorType.Subscription -> ErrorCategory.BUSINESS
        is GlobalErrorType.Payment -> ErrorCategory.BUSINESS
        is GlobalErrorType.Feature -> ErrorCategory.BUSINESS
        is GlobalErrorType.User -> ErrorCategory.BUSINESS
        is GlobalErrorType.AI -> ErrorCategory.SYSTEM
        is GlobalErrorType.Cache -> ErrorCategory.DATA
        is GlobalErrorType.General -> ErrorCategory.UNKNOWN
        GlobalErrorType.Unknown -> ErrorCategory.UNKNOWN
    }

/**
 * 将Throwable转换为适当的ModernDataError，带有默认错误类型
 *
 * @param uiMessage 默认UI消息
 * @param defaultErrorType 默认错误类型
 * @param metadataMap 元数据映射
 * @param operationName 操作名称
 * @return 转换后的ModernDataError
 */
fun Throwable.toModernDataError(
    uiMessage: UiText,
    defaultErrorType: GlobalErrorType,
    metadataMap: Map<String, Any> = emptyMap(),
    operationName: String = "Throwable.toModernDataError_withDefaultType",
): ModernDataError =
    when (this) {
        is ModernDataError -> {
            // 如果已经是ModernDataError，保留原始信息但更新默认值
            val updatedMap =
                metadataMap.toMutableMap().apply {
                    putAll(<EMAIL>)
                }
            copy(
                operationName = operationName,
                uiMessage = uiMessage,
                metadataMap = updatedMap,
            )
        }
        else -> {
            // 创建新的ModernDataError，使用提供的默认错误类型而非推断
            val category = inferErrorCategory(defaultErrorType)
            ModernDataError(
                operationName = operationName,
                errorType = defaultErrorType,
                category = category,
                uiMessage = uiMessage,
                cause = this,
                metadataMap =
                metadataMap.toMutableMap().apply {
                    put(StandardKeys.TIMESTAMP.key, System.currentTimeMillis())
                    put(StandardKeys.ERROR_SUBTYPE.key, <EMAIL>)
                },
            )
        }
    }

//endregion

//region 错误工厂函数

// DatabaseError factory functions (databaseError, queryError) are removed from here.

//endregion

//region 通用错误处理工具

/**
 * 将Flow<T>转换为Flow<ModernResult<T>>
 * 捕获所有异常并转换为ModernResult.Error
 */
fun <T> Flow<T>.asModernResultFlow(
    operationName: String,
    defaultUiMessage: UiText? = null,
): Flow<ModernResult<T>> =
    this
        .map<T, ModernResult<T>> { ModernResult.Success(it) }
        .catch { e ->
            emit(
                ModernResult.Error(
                    e.toModernDataError(
                        operationName = operationName,
                        uiMessage = defaultUiMessage,
                    ),
                ),
            )
        }

/**
 * 安全地收集Flow<ModernResult<T>>
 * 将ModernResult.Success中的数据传递给onSuccess
 * 将ModernResult.Error中的错误传递给onError
 */
suspend fun <T> Flow<ModernResult<T>>.collectSafely(
    onSuccess: suspend (T) -> Unit,
    onError: suspend (ModernDataError) -> Unit,
) {
    collect { result ->
        when (result) {
            is ModernResult.Loading -> { /* 忽略Loading状态 */ }
            is ModernResult.Success -> onSuccess(result.data)
            is ModernResult.Error -> onError(result.error)
        }
    }
}

/**
 * 映射ModernResult<T>到ModernResult<R>
 * 保持错误状态不变
 */
fun <T, R> ModernResult<T>.mapResult(transform: (T) -> R): ModernResult<R> =
    when (this) {
        is ModernResult.Loading -> ModernResult.Loading
        is ModernResult.Success -> ModernResult.Success(transform(data))
        is ModernResult.Error -> ModernResult.Error(error)
    }

//endregion
