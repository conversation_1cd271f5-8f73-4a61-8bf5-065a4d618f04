package com.example.gymbro.data.local.dao.user

import androidx.room.*
import com.example.gymbro.data.local.entity.user.UserSettingsEntity

/**
 * 用户设置数据访问对象
 */
@Dao
interface UserSettingsDao {
    /**
     * 根据用户ID获取设置
     */
    @Query("SELECT * FROM user_settings WHERE userId = :userId")
    suspend fun getUserSettings(userId: String): UserSettingsEntity?

    /**
     * 插入用户设置
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUserSettings(settings: UserSettingsEntity)

    /**
     * 更新用户设置
     */
    @Update
    suspend fun updateUserSettings(settings: UserSettingsEntity)
}
