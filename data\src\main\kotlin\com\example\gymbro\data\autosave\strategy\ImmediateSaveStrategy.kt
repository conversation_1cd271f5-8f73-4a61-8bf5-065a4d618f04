package com.example.gymbro.data.autosave.strategy

import com.example.gymbro.core.autosave.AutoSaveManager
import com.example.gymbro.core.autosave.strategy.AutoSaveStrategy
import com.example.gymbro.core.logging.Logger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 即时保存策略实现
 *
 * 🎯 功能特性：
 * - 数据变更后立即触发保存
 * - 受1秒最低间隔限制保护
 * - 适用于重要数据的即时持久化
 * - 支持协程异步操作
 *
 * @param T 要保存的数据类型
 * @param logger 日志记录器
 */
class ImmediateSaveStrategy<T : Any> @Inject constructor(
    private val logger: Logger,
) : AutoSaveStrategy<T> {

    private var saveCallback: (suspend () -> Result<Unit>)? = null
    private var errorCallback: ((Throwable) -> Unit)? = null
    private var scope: CoroutineScope? = null
    private var lastSaveTime: Long = 0L

    override fun start(
        scope: CoroutineScope,
        onSave: suspend () -> Result<Unit>,
        onError: (Throwable) -> Unit,
    ) {
        this.scope = scope
        this.saveCallback = onSave
        this.errorCallback = onError

        logger.d("ImmediateSaveStrategy", "即时保存策略已启动")
    }

    override fun onDataChanged(oldData: T?, newData: T) {
        val scope = this.scope ?: return
        val saveCallback = this.saveCallback ?: return
        val errorCallback = this.errorCallback ?: return

        // 检查1秒最低间隔限制
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastSaveTime < AutoSaveManager.MIN_SAVE_INTERVAL_MS) {
            logger.d("ImmediateSaveStrategy", "保存操作被限制，距离上次保存不足1秒")
            return
        }

        scope.launch {
            try {
                logger.d("ImmediateSaveStrategy", "开始即时保存")
                val result = saveCallback()

                result.fold(
                    onSuccess = {
                        lastSaveTime = currentTime
                        logger.d("ImmediateSaveStrategy", "即时保存成功")
                    },
                    onFailure = { error ->
                        logger.e(error, "即时保存失败")
                        errorCallback(error)
                    },
                )
            } catch (e: Exception) {
                logger.e(e, "即时保存异常")
                errorCallback(e)
            }
        }
    }

    override fun stop() {
        scope = null
        saveCallback = null
        errorCallback = null
        lastSaveTime = 0L

        logger.d("ImmediateSaveStrategy", "即时保存策略已停止")
    }

    companion object {
        /**
         * 创建即时保存策略实例
         */
        fun <T : Any> create(logger: Logger): ImmediateSaveStrategy<T> {
            return ImmediateSaveStrategy(logger)
        }
    }
}
