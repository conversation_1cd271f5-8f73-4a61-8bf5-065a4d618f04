package com.example.gymbro.data.workout.model

import java.util.*

/**
 * 训练动作数据模型
 * 用于初始化和更新动作库数据
 */
data class ExerciseData(
    val id: String = UUID.randomUUID().toString(),
    val name: String,
    val category: String,
    val equipment: String,
    val targetMuscles: List<String>,
    val secondaryMuscles: List<String> = emptyList(),
    val steps: List<String>,
    val notes: List<String> = emptyList(),
    val imageUrl: String? = null,
    val videoUrl: String? = null,
)
