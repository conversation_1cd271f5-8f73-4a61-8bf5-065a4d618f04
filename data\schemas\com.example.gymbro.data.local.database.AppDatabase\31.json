{"formatVersion": 1, "database": {"version": 31, "identityHash": "60c6684fd1e6cc14a952b010966b537d", "entities": [{"tableName": "users", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`user_id` TEXT NOT NULL, `email` TEXT, `username` TEXT NOT NULL, `displayName` TEXT, `photoUrl` TEXT, `phoneNumber` TEXT, `isActive` INTEGER NOT NULL, `isEmailVerified` INTEGER NOT NULL, `wechatId` TEXT, `anonymousId` TEXT, `gender` TEXT, `weight` REAL, `weightUnit` TEXT, `fitnessLevel` INTEGER, `preferredGym` TEXT, `avatar` TEXT, `bio` TEXT, `themeMode` TEXT NOT NULL, `languageCode` TEXT NOT NULL, `measurementSystem` TEXT NOT NULL, `notificationsEnabled` INTEGER NOT NULL, `soundsEnabled` INTEGER NOT NULL, `locationSharingEnabled` INTEGER NOT NULL, `settingsJson` TEXT, `fitnessGoalsJson` TEXT, `privacySettingsJson` TEXT, `workoutDaysJson` TEXT, `preferredWorkoutTimesJson` TEXT, `preferredFoodsJson` TEXT, `notificationSettingsJson` TEXT, `soundSettingsJson` TEXT, `backupSettingsJson` TEXT, `partnerMatchPreferencesJson` TEXT, `blockedUsersJson` TEXT, `allowPartnerMatching` INTEGER NOT NULL, `weeklyActiveMinutes` INTEGER NOT NULL, `likesReceived` INTEGER NOT NULL, `createdAt` INTEGER, `lastLoginAt` INTEGER, `isSynced` INTEGER NOT NULL, `lastSynced` INTEGER NOT NULL, `lastModified` INTEGER NOT NULL, `serverUpdatedAt` INTEGER NOT NULL, `isAnonymous` INTEGER NOT NULL, `userType` TEXT NOT NULL, `subscriptionPlan` TEXT NOT NULL, `subscriptionExpiryDate` INTEGER, PRIMARY KEY(`user_id`))", "fields": [{"fieldPath": "userId", "columnName": "user_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "email", "columnName": "email", "affinity": "TEXT", "notNull": false}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT", "notNull": true}, {"fieldPath": "displayName", "columnName": "displayName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "photoUrl", "columnName": "photoUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "phoneNumber", "columnName": "phoneNumber", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isActive", "columnName": "isActive", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isEmailVerified", "columnName": "isEmailVerified", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "wechatId", "columnName": "wechatId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "anonymousId", "columnName": "anonymousId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "gender", "columnName": "gender", "affinity": "TEXT", "notNull": false}, {"fieldPath": "weight", "columnName": "weight", "affinity": "REAL", "notNull": false}, {"fieldPath": "weightUnit", "columnName": "weightUnit", "affinity": "TEXT", "notNull": false}, {"fieldPath": "fitnessLevel", "columnName": "fitnessLevel", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "preferredGym", "columnName": "preferredGym", "affinity": "TEXT", "notNull": false}, {"fieldPath": "avatar", "columnName": "avatar", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bio", "columnName": "bio", "affinity": "TEXT", "notNull": false}, {"fieldPath": "themeMode", "columnName": "themeMode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "languageCode", "columnName": "languageCode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "measurementSystem", "columnName": "measurementSystem", "affinity": "TEXT", "notNull": true}, {"fieldPath": "notificationsEnabled", "columnName": "notificationsEnabled", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "soundsEnabled", "columnName": "soundsEnabled", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "locationSharingEnabled", "columnName": "locationSharingEnabled", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "<PERSON><PERSON><PERSON>", "columnName": "<PERSON><PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "fitnessGoals<PERSON>son", "columnName": "fitnessGoals<PERSON>son", "affinity": "TEXT", "notNull": false}, {"fieldPath": "privacySettingsJson", "columnName": "privacySettingsJson", "affinity": "TEXT", "notNull": false}, {"fieldPath": "workoutDaysJson", "columnName": "workoutDaysJson", "affinity": "TEXT", "notNull": false}, {"fieldPath": "preferredWorkoutTimesJson", "columnName": "preferredWorkoutTimesJson", "affinity": "TEXT", "notNull": false}, {"fieldPath": "preferred<PERSON><PERSON>s<PERSON><PERSON>", "columnName": "preferred<PERSON><PERSON>s<PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "notificationSettingsJson", "columnName": "notificationSettingsJson", "affinity": "TEXT", "notNull": false}, {"fieldPath": "soundSettingsJson", "columnName": "soundSettingsJson", "affinity": "TEXT", "notNull": false}, {"fieldPath": "backupSettings<PERSON><PERSON>", "columnName": "backupSettings<PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "partnerMatchPreferencesJson", "columnName": "partnerMatchPreferencesJson", "affinity": "TEXT", "notNull": false}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "columnName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "allowPartnerMatching", "columnName": "allowPartnerMatching", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "weeklyActiveMinutes", "columnName": "weeklyActiveMinutes", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "likesReceived", "columnName": "likesReceived", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "lastLoginAt", "columnName": "lastLoginAt", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "isSynced", "columnName": "isSynced", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastSynced", "columnName": "lastSynced", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastModified", "columnName": "lastModified", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "serverUpdatedAt", "columnName": "serverUpdatedAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isAnonymous", "columnName": "isAnonymous", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userType", "columnName": "userType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "subscriptionPlan", "columnName": "subscriptionPlan", "affinity": "TEXT", "notNull": true}, {"fieldPath": "subscriptionExpiryDate", "columnName": "subscriptionExpiryDate", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["user_id"]}, "indices": [{"name": "index_users_email", "unique": false, "columnNames": ["email"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_users_email` ON `${TABLE_NAME}` (`email`)"}, {"name": "index_users_isActive", "unique": false, "columnNames": ["isActive"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_users_isActive` ON `${TABLE_NAME}` (`isActive`)"}, {"name": "index_users_userType", "unique": false, "columnNames": ["userType"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_users_userType` ON `${TABLE_NAME}` (`userType`)"}, {"name": "index_users_lastLoginAt", "unique": false, "columnNames": ["lastLoginAt"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_users_lastLoginAt` ON `${TABLE_NAME}` (`lastLoginAt`)"}, {"name": "index_users_synced", "unique": false, "columnNames": ["isSynced"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_users_synced` ON `${TABLE_NAME}` (`isSynced`)"}], "foreignKeys": []}, {"tableName": "user_profiles", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` TEXT NOT NULL, `username` TEXT, `displayName` TEXT, `email` TEXT, `phoneNumber` TEXT, `profileImageUrl` TEXT, `bio` TEXT, `gender` TEXT, `height` REAL, `heightUnit` TEXT, `weight` REAL, `weightUnit` TEXT, `fitnessLevel` INTEGER, `fitnessGoals` TEXT NOT NULL, `workoutDays` TEXT NOT NULL, `allowPartnerMatching` INTEGER NOT NULL, `totalWorkoutCount` INTEGER NOT NULL, `weeklyActiveMinutes` INTEGER NOT NULL, `likesReceived` INTEGER NOT NULL, `isAnonymous` INTEGER NOT NULL, `hasValidSubscription` INTEGER NOT NULL, `lastUpdated` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, `profileSummary` TEXT, `vector` BLOB, `vectorCreatedAt` INTEGER, PRIMARY KEY(`userId`))", "fields": [{"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT", "notNull": false}, {"fieldPath": "displayName", "columnName": "displayName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "email", "columnName": "email", "affinity": "TEXT", "notNull": false}, {"fieldPath": "phoneNumber", "columnName": "phoneNumber", "affinity": "TEXT", "notNull": false}, {"fieldPath": "profileImageUrl", "columnName": "profileImageUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bio", "columnName": "bio", "affinity": "TEXT", "notNull": false}, {"fieldPath": "gender", "columnName": "gender", "affinity": "TEXT", "notNull": false}, {"fieldPath": "height", "columnName": "height", "affinity": "REAL", "notNull": false}, {"fieldPath": "heightUnit", "columnName": "heightUnit", "affinity": "TEXT", "notNull": false}, {"fieldPath": "weight", "columnName": "weight", "affinity": "REAL", "notNull": false}, {"fieldPath": "weightUnit", "columnName": "weightUnit", "affinity": "TEXT", "notNull": false}, {"fieldPath": "fitnessLevel", "columnName": "fitnessLevel", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "fitnessGoals", "columnName": "fitnessGoals", "affinity": "TEXT", "notNull": true}, {"fieldPath": "workoutDays", "columnName": "workoutDays", "affinity": "TEXT", "notNull": true}, {"fieldPath": "allowPartnerMatching", "columnName": "allowPartnerMatching", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalWorkoutCount", "columnName": "totalWorkoutCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "weeklyActiveMinutes", "columnName": "weeklyActiveMinutes", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "likesReceived", "columnName": "likesReceived", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isAnonymous", "columnName": "isAnonymous", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "hasValidSubscription", "columnName": "hasValidSubscription", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastUpdated", "columnName": "lastUpdated", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "profile<PERSON>ummary", "columnName": "profile<PERSON>ummary", "affinity": "TEXT", "notNull": false}, {"fieldPath": "vector", "columnName": "vector", "affinity": "BLOB", "notNull": false}, {"fieldPath": "vectorCreatedAt", "columnName": "vectorCreatedAt", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["userId"]}, "indices": [], "foreignKeys": []}, {"tableName": "user_settings", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` TEXT NOT NULL, `themeMode` TEXT NOT NULL, `languageCode` TEXT NOT NULL, `measurementSystem` TEXT NOT NULL, `notificationsEnabled` INTEGER NOT NULL, `soundsEnabled` INTEGER NOT NULL, `locationSharingEnabled` INTEGER NOT NULL, `dataSharingEnabled` INTEGER NOT NULL, `allowWorkoutSharing` INTEGER NOT NULL, `autoBackupEnabled` INTEGER NOT NULL, `backupFrequency` INTEGER NOT NULL, `lastBackupTime` INTEGER NOT NULL, `allowPartnerMatching` INTEGER NOT NULL, `preferredMatchDistance` INTEGER NOT NULL, `matchByFitnessLevel` INTEGER NOT NULL, `lastModified` INTEGER NOT NULL, PRIMARY KEY(`userId`), FOREI<PERSON><PERSON> KEY(`userId`) REFERENCES `users`(`user_id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "themeMode", "columnName": "themeMode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "languageCode", "columnName": "languageCode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "measurementSystem", "columnName": "measurementSystem", "affinity": "TEXT", "notNull": true}, {"fieldPath": "notificationsEnabled", "columnName": "notificationsEnabled", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "soundsEnabled", "columnName": "soundsEnabled", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "locationSharingEnabled", "columnName": "locationSharingEnabled", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dataSharingEnabled", "columnName": "dataSharingEnabled", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "allowWorkoutSharing", "columnName": "allowWorkoutSharing", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "autoBackupEnabled", "columnName": "autoBackupEnabled", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "backupFrequency", "columnName": "backupFrequency", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastBackupTime", "columnName": "lastBackupTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "allowPartnerMatching", "columnName": "allowPartnerMatching", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "preferredMatchDistance", "columnName": "preferredMatchDistance", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "matchByFitnessLevel", "columnName": "matchByFitnessLevel", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastModified", "columnName": "lastModified", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["userId"]}, "indices": [{"name": "index_user_settings_userId", "unique": true, "columnNames": ["userId"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_user_settings_userId` ON `${TABLE_NAME}` (`userId`)"}], "foreignKeys": [{"table": "users", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["userId"], "referencedColumns": ["user_id"]}]}, {"tableName": "tokens", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `accessToken` TEXT NOT NULL, `refreshToken` TEXT NOT NULL, `tokenType` TEXT NOT NULL, `expiresIn` INTEGER NOT NULL, `issuedAt` INTEGER NOT NULL, `userId` TEXT NOT NULL, `scope` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "accessToken", "columnName": "accessToken", "affinity": "TEXT", "notNull": true}, {"fieldPath": "refreshToken", "columnName": "refreshToken", "affinity": "TEXT", "notNull": true}, {"fieldPath": "tokenType", "columnName": "tokenType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "expiresIn", "columnName": "expiresIn", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "issuedAt", "columnName": "issuedAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "scope", "columnName": "scope", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "search_content", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `content` TEXT NOT NULL, `embedding` TEXT, `metadata` TEXT NOT NULL, `created_at` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": true}, {"fieldPath": "embedding", "columnName": "embedding", "affinity": "TEXT", "notNull": false}, {"fieldPath": "metadata", "columnName": "metadata", "affinity": "TEXT", "notNull": true}, {"fieldPath": "createdAt", "columnName": "created_at", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "calendar_events", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `user_id` TEXT NOT NULL, `workout_id` TEXT, `event_type` TEXT NOT NULL, `date` INTEGER NOT NULL, `title` TEXT NOT NULL, `description` TEXT, `duration_minutes` INTEGER, `color` TEXT, `is_all_day` INTEGER NOT NULL, `reminder_minutes_before` INTEGER, `is_completed` INTEGER NOT NULL, `completion_date` INTEGER, `cancelled` INTEGER NOT NULL, `recurrence_rule` TEXT, `created_at` INTEGER NOT NULL, `modified_at` INTEGER NOT NULL, `is_synced` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "user_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "workoutId", "columnName": "workout_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "eventType", "columnName": "event_type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "date", "columnName": "date", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": false}, {"fieldPath": "durationMinutes", "columnName": "duration_minutes", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "color", "columnName": "color", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isAllDay", "columnName": "is_all_day", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "reminderMinutesBefore", "columnName": "reminder_minutes_before", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "isCompleted", "columnName": "is_completed", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "completionDate", "columnName": "completion_date", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "cancelled", "columnName": "cancelled", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "recurrenceRule", "columnName": "recurrence_rule", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createdAt", "columnName": "created_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "modifiedAt", "columnName": "modified_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isSynced", "columnName": "is_synced", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_calendar_events_user_id", "unique": false, "columnNames": ["user_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_calendar_events_user_id` ON `${TABLE_NAME}` (`user_id`)"}, {"name": "index_calendar_events_workout_id", "unique": false, "columnNames": ["workout_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_calendar_events_workout_id` ON `${TABLE_NAME}` (`workout_id`)"}, {"name": "index_calendar_events_date", "unique": false, "columnNames": ["date"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_calendar_events_date` ON `${TABLE_NAME}` (`date`)"}], "foreignKeys": []}, {"tableName": "chat_raw", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `session_id` TEXT NOT NULL, `role` TEXT NOT NULL, `content` TEXT NOT NULL, `timestamp` INTEGER NOT NULL, `metadata` TEXT NOT NULL, `message_id` TEXT NOT NULL, `in_reply_to_message_id` TEXT, `thinking_nodes` TEXT, `final_markdown` TEXT, FOREIGN KEY(`session_id`) REFERENCES `chat_sessions`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sessionId", "columnName": "session_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "role", "columnName": "role", "affinity": "TEXT", "notNull": true}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "metadata", "columnName": "metadata", "affinity": "TEXT", "notNull": true}, {"fieldPath": "messageId", "columnName": "message_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "inReplyToMessageId", "columnName": "in_reply_to_message_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "thinkingNodes", "columnName": "thinking_nodes", "affinity": "TEXT", "notNull": false}, {"fieldPath": "finalMarkdown", "columnName": "final_markdown", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_chat_raw_session_id", "unique": false, "columnNames": ["session_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_chat_raw_session_id` ON `${TABLE_NAME}` (`session_id`)"}, {"name": "index_chat_raw_role", "unique": false, "columnNames": ["role"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_chat_raw_role` ON `${TABLE_NAME}` (`role`)"}, {"name": "index_chat_raw_timestamp", "unique": false, "columnNames": ["timestamp"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_chat_raw_timestamp` ON `${TABLE_NAME}` (`timestamp`)"}, {"name": "index_chat_raw_session_id_timestamp", "unique": false, "columnNames": ["session_id", "timestamp"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_chat_raw_session_id_timestamp` ON `${TABLE_NAME}` (`session_id`, `timestamp`)"}], "foreignKeys": [{"table": "chat_sessions", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["session_id"], "referencedColumns": ["id"]}]}, {"ftsVersion": "FTS4", "ftsOptions": {"tokenizer": "simple", "tokenizerArgs": [], "contentTable": "", "languageIdColumnName": "", "matchInfo": "FTS4", "notIndexedColumns": [], "prefixSizes": [], "preferredOrder": "ASC"}, "contentSyncTriggers": [], "tableName": "chat_fts", "createSql": "CREATE VIRTUAL TABLE IF NOT EXISTS `${TABLE_NAME}` USING FTS4(`content` TEXT NOT NULL)", "fields": [{"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": []}, "indices": [], "foreignKeys": []}, {"tableName": "chat_sessions", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `title` TEXT NOT NULL, `user_id` TEXT NOT NULL, `created_at` INTEGER NOT NULL, `last_active_at` INTEGER NOT NULL, `is_active` INTEGER NOT NULL, `status` INTEGER NOT NULL, `message_count` INTEGER NOT NULL, `summary` TEXT, `metadata` TEXT, `db_created_at` INTEGER NOT NULL, `db_updated_at` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "user_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "createdAt", "columnName": "created_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastActiveAt", "columnName": "last_active_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isActive", "columnName": "is_active", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "status", "columnName": "status", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "messageCount", "columnName": "message_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "summary", "columnName": "summary", "affinity": "TEXT", "notNull": false}, {"fieldPath": "metadata", "columnName": "metadata", "affinity": "TEXT", "notNull": false}, {"fieldPath": "dbCreated<PERSON>t", "columnName": "db_created_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dbUpdatedAt", "columnName": "db_updated_at", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_chat_sessions_user_id", "unique": false, "columnNames": ["user_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_chat_sessions_user_id` ON `${TABLE_NAME}` (`user_id`)"}, {"name": "index_chat_sessions_user_id_is_active", "unique": false, "columnNames": ["user_id", "is_active"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_chat_sessions_user_id_is_active` ON `${TABLE_NAME}` (`user_id`, `is_active`)"}, {"name": "index_chat_sessions_user_id_last_active_at", "unique": false, "columnNames": ["user_id", "last_active_at"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_chat_sessions_user_id_last_active_at` ON `${TABLE_NAME}` (`user_id`, `last_active_at`)"}, {"name": "index_chat_sessions_created_at", "unique": false, "columnNames": ["created_at"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_chat_sessions_created_at` ON `${TABLE_NAME}` (`created_at`)"}, {"name": "index_chat_sessions_status", "unique": false, "columnNames": ["status"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_chat_sessions_status` ON `${TABLE_NAME}` (`status`)"}], "foreignKeys": []}, {"tableName": "chat_vec", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `embedding` BLOB NOT NULL, `embedding_dim` INTEGER NOT NULL, `created_at` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "embedding", "columnName": "embedding", "affinity": "BLOB", "notNull": true}, {"fieldPath": "embedding<PERSON>im", "columnName": "embedding_dim", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "created_at", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "message_embedding", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `message_id` INTEGER NOT NULL, `vector` BLOB NOT NULL, `vector_dim` INTEGER NOT NULL, `embedding_status` TEXT NOT NULL, `created_at` INTEGER NOT NULL, `model_version` TEXT NOT NULL, `generation_time_ms` INTEGER, `text_length` INTEGER NOT NULL, FOREIGN KEY(`message_id`) REFERENCES `chat_raw`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "messageId", "columnName": "message_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "vector", "columnName": "vector", "affinity": "BLOB", "notNull": true}, {"fieldPath": "vectorDim", "columnName": "vector_dim", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "embedding<PERSON><PERSON><PERSON>", "columnName": "embedding_status", "affinity": "TEXT", "notNull": true}, {"fieldPath": "createdAt", "columnName": "created_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "modelVersion", "columnName": "model_version", "affinity": "TEXT", "notNull": true}, {"fieldPath": "generationTimeMs", "columnName": "generation_time_ms", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "textLength", "columnName": "text_length", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_message_embedding_message_id", "unique": true, "columnNames": ["message_id"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_message_embedding_message_id` ON `${TABLE_NAME}` (`message_id`)"}, {"name": "index_message_embedding_created_at", "unique": false, "columnNames": ["created_at"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_message_embedding_created_at` ON `${TABLE_NAME}` (`created_at`)"}, {"name": "index_message_embedding_embedding_status", "unique": false, "columnNames": ["embedding_status"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_message_embedding_embedding_status` ON `${TABLE_NAME}` (`embedding_status`)"}], "foreignKeys": [{"table": "chat_raw", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["message_id"], "referencedColumns": ["id"]}]}, {"tableName": "session_summary", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `session_id` TEXT NOT NULL, `range_start` INTEGER NOT NULL, `range_end` INTEGER NOT NULL, `summary_content` TEXT NOT NULL, `summary_type` TEXT NOT NULL, `created_at` INTEGER NOT NULL, `original_message_count` INTEGER NOT NULL, `original_token_count` INTEGER NOT NULL, `summary_token_count` INTEGER NOT NULL, `compression_ratio` REAL NOT NULL, `model_used` TEXT NOT NULL, `generation_time_ms` INTEGER, `quality_score` REAL, FOREIGN KEY(`session_id`) REFERENCES `chat_sessions`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sessionId", "columnName": "session_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "rangeStart", "columnName": "range_start", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "rangeEnd", "columnName": "range_end", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "summaryContent", "columnName": "summary_content", "affinity": "TEXT", "notNull": true}, {"fieldPath": "summaryType", "columnName": "summary_type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "createdAt", "columnName": "created_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "originalMessageCount", "columnName": "original_message_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "originalTokenCount", "columnName": "original_token_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "summaryTokenCount", "columnName": "summary_token_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "compressionRatio", "columnName": "compression_ratio", "affinity": "REAL", "notNull": true}, {"fieldPath": "modelUsed", "columnName": "model_used", "affinity": "TEXT", "notNull": true}, {"fieldPath": "generationTimeMs", "columnName": "generation_time_ms", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "qualityScore", "columnName": "quality_score", "affinity": "REAL", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_session_summary_session_id", "unique": false, "columnNames": ["session_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_session_summary_session_id` ON `${TABLE_NAME}` (`session_id`)"}, {"name": "index_session_summary_range_start_range_end", "unique": false, "columnNames": ["range_start", "range_end"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_session_summary_range_start_range_end` ON `${TABLE_NAME}` (`range_start`, `range_end`)"}, {"name": "index_session_summary_created_at", "unique": false, "columnNames": ["created_at"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_session_summary_created_at` ON `${TABLE_NAME}` (`created_at`)"}, {"name": "index_session_summary_summary_type", "unique": false, "columnNames": ["summary_type"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_session_summary_summary_type` ON `${TABLE_NAME}` (`summary_type`)"}], "foreignKeys": [{"table": "chat_sessions", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["session_id"], "referencedColumns": ["id"]}]}, {"tableName": "memory_records", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `user_id` TEXT NOT NULL, `tier` TEXT NOT NULL, `created_at` INTEGER NOT NULL, `expires_at` INTEGER, `importance` INTEGER NOT NULL, `embedding` BLOB, `embedding_dim` INTEGER NOT NULL, `embedding_status` TEXT NOT NULL, `payload_json` TEXT NOT NULL, `content_length` INTEGER NOT NULL, `model_version` TEXT NOT NULL, `generation_time_ms` INTEGER, `updated_at` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "user_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "tier", "columnName": "tier", "affinity": "TEXT", "notNull": true}, {"fieldPath": "createdAt", "columnName": "created_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "expiresAt", "columnName": "expires_at", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "importance", "columnName": "importance", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "embedding", "columnName": "embedding", "affinity": "BLOB", "notNull": false}, {"fieldPath": "embedding<PERSON>im", "columnName": "embedding_dim", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "embedding<PERSON><PERSON><PERSON>", "columnName": "embedding_status", "affinity": "TEXT", "notNull": true}, {"fieldPath": "payloadJson", "columnName": "payload_json", "affinity": "TEXT", "notNull": true}, {"fieldPath": "contentLength", "columnName": "content_length", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "modelVersion", "columnName": "model_version", "affinity": "TEXT", "notNull": true}, {"fieldPath": "generationTimeMs", "columnName": "generation_time_ms", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "updatedAt", "columnName": "updated_at", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_memory_records_user_id", "unique": false, "columnNames": ["user_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_memory_records_user_id` ON `${TABLE_NAME}` (`user_id`)"}, {"name": "index_memory_records_tier", "unique": false, "columnNames": ["tier"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_memory_records_tier` ON `${TABLE_NAME}` (`tier`)"}, {"name": "index_memory_records_user_id_tier", "unique": false, "columnNames": ["user_id", "tier"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_memory_records_user_id_tier` ON `${TABLE_NAME}` (`user_id`, `tier`)"}, {"name": "index_memory_records_user_id_tier_created_at", "unique": false, "columnNames": ["user_id", "tier", "created_at"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_memory_records_user_id_tier_created_at` ON `${TABLE_NAME}` (`user_id`, `tier`, `created_at`)"}, {"name": "index_memory_records_expires_at", "unique": false, "columnNames": ["expires_at"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_memory_records_expires_at` ON `${TABLE_NAME}` (`expires_at`)"}, {"name": "index_memory_records_importance", "unique": false, "columnNames": ["importance"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_memory_records_importance` ON `${TABLE_NAME}` (`importance`)"}, {"name": "index_memory_records_embedding_status", "unique": false, "columnNames": ["embedding_status"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_memory_records_embedding_status` ON `${TABLE_NAME}` (`embedding_status`)"}, {"name": "index_memory_records_created_at", "unique": false, "columnNames": ["created_at"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_memory_records_created_at` ON `${TABLE_NAME}` (`created_at`)"}], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '60c6684fd1e6cc14a952b010966b537d')"]}}