package com.example.gymbro.core.ml.di

import com.example.gymbro.core.ml.embedding.BgeEmbeddingEngine
import com.example.gymbro.core.ml.embedding.EmbeddingEngine
import com.example.gymbro.core.ml.impl.BgeWorkoutStateVectorizer
import com.example.gymbro.core.ml.impl.InMemoryVectorSearchEngine
import com.example.gymbro.core.ml.impl.PureMemoryExerciseEngine
import com.example.gymbro.core.ml.interfaces.ExerciseMatchingEngine
import com.example.gymbro.core.ml.interfaces.VectorSearchEngine
import com.example.gymbro.core.ml.interfaces.WorkoutStateVectorizer
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Core-ML模块的依赖注入配置
 *
 * 绑定所有ML算法接口和实现
 * 不依赖任何上层组件，保持core-ml层的纯净性
 */
@Module
@InstallIn(SingletonComponent::class)
interface CoreMlModule {

    /**
     * 绑定训练状态向量化服务
     */
    @Binds
    @Singleton
    fun bindWorkoutStateVectorizer(
        impl: BgeWorkoutStateVectorizer,
    ): WorkoutStateVectorizer

    /**
     * 绑定向量搜索引擎
     */
    @Binds
    @Singleton
    fun bindVectorSearchEngine(
        impl: InMemoryVectorSearchEngine,
    ): VectorSearchEngine

    /**
     * 绑定动作匹配引擎
     */
    @Binds
    @Singleton
    fun bindExerciseMatchingEngine(
        impl: PureMemoryExerciseEngine,
    ): ExerciseMatchingEngine

    /**
     * 绑定BGE嵌入引擎
     */
    @Binds
    @Singleton
    fun bindEmbeddingEngine(
        impl: BgeEmbeddingEngine,
    ): EmbeddingEngine
}
