package com.example.gymbro.data.coach.entity

import androidx.room.*
import com.example.gymbro.data.local.entity.ChatRaw

/**
 * 消息嵌入向量实体
 *
 * 基于 history补充.md 规范设计，存储BGE生成的768维向量
 * 支持RAG检索和语义搜索功能
 */
@Entity(
    tableName = "message_embedding",
    foreignKeys = [
        ForeignKey(
            entity = ChatRaw::class,
            parentColumns = ["id"],
            childColumns = ["message_id"],
            onDelete = ForeignKey.CASCADE,
        ),
    ],
    indices = [
        Index(value = ["message_id"], unique = true),
        Index(value = ["created_at"]),
        Index(value = ["embedding_status"]),
    ],
)
data class MessageEmbeddingEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    /**
     * 关联的消息ID (ChatRaw.id)
     */
    @ColumnInfo(name = "message_id")
    val messageId: Long,
    /**
     * BGE嵌入向量 (768维)
     * 存储为ByteArray以节省空间
     */
    @ColumnInfo(name = "vector", typeAffinity = ColumnInfo.BLOB)
    val vector: ByteArray,
    /**
     * 向量维度
     * 用于验证向量完整性
     */
    @ColumnInfo(name = "vector_dim")
    val vectorDim: Int = 768,
    /**
     * 嵌入状态
     * PENDING, PROCESSING, COMPLETED, FAILED
     */
    @ColumnInfo(name = "embedding_status")
    val embeddingStatus: String = "COMPLETED",
    /**
     * 向量生成时间戳
     */
    @ColumnInfo(name = "created_at")
    val createdAt: Long = System.currentTimeMillis(),
    /**
     * BGE模型版本
     * 用于向量兼容性检查
     */
    @ColumnInfo(name = "model_version")
    val modelVersion: String = "bge-small-zh-v1.5",
    /**
     * 向量生成耗时 (毫秒)
     * 用于性能监控
     */
    @ColumnInfo(name = "generation_time_ms")
    val generationTimeMs: Long? = null,
    /**
     * 原始文本长度
     * 用于性能分析
     */
    @ColumnInfo(name = "text_length")
    val textLength: Int = 0,
) {
    companion object {
        // 嵌入状态常量
        const val STATUS_PENDING = "PENDING"
        const val STATUS_PROCESSING = "PROCESSING"
        const val STATUS_COMPLETED = "COMPLETED"
        const val STATUS_FAILED = "FAILED"

        // BGE模型配置
        const val BGE_VECTOR_DIM = 768
        const val BGE_MODEL_VERSION = "bge-small-zh-v1.5"
    }

    /**
     * 将ByteArray向量转换为FloatArray
     */
    fun getVectorAsFloatArray(): FloatArray {
        val floatArray = FloatArray(vectorDim)
        val buffer = java.nio.ByteBuffer.wrap(vector)
        buffer.order(java.nio.ByteOrder.LITTLE_ENDIAN)

        for (i in 0 until vectorDim) {
            floatArray[i] = buffer.float
        }
        return floatArray
    }

    /**
     * 检查向量是否有效
     */
    fun isVectorValid(): Boolean =
        vector.size == vectorDim * 4 &&
            // 4 bytes per float
            embeddingStatus == STATUS_COMPLETED &&
            vectorDim == BGE_VECTOR_DIM

    /**
     * 重写equals方法
     * ByteArray需要特殊处理
     */
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as MessageEmbeddingEntity

        if (id != other.id) return false
        if (messageId != other.messageId) return false
        if (!vector.contentEquals(other.vector)) return false
        if (vectorDim != other.vectorDim) return false
        if (embeddingStatus != other.embeddingStatus) return false
        if (createdAt != other.createdAt) return false
        if (modelVersion != other.modelVersion) return false
        if (generationTimeMs != other.generationTimeMs) return false
        if (textLength != other.textLength) return false

        return true
    }

    /**
     * 重写hashCode方法
     * ByteArray需要特殊处理
     */
    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + messageId.hashCode()
        result = 31 * result + vector.contentHashCode()
        result = 31 * result + vectorDim
        result = 31 * result + embeddingStatus.hashCode()
        result = 31 * result + createdAt.hashCode()
        result = 31 * result + modelVersion.hashCode()
        result = 31 * result + (generationTimeMs?.hashCode() ?: 0)
        result = 31 * result + textLength
        return result
    }
}

/**
 * 向量搜索结果
 * 包含相似度分数的搜索结果
 */
data class VectorSearchResult(
    val messageId: Long,
    val similarity: Float,
    val embedding: MessageEmbeddingEntity,
)

/**
 * 混合搜索结果
 * 结合向量搜索和关键词搜索的结果
 */
data class HybridSearchResult(
    val messageId: Long,
    val vectorSimilarity: Float,
    val keywordScore: Float,
    val combinedScore: Float,
    val embedding: MessageEmbeddingEntity,
)

/**
 * 工具函数：将FloatArray转换为ByteArray
 */
fun FloatArray.toByteArray(): ByteArray {
    val buffer = java.nio.ByteBuffer.allocate(this.size * 4)
    buffer.order(java.nio.ByteOrder.LITTLE_ENDIAN)

    for (float in this) {
        buffer.putFloat(float)
    }
    return buffer.array()
}

/**
 * 工具函数：计算余弦相似度
 */
fun cosineSimilarity(
    vectorA: FloatArray,
    vectorB: FloatArray,
): Float {
    require(vectorA.size == vectorB.size) { "向量维度必须相同" }

    var dotProduct = 0.0f
    var normA = 0.0f
    var normB = 0.0f

    for (i in vectorA.indices) {
        dotProduct += vectorA[i] * vectorB[i]
        normA += vectorA[i] * vectorA[i]
        normB += vectorB[i] * vectorB[i]
    }

    return if (normA == 0.0f || normB == 0.0f) {
        0.0f
    } else {
        dotProduct / (kotlin.math.sqrt(normA) * kotlin.math.sqrt(normB))
    }
}
