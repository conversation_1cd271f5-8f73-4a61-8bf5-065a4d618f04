# RAG评分算法技术详解

## 🎯 概述

GymBro RAG（检索增强生成）评分系统采用多策略架构，支持A/B测试和动态算法切换，为AI教练提供智能的上下文检索能力。

## 🧠 核心算法

### 1. 默认评分策略 (DefaultScoringStrategy)

**特点**: 标准的时间衰减+会话惩罚算法
**复杂度**: O(1)
**适用场景**: 生产环境基线

```kotlin
fun calculateCombinedScore(
    vectorScore: Float,
    keywordScore: Float,
    hybridWeight: Float,
    timestamp: Long,
    isCurrentSession: Boolean
): Float {
    // 基础混合分数
    val baseScore = vectorScore * hybridWeight + keywordScore * (1 - hybridWeight)

    // 30天半衰期的指数衰减
    val daysSinceMessage = (System.currentTimeMillis() - timestamp) / MILLIS_PER_DAY
    val timeDecay = exp(-daysSinceMessage / TIME_DECAY_HALF_LIFE_DAYS)

    // 当前会话惩罚（避免检索当前会话内容）
    val sessionPenalty = if (isCurrentSession) CURRENT_SESSION_PENALTY else 1.0f

    return baseScore * timeDecay * sessionPenalty
}
```

### 2. 高级评分策略 (AdvancedScoringStrategy)

**特点**: 基于机器学习启发的多因素非线性算法
**复杂度**: O(1)
**适用场景**: 高质量要求场景

#### 核心创新:

##### 非线性混合权重
根据搜索结果质量动态调整权重：
```kotlin
private fun calculateNonlinearWeight(
    hybridWeight: Float,
    vectorScore: Float,
    keywordScore: Float
): Float {
    val scoreDifference = abs(vectorScore - keywordScore)
    val adaptiveAdjustment = scoreDifference * 0.2f

    return when {
        vectorScore > keywordScore -> (hybridWeight + adaptiveAdjustment).coerceIn(0f, 1f)
        keywordScore > vectorScore -> (hybridWeight - adaptiveAdjustment).coerceIn(0f, 1f)
        else -> hybridWeight
    }
}
```

##### 智能时间衰减
指数衰减 + 近期内容加成：
```kotlin
private fun calculateAdvancedTimeDecay(timestamp: Long): Float {
    val ageHours = (System.currentTimeMillis() - timestamp) / (1000f * 60f * 60f)

    // 7天半衰期的基础衰减
    val baseDecay = exp(-ageHours * TIME_DECAY_LAMBDA)

    // 24小时内内容获得1.2x加成
    val recentBoost = if (ageHours <= 24f) 1.2f else 1f

    return (baseDecay * recentBoost).coerceIn(0.1f, 1f)
}
```

##### 会话上下文感知
高质量内容的当前会话降权幅度更小：
```kotlin
private fun calculateSessionContextFactor(isCurrentSession: Boolean, baseScore: Float): Float {
    return if (isCurrentSession) {
        // 高分内容惩罚更轻
        val qualityAdjustment = baseScore * 0.5f
        CURRENT_SESSION_PENALTY + qualityAdjustment
    } else {
        CROSS_SESSION_BOOST // 跨会话内容轻微加成
    }
}
```

### 3. 实验性评分策略 (ExperimentalScoringStrategy)

**特点**: 激进的新鲜度导向算法
**复杂度**: O(1)
**适用场景**: 实时性要求场景

```kotlin
fun calculateCombinedScore(...): Float {
    // 极度重视向量相似度
    val boostedVectorScore = (vectorScore * 1.5f).coerceAtMost(1f)
    val dampedKeywordScore = keywordScore * 0.8f

    // 简单混合
    val baseScore = boostedVectorScore * hybridWeight + dampedKeywordScore * (1 - hybridWeight)

    // 极强的新鲜度权重（12小时半衰期）
    val hoursAge = (System.currentTimeMillis() - timestamp) / (1000f * 60f * 60f)
    val freshnessScore = exp(-hoursAge / 12f).pow(2.0f)

    // 最小会话惩罚
    val sessionFactor = if (isCurrentSession) 0.1f else 1f

    return (baseScore * freshnessScore * sessionFactor).coerceIn(0f, 1f)
}
```

### 4. 保守评分策略 (ConservativeScoringStrategy)

**特点**: 稳定均衡的传统算法
**复杂度**: O(1)
**适用场景**: 稳定性优先场景

```kotlin
fun calculateCombinedScore(...): Float {
    // 标准线性混合
    val baseScore = vectorScore * hybridWeight + keywordScore * (1 - hybridWeight)

    // 温和的时间衰减（每天2%衰减）
    val daysAge = (System.currentTimeMillis() - timestamp) / (1000f * 60f * 60f * 24f)
    val timeDecay = (1f - GENTLE_TIME_DECAY * daysAge).coerceAtLeast(0.3f)

    // 温和的会话惩罚
    val sessionFactor = if (isCurrentSession) 0.7f else 1f

    // 稳定性调节
    return (baseScore * timeDecay * sessionFactor * 0.9f).coerceIn(0.1f, 0.9f)
}
```

## 🧪 A/B测试框架

### ScoringStrategyManager

**职责**: 动态策略选择、A/B测试管理、性能监控

#### 核心功能:

##### 1. 用户分组算法
基于用户ID哈希值确保分组稳定性：
```kotlin
private fun getABTestStrategy(userId: String): StrategyType {
    val hash = userId.hashCode()
    val normalizedHash = (hash.toDouble() / Int.MAX_VALUE.toDouble()).coerceIn(0.0, 1.0)

    var cumulativePercentage = 0f
    for ((strategy, percentage) in currentABTestConfig.userGroupPercentages) {
        cumulativePercentage += percentage
        if (normalizedHash <= cumulativePercentage) {
            return strategy
        }
    }

    return StrategyType.DEFAULT // 兜底策略
}
```

##### 2. 性能基准测试
```kotlin
fun performBenchmark(testCases: List<ScoringTestCase>): ScoringBenchmarkReport {
    val results = mutableMapOf<StrategyType, BenchmarkResult>()

    StrategyType.values().forEach { strategyType ->
        val strategy = getStrategy(strategyType)
        val startTime = System.nanoTime()

        testCases.forEach { testCase ->
            strategy.calculateCombinedScore(...) // 执行计算
        }

        val endTime = System.nanoTime()
        val avgTimePerCall = (endTime - startTime) / testCases.size.toDouble()

        results[strategyType] = BenchmarkResult(avgTimePerCall, testCases.size)
    }

    return ScoringBenchmarkReport(results)
}
```

## 📊 算法对比分析

### 性能对比
| 策略         | 平均耗时 | 内存占用 | 计算复杂度   |
| ------------ | -------- | -------- | ------------ |
| Default      | ~0.05μs  | 0.5KB    | 简单线性     |
| Advanced     | ~0.12μs  | 0.5KB    | 多因子非线性 |
| Experimental | ~0.03μs  | 0.5KB    | 极简计算     |
| Conservative | ~0.04μs  | 0.5KB    | 温和线性     |

### 适用场景对比
| 策略         | 精确度 | 稳定性 | 实时性 | 推荐场景   |
| ------------ | ------ | ------ | ------ | ---------- |
| Default      | ⭐⭐⭐    | ⭐⭐⭐⭐   | ⭐⭐⭐    | 生产基线   |
| Advanced     | ⭐⭐⭐⭐⭐  | ⭐⭐⭐    | ⭐⭐     | 高质量要求 |
| Experimental | ⭐⭐     | ⭐⭐     | ⭐⭐⭐⭐⭐  | 实时场景   |
| Conservative | ⭐⭐⭐    | ⭐⭐⭐⭐⭐  | ⭐⭐⭐    | 稳定优先   |

## 🔧 使用示例

### 启动A/B测试
```kotlin
// 50%默认 vs 50%高级策略对比
scoringStrategyManager.startABTest(
    "算法效果评估",
    mapOf(
        StrategyType.DEFAULT to 0.5f,
        StrategyType.ADVANCED to 0.5f
    )
)
```

### 性能基准测试
```kotlin
val testCases = listOf(
    ScoringTestCase(0.8f, 0.6f, 0.7f, System.currentTimeMillis() - 3600000, false),
    ScoringTestCase(0.9f, 0.3f, 0.8f, System.currentTimeMillis() - 86400000, true)
)

val report = scoringStrategyManager.performBenchmark(testCases)
println("最快策略: ${report.getFastestStrategy()}")
```

### 手动策略切换
```kotlin
// 调试时强制使用实验策略
scoringStrategyManager.setManualOverride(StrategyType.EXPERIMENTAL)

// 恢复自动选择
scoringStrategyManager.setManualOverride(null)
```

## 🚀 算法调优指南

### 参数调优建议

#### DefaultScoringStrategy
- `TIME_DECAY_HALF_LIFE_DAYS`: 30天（适中衰减）
- `CURRENT_SESSION_PENALTY`: 0.5（中等惩罚）

#### AdvancedScoringStrategy
- `TIME_DECAY_LAMBDA`: 0.693f / 7f（7天半衰期）
- `RECENT_BOOST_FACTOR`: 1.2f（20%近期加成）
- `HIGH_CONFIDENCE_BOOST`: 1.3f（30%高置信度加成）

#### ExperimentalScoringStrategy
- `FRESHNESS_POWER`: 2.0f（强新鲜度权重）
- `VECTOR_BOOST`: 1.5f（50%向量加成）
- `SESSION_PENALTY`: 0.1f（最小会话惩罚）

#### ConservativeScoringStrategy
- `GENTLE_TIME_DECAY`: 0.02f（每天2%衰减）
- `STABILITY_FACTOR`: 0.9f（整体稳定性调节）

### 效果评估指标

1. **搜索精度**: 相关结果占比
2. **响应时间**: 算法计算耗时
3. **用户满意度**: 点击率、停留时间
4. **缓存命中率**: 结果重用效率

## 📈 监控与分析

### 关键指标监控
```kotlin
// 策略使用分布
val config = scoringStrategyManager.getCurrentConfig()
logger.i("当前A/B测试: ${config.currentTestName}")
logger.i("用户分组: ${config.userGroupDistribution}")

// 性能监控
val report = scoringStrategyManager.performBenchmark(testCases)
logger.i("性能最优策略: ${report.getFastestStrategy()}")
logger.i("性能比率: ${report.getPerformanceRatio(StrategyType.DEFAULT)}")
```

### 日志分析
- **RagContextRetrievalUseCase**: 检索过程日志
- **ScoringStrategyManager**: 策略选择日志
- **ResultMerger**: 结果合并日志

---

**🔗 相关文档**:
- [History模块总览README](../README.md)
- [GymBro架构文档](../../../../../../docs/02_Architecture_and_Design/)
- [测试指南](../../../../../test/)

**📊 性能报告**: 见 `ScoringBenchmarkTest` 测试结果
