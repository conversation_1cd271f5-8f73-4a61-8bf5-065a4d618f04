package com.example.gymbro.core.ml.tokenizer

import com.example.gymbro.core.ml.config.BgeModelConfig
import java.io.BufferedReader
import java.io.InputStream
import java.io.InputStreamReader

/**
 * BGE模型分词器
 *
 * 实现基于WordPiece的分词算法，兼容BERT/BGE模型的输入格式
 */
class BgeTokenizer(
    vocabularyStream: InputStream,
    private val maxLength: Int = BgeModelConfig.maxSequenceLength, // 🔥 使用统一配置
    private val doLowerCase: Boolean = true,
) {

    companion object {
        private const val UNK_TOKEN = "[UNK]"
        private const val CLS_TOKEN = "[CLS]"
        private const val SEP_TOKEN = "[SEP]"
        private const val PAD_TOKEN = "[PAD]"
        private const val MASK_TOKEN = "[MASK]"
    }

    private val vocab: Map<String, Int>
    private val idsToTokens: Map<Int, String>
    private val unkTokenId: Int
    private val clsTokenId: Int
    private val sepTokenId: Int
    private val padTokenId: Int

    init {
        vocab = loadVocabulary(vocabularyStream)
        idsToTokens = vocab.entries.associate { it.value to it.key }
        unkTokenId = vocab[UNK_TOKEN] ?: error("Vocabulary must contain $UNK_TOKEN")
        clsTokenId = vocab[CLS_TOKEN] ?: error("Vocabulary must contain $CLS_TOKEN")
        sepTokenId = vocab[SEP_TOKEN] ?: error("Vocabulary must contain $SEP_TOKEN")
        padTokenId = vocab[PAD_TOKEN] ?: error("Vocabulary must contain $PAD_TOKEN")
    }

    /**
     * 对文本进行分词处理
     *
     * @param text 输入文本
     * @return 分词结果
     */
    fun tokenize(text: String): TokenizationResult {
        val processedText = if (doLowerCase) text.lowercase() else text
        val tokens = basicTokenize(processedText)
        val wordPieceTokens = tokens.flatMap { wordPieceTokenize(it) }

        // 构建输入序列: [CLS] + tokens + [SEP]
        val inputTokens = mutableListOf<String>().apply {
            add(CLS_TOKEN)
            addAll(wordPieceTokens.take(maxLength - 2)) // 预留CLS和SEP位置
            add(SEP_TOKEN)
        }

        // 转换为ID并填充
        val inputIds = IntArray(maxLength) { padTokenId }
        val attentionMask = IntArray(maxLength) { 0 }

        for (i in inputTokens.indices) {
            if (i < maxLength) {
                inputIds[i] = vocab[inputTokens[i]] ?: unkTokenId
                attentionMask[i] = 1
            }
        }

        return TokenizationResult(
            inputIds = inputIds,
            attentionMask = attentionMask,
        )
    }

    /**
     * 基础分词：按空格和标点符号分割
     */
    private fun basicTokenize(text: String): List<String> {
        val tokens = mutableListOf<String>()
        var current = ""

        for (char in text) {
            when {
                char.isWhitespace() -> {
                    if (current.isNotEmpty()) {
                        tokens.add(current)
                        current = ""
                    }
                }
                isPunctuation(char) -> {
                    if (current.isNotEmpty()) {
                        tokens.add(current)
                        current = ""
                    }
                    tokens.add(char.toString())
                }
                isChinese(char) -> {
                    if (current.isNotEmpty()) {
                        tokens.add(current)
                        current = ""
                    }
                    tokens.add(char.toString())
                }
                else -> {
                    current += char
                }
            }
        }

        if (current.isNotEmpty()) {
            tokens.add(current)
        }

        return tokens
    }

    /**
     * WordPiece分词：将单词分解为子词
     */
    private fun wordPieceTokenize(word: String): List<String> {
        if (word.length > 100) {
            return listOf(UNK_TOKEN)
        }

        val tokens = mutableListOf<String>()
        var start = 0

        while (start < word.length) {
            var end = word.length
            var curSubStr: String? = null

            while (start < end) {
                var subStr = word.substring(start, end)
                if (start > 0) {
                    subStr = "##$subStr"
                }

                if (vocab.containsKey(subStr)) {
                    curSubStr = subStr
                    break
                }
                end--
            }

            if (curSubStr == null) {
                tokens.add(UNK_TOKEN)
                break
            }

            tokens.add(curSubStr)
            start = end
        }

        return tokens
    }

    /**
     * 加载词汇表
     */
    private fun loadVocabulary(stream: InputStream): Map<String, Int> {
        val vocab = mutableMapOf<String, Int>()
        BufferedReader(InputStreamReader(stream, "UTF-8")).use { reader ->
            var index = 0
            reader.forEachLine { line ->
                val token = line.trim()
                if (token.isNotEmpty()) {
                    vocab[token] = index++
                }
            }
        }
        return vocab
    }

    /**
     * 判断是否为标点符号
     */
    private fun isPunctuation(char: Char): Boolean {
        val cp = char.code
        return when {
            cp in 33..47 -> true
            cp in 58..64 -> true
            cp in 91..96 -> true
            cp in 123..126 -> true
            Character.getType(char) == Character.DASH_PUNCTUATION.toInt() -> true
            Character.getType(char) == Character.START_PUNCTUATION.toInt() -> true
            Character.getType(char) == Character.END_PUNCTUATION.toInt() -> true
            Character.getType(char) == Character.OTHER_PUNCTUATION.toInt() -> true
            else -> false
        }
    }

    /**
     * 判断是否为中文字符
     */
    private fun isChinese(char: Char): Boolean {
        val cp = char.code
        return cp in 0x4E00..0x9FFF || // CJK Unified Ideographs
            cp in 0x3400..0x4DBF || // CJK Extension A
            cp in 0x20000..0x2A6DF || // CJK Extension B
            cp in 0x2A700..0x2B73F || // CJK Extension C
            cp in 0x2B740..0x2B81F || // CJK Extension D
            cp in 0x2B820..0x2CEAF // CJK Extension E
    }
}
