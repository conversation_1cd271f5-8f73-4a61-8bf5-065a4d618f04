package com.example.gymbro.data.shared.initializer

import android.content.Context
import android.database.Cursor
import androidx.sqlite.db.SupportSQLiteDatabase
import com.example.gymbro.data.coach.dao.ChatRawDao
import com.example.gymbro.data.coach.dao.ChatSessionDao
import com.example.gymbro.data.local.database.AppDatabase
import com.example.gymbro.data.local.entity.ChatRaw
// ExerciseDataInitializer已移除，Exercise初始化由Exercise-Library模块自己负责
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import timber.log.Timber
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 数据库初始化服务
 * 专注于初始化数据库的预设内容，如默认训练动作、预置模板等
 */
@Singleton
class DatabaseInitializerService
@Inject
constructor(
    @ApplicationContext private val context: Context,
    private val database: AppDatabase,
    private val chatRawDao: ChatRawDao,
    private val chatSessionDao: ChatSessionDao,
    private val json: Json,
) {
    private val initializeScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    /**
     * 执行数据库初始化
     * 初始化应用首次启动或升级后需要的预设数据
     */
    fun initializeDatabase() {
        initializeScope.launch {
            try {
                Timber.d("开始数据库初始化...")

                // 检查是否已经初始化过
                if (isInitializationCompleted()) {
                    Timber.d("数据库已经初始化过，跳过初始化")
                    return@launch
                }

                // 训练动作数据初始化由Exercise-Library模块自己负责
                // initializeExerciseData() - 已移除

                // P0-3: FTS健康检查和修复
                performFtsHealthCheck()

                // P0-2: 历史消息备份回补
                restoreBackupMessages()

                // 初始化训练模板数据（可以根据实际需求添加）
                // initializeWorkoutTemplates()

                // 初始化训练计划（可以根据实际需求添加）
                // initializeWorkoutPlans()

                // 标记初始化已完成
                markInitializationCompleted()

                Timber.d("数据库初始化完成")
            } catch (e: Exception) {
                Timber.e(e, "数据库初始化失败: %s", e.message)
                recordInitializationError(e)
            }
        }
    }

    // initializeExerciseData方法已移除，由Exercise-Library模块自己负责

    /**
     * 检查是否已经完成整体初始化
     */
    private fun isInitializationCompleted(): Boolean {
        val sharedPrefs = context.getSharedPreferences("database_initialization", Context.MODE_PRIVATE)
        return sharedPrefs.getBoolean("db_initialized", false)
    }

    // isExerciseDataInitialized方法已移除，由Exercise-Library模块自己负责

    /**
     * 标记整体初始化已完成
     */
    private fun markInitializationCompleted() {
        val sharedPrefs = context.getSharedPreferences("database_initialization", Context.MODE_PRIVATE)
        sharedPrefs.edit().putBoolean("db_initialized", true).apply()
    }

    // markExerciseDataInitialized方法已移除，由Exercise-Library模块自己负责

    /**
     * P0-3: FTS健康检查和修复
     * 检查FTS索引是否损坏，如有需要进行重建
     */
    private suspend fun performFtsHealthCheck() {
        try {
            Timber.d("开始FTS健康检查...")

            val db = database.openHelper.writableDatabase

            // 1. 执行 PRAGMA quick_check
            var quickCheckCursor: Cursor? = null
            val isHealthy =
                try {
                    quickCheckCursor = db.query("PRAGMA quick_check")
                    if (quickCheckCursor?.moveToFirst() == true) {
                        val result = quickCheckCursor.getString(0)
                        result == "ok"
                    } else {
                        false
                    }
                } finally {
                    quickCheckCursor?.close()
                }

            if (isHealthy) {
                // 2. 检查FTS表是否存在且可查询
                var ftsCheckCursor: Cursor? = null
                val ftsWorking =
                    try {
                        ftsCheckCursor = db.query("SELECT COUNT(*) FROM chat_fts LIMIT 1")
                        if (ftsCheckCursor?.moveToFirst() == true) {
                            ftsCheckCursor.getInt(0) >= 0 // 能执行查询就说明FTS表正常
                        } else {
                            false
                        }
                    } catch (e: Exception) {
                        Timber.w(e, "FTS表查询失败")
                        false
                    } finally {
                        ftsCheckCursor?.close()
                    }

                if (ftsWorking) {
                    Timber.d("FTS健康检查通过")
                    markFtsHealthy()
                } else {
                    Timber.w("FTS表查询失败，开始重建")
                    rebuildFtsIndex()
                }
            } else {
                Timber.w("数据库integrity检查失败，开始FTS重建")
                rebuildFtsIndex()
            }
        } catch (e: Exception) {
            Timber.e(e, "FTS健康检查失败，尝试重建: ${e.message}")
            try {
                rebuildFtsIndex()
            } catch (rebuildError: Exception) {
                Timber.e(rebuildError, "FTS重建也失败了: ${rebuildError.message}")
                recordFtsError(rebuildError)
            }
        }
    }

    /**
     * 重建FTS索引
     */
    private suspend fun rebuildFtsIndex() {
        try {
            Timber.d("开始重建FTS索引...")

            val db = database.openHelper.writableDatabase

            // 1. 删除现有FTS表
            db.execSQL("DROP TABLE IF EXISTS chat_fts")

            // 2. 重新创建FTS5表
            db.execSQL(
                """
                    CREATE VIRTUAL TABLE chat_fts USING fts5(
                        content TEXT,
                        content='chat_raw',
                        content_rowid='id'
                    )
                """,
            )

            // 3. 从chat_raw重新填充数据
            db.execSQL(
                """
                    INSERT INTO chat_fts(docid, content)
                    SELECT id, content FROM chat_raw WHERE content IS NOT NULL
                """,
            )

            // 4. 重建触发器
            rebuildFtsTriggers(db)

            Timber.d("FTS索引重建完成")
            markFtsHealthy()
        } catch (e: Exception) {
            Timber.e(e, "FTS索引重建失败: ${e.message}")
            throw e
        }
    }

    /**
     * 重建FTS触发器
     */
    private fun rebuildFtsTriggers(db: SupportSQLiteDatabase) {
        // 删除旧触发器
        db.execSQL("DROP TRIGGER IF EXISTS chat_fts_insert_trigger")
        db.execSQL("DROP TRIGGER IF EXISTS chat_fts_update_trigger")
        db.execSQL("DROP TRIGGER IF EXISTS chat_fts_delete_trigger")

        // 创建新触发器
        db.execSQL(
            """
                CREATE TRIGGER chat_fts_insert_trigger
                AFTER INSERT ON chat_raw BEGIN
                    INSERT INTO chat_fts (docid, content) VALUES (NEW.id, NEW.content);
                END
            """,
        )

        db.execSQL(
            """
                CREATE TRIGGER chat_fts_update_trigger
                AFTER UPDATE ON chat_raw BEGIN
                    UPDATE chat_fts SET content = NEW.content WHERE docid = NEW.id;
                END
            """,
        )

        db.execSQL(
            """
                CREATE TRIGGER chat_fts_delete_trigger
                AFTER DELETE ON chat_raw BEGIN
                    DELETE FROM chat_fts WHERE docid = OLD.id;
                END
            """,
        )
    }

    /**
     * 标记FTS健康状态
     */
    private fun markFtsHealthy() {
        val sharedPrefs = context.getSharedPreferences("database_initialization", Context.MODE_PRIVATE)
        sharedPrefs
            .edit()
            .putBoolean("fts_healthy", true)
            .putLong("fts_last_check", System.currentTimeMillis())
            .apply()
    }

    /**
     * 记录FTS错误
     */
    private fun recordFtsError(e: Exception) {
        val sharedPrefs = context.getSharedPreferences("database_initialization", Context.MODE_PRIVATE)
        sharedPrefs
            .edit()
            .putBoolean("fts_healthy", false)
            .putString("fts_error_message", e.message ?: "Unknown FTS error")
            .putLong("fts_error_time", System.currentTimeMillis())
            .apply()
    }

    /**
     * P0-2: 备份消息数据类
     */
    @Serializable
    private data class BackupMessage(
        val sessionId: String,
        val messageId: String,
        val role: String,
        val content: String,
        val timestamp: Long,
        val backupTimestamp: Long,
    )

    /**
     * P0-2: 历史消息备份回补
     * 启动时检查并恢复备份文件中的消息
     */
    private suspend fun restoreBackupMessages() {
        try {
            val backupFile = File(context.filesDir, "chat_backup.json")
            if (!backupFile.exists()) {
                Timber.d("无备份文件需要恢复")
                return
            }

            Timber.d("开始恢复备份消息: ${backupFile.absolutePath}")

            val backupLines = backupFile.readLines().filter { it.isNotBlank() }
            if (backupLines.isEmpty()) {
                Timber.d("备份文件为空，删除文件")
                backupFile.delete()
                return
            }

            var successCount = 0
            var failCount = 0

            backupLines.forEach { line ->
                try {
                    val backupMessage = json.decodeFromString<BackupMessage>(line)

                    // 转换为ChatRaw实体
                    val chatRaw =
                        ChatRaw(
                            id = 0L, // 自动生成
                            sessionId = backupMessage.sessionId,
                            role =
                            when (backupMessage.role) {
                                "user" -> ChatRaw.ROLE_USER
                                "assistant" -> ChatRaw.ROLE_ASSISTANT
                                else -> ChatRaw.ROLE_USER
                            },
                            content = backupMessage.content,
                            timestamp = backupMessage.timestamp,
                            metadata = emptyMap(),
                            messageId = backupMessage.messageId,
                        )

                    // 尝试插入消息（使用IGNORE策略避免重复）
                    val insertedId = chatRawDao.insertChatMessage(chatRaw)
                    if (insertedId > 0) {
                        // 插入成功，更新会话消息计数
                        val messageCount =
                            chatRawDao.getChatMessageCountBySession(
                                backupMessage.sessionId,
                            )
                        chatSessionDao.updateMessageCount(backupMessage.sessionId, messageCount.toInt())

                        successCount++
                        Timber.d("备份消息恢复成功: messageId=${backupMessage.messageId}")
                    } else {
                        Timber.d("消息已存在，跳过: messageId=${backupMessage.messageId}")
                    }
                } catch (e: Exception) {
                    failCount++
                    Timber.w(e, "备份消息恢复失败: $line")
                }
            }

            // 恢复完成后删除备份文件
            if (successCount > 0 || failCount == 0) {
                backupFile.delete()
                Timber.i("备份消息恢复完成: 成功=$successCount, 失败=$failCount, 备份文件已删除")
            } else {
                Timber.w("备份消息恢复部分失败: 成功=$successCount, 失败=$failCount, 保留备份文件")
            }

            // 记录恢复统计
            recordBackupRestoreStats(successCount, failCount)
        } catch (e: Exception) {
            Timber.e(e, "备份消息恢复过程发生异常: ${e.message}")
            recordBackupRestoreError(e)
        }
    }

    /**
     * 记录备份恢复统计
     */
    private fun recordBackupRestoreStats(
        successCount: Int,
        failCount: Int,
    ) {
        val sharedPrefs = context.getSharedPreferences("database_initialization", Context.MODE_PRIVATE)
        sharedPrefs
            .edit()
            .putInt("backup_restore_success_count", successCount)
            .putInt("backup_restore_fail_count", failCount)
            .putLong("backup_restore_last_time", System.currentTimeMillis())
            .apply()
    }

    /**
     * 记录备份恢复错误
     */
    private fun recordBackupRestoreError(e: Exception) {
        val sharedPrefs = context.getSharedPreferences("database_initialization", Context.MODE_PRIVATE)
        sharedPrefs
            .edit()
            .putBoolean("backup_restore_error", true)
            .putString("backup_restore_error_message", e.message ?: "Unknown backup restore error")
            .putLong("backup_restore_error_time", System.currentTimeMillis())
            .apply()
    }

    /**
     * 记录初始化错误
     */
    private fun recordInitializationError(e: Exception) {
        val sharedPrefs = context.getSharedPreferences("database_initialization", Context.MODE_PRIVATE)
        sharedPrefs
            .edit()
            .putBoolean("initialization_error", true)
            .putString("initialization_error_message", e.message ?: "Unknown error")
            .putLong("initialization_error_time", System.currentTimeMillis())
            .apply()
    }
}
