package com.example.gymbro.di.feature.user

import com.example.gymbro.data.repository.user.FirebaseUserRepositoryImpl
// TODO: SyncableUserRepositoryImpl 不存在，如需要请创建实现类
// import com.example.gymbro.data.repository.user.SyncableUserRepositoryImpl
// TODO: UserRepositoryFacade 不存在，如需要请创建实现类
// import com.example.gymbro.data.repository.user.UserRepositoryFacade
// TODO: SyncableUserRepository 接口可能不存在
// import com.example.gymbro.domain.repository.SyncableUserRepository
import com.example.gymbro.domain.profile.repository.user.FirebaseUserRepository
// TODO: UserRepository 接口可能不存在
// import com.example.gymbro.domain.repository.user.UserRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 用户模块
 *
 * 提供用户相关的存储库实现
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class UserModule {
    // UserRepository绑定已迁移到di/data/repository/UserRepositoryModule
    // 使用基于UserDataCenter的统一实现

    // UserProfileRepository绑定已迁移到features/profile/internal/di/ProfileModule

    /**
     * 绑定Firebase用户存储库实现
     */
    @Binds
    @Singleton
    abstract fun bindFirebaseUserRepository(
        impl: FirebaseUserRepositoryImpl,
    ): FirebaseUserRepository
}
