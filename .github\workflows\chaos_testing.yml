name: Chaos Engineering Tests

on:
  pull_request:
    paths:
      - 'features/coach/**'
      - 'data/**/ai/**'
      - 'data/**/repository/aicoach/**'
  push:
    branches: [ main, develop ]

env:
  JAVA_VERSION: 17

jobs:
  chaos-interceptor-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 30

    strategy:
      matrix:
        api-level: [28, 33]
        chaos-level: [0.2, 0.4, 0.6]

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Setup JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v3
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: Setup Android SDK
      uses: android-actions/setup-android@v2

    - name: Enable KVM group perms
      run: |
        echo 'KERNEL=="kvm", GROUP="kvm", MODE="0666", OPTIONS+="static_node=kvm"' | sudo tee /etc/udev/rules.d/99-kvm4all.rules
        sudo udevadm control --reload-rules
        sudo udevadm trigger --name-match=kvm

    - name: Cache Gradle dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-

    - name: AVD cache
      uses: actions/cache@v3
      id: avd-cache
      with:
        path: |
          ~/.android/avd/*
          ~/.android/adb*
        key: avd-${{ matrix.api-level }}

    - name: Create AVD and generate snapshot for caching
      if: steps.avd-cache.outputs.cache-hit != 'true'
      uses: reactivecircus/android-emulator-runner@v2
      with:
        api-level: ${{ matrix.api-level }}
        target: google_apis
        arch: x86_64
        force-avd-creation: false
        emulator-options: -no-window -gpu swiftshader_indirect -noaudio -no-boot-anim -camera-back none
        disable-animations: false
        script: echo "Generated AVD snapshot for caching."

    - name: Run Chaos Interceptor Tests
      uses: reactivecircus/android-emulator-runner@v2
      with:
        api-level: ${{ matrix.api-level }}
        target: google_apis
        arch: x86_64
        force-avd-creation: false
        emulator-options: -no-snapshot-save -no-window -gpu swiftshader_indirect -noaudio -no-boot-anim -camera-back none
        disable-animations: true
        script: |
          # 设置混沌测试参数
          adb shell setprop debug.gymbro.chaos.enabled true
          adb shell setprop debug.gymbro.chaos.level ${{ matrix.chaos-level }}
          adb shell setprop debug.gymbro.chaos.seed $(date +%s)

          # 编译并安装应用
          ./gradlew assembleDebug assembleDebugAndroidTest

          # 运行AI Coach混沌测试
          ./gradlew connectedDebugAndroidTest \
            -Pandroid.testInstrumentationRunnerArguments.class=com.example.gymbro.features.coach.ui.AiCoachChaosTest \
            -Pandroid.testInstrumentationRunnerArguments.chaos_level=${{ matrix.chaos-level }} \
            --continue

    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: chaos-test-results-api${{ matrix.api-level }}-chaos${{ matrix.chaos-level }}
        path: |
          **/build/reports/androidTests/
          **/build/outputs/androidTest-results/

    - name: Upload test failure logs
      uses: actions/upload-artifact@v3
      if: failure()
      with:
        name: failure-logs-api${{ matrix.api-level }}-chaos${{ matrix.chaos-level }}
        path: |
          **/build/reports/
          **/logcat.txt

  state-machine-validation:
    runs-on: ubuntu-latest
    needs: chaos-interceptor-tests

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Setup JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v3
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: Run State Machine Unit Tests
      run: |
        ./gradlew test \
          --tests "*AiStateMachine*" \
          --tests "*AiCoachViewModel*" \
          --tests "*AICoachRepository*" \
          --continue

    - name: Validate State Machine Documentation
      run: |
        # 检查状态机文档是否与代码同步
        python3 scripts/validate_state_machine.py \
          --doc docs/state_machine.md \
          --code features/coach/src/main/kotlin/

    - name: Check Metrics Collection
      run: |
        # 验证所有状态转换都有对应的指标埋点
        grep -r "ai_message_count\|thinking_state_duration\|retry_attempt_count" \
          features/coach/src/main/kotlin/ || \
        (echo "❌ 缺少必要的指标埋点" && exit 1)

  performance-regression:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
    - name: Checkout base branch
      uses: actions/checkout@v4
      with:
        ref: ${{ github.base_ref }}
        path: base

    - name: Checkout PR branch
      uses: actions/checkout@v4
      with:
        path: pr

    - name: Setup JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v3
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: Run performance comparison
      run: |
        cd base && ./gradlew :features:coach:benchmarkDebug
        cd ../pr && ./gradlew :features:coach:benchmarkDebug

        # 比较性能指标
        python3 scripts/compare_performance.py \
          base/features/coach/build/reports/benchmark/ \
          pr/features/coach/build/reports/benchmark/

  security-analysis:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Run security analysis
      run: |
        # 检查是否有敏感信息泄露
        ./gradlew detekt

        # 检查依赖漏洞
        ./gradlew dependencyCheckAnalyze

        # 检查状态机是否有竞态条件
        python3 scripts/race_condition_detector.py \
          --target features/coach/src/main/kotlin/

  notification:
    runs-on: ubuntu-latest
    needs: [chaos-interceptor-tests, state-machine-validation, performance-regression]
    if: always()

    steps:
    - name: Notify on success
      if: needs.chaos-interceptor-tests.result == 'success' && needs.state-machine-validation.result == 'success'
      run: |
        echo "✅ 混沌工程测试通过，状态机验证成功"

    - name: Notify on failure
      if: needs.chaos-interceptor-tests.result == 'failure' || needs.state-machine-validation.result == 'failure'
      run: |
        echo "❌ 混沌工程测试失败，禁止合并PR"
        echo "::error::State machine tests failed - blocking merge"
        exit 1
