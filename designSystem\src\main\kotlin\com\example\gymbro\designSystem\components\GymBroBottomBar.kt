package com.example.gymbro.designSystem.components

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.LibraryBooks
import androidx.compose.material.icons.automirrored.outlined.LibraryBooks
import androidx.compose.material.icons.filled.FitnessCenter
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.SmartToy
import androidx.compose.material.icons.outlined.FitnessCenter
import androidx.compose.material.icons.outlined.Home
import androidx.compose.material.icons.outlined.SmartToy
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.NavigationBarItemDefaults
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.vector.ImageVector
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.motion.MotionSpecs
import com.example.gymbro.designSystem.theme.tokens.Tokens

/**
 * 底部导航栏动画类型
 */
enum class BottomBarAnimationType {
    /** 点击渐隐 + 缩放 (默认推荐) */
    FADE_SCALE,

    /** 弹性按压 */
    SPRING_PRESS,

    /** 图标旋转 + 渐隐 */
    ROTATE_FADE,

    /** 波纹扩散 + 缩放 */
    RIPPLE_SCALE,

    /** 无动画 */
    NONE,
}

/**
 * GymBro应用统一底部导航栏
 * 支持多种动画效果，默认使用点击渐隐0.1秒展示
 *
 * @param onHomeClick 主页点击回调
 * @param onExerciseLibraryClick 动作库点击回调
 * @param onCoachClick 教练点击回调
 * @param onWorkoutClick 训练点击回调
 * @param currentRoute 当前路由
 * @param animationType 动画类型，默认为FADE_SCALE
 * @param modifier 修饰符
 */
@Composable
fun GymBroBottomBar(
    onHomeClick: () -> Unit,
    onExerciseLibraryClick: () -> Unit,
    onCoachClick: () -> Unit,
    onWorkoutClick: () -> Unit,
    currentRoute: String,
    animationType: BottomBarAnimationType = BottomBarAnimationType.FADE_SCALE,
    modifier: Modifier = Modifier,
) {
    Surface(
        shadowElevation = Tokens.Elevation.Card,
    ) {
        NavigationBar(
            containerColor = MaterialTheme.colorScheme.surface,
            contentColor = MaterialTheme.colorScheme.onSurface,
        ) {
            // 主页按钮
            BottomNavItem(
                selected = currentRoute == "home" || currentRoute.contains("home"),
                onClick = onHomeClick,
                selectedIcon = Icons.Filled.Home,
                unselectedIcon = Icons.Outlined.Home,
                label = "主页",
                animationType = animationType,
            )

            // 动作库按钮
            BottomNavItem(
                selected = currentRoute == "exercise-library" || currentRoute.contains("exercise"),
                onClick = onExerciseLibraryClick,
                selectedIcon = Icons.AutoMirrored.Filled.LibraryBooks,
                unselectedIcon = Icons.AutoMirrored.Outlined.LibraryBooks,
                label = "动作库",
                animationType = animationType,
            )

            // AI教练按钮
            BottomNavItem(
                selected = currentRoute == "coach" || currentRoute.contains("coach"),
                onClick = onCoachClick,
                selectedIcon = Icons.Filled.SmartToy,
                unselectedIcon = Icons.Outlined.SmartToy,
                label = "教练",
                animationType = animationType,
            )

            // 训练按钮
            BottomNavItem(
                selected = currentRoute == "workout" || currentRoute.contains("workout") || currentRoute.contains("workout"),
                onClick = onWorkoutClick,
                selectedIcon = Icons.Filled.FitnessCenter,
                unselectedIcon = Icons.Outlined.FitnessCenter,
                label = "训练",
                animationType = animationType,
            )
        }
    }
}

/**
 * 底部导航项组件 - 支持多种动画效果
 */
@Composable
private fun RowScope.BottomNavItem(
    selected: Boolean,
    onClick: () -> Unit,
    selectedIcon: ImageVector,
    unselectedIcon: ImageVector,
    label: String,
    animationType: BottomBarAnimationType = BottomBarAnimationType.FADE_SCALE,
) {
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()

    // 根据动画类型应用不同的动画效果
    val iconModifier =
        when (animationType) {
            BottomBarAnimationType.FADE_SCALE -> Modifier.bottomBarFadeScaleAnimation(isPressed)
            BottomBarAnimationType.SPRING_PRESS -> Modifier.bottomBarSpringPressAnimation(isPressed)
            BottomBarAnimationType.ROTATE_FADE -> Modifier.bottomBarRotateFadeAnimation(isPressed, selected)
            BottomBarAnimationType.RIPPLE_SCALE -> Modifier.bottomBarRippleScaleAnimation(isPressed)
            BottomBarAnimationType.NONE -> Modifier
        }

    NavigationBarItem(
        selected = selected,
        onClick = onClick,
        interactionSource = interactionSource,
        icon = {
            Icon(
                imageVector = if (selected) selectedIcon else unselectedIcon,
                contentDescription = label,
                modifier = iconModifier.size(Tokens.Spacing.Large),
            )
        },
        label = { Text(text = label) },
        colors =
        NavigationBarItemDefaults.colors(
            selectedIconColor = MaterialTheme.colorScheme.primary,
            selectedTextColor = MaterialTheme.colorScheme.primary,
            indicatorColor = MaterialTheme.colorScheme.primaryContainer,
            unselectedIconColor = MaterialTheme.colorScheme.onSurfaceVariant,
            unselectedTextColor = MaterialTheme.colorScheme.onSurfaceVariant,
        ),
    )
}

/**
 * 点击渐隐 + 缩放动画 (推荐)
 * 0.1秒渐隐到80%透明度 + 轻微缩放
 */
@Composable
private fun Modifier.bottomBarFadeScaleAnimation(isPressed: Boolean): Modifier {
    val alpha by animateFloatAsState(
        targetValue = if (isPressed) 0.8f else 1f,
        animationSpec = tween(100), // 0.1秒
        label = "bottomBarAlpha",
    )
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.97f else 1f,
        animationSpec = MotionSpecs.Button.FluidInteraction.INSTANT_STATE_CHANGE(),
        label = "bottomBarScale",
    )

    return this.graphicsLayer {
        this.alpha = alpha
        scaleX = scale
        scaleY = scale
    }
}

/**
 * 弹性按压动画
 * 使用项目标准的弹簧动画
 */
@Composable
private fun Modifier.bottomBarSpringPressAnimation(isPressed: Boolean): Modifier {
    val scale by animateFloatAsState(
        targetValue = if (isPressed) MotionSpecs.Button.Toggle.PRESSED_SCALE else 1f,
        animationSpec = MotionSpecs.Button.Toggle.PRESS_SCALE_ANIMATION_SPEC(),
        label = "bottomBarSpringScale",
    )

    return this.graphicsLayer {
        scaleX = scale
        scaleY = scale
    }
}

/**
 * 图标旋转 + 渐隐动画
 * 选中状态时图标旋转，点击时渐隐
 */
@Composable
private fun Modifier.bottomBarRotateFadeAnimation(
    isPressed: Boolean,
    isSelected: Boolean,
): Modifier {
    val alpha by animateFloatAsState(
        targetValue = if (isPressed) 0.7f else 1f,
        animationSpec = tween(100),
        label = "bottomBarRotateAlpha",
    )
    val rotation by animateFloatAsState(
        targetValue = if (isSelected) 360f else 0f,
        animationSpec = MotionSpecs.Button.FluidInteraction.SMOOTH_SLIDE(),
        label = "bottomBarRotation",
    )

    return this.graphicsLayer {
        this.alpha = alpha
        rotationZ = rotation
    }
}

/**
 * 波纹扩散 + 缩放动画
 * 结合缩放和透明度变化模拟波纹效果
 */
@Composable
private fun Modifier.bottomBarRippleScaleAnimation(isPressed: Boolean): Modifier {
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.95f else 1f,
        animationSpec = MotionSpecs.Button.FluidInteraction.QUICK_FEEDBACK(),
        label = "bottomBarRippleScale",
    )
    val alpha by animateFloatAsState(
        targetValue = if (isPressed) 0.85f else 1f,
        animationSpec = tween(120),
        label = "bottomBarRippleAlpha",
    )

    return this.graphicsLayer {
        scaleX = scale
        scaleY = scale
        this.alpha = alpha
    }
}

// === 预览组件 ===

@GymBroPreview
@Composable
private fun GymBroBottomBarPreview() {
    GymBroTheme {
        GymBroBottomBar(
            onHomeClick = {},
            onExerciseLibraryClick = {},
            onCoachClick = {},
            onWorkoutClick = {},
            currentRoute = "home",
            animationType = BottomBarAnimationType.FADE_SCALE,
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroBottomBarSpringPreview() {
    GymBroTheme {
        GymBroBottomBar(
            onHomeClick = {},
            onExerciseLibraryClick = {},
            onCoachClick = {},
            onWorkoutClick = {},
            currentRoute = "coach",
            animationType = BottomBarAnimationType.SPRING_PRESS,
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroBottomBarRotatePreview() {
    GymBroTheme {
        GymBroBottomBar(
            onHomeClick = {},
            onExerciseLibraryClick = {},
            onCoachClick = {},
            onWorkoutClick = {},
            currentRoute = "workout",
            animationType = BottomBarAnimationType.ROTATE_FADE,
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroBottomBarRipplePreview() {
    GymBroTheme(darkTheme = true) {
        GymBroBottomBar(
            onHomeClick = {},
            onExerciseLibraryClick = {},
            onCoachClick = {},
            onWorkoutClick = {},
            currentRoute = "exercise-library",
            animationType = BottomBarAnimationType.RIPPLE_SCALE,
        )
    }
}
