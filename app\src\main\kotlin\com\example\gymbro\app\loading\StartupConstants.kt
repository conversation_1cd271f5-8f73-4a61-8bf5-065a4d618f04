package com.example.gymbro.app.loading

/**
 * 启动流程相关常量定义
 * 统一管理所有魔数，便于维护和调整
 */
object StartupConstants {

    /**
     * 动画相关常量
     */
    object Animation {
        /** 彩虹渐变动画时长(毫秒) - 基于用户体验测试的最佳值 */
        const val RAINBOW_GRADIENT_DURATION_MS = 3000

        /** 渐变宽度 - 确保在各种屏幕尺寸下的流畅效果 */
        const val GRADIENT_WIDTH = 800f

        /** Loading完成后的延迟时间(毫秒) - 让用户感知到完成状态 */
        const val LOADING_FINISH_DELAY_MS = 300L
    }

    /**
     * 网络和超时相关常量
     */
    object Network {
        /** 地区检测超时时间(毫秒) - 平衡准确性和用户体验 */
        const val REGION_DETECTION_TIMEOUT_MS = 3000L

        /** 版本检查超时时间(毫秒) */
        const val VERSION_CHECK_TIMEOUT_MS = 5000L
    }

    /**
     * 字体相关常量
     */
    object Font {
        /** 主Logo字体大小 */
        const val LOGO_FONT_SIZE_SP = 150
    }

    /**
     * 区域代码常量
     */
    object Region {
        /** 国际区域代码 */
        const val INTERNATIONAL = "INTERNATIONAL"

        /** 中国区域代码 */
        const val CN = "CN"

        /** 默认区域代码 - 当检测失败时使用 */
        const val DEFAULT = INTERNATIONAL
    }

    /**
     * 日志标签
     */
    object LogTag {
        const val STARTUP = "Startup"
        const val REGION_DETECTION = "RegionDetection"
        const val VERSION_CHECK = "VersionCheck"
        const val USER_ID_ASSIGNMENT = "UserIdAssignment"
    }
}
