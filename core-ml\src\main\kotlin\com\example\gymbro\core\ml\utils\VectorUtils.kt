package com.example.gymbro.core.ml.utils

import kotlin.math.sqrt

/**
 * 向量处理工具类
 *
 * 提供向量运算、相似度计算等功能
 */
object VectorUtils {

    /**
     * L2归一化
     *
     * @param vector 原始向量
     * @return 归一化后的向量
     */
    fun normalizeL2(vector: FloatArray): FloatArray {
        val norm = sqrt(vector.sumOf { it * it.toDouble() }).toFloat()
        return if (norm == 0f) vector else vector.map { it / norm }.toFloatArray()
    }

    /**
     * 计算余弦相似度
     *
     * @param vector1 向量1
     * @param vector2 向量2
     * @return 余弦相似度值 [-1, 1]
     */
    fun cosineSimilarity(vector1: FloatArray, vector2: FloatArray): Float {
        require(vector1.size == vector2.size) {
            "向量维度不匹配: ${vector1.size} vs ${vector2.size}"
        }

        var dotProduct = 0f
        var norm1 = 0f
        var norm2 = 0f

        for (i in vector1.indices) {
            dotProduct += vector1[i] * vector2[i]
            norm1 += vector1[i] * vector1[i]
            norm2 += vector2[i] * vector2[i]
        }

        val denominator = sqrt(norm1) * sqrt(norm2)
        return if (denominator == 0f) 0f else dotProduct / denominator
    }

    /**
     * 计算欧几里得距离
     *
     * @param vector1 向量1
     * @param vector2 向量2
     * @return 欧几里得距离
     */
    fun euclideanDistance(vector1: FloatArray, vector2: FloatArray): Float {
        require(vector1.size == vector2.size) {
            "向量维度不匹配: ${vector1.size} vs ${vector2.size}"
        }

        var sum = 0f
        for (i in vector1.indices) {
            val diff = vector1[i] - vector2[i]
            sum += diff * diff
        }

        return sqrt(sum)
    }

    /**
     * 将FloatArray转换为ByteArray（用于数据库存储）
     *
     * @param floatArray 浮点向量
     * @return 字节数组
     */
    fun floatArrayToByteArray(floatArray: FloatArray): ByteArray {
        val byteArray = ByteArray(floatArray.size * 4)
        var index = 0

        for (float in floatArray) {
            val bits = java.lang.Float.floatToIntBits(float)
            byteArray[index++] = (bits shr 24).toByte()
            byteArray[index++] = (bits shr 16).toByte()
            byteArray[index++] = (bits shr 8).toByte()
            byteArray[index++] = bits.toByte()
        }

        return byteArray
    }

    /**
     * 将ByteArray转换为FloatArray（从数据库读取）
     *
     * @param byteArray 字节数组
     * @return 浮点向量
     */
    fun byteArrayToFloatArray(byteArray: ByteArray): FloatArray {
        require(byteArray.size % 4 == 0) {
            "字节数组长度必须是4的倍数: ${byteArray.size}"
        }

        val floatArray = FloatArray(byteArray.size / 4)
        var index = 0

        for (i in floatArray.indices) {
            val bits = ((byteArray[index++].toInt() and 0xFF) shl 24) or
                ((byteArray[index++].toInt() and 0xFF) shl 16) or
                ((byteArray[index++].toInt() and 0xFF) shl 8) or
                (byteArray[index++].toInt() and 0xFF)
            floatArray[i] = java.lang.Float.intBitsToFloat(bits)
        }

        return floatArray
    }

    /**
     * 批量计算向量与目标向量的相似度
     *
     * @param targetVector 目标向量
     * @param candidateVectors 候选向量列表
     * @return 相似度分数列表（与候选向量顺序对应）
     */
    fun batchCosineSimilarity(
        targetVector: FloatArray,
        candidateVectors: List<FloatArray>,
    ): List<Float> {
        return candidateVectors.map { candidate ->
            cosineSimilarity(targetVector, candidate)
        }
    }

    /**
     * 查找最相似的K个向量
     *
     * @param targetVector 目标向量
     * @param candidateVectors 候选向量列表
     * @param k 返回数量
     * @return 最相似的K个向量的索引和相似度
     */
    fun findTopKSimilar(
        targetVector: FloatArray,
        candidateVectors: List<FloatArray>,
        k: Int,
    ): List<Pair<Int, Float>> {
        val similarities = candidateVectors.mapIndexed { index, vector ->
            index to cosineSimilarity(targetVector, vector)
        }

        return similarities
            .sortedByDescending { it.second }
            .take(k)
    }

    /**
     * 计算向量的L2范数
     *
     * @param vector 向量
     * @return L2范数
     */
    fun l2Norm(vector: FloatArray): Float {
        return sqrt(vector.sumOf { it * it.toDouble() }).toFloat()
    }

    /**
     * 检查向量是否已归一化
     *
     * @param vector 向量
     * @param tolerance 容差
     * @return 是否已归一化
     */
    fun isNormalized(vector: FloatArray, tolerance: Float = 1e-6f): Boolean {
        val norm = l2Norm(vector)
        return kotlin.math.abs(norm - 1.0f) <= tolerance
    }
}
