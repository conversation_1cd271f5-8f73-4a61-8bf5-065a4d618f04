package com.example.gymbro.core.logging

import android.util.Log
import timber.log.Timber
import kotlin.math.max

/**
 * 可选择性的调试树
 * 支持按标签过滤和采样率控制，避免日志噪音
 *
 * 🎯 基于"最小可诊断"原则设计：
 * - 默认只显示关键链路日志
 * - 支持运行时开关和采样
 * - 避免Token级别的刷屏
 */
class SelectiveDebugTree(
    private var enabledTags: Set<String> = setOf(
        // === Template 数据保存调试专用日志 ===
        "DEBUG-SAVE",            // 🔥 数据保存调试（最高优先级）
        "JSON-PARSE",            // 🔥 JSON 解析调试
        "TemplateDataMapper",    // 🔥 数据映射器日志
        "WorkoutExerciseComponent", // 🔥 组件日志
        "TemplateJsonProcessor", // 🔥 JSON 处理器日志

        // === Template 相关核心日志 ===
        "TemplateEditViewModel", // 🔥 ViewModel日志
        "TemplateEditReducer",   // 🔥 Reducer日志
        "TemplateSaver",         // 🔥 保存器日志
        "TemplateAutoSaveManager", // 🔥 AutoSave日志
        "TemplateViewModel",     // 🔥 模板ViewModel日志
        "TemplateEffectHandler", // 🔥 模板EffectHandler日志
        "TemplateScreen",        // 🔥 模板Screen日志

        // === 已关闭的噪音日志 ===
        // "XML-PARSER",         // 关闭：产生噪音
        // "PHASE-EXTRACTOR",    // 关闭：产生噪音
        // "REDUCER",            // 关闭：BaseMviViewModel 噪音
        // "UI",                 // 关闭：通用 UI 噪音
        // "BUTTON-TEST",        // 关闭：测试相关
        // "INTENT-TEST",        // 关闭：测试相关
        // "DATA-RECOVERY",      // 关闭：已移除恢复机制
        // "DraggableExerciseCard", // 关闭：非核心组件
    ),
    private var sampleRate: Int = 1,
    private var minPriority: Int = Log.DEBUG,
) : Timber.DebugTree() {

    private val counter = mutableMapOf<String, Int>()

    // 🔥 Token 采样配置
    private var tokenSampleRate: Int = 50 // Token 采样率：每50个token记录1个
    private val tokenCounters = mutableMapOf<String, Int>()

    @Volatile private var active = true // 支持运行时开关

    /**
     * 启用指定标签的日志
     */
    fun enable(vararg tags: String) {
        enabledTags = tags.toSet()
    }

    /**
     * 禁用所有日志
     */
    fun disableAll() {
        enabledTags = emptySet()
    }

    /**
     * 设置采样率
     * @param n 采样率，1=全量，50=每50条打印1条
     */
    fun setSampleRate(n: Int) {
        sampleRate = max(1, n)
    }

    /**
     * 设置最小日志级别
     */
    fun setMinPriority(priority: Int) {
        minPriority = priority
    }

    /**
     * 运行时开关
     */
    fun setActive(on: Boolean) {
        active = on
    }

    /**
     * Token 采样记录（避免日志泛滥）
     */
    fun logToken(tag: String, token: String) {
        if (!active) return

        val count = tokenCounters.getOrPut(tag) { 0 } + 1
        tokenCounters[tag] = count

        // 🔥 立即记录包含关键标签的 token
        if (token.contains("<think") || token.contains("<title") || token.contains("<final") ||
            token.contains("</think") || token.contains("</title") || token.contains("</final")
        ) {
            super.log(Log.WARN, "THINKING-EVENTS", "🎯 关键标签 #$count: ${token.replace("\n", "\\n")}", null)
            return
        }

        // 🔥 采样记录普通 token
        if (count % tokenSampleRate == 0) {
            super.log(Log.VERBOSE, tag, "📊 Token采样 #$count: ${token.replace("\n", "\\n")}", null)
        }
    }

    override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
        // 🔥 运行时开关检查
        if (!active) return

        // 检查优先级
        if (priority < minPriority) return

        // 检查标签
        if (tag == null) return
        if (enabledTags.isNotEmpty() && tag !in enabledTags) return

        // 采样控制
        val hit = counter.merge(tag, 1) { old, _ -> old + 1 } ?: 1
        if (hit % sampleRate != 0) return

        super.log(priority, tag, message, t)
    }

    /**
     * 获取当前配置信息
     */
    fun getConfig(): String {
        return "tags=${enabledTags.joinToString()}, sample=$sampleRate, minPriority=$minPriority"
    }

    /**
     * 预设配置：最小可诊断模式
     */
    fun enableMinimalDiagnostic() {
        enabledTags = setOf("XML-PARSER", "PHASE-EXTRACTOR", "REDUCER", "UI")
        sampleRate = 1
        minPriority = Log.DEBUG
    }

    /**
     * 预设配置：精准调试模式（只显示关键事件）
     * 🔥 修复：只显示最重要的两个文件的日志
     */
    fun enableTokenDebug() {
        enabledTags = setOf(
            "PROMPT-BUILDER", // LayeredPromptBuilder 系统提示词构建
            "AI-COACH-RAW", // AICoachRepositoryImpl 发送接收RAW信息
        )
        sampleRate = 1 // 全量输出关键事件
        minPriority = Log.DEBUG
    }

    /**
     * 预设配置：静音模式
     */
    fun enableSilentMode() {
        disableAll()
    }
}

/**
 * 全局可控日志实例
 * 在Application中初始化，支持运行时控制
 */
object LogController {
    lateinit var selectiveTree: SelectiveDebugTree
        private set

    /**
     * 初始化日志系统
     * 在Application.onCreate()中调用
     */
    fun initialize(debugMode: Boolean = true) {
        if (debugMode) {
            selectiveTree = SelectiveDebugTree()
            Timber.plant(selectiveTree)

            // 打印初始配置
            Timber.tag("LOG").i("日志系统已启动: ${selectiveTree.getConfig()}")
        }
    }

    /**
     * 快速切换到最小可诊断模式
     */
    fun enableMinimalDiagnostic() {
        if (::selectiveTree.isInitialized) {
            selectiveTree.enableMinimalDiagnostic()
            Timber.tag("LOG").i("切换到最小可诊断模式")
        }
    }

    /**
     * 快速切换到Token调试模式
     */
    fun enableTokenDebug() {
        if (::selectiveTree.isInitialized) {
            selectiveTree.enableTokenDebug()
            Timber.tag("LOG").i("切换到Token调试模式（采样）")
        }
    }

    /**
     * 快速切换到静音模式
     */
    fun enableSilentMode() {
        if (::selectiveTree.isInitialized) {
            selectiveTree.enableSilentMode()
            Timber.tag("LOG").i("切换到静音模式")
        }
    }

    /**
     * 自定义配置
     */
    fun configure(tags: Array<String>, sampleRate: Int = 1) {
        if (::selectiveTree.isInitialized) {
            selectiveTree.enable(*tags)
            selectiveTree.setSampleRate(sampleRate)
            Timber.tag("LOG").i("自定义配置: ${selectiveTree.getConfig()}")
        }
    }
}
