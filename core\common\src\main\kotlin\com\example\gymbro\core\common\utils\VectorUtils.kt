package com.example.gymbro.core.common.utils

/**
 * 向量处理工具类 - v5.0-GOLD标准的公共工具
 *
 * 设计原则：
 * 1. 纯函数 - 无副作用，相同输入产生相同输出
 * 2. 高性能 - 优化的字节转换算法
 * 3. 类型安全 - 严格的类型检查和边界处理
 * 4. 可复用 - 跨模块共享，避免重复实现
 * 5. internal封装 - 仅供内部模块使用
 *
 * 🔥 架构优化：
 * - 从多个模块中提取重复的向量转换逻辑
 * - 统一的向量处理标准，便于维护
 * - 支持BGE向量搜索的核心转换需求
 *
 * @since v5.0-GOLD Code Review 修复
 */
internal object VectorUtils {

    /**
     * 将FloatArray转换为ByteArray
     * 
     * 用于向量数据库存储，将32位浮点数组转换为字节数组
     * 每个float占用4个字节，使用小端序存储
     * 
     * @param floats 输入的浮点数组
     * @return 转换后的字节数组，长度为 floats.size * 4
     * 
     * @throws IllegalArgumentException 如果输入数组为空
     */
    fun floatArrayToByteArray(floats: FloatArray): ByteArray {
        require(floats.isNotEmpty()) { "输入的FloatArray不能为空" }
        
        val bytes = ByteArray(floats.size * 4)
        var index = 0
        
        for (float in floats) {
            val bits = java.lang.Float.floatToIntBits(float)
            // 小端序存储：低位字节在前
            bytes[index++] = (bits and 0xFF).toByte()
            bytes[index++] = ((bits shr 8) and 0xFF).toByte()
            bytes[index++] = ((bits shr 16) and 0xFF).toByte()
            bytes[index++] = ((bits shr 24) and 0xFF).toByte()
        }
        
        return bytes
    }

    /**
     * 将ByteArray转换为FloatArray
     * 
     * 用于从向量数据库读取，将字节数组转换为32位浮点数组
     * 假设输入使用小端序存储
     * 
     * @param bytes 输入的字节数组
     * @return 转换后的浮点数组，长度为 bytes.size / 4
     * 
     * @throws IllegalArgumentException 如果字节数组长度不是4的倍数
     */
    fun byteArrayToFloatArray(bytes: ByteArray): FloatArray {
        require(bytes.size % 4 == 0) { "字节数组长度必须是4的倍数，当前长度: ${bytes.size}" }
        
        val floats = FloatArray(bytes.size / 4)
        var byteIndex = 0
        
        for (i in floats.indices) {
            // 小端序读取：低位字节在前
            val bits = (bytes[byteIndex].toInt() and 0xFF) or
                      ((bytes[byteIndex + 1].toInt() and 0xFF) shl 8) or
                      ((bytes[byteIndex + 2].toInt() and 0xFF) shl 16) or
                      ((bytes[byteIndex + 3].toInt() and 0xFF) shl 24)
            
            floats[i] = java.lang.Float.intBitsToFloat(bits)
            byteIndex += 4
        }
        
        return floats
    }

    /**
     * 计算两个向量的余弦相似度
     * 
     * @param vector1 第一个向量
     * @param vector2 第二个向量
     * @return 余弦相似度值，范围 [-1, 1]
     * 
     * @throws IllegalArgumentException 如果向量长度不匹配或为空
     */
    fun cosineSimilarity(vector1: FloatArray, vector2: FloatArray): Float {
        require(vector1.isNotEmpty() && vector2.isNotEmpty()) { "向量不能为空" }
        require(vector1.size == vector2.size) { 
            "向量维度必须相同: vector1=${vector1.size}, vector2=${vector2.size}" 
        }
        
        var dotProduct = 0.0
        var norm1 = 0.0
        var norm2 = 0.0
        
        for (i in vector1.indices) {
            dotProduct += vector1[i] * vector2[i]
            norm1 += vector1[i] * vector1[i]
            norm2 += vector2[i] * vector2[i]
        }
        
        val denominator = kotlin.math.sqrt(norm1 * norm2)
        return if (denominator == 0.0) 0f else (dotProduct / denominator).toFloat()
    }

    /**
     * 归一化向量（L2范数）
     * 
     * @param vector 输入向量
     * @return 归一化后的向量
     * 
     * @throws IllegalArgumentException 如果向量为空或全零
     */
    fun normalizeVector(vector: FloatArray): FloatArray {
        require(vector.isNotEmpty()) { "向量不能为空" }
        
        val norm = kotlin.math.sqrt(vector.sumOf { (it * it).toDouble() })
        require(norm > 0.0) { "不能归一化零向量" }
        
        return vector.map { (it / norm).toFloat() }.toFloatArray()
    }

    /**
     * 验证向量维度是否符合BGE模型要求
     * 
     * @param vector 输入向量
     * @param expectedDimension 期望的维度（BGE-small-cn-v1.5为384维）
     * @return 是否符合要求
     */
    fun validateVectorDimension(vector: FloatArray, expectedDimension: Int = 384): Boolean {
        return vector.size == expectedDimension
    }
}
