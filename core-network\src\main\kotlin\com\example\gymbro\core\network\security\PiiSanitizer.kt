package com.example.gymbro.core.network.security

import java.security.MessageDigest
import java.util.regex.Pattern

/**
 * PII数据脱敏工具
 *
 * 提供个人敏感信息的识别和脱敏处理，确保日志安全合规
 */
object PiiSanitizer {

    private const val HASH_PREFIX = "hash:"
    private const val TRUNCATED_SUFFIX = "..."
    private const val MAX_CONTENT_LENGTH = 50

    // 敏感信息正则表达式
    private val EMAIL_PATTERN = Pattern.compile(
        "[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}",
    )

    private val PHONE_PATTERN = Pattern.compile(
        "(?:\\+?86)?\\s*1[3-9]\\d{9}|\\(?\\d{3}\\)?[-\\s]?\\d{3}[-\\s]?\\d{4}",
    )

    private val ID_CARD_PATTERN = Pattern.compile(
        "\\b\\d{17}[\\dXx]\\b|\\b\\d{15}\\b",
    )

    private val CREDIT_CARD_PATTERN = Pattern.compile(
        "\\b(?:\\d{4}[-\\s]?){3}\\d{4}\\b",
    )

    // 敏感字段名称（不区分大小写）
    private val SENSITIVE_FIELD_NAMES = setOf(
        "password", "pwd", "token", "secret", "key", "authorization",
        "email", "phone", "mobile", "idcard", "creditcard", "bankcard",
        "prompt", "content", "message", "query", "input", "output",
        "name", "username", "nickname", "realname", "address",
    )

    /**
     * 脱敏文本内容
     *
     * @param content 原始内容
     * @param isProduction 是否为生产环境
     * @return 脱敏后的内容
     */
    fun sanitizeContent(content: String, isProduction: Boolean = true): String {
        if (content.isBlank()) return content

        var sanitized = content

        // 生产环境进行完整脱敏
        if (isProduction) {
            // 脱敏邮箱
            sanitized = EMAIL_PATTERN.matcher(sanitized).replaceAll { matchResult ->
                val email = matchResult.group()
                val atIndex = email.indexOf('@')
                if (atIndex > 0) {
                    "${email.substring(0, 1)}***@${email.substring(atIndex + 1)}"
                } else {
                    "***@***.***"
                }
            }

            // 脱敏手机号
            sanitized = PHONE_PATTERN.matcher(sanitized).replaceAll { matchResult ->
                val phone = matchResult.group()
                if (phone.length >= 7) {
                    "${phone.substring(0, 3)}****${phone.substring(phone.length - 4)}"
                } else {
                    "***-****"
                }
            }

            // 脱敏身份证号
            sanitized = ID_CARD_PATTERN.matcher(sanitized).replaceAll("***************")

            // 脱敏信用卡号
            sanitized = CREDIT_CARD_PATTERN.matcher(sanitized).replaceAll("****-****-****-****")
        }

        return sanitized
    }

    /**
     * 脱敏JSON字段
     *
     * @param jsonContent JSON内容
     * @param isProduction 是否为生产环境
     * @return 脱敏后的JSON内容
     */
    fun sanitizeJsonFields(jsonContent: String, isProduction: Boolean = true): String {
        if (jsonContent.isBlank()) return jsonContent

        var sanitized = jsonContent

        SENSITIVE_FIELD_NAMES.forEach { fieldName ->
            // 匹配 "fieldName": "value" 格式
            val regex = """"$fieldName"\s*:\s*"([^"]*)"""".toRegex(RegexOption.IGNORE_CASE)
            sanitized = regex.replace(sanitized) { matchResult ->
                val originalValue = matchResult.groupValues[1]
                val sanitizedValue = if (isProduction) {
                    sanitizeFieldValue(fieldName, originalValue)
                } else {
                    truncateValue(originalValue)
                }
                """"$fieldName":"$sanitizedValue""""
            }

            // 匹配 "fieldName": value 格式（非字符串值）
            val nonStringRegex = """"$fieldName"\s*:\s*([^,}\]]+)""".toRegex(RegexOption.IGNORE_CASE)
            sanitized = nonStringRegex.replace(sanitized) { matchResult ->
                val originalValue = matchResult.groupValues[1].trim()
                val sanitizedValue = if (isProduction && originalValue.startsWith('"')) {
                    sanitizeFieldValue(fieldName, originalValue.removeSurrounding("\""))
                } else {
                    "***"
                }
                """"$fieldName":"$sanitizedValue""""
            }
        }

        return sanitized
    }

    /**
     * 根据字段类型脱敏值
     */
    private fun sanitizeFieldValue(fieldName: String, value: String): String {
        return when {
            fieldName.contains("email", ignoreCase = true) -> maskEmail(value)
            fieldName.contains("phone", ignoreCase = true) ||
                fieldName.contains("mobile", ignoreCase = true) -> maskPhone(value)
            fieldName.contains("password", ignoreCase = true) ||
                fieldName.contains("pwd", ignoreCase = true) -> "***"
            fieldName.contains("token", ignoreCase = true) ||
                fieldName.contains("key", ignoreCase = true) -> "***"
            fieldName.contains("prompt", ignoreCase = true) ||
                fieldName.contains("content", ignoreCase = true) ||
                fieldName.contains("message", ignoreCase = true) -> hashAndTruncate(value)
            else -> truncateValue(value)
        }
    }

    /**
     * 脱敏邮箱地址
     */
    private fun maskEmail(email: String): String {
        val atIndex = email.indexOf('@')
        return if (atIndex > 0) {
            "${email.substring(0, 1)}***@${email.substring(atIndex + 1)}"
        } else {
            "***@***.***"
        }
    }

    /**
     * 脱敏手机号码
     */
    private fun maskPhone(phone: String): String {
        return if (phone.length >= 7) {
            "${phone.substring(0, 3)}****${phone.substring(phone.length - 4)}"
        } else {
            "***-****"
        }
    }

    /**
     * 对长文本进行哈希和截断处理
     */
    private fun hashAndTruncate(content: String): String {
        if (content.length <= MAX_CONTENT_LENGTH) {
            return content.take(MAX_CONTENT_LENGTH / 2) + TRUNCATED_SUFFIX
        }

        val hash = generateHash(content)
        val preview = content.take(MAX_CONTENT_LENGTH)
        return "$HASH_PREFIX$hash, preview: $preview$TRUNCATED_SUFFIX"
    }

    /**
     * 截断长值
     */
    private fun truncateValue(value: String): String {
        return if (value.length > MAX_CONTENT_LENGTH) {
            value.take(MAX_CONTENT_LENGTH) + TRUNCATED_SUFFIX
        } else {
            value
        }
    }

    /**
     * 生成内容的SHA-256哈希值
     */
    private fun generateHash(content: String): String {
        return try {
            val digest = MessageDigest.getInstance("SHA-256")
            val hashBytes = digest.digest(content.toByteArray())
            hashBytes.joinToString("") { "%02x".format(it) }.take(8) // 只取前8位
        } catch (e: Exception) {
            "unknown"
        }
    }
}
