package com.example.gymbro.data.util

import timber.log.Timber
import java.net.InetAddress

/**
 * IP地址工具类
 * 提供IP地址解析和CIDR匹配功能
 */
object IpUtils {

    /**
     * 将IP地址字符串转换为长整型
     * @param ip IP地址字符串 (例如: "***********")
     * @return IP地址的长整型表示，失败返回null
     */
    fun ipToLong(ip: String): Long? {
        return try {
            val address = InetAddress.getByName(ip)
            val bytes = address.address

            // 将4个字节转换为长整型
            var result = 0L
            for (i in bytes.indices) {
                result = result shl 8
                result = result or (bytes[i].toLong() and 0xFF)
            }
            result
        } catch (e: Exception) {
            Timber.w(e, "Failed to parse IP address: $ip")
            null
        }
    }

    /**
     * 解析CIDR格式的IP段
     * @param cidr CIDR格式字符串 (例如: "***********/24")
     * @return IP段的起始和结束地址，失败返回null
     */
    fun parseCidr(cidr: String): Pair<Long, Long>? {
        return try {
            val parts = cidr.split("/")
            if (parts.size != 2) return null

            val ip = parts[0]
            val prefixLength = parts[1].toInt()

            val ipLong = ipToLong(ip) ?: return null

            // 计算网络掩码
            val mask = (-1L shl (32 - prefixLength)) and 0xFFFFFFFFL

            // 计算网络地址和广播地址
            val networkAddress = ipLong and mask
            val broadcastAddress = networkAddress or (mask.inv() and 0xFFFFFFFFL)

            Pair(networkAddress, broadcastAddress)
        } catch (e: Exception) {
            Timber.w(e, "Failed to parse CIDR: $cidr")
            null
        }
    }

    /**
     * 检查IP地址是否在CIDR范围内
     * @param ip IP地址字符串
     * @param cidr CIDR格式字符串
     * @return 是否在范围内
     */
    fun isIpInCidr(ip: String, cidr: String): Boolean {
        val ipLong = ipToLong(ip) ?: return false
        val range = parseCidr(cidr) ?: return false

        return ipLong >= range.first && ipLong <= range.second
    }

    /**
     * 检查IP地址是否在CIDR列表中
     * @param ip IP地址字符串
     * @param cidrList CIDR列表
     * @return 是否匹配任何一个CIDR段
     */
    fun isIpInCidrList(ip: String, cidrList: List<String>): Boolean {
        val ipLong = ipToLong(ip) ?: return false

        for (cidr in cidrList) {
            val range = parseCidr(cidr) ?: continue
            if (ipLong >= range.first && ipLong <= range.second) {
                return true
            }
        }
        return false
    }

    /**
     * 验证IP地址格式是否正确
     * @param ip IP地址字符串
     * @return 是否为有效的IPv4地址
     */
    fun isValidIpv4(ip: String): Boolean {
        return try {
            val address = InetAddress.getByName(ip)
            address.hostAddress == ip && address.address.size == 4
        } catch (e: Exception) {
            false
        }
    }
}
