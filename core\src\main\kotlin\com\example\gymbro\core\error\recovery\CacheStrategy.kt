package com.example.gymbro.core.error.recovery

import com.example.gymbro.core.error.internal.recovery.CacheStrategyImpl

/**
 * 缓存恢复策略接口
 * 当主操作失败时从缓存中获取数据
 */
interface CacheStrategy<T> : RecoveryStrategy<T> {
    companion object {
        /**
         * 创建网络错误专用的缓存策略
         * @param getCachedData 获取缓存数据的函数
         * @param validateCache 验证缓存数据有效性的函数
         * @return 网络错误专用的缓存策略
         */
        fun <T> forNetworkErrors(
            getCachedData: suspend () -> T?,
            validateCache: suspend (T) -> Boolean = { true },
        ): CacheStrategy<T> =
            CacheStrategyImpl(
                getCachedData = getCachedData,
                validateCache = validateCache,
            )
    }
}
