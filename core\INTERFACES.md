# core 模块 接口文档

> **版本**: v2.0 - 核心功能库
> **状态**: ✅ 生产就绪
> **最后更新**: 2025-06-25

## 🎯 模块职责

- **核心职责**: `core`模块是整个应用的技术基础，提供了一系列平台无关的核心功能和工具。它旨在通过定义通用的接口和实现，来解耦业务逻辑与具体平台，从而提高代码的可测试性、可维护性和复用性。

## 🔌 公共接口

本模块的公共接口主要是其定义的核心服务和工具类。以下是各分类下的核心接口。

### 错误处理

| 接口/类 | 描述 | 状态 |
| --- | --- | --- |
| `ModernResult<T>` | **[核心]** 封装异步操作结果（Success, Error, Loading）的密封类。 | ✅ 稳定 |
| `ModernErrorHandler` | **[核心]** 统一的错误处理器接口，负责将技术错误转换为用户友好的消息。 | ✅ 稳定 |
| `ModernDataError` | 标准化的错误数据类，用于在各层之间传递丰富的错误信息。 | ✅ 稳定 |
| `RecoveryStrategy<T>`| 错误恢复策略的接口，定义了从错误中恢复的通用契约。 | ✅ 稳定 |

### 日志与资源

| 接口/类 | 描述 | 状态 |
| --- | --- | --- |
| `Logger` | **[核心]** 日志记录接口，解耦了具体的日志框架（如Timber）。 | ✅ 稳定 |
| `ResourceProvider` | **[核心]** 平台无关的资源访问接口，用于获取字符串等资源。 | ✅ 稳定 |
| `UiText` | UI文本的抽象表示，支持国际化和动态文本。 | ✅ 稳定 |

### AI 与 Prompt

| 接口/类 | 描述 | 状态 |
| --- | --- | --- |
| `LayeredPromptBuilder`| **[核心]** AI提示词构建器，负责动态、分层地构建提示词。 | ✅ 稳定 |
| `PromptRegistry` | 系统提示词注册表，支持热切换和版本管理。 | ✅ 稳定 |
| `TokenizerService` | Token计算服务接口，用于估算提示词的Token数量。 | ✅ 稳定 |

### 通用服务

| 接口/类 | 描述 | 状态 |
| --- | --- | --- |
| `AutoSaveManager` | 自动保存管理器接口，提供可配置的数据自动保存和恢复机制。 | ✅ 稳定 |
| `NetworkMonitor` | 网络状态监控接口，提供网络状态的实时信息。 | ✅ 稳定 |
| `ThemeManager` | 动态主题管理器，负责主题的切换和持久化。 | ✅ 稳定 |
| `EncryptionManager`| 加密管理器，负责敏感数据的加密和解密。 | ✅ 稳定 |

## 📋 当前活跃接口详细说明 (核心接口)

- **`ModernResult<T>`**:
  ```kotlin
  // 封装异步操作结果，替代传统的try-catch和回调
  sealed class ModernResult<out T> {
      object Loading : ModernResult<Nothing>()
      data class Success<T>(val data: T) : ModernResult<T>()
      data class Error(val error: ModernDataError) : ModernResult<Nothing>()
  }
  ```

- **`ModernErrorHandler`**:
  ```kotlin
  // 统一的错误处理器，提供错误消息、严重性、可恢复性等信息的判断
  interface ModernErrorHandler {
      fun getUiMessage(error: ModernDataError): UiText
      fun isRetryable(error: ModernDataError): Boolean
      fun getSeverity(error: ModernDataError): ErrorSeverity
      // ...
  }
  ```

- **`Logger`**:
  ```kotlin
  // 日志记录接口，解耦具体实现
  interface Logger {
      fun d(message: String, vararg args: Any?)
      fun e(t: Throwable?, message: String, vararg args: Any?)
      // ...
  }
  ```

## 📊 UI数据模型

`core`模块定义了`UiText`作为UI文本的基础模型，但不直接定义具体的UI状态（`UiState`）。`BaseUiState`接口定义了所有UI状态应遵循的基本结构，具体的`UiState`数据类在各自的feature模块中实现。

- **`UiText`**:
  ```kotlin
  // UI文本的抽象表示，支持多种来源
  sealed class UiText {
      data class DynamicString(val value: String) : UiText()
      data class StringResource(val resId: Int, val args: List<String>) : UiText()
      data class ErrorCode(val errorCode: com.example.gymbro.core.error.ErrorCode) : UiText()
      // ...
  }
  ```