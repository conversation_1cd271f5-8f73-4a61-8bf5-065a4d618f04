package com.example.gymbro.data.coach.model

/**
 * Data层流式事件 - 用于WebSocket原始数据解析
 *
 * 🔧 WebSocket迁移：从SSE迁移到WebSocket后的数据层事件模型
 * 这些事件将被转换为Domain层的StreamEvent
 *
 * 设计原则：
 * 1. 简化数据结构 - 只包含必要的数据字段
 * 2. 无业务逻辑 - 纯数据传输对象
 * 3. 易于解析 - 适配WebSocket原始响应格式
 */
sealed interface DataStreamEvent {

    /**
     * 思考事件 - AI开始思考
     */
    data class Thinking(
        val thinkingId: String = "unknown",
    ) : DataStreamEvent

    /**
     * 内容块事件 - 流式内容片段
     */
    data class Chunk(
        val content: String,
    ) : DataStreamEvent

    /**
     * 完成事件 - 流式响应结束
     */
    data class Done(
        val finishReason: String? = null,
    ) : DataStreamEvent

    /**
     * 错误事件 - 处理异常
     */
    data class Error(
        val throwable: Throwable,
    ) : DataStreamEvent

    /**
     * 函数调用事件 - AI调用工具
     */
    data class FunctionCall(
        val functionName: String,
        val arguments: String,
    ) : DataStreamEvent
}
