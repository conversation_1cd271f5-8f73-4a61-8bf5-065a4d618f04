{"formatVersion": 1, "database": {"version": 3, "identityHash": "a965c0e6d3c1de3d904d9aded088b9db", "entities": [{"tableName": "workout_templates", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `description` TEXT, `targetMuscleGroups` TEXT NOT NULL, `difficulty` INTEGER NOT NULL, `estimatedDuration` INTEGER, `userId` TEXT NOT NULL, `isPublic` INTEGER NOT NULL, `isFavorite` INTEGER NOT NULL, `tags` TEXT NOT NULL, `currentVersion` INTEGER NOT NULL, `isDraft` INTEGER NOT NULL, `isPublished` INTEGER NOT NULL, `lastPublishedAt` INTEGER, `versionTag` INTEGER NOT NULL, `jsonData` TEXT, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": false}, {"fieldPath": "targetMuscleGroups", "columnName": "targetMuscleGroups", "affinity": "TEXT", "notNull": true}, {"fieldPath": "difficulty", "columnName": "difficulty", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "estimatedDuration", "columnName": "estimatedDuration", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isPublic", "columnName": "isPublic", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isFavorite", "columnName": "isFavorite", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "tags", "columnName": "tags", "affinity": "TEXT", "notNull": true}, {"fieldPath": "currentVersion", "columnName": "currentVersion", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isDraft", "columnName": "isDraft", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isPublished", "columnName": "isPublished", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastPublishedAt", "columnName": "lastPublishedAt", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "versionTag", "columnName": "versionTag", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "jsonData", "columnName": "jsonData", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_workout_templates_userId", "unique": false, "columnNames": ["userId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_templates_userId` ON `${TABLE_NAME}` (`userId`)"}, {"name": "index_workout_templates_isPublic", "unique": false, "columnNames": ["isPublic"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_templates_isPublic` ON `${TABLE_NAME}` (`isPublic`)"}, {"name": "index_workout_templates_isFavorite", "unique": false, "columnNames": ["isFavorite"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_templates_isFavorite` ON `${TABLE_NAME}` (`isFavorite`)"}, {"name": "index_workout_templates_createdAt", "unique": false, "columnNames": ["createdAt"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_templates_createdAt` ON `${TABLE_NAME}` (`createdAt`)"}, {"name": "index_workout_templates_updatedAt", "unique": false, "columnNames": ["updatedAt"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_templates_updatedAt` ON `${TABLE_NAME}` (`updatedAt`)"}, {"name": "index_workout_templates_isDraft", "unique": false, "columnNames": ["isDraft"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_templates_isDraft` ON `${TABLE_NAME}` (`isDraft`)"}, {"name": "index_workout_templates_isPublished", "unique": false, "columnNames": ["isPublished"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_templates_isPublished` ON `${TABLE_NAME}` (`isPublished`)"}, {"name": "index_workout_templates_currentVersion", "unique": false, "columnNames": ["currentVersion"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_templates_currentVersion` ON `${TABLE_NAME}` (`currentVersion`)"}], "foreignKeys": []}, {"tableName": "template_exercises", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `templateId` TEXT NOT NULL, `exerciseId` TEXT NOT NULL, `order` INTEGER NOT NULL, `sets` INTEGER NOT NULL, `repsPerSet` TEXT NOT NULL, `weightSuggestion` TEXT, `restSeconds` INTEGER NOT NULL, `notes` TEXT, `superset` INTEGER NOT NULL, `supersetGroupId` TEXT, PRIMARY KEY(`id`), FOREIGN KEY(`templateId`) REFERENCES `workout_templates`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "templateId", "columnName": "templateId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "exerciseId", "columnName": "exerciseId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "order", "columnName": "order", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sets", "columnName": "sets", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "repsPerSet", "columnName": "repsPerSet", "affinity": "TEXT", "notNull": true}, {"fieldPath": "weightSuggestion", "columnName": "weightSuggestion", "affinity": "TEXT", "notNull": false}, {"fieldPath": "restSeconds", "columnName": "restSeconds", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "notes", "columnName": "notes", "affinity": "TEXT", "notNull": false}, {"fieldPath": "superset", "columnName": "superset", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "supersetGroupId", "columnName": "supersetGroupId", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_template_exercises_templateId", "unique": false, "columnNames": ["templateId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_template_exercises_templateId` ON `${TABLE_NAME}` (`templateId`)"}, {"name": "index_template_exercises_exerciseId", "unique": false, "columnNames": ["exerciseId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_template_exercises_exerciseId` ON `${TABLE_NAME}` (`exerciseId`)"}, {"name": "index_template_exercises_order", "unique": false, "columnNames": ["order"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_template_exercises_order` ON `${TABLE_NAME}` (`order`)"}, {"name": "index_template_exercises_supersetGroupId", "unique": false, "columnNames": ["supersetGroupId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_template_exercises_supersetGroupId` ON `${TABLE_NAME}` (`supersetGroupId`)"}], "foreignKeys": [{"table": "workout_templates", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["templateId"], "referencedColumns": ["id"]}]}, {"tableName": "template_versions", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `templateId` TEXT NOT NULL, `versionNumber` INTEGER NOT NULL, `contentJson` TEXT NOT NULL, `createdAt` INTEGER NOT NULL, `description` TEXT, `isAutoSaved` INTEGER NOT NULL, PRIMARY KEY(`id`), FOREIGN KEY(`templateId`) REFERENCES `workout_templates`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "templateId", "columnName": "templateId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "versionNumber", "columnName": "versionNumber", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "contentJson", "columnName": "contentJson", "affinity": "TEXT", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isAutoSaved", "columnName": "isAutoSaved", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_template_versions_templateId", "unique": false, "columnNames": ["templateId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_template_versions_templateId` ON `${TABLE_NAME}` (`templateId`)"}, {"name": "index_template_versions_templateId_versionNumber", "unique": true, "columnNames": ["templateId", "versionNumber"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_template_versions_templateId_versionNumber` ON `${TABLE_NAME}` (`templateId`, `versionNumber`)"}, {"name": "index_template_versions_createdAt", "unique": false, "columnNames": ["createdAt"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_template_versions_createdAt` ON `${TABLE_NAME}` (`createdAt`)"}, {"name": "index_template_versions_isAutoSaved", "unique": false, "columnNames": ["isAutoSaved"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_template_versions_isAutoSaved` ON `${TABLE_NAME}` (`isAutoSaved`)"}], "foreignKeys": [{"table": "workout_templates", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["templateId"], "referencedColumns": ["id"]}]}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'a965c0e6d3c1de3d904d9aded088b9db')"]}}