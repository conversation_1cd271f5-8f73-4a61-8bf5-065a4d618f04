package com.example.gymbro.data.coach.dao

import androidx.room.*
import com.example.gymbro.data.coach.entity.MessageEmbeddingEntity
import kotlinx.coroutines.flow.Flow

/**
 * 消息嵌入向量DAO
 *
 * 基于 history补充.md 规范设计，支持：
 * - BGE向量的CRUD操作
 * - KNN向量相似度搜索
 * - 混合搜索（向量+关键词）
 * - 批量向量操作
 */
@Dao
interface MessageEmbeddingDao {
    // ==================== 基础CRUD操作 ====================

    /**
     * 插入嵌入向量
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertEmbedding(embedding: MessageEmbeddingEntity): Long

    /**
     * 批量插入嵌入向量
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertEmbeddings(embeddings: List<MessageEmbeddingEntity>): List<Long>

    /**
     * 更新嵌入向量
     */
    @Update
    suspend fun updateEmbedding(embedding: MessageEmbeddingEntity)

    /**
     * 删除嵌入向量
     */
    @Delete
    suspend fun deleteEmbedding(embedding: MessageEmbeddingEntity)

    /**
     * 根据消息ID删除嵌入向量
     */
    @Query("DELETE FROM message_embedding WHERE message_id = :messageId")
    suspend fun deleteEmbeddingByMessageId(messageId: Long)

    /**
     * 根据会话ID删除所有嵌入向量
     */
    @Query(
        """
        DELETE FROM message_embedding
        WHERE message_id IN (
            SELECT id FROM chat_raw WHERE session_id = :sessionId
        )
    """,
    )
    suspend fun deleteEmbeddingsBySessionId(sessionId: String)

    // ==================== 查询操作 ====================

    /**
     * 根据消息ID获取嵌入向量
     */
    @Query("SELECT * FROM message_embedding WHERE message_id = :messageId")
    suspend fun getEmbeddingByMessageId(messageId: Long): MessageEmbeddingEntity?

    /**
     * 获取所有嵌入向量
     */
    @Query("SELECT * FROM message_embedding ORDER BY created_at DESC")
    suspend fun getAllEmbeddings(): List<MessageEmbeddingEntity>

    /**
     * 获取指定状态的嵌入向量
     */
    @Query("SELECT * FROM message_embedding WHERE embedding_status = :status ORDER BY created_at DESC")
    suspend fun getEmbeddingsByStatus(status: String): List<MessageEmbeddingEntity>

    /**
     * 获取待处理的消息ID列表
     */
    @Query(
        """
        SELECT cr.id FROM chat_raw cr
        LEFT JOIN message_embedding me ON cr.id = me.message_id
        WHERE me.message_id IS NULL OR me.embedding_status = 'FAILED'
        ORDER BY cr.timestamp DESC
        LIMIT :limit
    """,
    )
    suspend fun getPendingMessageIds(limit: Int = 100): List<Long>

    /**
     * 检查消息是否已有嵌入向量
     */
    @Query(
        "SELECT COUNT(*) > 0 FROM message_embedding WHERE message_id = :messageId AND embedding_status = 'COMPLETED'",
    )
    suspend fun hasEmbedding(messageId: Long): Boolean

    // ==================== 向量搜索操作 ====================

    /**
     * 获取所有完成的嵌入向量用于相似度计算
     * 注意：这是简化版本，实际应用中需要优化为批量计算
     */
    @Query(
        """
        SELECT me.*, cr.content, cr.session_id, cr.role, cr.timestamp
        FROM message_embedding me
        INNER JOIN chat_raw cr ON me.message_id = cr.id
        WHERE me.embedding_status = 'COMPLETED'
        ORDER BY cr.timestamp DESC
        LIMIT :limit
    """,
    )
    suspend fun getEmbeddingsForSearch(limit: Int = 1000): List<EmbeddingWithMessage>

    /**
     * 根据会话ID获取嵌入向量
     */
    @Query(
        """
        SELECT me.*, cr.content, cr.session_id, cr.role, cr.timestamp
        FROM message_embedding me
        INNER JOIN chat_raw cr ON me.message_id = cr.id
        WHERE cr.session_id = :sessionId AND me.embedding_status = 'COMPLETED'
        ORDER BY cr.timestamp DESC
    """,
    )
    suspend fun getEmbeddingsBySessionId(sessionId: String): List<EmbeddingWithMessage>

    /**
     * 获取最近的嵌入向量
     */
    @Query(
        """
        SELECT me.*, cr.content, cr.session_id, cr.role, cr.timestamp
        FROM message_embedding me
        INNER JOIN chat_raw cr ON me.message_id = cr.id
        WHERE me.embedding_status = 'COMPLETED'
        ORDER BY cr.timestamp DESC
        LIMIT :limit
    """,
    )
    suspend fun getRecentEmbeddings(limit: Int = 50): List<EmbeddingWithMessage>

    // ==================== 统计和监控 ====================

    /**
     * 获取嵌入向量统计信息
     */
    @Query(
        """
        SELECT
            COUNT(*) as total,
            SUM(CASE WHEN embedding_status = 'COMPLETED' THEN 1 ELSE 0 END) as completed,
            SUM(CASE WHEN embedding_status = 'PENDING' THEN 1 ELSE 0 END) as pending,
            SUM(CASE WHEN embedding_status = 'PROCESSING' THEN 1 ELSE 0 END) as processing,
            SUM(CASE WHEN embedding_status = 'FAILED' THEN 1 ELSE 0 END) as failed,
            AVG(generation_time_ms) as avgGenerationTime,
            AVG(text_length) as avgTextLength
        FROM message_embedding
    """,
    )
    suspend fun getEmbeddingStats(): EmbeddingStats

    /**
     * 获取向量化进度
     */
    @Query(
        """
        SELECT
            (SELECT COUNT(*) FROM chat_raw) as totalMessages,
            (SELECT COUNT(*) FROM message_embedding WHERE embedding_status = 'COMPLETED') as embeddedMessages
    """,
    )
    suspend fun getEmbeddingProgress(): EmbeddingProgress

    /**
     * 清理失败的嵌入记录
     */
    @Query(
        "DELETE FROM message_embedding WHERE embedding_status = 'FAILED' AND created_at < :beforeTimestamp",
    )
    suspend fun cleanupFailedEmbeddings(beforeTimestamp: Long): Int

    /**
     * 更新嵌入状态
     */
    @Query("UPDATE message_embedding SET embedding_status = :status WHERE message_id = :messageId")
    suspend fun updateEmbeddingStatus(
        messageId: Long,
        status: String,
    ): Int

    // ==================== Flow观察方法 ====================

    /**
     * 观察嵌入向量统计信息
     */
    @Query(
        """
        SELECT
            COUNT(*) as total,
            SUM(CASE WHEN embedding_status = 'COMPLETED' THEN 1 ELSE 0 END) as completed,
            SUM(CASE WHEN embedding_status = 'PENDING' THEN 1 ELSE 0 END) as pending,
            SUM(CASE WHEN embedding_status = 'PROCESSING' THEN 1 ELSE 0 END) as processing,
            SUM(CASE WHEN embedding_status = 'FAILED' THEN 1 ELSE 0 END) as failed,
            AVG(generation_time_ms) as avgGenerationTime,
            AVG(text_length) as avgTextLength
        FROM message_embedding
    """,
    )
    fun observeEmbeddingStats(): Flow<EmbeddingStats>

    /**
     * 观察向量化进度
     */
    @Query(
        """
        SELECT
            (SELECT COUNT(*) FROM chat_raw) as totalMessages,
            (SELECT COUNT(*) FROM message_embedding WHERE embedding_status = 'COMPLETED') as embeddedMessages
    """,
    )
    fun observeEmbeddingProgress(): Flow<EmbeddingProgress>
}

/**
 * 嵌入向量与消息的联合查询结果
 */
data class EmbeddingWithMessage(
    @Embedded val embedding: MessageEmbeddingEntity,
    @ColumnInfo(name = "content") val content: String,
    @ColumnInfo(name = "session_id") val sessionId: String,
    @ColumnInfo(name = "role") val role: String,
    @ColumnInfo(name = "timestamp") val timestamp: Long,
)

/**
 * 嵌入向量统计信息
 */
data class EmbeddingStats(
    val total: Int,
    val completed: Int,
    val pending: Int,
    val processing: Int,
    val failed: Int,
    val avgGenerationTime: Double?,
    val avgTextLength: Double?,
) {
    val completionRate: Float
        get() = if (total > 0) completed.toFloat() / total else 0f

    val failureRate: Float
        get() = if (total > 0) failed.toFloat() / total else 0f
}

/**
 * 向量化进度
 */
data class EmbeddingProgress(
    val totalMessages: Int,
    val embeddedMessages: Int,
) {
    val progressPercentage: Float
        get() = if (totalMessages > 0) embeddedMessages.toFloat() / totalMessages else 0f

    val remainingMessages: Int
        get() = totalMessages - embeddedMessages
}
