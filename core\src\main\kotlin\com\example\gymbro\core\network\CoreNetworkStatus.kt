package com.example.gymbro.core.network

/**
 * 核心网络状态类
 *
 * 仅用于core模块内部，封装网络连接的状态信息
 */
data class CoreNetworkStatus(
    /** 网络是否可用 */
    val isAvailable: Boolean = false,

    /** 网络连接类型 */
    val type: NetworkType = NetworkType.NONE,

    /** 网络是否计费（例如移动数据） */
    val isMetered: Boolean = false,

    /** 网络速度估计 */
    val networkSpeed: NetworkSpeed = NetworkSpeed.UNKNOWN,

    /** 网络是否受限（例如需要登录门户认证） */
    val isRestricted: Boolean = false,
) {
    /**
     * 转换为NetworkStatus
     */
    fun toNetworkStatus(): NetworkStatus {
        return NetworkStatus(
            isAvailable = isAvailable,
            type = type,
            isMetered = isMetered,
            networkSpeed = networkSpeed,
            isRestricted = isRestricted,
        )
    }
}
