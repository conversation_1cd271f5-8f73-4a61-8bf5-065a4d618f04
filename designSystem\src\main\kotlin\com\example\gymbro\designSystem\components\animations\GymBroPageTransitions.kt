package com.example.gymbro.designSystem.components.animations

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.navigation.NavBackStackEntry

/**
 * GymBro 统一页面切换动画系统
 *
 * 提供ChatGPT风格的平滑切换动画，替代传统的滑动效果
 * 使用淡入淡出 + 轻微缩放，提供更现代的用户体验
 */
object GymBroPageTransitions {
    // === 动画时长配置 ===
    private const val FAST_DURATION = 200 // 快速切换
    private const val SMOOTH_DURATION = 250 // 平滑切换（推荐）
    private const val SLOW_DURATION = 300 // 慢速切换

    // === 缩放参数 ===
    private const val ENTER_SCALE = 0.95f // 进入时的初始缩放
    private const val EXIT_SCALE = 1.05f // 退出时的目标缩放

    // === ChatGPT风格动画 - 主推荐 ===

    /**
     * 平滑淡入动画
     * 页面从95%缩放淡入到100%，模拟ChatGPT的响应切换效果
     */
    val smoothFadeIn: AnimatedContentTransitionScope<NavBackStackEntry>.() -> EnterTransition = {
        fadeIn(
            animationSpec =
            tween(
                durationMillis = SMOOTH_DURATION,
                easing = FastOutSlowInEasing,
            ),
        ) +
            scaleIn(
                animationSpec =
                tween(
                    durationMillis = SMOOTH_DURATION,
                    easing = FastOutSlowInEasing,
                ),
                initialScale = ENTER_SCALE,
            )
    }

    /**
     * 平滑淡出动画
     * 页面从100%缩放淡出到105%，创造自然的离开感
     */
    val smoothFadeOut: AnimatedContentTransitionScope<NavBackStackEntry>.() -> ExitTransition = {
        fadeOut(
            animationSpec =
            tween(
                durationMillis = SMOOTH_DURATION,
                easing = FastOutSlowInEasing,
            ),
        ) +
            scaleOut(
                animationSpec =
                tween(
                    durationMillis = SMOOTH_DURATION,
                    easing = FastOutSlowInEasing,
                ),
                targetScale = EXIT_SCALE,
            )
    }

    /**
     * 返回时的平滑淡入
     * 与前进动画保持一致性
     */
    val smoothPopEnter: AnimatedContentTransitionScope<NavBackStackEntry>.() -> EnterTransition = smoothFadeIn

    /**
     * 返回时的平滑淡出
     * 与前进动画保持一致性
     */
    val smoothPopExit: AnimatedContentTransitionScope<NavBackStackEntry>.() -> ExitTransition = smoothFadeOut

    // === 快速切换动画 - 适合频繁操作 ===

    /**
     * 快速淡入动画
     * 适合底部导航栏切换等频繁操作
     */
    val fastFadeIn: AnimatedContentTransitionScope<NavBackStackEntry>.() -> EnterTransition = {
        fadeIn(
            animationSpec =
            tween(
                durationMillis = FAST_DURATION,
                easing = LinearOutSlowInEasing,
            ),
        )
    }

    /**
     * 快速淡出动画
     */
    val fastFadeOut: AnimatedContentTransitionScope<NavBackStackEntry>.() -> ExitTransition = {
        fadeOut(
            animationSpec =
            tween(
                durationMillis = FAST_DURATION,
                easing = FastOutLinearInEasing,
            ),
        )
    }

    // === 即时切换动画 - 适合设置页面 ===

    /**
     * 即时淡入 - 无缩放效果，纯淡入淡出
     */
    val instantFadeIn: AnimatedContentTransitionScope<NavBackStackEntry>.() -> EnterTransition = {
        fadeIn(
            animationSpec =
            tween(
                durationMillis = 150,
                easing = LinearEasing,
            ),
        )
    }

    /**
     * 即时淡出
     */
    val instantFadeOut: AnimatedContentTransitionScope<NavBackStackEntry>.() -> ExitTransition = {
        fadeOut(
            animationSpec =
            tween(
                durationMillis = 150,
                easing = LinearEasing,
            ),
        )
    }

    // === 特殊效果动画 ===

    /**
     * 弹性进入动画
     * 适合重要页面或特殊场景
     */
    val springFadeIn: AnimatedContentTransitionScope<NavBackStackEntry>.() -> EnterTransition = {
        fadeIn(
            animationSpec = tween(SMOOTH_DURATION),
        ) +
            scaleIn(
                animationSpec =
                spring(
                    dampingRatio = Spring.DampingRatioMediumBouncy,
                    stiffness = Spring.StiffnessMedium,
                ),
                initialScale = ENTER_SCALE,
            )
    }

    // === 预设组合 ===

    /**
     * ChatGPT风格预设 - 推荐用于所有页面切换
     */
    object ChatGPTStyle {
        val enterTransition = smoothFadeIn
        val exitTransition = smoothFadeOut
        val popEnterTransition = smoothPopEnter
        val popExitTransition = smoothPopExit
    }

    /**
     * 快速切换预设 - 适合底部导航栏
     */
    object FastStyle {
        val enterTransition = fastFadeIn
        val exitTransition = fastFadeOut
        val popEnterTransition = fastFadeIn
        val popExitTransition = fastFadeOut
    }

    /**
     * 即时切换预设 - 适合设置页面
     */
    object InstantStyle {
        val enterTransition = instantFadeIn
        val exitTransition = instantFadeOut
        val popEnterTransition = instantFadeIn
        val popExitTransition = instantFadeOut
    }
}

/**
 * 便捷扩展函数 - 简化使用
 */

/**
 * 应用ChatGPT风格动画的扩展函数
 */
fun AnimatedContentTransitionScope<NavBackStackEntry>.chatGPTStyleEnter() = GymBroPageTransitions.ChatGPTStyle.enterTransition(
    this,
)

fun AnimatedContentTransitionScope<NavBackStackEntry>.chatGPTStyleExit() = GymBroPageTransitions.ChatGPTStyle.exitTransition(
    this,
)

fun AnimatedContentTransitionScope<NavBackStackEntry>.chatGPTStylePopEnter() = GymBroPageTransitions.ChatGPTStyle.popEnterTransition(
    this,
)

fun AnimatedContentTransitionScope<NavBackStackEntry>.chatGPTStylePopExit() = GymBroPageTransitions.ChatGPTStyle.popExitTransition(
    this,
)
