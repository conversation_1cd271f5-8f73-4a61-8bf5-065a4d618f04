package com.example.gymbro.data.coach.mapper

import com.example.gymbro.data.local.dto.FtsHit
import com.example.gymbro.data.local.dto.VssHit
import com.example.gymbro.data.local.entity.SearchContentEntity
import com.example.gymbro.domain.model.search.SearchResult
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 搜索结果映射器
 *
 * 负责将数据层的搜索结果转换为领域层模型
 */
@Singleton
class SearchMapper @Inject constructor() {

    /**
     * 将FTS和VSS搜索结果映射为SearchResult列表
     */
    fun mapToSearchResults(
        ftsHits: List<FtsHit>,
        details: List<SearchContentEntity>,
    ): List<SearchResult> {
        val detailsMap = details.associateBy { it.id }

        return ftsHits.mapNotNull { hit ->
            val detail = detailsMap[hit.id]
            detail?.let {
                SearchResult(
                    id = hit.id.toString(),
                    content = hit.highlight ?: detail.content,
                    score = hit.score,
                    contentType = "chat_message",
                    metadata = detail.metadata,
                    timestamp = detail.createdAt,
                )
            }
        }
    }

    /**
     * 将VSS搜索结果映射为SearchResult列表
     */
    fun mapVssToSearchResults(
        vssHits: List<VssHit>,
        details: List<SearchContentEntity>,
    ): List<SearchResult> {
        val detailsMap = details.associateBy { it.id }

        return vssHits.mapNotNull { hit ->
            val detail = detailsMap[hit.id]
            detail?.let {
                SearchResult(
                    id = hit.id.toString(),
                    content = detail.content,
                    score = hit.score,
                    contentType = "chat_message",
                    metadata = detail.metadata,
                    timestamp = detail.createdAt,
                )
            }
        }
    }
}
