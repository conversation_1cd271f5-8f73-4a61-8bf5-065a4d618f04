package com.example.gymbro.data.remote.subscription.dto

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * 区域定价数据传输对象
 *
 * @property id 区域定价ID
 * @property regionCode 区域代码
 * @property regionName 区域名称
 * @property currencyCode 货币代码
 * @property currencySymbol 货币符号
 * @property monthlyPrice 月度价格
 * @property yearlyPrice 年度价格
 * @property discountPercentage 折扣百分比
 */
@Serializable
data class RegionalPricingDto(
    @SerialName("id") val id: String,
    @SerialName("region_code") val regionCode: String,
    @SerialName("region_name") val regionName: String,
    @SerialName("currency_code") val currencyCode: String,
    @SerialName("currency_symbol") val currencySymbol: String,
    @SerialName("monthly_price") val monthlyPrice: Double,
    @SerialName("yearly_price") val yearlyPrice: Double,
    @SerialName("discount_percentage") val discountPercentage: Int,
)
