package com.example.gymbro.core.ai.prompt.builder

import org.junit.Assert.*
import org.junit.Test

/**
 * DeepSeek模型检测机制测试
 *
 * 测试LayeredPromptBuilder的通用DeepSeek检测功能：
 * 1. 检测各种DeepSeek模型名称（大小写不敏感）
 * 2. 验证自动添加_deepseek后缀的逻辑
 * 3. 确保非DeepSeek模型不受影响
 */
class DeepSeekDetectionTest {

    /**
     * 测试DeepSeek模型检测（大小写不敏感）
     */
    @Test
    fun `test DeepSeek model detection case insensitive`() {
        val testCases = listOf(
            "deepseek-chat" to true,
            "DeepSeek-V2" to true,
            "DEEPSEEK-CODER" to true,
            "deepseek-v2.5" to true,
            "gpt-4" to false,
            "claude-3" to false,
            "gemini-pro" to false,
            null to false,
            "" to false,
        )

        testCases.forEach { (modelName, expected) ->
            val result = isDeepSeekModelPublic(modelName)
            assertEquals(
                "Model '$modelName' should ${if (expected) "be" else "not be"} detected as DeepSeek",
                expected,
                result,
            )
        }
    }

    /**
     * 测试提示词模式选择逻辑
     */
    @Test
    fun `test prompt mode selection for different models`() {
        val testCases = listOf(
            // DeepSeek模型应该添加_deepseek后缀
            Triple("standard", "deepseek-chat", "standard_deepseek"),
            Triple("layered", "DeepSeek-V2", "layered_deepseek"),
            Triple("pipeline", "DEEPSEEK-CODER", "pipeline_deepseek"),
            Triple("blank", "deepseek-v2.5", "blank_deepseek"),

            // 非DeepSeek模型保持原模式
            Triple("standard", "gpt-4", "standard"),
            Triple("layered", "claude-3", "layered"),
            Triple("pipeline", "gemini-pro", "pipeline"),
            Triple("blank", null, "blank"),
        )

        testCases.forEach { (currentMode, modelName, expectedMode) ->
            val result = selectPromptModeForModelPublic(currentMode, modelName)
            assertEquals(
                "Mode '$currentMode' with model '$modelName' should result in '$expectedMode'",
                expectedMode,
                result,
            )
        }
    }

    /**
     * 测试边界情况
     */
    @Test
    fun `test edge cases`() {
        // 空字符串和null
        assertFalse("Empty string should not be detected as DeepSeek", isDeepSeekModelPublic(""))
        assertFalse("Null should not be detected as DeepSeek", isDeepSeekModelPublic(null))

        // 包含deepseek但不是模型名的情况
        assertTrue(
            "String containing deepseek should be detected",
            isDeepSeekModelPublic("my-deepseek-model"),
        )
        assertTrue("Partial match should work", isDeepSeekModelPublic("deepseek"))

        // 大小写混合
        assertTrue("Mixed case should work", isDeepSeekModelPublic("DeEpSeEk-ChAt"))
    }

    // 公共测试方法，模拟LayeredPromptBuilder的私有方法
    private fun isDeepSeekModelPublic(modelName: String?): Boolean {
        return modelName?.contains("deepseek", ignoreCase = true) == true
    }

    private fun selectPromptModeForModelPublic(currentMode: String, model: String?): String {
        return if (isDeepSeekModelPublic(model)) {
            "${currentMode}_deepseek"
        } else {
            currentMode
        }
    }
}
