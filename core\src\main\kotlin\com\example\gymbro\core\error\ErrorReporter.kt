package com.example.gymbro.core.error

import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.ui.text.UiText
import kotlinx.coroutines.flow.Flow

/**
 * 错误报告接口
 * 提供统一的错误收集和处理机制
 */
interface ErrorReporter {
    /**
     * 报告错误
     * @param error 错误对象
     * @param tag 错误标签，用于分类
     * @param fatal 是否为致命错误
     */
    fun reportError(error: Throwable, tag: String = "", fatal: Boolean = false)

    /**
     * 报告业务错误
     * @param error 业务错误对象
     * @param tag 错误标签，用于分类
     */
    fun reportBusinessError(error: ModernDataError, tag: String = "")

    /**
     * 报告用户可见的错误消息
     * @param message 错误消息
     * @param tag 错误标签，用于分类
     */
    fun reportUserError(message: UiText, tag: String = "")

    /**
     * 获取最近的错误流
     * @return 错误对象流
     */
    fun getRecentErrors(): Flow<ErrorReport>

    /**
     * 清除错误历史
     */
    fun clearErrorHistory()
}

/**
 * 错误报告数据类
 * @property error 错误对象
 * @property tag 错误标签
 * @property timestamp 错误发生时间戳
 * @property fatal 是否为致命错误
 */
data class ErrorReport(
    val error: Any,
    val tag: String,
    val timestamp: Long = System.currentTimeMillis(),
    val fatal: Boolean = false,
)
