package com.example.gymbro.core.network.rest.interceptors

import com.example.gymbro.core.network.security.PiiSanitizer
import okhttp3.Interceptor
import okhttp3.Response
import okio.Buffer
import timber.log.Timber
import java.nio.charset.Charset

/**
 * 安全的日志拦截器
 *
 * 提供网络请求日志记录，同时保护敏感信息
 * 集成PII数据脱敏处理，确保生产环境日志安全合规
 */
class SafeLoggingInterceptor(
    private val isDebugMode: Boolean = false,
    private val isProduction: Boolean = !isDebugMode,
) : Interceptor {

    companion object {
        private val SENSITIVE_HEADERS = setOf(
            "authorization",
            "cookie",
            "set-cookie",
            "x-api-key",
            "api-key",
        )

        private val SENSITIVE_BODY_KEYS = setOf(
            "password",
            "token",
            "secret",
            "key",
            "authorization",
        )
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val startTime = System.currentTimeMillis()

        if (isDebugMode) {
            logRequest(request)
        }

        val response = try {
            chain.proceed(request)
        } catch (e: Exception) {
            val duration = System.currentTimeMillis() - startTime
            Timber.e(e, "🚨 网络请求失败: ${request.method} ${request.url} (${duration}ms)")
            throw e
        }

        val duration = System.currentTimeMillis() - startTime

        if (isDebugMode) {
            logResponse(response, duration)
        } else {
            // 生产模式只记录基本信息
            Timber.d("🌐 ${request.method} ${request.url} - ${response.code} (${duration}ms)")
        }

        return response
    }

    private fun logRequest(request: okhttp3.Request) {
        Timber.d("🚀 --> ${request.method} ${request.url}")

        // 记录头部（过滤敏感信息）
        request.headers.forEach { (name, value) ->
            if (name.lowercase() in SENSITIVE_HEADERS) {
                Timber.v("📋 $name: ***")
            } else {
                Timber.v("📋 $name: $value")
            }
        }

        // 记录请求体（如果存在且不太大）
        request.body?.let { body ->
            if (body.contentLength() in 1..8192) { // 最大8KB
                val buffer = Buffer()
                body.writeTo(buffer)
                val charset = body.contentType()?.charset(Charset.forName("UTF-8")) ?: Charset.forName("UTF-8")
                val content = buffer.readString(charset)

                val sanitizedContent = sanitizeContent(content)
                Timber.v("📝 Request Body: $sanitizedContent")
            } else {
                Timber.v("📝 Request Body: [${body.contentLength()} bytes]")
            }
        }

        Timber.d("🚀 --> END ${request.method}")
    }

    private fun logResponse(response: Response, duration: Long) {
        Timber.d("🏁 <-- ${response.code} ${response.message} ${response.request.url} (${duration}ms)")

        // 记录响应头部
        response.headers.forEach { (name, value) ->
            if (name.lowercase() in SENSITIVE_HEADERS) {
                Timber.v("📋 $name: ***")
            } else {
                Timber.v("📋 $name: $value")
            }
        }

        // 记录响应体（如果存在且不太大）
        response.body?.let { body ->
            val contentLength = body.contentLength()
            if (contentLength in 1..8192) { // 最大8KB
                val source = body.source()
                source.request(Long.MAX_VALUE)
                val buffer = source.buffer

                val charset = body.contentType()?.charset(Charset.forName("UTF-8")) ?: Charset.forName("UTF-8")
                val content = buffer.clone().readString(charset)

                val sanitizedContent = sanitizeContent(content)
                Timber.v("📝 Response Body: $sanitizedContent")
            } else {
                Timber.v("📝 Response Body: [$contentLength bytes]")
            }
        }

        Timber.d("🏁 <-- END HTTP")
    }

    /**
     * 清理敏感内容
     * 使用PiiSanitizer进行全面的敏感信息脱敏
     */
    private fun sanitizeContent(content: String): String {
        // 首先进行JSON字段脱敏
        var sanitized = PiiSanitizer.sanitizeJsonFields(content, isProduction)

        // 然后进行通用内容脱敏（邮箱、手机号等）
        sanitized = PiiSanitizer.sanitizeContent(sanitized, isProduction)

        // 兼容原有的简单字段过滤（作为备用）
        SENSITIVE_BODY_KEYS.forEach { key ->
            val regex = """"$key"\s*:\s*"[^"]*"""".toRegex(RegexOption.IGNORE_CASE)
            sanitized = sanitized.replace(regex, """"$key":"***"""")
        }

        return sanitized
    }
}
