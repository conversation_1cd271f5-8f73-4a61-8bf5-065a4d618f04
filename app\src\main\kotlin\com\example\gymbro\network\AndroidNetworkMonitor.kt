package com.example.gymbro.network

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import com.example.gymbro.core.network.NetworkMonitor
import com.example.gymbro.core.network.NetworkSpeed
import com.example.gymbro.core.network.NetworkStatus
import com.example.gymbro.core.network.NetworkType
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * NetworkMonitor的Android实现
 *
 * 使用Android ConnectivityManager监控网络状态
 */
@Singleton
class AndroidNetworkMonitor @Inject constructor(
    @ApplicationContext private val context: Context,
) : NetworkMonitor {

    private val connectivityManager = context.getSystemService(
        Context.CONNECTIVITY_SERVICE,
    ) as ConnectivityManager

    private val _isNetworkAvailable = MutableStateFlow(false)
    override val isNetworkAvailable: Flow<Boolean> = _isNetworkAvailable.asStateFlow()

    private val _networkType = MutableStateFlow(NetworkType.NONE)
    override val networkType: Flow<NetworkType> = _networkType.asStateFlow()

    private val _isMetered = MutableStateFlow(false)
    override val isMetered: Flow<Boolean> = _isMetered.asStateFlow()

    private val _networkSpeed = MutableStateFlow(NetworkSpeed.UNKNOWN)
    override val networkSpeed: Flow<NetworkSpeed> = _networkSpeed.asStateFlow()

    private val _isRestricted = MutableStateFlow(false)
    override val isRestricted: Flow<Boolean> = _isRestricted.asStateFlow()

    private val _status = MutableStateFlow(
        NetworkStatus(
            isAvailable = false,
            type = NetworkType.NONE,
            isMetered = false,
            networkSpeed = NetworkSpeed.UNKNOWN,
            isRestricted = false,
        ),
    )
    override val status: Flow<NetworkStatus> = _status.asStateFlow()

    private val networkCallback = object : ConnectivityManager.NetworkCallback() {
        override fun onAvailable(network: Network) {
            Timber.d("网络连接已建立")
            _isNetworkAvailable.value = true
        }

        override fun onLost(network: Network) {
            Timber.d("网络连接已断开")
            _isNetworkAvailable.value = false
            _networkType.value = NetworkType.NONE
            updateStatusFlow()
        }

        override fun onCapabilitiesChanged(
            network: Network,
            networkCapabilities: NetworkCapabilities,
        ) {
            val hasInternet = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
                networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
            Timber.d("网络能力已改变，Internet连接: %s", hasInternet)
            _isNetworkAvailable.value = hasInternet

            // 更新网络类型
            _networkType.value = when {
                !hasInternet -> NetworkType.NONE
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> NetworkType.WIFI
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> NetworkType.CELLULAR
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> NetworkType.ETHERNET
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_VPN) -> NetworkType.VPN
                else -> NetworkType.OTHER
            }

            // 更新网络速度估计
            _networkSpeed.value = estimateNetworkSpeed(networkCapabilities)

            // 更新计费状态
            _isMetered.value = !networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_NOT_METERED)

            // 更新受限状态
            _isRestricted.value = !networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_NOT_RESTRICTED)

            // 更新汇总状态
            updateStatusFlow()
        }
    }

    /**
     * 根据网络能力估计网络速度
     */
    private fun estimateNetworkSpeed(networkCapabilities: NetworkCapabilities): NetworkSpeed {
        return when {
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> {
                if (networkCapabilities.linkDownstreamBandwidthKbps > 50000) {
                    NetworkSpeed.VERY_FAST
                } else if (networkCapabilities.linkDownstreamBandwidthKbps > 10000) {
                    NetworkSpeed.FAST
                } else {
                    NetworkSpeed.MEDIUM
                }
            }
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> {
                if (networkCapabilities.linkDownstreamBandwidthKbps > 20000) {
                    NetworkSpeed.FAST
                } else if (networkCapabilities.linkDownstreamBandwidthKbps > 5000) {
                    NetworkSpeed.MEDIUM
                } else {
                    NetworkSpeed.SLOW
                }
            }
            else -> NetworkSpeed.UNKNOWN
        }
    }

    override fun startMonitoring() {
        try {
            val networkRequest = NetworkRequest.Builder()
                .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                .build()
            connectivityManager.registerNetworkCallback(networkRequest, networkCallback)
            Timber.d("网络状态监听已注册")

            // 初始化网络状态
            updateInitialNetworkState()
        } catch (e: Exception) {
            Timber.e(e, "注册网络状态监听失败")
        }
    }

    override fun stopMonitoring() {
        try {
            connectivityManager.unregisterNetworkCallback(networkCallback)
            Timber.d("网络状态监听已注销")
        } catch (e: Exception) {
            Timber.e(e, "注销网络状态监听失败")
        }
    }

    /**
     * 初始化时更新当前网络状态
     */
    private fun updateInitialNetworkState() {
        val network = connectivityManager.activeNetwork
        if (network != null) {
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            if (capabilities != null) {
                networkCallback.onCapabilitiesChanged(network, capabilities)
            } else {
                _isNetworkAvailable.value = false
                _networkType.value = NetworkType.NONE
                updateStatusFlow()
            }
        } else {
            _isNetworkAvailable.value = false
            _networkType.value = NetworkType.NONE
            updateStatusFlow()
        }
    }

    /**
     * 更新状态Flow
     */
    private fun updateStatusFlow() {
        _status.value = NetworkStatus(
            isAvailable = _isNetworkAvailable.value,
            type = _networkType.value,
            isMetered = _isMetered.value,
            networkSpeed = _networkSpeed.value,
            isRestricted = _isRestricted.value,
        )
    }
}
