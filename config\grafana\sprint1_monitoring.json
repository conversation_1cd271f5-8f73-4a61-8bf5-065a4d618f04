{"dashboard": {"id": null, "title": "Sprint 1 - Function Calling & Workout Metrics", "tags": ["gymbro", "sprint1", "function-calling", "workout"], "timezone": "browser", "panels": [{"id": 1, "title": "Function Calling成功率", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "horizontal", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "textMode": "auto"}, "pluginVersion": "8.5.0", "targets": [{"expr": "rate(func_call_success_total[5m]) / rate(func_call_total[5m]) * 100", "interval": "", "legendFormat": "FuncCallHitRate", "refId": "A"}], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 90}, {"color": "green", "value": 95}]}, "unit": "percent"}, {"id": 2, "title": "Schema错误率", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "horizontal", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "textMode": "auto"}, "targets": [{"expr": "rate(schema_validation_errors_total[5m]) / rate(function_calls_total[5m]) * 100", "interval": "", "legendFormat": "func_error_rate", "refId": "A"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 3}, {"color": "red", "value": 5}]}, "unit": "percent"}, {"id": 3, "title": "Token消耗分离统计", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"expr": "avg_over_time(tokens_input_per_request[5m])", "interval": "", "legendFormat": "Input Tokens (avg)", "refId": "A"}, {"expr": "avg_over_time(tokens_output_per_request[5m])", "interval": "", "legendFormat": "Output Tokens (avg)", "refId": "B"}, {"expr": "avg_over_time(tokens_func_def_per_request[5m])", "interval": "", "legendFormat": "Function Def <PERSON> (avg)", "refId": "C"}], "yAxes": [{"label": "Token Count", "logBase": 1, "max": null, "min": 0, "show": true}]}, {"id": 4, "title": "端到端延迟分布", "type": "histogram", "gridPos": {"h": 8, "w": 8, "x": 0, "y": 8}, "targets": [{"expr": "histogram_quantile(0.50, rate(end_to_end_latency_seconds_bucket[5m]))", "legendFormat": "P50", "refId": "A"}, {"expr": "histogram_quantile(0.90, rate(end_to_end_latency_seconds_bucket[5m]))", "legendFormat": "P90", "refId": "B"}, {"expr": "histogram_quantile(0.99, rate(end_to_end_latency_seconds_bucket[5m]))", "legendFormat": "P99", "refId": "C"}], "yAxes": [{"label": "Latency (seconds)", "logBase": 1, "max": 30, "min": 0, "show": true}]}, {"id": 5, "title": "Token成本监控", "type": "timeseries", "gridPos": {"h": 8, "w": 8, "x": 8, "y": 8}, "options": {"legend": {"calcs": ["last"], "displayMode": "table", "placement": "bottom"}}, "targets": [{"expr": "increase(tokens_cost_usd_total[1h])", "legendFormat": "Hourly Cost ($)", "refId": "A"}, {"expr": "increase(tokens_cost_usd_total[1d])", "legendFormat": "Daily Cost ($)", "refId": "B"}], "thresholds": [{"colorMode": "critical", "op": "gt", "value": 8.33, "visible": true}], "unit": "currencyUSD"}, {"id": 6, "title": "ActiveWorkout性能指标", "type": "timeseries", "gridPos": {"h": 8, "w": 8, "x": 16, "y": 8}, "targets": [{"expr": "avg_over_time(active_workout_ui_response_time_ms[5m])", "legendFormat": "UI Response Time (ms)", "refId": "A"}, {"expr": "rate(active_workout_crashes_total[5m]) * 3600", "legendFormat": "Crash Rate (/hour)", "refId": "B"}, {"expr": "avg_over_time(session_state_save_time_ms[5m])", "legendFormat": "State Save Time (ms)", "refId": "C"}], "yAxes": [{"label": "Time (ms) / Rate", "logBase": 1, "max": null, "min": 0, "show": true}]}, {"id": 7, "title": "Function Calling降级统计", "type": "piechart", "gridPos": {"h": 8, "w": 6, "x": 0, "y": 16}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "pieType": "pie", "tooltip": {"mode": "single", "sort": "none"}, "legend": {"displayMode": "visible", "placement": "right"}}, "targets": [{"expr": "sum(increase(func_call_success_total[1h]))", "legendFormat": "Function Call Success", "refId": "A"}, {"expr": "sum(increase(func_call_fallback_json_total[1h]))", "legendFormat": "Fallback to JSON", "refId": "B"}, {"expr": "sum(increase(func_call_schema_retry_total[1h]))", "legendFormat": "<PERSON><PERSON><PERSON>", "refId": "C"}]}, {"id": 8, "title": "训练会话状态分布", "type": "bargauge", "gridPos": {"h": 8, "w": 6, "x": 6, "y": 16}, "options": {"orientation": "horizontal", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}}, "targets": [{"expr": "sum by(status) (workout_sessions_total)", "legendFormat": "{{status}}", "refId": "A"}]}, {"id": 9, "title": "系统资源使用率", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "targets": [{"expr": "rate(process_cpu_seconds_total[5m]) * 100", "legendFormat": "CPU Usage (%)", "refId": "A"}, {"expr": "process_resident_memory_bytes / 1024 / 1024", "legendFormat": "Memory Usage (MB)", "refId": "B"}, {"expr": "rate(go_gc_duration_seconds_sum[5m]) * 1000", "legendFormat": "GC Duration (ms)", "refId": "C"}], "yAxes": [{"label": "Usage", "logBase": 1, "max": null, "min": 0, "show": true}]}], "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "templating": {"list": [{"current": {"selected": false, "text": "All", "value": "$__all"}, "hide": 0, "includeAll": true, "label": "Environment", "multi": false, "name": "env", "options": [], "query": "label_values(up, env)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}]}, "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}, {"datasource": "prometheus", "enable": true, "expr": "changes(sprint1_deployment_info[1h]) > 0", "iconColor": "green", "name": "Deployments", "titleFormat": "Sprint 1 Deployment", "textFormat": "{{version}}"}]}, "refresh": "30s", "schemaVersion": 30, "style": "dark", "uid": "sprint1-monitoring", "version": 1}}