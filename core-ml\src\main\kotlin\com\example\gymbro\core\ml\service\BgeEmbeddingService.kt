package com.example.gymbro.core.ml.service

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.features.FeatureErrors
import com.example.gymbro.core.ml.embedding.EmbeddingEngine
import com.example.gymbro.core.ui.text.UiText
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * BGE嵌入服务
 *
 * 提供高性能的中英文语义向量化服务，支持：
 * - 单文本向量化
 * - 批量文本向量化
 * - 语义相似度计算
 * - 模型预热和性能优化
 */
@Singleton
class BgeEmbeddingService @Inject constructor(
    private val embeddingEngine: EmbeddingEngine,
) {

    private var isWarmedUp = false

    /**
     * 单文本向量化
     */
    suspend fun embedText(text: String): ModernResult<FloatArray> {
        return withContext(Dispatchers.Default) {
            try {
                if (text.isBlank()) {
                    return@withContext ModernResult.Error(
                        FeatureErrors.CoachError.invalidInput(
                            operationName = "BgeEmbeddingService.embedText",
                            message = UiText.DynamicString("输入文本不能为空"),
                            inputType = "empty_text",
                            value = text,
                        ),
                    )
                }

                // 确保模型已预热
                ensureWarmedUp()

                val startTime = System.currentTimeMillis()
                val embedding = embeddingEngine.embed(text)
                val duration = System.currentTimeMillis() - startTime

                Timber.d("BgeEmbeddingService: 文本向量化完成 duration=${duration}ms, dim=${embedding.size}")

                if (duration > 20) {
                    Timber.w("BgeEmbeddingService: 向量化耗时超过阈值 ${duration}ms > 20ms")
                }

                ModernResult.Success(embedding)
            } catch (e: Exception) {
                Timber.e(e, "BgeEmbeddingService: 文本向量化失败")
                ModernResult.Error(
                    FeatureErrors.CoachError.processingFailed(
                        operationName = "BgeEmbeddingService.embedText",
                        message = UiText.DynamicString("文本向量化失败"),
                        processType = "embedding",
                        reason = "embedding_failed",
                        cause = e,
                        metadataMap = mapOf(
                            "text_length" to text.length,
                            "exception_type" to e.javaClass.simpleName,
                        ),
                    ),
                )
            }
        }
    }

    /**
     * 批量文本向量化
     */
    suspend fun embedTexts(texts: List<String>): ModernResult<List<FloatArray>> {
        return withContext(Dispatchers.Default) {
            try {
                if (texts.isEmpty()) {
                    return@withContext ModernResult.Error(
                        FeatureErrors.CoachError.invalidInput(
                            operationName = "BgeEmbeddingService.embedTexts",
                            message = UiText.DynamicString("文本列表不能为空"),
                            inputType = "empty_list",
                            value = "size=0",
                        ),
                    )
                }

                if (texts.size > 100) {
                    return@withContext ModernResult.Error(
                        FeatureErrors.CoachError.invalidInput(
                            operationName = "BgeEmbeddingService.embedTexts",
                            message = UiText.DynamicString("批量处理文本数量过多，请分批处理"),
                            inputType = "batch_too_large",
                            value = "size=${texts.size}",
                            metadataMap = mapOf(
                                "max_batch_size" to 100,
                                "actual_size" to texts.size,
                            ),
                        ),
                    )
                }

                // 过滤空文本
                val validTexts = texts.filter { it.isNotBlank() }
                if (validTexts.isEmpty()) {
                    return@withContext ModernResult.Error(
                        FeatureErrors.CoachError.invalidInput(
                            operationName = "BgeEmbeddingService.embedTexts",
                            message = UiText.DynamicString("没有有效的文本内容"),
                            inputType = "no_valid_text",
                            value = "filtered_size=0",
                        ),
                    )
                }

                // 确保模型已预热
                ensureWarmedUp()

                val startTime = System.currentTimeMillis()
                val embeddings = embeddingEngine.embedBatch(validTexts)
                val duration = System.currentTimeMillis() - startTime

                val avgTimePerText = duration.toFloat() / validTexts.size
                Timber.d(
                    "BgeEmbeddingService: 批量向量化完成 count=${validTexts.size}, total=${duration}ms, avg=${avgTimePerText}ms",
                )

                if (avgTimePerText > 20) {
                    Timber.w("BgeEmbeddingService: 平均向量化时间超过阈值 ${avgTimePerText}ms > 20ms")
                }

                ModernResult.Success(embeddings)
            } catch (e: Exception) {
                Timber.e(e, "BgeEmbeddingService: 批量文本向量化失败")
                ModernResult.Error(
                    FeatureErrors.CoachError.processingFailed(
                        operationName = "BgeEmbeddingService.embedTexts",
                        message = UiText.DynamicString("批量文本向量化失败"),
                        processType = "batch_embedding",
                        reason = "batch_embedding_failed",
                        cause = e,
                        metadataMap = mapOf(
                            "text_count" to texts.size,
                            "exception_type" to e.javaClass.simpleName,
                        ),
                    ),
                )
            }
        }
    }

    /**
     * 计算语义相似度
     *
     * @param text1 第一个文本
     * @param text2 第二个文本
     * @return 相似度分数 (0.0 到 1.0)
     */
    suspend fun calculateSimilarity(text1: String, text2: String): ModernResult<Float> {
        return try {
            val embedding1Result = embedText(text1)
            if (embedding1Result is ModernResult.Error) {
                return embedding1Result.copy()
            }

            val embedding2Result = embedText(text2)
            if (embedding2Result is ModernResult.Error) {
                return embedding2Result.copy()
            }

            val embedding1 = (embedding1Result as ModernResult.Success).data
            val embedding2 = (embedding2Result as ModernResult.Success).data

            val similarity = cosineSimilarity(embedding1, embedding2)

            Timber.d("BgeEmbeddingService: 语义相似度计算完成 similarity=$similarity")
            ModernResult.Success(similarity)
        } catch (e: Exception) {
            Timber.e(e, "BgeEmbeddingService: 语义相似度计算失败")
            ModernResult.Error(
                FeatureErrors.CoachError.processingFailed(
                    operationName = "BgeEmbeddingService.calculateSimilarity",
                    message = UiText.DynamicString("语义相似度计算失败"),
                    processType = "similarity_calculation",
                    reason = "similarity_failed",
                    cause = e,
                ),
            )
        }
    }

    /**
     * 批量计算语义相似度
     *
     * @param queryText 查询文本
     * @param candidates 候选文本列表
     * @return 相似度分数列表，与候选文本列表对应
     */
    suspend fun calculateBatchSimilarity(
        queryText: String,
        candidates: List<String>,
    ): ModernResult<List<Float>> {
        return try {
            // 向量化查询文本
            val queryEmbeddingResult = embedText(queryText)
            if (queryEmbeddingResult is ModernResult.Error) {
                return queryEmbeddingResult.copy()
            }
            val queryEmbedding = (queryEmbeddingResult as ModernResult.Success).data

            // 批量向量化候选文本
            val candidateEmbeddingsResult = embedTexts(candidates)
            if (candidateEmbeddingsResult is ModernResult.Error) {
                return candidateEmbeddingsResult.copy()
            }
            val candidateEmbeddings = (candidateEmbeddingsResult as ModernResult.Success).data

            // 计算相似度
            val similarities = candidateEmbeddings.map { candidateEmbedding ->
                cosineSimilarity(queryEmbedding, candidateEmbedding)
            }

            Timber.d(
                "BgeEmbeddingService: 批量相似度计算完成 query='${queryText.take(
                    20,
                )}...', candidates=${candidates.size}",
            )
            ModernResult.Success(similarities)
        } catch (e: Exception) {
            Timber.e(e, "BgeEmbeddingService: 批量相似度计算失败")
            ModernResult.Error(
                FeatureErrors.CoachError.processingFailed(
                    operationName = "BgeEmbeddingService.calculateBatchSimilarity",
                    message = UiText.DynamicString("批量相似度计算失败"),
                    processType = "batch_similarity",
                    reason = "batch_similarity_failed",
                    cause = e,
                    metadataMap = mapOf(
                        "query_length" to queryText.length,
                        "candidate_count" to candidates.size,
                    ),
                ),
            )
        }
    }

    /**
     * 获取服务性能统计
     */
    fun getPerformanceStats(): BgePerformanceStats {
        val modelInfo = embeddingEngine.getModelInfo()
        return BgePerformanceStats(
            modelName = modelInfo.modelName,
            embeddingDim = modelInfo.embeddingDim,
            maxSequenceLength = modelInfo.maxSequenceLength,
            isInitialized = modelInfo.isInitialized,
            isWarmedUp = isWarmedUp,
        )
    }

    /**
     * 确保模型已预热
     */
    private suspend fun ensureWarmedUp() {
        if (!isWarmedUp) {
            try {
                Timber.d("BgeEmbeddingService: 开始模型预热")
                val startTime = System.currentTimeMillis()

                embeddingEngine.warmUp()

                val warmupTime = System.currentTimeMillis() - startTime
                isWarmedUp = true

                Timber.i("BgeEmbeddingService: 模型预热完成 warmupTime=${warmupTime}ms")
            } catch (e: Exception) {
                Timber.w(e, "BgeEmbeddingService: 模型预热失败，继续使用冷启动")
            }
        }
    }

    /**
     * 计算余弦相似度
     */
    private fun cosineSimilarity(a: FloatArray, b: FloatArray): Float {
        require(a.size == b.size) { "向量维度不匹配: ${a.size} vs ${b.size}" }

        var dotProduct = 0.0f
        var normA = 0.0f
        var normB = 0.0f

        for (i in a.indices) {
            dotProduct += a[i] * b[i]
            normA += a[i] * a[i]
            normB += b[i] * b[i]
        }

        normA = kotlin.math.sqrt(normA)
        normB = kotlin.math.sqrt(normB)

        return if (normA == 0.0f || normB == 0.0f) {
            0.0f
        } else {
            dotProduct / (normA * normB)
        }
    }
}

/**
 * BGE性能统计
 */
data class BgePerformanceStats(
    val modelName: String,
    val embeddingDim: Int,
    val maxSequenceLength: Int,
    val isInitialized: Boolean,
    val isWarmedUp: Boolean,
)
