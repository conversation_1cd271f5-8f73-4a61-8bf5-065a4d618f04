package com.example.gymbro.core.userdata.api

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.userdata.api.model.UnifiedUserData
import com.example.gymbro.core.userdata.api.model.UserDataState
import com.example.gymbro.domain.model.auth.AuthUser
import com.example.gymbro.domain.profile.model.user.UserProfile
import kotlinx.coroutines.flow.Flow

/**
 * UserDataCenter 核心 API 接口
 *
 * 作为用户数据的统一管理中心，为所有消费模块提供一致的用户数据访问接口。
 * 遵循 GymBro 项目的 Clean Architecture 原则和 API 设计规范。
 *
 * 设计原则：
 * - 单一数据源：所有用户数据的唯一真实来源
 * - 响应式：通过 Flow 提供实时数据更新
 * - 类型安全：使用 ModernResult 和 UserDataState 进行错误处理
 * - 模块解耦：各模块通过此接口交互，避免直接依赖
 * - 向后兼容：保持接口稳定，内部实现可演进
 *
 * 主要消费者：
 * - Auth 模块：同步认证数据
 * - Profile 模块：读取和更新用户资料
 * - Coach 模块：获取用户上下文用于 AI 个性化
 * - Workout 模块：获取用户偏好和统计数据
 */
interface UserDataCenterApi {

    /**
     * 观察统一用户数据
     *
     * 为所有消费模块提供响应式的用户数据流。当用户数据发生变化时，
     * 所有订阅者都会收到最新的数据更新。
     *
     * 数据包含：
     * - 认证信息（用户ID、邮箱、验证状态等）
     * - 个人资料（姓名、身体数据、健身偏好等）
     * - 统计数据（活动次数、活跃时间等）
     * - 同步状态和元数据
     *
     * @return Flow<UserDataState<UnifiedUserData>> 用户数据状态流
     *         - Loading: 正在加载数据
     *         - Success: 成功获取数据
     *         - Error: 获取数据失败，可能包含部分数据
     *         - Empty: 用户未登录或数据不存在
     *         - Syncing: 数据正在同步中
     */
    fun observeUserData(): Flow<UserDataState<UnifiedUserData>>

    /**
     * 同步认证数据
     *
     * Auth 模块在用户成功登录或注册后调用此方法，将认证信息同步到
     * UserDataCenter。如果是新用户，会自动创建初始的用户资料。
     *
     * 同步内容：
     * - 用户ID、邮箱、电话号码
     * - 验证状态（邮箱验证、电话验证）
     * - 匿名用户标识
     * - 显示名称（如果在注册时提供）
     *
     * @param authUser 认证用户信息
     * @return ModernResult<Unit> 同步结果
     *         - Success: 同步成功
     *         - Error: 同步失败，包含错误详情
     */
    suspend fun syncAuthData(authUser: AuthUser): ModernResult<Unit>

    /**
     * 更新用户资料
     *
     * Profile 模块在用户修改个人资料时调用此方法，更新用户的详细信息。
     * 更新后会自动触发数据同步，通知所有订阅者。
     *
     * 更新内容：
     * - 个人信息（姓名、性别、生日等）
     * - 身体数据（身高、体重等）
     * - 健身偏好（健身水平、目标、训练日等）
     * - 其他设置（隐私设置、伙伴匹配等）
     *
     * @param profileData 用户资料数据
     * @return ModernResult<Unit> 更新结果
     *         - Success: 更新成功
     *         - Error: 更新失败，包含错误详情
     */
    suspend fun updateProfile(profileData: UserProfile): ModernResult<Unit>

    /**
     * 强制同步所有数据
     *
     * 触发完整的数据同步，从各个数据源重新获取最新数据。
     * 适用于数据修复、强制刷新或解决同步冲突的场景。
     *
     * 同步范围：
     * - 重新获取认证状态
     * - 刷新用户资料数据
     * - 更新统计信息
     * - 重置同步状态
     *
     * @return ModernResult<Unit> 同步结果
     *         - Success: 同步成功
     *         - Error: 同步失败，包含错误详情
     */
    suspend fun forceSync(): ModernResult<Unit>

    /**
     * 获取当前用户数据快照
     *
     * 立即获取当前可用的用户数据，不等待异步更新。
     * 适用于需要立即获取数据的同步场景。
     *
     * 注意：此方法返回的是数据快照，可能不是最新状态。
     * 对于需要实时数据的场景，建议使用 observeUserData()。
     *
     * @return ModernResult<UnifiedUserData?> 当前用户数据
     *         - Success: 成功获取数据，数据可能为 null（用户未登录）
     *         - Error: 获取失败，包含错误详情
     */
    suspend fun getCurrentUserData(): ModernResult<UnifiedUserData?>

    /**
     * 清除用户数据
     *
     * 用户登出时调用，清除所有本地用户数据和缓存。
     * 清除后会发出 Empty 状态通知所有订阅者。
     *
     * 清除内容：
     * - 本地用户数据缓存
     * - 同步状态
     * - 临时数据和设置
     *
     * @return ModernResult<Unit> 清除结果
     *         - Success: 清除成功
     *         - Error: 清除失败，包含错误详情
     */
    suspend fun clearUserData(): ModernResult<Unit>

    /**
     * 检查数据同步状态
     *
     * 获取当前的数据同步状态，用于诊断和监控。
     *
     * @return ModernResult<SyncStatus> 当前同步状态
     *         - Success: 成功获取状态
     *         - Error: 获取失败
     */
    suspend fun getSyncStatus(): ModernResult<com.example.gymbro.core.userdata.api.model.SyncStatus>

    /**
     * 重试失败的同步操作
     *
     * 当数据同步失败时，可以调用此方法重试同步。
     * 会自动识别失败的同步任务并重新执行。
     *
     * @return ModernResult<Unit> 重试结果
     *         - Success: 重试成功
     *         - Error: 重试失败
     */
    suspend fun retrySyncIfNeeded(): ModernResult<Unit>
}
