package com.example.gymbro.data.coach.repository

import android.content.Context
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.coach.repository.SuggestionRepository
import com.example.gymbro.shared.models.coach.SuggestionConfig
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.serialization.json.Json
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SuggestionRepositoryImpl @Inject constructor(
    @ApplicationContext private val context: Context,
    private val json: Json,
) : SuggestionRepository {

    override fun getSuggestionConfig(): Flow<ModernResult<SuggestionConfig>> = flow {
        emit(ModernResult.Loading)
        try {
            val jsonString = context.assets.open("suggestions_config.json")
                .bufferedReader()
                .use { it.readText() }
            val config = json.decodeFromString<SuggestionConfig>(jsonString)
            emit(ModernResult.Success(config))
        } catch (e: Exception) {
            emit(
                ModernResult.Error(
                    ModernDataError(
                        operationName = "getSuggestionConfig",
                        errorType = GlobalErrorType.Data.General,
                        uiMessage = UiText.DynamicString(e.message ?: "Unknown error"),
                        cause = e,
                    ),
                ),
            )
        }
    }
}
