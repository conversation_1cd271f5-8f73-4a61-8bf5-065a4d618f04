package com.example.gymbro.core.ai.prompt.builder

import com.example.gymbro.core.ai.prompt.model.AiContextData
import com.example.gymbro.core.ai.prompt.structure.SystemLayer
import kotlinx.coroutines.flow.Flow

/**
 * 对话轮次数据
 * @since 618重构
 */
data class ConversationTurn(
    val user: String,
    val assistant: String,
)

/**
 * Prompt构建器接口 - 618重构版本
 *
 * 实现智能的Prompt构建，支持：
 * 1. 系统提示词模式切换（4种模式）
 * 2. Memory系统集成（4层记忆）
 * 3. Token预算管理
 * 4. ChatMessage标准格式
 * 5. 统一提示词系统（支持多种模型）🆕
 *
 * @since 618重构 - 简化接口，移除Pipeline
 */
interface PromptBuilder {

    /**
     * 构建ChatMessage列表 - 核心方法
     *
     * @param systemLayer 系统层配置（可选，null时使用当前激活模式）
     * @param userInput 用户输入
     * @param history 对话历史
     * @param model 模型名称（用于统一提示词系统）🆕
     * @return 正确分离角色的消息列表
     *
     * @since 618重构
     */
    suspend fun buildChatMessages(
        systemLayer: SystemLayer? = null,
        userInput: String,
        history: List<ConversationTurn> = emptyList(),
        model: String? = null, // 🆕 新增模型参数
    ): List<CoreChatMessage>

    /**
     * 估算Prompt的token数量
     *
     * @param prompt 要估算的prompt字符串
     * @return 估算的token数量
     */
    fun estimateTokens(prompt: String): Int

    // ==================== 已废弃的方法 ====================

    /**
     * @deprecated 改用 buildChatMessages
     */
    @Deprecated("改用 buildChatMessages", level = DeprecationLevel.ERROR)
    suspend fun buildPrompt(
        context: AiContextData,
        userMessage: String,
        tokenBudget: Int = 3000,
    ): String

    /**
     * @deprecated 增量模式已下线
     */
    @Deprecated("增量模式已下线", level = DeprecationLevel.ERROR)
    suspend fun buildIncrementalPrompt(
        context: AiContextData,
        previousContext: AiContextData?,
        userMessage: String,
    ): String

    /**
     * @deprecated Pipeline已移除
     */
    @Deprecated("Pipeline已移除", level = DeprecationLevel.ERROR)
    fun executeSteps(
        userPrompt: String,
        systemLayer: SystemLayer,
        enableFunctions: Boolean = false,
    ): Flow<PipelineEvent>

    /**
     * @deprecated Pipeline已移除
     */
    @Deprecated("Pipeline已移除", level = DeprecationLevel.ERROR)
    fun executeStepsIntelligently(
        userPrompt: String,
        systemLayer: SystemLayer,
        enableFunctions: Boolean = false,
        forceSimpleMode: Boolean = false,
    ): Flow<PipelineEvent>
}

/**
 * Pipeline事件（仅为兼容性保留）
 * @deprecated Pipeline已在618重构中移除
 */
@Deprecated("Pipeline已移除")
data class PipelineEvent(
    val step: String,
    val description: String,
    val payload: Any? = null,
    val done: Boolean = false,
)
