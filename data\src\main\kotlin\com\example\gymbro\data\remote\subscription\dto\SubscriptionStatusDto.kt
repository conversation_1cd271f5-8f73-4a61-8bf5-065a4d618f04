package com.example.gymbro.data.remote.subscription.dto

import com.google.gson.annotations.SerializedName

/**
 * 订阅状态DTO
 * 定义远程API的订阅状态
 */
enum class SubscriptionStatusDto {
    /**
     * 活跃状态
     */
    @SerializedName("active")
    ACTIVE,

    /**
     * 不活跃状态
     */
    @SerializedName("inactive")
    INACTIVE,

    /**
     * 待处理状态
     */
    @SerializedName("pending")
    PENDING,

    /**
     * 已取消状态
     */
    @SerializedName("cancelled")
    CANCELLED,

    /**
     * 已过期状态
     */
    @SerializedName("expired")
    EXPIRED,

    /**
     * 试用状态
     */
    @SerializedName("trial")
    TRIAL,

    /**
     * 未知状态
     */
    @SerializedName("unknown")
    UNKNOWN,
}
