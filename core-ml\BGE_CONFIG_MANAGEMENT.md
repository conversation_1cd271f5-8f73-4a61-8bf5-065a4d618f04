# BGE模型配置统一管理指南

## 🎯 概述

为了避免在多个地方硬编码BGE模型配置，我们创建了统一的配置管理系统。现在所有BGE相关配置都通过 `BgeModelConfig` 集中管理。

## 📋 配置切换方法

### 1. 修改当前配置

只需要修改 `BgeModelConfig.kt` 中的一行代码：

```kotlin
// 在 BgeModelConfig.kt 中修改这一行
val CURRENT: ModelVariant = ModelVariant.SMALL_128  // 当前使用128序列长度

// 切换到其他配置：
val CURRENT: ModelVariant = ModelVariant.SMALL_256  // 切换到256序列长度
val CURRENT: ModelVariant = ModelVariant.SMALL_512  // 切换到512序列长度
val CURRENT: ModelVariant = ModelVariant.BASE_512   // 切换到基础模型
```

### 2. 可用的配置预设

| 配置名称    | 序列长度 | 嵌入维度 | 模型文件                       | 推荐用途           |
| ----------- | -------- | -------- | ------------------------------ | ------------------ |
| `SMALL_128` | 128      | 512      | `bge-small-cn-v1.5.tflite`     | 移动端、低内存设备 |
| `SMALL_256` | 256      | 512      | `bge-small-cn-v1.5-256.tflite` | 中等长度文本       |
| `SMALL_512` | 512      | 512      | `bge-small-cn-v1.5-512.tflite` | 长文本、高精度     |
| `BASE_512`  | 512      | 768      | `bge-base-cn-v1.5.tflite`      | 最高精度要求       |

## 🔥 重要修复说明

**2025-01-28 关键修复**: 发现BGE-small模型实际输出维度为**512维**，而不是之前配置的384维。已修复所有SMALL系列配置的`embeddingDim`参数。这解决了之前出现的TensorFlow Lite维度不匹配错误。

## 🔧 配置验证

系统会自动验证配置的合理性：

```kotlin
val validation = BgeModelConfig.validateConfig()
if (!validation.isValid) {
    println("配置问题: ${validation.issues.joinToString(", ")}")
}
```

## 📊 调试信息

获取当前配置的详细信息：

```kotlin
println(BgeModelConfig.getDebugInfo())
```

输出示例：
```
=== BGE模型配置信息 ===
当前配置: SMALL_128
模型名称: BGE-small-zh-v1.5-128
模型文件: bge-small-cn-v1.5.tflite
词汇表文件: vocab-cn.txt
最大序列长度: 128
嵌入维度: 384
推荐文本长度: 100
推荐批处理大小: 16
描述: BGE小模型，128序列长度，兼容性最佳
推荐用途: 移动端、低内存设备、快速响应场景
低内存配置: true
========================
```

## 🚀 快速访问配置

```kotlin
// 直接访问当前配置
val maxLength = BgeModelConfig.maxSequenceLength
val embeddingDim = BgeModelConfig.embeddingDim
val modelFile = BgeModelConfig.modelFileName
val vocabFile = BgeModelConfig.vocabFileName
val recommendedTextLength = BgeModelConfig.recommendedTextLength
val recommendedBatchSize = BgeModelConfig.recommendedBatchSize
```

## 📁 受影响的文件

以下文件已更新为使用统一配置：

- ✅ `BgeEmbeddingEngine.kt` - 主要引擎实现
- ✅ `CoreMlModule.kt` - 依赖注入配置
- ✅ `BgeTokenizer.kt` - 分词器配置
- ✅ `EmbeddingEngineFactory.kt` - 工厂类配置
- ✅ `OnnxEmbeddingEngine.kt` - ONNX引擎配置
- ✅ `BgeEmbeddingEngineTest.kt` - 单元测试
- ✅ `BgeRagIntegrationTest.kt` - 集成测试

## ⚠️ 注意事项

1. **模型文件匹配**: 确保 `assets` 目录中有对应的模型文件
2. **兼容性测试**: 切换配置后需要进行完整测试
3. **性能影响**: 不同配置对性能和内存的影响不同
4. **向后兼容**: 旧的硬编码配置已全部移除

## 🔄 配置切换流程

1. 修改 `BgeModelConfig.CURRENT`
2. 确保对应的模型文件存在于 `assets` 目录
3. 运行测试验证配置正确性
4. 部署前进行性能测试

## 🐛 故障排除

### 问题：维度不匹配错误
**解决方案**: 检查模型文件是否与配置匹配

### 问题：模型文件未找到
**解决方案**: 确保 `assets` 目录中有对应的 `.tflite` 文件

### 问题：性能问题
**解决方案**: 尝试切换到更低的序列长度配置

## 📈 性能建议

- **移动端**: 使用 `SMALL_128`
- **平板设备**: 使用 `SMALL_256`
- **高端设备**: 使用 `SMALL_512` 或 `BASE_512`
- **低内存设备**: 强制使用 `SMALL_128`
