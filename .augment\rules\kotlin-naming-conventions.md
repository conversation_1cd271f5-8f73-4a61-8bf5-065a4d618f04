---
type: "always_apply"
---

# Kotlin 命名规范

### 1. 包 (Packages)
**规则**: 全部小写，使用 `.` 作为分隔符，禁止使用下划线 `_`。
```kotlin
// ✅
package com.project.feature.data

// ❌
package com.project.Feature.data_source
```

### 2. 文件 (Files)
**规则**: PascalCase，与文件内核心声明的名称一致。
```kotlin
// ✅ 如果文件内核心是 class UserProfile
UserProfile.kt
```

### 3. 类型声明 (Classes, Interfaces, Enums)
**规则**: 使用 PascalCase。
```kotlin
// ✅
class UserProfile
interface NetworkClient
enum class Status
annotation class Inject
object AppConstants
```

### 4. 函数 (Functions)
**规则**: 使用 camelCase，以小写字母开头。
```kotlin
// ✅
fun calculateScore()
fun fetchUserData()

// ❌
fun CalculateScore()
fun fetch_user_data()
```

### 5. 测试函数 (Test Functions)
**规则**: 使用反引号 `` ` `` 包裹自然语言描述的测试场景。
```kotlin
// ✅
@Test
fun `given invalid credentials, login should fail`() { ... }
```

### 6. 属性与变量 (Properties & Variables)
**规则**: 使用 camelCase，以小写字母开头。
- **Backing Property**: 使用下划线 `_` 前缀。
- **Boolean**: 使用 `is`, `has`, `can`, `should` 等作为前缀。
```kotlin
// ✅
val userName: String
val isLoading: Boolean
private val _items = mutableListOf<String>()
val items: List<String> = _items
```

### 7. 常量 (Constants)
**规则**: `const val` 或 `object` 内的 `val` 使用 `SCREAMING_SNAKE_CASE`。
```kotlin
// ✅
const val MAX_RETRIES = 3
object Config {
    const val API_URL = "..."
}
```

### 8. 架构组件 (Architectural Components)
- **UseCase**: `动词 + 名词/名词短语 + UseCase`。
    - ✅ `class GetUserProfileUseCase`
- **Repository**: 接口为 `名词 + Repository`，实现类为 `...Impl`。
    - ✅ `interface UserRepository`
    - ✅ `class UserRepositoryImpl`
- **ViewModel**: `功能模块名 + ViewModel`。
    - ✅ `class UserProfileViewModel`

### 9. Jetpack Compose
- **Composable 函数**: 使用 PascalCase。
    - ✅ `@Composable fun UserProfileCard() { ... }`
- **Preview 函数**:
    1. 必须为 `private`。
    2. 命名为 `被预览组件名 + Preview`。
    3. 必须使用项目级统一的自定义预览注解 (`@GymBroPreview`)。
    ```kotlin
    // ✅
    @GymBroPreview
    @Composable
    private fun UserProfileCardPreview() {
        GymBroTheme {
            UserProfileCard()
        }
    }
    ```
