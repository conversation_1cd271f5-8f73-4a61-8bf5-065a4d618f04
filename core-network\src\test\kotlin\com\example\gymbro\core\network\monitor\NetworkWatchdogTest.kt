package com.example.gymbro.core.network.monitor

import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.test.*
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * NetworkWatchdog单元测试 - D2阶段
 *
 * 测试网络监控狗的核心功能：
 * - 事件流处理
 * - 防抖机制
 * - 状态转换逻辑
 * - 生命周期管理
 */
@OptIn(ExperimentalCoroutinesApi::class)
class NetworkWatchdogTest {

    private lateinit var testScope: TestScope
    private lateinit var testDispatcher: TestDispatcher
    private lateinit var mockNetworkMonitor: MockNetworkMonitor
    private lateinit var networkWatchdog: NetworkWatchdog

    @Before
    fun setup() {
        testDispatcher = StandardTestDispatcher()
        testScope = TestScope(testDispatcher)
        mockNetworkMonitor = MockNetworkMonitor()

        networkWatchdog = NetworkWatchdogImpl(
            networkMonitor = mockNetworkMonitor,
            scope = testScope,
            debounceTimeMs = 100L, // 缩短防抖时间以便测试
        )
    }

    @After
    fun tearDown() {
        networkWatchdog.stopWatching()
    }

    @Test
    fun `startWatching should begin monitoring network events`() = testScope.runTest {
        // Given
        assertNull(networkWatchdog.currentEvent)

        // When
        networkWatchdog.startWatching()
        advanceTimeBy(200L) // 等待防抖

        // Then
        assertTrue(mockNetworkMonitor.isMonitoring)
    }

    @Test
    fun `network available should emit Connected event`() = testScope.runTest {
        // Given
        networkWatchdog.startWatching()
        val events = mutableListOf<NetworkEvent?>()
        val job = launch {
            networkWatchdog.networkEvents.collect { events.add(it) }
        }

        // When
        mockNetworkMonitor.emitNetworkState(NetworkState.Available(NetworkType.WIFI))
        advanceTimeBy(200L) // 等待防抖

        // Then
        assertEquals(1, events.size)
        assertTrue(events[0] is NetworkEvent.Connected)
        assertEquals(NetworkType.WIFI, (events[0] as NetworkEvent.Connected).networkType)

        job.cancel()
    }

    @Test
    fun `network lost should emit Lost event`() = testScope.runTest {
        // Given
        networkWatchdog.startWatching()
        val events = mutableListOf<NetworkEvent?>()
        val job = launch {
            networkWatchdog.networkEvents.collect { events.add(it) }
        }

        // 先建立连接
        mockNetworkMonitor.emitNetworkState(NetworkState.Available(NetworkType.WIFI))
        advanceTimeBy(200L)

        // When
        mockNetworkMonitor.emitNetworkState(NetworkState.Lost)
        advanceTimeBy(200L)

        // Then
        assertEquals(2, events.size)
        assertTrue(events[0] is NetworkEvent.Connected)
        assertTrue(events[1] is NetworkEvent.Lost)

        job.cancel()
    }

    @Test
    fun `network type change should emit Connected event`() = testScope.runTest {
        // Given
        networkWatchdog.startWatching()
        val events = mutableListOf<NetworkEvent?>()
        val job = launch {
            networkWatchdog.networkEvents.collect { events.add(it) }
        }

        // 先建立WiFi连接
        mockNetworkMonitor.emitNetworkState(NetworkState.Available(NetworkType.WIFI))
        advanceTimeBy(200L)

        // When - 切换到蜂窝网络
        mockNetworkMonitor.emitNetworkState(NetworkState.Available(NetworkType.CELLULAR))
        advanceTimeBy(200L)

        // Then
        assertEquals(2, events.size)
        assertTrue(events[0] is NetworkEvent.Connected)
        assertEquals(NetworkType.WIFI, (events[0] as NetworkEvent.Connected).networkType)
        assertTrue(events[1] is NetworkEvent.Connected)
        assertEquals(NetworkType.CELLULAR, (events[1] as NetworkEvent.Connected).networkType)

        job.cancel()
    }

    @Test
    fun `debounce should prevent rapid state changes`() = testScope.runTest {
        // Given
        networkWatchdog.startWatching()
        val events = mutableListOf<NetworkEvent?>()
        val job = launch {
            networkWatchdog.networkEvents.collect { events.add(it) }
        }

        // When - 快速发送多个状态变化
        mockNetworkMonitor.emitNetworkState(NetworkState.Available(NetworkType.WIFI))
        advanceTimeBy(50L) // 小于防抖时间
        mockNetworkMonitor.emitNetworkState(NetworkState.Connecting)
        advanceTimeBy(50L)
        mockNetworkMonitor.emitNetworkState(NetworkState.Available(NetworkType.CELLULAR))
        advanceTimeBy(200L) // 等待防抖完成

        // Then - 只应该收到最后一个状态的事件
        assertEquals(1, events.size)
        assertTrue(events[0] is NetworkEvent.Connected)
        assertEquals(NetworkType.CELLULAR, (events[0] as NetworkEvent.Connected).networkType)

        job.cancel()
    }

    @Test
    fun `pauseWatching should stop event processing`() = testScope.runTest {
        // Given
        networkWatchdog.startWatching()
        val events = mutableListOf<NetworkEvent?>()
        val job = launch {
            networkWatchdog.networkEvents.collect { events.add(it) }
        }

        // When
        networkWatchdog.pauseWatching()
        mockNetworkMonitor.emitNetworkState(NetworkState.Available(NetworkType.WIFI))
        advanceTimeBy(200L)

        // Then - 暂停期间不应该收到事件
        assertEquals(0, events.size)

        job.cancel()
    }

    @Test
    fun `resumeWatching should restart event processing`() = testScope.runTest {
        // Given
        networkWatchdog.startWatching()
        networkWatchdog.pauseWatching()

        val events = mutableListOf<NetworkEvent?>()
        val job = launch {
            networkWatchdog.networkEvents.collect { events.add(it) }
        }

        // When
        networkWatchdog.resumeWatching()
        mockNetworkMonitor.emitNetworkState(NetworkState.Available(NetworkType.WIFI))
        advanceTimeBy(200L)

        // Then
        assertEquals(1, events.size)
        assertTrue(events[0] is NetworkEvent.Connected)

        job.cancel()
    }

    @Test
    fun `stopWatching should clean up resources`() = testScope.runTest {
        // Given
        networkWatchdog.startWatching()
        assertTrue(mockNetworkMonitor.isMonitoring)

        // When
        networkWatchdog.stopWatching()

        // Then
        assertFalse(mockNetworkMonitor.isMonitoring)
        assertNull(networkWatchdog.currentEvent)
    }
}

/**
 * Mock网络监控器，用于测试
 */
private class MockNetworkMonitor : NetworkMonitor {
    private val _networkState = MutableStateFlow<NetworkState>(NetworkState.Unknown)
    override val networkState: StateFlow<NetworkState> = _networkState.asStateFlow()

    override val isOnline: Boolean
        get() = networkState.value is NetworkState.Available

    var isMonitoring = false
        private set

    override fun startMonitoring() {
        isMonitoring = true
    }

    override fun stopMonitoring() {
        isMonitoring = false
        _networkState.value = NetworkState.Unknown
    }

    fun emitNetworkState(state: NetworkState) {
        _networkState.value = state
    }
}
