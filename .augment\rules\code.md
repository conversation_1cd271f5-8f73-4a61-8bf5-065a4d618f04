---
alwaysApply: true
---

# GymBro Coding Standards & Conventions

## Package Structure & Naming

### Package Naming
- All lowercase with dots: `com.example.gymbro.features.profile`
- No underscores or hyphens
- Follow domain-driven structure

### File Naming
- PascalCase matching primary class: `UserProfileViewModel.kt`
- Contract files: `{Feature}Contract.kt`
- Reducer files: `{Feature}Reducer.kt`
- EffectHandler files: `{Feature}EffectHandler.kt`
- API interfaces: `{Feature}Navigatable.kt`, `{Feature}FeatureApi.kt`

### Class & Interface Naming
- Classes: PascalCase (`UserProfile`, `AiCoachViewModel`)
- Interfaces: PascalCase (`ProfileRepository`, `NetworkClient`)
- Enums: PascalCase (`Status`, `LoadState`)
- Objects: PascalCase (`AppConstants`, `ProfileRoutes`)

### Function Naming
- camelCase starting with lowercase (`calculateScore`, `fetchUserData`)
- UseCase invoke: `suspend operator fun invoke(params: Params): Result`
- Boolean functions: `isLoading`, `hasPermission`, `canExecute`

### Variable & Property Naming
- camelCase: `userName`, `isLoading`, `currentState`
- Backing properties: `_items` (private), `items` (public)
- Constants: `SCREAMING_SNAKE_CASE` (`MAX_RETRIES`, `API_URL`)

## Architecture Component Naming

### UseCase Pattern
```kotlin
// Naming: {Verb}{Noun}UseCase
class GetUserProfileUseCase @Inject constructor(
    private val repository: UserRepository,
    @IoDispatcher private val dispatcher: CoroutineDispatcher
) {
    suspend operator fun invoke(userId: String): ModernResult<UserProfile, DomainError>
}
```

### Repository Pattern
```kotlin
// Interface: {Domain}Repository
interface UserRepository {
    suspend fun getUser(id: String): ModernResult<User, ModernDataError>
}

// Implementation: {Domain}RepositoryImpl
class UserRepositoryImpl @Inject constructor(
    private val localDataSource: UserLocalDataSource,
    private val remoteDataSource: UserRemoteDataSource
) : UserRepository
```

### MVI Components
```kotlin
// Contract: {Feature}Contract
object ProfileContract {
    data class State(...)
    sealed interface Intent
    sealed interface Effect
}

// Reducer: {Feature}Reducer
class ProfileReducer @Inject constructor() {
    fun reduce(intent: Intent, state: State): ReduceResult<State, Effect>
}

// EffectHandler: {Feature}EffectHandler
class ProfileEffectHandler @Inject constructor() {
    suspend fun handle(intent: Intent, state: State): Flow<Effect>
}

// ViewModel: {Feature}ViewModel
class ProfileViewModel @Inject constructor() : BaseMviViewModel<Intent, State, Effect>
```

## Import Organization

### Import Order
1. Android framework imports
2. Third-party library imports (alphabetical)
3. Project imports (alphabetical)
4. Relative imports (if any)

### Import Rules
- Use absolute imports: `com.example.gymbro.core.error.ModernResult`
- No wildcard imports except for standard library
- Group related imports with blank lines

### Example Import Block
```kotlin
import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope

import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.launch
import timber.log.Timber

import com.example.gymbro.core.error.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.profile.repository.ProfileRepository
import javax.inject.Inject
```

## Visibility & Access Control

### Visibility Rules
- Implementation classes: `internal` (within module)
- Public APIs: `public` interfaces only
- Private helpers: `private` within class
- Protected: Only for inheritance

### API Design
```kotlin
// Public API (in api/ package)
interface ProfileFeatureApi {
    fun observeUserContext(): Flow<UserAiContext>
}

// Internal implementation (in internal/ package)
internal class ProfileFeatureApiImpl @Inject constructor() : ProfileFeatureApi {
    override fun observeUserContext(): Flow<UserAiContext> = ...
}
```

## Error Handling Standards

### Result Types
- Use `ModernResult<T, E>` for all operations
- Data layer: `ModernResult<Data, ModernDataError>`
- Domain layer: `ModernResult<Entity, DomainError>`
- UI layer: Display errors using `UiText`

### Error Handling Pattern
```kotlin
// Repository level
suspend fun getUser(id: String): ModernResult<User, ModernDataError> = safeCatch {
    val dto = apiService.getUser(id)
    mapper.toDomain(dto)
}

// UseCase level
suspend operator fun invoke(id: String): ModernResult<User, DomainError> =
    withContext(dispatcher) {
        repository.getUser(id).mapError { DomainError.UserNotFound }
    }

// ViewModel level
private fun handleResult(result: ModernResult<User, DomainError>) {
    when (result) {
        is ModernResult.Success -> updateState { copy(user = result.data) }
        is ModernResult.Error -> showError(result.error.toUiText())
    }
}
```

## Logging Standards

### Logging Usage
- Global: Use `Timber` in implementation classes
- Domain: Use `Logger` interface (injected)
- Debug logs: `Timber.d("Message with %s", param)`
- Error logs: `Timber.e(throwable, "Error message")`

### Logging Pattern
```kotlin
class FeatureRepositoryImpl @Inject constructor(
    private val logger: Logger
) : FeatureRepository {

    override suspend fun getData(): ModernResult<Data, ModernDataError> {
        logger.d("Fetching data from repository")
        return safeCatch {
            val result = dataSource.fetch()
            logger.d("Successfully fetched ${result.size} items")
            result
        }.onError { error ->
            logger.e("Failed to fetch data: ${error.message}")
        }
    }
}
```

## Compose Standards

### Composable Naming
- PascalCase: `@Composable fun UserProfileCard()`
- Preview functions: `private` + `{Component}Preview`
- Use project preview annotation: `@GymBroPreview`

### Composable Structure
```kotlin
@Composable
fun UserProfileCard(
    user: UserProfile,
    onEditClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    // Implementation
}

@GymBroPreview
@Composable
private fun UserProfileCardPreview() {
    GymBroTheme {
        UserProfileCard(
            user = UserProfile.sample(),
            onEditClick = {}
        )
    }
}
```

## Dependency Injection Standards

### Hilt Annotations
- ViewModels: `@HiltViewModel`
- Modules: `@Module @InstallIn(SingletonComponent::class)`
- Bindings: `@Binds` for interfaces, `@Provides` for concrete types
- Qualifiers: `@IoDispatcher`, `@MainDispatcher`

### DI Module Organization
```kotlin
@Module
@InstallIn(SingletonComponent::class)
abstract class FeatureRepositoryModule {

    @Binds
    @Singleton
    abstract fun bindFeatureRepository(
        impl: FeatureRepositoryImpl
    ): FeatureRepository
}
```

### Scope Usage
- `@Singleton`: Repositories, network clients, databases
- `@ViewModelScoped`: ViewModel dependencies
- No scope: UseCase (lightweight, stateless)

## Testing Standards

### Test Naming
- Test classes: `{ClassUnderTest}Test`
- Test functions: `` `given condition, when action, then result` ``
- Test packages: Mirror main package structure in test/

### Test Structure
```kotlin
class UserRepositoryTest {

    @Test
    fun `given valid user id, when getting user, then returns success`() {
        // Given
        val userId = "123"
        val expectedUser = User.sample()

        // When
        val result = repository.getUser(userId)

        // Then
        assertThat(result).isEqualTo(ModernResult.Success(expectedUser))
    }
}
```

## Documentation Standards

### KDoc Requirements
- All public APIs must have KDoc
- Include `@param` and `@return` for functions
- Use `@see` for related components
- Include usage examples for complex APIs

### KDoc Example
```kotlin
/**
 * Retrieves user profile data with caching support.
 *
 * This function implements a cache-first strategy, falling back to network
 * requests when local data is unavailable or stale.
 *
 * @param userId The unique identifier for the user
 * @param forceRefresh Whether to bypass cache and fetch fresh data
 * @return A [ModernResult] containing the user profile or an error
 *
 * @see UserProfile
 * @see ModernResult
 */
suspend fun getUserProfile(
    userId: String,
    forceRefresh: Boolean = false
): ModernResult<UserProfile, ModernDataError>
```

## Performance Standards

### Coroutine Usage
- Use appropriate dispatchers: `@IoDispatcher` for I/O, `@MainDispatcher` for UI
- Prefer `Flow` over `LiveData` for reactive streams
- Use `viewModelScope` for ViewModel coroutines
- Cancel jobs properly in cleanup

### Memory Management
- Avoid memory leaks in ViewModels
- Use `WeakReference` for callbacks when needed
- Clear resources in `onCleared()`
- Prefer immutable data structures

### State Management
- Use `@Immutable` and `@Stable` annotations
- Minimize recomposition with `derivedStateOf`
- Use `remember` for expensive calculations
- Avoid unnecessary state hoisting
