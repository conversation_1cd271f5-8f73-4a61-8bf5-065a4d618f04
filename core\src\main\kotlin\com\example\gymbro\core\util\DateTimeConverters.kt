package com.example.gymbro.core.util

import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.plus
import kotlinx.datetime.toInstant
import kotlinx.datetime.toJavaInstant
import kotlinx.datetime.toLocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*
import kotlin.time.Duration.Companion.days

/**
 * 日期时间转换工具
 *
 * 提供时间戳、日期和Instant之间的双向转换工具方法
 */
object DateTimeConverters {
    /**
     * 默认时区 - 系统当前时区
     */
    private val defaultTimeZone = TimeZone.currentSystemDefault()

    /**
     * 将时间戳转换为Instant
     */
    fun Long?.toInstant(): Instant? {
        return this?.let { Instant.fromEpochMilliseconds(it) }
    }

    /**
     * 将Instant转换为时间戳
     */
    fun Instant?.toLong(): Long? {
        return this?.toEpochMilliseconds()
    }

    /**
     * 获取当前时间的Instant
     */
    fun now(): Instant = Clock.System.now()

    /**
     * 将Date转换为Instant
     */
    fun dateToInstant(value: Date?): Instant? {
        return value?.let { Instant.fromEpochMilliseconds(it.time) }
    }

    /**
     * 将Instant转换为Date
     */
    fun instantToDate(value: Instant?): Date? {
        return value?.let { Date(it.toEpochMilliseconds()) }
    }

    /**
     * 将Long时间戳转换为Date
     */
    fun longToDate(value: Long?): Date? {
        return value?.let { Date(it) }
    }

    /**
     * 将Date转换为Long时间戳
     */
    fun dateToEpochMillis(value: Date?): Long? {
        return value?.time
    }

    /**
     * 获取当前时间的Long时间戳
     */
    fun getCurrentTimeMillis(): Long {
        return System.currentTimeMillis()
    }

    /**
     * 判断时间是否在给定范围内
     */
    fun isInstantBetween(value: Instant?, start: Instant?, end: Instant?): Boolean {
        if (value == null || start == null || end == null) return false
        return value >= start && value <= end
    }

    /**
     * 添加天数
     */
    fun addDaysToInstant(value: Instant?, days: Int): Instant? {
        if (value == null) return null
        return value.plus(days.days)
    }

    /**
     * 格式化显示（用于调试）
     */
    fun Instant.format(pattern: String = "yyyy-MM-dd HH:mm:ss"): String {
        val formatter = DateTimeFormatter.ofPattern(pattern)
            .withZone(ZoneId.systemDefault())
        return formatter.format(this.toJavaInstant())
    }

    /**
     * 将Instant转换为Long时间戳（毫秒）
     *
     * @param instant Instant对象，可为null
     * @return Long时间戳，如果输入为null则返回null
     */
    fun instantToLong(instant: Instant?): Long? {
        return instant?.toEpochMilliseconds()
    }

    /**
     * 将Long时间戳（毫秒）转换为Instant
     *
     * @param timestamp Long时间戳，可为null
     * @return Instant对象，如果输入为null则返回null
     */
    fun longToInstant(timestamp: Long?): Instant? {
        return timestamp?.let { Instant.fromEpochMilliseconds(it) }
    }

    /**
     * 获取当前时间的Long时间戳（毫秒）
     *
     * @return 当前时间的Long时间戳
     */
    fun nowMillis(): Long {
        return Clock.System.now().toEpochMilliseconds()
    }

    /**
     * 将Long转换为非空Instant，如果输入为null则返回当前时间
     * @param timestamp 时间戳(毫秒)，可为null
     * @return 非空的Instant
     */
    fun longToInstantOrNow(timestamp: Long?): Instant {
        return timestamp?.let { Instant.fromEpochMilliseconds(it) } ?: Clock.System.now()
    }

    /**
     * 将Instant转换为非空Long，如果输入为null则返回当前时间戳
     * @param instant Instant对象，可为null
     * @return 非空的时间戳(毫秒)
     */
    fun instantToLongOrNow(instant: Instant?): Long {
        return instant?.toEpochMilliseconds() ?: Clock.System.now().toEpochMilliseconds()
    }

    /**
     * 将Instant转换为LocalDateTime
     *
     * @param instant Instant对象，可为null
     * @param timeZone 时区，默认为系统当前时区
     * @return LocalDateTime对象，如果输入为null则返回null
     */
    fun instantToLocalDateTime(instant: Instant?, timeZone: TimeZone = defaultTimeZone): LocalDateTime? {
        return instant?.toLocalDateTime(timeZone)
    }

    /**
     * 将LocalDateTime转换为Instant
     *
     * @param localDateTime LocalDateTime对象，可为null
     * @param timeZone 时区，默认为系统当前时区
     * @return Instant对象，如果输入为null则返回null
     */
    fun localDateTimeToInstant(
        localDateTime: LocalDateTime?,
        timeZone: TimeZone = defaultTimeZone,
    ): Instant? {
        return localDateTime?.toInstant(timeZone)
    }

    /**
     * 将Long时间戳转换为LocalDateTime
     *
     * @param timestamp Long时间戳，可为null
     * @param timeZone 时区，默认为系统当前时区
     * @return LocalDateTime对象，如果输入为null则返回null
     */
    fun longToLocalDateTime(timestamp: Long?, timeZone: TimeZone = defaultTimeZone): LocalDateTime? {
        return timestamp?.let { Instant.fromEpochMilliseconds(it).toLocalDateTime(timeZone) }
    }

    /**
     * 将LocalDateTime转换为Long时间戳
     *
     * @param localDateTime LocalDateTime对象，可为null
     * @param timeZone 时区，默认为系统当前时区
     * @return Long时间戳，如果输入为null则返回null
     */
    fun localDateTimeToLong(localDateTime: LocalDateTime?, timeZone: TimeZone = defaultTimeZone): Long? {
        return localDateTime?.toInstant(timeZone)?.toEpochMilliseconds()
    }
}

/**
 * Long转Instant扩展函数
 */
fun Long?.toInstant(): Instant? {
    return this?.let { Instant.fromEpochMilliseconds(it) }
}

/**
 * Instant转Long扩展函数
 */
fun Instant?.toEpochMillis(): Long? {
    return this?.toEpochMilliseconds()
}

/**
 * Long转Date扩展函数
 */
fun Long?.toDate(): Date? {
    return this?.let { Date(it) }
}

/**
 * Date转Instant扩展函数
 */
fun Date?.toInstant(): Instant? {
    return this?.let { Instant.fromEpochMilliseconds(it.time) }
}

/**
 * Date转Long扩展函数
 */
fun Date?.toEpochMillis(): Long? {
    return this?.time
}

/**
 * Instant转Date扩展函数
 */
fun Instant?.toDate(): Date? {
    return this?.let { Date(it.toEpochMilliseconds()) }
}

/**
 * Instant添加天数扩展函数
 */
fun Instant?.plusDays(days: Int): Instant? {
    if (this == null) return null
    return this.plus(days.days)
}

/**
 * Instant范围判断扩展函数
 */
fun Instant?.isBetween(start: Instant?, end: Instant?): Boolean {
    if (this == null || start == null || end == null) return false
    return this >= start && this <= end
}
