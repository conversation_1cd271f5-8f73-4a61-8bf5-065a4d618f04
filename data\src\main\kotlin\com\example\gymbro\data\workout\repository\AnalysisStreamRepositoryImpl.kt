package com.example.gymbro.data.workout.repository

import com.example.gymbro.core.ai.prompt.builder.CoreChatMessage
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.data.ai.service.AiApiService
import com.example.gymbro.domain.workout.repository.AnalysisStreamRepository
import com.example.gymbro.shared.models.ai.ChatMessage
import com.example.gymbro.shared.models.ai.ChatRequest
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.delay
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 训练分析流式Repository实现
 *
 * 🔥 【事件总线架构】升级版本：支持事件总线架构
 */
@Singleton
class AnalysisStreamRepositoryImpl @Inject constructor(
    private val aiApiService: AiApiService,
    private val llmStreamClient: com.example.gymbro.core.network.ws.LlmStreamClient,
    private val logger: Logger
) : AnalysisStreamRepository {

    override fun getStreamingResponse(
        analysisId: String,
        messages: List<CoreChatMessage>,
        model: String
    ): Flow<String> = flow {
        try {
            logger.d("🚀 开始AI分析请求: analysisId=$analysisId, model=$model")

            // 1. 转换消息格式（参考coach的转换模式）
            val chatMessages = messages.map { coreMessage ->
                ChatMessage(
                    role = coreMessage.role,
                    content = coreMessage.content
                )
            }

            // 2. 构建API请求
            val chatRequest = ChatRequest(
                model = model,
                messages = chatMessages,
                maxTokens = 1500, // 控制成本
                temperature = 0.3, // 保持分析一致性
                stream = false // 简化实现，使用非流式
            )

            // 3. 调用AI API
            val response = aiApiService.chatCompletion(chatRequest)

            if (!response.isSuccessful) {
                val error = "AI API调用失败: ${response.code()}"
                logger.e(error)
                throw Exception(error)
            }

            val chatResponse = response.body()
                ?: throw Exception("AI响应为空")

            // 4. 获取AI响应内容
            val aiContent = chatResponse.choices.firstOrNull()?.message?.content
                ?: throw Exception("AI响应内容为空")

            // 5. 模拟流式响应（按句子发送，参考coach的token发送模式）
            val sentences = aiContent.split(Regex("(?<=[。！？\\n])")).filter { it.isNotBlank() }

            for (sentence in sentences) {
                emit(sentence)
                delay(100) // 模拟网络延迟，提供流式体验
            }

            // 6. 记录Token使用信息
            val tokenUsage = chatResponse.usage
            logger.d("AI分析完成: analysisId=$analysisId, tokens=${tokenUsage?.totalTokens ?: 0}")

        } catch (e: Exception) {
            logger.e(e, "AI分析流式响应失败: analysisId=$analysisId")
            throw e
        }
    }

    /**
     * 🔥 【事件总线架构】新的流式响应方法 - 直接发布到TokenBus
     */
    override suspend fun getStreamingResponseWithMessageId(
        messageId: String,
        messages: List<CoreChatMessage>,
        model: String
    ) {
        try {
            logger.d("🚀 开始AI分析请求 (事件总线版本): messageId=$messageId, model=$model")

            // 转换消息格式
            val chatMessages = messages.map { coreMessage ->
                ChatMessage(
                    role = coreMessage.role,
                    content = coreMessage.content
                )
            }

            // 构建API请求
            val chatRequest = ChatRequest(
                model = model,
                messages = chatMessages,
                maxTokens = 1500,
                temperature = 0.3,
                stream = true // 🔥 使用流式请求
            )

            // 🔥 【事件总线架构】调用LlmStreamClient，Token会自动发布到TokenBus
            llmStreamClient.streamChatWithMessageId(chatRequest, messageId)

            logger.d("✅ AI分析请求已启动，Token将通过事件总线路由: messageId=$messageId")

        } catch (e: Exception) {
            logger.e(e, "AI分析流式响应失败 (事件总线版本): messageId=$messageId")
            throw e
        }
    }
}
