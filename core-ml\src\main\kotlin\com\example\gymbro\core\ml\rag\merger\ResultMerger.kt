package com.example.gymbro.core.ml.rag.merger

import com.example.gymbro.core.ml.rag.scoring.ScoringStrategy
import com.example.gymbro.core.ml.rag.scoring.ScoringStrategyManager
import javax.inject.Inject

/**
 * RAG搜索结果合并器
 *
 * 基于 history_code_review.md 建议，将结果合并逻辑从RagContextRetrievalUseCase中提取，
 * 专门负责向量搜索和关键词搜索结果的合并、去重和排序。
 *
 * 🚀 增强：集成ScoringStrategyManager，支持动态策略切换和A/B测试。
 */
class ResultMerger @Inject constructor(
    private val scoringStrategyManager: ScoringStrategyManager,
) {

    /**
     * 合并并排序搜索结果
     *
     * @param vectorResults 向量搜索结果
     * @param keywordResults 关键词搜索结果
     * @param hybridWeight 混合权重
     * @param currentSessionId 当前会话ID
     * @param userId 用户ID（用于A/B测试策略选择）
     * @param minScore 最小分数阈值
     * @return 排序后的合并结果
     */
    fun mergeAndRank(
        vectorResults: List<VectorSearchCandidate>,
        keywordResults: List<KeywordSearchCandidate>,
        hybridWeight: Float,
        currentSessionId: String?,
        userId: String? = null,
        minScore: Float = 0.1f,
    ): List<RankedSearchResult> {
        // 🚀 获取当前生效的评分策略（支持A/B测试）
        val scoringStrategy = scoringStrategyManager.getCurrentStrategy(userId)

        val resultMap = mutableMapOf<Long, RankedSearchResult>()

        // 处理向量搜索结果
        processVectorResults(vectorResults, resultMap, hybridWeight, currentSessionId, scoringStrategy)

        // 处理关键词搜索结果
        processKeywordResults(keywordResults, resultMap, hybridWeight, currentSessionId, scoringStrategy)

        // 过滤低分结果并排序
        return resultMap.values
            .filter { it.combinedScore > minScore }
            .sortedByDescending { it.combinedScore }
    }

    /**
     * 处理向量搜索结果
     */
    private fun processVectorResults(
        vectorResults: List<VectorSearchCandidate>,
        resultMap: MutableMap<Long, RankedSearchResult>,
        hybridWeight: Float,
        currentSessionId: String?,
        scoringStrategy: ScoringStrategy,
    ) {
        vectorResults.forEach { vector ->
            val existing = resultMap[vector.messageId]
            if (existing != null) {
                // 合并现有结果的分数
                resultMap[vector.messageId] = existing.copy(
                    vectorScore = vector.similarity,
                    combinedScore = scoringStrategy.calculateCombinedScore(
                        vectorScore = vector.similarity,
                        keywordScore = existing.keywordScore,
                        hybridWeight = hybridWeight,
                        timestamp = vector.timestamp,
                        isCurrentSession = vector.sessionId == currentSessionId,
                    ),
                )
            } else {
                // 创建新的结果条目
                resultMap[vector.messageId] = createRankedResult(
                    messageId = vector.messageId,
                    content = vector.content,
                    sessionId = vector.sessionId,
                    timestamp = vector.timestamp,
                    role = vector.role,
                    vectorScore = vector.similarity,
                    keywordScore = 0.0f,
                    hybridWeight = hybridWeight,
                    currentSessionId = currentSessionId,
                    scoringStrategy = scoringStrategy,
                )
            }
        }
    }

    /**
     * 处理关键词搜索结果
     */
    private fun processKeywordResults(
        keywordResults: List<KeywordSearchCandidate>,
        resultMap: MutableMap<Long, RankedSearchResult>,
        hybridWeight: Float,
        currentSessionId: String?,
        scoringStrategy: ScoringStrategy,
    ) {
        keywordResults.forEach { keyword ->
            val existing = resultMap[keyword.messageId]
            if (existing != null) {
                // 合并现有结果的分数
                resultMap[keyword.messageId] = existing.copy(
                    keywordScore = keyword.score,
                    combinedScore = scoringStrategy.calculateCombinedScore(
                        vectorScore = existing.vectorScore,
                        keywordScore = keyword.score,
                        hybridWeight = hybridWeight,
                        timestamp = keyword.timestamp,
                        isCurrentSession = keyword.sessionId == currentSessionId,
                    ),
                )
            } else {
                // 创建新的结果条目
                resultMap[keyword.messageId] = createRankedResult(
                    messageId = keyword.messageId,
                    content = keyword.content,
                    sessionId = keyword.sessionId,
                    timestamp = keyword.timestamp,
                    role = keyword.role,
                    vectorScore = 0.0f,
                    keywordScore = keyword.score,
                    hybridWeight = hybridWeight,
                    currentSessionId = currentSessionId,
                    scoringStrategy = scoringStrategy,
                )
            }
        }
    }

    /**
     * 创建排序结果
     */
    private fun createRankedResult(
        messageId: Long,
        content: String,
        sessionId: String,
        timestamp: Long,
        role: String,
        vectorScore: Float,
        keywordScore: Float,
        hybridWeight: Float,
        currentSessionId: String?,
        scoringStrategy: ScoringStrategy,
    ): RankedSearchResult {
        val combinedScore = scoringStrategy.calculateCombinedScore(
            vectorScore = vectorScore,
            keywordScore = keywordScore,
            hybridWeight = hybridWeight,
            timestamp = timestamp,
            isCurrentSession = sessionId == currentSessionId,
        )

        return RankedSearchResult(
            messageId = messageId,
            content = content,
            sessionId = sessionId,
            timestamp = timestamp,
            role = role,
            vectorScore = vectorScore,
            keywordScore = keywordScore,
            combinedScore = combinedScore,
        )
    }
}

// 搜索候选结果数据类
data class VectorSearchCandidate(
    val messageId: Long,
    val similarity: Float,
    val content: String,
    val sessionId: String,
    val timestamp: Long,
    val role: String,
)

data class KeywordSearchCandidate(
    val messageId: Long,
    val score: Float,
    val content: String,
    val sessionId: String,
    val timestamp: Long,
    val role: String,
)

data class RankedSearchResult(
    val messageId: Long,
    val content: String,
    val sessionId: String,
    val timestamp: Long,
    val role: String,
    val vectorScore: Float,
    val keywordScore: Float,
    val combinedScore: Float,
)
