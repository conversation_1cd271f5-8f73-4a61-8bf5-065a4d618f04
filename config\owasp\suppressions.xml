<?xml version="1.0" encoding="UTF-8"?>
<suppressions xmlns="https://jeremylong.github.io/DependencyCheck/dependency-suppression.1.3.xsd">

    <!--
    GymBro项目OWASP依赖安全扫描抑制规则

    这个文件用于抑制已知的误报或已接受的安全风险。
    每个抑制规则都应该包含详细的注释说明原因。
    -->

    <!-- Android相关误报抑制 -->
    <suppress>
        <notes><![CDATA[
        Android SDK相关组件的误报，这些是Google官方维护的组件，安全性由Google保证
        ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.android\..*</packageUrl>
        <cve>CVE-2018-10237</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
        AndroidX库的误报，这些是Google官方维护的现代Android支持库
        ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\..*</packageUrl>
        <cve>CVE-2018-10237</cve>
    </suppress>

    <!-- Kotlin相关误报抑制 -->
    <suppress>
        <notes><![CDATA[
        Kotlin标准库的误报，Kotlin是由JetBrains官方维护的现代语言
        ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.jetbrains\.kotlin.*</packageUrl>
        <cve>CVE-2020-29582</cve>
    </suppress>

    <!-- Firebase相关误报抑制 -->
    <suppress>
        <notes><![CDATA[
        Firebase SDK的误报，这些是Google官方维护的云服务SDK
        ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.google\.firebase.*</packageUrl>
        <cve>CVE-2021-22119</cve>
    </suppress>

    <!-- 测试库相关误报抑制 -->
    <suppress>
        <notes><![CDATA[
        JUnit测试框架的误报，只在测试环境使用，不影响生产安全
        ]]></notes>
        <packageUrl regex="true">^pkg:maven/junit.*</packageUrl>
        <cve>CVE-2020-15250</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
        MockK测试库的误报，只在测试环境使用，不影响生产安全
        ]]></notes>
        <packageUrl regex="true">^pkg:maven/io\.mockk.*</packageUrl>
    </suppress>

    <!-- Gradle相关误报抑制 -->
    <suppress>
        <notes><![CDATA[
        Gradle构建工具相关组件的误报，只在构建时使用，不包含在最终APK中
        ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.gradle.*</packageUrl>
    </suppress>

    <!-- OkHttp相关已知安全问题的处理 -->
    <suppress until="2025-12-31">
        <notes><![CDATA[
        OkHttp 4.x版本的已知安全问题，计划在2025年底前升级到最新版本
        临时抑制，需要定期评估和更新
        ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.squareup\.okhttp3.*</packageUrl>
        <vulnerabilityName>CVE-2023-3635</vulnerabilityName>
    </suppress>

    <!-- 开发工具相关抑制 -->
    <suppress>
        <notes><![CDATA[
        开发和调试工具的误报，这些工具只在开发环境使用，不包含在生产构建中
        ]]></notes>
        <packageUrl regex="true">^pkg:maven/.*debug.*</packageUrl>
    </suppress>

    <suppress>
        <notes><![CDATA[
        Timber日志库的误报，这是广泛使用的Android日志库，安全性良好
        ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.jakewharton\.timber.*</packageUrl>
    </suppress>

    <!-- Compose相关误报抑制 -->
    <suppress>
        <notes><![CDATA[
        Jetpack Compose相关库的误报，这些是Google官方现代UI工具包
        ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.compose.*</packageUrl>
    </suppress>

    <!-- Hilt/Dagger相关误报抑制 -->
    <suppress>
        <notes><![CDATA[
        Hilt依赖注入框架的误报，这是Google官方推荐的DI框架
        ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.google\.dagger.*</packageUrl>
    </suppress>

    <!--
    注意事项：
    1. 每个抑制规则都应该有明确的时间限制（until属性）或定期审查
    2. 对于生产环境真正使用的库，应该谨慎抑制安全漏洞
    3. 建议定期（如每月）审查和更新这个文件
    4. 新增抑制规则时，必须详细说明原因和风险评估
    -->

</suppressions>
