package com.example.gymbro.core.startup

import kotlinx.coroutines.flow.StateFlow

/**
 * 启动状态提供器接口 - Clean Architecture合规
 * 
 * 允许features模块获取应用启动状态，而不直接依赖app模块
 * 遵循依赖倒置原则：高层模块不依赖低层模块
 */
interface StartupStatusProvider {
    
    /**
     * 启动就绪状态数据类
     */
    data class StartupReadiness(
        val isNetworkReady: Boolean,
        val isAiCoreReady: Boolean,
        val isBgeEngineReady: Boolean,
        val overallProgress: Float,
        val estimatedTimeToReady: Long,
        val canStartCoach: Boolean
    )
    
    /**
     * 获取当前启动就绪状态
     */
    fun getStartupReadiness(): StartupReadiness
    
    /**
     * 监听启动状态变化
     */
    val startupReadiness: StateFlow<StartupReadiness>
    
    /**
     * Coach模块启动就绪检查
     */
    fun canStartCoachModule(): Boolean
}