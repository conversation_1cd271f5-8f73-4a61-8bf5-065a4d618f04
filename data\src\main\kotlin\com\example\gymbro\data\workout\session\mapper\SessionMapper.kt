package com.example.gymbro.data.workout.session.mapper

import com.example.gymbro.core.error.extensions.toModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.workout.session.entity.*
import com.example.gymbro.domain.workout.model.*

/**
 * SessionDB 数据映射器
 *
 * 基于 GymBro Clean Architecture 原则
 * 负责 Entity <-> Domain Model 的双向转换
 */

// ==================== WorkoutSession 映射 ====================

/**
 * 将 SessionEntity 转换为 WorkoutSession 领域模型
 */
fun SessionEntity.toDomain(): WorkoutSession =
    WorkoutSession(
        id = this.id,
        userId = this.userId,
        templateId = this.templateId,
        planId = this.planId,
        name = this.name,
        status = this.status,
        startTime = this.startTime,
        endTime = this.endTime,
        totalDuration = this.totalDuration,
        totalVolume = this.totalVolume,
        caloriesBurned = this.caloriesBurned,
        notes = this.notes,
        rating = this.rating,
        lastAutosaveTime = this.lastAutosaveTime,
        exercises = emptyList(), // 需要单独加载
    )

/**
 * 将 WorkoutSession 领域模型转换为 SessionEntity
 */
fun WorkoutSession.toEntity(): SessionEntity =
    SessionEntity(
        id = this.id,
        userId = this.userId,
        templateId = this.templateId,
        planId = this.planId,
        name = this.name,
        status = this.status,
        startTime = this.startTime,
        endTime = this.endTime,
        totalDuration = this.totalDuration,
        totalVolume = this.totalVolume,
        caloriesBurned = this.caloriesBurned,
        notes = this.notes,
        rating = this.rating,
        lastAutosaveTime = this.lastAutosaveTime,
    )

/**
 * 安全转换 SessionEntity 列表为 WorkoutSession 列表
 */
fun List<SessionEntity>.toDomainSafely(): ModernResult<List<WorkoutSession>> =
    try {
        ModernResult.Success(this.map { it.toDomain() })
    } catch (e: Exception) {
        ModernResult.Error(
            e.toModernDataError(
                operationName = "WorkoutSessionEntity.toDomainSafely",
                uiMessage = UiText.DynamicString("转换训练会话数据失败"),
            ),
        )
    }

// ==================== SessionExercise 映射 ====================

/**
 * 将 SessionExerciseEntity 转换为 SessionExercise 领域模型
 */
fun SessionExerciseEntity.toDomain(): SessionExercise =
    SessionExercise(
        id = this.id,
        sessionId = this.sessionId,
        exerciseId = this.exerciseId,
        order = this.order,
        name = this.name,
        targetSets = this.targetSets,
        completedSets = this.completedSets,
        status = this.status,
        startTime = this.startTime,
        endTime = this.endTime,
        notes = this.notes,
        isCompleted = this.isCompleted,
        sets = emptyList(), // 需要单独加载
        // ✅ 修复：添加缺失的休息时间字段映射
        restSeconds = this.restSeconds,
        restSecondsOverride = this.restSecondsOverride,
        imageUrl = this.imageUrl,
        videoUrl = this.videoUrl,
    )

/**
 * 将 SessionExercise 领域模型转换为 SessionExerciseEntity
 */
fun SessionExercise.toEntity(): SessionExerciseEntity =
    SessionExerciseEntity(
        id = this.id,
        sessionId = this.sessionId,
        exerciseId = this.exerciseId,
        order = this.order,
        name = this.name,
        targetSets = this.targetSets,
        completedSets = this.completedSets,
        status = this.status,
        startTime = this.startTime,
        endTime = this.endTime,
        notes = this.notes,
        isCompleted = this.isCompleted,
        restSeconds = this.restSeconds, // ✅ 修复：保持原始休息时间数据
        restSecondsOverride = this.restSecondsOverride, // ✅ 修复：保持Session覆盖数据
        imageUrl = this.imageUrl, // ✅ 修复：保持媒资字段
        videoUrl = this.videoUrl, // ✅ 修复：保持媒资字段
    )

/**
 * 安全转换 SessionExerciseEntity 列表为 SessionExercise 列表
 */
fun List<SessionExerciseEntity>.toSessionExerciseDomainSafely(): ModernResult<List<SessionExercise>> =
    try {
        ModernResult.Success(this.map { it.toDomain() })
    } catch (e: Exception) {
        ModernResult.Error(
            e.toModernDataError(
                operationName = "SessionExerciseEntity.toSessionExerciseDomainSafely",
                uiMessage = UiText.DynamicString("转换会话动作数据失败"),
            ),
        )
    }

// ==================== SessionSet 映射 ====================

/**
 * 将 SessionSetEntity 转换为 SessionSet 领域模型
 */
fun SessionSetEntity.toDomain(): SessionSet =
    SessionSet(
        id = this.id,
        sessionExerciseId = this.sessionExerciseId,
        setNumber = this.setNumber,
        weight = this.weight?.toDouble(),
        weightUnit = this.weightUnit,
        reps = this.reps,
        timeSeconds = this.timeSeconds,
        rpe = this.rpe?.toDouble(),
        isCompleted = this.isCompleted,
        isWarmupSet = this.isWarmupSet,
        notes = this.notes,
        timestamp = this.timestamp,
    )

/**
 * 将 SessionSet 领域模型转换为 SessionSetEntity
 */
fun SessionSet.toEntity(): SessionSetEntity =
    SessionSetEntity(
        id = this.id,
        sessionExerciseId = this.sessionExerciseId,
        setNumber = this.setNumber,
        weight = this.weight, // 保持Double类型
        weightUnit = this.weightUnit,
        reps = this.reps,
        timeSeconds = this.timeSeconds,
        rpe = this.rpe, // 保持Double类型
        isCompleted = this.isCompleted,
        isWarmupSet = this.isWarmupSet,
        notes = this.notes,
        timestamp = this.timestamp,
    )

/**
 * 安全转换 SessionSetEntity 列表为 SessionSet 列表
 */
fun List<SessionSetEntity>.toSessionSetDomainSafely(): ModernResult<List<SessionSet>> =
    try {
        ModernResult.Success(this.map { it.toDomain() })
    } catch (e: Exception) {
        ModernResult.Error(
            e.toModernDataError(
                operationName = "SessionSetEntity.toSessionSetDomainSafely",
                uiMessage = UiText.DynamicString("转换组数数据失败"),
            ),
        )
    }

// ==================== ExerciseHistoryStats 映射 ====================

/**
 * 将 ExerciseHistoryStatsEntity 转换为 ExerciseHistoryStats 领域模型
 */
fun ExerciseHistoryStatsEntity.toDomain(): ExerciseHistoryStats =
    ExerciseHistoryStats(
        id = this.id,
        userId = this.userId,
        exerciseId = this.exerciseId,
        personalBestWeight = this.personalBestWeight?.toDouble(),
        personalBestReps = this.personalBestReps,
        totalSetsCompleted = this.totalSetsCompleted,
        totalVolumeLifted = this.totalVolumeLifted.toDouble(),
        lastPerformanceDate = this.lastPerformanceDate,
        lastUpdated = this.lastUpdated,
    )

/**
 * 将 ExerciseHistoryStats 领域模型转换为 ExerciseHistoryStatsEntity
 */
fun ExerciseHistoryStats.toEntity(): ExerciseHistoryStatsEntity =
    ExerciseHistoryStatsEntity(
        id = this.id,
        userId = this.userId,
        exerciseId = this.exerciseId,
        personalBestWeight = this.personalBestWeight, // 保持Double类型
        personalBestReps = this.personalBestReps,
        totalSetsCompleted = this.totalSetsCompleted,
        totalVolumeLifted = this.totalVolumeLifted, // 保持Double类型
        lastPerformanceDate = this.lastPerformanceDate,
        lastUpdated = this.lastUpdated,
    )

// ==================== SessionAutoSave 映射 ====================

/**
 * 将 SessionAutoSaveEntity 转换为 SessionAutoSave 领域模型
 */
fun SessionAutoSaveEntity.toDomain(): SessionAutoSave =
    SessionAutoSave(
        id = this.id,
        sessionId = this.sessionId,
        saveType = this.saveType,
        saveTime = this.saveTime,
        sessionSnapshot = this.sessionSnapshot,
        progressSnapshot = this.progressSnapshot,
        currentState = this.currentState,
        nextAction = this.nextAction,
        isValid = this.isValid,
        expiresAt = this.expiresAt,
        createdAt = this.createdAt,
    )

/**
 * 将 SessionAutoSave 领域模型转换为 SessionAutoSaveEntity
 */
fun SessionAutoSave.toEntity(): SessionAutoSaveEntity =
    SessionAutoSaveEntity(
        id = this.id,
        sessionId = this.sessionId,
        saveType = this.saveType,
        saveTime = this.saveTime,
        sessionSnapshot = this.sessionSnapshot,
        progressSnapshot = this.progressSnapshot,
        currentState = this.currentState,
        nextAction = this.nextAction,
        isValid = this.isValid,
        expiresAt = this.expiresAt,
        createdAt = this.createdAt,
    )

// ==================== 复合映射 ====================

/**
 * 将 SessionExerciseEntity 和 SessionSetEntity 列表组合为完整的 SessionExercise
 */
fun SessionExerciseEntity.toDomainWithSets(sets: List<SessionSetEntity>): SessionExercise {
    val domainSets = sets.map { it.toDomain() }
    return this.toDomain().copy(sets = domainSets)
}

/**
 * 将 SessionEntity 和 SessionExerciseEntity 列表组合为完整的 WorkoutSession
 */
fun SessionEntity.toDomainWithExercises(exercises: List<SessionExercise>): WorkoutSession = this.toDomain().copy(exercises = exercises)

/**
 * 安全组合转换
 */
fun SessionExerciseEntity.toDomainWithSetsSafely(
    sets: List<SessionSetEntity>,
): ModernResult<SessionExercise> =
    try {
        ModernResult.Success(this.toDomainWithSets(sets))
    } catch (e: Exception) {
        ModernResult.Error(
            e.toModernDataError(
                operationName = "SessionExerciseEntity.toDomainWithSetsSafely",
                uiMessage = UiText.DynamicString("转换会话动作和组数数据失败"),
            ),
        )
    }

/**
 * 安全组合转换
 */
fun SessionEntity.toDomainWithExercisesSafely(
    exercises: List<SessionExercise>,
): ModernResult<WorkoutSession> =
    try {
        ModernResult.Success(this.toDomainWithExercises(exercises))
    } catch (e: Exception) {
        ModernResult.Error(
            e.toModernDataError(
                operationName = "SessionEntity.toDomainWithExercisesSafely",
                uiMessage = UiText.DynamicString("转换会话和动作数据失败"),
            ),
        )
    }
