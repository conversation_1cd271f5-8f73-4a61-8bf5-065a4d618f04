package com.example.gymbro.core.network.config

/**
 * AI任务类型枚举
 *
 * 用于AI提供商路由，不同类型的任务使用不同的优先级提供商
 */
enum class AiTaskType {
    /**
     * 聊天对话
     * 优先级：DeepSeek > OpenAI > Gemini
     */
    CHAT,

    /**
     * 标题生成
     * 优先级：Gemini > DeepSeek > OpenAI
     */
    TITLE_GENERATION,

    /**
     * 摘要生成
     * 优先级：Gemini > DeepSeek > OpenAI
     */
    SUMMARY,

    /**
     * 训练计划生成
     * 优先级：DeepSeek > OpenAI > Gemini
     */
    TRAINING_PLAN,

    /**
     * 营养建议
     * 优先级：DeepSeek > OpenAI > Gemini
     */
    NUTRITION_ADVICE,
}

/**
 * AI提供商枚举
 */
enum class AiProvider {
    DEEPSEEK,
    OPENAI,
    GOOGLE_GEMINI,
}

/**
 * AI流配置
 */
data class AiStreamConfig(
    val provider: AiProvider,
    val wsEndpoint: String,
    val sseEndpoint: String,
    val restEndpoint: String,
    val apiKey: String,
    val model: String,
    val maxTokens: Int = 4096,
    val temperature: Float = 0.7f,
    val supportWebSocket: Boolean = true,
    val supportSSE: Boolean = true,
)
