package com.example.gymbro.data.local.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 向量搜索表
 * 存储BGE模型生成的向量嵌入
 *
 * 用于语义搜索和混合搜索，支持VSS向量相似度计算
 */
@Entity(tableName = "chat_vec")
data class ChatVec(
    /**
     * 主键ID
     * 对应chat_raw表的id
     */
    @PrimaryKey
    val id: Long,

    /**
     * 向量嵌入数据
     * 使用ByteArray存储，由BGE模型生成的384维向量
     * 存储格式：Float数组序列化为ByteArray
     */
    @ColumnInfo(name = "embedding", typeAffinity = ColumnInfo.BLOB)
    val embedding: ByteArray,

    /**
     * 向量维度
     * BGE模型默认384维
     */
    @ColumnInfo(name = "embedding_dim")
    val embeddingDim: Int = 384,

    /**
     * 创建时间
     * 用于向量数据的管理和清理
     */
    @ColumnInfo(name = "created_at")
    val createdAt: Long = System.currentTimeMillis(),
) {

    companion object {
        /**
         * BGE模型默认向量维度
         */
        const val DEFAULT_EMBEDDING_DIM = 384
    }

    /**
     * 重写equals方法
     * ByteArray需要特殊处理
     */
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as ChatVec

        if (id != other.id) return false
        if (!embedding.contentEquals(other.embedding)) return false
        if (embeddingDim != other.embeddingDim) return false
        if (createdAt != other.createdAt) return false

        return true
    }

    /**
     * 重写hashCode方法
     * ByteArray需要特殊处理
     */
    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + embedding.contentHashCode()
        result = 31 * result + embeddingDim
        result = 31 * result + createdAt.hashCode()
        return result
    }
}
