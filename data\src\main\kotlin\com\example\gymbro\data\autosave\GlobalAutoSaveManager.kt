package com.example.gymbro.data.autosave

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import com.example.gymbro.core.autosave.AutoSaveManager
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.first
import kotlinx.serialization.json.Json
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 全局自动保存管理器实现
 *
 * 🎯 核心功能：
 * - 利用现有DataStore进行数据持久化
 * - 支持1秒最低间隔限制
 * - 提供类型安全的泛型API
 * - 集成ModernResult错误处理体系
 * - 支持协程和Flow响应式编程
 *
 * 注意：这是一个全局工具类，不直接实现AutoSaveManager接口
 * AutoSaveManager的具体实现在AutoSaveManagerImpl中
 *
 * @param context Android上下文
 * @param dataStore DataStore实例
 * @param json Json序列化器
 * @param scope 协程作用域
 * @param logger 日志记录器
 */
@Singleton
class GlobalAutoSaveManager
@Inject
constructor(
    @ApplicationContext private val context: Context,
    private val dataStore: DataStore<Preferences>,
    private val json: Json,
    private val scope: CoroutineScope,
    private val logger: Logger,
) {
    companion object {
        private const val AUTOSAVE_KEY_PREFIX = "autosave_"
        private const val LAST_SAVE_TIME_PREFIX = "last_save_time_"
        private const val SAVE_COUNT_PREFIX = "save_count_"
    }

    // 跟踪最后保存时间，实现1秒最低间隔限制
    private val lastSaveTimeMap = mutableMapOf<String, Long>()

    /**
     * 保存数据（支持泛型）
     *
     * @param key 数据键
     * @param data 要保存的数据
     * @return 保存结果
     */
    suspend fun <T : Any> save(
        key: String,
        data: T,
        serializer: (T) -> String,
    ): ModernResult<Unit> {
        return try {
            // 🎯 方案1：改进1秒间隔机制，不再静默忽略，而是记录最新数据
            val currentTime = System.currentTimeMillis()
            val lastSaveTime = lastSaveTimeMap[key] ?: 0L

            if (currentTime - lastSaveTime < AutoSaveManager.MIN_SAVE_INTERVAL_MS) {
                logger.d("GlobalAutoSaveManager", "⏳ 保存间隔不足1秒，但仍执行保存以确保数据不丢失: $key")
                // 不再静默忽略，而是继续执行保存，确保最新数据不会丢失
            }

            // 序列化数据
            val jsonString = serializer(data)

            // 保存到DataStore
            this.dataStore.edit { preferences ->
                val dataKey = stringPreferencesKey("$AUTOSAVE_KEY_PREFIX$key")
                val timeKey = stringPreferencesKey("$LAST_SAVE_TIME_PREFIX$key")
                val countKey = stringPreferencesKey("$SAVE_COUNT_PREFIX$key")

                preferences[dataKey] = jsonString
                preferences[timeKey] = currentTime.toString()

                // 更新保存次数
                val currentCount = preferences[countKey]?.toIntOrNull() ?: 0
                preferences[countKey] = (currentCount + 1).toString()
            }

            // 更新内存中的最后保存时间
            this.lastSaveTimeMap[key] = currentTime

            this.logger.d("GlobalAutoSaveManager", "数据已保存: $key")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            this.logger.e(e, "保存数据失败: $key")
            ModernResult.Error(
                com.example.gymbro.core.error.types.ModernDataError(
                    operationName = "GlobalAutoSaveManager.save",
                    errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                    category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                    cause = e,
                ),
            )
        }
    }

    /**
     * 恢复数据（支持泛型）
     *
     * @param key 数据键
     * @return 恢复的数据，如果不存在则返回null
     */
    suspend fun <T : Any> restore(
        key: String,
        deserializer: (String) -> T,
    ): ModernResult<T?> {
        return try {
            val preferences = this.dataStore.data.first()
            val dataKey = stringPreferencesKey("$AUTOSAVE_KEY_PREFIX$key")
            val jsonString = preferences[dataKey]

            if (jsonString.isNullOrBlank()) {
                this.logger.d("GlobalAutoSaveManager", "未找到数据: $key")
                return ModernResult.Success(null)
            }

            val data = deserializer(jsonString)
            this.logger.d("GlobalAutoSaveManager", "数据已恢复: $key")
            ModernResult.Success(data)
        } catch (e: Exception) {
            this.logger.e(e, "恢复数据失败: $key")
            ModernResult.Error(
                com.example.gymbro.core.error.types.ModernDataError(
                    operationName = "GlobalAutoSaveManager.restore",
                    errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                    category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                    cause = e,
                ),
            )
        }
    }

    /**
     * 删除数据
     *
     * @param key 数据键
     * @return 删除结果
     */
    suspend fun delete(key: String): ModernResult<Unit> =
        try {
            this.dataStore.edit { preferences ->
                val dataKey = stringPreferencesKey("$AUTOSAVE_KEY_PREFIX$key")
                val timeKey = stringPreferencesKey("$LAST_SAVE_TIME_PREFIX$key")
                val countKey = stringPreferencesKey("$SAVE_COUNT_PREFIX$key")

                preferences.remove(dataKey)
                preferences.remove(timeKey)
                preferences.remove(countKey)
            }

            // 清除内存中的记录
            this.lastSaveTimeMap.remove(key)

            this.logger.d("GlobalAutoSaveManager", "数据已删除: $key")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            this.logger.e(e, "删除数据失败: $key")
            ModernResult.Error(
                com.example.gymbro.core.error.types.ModernDataError(
                    operationName = "GlobalAutoSaveManager.delete",
                    errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                    category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                    cause = e,
                ),
            )
        }

    /**
     * 检查数据是否存在
     *
     * @param key 数据键
     * @return 是否存在
     */
    suspend fun exists(key: String): ModernResult<Boolean> =
        try {
            val preferences = this.dataStore.data.first()
            val dataKey = stringPreferencesKey("$AUTOSAVE_KEY_PREFIX$key")
            val exists = preferences.contains(dataKey)

            this.logger.d("GlobalAutoSaveManager", "检查数据存在性: $key = $exists")
            ModernResult.Success(exists)
        } catch (e: Exception) {
            this.logger.e(e, "检查数据存在性失败: $key")
            ModernResult.Error(
                com.example.gymbro.core.error.types.ModernDataError(
                    operationName = "GlobalAutoSaveManager.exists",
                    errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                    category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                    cause = e,
                ),
            )
        }

    /**
     * 获取最后保存时间
     *
     * @param key 数据键
     * @return 最后保存时间戳
     */
    suspend fun getLastSaveTime(key: String): ModernResult<Long> =
        try {
            val preferences = this.dataStore.data.first()
            val timeKey = stringPreferencesKey("$LAST_SAVE_TIME_PREFIX$key")
            val timeString = preferences[timeKey]
            val time = timeString?.toLongOrNull() ?: 0L

            this.logger.d("GlobalAutoSaveManager", "获取最后保存时间: $key = $time")
            ModernResult.Success(time)
        } catch (e: Exception) {
            this.logger.e(e, "获取最后保存时间失败: $key")
            ModernResult.Error(
                com.example.gymbro.core.error.types.ModernDataError(
                    operationName = "GlobalAutoSaveManager.getLastSaveTime",
                    errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                    category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                    cause = e,
                ),
            )
        }

    /**
     * 获取保存次数
     *
     * @param key 数据键
     * @return 保存次数
     */
    suspend fun getSaveCount(key: String): ModernResult<Int> =
        try {
            val preferences = this.dataStore.data.first()
            val countKey = stringPreferencesKey("$SAVE_COUNT_PREFIX$key")
            val countString = preferences[countKey]
            val count = countString?.toIntOrNull() ?: 0

            this.logger.d("GlobalAutoSaveManager", "获取保存次数: $key = $count")
            ModernResult.Success(count)
        } catch (e: Exception) {
            this.logger.e(e, "获取保存次数失败: $key")
            ModernResult.Error(
                com.example.gymbro.core.error.types.ModernDataError(
                    operationName = "GlobalAutoSaveManager.getSaveCount",
                    errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                    category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                    cause = e,
                ),
            )
        }

    /**
     * 便利方法：保存数据（使用内置Json）
     */
    suspend fun <T : Any> saveWithJson(
        key: String,
        data: T,
        serializer: (T) -> String,
    ): ModernResult<Unit> = save(key, data, serializer)

    /**
     * 便利方法：恢复数据（使用内置Json）
     */
    suspend fun <T : Any> restoreWithJson(
        key: String,
        deserializer: (String) -> T,
    ): ModernResult<T?> = restore(key, deserializer)
}
