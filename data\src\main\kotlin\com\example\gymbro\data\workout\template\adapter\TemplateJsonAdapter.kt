package com.example.gymbro.data.workout.template.adapter

import com.example.gymbro.core.error.extensions.safeCatch
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.TemplateDebugLogger
import com.example.gymbro.domain.shared.json.EntityType
import com.example.gymbro.domain.shared.json.EntityWrapper
import com.example.gymbro.domain.shared.json.JsonCompat
import com.example.gymbro.domain.shared.json.VersionInfo
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.shared.models.workout.TemplatePayload
import com.example.gymbro.shared.models.workout.withUpdatedSummary
import kotlinx.serialization.json.Json

/**
 * Template JSON适配器
 *
 * 基于 shared-models/docs/json表头.md 设计
 * 负责Domain Model与统一JSON Schema格式的双向转换
 */
object TemplateJsonAdapter {

    private val json = Json {
        ignoreUnknownKeys = true
        isLenient = true
        encodeDefaults = true      // 🔥 确保默认值被编码
        explicitNulls = true       // 🔥 修复：确保所有字段都被包含，包括默认值
        prettyPrint = false        // 🔥 紧凑格式节省空间
        coerceInputValues = true   // 🔥 修复：强制输入值转换，提高解析健壮性
    }

    /**
     * 将WorkoutTemplate转换为EntityWrapper JSON格式
     * 🔥 核心调整：自动计算并包含动态总结信息
     */
    suspend fun toWrappedJson(template: WorkoutTemplate): ModernResult<String> = safeCatch {
        val payload = template.toTemplatePayload()

        // 🔥 关键功能：自动计算动态总结信息
        val payloadWithSummary = payload.withUpdatedSummary()

        val wrapper = EntityWrapper<TemplatePayload>(
            entity = EntityType.TEMPLATE,
            payload = payloadWithSummary,
        )
        JsonCompat.toJson(wrapper)
    }

    /**
     * 从JSON字符串解析为WorkoutTemplate
     * 支持新旧格式兼容
     */
    suspend fun fromJson(jsonString: String): ModernResult<WorkoutTemplate> = safeCatch {
        val wrapper = JsonCompat.parseWithFallback<TemplatePayload>(
            jsonString = jsonString,
            entityType = EntityType.TEMPLATE,
        ) ?: throw IllegalArgumentException("Failed to parse template JSON")

        wrapper.payload.toWorkoutTemplate()
    }

    /**
     * 检查JSON格式版本
     */
    suspend fun getVersionInfo(jsonString: String): ModernResult<VersionInfo?> = safeCatch {
        JsonCompat.getVersionInfo(jsonString)
    }

    /**
     * 批量转换JSON列表
     */
    suspend fun fromJsonList(jsonList: List<String>): ModernResult<List<WorkoutTemplate>> = safeCatch {
        val wrappers = JsonCompat.parseList<TemplatePayload>(
            jsonList = jsonList,
            entityType = EntityType.TEMPLATE,
        )
        wrappers.map { it.payload.toWorkoutTemplate() }
    }
}

/**
 * WorkoutTemplate转换为TemplatePayload
 */
private fun WorkoutTemplate.toTemplatePayload(): TemplatePayload {
    return TemplatePayload(
        id = this.id,
        name = this.name,
        description = this.description,
        versionTag = 1, // 默认版本，实际应从数据库获取
        ownerId = this.userId,
        exercises = this.exercises.map { it.toTemplateExercisePayload() },
        metadata = this.toTemplateMetadataPayload(),
        createdAt = this.createdAt,
        updatedAt = this.updatedAt,
    )
}

/**
 * TemplatePayload转换为WorkoutTemplate
 */
private fun TemplatePayload.toWorkoutTemplate(): WorkoutTemplate {
    return WorkoutTemplate(
        id = this.id,
        name = this.name,
        description = this.description,
        targetMuscleGroups = this.metadata.targetMuscleGroups ?: emptyList(),
        difficulty = 1, // 默认难度，可从metadata扩展
        estimatedDuration = this.metadata.estimatedDuration,
        userId = this.ownerId,
        isPublic = false, // 默认私有，需要从其他地方获取
        isFavorite = false, // 默认不收藏，需要从其他地方获取
        tags = this.metadata.tags ?: emptyList(),
        createdAt = this.createdAt,
        updatedAt = this.updatedAt,
        exercises = this.exercises.map { it.toTemplateExercise() },
    )
}

/**
 * Exercise转换扩展函数（修复动作库集成）
 * 🔥 核心调整：确保每组作为独立数据单元存储，并正确解析notes中的customSets数据
 */
private fun com.example.gymbro.domain.workout.model.template.TemplateExercise.toTemplateExercisePayload(): com.example.gymbro.shared.models.workout.TemplateExercisePayload {
    TemplateDebugLogger.criticalDb("🔥 [ROOT-FIX] toTemplateExercisePayload 开始处理: ${this.name}")
    TemplateDebugLogger.criticalDb("🔥 [ROOT-FIX] Domain基础字段: sets=${this.sets}, reps=${this.reps}, weight=${this.weight}, rest=${this.restSeconds}")
    TemplateDebugLogger.criticalDb("🔥 [ROOT-FIX] Domain notes长度: ${this.notes?.length}, 内容预览: ${this.notes?.take(100)}")

    // 🔥 根本修复：优先从 notes 中解析真实的 customSets 数据
    val actualCustomSets = try {
        if (this.notes != null && this.notes!!.contains("__CUSTOM_SETS_JSON__")) {
            TemplateDebugLogger.criticalDb("🔥 [ROOT-FIX] 发现notes中包含customSets数据，开始解析")

            // 🔥 关键修复：支持两种标记格式，兼容现有数据
            val colonMarker = "__CUSTOM_SETS_JSON__:"
            val bracketMarker = "__CUSTOM_SETS_JSON__["

            // 🔥 关键修复：优先尝试冒号格式，然后尝试方括号格式
            val jsonData = when {
                this.notes!!.contains(colonMarker) -> {
                    val markerStart = this.notes!!.indexOf(colonMarker)
                    val jsonStart = markerStart + colonMarker.length
                    this.notes!!.substring(jsonStart)
                }
                this.notes!!.contains(bracketMarker) -> {
                    val markerStart = this.notes!!.indexOf(bracketMarker)
                    val jsonStart = markerStart + "__CUSTOM_SETS_JSON__".length
                    this.notes!!.substring(jsonStart)
                }
                else -> null
            }

            if (jsonData != null) {
                TemplateDebugLogger.criticalDb("🔥 [ROOT-FIX] 成功提取JSON数据，格式兼容")
            }

            if (jsonData != null) {
                TemplateDebugLogger.criticalDb("🔥 [ROOT-FIX] 提取JSON数据: ${jsonData.take(200)}")

                val parseJson = Json {
                    ignoreUnknownKeys = true
                    isLenient = true
                    encodeDefaults = true
                    explicitNulls = false
                }
                // 🔥 修复：清理JSON数据，移除可能的换行符和空白字符
                val cleanedJsonData = jsonData.trim()
                    .replace("\r\n", "")
                    .replace("\n", "")
                    .replace("\r", "")

                TemplateDebugLogger.criticalDb("🔥 [ROOT-FIX] 清理后JSON: ${cleanedJsonData.take(200)}")

                val parsedSets = parseJson.decodeFromString<List<com.example.gymbro.shared.models.workout.TemplateSetDto>>(cleanedJsonData)
                TemplateDebugLogger.criticalDb("🔥 [ROOT-FIX] 成功解析到 ${parsedSets.size} 组数据")

                // 转换为 TemplateSetPayload
                val payloadSets = parsedSets.map { set ->
                    TemplateDebugLogger.criticalDb("🔥 [ROOT-FIX] 转换组${set.setNumber}: weight=${set.targetWeight}, reps=${set.targetReps}, rest=${set.restTimeSeconds}")
                    com.example.gymbro.shared.models.workout.TemplateSetPayload(
                        setNumber = set.setNumber,
                        targetWeight = set.targetWeight,
                        targetReps = set.targetReps,
                        restTimeSeconds = set.restTimeSeconds,
                        targetDuration = set.targetDuration,
                        rpe = set.rpe
                    )
                }
                payloadSets
            } else {
                throw Exception("未找到JSON标记起始位置")
            }
        } else {
            throw Exception("notes中不包含customSets数据")
        }
    } catch (e: Exception) {
        TemplateDebugLogger.error("🔥 [CRITICAL-FIX] 从notes解析customSets失败: ${e.message}")
        TemplateDebugLogger.error("🔥 [CRITICAL-FIX] notes内容: ${this.notes}")

        // 🔥 紧急修复：提供安全的降级方案，避免界面卡死
        // 当历史数据损坏时，使用基础字段重建最小可用的 customSets
        TemplateDebugLogger.warn("🔥 [EMERGENCY-RECOVERY] 启用紧急恢复模式，从基础字段重建 customSets")

        // 从 TemplateExercise 的基础信息重建最小可用数据
        // TemplateExercise 没有 customSets 属性，需要从基础字段重建
        TemplateDebugLogger.warn("🔥 [EMERGENCY-RECOVERY] 创建默认 ${this.sets} 组数据")
        val emergencyCustomSets = (1..this.sets).map { setNumber ->
            com.example.gymbro.shared.models.workout.TemplateSetPayload(
                setNumber = setNumber,
                targetWeight = this.weight ?: 0f,
                targetReps = this.reps,
                restTimeSeconds = this.restSeconds,
                targetDuration = null,
                rpe = null
            )
        }

        emergencyCustomSets
    }

    TemplateDebugLogger.criticalDb("🔥 [ROOT-FIX] 最终生成 ${actualCustomSets.size} 组customSets")
    actualCustomSets.forEachIndexed { index, set ->
        TemplateDebugLogger.criticalDb("🔥 [ROOT-FIX] 最终组${index + 1}: weight=${set.targetWeight}, reps=${set.targetReps}, rest=${set.restTimeSeconds}")
    }

    return com.example.gymbro.shared.models.workout.TemplateExercisePayload(
        id = this.id,
        exerciseId = this.exerciseId,
        exerciseName = this.name,
        customSets = actualCustomSets, // 🔥 核心修复：使用解析的真实数据
        restTimeSeconds = this.restSeconds,
        notes = this.notes,
    )
}

/**
 * TemplateExercisePayload转换为TemplateExercise
 * 🔥 核心调整：sets 字段严格等于 customSets.size
 * 🔥 修复：基础字段不使用第一组数据，避免重量重置问题
 */
private fun com.example.gymbro.shared.models.workout.TemplateExercisePayload.toTemplateExercise(): com.example.gymbro.domain.workout.model.template.TemplateExercise {
    // 🔥 关键修复：将 customSets 数据序列化到 notes 字段中
    TemplateDebugLogger.criticalDb("TemplateJsonAdapter.toTemplateExercise 开始处理: ${this.exerciseName}")
    TemplateDebugLogger.criticalDb("原始 notes: ${this.notes}")
    TemplateDebugLogger.criticalDb("customSets 数量: ${this.customSets.size}")

    // 🔥 新增调试：验证输入的 customSets 数据
    this.customSets.forEachIndexed { index, payloadSet ->
        TemplateDebugLogger.criticalDb("输入TemplateSetPayload ${index + 1}: setNumber=${payloadSet.setNumber}, targetWeight=${payloadSet.targetWeight}, targetReps=${payloadSet.targetReps}, restTimeSeconds=${payloadSet.restTimeSeconds}")
    }

    val customSetsJson = try {
        // 🔥 关键修复：绕过损坏的TemplateSetPayload，直接从原始notes重新解析
        val originalNotes = this.notes
        TemplateDebugLogger.criticalDb("尝试从原始notes重新解析数据: ${originalNotes?.take(100)}")

        val templateSets = if (originalNotes != null && originalNotes.contains("__CUSTOM_SETS_JSON__:")) {
            // 方案1：从原始notes重新解析完整数据
            TemplateDebugLogger.criticalDb("从原始notes重新解析customSets数据")
            val standardMarker = "__CUSTOM_SETS_JSON__:"
            val markerStart = originalNotes.indexOf(standardMarker)
            if (markerStart != -1) {
                val jsonStart = markerStart + standardMarker.length
                val jsonData = originalNotes.substring(jsonStart)
                TemplateDebugLogger.criticalDb("原始JSON数据: ${jsonData.take(200)}")

                try {
                    // 创建本地JSON实例用于解析
                    val parseJson = Json {
                        ignoreUnknownKeys = true
                        isLenient = true
                        encodeDefaults = true
                        explicitNulls = false
                    }
                    val parsedSets = parseJson.decodeFromString<List<com.example.gymbro.shared.models.workout.TemplateSetDto>>(jsonData)
                    TemplateDebugLogger.criticalDb("成功从原始notes解析到 ${parsedSets.size} 组数据")
                    parsedSets.forEachIndexed { index, set ->
                        TemplateDebugLogger.criticalDb("原始解析组${index + 1}: setNumber=${set.setNumber}, targetWeight=${set.targetWeight}, targetReps=${set.targetReps}")
                    }
                    parsedSets
                } catch (parseException: Exception) {
                    TemplateDebugLogger.error("从原始notes解析失败，使用损坏的TemplateSetPayload", parseException)
                    // 备用方案：使用损坏的TemplateSetPayload但尝试修复
                    this.customSets.map { payloadSet ->
                        TemplateDebugLogger.criticalDb("备用处理组${payloadSet.setNumber}: weight=${payloadSet.targetWeight}, reps=${payloadSet.targetReps}")
                        com.example.gymbro.shared.models.workout.TemplateSetDto(
                            setNumber = payloadSet.setNumber,
                            targetWeight = payloadSet.targetWeight,
                            targetReps = payloadSet.targetReps,
                            restTimeSeconds = payloadSet.restTimeSeconds,
                            targetDuration = payloadSet.targetDuration,
                            rpe = payloadSet.rpe
                        )
                    }
                }
            } else {
                TemplateDebugLogger.warn("未找到customSets标记，使用TemplateSetPayload")
                this.customSets.map { payloadSet ->
                    com.example.gymbro.shared.models.workout.TemplateSetDto(
                        setNumber = payloadSet.setNumber,
                        targetWeight = payloadSet.targetWeight,
                        targetReps = payloadSet.targetReps,
                        restTimeSeconds = payloadSet.restTimeSeconds,
                        targetDuration = payloadSet.targetDuration,
                        rpe = payloadSet.rpe
                    )
                }
            }
        } else {
            // 方案2：使用TemplateSetPayload（可能已损坏）
            TemplateDebugLogger.warn("原始notes不包含customSets数据，使用TemplateSetPayload")
            this.customSets.map { payloadSet ->
                TemplateDebugLogger.criticalDb("处理组${payloadSet.setNumber}: weight=${payloadSet.targetWeight}, reps=${payloadSet.targetReps}")
                // 🔥 关键修复：确保 targetWeight 字段被正确传递
                val templateSetDto = com.example.gymbro.shared.models.workout.TemplateSetDto(
                    setNumber = payloadSet.setNumber,
                    targetWeight = payloadSet.targetWeight, // 🔥 确保重量数据被正确传递
                    targetReps = payloadSet.targetReps,
                    restTimeSeconds = payloadSet.restTimeSeconds,
                    targetDuration = payloadSet.targetDuration,
                    rpe = payloadSet.rpe
                )
                TemplateDebugLogger.criticalDb("创建 TemplateSetDto: setNumber=${templateSetDto.setNumber}, targetWeight=${templateSetDto.targetWeight}")
                templateSetDto
            }
        }

        // 🔥 验证序列化前的数据 - 所有关键字段
        templateSets.forEachIndexed { index, set ->
            TemplateDebugLogger.criticalDb("序列化前验证组${index + 1}: setNumber=${set.setNumber}, weight=${set.targetWeight}, reps=${set.targetReps}, rest=${set.restTimeSeconds}s")
        }

        // 创建本地JSON实例用于序列化
        val localJson = Json {
            ignoreUnknownKeys = true
            isLenient = true
            encodeDefaults = true      // 🔥 确保默认值被编码
            explicitNulls = true       // 🔥 修复：确保所有字段都被包含，包括默认值
            prettyPrint = false
            coerceInputValues = true   // 🔥 修复：强制输入值转换，提高解析健壮性
        }

        val jsonResult = localJson.encodeToString<List<com.example.gymbro.shared.models.workout.TemplateSetDto>>(templateSets) // 🔥 使用配置好的json实例
        TemplateDebugLogger.criticalDb("customSets JSON 序列化成功，长度: ${jsonResult.length}")
        TemplateDebugLogger.criticalDb("customSets JSON 内容: ${jsonResult.take(200)}...")

        // 🔥 验证序列化后的 JSON 是否包含所有关键字段
        val missingFields = mutableListOf<String>()
        if (!jsonResult.contains("setNumber")) missingFields.add("setNumber")
        if (!jsonResult.contains("targetWeight")) missingFields.add("targetWeight")
        if (!jsonResult.contains("targetReps")) missingFields.add("targetReps")
        if (!jsonResult.contains("restTimeSeconds")) missingFields.add("restTimeSeconds")

        if (missingFields.isNotEmpty()) {
            TemplateDebugLogger.error("序列化后的 JSON 缺少关键字段: ${missingFields.joinToString(", ")}！JSON: $jsonResult")
        } else {
            TemplateDebugLogger.criticalDb("✅ 所有关键字段都已正确序列化到 JSON 中")
        }

        jsonResult
    } catch (e: Exception) {
        TemplateDebugLogger.error("customSets 序列化失败", e)
        null
    }

    // 🔥 根本修复：从解析的customSets中计算Domain基础字段，防止污染循环
    // 在这里重新解析 customSets 用于计算基础字段
    val templateSetsForBasics = try {
        if (this.notes != null && this.notes!!.contains("__CUSTOM_SETS_JSON__:")) {
            // 🔥 修复：使用统一的标记格式，消除多格式支持的复杂性
            val standardMarker = "__CUSTOM_SETS_JSON__:"

            val markerStart = this.notes!!.indexOf(standardMarker)
            val jsonData = if (markerStart != -1) {
                val jsonStart = markerStart + standardMarker.length
                this.notes!!.substring(jsonStart)
            } else {
                null
            }

            if (jsonData != null) {
                TemplateDebugLogger.criticalDb("🔥 [CRITICAL-LOAD-FIX] 使用统一marker: $standardMarker")
            }

            if (jsonData != null) {
                // 🔥 Phase 0: 关键解析日志
                com.example.gymbro.core.logging.TemplateDebugLogger.criticalLoad("🔥 [PHASE0-JSON-PARSE] 开始解析customSets: ${this.exerciseName}")
                TemplateDebugLogger.criticalDb("🔥 [CRITICAL-LOAD-FIX] 提取JSON数据: ${jsonData.take(200)}")

                val parseJson = Json {
                    ignoreUnknownKeys = true
                    isLenient = true
                    encodeDefaults = true
                    explicitNulls = false
                }

                // 🔥 修复：清理JSON数据，移除可能的换行符和空白字符
                val cleanedJsonData = jsonData.trim()
                    .replace("\r\n", "")
                    .replace("\n", "")
                    .replace("\r", "")

                val parsedSets = parseJson.decodeFromString<List<com.example.gymbro.shared.models.workout.TemplateSetDto>>(cleanedJsonData)

                // 🔥 Phase 0: 记录解析结果
                com.example.gymbro.core.logging.TemplateDebugLogger.criticalLoad("🔥 [PHASE0-JSON-PARSE] 解析成功: ${this.exerciseName}, 组数=${parsedSets.size}")
                parsedSets.forEachIndexed { index, set ->
                    com.example.gymbro.core.logging.TemplateDebugLogger.criticalLoad("🔥 [PHASE0-JSON-PARSE] 组${index + 1}: weight=${set.targetWeight}, reps=${set.targetReps}")
                }
                parsedSets
            } else {
                // 🚨 Phase 0: 记录marker存在但解析失败的情况
                com.example.gymbro.core.logging.TemplateDebugLogger.warn("🚨 [PHASE0-WARNING] notes包含marker但找不到起始位置: ${this.exerciseName}")
                emptyList()
            }
        } else {
            // 🔥 Phase 0: 记录使用payload中customSets的情况
            com.example.gymbro.core.logging.TemplateDebugLogger.criticalLoad("🔥 [PHASE0-JSON-PARSE] 使用payload中的customSets: ${this.exerciseName}, 组数=${this.customSets.size}")
            // 使用payload中的customSets
            this.customSets.map { payloadSet ->
                com.example.gymbro.shared.models.workout.TemplateSetDto(
                    setNumber = payloadSet.setNumber,
                    targetWeight = payloadSet.targetWeight,
                    targetReps = payloadSet.targetReps,
                    restTimeSeconds = payloadSet.restTimeSeconds,
                    targetDuration = payloadSet.targetDuration,
                    rpe = payloadSet.rpe
                )
            }
        }
    } catch (e: Exception) {
        // 🔥 修复：降级处理而不是抛出异常，避免界面崩溃
        com.example.gymbro.core.logging.TemplateDebugLogger.error("🚨 [PHASE0-ERROR] customSets解析失败，启用降级处理: ${this.exerciseName}", e)
        // 使用payload中的customSets作为降级方案
        this.customSets.map { payloadSet ->
            com.example.gymbro.shared.models.workout.TemplateSetDto(
                setNumber = payloadSet.setNumber,
                targetWeight = payloadSet.targetWeight,
                targetReps = payloadSet.targetReps,
                restTimeSeconds = payloadSet.restTimeSeconds,
                targetDuration = payloadSet.targetDuration,
                rpe = payloadSet.rpe
            )
        }
    }

    // 🔥 Phase 0: 剔除重置逻辑 - TemplateExercisePayload 没有基础字段，直接使用默认值
    val effectiveWeight = 0f // TemplateExercisePayload 没有 weight 属性
    val effectiveReps = 10   // TemplateExercisePayload 没有 reps 属性，使用默认值
    TemplateDebugLogger.criticalDb("🔥 [NO-RESET] TemplateExercisePayload 使用默认基础字段: weight=$effectiveWeight, reps=$effectiveReps")

    // 🔥 Phase 0: 剔除污染检测代码 - 不再重置基础字段，无需检测污染

    // 🔥 关键修复：确保 notes 字段永远不为 null，即使 customSets 序列化失败
    val notesWithCustomSets = buildString {
        if (<EMAIL>?.isNotBlank() == true) {
            append(<EMAIL>)
            TemplateDebugLogger.criticalDb("添加原始 notes: ${<EMAIL>}")
        }
        if (customSetsJson != null) {
            if (isNotEmpty()) append("\n")
            // 🔥 修复：使用统一的标记格式，与 TemplateEditConfig.CUSTOM_SETS_JSON_MARKER 一致
            append("__CUSTOM_SETS_JSON__:$customSetsJson")
            TemplateDebugLogger.criticalDb("添加 customSets JSON，总长度: ${length}")
        } else {
            // 🔥 关键修复：即使 customSetsJson 为 null，也要确保有 customSets 数据
            if (<EMAIL>()) {
                TemplateDebugLogger.error("customSets JSON 序列化失败，但 customSets 不为空！强制重建 JSON")
                // 使用简化的 JSON 格式作为备用方案
                val fallbackJson = <EMAIL>(",") { set ->
                    """{"setNumber":${set.setNumber},"targetWeight":${set.targetWeight},"targetReps":${set.targetReps},"restTimeSeconds":${set.restTimeSeconds}}"""
                }
                if (isNotEmpty()) append("\n")
                // 🔥 修复：使用统一的标记格式，确保解析一致性
                append("__CUSTOM_SETS_JSON__:[$fallbackJson]")
                TemplateDebugLogger.criticalDb("使用备用 JSON 格式，总长度: ${length}")
            }
        }
    }

    // 🔥 关键修复：确保 notes 永远不为 null，至少包含 customSets 数据
    val finalNotes = if (notesWithCustomSets.isBlank() && <EMAIL>()) {
        // 最后的备用方案：直接使用简化格式
        val emergencyJson = <EMAIL>(",") { set ->
            """{"setNumber":${set.setNumber},"targetWeight":${set.targetWeight},"targetReps":${set.targetReps},"restTimeSeconds":${set.restTimeSeconds}}"""
        }
        // 🔥 修复：使用统一的标记格式
        val emergencyNotes = "__CUSTOM_SETS_JSON__:[$emergencyJson]"
        TemplateDebugLogger.warn("使用紧急备用格式生成 notes: ${emergencyNotes.take(100)}")
        emergencyNotes
    } else if (notesWithCustomSets.isNotBlank()) {
        notesWithCustomSets
    } else {
        // 🔥 强化：即使没有customSets，也要生成基础的JSON标记
        val exerciseName = <EMAIL>
        TemplateDebugLogger.warn("动作 $exerciseName 没有customSets数据，生成空白标记")
        // 🔥 修复：使用统一的标记格式，确保解析一致性
        "__CUSTOM_SETS_JSON__:[]"  // 至少保证有JSON标记
    }

    TemplateDebugLogger.criticalDb("最终 finalNotes: ${finalNotes.take(100)}...")
    TemplateDebugLogger.criticalDb("最终 finalNotes 长度: ${finalNotes.length}")

    return com.example.gymbro.domain.workout.model.template.TemplateExercise(
        id = this.id,
        exerciseId = this.exerciseId,
        name = this.exerciseName,
        order = 0, // TODO: 需要计算，暂时设为0
        sets = this.customSets.size, // 🔥 关键：组数严格等于 customSets 数量
        reps = effectiveReps, // 🔥 修复：使用从customSets计算的有效次数
        restSeconds = this.restTimeSeconds,
        weight = effectiveWeight, // 🔥 修复：使用从customSets计算的有效重量
        notes = finalNotes, // 🔥 关键：包含序列化的 customSets 数据，确保不为 null
    )
}

/**
 * WorkoutTemplate转换为TemplateMetadataPayload
 */
private fun WorkoutTemplate.toTemplateMetadataPayload(): com.example.gymbro.shared.models.workout.TemplateMetadataPayload {
    return com.example.gymbro.shared.models.workout.TemplateMetadataPayload(
        estimatedDuration = this.estimatedDuration ?: 0,
        targetMuscleGroups = this.targetMuscleGroups ?: emptyList(),
        equipment = emptyList(), // 需要从exercises计算
        tags = this.tags ?: emptyList(),
    )
}

/**
 * 根据exerciseId获取动作名称的辅助函数
 * 与ViewModel中的实现保持一致
 */
private fun getExerciseNameById(exerciseId: String): String {
    return when (exerciseId) {
        "off_5023c57c" -> "杠铃卧推"
        "off_eb02d317" -> "杠铃深蹲"
        "off_a1b2c3d4" -> "哑铃卧推"
        "off_e5f6g7h8" -> "引体向上"
        "off_i9j0k1l2" -> "硬拉"
        "off_m3n4o5p6" -> "肩上推举"
        "off_q7r8s9t0" -> "俯卧撑"
        "off_u1v2w3x4" -> "深蹲"
        else -> {
            when {
                exerciseId.startsWith("off_") -> "官方动作"
                exerciseId.startsWith("usr_") -> "自定义动作"
                else -> exerciseId.take(10) // 截取前10个字符作为显示名称
            }
        }
    }
}
