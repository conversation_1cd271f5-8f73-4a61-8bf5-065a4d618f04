package com.example.gymbro.core.datastore

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStore

/**
 * 应用程序统一的 DataStore 配置
 *
 * 解决多实例问题：确保整个应用只有一个 DataStore 实例
 * 避免 IllegalStateException: multiple DataStores active for the same file
 *
 * 使用方式：
 * - 在 Hilt Module 中注入此实例
 * - 所有需要 DataStore 的地方都使用同一个实例
 */

/**
 * 全局唯一的用户偏好设置 DataStore
 * 文件名：user_preferences
 */
val Context.userPreferencesDataStore: DataStore<Preferences> by preferencesDataStore(
    name = "user_preferences",
)
