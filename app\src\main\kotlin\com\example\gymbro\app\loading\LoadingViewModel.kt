package com.example.gymbro.app.loading

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.gymbro.app.extensions.fold
import com.example.gymbro.app.version.RegionDetectionManager
import com.example.gymbro.domain.auth.repository.AuthRepository
import com.example.gymbro.domain.auth.usecase.LoginAnonymouslyUseCase
import com.example.gymbro.domain.model.auth.AuthUser
import com.example.gymbro.domain.shared.base.version.VersionAccessResult
import com.example.gymbro.domain.shared.version.CheckVersionAccessUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeoutOrNull
import timber.log.Timber
import javax.inject.Inject

/**
 * Loading页面状态机
 */
sealed class LoadingState {
    /** 初始化中 */
    object Initializing : LoadingState()

    /** 版本检查中 */
    object CheckingVersion : LoadingState()

    /** 应用被锁定 */
    data class AppLocked(
        val lockInfo: VersionAccessResult.Locked,
    ) : LoadingState()

    /** 需要强制更新 */
    data class ForceUpdate(
        val updateInfo: VersionAccessResult.ForceUpdate,
    ) : LoadingState()

    /** 准备完成，可以跳转 */
    object Ready : LoadingState()
}

@HiltViewModel
class LoadingViewModel
@Inject
constructor(
    private val regionDetectionManager: RegionDetectionManager,
    private val authRepository: AuthRepository,
    private val loginAnonymouslyUseCase: LoginAnonymouslyUseCase,
    private val checkVersionAccessUseCase: CheckVersionAccessUseCase,
) : ViewModel() {
    private val _loadingState = MutableStateFlow<LoadingState>(LoadingState.Initializing)
    val loadingState: StateFlow<LoadingState> = _loadingState.asStateFlow()

    private var regionDetectionJob: Job? = null

    init {
        startLoadingProcess()
    }

    /**
     * 启动Loading流程
     * 采用静默后台策略，用户无需感知地区检测过程
     */
    private fun startLoadingProcess() {
        viewModelScope.launch {
            Timber.tag(StartupConstants.LogTag.STARTUP).d("Loading流程开始：启动后台静默地区检测")

            // 后台静默启动地区检测（不阻塞主流程）
            regionDetectionJob =
                launch {
                    silentRegionDetection()
                }

            // 主流程：直接进行版本检查（使用默认区域）
            performVersionCheck(regionCode = StartupConstants.Region.DEFAULT)
        }
    }

    /**
     * 后台静默地区检测
     * 失败时不影响主流程，默认使用国际区域
     */
    private suspend fun silentRegionDetection() {
        try {
            Timber.tag(StartupConstants.LogTag.REGION_DETECTION).d("后台静默地区检测开始")

            // 超时保护，不影响用户体验
            withTimeoutOrNull(StartupConstants.Network.REGION_DETECTION_TIMEOUT_MS) {
                regionDetectionManager.initializeRegionDetection()
            }

            val detectedRegion = regionDetectionManager.getCurrentRegion()
            val regionCode =
                when (detectedRegion) {
                    com.example.gymbro.core.region.RegionProvider.UserRegion.CN -> StartupConstants.Region.CN
                    com.example.gymbro.core.region.RegionProvider.UserRegion.INTERNATIONAL -> StartupConstants.Region.INTERNATIONAL
                    null -> StartupConstants.Region.DEFAULT
                }

            Timber.tag(StartupConstants.LogTag.REGION_DETECTION).d("地区检测完成，结果: $regionCode")

            // 如果版本检查还在进行，用检测到的区域重新检查
            if (_loadingState.value == LoadingState.CheckingVersion) {
                performVersionCheck(regionCode)
            }
        } catch (e: Exception) {
            Timber.tag(StartupConstants.LogTag.REGION_DETECTION).d("地区检测失败，使用默认区域: ${e.message}")
            // 静默失败，不影响用户体验
        }
    }

    /**
     * 执行版本检查
     * @param regionCode 区域代码，默认为国际区域
     */
    private suspend fun performVersionCheck(regionCode: String) {
        try {
            Timber.tag(StartupConstants.LogTag.VERSION_CHECK).d("开始版本检查，区域: $regionCode")
            _loadingState.value = LoadingState.CheckingVersion

            val versionResult = checkVersionAccessUseCase.execute(regionCode)

            // 使用扩展函数简化处理
            versionResult.fold(
                onSuccess = { access ->
                    when (access) {
                        is VersionAccessResult.Allowed -> {
                            Timber.tag(StartupConstants.LogTag.VERSION_CHECK).d("版本检查通过，准备跳转")
                            _loadingState.value = LoadingState.Ready
                            // Loading完成后1秒自动执行用户ID分配
                            scheduleUserIdAssignment()
                        }
                        is VersionAccessResult.Locked -> {
                            Timber.tag(StartupConstants.LogTag.VERSION_CHECK).w("应用被锁定: ${access.reason}")
                            _loadingState.value = LoadingState.AppLocked(access)
                        }
                        is VersionAccessResult.ForceUpdate -> {
                            Timber
                                .tag(
                                    StartupConstants.LogTag.VERSION_CHECK,
                                ).w("需要强制更新: ${access.currentVersion} -> ${access.requiredVersion}")
                            _loadingState.value = LoadingState.ForceUpdate(access)
                        }
                    }
                },
                onError = { error ->
                    Timber
                        .tag(
                            StartupConstants.LogTag.VERSION_CHECK,
                        ).w("版本检查失败，允许继续使用: ${error.operationName}")
                    _loadingState.value = LoadingState.Ready
                    // Loading完成后1秒自动执行用户ID分配
                    scheduleUserIdAssignment()
                },
            )
        } catch (e: Exception) {
            Timber.tag(StartupConstants.LogTag.VERSION_CHECK).e(e, "版本检查异常，允许继续使用")
            // 优雅降级：出错时允许继续使用
            _loadingState.value = LoadingState.Ready
            // Loading完成后1秒自动执行用户ID分配
            scheduleUserIdAssignment()
        }
    }

    /**
     * 调度用户ID分配任务
     * Loading完成后1秒自动执行
     */
    private fun scheduleUserIdAssignment() {
        viewModelScope.launch {
            try {
                // 等待1秒
                kotlinx.coroutines.delay(1000)
                Timber.tag(StartupConstants.LogTag.USER_ID_ASSIGNMENT).d("开始自动用户ID分配...")
                ensureUserIdAssigned()
            } catch (e: Exception) {
                Timber.tag(StartupConstants.LogTag.USER_ID_ASSIGNMENT).e(e, "自动用户ID分配失败")
            }
        }
    }

    /**
     * 确保用户已分配userid（现在会在Loading完成后1秒自动执行）
     * 也可以手动调用以确保用户ID存在
     */
    suspend fun ensureUserIdAssigned(): Boolean {
        return try {
            Timber.tag(StartupConstants.LogTag.USER_ID_ASSIGNMENT).d("开始检查用户ID分配状态...")

            // 检查是否已经有用户ID
            val authStateResult = authRepository.getCurrentUser().first()

            // 使用扩展函数简化处理
            var userExists = false
            authStateResult.fold(
                onSuccess = { currentUser: AuthUser? ->
                    if (currentUser != null && currentUser.uid.isNotEmpty()) {
                        Timber
                            .tag(
                                StartupConstants.LogTag.USER_ID_ASSIGNMENT,
                            ).d("用户已存在，userid: ${currentUser.uid}")
                        userExists = true
                    }
                },
                onError = { error ->
                    Timber.tag(StartupConstants.LogTag.USER_ID_ASSIGNMENT).w("检查用户状态失败: ${error.message}")
                },
            )

            if (userExists) return true

            // 如果没有用户ID，创建匿名用户
            Timber.tag(StartupConstants.LogTag.USER_ID_ASSIGNMENT).d("未找到现有用户，创建匿名用户...")
            val loginResult = loginAnonymouslyUseCase()

            var createSuccess = false
            loginResult.fold(
                onSuccess = { anonymousUser: AuthUser ->
                    Timber
                        .tag(
                            StartupConstants.LogTag.USER_ID_ASSIGNMENT,
                        ).d("匿名用户创建成功，userid: ${anonymousUser.uid}")
                    createSuccess = true
                    // 用户ID分配成功后，启用延迟的功能
                    enableDelayedFeatures()
                },
                onError = { error ->
                    Timber.tag(StartupConstants.LogTag.USER_ID_ASSIGNMENT).e("创建匿名用户失败: ${error.message}")
                },
            )

            createSuccess
        } catch (e: Exception) {
            Timber.tag(StartupConstants.LogTag.USER_ID_ASSIGNMENT).e(e, "用户ID分配过程中发生异常")
            false
        }
    }

    /**
     * 启用延迟的功能
     * 在用户ID分配成功后调用
     */
    private fun enableDelayedFeatures() {
        viewModelScope.launch {
            try {
                Timber.tag(StartupConstants.LogTag.USER_ID_ASSIGNMENT).d("开始启用延迟功能...")

                // 注意：网络监控和后台同步等功能将在需要时自动启动
                // 这里主要是为了记录用户ID分配成功的事件

                Timber.tag(StartupConstants.LogTag.USER_ID_ASSIGNMENT).d("延迟功能启用完成")
            } catch (e: Exception) {
                Timber.tag(StartupConstants.LogTag.USER_ID_ASSIGNMENT).e(e, "启用延迟功能失败")
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        regionDetectionJob?.cancel()
    }
}
