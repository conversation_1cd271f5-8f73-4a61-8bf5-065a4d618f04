package com.example.gymbro.designSystem.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme

/**
 * 🎯 GymBro 通用文本输入对话框
 * 遵循 MVI-Architecture-Performance-Guide.md 最佳实践
 *
 * 特性：
 * - 无状态设计，状态完全由外部 ViewModel 管理
 * - 集成 GymBro 设计系统
 * - 统一的样式和行为
 * - 支持输入验证和错误显示
 * - 字符长度限制支持
 *
 * @param show 是否显示对话框
 * @param title 对话框标题
 * @param value 当前输入值
 * @param onValueChange 输入值变化回调
 * @param onDismiss 取消回调
 * @param onConfirm 确认回调
 * @param modifier 修饰符
 * @param label 输入框标签
 * @param placeholder 占位符文本
 * @param isError 是否显示错误状态
 * @param errorMessage 错误消息
 * @param maxLength 最大字符长度
 * @param enabled 是否启用
 * @param confirmText 确认按钮文本
 * @param dismissText 取消按钮文本
 */
@Composable
fun GymBroTextEditDialog(
    show: Boolean,
    title: UiText,
    value: String,
    onValueChange: (String) -> Unit,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit,
    modifier: Modifier = Modifier,
    label: UiText? = null,
    placeholder: UiText? = null,
    isError: Boolean = false,
    errorMessage: UiText? = null,
    maxLength: Int? = null,
    enabled: Boolean = true,
    confirmText: UiText = UiText.DynamicString("确定"),
    dismissText: UiText = UiText.DynamicString("取消"),
) {
    if (show) {
        AlertDialog(
            onDismissRequest = onDismiss,
            title = {
                Text(
                    text = title.asString(),
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface,
                )
            },
            text = {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.spacedBy(12.dp),
                ) {
                    GymBroInputField(
                        value = value,
                        onValueChange = onValueChange,
                        label = label?.let { { Text(it.asString()) } },
                        placeholder = placeholder?.asString() ?: "",
                        modifier = Modifier.fillMaxWidth(),
                        isError = isError,
                        errorMessage = errorMessage,
                        enabled = enabled,
                        maxLength = maxLength,
                        singleLine = true,
                    )

                    // 字符计数显示
                    if (maxLength != null) {
                        Text(
                            text = "${value.length}/$maxLength",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.align(Alignment.End),
                        )
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = onConfirm,
                    enabled = enabled && !isError,
                ) {
                    Text(
                        text = confirmText.asString(),
                        color = if (enabled && !isError) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                        },
                    )
                }
            },
            dismissButton = {
                TextButton(onClick = onDismiss) {
                    Text(
                        text = dismissText.asString(),
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                    )
                }
            },
            modifier = modifier,
            containerColor = MaterialTheme.colorScheme.surface,
            tonalElevation = 6.dp,
        )
    }
}

/**
 * 🎯 GymBro 通用数值输入对话框
 * 专门用于数值（身高、体重、年龄等）输入
 *
 * @param show 是否显示对话框
 * @param title 对话框标题
 * @param value 当前数值（字符串格式）
 * @param onValueChange 数值变化回调
 * @param onDismiss 取消回调
 * @param onConfirm 确认回调
 * @param modifier 修饰符
 * @param label 输入框标签
 * @param unit 单位文本（如"kg", "cm"）
 * @param minValue 最小值
 * @param maxValue 最大值
 * @param decimalPlaces 小数位数
 * @param isError 是否显示错误状态
 * @param errorMessage 错误消息
 * @param enabled 是否启用
 * @param confirmText 确认按钮文本
 * @param dismissText 取消按钮文本
 */
@Composable
fun GymBroNumberEditDialog(
    show: Boolean,
    title: UiText,
    value: String,
    onValueChange: (String) -> Unit,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit,
    modifier: Modifier = Modifier,
    label: UiText? = null,
    unit: UiText? = null,
    minValue: Double? = null,
    maxValue: Double? = null,
    decimalPlaces: Int = 0,
    isError: Boolean = false,
    errorMessage: UiText? = null,
    enabled: Boolean = true,
    confirmText: UiText = UiText.DynamicString("确定"),
    dismissText: UiText = UiText.DynamicString("取消"),
) {
    if (show) {
        AlertDialog(
            onDismissRequest = onDismiss,
            title = {
                Text(
                    text = title.asString(),
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface,
                )
            },
            text = {
                numberField(
                    value = value,
                    onValueChange = onValueChange,
                    label = label ?: UiText.DynamicString("请输入数值"),
                    modifier = Modifier.fillMaxWidth(),
                    isError = isError,
                    errorMessage = errorMessage,
                    enabled = enabled,
                    minValue = minValue,
                    maxValue = maxValue,
                    decimalPlaces = decimalPlaces,
                    unit = unit,
                )
            },
            confirmButton = {
                val isValidNumber = value.toDoubleOrNull() != null
                TextButton(
                    onClick = onConfirm,
                    enabled = enabled && !isError && isValidNumber,
                ) {
                    Text(
                        text = confirmText.asString(),
                        color = if (enabled && !isError && isValidNumber) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                        },
                    )
                }
            },
            dismissButton = {
                TextButton(onClick = onDismiss) {
                    Text(
                        text = dismissText.asString(),
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                    )
                }
            },
            modifier = modifier,
            containerColor = MaterialTheme.colorScheme.surface,
            tonalElevation = 6.dp,
        )
    }
}

/**
 * 🎯 GymBro 通用单选对话框
 * 用于性别、健身水平等单选场景
 *
 * @param T 选项类型
 * @param show 是否显示对话框
 * @param title 对话框标题
 * @param options 可选项列表
 * @param selectedOption 当前选中项
 * @param onOptionSelected 选项选择回调
 * @param onDismiss 取消回调
 * @param onConfirm 确认回调
 * @param getDisplayText 获取选项显示文本的函数
 * @param modifier 修饰符
 * @param enabled 是否启用
 * @param confirmText 确认按钮文本
 * @param dismissText 取消按钮文本
 */
@Composable
fun <T> GymBroSingleChoiceDialog(
    show: Boolean,
    title: UiText,
    options: List<T>,
    selectedOption: T?,
    onOptionSelected: (T) -> Unit,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit,
    getDisplayText: (T) -> String,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    confirmText: UiText = UiText.DynamicString("确定"),
    dismissText: UiText = UiText.DynamicString("取消"),
) {
    if (show) {
        AlertDialog(
            onDismissRequest = onDismiss,
            title = {
                Text(
                    text = title.asString(),
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface,
                )
            },
            text = {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(max = 400.dp)
                        .verticalScroll(rememberScrollState()),
                ) {
                    options.forEach { option ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable(enabled = enabled) {
                                    onOptionSelected(option)
                                }
                                .padding(vertical = 12.dp, horizontal = 4.dp),
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            RadioButton(
                                selected = selectedOption == option,
                                onClick = { if (enabled) onOptionSelected(option) },
                                enabled = enabled,
                                colors = RadioButtonDefaults.colors(
                                    selectedColor = MaterialTheme.colorScheme.primary,
                                    unselectedColor = MaterialTheme.colorScheme.onSurfaceVariant,
                                ),
                            )
                            Spacer(modifier = Modifier.width(16.dp))
                            Text(
                                text = getDisplayText(option),
                                style = MaterialTheme.typography.bodyLarge,
                                color = if (enabled) {
                                    MaterialTheme.colorScheme.onSurface
                                } else {
                                    MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                                },
                            )
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = onConfirm,
                    enabled = enabled && selectedOption != null,
                ) {
                    Text(
                        text = confirmText.asString(),
                        color = if (enabled && selectedOption != null) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                        },
                    )
                }
            },
            dismissButton = {
                TextButton(onClick = onDismiss) {
                    Text(
                        text = dismissText.asString(),
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                    )
                }
            },
            modifier = modifier,
            containerColor = MaterialTheme.colorScheme.surface,
            tonalElevation = 6.dp,
        )
    }
}

/**
 * 🎯 GymBro 通用多选对话框
 * 用于训练日等多选场景
 *
 * @param T 选项类型
 * @param show 是否显示对话框
 * @param title 对话框标题
 * @param options 可选项列表
 * @param selectedOptions 当前选中项集合
 * @param onOptionsChanged 选项变化回调
 * @param onDismiss 取消回调
 * @param onConfirm 确认回调
 * @param getDisplayText 获取选项显示文本的函数
 * @param modifier 修饰符
 * @param enabled 是否启用
 * @param confirmText 确认按钮文本
 * @param dismissText 取消按钮文本
 */
@Composable
fun <T> GymBroMultiChoiceDialog(
    show: Boolean,
    title: UiText,
    options: List<T>,
    selectedOptions: Set<T>,
    onOptionsChanged: (Set<T>) -> Unit,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit,
    getDisplayText: (T) -> String,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    confirmText: UiText = UiText.DynamicString("确定"),
    dismissText: UiText = UiText.DynamicString("取消"),
) {
    if (show) {
        AlertDialog(
            onDismissRequest = onDismiss,
            title = {
                Text(
                    text = title.asString(),
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface,
                )
            },
            text = {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(max = 400.dp)
                        .verticalScroll(rememberScrollState()),
                ) {
                    options.forEach { option ->
                        val isSelected = selectedOptions.contains(option)
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable(enabled = enabled) {
                                    val newSelection = if (isSelected) {
                                        selectedOptions - option
                                    } else {
                                        selectedOptions + option
                                    }
                                    onOptionsChanged(newSelection)
                                }
                                .padding(vertical = 12.dp, horizontal = 4.dp),
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            Checkbox(
                                checked = isSelected,
                                onCheckedChange = { checked ->
                                    if (enabled) {
                                        val newSelection = if (checked) {
                                            selectedOptions + option
                                        } else {
                                            selectedOptions - option
                                        }
                                        onOptionsChanged(newSelection)
                                    }
                                },
                                enabled = enabled,
                                colors = CheckboxDefaults.colors(
                                    checkedColor = MaterialTheme.colorScheme.primary,
                                    uncheckedColor = MaterialTheme.colorScheme.onSurfaceVariant,
                                ),
                            )
                            Spacer(modifier = Modifier.width(16.dp))
                            Text(
                                text = getDisplayText(option),
                                style = MaterialTheme.typography.bodyLarge,
                                color = if (enabled) {
                                    MaterialTheme.colorScheme.onSurface
                                } else {
                                    MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                                },
                            )
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = onConfirm,
                    enabled = enabled,
                ) {
                    Text(
                        text = confirmText.asString(),
                        color = if (enabled) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                        },
                    )
                }
            },
            dismissButton = {
                TextButton(onClick = onDismiss) {
                    Text(
                        text = dismissText.asString(),
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                    )
                }
            },
            modifier = modifier,
            containerColor = MaterialTheme.colorScheme.surface,
            tonalElevation = 6.dp,
        )
    }
}

// === Preview 组件 ===

@GymBroPreview
@Composable
private fun GymBroTextEditDialogPreview() {
    GymBroTheme {
        var showDialog by remember { mutableStateOf(true) }
        var text by remember { mutableStateOf("示例文本") }

        GymBroTextEditDialog(
            show = showDialog,
            title = UiText.DynamicString("编辑昵称"),
            value = text,
            onValueChange = { text = it },
            onDismiss = { showDialog = false },
            onConfirm = { showDialog = false },
            label = UiText.DynamicString("请输入昵称"),
            maxLength = 20,
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroNumberEditDialogPreview() {
    GymBroTheme {
        var showDialog by remember { mutableStateOf(true) }
        var number by remember { mutableStateOf("175") }

        GymBroNumberEditDialog(
            show = showDialog,
            title = UiText.DynamicString("编辑身高"),
            value = number,
            onValueChange = { number = it },
            onDismiss = { showDialog = false },
            onConfirm = { showDialog = false },
            label = UiText.DynamicString("请输入身高"),
            unit = UiText.DynamicString("cm"),
            minValue = 50.0,
            maxValue = 300.0,
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroSingleChoiceDialogPreview() {
    GymBroTheme {
        var showDialog by remember { mutableStateOf(true) }
        val options = listOf("男", "女", "其他", "不愿说")
        var selected by remember { mutableStateOf("男") }

        GymBroSingleChoiceDialog(
            show = showDialog,
            title = UiText.DynamicString("选择性别"),
            options = options,
            selectedOption = selected,
            onOptionSelected = { selected = it },
            onDismiss = { showDialog = false },
            onConfirm = { showDialog = false },
            getDisplayText = { it },
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroMultiChoiceDialogPreview() {
    GymBroTheme {
        var showDialog by remember { mutableStateOf(true) }
        val options = listOf("周一", "周二", "周三", "周四", "周五", "周六", "周日")
        var selected by remember { mutableStateOf(setOf("周一", "周三", "周五")) }

        GymBroMultiChoiceDialog(
            show = showDialog,
            title = UiText.DynamicString("选择训练日"),
            options = options,
            selectedOptions = selected,
            onOptionsChanged = { selected = it },
            onDismiss = { showDialog = false },
            onConfirm = { showDialog = false },
            getDisplayText = { it },
        )
    }
}
