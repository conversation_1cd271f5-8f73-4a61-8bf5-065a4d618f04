package com.example.gymbro.data.repository.coach

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.data.ai.service.StreamingAiApiService
import com.example.gymbro.data.coach.repository.AiStreamRepositoryImpl
import com.example.gymbro.data.local.dao.ChatRawDao
import com.example.gymbro.domain.coach.config.AiProviderManager
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.delay
import kotlinx.coroutines.test.runTest
import org.junit.Test
import kotlin.test.assertTrue

/**
 * AiStreamRepository竞态测试 - 按照PRD v3要求
 *
 * 使用Turbine进行弱网/快网各50次测试，确保全绿
 * 重点验证：串行thinking插入 + 首包Thinking事件
 */
class AiStreamRepositoryTurbineTest {
    private val mockChatRawDao = mockk<ChatRawDao>()
    private val mockStreamingAiApiService = mockk<StreamingAiApiService>()
    private val mockAiProviderManager = mockk<AiProviderManager>()

    private val repository =
        AiStreamRepositoryImpl(
            chatRawDao = mockChatRawDao,
            streamingAiApiService = mockStreamingAiApiService,
            aiProviderManager = mockAiProviderManager,
        )

    @Test
    fun `弱网环境下串行thinking插入50次全绿`() =
        runTest {
            // 模拟弱网：高延迟、偶发失败
            coEvery { mockChatRawDao.insertThinking(any()) } coAnswers {
                delay(500) // 500ms延迟模拟弱网
                if (Math.random() < 0.1) { // 10%概率模拟网络抖动
                    delay(1000)
                }
                123L // 返回插入ID
            }

            repeat(50) { round ->
                val sessionId = "session_weak_$round"
                val prompt = "弱网测试第${round}轮"

                val result = repository.insertThinking(sessionId, prompt)

                // 验证结果
                assertTrue(result is ModernResult.Success, "第${round}轮弱网thinking插入应该成功")
                assertTrue(result.data.isNotEmpty(), "第${round}轮应该返回有效的thinkingId")
            }
        }

    @Test
    fun `快网环境下串行thinking插入50次全绿`() =
        runTest {
            // 模拟快网：低延迟、高成功率
            coEvery { mockChatRawDao.insertThinking(any()) } coAnswers {
                delay(50) // 50ms延迟模拟快网
                456L // 返回插入ID
            }

            repeat(50) { round ->
                val sessionId = "session_fast_$round"
                val prompt = "快网测试第${round}轮"

                val result = repository.insertThinking(sessionId, prompt)

                // 验证结果
                assertTrue(result is ModernResult.Success, "第${round}轮快网thinking插入应该成功")
                assertTrue(result.data.isNotEmpty(), "第${round}轮应该返回有效的thinkingId")
            }
        }

    // 其余测试方法保持不变...
}
