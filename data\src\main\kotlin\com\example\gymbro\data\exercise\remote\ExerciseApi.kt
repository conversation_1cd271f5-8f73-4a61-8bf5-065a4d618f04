package com.example.gymbro.data.exercise.remote

import com.example.gymbro.shared.models.exercise.ExerciseDto
import retrofit2.http.*

/**
 * Exercise远程API接口
 *
 * 基于plan.md设计：
 * - 官方库同步
 * - 增量更新
 * - 版本管理
 * - 用户自定义动作云同步
 */
interface ExerciseApi {

    /**
     * 获取官方库元数据
     * 用于版本检查和同步决策
     */
    @GET("api/v1/exercise/library/meta")
    suspend fun getLibraryMeta(): ExerciseLibraryMeta

    /**
     * 获取官方库增量更新
     * 基于版本号获取差异数据
     *
     * @param fromVersion 本地版本号
     * @param toVersion 目标版本号
     * @return 增量更新数据
     */
    @GET("api/v1/exercise/library/updates")
    suspend fun getLibraryUpdates(
        @Query("from") fromVersion: Int,
        @Query("to") toVersion: Int,
    ): ExerciseLibraryUpdates

    /**
     * 获取完整官方库
     * 用于首次安装或完全重置
     */
    @GET("api/v1/exercise/library/full")
    suspend fun getFullLibrary(): List<ExerciseDto>

    /**
     * 获取官方库分页数据
     * 用于大数据集的分批下载
     */
    @GET("api/v1/exercise/library")
    suspend fun getLibraryPage(
        @Query("page") page: Int,
        @Query("size") size: Int = 50,
    ): ExerciseLibraryPage

    /**
     * 根据ID批量获取动作
     * 用于精确同步特定动作
     */
    @POST("api/v1/exercise/batch")
    suspend fun getExercisesBatch(
        @Body ids: List<String>,
    ): List<ExerciseDto>

    // ========== 用户自定义动作同步 ==========

    /**
     * 上传用户自定义动作
     * 支持新增和更新
     */
    @POST("api/v1/exercise/custom")
    suspend fun uploadCustomExercise(
        @Body exercise: ExerciseDto,
        @Header("Authorization") token: String,
    ): ExerciseDto

    /**
     * 批量上传用户自定义动作
     */
    @POST("api/v1/exercise/custom/batch")
    suspend fun uploadCustomExercisesBatch(
        @Body exercises: List<ExerciseDto>,
        @Header("Authorization") token: String,
    ): List<ExerciseDto>

    /**
     * 获取用户的自定义动作
     */
    @GET("api/v1/exercise/custom")
    suspend fun getUserCustomExercises(
        @Header("Authorization") token: String,
        @Query("lastSync") lastSyncTimestamp: Long? = null,
    ): List<ExerciseDto>

    /**
     * 删除用户自定义动作
     */
    @DELETE("api/v1/exercise/custom/{id}")
    suspend fun deleteCustomExercise(
        @Path("id") exerciseId: String,
        @Header("Authorization") token: String,
    )

    // ========== 搜索和推荐 ==========

    /**
     * 远程搜索动作
     * 用于本地搜索结果不足时的补充
     */
    @GET("api/v1/exercise/search")
    suspend fun searchExercises(
        @Query("q") query: String,
        @Query("muscleGroup") muscleGroup: String? = null,
        @Query("equipment") equipment: String? = null,
        @Query("limit") limit: Int = 20,
    ): List<ExerciseDto>

    /**
     * 获取推荐动作
     * 基于用户历史和偏好
     */
    @GET("api/v1/exercise/recommendations")
    suspend fun getRecommendations(
        @Header("Authorization") token: String,
        @Query("muscleGroup") muscleGroup: String? = null,
        @Query("limit") limit: Int = 10,
    ): List<ExerciseDto>

    /**
     * 获取热门动作
     * 基于全平台使用统计
     */
    @GET("api/v1/exercise/popular")
    suspend fun getPopularExercises(
        @Query("muscleGroup") muscleGroup: String? = null,
        @Query("period") period: String = "week", // week, month, all
        @Query("limit") limit: Int = 20,
    ): List<ExerciseDto>

    // ========== 统计和分析 ==========

    /**
     * 上报动作使用统计
     * 用于改进推荐算法
     */
    @POST("api/v1/exercise/usage")
    suspend fun reportUsageStats(
        @Body stats: ExerciseUsageReport,
        @Header("Authorization") token: String,
    )

    /**
     * 获取动作详细信息
     * 包括使用统计、评分等
     */
    @GET("api/v1/exercise/{id}/details")
    suspend fun getExerciseDetails(
        @Path("id") exerciseId: String,
    ): ExerciseDetailsResponse

    // ========== 媒体资源 ==========

    /**
     * 获取动作媒体资源URL
     * 支持图片、视频的CDN链接
     */
    @GET("api/v1/exercise/{id}/media")
    suspend fun getExerciseMedia(
        @Path("id") exerciseId: String,
        @Query("type") mediaType: String = "all", // image, video, all
    ): ExerciseMediaResponse

    /**
     * 上传自定义动作媒体
     */
    @Multipart
    @POST("api/v1/exercise/{id}/media")
    suspend fun uploadExerciseMedia(
        @Path("id") exerciseId: String,
        @Part("type") mediaType: String,
        @Part media: okhttp3.MultipartBody.Part,
        @Header("Authorization") token: String,
    ): ExerciseMediaUploadResponse
}

/**
 * 官方库元数据
 */
data class ExerciseLibraryMeta(
    val version: Int,
    val totalCount: Int,
    val lastUpdated: Long,
    val checksum: String,
    val downloadUrl: String? = null,
)

/**
 * 官方库增量更新数据
 */
data class ExerciseLibraryUpdates(
    val fromVersion: Int,
    val toVersion: Int,
    val added: List<ExerciseDto>,
    val updated: List<ExerciseDto>,
    val deleted: List<String>, // 删除的动作ID列表
    val timestamp: Long,
)

/**
 * 官方库分页数据
 */
data class ExerciseLibraryPage(
    val exercises: List<ExerciseDto>,
    val page: Int,
    val size: Int,
    val totalPages: Int,
    val totalElements: Int,
    val hasNext: Boolean,
)

/**
 * 动作使用统计上报
 */
data class ExerciseUsageReport(
    val exerciseId: String,
    val userId: String,
    val sessionId: String,
    val sets: Int,
    val reps: Int,
    val weight: Float? = null,
    val duration: Long? = null,
    val timestamp: Long = System.currentTimeMillis(),
)

/**
 * 动作详细信息响应
 */
data class ExerciseDetailsResponse(
    val exercise: ExerciseDto,
    val usageStats: ExerciseUsageStats,
    val rating: ExerciseRating,
    val alternatives: List<ExerciseDto>,
)

/**
 * 动作使用统计
 */
data class ExerciseUsageStats(
    val totalUsers: Int,
    val weeklyUsers: Int,
    val averageSets: Float,
    val averageReps: Float,
    val averageWeight: Float? = null,
    val popularityRank: Int,
)

/**
 * 动作评分
 */
data class ExerciseRating(
    val averageRating: Float,
    val totalRatings: Int,
    val difficultyRating: Float,
    val effectivenessRating: Float,
)

/**
 * 动作媒体资源响应
 */
data class ExerciseMediaResponse(
    val exerciseId: String,
    val images: List<MediaAsset>,
    val videos: List<MediaAsset>,
    val thumbnails: List<MediaAsset>,
)

/**
 * 媒体资源
 */
data class MediaAsset(
    val id: String,
    val type: String, // image, video, thumbnail
    val url: String,
    val size: Long? = null,
    val width: Int? = null,
    val height: Int? = null,
    val duration: Long? = null, // 视频时长（毫秒）
)

/**
 * 媒体上传响应
 */
data class ExerciseMediaUploadResponse(
    val mediaId: String,
    val url: String,
    val type: String,
    val size: Long,
    val uploadedAt: Long,
)
