---
alwaysApply: false
---

## Clean Architecture Compliance Enforcement

### Layer Dependency Direction Rules
```
✅ ALLOWED:
Presentation (features/*) → Domain
Domain ← Data
All layers → Core
All layers → DI (for injection only)

❌ PROHIBITED:
Domain → Data (reverse dependency)
Data → Presentation (skip layer)
Feature → Feature (horizontal dependency)
Any circular dependencies
```

### Interface Design Principles

#### Repository Pattern Compliance
```kotlin
// ✅ REQUIRED: Domain layer interface definition
interface ChatRepository {
    suspend fun getChatHistory(): ModernResult<List<ChatMsg>>
    fun streamAnswer(history: List<ChatMsg>, newMsg: ChatMsg): Flow<ModernResult<ChatMsg>>
    suspend fun clearChatHistory(): ModernResult<Unit>
}

// ✅ REQUIRED: Data layer implementation
class ChatRepositoryImpl @Inject constructor(
    private val api: AICoachApi,
    private val dao: ChatDao,
    private val errorHandler: ModernErrorHandler
) : ChatRepository

// ❌ PROHIBITED: Direct implementation injection
@Inject constructor(private val repo: ChatRepositoryImpl)
```

#### UseCase Pattern Compliance
```kotlin
// ✅ REQUIRED: UseCase pattern
class StreamAnswerUseCase @Inject constructor(
    private val repository: ChatRepository,  // Interface, not implementation
    @IoDispatcher private val dispatcher: CoroutineDispatcher
) {
    operator fun invoke(
        history: List<ChatMsg>,
        newMsg: ChatMsg
    ): Flow<ModernResult<ChatMsg>> =
        repository.streamAnswer(history, newMsg)
            .flowOn(dispatcher)
}

// ❌ PROHIBITED: Direct repository access in ViewModel
@HiltViewModel
class ViewModel @Inject constructor(
    private val repository: ChatRepository  // Should use UseCase
)
```

## Error Handling Pattern Enforcement

### ModernResult<T> Usage Rules
```kotlin
// ✅ REQUIRED: All async operations return ModernResult
suspend fun operation(): ModernResult<Data>
fun streamOperation(): Flow<ModernResult<Data>>

// ✅ REQUIRED: Proper error conversion
try {
    val data = api.fetchData()
    ModernResult.Success(data)
} catch (e: Exception) {
    ModernResult.Error(e.toModernDataError(
        operationName = "fetchData",
        defaultUiMessage = UiText.StringResource(R.string.error_fetch)
    ))
}

// ❌ PROHIBITED: Custom result types
sealed class CustomResult<T>
data class ApiResponse<T>(val success: Boolean, val data: T?)
```

### Error Type Compliance
```kotlin
// ✅ REQUIRED: Use GlobalErrorType hierarchy
ModernDataError(
    operationName = "streamAnswer",
    errorType = GlobalErrorType.NETWORK_ERROR,
    uiMessage = UiText.StringResource(R.string.error_network)
)

// ❌ PROHIBITED: Custom error enums
enum class CustomError { NETWORK, DATABASE, UNKNOWN }
```

## Interface Contract Enforcement

### Repository Interface Standards
```kotlin
// ✅ REQUIRED: Interface naming convention
interface {Entity}Repository
interface ChatRepository
interface UserRepository
interface WorkoutRepository

// ✅ REQUIRED: Implementation naming convention
class {Entity}RepositoryImpl
class ChatRepositoryImpl : ChatRepository

// ✅ REQUIRED: Method signatures
suspend fun get{Entity}(): ModernResult<Entity>
suspend fun save{Entity}(entity: Entity): ModernResult<Unit>
fun observe{Entity}(): Flow<ModernResult<Entity>>

// ❌ PROHIBITED: Inconsistent naming
interface ChatRepo
class ChatRepositoryImplementation
```

### UseCase Interface Standards
```kotlin
// ✅ REQUIRED: UseCase naming convention
class {Verb}{Noun}UseCase
class GetUserProfileUseCase
class StreamAnswerUseCase
class SaveWorkoutUseCase

// ✅ REQUIRED: Invoke operator
operator fun invoke(params): ModernResult<T>
operator fun invoke(params): Flow<ModernResult<T>>

// ❌ PROHIBITED: Method-based UseCase
class UseCase {
    fun execute(params): Result<T>
    fun perform(params): T
}
```

## Dependency Injection Compliance

### Hilt Module Standards
```kotlin
// ✅ REQUIRED: Repository binding pattern
@Module
@InstallIn(SingletonComponent::class)
abstract class RepositoryModule {
    @Binds
    abstract fun bindChatRepository(impl: ChatRepositoryImpl): ChatRepository
}

// ✅ REQUIRED: Provider pattern
@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {
    @Provides
    @Singleton
    fun provideJson(): Json = Json { /* config */ }
}

// ❌ PROHIBITED: Concrete class binding
@Binds
abstract fun bindRepository(impl: ChatRepositoryImpl): ChatRepositoryImpl
```

### Scope Compliance
```kotlin
// ✅ REQUIRED: Appropriate scoping
@Singleton  // For shared, stateless services
@ViewModelScoped  // For ViewModel-specific dependencies
@ActivityScoped  // For Activity-specific instances

// ❌ PROHIBITED: Overuse of Singleton
@Singleton  // For every dependency regardless of lifecycle
```

## Data Layer Interface Compliance

### DataSource Pattern
```kotlin
// ✅ REQUIRED: Separate remote and local data sources
interface ChatRemoteDataSource {
    suspend fun streamAnswer(request: ChatRequestDto): Flow<ChatChunkDto>
}

interface ChatLocalDataSource {
    suspend fun getChatHistory(): List<ChatMsgEntity>
    suspend fun saveChatMessage(entity: ChatMsgEntity)
}

// ✅ REQUIRED: Repository coordinates data sources
class ChatRepositoryImpl @Inject constructor(
    private val remoteDataSource: ChatRemoteDataSource,
    private val localDataSource: ChatLocalDataSource,
    private val mapper: ChatMapper
) : ChatRepository
```

### Mapping Interface Standards
```kotlin
// ✅ REQUIRED: Bidirectional mapping functions
fun EntityDto.toDomain(): DomainEntity
fun DomainEntity.toDto(): EntityDto
fun DatabaseEntity.toDomain(): DomainEntity
fun DomainEntity.toEntity(): DatabaseEntity

// ✅ REQUIRED: Batch mapping extensions
fun List<EntityDto>.toDomain(): List<DomainEntity>
fun List<DomainEntity>.toDto(): List<EntityDto>

// ❌ PROHIBITED: Unidirectional mapping only
fun EntityDto.toDomain(): DomainEntity  // Missing reverse mapping
```

## Modern Tech Stack Interface Compliance

### Serialization Interface Standards
```kotlin
// ✅ REQUIRED: kotlinx.serialization usage
@Serializable
data class ChatRequestDto(
    @SerialName("model") val model: String,
    @SerialName("messages") val messages: List<MessageDto>
)

// ❌ PROHIBITED: Moshi annotations
@JsonClass(generateAdapter = true)
data class ChatRequestDto(
    @Json(name = "model") val model: String
)
```

### Time Handling Interface Standards
```kotlin
// ✅ REQUIRED: kotlinx.datetime usage
data class ChatMsg(
    val createdAt: Instant = Clock.System.now()
)

fun Instant.toEpochMilliseconds(): Long
fun Long.toInstant(): Instant = Instant.fromEpochMilliseconds(this)

// ❌ PROHIBITED: Legacy time APIs
val createdAt: Long = System.currentTimeMillis()
val date: Date = Date()
```

### Text Handling Interface Standards
```kotlin
// ✅ REQUIRED: UiText usage in domain models
data class ChatMsg(
    val text: UiText  // NOT String
)

// ✅ REQUIRED: UiText creation in ViewModel
UiText.DynamicString("Hello")
UiText.StringResource(R.string.greeting)

// ✅ REQUIRED: UiText display in UI
@Composable
fun DisplayText(text: UiText) {
    Text(text = text.asString())
}

// ❌ PROHIBITED: Hardcoded strings in domain
data class ChatMsg(
    val text: String  // Should be UiText
)
```

## Architecture Violation Detection

### Layer Boundary Violations
```kotlin
// ❌ PROHIBITED: Domain layer Android dependencies
// In domain module:
import android.util.Log
import android.content.Context
import androidx.compose.runtime.Composable

// ❌ PROHIBITED: Data layer UI dependencies
// In data module:
import androidx.compose.ui.graphics.Color
import com.example.gymbro.features.auth.ui.AuthScreen

// ❌ PROHIBITED: Feature cross-dependencies
// In features/auth:
import com.example.gymbro.features.workout.ui.WorkoutScreen
```

### Interface Contract Violations
```kotlin
// ❌ PROHIBITED: Repository returning non-ModernResult
interface ChatRepository {
    suspend fun getChatHistory(): List<ChatMsg>  // Should be ModernResult<List<ChatMsg>>
    fun streamAnswer(): Flow<ChatMsg>  // Should be Flow<ModernResult<ChatMsg>>
}

// ❌ PROHIBITED: UseCase not using Repository interface
class StreamAnswerUseCase @Inject constructor(
    private val repository: ChatRepositoryImpl  // Should be ChatRepository interface
)

// ❌ PROHIBITED: ViewModel accessing Repository directly
@HiltViewModel
class ChatViewModel @Inject constructor(
    private val repository: ChatRepository  // Should use UseCase
)
```

## Implementation Guide Reference Compliance

### Domain Layer Compliance
- Reference: `domain/IMPLEMENTATION_GUIDE.md`
- All entities must use UiText for user-visible text
- All async operations return ModernResult<T>
- No Android dependencies allowed
- UseCase pattern with operator fun invoke

### Data Layer Compliance
- Reference: `data/IMPLEMENTATION_GUIDE.md`
- Repository implements Domain interfaces
- Use kotlinx.serialization for API models
- Proper Entity ↔ Domain ↔ DTO mapping
- Error conversion to ModernDataError

### DI Layer Compliance
- Reference: `di/IMPLEMENTATION_GUIDE.md`
- Use @Binds for interface binding
- Use @Provides for complex object creation
- Appropriate scope annotations
- Test module replacements available

### Presentation Layer Compliance
- Reference: `features/IMPLEMENTATION_GUIDE.md`
- ViewModel uses StateFlow + Channel pattern
- All Composables have @Preview annotations
- Modifier as first parameter
- Stateless component design

## Compliance Verification Commands

### Architecture Dependency Check
```bash
# Check for prohibited dependencies
grep -r "import.*features" domain/ data/ di/
grep -r "import android" domain/
grep -r "import androidx.compose" domain/ data/

# Check for proper interface usage
grep -r "ChatRepositoryImpl" features/ domain/
grep -r ": ChatRepository" data/
```

### Error Handling Compliance Check
```bash
# Check for ModernResult usage
grep -r "suspend fun" domain/ | grep -v "ModernResult"
grep -r "Flow<" domain/ | grep -v "ModernResult"

# Check for proper error types
grep -r "Exception" data/ | grep -v "toModernDataError"
grep -r "try {" data/ | grep -v "ModernResult.Error"
```

### Modern Tech Stack Compliance Check
```bash
# Check for old serialization
grep -r "@JsonClass" .
grep -r "@Json(" .

# Check for old time handling
grep -r "System.currentTimeMillis" .
grep -r "Date()" .

# Check for hardcoded strings
grep -r "Text(" features/ | grep -v "uiState\|\.asString()"
```
