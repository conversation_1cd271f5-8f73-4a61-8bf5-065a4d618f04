<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <!-- Timer Card Container -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="8dp"
        app:cardBackgroundColor="?attr/colorSurface">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:gravity="center">

            <!-- Timer Display -->
            <TextView
                android:id="@+id/timerText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:fontFamily="@font/maple_mono"
                android:gravity="center"
                android:textColor="?attr/colorPrimary"
                android:textSize="24sp"
                android:textStyle="bold"
                android:contentDescription="剩余时间"
                tools:text="1:30" />

            <!-- Control Buttons Row -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center">

                <!-- Minus 10s Button -->
                <ImageButton
                    android:id="@+id/minusButton"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_marginEnd="8dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="@string/decrease_timer"
                    android:src="@drawable/ic_remove_24"
                    app:tint="?attr/colorOnSurface" />

                <!-- Close Button -->
                <ImageButton
                    android:id="@+id/closeButton"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_marginHorizontal="8dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="@string/close_timer"
                    android:src="@drawable/ic_close_24"
                    app:tint="?attr/colorOnSurface" />

                <!-- Plus 10s Button -->
                <ImageButton
                    android:id="@+id/addButton"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_marginStart="8dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="@string/increase_timer"
                    android:src="@drawable/ic_add_24"
                    app:tint="?attr/colorOnSurface" />

            </LinearLayout>

            <!-- Drag Hint -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="@string/drag_to_move"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:textSize="10sp" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

</LinearLayout>
