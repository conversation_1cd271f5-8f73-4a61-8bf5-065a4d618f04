package com.example.gymbro.core.error.internal

import com.example.gymbro.core.error.icon.ErrorIconProvider // Keep this import for the interface
import com.example.gymbro.core.error.types.ModernDataError
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 默认错误图标提供者实现
 * 返回null，表示没有图标
 */
@Singleton
class DefaultErrorIconProvider @Inject constructor() : ErrorIconProvider {
    /**
     * 获取错误图标标识符
     *
     * @param error 需要处理的错误
     * @return 0，表示没有图标
     */
    override fun getErrorIconIdentifier(error: ModernDataError): Any {
        // 默认实现返回0，表示没有图标
        return 0
    }
}
