package com.example.gymbro.core.error

import com.example.gymbro.core.error.recovery.RecoveryStrategy
import com.example.gymbro.core.error.types.ErrorSeverity
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText

/**
 * 统一错误处理器接口
 *
 * 负责将技术错误转换为用户友好消息，并提供错误处理建议
 * 此接口不依赖Android框架，可用于任何层
 */
interface ModernErrorHandler {
    /**
     * 获取用户友好的错误消息
     *
     * @param error 需要处理的错误
     * @return 用户友好的错误消息
     */
    fun getUiMessage(error: ModernDataError): UiText

    /**
     * 判断错误是否可恢复
     *
     * @param error 需要检查的错误
     * @return 错误是否可恢复
     */
    fun isRetryable(error: ModernDataError): Boolean

    /**
     * 获取错误的严重程度
     *
     * @param error 需要检查的错误
     * @return 错误的严重程度
     */
    fun getSeverity(error: ModernDataError): ErrorSeverity

    /**
     * 获取UI层面的错误严重程度
     *
     * @param error 需要检查的错误
     * @return UI严重程度类型
     */
    fun getSeverityType(error: ModernDataError): UiSeverityType

    /**
     * 获取错误的建议操作
     *
     * @param error 需要处理的错误
     * @return 建议的用户操作
     */
    fun getSuggestedAction(error: ModernDataError): SuggestedAction

    /**
     * 根据错误获取适当的图标资源ID
     *
     * @param error 需要处理的错误
     * @return 图标资源ID
     * @deprecated 使用 getErrorIconIdentifier 代替，它不依赖Android框架
     */
    @Deprecated(
        message = "使用 getErrorIconIdentifier 代替，它不依赖Android框架",
        replaceWith = ReplaceWith("getErrorIconIdentifier(error) as Int"),
        level = DeprecationLevel.WARNING,
    )
    fun getErrorIconResId(error: ModernDataError): Int

    /**
     * 获取错误图标标识符
     * 使用ErrorIconProvider接口获取错误图标标识符
     *
     * @param error 需要处理的错误
     * @return 错误图标标识符
     */
    fun getErrorIconIdentifier(error: ModernDataError): Any

    /**
     * 是否需要认证
     */
    fun needsReauthentication(error: ModernDataError): Boolean

    /**
     * 是否应该全局处理
     */
    fun shouldHandleGlobally(error: ModernDataError): Boolean

    /**
     * 获取技术错误消息（用于日志）
     */
    fun getTechnicalMessage(error: ModernDataError): String

    /**
     * 建议恢复策略
     */
    fun <T> suggestRecoveryStrategy(error: ModernDataError): RecoveryStrategy<T>?

    /**
     * 将Throwable转换为ModernResult.Error
     *
     * @param e 需要处理的异常
     * @return 包含错误的ModernResult
     */
    fun handleError(e: Throwable): ModernResult.Error

    /**
     * 将Throwable转换为ModernResult.Error，用于Flow上下文
     *
     * @param e 需要处理的异常
     * @return 包含错误的ModernResult
     */
    fun handleErrorFlow(e: Throwable): ModernResult.Error

    /**
     * UI中的错误严重程度类型
     */
    enum class UiSeverityType {
        /** 严重错误 */
        ERROR,

        /** 警告 */
        WARNING,

        /** 提示信息 */
        INFO,
    }

    /**
     * 建议的用户操作枚举
     */
    enum class SuggestedAction {
        /** 显示错误消息 */
        SHOW_MESSAGE,

        /** 重试操作 */
        RETRY,

        /** 重新登录 */
        RELOGIN,

        /** 检查网络连接 */
        CHECK_NETWORK,

        /** 重启应用 */
        RESTART_APP,

        /** 联系支持 */
        CONTACT_SUPPORT,
    }

    /**
     * Processes a given error, typically a [ModernResult.Error.ErrorData].
     *
     * @param error The error data to handle.
     * @return A [GlobalErrorType] which might be the original error or a transformed one.
     *         Implementations might also trigger side effects like logging or navigation.
     */
    fun processModernDataError(modernDataError: ModernDataError): GlobalErrorType

    /**
     * A more generic handler that can take any ModernResult.Error.
     * This can be useful if the specific type of ErrorData is not known or relevant.
     */
    fun processErrorResult(result: ModernResult.Error): GlobalErrorType

    /**
     * 从错误中提取字段错误信息
     *
     * @param error 错误对象
     * @return 字段名称到错误消息的映射
     */
    fun extractFieldErrors(error: ModernDataError): Map<String, UiText>
}

/**
 * ModernErrorHandler接口的别名，为了向后兼容
 */
typealias ModernErrorHandlerInterface = ModernErrorHandler
