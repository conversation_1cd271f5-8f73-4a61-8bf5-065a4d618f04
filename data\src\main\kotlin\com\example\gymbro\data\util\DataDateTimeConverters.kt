package com.example.gymbro.data.util

import kotlinx.datetime.Instant

/**
 * 提供Long与Instant之间的转换工具函数
 */
object DataDateTimeConverters {
    /**
     * 将毫秒时间戳转换为Instant
     */
    fun Long?.toInstant(): Instant? = this?.let { Instant.fromEpochMilliseconds(it) }
}

// 添加便捷扩展函数
fun Long?.toInstantInDataModule(): Instant? = this?.let { Instant.fromEpochMilliseconds(it) }

fun Instant?.toEpochMillisInDataModule(): Long? = this?.toEpochMilliseconds()
