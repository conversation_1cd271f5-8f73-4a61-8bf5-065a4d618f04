package com.example.gymbro.data.coach.dao

import androidx.room.*
import com.example.gymbro.data.local.entity.ChatSessionEntity
import kotlinx.coroutines.flow.Flow

/**
 * 聊天会话数据访问对象
 *
 * 提供聊天会话的CRUD操作和查询功能
 */
@Dao
interface ChatSessionDao {

    /**
     * 插入新的聊天会话
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSession(session: ChatSessionEntity): Long

    /**
     * 批量插入聊天会话
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSessions(sessions: List<ChatSessionEntity>)

    /**
     * 更新聊天会话
     */
    @Update
    suspend fun updateSession(session: ChatSessionEntity)

    /**
     * 根据ID获取聊天会话
     */
    @Query("SELECT * FROM chat_sessions WHERE id = :sessionId")
    suspend fun getSessionById(sessionId: String): ChatSessionEntity?

    /**
     * 获取用户的所有聊天会话
     */
    @Query(
        """
        SELECT * FROM chat_sessions
        WHERE user_id = :userId
        AND (:includeArchived = 1 OR status = ${ChatSessionEntity.STATUS_ACTIVE})
        AND status != ${ChatSessionEntity.STATUS_DELETED}
        ORDER BY last_active_at DESC
        LIMIT :limit OFFSET :offset
    """,
    )
    fun getUserSessions(
        userId: String,
        includeArchived: Boolean,
        limit: Int,
        offset: Int,
    ): Flow<List<ChatSessionEntity>>

    /**
     * 获取用户的所有聊天会话（用于分页）
     */
    @Query(
        """
        SELECT * FROM chat_sessions
        WHERE user_id = :userId
        AND (:includeArchived = 1 OR status = ${ChatSessionEntity.STATUS_ACTIVE})
        AND status != ${ChatSessionEntity.STATUS_DELETED}
        ORDER BY last_active_at DESC
        LIMIT :limit OFFSET :offset
    """,
    )
    suspend fun getUserSessionsPaged(
        userId: String,
        includeArchived: Boolean,
        limit: Int,
        offset: Int,
    ): List<ChatSessionEntity>

    /**
     * 获取用户的活跃会话
     */
    @Query(
        """
        SELECT * FROM chat_sessions
        WHERE user_id = :userId
        AND is_active = 1
        AND status = ${ChatSessionEntity.STATUS_ACTIVE}
        ORDER BY last_active_at DESC
        LIMIT :limit
    """,
    )
    fun getActiveSessions(userId: String, limit: Int): Flow<List<ChatSessionEntity>>

    /**
     * 观察用户最新的聊天会话（无感加载）
     *
     * 返回用户最近活跃的会话，用于实现瞬时的历史记录加载
     */
    @Query(
        """
        SELECT * FROM chat_sessions
        WHERE user_id = :userId
        AND status = ${ChatSessionEntity.STATUS_ACTIVE}
        ORDER BY last_active_at DESC
        LIMIT 1
    """,
    )
    fun observeLatestSession(userId: String): Flow<ChatSessionEntity?>

    /**
     * 按标题搜索会话
     */
    @Query(
        """
        SELECT * FROM chat_sessions
        WHERE user_id = :userId
        AND title LIKE '%' || :query || '%'
        AND status != ${ChatSessionEntity.STATUS_DELETED}
        ORDER BY
            CASE WHEN title LIKE :query || '%' THEN 1 ELSE 2 END,
            last_active_at DESC
        LIMIT :limit
    """,
    )
    fun searchSessionsByTitle(
        userId: String,
        query: String,
        limit: Int,
    ): Flow<List<ChatSessionEntity>>

    /**
     * 更新会话标题
     */
    @Query(
        """
        UPDATE chat_sessions
        SET title = :title, db_updated_at = :updatedAt
        WHERE id = :sessionId
    """,
    )
    suspend fun updateSessionTitle(
        sessionId: String,
        title: String,
        updatedAt: Long = System.currentTimeMillis(),
    )

    /**
     * 更新会话的最后活跃时间
     */
    @Query(
        """
        UPDATE chat_sessions
        SET last_active_at = :lastActiveAt, db_updated_at = :updatedAt
        WHERE id = :sessionId
    """,
    )
    suspend fun updateLastActiveTime(
        sessionId: String,
        lastActiveAt: Long = System.currentTimeMillis(),
        updatedAt: Long = System.currentTimeMillis(),
    )

    /**
     * 更新会话的消息数量
     */
    @Query(
        """
        UPDATE chat_sessions
        SET message_count = :messageCount, db_updated_at = :updatedAt
        WHERE id = :sessionId
    """,
    )
    suspend fun updateMessageCount(
        sessionId: String,
        messageCount: Int,
        updatedAt: Long = System.currentTimeMillis(),
    )

    /**
     * 更新会话概要
     */
    @Query(
        """
        UPDATE chat_sessions
        SET summary = :summary, db_updated_at = :updatedAt
        WHERE id = :sessionId
    """,
    )
    suspend fun updateSessionSummary(
        sessionId: String,
        summary: String,
        updatedAt: Long = System.currentTimeMillis(),
    )

    /**
     * 归档会话
     */
    @Query(
        """
        UPDATE chat_sessions
        SET status = ${ChatSessionEntity.STATUS_ARCHIVED},
            is_active = 0,
            last_active_at = :lastActiveAt,
            db_updated_at = :updatedAt
        WHERE id = :sessionId
    """,
    )
    suspend fun archiveSession(
        sessionId: String,
        lastActiveAt: Long = System.currentTimeMillis(),
        updatedAt: Long = System.currentTimeMillis(),
    )

    /**
     * 删除会话（软删除）
     */
    @Query(
        """
        UPDATE chat_sessions
        SET status = ${ChatSessionEntity.STATUS_DELETED},
            is_active = 0,
            last_active_at = :lastActiveAt,
            db_updated_at = :updatedAt
        WHERE id = :sessionId
    """,
    )
    suspend fun deleteSession(
        sessionId: String,
        lastActiveAt: Long = System.currentTimeMillis(),
        updatedAt: Long = System.currentTimeMillis(),
    )

    /**
     * 永久删除会话（硬删除）
     */
    @Query("DELETE FROM chat_sessions WHERE id = :sessionId")
    suspend fun permanentlyDeleteSession(sessionId: String)

    /**
     * 获取用户的会话统计信息
     */
    @Query(
        """
        SELECT
            COUNT(*) as total,
            SUM(CASE WHEN status = ${ChatSessionEntity.STATUS_ACTIVE} THEN 1 ELSE 0 END) as active,
            SUM(CASE WHEN status = ${ChatSessionEntity.STATUS_ARCHIVED} THEN 1 ELSE 0 END) as archived,
            SUM(message_count) as totalMessages,
            SUM(CASE WHEN created_at > :recentThreshold THEN 1 ELSE 0 END) as recent
        FROM chat_sessions
        WHERE user_id = :userId
        AND status != ${ChatSessionEntity.STATUS_DELETED}
    """,
    )
    suspend fun getSessionStats(
        userId: String,
        recentThreshold: Long = System.currentTimeMillis() - 7 * 24 * 60 * 60 * 1000L, // 7天前
    ): SessionStatsResult?

    /**
     * 清理旧会话
     */
    @Query(
        """
        UPDATE chat_sessions
        SET status = ${ChatSessionEntity.STATUS_DELETED},
            is_active = 0,
            db_updated_at = :updatedAt
        WHERE user_id = :userId
        AND last_active_at < :threshold
        AND status = ${ChatSessionEntity.STATUS_ARCHIVED}
    """,
    )
    suspend fun cleanupOldSessions(
        userId: String,
        threshold: Long,
        updatedAt: Long = System.currentTimeMillis(),
    ): Int

    /**
     * 获取会话数量
     */
    @Query(
        """
        SELECT COUNT(*) FROM chat_sessions
        WHERE user_id = :userId
        AND status != ${ChatSessionEntity.STATUS_DELETED}
    """,
    )
    suspend fun getSessionCount(userId: String): Int

    /**
     * 检查会话是否存在
     */
    @Query("SELECT COUNT(*) > 0 FROM chat_sessions WHERE id = :sessionId")
    suspend fun sessionExists(sessionId: String): Boolean

    /**
     * 当有新消息时，更新会话的最后活动时间和消息计数
     */
    @Query(
        """
        UPDATE chat_sessions
        SET last_active_at = :timestamp,
            message_count = message_count + 1,
            db_updated_at = :timestamp
        WHERE id = :sessionId
    """,
    )
    suspend fun updateSessionOnNewMessage(sessionId: String, timestamp: Long)
}

/**
 * 会话统计查询结果
 */
data class SessionStatsResult(
    val total: Int,
    val active: Int,
    val archived: Int,
    val totalMessages: Int,
    val recent: Int,
)
