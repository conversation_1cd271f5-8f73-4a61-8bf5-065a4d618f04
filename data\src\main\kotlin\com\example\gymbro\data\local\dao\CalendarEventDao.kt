package com.example.gymbro.data.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.example.gymbro.data.local.entity.CalendarEventEntity
import kotlinx.coroutines.flow.Flow

/**
 * 日历事件数据访问对象
 *
 * 🎯 功能特性：
 * - 访问AppDatabase中的calendar_events表
 * - 支持日期范围查询和CRUD操作
 * - 为Calendar功能提供数据访问层
 * - 集成自动保存和Function Call支持
 *
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0 (Calendar Persistence Implementation)
 */
@Dao
interface CalendarEventDao {

    // === 基础CRUD操作 ===

    @Query("SELECT * FROM calendar_events WHERE id = :eventId")
    suspend fun getEventById(eventId: String): CalendarEventEntity?

    @Query("SELECT * FROM calendar_events WHERE user_id = :userId ORDER BY date ASC")
    fun getEventsByUser(userId: String): Flow<List<CalendarEventEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertEvent(event: CalendarEventEntity)

    @Update
    suspend fun updateEvent(event: CalendarEventEntity)

    @Query("DELETE FROM calendar_events WHERE id = :eventId")
    suspend fun deleteEvent(eventId: String)

    // === 日期查询操作 ===

    @Query(
        """
        SELECT * FROM calendar_events
        WHERE user_id = :userId
        AND date >= :startDate
        AND date <= :endDate
        ORDER BY date ASC
    """,
    )
    suspend fun getEventsByDateRange(
        userId: String,
        startDate: Long,
        endDate: Long,
    ): List<CalendarEventEntity>

    @Query(
        """
        SELECT * FROM calendar_events
        WHERE user_id = :userId
        AND date >= :dayStart
        AND date < :dayEnd
        ORDER BY date ASC
    """,
    )
    suspend fun getEventsByDate(
        userId: String,
        dayStart: Long,
        dayEnd: Long = dayStart + (24 * 60 * 60 * 1000),
    ): List<CalendarEventEntity>

    // === 状态查询操作 ===

    @Query(
        """
        SELECT * FROM calendar_events
        WHERE user_id = :userId
        AND is_completed = 1
        AND date >= :startDate
        AND date <= :endDate
    """,
    )
    suspend fun getCompletedEvents(
        userId: String,
        startDate: Long,
        endDate: Long,
    ): List<CalendarEventEntity>

    @Query(
        """
        SELECT * FROM calendar_events
        WHERE user_id = :userId
        AND is_completed = 0
        AND cancelled = 0
        AND date >= :currentTime
        ORDER BY date ASC
    """,
    )
    suspend fun getUpcomingEvents(userId: String, currentTime: Long): List<CalendarEventEntity>

    // === 工作量统计查询 ===

    @Query(
        """
        SELECT COUNT(*) FROM calendar_events
        WHERE user_id = :userId
        AND date >= :startDate
        AND date <= :endDate
    """,
    )
    suspend fun getEventCount(userId: String, startDate: Long, endDate: Long): Int

    @Query(
        """
        SELECT COUNT(*) FROM calendar_events
        WHERE user_id = :userId
        AND is_completed = 1
        AND date >= :startDate
        AND date <= :endDate
    """,
    )
    suspend fun getCompletedCount(userId: String, startDate: Long, endDate: Long): Int

    @Query(
        """
        SELECT IFNULL(SUM(duration_minutes), 0) FROM calendar_events
        WHERE user_id = :userId
        AND is_completed = 1
        AND date >= :startDate
        AND date <= :endDate
    """,
    )
    suspend fun getTotalDuration(userId: String, startDate: Long, endDate: Long): Int

    // === 特定类型查询 ===

    @Query(
        """
        SELECT * FROM calendar_events
        WHERE user_id = :userId
        AND workout_id = :workoutId
    """,
    )
    suspend fun getEventsByWorkout(userId: String, workoutId: String): List<CalendarEventEntity>

    @Query(
        """
        SELECT * FROM calendar_events
        WHERE user_id = :userId
        AND event_type = :eventType
        AND date >= :startDate
        AND date <= :endDate
    """,
    )
    suspend fun getEventsByType(
        userId: String,
        eventType: String,
        startDate: Long,
        endDate: Long,
    ): List<CalendarEventEntity>

    // === 同步相关查询 ===

    @Query("SELECT * FROM calendar_events WHERE is_synced = 0")
    suspend fun getUnsyncedEvents(): List<CalendarEventEntity>

    @Query("UPDATE calendar_events SET is_synced = 1 WHERE id = :eventId")
    suspend fun markAsSynced(eventId: String)

    @Query("UPDATE calendar_events SET is_synced = 0 WHERE id = :eventId")
    suspend fun markAsUnsynced(eventId: String)

    // === 批量操作 ===

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertEvents(events: List<CalendarEventEntity>)

    @Query("DELETE FROM calendar_events WHERE user_id = :userId AND date < :cutoffDate")
    suspend fun deleteOldEvents(userId: String, cutoffDate: Long)

    @Query(
        """
        UPDATE calendar_events
        SET cancelled = 1, modified_at = :modifiedAt
        WHERE user_id = :userId
        AND workout_id = :workoutId
    """,
    )
    suspend fun cancelEventsByWorkout(userId: String, workoutId: String, modifiedAt: Long)
}
