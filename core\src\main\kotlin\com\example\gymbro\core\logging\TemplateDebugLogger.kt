package com.example.gymbro.core.logging

import timber.log.Timber

/**
 * 🔥 Template 重量数据重置问题专用调试日志工具
 *
 * 功能：
 * - 专门用于调试 Template 重量数据重置问题
 * - 自动过滤噪音日志，只显示关键信息
 * - 支持运行时开关控制
 */
object TemplateDebugLogger {

    /**
     * 关键数据流日志 - 用于追踪重量数据的保存和加载
     */
    fun criticalLoad(message: String) {
        if (LoggingConfig.CRITICAL_DATA_LOGGING_ENABLED) {
            Timber.tag("CRITICAL-LOAD").d(message)
        }
    }

    /**
     * 关键数据库操作日志 - 用于追踪数据库读写
     */
    fun criticalDb(message: String) {
        if (LoggingConfig.CRITICAL_DATA_LOGGING_ENABLED) {
            Timber.tag("CRITICAL-DB").d(message)
        }
    }

    /**
     * 关键保存操作日志 - 用于追踪数据保存过程
     */
    fun criticalSave(message: String) {
        if (LoggingConfig.CRITICAL_DATA_LOGGING_ENABLED) {
            Timber.tag("CRITICAL-SAVE").d(message)
        }
    }

    /**
     * JSON 解析日志 - 用于追踪 JSON 序列化/反序列化
     */
    fun jsonParse(message: String) {
        if (LoggingConfig.TEMPLATE_WEIGHT_DEBUG_ENABLED) {
            Timber.tag("JSON-PARSE").d(message)
        }
    }

    /**
     * Template 重量调试日志 - 用于追踪重量数据变化
     */
    fun weightDebug(message: String) {
        if (LoggingConfig.TEMPLATE_WEIGHT_DEBUG_ENABLED) {
            Timber.tag("TEMPLATE-WEIGHT-DEBUG").d(message)
        }
    }

    /**
     * 错误日志 - 始终显示
     */
    fun error(message: String, throwable: Throwable? = null) {
        Timber.tag("ERROR").e(throwable, message)
    }

    /**
     * 警告日志 - 始终显示
     */
    fun warn(message: String, throwable: Throwable? = null) {
        Timber.tag("ERROR").w(throwable, message)
    }

    /**
     * 运行时控制开关
     */
    object Controls {
        /**
         * 启用/禁用 MVI 噪音日志
         */
        fun setMviLogging(enabled: Boolean) {
            LoggingConfig.MVI_LOGGING_ENABLED = enabled
        }

        /**
         * 启用/禁用 System.out 噪音日志
         */
        fun setSystemOutLogging(enabled: Boolean) {
            LoggingConfig.SYSTEM_OUT_LOGGING_ENABLED = enabled
        }

        /**
         * 启用/禁用 Template 重量调试日志
         */
        fun setTemplateWeightDebug(enabled: Boolean) {
            LoggingConfig.TEMPLATE_WEIGHT_DEBUG_ENABLED = enabled
        }

        /**
         * 启用/禁用关键数据流日志
         */
        fun setCriticalDataLogging(enabled: Boolean) {
            LoggingConfig.CRITICAL_DATA_LOGGING_ENABLED = enabled
        }

        /**
         * 一键设置：只显示 Template 调试相关日志
         */
        fun enableOnlyTemplateDebug() {
            LoggingConfig.MVI_LOGGING_ENABLED = false
            LoggingConfig.SYSTEM_OUT_LOGGING_ENABLED = false
            LoggingConfig.TEMPLATE_WEIGHT_DEBUG_ENABLED = true
            LoggingConfig.CRITICAL_DATA_LOGGING_ENABLED = true
        }

        /**
         * 一键设置：显示所有日志（调试模式）
         */
        fun enableAllLogging() {
            LoggingConfig.MVI_LOGGING_ENABLED = true
            LoggingConfig.SYSTEM_OUT_LOGGING_ENABLED = true
            LoggingConfig.TEMPLATE_WEIGHT_DEBUG_ENABLED = true
            LoggingConfig.CRITICAL_DATA_LOGGING_ENABLED = true
        }

        /**
         * 一键设置：关闭所有调试日志（生产模式）
         */
        fun disableAllDebugLogging() {
            LoggingConfig.MVI_LOGGING_ENABLED = false
            LoggingConfig.SYSTEM_OUT_LOGGING_ENABLED = false
            LoggingConfig.TEMPLATE_WEIGHT_DEBUG_ENABLED = false
            LoggingConfig.CRITICAL_DATA_LOGGING_ENABLED = false
        }
    }
}
