package com.example.gymbro.app.extensions

import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import timber.log.Timber

/**
 * ModernResult扩展函数，简化错误处理和成功处理的模板代码
 */

/**
 * 简化的fold操作，处理成功和错误情况
 * @param onSuccess 成功时的处理函数
 * @param onError 错误时的处理函数
 */
suspend inline fun <T> ModernResult<T>.fold(
    crossinline onSuccess: suspend (T) -> Unit,
    crossinline onError: suspend (ModernDataError) -> Unit,
) {
    when (this) {
        is ModernResult.Success -> onSuccess(data)
        is ModernResult.Error -> onError(error)
        is ModernResult.Loading -> {
            // Loading状态通常在UI层处理，这里不做特殊处理
        }
    }
}

/**
 * 简化的fold操作，带默认错误处理
 * @param onSuccess 成功时的处理函数
 */
suspend inline fun <T> ModernResult<T>.foldWithDefaultError(
    crossinline onSuccess: suspend (T) -> Unit,
) {
    fold(
        onSuccess = onSuccess,
        onError = { error ->
            Timber.w("ModernResult处理错误: ${error.operationName} - ${error.message}")
        },
    )
}

/**
 * 获取数据或默认值
 * @param defaultValue 当结果为错误或Loading时返回的默认值
 * @return 成功时返回数据，否则返回默认值
 */
fun <T> ModernResult<T>.getOrDefault(defaultValue: T): T =
    when (this) {
        is ModernResult.Success -> data
        is ModernResult.Error -> {
            Timber.w("ModernResult获取默认值: ${error.operationName} - ${error.message}")
            defaultValue
        }
        is ModernResult.Loading -> defaultValue
    }

/**
 * 获取数据或null
 * @return 成功时返回数据，否则返回null
 */
fun <T> ModernResult<T>.getOrNull(): T? =
    when (this) {
        is ModernResult.Success -> data
        is ModernResult.Error -> {
            Timber.d("ModernResult返回null: ${error.operationName}")
            null
        }
        is ModernResult.Loading -> null
    }

/**
 * 检查是否为成功状态
 */
fun <T> ModernResult<T>.isSuccess(): Boolean = this is ModernResult.Success

/**
 * 检查是否为错误状态
 */
fun <T> ModernResult<T>.isError(): Boolean = this is ModernResult.Error

/**
 * 检查是否为加载状态
 */
fun <T> ModernResult<T>.isLoading(): Boolean = this is ModernResult.Loading

/**
 * 映射成功结果到新类型
 * @param transform 转换函数
 * @return 转换后的ModernResult
 */
inline fun <T, R> ModernResult<T>.map(crossinline transform: (T) -> R): ModernResult<R> =
    when (this) {
        is ModernResult.Success -> ModernResult.Success(transform(data))
        is ModernResult.Error -> ModernResult.Error(error)
        is ModernResult.Loading -> ModernResult.Loading
    }

/**
 * 优雅降级处理 - 当操作失败时使用默认行为
 * @param logTag 日志标签
 * @param fallbackAction 降级时执行的操作
 */
suspend inline fun <T> ModernResult<T>.gracefulFallback(
    logTag: String,
    crossinline fallbackAction: suspend () -> Unit,
) {
    when (this) {
        is ModernResult.Success -> {
            // 成功时不需要降级
        }
        is ModernResult.Error -> {
            Timber.w("[$logTag] 执行优雅降级: ${error.operationName} - ${error.message}")
            fallbackAction()
        }
        is ModernResult.Loading -> {
            // Loading状态通常不需要降级
        }
    }
}

/**
 * 优雅降级处理 - 使用默认日志标签
 * @param fallbackAction 降级时执行的操作
 */
suspend inline fun <T> ModernResult<T>.gracefulFallback(
    crossinline fallbackAction: suspend () -> Unit,
) {
    gracefulFallback("GracefulFallback", fallbackAction)
}
