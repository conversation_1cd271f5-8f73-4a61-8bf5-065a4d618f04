package com.example.gymbro.core.network.rest.interceptors

import com.example.gymbro.core.network.monitor.NetworkMonitor
import com.example.gymbro.core.network.monitor.NetworkState
import okhttp3.Interceptor
import okhttp3.Response
import timber.log.Timber
import java.io.IOException

/**
 * 网络状态拦截器
 *
 * 在网络不可用时快速失败，避免无效的网络请求
 */
class NetworkStatusInterceptor(
    private val networkMonitor: NetworkMonitor,
) : Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()

        // 检查网络状态
        when (val networkState = networkMonitor.networkState.value) {
            is NetworkState.Unavailable -> {
                Timber.w("🚫 网络不可用，请求被拦截: ${request.url}")
                throw IOException("Network is unavailable")
            }
            is NetworkState.Lost -> {
                Timber.w("🚫 网络连接丢失，请求被拦截: ${request.url}")
                throw IOException("Network connection lost")
            }
            is NetworkState.Unknown -> {
                Timber.w("⚠️ 网络状态未知，继续请求: ${request.url}")
                // 网络状态未知时继续请求，让底层网络栈处理
            }
            is NetworkState.Connecting -> {
                Timber.d("🔄 网络连接中，继续请求: ${request.url}")
                // 连接中时继续请求
            }
            is NetworkState.Available -> {
                Timber.v("🌐 网络可用 (${networkState.type}), 继续请求: ${request.url}")
                // 网络可用，正常处理
            }
        }

        return chain.proceed(request)
    }
}
