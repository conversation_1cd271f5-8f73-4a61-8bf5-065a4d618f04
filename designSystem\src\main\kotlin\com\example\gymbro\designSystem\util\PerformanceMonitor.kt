package com.example.gymbro.designSystem.util

import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalDensity
import kotlinx.coroutines.delay
import kotlin.system.measureTimeMillis

/**
 * GymBro性能监控工具
 *
 * 提供帧率监测、内存监控和计算时间测量
 * 用于3D渲染组件的性能感知和自适应质量控制
 */

/**
 * 性能监控数据类
 */
data class GymBroPerformanceMetrics(
    val frameRate: Float = 60f,
    val averageFrameTime: Float = 16.67f, // ms
    val memoryPressure: MemoryPressureLevel = MemoryPressureLevel.LOW,
    val computationTime: Float = 0f, // ms
    val qualityRecommendation: Float = 1.0f, // 0.5f-2.0f
)

/**
 * 内存压力等级
 */
enum class MemoryPressureLevel {
    LOW, // 内存充足
    MEDIUM, // 内存适中
    HIGH, // 内存紧张
    CRITICAL, // 内存严重不足
}

/**
 * 性能质量等级
 */
enum class GymBroQualityLevel(val factor: Float, val description: String) {
    ULTRA_LOW(0.3f, "超低质量 - 最大兼容性"),
    LOW(0.5f, "低质量 - 流畅优先"),
    MEDIUM(0.8f, "中等质量 - 平衡模式"),
    HIGH(1.0f, "高质量 - 标准模式"),
    ULTRA_HIGH(1.5f, "超高质量 - 性能设备"),
    EXTREME(2.0f, "极致质量 - 旗舰设备"),
}

/**
 * 性能监控状态管理
 */
@Composable
fun rememberGymBroPerformanceMonitor(
    targetFrameRate: Float = 60f,
    monitoringEnabled: Boolean = true,
): GymBroPerformanceState {
    return remember {
        GymBroPerformanceState(targetFrameRate, monitoringEnabled)
    }
}

/**
 * 性能监控状态类
 */
@Stable
class GymBroPerformanceState(
    private val targetFrameRate: Float,
    private val monitoringEnabled: Boolean,
) {
    private var _metrics by mutableStateOf(GymBroPerformanceMetrics())
    val metrics: GymBroPerformanceMetrics get() = _metrics

    private val frameTimeHistory = mutableListOf<Float>()
    private val maxHistorySize = 30 // 保持30帧的历史记录

    private var lastFrameTime = System.nanoTime()

    /**
     * 记录帧时间
     */
    fun recordFrame() {
        if (!monitoringEnabled) return

        val currentTime = System.nanoTime()
        val frameTime = (currentTime - lastFrameTime) / 1_000_000f // 转换为毫秒
        lastFrameTime = currentTime

        // 更新帧时间历史
        frameTimeHistory.add(frameTime)
        if (frameTimeHistory.size > maxHistorySize) {
            frameTimeHistory.removeAt(0)
        }

        // 计算平均帧率和帧时间
        if (frameTimeHistory.size >= 5) {
            val averageFrameTime = frameTimeHistory.average().toFloat()
            val currentFrameRate = 1000f / averageFrameTime

            _metrics = _metrics.copy(
                frameRate = currentFrameRate,
                averageFrameTime = averageFrameTime,
                qualityRecommendation = calculateQualityRecommendation(currentFrameRate, averageFrameTime),
            )
        }
    }

    /**
     * 记录计算时间
     */
    fun recordComputationTime(timeMs: Float) {
        if (!monitoringEnabled) return

        _metrics = _metrics.copy(
            computationTime = timeMs,
            qualityRecommendation = calculateQualityRecommendation(_metrics.frameRate, _metrics.averageFrameTime),
        )
    }

    /**
     * 更新内存压力
     */
    fun updateMemoryPressure() {
        if (!monitoringEnabled) return

        val runtime = Runtime.getRuntime()
        val maxMemory = runtime.maxMemory()
        val totalMemory = runtime.totalMemory()
        val freeMemory = runtime.freeMemory()
        val usedMemory = totalMemory - freeMemory
        val memoryUsageRatio = usedMemory.toFloat() / maxMemory.toFloat()

        val pressureLevel = when {
            memoryUsageRatio < 0.5f -> MemoryPressureLevel.LOW
            memoryUsageRatio < 0.7f -> MemoryPressureLevel.MEDIUM
            memoryUsageRatio < 0.85f -> MemoryPressureLevel.HIGH
            else -> MemoryPressureLevel.CRITICAL
        }

        _metrics = _metrics.copy(memoryPressure = pressureLevel)
    }

    /**
     * 计算质量建议
     */
    private fun calculateQualityRecommendation(frameRate: Float, frameTime: Float): Float {
        val targetFrameTime = 1000f / targetFrameRate

        return when {
            // 性能过剩，可以提升质量
            frameRate > targetFrameRate * 1.2f && frameTime < targetFrameTime * 0.8f -> {
                (_metrics.qualityRecommendation * 1.1f).coerceAtMost(2.0f)
            }
            // 性能良好，维持当前质量
            frameRate >= targetFrameRate * 0.9f -> {
                _metrics.qualityRecommendation
            }
            // 性能不足，降低质量
            frameRate < targetFrameRate * 0.8f || frameTime > targetFrameTime * 1.3f -> {
                (_metrics.qualityRecommendation * 0.9f).coerceAtLeast(0.3f)
            }
            // 性能严重不足，大幅降低质量
            frameRate < targetFrameRate * 0.6f -> {
                (_metrics.qualityRecommendation * 0.7f).coerceAtLeast(0.3f)
            }
            else -> _metrics.qualityRecommendation
        }
    }

    /**
     * 获取推荐的质量等级
     */
    fun getRecommendedQualityLevel(): GymBroQualityLevel {
        return when {
            _metrics.qualityRecommendation >= 1.8f -> GymBroQualityLevel.EXTREME
            _metrics.qualityRecommendation >= 1.3f -> GymBroQualityLevel.ULTRA_HIGH
            _metrics.qualityRecommendation >= 0.9f -> GymBroQualityLevel.HIGH
            _metrics.qualityRecommendation >= 0.7f -> GymBroQualityLevel.MEDIUM
            _metrics.qualityRecommendation >= 0.4f -> GymBroQualityLevel.LOW
            else -> GymBroQualityLevel.ULTRA_LOW
        }
    }

    /**
     * 重置监控数据
     */
    fun reset() {
        frameTimeHistory.clear()
        _metrics = GymBroPerformanceMetrics()
        lastFrameTime = System.nanoTime()
    }
}

/**
 * 计算时间测量工具
 */
inline fun <T> measureComputationTime(
    performanceState: GymBroPerformanceState?,
    block: () -> T,
): T {
    return if (performanceState != null) {
        var result: T
        val timeMs = measureTimeMillis {
            result = block()
        }.toFloat()
        performanceState.recordComputationTime(timeMs)
        result
    } else {
        block()
    }
}

/**
 * 自适应质量控制Composable
 */
@Composable
fun GymBroAdaptiveQuality(
    performanceMonitor: GymBroPerformanceState,
    content: @Composable (quality: Float, qualityLevel: GymBroQualityLevel) -> Unit,
) {
    val density = LocalDensity.current

    // 定期更新内存压力
    LaunchedEffect(Unit) {
        while (true) {
            performanceMonitor.updateMemoryPressure()
            delay(1000) // 每秒更新一次内存状态
        }
    }

    // 记录帧时间
    LaunchedEffect(performanceMonitor.metrics.frameRate) {
        performanceMonitor.recordFrame()
    }

    val currentQuality = performanceMonitor.metrics.qualityRecommendation
    val qualityLevel = performanceMonitor.getRecommendedQualityLevel()

    content(currentQuality, qualityLevel)
}
