# Core Network Module - 智能协议选择架构树状图

## 📁 模块文件结构

```
core-network/
├── 📋 build.gradle.kts                    # 模块构建配置
├── 📄 README.md                           # 模块说明文档
├── 📄 TREE.md                             # 架构树状图（本文件）
├── 📄 INTERFACES.md                       # 接口契约文档
│
├── 📂 src/main/kotlin/com/example/gymbro/core/network/
│   │
│   ├── 📂 protocol/                       # 🔥 协议选择核心模块
│   │   ├── 🧠 AdaptiveStreamClient.kt     # 智能协议选择客户端
│   │   ├── 🔍 ProtocolDetector.kt         # 协议检测器
│   │   └── 📋 SupportedProtocol.kt        # 支持的协议枚举
│   │
│   ├── 📂 ws/                             # WebSocket专用模块（指定模型）
│   │   ├── 🔌 LlmStreamClient.kt          # 流式客户端统一接口
│   │   ├── ⚡ LlmStreamClientImpl.kt       # WebSocket实现（待集成）
│   │   ├── 🔄 WsState.kt                  # WebSocket状态枚举
│   │   └── 💾 TokenOffsetStore.kt         # 断点续传存储接口
│   │
│   ├── 📂 rest/                           # REST API模块
│   │   ├── 🌐 RestClient.kt               # REST客户端接口
│   │   ├── ⚡ RestClientImpl.kt            # REST客户端实现
│   │   ├── 📊 ApiResult.kt                # API结果统一封装
│   │   ├── 🛡️ SafeApiCall.kt              # 安全API调用工具
│   │   │
│   │   └── 📂 interceptors/               # 拦截器链模块
│   │       ├── 🔑 AuthInterceptor.kt      # 认证拦截器
│   │       ├── 📡 NetworkStatusInterceptor.kt # 网络状态拦截器
│   │       ├── 🔄 RetryInterceptor.kt     # 重试拦截器
│   │       └── 📝 SafeLoggingInterceptor.kt # 安全日志拦截器
│   │
│   ├── 📂 config/                         # 🔥 配置管理模块
│   │   ├── ⚙️ NetworkConfig.kt            # 网络配置数据类
│   │   ├── 🎛️ NetworkConfigManager.kt     # 配置管理器
│   │   └── 🔧 NetworkConfigProvider.kt    # 配置提供者接口
│   │
│   ├── 📂 monitor/                        # 网络监控模块
│   │   ├── 📊 NetworkMonitor.kt           # 网络监控接口
│   │   ├── 📱 AndroidNetworkMonitor.kt    # Android平台网络监控实现
│   │   ├── 🐕 NetworkWatchdog.kt          # 网络监控狗（事件流）
│   │   └── ⚡ NetworkWatchdogImpl.kt       # 网络监控实现
│   │
│   ├── 📂 router/                         # 🔥 多轮对话路由模块（新增）
│   │   ├── 🎯 ConversationScope.kt        # 独立对话作用域
│   │   └── 🚀 TokenRouter.kt              # 对话路由管理中心
│   │
│   └── 📂 di/                             # 🔥 统一依赖注入配置
│       └── 🏗️ CoreNetworkModule.kt        # 统一DI模块
│
├── 📂 src/test/kotlin/                    # 单元测试
│   ├── 🧪 AdaptiveStreamClientTest.kt     # 智能协议选择测试
│   ├── 🧪 ProtocolDetectorTest.kt         # 协议检测测试
│   ├── 🧪 NetworkConfigTest.kt            # 配置管理测试
│   ├── 🧪 RestClientImplTest.kt           # REST客户端测试
│   ├── 🧪 NetworkWatchdogTest.kt          # 网络监控测试
│   ├── 📂 router/                         # 🔥 多轮对话路由测试（新增）
│   │   └── 🧪 TokenRouterTest.kt          # TokenRouter单元测试
│   └── 📂 interceptors/                   # 拦截器测试
│       ├── 🧪 AuthInterceptorTest.kt
│       ├── 🧪 NetworkStatusInterceptorTest.kt
│       ├── 🧪 RetryInterceptorTest.kt
│       └── 🧪 SafeLoggingInterceptorTest.kt
│
└── 📂 docs/                               # 文档目录
    ├── 📋 智能协议选择设计.md              # 协议选择架构设计
    ├── 📊 workflow.md                     # 工作流程记录
    ├── 🔍 coach-ai-network-diagnosis.md   # Coach模块网络诊断
    ├── 📋 websock+Okhttp.md               # WebSocket+OkHttp集成文档
    ├── 🏗️ 网络模块接口设计.md              # 接口设计文档
    └── 📋 任务总览.md                      # 任务总览
```

## 🏗️ 智能协议选择架构

### 1. 协议选择核心层 (Protocol Selection Core)
```
📦 智能协议选择
├── 🧠 AdaptiveStreamClient     # 智能协议选择客户端
│   ├── 🔍 协议检测逻辑
│   ├── 🎯 模型类型识别
│   ├── 📡 HTTP+SSE默认协议
│   ├── 🔗 WebSocket专用协议
│   └── 📄 HTTP基础降级协议
├── 🔍 ProtocolDetector         # 协议检测器
│   ├── 🌐 服务器能力检测
│   ├── ⏱️ 超时处理
│   └── 💾 检测结果缓存
└── 📋 SupportedProtocol        # 协议枚举
    ├── HTTP_SSE (默认)
    ├── HTTP_BASIC (降级)
    └── WEBSOCKET (专用)
```

### 2. 配置管理层 (Configuration Management)
```
📦 零硬编码配置
├── ⚙️ NetworkConfig            # 网络配置数据类
│   ├── wsBase: String
│   ├── restBase: String
│   ├── apiKey: String
│   └── validate(): Boolean
├── 🎛️ NetworkConfigManager     # 配置管理器
│   ├── getCurrentConfig()
│   ├── updateConfig()
│   └── observeConfig()
└── 🔧 NetworkConfigProvider    # 配置提供者接口
    ├── getConfig()
    └── observeConfig()
```

### 3. 协议实现层 (Protocol Implementation)
```
📦 多协议实现
├── 📡 HTTP+SSE实现 (默认)
│   ├── OkHttp + EventSource
│   ├── 流式数据解析
│   ├── 自动重连机制
│   └── 错误处理降级
├── 📄 HTTP基础实现 (降级)
│   ├── 标准HTTP请求
│   ├── 模拟流式输出
│   ├── 分块响应处理
│   └── 延迟模拟
└── 🔗 WebSocket实现 (专用)
    ├── 真正的全双工通信
    ├── 专用模型支持
    ├── 热更新配置
    └── 状态机管理
```

### 4. 模型专用配置层 (Model-Specific Configuration)
```
📦 热更新模型配置
├── 🎯 WebSocket专用模型
│   ├── o1-preview
│   ├── o1-mini
│   ├── gpt-4-turbo
│   ├── claude-3-opus
│   └── 🔄 热更新支持
├── 📡 HTTP+SSE默认模型
│   ├── deepseek-chat
│   ├── gpt-3.5-turbo
│   ├── claude-3-haiku
│   └── 其他通用模型
└── 🔧 配置管理
    ├── updateWebSocketSupportedModels()
    ├── getWebSocketSupportedModels()
    └── isWebSocketSupportedModel()
```

### 5. 多轮对话路由层 (Multi-Turn Conversation Routing) 🔥 新增
```
📦 多轮对话架构升级
├── 🎯 ConversationScope           # 独立对话作用域
│   ├── 🔄 MutableSharedFlow<String>    # 独立token流
│   ├── 🔄 MutableSharedFlow<Any>       # 独立事件流
│   ├── 🛡️ SupervisorJob               # 对话隔离
│   ├── 📊 性能监控指标
│   │   ├── totalTokensEmitted
│   │   ├── totalEventsEmitted
│   │   ├── tokenEmissionFailures
│   │   └── eventEmissionFailures
│   ├── 🔍 背压状态检查
│   │   ├── tokenBufferUsage
│   │   ├── eventBufferUsage
│   │   └── 容量预警机制
│   └── ⏰ Idle-Timeout清理
└── 🚀 TokenRouter                 # 对话路由管理中心
    ├── 🗺️ ConcurrentHashMap<String, ConversationScope>
    ├── 🔄 routeToken(messageId, token)
    ├── 🔄 routeEvent(messageId, event)
    ├── 🗑️ releaseScope(messageId)
    ├── 🧹 自动清理机制
    │   ├── 30分钟超时清理
    │   ├── 5分钟间隔检查
    │   └── 最大100个scope限制
    ├── 📊 性能监控指标
    │   ├── totalTokensRouted
    │   ├── totalEventsRouted
    │   ├── totalScopesCreated
    │   ├── totalScopesReleased
    │   └── 清理统计
    └── 🔍 背压监控
        ├── activeScopeCount
        ├── totalBufferUsage
        ├── averageBufferUsage
        └── isNearCapacity预警
```

## 🔄 智能协议选择数据流

### 协议选择流程
```
🎯 聊天请求 (ChatRequest)
    ↓
🧠 AdaptiveStreamClient.streamChat()
    ↓
🔍 selectOptimalProtocol(model, baseUrl, apiKey)
    ├── 🎯 检查模型类型
    │   ├── WebSocket专用模型? → 🔗 验证WebSocket支持
    │   └── 普通模型? → 📡 使用HTTP+SSE
    ├── 🔍 协议检测
    │   ├── ProtocolDetector.detectSupportedProtocol()
    │   └── 💾 缓存检测结果
    └── 📋 返回最优协议
    ↓
🔀 协议分发
    ├── 🔗 WEBSOCKET → streamChatWithWebSocket()
    ├── 📡 HTTP_SSE → streamChatWithSSE()
    └── 📄 HTTP_BASIC → streamChatWithHttp()
    ↓
📊 Flow<String> 统一流式响应
```

### HTTP+SSE数据流（默认协议）
```
📡 HTTP+SSE请求
    ↓
🔧 构建SSE请求 (stream=true)
    ↓
🌐 OkHttp + EventSource
    ↓
📡 Server-Sent Events
    ├── onOpen() → 连接建立
    ├── onEvent() → 数据接收
    │   ├── 解析SSE数据
    │   ├── 提取content字段
    │   └── 发送到Flow
    ├── onClosed() → 连接关闭
    └── onFailure() → 🔄 自动降级到HTTP基础
    ↓
📊 流式响应 Flow<String>
```

### WebSocket数据流（专用模型）
```
🔗 WebSocket请求 (o1-preview等)
    ↓
🎯 模型类型识别
    ↓
🔍 WebSocket支持验证
    ↓
🔗 WebSocket连接建立
    ↓
📦 真正的全双工通信
    ├── 发送请求帧
    ├── 接收响应帧
    ├── 状态机管理
    └── 断点续传支持
    ↓
📊 流式响应 Flow<String>
```

### HTTP基础数据流（降级方案）
```
📄 HTTP基础请求 (stream=false)
    ↓
🌐 标准HTTP POST请求
    ↓
📊 完整响应接收
    ↓
🔄 模拟流式输出
    ├── 按字符分块 (chunkSize=10)
    ├── 添加延迟 (50ms)
    └── 逐块发送到Flow
    ↓
📊 模拟流式响应 Flow<String>
```

### 多轮对话路由数据流（🔥 新增架构）
```
🎯 AI响应开始 (AiCoachViewModel)
    ↓
🚀 tokenRouter.routeToken(messageId, token)
    ↓
🎯 ConversationScope.emitToken(token)
    ↓
📡 独立token流 MutableSharedFlow<String>
    ↓
🔄 parseTokenStream(messageId, tokens, onEvent)
    ├── 🧠 无状态解析逻辑
    ├── 🏷️ <think> → phase id="perthink"
    ├── 📋 <phase> 标签处理
    ├── 📄 <final> 内容缓冲
    └── 🔄 SemanticEvent生成
    ↓
🎯 ConversationScope.emitEvent(semanticEvent)
    ↓
📡 独立事件流 MutableSharedFlow<Any>
    ↓
🗺️ DomainMapper.mapToThinkingEvents(semanticEvent)
    ↓
🎯 ThinkingBoxInstance.handleThinkingEvent()
    ↓
📊 UiState更新 (完全隔离)
    ↓
🗑️ tokenRouter.releaseScope(messageId) (流结束时)
```

### 多对话并发隔离示意图
```
📦 TokenRouter (路由中心)
├── 🎯 ConversationScope-1 (messageId: "conv-1-resp-123")
│   ├── 📡 独立token流 → parseTokenStream-1 → 事件流-1
│   └── 🎯 ThinkingBoxInstance-1 → UiState-1
├── 🎯 ConversationScope-2 (messageId: "conv-2-resp-456")
│   ├── 📡 独立token流 → parseTokenStream-2 → 事件流-2
│   └── 🎯 ThinkingBoxInstance-2 → UiState-2
└── 🎯 ConversationScope-N (messageId: "conv-N-resp-789")
    ├── 📡 独立token流 → parseTokenStream-N → 事件流-N
    └── 🎯 ThinkingBoxInstance-N → UiState-N

🔥 关键特性：
- ✅ 完全状态隔离：每个对话独立的协程作用域
- ✅ 资源自动管理：30分钟超时自动清理
- ✅ 背压控制：BufferOverflow.SUSPEND防止内存溢出
- ✅ 性能监控：完整的指标收集和预警机制
- ✅ 并发安全：ConcurrentHashMap + 线程安全设计
```

## 🎯 依赖关系图

### 模块间依赖
```
📦 shared-models (DTO/数据类)
    ↑
📦 core-network (智能协议选择)
    ↑
📦 di (DI配置装配)
    ↑
📦 data (Repository实现)
    ↑
📦 features (ViewModel+UI)
```

### 内部组件依赖
```
📦 配置层 (NetworkConfig, NetworkConfigManager)
    ↓
📦 协议选择层 (AdaptiveStreamClient, ProtocolDetector)
    ↓
📦 协议实现层 (HTTP+SSE, WebSocket, HTTP基础)
    ↓
📦 工具层 (ApiResult, SafeApiCall)
```

## 🔧 OkHttpClient架构

### 专用客户端配置
```
📦 OkHttp客户端分配
├── 🔧 sse_client (HTTP+SSE专用)
│   ├── EventSource支持
│   ├── 长连接配置
│   ├── 流式数据处理
│   └── 自动重连机制
├── 🌐 rest_client (REST API专用)
│   ├── 标准HTTP配置
│   ├── 拦截器链
│   ├── 重试机制
│   └── 缓存支持
└── 🔗 ws_client (WebSocket专用)
    ├── WebSocket升级支持
    ├── 全双工通信
    ├── 状态机管理
    └── 断点续传
```

## 📊 协议性能对比

### 协议特性对比
```
📋 协议对比表
┌─────────────┬─────────────┬─────────────┬─────────────┐
│    特性     │ WebSocket   │ HTTP+SSE    │ HTTP基础    │
├─────────────┼─────────────┼─────────────┼─────────────┤
│ 实时性      │ ⭐⭐⭐⭐⭐    │ ⭐⭐⭐⭐      │ ⭐⭐         │
│ 稳定性      │ ⭐⭐⭐       │ ⭐⭐⭐⭐⭐    │ ⭐⭐⭐⭐⭐    │
│ 兼容性      │ ⭐⭐⭐       │ ⭐⭐⭐⭐      │ ⭐⭐⭐⭐⭐    │
│ 资源消耗    │ ⭐⭐⭐       │ ⭐⭐⭐⭐      │ ⭐⭐⭐⭐⭐    │
│ 适用场景    │ 高级模型    │ 通用模型    │ 降级备选    │
└─────────────┴─────────────┴─────────────┴─────────────┘
```

### 使用策略
```
📋 协议使用策略
├── 🔗 WebSocket (专用)
│   ├── 适用模型: o1-preview, o1-mini, gpt-4-turbo, claude-3-opus
│   ├── 使用场景: 复杂推理、长时间思考、高级功能
│   └── 优势: 真正全双工、低延迟、状态保持
├── 📡 HTTP+SSE (默认)
│   ├── 适用模型: 大部分通用模型
│   ├── 使用场景: 日常对话、快速响应、稳定传输
│   └── 优势: 稳定可靠、兼容性好、易于调试
└── 📄 HTTP基础 (降级)
    ├── 适用场景: SSE不支持、网络受限、兜底方案
    ├── 使用策略: 自动降级、模拟流式、保证可用性
    └── 优势: 最大兼容性、零依赖、必定可用
```

## 🎯 设计原则

1. **智能选择优先**: 根据模型类型和服务器能力自动选择最优协议
2. **零硬编码**: 所有配置通过NetworkConfigManager动态获取
3. **优雅降级**: 协议不可用时自动降级到备选方案
4. **热更新支持**: 支持运行时更新协议配置和模型列表
5. **统一接口**: 通过LlmStreamClient提供协议透明的统一接口
6. **性能优化**: 针对不同协议特性进行专门优化
7. **监控完备**: 全面的协议选择和性能监控指标
