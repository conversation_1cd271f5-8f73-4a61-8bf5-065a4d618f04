package com.example.gymbro.core.ai.prompt.function

import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Function统一注册表
 *
 * 简化版实现：直接使用 GymBroFunctions 中定义的 Functions
 * 按业务域分组管理，提供智能上下文匹配
 *
 * @since 618重构
 */
@Singleton
class FunctionRegistry @Inject constructor() {

    /**
     * 所有可用的Function定义
     * 直接从 GymBroFunctions 获取，按业务域分组
     */
    private val allFunctions: Map<FunctionDomain, List<FunctionDescriptor>> = mapOf(
        FunctionDomain.EXERCISE to GymBroFunctions.EXERCISE_FUNCTIONS,
        FunctionDomain.TEMPLATE to GymBroFunctions.TEMPLATE_FUNCTIONS,
        FunctionDomain.PLAN to GymBroFunctions.PLAN_FUNCTIONS,
        FunctionDomain.SESSION to GymBroFunctions.SESSION_FUNCTIONS,
    )

    /**
     * 获取所有可用的Functions
     *
     * @since 618重构
     */
    fun getAllFunctions(): List<FunctionDescriptor> {
        return allFunctions.values.flatten()
    }

    /**
     * 根据上下文智能返回相关的Functions
     *
     * @param context AI上下文信息
     * @return 相关的Function列表
     *
     * @since 618重构
     */
    fun getFunctionsForContext(context: AiContext): List<FunctionDescriptor> {
        Timber.d("FunctionRegistry: 根据上下文获取Functions")

        // 根据上下文特征决定需要哪些Functions
        val requiredDomains = mutableSetOf<FunctionDomain>()

        // 分析上下文
        when {
            context.isTemplateCreation -> {
                requiredDomains.add(FunctionDomain.TEMPLATE)
                requiredDomains.add(FunctionDomain.EXERCISE) // 创建模板时需要搜索动作
            }
            context.isActiveSession -> {
                requiredDomains.add(FunctionDomain.SESSION)
                requiredDomains.add(FunctionDomain.EXERCISE) // 训练时可能需要查询动作
            }
            context.isPlanningPhase -> {
                requiredDomains.add(FunctionDomain.PLAN)
                requiredDomains.add(FunctionDomain.TEMPLATE) // 制定计划时可能需要模板
            }
            context.isExerciseQuery -> {
                requiredDomains.add(FunctionDomain.EXERCISE)
            }
            else -> {
                // 默认返回所有Functions
                Timber.d("FunctionRegistry: 无特定上下文，返回所有Functions")
                return getAllFunctions()
            }
        }

        // 收集相关Functions
        val functions = requiredDomains.flatMap { domain ->
            allFunctions[domain] ?: emptyList()
        }

        Timber.d("FunctionRegistry: 返回${functions.size}个Functions，域=$requiredDomains")
        return functions
    }

    /**
     * 根据Function名称获取定义
     *
     * @since 618重构
     */
    fun getFunctionByName(name: String): FunctionDescriptor? {
        return allFunctions.values.flatten().find { it.name == name }
    }

    /**
     * 根据业务域获取Functions
     *
     * @since 618重构
     */
    fun getFunctionsByDomain(domain: FunctionDomain): List<FunctionDescriptor> {
        return allFunctions[domain] ?: emptyList()
    }

    /**
     * 验证Function调用参数（简化版）
     *
     * @since 618重构
     */
    fun validateFunctionCall(
        functionName: String,
        arguments: Map<String, Any>,
    ): ValidationResult {
        val function = getFunctionByName(functionName)
            ?: return ValidationResult(false, "Function '$functionName' not found")

        // 基础验证：Function存在即认为有效
        // 详细的参数验证由具体的执行器处理
        return ValidationResult(true, "Valid")
    }
}

/**
 * Function业务域
 *
 * @since 618重构
 */
enum class FunctionDomain {
    EXERCISE, // 动作库
    TEMPLATE, // 训练模板
    PLAN, // 训练计划
    SESSION, // 训练记录
}

/**
 * AI上下文
 *
 * @since 618重构
 */
data class AiContext(
    val isTemplateCreation: Boolean = false,
    val isActiveSession: Boolean = false,
    val isPlanningPhase: Boolean = false,
    val isExerciseQuery: Boolean = false,
    val userQuery: String = "",
    val metadata: Map<String, Any> = emptyMap(),
)

/**
 * 验证结果
 *
 * @since 618重构
 */
data class ValidationResult(
    val isValid: Boolean,
    val message: String,
)
