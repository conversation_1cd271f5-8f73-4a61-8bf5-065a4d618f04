package com.example.gymbro.core.error.types

// import com.example.gymbro.core.error.recovery.RecoveryStrategy // 可能需要调整路径
import java.io.Serializable

/**
 * 错误元数据
 *
 * 提供类型安全的错误元数据存储和访问机制
 */
class ErrorMetadata private constructor(
    private val data: Map<String, Any>,
) : Serializable {
    /**
     * 获取指定键的元数据值
     *
     * @param key 元数据键
     * @return 元数据值，如果不存在则返回null
     */
    operator fun <T : Any> get(key: MetadataKey<T>): T? {
        val value = data[key.key] ?: return null
        @Suppress("UNCHECKED_CAST")
        return try {
            value as T
        } catch (e: ClassCastException) {
            null
        }
    }

    /**
     * 获取指定键的元数据值，如果不存在则返回键的默认值
     *
     * @param key 带有默认值的元数据键
     * @return 元数据值或键的默认值
     */
    operator fun <T : Any> get(key: MetadataKeyWithDefault<T>): T = get(key.key) ?: key.defaultValue

    /**
     * 检查是否包含指定键
     *
     * @param key 元数据键
     * @return 是否包含该键
     */
    fun <T : Any> contains(key: MetadataKey<T>): Boolean = data.containsKey(key.key)

    /**
     * 获取恢复策略
     *
     * @return 恢复策略或null
     */
//    @Suppress("UNCHECKED_CAST")
//    fun <T> getRecoveryStrategy(): RecoveryStrategy<T>? { // RecoveryStrategy 的路径可能已变
//        val key = MetadataKey.of<RecoveryStrategy<*>>(RECOVERY_STRATEGY_KEY)
//        val strategy = get(key) ?: return null
//        return try {
//            strategy as RecoveryStrategy<T>
//        } catch (e: ClassCastException) {
//            null
//        }
//    }

    /**
     * 转换为Map
     *
     * @return 元数据Map
     */
    fun toMap(): Map<String, Any> = data.toMap()

    /**
     * 检查元数据是否为空
     *
     * @return 元数据是否为空
     */
    fun isEmpty(): Boolean = data.isEmpty()

    /**
     * 检查元数据是否非空
     *
     * @return 元数据是否非空
     */
    fun isNotEmpty(): Boolean = data.isNotEmpty()

    /**
     * 构建器类
     */
    class Builder {
        private val data = mutableMapOf<String, Any>()

        /**
         * 添加元数据
         *
         * @param key 元数据键
         * @param value 元数据值
         * @return 构建器实例
         */
        fun <T : Any> put(
            key: MetadataKey<T>,
            value: T,
        ): Builder {
            data[key.key] = value
            return this
        }

        /**
         * 添加字符串元数据
         *
         * @param key 元数据键
         * @param value 字符串值
         * @return 构建器实例
         */
        fun putString(key: String, value: String): Builder {
            data[key] = value
            return this
        }

        /**
         * 添加布尔元数据
         *
         * @param key 元数据键
         * @param value 布尔值
         * @return 构建器实例
         */
        fun putBoolean(key: String, value: Boolean): Builder {
            data[key] = value
            return this
        }

        /**
         * 添加整数元数据
         *
         * @param key 元数据键
         * @param value 整数值
         * @return 构建器实例
         */
        fun putInt(key: String, value: Int): Builder {
            data[key] = value
            return this
        }

        /**
         * 添加长整型元数据
         *
         * @param key 元数据键
         * @param value 长整型值
         * @return 构建器实例
         */
        fun putLong(key: String, value: Long): Builder {
            data[key] = value
            return this
        }

        /**
         * 添加双精度浮点数元数据
         *
         * @param key 元数据键
         * @param value 双精度浮点数值
         * @return 构建器实例
         */
        fun putDouble(key: String, value: Double): Builder {
            data[key] = value
            return this
        }

        /**
         * 添加浮点数元数据
         *
         * @param key 元数据键
         * @param value 浮点数值
         * @return 构建器实例
         */
        fun putFloat(key: String, value: Float): Builder {
            data[key] = value
            return this
        }

        /**
         * 添加任意类型元数据
         *
         * @param key 元数据键
         * @param value 任意类型值
         * @return 构建器实例
         */
        fun putAny(key: String, value: Any): Builder {
            data[key] = value
            return this
        }

        /**
         * 从Map导入元数据
         *
         * @param map 元数据Map
         * @return 构建器实例
         */
        fun importFromMap(map: Map<String, Any>): Builder {
            data.putAll(map)
            return this
        }

        /**
         * 添加恢复策略
         *
         * @param strategy 恢复策略
         * @return 构建器实例
         */
//        fun <T> withRecoveryStrategy(strategy: RecoveryStrategy<T>): Builder { // RecoveryStrategy 的路径可能已变
//            data[RECOVERY_STRATEGY_KEY] = strategy
//            return this
//        }

        /**
         * 构建ErrorMetadata实例
         *
         * @return ErrorMetadata实例
         */
        fun build(): ErrorMetadata = ErrorMetadata(data.toMap())
    }

    companion object {
        private const val serialVersionUID = 1L

        /**
         * 恢复策略键
         */
        const val RECOVERY_STRATEGY_KEY = "recoveryStrategy"

        /**
         * 创建空的元数据
         *
         * @return 空的ErrorMetadata实例
         */
        fun empty(): ErrorMetadata = ErrorMetadata(emptyMap())

        /**
         * 从Map创建元数据
         *
         * @param map 元数据Map
         * @return ErrorMetadata实例
         */
        fun fromMap(map: Map<String, Any>): ErrorMetadata = ErrorMetadata(map)

        /**
         * 创建构建器
         *
         * @return Builder实例
         */
        fun builder(): Builder = Builder()
    }
}
