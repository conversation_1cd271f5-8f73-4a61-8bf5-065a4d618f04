下面给出一套\*\*“三圈九栈”\*\* 测试与质量保障系统，专门针对你目前的 16 个 Android 模块（以后扩展到 iOS 或 KMP 时也能平滑迁移）。核心设计目标：

* **先能动 →** 任何 PR 都要在 5 分钟内给出结果，不拖慢节奏
* **后能查 →** 重型任务（Mutation / Benchmark / Chaos 等）在 Nightly 或 Release 阶段一次跑干净
* **守护稳定 →** Module Graph、State-Guard、文档校验等“生命线”文件永不可被破坏

---

## ① 三个安全圈 & 对应 CI Job

| 圈层      | 触发时机                          | 主要任务                                                                      | YAML                                                                          | 预计耗时      |
| ------- | ----------------------------- | ------------------------------------------------------------------------- | ----------------------------------------------------------------------------- | --------- |
| **快速圈** | 本地 pre-commit & PR-Validation | ktlint 格式、Detekt 静态分析、Android-Lint、模块依赖 Graph Check                       | `pr-validation.yml` (code-quality job) 已覆盖 ⟶ 再加 Graph-Assert + 变更模块增量执行       | 2-5 min   |
| **完整圈** | `develop` 分支 / 手动             | ciCheck （Lint+Ktlint+Detekt）、全量单元测试 + 覆盖率、Paparazzi 快照、增量 Mutation 30% 阈值 | `develop-integration.yml` (full-quality-check job) ⟶ 补充 Kover + Pitest-plugin | 10-20 min |
| **重型圈** | Nightly / Release             | 全量 Mutation ≥ 60%、MacroBenchmark、Chaos Monkey、State-Guard UI 状态快照、文档/注释校验 | `nightly.yml` + `release-pipeline.yml`                                        | 30-45 min |

> 通过分圈让**轻任务前置、重任务靠后**：本地/PR 只拦截“Coding 低级错误”，Nightly/Release 把“系统性缺陷”兜底。

---

## ② 关键栈的落地细节

| 栈                      | 做什么                                                                                  | 具体实现                                                                                                                            |
| ---------------------- | ------------------------------------------------------------------------------------ | ------------------------------------------------------------------------------------------------------------------------------- |
| **代码风格**               | ktlint 0 warning，Detekt 0 warning                                                    | 已在 `code-quality` 步骤跑 ktlint/Detekt，你只需把 \**baseline *.xml** 上传成工件，并在每次 Nightly 与 baseline 做 diff（`detektBaselineCompare` 插件即可） |
| **模块依赖**               | 禁止 features ↔ features 直接依赖、UI 层依赖 data 层等                                           | `com.jraska.module.graph.assertion`<br>`gradle<br>moduleGraphAssert { maxHeight = 3 }`<br>在 **Fast 圈** 一起跑（几百毫秒）                |
| **单元测试 + 覆盖**          | Kover 汇总，核心模块 ≥ 85% 覆盖                                                               | `./gradlew koverXmlReport`；把 XML 上传供 SonarQube / Codecov 阶段门控                                                                   |
| **Mutation**           | Pitest-Kotlin：快速圈跑「受改文件」；Nightly 跑全量 / ≥ 60% survive                                 | `./gradlew pitest --fileFilter <changedFiles>`，Nightly 去掉 filter；阈值在 `pitest.gradle.kts` 里 `mutationThreshold = 60`             |
| **UI 快照**              | Paparazzi + Shot（可选）                                                                 | 已在 develop-integration 中示例 `recordPaparazziDebug`                                                                               |
| **MacroBenchmark**     | 启动、滚动、导航帧率                                                                           | 参考现有 `benchmark-ci.yml`，Nightly job 里用 Gradle Managed Device                                                                    |
| **Chaos / Resilience** | Chaos Monkey for Spring?  → Android 用 **Fail-Inject / Battery-Drain / NetworkTools** | `chaos_testing.yml` 里跑自定义 shell，注入随机网络中断、进程 Kill、低电量                                                                            |
| **State-Guard**        | 关键 Compose State Snapshot + Golden 图                                                 | `state-guard.yml` → 保存 `*.state.json` + `*.png`；Release 阶段 diff                                                                 |
| **文档 & KDoc**          | docs\_validation.yml                                                                 | `./gradlew dokkaHtml dokkaHtmlPartial` + `markdownlint`；强制公共 API 写 KDoc（见 workout PRD 代码质量门槛 ≥ 85% 覆盖率、KDoc 全覆盖 要求）             |

---

## ③ CI Matrix 与增量执行

* **模块变更检测**：你的 `pr-validation.yml` 已经用 `dorny/paths-filter` 动态拼 **\$MODULES** 环境变量，再按模块跑任务。
  *在同一 Job 内加：*

  ```yaml
  - name: Run Detekt only on changed modules
    run: ./gradlew $MODULES:detekt
  ```
* **Pitest & Kover**：同理，解析 GitHub API diff → 转换为 `--fileFilter` / `--include` 参数，减少 PR 时间。

---

## ④ 本地到 CI 的开发者体验

1. **pre-commit git hook**：`ktlintFormat`, `detekt`, `moduleGraphAssert`
2. **pre-push hook**：`./gradlew testDebugUnitTest koverXmlReport --continue`
3. **IDE 插件**：Ktlint-Idea、Detekt-Idea、SonarLint；CI 与本地规则同源（shared `.editorconfig`）。
4. **Danger-Kotlin**：在 PR 提示 Mutation↓ / 覆盖率↓ / Graph 违规等。

---

## ⑤ 监控与可观测

* **Build Scan URL & APK Size** 已在 develop-integration 步骤输出
* Nightly 汇总指标：`pitestSurvival`, `benchmarkStartMs`, `sse5xxRate`（见 AiStreamRepository metrics）。
* 失败时自动在 Slack / Teams @责任人，并附带失败模块、扫描报告 zip。

---

## ⑥ 迁移路径（两周落地）

| 时间          | 动作                                                           | 交付物                   |
| ----------- | ------------------------------------------------------------ | --------------------- |
| **Day 1-2** | 加 `module-graph-assertion`, 统一 `.editorconfig`、Detekt 规则     | PR #infra-static-gate |
| **Day 3-4** | 引入 **Kover**, 更新 `ciCheck` 聚合覆盖率                             | PR #coverage-gate     |
| **Day 5-7** | Pitest-Kotlin POC（auth & coach 模块）                           | Nightly 通过            |
| **Week 2**  | Chaos / State-Guard / Benchmark job 全量接入，Release-pipeline 调整 | CI All-Green          |

---

### 结论

* **Fast 圈** 拦截语法 & 依赖层次错误；
* **Full 圈** 保证语义正确 + 基础质量；
* **Heavy 圈** 验证鲁棒性、性能、文档，做“生命线”守护。

这样既保证 **迭代速度**，又守住了 **企业级稳健性**。如果需要具体 Gradle 脚本或 GitHub Actions 片段，我可以随时补充。
