package com.example.gymbro.data.workout.session.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * 会话动作实体 - SessionDB 动作执行记录
 *
 * 基于 05_Final_Database_Architecture.md 和 04_Implementation_Plan_CleanSlate_SessionDB.md 设计
 * 存储训练会话中每个动作的执行记录
 */
@Entity(
    tableName = "session_exercises",
    foreignKeys = [
        ForeignKey(
            entity = SessionEntity::class,
            parentColumns = ["id"],
            childColumns = ["sessionId"],
            onDelete = ForeignKey.CASCADE,
        ),
    ],
    indices = [
        Index("sessionId"),
        Index("exerciseId"),
        Index("order"),
        Index("status"),
    ],
)
data class SessionExerciseEntity(
    @PrimaryKey
    val id: String,

    // 关联信息
    val sessionId: String,
    val exerciseId: String, // 引用 ExerciseEntity.id
    val order: Int, // 在会话中的顺序

    // 冗余存储（便于历史查看）
    val name: String, // 动作名称

    // 计划配置
    val targetSets: Int, // 计划组数
    val completedSets: Int = 0, // 已完成组数

    // 休息时间配置（三层优先级）
    val restSeconds: Int?, // Template原始休息时间（冗余存储）
    val restSecondsOverride: Int? = null, // Session手动覆盖休息时间

    // 媒资字段（冗余存储，避免离线查询）
    val imageUrl: String? = null, // 动作示意图
    val videoUrl: String? = null, // 动作视频

    // 执行状态
    val status: String, // NOT_STARTED, IN_PROGRESS, COMPLETED, SKIPPED
    val startTime: Long?,
    val endTime: Long?,

    // 用户备注
    val notes: String?,
    val isCompleted: Boolean = false,
)
